<?php namespace Nt\Events;

use Nt\Container\Container;
use Nt\Contracts\Events\Dispatcher as DispatcherContract;

class Dispatcher implements DispatcherContract {

    protected $container;

    /**
     * The registered event listeners.
     *
     * @var array
     */
    protected $listeners = [];

    /**
     * The event firing stack.
     *
     * @var array
     */
    protected $firing = [];

    /**
     * Create a new event dispatcher instance.
     *
     * @param Container $container
     */
    public function __construct( Container $container = null )
    {
        $this->container = $container ?: new Container;
    }

    /**
     * Register an event listener with the dispatcher.
     *
     * @param  string|array $events
     * @param  mixed        $listener
     * @return void
     */
    public function listen( $events, $listener )
    {
        foreach ( (array)$events as $event ) {
            $this->listeners[ $event ][] = $this->makeListener( $listener );
        }
    }

    /**
     * Register an event listener with the dispatcher.
     *
     * @param  mixed  $listener
     * @return mixed
     */
    public function makeListener($listener)
    {
        return is_string($listener) ? $this->createClassListener($listener) : $listener;
    }

    /**
     * Create a class based listener using the IoC container.
     *
     * @param  mixed  $listener
     * @return \Closure
     */
    public function createClassListener($listener)
    {
        $container = $this->container;

        return function () use ($listener, $container) {
            return call_user_func_array(
                $this->createClassCallable($listener, $container), func_get_args()
            );
        };
    }
    /**
     * Create the class based event callable.
     *
     * @param  string  $listener
     * @param  \Illuminate\Container\Container  $container
     * @return callable
     */
    protected function createClassCallable($listener, $container)
    {
        list($class, $method) = $this->parseClassCallable($listener);


        return [$container->make($class), $method];

    }

    /**
     * Parse the class listener into class and method.
     *
     * @param  string  $listener
     * @return array
     */
    protected function parseClassCallable($listener)
    {
        $segments = explode('@', $listener);

        return [$segments[0], 2 === count( $segments) ? $segments[1] : 'handle'];
    }

    /**
     * Determine if a given event has listeners.
     *
     * @param  string $eventName
     * @return bool
     */
    public function hasListeners( $eventName )
    {
        return array_key_exists($eventName,$this->listeners);
    }

    /**
     * Register an event subscriber with the dispatcher.
     *
     * @param  object|string $subscriber
     * @return void
     */
    public function subscribe( $subscriber )
    {
        $subscriber = $this->resolveSubscriber($subscriber);

        $subscriber->subscribe($this);
    }

    /**
     * Resolve the subscriber instance.
     *
     * @param  object|string  $subscriber
     * @return mixed
     */
    protected function resolveSubscriber($subscriber)
    {
        if (is_string($subscriber)) {
            return $this->container->make($subscriber);
        }

        return $subscriber;
    }

    /**
     * Fire an event and call the listeners.
     *
     * @param  string|object $event
     * @param array          $payload
     * @param bool           $halt
     * @return array|null
     */
    public function fire( $event, $payload = [], $halt = false )
    {

        // When the given "event" is actually an object we will assume it is an event
        // object and use the class as the event name and this event itself as the
        // payload to the handler, which makes object based events quite simple.
        if (is_object($event)) {
            list($payload, $event) = [[$event], get_class($event)];
        }
        $responses = [];

        // If an array is not given to us as the payload, we will turn it into one so
        // we can easily use call_user_func_array on the listeners, passing in the
        // payload to each of them so that they receive each of these arguments.
        if (! is_array($payload)) {
            $payload = [$payload];
        }

        $this->firing[] = $event;


        foreach ($this->getListeners($event) as $listener) {
            $response = call_user_func_array($listener, $payload);

            // If a response is returned from the listener and event halting is enabled
            // we will just return this response, and not call the rest of the event
            // listeners. Otherwise we will add the response on the response list.
            if (!null === $response && $halt) {
                array_pop($this->firing);

                return $response;
            }

            // If a boolean false is returned from a listener, we will stop propagating
            // the event to any further listeners down in the chain, else we keep on
            // looping through the listeners and firing every one in our sequence.
            if ($response === false) {
                break;
            }

            $responses[] = $response;
        }

        array_pop($this->firing);

        return $halt ? null : $responses;
    }


    /**
     * Get all of the listeners for a given event name.
     *
     * @param  string  $eventName
     * @return array
     */
    public function getListeners($eventName)
    {
        if(array_key_exists($eventName,$this->listeners)){
            return (array)$this->listeners[$eventName];
        }
        else {
            return [];
        }
    }

    /**
     * Get the event that is currently firing.
     *
     * @return string
     */
    public function firing()
    {
        return last($this->firing);
    }

    /**
     * Remove a set of listeners from the dispatcher.
     *
     * @param  string $event
     * @return void
     */
    public function forget( $event )
    {
        unset($this->listeners[$event]);
    }
}