<?php namespace Nt\Contracts\Events;

interface Dispatcher
{
    /**
     * Register an event listener with the dispatcher.
     *
     * @param  string|array  $events
     * @param  mixed  $listener
     * @return void
     */
    public function listen($events, $listener);

    /**
     * Determine if a given event has listeners.
     *
     * @param  string  $eventName
     * @return bool
     */
    public function hasListeners($eventName);


    /**
     * Register an event subscriber with the dispatcher.
     *
     * @param  object|string  $subscriber
     * @return void
     */
    public function subscribe($subscriber);

    /**
     * Fire an event and call the listeners.
     *
     * @param  string|object $event
     * @param array          $payload
     * @param bool           $halt
     * @return array|null
     */
    public function fire($event, $payload = [], $halt = false);

    /**
     * Get the event that is currently firing.
     *
     * @return string
     */
    public function firing();

    /**
     * Remove a set of listeners from the dispatcher.
     *
     * @param  string  $event
     * @return void
     */
    public function forget($event);
}
