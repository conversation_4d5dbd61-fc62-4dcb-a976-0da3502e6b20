<?php
use Nt\Container\Container;

if (! function_exists( 'app')) {
    /**
     * Get the available container instance.
     *
     * @param  string  $make
     * @param  array   $parameters
     * @return mixed|\Nt\Container\Container
     */
    function app($make = null, $parameters = [])
    {
        if(null === Container::getInstance()) {
            Container::setInstance( new Container() );
        }
        if ( null === $make ) {
            return Container::getInstance();
        }

        return Container::getInstance()->make($make, $parameters);
    }
}