<?php
use Nt\Commander\CommanderServiceProvider;
use Nt\Commander\Events\EventListener;
use Topic\Application\Service\AddTopicService;
use Topic\Domain\Event\TopicWasPublished;
use Topic\Domain\Exception\InvalidTagId;
use Topic\Domain\Model\Topic\Topic;
use Topic\Domain\Model\Topic\TopicRepository;
use Topic\Domain\Service\TagService;

class addTopicServiceTest extends PHPUnit_Framework_TestCase {

    protected function setUp()
    {
        $provider = new CommanderServiceProvider( app() );
        $provider->register();
        app()->instance( 'Nt\Container\Container', app() );
        app()->bind( 'Topic\Domain\Service\TagService', 'InMemoryTagService' );
        app()->bind( 'Topic\Domain\Model\Topic\TopicRepository', 'InMemoryTopicRepository' );

        $provider = new \Nt\Events\EventServiceProvider();
        $provider->register();

        parent::setUp();
    }

    /**
     * @test
     * @expectedException \InvalidArgumentException
     * @expectedExceptionMessage Unable to map input to command: title
     */
    public function 发话题但是缺少Title字段()
    {
        $arrInput = [
            'method'  => 'addTopic',
            'user_id' => 1496,
            'tags'    => [
                1, 2,
            ],
        ];
        (new AddTopicService())->execute( $arrInput );

    }

    /**
     * @test
     */
    public function 发话题并且命令执行(  )
    {
        app('events')->listen(
            'Topic.Domain.Event.TopicWasPublished','DummyListener'
        );
//        dd(app('events'));

        (new AddTopicService())->execute( $this->dummyInput() );

        static::assertTrue(DummyListener::$fired);
    }

    /**
     * @test
     * @expectedException InvalidArgumentException
     * @expectedExceptionMessage Expected a value to contain between 5 and 20 characters. Got: "你好"
     */
    public function 发话题但是Title长度小于5()
    {
        (new AddTopicService())->execute($this->dummyInput( [ 'title' => '你好' ] ));
    }

    /**
     * @test
     */
    public function 发话题默认可以不传Desc()
    {
        (new AddTopicService())->execute($this->dummyInput());
    }

    /**
     * @test
     * @expectedException \Topic\Domain\Exception\InvalidTagId
     * @expectedExceptionMessage tag id 1 not validate
     * @expectedExceptionCode 210009
     */
    public function 发话题标签不合法()
    {
        app()->bind( 'Topic\Domain\Service\TagService', 'InMemoryTagServiceInvalidTag' );
        (new AddTopicService())->execute($this->dummyInput());
    }

    protected function dummyInput( array $override = [ ] )
    {
        return array_merge(
            [
                'method'  => 'addTopic',
                'user_id' => 1496,
                'title'   => '你好人大好',
                'tags'    => [
                    1, 2,
                ],
            ],
            $override
        );
    }

}

class DummyListener extends EventListener{

    public static $fired = false;

    public function whenTopicWasPublished( TopicWasPublished $event )
    {
        self::$fired = true;
    }
}

class InMemoryTagServiceInvalidTag implements TagService {

    /**
     * @param $id
     */
    public function ofId( $id )
    {
        // TODO: Implement ofId() method.
    }

    /**
     * @param array $ids
     */
    public function ofIds( array $ids )
    {
        // TODO: Implement ofIds() method.
    }

    /**
     * @param array $ids
     * @return bool
     * @throws InvalidTagId
     */
    public function validateTagIds( array $ids )
    {
        throw new InvalidTagId( sprintf( 'tag id %s not validate', reset( $ids ) ), 210009 );
    }
}

class InMemoryTagService implements TagService {

    /**
     * @param $id
     */
    public function ofId( $id )
    {
        // TODO: Implement ofId() method.
    }

    /**
     * @param array $ids
     */
    public function ofIds( array $ids )
    {
        // TODO: Implement ofIds() method.
    }

    /**
     * @param array $ids
     * @return bool
     * @throws InvalidTagId
     */
    public function validateTagIds( array $ids )
    {
        return true;
    }
}

class InMemoryTopicRepository implements TopicRepository {

    /**
     * @return \Topic\Domain\Model\Topic\TopicId
     */
    public function nextIdentity()
    {
        return new \Topic\Domain\Model\Topic\TopicId( mt_rand( 1, 10000 ) );
    }

    /**
     * @param Topic $topic
     */
    public function add( Topic $topic )
    {
        //        return $topic;
    }

    /**
     * @param  integer[] $ids
     */
    public function ofTopicIds( $ids )
    {
        // TODO: Implement ofTopicIds() method.
    }

    /**
     * @param $id
     */
    public function ofTopicId( $id )
    {
        // TODO: Implement ofTopicId() method.
    }

    /**
     * @param $id
     * @return Topic
     * @throws \Topic\Domain\Exception\InvalidTopicId
     */
    public function ofTopicIdOrFail( $id )
    {
        // TODO: Implement ofTopicIdOrFail() method.
    }

    public function save( $topic )
    {
        // TODO: Implement save() method.
    }

    /**
     * @param $topic_id
     * @return mixed
     */
    public function incReplyNum( $topic_id )
    {
        // TODO: Implement incReplyNum() method.
    }

    /**
     * @param $topic_id
     * @return mixed
     */
    public function decReplyNum( $topic_id )
    {
        // TODO: Implement decReplyNum() method.
    }

    /**
     * @param $topic_id
     * @return mixed
     */
    public function incFollowNum( $topic_id )
    {
        // TODO: Implement incFollowNum() method.
    }

    /**
     * @param $topic_id
     * @return mixed
     */
    public function decFollowNum( $topic_id )
    {
        // TODO: Implement decFollowNum() method.
    }
}
