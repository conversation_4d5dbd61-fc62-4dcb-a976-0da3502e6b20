<?php
use Nt\Commander\CommanderServiceProvider;
use Nt\Commander\Events\EventListener;
use Topic\Application\Service\AddTopicReplyService;
use Topic\Application\Service\AddTopicService;
use Topic\Domain\Event\TopicWasPublished;
use Topic\Domain\Exception\InvalidTopicId;
use Topic\Domain\Model\Reply\ReplyRepository;
use Topic\Domain\Model\Topic\Topic;
use Topic\Domain\Model\Topic\TopicRepository;
use Topic\Domain\Service\TagService;

class addTopicReplyServiceTest extends PHPUnit_Framework_TestCase {

    protected function setUp()
    {
        $provider = new CommanderServiceProvider( app() );
        $provider->register();
        app()->instance( 'Nt\Container\Container', app() );
        app()->bind( 'Topic\Domain\Model\Topic\TopicRepository', 'InMemoryTopicRepository' );
        app()->bind( 'Topic\Domain\Model\Reply\ReplyRepository', 'InMemoryReplyRepository' );

        $provider = new \Nt\Events\EventServiceProvider();
        $provider->register();

        parent::setUp();
    }

    /**
     * @test
     * @expectedException \Topic\Domain\Exception\InvalidTopicId
     */
    public function 增加回复但是TopicId不合法()
    {
        (new AddTopicReplyService())->execute($this->dummyInput());
    }

    /**
     * @test
     */
    public function 正常回复()
    {
        app()->bind( 'Topic\Domain\Model\Topic\TopicRepository', 'InMemoryTopicRepositorySuccess' );
        (new AddTopicReplyService())->execute($this->dummyInput());
    }

    protected function dummyInput( array $override = [ ] )
    {
        return array_merge(
            [
                'method'  => 'addTopicReply',
                'topic_id' => 1,
                'reply_id' => 1,
                'has_resource' => 1,
                'time' => 12121,
            ],
            $override
        );
    }

}


class InMemoryReplyRepository implements ReplyRepository{

    public static $reply;

    public function getRank( $topic_id, $order, $pn, $rz )
    {
        // TODO: Implement getRank() method.
    }

    public function count()
    {
        // TODO: Implement count() method.
    }

    /**
     * @param $topic_id
     * @param $reply_id
     * @return \Topic\Domain\Model\Reply\Reply
     */
    public function ofTopicIdAndId( $topic_id, $reply_id )
    {
        // TODO: Implement ofTopicIdAndId() method.
    }

    public function save( \Topic\Domain\Model\Reply\Reply $reply )
    {
        // TODO: Implement save() method.
    }

    public function add( $reply )
    {
        static::$reply = $reply;
    }

    /**
     * @param $topic_id
     * @param $reply_id
     */
    public function remove( $topic_id, $reply_id )
    {
        // TODO: Implement remove() method.
    }
}

class InMemoryTopicRepository implements TopicRepository {

    /**
     * @return \Topic\Domain\Model\Topic\TopicId
     */
    public function nextIdentity()
    {

    }

    /**
     * @param Topic $topic
     */
    public function add( Topic $topic )
    {
        //        return $topic;
    }

    /**
     * @param  integer[] $ids
     */
    public function ofTopicIds( $ids )
    {
        // TODO: Implement ofTopicIds() method.
    }

    /**
     * @param $id
     */
    public function ofTopicId( $id )
    {
        // TODO: Implement ofTopicId() method.
    }

    /**
     * @param $id
     * @return Topic
     * @throws \Topic\Domain\Exception\InvalidTopicId
     */
    public function ofTopicIdOrFail( $id )
    {
        throw new InvalidTopicId($id);
    }

    public function save( $topic )
    {
        // TODO: Implement save() method.
    }

    public function count( $topic_id )
    {
        // TODO: Implement count() method.
    }
}

class InMemoryTopicRepositorySuccess implements TopicRepository {

    /**
     * @return \Topic\Domain\Model\Topic\TopicId
     */
    public function nextIdentity()
    {

    }

    /**
     * @param Topic $topic
     */
    public function add( Topic $topic )
    {
        //        return $topic;
    }

    /**
     * @param  integer[] $ids
     */
    public function ofTopicIds( $ids )
    {
        // TODO: Implement ofTopicIds() method.
    }

    /**
     * @param $id
     */
    public function ofTopicId( $id )
    {
        // TODO: Implement ofTopicId() method.
    }

    /**
     * @param $id
     * @return Topic
     * @throws \Topic\Domain\Exception\InvalidTopicId
     */
    public function ofTopicIdOrFail( $id )
    {
    }

    public function save( $topic )
    {
        // TODO: Implement save() method.
    }

    public function count( $topic_id )
    {
        // TODO: Implement count() method.
    }
}
