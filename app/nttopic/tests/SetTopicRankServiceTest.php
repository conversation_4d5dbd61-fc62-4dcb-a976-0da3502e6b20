<?php
use Nt\Commander\CommanderServiceProvider;
use Topic\Application\Service\SetTopicRankService;
use Topic\Domain\Model\Reply\Reply;
use Topic\Domain\Model\Reply\ReplyRepository;

class SetTopicRankServiceTest extends PHPUnit_Framework_TestCase{

    protected function setUp()
    {
        $provider = new CommanderServiceProvider( app() );
        $provider->register();
        app()->instance( 'Nt\Container\Container', app() );
        app()->bind( 'Topic\Domain\Model\Reply\ReplyRepository', 'InMemoryReplyRepository' );

        $provider = new \Nt\Events\EventServiceProvider();
        $provider->register();

        parent::setUp();
    }

    /**
     * @test
     * @expectedException \InvalidArgumentException
     * @expectedExceptionMessage can not find topic_id:1 and reply_id:1
     */
    public function 测试还没有对应回复需要抛出异常(  )
    {
        (new SetTopicRankService())->execute($this->dummyInput());
    }

    /**
     * @test
     */
    public function 正常设置成功()
    {
        app()->bind( 'Topic\Domain\Model\Reply\ReplyRepository', 'InMemoryReplyRepositorySuccess' );
        $expected = [
            'rank' => 1100,
            'has_resource' => 1,
        ];
        (new SetTopicRankService())->execute($this->dummyInput(
            $expected
        ));

        static::assertEquals(
            $expected,
            InMemoryReplyRepositorySuccess::$dataModified
        );
    }

    protected function dummyInput( array $override = [ ] )
    {
        return array_merge(
            [
                'method'     => 'setTopicRank',
                'topic_id'   => 1,
                'reply_id'   => 1,
                'rank' => 10,
                'has_resource' => 1,
            ],
            $override
        );
    }
}


class InMemoryReplyRepository implements ReplyRepository{

    public function getRank( $topic_id, $order, $pn, $rz )
    {
        // TODO: Implement getRank() method.
    }

    public function count()
    {
        // TODO: Implement count() method.
    }

    /**
     * @param $topic_id
     * @param $reply_id
     * @return \Topic\Domain\Model\Reply\Reply
     */
    public function ofTopicIdAndId( $topic_id, $reply_id )
    {
        return null;
    }

    public function save( Reply $reply )
    {

    }

    /**
     * @param Reply $reply
     * @return mixed
     */
    public function add( $reply )
    {
        // TODO: Implement add() method.
    }
}
class InMemoryReplyRepositorySuccess implements ReplyRepository {

    public static $dataModified;

    public function getRank( $topic_id, $order, $pn, $rz )
    {
        // TODO: Implement getRank() method.
    }

    public function count()
    {
        // TODO: Implement count() method.
    }

    /**
     * @param $topic_id
     * @param $reply_id
     * @return \Topic\Domain\Model\Reply\Reply
     */
    public function ofTopicIdAndId( $topic_id, $reply_id )
    {
        return new Reply(
            1,
            1,
            0,
            0
        );
    }

    public function save( Reply $reply )
    {
        self::$dataModified = $reply->getDataModified();
    }

    /**
     * @param Reply $reply
     * @return mixed
     */
    public function add( $reply )
    {
        // TODO: Implement add() method.
    }
}