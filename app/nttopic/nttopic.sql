CREATE TABLE `topic` (
  `id` bigint(20) unsigned NOT NULL auto_increment COMMENT '自增id',
  `topic_id` bigint(20) unsigned NOT NULL default '0' COMMENT '话题id',
  `first_floor` bigint(20) unsigned NOT NULL default '0' COMMENT '回复id',
  `title` varchar(32) NOT NULL default '' COMMENT '标题',
  `description` varchar(128) NOT NULL default '' COMMENT '描述',
  `cover` varchar(1024) NOT NULL default '' COMMENT '封面',
  `tags` varchar(128) NOT NULL default '' COMMENT '标签',
  `status` tinyint(3) NOT NULL default 0 COMMENT '话题状态:0待审核:1正常2:封禁',
  `is_recommend` tinyint(3) NOT NULL default 0 COMMENT '话题推荐0，1',
  `reply_num` bigint(20) unsigned NOT NULL default '0' COMMENT '回复数',
  `floor_num` bigint(20) unsigned NOT NULL default 0 COMMENT '楼层数',
  `collect_num` bigint(20) unsigned NOT NULL default '0' COMMENT '关注数',
  `user_id` bigint(20) unsigned NOT NULL default '0' COMMENT '作者',
  `publish_at` int(11) unsigned NOT NULL default '0' COMMENT '发布时间',
  `update_at` int(11) unsigned NOT NULL default '0' COMMENT '更新时间',
  PRIMARY KEY  (`id`),
  UNIQUE KEY `topic_id` (`topic_id`),
  KEY `publish_at` (`publish_at`,`status`,`is_recommend`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='话题表';

CREATE TABLE `follower` (
  `id` bigint(20) unsigned NOT NULL auto_increment COMMENT '自增id',
  `topic_id` bigint(20) unsigned NOT NULL COMMENT '话题id',
  `user_id` bigint(20) unsigned NOT NULL default '0' COMMENT '用户',
  `follow_at` int(11) unsigned NOT NULL default '0' COMMENT '关注时间',
  PRIMARY KEY  (`id`),
  UNIQUE KEY `topic_user_id` (`topic_id`,`user_id`),
  KEY `topic_time` (`topic_id`,`follow_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='话题关注表';

CREATE TABLE `reply` (
  `id` bigint(20) unsigned NOT NULL auto_increment COMMENT '自增id',
  `topic_id` bigint(20) unsigned NOT NULL COMMENT '话题id',
  `reply_id` bigint(20) unsigned NOT NULL default '0' COMMENT '回复id',
  `rank` bigint(20) unsigned NOT NULL default '0' COMMENT '权值',
  `has_resource` tinyint(3) NOT NULL default '1' COMMENT '是否有资源：0无，1有',
  `status` tinyint(3) NOT NULL default '1' COMMENT '话题状态:1正常2:删除',
  `reply_at` int(11) unsigned NOT NULL default '0' COMMENT '回复时间',
  `update_at` int(11) unsigned NOT NULL default '0' COMMENT '更新时间',
  PRIMARY KEY  (`id`),
  UNIQUE KEY `topic_reply_id` (`topic_id`,`reply_id`),
  KEY `topic_rank` (`topic_id`,`status`,`rank`,`reply_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='话题回复表';