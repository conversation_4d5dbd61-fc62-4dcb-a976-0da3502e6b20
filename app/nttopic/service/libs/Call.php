<?php

class Service_Libs_Call extends Service_Libs_Base {

    /**
     * @param $service_name
     * @param $method
     * @param $arrInput
     * @return mixed
     * @throws BadMethodCallException
     */
    public static function call( $service_name,
                                 $method,
                                 $arrInput )
    {
        Bingo_Timer::start($service_name . ':' .  $method);
        $ret = Tieba_Service::call( $service_name, $method, $arrInput, null, null, 'post',
                                    null,
                                    'utf-8' );
        Bingo_Timer::end($service_name . ':' .  $method);

        if(self::isFail($ret)) {
            throw new BadMethodCallException(
                sprintf('call %s:%d error  [input:%s] [output:%s]',$service_name,$method,serialize($arrInput), serialize($ret)),
                Tieba_Errcode::ERR_CALL_SERVICE_FAIL
            );
        }
        return $ret['data'];
    }
}