<?php
use Nt\Commander\CommanderServiceProvider;
use Nt\Events\EventServiceProvider;
use Topic\Domain\Model\Topic\Attribute;
use Topic\Domain\Model\Topic\AttrRepository;
use Topic\Domain\Service\ReplyService;
use Topic\Infrastructure\Listeners\SendTopicBlockedNmq;
use Topic\Infrastructure\Listeners\SendTopicBlockedToSe;
use Topic\Infrastructure\Listeners\SendTopicPassedToSe;
use Topic\Infrastructure\Listeners\SendTopicPrivateNmq;
use Topic\Infrastructure\Listeners\SendTopicPublishedNmq;
use Topic\Infrastructure\Listeners\SendTopicPublishedToSe;
use Topic\Infrastructure\Listeners\SendTopicRecoveredNmq;
use Topic\Infrastructure\Listeners\SendTopicRecoveredToSe;
use Topic\Infrastructure\Model\Service\HttpReplyService;
use Topic\Infrastructure\Model\Topic\RedisAttrRepository;

require_once __DIR__ . "/../vendor/autoload.php";

define('BINGO_ENCODE_LANG','UTF-8');
define("MODULE","Service_Nttopic");

class Service_Nttopic {
    CONST SERVICE_NAME = "Service_Nttopic";

    public static $service_ie = 'utf-8';

    protected static $_conf = null;


    const NMQ_COMMAND_TOPIC_REPLY  = 100;
    const NMQ_COMMAND_REPLY_DELETE = 101;

    /**
     * @param:
     * @return:
     **/
    public static function getIE($methodName){
        return self::$service_ie;
    }

    /**
     * @brief init
     * @param :
     * @return bool
     */
    private static function _init(){
        //add init code here. init will be called at every public function beginning.
        //not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
        //init should be recalled for many times.

        if(self::$_conf == null){
            self::$_conf = Bd_Conf::getConf("/app/nttopic/service_nttopic");
            if(self::$_conf == false){
                Bingo_Log::warning("init get conf fail.");
                return false;
            }
        }
        return true;
    }

    /**
     * @param:
     **/
    public static function preCall($arrInput){
        // pre-call hook
    }

    /**
     * @param:
     **/
    public static function postCall($arrInput){
        // post-call hook
    }

    /**
     * @param        $errno
     * @param string $data
     * @param string $data_key
     * @return array
     */
    public static function _errRet($errno,$data = "",$data_key = "data"){
        $errmsg = Tieba_Error::getErrmsg($errno);
        $arrRet = array(
            'errno' => $errno,
            'errmsg' => $errmsg,
        );
        if($data !== ""){
            $arrRet[$data_key] = $data;
        }
        Bingo_Log::pushNotice("errno",$errno);
        return $arrRet;
    }

    /**
     * @param string $data
     * @param string $data_key
     * @return array
     */
    public static function success($data = "",$data_key = "data") {
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$data, $data_key);
    }

    /**
     * @param        $no
     * @param string $data
     * @param string $data_key
     * @return array
     */
    public static function fail($no, $data = "",$data_key = "data") {
        return self::_errRet($no,$data, $data_key);
    }

    /**
     *
     */
    public static function bootApplication()
    {
        app()->instance( 'Nt\Container\Container', app() );

        $provider = new CommanderServiceProvider( app() );
        $provider->register();
        $provider = new EventServiceProvider();
        $provider->register();

        app()->bind( Topic\Domain\Service\TagService::class, Topic\Infrastructure\Model\Service\SimpleTagService::class );
        app()->bind( Topic\Domain\Model\Topic\TopicRepository::class, Topic\Infrastructure\Model\Topic\MysqlTopicRepository::class );
        app()->bind( Topic\Infrastructure\Model\Topic\IdStrategy::class, Topic\Infrastructure\Model\Topic\ClubcmIdStrategy::class );
        app()->bind( Topic\Domain\Model\Reply\ReplyRepository::class, Topic\Infrastructure\Model\Reply\MysqlReplyRepository::class );
        app()->bind( Topic\Domain\Model\Follower\FollowerRepository::class, Topic\Infrastructure\Model\Follower\MysqlFollowerRepository::class );
        app()->bind( ReplyService::class, HttpReplyService::class);
        app()->bind( AttrRepository::class, RedisAttrRepository::class);

        app('events')->listen(
            'Topic.Domain.Event.TopicWasPublished', SendTopicPublishedNmq::class
        );
        app('events')->listen(
            'Topic.Domain.Event.TopicWasPassed', SendTopicPassedToSe::class
        );
        app('events')->listen(
            'Topic.Domain.Event.TopicWasBlocked', SendTopicBlockedToSe::class
        );
        app('events')->listen(
            'Topic.Domain.Event.TopicWasBlocked', SendTopicBlockedNmq::class
        );
        app('events')->listen(
            'Topic.Domain.Event.TopicWasRecovered', SendTopicRecoveredNmq::class
        );
        app('events')->listen(
            'Topic.Domain.Event.TopicWasRecovered', SendTopicRecoveredToSe::class
        );
        app('events')->listen(
            'Topic.Domain.Event.TopicSetPrivate', SendTopicPrivateNmq::class
        );
    }

    /**
     * @brief    : 统一接入函数
     * @param $methodName
     * @param $arrInput
     * @return array|mixed :
     */
    public static function call($methodName, $arrInput){


        $applicationService = "Topic\\Application\\Service\\" . ucfirst($methodName) . 'Service';

//        if(!method_exists('Service_Nttopic',$methodName)){
//            Bingo_Log::warning(__FUNCTION__.' method not found. [method='.$methodName.']');
//            return self::_errRet(Tieba_Errcode::ERR_METHOD_NOT_FOUND);
//        }

        if(!class_exists($applicationService)) {
            Bingo_Log::warning(__FUNCTION__.' method not found. [method='.$methodName.']' . '[class=' . $applicationService . ']');
            return self::_errRet(Tieba_Errcode::ERR_METHOD_NOT_FOUND);
        }

        // boot Application
        self::bootApplication();

        $serviceName = self::SERVICE_NAME;
        $timer_key = $serviceName.'_'.$methodName;
        Bingo_Timer::start($timer_key);

        try {
            //
            $service = app($applicationService);
            $data = call_user_func(array($service, 'execute'), $arrInput);
//            $data = call_user_func(array('Service_Nttopic', $methodName), $arrInput);
        }
        catch (InvalidArgumentException $e){
            self::logException($e, $arrInput);
            return self::fail(Tieba_Errcode::ERR_PARAM_ERROR,$e->getMessage());
        }
        catch (Exception $e){
            self::logException($e,$arrInput);
            $code = $e->getCode();
            if($code === 0) {
                $code = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            }
            return self::fail($code,$e->getMessage(),'desc');
        }
        Bingo_Timer::end($timer_key);

        return self::success($data);
    }

    /**
     * @param Exception $e
     * @param           $arrInput
     */
    public static function logException( Exception $e, $arrInput )
    {
        $file = $e->getFile();
        $line = $e->getLine();
        $traceInfo = $e->getTraceAsString();
        $msg = $e->getMessage();
        $log = sprintf("file:%s line:%s error:%s arrInput:%s traceInfo:\n%s",
                       $file,$line,$msg,var_export($arrInput,1),$traceInfo);
        Bingo_Log::warning($log);
    }


    /**
     * @brief nmq处理
     * @param
     * @return
     **/
    public static function nmqCallback($arrInput){
        //input params.
        $data = Tieba_Service::getArrayParams($arrInput, 'data');

//        if(!self::_init()){
//            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
//        }
        //output params.

        //your code here......
        if (!isset($data['command_no'])) {
            Bingo_Log::warning ( "command_no loss." );
            return array ('status' => 0 );
        }
        $intCmd = (int)$data['command_no'];

        $intTransId = 0;
        if(isset($data ['trans_id'])) {
            $intTransId = (int)$data['trans_id'];
        }
        Bingo_Log::pushNotice("command_no",$intCmd);
        Bingo_Log::pushNotice("trans_id",$intTransId);

//        $res = self::_errRet(Tieba_Errcode::ERR_SUCCESS);

        if ($intCmd == Tieba_Cmd_Ntreply::$cmd[Tieba_Cmd_Ntreply::ADD_REPLY]) {
            $method             = 'addTopicReply';
            $res                = self::call($method,$data);
        }else if ($intCmd == Tieba_Cmd_Ntreply::$cmd[Tieba_Cmd_Ntreply::DEL_REPLY]){
            $method             = 'deleteTopicReply';
            $res                = self::call($method,$data);
        }else {
            $topic_id = $data['topic_id'];
            switch ($intCmd) {
                case Tieba_Cmd_Ntreply::$cmd[Tieba_Cmd_Ntreply::ADD_ZAN]: // 赞回复
                    $action = $data['action'];
//                    Bingo_Log::warning( var_export( $data, 1 ) );
                    if ($action == 'cancelReplyZan' || $action == 'cancelCommentZan'){
                        $action = 'del';
                        $value = 1;
                    }else {
                        $action = 'add';
                        $value = 1;
                    }
                    $attr = Attribute::ZAN_NUM;

                    break;
                case Tieba_Cmd_Ntcomment::$cmd[Tieba_Cmd_Ntcomment::ADD_COMMENT]: // 增加评论
                    $action = 'add';
                    $attr = Attribute::COMMENT_NUM;
                    $value = 1;
                    break;
                case Tieba_Cmd_Ntcomment::$cmd[Tieba_Cmd_Ntcomment::DEL_COMMENT]: // 删除评论
                    $action = 'del';
                    $attr = Attribute::COMMENT_NUM;
                    $value = 1;
                    break;
                default:
                    Bingo_Log::warning("don't support command:".$intCmd);
                    return self::fail(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $method             = 'dealTopicAttr';
            $res                = self::call($method,compact('action','attr','value','topic_id'));
        }


        if(Tieba_Errcode::ERR_SUCCESS !== $res['errno']){
            Bingo_Log::warning("nmq err. [errno=".$res['errno']."],[input=".serialize($arrInput).']');
        }

        return $res;
    }

    /**
     * @brief:联调使用 上线后删除
     * @param
     * @return array
     */
    public static function querySql($arrInput){
        $sql = $arrInput['sql'];
        $sql = trim($sql);

        $db = Tieba_Mysql::getDB('forum_nttopic');
        if($db && $db->isConnected()) {
            $db->charset('utf8');
        } else {
            Bingo_Log::warning("db connect fail.");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $data = $db->query($sql);

        return array(
            'errno' => Tieba_Errcode::ERR_SUCCESS,
            'errmsg' => '',
            'data' => $data,
        );
    }

//    /**
//     * @param $arrInput
//     * @return array
//     */
//    public static function addTopic( $arrInput )
//    {
//        $service = new AddTopicService();
//        return $service->execute($arrInput);
//    }
//
//    /**
//     * @param $arrInput
//     * @return mixed
//     */
//    public static function followTopic( $arrInput )
//    {
//        $service = new FollowTopicService();
//        return $service->execute($arrInput);
//    }
//    /**
//     * @param $arrInput
//     * @return mixed
//     */
//    public static function unFollowTopic( $arrInput )
//    {
//        $service = new UnFollowTopicService();
//        return $service->execute($arrInput);
//    }

}