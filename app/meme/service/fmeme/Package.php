<?php
/**
 * Created by PhpStorm.
 * User: ligengyong
 * Date: 2017/5/27
 * Time: 15:16
 */

class Service_Fmeme_Package extends Service_Libs_Base {
    const DATABASE_NAME = 'forum_meme';
//    const DATABASE_DDBS_NAME = 'forum_collection';  // 现在还是用forum_meme ,单机表,后期有需求在去迁移
    const DATABASE_DDBS_NAME = 'forum_meme';
    const PREFIX_HOT_MEME = 'comm_meme_hottest_recommend';
    const PREFIX_NEW_MEME = 'comm_meme_latest_recommend';
    //const REDIS = 'tbguess';
    const REDIS = 'twlive';
    const CACHE = 'forum_twlive';
    const PREFIX_HOT_CACHE = 'comm_meme_hottest_cache';
    const PREFIX_NEW_CACHE = 'comm_meme_latest_cache';
    const ALL_DELETE = 'all_delete';
    const TIME_OUT = 900;
    
    const PREFIX_TOP_MEME = 'comm_meme_top_cache';

    const COLLECT_TYPE_PKG = 1;
    const COLLECT_TYPE_PIC = 2;
    const COLLECT_SEQUENCE_ONE = 1;
    const COLLECT_SEQUENCE_TWO = 2;
    const COLLECT_SEQUENCE_THREE = 3;

    const MEME_PKG_VOTE_PREFIX = "MEME_PKG_VOTE_";
    const MEME_VOTE_UID_PREFIX = "meme_vote_uid_";

    const PKG_STATUS_WAIT_RANK = 5;
    const PKG_STATUS_PASS= 1;

    // 缓存
    const FORUM_PKG_PREFIX = 'FORUM_PKG_KEY';
    const FORUM_RECOMMEND_PKG_PREFIX = 'FORUM_RECOMMEND_PKG_KEY';
    const COLLECTION_PREFIX = 'COLLECTION_KEY';
    const FORUM_PKG_TIME_OUT = 120;
    const COLLECTION_TIME_OUT = 600;

    /**
     * @param $input
     * @return array
     */
    public static function addPackage($input){
        $name = strval($input['name']);
        $type = intval($input['type']);
        $owner = intval($input['owner']);
        $cover = intval($input['cover']);
        $status = intval($input['status']);
        // 单吧
        if(intval($input['forum_id']) > 0){
            $forum_id = intval($input['forum_id']);
        }

        $download = 0;
        $share = 0;
        $pics = $input['pics'];
        if(empty($name) || empty($cover) || !is_array($pics) || $owner < 1 ||
            count($pics)<Libs_Define::PACKAGE_NUM_SMALL || count($pics)>Libs_Define::PACKAGE_NUM_BIG){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }
        $num = count($pics);

        $arrInput = array(
            "user_id" => $owner,
        );
        $data = Tieba_Service::call('user', 'getUnameByUids', $arrInput, null, null, 'post', 'php', 'utf-8');
        if(!empty($data) && isset($data['errno']) && Tieba_Errcode::ERR_SUCCESS === $data['errno']){
            $user_name = $data['output']['unames'][0]['user_name'];
        }
        else{
            Bingo_Log::warning("call getUnameByUids failed,rets: ".serialize($data).' input:'.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $intErrno = Tieba_Errcode::ERR_SUCCESS;

        //开启事务
        if(false == $db->startTransaction()){
            Bingo_Log::warning('start trans failed.');
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        do{
            $arrInput = array(
                'table' => Dl_Fmeme_Package::$_table_name,
                'field' => array(
                    'id',
                ),
                'cond' => array(
                    'name' => $name,
//                    'author' => $user_name,
//                    'cover' => $cover,
                    'owner' => $owner,
                ),
            );
            $ret = $db->select($arrInput);
            if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning('select db error. input:'.serialize($arrInput).' output:'.serialize($ret));
                $intErrno = $ret['errno'];
                break;
            }
            if(!empty($ret['data'])){
                Bingo_Log::warning('meme package already exists: '.serialize($ret));
                $intErrno = Tieba_Errcode::ERR_ALREADY_USED;
                break;
            }

            $arrInput = array(
                'table' => Dl_Fmeme_Package::$_table_name,
                'field' => array(
                    'name' => $name,
                    'type' => $type,
                    'owner' => $owner,
                    'author' => $user_name,
                    'timestamp' => time(),
                    'status' => $status == 0 ? 0 : $status,
                    'download' => 0,
                    'share' => 0,
                    'cover' => $cover,
                    'num' => $num,
                ),
            );
            // 单吧,存 ext1 字段,是varchar
            if($forum_id != ''){
                $arrInput['field']['forum_id'] =  $forum_id;
            }
            $ret = $db->insert($arrInput);
            $package_id = intval($ret['data']);
            if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning('insert db error. input:'.serialize($arrInput).' output:'.serialize($ret));
                $intErrno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
            $arrInput = array(
                'table' => Dl_Fmeme_Map::$_table_name,
                'field' => array(
                    'sequence',
                    'pic_id',
                    'meme_id',
                    'height',
                    'width',
                ),
            );
            $values = array();
            $seq = 0;
            foreach($pics as $pic){
                $id = intval($pic['id']);
                $width = intval($pic['width']);
                $height = intval($pic['height']);
                $seq += 1;
                $values[] = array(
                    $seq,
                    $id,
                    $package_id,
                    $height,
                    $width,
                );
            }
            $arrInput['values'] = $values;
            $ret = $db->multiInsert($arrInput);
            if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning('multiInsert db error. input:'.serialize($arrInput).' output:'.serialize($ret));
                $intErrno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
        }while(0);

        if(Tieba_Errcode::ERR_SUCCESS != $intErrno || false == $db->commit()){
            $db->rollback();
            Bingo_Log::warning('db commit failed or something wrong,errno=['.$intErrno.'].');
            return self::_errRet($intErrno);
        }
        $arrOutData = array(
            'pkg_id' => $package_id,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,'',$arrOutData);
    }

    /**
     * @param $input
     * @return array
     */
    public static function updatePackage($input){
        $params = Dl_Fmeme_Package::filter($input);
        $id = intval($params['id']);
        unset($params['id']);
        if($id < 1 || empty($params)){
            Bingo_Log::warning('params error: '.serialize($input).' filter:'.serialize($params));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }
        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => array(
                'name',
            ),
            'cond' => array(
                'id' => $id,
            ),
        );
        $ret = $db->select($arrInput);
        if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('select db error. input:'.serialize($arrInput).' output:'.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        if(empty($ret['data'])){
            Bingo_Log::warning('meme package is null: '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_FACES_GET_PACK_INFO_BY_PID, '表情包不存在');
        }
        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => $params,
            'cond' => array(
                'id' => $id,
            ),
        );
        $ret = $db->update($arrInput);
        if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('update db error. input:'.serialize($arrInput).' output:'.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return $ret;
    }

    /**
     * @param $input
     * @return array
     */
    public static function updatePic($input){
        $params = Dl_Fmeme_Map::filter($input);
        Bingo_Log::warning(print_r($params,1));
        $meme = intval($params['meme_id']);
        $pic = intval($params['pic_id']);
        unset($params['meme_id']);
        unset($params['pic_id']);
        if($meme < 1 || $pic< 1 ||empty($params)){
            Bingo_Log::warning('params error: '.serialize($input).' filter:'.serialize($params));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }
        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrInput = array(
            'table' => Dl_Fmeme_Map::$_table_name,
            'field' => array('id'),
            'cond' => array(
                'meme_id' => $meme,
                'pic_id' => $pic,
            ),
        );
        $ret = $db->select($arrInput);
        if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('select db error. input:'.serialize($arrInput).' output:'.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        if(empty($ret['data'])){
            Bingo_Log::warning('meme package already exists: '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_ALREADY_USED, '表情不存在');
        }
        $arrInput = array(
            'table' => Dl_Fmeme_Map::$_table_name,
            'field' => $params,
            'cond' => array(
                'meme_id' => $meme,
                'pic_id' => $pic,
            ),
        );
        $ret = $db->update($arrInput);
        if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('update db error. input:'.serialize($arrInput).' output:'.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return $ret;
    }

    /**
     * @param $input
     * @return array
     */
    public static function uploadPackage($arrInput){

        $intUserId = $arrInput['user_id'];
        $strPackageIds = $arrInput['package_ids'];
        $intUpdateTime = $arrInput['update_time'];
        $intType = $arrInput['type'];
        $intSequence = $arrInput['sequence'];
        $strExt = $arrInput['ext'];

        if($intUserId < 1 || $strPackageIds == ''){
            Bingo_Log::warning('params error: '.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }
        $db = Libs_ShareDb::getInstance(self::DATABASE_DDBS_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        // get by user_id, if has records, update ,if no records, insert

        $arrGetInput = array(
            'table' => Dl_Fmeme_Collection::$_table_name,
            'field' => array(
                'user_id'
            ),
            'cond' => array(
                'user_id' => $intUserId,
                'type'      => $intType,
            ),
        );
        $getOutput = $db->select($arrGetInput);
        if($getOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('select db error. input:'.serialize($arrGetInput).' output:'.serialize($getOutput));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        if(empty($getOutput['data'])){
            $arrInsertDB = array(
                'table' => Dl_Fmeme_Collection::$_table_name,
                'field' => array(
                    'user_id' => $intUserId,
                    'type' => $intType,
                    'sequence' => $intSequence,
                    'content' => $strPackageIds,
                    'update_time' => $intUpdateTime,
                    'ext1' => $strExt,
                ),
            );
            $insertRet = $db->insert($arrInsertDB);

            if($insertRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning('insert db error. input:'.serialize($arrInsertDB).' output:'.serialize($insertRet));
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }

        } else {
            $arrUpdateDB = array(
                'table' => Dl_Fmeme_Collection::$_table_name,
                'field' => array(
                    'user_id' => $intUserId,
                    'type' => $intType,
                    'sequence' => $intSequence,
                    'content' => $strPackageIds,
                    'update_time' => $intUpdateTime,
                    'ext1' => $strExt,
                ),
                'cond' => array(
                    'user_id'   => $intUserId,
                    'sequence'     => $intSequence,
                    'type'      => $intType,
                ),
            );

            if($strPackageIds == self::ALL_DELETE){
                $arrUpdateDB['field']['content'] = '';
            }
            $updateRet = $db->update($arrUpdateDB);

            if($updateRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning('update db error. input:'.serialize($arrUpdateDB).' output:'.serialize($updateRet));
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
        }

        // del redis cache

//        $redis = Libs_Redis::getInstance(self::REDIS);
//        $redisKey = self::COLLECTION_PREFIX;
//
//        $arrDelInput = array(
//            'key' => $redisKey,
//            'field' => $intUserId,
//
//        );
//        $redisRet = $redis->HDEL($arrDelInput);

        // del memcache
        $objCache = Libs_Cache::getInstance(self::CACHE);
        $strKey = self::COLLECTION_PREFIX.'_'.$intUserId;
        $arrCacheInput = array(
            'key'   =>  $strKey,
        );
        $objCache->del($arrCacheInput);

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * @param $arrInput
     * @return array
     */
    public static function deleteAllCollections($arrInput){
        $intUid = $arrInput['user_id'];
        $intType = $arrInput['type'];
        if($intUid < 1 || $intType <= 0){
            Bingo_Log::warning('params error: '.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }
        $db = Libs_ShareDb::getInstance(self::DATABASE_DDBS_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrDelInput = array(
            'table' => Dl_Fmeme_Collection::$_table_name,
            'cond' => array(
                'user_id'   => $intUid,
                'type'      => $intType,
            ),
        );

        $delOutput = $db->delete($arrDelInput);
        if($delOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('select db error. input:'.serialize($arrDelInput).' output:'.serialize($delOutput));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        // del redis cache

//        $redis = Libs_Redis::getInstance(self::REDIS);
//        $redisKey = self::COLLECTION_PREFIX;
//
//        $arrDelInput = array(
//            'key' => $redisKey,
//            'field' => $intUid,
//
//        );
//        $redisRet = $redis->HDEL($arrDelInput);

        // del memcache
        $objCache = Libs_Cache::getInstance(self::CACHE);
        $strKey = self::COLLECTION_PREFIX.'_'.$intUid;
        $arrCacheInput = array(
            'key'   =>  $strKey,
        );
        $objCache->del($arrCacheInput);


        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $input
     * @return array
     */
    public static function uploadPics($arrInput){

        $intUserId = (int)$arrInput['user_id'];
        $strPicsIds = $arrInput['pic_ids'];
        $intUpdateTime = $arrInput['update_time'];
        $intType = (int)$arrInput['type'];
        $intSequence = (int)$arrInput['sequence'];
        $strExt = $arrInput['ext'];

        if($intUserId < 1 || $strPicsIds == ''){
            Bingo_Log::warning('params error: '.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }
        $db = Libs_ShareDb::getInstance(self::DATABASE_DDBS_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        // get by user_id, if has records, update ,if no records, insert

        $arrGetInput = array(
            'table' => Dl_Fmeme_Collection::$_table_name,
            'field' => array(
                'user_id'
            ),
            'cond' => array(
                'user_id'   => $intUserId,
                'sequence'     => $intSequence,
                'type'      => $intType,
            ),
        );
        $getOutput = $db->select($arrGetInput);
        if($getOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('select db error. input:'.serialize($arrGetInput).' output:'.serialize($getOutput));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        if(!empty($getOutput['data'])){

            $arrDelInput = array(
                'table' => Dl_Fmeme_Collection::$_table_name,
                'cond' => array(
                    'user_id'   => $intUserId,
                    'type'      => $intType,
                ),
            );

            $delOutput = $db->delete($arrDelInput);
            if($delOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning('select db error. input:'.serialize($arrDelInput).' output:'.serialize($delOutput));
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }

        }

        $arrInsertDB = array(
            'table' => Dl_Fmeme_Collection::$_table_name,
            'field' => array(
                'user_id' => $intUserId,
                'type' => $intType,
                'sequence' => $intSequence,
                'content' => $strPicsIds,
                'update_time' => $intUpdateTime,
                'ext1' => $strExt,
            ),
        );

        if($strPicsIds == self::ALL_DELETE){
            $arrInsertDB['field']['content'] = '';
        }

        $insertRet = $db->insert($arrInsertDB);


        if($insertRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('insert db error. input:'.serialize($arrInsertDB).' output:'.serialize($insertRet));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }


        // del redis cache

//        $redis = Libs_Redis::getInstance(self::REDIS);
//        $redisKey = self::COLLECTION_PREFIX;
//
//        $arrDelInput = array(
//            'key' => $redisKey,
//            'field' => $intUserId,
//
//        );
//        $redisRet = $redis->HDEL($arrDelInput);

        // del memcache
        $objCache = Libs_Cache::getInstance(self::CACHE);
        $strKey = self::COLLECTION_PREFIX.'_'.$intUserId;
        $arrCacheInput = array(
            'key'   =>  $strKey,
        );
        $objCache->del($arrCacheInput);

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * @param $arrInput
     * @return array
     */
    public static function getAllCollections($arrInput){
        $intUserId = intval($arrInput['user_id']);
        if($intUserId < 1){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        // checkout redis
//        $redis = Libs_Redis::getInstance(self::REDIS);
//        $redisKey = self::COLLECTION_PREFIX;
//
//        $arrGetRedisInput = array(
//            'redis' => self::REDIS,
//            'key' => $redisKey,
//            'field' => $intUserId,
//        );
//
//        $redisRet = $redis->HGET($arrGetRedisInput);
//        if(!$redisRet){
//            Bingo_Log::warning('hgetToRedis  error. input:'.serialize($arrGetRedisInput));
//        }
//        $redisCollectionData = $redisRet;

        // get memcache
        $objCache = Libs_Cache::getInstance(self::CACHE);
        $strCacheKey = self::COLLECTION_PREFIX.'_'.$intUserId;
        $arrCacheInput = array(
            'key'   =>  $strCacheKey,
        );
        $arrCachedData = $objCache->get($arrCacheInput);
        if (!$arrCachedData) {
            Bingo_Log::warning('get from memcached failed. [input: '.serialize($arrCacheInput).' ]');
        } else if(!empty($arrCachedData)) {
            Bingo_Log::warning('hit cache !!! key:'.serialize($strCacheKey));
            $ret = $arrCachedData;
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $ret);
        }


        // if not in cache,read db

        $db = Libs_ShareDb::getInstance(self::DATABASE_DDBS_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrInput = array(
            'table' => Dl_Fmeme_Collection::$_table_name,
            'field' => Dl_Fmeme_Collection::$_table_fields,
            'cond' => array(
                'user_id' => $intUserId,
            ),
        );
        $ret = $db->select($arrInput);
        if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('select error '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrOutData = $ret['data'];

        $strPackageIds = '';
        $intPkgUpdateTime = 0;
        $strPicIds = '';
        $intPicUpdateTime = 0;
        $arrPicsTmp = array();
        foreach ($arrOutData  as $collect){
            if($collect['type'] == self::COLLECT_TYPE_PKG){
                $strPackageIds = $collect['content'];
                $intPkgUpdateTime = $collect['update_time'];
            } else if ($collect['type'] ==  self::COLLECT_TYPE_PIC ){
                $intPicUpdateTime = $collect['update_time'];
                $arrPicsTmp[] = $collect['content'];
            }
        }
        $strPicIds = implode("_",$arrPicsTmp);
        $arrResult = array(
            'package_ids' => $strPackageIds,
            'pkg_update_time' => $intPkgUpdateTime,
            'pic_ids'   => $strPicIds,
            'pic_update_time' => $intPicUpdateTime,
        );

        // set into redis

//        $arrUidInput = array(
//            'redis' => self::REDIS,
//            'key' => $redisKey,
//            'field' => $intUserId,
//            'value' => $arrResult,
//            'expire' => self::COLLECTION_TIME_OUT,
//        );
//        $redisRet = $redis->HSET($arrUidInput);
//        if(!$redisRet){
//            Bingo_Log::warning('hsetToRedis  error. input:'.serialize($arrUidInput));
//        }

        // set into memcache
        $objCache = Libs_Cache::getInstance(self::CACHE);
        $strCacheKey = self::COLLECTION_PREFIX.'_'.$intUserId;
        $arrCacheInput = array(
            'key'   =>  $strCacheKey,
            'value' =>  $arrResult,
            'expire' => self::COLLECTION_TIME_OUT,
        );
        $arrCacheRet = $objCache->add($arrCacheInput);
        if(!$arrCacheRet){
            Bingo_Log::warning('add to cache error. input:'.serialize($arrCacheInput));
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '',$arrResult);
    }


    /**
     * @param $input
     * @return array
     */
    public static function delPackage($input){
        $name = strval($input['name']);
        if(empty($name)){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $intErrno = Tieba_Errcode::ERR_SUCCESS;

        //开启事务
        if(false == $db->startTransaction()){
            Bingo_Log::warning('start trans failed.');
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        do{
            $arrInput = array(
                'table' => Dl_Fmeme_Package::$_table_name,
                'field' => array(
                    'id',
                ),
                'cond' => array(
                    'name' => $name,
                ),
            );
            $ret = $db->select($arrInput);
            if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning('select db error. input:'.serialize($arrInput).' output:'.serialize($ret));
                $intErrno = $ret['errno'];
                break;
            }
            if(empty($ret['data'])){
                Bingo_Log::warning('meme package not exists: '.serialize($ret));
                break;
            }
            $package = intval($ret['data'][0]['id']);
            if($package < 1){
                Bingo_Log::warning('package illegal');
                break;
            }
            $arrInput = array(
                'table' => Dl_Fmeme_Package::$_table_name,
                'cond' => array(
                    'name' => $name,
                ),
            );
            $ret = $db->delete($arrInput);
            if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning('delete db error. input:'.serialize($arrInput).' output:'.serialize($ret));
                $intErrno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
            $arrInput = array(
                'table' => Dl_Fmeme_Map::$_table_name,
                'cond' => array(
                    'meme_id' => $package,
                ),
            );
            $ret = $db->delete($arrInput);
            if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning('delete db error. input:'.serialize($arrInput).' output:'.serialize($ret));
                $intErrno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
        }while(0);

        if(Tieba_Errcode::ERR_SUCCESS != $intErrno || false == $db->commit()){
            $db->rollback();
            Bingo_Log::warning('db commit failed or something wrong,errno=['.$intErrno.'].');
            return self::_errRet($intErrno);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * @param $input
     * @return array
     */
    public static function getPackage($input){
        $name = strval($input['name']);
        if(empty($name)){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'cond' => array(
                'name' => $name,
            ),
        );
        return $db->select($arrInput);
    }

    /**
     * @param $input
     * @return array
     */
    public static function downloadPackage($input){
        $id = intval($input['id']);
        if($id < 1){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $mysql = 'update '.Dl_Fmeme_Package::$_table_name.' set download=download+1 where id='.$id.';';
        Bingo_Log::warning($mysql);
        $ret = $db->query($mysql);
        if(!$ret){
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $input
     * @return array
     */
    public static function sharePackage($input){
        $id = intval($input['id']);
        if($id < 1){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $mysql = 'update '.Dl_Fmeme_Package::$_table_name.' set share=share+1 where id='.$id.';';
        Bingo_Log::warning($mysql);
        $ret = $db->query($mysql);
        if(!$ret){
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $input
     * @return array
     */
    public static function getWaitZipPkgIdAndPicIds($input){

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $mysql = 'select pic_id,meme_id from meme_map where meme_id = (select min(id) from meme_package where status=3 );';
        Bingo_Log::warning($mysql);
        $ret = $db->query($mysql);
        if(!$ret){
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrOut = $ret;
        $intPkgId = 0;
        $arrPics = array();
        if(count($arrOut) > 1){
            $intPkgId = $arrOut[0]['meme_id'];
            foreach ($arrOut as $item){
                $arrPics[] = (int)$item['pic_id'];
            }
        }

        $arrResult = array(
            'pkg_id'    => $intPkgId,
            'pics'      => $arrPics,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,'',$arrResult);
    }

    /**
     * @brief   刷库用
     * @param   array   $arrInput
     * @return  array   $arrResult
     */
    public static function replacePackageUrl($arrInput) {
        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        $arrSqlInput = array(
            'table'     =>  Dl_Fmeme_Package::$_table_name,
            'field'     =>  array('id', 'url'),
            'append'    =>  ' where url like "%bos.nj.bpc%" limit 1',
        );
        $ret = $db->select($arrSqlInput);
        if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('select error '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        } else if (empty($ret['data'])) {
            Bingo_Log::warning('no more bos url');
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', array('done' => 1));
        }
        $arrPackageInfo = $ret['data'][0];
        $arrUrl = parse_url($arrPackageInfo['url']);
        $arrUrl['host'] = 'su.bcebos.com';
        $strUrl = $arrUrl['scheme'].'://'.$arrUrl['host'].$arrUrl['path'];
        $arrSqlInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => array(
                'url' => $strUrl,
            ),
            'cond'  => array(
                'id' => $arrPackageInfo['id'],
            ),
        );
        $ret = $db->update($arrSqlInput);
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', array('done' => 0));
    }

    /**
     * @param $input
     * @return array
     */
    public static function mgetPckIdByPicId($input){
        $arrPicId = $input['pic_id'];
        if(count($arrPicId)< 1){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrSqlInput = array(
            'table' => Dl_Fmeme_Map::$_table_name,
            'field' => Dl_Fmeme_Map::$_table_fields,
            'append' => ' where pic_id in ('.implode(',', $arrPicId).' )',
        );
        $ret = $db->select($arrSqlInput);
        if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('select error '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrOut = array();
        if(!empty($ret['data'])){
            foreach ($ret['data'] as $pic){
                $arrOut[$pic['pic_id']] = $pic['meme_id'];
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $arrOut);
    }


    /**
     * @param $input
     * @return array
     */
    public static function getPackageById($input){
        $id = intval($input['id']);
        if($id < 1){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'cond' => array(
                'id' => $id,
            ),
        );
        $ret = $db->select($arrInput);
        if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('select error '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $ret['data'][0]);
    }

    /**
     * @param $input
     * @return array
     */
    public static function getPackageNumByUserId($input){
        $userId = intval($input['user_id']);
        if($userId < 1){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
//            'cond' => array(
//                'owner' => $userId,
//            ),
            'append' => ' where owner =  '.$userId." and status in (0,1,2,3)",

        );
        $ret = $db->select($arrInput);
        if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('select error '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrOut['pck_num'] = count($ret['data']);
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $arrOut);
    }

    /**
     * @param $input
     * @return array
     */
    public static function getPackageByIds($input){
        $ids = $input['ids'];
        if(empty($ids) || !is_array($ids)){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }
        $arrIds = array();
        foreach ($ids as $id){
            $format = intval($id);
            if($format > 0){
                $arrIds[] = $format;
            }
        }
        if(empty($arrIds)){
            Bingo_Log::warning('has no good package ids ');
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'append' => ' where id in ('.implode(',', $arrIds).' )',
        );
        if(isset($input['user_id']) && $input['user_id'] >0){
            $arrInput['append'] =  $arrInput['append']." or owner = ".intval($input['user_id']);
        }
        $ret = $db->select($arrInput);
        if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
            $data = $ret['data'];
            foreach ($data as $key=>$value){
                $data[$key]['cover'] = self::_pid2Url(intval($value['cover']));
            }
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $data);
        }else{
            Bingo_Log::warning('db call failed input:'.serialize($arrInput).' output:'.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
    }

    /**
     * @param $input
     * @return array
     */
    public static function getPackageByUserId($input){
        $userId = intval($input['user_id']);
        if(empty($userId) || !is_integer($userId)){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'append' => ' where owner = '.$userId." order by id desc ",
        );
        $ret = $db->select($arrInput);
        if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
            $data = $ret['data'];
            foreach ($data as $key=>$value){
                $data[$key]['cover'] = self::_pid2Url(intval($value['cover']));
            }
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $data);
        }else{
            Bingo_Log::warning('db call failed input:'.serialize($arrInput).' output:'.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
    }

    /**
     * @param $input
     * @return array
     */
    public static function getPackageByPic($input){
        $id = intval($input['id']);
        if($id < 1){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrInput = array(
            'table' => Dl_Fmeme_Map::$_table_name,
            'field' => array('meme_id'),
            'cond' => array(
                'pic_id' => $id,
            ),
        );
        $ret = $db->select($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']){
            Bingo_Log::warning('call db failed input:'.serialize($arrInput).' rets:'.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $num = 0;
        foreach($ret['data'] as $value){
            $arrIds[] = intval($value['meme_id']);
            $num += 1;
            if($num == 3){
                break;
            }
        }

        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'append' => ' where id in ('.implode(',', $arrIds).' )',
        );
        return $db->select($arrInput);
    }

    /**
     * @param $input
     * @return array
     */
    public static function getPackageDetailByName($input){
        $name = strval($input['name']);
        if(empty($name)){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $fields = Dl_Fmeme_Package::$_table_fields;
        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => $fields,
            'cond' => array(
                'name' => $name,
            ),
        );
        $ret = $db->select($arrInput);
        if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $output = $ret['data'][0];
        if(empty($output)){
            Bingo_Log::warning('package not exists');
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $id = intval($output['id']);
        $arrInput = array(
            'table' => Dl_Fmeme_Map::$_table_name,
            'field' => array(
                'pic_id',
                'height',
                'width',
            ),
            'cond' => array(
                'meme_id' => $id,
            ),
        );
        $ret = $db->select($arrInput);
        if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        foreach ($ret['data'] as $pic){
            $tmp = array(
                'id' => intval($pic['pic_id']),
                'url' => self::_pid2Url(intval($pic['pic_id'])),
                'thumbnail' => self::_pid2Url(intval($pic['pic_id']), 'abpic'),
                'height' => intval($pic['height']),
                'width' => intval($pic['width']),
            );
            $pics[] = $tmp;
        }
        $output['pics'] = $pics;
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $output);
    }

    /**
     * @param $input
     * @return array
     */
    public static function getPackageDetailById($input){
        $id = intval($input['id']);
        if($id < 1){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $fields = Dl_Fmeme_Package::$_table_fields;
        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => $fields,
            'cond' => array(
                'id' => $id,
            ),
        );
        $ret = $db->select($arrInput);
        if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $output = $ret['data'][0];
        if(empty($output)){
            Bingo_Log::warning('package not exists');
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $arrInput = array(
            'table' => Dl_Fmeme_Map::$_table_name,
            'field' => array(
                'pic_id',
                'height',
                'width',
                'name',
                'thumbname',
            ),
            'cond' => array(
                'meme_id' => $id,
            ),
        );
        $ret = $db->select($arrInput);
        if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        foreach ($ret['data'] as $pic){
            $id = intval($pic['pic_id']);
            $tmp = array(
                'id' => $id,
                'url' => self::_pid2Url($id),
                'thumbnail' => self::_pid2Url($id, 'abpic'),
                'height' => intval($pic['height']),
                'width' => intval($pic['width']),
                'name' => $pic['name'],
                'thumbname' => $pic['thumbname'],
            );
            $pics[] = $tmp;
        }
        $output['cover'] = self::_pid2Url(intval($output['cover']));
        $output['pics'] = $pics;
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $output);
    }

    /**
     * @param $intPicId
     * @param string $type
     * @return mixed
     */
    private static function _pid2Url($intPicId, $type = 'pic' ){
        $arrParams = array(
            array(
                'pic_id' => $intPicId,
                'product_name' => 'forum',
                'pic_spec' => $type,
                'domain' => 'imgsrc.baidu.com',
            ),
        );

        $arrPicUrls = Bd_Pic::pid2Url($arrParams, false);
        return $arrPicUrls['resps'][0];
    }

    /**
     * @param $input
     * @return array
     */
    public static function urltopid($input){
        $strPicUrl = $input['url'];
        $options = array(
            'timeout' => 1000,
            'conn_timeout' => 1000,
            'max_response_size' => 10000000,
        );
        $headers = array(
            'Referer: www.baidu.com',
        );
        $httpproxy = Orp_FetchUrl::getInstance($options);
        Bingo_Timer::start("fetchurl");
        $strContents = false;
        for ($intTryTimes = 0; $intTryTimes < 3; ++$intTryTimes) {
            $strContents = $httpproxy->get($strPicUrl, $headers);
            if (false !== $strContents) {
                break;
            }
        }
        Bingo_Timer::end("fetchurl");
        if (false === $strContents) {
            $errno = $httpproxy->errno();
            $errmsg = $httpproxy->errmsg();
            Bingo_Log::warning("fetch url fail, url=$strPicUrl, errno=$errno, errmsg=$errmsg");
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        if (!imagecreatefromstring($strContents)) {
            Bingo_Log::warning("invalid image format, pic_url=$strPicUrl");
            return false;
        }

        $intUserId = 237871698;
        $strUserName = 'ligengyong2010';
        $intUserIp = 2887141441;
        $arrReq = array(
            'pic' => $strContents,
            'uid' => $intUserId,
            'uname' => $strUserName,
            'uip' => $intUserIp,
            'log_data' => array(
                'uid' => $intUserId,
                'uname' => $strUserName,
                'ip' => $intUserIp,
            ),
        );
        $arrRet = Space_Imgcmnew::upload($arrReq);
        if (false === $arrRet || 0 != $arrRet['err_no']) {
            Bingo_Log::warning("upload image fail, pic_url=$strPicUrl, result=" . serialize($arrRet));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $intPicId = $arrRet['pic_id'];
        $data = array(
            'pic_id' => $intPicId,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $data);
    }

    /**
     * @param $input
     * @return array
     */
    public static function picid2url($input){
        $intPicId = intval($input['id']);
        $type = $input['type'];
        $arrParams = array(
            array(
                'pic_id' => $intPicId,
                'product_name' => 'forum',
                'pic_spec' => $type,
                'domain' => 'imgsrc.baidu.com',
            ),
        );

        $arrPicUrls = Bd_Pic::pid2Url($arrParams, false);
        $url = $arrPicUrls['resps'][0];
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $url);
    }

    /**
     * @param $input
     * @return array
     */
    public static function getMapByPackage($input){
        $id = intval($input['id']);
        if($id < 1){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrInput = array(
            'table' => Dl_Fmeme_Map::$_table_name,
            'field' => Dl_Fmeme_Map::$_table_fields,
            'cond' => array(
                'meme_id' => $id,
            ),
        );
        return $db->select($arrInput);
    }

    /**
     * @param $input
     * @return array
     */
    public static function getMapByPackages($input){
        $ids = $input['ids'];
        if(empty($ids) || !is_array($ids)){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }
        $arrIds = array();
        foreach ($ids as $id){
            $format = intval($id);
            if($format > 0){
                $arrIds[] = $format;
            }
        }
        if(empty($arrIds)){
            Bingo_Log::warning('has no good package ids ');
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrInput = array(
            'table' => Dl_Fmeme_Map::$_table_name,
            'field' => Dl_Fmeme_Map::$_table_fields,
            'append' => ' where meme_id in ('.implode(',', $arrIds).' )',
        );
        $ret = $db->select($arrInput);
        if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
            $data = array();
            $flag = null;
            foreach($ids as $id){
                $map = array();
                foreach($ret['data'] as $value){
                    if($id == $value['meme_id']){
                        $tmp = array(
                            'id' => intval($value['pic_id']),
                            'url' => self::_pid2Url(intval($value['pic_id'])),
                            'thumbnail' => self::_pid2Url(intval($value['pic_id']), 'abpic'),
                            'height' => intval($value['height']),
                            'width' => intval($value['width']),
                        );
                        $map[] = $tmp;
                    }
                }
                if(!empty($map)){
                    $data[$id] = $map;
                }
            }

            return self::_errRet(Tieba_Errcode::ERR_SUCCESS,'',$data);
        }else{
            Bingo_Log::warning('call db failed '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
    }

    /**
     * @param $input
     * @return array
     */
    public static function getHottest($input){
        $num = intval($input['num']);
        if($num > 10 || $num < 1){
            $num = 10;
        }
        
        $redis = Libs_Redis::getInstance(self::REDIS);
        $input = array(
            'key' => self::PREFIX_HOT_CACHE,
        );
        $data = $redis->GET($input);
        if(empty($data)){
            Bingo_Log::pushNotice('hotest','redis_miss');
        }else{
            Bingo_Log::pushNotice('hotest','redis_exists');
            //防止set的时候 expire没有成功
            $ttl = $redis->TTL($input);
            Bingo_Log::pushNotice('hotest_ttl',"$ttl");
            if($ttl == -1){
                $data = $redis->DEL($input);
            }
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $data);
        }
        
        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $timeNow = time();
        $timeLast7days = $timeNow - 86400 * 7;
        
        $redisKey = self::PREFIX_TOP_MEME;
        $arrPackageInput = array(
                'key' => $redisKey,
                );
        $redisRet = $redis->HGETALL($arrPackageInput);
        $intTopCnt = 0;
        if(!$redisRet || $redisRet['err_no']!=0){
            Bingo_Log::warning('hgetall Redis:comm_meme_top_cache  error. input:'.serialize($arrPackageInput));
        }
        else{
            $intTopCnt = count($redisRet['ret'][$redisKey]);
        }
        $topInput = array();
        
        foreach($redisRet as $key => $value){
            $topInput[]= intval($key);
        }
        $topInput =array_reverse($topInput);
        $arrInput = array(
                'table' => Dl_Fmeme_Package::$_table_name,
                'field' => Dl_Fmeme_Package::$_table_fields,
                'cond' => array(
                    'status' => 1,
                    ),
                //            'append' => ' order by download DESC limit '.$num,
                'append' => " and id in (".implode(',', $topInput)." )  limit ".$num ,
                );
        $ret = $db->select($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']){
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrTmp2 = array();
        $arrTmp3 = array();
        foreach($ret['data'] as $value){
             $arrTmp2[$value['id']]= $value;
        }
        foreach($topInput as $id){
            $arrTmp3[]= $arrTmp2[$id];
        }
        $data = $arrTmp3;
        if($intTopCnt< $num){
            $arrInput = array(
                    'table' => Dl_Fmeme_Package::$_table_name,
                    'field' => Dl_Fmeme_Package::$_table_fields,
                    'cond' => array(
                        'status' => 1,
                        ),
                    //            'append' => ' order by download DESC limit '.$num,
                    'append' => " and download > 10 and timestamp > ".$timeLast7days." and timestamp <= ".$timeNow. "   order by download DESC limit ".$num ,
                    );
            $ret = $db->select($arrInput);
            if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']){
                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $dataExtr = $ret['data'];
            $data = array_merge($data,$dataExtr);
        }
        $arrTmp = array();
        foreach($data as $item){
            $arrTmp[$item['id']]= $item;
        }
        $hot  =array_splice($arrTmp,0,$num);
       /* $hot = array();
        $unique = array();
        //获取人工设置推荐内容
        $recommend = self::getHottestRecomm();
        if(Tieba_Errcode::ERR_SUCCESS === $recommend['errno']){
            $arrInput = array(
                    'ids' => $recommend['data'],
                    );
            $ret = self::getPackageByIds($arrInput);
            if(Tieba_Errcode::ERR_SUCCESS === $ret['errno'] && !empty($ret['data']) && is_array($ret['data'])){
                foreach($ret['data'] as $value){
                    $hot[] = $value;
                    $unique[intval($value['id'])] = 1;
                }
            }else{
                Bingo_Log::warning('getPackageByIds error:'.serialize($ret));
            }
        }else{
            Bingo_Log::warning('getHottestRecomm failed '.serialize($recommend));
        }
        if(!empty($hot)){
            //优先吧人工配置的推荐内容放在前面
            foreach ($data as $value){
                if(count($hot) == 6){
                    break;
                }
                if($unique[intval($value['id'])] != 1){
                    $hot[] = $value;
                }
            }
        }else{
            $hot = $data;
        }*/
        $packages = array();
        foreach($hot as $value){
            $packages[] = $value['id'];
        }
        $input = array(
                'ids' => $packages,
                );
        if(empty($packages)){
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS,'', array());
        }

        $ret = self::getMapByPackages($input);
        if(Tieba_Errcode::ERR_SUCCESS === $ret['errno']){
            $mapDetails = $ret['data'];
            foreach($hot as $key=>$value){
                $hot[$key]['pics'] = $mapDetails[$value['id']];
                $hot[$key]['cover'] = self::_pid2Url(intval($hot[$key]['cover']));
            }
        }else{
            Bingo_Log::warning('getMapByPackages errno, input:'.serialize($input).' rets:'.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $res = array();
        foreach($hot as $key => $value){
            if(in_array($value['id'],$packages)){
                $res[] = $value;
            }
        }
        $input = array(
                'key' => self::PREFIX_HOT_CACHE,
                'value' => $res,
                'expire' => self::TIME_OUT,
                );
        $redis->SET($input);
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $res);
    }

    /**
     * @param $input
     * @return array
     */
    public static function getLatest($input){
        $num = intval($input['num']);
        if($num > 6 || $num < 1){
            $num = 6;
        }
        $redis = Libs_Redis::getInstance(self::REDIS);
        $input = array(
            'key' => self::PREFIX_NEW_CACHE,
        );
        $data = $redis->GET($input);
        if(empty($data)){
            Bingo_Log::pushNotice('latest','redis_mis');
        }else{
            Bingo_Log::pushNotice('latest','redis_exist');
            //防止set的时候，expire没有成功
            $ttl = $redis->TTL($input);
            Bingo_Log::pushNotice('hotest_ttl',"$ttl");
            if($ttl == -1){
                $data = $redis->DEL($input);
            }
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $data);
        }

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $time = strtotime("-3 days");
        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'cond' => array(
                'status' => 1,
            ),
            'append' => ' and download > 10 order by timestamp DESC limit '.$num,
            //'append' => ' where timestamp > '.$time.' order by download DESC limit '.$num,
        );
        $ret = $db->select($arrInput);
        $data = $ret['data'];
        if(Tieba_Errcode::ERR_SUCCESS === $ret['errno']){
            $new = array();
            $unique = array();
            //获取人工设置推荐内容
            $recommend = self::getLatestRecomm();
            if(Tieba_Errcode::ERR_SUCCESS === $recommend['errno']){
                $arrInput = array(
                    'ids' => $recommend['data'],
                );
                $ret = self::getPackageByIds($arrInput);
                if(Tieba_Errcode::ERR_SUCCESS === $ret['errno'] && !empty($ret['data']) && is_array($ret['data'])){
                    foreach($ret['data'] as $value){
                        $new[] = $value;
                        $unique[intval($value['id'])] = 1;
                    }
                }else{
                    Bingo_Log::warning('getPackageByIds error:'.serialize($ret));
                }
            }else{
                Bingo_Log::warning('getHottestRecomm failed '.serialize($recommend));
            }
            if(!empty($new)){
                //优先吧人工配置的推荐内容放在前面
                foreach ($data as $value){
                    if(count($new) == 6){
                        break;
                    }
                    if($unique[intval($value['id'])] != 1){
                        $new[] = $value;
                    }
                }
            }else{
                $new = $data;
            }
            foreach($new as $value){
                $packages[] = $value['id'];
            }
            $input = array(
                'ids' => $packages,
            );
            if(empty($packages)){
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS,'', array());
            }
            $ret = self::getMapByPackages($input);
            if(Tieba_Errcode::ERR_SUCCESS === $ret['errno']){
                $mapDetails = $ret['data'];
                foreach($new as $key=>$value){
                    $new[$key]['pics'] = $mapDetails[$value['id']];
                    $new[$key]['cover'] = self::_pid2Url(intval($new[$key]['cover']));
                }
            }else{
                Bingo_Log::warning('getMapByPackages errno, input:'.serialize($input).' rets:'.serialize($ret));
                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            $input = array(
                'key' => self::PREFIX_NEW_CACHE,
                'value' => $new,
                'expire' => self::TIME_OUT,
            );
            $redis->SET($input);
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $new);
        }
        return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
    }

    /**
     * @param $input
     * @return array
     */
    public static function getHottestList($input){
        $pn = intval($input['pn']);
        $rn = intval($input['rn']);
        $pn = $pn < 1 ? 1 : $pn;
        $rn = $rn > 10 ? 10 : $rn;
        $rn = $rn < 1 ? 10 : $rn;
        $start = ($pn - 1) * $rn;
        $isAll = intval($input['all']);

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'cond'  => array(
                'status' => 1,
            ),
            'append' => ' order by download DESC limit '.$start.", $rn",
        );
        if($isAll){
            unset($arrInput['cond']);
        }
        $ret = $db->select($arrInput);
        if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
            if(empty($ret['data'])){
                $data['has_more'] = 0;
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $data);
            }else{
                $data['has_more'] = 1;
                foreach ($ret['data'] as $value){
                    $ids[] = intval($value['id']);
                }
                $pics = self::getMapByPackages(array('ids'=>$ids));
                if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
                    foreach ($ret['data'] as $value){
                        if(!empty($pics['data'][intval($value['id'])])){
                            $value['pics'] = $pics['data'][intval($value['id'])];
                            $value['cover'] = self::_pid2Url(intval($value['cover']));
                            $tmp[] = $value;
                        }
                    }
                    $data['list'] = $tmp;
                    return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $data);
                }else{
                    Bingo_Log::warning('call db failed '.serialize($pics));
                    return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                }
            }
        }else{
            Bingo_Log::warning('call db failed '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
    }

    /**
     * @param $input
     * @return array
     */
    public static function getLatestList($input){
        $pn = intval($input['pn']);
        $rn = intval($input['rn']);
        $pn = $pn < 1 ? 1 : $pn;
        $rn = $rn < 1 ? 10 : $rn;
        $start = ($pn - 1) * $rn;
        $all = intval($input['all']);

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $offSet = $rn + 1;
        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'cond'  => array(
                'status' => 1,
            ),
            'append' => ' and download > 10 and up_status < 2 order by timestamp DESC limit '.$start.",". $offSet,

        );
        if($all){
            unset($arrInput['cond']);
        }
        $ret = $db->select($arrInput);

        if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
            $data['has_more'] = 0;
            if(count($ret['data']) == $offSet ){
                $data['has_more'] = 1;
            }


            //增加置顶表情包,不影响推荐表情包排序,只在首页的时候触发;
            if($pn == 1){
                $arrInput_zd = array(
                    'table' => Dl_Fmeme_Package::$_table_name,
                    'field' => Dl_Fmeme_Package::$_table_fields,
                    'cond'  => array(
                        'status' => 1,
                        'up_status' => 2,
                    ),
                    'append' => ' order by timestamp DESC limit 0,20',

                );
                $res_zd = $db->select($arrInput_zd);

                if($res_zd['errno'] === Tieba_Errcode::ERR_SUCCESS){
                    if(count($res_zd['data']) > 0){
                        $res_merge = array_merge($res_zd['data'], $ret['data']);
                        $ret['data'] = $res_merge;
                    }
                }else{
                    Bingo_Log::warning('call db failed '.serialize($ret));
                    return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                }
            }



            foreach ($ret['data'] as $value){
                $ids[] = intval($value['id']);
            }
            $pics = self::getMapByPackages(array('ids'=>$ids));
            if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
                foreach ($ret['data'] as $value){
                    if(!empty($pics['data'][intval($value['id'])])){
                        $value['pics'] = $pics['data'][intval($value['id'])];
                        $value['cover'] = self::_pid2Url(intval($value['cover']));
                        $tmp[] = $value;
                    }
                }
                $data['list'] = array_slice($tmp,0,$rn);
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $data);
            }else{
                Bingo_Log::warning('call db failed '.serialize($pics));
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
        }else{
            Bingo_Log::warning('call db failed '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
    }

    /**
     * @param $input
     * @return array
     */
    public static function getRandList($input){
        $pn = intval($input['pn']);
        $rn = intval($input['rn']);
        $pn = $pn < 1 ? 1 : $pn;
        $rn = $rn < 1 ? 10 : $rn;
        $start = ($pn - 1) * $rn;
        $all = intval($input['all']);

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $offSet = $rn + 1;
        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'cond'  => array(
                'status' => 1,
            ),
            'append' => ' and up_status in (1,2) limit '.$start.",". $offSet,

        );
        if($all){
            unset($arrInput['cond']);
        }
        $ret = $db->select($arrInput);

        if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
            $data['has_more'] = 0;
            if(count($ret['data']) == $offSet ){
                $data['has_more'] = 1;
            }
            foreach ($ret['data'] as $value){
                $ids[] = intval($value['id']);
            }
            $pics = self::getMapByPackages(array('ids'=>$ids));
            if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
                foreach ($ret['data'] as $value){
                    if(!empty($pics['data'][intval($value['id'])])){
                        $value['pics'] = $pics['data'][intval($value['id'])];
                        $value['cover'] = self::_pid2Url(intval($value['cover']));
                        $tmp[] = $value;
                    }
                }
                $data['list'] = array_slice($tmp,0,$rn);
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $data);
            }else{
                Bingo_Log::warning('call db failed '.serialize($pics));
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
        }else{
            Bingo_Log::warning('call db failed '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
    }

    /**
     * @param $input
     * @return array
     */
    public static function getPackageList($input){
        $pn = intval($input['pn']);
        $rn = intval($input['rn']);
        $pn = $pn < 1 ? 1 : $pn;
        $rn = $rn > 20 ? 20 : $rn;
        $rn = $rn < 1 ? 20 : $rn;
        $start = ($pn - 1) * $rn;
        $status = $input['status'];
        $all = intval($input['all']);

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => array(
                'max(id) as num',
            ),
        );
        $ret = $db->select($arrInput);
        if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
            $total = intval($ret['data'][0]['num']);
        }else{
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'cond'  => array(
                'status' => $status,
                'type' => 1,
            ),
            'append' => ' order by id desc limit '.$start.", $rn",
        );
//        if($all){
//            unset($arrInput['cond']);
//        }
        $ret = $db->select($arrInput);
        if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
            if(empty($ret['data'])){
                $ret['data']['has_more'] = 0;
                return $ret;
            }else{
                $ret['data']['has_more'] = 1;
                foreach ($ret['data'] as $value){
                    $ids[] = intval($value['id']);
                }
                $pics = self::getMapByPackages(array('ids'=>$ids));
                if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
                    foreach ($ret['data'] as $value){
                        if(!empty($pics['data'][intval($value['id'])])){
                            $value['pics'] = $pics['data'][intval($value['id'])];
                            $value['cover'] = self::_pid2Url(intval($value['cover']));
                            $tmp[] = $value;
                        }
                    }
                    $ret = array(
                        'total' => $total,
                        'list' => $tmp,
                    );
                    return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $ret);
                }else{
                    Bingo_Log::warning('call db failed '.serialize($pics));
                    return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                }
            }
        }else{
            Bingo_Log::warning('call db failed '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
    }


    /**
     * @param $input
     * @return array
     */
    public static function getUserPackageList($input){
        $pn = intval($input['pn']);
        $rn = intval($input['rn']);
        $up = $input['up'];
        $up = isset($up)?$up:-1;
        $up = intval($up);

        $pn = $pn < 1 ? 1 : $pn;
        $rn = $rn > 20 ? 20 : $rn;
        $rn = $rn < 1 ? 20 : $rn;

        $start = ($pn - 1) * $rn;
        $status = intval($input['status']);
        $user_id = intval($input['user_id']);
        $name = strval($input['name']);
        $all = intval($input['all']);

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'cond'  => array(
                'status' => $status,
                'type' => 0,
            ),
            'append' => ' order by id desc limit '.$start.", $rn",
        );
        if($user_id !=0){
            $arrInput['cond']['owner'] = $user_id;
        }
        if($name != '' && $name != '0'){
            $arrInput['cond']['name'] = $name;
        }

        if ($up >0 && $up <4) {
            if ($up == 3){
                $arrInput['append'] = " and up_status in (1,2)".$arrInput['append'];
            } else {
                $arrInput['cond']['up_status'] = $up;
            }
        }
        
        if($all){
            unset($arrInput['cond']);
        }

        //count
        $arrCountInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => array(
                'count(id) as cnt',
            ),
            'cond' => $arrInput['cond'],
        );

        if ($up == 3){
            $arrCountInput['append'] = " and up_status in (1,2)";
        }

        //
        $retCount = $db->select($arrCountInput);
        if($retCount['errno'] === Tieba_Errcode::ERR_SUCCESS){
            $total = intval($retCount['data'][0]['cnt']);
        }else{
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        // data

        $ret = $db->select($arrInput);
        if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
            if(empty($ret['data'])){
                $ret['data']['has_more'] = 0;
                return $ret;
            }else{
                $ret['data']['has_more'] = 1;
                foreach ($ret['data'] as $value){
                    $ids[] = intval($value['id']);
                }
                $pics = self::getMapByPackages(array('ids'=>$ids));
                if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
                    foreach ($ret['data'] as $value){
                        if(!empty($pics['data'][intval($value['id'])])){
                            $value['pics'] = $pics['data'][intval($value['id'])];
                            $value['cover'] = self::_pid2Url(intval($value['cover']));
                            $tmp[] = $value;
                        }
                    }
                    $ret = array(
                        'total' => $total,
                        'list' => $tmp,
                    );
                    return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $ret);
                }else{
                    Bingo_Log::warning('call db failed '.serialize($pics));
                    return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                }
            }
        }else{
            Bingo_Log::warning('call db failed '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
    }

    /**
     * @param $input
     * @return array
     */
    public static function getMyForumPackage($input){

        $user_id = intval($input['user_id']);

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'cond'  => array(
                'type' => 2,
                'owner' => $user_id,
            ),
            'append' => ' and status in (1,5) order by timestamp desc  ',
        );


        // data

        $ret = $db->select($arrInput);
        if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
            if(empty($ret['data'])){
                $ret['data']['has_more'] = 0;
                return $ret;
            }else{
                $ret['data']['has_more'] = 1;
                foreach ($ret['data'] as $value){
                    $ids[] = intval($value['id']);
                }
                $pics = self::getMapByPackages(array('ids'=>$ids));
                if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
                    foreach ($ret['data'] as $value){
                        if(!empty($pics['data'][intval($value['id'])])){
                            $value['pics'] = $pics['data'][intval($value['id'])];
                            $value['cover'] = self::_pid2Url(intval($value['cover']));
                            $tmp[] = $value;
                        }
                    }
                    $ret = array(
                        'list' => $tmp,
                    );
                    return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $ret);
                }else{
                    Bingo_Log::warning('call db failed '.serialize($pics));
                    return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                }
            }
        }else{
            Bingo_Log::warning('call db failed '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
    }

    /**
     * @param $input
     * @return array
     */
    public static function getVotePoolForumPackage($input){

        $pn = intval($input['pn']);
        $rn = intval($input['rn']);

        $arrFids = empty($input['fids']) ? array() : $input['fids'];
        $pn = $pn < 1 ? 1 : $pn;
//        $rn = $rn > 20 ? 20 : $rn;
        $rn = $rn < 1 ? 10 : $rn;
        $start = ($pn - 1) * $rn;

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'cond'  => array(
                'type' => 2,
                'status' => self::PKG_STATUS_WAIT_RANK,
            ),
//            'append' => ' order by ext2 desc limit '.$start.", $rn",
        );

        $offset = $rn + 1;
        if(!empty($arrFids)){
            $strFid = implode(",",$arrFids);
            $arrInput['append']  = " and forum_id in ( $strFid ) order by vote_num desc limit ".$start.", $offset";
        } else {
            $arrInput['append'] = ' order by vote_num desc limit '.$start.", $offset";
        }


        // data

        $ret = $db->select($arrInput);
        $has_more = 0;
        if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
            $has_more  = 0;
            if(count($ret['data']) == $offset ){
                $has_more  = 1;
            }

            foreach ($ret['data'] as $value){
                $ids[] = intval($value['id']);
            }
            if(empty($ids)){
                Bingo_Log::warning("no forum_id has packages");
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', array());
            }
            $pics = self::getMapByPackages(array('ids'=>$ids));
            if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
                foreach ($ret['data'] as $value){
                    if(!empty($pics['data'][intval($value['id'])])){
                        $value['pics'] = $pics['data'][intval($value['id'])];
                        $value['cover'] = self::_pid2Url(intval($value['cover']));
                        $tmp[] = $value;
                    }
                }
                $dataOut = array_slice($tmp,0,$rn);
                $ret = array(
                    'list' => $dataOut,
                    'has_more' =>$has_more,
                );
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $ret);
            }else{
                Bingo_Log::warning('call db failed '.serialize($pics));
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
        }else{
            Bingo_Log::warning('call db failed '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
    }


    /**
     * @param $input
     * @return array
     */
    public static function getVotePoolForumAvatar($input){

        $pn = intval($input['pn']);
        $rn = intval($input['rn']);

        $arrFids = empty($input['fids']) ? array() : $input['fids'];
        $pn = $pn < 1 ? 1 : $pn;
//        $rn = $rn > 20 ? 20 : $rn;
        $rn = $rn < 1 ? 10 : $rn;
        $start = ($pn - 1) * $rn;

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'cond'  => array(
                'type' => 2,
                'status' => self::PKG_STATUS_WAIT_RANK,
            ),
//            'append' => ' order by ext2 desc limit '.$start.", $rn",
        );

        $offset = $rn + 1;
        if(!empty($arrFids)){
            $strFid = implode(",",$arrFids);
            $arrInput['append']  = " and forum_id in ( $strFid ) order by vote_num desc limit ".$start.", $offset";
        } else {
            $arrInput['append'] = ' order by vote_num desc limit '.$start.", $offset";
        }


        // data

        $ret = $db->select($arrInput);
        $has_more = 0;
        if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
            $has_more  = 0;
            if(count($ret['data']) == $offset ){
                $has_more  = 1;
            }

            foreach ($ret['data'] as $value){
                $ids[] = intval($value['id']);
            }
            if(empty($ids)){
                Bingo_Log::warning("no forum_id has packages");
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', array());
            }

            foreach ($ret['data'] as $value){
                $tmp[] = $value;
            }
            $dataOut = array_slice($tmp,0,$rn);
            $retOut = array(
                'list' => $dataOut,
                'has_more' =>$has_more,
            );
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $retOut);
        }else{
            Bingo_Log::warning('call db failed '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
    }

    /**
     * @param $input
     * @return array
     */
    public static function getVoteFinishForumPackage($input){

        $pn = intval($input['pn']);
        $rn = intval($input['rn']);
        $pn = $pn < 1 ? 1 : $pn;
//        $rn = $rn > 20 ? 20 : $rn;
        $rn = $rn < 1 ? 10 : $rn;
        $start = ($pn - 1) * $rn;

        $arrFids = empty($input['fids']) ? array() : $input['fids'];

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }


        $offset = $rn + 1;
        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'cond'  => array(
                'type' => 2,
                'status' => self::PKG_STATUS_PASS,
            ),
//            'append' => ' order by download desc limit '.$start.", $offset",
        );

        if(!empty($arrFids)){
            $strFid = implode(",",$arrFids);
            $arrInput['append']  = " and forum_id in ( $strFid ) order by download desc limit ".$start.", $offset";
        } else {
            $arrInput['append'] = ' order by download desc limit '.$start.", $offset";
        }


        // data

        $ret = $db->select($arrInput);
        $has_more = 0;
        if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){

            if(empty($ret['data'])){
                $outData['has_more'] = 0;
                $outData['list'] = array();
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '',$outData);
            }else{
                $has_more = 0;
                if(count($ret['data']) == $offset ){
                    $has_more = 1;
                }

                foreach ($ret['data'] as $value){
                    $ids[] = intval($value['id']);
                }
                if(empty($ids)){
                    Bingo_Log::warning("no forum_id has packages");
                    return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', array());
                }
                $pics = self::getMapByPackages(array('ids'=>$ids));
                if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
                    foreach ($ret['data'] as $value){
                        if(!empty($pics['data'][intval($value['id'])])){
                            $value['pics'] = $pics['data'][intval($value['id'])];
                            $value['cover'] = self::_pid2Url(intval($value['cover']));
                            $tmp[] = $value;
                        }
                    }
                    $dataOut = array_slice($tmp,0,$rn);
                    $ret = array(
                        'list' => $dataOut,
                        'has_more' =>$has_more,
                    );
                    return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $ret);
                }else{
                    Bingo_Log::warning('call db failed '.serialize($pics));
                    return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                }
            }
        }else{
            Bingo_Log::warning('call db failed '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
    }
    /**
     * @param $input
     * @return array
     */
    public static function processVotePackage($input){

        $intPkgId = $input['pkg_id'];
        $intUserId = $input['user_id'];

        // step 0 param check

        if($intPkgId == 0 || $intUserId == 0){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        // step 1 add 1 for package vote num
        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $sqlUpdate = 'update '. Dl_Fmeme_Package::$_table_name.' set vote_num=vote_num+1 where id='.$intPkgId;
        $ret = $db->query($sqlUpdate);
        if(!$ret){
            Bingo_Log::warning('update db error. sql:'.$sqlUpdate);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        // step 2 set user_id & pkg_id has vote

        $today = date("Ymd");
        $redis = Libs_Redis::getInstance(self::REDIS);

        $redisVoteKey = self::MEME_PKG_VOTE_PREFIX.$today;

        $arrVoteInput = array(

            'key' => $redisVoteKey,
            'field' => $intUserId."_".$intPkgId,
            'value' => 1,
        );

        $redisRet = $redis->HSET($arrVoteInput);

        if(!$redisRet){
            Bingo_Log::warning('hsetToRedis  error. input:'.serialize($arrVoteInput));
        }

        // step 3 add 1 for user_id vote_num

        $arrIncrInput = array(
            'key' => $redisVoteKey,
            'field' => self::MEME_VOTE_UID_PREFIX.$intUserId,
            'step' => 1,
        );

        $redisRetIncr = $redis->HINCRBY($arrIncrInput);
        if(!$redisRetIncr){
            Bingo_Log::warning('HINCRBY  error. input:'.serialize($arrIncrInput));
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', array());

    }


    /**
     * @param $input
     * @return array
     */
    public static function mgetUidPkgIdVote($input){
        $intUserId = $input['user_id'];
        $arrPkgId = $input['pkg_ids'];
        // step 0 param check

        if($intUserId == 0 || count($arrPkgId) == 0){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $arrFiled = array();

        foreach ($arrPkgId as $pkg_id){
            $temStr = $intUserId."_".$pkg_id;
            $arrFiled[] = $temStr;
        }
        $today = date("Ymd");
        $redis = Libs_Redis::getInstance(self::REDIS);

        $redisKey = self::MEME_PKG_VOTE_PREFIX.$today;

        $arrUidInput = array(
            'redis' => self::REDIS,
            'key' => $redisKey,
            'fields' =>  $arrFiled,
        );
        $redisRet = $redis->HMGET($arrUidInput);
        if(!$redisRet){
            Bingo_Log::warning('hmget  error. input:'.serialize($arrUidInput));
        }

        $arrVote = array(
            'vote_pkg_ids' => is_null($redisRet) ? array() : $redisRet,
        );

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $arrVote);


    }


    /**
     * @param $input
     * @return array
     */
    public static function getUserVoteNum($input){
        $intUserId = $input['user_id'];

        // step 0 param check

        if($intUserId == 0){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }
        $today = date("Ymd");
        $redis = Libs_Redis::getInstance(self::REDIS);

        $redisKey = self::MEME_PKG_VOTE_PREFIX.$today;

        $arrUidInput = array(
            'redis' => self::REDIS,
            'key' => $redisKey,
            'field' =>  self::MEME_VOTE_UID_PREFIX.$intUserId,
        );
        $redisRet = $redis->HGET($arrUidInput);
        if(!$redisRet){
            Bingo_Log::warning('hgetToRedis  error. input:'.serialize($arrUidInput));
        }

        $arrVote = array(
            'vote_num' => is_null($redisRet) ? 0 : $redisRet,
        );

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $arrVote);


    }


    /**
     * @param $input
     * @return array
     */
    public static function checkUserVoteStatus($input){
        $intUserId = $input['user_id'];
        $intPkgId = $input['pkg_id'];
        // step 0 param check

        if($intUserId == 0 ||$intPkgId == 0 ){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }
        $today = date("Ymd");
        $redis = Libs_Redis::getInstance(self::REDIS);

        $redisKey = self::MEME_PKG_VOTE_PREFIX.$today;

        $arrUidInput = array(
            'redis' =>  self::REDIS,
            'key'   =>  $redisKey,
            'field' =>  $intUserId."_".$intPkgId,
        );
        $redisRet = $redis->HGET($arrUidInput);
        if(!$redisRet){
            Bingo_Log::warning('hgetToRedis  error. input:'.serialize($arrUidInput));
        }

        $arrVote = array(
            'status' => is_null($redisRet) ? 0 : $redisRet,
        );

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $arrVote);


    }

    /**
     * @param $input
     * @return array
     */
    public static function getForumPackageList($input){
        $pn = intval($input['pn']);
        $rn = intval($input['rn']);
        $pn = $pn < 1 ? 1 : $pn;
//        $rn = $rn > 20 ? 20 : $rn;
        $rn = $rn < 1 ? 10 : $rn;
        $start = ($pn - 1) * $rn;
        $status = intval($input['status']);
        $forum_id = intval($input['forum_id']);
        $name = strval($input['name']);
        $all = intval($input['all']);

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'cond'  => array(
                'status' => $status,
                'type' => 2,
            ),
            'append' => ' order by id desc limit '.$start.", $rn",
        );
        if($forum_id !=0){
            $arrInput['cond']['forum_id'] = $forum_id;
        }
        if($name != '' && $name != '0'){
            $arrInput['cond']['name'] = $name;
        }
        if($all){
            unset($arrInput['cond']);
        }

        //count
        $arrCountInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => array(
                'count(id) as cnt',
            ),
            'cond' => $arrInput['cond'],
        );
        $retCount = $db->select($arrCountInput);
        if($retCount['errno'] === Tieba_Errcode::ERR_SUCCESS){
            $total = intval($retCount['data'][0]['cnt']);
        }else{
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        // data

        $ret = $db->select($arrInput);
        if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
            if(empty($ret['data'])){
                $ret['data']['has_more'] = 0;
                return $ret;
            }else{
                $ret['data']['has_more'] = 1;
                foreach ($ret['data'] as $value){
                    $ids[] = intval($value['id']);
                }
                $pics = self::getMapByPackages(array('ids'=>$ids));
                if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
                    foreach ($ret['data'] as $value){
                        if(!empty($pics['data'][intval($value['id'])])){
                            $value['pics'] = $pics['data'][intval($value['id'])];
                            $value['cover'] = self::_pid2Url(intval($value['cover']));
                            $tmp[] = $value;
                        }
                    }
                    $ret = array(
                        'total' => $total,
                        'list' => $tmp,
                    );
                    return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $ret);
                }else{
                    Bingo_Log::warning('call db failed '.serialize($pics));
                    return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                }
            }
        }else{
            Bingo_Log::warning('call db failed '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
    }

    /**
     * @param $input
     * @return array
     */
    public static function getForumPkgByForumId($input){


        $status = intval($input['status']);
        $forum_id = intval($input['forum_id']);
        $limit = intval($input['limit']);
        $pn = intval($input['pn']);

        // checkout redis_cache
        $redis = Libs_Redis::getInstance(self::REDIS);
        $redisKey = self::FORUM_PKG_PREFIX;

        $arrGetRedisInput = array(
            'redis' => self::REDIS,
            'key' => $redisKey,
            'field' => $forum_id."_".$limit,
        );

        $redisRet = $redis->HGET($arrGetRedisInput);
        if(!$redisRet){
            Bingo_Log::warning('hgetToRedis  error. input:'.serialize($arrGetRedisInput));
        }

        $redisForumData = $redisRet;


        if(!empty($redisForumData)){
            Bingo_Log::warning('hit cache !!! key:'.serialize($redisKey));
            $ret = array(
                'list' => $redisForumData,
            );
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $ret);

        }

        // if not in cache,read db

        $start = ($pn - 1) * $limit;
        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'cond'  => array(
                'status' => $status,
                'type' => 2,
                'forum_id' => intval($forum_id),
            ),
            'append' => " order by id desc limit ".$limit,
        );

        if($pn > 1){
            $arrInput['append'] = " order by timestamp desc limit ".$start." , ".$limit;
        }

        // data

        $ret = $db->select($arrInput);
        if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
            if(empty($ret['data'])){
                $ret['data'] = array();
                // set into redis
                $arrUidInput = array(
                    'redis' => self::REDIS,
                    'key' => $redisKey,
                    'field' => $forum_id."_".$limit,
                    'value' => array(
                        array(
                            'id' => 0,
                            'cover' => '',
                        )
                    ),
                    'expire' => self::FORUM_PKG_TIME_OUT,
                );
                $redisRet = $redis->HSET($arrUidInput);
                if(!$redisRet){
                    Bingo_Log::warning('hsetToRedis  error. input:'.serialize($arrUidInput));
                }
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', array());
            }else{

                foreach ($ret['data'] as $value){
                    $ids[] = intval($value['id']);
                }
                $pics = self::getMapByPackages(array('ids'=>$ids));
                if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
                    foreach ($ret['data'] as $value){
                        if(!empty($pics['data'][intval($value['id'])])){
                            $value['pics'] = $pics['data'][intval($value['id'])];
                            $value['cover'] = self::_pid2Url(intval($value['cover']));
                            $tmp[] = $value;
                        }
                    }
                    $ret = array(
                        'list' => $tmp,
                    );


                    // set into redis

                    $arrUidInput = array(
                        'redis' => self::REDIS,
                        'key' => $redisKey,
                        'field' => $forum_id."_".$limit,
                        'value' => $tmp,
                        'expire' => self::FORUM_PKG_TIME_OUT,
                    );
                    $redisRet = $redis->HSET($arrUidInput);
                    if(!$redisRet){
                        Bingo_Log::warning('hsetToRedis  error. input:'.serialize($arrUidInput));
                    }

                    return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $ret);
                }else{
                    Bingo_Log::warning('call db failed '.serialize($pics));
                    return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                }
            }
        }else{
            Bingo_Log::warning('call db failed '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
    }


    /**
     * @param $input
     * @return array
     */
    public static function mgetForumPkg($input){

        $status = intval($input['status']);
        $forum_ids = $input['forum_ids'];

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $strFid = implode(",",$forum_ids);
        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'cond'  => array(
                'status' => $status,
                'type' => 2,
            ),
            'append' => " and forum_id in ( ".$strFid." )",
        );

        // data

        $ret = $db->select($arrInput);
        if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
            if(empty($ret['data'])){
                $ret['data'] = array();
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $ret);
            }else{

                foreach ($ret['data'] as $value){
                    $ids[] = intval($value['id']);
                }
                $pics = self::getMapByPackages(array('ids'=>$ids));
                if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
                    foreach ($ret['data'] as $value){
                        if(!empty($pics['data'][intval($value['id'])])){
                            $value['pics'] = $pics['data'][intval($value['id'])];
                            $value['cover'] = self::_pid2Url(intval($value['cover']));
                            $tmp[] = $value;
                        }
                    }
                    $ret = array(
                        'list' => $tmp,
                    );
                    return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $ret);
                }else{
                    Bingo_Log::warning('call db failed '.serialize($pics));
                    return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                }
            }
        }else{
            Bingo_Log::warning('call db failed '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
    }
    /**
     * @param $input
     * @return array
     */
    public static function myPackage($input){
        $pn = intval($input['pn']);
        $rn = intval($input['rn']);
        $pn = $pn < 1 ? 1 : $pn;
        $rn = $rn > 10 ? 10 : $rn;
        $rn = $rn < 1 ? 10 : $rn;
        $start = ($pn - 1) * $rn;
        $owner = intval($input['owner']);
        if($owner < 1){
            Bingo_Log::warning('params error:'.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'append' => ' where owner = '.$owner.' limit '.$start.", $rn",
        );
        $ret = $db->select($arrInput);
        if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
            if(empty($ret['data'])){
                $ret['data']['has_more'] = 0;
                return $ret;
            }else{
                $ret['data']['has_more'] = 1;
                foreach ($ret['data'] as $value){
                    $ids[] = intval($value['id']);
                }
                $pics = self::getMapByPackages(array('ids'=>$ids));
                if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
                    foreach ($ret['data'] as $value){
                        if(!empty($pics['data'][intval($value['id'])])){
                            $value['pics'] = $pics['data'][intval($value['id'])];
                            $tmp[] = $value;
                        }
                    }
                    return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $tmp);
                }else{
                    Bingo_Log::warning('call db failed '.serialize($pics));
                    return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                }
            }
        }else{
            Bingo_Log::warning('call db failed '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
    }

    /**
     * @param $input
     * @return array
     */
    public static function addLatestRecomm($input){
        $id = intval($input['id']);
        if($id < 1){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => array(
                'id',
            ),
            'cond' => array(
                'id' => $id,
            ),
        );
        $ret = $db->select($arrInput);
        if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $output = $ret['data'][0];
        if(empty($output)){
            Bingo_Log::warning('package not exists');
            return self::_errRet(Tieba_Errcode::FORUM_ID_NOT_EXIST, '表情包不存在');
        }
        $redis = Libs_Redis::getInstance(self::REDIS);
        if(empty($redis)){
            Bingo_Log::warning('redis create failed');
            return self::_errRet(Tieba_Errcode::ERR_INIT_REDIS);
        }
        $arrInput = array(
            'key' => self::PREFIX_NEW_MEME,
            'member' => $id,
        );
        $ret = $redis->SADD($arrInput);
        if($ret){
            $input = array(
                'key' => self::PREFIX_NEW_CACHE,
            );
            $redis->DEL($input);
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        else{
            Bingo_Log::warning('add redis faild! input:'.serialize($arrInput).' output:'.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
    }

    /**
     * @param $input
     * @return array
     */
    public static function delLatestRecomm($input){
        $id = intval($input['id']);
        if($id < 1){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $redis = Libs_Redis::getInstance(self::REDIS);
        if(empty($redis)){
            Bingo_Log::warning('redis create failed');
            return self::_errRet(Tieba_Errcode::ERR_INIT_REDIS);
        }
        $arrInput = array(
            'key' => self::PREFIX_NEW_MEME,
            'member' => $id,
        );
        $ret = $redis->SREM($arrInput);
        if($ret){
            $input = array(
                'key' => self::PREFIX_NEW_CACHE,
            );
            $redis->DEL($input);
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        else{
            Bingo_Log::warning('del redis faild! input:'.serialize($arrInput).' output:'.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
    }

    /**
     * @param $input
     * @return array
     */
    public static function addHottestRecomm($input){
        $id = intval($input['id']);
        if($id < 1){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => array(
                'id',
            ),
            'cond' => array(
                'id' => $id,
            ),
        );
        $ret = $db->select($arrInput);
        if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $output = $ret['data'][0];
        if(empty($output)){
            Bingo_Log::warning('package not exists');
            return self::_errRet(Tieba_Errcode::FORUM_ID_NOT_EXIST, '表情包不存在');
        }
        $redis = Libs_Redis::getInstance(self::REDIS);
        if(empty($redis)){
            Bingo_Log::warning('redis create failed');
            return self::_errRet(Tieba_Errcode::ERR_INIT_REDIS);
        }
        $arrInput = array(
            'key' => self::PREFIX_HOT_MEME,
            'member' => $id,
        );
        $ret = $redis->SADD($arrInput);
        if($ret){
            $input = array(
                'key' => self::PREFIX_HOT_CACHE,
            );
            $redis->DEL($input);
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        else{
            Bingo_Log::warning('add redis faild! input:'.serialize($arrInput).' output:'.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
    }

    /**
     * @param $input
     * @return array
     */
    public static function delHottestRecomm($input){
        $id = intval($input['id']);
        if($id < 1){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }

        $redis = Libs_Redis::getInstance(self::REDIS);
        if(empty($redis)){
            Bingo_Log::warning('redis create failed');
            return self::_errRet(Tieba_Errcode::ERR_INIT_REDIS);
        }
        $arrInput = array(
            'key' => self::PREFIX_HOT_MEME,
            'member' => $id,
        );
        $ret = $redis->SREM($arrInput);
        if($ret){
            $input = array(
                'key' => self::PREFIX_HOT_CACHE,
            );
            $redis->DEL($input);
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        else{
            Bingo_Log::warning('del redis faild! input:'.serialize($arrInput).' output:'.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
    }

    /**
     * @return array
     */
    public static function getHottestRecomm(){

        $redis = Libs_Redis::getInstance(self::REDIS);
        if(empty($redis)){
            Bingo_Log::warning('redis create failed');
            return self::_errRet(Tieba_Errcode::ERR_INIT_REDIS);
        }
        $arrInput = array(
            'key' => self::PREFIX_HOT_MEME,
        );
        $ret = $redis->SMEMBERS($arrInput);
        if($ret!==false){
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS,'', $ret);
        }
        else{
            Bingo_Log::warning('del redis faild! input:'.serialize($arrInput).' output:'.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
    }

    /**
     * @return array
     */
    public static function getLatestRecomm(){

        $redis = Libs_Redis::getInstance(self::REDIS);
        if(empty($redis)){
            Bingo_Log::warning('redis create failed');
            return self::_errRet(Tieba_Errcode::ERR_INIT_REDIS);
        }
        $arrInput = array(
            'key' => self::PREFIX_NEW_MEME,
        );
        $ret = $redis->SMEMBERS($arrInput);
        if($ret!==false){
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS,'', $ret);
        }
        else{
            Bingo_Log::warning('del redis faild! input:'.serialize($arrInput).' output:'.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
    }

    /**
     * 设置置顶状态标记
     * @param $input
     * @return array
     */
    public static function setPackageUpstatus($input) {
        $intPackageId = intval($input['id']);
        $intUpstatus  = intval($input['upstatus']);
        if ($intPackageId <= 0 || $intUpstatus < 0 || $intUpstatus > 2) {
            Bingo_Log::warning(__FUNCTION__ . ' params error: ' . serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }
        $db  = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        $ret = $db->update(array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => array(
                'up_status' => $intUpstatus,
                'timestamp' => time(),
            ),
            'cond'  => array(
                'id' => $intPackageId,
            ),
        ));
        if (!$ret || $ret['errno'] != 0) {
            Bingo_Log::warning(__FUNCTION__ . ' db query fail : ' . $ret['errmsg']);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $input
     * @return array
     */
    public static function setPackageUp($input){
        $intPackageId = intval($input['id']);

        if($intPackageId <= 0){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }
        $redis = Libs_Redis::getInstance(self::REDIS);

        $redisKey = self::PREFIX_TOP_MEME;
        $intTimestamp = intval(time());
        $arrPackageInput = array( 
                'key' => $redisKey,
                'field' =>  $intPackageId,
                'value' => $intTimestamp,
                );
        $redisRet = $redis->HSETNX($arrPackageInput);
        if(!$redisRet || $redisRet['err_no']!=0){
            Bingo_Log::warning('hsetRedis:comm_meme_top_cache  error. input:'.serialize($arrPackageInput));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        else{
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
    }
    /**
     * @param $input
     * @return array
     */
    public static function unsetPackageUp($input){
        $intPackageId = intval($input['id']);

        if($intPackageId <= 0){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }
        $redis = Libs_Redis::getInstance(self::REDIS);

        $redisKey = self::PREFIX_TOP_MEME;
        $arrPackageInput = array(
                'key' => $redisKey,
                'field' => $intPackageId,
                    
                );
        $redisRet = $redis->HDEL($arrPackageInput);
        if(!$redisRet || $redisRet['err_no']!=0){
            Bingo_Log::warning('hDEL Redis:comm_meme_top_cache  error. input:'.serialize($arrPackageInput));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        else{
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
    }
    /**
     * @param $input
     * @return array
     */
    public static function getPackageUpStatus($input){
        $intPackageId = intval($input['id']);

        if($intPackageId <= 0){
            Bingo_Log::warning(__FUNCTION__.' params error: '.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }
        $redis = Libs_Redis::getInstance(self::REDIS);

        $redisKey = self::PREFIX_TOP_MEME;
        $arrPackageInput = array(
                'key' => $redisKey,
                'field' =>  $intPackageId,
                );
        $redisRet = $redis->HEXISTS($arrPackageInput);
        if(!$redisRet || $redisRet['err_no']!=0){
            Bingo_Log::warning('hexist Redis:comm_meme_top_cache  error. input:'.serialize($arrPackageInput));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        else{
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS,'',$redisRet['ret'][$redisKey]);
        }
    }
    /**
     * @param $input
     * @return array
     */
    public static function getRedisKey($input){
        $redis_name = $input['reids'];
        $key = $input['key'];
        if(empty($key)){
            Bingo_Log::warning('params error:'.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }
        if(empty($redis_name)){
            $redis_name = self::REDIS;
        }
        $redis = Libs_Redis::getInstance($redis_name);
        if(empty($redis)){
            Bingo_Log::warning('redis create failed');
            return self::_errRet(Tieba_Errcode::ERR_INIT_REDIS);
        }
        $arrInput = array(
            'key' => $key,
        );
        $ret = $redis->GET($arrInput);
        if($ret!==false){
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $ret);
        }
        else{
            Bingo_Log::warning('get redis faild! input:'.serialize($arrInput).' output:'.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
    }


    /**
     * @param $input
     * @return array
     */
    public static function getRedisTTL($input){
        $redis_name = $input['reids'];
        $key = $input['key'];
        if(empty($key)){
            Bingo_Log::warning('params error:'.serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_INPUT_PARAM);
        }
        if(empty($redis_name)){
            $redis_name = self::REDIS;
        }
        $redis = Libs_Redis::getInstance($redis_name);
        if(empty($redis)){
            Bingo_Log::warning('redis create failed');
            return self::_errRet(Tieba_Errcode::ERR_INIT_REDIS);
        }
        $arrInput = array(
            'key' => $key,
        );
        $ret = $redis->TTL($arrInput);
        if($ret!==false){
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $ret);
        }
        else{
            Bingo_Log::warning('get redis faild! input:'.serialize($arrInput).' output:'.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
    }


    /**
     * @param $input
     * @return array
     */
    public static function getForumRecommendPkgByForumId($input){

        //
        $forum_id = intval($input['forum_id']);
        $limit = 1;
        $pn = 1;

        // checkout redis_cache
        $redis = Libs_Redis::getInstance(self::REDIS);
        $redisKey = self::FORUM_RECOMMEND_PKG_PREFIX;

        $arrGetRedisInput = array(
            'redis' => self::REDIS,
            'key' => $redisKey,
            'field' => $forum_id,
        );

        $redisRet = $redis->HGET($arrGetRedisInput);
        if(!$redisRet){
            Bingo_Log::warning('hgetToRedis  error. input:'.serialize($arrGetRedisInput));
        }

        $redisForumData = $redisRet;
        if(!empty($redisForumData)){
            Bingo_Log::warning('hit cache !!! key:'.serialize($redisKey));
            $ret = array(
                'list' => $redisForumData,
            );
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $ret);

        }

        // if not in cache,read db
        $start = ($pn - 1) * $limit;
        $db = Libs_ShareDb::getInstance(self::DATABASE_NAME);
        if(empty($db)){
            Bingo_Log::warning('getDb failed.function:'.__FUNCTION__);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrInput = array(
            'table' => Dl_Fmeme_Package::$_table_name,
            'field' => Dl_Fmeme_Package::$_table_fields,
            'cond'  => array(
                'type' => 2,
                'forum_id' => intval($forum_id),
            ),
            'append' => "AND status IN (1,5) order by id desc limit ".$limit,
        );


        // data
        $ret = $db->select($arrInput);
        if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
            if(empty($ret['data'])){
                $ret['data'] = array();
                // set into redis
                $arrUidInput = array(
                    'redis' => self::REDIS,
                    'key' => $redisKey,
                    'field' => $forum_id,
                    'value' => array(
                        array(
                            'id' => 0,
                            'cover' => '',
                        )
                    ),
                    'expire' => self::FORUM_PKG_TIME_OUT,
                );
                $redisRet = $redis->HSET($arrUidInput);
                if(!$redisRet){
                    Bingo_Log::warning('hsetToRedis  error. input:'.serialize($arrUidInput));
                }
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', array());
            }else{

                foreach ($ret['data'] as $value){
                    $ids[] = intval($value['id']);
                }
                $pics = self::getMapByPackages(array('ids'=>$ids));
                if($ret['errno'] === Tieba_Errcode::ERR_SUCCESS){
                    foreach ($ret['data'] as $value){
                        if(!empty($pics['data'][intval($value['id'])])){
                            $value['pics'] = $pics['data'][intval($value['id'])];
                            $value['cover'] = self::_pid2Url(intval($value['cover']));
                            $tmp[] = $value;
                        }
                    }
                    $ret = array(
                        'list' => $tmp,
                    );


                    // set into redis

                    $arrUidInput = array(
                        'redis' => self::REDIS,
                        'key' => $redisKey,
                        'field' => $forum_id,
                        'value' => $tmp,
                        'expire' => self::FORUM_PKG_TIME_OUT,
                    );
                    $redisRet = $redis->HSET($arrUidInput);
                    if(!$redisRet){
                        Bingo_Log::warning('hsetToRedis  error. input:'.serialize($arrUidInput));
                    }

                    return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $ret);
                }else{
                    Bingo_Log::warning('call db failed '.serialize($pics));
                    return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                }
            }
        }else{
            Bingo_Log::warning('call db failed '.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
    }

}
