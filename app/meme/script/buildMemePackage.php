<?php
/**
 * Created by PhpStorm.
 * User: liukaining
 * Date: 2017/7/6
 * Time: 15:19
 */
require_once 'base.php';

class BuildMemePackage extends OnlineTask {

    //zip包下载目录
    private static $_download_dir = "pic_download/";

    const MEME_NAME_BIG_PREFIX = 'b';
    const MEME_NAME_SMALL_PREFIX = 's';
    const MEME_NAME_SURFIX = '.jpg';

    const MEME_LIMIT_NUM = 1;

    const PKG_STATUS_WAIT_ZIP = 3;
    const PKG_STATUS_WAIT_AUDIT = 0;

    private $arrAllFile = array();

    /**
     * @brief 初始化
     * @param void
     * @return void
     **/
    protected function _init() {
        echo date('Y-m-d H:i:s') . "\n";
        $strPath = dirname(__FILE__);
        self::$_download_dir= $strPath . '/' . self::$_download_dir;

        if (!is_dir(self::$_download_dir)) {
            $bol = mkdir(self::$_download_dir, 0777, true);
            if ($bol == false) {
                echo "mkdir " . self::$_download_dir . " failed\n";
                exit;
            }
        }


    }

    /**
     * @param $url
     * @param $cartoon_id
     * @param $inner_num
     * @return bool|int
     */
    private  function downloadPic($arrInput) {
        if (!is_string($arrInput['url'])) {
            echo "param is invaild" . var_export($arrInput['url'], true) . "\n";
            return false;
        }
        $url  = $arrInput['url'];
        $name = $arrInput['name'];
        $flag = $arrInput['flag'];

        //需要设置最大下载大小
        $option   = array(
            'max_response_size' => 10485760, //10M
        );
        $objFetch = Orp_FetchUrl::getInstance($option);
        $strContent = $objFetch->get($url);
        if (empty($strContent)) {
            echo "fetch url failed: {$url}\n";
            return false;
        }
        if($flag == self::MEME_NAME_BIG_PREFIX){
            $prfix = self::MEME_NAME_BIG_PREFIX."_";
        } else if($flag == self::MEME_NAME_SMALL_PREFIX){
            $prfix = self::MEME_NAME_SMALL_PREFIX."_";
        }
        $strFileName = self::$_download_dir."/".$prfix.$name.self::MEME_NAME_SURFIX;
        $bol = file_put_contents($strFileName, $strContent);
        return $bol;
    }

    /**
     * @return bool
     */
    public function _run() {
        //  STEP 1 get wait_zip package id, each time MEME_LIMIT_NUM

        $arrWaitZipOut = $this->getWaitZipPkgIdAndPicIds();
        //  STEP 2 get all pic_id
        $intPkgId = (int)$arrWaitZipOut['data']['pkg_id'];
        $arrPics = $arrWaitZipOut['data']['pics'];
        if($intPkgId == 0){
            $this->_log('no wait_zip!!!! ');
            return true;
        }
        //  STEP 3 get pic_url by pic_id
        $arrPicInfoOut = $this->getPicInfo($arrPics);
        $arrPicInfo = $arrPicInfoOut['data']['pic_info'];

        //  STEP 4 orpFetchUrl download pics, change name, update name into db
        $arrProcessDownloadOut = $this->processDownload($arrPicInfo,$intPkgId);
        $arrZipInfo = $arrProcessDownloadOut['data']['dir_name'];
        $strCovarName = $arrProcessDownloadOut['data']['covername'];
        //  STEP 5 build zip, upload ossp platform,get ZIP url
        $arrProcessBuildZip = $this->processBuildZipNew($arrZipInfo,$intPkgId);
        $strZipUrl = $arrProcessBuildZip['data']['zip_url'];
        $arrChangeInput = array(
            'id' => $intPkgId,
            'status' => self::PKG_STATUS_WAIT_AUDIT,
            'url' => $strZipUrl,
            'covername' => $strCovarName,
        );
        //  STEP 6 change status from wait_zip(3) TO wait_audit (0)
        $arrChangeStatusOut =  $this->changeStatus($arrChangeInput);

        // STEP 7 delete file

//        if(is_dir(self::$_download_dir)){
//            $cmdDel = "rm -rf ".self::$_download_dir;
//            exec($cmdDel);
//        }

        $this->delAllFile(self::$_download_dir);
        $this->_log('finished');


    }

    /**
     * @param $dirname
     * @return array
     */
    private function getAllFilesName($dirname){

        if(is_dir($dirname)){
            if($dir = opendir($dirname)){
                while (($file = readdir($dir)) !== false){
                    if((is_dir($dirname."/".$file)) && $file != "." && $file != ".."){
                        $this->arrAllFile[] = $dirname."/".$file;
                        $this->getAllFilesName($file);
                    } else {
                        if($file != "." && $file != ".."){
                            $this->arrAllFile[] = $file;
                        }
                    }
                }
                closedir($dirname);
            }
        }

        return $this->arrAllFile;
    }

    /**
     * @param $dirName
     * @return bool
     */
    private function delAllFile($dir){
        if(is_dir($dir)){
            foreach(scandir($dir) as $row){
                if($row == '.' || $row == '..'){
                    continue;
                }
                $path = $dir .'/'. $row;
                if(filetype($path) == 'dir'){
                    $this->delAllFile($path);
                }else{
                    unlink($path);
                }
            }
            rmdir($dir);
        }else{
            return false;
        }
    }
    /**
     * @param $url
     * @param $cartoon_id
     * @param $inner_num
     * @return bool|int
     */
    private function getWaitZipPkgIdAndPicIds() {

        $arrParams = array();
        $arrRet = $this->_callService('meme', 'getWaitZipPkgIdAndPicIds', $arrParams);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning("call service meme::getWaitZipPkgIdAndPicIds fail, errno={$arrRet['errno']}");
            return;
        }

        $arrOutData['data']['pkg_id'] = $arrRet['data']['pkg_id'];
        $arrOutData['data']['pics'] = $arrRet['data']['pics'];;
        return $arrOutData;
    }


    /**
     * @param $intPicId
     * @param string $type
     * @return mixed
     */
    private  function pid2Url($intPicId, $type = 'pic' ){
        $arrParams[] = array(
            'pic_id' => $intPicId,
            'product_name' => 'forum',
            'pic_spec' => $type,
            'domain' => 'imgsrc.baidu.com',
        );

        $arrPicUrls = Bd_Pic::pid2Url($arrParams, false);
        return $arrPicUrls['resps'][0];
    }
    /**
     * @param $arrInput
     * @return array
     */
    private  function getPicInfo($arrInput) {
        $arrPicIds = $arrInput;
        $arrPicInfo = array();

        foreach ($arrPicIds as $pid){
            $tmpInfo['pic_id'] = $pid;
            $tmpInfo['url'] = $this->pid2Url($pid);
            $tmpInfo['thumbnail'] = $this->pid2Url($pid, 'abpic');
            $arrPicInfo[$pid] = $tmpInfo;
        }
        $arrOut['data']['pic_info'] = $arrPicInfo;
        return $arrOut;
    }

    /**
     * @param $arrInput
     * @return array
     */
    private  function processDownload($arrPics,$intPgkId) {
        $arrPicInfo = $arrPics;
        $arrIntPackageId = $intPgkId;
        $coverFlag= true;
        $strCoverName = '';

        foreach ($arrPicInfo as $picKey => $picInfo){
            $temArrInputBig['flag'] = self::MEME_NAME_BIG_PREFIX;
            $temArrInputBig['url']  = $picInfo['url'];
            $temArrInputBig['name'] = $arrIntPackageId."_".$picInfo['pic_id'];
            $arrTemOutBig = $this->downloadPic($temArrInputBig);

            $temArrInputSmall['flag'] = self::MEME_NAME_SMALL_PREFIX;
            $temArrInputSmall['url']  = $picInfo['thumbnail'];
            $temArrInputSmall['name'] = $arrIntPackageId."_".$picInfo['pic_id'];
            $arrTemOutSmall = $this->downloadPic($temArrInputSmall);

            $input = array(
                'meme_id'   => $arrIntPackageId,
                'pic_id'    => $picInfo['pic_id'],
                'name'      => self::MEME_NAME_BIG_PREFIX."_".$temArrInputBig['name'].self::MEME_NAME_SURFIX,
                'thumbname' => self::MEME_NAME_SMALL_PREFIX."_".$temArrInputSmall['name'].self::MEME_NAME_SURFIX,
            );
            $arrRet = $this->_callService('meme', 'updatePic', $input);
            if(Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
                Bingo_Log::warning("call service meme::updatePic fail, ".serialize($arrRet));
            }
            if($coverFlag){
                $strCoverName = self::MEME_NAME_SMALL_PREFIX."_".$temArrInputSmall['name'].self::MEME_NAME_SURFIX;
                $coverFlag = false;
            }
        }
        $arrOut['data']['dir_name'] = self::$_download_dir;
        $arrOut['data']['covername'] = $strCoverName;
        return $arrOut;
    }

    /**
     * @param $arrInput
     * @return array
     */
    private  function processBuildZipNew($strPicInfo,$intPkgId) {
        // build ZIP
        // 转到打包目录
        chdir($strPicInfo);
        $strPicDirFile = $strPicInfo;
        $arrIntPackageId = $intPkgId;

        $strBaseName = $arrIntPackageId."_".time();
        $zipName = self::$_download_dir. $strBaseName . '.zip';

        $cmd = "zip -q -r ".$zipName. " *";
        exec($cmd);

        // upload ossp
        $strUrl = '';
        if (file_exists($zipName)) {
            $fileSize = filesize($zipName);
//            $strUrl = $this->uploadOssp($zipName);
            $strUrl = $this->uploadBos($strBaseName,$zipName);
            if (empty($strUrl)) {
                for ($i=1; $i<=3; $i++) {
                    echo "uploadOssp try {$i}\n";
                    $strUrl = $this->uploadBos($strBaseName,$zipName);
                    if (strlen($strUrl) > 15) {
                        break;
                    }
                    echo "uploadOssp failed\n";
                }
            }

        }
        $arrOut['data']['zip_url'] = $strUrl;

        // 回到当前目录
        chdir($strPicInfo."../");
        return $arrOut;
    }

    /**
     * @param $arrInput
     * @return array
     */
    private  function processBuildZip($strPicInfo,$intPkgId) {
        // build ZIP
        // 转到打包目录
        chdir($strPicInfo);
        $strPicDirFile = $strPicInfo;
        $arrIntPackageId = $intPkgId;

        $strBaseName = $arrIntPackageId."_".time();

        $objPackage  = new ZipArchive();

        $zipName = self::$_download_dir. $strBaseName . '.zip';
        $bolCdn = $objPackage->open($zipName, ZipArchive::CREATE);
        if ($bolCdn == false) {
            echo "create " . self::$_download_dir . "failed\n";

            $objPackage->close();
            return false;
        }
        $arrAllFile = $this->getAllFilesName($strPicDirFile);
        foreach ($arrAllFile as $file){
            if(is_file($file)){
                $objPackage->addFile($file);
            }
        }

        $objPackage->close();


        // upload ossp
        $strUrl = '';
        if (file_exists($zipName)) {
            $fileSize = filesize($zipName);
//            $strUrl = $this->uploadOssp($zipName);
            $strUrl = $this->uploadBos($strBaseName,$zipName);
            if (empty($strUrl)) {
                for ($i=1; $i<=3; $i++) {
                    echo "uploadOssp try {$i}\n";
                    $strUrl = $this->uploadBos($strBaseName,$zipName);
                    if (strlen($strUrl) > 15) {
                        break;
                    }
                    echo "uploadOssp failed\n";
                }
            }

        }
        $arrOut['data']['zip_url'] = $strUrl;

        // 回到当前目录
        chdir($strPicInfo."../");
        return $arrOut;
    }

    /**
     * @brief ossp zip包上传
     * @param $filename
     * @return $string
     **/
    private static function uploadOssp($fileName) {
        if (!file_exists($fileName)) {
            echo "uploadOssp:" . $fileName . " not exists\n";
            return '';
        }

        $fd = fopen($fileName,"rb");
        $fileContent = fread($fd, filesize($fileName));
        fclose($fd);

        $param = array(
            'pname' => "tieba", //线上
            'token' => '5Pm4xjGMUml9OBFk1EsOsJJra765jUYn', //线上
            'path'  => "/tb/package", //线上
//            'pname' => "demo",
//            'token' => "test",
//            'path'     => "/",
            'fileName' => basename($fileName),
            'md5' => 1, // 默认为1:文件名添加md5戳;0:md5文件名字不添加md5戳
        );
        $query_string = http_build_query($param);
//        $uri = "http://cp01-rdqa-dev411.cp01.baidu.com:8080/operationmenu/api/trans?";
        $uri = "http://ossp.baidu.com/operationmenu/api/trans?"; //线上
        $url = $uri.$query_string;
        //curl
        $header = array('Content-Type: application/octet-stream');
        $resource = curl_init();
        curl_setopt($resource, CURLOPT_URL, $url);
        curl_setopt($resource, CURLOPT_HTTPHEADER, $header);
        curl_setopt($resource, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($resource, CURLOPT_POST, 1);
        curl_setopt($resource, CURLOPT_POSTFIELDS, $fileContent);
        $result = json_decode(curl_exec($resource), true);
        curl_close($resource);
        if ($result['result'] != 1 && empty($result['data']['url'])) {
            echo "uploadOssp failed :" .var_export() . "\n";
            return '';
        }
        return $result['data']['url'];
    }

    /**
     * @param $objName
     * @param $fileName
     * @return bool|string
     */
    private static function uploadBos($objName,$fileName){
        if (!file_exists($fileName)) {
            echo "uploadBos:" . $fileName . " not exists\n";
            return '';
        }
        $ret = Libs_Bos_Util::saveObjectFromFile($objName,$fileName);
        if(!$ret) {
            Bingo_Log::warning("uploadBos fail,filename : $fileName");
        }
        $returnUrl = '';
        if(!Libs_Bos_Util::generateUrl($objName, -1, $returnUrl, false)) {
            return false;
        }

        return $returnUrl;
    }

    /**
     * @param $arrInput
     * @return array
     */
    private  function changeStatus($arrInput) {
        $arrParams = array(
            'id'        => $arrInput['id'],
            'status'    => $arrInput['status'],
            'url'       => $arrInput['url'],
            'covername' => $arrInput['covername'],
        );
        $arrRet = $this->_callService('meme', 'updatePackage', $arrParams);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning("call service meme::updatePackage fail, errno={$arrRet['errno']}");
            return;
        }
        return array();
    }



}


$objTask = new BuildMemePackage();
$objTask->run();