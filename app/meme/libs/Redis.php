<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2017/3/14
 * Time: 14:39
 */
class Libs_Redis {

    private static $_instances = null;
    private $_redis_name = null;
    private $_redis = null;

    /**
     * @param string $strRedisName
     * @return Libs_Redis
     */
    public static function getInstance($strRedisName = '')
    {
        if(!isset(self::$_instances[$strRedisName])) {
            self::$_instances[$strRedisName] = new self($strRedisName);
        }
        return self::$_instances[$strRedisName];
    }

    /**
     * @brief Redis SET & SETNX (setnx = 1)
     * @param $arrInput
     * @return bool
     */
    public function SET($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['value'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $value = $arrInput['value'];
        $setnx = isset($arrInput['setnx'])?intval($arrInput['setnx']):0;
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => $key,
            'value' => $this->_serialize($value),
        );
        if($setnx){
            $ret = $objRedis->SETNX($arrParams);
        } else {
            $ret = $objRedis->SET($arrParams);
        }
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[SET/SETNX] error.[".serialize($ret)."]");
            return false;
        }

        if($expire > 0){
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @brief Redis SETEX
     * @param $arrInput
     * @return bool
     */
    public function SETEX($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['value'])
            || !isset($arrInput['expire']) || $arrInput['expire'] <= 0){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $value = $arrInput['value'];
        $expire = intval($arrInput['expire']);

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => $key,
            'value' => $this->_serialize($value),
            'seconds' => $expire,
        );
        $ret = $objRedis->SETEX($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[SETEX] error.[".serialize($ret)."]");
            return false;
        }

        return true;
    }

    /**
     * @brief Redis GET
     * @param $arrInput
     * @return mixed|null
     */
    public function GET($arrInput)
    {
        if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array(
            'key' => $key,
        );
        $ret = $objRedis->GET($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[GET] error.[".serialize($ret)."]");
            return false;
        }

        $data = $this->_unserialize($ret['ret'][$key]);
        return $data;
    }

    /**
     * @brief Redis GET
     * @param $arrInput
     * @return mixed|null
     */
    public function TTL($arrInput)
    {
        if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array(
            'key' => $key,
        );
        $ret = $objRedis->TTL($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[GET] error.[".serialize($ret)."]");
            return false;
        }

        $data = $ret['ret'][$key];
        return $data;
    }

    /**
     * @brief Redis EXPIRE
     * @param $arrInput
     * @return bool
     */
    public function EXPIRE($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['expire'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => $key,
        );
        if($expire > 0){
            $arrParams['seconds'] = intval($expire);
            $ret = $objRedis->EXPIRE($arrParams);
        } else {
            $ret = $objRedis->PERSIST($arrParams);
        }
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[EXPIRE] error.[".serialize($ret)."]");
            return false;
        }

        return true;
    }

    /**
     * @brief Redis DEL
     * @param $arrInput
     * @return bool
     */
    public function DEL($arrInput)
    {
        if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => strval($key),
        );
        $ret = $objRedis->DEL($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[DEL] error.[".serialize($ret)."]");
            return false;
        }

        return true;
    }

    /**
     * @brief (multi keys) Redis SET & SETNX(setnx=1)
     * @param $arrInput
     * @return bool
     */
    public function mSET($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        if(empty($arrInput['reqs'])) {
            return true;
        }

        $setnx = isset($arrInput['setnx'])?intval($arrInput['setnx']):0;
        $arrParams = array();
        $arrKeyExpires = array();
        foreach($arrInput['reqs'] as $req) {
            if(!isset($req['key']) || !isset($req['value'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }

            $key = $req['key'];
            $value = $req['value'];
            $arrParams['reqs'][] = array(
                'key' => $key,
                'value' => $this->_serialize($value),
            );

            $expire = isset($req['expire']) ? intval($req['expire']) : 0;
            if(isset($arrKeyExpires[$req['key']])) { //conflict keys
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrKeyExpires[$req['key']] = $expire;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        if($setnx){
            $ret = $objRedis->SETNX($arrParams);
        } else {
            $ret = $objRedis->SET($arrParams);
        }
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[SET/SETNX] error.[".serialize($ret)."]");
            return false;
        }

        $arrParams = array();
        foreach($arrKeyExpires as $key => $expire) {
            if($expire > 0) {
                $arrParams['reqs'][] = array(
                    'key' => $key,
                    'seconds' => $expire,
                );
            }
        }
        if(!empty($arrParams)) {
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @brief (multi keys) Redis SETEX
     * @param $arrInput
     * @return bool
     */
    public function mSETEX($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        if(empty($arrInput['reqs'])) {
            return true;
        }

        $arrParams = array();
        $arrKeyExpires = array();
        foreach($arrInput['reqs'] as $req) {
            if(!isset($req['key']) || !isset($req['value'])
                || !isset($req['expire']) || $req['expire'] <= 0) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }

            $key = $req['key'];
            $value = $req['value'];
            $expire = intval($req['expire']);

            $arrParams['reqs'][] = array(
                'key' => $key,
                'value' => $this->_serialize($value),
                'seconds' => $expire,
            );

            if(isset($arrKeyExpires[$req['key']])) { //conflict keys
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrKeyExpires[$req['key']] = $expire;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $ret = $objRedis->SETEX($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[SETEX] error.[".serialize($ret)."]");
            return false;
        }

        return true;
    }

    /**
     * @brief (multi keys) Redis GET
     * @param $arrInput
     * @return bool
     */
    public function mGET($arrInput)
    {
        if(!isset($arrInput['keys']) || !is_array($arrInput['keys'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $keys = $arrInput['keys'];
        if(empty($keys)) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        foreach($keys as $key) {
            $arrParams['reqs'][] = array(
                'key' => strval($key),
            );
        }
        $ret = $objRedis->GET($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[GET] error.[".serialize($ret)."]");
            return false;
        }

        $arrOutput = array();
        foreach($keys as $key) {
            $arrOutput[$key] = $this->_unserialize($ret['ret'][$key]);
        }

        return $arrOutput;
    }

    /**
     * @brief (multi keys) Redis DEL
     * @param $arrInput
     * @return bool
     */
    public function mDEL($arrInput)
    {
        if(!isset($arrInput['keys']) || !is_array($arrInput['keys'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $keys = $arrInput['keys'];
        if(empty($keys)) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array();
        foreach($keys as $key) {
            $arrParams['reqs'][] = array(
                'key' => strval($key),
            );
        }
        $ret = $objRedis->DEL($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[DEL] error.[".serialize($ret)."]");
            return false;
        }

        return true;
    }

    /**
     * @brief Redis HSET
     * @param $arrInput
     * @return bool
     */
    public function HSET($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['field'])
            || !isset($arrInput['value'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $field = $arrInput['field'];
        $value = $arrInput['value'];
        $setnx = isset($arrInput['setnx'])?intval($arrInput['setnx']):0;
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => $key,
            'field' => $field,
            'value' => $this->_serialize($value),
        );
        if($setnx) {
            $ret = $objRedis->HSETNX($arrParams);
        } else {
            $ret = $objRedis->HSET($arrParams);
        }
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HSET] error.[".serialize($ret)."]");
            return false;
        }

        if($expire > 0){
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @brief Redis HGET
     * @param $arrInput
     * @return mixed|null
     */
    public function HGET($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['field'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $field = $arrInput['field'];

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array(
            'key' => $key,
            'field' => $field,
        );

        $ret = $objRedis->HGET($arrParams);

        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HGET] error.[".serialize($ret)."]");
            return false;
        }
        $data = $this->_unserialize($ret['ret'][$key]);
        return $data;
    }

    /**
     * @brief Redis HDEL
     * @param $arrInput
     * @return bool
     */
    public function HDEL($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['field'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $field = $arrInput['field'];

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array(
            'key' => strval($key),
            'field' => strval($field),
        );
        $ret = $objRedis->HDEL($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HDEL] error.[".serialize($ret)."]");
            return false;
        }

        return true;
    }

    /**
     * @brief Redis HLEN
     * @param $arrInput
     * @return array
     */
    public function HLEN($arrInput)
    {
        if(!isset($arrInput['key'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $strKey = $arrInput['key'];
        $arrParams = array(
            'key' => $strKey,
        );
        $ret = $objRedis->HLEN($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('HLEN fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        return isset($ret['ret'][$strKey]) ? intval($ret['ret'][$strKey]) : 0;
    }

    /**
     * @brief (multi keys) Redis HLEN
     * @param $arrInput
     * @return array
     */
    public function mHLEN($arrInput)
    {
        if(!isset($arrInput['keys'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['keys'])) {
            return array();
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        foreach($arrInput['keys'] as $strKey) {
            $arrParams['reqs'][] = array(
                'key' => $strKey,
            );
        }

        $ret = $objRedis->HLEN($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('HLEN fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        $arrOutput = array();
        foreach($arrInput['keys'] as $strKey) {
            $arrOutput[$strKey] = isset($ret['ret'][$strKey]) ? intval($ret['ret'][$strKey]) : 0;
        }

        return $arrOutput;
    }

    /**
     * @brief Redis HMSET
     * @param $arrInput
     * @return bool
     */
    public function HMSET($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['fields'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $fields = $arrInput['fields'];
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;

        $arrFieldValues = array();
        foreach($fields as $field=>$val) {
            $arrFieldValues[] = array(
                'field' => $field,
                'value' => $this->_serialize($val),
            );
        }
        if(empty($arrFieldValues)) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array(
            'key' => $key,
            'fields' => $arrFieldValues,
        );
        $ret = $objRedis->HMSET($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HMSET] error.[".serialize($ret)."]");
            return false;
        }

        if($expire> 0) {
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $objRedis->EXPIRE($arrParams);
        }

        return true;
    }

    /**
     * @brief (multi keys) Redis HSET
     * @param $arrInput
     * @return bool
     */
    public function mHSET($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        if(empty($arrInput['reqs'])) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrKeyReqs = array();
        foreach($arrInput['reqs'] as $req) {
            if(!isset($req['key']) || !isset($req['field']) || !isset($req['value'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $expire = isset($req['expire']) ? intval($req['expire']) : 0;

            $arrKeyReqs[$req['key']]['fields'][] = array(
                'field' => $req['field'],
                'value' => $this->_serialize($req['value']),
            );
            if(isset($arrKeyReqs[$req['key']]['expire'])
                && $arrKeyReqs[$req['key']]['expire'] != $expire) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrKeyReqs[$req['key']]['expire'] = $expire;
        }

        $arrSetParams = array();
        $arrExpireParams = array();
        foreach($arrKeyReqs as $key => $arrReq) {
            $arrSetParams['reqs'][] = array(
                'key' => $key,
                'fields' => $arrReq['fields'],
            );
            if($arrReq['expire'] > 0) {
                $arrExpireParams['reqs'][] = array(
                    'key' => $key,
                    'seconds' => $arrReq['expire'],
                );
            }
        }

        $ret = $objRedis->HMSET($arrSetParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HMSET] error.[".serialize($ret)."]");
            return false;
        }

        if(!empty($arrExpireParams)) {
            $objRedis->EXPIRE($arrExpireParams);
        }

        return true;
    }

    /**
     * @brief Redis HMSET
     * @param $arrInput
     * @return mixed|null
     */
    public function HMGET($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['fields'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $fields = $arrInput['fields'];
        if(empty($fields)) {
            return array();
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array(
            'key' => $key,
            'field' => $fields,
        );
        $ret = $objRedis->HMGET($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HMGET] error.[".serialize($ret)."]");
            return false;
        }
        $data = array();
        if(!is_null($ret['ret'][$key])){
            foreach($ret['ret'][$key] as $k => $v) {
                $data[$fields[$k]] = $this->_unserialize($v);
            }
        }
        return $data;
    }

    /**
     * @brief Redis HMDEL (extended from HDEL)
     * @param $arrInput
     * @return bool
     */
    public function HMDEL($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['fields'])
            || !is_array($arrInput['fields']) || empty($arrInput['fields'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $fields = $arrInput['fields'];

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        foreach($fields as $field) {
            $arrParams['reqs'][] = array(
                'key' => strval($key),
                'field' => strval($field),
            );
        }
        $ret = $objRedis->HDEL($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HDEL] error.[".serialize($ret)."]");
            return false;
        }
        return true;
    }

    /**
     * @brief Redis HINCRBY
     * @param $arrInput
     * @return bool
     */
    public function HINCRBY($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['field'])
            || !isset($arrInput['step'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        $key = $arrInput['key'];
        $field = $arrInput['field'];
        $step = $arrInput['step'];
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array(
            'key' => $key,
            'field' => $field,
            'step' => $step,
        );

        $ret = $objRedis->HINCRBY($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HINCRBY] error.[".serialize($ret)."]");
            return false;
        }

        if($expire > 0){
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @brief Redis HMINCRBY (extended from HINCRBY)
     * @param $arrInput
     * @return bool
     */
    public function HMINCRBY($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['fields'])
            || !isset($arrInput['step'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $fields = $arrInput['fields'];
        if(empty($fields)) {
            return true;
        }
        $step = $arrInput['step'];
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        foreach($fields as $field) {
            $arrParams['reqs'][] = array(
                'key' => $key,
                'field' => $field,
                'step' => $step,
            );
        }
        $ret = $objRedis->HINCRBY($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HINCRBY] error.[".serialize($ret)."]");
            return false;
        }

        if($expire > 0){
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @brief (multi keys) Redis HINCRBY
     * @param $arrInput
     * @return bool
     */
    public function mHINCRBY($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        if(empty($arrInput['reqs'])) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        $arrKeyExpires = array();
        foreach($arrInput['reqs'] as $req) {
            if(!isset($req['key']) || !isset($req['field']) || !isset($req['step'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrParams['reqs'][] = array(
                'key' => $req['key'],
                'field' => $req['field'],
                'step' => $req['step'],
            );
            $expire = isset($req['expire']) ? intval($req['expire']) : 0;
            if(isset($arrKeyExpires[$req['key']]) && $arrKeyExpires[$req['key']] != $expire) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrKeyExpires[$req['key']] = $expire;
        }

        $ret = $objRedis->HINCRBY($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HINCRBY] error.[".serialize($ret)."]");
            return false;
        }

        $arrParams = array();
        foreach($arrKeyExpires as $key => $expire) {
            if($expire > 0) {
                $arrParams['reqs'][] = array(
                    'key' => $key,
                    'seconds' => $expire,
                );
            }
        }
        if(!empty($arrParams)) {
            $objRedis->EXPIRE($arrParams);
        }

        return true;
    }

    /**
     * @brief Redis HGETALL
     * @param $arrInput
     * @return array|null
     */
    public function HGETALL($arrInput)
    {
        if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array(
            'key' => $key,
        );
        $ret = $objRedis->HGETALL($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HGETALL] error.[".serialize($ret)."]");
            return false;
        }
        $data = null;
        if(!is_null($ret['ret'][$key])){
            foreach($ret['ret'][$key] as $item) {
                $data[$item['field']] = $this->_unserialize($item['value']);
            }
        }
        return $data;
    }

    /**
     * @brief (multi keys) Redis HGETALL
     * @param $arrInput
     * @return array|null
     */
    public function mHGETALL($arrInput)
    {
        if(!isset($arrInput['keys']) || !is_array($arrInput['keys'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $keys = $arrInput['keys'];
        if(empty($keys)) {
            return array();
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        foreach($keys as $key) {
            $arrParams['reqs'][] = array(
                'key' => strval($key),
            );
        }
        $ret = $objRedis->HGETALL($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HGETALL] error.[".serialize($ret)."]");
            return false;
        }
        $data = array();
        foreach($keys as $key) {
            if(is_null($ret['ret'][$key])) {
                $data[$key] = null;
                continue;
            }
            foreach($ret['ret'][$key] as $item) {
                $data[$key][$item['field']] = $this->_unserialize($item['value']);
            }
        }
        return $data;
    }

    /**
     * @brief Redis ZADD
     * @param $arrInput
     * @return bool
     */
    public function ZADD($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['member'])
            || !isset($arrInput['score'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParam = array(
            'key' => $arrInput['key'],
            'member' => strval($arrInput['member']),
            'score' => $arrInput['score'],
        );

        $ret = $objRedis->ZADD($arrParam);
        if(!$ret || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZADD fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        if(isset($arrInput['expire'])) {
            $arrParam = array(
                'key' => $arrInput['key'],
                'seconds' => intval($arrInput['expire']),
            );
            $objRedis->EXPIRE($arrParam);
        }
        return true;
    }

    /**
     * @brief Redis ZMADD (extended from ZADD)
     * @param $arrInput
     * @return bool
     */
    public function ZMADD($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['members']) || !is_array($arrInput['members'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['members'])) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        foreach($arrInput['members'] as $member => $score) {
            $arrParams['reqs'][] = array(
                'key' => $arrInput['key'],
                'member' => strval($member),
                'score' => $score,
            );
        }

        $ret = $objRedis->ZADD($arrParams);
        if(!$ret || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZADD fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        if(isset($arrInput['expire'])) {
            $arrParams = array(
                'key' => $arrInput['key'],
                'seconds' => intval($arrInput['expire']),
            );
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @brief (multi keys) Redis ZADD
     * @param $arrInput
     * @return bool
     */
    public function mZADD($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['reqs'])) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        $arrKeyExpires = array();
        foreach($arrInput['reqs'] as $req) {
            if(!isset($req['key']) || !isset($req['member']) || !isset($req['score'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrParams['reqs'][] = array(
                'key' => $req['key'],
                'member' => strval($req['member']),
                'score' => $req['score'],
            );
            $expire = isset($req['expire']) ? intval($req['expire']) : 0;
            if(isset($arrKeyExpires[$req['key']]) && $arrKeyExpires[$req['key']] != $expire) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrKeyExpires[$req['key']] = $expire;
        }

        $ret = $objRedis->ZADD($arrParams);
        if(!$ret || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZADD fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        $arrParams = array();
        foreach($arrKeyExpires as $key => $expire) {
            if($expire > 0) {
                $arrParams['reqs'][] = array(
                    'key' => $key,
                    'seconds' => $expire,
                );
            }
        }
        if(!empty($arrParams)) {
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @brief Redis ZINCRBY
     * @param $arrInput
     * @return bool
     */
    public function ZINCRBY($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['member'])
            || !isset($arrInput['step'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array(
            'key' => $arrInput['key'],
            'member' => strval($arrInput['member']),
            'step' => $arrInput['step'],
        );

        $ret = $objRedis->ZINCRBY($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZINCRBY fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        if(isset($arrInput['expire'])) {
            $arrParams = array(
                'key' => $arrInput['key'],
                'seconds' => intval($arrInput['expire']),
            );
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @brief Redis ZMINCRBY (extended from ZINCRBY)
     * @param $arrInput
     * @return bool
     */
    public function ZMINCRBY($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['members']) || !is_array($arrInput['members'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['members'])) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        foreach($arrInput['members'] as $member => $step) {
            $arrParams['reqs'][] = array(
                'key' => $arrInput['key'],
                'member' => strval($member),
                'step' => $step,
            );
        }

        $ret = $objRedis->ZINCRBY($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZINCRBY fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        if(isset($arrInput['expire'])) {
            $arrParams = array(
                'key' => $arrInput['key'],
                'seconds' => intval($arrInput['expire']),
            );
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @brief (multi keys) Redis ZINCRBY
     * @param $arrInput
     * @return bool
     */
    public function mZINCRBY($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['reqs'])) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        $arrKeyExpires = array();
        foreach($arrInput['reqs'] as $req) {
            if(!isset($req['key']) || !isset($req['member']) || !isset($req['step'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrParams['reqs'][] = array(
                'key' => $req['key'],
                'member' => strval($req['member']),
                'step' => $req['step'],
            );
            $expire = isset($req['expire']) ? intval($req['expire']) : 0;
            if(isset($arrKeyExpires[$req['key']]) && $arrKeyExpires[$req['key']] != $expire) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrKeyExpires[$req['key']] = $expire;
        }

        $ret = $objRedis->ZINCRBY($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZINCRBY fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        $arrParams = array();
        foreach($arrKeyExpires as $key => $expire) {
            if($expire > 0) {
                $arrParams['reqs'][] = array(
                    'key' => $key,
                    'seconds' => $expire,
                );
            }
        }
        if(!empty($arrParams)) {
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @brief Redis ZREM
     * @param $arrInput
     * @return bool
     */
    public function ZREM($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['member'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array(
            'key' => $arrInput['key'],
            'member' => strval($arrInput['member']),
        );

        $ret = $objRedis->ZREM($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZREM fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        return true;
    }

    /**
     * @brief Redis ZMREM (extended from ZREM)
     * @param $arrInput
     * @return bool
     */
    public function ZMREM($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['members'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['members'])) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        foreach($arrInput['members'] as $member) {
            $arrParams['reqs'][] = array(
                'key' => $arrInput['key'],
                'member' => strval($member),
            );
        }

        $ret = $objRedis->ZREM($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZREM fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        return true;
    }

    /**
     * @brief (multi keys) Redis ZREM
     * @param $arrInput
     * @return bool
     */
    public function mZREM($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['reqs'])) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        foreach($arrInput['reqs'] as $req) {
            if(!isset($req['key']) || !isset($req['member'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrParams['reqs'][] = array(
                'key' => $req['key'],
                'member' => strval($req['member']),
            );
        }

        $ret = $objRedis->ZREM($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZREM fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        return true;
    }

    /**
     * @brief Redis ZCARD
     * @param $arrInput
     * @return array
     */
    public function ZCARD($arrInput)
    {
        if(!isset($arrInput['key'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $strKey = $arrInput['key'];
        $arrParams = array(
            'key' => $strKey,
        );
        $ret = $objRedis->ZCARD($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZCARD fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        return isset($ret['ret'][$strKey]) ? intval($ret['ret'][$strKey]) : 0;
    }

    /**
     * @brief (multi keys) Redis ZCARD
     * @param $arrInput
     * @return array
     */
    public function mZCARD($arrInput)
    {
        if(!isset($arrInput['keys'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['keys'])) {
            return array();
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        foreach($arrInput['keys'] as $strKey) {
            $arrParams['reqs'][] = array(
                'key' => $strKey,
            );
        }

        $ret = $objRedis->ZCARD($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZCARD fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        $arrOutput = array();
        foreach($arrInput['keys'] as $strKey) {
            $arrOutput[$strKey] = isset($ret['ret'][$strKey]) ? intval($ret['ret'][$strKey]) : 0;
        }

        return $arrOutput;
    }

    /**
     * @brief Redis ZRANK(order=1)/ZREVRANK(order=0)
     * @param $arrInput
     * @return array
     */
    public function ZRANK($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['member'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $strKey = $arrInput['key'];
        $intOrder = isset($arrInput['order']) ? intval($arrInput['order']) : 0;
        $arrParams = array(
            'key' => $strKey,
            'member' => strval($arrInput['member']),
        );

        if($intOrder > 0) {
            $ret = $objRedis->ZRANK($arrParams);
        } else {
            $ret = $objRedis->ZREVRANK($arrParams);
        }
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZREVRANK fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        return isset($ret['ret'][$strKey]) ? intval($ret['ret'][$strKey]) : null;
    }

    /**
     * @brief Redis ZMRANK (extended from ZRANK/ZRAVRANK)
     * @param $arrInput
     * @return array
     */
    public function ZMRANK($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['members'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['members'])) {
            return array();
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $strKey = $arrInput['key'];
        $intOrder = isset($arrInput['order']) ? intval($arrInput['order']) : 0;
        $arrMembers = $arrInput['members'];
        $arrParams = array();
        foreach($arrMembers as $member) {
            $arrParams['reqs'][] = array(
                'key' => $strKey,
                'member' => strval($member),
            );
        }

        if($intOrder > 0) {
            $ret = $objRedis->ZRANK($arrParams);
        } else {
            $ret = $objRedis->ZREVRANK($arrParams);
        }
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZREVRANK fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        if(count($arrMembers) > 1) {
            $arrOutput = array();
            foreach($arrMembers as $member) {
                $arrOutput[$member] = isset($ret['ret'][$strKey][$member]) ?
                    intval($ret['ret'][$strKey][$member]) : null;
            }
        } else {
            $member = $arrMembers[0];
            $arrOutput[$member] = isset($ret['ret'][$strKey]) ? intval($ret['ret'][$strKey]) : null;
        }

        return $arrOutput;
    }

    /**
     * @brief (multi keys) Redis ZRANK(order=1)/ZREVRANK(order=0)
     * @param $arrInput
     * @return array
     */
    public function mZRANK($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['reqs'])) {
            return array();
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $intOrder = isset($arrInput['order']) ? intval($arrInput['order']) : 0;
        $arrParams = array();
        $bolMultiMember = false;
        foreach($arrInput['reqs'] as $req) {
            if(!isset($req['key']) || !isset($req['member'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrParams['reqs'][] = array(
                'key' => $req['key'],
                'member' => strval($req['member']),
            );
            if(!$bolMultiMember) {
                if(!isset($arrMemberMark[$req['key']])) {
                    $arrMemberMark[$req['key']] = 1;
                } else {
                    $bolMultiMember = true;
                }
            }
        }

        if($intOrder > 0) {
            $ret = $objRedis->ZRANK($arrParams);
        } else {
            $ret = $objRedis->ZREVRANK($arrParams);
        }
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZREVRANK fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        $arrOutput = array();
        foreach($arrInput['reqs'] as $req) {
            if($bolMultiMember) {
                $arrOutput[$req['key']][$req['member']] =
                    isset($ret['ret'][$req['key']][$req['member']]) ?
                        intval($ret['ret'][$req['key']][$req['member']]) : null;
            } else {
                $arrOutput[$req['key']][$req['member']] =
                    isset($ret['ret'][$req['key']]) ? intval($ret['ret'][$req['key']]) : null;
            }
        }
        return $arrOutput;
    }

    /**
     * @brief Redis ZSCORE
     * @param $arrInput
     * @return array
     */
    public function ZSCORE($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['member'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $strKey = $arrInput['key'];
        $arrParam = array(
            'key' => $strKey,
            'member' => strval($arrInput['member']),
        );

        $ret = $objRedis->ZSCORE($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZSCORE fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        return isset($ret['ret'][$strKey]) ? $ret['ret'][$strKey] : null;
    }

    /**
     * @brief Redis ZMSCORE (extended from ZSCORE)
     * @param $arrInput
     * @return array
     */
    public function ZMSCORE($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['members'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['members'])) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $strKey = $arrInput['key'];
        $arrMembers = $arrInput['members'];
        $arrParams = array(
            'reqs' => array(),
        );
        foreach($arrMembers as $member) {
            $arrParams['reqs'][] = array(
                'key' => $strKey,
                'member' => strval($member),
            );
        }

        $ret = $objRedis->ZSCORE($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZSCORE fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        if(count($arrMembers) > 1) {
            $arrOutput = array();
            foreach($arrMembers as $member) {
                $arrOutput[$member] = isset($ret['ret'][$strKey][$member]) ? $ret['ret'][$strKey][$member] : null;
            }
        } else {
            $member = $arrMembers[0];
            $arrOutput[$member] = isset($ret['ret'][$strKey]) ? $ret['ret'][$strKey] : null;
        }

        return $arrOutput;
    }

    /**
     * @brief (multi keys) Redis ZSCORE
     * @param $arrInput
     * @return array
     */
    public function mZSCORE($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['reqs'])) {
            return array();
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        $bolMultiMember = false;
        foreach($arrInput['reqs'] as $req) {
            if(!isset($req['key']) || !isset($req['member'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrParams['reqs'][] = array(
                'key' => $req['key'],
                'member' => strval($req['member']),
            );
            if(!$bolMultiMember) {
                if(!isset($arrMemberMark[$req['key']])) {
                    $arrMemberMark[$req['key']] = 1;
                } else {
                    $bolMultiMember = true;
                }
            }
        }

        $ret = $objRedis->ZSCORE($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZSCORE fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        $arrOutput = array();
        foreach($arrInput['reqs'] as $req) {
            if($bolMultiMember) {
                $arrOutput[$req['key']][$req['member']] =
                    isset($ret['ret'][$req['key']][$req['member']]) ? $ret['ret'][$req['key']][$req['member']] : null;
            } else {
                $arrOutput[$req['key']][$req['member']] =
                    isset($ret['ret'][$req['key']]) ? $ret['ret'][$req['key']] : null;
            }
        }
        return $arrOutput;
    }

    /**
     * @brief Redis ZRANGEBYSCOREWITHSCORES(order=1)/ZREVRANGEBYSCOREWITHSCORES(order=0)
     * @param $arrInput
     * @return array
     */
    public function ZRANGEBYSCORE($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['offset']) || !isset($arrInput['count']) ) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $strKey = $arrInput['key'];
        $intOrder = isset($arrInput['order']) ? intval($arrInput['order']) : 0;
        if($intOrder > 0) {
            $mixMax = isset($arrInput['max']) ? $arrInput['max'] : '+inf';
            $mixMin = isset($arrInput['min']) ? $arrInput['min'] : '-inf';
        } else {
            $mixMax = isset($arrInput['max']) ? $arrInput['max'] : '-inf';
            $mixMin = isset($arrInput['min']) ? $arrInput['min'] : '+inf';
        }
        $intOffset = intval($arrInput['offset']);
        $intCount = intval($arrInput['count']);
        $arrParams = array(
            'key' => $strKey,
            'max' => $mixMax,
            'min' => $mixMin,
            'offset' => $intOffset,
            'count' => $intCount,
        );

        if($intOrder > 0) {
            $ret = $objRedis->ZRANGEBYSCOREWITHSCORES($arrParams);
            if($ret === false || $ret['err_no'] != 0) {
                Bingo_Log::warning('ZRANGEBYSCOREWITHSCORES fail, params='.serialize($arrParams).',ret='
                    .serialize($ret));
                return false;
            }
        } else {
            $ret = $objRedis->ZREVRANGEBYSCOREWITHSCORES($arrParams);
            if($ret === false || $ret['err_no'] != 0) {
                Bingo_Log::warning('ZREVRANGEBYSCOREWITHSCORES fail, params='.serialize($arrParams).',ret='
                    .serialize($ret));
                return false;
            }
        }

        return isset($ret['ret'][$strKey]) ? $ret['ret'][$strKey] : array();
    }

    /**
     * @brief Redis ZRANGEBYSCOREWITHSCORES(order=1)/ZREVRANGEBYSCOREWITHSCORES(order=0)
     * @param $arrInput
     * @return array
     */
    public function mZRANGEBYSCORE($arrInput)
    {
        if(!isset($arrInput['keys']) || !isset($arrInput['offset']) || !isset($arrInput['count']) ) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['keys'])) {
            return array();
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $strKey = $arrInput['key'];
        $intOrder = isset($arrInput['order']) ? intval($arrInput['order']) : 0;
        if($intOrder > 0) {
            $mixMax = isset($arrInput['max']) ? $arrInput['max'] : '+inf';
            $mixMin = isset($arrInput['min']) ? $arrInput['min'] : '-inf';
        } else {
            $mixMax = isset($arrInput['max']) ? $arrInput['max'] : '-inf';
            $mixMin = isset($arrInput['min']) ? $arrInput['min'] : '+inf';
        }
        $intOffset = intval($arrInput['offset']);
        $intCount = intval($arrInput['count']);

        $arrParams = array();
        foreach($arrInput['keys'] as $strKey) {
            $arrParams['reqs'][] = array(
                'key' => $strKey,
                'max' => $mixMax,
                'min' => $mixMin,
                'offset' => $intOffset,
                'count' => $intCount,
            );
        }

//        $arrParams = array(
//            'key' => $strKey,
//            'max' => $mixMax,
//            'min' => $mixMin,
//            'offset' => $intOffset,
//            'count' => $intCount,
//        );

        if($intOrder > 0) {
            $ret = $objRedis->ZRANGEBYSCOREWITHSCORES($arrParams);
            if($ret === false || $ret['err_no'] != 0) {
                Bingo_Log::warning('ZRANGEBYSCOREWITHSCORES fail, params='.serialize($arrParams).',ret='
                    .serialize($ret));
                return false;
            }
        } else {
            $ret = $objRedis->ZREVRANGEBYSCOREWITHSCORES($arrParams);
            if($ret === false || $ret['err_no'] != 0) {
                Bingo_Log::warning('ZREVRANGEBYSCOREWITHSCORES fail, params='.serialize($arrParams).',ret='
                    .serialize($ret));
                return false;
            }
        }

        $arrOutput = array();
        foreach($arrInput['keys'] as $strKey) {
            $arrOutput[$strKey] = isset($ret['ret'][$strKey]) ? $ret['ret'][$strKey] : array();
        }

        return $arrOutput;
    }

    /**
     * @brief Redis ZCOUNT
     * @param $arrInput
     * @return bool|int
     */
    public function ZCOUNT($arrInput)
    {
        if (!isset($arrInput['key']) || (!isset($arrInput['max']) && !isset($arrInput['min']))) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return false;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $strKey = $arrInput['key'];
        $mixMax = isset($arrInput['max']) ? $arrInput['max'] : '+inf';
        $mixMin = isset($arrInput['min']) ? $arrInput['min'] : '-inf';
        $arrParams = array(
            'key' => $strKey,
            'max' => $mixMax,
            'min' => $mixMin,
        );

        $ret = $objRedis->ZCOUNT($arrParams);
        if ($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZCOUNT fail, params=' . serialize($arrParams) . ',ret=' . serialize($ret));
            return false;
        }

        return isset($ret['ret'][$strKey]) ? intval($ret['ret'][$strKey]) : 0;
    }

    /**
     * @brief Redis SADD
     * @param $arrInput
     * @return bool
     */
    public function SADD($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['member'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $member = $arrInput['member'];
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => $key,
            'member' => array($member),
        );
        $ret = $objRedis->SADD($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[SADD] error.[".serialize($ret)."]");
            return false;
        }

        if($expire > 0){
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @brief Redis SREM
     * @param $arrInput
     * @return bool
     */
    public function SREM($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['member'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $member = $arrInput['member'];

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => $key,
            'member' => array($member),
        );
        $ret = $objRedis->SREM($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[SREM] error.[".serialize($ret)."]");
            return false;
        }

        return true;
    }

    /**
     * @brief Redis SMADD (extended from SADD)
     * @param $arrInput
     * @return bool
     */
    public function SMADD($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['members']) || !is_array($arrInput['members'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $members = $arrInput['members'];
        if(empty($members)) {
            return true;
        }
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => $key,
            'member' => $members,
        );
        $ret = $objRedis->SADD($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[SADD] error.[".serialize($ret)."]");
            return false;
        }

        if($expire > 0){
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @brief Redis SMREM (extended from SREM)
     * @param $arrInput
     * @return bool
     */
    public function SMREM($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['members']) || !is_array($arrInput['members'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $members = $arrInput['members'];
        if(empty($members)) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => $key,
            'member' => $members,
        );
        $ret = $objRedis->SREM($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[SREM] error.[".serialize($ret)."]");
            return false;
        }

        return true;
    }

    /**
     * @brief Redis SMEMBERS(count=0)/SRANDMEMBER(count>0, yet not supported by 20170314)
     * @param $arrInput
     * @return bool
     */
    public function SMEMBERS($arrInput)
    {
        if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $count = isset($arrInput['count'])? intval($arrInput['count']) : 0;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => $key,
        );
        if($count > 0) { //
            $arrParams['count'] = $count;
            $ret = $objRedis->SRANDMEMBER($arrParams);
        } else {
            $ret = $objRedis->SMEMBERS($arrParams);
        }
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[SMEMBERS] error.[".serialize($ret)."]");
            return false;
        }

        return isset($ret['ret'][$key]) ? $ret['ret'][$key] : null;
    }

    /**
     * @brief Redis RPUSH
     * @param $arrInput
     * @return bool
     */
    public function RPUSH($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['value'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $value = $arrInput['value'];
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParam = array(
            'key' => $key,
            'value' => $this->_serialize($value),
        );

        $ret = $objRedis->RPUSH($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('RPUSH fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        if($expire > 0){
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $objRedis->EXPIRE($arrParams);
        }

        return true;
    }

    /**
     * @brief Redis LPOP
     * @param $arrInput
     * @return bool
     */
    public function LPOP($arrInput)
    {
        if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParam = array(
            'key' => $key,
        );

        $ret = $objRedis->LPOP($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('LPOP fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        $data = $this->_unserialize($ret['ret'][$key]);
        return $data;
    }

    /**
     * @brief Redis LLEN
     * @param $arrInput
     * @return bool
     */
    public function LLEN($arrInput)
    {
        if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParam = array(
            'key' => $key,
        );

        $ret = $objRedis->LLEN($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('LLEN fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        return !is_null($ret['ret'][$key]) ? intval($ret['ret'][$key]) : 0;
    }

    /**
     * __construct
     * @param $strRedisName
     */
    protected function __construct($strRedisName)
    {
        $this->_redis_name = $strRedisName;
    }

    /**
     * @param $strMethod
     * @param $arrInput
     * @return bool
     */
    public function __call($strMethod, $arrInput)
    {
        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $ret = $objRedis->$strMethod($arrInput[0]);
        if($ret === false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[$strMethod] error.[".serialize($ret)."]");
        }

        return $ret;
    }

    /**
     * _getRedis
     * @return Bingo_Cache_Redis|null
     */
    private function _getRedis()
    {
        if(!is_null($this->_redis)) {
            $objRedis = $this->_redis;
        } else {
            $objRedis = new Bingo_Cache_Redis($this->_redis_name);
            $this->_redis = $objRedis;
        }
        if(!$objRedis || !$objRedis->isEnable()){
            Bingo_Log::warning("init redis{$this->_redis_name} fail.");
            return null;
        }
        return $objRedis;
    }

    /**
     * @param $data
     * @return bool
     */
    private function _is_serialized($data)
    {
        if (!is_string($data)) {
            return false;
        }
        $data = trim( $data );
        if ('N;' == $data) {
            return true;
        }
        $length = strlen($data);
        if ($length < 4) {
            return false;
        }
        if (':' !== $data[1]) {
            return false;
        }
        $lastc = $data[$length-1];
        if (';' !== $lastc && '}' !== $lastc) {
            return false;
        }
        $token = $data[0];
        switch ($token) {
            case 's' :
                if ('"' !== $data[$length-2]) {
                    return false;
                }
                continue;
            case 'a' :
            case 'O' :
                return (bool) preg_match("/^{$token}:[0-9]+:/s", $data);
            case 'b' :
            case 'i' :
            case 'd' :
                return (bool) preg_match("/^{$token}:[0-9.E-]+;\$/", $data);
        }
        return false;
    }

    /**
     * @param $data
     * @return string
     */
    private function _serialize($data)
    {
        if(is_null($data) || is_array($data) || is_object($data)) {
            return serialize($data);
        }

        return $data;
    }

    /**
     * @param $data
     * @return mixed
     */
    private function _unserialize($data)
    {
        if(!is_null($data) && $this->_is_serialized($data)) {
            return unserialize($data);
        }

        return $data;
    }


    /**
     * @brief Redis LPUSH
     * @param $arrInput
     * @return bool
     */
    public function LPUSH($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['value'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $value = $arrInput['value'];
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParam = array(
            'key' => $key,
            'value' => $this->_serialize($value),
        );
        $ret = $objRedis->LPUSH($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('LPUSH fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        if($expire > 0){
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $objRedis->EXPIRE($arrParams);
        }

        return true;
    }

    /**
     * @brief Redis LPUSH
     * @param $arrInput
     * @return bool
     */
    public function LRANGE($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['start']) || !isset($arrInput['stop'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $start = $arrInput['start'];
        $stop = $arrInput['stop'];


        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParam = array(
            'key' => $key,
            'start' => $start,
            'stop' => $stop,
        );

        $ret = $objRedis->LRANGE($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('LPUSH fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        return isset($ret['ret'][$key]) ? $ret['ret'][$key] : array();

    }
}
