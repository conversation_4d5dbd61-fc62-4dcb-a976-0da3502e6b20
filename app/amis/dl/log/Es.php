<?php
/***************************************************************************
 * 
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Es.php
 * <AUTHOR>
 * @date 2018/08/02 13:58:44
 * @brief 
 *  
 **/

class Dl_Log_Es {

    const ES_INDEX_PREFIX   = 'amisnew_log_system_';

    /**
     * <AUTHOR>
     * @param
     * @return
     */
    public static function insert_es($index, $row) {
        $arrInput = self::convert_data($row);
        if ($index <= 0) {
            Bingo_Log::warning("id = [$index] is  invaild,");
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $index = self::ES_INDEX_PREFIX . $index;

        $arrOutput = Tieba_Ral::call("ueges", "post", $arrInput, rand(), array("pathinfo" => $index . '/post'));
        Bingo_Log::notice("[es insert] row[" . serialize($row) . "],output[" . serialize($arrOutput) . "]");

        if ($arrOutput == false || !is_array($arrOutput)) {
            Bingo_Log::warning("call ueges:post failed[es insert]! input[" . serialize($arrInput) . "],output[" . serialize($arrOutput) . "]");
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    /**
     * [update_es 所有代码请重构]
     * <AUTHOR>
     * @param
     * @return
     */
    public static function update_es($index, $row, $conds = null, $appends = null) {
        if ($index < 0) {
            Bingo_Log::warning("index = [$index] is  invaild,");
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if ($index == 0) {
            $index = '*';
        }
        $index  = self::ES_INDEX_PREFIX . $index;

        $row    = self::convert_data($row);
        $query  = self::convert_cond($conds);
        $script = self::convert_script($row);
        $append = self::convert_append($appends);

        $arrInput = array();

        if ($append["size"] > 0) {
            $arrInput["size"] = $append["size"];
        }

        $arrInput["query"]  = $query;
        $arrInput["sort"]  = array(
            'op_time' => array(
                'order' => 'desc',
            ),
        );
        $arrInput["script"] = $script;

        $arrOutput = Tieba_Ral::call("ueges", "post", $arrInput, rand(), array("pathinfo" => $index . '/post/_update_by_query'));
        Bingo_Log::notice("[es update] row[" . serialize($row) . "],output[" . serialize($arrOutput) . "]");

        if (!is_array($arrOutput)) {
            Bingo_Log::warning('call ueges post failed');
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, intval($arrOutput["updated"]));
    }

    /**
     * [select_es description]
     * <AUTHOR>
     * @param
     * @return
     */
    public static function select_es($index, $conds = null, $appends = null) {
        if ($index < 0) {
            Bingo_Log::warning("index = [$index] is  invaild,");
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if ($index == 0) {
            $index = '*';
        }
        $index  = self::ES_INDEX_PREFIX . $index;

        $query  = self::convert_cond($conds);
        $append = self::convert_append($appends);

        $arrInput = array(
            "from"  => $append["from"],
            "size"  => $append["size"],
            "query" => $query,
        );

        $arrOutput = Tieba_Ral::call("ueges", "post", $arrInput, rand(), array("pathinfo" => $index . '/post/_search'));

        $result = array();
        if (is_array($arrOutput) && isset($arrOutput["hits"]["hits"])) {
            foreach($arrOutput["hits"]["hits"] as $hit) {
                $hit["_source"]["req_url"]      = strval($hit["_source"]["req_url"]);
                $hit["_source"]["req_input"]    = strval($hit["_source"]["req_input"]);
                $hit["_source"]["req_output"]   = strval($hit["_source"]["req_output"]);
                $hit["_source"]["status"]       = intval($hit["_source"]["status"]);
                $hit["_source"]["call_from"]   = strval($hit["_source"]["call_from"]);
                $hit["_source"]["ext_desc"]     = strval($hit["_source"]["ext_desc"]);
                $hit["_source"]["op_id"]        = intval($hit["_source"]["op_id"]);
                $hit["_source"]["op_name"]      = strval($hit["_source"]["op_name"]);
                $hit["_source"]["op_time"]      = intval($hit["_source"]["op_time"]);
                $result[] = $hit["_source"];
            }
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $result);
    }

    /**
     * [count_es description]
     * <AUTHOR>
     * @param
     * @return
     */
    public static function count_es($index, $conds = null) {
        if ($index < 0) {
            Bingo_Log::warning("index = [$index] is  invaild,");
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if ($index == 0) {
            $index = '*';
        }
        $index = self::ES_INDEX_PREFIX . $index;

        $query = self::convert_cond($conds);
        $arrInput = array(
            "query" => $query
        );
        $arrOutput = Tieba_Ral::call("ueges", "post", $arrInput, rand(), array("pathinfo" => $index . '/post/_count'));
        Bingo_Log::notice("es count: input[" . serialize($arrInput) . "] output:[" . serialize($arrOutput) . "]");

        if (!is_array($arrOutput)) {
            Bingo_Log::warning('call ueges post failed');
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, intval($arrOutput["count"]));
    }

    /**
     * [count_es description]
     * <AUTHOR>
     * @param
     * @return
     */
    public static function select_es_cnt($index, $conds = null, $appends = null) {
        if ($index < 0) {
            Bingo_Log::warning("index = [$index] is  invaild,");
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if ($index == 0) {
            $index = '*';
        }
        $index  = self::ES_INDEX_PREFIX . $index;
        $query  = self::convert_cond($conds);
        $append = self::convert_append($appends);

        // 查询记录
        $arrInputData = array(
            "from"  => $append["from"],
            "size"  => $append["size"],
            "query" => $query,
        );
        $arrOutputData = Tieba_Ral::call("ueges", "post", $arrInputData, rand(), array("pathinfo" => $index . '/post/_search'));
        Bingo_Log::notice("es select: input[" . serialize($arrInputData) . "] output:[" . serialize($arrOutputData) . "]");

        // 查询数量
        $arrInputCnt = array(
            "query" => $query,
        );
        $arrOutputCnt = Tieba_Ral::call("ueges", "post", $arrInputCnt, rand(), array("pathinfo" => $index . '/post/_count'));
        Bingo_Log::notice("es count: input[" . serialize($arrInputCnt) . "] output:[" . serialize($arrOutputCnt) . "]");
        if (!is_array($arrOutputCnt)) {
            Bingo_Log::warning('call ueges post failed');
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $result = array();
        if (is_array($arrOutputData) && isset($arrOutputData["hits"]["hits"])) {
            foreach($arrOutputData["hits"]["hits"] as $hit) {
                $hit["_source"]["req_url"]      = strval($hit["_source"]["req_url"]);
                $hit["_source"]["req_input"]    = strval($hit["_source"]["req_input"]);
                $hit["_source"]["req_output"]   = strval($hit["_source"]["req_output"]);
                $hit["_source"]["status"]       = intval($hit["_source"]["status"]);
                $hit["_source"]["call_from"]   = strval($hit["_source"]["call_from"]);
                $hit["_source"]["ext_desc"]     = strval($hit["_source"]["ext_desc"]);
                $hit["_source"]["op_id"]        = intval($hit["_source"]["op_id"]);
                $hit["_source"]["op_name"]      = strval($hit["_source"]["op_name"]);
                $hit["_source"]["op_time"]      = intval($hit["_source"]["op_time"]);
                $result['rows'][] = $hit["_source"];
            }
        }
        $result['count'] = min(intval($arrOutputCnt["count"]), 10000);
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $result);
    }

    /**
     * [convert_data 所有代码请重构]
     * <AUTHOR>
     * @param
     * @return
     */
    protected static function convert_data($row) {
        if (isset($row["primary_key"])) {
            $row["id"] = $row["primary_key"];
        }
        if (isset($row["req_url"])) {
            $row["req_url"] = strval($row["req_url"]);
        }
        if (isset($row["req_input"])) {
            $row["req_input"] = serialize($row["req_input"]);
        }
        if (isset($row["req_output"])) {
            $row["req_output"] = serialize($row["req_output"]);
        }
        if (isset($row["status"])) {
            $row["status"] = intval($row["status"]);
        }
        if (isset($row["op_id"])) {
            $row["op_id"] =  intval($row["op_id"]);
        }
        if (isset($row["op_time"])) {
            $row["op_time"] =  intval($row["op_time"]);
        }
        return $row;
    }

    /**
     * [convert_script description]
     * <AUTHOR>
     * @param
     * @return
     */
    protected static function convert_script($row) {
        $arrScript = array();
        foreach ($row as $key => $val) {
            if (is_integer($val)) {
                $arrScript[] = "ctx._source." . $key . "=" . $val . "L";
            } else if (is_string($val)) {
                $arrScript[] = "ctx._source." . $key . "=\"" . $val . "\"";
            }
        }
        return array(
            "inline"    => join(";", $arrScript),
            "lang"      => "painless"
        );
    }

    /**
     * [convert_append description]
     * <AUTHOR>
     * @param
     * @return
     */
    protected static function convert_append($append) {
        if (!is_null($append)) {
            preg_match("/limit\s+(\d+)\s*(,\s*(\d+))?$/miU", $append, $arr);
            if (count($arr) == 2) {
                return array(
                    "size" => intval($arr[1]),
                    "from" => 0,
                );
            } else if (count($arr) == 4) {
                return array(
                    "size" => intval($arr[3]),
                    "from" => intval($arr[1])
                );
            }
        }

        return array(
            "from" => 0,
            "size" => 10000,
        );
    }

    /**
     * [convert_cond 所有代码请重构,只是临时修补]
     * <AUTHOR>
     * @param
     * @return
     */
    protected static function convert_cond($conds) {
        $arrMust = array();
        $arrMust[] = array(
            "query_string" => array(
                "query" => "*",
                "analyze_wildcard" => true
            )
        );
        if (!empty($conds)) {
            foreach($conds as $cond) {
                if (!isset($cond[1])) {
                    continue;
                }

                switch ($cond[1]) {
                    case '=':
                        $arrMust[] = array(
                            "term" => array(
                                $cond[0] => $cond[2],
                            )
                        );
                        break;
                    case 'in': //in查询
                        $arrMust[] = array(
                            "terms" => array(
                                $cond[0] => $cond[2],
                            )
                        );
                        break;
                    case '>': //>查询
                        $arrMust[] = array(
                            "range" => array(
                                $cond[0] => array(
                                    "gt" => $cond[2],
                                )
                            )
                        );
                        break;
                    case '>=': //>=查询
                        $arrMust[] = array(
                            "range" => array(
                                $cond[0] => array(
                                    "gte" => $cond[2],
                                )
                            )
                        );
                        break;
                    case '<':
                        $arrMust[] = array(
                            "range" => array(
                                $cond[0] => array(
                                    "lt" => $cond[2],
                                )
                            )
                        );
                        break;
                    case '<=':
                        $arrMust[] = array(
                            "range" => array(
                                $cond[0] => array(
                                    "lte" => $cond[2],
                                )
                            )
                        );
                        break;
                    case 'like':
                        $arrMust[] = array(
                            "match" => array(
                                $cond[0] => $cond[2],
                            )
                        );
                        break;
                }
            }
        }

        return array(
            "bool" => array(
                "must" => $arrMust
            )
        );
    }

    /**
     * 规范化返回结果
     * @param
     * @return
     */
    private static function errRet($errno, $data = array()) {
        $arrRet = array(
            'errno'     => $errno,
            'errmsg'    => Tieba_Error::getErrmsg($errno),
        );
        if (!empty($data)) {
            $arrRet['data'] = $data;
        }
        return $arrRet;
    }
}







/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
