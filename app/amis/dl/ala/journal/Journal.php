<?php
/**
 * Created by PhpStorm.
 * User: zhanghanqing
 * Date: 2017/7/18
 * Time: 15:05
 */

class Dl_Ala_Journal_Journal extends Dl_Ala_Base {

    const MODULE_NAME = 'ala';

    const SERVICE_NAME = 'Dl_Ala_Journal_Journal';

    const DB_CHARSET = 'utf8';

    // fixme
    const DATABASE_NAME = 'forum_ala';

    const TABLE_LIVE_JOURNAL_ANCHOR = 'amis_live_anchor_journal';

    const TABLE_LIVE_SCENE_JOURNAL_ANCHOR = 'amis_live_anchor_scene_journal';

    const TABLE_LIVE_JOURNAL_UNION = 'amis_live_union_journal';

    const TABLE_LIVE_JOURNAL_ANCHOR_SCENE = 'amis_live_anchor_scene_journal';

    const TABLE_NAME_OPERATOR_FLOWER = 'yunying_live_flower_send';

    const TABLE_NAME_PETAL = 'currency_petal';

    private static $_db = null;

    private static $_conf = null;

    /**
     * @param $errno
     * @return array
     */
    private static function _errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Alalib_Conf_Error::getErrorMsg($errno)
        );
    }

    /**
     * @return Bd_DB|null
     */
    private static function _getDB()
    {
        if (self::$_db) {
            return self::$_db;
        }
        self::$_db = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if (self::$_db == null || ! self::$_db->isConnected()) {
            Bingo_Log::warning('db connect fail.');
            return null;
        }
        self::$_db->query("set names " . self::DB_CHARSET);
        return self::$_db;
    }



    /**
     * @return array|bool
     */
    private static function _init()
    {
        if (self::_getDB() == null) {
            Bingo_Log::warning("init db fail.");
            return self::_errRet(Alalib_Conf_Error::ERR_DB_CONN_FAIL);
        }
        if (self::$_conf == null) {
            $dlConfFile = '/app/amis/' . self::MODULE_NAME . '/' . strtolower(self::SERVICE_NAME);
            self::$_conf = Bd_Conf::getConf($dlConfFile);
            if (self::$_conf == false) {
                Bingo_Log::warning('init get conf fail.' . $dlConfFile);
                return self::_errRet(Alalib_Conf_Error::ERR_LOAD_CONFIG_FAIL);
            }
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function execSql($arrInput)
    {
        if (! isset($arrInput['function'])) {
            Bingo_Log::warning('input params invalid: function is empty. [' . serialize($arrInput) . ']');
            return self::_errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }
        $ret = self::_init();
        if ($ret !== true) {
            return $ret;
        }
        Bingo_Timer::start('initlib');
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        Bingo_Timer::end('initlib');
        if ($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::_errRet(Alalib_Conf_Error::ERR_DB_CONN_FAIL);
        }
        $bolPrintSql = true;
        $arrOut = $mdb->execSql($arrInput, $bolPrintSql);
        return $arrOut;
    }

    /**
     * @param $arrInputs
     * @return array|bool
     */
    public static function execSqlWithTransaction($arrInputs) {
        $ret = self::_init();
        if ($ret !== true) {
            return $ret;
        }

        Bingo_Timer::start('initlib');
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        Bingo_Timer::end('initlib');
        if ($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::_errRet(Alalib_Conf_Error::ERR_DB_CONN_FAIL);
        }

        $ret = self::$_db->startTransaction();
        if ($ret === false) {
            return false;
        }

        $arrOut = false;
        foreach ($arrInputs as $arrInput) {
            $arrOut = $mdb->execSql($arrInput, true);
            if (false === $arrOut || Alalib_Conf_Error::ERR_SUCCESS != $arrOut["errno"]) {
                self::$_db->rollback();
                return false;
            }
        }

        $ret = self::$_db->commit();
        if ($ret === false) {
            self::$_db->rollback();
            return false;
        }
        return $arrOut;
    }


    /**
     * getAnchorJournalCount   主播总数
     * @param array
     * @return array
     * */
    public static function getAnchorJournalCount($arrParamInput)
    {
        if(empty($arrParamInput["anchor_type"]) || empty($arrParamInput["start_time"]) || empty($arrParamInput["end_time"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUserId = intval($arrParamInput['user_id']);
        $arrUserIds = $arrParamInput['user_ids'];
        $strUserName = strval($arrParamInput['user_name']);
        $intAnchorType = intval($arrParamInput['anchor_type']);
        $strSearchOrder = strval($arrParamInput['search_order']);
        $intStartTime= intval($arrParamInput['start_time']);
        $intUnionId = intval($arrParamInput['union_id']);
        $strUnionIds = strval($arrParamInput['union_ids']);
        $intEndTime = intval($arrParamInput['end_time']);
        $strConditions = '';
        $strSqlWhere = ' anchor_type = '.$intAnchorType.' and date >= '.$intStartTime.' and date <= '.$intEndTime;
        // $strSqlOrder = ' order by income_td '.$strSearchOrder;
        if(!empty($intUserId)) {
            $strSqlWhere .= ' and user_id = '.$intUserId;
        }
        if(!empty($arrUserIds)) {
            $strSqlWhere .= ' and user_id in ('. implode(',', $arrUserIds) . ')';
        }
        if('' != $strUserName) {
            $strSqlWhere .= " and user_name like '%".$strUserName."%' ";
        }
        if(!empty($intUnionId)) {
            $strSqlWhere .= ' and union_id = '.$intUnionId;
        }
        if (!empty($strUnionIds)) {
            $strSqlWhere .= ' and union_id in ( '.($strUnionIds) .' )';
        }

        $strConditions = $strSqlWhere;

        $arrInput = array(
            'function'   => 'getAnchorJournalCount',
            'table_name' => self::TABLE_LIVE_JOURNAL_ANCHOR,
            'conditions' => $strConditions,
        );

        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRetData);
    }


    /**
     * getAnchorJournalCount   主播流水信息及主播等级检索
     * @param array
     * @return array
     * */
    public static function getAnchorJournalAndLevel($arrParamInput)
    {
        if(empty($arrParamInput["start_time"]) || empty($arrParamInput["end_time"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $strSearchUserLevel = strval($arrParamInput['anchor_level_present']);
        $strSearchOrder = strval($arrParamInput['search_order']);
        $intStartDayDate = intval($arrParamInput['start_time']);
        $intEndDayDate = intval($arrParamInput['end_time']);
        $strOrderBy = strval($arrParamInput['order_by']);
        $bolCount = $arrParamInput['count'];
        $intOffset = intval($arrParamInput['offset']);
        $intLimit = intval($arrParamInput['limit']);

        $arrInput = array(
            'table_name' => self::TABLE_LIVE_JOURNAL_ANCHOR,
            'anchor_level_present' => $strSearchUserLevel,
            'order_by' => $strOrderBy,
            'search_order' => $strSearchOrder,
            'start_time' => $intStartDayDate,
            'end_time' => $intEndDayDate,
            'offset' => $intOffset,
            'limit'  => $intLimit,
            'count' => $bolCount,
        );
        if($bolCount) {
            $arrInput['function'] = 'getAnchorJournalAndLevelCount';
            if (!empty($strSearchUserLevel)) {
                $arrInput['anchor_level_present'] = "\"".$strSearchUserLevel."\"";
                $arrInput['function'] = 'getAnchorJournalAndLevelCountAndLevel';
            }
        }else{
            $arrInput['function'] = 'getAnchorJournalAndLevel';
            if (!empty($strSearchUserLevel)) {
                $arrInput['anchor_level_present'] = "\"".$strSearchUserLevel."\"";
                $arrInput['function'] = 'getAnchorJournalAndLevelSelect';
            }
        }
        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRetData);
    }

    /**
     * getAnchorJournalList  主播流水列表--月纬度
     * @param array
     * @return array
     * */
    public static function getAnchorJournalListOfMonth($arrParamInput)
    {
        if( empty($arrParamInput["start_time"]) || empty($arrParamInput["end_time"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUserId = intval($arrParamInput['user_id']);
        $arrUserIds = $arrParamInput['user_ids'];
        $strUserName = strval($arrParamInput['user_name']);
        $intAnchorType = intval($arrParamInput['anchor_type']);
        $strSearchOrder = strval($arrParamInput['search_order']);
        $strOrderBy = strval($arrParamInput['order_by']);
        $strOrderBy = ('' == $strOrderBy) ? 'income_td' : $strOrderBy;
        $intStartTime= intval($arrParamInput['start_time']);
        $intUnionId = intval($arrParamInput['union_id']);
        $intEndTime = intval($arrParamInput['end_time']);
        $intOffset = intval(isset($arrParamInput['offset']) ? $arrParamInput['offset'] : 0);
        $intLimit = intval(isset($arrParamInput['limit']) ? $arrParamInput['limit'] : 5);

        $strConditions = ' ';
        $strSqlWhere = ' date >= '.$intStartTime.' and date <= '.$intEndTime;
        if(!empty($intAnchorType)){
            $strSqlWhere .= ' and anchor_type = '.$intAnchorType;
        }
        $strSqlOrder = ' order by '.$strOrderBy.' '.$strSearchOrder;
        if(!empty($intUserId)) {
            $strSqlWhere .= ' and user_id = '.$intUserId;
        }
        if(!empty($arrUserIds)) {
            $strSqlWhere .= ' and user_id in ('. implode(',', $arrUserIds) . ')';
        }
        if('' != $strUserName) {
            $strSqlWhere .= " and user_name like '%".$strUserName."%' ";
        }
        if(!empty($intUnionId)) {
            $strSqlWhere .= ' and union_id = '.$intUnionId;
        }
        $strUnionIds = strval($arrParamInput['union_ids']);
        if (!empty($strUnionIds)) {
            $strSqlWhere .= ' and union_id in ( '.($strUnionIds) .' )';
        }
        $strSqlWhere .= ' and anchor_type in (1,2) ';
        $strConditions = $strSqlWhere;

        $arrInput = array(
            'function'   => 'getAnchorJournalListOfMonth',
            'table_name' => self::TABLE_LIVE_JOURNAL_ANCHOR,
            'conditions' => $strConditions,
            'offset' => $intOffset,
            'limit' => $intLimit,
        );

        if(isset($arrParamInput['count'])){
            if($arrParamInput['count']){
                $arrInput['function'] = 'getAnchorJournalListOfMonthCount';
            }
        }

        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRetData);

    }


    /**
     * getAnchorJournalList  主播直播时长列表--天纬度
     * @param array
     * @return array
     * */
    public static function getAnchorDurationOfDay($arrParamInput)
    {
        if( empty($arrParamInput["start_time"]) || empty($arrParamInput["end_time"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrUserIds = $arrParamInput['user_ids'];
        $intStartTime= intval($arrParamInput['start_time']);
        $intEndTime = intval($arrParamInput['end_time']);
        $strConditions = ' ';
        $strSqlWhere = ' date >= '.$intStartTime.' and date <= '.$intEndTime;

        if(!empty($arrUserIds)) {
            $strSqlWhere .= ' and user_id in ('. implode(',', $arrUserIds) . ')';
        }
        $strSqlWhere .= ' and anchor_type in (1,2) ';
        $strConditions = $strSqlWhere;

        $arrInput = array(
            'function'   => 'getAnchorDurationOfDay',
            'table_name' => self::TABLE_LIVE_JOURNAL_ANCHOR,
            'conditions' => $strConditions,
        );
        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRetData);

    }

    /**
     * getAnchorJournalList  主播流水列表
     * @param array
     * @return array
     * */
    public static function getAnchorJournalList($arrParamInput)
    {
        if( empty($arrParamInput["start_time"]) || empty($arrParamInput["end_time"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUserId = intval($arrParamInput['user_id']);
        $arrUserIds = $arrParamInput['user_ids'];
        $strUserName = strval($arrParamInput['user_name']);
        $intAnchorType = intval($arrParamInput['anchor_type']);
        $strSearchOrder = strval($arrParamInput['search_order']);
        $strOrderBy = strval($arrParamInput['order_by']);
        $strOrderBy = ('' == $strOrderBy) ? 'income_td' : $strOrderBy;
        $intStartTime= intval($arrParamInput['start_time']);
        $intUnionId = intval($arrParamInput['union_id']);
        $intEndTime = intval($arrParamInput['end_time']);
        $intOffset = intval(isset($arrParamInput['offset']) ? $arrParamInput['offset'] : 0);
        $intLimit = intval(isset($arrParamInput['limit']) ? $arrParamInput['limit'] : 5);

        $strConditions = '';
        $strSqlWhere = ' date >= '.$intStartTime.' and date <= '.$intEndTime;
        if(!empty($intAnchorType)){
            $strSqlWhere .= ' and anchor_type = '.$intAnchorType;
        }
        $strSqlOrder = ' order by '.$strOrderBy.' '.$strSearchOrder;
        if(!empty($intUserId)) {
            $strSqlWhere .= ' and user_id = '.$intUserId;
        }
        if(!empty($arrUserIds)) {
            $strSqlWhere .= ' and user_id in ('. implode(',', $arrUserIds) . ')';
        }
        if('' != $strUserName) {
            $strSqlWhere .= " and user_name like '%".$strUserName."%' ";
        }
        if(!empty($intUnionId)) {
            $strSqlWhere .= ' and union_id = '.$intUnionId;
        }
        $strUnionIds = strval($arrParamInput['union_ids']);
        if (!empty($strUnionIds)) {
            $strSqlWhere .= ' and union_id in ( '.($strUnionIds) .' )';
        }
        $strConditions = $strSqlWhere.$strSqlOrder;

        $arrInput = array(
            'function'   => 'getAnchorJournalList',
            'table_name' => self::TABLE_LIVE_JOURNAL_ANCHOR,
            'conditions' => $strConditions,
            'offset' => $intOffset,
            'limit' => $intLimit,
        );

        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRetData);

    }


    /**
     * getUnionJournalCount  公会流水总数
     * @param array
     * @return array
     * */
    public static function getUnionJournalCount($arrParamInput)
    {
        if(empty($arrParamInput["start_time"]) || empty($arrParamInput["end_time"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUnionId = intval($arrParamInput['union_id']);
        $strUnionName = strval($arrParamInput['union_name']);
        $strSearchOrder = strval($arrParamInput['search_order']);
        $intStartTime= intval($arrParamInput['start_time']);
        $intEndTime = intval($arrParamInput['end_time']);
        $intOffset = intval(isset($arrParamInput['offset']) ? $arrParamInput['offset'] : 0);
        $intLimit = intval(isset($arrParamInput['limit']) ? $arrParamInput['limit'] : 5);


        $strConditions = '';
        $strSqlWhere = ' date >= '.$intStartTime.' and date <= '.$intEndTime;
        // $strSqlOrder = ' order by income_td '.$strSearchOrder;
        if(!empty($intUnionId)) {
            $strSqlWhere .= ' and union_id = '.$intUnionId;
        }
        $strUnionIds = strval($arrParamInput['union_ids']);
        if (!empty($strUnionIds)) {
            $strSqlWhere .= ' and union_id in ( '.($strUnionIds) .' )';
        }
        if('' != $strUnionName) {
            $strSqlWhere .= " and union_name like '%".$strUnionName."%' ";
        }
        $strConditions = $strSqlWhere;

        $arrInput = array(
            'function'   => 'getUnionJournalCount',
            'table_name' => self::TABLE_LIVE_JOURNAL_UNION,
            'conditions' => $strConditions,
        );

        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRetData);

    }


    /**
     * getUnionJournalCount  公会流水列表
     * @param array
     * @return array
     * */
    public static function getUnionJournalList($arrParamInput)
    {
        if( empty($arrParamInput["start_time"]) || empty($arrParamInput["end_time"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUnionId = intval($arrParamInput['union_id']);
        $strUnionName = strval($arrParamInput['union_name']);
        $strSearchOrder = strval($arrParamInput['search_order']);
        $intStartTime= intval($arrParamInput['start_time']);
        $intEndTime = intval($arrParamInput['end_time']);
        $intOffset = intval(isset($arrParamInput['offset']) ? $arrParamInput['offset'] : 0);
        $intLimit = intval(isset($arrParamInput['limit']) ? $arrParamInput['limit'] : 5);
        $strOrderBy = strval($arrParamInput['order_by']);
        $strOrderBy = ('' == $strOrderBy) ? 'income_td' : $strOrderBy;

        $strConditions = '';
        $strSqlWhere = ' date >= '.$intStartTime.' and date <= '.$intEndTime;
        $strSqlOrder = ' order by '.$strOrderBy.' '.$strSearchOrder;
        if(!empty($intUnionId)) {
            $strSqlWhere .= ' and union_id = '.$intUnionId;
        }
        if('' != $strUnionName) {
            $strSqlWhere .= " and union_name like '%".$strUnionName."%' ";
        }
        $strConditions = $strSqlWhere.$strSqlOrder;

        $arrInput = array(
            'function'   => 'getUnionJournalList',
            'table_name' => self::TABLE_LIVE_JOURNAL_UNION,
            'conditions' => $strConditions,
            'offset' => $intOffset,
            'limit' => $intLimit,
        );


        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRetData);

    }

    /**
     * @param $arrParamInput
     * @return array
     */
    public static function getMonthJournalListWithType($arrParamInput){
        if( empty($arrParamInput["start_time"]) || empty($arrParamInput["end_time"]) || empty($arrParamInput['type'])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intType = intval($arrParamInput['type']);
        $intUnionId = intval($arrParamInput['union_id']);
        $intStartTime= intval($arrParamInput['start_time']);
        $intEndTime = intval($arrParamInput['end_time']);
        $arrUnionIds = $arrParamInput['union_ids'];

        if(2 == $intType) {   //按工会union_id 查
            $strSqlWhere = ' date >= '.$intStartTime.' and date <= '.$intEndTime;
            if(!empty($intUnionId)) {
                $strSqlWhere .= ' and union_id = '.$intUnionId;
            }
            if(!empty($arrUnionIds) && is_array($arrUnionIds)) {
                $strSqlWhere .= ' and union_id in ('. implode(',', $arrUnionIds) . ')';
            }
            $strConditions = $strSqlWhere;

            $arrInput = array(
                'function'   => 'getMonthJournalListWithType',
                'table_name' => self::TABLE_LIVE_JOURNAL_UNION,
                'conditions' => $strConditions,
            );
        }
        else {
            $strSqlWhere = ' date >= '.$intStartTime.' and date <= '.$intEndTime;
            if (!empty($arrParamInput['anchor_type'])) {
                $strSqlWhere .= ' and anchor_type in (' . implode(',', $arrParamInput['anchor_type']) . ')';
            }
            $strConditions = $strSqlWhere;
            $arrInput = array(
                'function'   => 'getMonthJournalListWithType',
                'table_name' => self::TABLE_LIVE_JOURNAL_ANCHOR,
                'conditions' => $strConditions,
            );
        }

        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrRetData = $arrOutput['results'][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRetData);

    }

    /**
     * addUnionJournal  添加公会流水
     * @param array
     * @return array
     * */
    public static function addUnionJournal($arrParamInput)
    {
        if(empty($arrParamInput["union_id"]) || '' == $arrParamInput["union_name"] || empty($arrParamInput["date"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUnionId = intval($arrParamInput["union_id"]);
        $strUnionName = strval($arrParamInput['union_name']);
        $intDate = intval($arrParamInput['date']);
        $intIncomeTD = intval($arrParamInput['income_td']);

        $arrInput = array(
            'function'   => 'addUnionJournal',
            'table_name' => self::TABLE_LIVE_JOURNAL_UNION,
            'union_id' => $intUnionId,
            'union_name' => $strUnionName,
            'date' => $intDate,
            'income_td' => $intIncomeTD,
        );
        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);

    }

    /**
     * addAnchorJournal  添加主播流水
     * @param array
     * @return array
     * */
    public static function addAnchorJournal($arrParamInput)
    {
        if(empty($arrParamInput["user_id"]) || empty($arrParamInput["anchor_type"]) || empty($arrParamInput["date"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUserId = intval($arrParamInput["user_id"]);
        $strUserName = strval($arrParamInput['user_name']);
        $intAnchorType= intval($arrParamInput['anchor_type']);
        $intDate = intval($arrParamInput['date']);

        if ($intAnchorType == Util_Ala_Journal::ANCHOR_TYPE_UNION) {
            if(empty($arrParamInput['union_id'])){
                $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
        }
        $intUnionId = intval($arrParamInput['union_id']);
        $intIncomeTD = intval($arrParamInput['income_td']);
        $intLiveDuration = intval($arrParamInput['live_duration']);
        $arrInput = array(
            'function'   => 'addAnchorJournal',
            'table_name' => self::TABLE_LIVE_JOURNAL_ANCHOR,
            'user_id' => $intUserId,
            'user_name' => $strUserName,
            'anchor_type' => $intAnchorType,
            'date' => $intDate,
            'union_id' => $intUnionId,
            'income_td' => $intIncomeTD,
            'live_duration' => $intLiveDuration,
        );
        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * getAnchorJournalCount   主播总数
     * @param array
     * @return array
     * */
    public static function getAnchorSumJournalCount($arrParamInput)
    {
        if(empty($arrParamInput["anchor_type"]) || empty($arrParamInput["start_time"]) || empty($arrParamInput["end_time"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUserId = intval($arrParamInput['user_id']);
        $arrUserIds = $arrParamInput['user_ids'];
        $strUserName = strval($arrParamInput['user_name']);
        $intAnchorType = intval($arrParamInput['anchor_type']);
        $strSearchOrder = strval($arrParamInput['search_order']);
        $intStartTime= intval($arrParamInput['start_time']);
        $intUnionId = intval($arrParamInput['union_id']);
        $intEndTime = intval($arrParamInput['end_time']);
        $strUnionIds = strval($arrParamInput['union_ids']);
        $strConditions = '';
        $strSqlWhere = ' anchor_type = '.$intAnchorType.' and date >= '.$intStartTime.' and date <= '.$intEndTime;
        // $strSqlOrder = ' order by income_td '.$strSearchOrder;
        if(!empty($intUserId)) {
            $strSqlWhere .= ' and user_id = '.$intUserId;
        }
        if(!empty($arrUserIds)) {
            $strSqlWhere .= ' and user_id in ('. implode(',', $arrUserIds) . ')';
        }
        if('' != $strUserName) {
            $strSqlWhere .= " and user_name like '%".$strUserName."%' ";
        }
        if(!empty($intUnionId)) {
            unset($strUnionIds);
            $strSqlWhere .= ' and union_id = '.$intUnionId;
        }
        if (!empty($strUnionIds)) {
            $strSqlWhere .= ' and union_id in ( '.($strUnionIds) .' )';
        }
        $strConditions = $strSqlWhere;

        $arrInput = array(
            'function'   => 'getAnchorSumJournalCount',
            'table_name' => self::TABLE_LIVE_JOURNAL_ANCHOR,
            'conditions' => $strConditions,
        );

        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRetData);
    }


    /**
     * getAnchorJournalCount   主播总数
     * @param array
     * @return array
     * */
    public static function getAnchorPlatformJournal($arrParamInput) {
        if(empty($arrParamInput["type"]) || empty($arrParamInput["start_time"]) || empty($arrParamInput["end_time"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intType = intval($arrParamInput['type']);
        $intStartTime= intval($arrParamInput['start_time']);
        $intEndTime = intval($arrParamInput['end_time']);
        $strSqlWhere = '';
        $strTableName = '';
        if(1 == $intType) {  //pgc
            $strTableName = self::TABLE_LIVE_JOURNAL_UNION;
            $strSqlWhere = ' date >= '.$intStartTime.' and date <= '.$intEndTime;
        }
        else if(2 == $intType) {  //ugc
            $strTableName = self::TABLE_LIVE_JOURNAL_ANCHOR;
            $strSqlWhere = ' anchor_type = 2 and date >= '.$intStartTime.' and date <= '.$intEndTime;
        }
        else if(3 == $intType) {  //秀场
            $strTableName = self::TABLE_LIVE_JOURNAL_ANCHOR_SCENE;
            $strSqlWhere = ' scene_id in (6200003, 6200004, 8000001, 8000002) and date >= '.$intStartTime.' and date <= '.$intEndTime;
        }
        else if(4 == $intType) {   //游戏
            $strTableName = self::TABLE_LIVE_JOURNAL_ANCHOR_SCENE;
            $strSqlWhere = ' scene_id in (6200005, 6200006) and date >= '.$intStartTime.' and date <= '.$intEndTime;
        }
        else if(5 == $intType) {  //总流水
            $strTableName = self::TABLE_LIVE_JOURNAL_ANCHOR;
            $strSqlWhere = ' date >= '.$intStartTime.' and date <= '.$intEndTime;
        }
        $arrInput = array(
            'function'   => 'getAnchorPlatformJournal',
            'table_name' => $strTableName,
            'conditions' => $strSqlWhere,
        );
        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRetData);
    }


    /**
     * getAnchorJournalCount   主播总数
     * @param array
     * @return array
     * */
    public static function getAnchorCooperationJournal($arrParamInput) {
        if(empty($arrParamInput["union_ids"]) || empty($arrParamInput["start_time"]) || empty($arrParamInput["end_time"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrUnionIds = $arrParamInput['union_ids'];
        $intStartTime= intval($arrParamInput['start_time']);
        $intEndTime = intval($arrParamInput['end_time']);

        $strTableName = self::TABLE_LIVE_JOURNAL_UNION;
        $strSqlWhere = ' union_id in (' . implode(',', $arrUnionIds) . ') and date >= '.$intStartTime.' and date <= '.$intEndTime;

        $arrInput = array(
            'function'   => 'getAnchorPlatformJournal',
            'table_name' => $strTableName,
            'conditions' => $strSqlWhere,
        );

        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRetData);
    }


    /**
     * getAnchorJournalList  主播流水列表
     * @param array
     * @return array
     * */
    public static function getAnchorSumJournalList($arrParamInput)
    {
        if( empty($arrParamInput["start_time"]) || empty($arrParamInput["end_time"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrSearchUserId = !empty($arrParamInput['user_id']) ? (array)$arrParamInput['user_id'] : array();
        $strUserName = strval($arrParamInput['user_name']);
        $intAnchorType = intval($arrParamInput['anchor_type']);
        $strSearchOrder = strval($arrParamInput['search_order']);
        $intStartTime= intval($arrParamInput['start_time']);
        $intUnionId = intval($arrParamInput['union_id']);
        $intEndTime = intval($arrParamInput['end_time']);
        $intOffset = intval(isset($arrParamInput['offset']) ? $arrParamInput['offset'] : 0);
        $intLimit = intval(isset($arrParamInput['limit']) ? $arrParamInput['limit'] : 5);
        $strUnionIds = strval($arrParamInput['union_ids']);
        $strConditions = '';
        $strSqlWhere = ' date >= '.$intStartTime.' and date <= '.$intEndTime;
        if(!empty($intAnchorType)){
            $strSqlWhere .= ' and anchor_type = '.$intAnchorType;
        }
        //$strSqlOrder = ' order by income_td '.$strSearchOrder;
        if(!empty($arrSearchUserId)) {
            $strSqlWhere .= ' and user_id IN ('.implode(',', $arrSearchUserId).')';
        }
        if('' != $strUserName) {
            $strSqlWhere .= " and user_name like '%".$strUserName."%' ";
        }
        if(!empty($intUnionId)) {
            unset($strUnionIds);
            $strSqlWhere .= ' and union_id = '.$intUnionId;
        }
        if (!empty($strUnionIds)) {
            $strSqlWhere .= ' and union_id in ( '.($strUnionIds) .' )';
        }
        $strConditions = $strSqlWhere;

        $arrInput = array(
            'function'   => 'getAnchorSumJournalList',
            'table_name' => self::TABLE_LIVE_JOURNAL_ANCHOR,
            'conditions' => $strConditions,
            'order_type' => $strSearchOrder,
            'offset' => $intOffset,
            'limit' => $intLimit,
        );

        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRetData);

    }

    /**
     * getAnchorJournalList  分批查出直播时长非0的流水主播，脚本数据回朔最大直播时长使用
     * @param array
     * @return array
     * */
    public static function getAnchorJournalListOfMaxLive($arrParamInput)
    {
        if(empty($arrParamInput["limit"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intOffset = $arrParamInput['offset'];
        $intLimit =  $arrParamInput['limit'];

        $arrInput = array(
            'function'   => 'getAnchorJournalListOfMaxLive',
            'table_name' => self::TABLE_LIVE_JOURNAL_ANCHOR,
            'offset' => $intOffset,
            'limit' => $intLimit,
        );
        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRetData);

    }
    
    /**
     * getAnchorJournalList  查询每天主播流水信息
     * @param array
     * @return array
     * */
    public static function getAnchorJournalListOfDay($arrParamInput)
    {
        if(empty($arrParamInput["date"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intDate = $arrParamInput['date'];

        $arrInput = array(
            'function'   => 'getAnchorJournalListOfDay',
            'table_name' => self::TABLE_LIVE_JOURNAL_ANCHOR,
            'date' => $intDate,
        );
        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRetData);

    }
    
    /**
     * 返回值
     * @param {Array} $arrAnchorUserId
     * @return {Array} :errno :errmsg :{Array}output
     */
    public static function getAmisSumIncomeJournal($arrParamInput)
    {
        if(empty($arrParamInput["anchor_type"]) || empty($arrParamInput["start_time"]) || empty($arrParamInput["end_time"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUserId = intval($arrParamInput['user_id']);
        $strUserName = strval($arrParamInput['user_name']);
        $intAnchorType = intval($arrParamInput['anchor_type']);
        $intStartTime= intval($arrParamInput['start_time']);
        $intUnionId = intval($arrParamInput['union_id']);
        $intEndTime = intval($arrParamInput['end_time']);
        $strUnionIds = strval($arrParamInput['union_ids']);
        $strConditions = '';
        $strSqlWhere = ' anchor_type = '.$intAnchorType.' and date >= '.$intStartTime.' and date <= '.$intEndTime;
        // $strSqlOrder = ' order by income_td '.$strSearchOrder;
        if(!empty($intUserId)) {
            $strSqlWhere .= ' and user_id = '.$intUserId;
        }
        if('' != $strUserName) {
            $strSqlWhere .= " and user_name like '%".$strUserName."%' ";
        }
        if(!empty($intUnionId)) {
            unset($strUnionIds);
            $strSqlWhere .= ' and union_id = '.$intUnionId;
        }
        if (!empty($strUnionIds)) {
            $strSqlWhere .= ' and union_id in ( '.($strUnionIds) .' )';
        }
        $strConditions = $strSqlWhere;

        $arrInput = array(
            'function'   => 'getAmisSumIncomeJournal',
            'table_name' => self::TABLE_LIVE_JOURNAL_ANCHOR,
            'conditions' => $strConditions,
        );

        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRetData);
    }


    /**
     * 返回值
     * @param {Array} $arrAnchorUserId
     * @return {Array} :errno :errmsg :{Array}output
     */
    public static function getAmisSumUnionIncomeJournal($arrParamInput)
    {
        if(empty($arrParamInput["start_time"]) || empty($arrParamInput["end_time"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUnionId = intval($arrParamInput['union_id']);
        $strUnionName = strval($arrParamInput['union_name']);
        $intStartTime= intval($arrParamInput['start_time']);
        $intEndTime = intval($arrParamInput['end_time']);
        $strUnionIds = strval($arrParamInput['union_ids']);

        $strConditions = '';
        $strSqlWhere = ' date >= '.$intStartTime.' and date <= '.$intEndTime;
        // $strSqlOrder = ' order by income_td '.$strSearchOrder;
        if('' != $strUnionName) {
            $strSqlWhere .= " and union_name like '%".$strUnionName."%' ";
        }
        if(!empty($intUnionId)) {
            unset($strUnionIds);
            $strSqlWhere .= ' and union_id = '.$intUnionId;
        }
        if (!empty($strUnionIds)) {
            $strSqlWhere .= ' and union_id in ( '.($strUnionIds) .' )';
        }
        $strConditions = $strSqlWhere;

        $arrInput = array(
            'function'   => 'getAmisSumUnionIncomeJournal',
            'table_name' => self::TABLE_LIVE_JOURNAL_UNION,
            'conditions' => $strConditions,
        );

        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRetData);
    }



    /**
     * getUnionJournalCount  公会流水总数
     * @param array
     * @return array
     * */
    public static function getUnionSumJournalCount($arrParamInput)
    {
        if(empty($arrParamInput["start_time"]) || empty($arrParamInput["end_time"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUnionId = intval($arrParamInput['union_id']);
        $strUnionName = strval($arrParamInput['union_name']);
        $strSearchOrder = strval($arrParamInput['search_order']);
        $intStartTime= intval($arrParamInput['start_time']);
        $intEndTime = intval($arrParamInput['end_time']);
        $intOffset = intval(isset($arrParamInput['offset']) ? $arrParamInput['offset'] : 0);
        $intLimit = intval(isset($arrParamInput['limit']) ? $arrParamInput['limit'] : 5);
        $strUnionIds = strval($arrParamInput['union_ids']);


        $strConditions = '';
        $strSqlWhere = ' date >= '.$intStartTime.' and date <= '.$intEndTime;
        //$strSqlOrder = ' order by income_td '.$strSearchOrder;
        if(!empty($intUnionId)) {
            unset($strUnionIds);
            $strSqlWhere .= ' and union_id = '.$intUnionId;
        }
        if (!empty($strUnionIds)) {
            $strSqlWhere .= ' and union_id in ( '.($strUnionIds) .' )';
        }
        if('' != $strUnionName) {
            $strSqlWhere .= " and union_name like '%".$strUnionName."%' ";
        }
        $strConditions = $strSqlWhere;

        $arrInput = array(
            'function'   => 'getUnionSumJournalCount',
            'table_name' => self::TABLE_LIVE_JOURNAL_UNION,
            'conditions' => $strConditions,
        );

        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRetData);

    }


    /**
     * getUnionJournalCount  公会流水列表
     * @param array
     * @return array
     * */
    public static function getUnionSumJournalList($arrParamInput)
    {
        if( empty($arrParamInput["start_time"]) || empty($arrParamInput["end_time"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUnionId = intval($arrParamInput['union_id']);
        $strUnionName = strval($arrParamInput['union_name']);
        $strSearchOrder = strval($arrParamInput['search_order']);
        $intStartTime= intval($arrParamInput['start_time']);
        $intEndTime = intval($arrParamInput['end_time']);
        $intOffset = intval(isset($arrParamInput['offset']) ? $arrParamInput['offset'] : 0);
        $intLimit = intval(isset($arrParamInput['limit']) ? $arrParamInput['limit'] : 5);
        $strUnionIds = strval($arrParamInput['union_ids']);


        $strConditions = '';
        $strSqlWhere = ' date >= '.$intStartTime.' and date <= '.$intEndTime;
        // $strSqlOrder = ' order by income_td '.$strSearchOrder;
        if(!empty($intUnionId)) {
            unset($strUnionIds);
            $strSqlWhere .= ' and union_id = '.$intUnionId;
        }
        if (!empty($strUnionIds)) {
            $strSqlWhere .= ' and union_id in ( '.($strUnionIds) .' )';
        }
        if('' != $strUnionName) {
            $strSqlWhere .= " and union_name like '%".$strUnionName."%' ";
        }
        $strConditions = $strSqlWhere;

        $arrInput = array(
            'function'   => 'getUnionSumJournalList',
            'table_name' => self::TABLE_LIVE_JOURNAL_UNION,
            'conditions' => $strConditions,
            'order_type' => $strSearchOrder,
            'offset' => $intOffset,
            'limit' => $intLimit,
        );


        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRetData);

    }

    /**
     * 更新主播发放蓝钻记录
     * @param $arrParamInput
     * @return array
     * <AUTHOR>
     */
    public static function updateAnchorJournalSendTd($arrParamInput) {
        if (empty($arrParamInput["user_id"]) || empty($arrParamInput["date"])
            || !isset($arrParamInput['send_td_android']) || !isset($arrParamInput['send_td_status'])
        ){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrInput = array(
            'function'        => 'updateAnchorJournalSendTd',
            'table_name'      => self::TABLE_LIVE_JOURNAL_ANCHOR,
            'user_id'         => (int)$arrParamInput["user_id"],
            'date'            => (int)$arrParamInput["date"],
            'send_td_android' => (int)$arrParamInput["send_td_android"],
            'send_td_status'  => (int)$arrParamInput['send_td_status'],
        );

        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 更新公会发放蓝钻记录
     * @param $arrParamInput
     * @return array
     * <AUTHOR>
     */
    public static function updateUnionJournalSendTd($arrParamInput) {
        if (empty($arrParamInput["union_id"]) || empty($arrParamInput["date"])
            || !isset($arrParamInput['send_td_android']) || !isset($arrParamInput['send_td_status'])
        ){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrInput = array(
            'function'        => 'updateUnionJournalSendTd',
            'table_name'      => self::TABLE_LIVE_JOURNAL_UNION,
            'union_id'        => (int)$arrParamInput["union_id"],
            'date'            => (int)$arrParamInput["date"],
            'send_td_android' => (int)$arrParamInput["send_td_android"],
            'send_td_status'  => (int)$arrParamInput['send_td_status'],
        );

        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * @param $arrParamInput
     * @return array
     */
    public static function getUnionJournalMonthData($arrParamInput) {
        $intBeginDate = intval($arrParamInput['begin_date']);
        $intEndDate = intval($arrParamInput['end_date']);

        if (empty($intBeginDate) || empty($intEndDate) ){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrInput = array(
            'function'     => 'getUnionJournalMonthData',
            'table_name'   => self::TABLE_LIVE_JOURNAL_UNION,
            'begin_date'   => $intBeginDate,
            'end_date'     => $intEndDate,
        );

        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrRet = $arrOutput["results"][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }

    /**
     * @param $arrParamInput
     * @return array
     */
    public static function getMainPageHuabanData($arrParamInput) {
        if (empty($arrParamInput['send_type'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }
        $intUserId = $arrParamInput['user_id'];
        $intExpireTime = $arrParamInput['expire_time'];
        $intSendUserId = $arrParamInput['send_user_id'];
        $intBeginSendTime = $arrParamInput['begin_send_time'];
        $intOffset = isset($arrParamInput['offset']) ? intval($arrParamInput['offset']) : 0;
        $intLimit = isset($arrParamInput['limit']) ? intval($arrParamInput['limit']) : 1000;
        $strWhere = ' ';
        if(!empty($intSendUserId)){
            $strWhere .= sprintf(" send_user_id = %u and",$intSendUserId);
        }
        if(!empty($intUserId)){
            $strWhere .= sprintf(" user_id = %u and",$intUserId);
        }
        if(!empty($intExpireTime)){
            $strWhere .= sprintf(" expire_time = %u and",$intExpireTime);
        }
        if(!empty($intBeginSendTime)) {
            $strWhere .= " send_time >= $intBeginSendTime and ";
        }
        $strCond = $strWhere. sprintf(" send_type = %u ",$arrParamInput['send_type']);
        $arrInput = array(
            'function' => 'getMainPageHuabanData',
            'table_name' => self::TABLE_NAME_OPERATOR_FLOWER,
            'condition' => strval($strCond),
            'offset' => intval($intOffset),
            'limit' => intval($intLimit),
        );
        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_DB_QUERY_FAIL);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput['results'][0]);
    }


    /**
     * @param $arrParamInput
     * @return array
     */
    public static function petalGetInfoByUserIdArr($arrParamInput) {
        if (empty($arrParamInput['user_ids']) || !is_array($arrParamInput['user_ids'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }
        $arrUserIds = $arrParamInput['user_ids'];
        $arrInput = array(
            'function' => 'petalGetInfoByUserIdArr',
            'table_name' => self::TABLE_NAME_PETAL,
            'user_ids' => implode(',', $arrUserIds)
        );
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Petal::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $arrDlRet = $arrDlRet["results"][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrDlRet);
    }

    /**
     * @param $arrParamInput
     * @return array
     */
    public static function getHistoryJournalWithCondition($strCondition) {
        $arrInput = array(
            'function' => 'getHistoryJournalWithCondition',
            'table_name' => self::TABLE_LIVE_JOURNAL_UNION,
            'cond' => $strCondition,
        );
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Journal_Journal::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }

    /**
     * @param $arrParamInput
     * 获取公会流水
     * @return array
     */
    public static function getHistoryJournalWithConditionForUnionAll($strCondition) {
        $arrInput = array(
            'function' => 'getHistoryJournalWithConditionForUnionAll',
            'table_name' => self::TABLE_LIVE_JOURNAL_UNION,
            'cond' => $strCondition,
        );
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Journal_Journal::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }

    /**
     * @param $arrParamInput
     * @return array
     */
    public static function addAnchorSceneJournal($arrParamInput) {
        if(empty($arrParamInput["user_id"]) || empty($arrParamInput["anchor_type"]) || empty($arrParamInput["date"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUserId = intval($arrParamInput["user_id"]);
        $strUserName = strval($arrParamInput['user_name']);
        $intAnchorType= intval($arrParamInput['anchor_type']);
        $intDate = intval($arrParamInput['date']);

        if ($intAnchorType == Util_Ala_Journal::ANCHOR_TYPE_UNION) {
            if(empty($arrParamInput['union_id'])){
                $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
        }
        $intUnionId = intval($arrParamInput['union_id']);
        $intIncomeTD = intval($arrParamInput['income_td']);
        $intSceneId = intval($arrParamInput['scene_id']);
        $arrInput = array(
            'function'   => 'addAnchorSceneJournal',
            'table_name' => self::TABLE_LIVE_SCENE_JOURNAL_ANCHOR,
            'user_id' => $intUserId,
            'user_name' => $strUserName,
            'anchor_type' => $intAnchorType,
            'date' => $intDate,
            'union_id' => $intUnionId,
            'income_td' => $intIncomeTD,
            'scene_id' => $intSceneId,
        );
        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

}
