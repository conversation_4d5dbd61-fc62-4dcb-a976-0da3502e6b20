<?php
/**
 * Photo.php 主播照片
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 17/7/19 下午5:14
 */

class Dl_Ala_Anchor_Photo extends Dl_Ala_Base
{

    // db config
    const MODULE_NAME = 'ala';

    const SERVICE_NAME = 'Dl_Ala_Anchor_Photo';

    const DB_CHARSET = 'utf8';

    const DATABASE_NAME = 'forum_ala';

    const TABLE_NAME_PHOTO = 'amis_anchor_photo';

    private static $_db = null;

    private static $_conf = null;

    /**
     * private _errRet
     *
     * @param
     *            $errno
     * @return array
     */
    private static function _errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Alalib_Conf_Error::getErrorMsg($errno)
        );
    }

    /**
     * private _getDB
     *
     * @param
     *            $errno
     * @return array
     */
    private static function _getDB()
    {
        if (self::$_db) {
            return self::$_db;
        }
        self::$_db = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if (self::$_db == null || ! self::$_db->isConnected()) {
            Bingo_Log::warning('db connect fail.');
            return null;
        }
        self::$_db->query("set names " . self::DB_CHARSET);
        return self::$_db;
    }

    /**
     * private _init
     *
     * @param
     *            $errno
     * @return array
     */
    private static function _init()
    {
        if (self::_getDB() == null) {
            Bingo_Log::warning("init db fail.");
            return self::_errRet(Alalib_Conf_Error::ERR_DB_CONN_FAIL);
        }
        if (self::$_conf == null) {
            $dlConfFile = '/app/amis/' . self::MODULE_NAME . '/' . strtolower(self::SERVICE_NAME);
            self::$_conf = Bd_Conf::getConf($dlConfFile);
            if (self::$_conf == false) {
                Bingo_Log::warning('init get conf fail.' . $dlConfFile);
                return self::_errRet(Alalib_Conf_Error::ERR_LOAD_CONFIG_FAIL);
            }
        }
        return true;
    }

    /**
     * execSql
     *
     * @param
     *            $arrInput
     * @return array
     */
    public static function execSql($arrInput)
    {
        if (! isset($arrInput['function'])) {
            Bingo_Log::warning('input params invalid: function is empty. [' . serialize($arrInput) . ']');
            return self::_errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }
        $ret = self::_init();
        if ($ret !== true) {
            return $ret;
        }
        Bingo_Timer::start('initlib');
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        Bingo_Timer::end('initlib');
        if ($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::_errRet(Alalib_Conf_Error::ERR_DB_CONN_FAIL);
        }
        $bolPrintSql = true;
        $arrOut = $mdb->execSql($arrInput, $bolPrintSql);
        return $arrOut;
    }

    public static function execSqlWithTransaction($arrInputs)
    {
        $ret = self::_init();
        if ($ret !== true) {
            return $ret;
        }

        Bingo_Timer::start('initlib');
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        Bingo_Timer::end('initlib');
        if ($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::_errRet(Alalib_Conf_Error::ERR_DB_CONN_FAIL);
        }

        $ret = self::$_db->startTransaction();
        if ($ret === false) {
            return false;
        }

        $arrOut = false;
        foreach ($arrInputs as $arrInput) {
            $arrOut = $mdb->execSql($arrInput, true);
            if (false === $arrOut || Alalib_Conf_Error::ERR_SUCCESS != $arrOut["errno"]) {
                self::$_db->rollback();
                return false;
            }
        }

        $ret = self::$_db->commit();
        if ($ret === false) {
            self::$_db->rollback();
            return false;
        }
        return $arrOut;
    }

    /**
     * 添加照片
     * @param $intUserId
     * @param string|array $arrPhoto
     * @param string $strNote
     * @return array
     */
    public static function addPhoto($intUserId, $intAuditUserId = 0, $arrPhoto = array(), $strNote = '')
    {
        // serialize 存储
        $strPhoto = serialize($arrPhoto);

        $arrInput = array(
            'function'      => 'addPhoto',
            'table_name'    => self::TABLE_NAME_PHOTO,
            'user_id'       => (int)$intUserId,
            'photo'         => $strPhoto,
            'note'          => $strNote,
            'audit_user_id' => (int)$intAuditUserId,
            'create_time'   => date('Y-m-d H:i:s'),
        );

        $arrOutput = self::execSql($arrInput);

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);

            return self::errRet(Alalib_Conf_Error::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }

    /**
     * 获取照片信息
     * @param $intUserId
     * @return array
     */
    public static function getPhotoInfo($intUserId) {
        $arrInput = array(
            'function'   => 'getPhotoInfo',
            'table_name' => self::TABLE_NAME_PHOTO,
            'user_id'    => (int)$intUserId,
        );

        $arrOutput = self::execSql($arrInput);

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);

            return self::errRet(Alalib_Conf_Error::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0];

        // serialize 存储的信息进行 unserialize
        if (!empty($arrRetData)) {
            $arrRetData['photo'] = unserialize($arrRetData['photo']);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }

    /**
     * 批量获取用户的照片信息列表
     * @param $arrUserIds
     * @return array
     */
    public static function getPhotoList($arrUserIds) {
        $arrUserIds = array_unique(array_filter($arrUserIds));

        if (!$arrUserIds) {
            $strLog = __CLASS__."::".__FUNCTION__."  param empty. input:[".serialize($arrUserIds)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR, array());
        }

        $strUserIds = implode(',', $arrUserIds);

        $arrInput = array(
            'function'   => 'getPhotoList',
            'table_name' => self::TABLE_NAME_PHOTO,
            'user_ids'   => $strUserIds,
        );

        $arrOutput = self::execSql($arrInput);

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);

            return self::errRet(Alalib_Conf_Error::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0];

        $arrPhotoList = array();

        // serialize 存储的信息进行 unserialize
        if (!empty($arrRetData)) {
            foreach ($arrRetData as $v) {
                $v['photo'] = !empty($v['photo']) ? unserialize($v['photo']) : array();

                $arrPhotoList[$v['user_id']] = $v;
            }
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrPhotoList);
    }
}