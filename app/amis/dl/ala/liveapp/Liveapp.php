<?php
/**
 * Created by PhpStorm.
 * User: caowu
 * Date: 19/1/8
 * Time: 下午3:43
 */
class Dl_Ala_Liveapp_Liveapp extends Dl_Ala_Base
{

    // db config
    const MODULE_NAME = 'ala';

    const SERVICE_NAME = 'Dl_Ala_Liveapp_Liveapp';

    const DB_CHARSET = 'utf8';

    const DATABASE_NAME = 'forum_ala';

    const TABLE_NAME_NOTICE_INFO = 'amis_liveapp_notice_info';

    private static $_db = null;

    private static $_conf = null;

    /**
     * private _errRet
     *
     * @param
     *            $errno
     * @return array
     */
    private static function _errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Alalib_Conf_Error::getErrorMsg($errno)
        );
    }

    /**
     * private _getDB
     *
     * @param
     *            $errno
     * @return array
     */
    private static function _getDB()
    {
        if (self::$_db) {
            return self::$_db;
        }
        self::$_db = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if (self::$_db == null || ! self::$_db->isConnected()) {
            Bingo_Log::warning('db connect fail.');
            return null;
        }
        self::$_db->query("set names " . self::DB_CHARSET);
        return self::$_db;
    }

    /**
     * private _init
     *
     * @param
     *            $errno
     * @return array
     */
    private static function _init()
    {
        if (self::_getDB() == null) {
            Bingo_Log::warning("init db fail.");
            return self::_errRet(Alalib_Conf_Error::ERR_DB_CONN_FAIL);
        }
        if (self::$_conf == null) {
            $dlConfFile = '/app/amis/' . self::MODULE_NAME . '/' . strtolower(self::SERVICE_NAME);
            self::$_conf = Bd_Conf::getConf($dlConfFile);
            if (self::$_conf == false) {
                Bingo_Log::warning('init get conf fail.' . $dlConfFile);
                return self::_errRet(Alalib_Conf_Error::ERR_LOAD_CONFIG_FAIL);
            }
        }
        return true;
    }

    /**
     * execSql
     *
     * @param
     *            $arrInput
     * @return array
     */
    public static function execSql($arrInput)
    {
        if (! isset($arrInput['function'])) {
            Bingo_Log::warning('input params invalid: function is empty. [' . serialize($arrInput) . ']');
            return self::_errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }
        $ret = self::_init();
        if ($ret !== true) {
            return $ret;
        }
        Bingo_Timer::start('initlib');
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        Bingo_Timer::end('initlib');
        if ($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::_errRet(Alalib_Conf_Error::ERR_DB_CONN_FAIL);
        }
        $bolPrintSql = true;
        $arrOut = $mdb->execSql($arrInput, $bolPrintSql);
        return $arrOut;
    }

    /**
     * @param $arrInputs
     * @return
     */
    public static function execSqlWithTransaction($arrInputs)
    {
        $ret = self::_init();
        if ($ret !== true) {
            return $ret;
        }

        Bingo_Timer::start('initlib');
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        Bingo_Timer::end('initlib');
        if ($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::_errRet(Alalib_Conf_Error::ERR_DB_CONN_FAIL);
        }

        $ret = self::$_db->startTransaction();
        if ($ret === false) {
            return false;
        }

        $arrOut = false;
        foreach ($arrInputs as $arrInput) {
            $arrOut = $mdb->execSql($arrInput, true);
            if (false === $arrOut || Alalib_Conf_Error::ERR_SUCCESS != $arrOut["errno"]) {
                self::$_db->rollback();
                return false;
            }
        }

        $ret = self::$_db->commit();
        if ($ret === false) {
            self::$_db->rollback();
            return false;
        }
        return $arrOut;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function addLiveAppNoticeToDb($arrInput) {
        $arrInput['function'] = 'addLiveAppNoticeToDb';
        $arrInput['table_name'] = self::TABLE_NAME_NOTICE_INFO;

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "addGiftUserList fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function updateLiveAppNotice($arrInput) {
        $arrInput['function'] = 'updateLiveAppNotice';
        $arrInput['table_name'] = self::TABLE_NAME_NOTICE_INFO;

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "updateLiveAppNotice fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function deleteLiveAppNotice($arrInput) {
        $arrInput['function'] = 'deleteLiveAppNotice';
        $arrInput['table_name'] = self::TABLE_NAME_NOTICE_INFO;

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "deleteLiveAppNotice fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function selectLiveAppNotice($arrInput) {
        $arrDBInput = $arrInput;
        $arrDBInput['function'] = 'selectLiveAppNotice';
        $arrDBInput['table_name'] = self::TABLE_NAME_NOTICE_INFO;
        $arrDlRet = self::execSql($arrDBInput);
        if(false === $arrDlRet || Tieba_Errcode::ERR_SUCCESS != $arrDlRet["errno"]){
            $strLog = __CLASS__."::".__FUNCTION__. " call Dl_Ala_Yunying::execSql fail. input:[".serialize($arrDBInput)."]; output:[".serialize($arrDlRet)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrData = $arrDlRet['results'][0];

        $arrDBInput['function'] = 'selectCountLiveAppNotice';
        $arrDBInput['table_name'] = self::TABLE_NAME_NOTICE_INFO;
        $arrDlRet = self::execSql($arrDBInput);
        if(false === $arrDlRet || Tieba_Errcode::ERR_SUCCESS != $arrDlRet["errno"]){
            $strLog = __CLASS__."::".__FUNCTION__. " call Dl_Ala_Yunying::execSql fail. input:[".serialize($arrDBInput)."]; output:[".serialize($arrDlRet)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $intCount = $arrDlRet['results'][0][0]['total_count'];
        $arrRetData = array(
            'rows' => $arrData,
            'count' => $intCount,
        );
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }
}