<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR> @date 2015:10:14 10:48:22
 * @version
 * @structs & methods(copied from idl.)
 */
class Dl_Ala_Union_Union extends Dl_Ala_Base
{

    // db config
    const MODULE_NAME = 'ala';

    const SERVICE_NAME = 'Dl_Ala_Union_Union';

    const DB_CHARSET = 'utf8';

    const DATABASE_NAME = 'forum_ala';

    const TABLE_NAME_UNION = 'amis_live_union';

    const TABLE_NAME_LIVE_UNION_JOURNAL = 'amis_live_union_journal';

    const TABLE_NAME_COUNT = 'amis_live_union_count';

    const TABLE_NAME_ANCHOR = 'amis_live_union_anchor';

    const TABLE_NAME_AUTHORITY = 'amis_live_yunying_authority_info';

    const TABLE_NAME_UNION_LIVE_RATIO = 'amis_live_union_live_ratio_memo';

    private static $_db = null;

    private static $_conf = null;

    /**
     * private _errRet
     *
     * @param
     *            $errno
     * @return array
     */
    private static function _errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Alalib_Conf_Error::getErrorMsg($errno)
        );
    }

    /**
     * private _getDB
     *
     * @param
     *            $errno
     * @return array
     */
    private static function _getDB()
    {
        if (self::$_db) {
            return self::$_db;
        }
        self::$_db = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if (self::$_db == null || ! self::$_db->isConnected()) {
            Bingo_Log::warning('db connect fail.');
            return null;
        }
        self::$_db->query("set names " . self::DB_CHARSET);
        return self::$_db;
    }

    /**
     * private _init
     *
     * @param
     *            $errno
     * @return array
     */
    private static function _init()
    {
        if (self::_getDB() == null) {
            Bingo_Log::warning("init db fail.");
            return self::_errRet(Alalib_Conf_Error::ERR_DB_CONN_FAIL);
        }
        if (self::$_conf == null) {
            $dlConfFile = '/app/amis/' . self::MODULE_NAME . '/' . strtolower(self::SERVICE_NAME);
            self::$_conf = Bd_Conf::getConf($dlConfFile);
            if (self::$_conf == false) {
                Bingo_Log::warning('init get conf fail.' . $dlConfFile);
                return self::_errRet(Alalib_Conf_Error::ERR_LOAD_CONFIG_FAIL);
            }
        }
        return true;
    }

    /**
     * execSql
     *
     * @param
     *            $arrInput
     * @return array
     */
    public static function execSql($arrInput)
    {
        if (! isset($arrInput['function'])) {
            Bingo_Log::warning('input params invalid: function is empty. [' . serialize($arrInput) . ']');
            return self::_errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }
        $ret = self::_init();
        if ($ret !== true) {
            return $ret;
        }
        Bingo_Timer::start('initlib');
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        Bingo_Timer::end('initlib');
        if ($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::_errRet(Alalib_Conf_Error::ERR_DB_CONN_FAIL);
        }
        $bolPrintSql = true;
        $arrOut = $mdb->execSql($arrInput, $bolPrintSql);
        return $arrOut;
    }

    public static function execSqlWithTransaction($arrInputs)
    {
        $ret = self::_init();
        if ($ret !== true) {
            return $ret;
        }
        
        Bingo_Timer::start('initlib');
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        Bingo_Timer::end('initlib');
        if ($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::_errRet(Alalib_Conf_Error::ERR_DB_CONN_FAIL);
        }
        
        $ret = self::$_db->startTransaction();
        if ($ret === false) {
            return false;
        }
        
        $arrOut = false;
        foreach ($arrInputs as $arrInput) {
            $arrOut = $mdb->execSql($arrInput, true);
            if (false === $arrOut || Alalib_Conf_Error::ERR_SUCCESS != $arrOut["errno"]) {
                self::$_db->rollback();
                return false;
            }
        }
        
        $ret = self::$_db->commit();
        if ($ret === false) {
            self::$_db->rollback();
            return false;
        }
        return $arrOut;
    }

    /**
     * 返回值
     *
     * @param {String} $strUnionName            
     * @param {Int} $intUnionUserId            
     * @param {String} $strUnionUserPhone 
     * @param {Int} $intUnionType
     * @param {Array} $arrUnionJson
     *
     * @return {Array} :errno :errmsg :{Array}output
     *        
     */
    public static function add($strUnionName, $intUnionUserId, $strUnionUserPhone, $intUnionType, $arrUnionJson, $arrAuthorityDlInput, $intCreateUserId = 0) {
        $arrInputs = array();
        $arrInputs[] = array(
            'function'          => 'add',
            'table_name'        => self::TABLE_NAME_UNION,
            'union_name'        => $strUnionName,
            'union_user_id'     => $intUnionUserId,
            'audit_user_id'     => Util_User::$intUserId,
            'create_time'       => Bingo_Timer::getNowTime(),
            'union_user_phone'  => $strUnionUserPhone,
            'union_type'        => $intUnionType,
            'json'              => empty($arrUnionJson) ? '' : json_encode($arrUnionJson),
            'create_user_id'    => empty($intCreateUserId) ? Util_User::$intUserId : $intCreateUserId,
        );
        
        $arrInputs[] = array(
            'function' => 'incrCount',
            'table_name' => self::TABLE_NAME_COUNT
        );

        $arrAuthorityDlInput['function'] = 'addOrUpdate';
        $arrAuthorityDlInput['table_name'] = self::TABLE_NAME_AUTHORITY;

        $arrInputs[] = $arrAuthorityDlInput;

        $arrDlRet = self::execSqlWithTransaction($arrInputs);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSqlWithTransaction fail. input:[" . serialize($arrInputs) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, true);
    }

    /**
     * 初始化amis_live_union_count
     * @param int $intCount
     * @return array
     */
    public static function addCount($intCount = 1) {
        $arrInput = array(
            'function'   => 'addCount',
            'table_name' => self::TABLE_NAME_COUNT,
            'count'      => $intCount,
        );

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSqlWithTransaction fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, true);

    }

    /**
     * 从Union表中获取总数，用来修复Count表的数
     * 正常获取请使用getCount()
     * @param $cond
     * @return array
     */
    public static function getCountFromUnion($cond = true) {
        $arrInput = array(
            'function'   => 'getCountFromUnion',
            'table_name' => self::TABLE_NAME_UNION,
            'cond'       => $cond,
        );

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSqlWithTransaction fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet[0]);
    }

    /**
     * 返回值
     *
     * @param {Int} $intUnionId            
     *
     * @return {Array} :errno :errmsg :{Array}output
     *        
     */
    public static function del($intUnionId)
    {
        $arrInputs = array();
        $arrInputs[] = array(
            'function' => 'del',
            'table_name' => self::TABLE_NAME_UNION,
            'union_id' => $intUnionId
        );
        
        $arrInputs[] = array(
            'function' => 'del',
            'table_name' => self::TABLE_NAME_ANCHOR,
            'union_id' => $intUnionId
        );
        
        $arrInputs[] = array(
            'function' => 'decrCount',
            'table_name' => self::TABLE_NAME_COUNT
        );
        
        $arrDlRet = self::execSqlWithTransaction($arrInputs);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSqlWithTransaction fail. input:[" . serialize($arrInputs) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, true);
    }

    /**
     * 返回值
     *
     * @param {Int} $intUnionId
     * @param {String} $strUnionName
     * @param {String} $strUnionUserPhone
     * @param {Int} $intUnionType
     * @param {Array} $arrUnionJson
     *
     * @return {Array} :errno :errmsg :{Array}output
     *
     */
    public static function update($intUnionId, $strUnionName, $strUnionUserPhone, $intUnionType, $arrJson, $boolNeedUpdateUnionName = false) {
        $arrInput = array(
            'function'          => 'update',
            'table_name'        => self::TABLE_NAME_UNION,
            'union_id'          => $intUnionId,
            'union_name'        => $strUnionName,
            'union_user_phone'  => $strUnionUserPhone,
            'update_time'       => Bingo_Timer::getNowTime(),
            'union_type'        => $intUnionType,
            'json'              => empty($arrJson) ? '' : json_encode($arrJson)
        );
        
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSqlWithTransaction fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }

        if(true === $boolNeedUpdateUnionName) {
            $arrInput = array(
                'function'          => 'updateJournalUnionName',
                'table_name'        => self::TABLE_NAME_LIVE_UNION_JOURNAL,
                'union_id'          => $intUnionId,
                'union_name'        => $strUnionName,
            );
            $arrDlRet = self::execSql($arrInput);
            if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
                $strLog = "Dl_Ala_Union_Union::execSqlWithTransaction fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
                Bingo_Log::warning($strLog);
                return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
            }
        }

        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, true);
    }

    /**
     * 返回值
     *
     * @param {String} $strUnionName            
     *
     * @return {Array} :errno :errmsg :{Array}output
     *        
     */
    public static function getOneByUnionName($strUnionName)
    {
        $arrInput = array(
            'function' => 'getOneByUnionName',
            'table_name' => self::TABLE_NAME_UNION,
            'union_name' => $strUnionName
        );
        
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        
        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet[0]);
    }

    /**
     * 返回值
     *
     * @param {Int} $intUnionId            
     *
     * @return {Array} :errno :errmsg :{Array}output
     *        
     */
    public static function getOne($intUnionId)
    {
        $arrInput = array(
            'function' => 'getOne',
            'table_name' => self::TABLE_NAME_UNION,
            'union_id' => $intUnionId
        );
        
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        
        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet[0]);
    }

    /**
     * 返回值
     *
     * @param {Int} $intPage            
     * @param {Int} $intReqNum
     *
     * @return {Array} :errno :errmsg :{Array}output
     *        
     */
    public static function getList($intPage, $intReqNum, $cond = true)
    {
        $arrInput = array(
            'function' => 'getList',
            'table_name' => self::TABLE_NAME_UNION,
            'page' => ($intPage - 1) * $intReqNum,
            'req_num' => $intReqNum,
            'cond' => $cond,
        );
        
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        
        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }

    /**
     * 返回值
     *
     * @param {Array} $arrUnionId            
     *
     * @return {Array} :errno :errmsg :{Array}output
     *        
     */
    public static function getUnionArr($arrUnionId){
        if(empty($arrUnionId)) {
            return self::errRet(Alalib_Conf_Error::ERR_SUCCESS);
        }
        $arrInput = array(
            'function' => 'getUnionArr',
            'table_name' => self::TABLE_NAME_UNION,
            'union_ids' => implode(',', $arrUnionId)
        );
        
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Anchor::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        
        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }

    /**
     * 返回值
     *
     * @param {String} $strUnionName            
     *
     * @return {Array} :errno :errmsg :{Array}output
     *        
     */
    public static function getListByUnionName($strUnionName, $intPage, $intReqNum, $cond = true)
    {
        $arrInput = array(
            'function' => 'getListByUnionName',
            'table_name' => self::TABLE_NAME_UNION,
            'union_name' => $strUnionName,
            'page' => ($intPage - 1) * $intReqNum,
            'req_num' => $intReqNum,
            'cond' => $cond,
        );
        
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        
        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }

    /**
     * 返回值
     *
     * @param {String} $strUnionName            
     *
     * @return {Array} :errno :errmsg :{Array}output
     *        
     */
    public static function getCountByUnionName($strUnionName, $cond = true)
    {
        $arrInput = array(
            'function' => 'getCountByUnionName',
            'table_name' => self::TABLE_NAME_UNION,
            'union_name' => '%' .$strUnionName . '%',
            'cond' => $cond,
        );
        
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        
        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet[0]);
    }

    /**
     * 返回值
     *
     * @param {Int} $intUnionUserId            
     *
     * @return {Array} :errno :errmsg :{Array}output
     *        
     */
    public static function getListByUnionUserId($intUnionUserId, $intPage, $intReqNum, $cond = true)
    {
        $arrInput = array(
            'function' => 'getListByUnionUserId',
            'table_name' => self::TABLE_NAME_UNION,
            'union_user_id' => $intUnionUserId,
            'page' => ($intPage - 1) * $intReqNum,
            'req_num' => $intReqNum,
            'cond' => $cond,
        );
        
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        
        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }

    /**
     * 返回值
     *
     * @param {Int} $intUnionUserId            
     *
     * @return {Array} :errno :errmsg :{Array}output
     *        
     */
    public static function getCountByUnionUserId($intUnionUserId, $cond = true)
    {
        $arrInput = array(
            'function' => 'getCountByUnionUserId',
            'table_name' => self::TABLE_NAME_UNION,
            'union_user_id' => $intUnionUserId,
            'cond' => $cond,
        );
        
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        
        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet[0]);
    }

    /**
     * 返回值
     *
     * @param
     *            null
     *            
     * @return {Array} :errno :errmsg :{Array}output
     *        
     */
    public static function getCount()
    {
        $arrInput = array(
            'function' => 'getCount',
            'table_name' => self::TABLE_NAME_COUNT
        );
        
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        
        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet[0]);
    }

    /**
     * 更新公会开播率
     * @param $intUnionId
     * @param $intLivedRatio
     * @return {Array} :errno :errmsg :{Array}output
     */
    public static function updateUnionLivedRatio($intUnionId, $intLivedRatio)
    {
        $arrInput = array(
            'function'    => 'updateUnionLivedRatio',
            'table_name'  => self::TABLE_NAME_UNION,
            'union_id'    => $intUnionId,
            'lived_ratio' => $intLivedRatio,
        );

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, true);
    }



    /**
     * 更新公会开播率
     * @param $intUnionId
     * @param $intLivedRatio
     * @return {Array} :errno :errmsg :{Array}output
     */
    public static function updateUnionLivedRatioTwo($intUnionId, $intUnionAnchorNum, $intDate, $intValidAnchorNum) {
        $arrInput = array(
            'function'    => 'updateUnionLivedRatioTwo',
            'table_name'  => self::TABLE_NAME_UNION_LIVE_RATIO,
            'union_id'    => $intUnionId,
            'union_anchor_num' => $intUnionAnchorNum,
            'union_valid_anchor_num' => $intValidAnchorNum,
            'date' => $intDate,
        );

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, true);
    }

    /**
     * 返回值
     *
     * @param {Int} $intCreateUserId
     *
     * @return {Array} :errno :errmsg :{Array}output
     *
     */
    public static function getListByCreateUserId($intCreateUserId, $intPage, $intReqNum)
    {
        $arrInput = array(
            'function' => 'getListByCreateUserId',
            'table_name' => self::TABLE_NAME_UNION,
            'create_user_id' => $intCreateUserId,
            'page' => ($intPage - 1) * $intReqNum,
            'req_num' => $intReqNum
        );

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }

        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }

    /**
     * 返回值
     *
     * @param {Int} $intCreateUserId
     *
     * @return {Array} :errno :errmsg :{Array}output
     *
     */
    public static function getListByCreateUserIdAndUnionId($intCreateUserId, $intUnionId, $intPage, $intReqNum)
    {
        $arrInput = array(
            'function' => 'getListByCreateUserIdAndUnionId',
            'table_name' => self::TABLE_NAME_UNION,
            'create_user_id' => $intCreateUserId,
            'union_id' => $intUnionId,
            'page' => ($intPage - 1) * $intReqNum,
            'req_num' => $intReqNum
        );

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }

        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }

    /**
     * 返回值
     *
     * @param {Int} $intCreateUserId
     *
     * @return {Array} :errno :errmsg :{Array}output
     *
     */
    public static function getListByCreateUserIdAndUnionUserId($intCreateUserId, $intUnionUserId, $intPage, $intReqNum)
    {
        $arrInput = array(
            'function' => 'getListByCreateUserIdAndUnionUserId',
            'table_name' => self::TABLE_NAME_UNION,
            'create_user_id' => $intCreateUserId,
            'union_user_id' => $intUnionUserId,
            'page' => ($intPage - 1) * $intReqNum,
            'req_num' => $intReqNum
        );

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }

        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }

    /**
     * @param $arrUserIds
     * @return array
     */
    public static function getListByCreateUserIdsArray($arrUserIds){
        if(empty($arrUserIds) || !is_array($arrUserIds)) {
            Bingo_Log::warning("input error");
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }
        $arrInput = array(
            'function' => 'getListByCreateUserIdsArray',
            'table_name' => self::TABLE_NAME_UNION,
            'create_user_ids' => implode(',', $arrUserIds),
        );
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }

    /**
     * @param $arrUserIds
     * @return array
     */
    public static function getAllAdminUserIds(){
        $arrInput = array(
            'function' => 'getAllAdminUserIds',
            'table_name' => self::TABLE_NAME_UNION,
        );
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }

    /**
     * 根据公会id获取公会名称
     * @param $arrInput
     * @return array
     */
    public static function getUnionNameByUnionId($arrInput)
    {
        if(empty($arrInput['union_id'])){
            $strLog = __CLASS__."::".__FUNCTION__. " param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR,array());
        }

        $arrInput = array(
            'function'   => 'getUnionNameByUnionId',
            'table_name' => self::TABLE_NAME_UNION,
            'union_id'    => $arrInput['union_id'],
        );

        $arrOutput = self::execSql($arrInput);

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);

            return self::errRet(Alalib_Conf_Error::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }

    /**
     * @param $arrUserIds
     * @return array
     */
    public static function getAllLiveUnion(){
        $arrInput = array(
            'function' => 'getAllLiveUnion',
            'table_name' => self::TABLE_NAME_UNION,
        );
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }


    /**
     * @param $arrUserIds
     * 获取合作运营开播信息
     * @return array
     */
    public static function getHistoryLiveRatio($strCondition){
        $arrInput = array(
            'function' => 'getHistoryLiveRatio',
            'table_name' => self::TABLE_NAME_UNION_LIVE_RATIO,
            'cond' => $strCondition,
        );
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }


    /**
     * @param $arrUserIds
     * 获取公会开播信息
     * @return array
     */
    public static function getHistoryLiveRatioForUnionAll($strCondition){
        $arrInput = array(
            'function' => 'getHistoryLiveRatioForUnionAll',
            'table_name' => self::TABLE_NAME_UNION_LIVE_RATIO,
            'cond' => $strCondition,
        );
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }

    /**
     * 获取公会天维度的主播数
     * @param $strCondition
     * @return array
     */
    public static function getUnionHistoryLiveRatio($strCondition) {
        $arrInput = array(
            'function' => 'getUnionHistoryLiveRatio',
            'table_name' => self::TABLE_NAME_UNION_LIVE_RATIO,
            'cond' => $strCondition,
        );
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Union_Union::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }

}
