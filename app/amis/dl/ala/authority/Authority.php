<?php

/**
 * Created by PhpStorm.
 * User: caowu
 * Date: 17/7/17
 * Time: 上午11:23
 */
class Dl_Ala_Authority_Authority extends Dl_Ala_Base {

    const MODULE_NAME = 'ala';

    const SERVICE_NAME = 'Dl_Ala_Authority_Authority';

    const DB_CHARSET = 'utf8';

    const DATABASE_NAME = 'forum_ala';

    const TABLE_NAME_AUTHORITY = 'amis_live_yunying_authority_info';

    const TABLE_NAME_COOPERATION = 'amis_live_cooperation';

    private static $_db = null;

    private static $_conf = null;

    /**
     * @param $errno
     * @return array
     */
    private static function _errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Alalib_Conf_Error::getErrorMsg($errno)
        );
    }

    /**
     * @return Bd_DB|null
     */
    private static function _getDB()
    {
        if (self::$_db) {
            return self::$_db;
        }
        self::$_db = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if (self::$_db == null || ! self::$_db->isConnected()) {
            Bingo_Log::warning('db connect fail.');
            return null;
        }
        self::$_db->query("set names " . self::DB_CHARSET);
        return self::$_db;
    }

    /**
     * @return array|bool
     */
    private static function _init()
    {
        if (self::_getDB() == null) {
            Bingo_Log::warning("init db fail.");
            return self::_errRet(Alalib_Conf_Error::ERR_DB_CONN_FAIL);
        }
        if (self::$_conf == null) {
            $dlConfFile = '/app/amis/' . self::MODULE_NAME . '/' . strtolower(self::SERVICE_NAME);
            self::$_conf = Bd_Conf::getConf($dlConfFile);
            if (self::$_conf == false) {
                Bingo_Log::warning('init get conf fail.' . $dlConfFile);
                return self::_errRet(Alalib_Conf_Error::ERR_LOAD_CONFIG_FAIL);
            }
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function execSql($arrInput)
    {
        if (! isset($arrInput['function'])) {
            Bingo_Log::warning('input params invalid: function is empty. [' . serialize($arrInput) . ']');
            return self::_errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }
        $ret = self::_init();
        if ($ret !== true) {
            return $ret;
        }
        Bingo_Timer::start('initlib');
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        Bingo_Timer::end('initlib');
        if ($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::_errRet(Alalib_Conf_Error::ERR_DB_CONN_FAIL);
        }
        $bolPrintSql = true;
        $arrOut = $mdb->execSql($arrInput, $bolPrintSql);
        return $arrOut;
    }

    /**
     * @param $arrInputs
     * @return array|bool
     */
    public static function execSqlWithTransaction($arrInputs) {
        $ret = self::_init();
        if ($ret !== true) {
            return $ret;
        }

        Bingo_Timer::start('initlib');
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        Bingo_Timer::end('initlib');
        if ($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::_errRet(Alalib_Conf_Error::ERR_DB_CONN_FAIL);
        }

        $ret = self::$_db->startTransaction();
        if ($ret === false) {
            return false;
        }

        $arrOut = false;
        foreach ($arrInputs as $arrInput) {
            $arrOut = $mdb->execSql($arrInput, true);
            if (false === $arrOut || Alalib_Conf_Error::ERR_SUCCESS != $arrOut["errno"]) {
                self::$_db->rollback();
                return false;
            }
        }

        $ret = self::$_db->commit();
        if ($ret === false) {
            self::$_db->rollback();
            return false;
        }
        return $arrOut;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function search($arrInput) {

        $arrInput['function'] = 'search';
        $arrInput['table_name'] = self::TABLE_NAME_AUTHORITY;

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Authority_Authority::search  execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }

        $arrData = $arrDlRet["results"][0];

        $arrInput['function'] = 'countTotal';
        $arrInput['table_name'] = self::TABLE_NAME_AUTHORITY;

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Authority_Authority::countTotal  execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $intCount = $arrDlRet["results"][0][0]['count'];

        $arrRet['rows'] = $arrData;
        $arrRet['count'] = $intCount;

        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function add($arrInput) {

        $arrInput['function'] = 'add';
        $arrInput['table_name'] = self::TABLE_NAME_AUTHORITY;

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Authority_Authority::add  execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function update($arrInput) {

        $arrInput['function'] = 'update';
        $arrInput['table_name'] = self::TABLE_NAME_AUTHORITY;

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Authority_Authority::update  execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function setRoleIdWhoIsEmpty($arrInput) {

        $arrInput['function'] = 'setRoleIdWhoIsEmpty';
        $arrInput['table_name'] = self::TABLE_NAME_AUTHORITY;

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Authority_Authority::setRoleIdWhoIsEmpty  execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function updateInfo($arrInput) {

        $arrInput['function'] = 'updateInfo';
        $arrInput['table_name'] = self::TABLE_NAME_AUTHORITY;

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Authority_Authority::updateInfo  execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function delete($arrInput) {

        $arrInput['function'] = 'delete';
        $arrInput['table_name'] = self::TABLE_NAME_AUTHORITY;

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Authority_Authority::delete  execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS);
    }


    /**
     * @param $arrInput
     * @return array
     */
    public static function searchCooperation($arrInput) {

        $arrInput['function'] = 'search';
        $arrInput['table_name'] = self::TABLE_NAME_COOPERATION;

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Authority_Authority::search  execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }

        $arrData = $arrDlRet["results"][0];

        $arrInput['function'] = 'countTotal';
        $arrInput['table_name'] = self::TABLE_NAME_COOPERATION;

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Authority_Authority::countTotal  execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $intCount = $arrDlRet["results"][0][0]['count'];

        $arrRet['rows'] = $arrData;
        $arrRet['count'] = $intCount;

        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }


    /**
     * @param $arrInput
     * @return array
     */
    public static function addCooperation($arrInput) {

        $arrInput['function'] = 'addCooperation';
        $arrInput['table_name'] = self::TABLE_NAME_COOPERATION;

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Authority_Authority::addCooperation  execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function addOrUpdate($arrInput) {

        $arrInput['function'] = 'addOrUpdate';
        $arrInput['table_name'] = self::TABLE_NAME_AUTHORITY;

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Authority_Authority::addOrUpdate  execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function addCooperationTransaction($arrCooperationDlInput, $arrAuthorityDlInput, $boolUpdateAuthrity = true) {
        $arrInputs = array();
        $arrCooperationDlInput['function'] = 'addCooperation';
        $arrCooperationDlInput['table_name'] = self::TABLE_NAME_COOPERATION;
        $arrInputs[] = $arrCooperationDlInput;

        if(true === $boolUpdateAuthrity) {
            $arrAuthorityDlInput['function'] = 'addOrUpdate';
            $arrAuthorityDlInput['table_name'] = self::TABLE_NAME_AUTHORITY;
            $arrInputs[] = $arrAuthorityDlInput;
        }


        $arrDlRet = self::execSqlWithTransaction($arrInputs);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "execSqlWithTransaction fail. input:[" . serialize($arrInputs) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, true);
    }


    /**
     * @param $arrInput
     * @return array
     */
    public static function updateCooperation($arrInput) {

        $arrInput['function'] = 'updateCooperation';
        $arrInput['table_name'] = self::TABLE_NAME_COOPERATION;

        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Authority_Authority::updateCooperation  execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function deleteCooperation($arrInput) {
        $arrInput['function'] = 'deleteCooperation';
        $arrInput['table_name'] = self::TABLE_NAME_COOPERATION;
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Authority_Authority::deleteCooperation  execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }

        $arrInput['function'] = 'delete';
        $arrInput['table_name'] = self::TABLE_NAME_AUTHORITY;
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_Authority_Authority::delete  execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS);
    }

}
