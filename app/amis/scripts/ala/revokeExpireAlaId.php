<?php
/**
 * 收回过期的靓号 alaid
 * Created by PhpStorm.
 * User: zhanghanqing
 * Date: 2018/7/25
 * Time: 20:53
 */
define('ROOT_PATH', '/home/<USER>/orp');
define('APP_NAME', 'amis');
define('IS_ORP_RUNTIME', true);
Tieba_Init::init('ala');

set_time_limit(0);
/**
 * @param $strClassName
 * @return null
 */
function __autoload($strClassName)
{
    $strNewClassName = str_replace('_', '/', $strClassName . ".php");
    $arrClass = explode('/', $strNewClassName);
    $intPathLen = count($arrClass);
    $strLastName = $arrClass [$intPathLen - 1];
    $strTmp = strtolower($strNewClassName);
    $intPreLen = strlen($strTmp) - strlen($strLastName);
    $strNewClassName = substr($strTmp, 0, $intPreLen) . $strLastName;
    $strClassPath = ROOT_PATH . '/app/' . APP_NAME . '/' . $strNewClassName;
    require_once $strClassPath;
}
spl_autoload_register('__autoload');


class revokeExpireAlaId
{
    /**
     * @param $intNowTime
     * @return bool
     */
    public function run()
    {
        $intExpireTime = Bingo_Timer::getNowTime();
        echo 'expire_time: '.$intExpireTime."\n";
        $arrInput = array(
            'is_nice' =>  1,
            'expire_time' => $intExpireTime,
        );
        $strService = "ala";
        $strMethod = "getAllocedNiceIdCountByConditions";
        $arrRes = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($arrRes === false || $arrRes['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strService::$strMethod fail. input[" . serialize($arrInput) . "] output[" . serialize($arrRes) . "]";
            Bingo_Log::warning($strLog);
            return false;
        }
        $intCount = $arrRes['data']['count'];
        if (empty($intCount)){
            echo "empty expire\n";
            return true;
        }
        $intPn = 0;
        $intPs = $intCount;

        $arrInput = array(
            'is_nice' => 1,
            'pn' => $intPn,
            'ps' => $intPs,
            'expire_time' => $intExpireTime,
        );
        var_dump($arrInput);
        $strService = "ala";
        $strMethod = "getAllocedNiceIdListByConditions";
        $arrRes = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($arrRes === false || $arrRes['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strService::$strMethod fail. input[" . serialize($arrInput) . "] output[" . serialize($arrRes) . "]";
            Bingo_Log::warning($strLog);
            return false;
        }
        $arrList = $arrRes['data']['id_list'];

        foreach ($arrList as $item) {
            if (empty($item['expire_time']) || $item['expire_time'] > $intExpireTime){
                continue;
            }
            $arrInput = array(
                'ala_id' => intval($item['ala_id']),
                'user_id' => intval($item['user_id']),
            );
            $strService = "ala";
            $strMethod = "userRevokeAlaId";
            $arrRes = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
            if ($arrRes === false || $arrRes['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strService::$strMethod fail. input[" . serialize($arrInput) . "] output[" . serialize($arrRes) . "]";
                Bingo_Log::warning($strLog);
                echo $item['ala_id'] . " : " . $item['user_id'] . "  fail \n";
                continue;
            }
            echo $item['ala_id'] . " : " . $item['user_id'] . "  succ \n";

            $arrInput = array(
                'user_id' => intval($item['user_id']),
                'alloc_type' => Lib_Ala_Define_AlaId::ALLOC_TYPE_NORMAL,
                'send_user' => 'system',
            );
            $strService = "ala";
            $strMethod = "userAllocAlaId";
            $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
            if ($arrOutput === false || (isset($arrOutput['errno']) && intval($arrOutput['errno']) !== 0)) {
                Bingo_Log::warning(__FUNCTION__ . " call service $strService : $strMethod, input =" . serialize($arrInput) . " output =" . serialize($arrOutput));
                echo $item['user_id'] . " 自动分配fail \n";
            }else{
                echo  $item['user_id'] . "  自动分配success \n";
            }

            $strContent = "系统自动回收了 user_id : " . $item['user_id'] . " 过期的靓号 " . $item['ala_id'];
            $arrInput = array(
                'user_id' => 1096972466,
                'type' => Lib_Ala_Define_Op::OP_TYPE_ALAID,
                'content' => $strContent,
                'create_time' => Bingo_Timer::getNowTime(),
            );
            $arrOutput = Service_Ala_Log_Op::addLog($arrInput);
            if ($arrOutput === false || (isset($arrOutput['errno']) && intval($arrOutput['errno']) !== 0)) {
                Bingo_Log::warning(__FUNCTION__ . " call service Service_Ala_Log_Op : addLog, input =" . serialize($arrInput) . " output =" . serialize($arrOutput));
            }
            usleep(300);
        }
    }
}

$objRank = new revokeExpireAlaId();
$objRank->run();