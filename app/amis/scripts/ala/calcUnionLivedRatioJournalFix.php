<?php
/**
 * calcUnionLivedRatioJournal.php 计算每个公会的开播率, 每晚2点执行
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 17/9/19 下午8:06
 */

define('ROOT_PATH', '/home/<USER>/orp');
define('APP_NAME', 'amis');
define('IS_ORP_RUNTIME', true);
Tieba_Init::init('ala');

set_time_limit(0);
ini_set('memory_limit', '-1');

/**
 * @param $strClassName
 * @return null
 */
function __autoload($strClassName)
{
    $strNewClassName = str_replace('_', '/', $strClassName . ".php");
    $arrClass = explode('/', $strNewClassName);
    $intPathLen = count($arrClass);
    $strLastName = $arrClass [$intPathLen - 1];
    $strTmp = strtolower($strNewClassName);
    $intPreLen = strlen($strTmp) - strlen($strLastName);
    $strNewClassName = substr($strTmp, 0, $intPreLen) . $strLastName;
    $strClassPath = ROOT_PATH . '/app/' . APP_NAME . '/' . $strNewClassName;
    require_once $strClassPath;
}

spl_autoload_register('__autoload');
$objSend = new calcUnionLivedRatioJournalFix();

// 获取昨天的起始和今天的开始时间
$intYesterdayTime = isset($argv[1]) ? intval($argv[1]) : strtotime('-1 day');
$intTodayStart = isset($argv[2]) ? intval($argv[2]) : strtotime(date('Ymd',time()));

$objSend->run($intYesterdayTime,$intTodayStart);

class calcUnionLivedRatioJournalFix
{
    //公会对应的开播率 $key=>$intUnionId  $value=>$livedRatio
    private $arrUnionLivedRatio = array();
    private static $arrUnionIdMapUnionInfo = array();
    private static $arrValidUserIds = array();

    /**
     * @param $intYesterdayTime
     * @param $intTodayStart
     * @return bool {bool}
     */
    public function run($intYesterdayTime,$intTodayStart) {
        // 获取所有公会
        $intPage   = 1;
        $intReqNum = 100;
        $count     = 100;

        //查出昨天有效开播主播
        $intYesterdayTime = isset($intYesterdayTime) ? intval($intYesterdayTime) : strtotime('-1 day');
//        $intYesterdayTime = strtotime('-1 day');   // 获取昨天的起始和今天的开始时间

        $intYesterdayDate = intval(date('Ymd',$intYesterdayTime));
        $intYesterdayStart = strtotime($intYesterdayDate);

        $intTodayStart = isset($intTodayStart) ? intval($intTodayStart) : strtotime(date('Ymd',time()));
//        $intTodayStart =  strtotime(date('Ymd',time()));

//        echo '---s;'.var_export($intYesterdayTime,1)."----s:ds".var_export($intTodayStart,1)."\n";
        // $intDurationTime = 3600 * 2;   //2hour 算有效开播
        // 直播一小时算有效开播 http://newicafe.baidu.com:80/issue/ALa-22870/show?from=page
        $intDurationTime = 3600;
        $arrUserMapLiveDuration = array();
        $intOffset = 0;
        $intPs = 10;
        $intHasMore = 1;
        while($intHasMore) {
            $arrOutput = Dl_Ala_User_User::getYesterdayLiveInfo($intYesterdayStart, $intTodayStart, $intOffset, $intPs);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_User_User::getYesterdayLiveInfo fail. input:[$intPage, $intReqNum]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                echo "$strLog \n";
                return false;
            }
            foreach ($arrOutput['data'] as $value) {
                $arrUserMapLiveDuration[intval($value['user_id'])] = intval($arrUserMapLiveDuration[intval($value['user_id'])]) + intval($value['live_duration']);
            }

            $intResThisTime = count($arrOutput['data']);
            if($intResThisTime < $intPs) {
                $intHasMore = 0;
            }
            $intOffset += $intPs;
        }

        foreach ($arrUserMapLiveDuration as $intTempUserId => $intLiveDuration) {
            if($intLiveDuration >= $intDurationTime) {
                self::$arrValidUserIds[] = $intTempUserId;
            }
        }

        echo "------------valid_total_count=" .  count(self::$arrValidUserIds). "-----------\n";
        echo "==========" . var_export(self::$arrValidUserIds, true);  //for test


        // 公会列表
        while ($count == $intReqNum) {
            $arrOutput = Dl_Ala_Union_Union::getList($intPage, $intReqNum);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getList fail. input:[$intPage, $intReqNum]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return false;
            }

            $arrUnionList = $arrOutput['data'];

            $this->debugInfo(__CLASS__.'::'.__FUNCTION__.' $arrUnionList:'.serialize($arrUnionList));

            if (is_array($arrUnionList) && !empty($arrUnionList)) {
                foreach ($arrUnionList as $arrUnionInfo) {
                    self::$arrUnionIdMapUnionInfo[intval($arrUnionInfo['union_id'])] = $arrUnionInfo;
                    $this->calcUnionLivedRatio(intval($arrUnionInfo['union_id']), intval($arrUnionInfo['union_anchor_num']), $intYesterdayDate);
                }
            }

            $count = count($arrUnionList);
            $intPage++;

            usleep(100);
        }

        //更新至数据库
        foreach ($this->arrUnionLivedRatio as $intUnionId => $intLivedRatio) {
            $arrOutput = Dl_Ala_Union_Union::updateUnionLivedRatio($intUnionId, $intLivedRatio);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::updateUnionLivedRatio fail. input:[$intUnionId, $intLivedRatio]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return false;
            }
            usleep(100);
        }

        return true;
    }

    /**
     * 计算公会开播率
     * @param int $intUnionId  int $intUnionAnchorNum
     * @return bool
     */
    private function calcUnionLivedRatio($intUnionId, $intUnionAnchorNum, $intDate) {

        $intPage   = 1;
        $intReqNum = 100;
        $count     = 100;

        if (intval($intUnionAnchorNum) <= 0) {
            $this->arrUnionLivedRatio[$intUnionId] = 0;
            return true;
        }

        // 18/4/20 gaojingjing03 新需求：对现有主播开播时间进行筛查，对于已经进入公会且超过60天未开播的，系统自动解除公会绑定。
        $intNow = Bingo_Timer::getNowTime();
        $intNeedCheckTime = 60 * 24 * 3600;

        // 获取该公会下的主播列表
        $arrAnchorUserIds = $arrNeedUnbindCheck = array();
        $intValidAnchorNum = 0;
        while ($count == $intReqNum) {
            $arrOutput = Dl_Ala_Union_Anchor::getList($intUnionId, $intPage, $intReqNum);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Anchor::getList fail. input:[$intUnionId, $intPage, $intReqNum]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);

                return false;
            }
            $arrAnchorList = $arrOutput['data'];

            $this->debugInfo(__CLASS__.'::'.__FUNCTION__.' $arrAnchorList:'.serialize($arrAnchorList));

            if (is_array($arrAnchorList) && !empty($arrAnchorList)) {
                foreach ($arrAnchorList as $arrAnchorInfo) {
                    if ( Lib_Ala_Define_UnionAnchor::JOIN_STATUS_PASSED == $arrAnchorInfo['status']) {
                        $arrAnchorUserIds[] = $arrAnchorInfo['anchor_user_id'];

                        // 18/4/20 gaojingjing03 新需求：对现有主播开播时间进行筛查，对于已经进入公会且超过60天未开播的，系统自动解除公会绑定。
                        if ($intNow - $arrAnchorInfo['update_time'] > $intNeedCheckTime) {
                            $arrNeedUnbindCheck[$arrAnchorInfo['anchor_user_id']] = $arrAnchorInfo['update_time'];
                        }

                        if(in_array(intval($arrAnchorInfo['anchor_user_id']), self::$arrValidUserIds)) {
                            $intValidAnchorNum += 1;
                        }
                    }
                }
            }

            $count = count($arrAnchorList);
            $intPage++;

            usleep(100);
        }

//        //获取对应主播info
//        $req = array('uids' => $arrAnchorUserIds,);
//        $res = Tieba_Service::call('ala', 'userGetInfo', $req, null, null, 'post', 'php', 'utf-8');
//        if(false === $res || !isset($res['errno']) || Tieba_Errcode::ERR_SUCCESS !== $res['errno']) {
//            Bingo_Log::warning(__FUNCTION__." call service failed : userGetInfo, input =".serialize($req)." output =".serialize($res));
//            return false;
//        }
//        $arrAnchorInfos = $res['data'];
//        //统计开播主播数量
//        $intUnionLivedAnchorCount = 0;
//        foreach ($arrAnchorInfos as $arrAnchorInfo) {
//            if (isset($arrAnchorInfo['live_id']) && $arrAnchorInfo['live_id'] != 0 ) {
//                ++$intUnionLivedAnchorCount;
//            }
//
//            // 18/4/20 gaojingjing03 新需求：对现有主播开播时间进行筛查，对于已经进入公会且超过60天未开播的，系统自动解除公会绑定。
//            $intAnchorUserId = $arrAnchorInfo['user_id'];
//
//            //0 === 1   false   7月31号下掉自动退出工会
//            if (0 === 1 && isset($arrNeedUnbindCheck[$intAnchorUserId]) && (empty($arrAnchorInfo['live_id']) || $intNow - $arrAnchorInfo['start_time'] > $intNeedCheckTime)) {
//                // live_id为空，二次确认，防止调用用户扩展属性service失败
//                if (empty($arrAnchorInfo['live_id'])) {
//                    $arrInput = array(
//                        'user_id' => $intAnchorUserId,
//                    );
//
//                    $arrRet = Alalib_Util_Service::call('ala', 'selectMaxLiveIdByUser', $arrInput);
//
//                    // 查询失败，或者查询live_id不为空，则不处理
//                    if (false === $arrRet || $arrRet['errno'] != Alalib_Conf_Error::ERR_SUCCESS || !empty($arrRet['data']['live_id'])) {
//                        continue;
//                    }
//                }
//
//                // 解除绑定
//                $arrOutput = Util_Ala_Anchor::delUnionAnchor($intUnionId, $intAnchorUserId, Lib_Ala_Define_UnionAnchor::JOIN_STATUS_PASSED);
//
//                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
//                    Bingo_Log::warning('Auto Unbind Anchor Failed; $intAnchorUserId:'.$intAnchorUserId.' $intUnionId:'.$intUnionId.' $arrAnchorInfo:'.serialize($arrAnchorInfo));
//                } else {
//                    Bingo_Log::warning('Auto Unbind Anchor Success; $intAnchorUserId:'.$intAnchorUserId.' $intUnionId:'.$intUnionId.' $arrAnchorInfo:'.serialize($arrAnchorInfo));
//                    $this->_sendMail('Auto Unbind Anchor Success; $intAnchorUserId:'.$intAnchorUserId.' $intUnionId:'.$intUnionId.' $arrAnchorInfo:'.serialize($arrAnchorInfo));
//                }
//
//                usleep(100);
//            }
//        }


        //开播率
        $this->arrUnionLivedRatio[$intUnionId] = intval(round($intValidAnchorNum * 100 / $intUnionAnchorNum));


        //更新到开播率新表
        $arrOutput = Dl_Ala_Union_Union::updateUnionLivedRatioTwo($intUnionId, $intUnionAnchorNum, $intDate, $intValidAnchorNum);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::updateUnionLivedRatioTwo fail. input:[$intUnionId]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return false;
        }




        return true;
    }

    /**
     * 发邮件
     * @param $strContent
     * @return bool
     */
    private function _sendMail($strContent) {
        $boundary = '----='.uniqid();
        $previewToMail = array(
            '<EMAIL>',
            '<EMAIL>',
        );
        $to = implode(',', $previewToMail);

        $headers = "MIME-Version: 1.0\r\n";
        $headers .= "Content-Transfer-Encoding: 8bit\r\n";
        $headers .= "From: <EMAIL>\r\n";
        $headers .= "Content-type: multipart/mixed; boundary=\"$boundary\""  . "\r\n";
        //echo $to;
        $subject =  "Amis公会60天未开播自动解除通知";
        $subject = "=?UTF-8?B?" . base64_encode($subject) . "?=";
        $ret =  mail($to, $subject, $strContent, $headers);
        return $ret;
    }

    /**
     * debug info
     * @param $str
     * @return true
     */
    public function debugInfo($str) {
        Bingo_Log::warning($str);
        echo $str."\r\n";

        return true;
    }
}
