<?php
/**
 * sendUnionAnchorDiamond.php 公会主播蓝钻分成，每晚2点执行
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 17/8/18 上午11:06
*/

define('ROOT_PATH', '/home/<USER>/orp');
define('APP_NAME', 'amis');
define('IS_ORP_RUNTIME', true);
Tieba_Init::init('ala');

set_time_limit(0);
ini_set('memory_limit', '-1');

/**
 * @param $strClassName
 * @return null
 */
function __autoload($strClassName)
{
    $strNewClassName = str_replace('_', '/', $strClassName . ".php");
    $arrClass = explode('/', $strNewClassName);
    $intPathLen = count($arrClass);
    $strLastName = $arrClass [$intPathLen - 1];
    $strTmp = strtolower($strNewClassName);
    $intPreLen = strlen($strTmp) - strlen($strLastName);
    $strNewClassName = substr($strTmp, 0, $intPreLen) . $strLastName;
    $strClassPath = ROOT_PATH . '/app/' . APP_NAME . '/' . $strNewClassName;
    require_once $strClassPath;
}
spl_autoload_register('__autoload');

$bolDebug = true;

if (!empty($argv[1]) && 'exec' == $argv[1]) {
    // 脚本默认为debug模式，只进行数据统计，不进行蓝钻的实际发放，邮件只发给gaojingjing03
    // 实际执行必须添加参数：'exec'，如下所示：
    // php -f ~/orp/app/amis/scripts/ala/sendUnionAnchorDiamond.php 'exec'
    $bolDebug = false;
}

$bolLog = false;

// 17/11/16 gaojingjing03 增加log模式
if (!empty($argv[1]) && 'log' == $argv[1]) {
    // 脚本默认为debug模式，只进行数据统计，不进行蓝钻的实际发放，发放状态为1，邮件只发给gaojingjing03
    // log模式，只记录用户的统计数据，不进行蓝钻的实际发放，发放状态为0，邮件只发给gaojingjing03
    // php -f ~/orp/app/amis/scripts/ala/sendUnionAnchorDiamond.php 'log'
    $bolLog = true;
}

$objSend = new sendUnionAnchorDiamond();
$objSend->setDebug($bolDebug);
$objSend->setLogMode($bolLog);
$objSend->run();


class sendUnionAnchorDiamond
{
    private $bolDebug = false;

    private $bolLog = false;

    /**
     * run
     * @return bool
     */
    public function run() {
        $arrInput = array();
        $intPage   = 1;
        $intReqNum = 1000;
        $count     = 1000;

        $arrUserIds = array();

        while ($count == $intReqNum) {
            $arrReturnData  = Dl_Ala_Union_Anchor::getListByTime($arrInput, $intPage, $intReqNum);

            $arrReturnList  = $arrReturnData['data'];

            foreach ($arrReturnList as $v) {
                $arrUserIds[] = $v['anchor_user_id'];
            }

            $count = count($arrReturnList);
            $intPage++;
        }

        echo implode("\n", $arrUserIds);

    }


    /**
     * 设置debug模式
     * @param bool $bolDebug
     * @return bool
     */
    public function setDebug($bolDebug) {
        $this->bolDebug = $bolDebug;
        return true;
    }

    /**
     * 设置log模式
     * @param bool $bolLog
     * @return bool
     */
    public function setLogMode($bolLog) {
        $this->bolLog = $bolLog;
        return true;
    }

    /**
     * debug info
     * @param $str
     * @return true
     */
    public function debugInfo($str) {
        Bingo_Log::warning($str);
        echo $str."\r\n";

        return true;
    }
}