<?php
/**
 * testAnchorUnionPresidentRedis.php
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 17/8/21 下午4:48
*/

define('ROOT_PATH', '/home/<USER>/orp');
define('APP_NAME', 'amis');
define('IS_ORP_RUNTIME', true);
Tieba_Init::init('ala');

set_time_limit(0);
ini_set('memory_limit', '-1');

/**
 * @param $strClassName
 * @return null
 */
function __autoload($strClassName)
{
    $strNewClassName = str_replace('_', '/', $strClassName . ".php");
    $arrClass = explode('/', $strNewClassName);
    $intPathLen = count($arrClass);
    $strLastName = $arrClass [$intPathLen - 1];
    $strTmp = strtolower($strNewClassName);
    $intPreLen = strlen($strTmp) - strlen($strLastName);
    $strNewClassName = substr($strTmp, 0, $intPreLen) . $strLastName;
    $strClassPath = ROOT_PATH . '/app/' . APP_NAME . '/' . $strNewClassName;
    require_once $strClassPath;
}
spl_autoload_register('__autoload');

$objSend = new testAnchorUnionPresidentRedis();
$objSend->run();


class testAnchorUnionPresidentRedis
{
    /**
     * run
     * @return bool
     */
    public function run() {
        // 获取所有公会
        $intPage   = 1;
        $intReqNum = 100;
        $count     = 100;

        while ($count == $intReqNum) {
            // 工会列表
            $arrOutput = Dl_Ala_Union_Union::getList($intPage, $intReqNum);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getList fail. input:[$intPage, $intReqNum]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return false;
            }

            $arrUnionList = $arrOutput['data'];

            $this->debugInfo(__CLASS__.'::'.__FUNCTION__.' $arrUnionList:'.serialize($arrUnionList));

            foreach ($arrUnionList as $arrUnionInfo) {
                $this->testAnchorUnionPresident($arrUnionInfo);
            }

            $count = count($arrUnionList);
            $intPage++;

            usleep(100);
        }

        $this->debugInfo(__CLASS__.'::'.__FUNCTION__.' all union finished.');

        return true;
    }

    /**
     * testAnchorUnionPresident
     * @param array $arrUnionInfo
     * @return bool
     */
    private function testAnchorUnionPresident($arrUnionInfo) {
        $intUnionId          = $arrUnionInfo['union_id'];
        $intUnionPresidentId = $arrUnionInfo['union_user_id'];

        $intPage   = 1;
        $intReqNum = 100;
        $count     = 100;

        $strUrl = 'http://tieba.baidu.com/present/getAnchorUnionPresident?anchor_user_id=';

        $this->debugInfo(__CLASS__.'::'.__FUNCTION__.' start $intUnionId:'.$intUnionId);

        $intTotalNum = $intPassedNum = 0;

        while ($count == $intReqNum) {
            // 获取该公会下的主播列表
            $arrOutput = Dl_Ala_Union_Anchor::getList($intUnionId, $intPage, $intReqNum);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getList fail. input:[$intUnionId, $intPage, $intReqNum]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);

                return false;
            }
            $arrAnchorList = $arrOutput['data'];

//            $this->debugInfo(__CLASS__.'::'.__FUNCTION__.' $arrAnchorList:'.serialize($arrAnchorList));

            foreach ($arrAnchorList as $arrAnchorInfo) {
                $intTotalNum++;

                $obj = orp_FetchUrl::getInstance();

                $strContent = $obj->get($strUrl.$arrAnchorInfo['anchor_user_id']);

//                $strContent = file_get_contents($strUrl.$arrAnchorInfo['anchor_user_id']);

                $arrContent = json_decode($strContent, true);

                if (empty($arrContent['data']['president_id'])) {
                    $this->debugInfo(__CLASS__.'::'.__FUNCTION__.' No President_id. $intUnionId:'.$intUnionId.' Anchor_user_id:'.$arrAnchorInfo['anchor_user_id']);
                    continue;
                }

                if ($intUnionPresidentId != $arrContent['data']['president_id']) {
                    $this->debugInfo(__CLASS__.'::'.__FUNCTION__.' President_id Not equal. $intUnionId:'.$intUnionId.' Anchor_user_id:'.$arrAnchorInfo['anchor_user_id'].' $arrContent:'.serialize($arrContent));
                    continue;
                }

                $intPassedNum++;
            }

            $count = count($arrAnchorList);
            $intPage++;

            usleep(100);
        }

        $intFailedNum = $intTotalNum - $intPassedNum;

        $this->debugInfo(__CLASS__.'::'.__FUNCTION__.' finished $intUnionId:'.$intUnionId.' Total:'.$intTotalNum.'; Passed:'.$intPassedNum.'; Failed:'.$intFailedNum);

        return true;
    }

    /**
     * 设置debug模式
     * @param bool $bolDebug
     * @return bool
     */
    public function setDebug($bolDebug) {
        $this->bolDebug = $bolDebug;
        return true;
    }

    /**
     * debug info
     * @param $str
     * @return true
     */
    public function debugInfo($str) {
        Bingo_Log::warning($str);
        echo $str."\r\n";

        return true;
    }
}