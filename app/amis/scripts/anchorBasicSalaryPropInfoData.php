<?php
/**
 * Created by PhpStorm.
 * 脚本写入手百推送白名单的最近直播时间 每天凌晨跑
 * User: weiyan02
 * Date: 2019/3/25
 * Time: 下午3:38
 */

ini_set('memory_limit', '-1');
define('MODULE_NAME', 'amis');
date_default_timezone_set("Asia/Chongqing");
define('APP_NAME', 'amis');
define('SCRIPT_NAME', 'anchorBasicSalaryMaxLiveDurationData');
define('ROOT_PATH', dirname(__FILE__) . '/../../..');
define('SCRIPT_ROOT_PATH', ROOT_PATH . '/app/' . APP_NAME . '/scripts');
define('SCRIPT_LOG_PATH', ROOT_PATH . '/log/app/' . APP_NAME);
define('SCRIPT_CONF_PATH', ROOT_PATH . '/conf/app/' . APP_NAME);
define('BASE_PATH', dirname(__FILE__));
define('IS_ORP_RUNTIME', true);
set_time_limit(0);
Tieba_Init::init('ala');
//加日志运行状况统计
Bingo_Log::warning('OrpCronStart:17159');
Tieba_Service::call('ala', 'assignCronTab', array('task_id' => 17159), null, null, "post", null, "utf-8");
if (!defined('REQUEST_ID')) {
    $requestTime = gettimeofday();
    define('REQUEST_ID', (intval($requestTime ['sec'] * 100000 + $requestTime ['usec'] / 10) & 0x7FFFFFFF));
}

if (function_exists('camel_set_logid')) {
    camel_set_logid(REQUEST_ID);
}
Bingo_Log::init(array(
    LOG_SCRIPT => array(
        'file' => SCRIPT_LOG_PATH . '/' . SCRIPT_NAME . '/' . SCRIPT_NAME . "_$requestTime.log",
        'level' => 0x01 | 0x02 | 0x04 | 0x08,
    ),
), LOG_SCRIPT);


/**
 * @param $strClassName
 * @return null
 */
function __autoload($strClassName)
{
    $strNewClassName = str_replace('_', '/', $strClassName . ".php");
    $arrClass = explode('/', $strNewClassName);
    $intPathLen = count($arrClass);
    $strLastName = $arrClass [$intPathLen - 1];
    $strTmp = strtolower($strNewClassName);
    $intPreLen = strlen($strTmp) - strlen($strLastName);
    $strNewClassName = substr($strTmp, 0, $intPreLen) . $strLastName;
    $strClassPath = ROOT_PATH . '/app/' . APP_NAME . '/' . $strNewClassName;
    require_once $strClassPath;
}

spl_autoload_register('__autoload');

class anchorBasicSalaryPropInfoData
{
    /**
     * amis_live_anchor_journal新增字段prop_count,prop_tdou 道具是20190408以后上线，故时间从20190408开始至今
     * @param null
     * @return bool
     */
    public function run()
    {
        $intEndTime = strtotime(date('Y-m-d',strtotime('+1 day'))); //明天凌晨时间戳
        $intStartTime = 1554652800;  //2019-04-08 00:00:00  道具是20190408以后上线，故时间从20190408开始至今

        for ($flag = $intStartTime; $flag <= $intEndTime; $flag += 24*60*60) {
            echo '----flag---:'.var_export($flag,1)."\n";
            $intSelectTime = date('Ymd', $flag);
            $arrServiceInput = array(
                'date' => $intSelectTime,
            );
            $arrOutput = Service_Ala_Journal_Journal::getAnchorJournalListOfDay($arrServiceInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Journal_Journal::getAnchorSumJournalList input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                //如果失败则flag重新执行
                $flag = $flag - 24*60*60;
                continue;
            }
            if(empty($arrOutput['data'])){
                return true;
            }
            $arrUserIdsOfDay = array();
            $intDay = 0;
            if (!empty($arrOutput['data'])) {
                $resAnchorInfo = $arrOutput['data'];
                unset($arrOutput);
                foreach ($resAnchorInfo as $arrAnchorInfo) {
                    $arrUserIdsOfDay[] = $arrAnchorInfo['user_id'];
                    $intDay = $arrAnchorInfo['date'];
                }
            }
            $arrAnchorPropInfoRes = array();
            $arrAnchorPropInfo = self::getAnchorPropGiftInfo($arrUserIdsOfDay,$flag,$flag+24*60*60);
            if(!empty($arrAnchorPropInfo)) {
                foreach ($arrAnchorPropInfo as $k => $v) {
                    foreach ($v as $key => $value) {
                        foreach ($value as $key_v => $value_v) {
                            $intAnchorPropId = $key_v;
                            $intAnchorPropCount = $value_v['prop_count'];
                            $intAnchorPropTdou = $value_v['prop_tdou'];
                            if (!isset($arrAnchorPropInfoRes[$intAnchorPropId]['user_id'])) {
                                $arrAnchorPropInfoRes[$intAnchorPropId]['user_id'] = $intAnchorPropId;
                                $arrAnchorPropInfoRes[$intAnchorPropId]['prop_count'] = 0;
                                $arrAnchorPropInfoRes[$intAnchorPropId]['prop_tdou'] = 0;
                            }
                            $arrAnchorPropInfoRes[$intAnchorPropId]['prop_count'] += $intAnchorPropCount;
                            $arrAnchorPropInfoRes[$intAnchorPropId]['prop_tdou'] += $intAnchorPropTdou;
                        }
                    }
                }
            }

            $Obj = new Tieba_Multi('add_prop_info');
            $strServiceKey = 'ala';
            $strUserMethod = 'addAnchorJournalPropInfo';
            $caller = new Tieba_Service($strServiceKey);
            foreach ($arrUserIdsOfDay as $intUserId) {
                if(empty($arrAnchorPropInfoRes[$intUserId]['prop_count']) && empty($arrAnchorPropInfoRes[$intUserId]['prop_tdou'])){
                    continue;
                }
                $arrServiceParams = array(
                    'user_id' => $intUserId,
                    'date' => $intDay,
                    'prop_count' => $arrAnchorPropInfoRes[$intUserId]['prop_count'],
                    'prop_tdou'  => $arrAnchorPropInfoRes[$intUserId]['prop_tdou'],
                );

                $strKey = $strUserMethod . '_' . $intDay . '_' . $intUserId;
                $arrInput = array(
                    'serviceName' => $strServiceKey,
                    'method' => $strUserMethod,
                    'ie' => 'utf-8',
                    'input' => $arrServiceParams,
                );
                echo '----param:'.var_export($arrInput,1)."\n";
                $Obj->register($strKey, $caller, $arrInput);
            }
            $Obj->call();
            foreach ($arrUserIdsOfDay as $intUserId) {
                if(empty($arrAnchorPropInfoRes[$intUserId]['prop_count']) && empty($arrAnchorPropInfoRes[$intUserId]['prop_tdou'])){
                    continue;
                }
                $strKey = $strUserMethod . '_' . $intDay . '_' . $intUserId;
                $resultUser = $Obj->getResult($strKey);
                $arrOutput = $resultUser['data'];
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strKey fail. input:[" . "]  output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                    file_put_contents('/home/<USER>/set_fail_prop_uid', $intUserId . "\n", FILE_APPEND);
                }
            }
            file_put_contents('/home/<USER>/set_success_info', $flag . "\n", FILE_APPEND);
            sleep(1);
            }

    }

    /**
     * @brief 根据主播id获取座驾信息
     * @param $arrUserId
     * @param $intYesterdayStart
     * @param $intTodayStart
     * @return array().
     */
    protected  static function getAnchorPropGiftInfo($arrUserId,$intYesterdayStart,$intTodayStart)
    {
        //筛选时间
        $intStartTime = $intYesterdayStart;
        $intEndTime   = $intTodayStart;
        $arrUsersReq = array();
        $arrRet = array();

        $arrUserIds = $arrUserId;
        if (empty($arrUserIds)) {
            return $arrRet;
        }
        $offset = 0;
        for ($i = 0; $i < (count($arrUserIds) / 10); $i++) {
            $arrUsersReq[] = array_slice($arrUserIds, $offset, 10);
            $offset = $offset + 10;
        }
        for ($timestamp = $intStartTime;$timestamp < $intEndTime;$timestamp = $lastday) {
            $firstday = strtotime(date('Y-m-d', $timestamp));
            $lastday = $firstday + 24*60*60;
            $Obj = new Tieba_Multi('getAnchorPropGiftInfo');
            $strServiceKey = 'present';
            $strUserMethod = 'getAnchorPropGiftInfo';
            $caller = new Tieba_Service($strServiceKey);
            foreach ($arrUsersReq as $index => $item) {
                $arrServiceParams = array(
                    'user_ids' => $item,
                    'start_time' => $firstday,
                    'end_time' => $lastday,
                );
                $strKey = $strUserMethod . '_' . $index;
                $arrInput = array(
                    'serviceName' => $strServiceKey,
                    'method' => $strUserMethod,
                    'ie' => 'utf-8',
                    'input' => $arrServiceParams,
                );
                echo 'QQQQ adsfasdfa:'.var_export($arrInput,1)."\n";
                $Obj->register($strKey, $caller, $arrInput);
            }
            $Obj->call();
            foreach ($arrUsersReq as $index => $item) {
                $strKey = $strUserMethod . '_' . $index;
                $resultUser = $Obj->getResult($strKey);
                foreach ($resultUser['data'] as $k => $v) {
                    $resultTmp = array();
                    $resultTmp[$k] = $v;
                    $arrRet[] = $resultTmp;
                }
            }
        }
        return $arrRet;
    }

}

$intStartTime = time();
$rank = new anchorBasicSalaryPropInfoData();
$rank->run();
$intEndTime = time();
$intDurTime = $intEndTime - $intStartTime;
$intMemory = memory_get_usage();
echo 'duration is:' . var_export($intDurTime, 1) . 'start:' . var_export($intStartTime, 1) . 'end:' . var_export($intEndTime, 1) . 'memory is:' . var_export($intMemory, 1) . "\n";
