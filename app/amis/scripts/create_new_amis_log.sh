#!/usr/bin/env bash

#
# Author: <PERSON><PERSON><PERSON>n
# Description: 用户每月在 es 集群上按月创建 uegmonitor 索引
#

datestr=`date -d"+1 month" +"%Y%m"`
#tablename='newmis_logsystem_'
tablename='amisnew_log_system_'


curl --user superuser:superman -XPUT "http://nj03-ditu-mola55.nj03:8500/${tablename}${datestr}?pretty" -d'
{
	"settings" : {
		"number_of_shards" : 6,
		"number_of_replicas" : 2,
		"refresh_interval" : "10s"
	},
	"mappings": {
            "recall": {
                "dynamic": "true",
                "_all": {
                    "enabled": false
                },
                "properties": {
                    "req_url": {
                        "type": "text",
                        "analyzer": "ik_max_word"
                    },
		            "req_input": {
                        "type": "text",
                        "analyzer": "ik_max_word"
                    },
		            "req_output": {
                        "type": "text",
                        "analyzer": "ik_max_word"
                    },
                    "status": {
                        "type": "byte"
                    },
                    "op_id": {
                        "type": "long"
                    },
                    "op_name": {
                        "type": "text",
                        "analyzer": "ik_max_word"
                    },
                    "op_time": {
                        "type": "long"
                    },
                    "call_from": {
                        "type": "text",
                        "analyzer": "ik_max_word"
                    },
                    "ext_desc" :{
                        "type": "text",
                        "analyzer": "ik_max_word"
                    }
                }
            }
        }
}'
