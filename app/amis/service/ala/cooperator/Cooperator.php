<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR> @date 2015:10:14 10:48:22
 * @version
 * @structs & methods(copied from idl.)
 */
class Service_Ala_Cooperator_Cooperator extends Service_Ala_Base
{

    /**
     * 获取合作运营
     * @param $arrInput
     * @return array
     */
    public static function getUnionCooperator($arrInput)
    {
        $arrDlInput = array();
        $arrDlOutput = Dl_Ala_Cooperator_Cooperator::getUnionCooperator($arrDlInput);
        if(false === $arrDlOutput || Tieba_Errcode::ERR_SUCCESS !== $arrDlOutput['errno']){
            $strLog = __CLASS__."::".__FUNCTION__." call Dl_Ala_Cooperator_Cooperator::getUnionCooperator. input:[".serialize($arrDlInput)."]; output:[".serialize($arrDlOutput)."]";
            Bingo_Log::warning($strLog);

            return self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrDlOutput['data']);
    }

}
