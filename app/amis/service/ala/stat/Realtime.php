<?php
/**
 *
 * <AUTHOR>
 * @date 2019/4/29
 */

class Service_Ala_Stat_Realtime extends Service_Ala_Base
{

    /**
     * @param string $statDate
     * @param array $showCategories
     * @return array
     */
    public static function getCategories($statDate, &$showCategories)
    {
        $cateInterval = 15;
        $currentTime = time();
        $categories = array();
        $startTime = strtotime($statDate); // + $cateInterval * 60;
        for ($statDateTime = $startTime; $statDateTime < $startTime + 86400; $statDateTime += $cateInterval * 60) {
            $categories[] = date("H:i", $statDateTime);
            if ($statDateTime + $cateInterval * 60 > $currentTime) {
                break;
            }
            $showCategories[] = date("H:i", $statDateTime);
        }

        return $categories;
    }


    /**
     * 取广告位大类下所有根节点广告位
     *
     * @param $parentId
     * @return array
     */
    public static function getPositionTreeLeaf($parentId)
    {
        $arrInput = array(
            'parent_id' => $parentId
        );
        $arrOutput = Tieba_Service::call('standalone', 'getPositionTreeLeaf', $arrInput, null, null, 'post', 'php', 'gbk');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call standalone::getAdPositionTree fail. input:[" . serialize($arrOutput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return array();
        }

        return $arrOutput['data'];
    }

    /**
     * @param $dateList
     * @param $positionId
     * @param $indicatorId
     * @return array
     */
    public static function multiGetPeriodData($dateList, $positionId, $indicatorId)
    {
        $objMultiCall = new Tieba_Multi('get_period_data');
        foreach ($dateList as $date => $dateKey) {
            $arrReq = array(
                'stat_date' => $date,
                'position_id' => $positionId,
                'indicator_id' => $indicatorId,
            );
            $arrReqParam = array(
                'serviceName' => 'standalone',
                'method'      => 'getPeriodData',
                'input'       => $arrReq,
                'ie'          => 'utf-8',
                'extra'       => array(
                    'rtimeout' => 10000
                )
                //'calltype'    => 'local',
            );
            $objMultiCall->register($dateKey, new Tieba_Service('standalone'), $arrReqParam);
        }
        $result = $objMultiCall->call();
        $arrPeriodData = array();
        foreach($result as $dateKey => $arrOut) {
            if ($arrOut === false || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                $strLog = "standalone::getPeriodData call fail. date[".serialize($arrReq)."] output[".serialize($arrOut)."]";
                Bingo_Log::warning($strLog);
                continue;
            }
            $arrPeriodData[$dateKey] = $arrOut['data'];
        }

        return $arrPeriodData;
    }


}