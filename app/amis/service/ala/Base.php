<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR> @date 2015:10:14 10:48:22
 * @version
 * @structs & methods(copied from idl.)
 */
class Service_Ala_Base
{

    /**
     * 返回值
     *
     * @param {Int} $intErrno
     *            default Alalib_Conf_Error::ERR_SUCCESS
     * @param {Array} $arrOutData
     *            default array()
     * @param {String} $strMsg
     *            default ""
     * @return {Array} :errno :errmsg :{Array}output
     *        
     */
    public static function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS, $arrOutData = array(), $strMsg = "")
    {
        $arrOutput = array();
        $arrOutput['errno'] = $intErrno;
        $arrOutput['errmsg'] = Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput['usermsg'] = $strMsg;
        $arrOutput['data'] = $arrOutData;
        return $arrOutput;
    }
}
