<?php
/**
 * Anchor.php 主播
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 17/7/21 上午10:05
 */

class Service_Ala_Union_Anchor extends Service_Ala_Base
{

    /**
     * 获取主播的联系方式、照片
     * 
     * @param array $arrAnchorIds
     * @return array array(
     *                  {$intAnchorId1} => array(
     *                       'phone_number' => '13811111111',
     *                       'photo'        => 'http://xxx',
     *                       'note'         => '',
     *                  ),
     *                  {$intAnchorId2} => array(...),
     *                  ...
     *               )
     */
    public static function getAnchorInfo($arrAnchorIds) {
        // 获取认证信息里的联系方式
        $arrServiceInput = array(
            'user_ids' => (array) $arrAnchorIds
        );
        
        $strServiceName = "ala";
        $strServiceMethod = "getAuthenticationList";
        
        $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
        
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        
        $arrAuthList = $arrOutput['data'];
        
        $arrAuthUserList = array();
        foreach ($arrAuthList as $v) {
            $arrAuthUserList[$v['user_id']] = $v;
        }

        // 获取主播的照片
        $arrPhotoData = Dl_Ala_Anchor_Photo::getPhotoList($arrAnchorIds);
        $arrPhotoList = $arrPhotoData['data'];

        $arrRetData = $arrNeedAnchorIds = array();
        foreach ($arrAnchorIds as $anchorId) {
            $arrRetData[$anchorId] = isset($arrAuthUserList[$anchorId]) ? array(
                'phone_number' => $arrAuthUserList[$anchorId]['phone_number'],
                'photo' => $arrPhotoList[$anchorId]['photo'][0],
                'note' => $arrPhotoList[$anchorId]['note'],
                'anchor_level_previous' => $arrAuthUserList[$anchorId]['anchor_level_previous'],
                'anchor_level_present' => $arrAuthUserList[$anchorId]['anchor_level_present'],
            ) : array();

            if (empty($arrRetData[$anchorId]['photo'])) {
                // 17/11/8 gaojingjing03 PM需求：照片为空，获取主播最近的一次直播间封面展示
                $arrNeedAnchorIds[] = $anchorId;
            }
        }

        // 17/11/8 gaojingjing03 PM需求：照片为空，获取主播最近的一次直播间封面展示
        $arrNeedPhotos = Util_Ala_Photo::getAnchorLiveCover($arrNeedAnchorIds);

        foreach ($arrNeedPhotos as $intPhotoUserId => $arrPhotoInfo) {
            $arrRetData[$intPhotoUserId]['photo'] = $arrPhotoInfo['photo'];
        }
        
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function processModifyAnchorLevel($arrInput) {
        $intUserId = intval($arrInput['user_id']);
        $intModifyUserId = intval($arrInput['modify_user_id']);
        $strLevelModify = strval($arrInput['anchor_level_modify']);
        $intUserRoleId = intval($arrInput['user_role_id']);

        if(empty($intUserId) || empty($intModifyUserId) || empty($strLevelModify)) {
            $strLog = __CLASS__."::".__FUNCTION__. " param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR,array());
        }

        if($intUserRoleId != Lib_Ala_Define_Role::ROLE_ADMIN) {
            Bingo_Log::warning("user is not administrator,can not update anchor level");
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), "非管理员角色不能修改主播等级");
        }

        // 权限
        $arrInput = array(
            'user_id' => $intUserId,
            'auth_id' => Lib_Ala_Define_Authority::AUTHORITY_LIVE_MANAGEMENT_ANCHOR_MANAGEMENT_UPDATE_LEVEL,
        );
        $arrOutput = Service_Ala_Authority_Authority::canUserAccess($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Authority_Authority::canUserAccess input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $bolCanAccess = $arrOutput['data'];
        if ($bolCanAccess === false) {
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), "无权限,不能修改主播等级");
        }

        $boolNeedCheckCount = self::userNeedCheckModifyCount($intUserRoleId);
        $intCount = 0;
        if(true === $boolNeedCheckCount) {
            $arrInput = array(
                'user_id' => $intUserId,
            );
            $arrOutput = Tieba_Service::call('ala', 'getUserModifyAnchorLevelCount', $arrInput, null, null, 'post', 'php', 'utf-8');
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala::getUserModifyAnchorLevelCount fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $intCount = intval($arrOutput['data']);

            $arrOutput = Dl_Ala_Union_Anchor::getUserManageAnchorCount($arrInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala::getUserManageAnchorCount fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            $intTotalCount = intval( intval($arrOutput['data']) * Lib_Ala_Define_Role::ROLE_OPERATE_USER_ANCHOR_LEVEL_PERCENT );

            if($intCount >= $intTotalCount) {
                Bingo_Log::warning("user count use over");
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), "这个周期内您只能修改" . $intTotalCount . "次主播等级,已经用完了!");
            }
        }

        $arrInput = array(
            'user_id' => $intModifyUserId,
            'anchor_level' => $strLevelModify,
        );
        $arrOutput = Tieba_Service::call('ala', 'setUserAnchorLevel', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala::setUserAnchorLevel fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        if(true === $boolNeedCheckCount) {
            $arrInput = array(
                'user_id' => $intUserId,
                'count' => ($intCount + 1),
            );
            $arrOutput = Tieba_Service::call('ala', 'setUserModifyAnchorLevelCount', $arrInput, null, null, 'post', 'php', 'utf-8');
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala:setUserModifyAnchorLevelCount fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
            }

            return self::errRet(Tieba_Errcode::ERR_SUCCESS, array(), "这个周期内您还剩" . ($intTotalCount - $intCount - 1) . "次修改主播等级的机会!");
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $intUserId
     * @param $strUserRole
     * @return bool
     */
    private static function userNeedCheckModifyCount($intUserRoleId) {
        if(Lib_Ala_Define_Role::ROLE_ADMIN != $intUserRoleId) {
            return true;
        }
        return false;
    }

    /**
     * 根据主播uid获取公会信息
     * @param $arrInput
     * @return array
     */
    public static function getUnionInfoByUserId($arrInput)
    {
        //根据主播id获取公会id
        $arrOutput = Dl_Ala_Union_Anchor::getUnionInfoByUserId($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala:setUserModifyAnchorLevelCount fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }
}
