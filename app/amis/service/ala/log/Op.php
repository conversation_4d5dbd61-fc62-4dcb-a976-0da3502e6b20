<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR> @date 2015:10:14 10:48:22
 * @version
 * @structs & methods(copied from idl.)
 */
class Service_Ala_Log_Op extends Service_Ala_Base
{

    /**
     * 返回值
     *
     * @param {String} $strContent
     *
     * @return {Array} :errno :errmsg :{Array}output
     *
     */
    public static function add($strContent)
    {
        $intUserId = Util_User::$intUserId;
        $arrOutput = Dl_Ala_Log_Op::add($intUserId, $strContent);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Log_Op::add fail. input:[" . serialize($intUserId) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 添加日志，新
     * @param {String} $strContent
     * @return {Array} :errno :errmsg :{Array}output
     */
    public static function addLog($arrParamInput)
    {
        $intUserId = intval($arrParamInput['user_id']);
        $intType = intval($arrParamInput['type']);
        $strContent = strval($arrParamInput['content']);
        $intTime = !empty($arrParamInput['create_time']) ? intval($arrParamInput['create_time']) : Bingo_Timer::getNowTime();

        $arrInput = array(
            'user_id' => $intUserId,
            'type' => $intType,
            'content' => $strContent,
            'create_time' => $intTime,
        );
        $arrOutput = Dl_Ala_Log_Op::addLog($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Log_Op::add fail. input:[" . serialize($intUserId) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 获取日志count by type
     * @param {String} $strContent
     * @return {Array} :errno :errmsg :{Array}output
     */
    public static function getLogCount($arrParamInput)
    {
        $intType = intval($arrParamInput['type']);
        $arrInput = array(
            'type' => $intType,
        );
        $arrOutput = Dl_Ala_Log_Op::getLogCount($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Log_Op::getLogCount fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrOutput['data']);
    }


    /**
     * 获取日志列表 by type
     * @param {String} $strContent
     * @return {Array} :errno :errmsg :{Array}output
     */
    public static function getLogListByType($arrParamInput)
    {
        $intType = intval($arrParamInput['type']);
        $intPage = !empty($arrParamInput['page']) ? intval($arrParamInput['page']) : 0;
        $intReqNum = !empty($arrParamInput['req_num']) ? intval($arrParamInput['req_num']) : 20;
        $arrInput = array(
            'type' => $intType,
            'page' => $intPage,
            'req_num' => $intReqNum,
        );
        $arrOutput = Dl_Ala_Log_Op::getLogListByType($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Log_Op::getLogCount fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrOutput['data']);
    }


    /**
     *
     * @param {String} $strContent
     *
     * @return {Array} :errno :errmsg :{Array}output
     *
     */
    public static function getLatestByUserId($intUserId)
    {
        $arrOutput = Dl_Ala_Log_Op::getListByUserId($intUserId, $intPage = 1, $intPageNum = 1);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Log_Op::getLatestOp fail. input:[" . serialize($intUserId) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::fatal($strLog);
            return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput['data']);
    }
}
