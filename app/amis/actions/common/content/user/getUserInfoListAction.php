<?php

class getUserInfoListAction extends  Actions_Common_Content_Util_Actioncontent
{
	/**
	 * inherit from parent and do nothing
	 * @param null
	 * @return boolean
	 */
	public function process(){
		$pn = intval(Bingo_Http_Request::get('pn', 1));
		$rn = intval(Bingo_Http_Request::get('rn', 10));
		$teamid = intval(Bingo_Http_Request::get('team_id', 0));
		$strUserName = strval(Bingo_Http_Request::get('user_name', ''));
		
		if($pn <= 0 || $rn <= 0 || $teamid <= 0){
			Bingo_Log::warning('getUserInfoListAction input param is invalid.pn:['.$pn.'] rn:['.$rn.'] team_id:['.$teamid.']');
			$this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
			return true;
		}
	
		$arrInput = array(
			'team_id' => $teamid,
			'pn' => $pn,
			'rn' => $rn,
		);
		if(!empty($strUserName)){ $arrInput['user_name'] =$strUserName;}
		$arrRes = Tieba_Service::call('common', 'contentGetUserInfoList', $arrInput, null, null, 'post', 'php', 'gbk' );
		if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
			Bingo_Log::warning("call service fail ! input = ".serialize($arrInput)." output = ".serialize($arrRes));
			$this->_jsonRet(empty($arrRes['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrRes['errno']);
			return true;
		}
		$this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,'', $arrRes['data']);
		return true;
	}

}