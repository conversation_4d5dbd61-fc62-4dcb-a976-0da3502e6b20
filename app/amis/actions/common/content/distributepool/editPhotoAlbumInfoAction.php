<?php
class editPhotoAlbumInfoAction extends Actions_Common_Content_Util_Actioncontent {
	private $intResourceId      = 0;
	private $intResourceType    = 0;
	private $intReplyNum        = 0;
	private $intStreamId        = 0;
	private $strResourceTitle   = '';
	private $strResourceAbstract= '';
	private $arrResourceCover   = array();
	private $intAuthorId        = 0;
	private $strResourceAuthor  = '';
	private $strForumName       = '';
	private $strResourceUrl     = '';
	private $intId              = 0;
	
	/**
	 * 校验非空、非0
	 * @param unknown $arrOut
	 * @return string $key
	 */
	public function process() {
        // 获取输入参数
        if (!$this->initParam()) {
            $this->_jsonRet(Tieba_Errcode::ERR_NVOTE_INVALID_PARAMS,'', Tieba_Error::getErrmsg(Tieba_Errcode::ERR_NVOTE_INVALID_PARAMS));
            return false;
        }
        // 判断标题是否满足
        if (strlen($this->strResourceTitle) < 24 || strlen($this->strResourceTitle) > 120) {
            $this->_jsonRet(Tieba_Errcode::ERR_POST_CT_TAR_NUM_NOT_MATCH, '标题字数不满足');
            return false;
        }
        $intOpUid = $this->_intUserId;
        $input = array(
            'user_id' => array(
                0 => intval($intOpUid),
            ),
        );
        $output = Tieba_Service::call('user', 'getUnameByUids', $input, null, null, 'post', 'php', 'utf-8');
        if (false == $output || Tieba_Errcode::ERR_SUCCESS !== $output['errno']) {
            Bingo_Log::warning("call service fail ! input = " . serialize($input) . " output = " . serialize($output));
        }
        $strOpUname = $output['output']['unames'][0]['user_name'];

        // 整理图集内容结构
        $intCoverNum= 0;
        $arrTemp    = array();
        $arrCover   = array();
        foreach ($this->arrResourceCover as $key => $value) {
            $arrCover[$key] = array(
                'cover'         => isset($value['cover']) ? $value['cover'] : $value['pic_url'],
                'pic_url'       => $value['pic_url'],
                'desc'          => $value['desc'],
                'is_cover'      => intval($value['is_cover']),
            );
            if (isset($value['cover_order'])) {
                $arrCover[$key]['cover_order'] = $value['cover_order'];
            }
            // 这里兼容amis,判断封面位置是否重复
            if (!empty($arrTemp) && in_array($value['cover_order'], $arrTemp)) {
                $this->_jsonRet(Tieba_Errcode::ERR_POST_CT_TAR_NUM_NOT_MATCH, '封面排序id重复');
                return false;
            }
            if ($value['is_cover'] == 1 && isset($value['cover_order'])) {
                $arrTemp[$value['cover_order']] = $value['cover_order'];
                $intCoverNum++;
            }
        }
        if ($intCoverNum != 1 && $intCoverNum != 3) {
            $this->_jsonRet(Tieba_Errcode::ERR_POST_CT_TAR_NUM_NOT_MATCH, '封面图个数不符合');
            return false;
        }

        // 整理富文本内容
        $arrContent = array();
        foreach ($arrCover as $key => &$value) {
            $picInfo = self::checkPic($value['pic_url']);
            if ($picInfo) {
                $arrContent[$key] = array(
                    'content' => $value['desc'],
                    'pic_url' => array(
                        'tag' => 'img',
                        'class' => 'BDE_Image',
                        'pic_type' => intval($picInfo['pic_type']),
                        'width' => intval($picInfo['width']),
                        'height' => intval($picInfo['height']),
                        'src' => $value['pic_url'],
                        'unselectable' => 'on',
                    )
                );
                $value['pic_width']     = intval($picInfo['width']);
                $value['pic_height']    = intval($picInfo['height']);
            } else {
                unset($arrCover[$key]);
            }
        }
        // 判断图片个数
        if (count($arrContent) < 6) {
            $this->_jsonRet(Tieba_Errcode::ERR_POST_CT_TAR_NUM_NOT_MATCH, '图片个数不够');
            return false;
        }
        $strResourceContent =self::transHtml($arrContent);

        //从代码中获取角色配置
        $arrInput = array(
            'stream_id'         => $this->intStreamId,
            'resource_id'       => $this->intResourceId,
            'resource_type'     => $this->intResourceType,
            'resource_title'    => $this->strResourceTitle,
            'resource_abstract' => $this->strResourceAbstract,
            'resource_data'     => $arrCover,
            'resource_content'  => $strResourceContent,
            'id'                => $this->intId,
            'op_uid'            => intval($intOpUid),
            'op_name'           => $strOpUname,
        );
//			Bingo_Log::warning('hanpeiyan=======setResourcePhotoAlbum' . var_export($arrInput, true));
        $arrOutput = Tieba_Service::call('common', 'setResourcePhotoAlbum', $arrInput);
//			Bingo_Log::warning('hanpeiyan=======setResourcePhotoAlbum' . var_export($arrOutput, true));
        if (false == $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
            Bingo_Log::warning("call service fail ! input = " . serialize($arrInput) . " output = " . serialize($arrOutput));
            $this->_jsonRet(empty($arrOutput['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrOutput['errno']);
            return false;
        }

        // 由于耗时严重,暂时先下掉去重逻辑
//        // 和百家号内容进行重复度校验
//        if (isset($strResourceContent)) {
//            $input = array(
//                'content' => $strResourceContent,
//            );
//            $output = Tieba_Service::call('common', 'bjhCheckRepeat', $input, null, null, 'post', null);
//            if (false == $output || Tieba_Errcode::ERR_SUCCESS !== $output['errno']) {
//                Bingo_Log::warning("call service fail ! input = " . serialize($input) . " output = " . serialize($output));
//            } else {
//                $arrOutput['data']['bjh_like_info_bottom'] = $output['data']['msg'];
//            }
//        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        // 返回结果
        $this->_jsonRet($error, Tieba_Error::getErrmsg($error), $arrOutput['data']);
	}

    /**
     * 判断图片是否满足要求
     * @param: pic url
     * @return: bool (true | false)
     **/
    private static function checkPic($strPicUrl) {
        $obj = new Bd_Pic_UrlCrypt();
        $urlInfo = pathinfo($strPicUrl);
        $picId = $obj->decode_pic_url_crypt($urlInfo['filename']);
        if ($picId <= 0) {
            Bingo_Log::warning("decode_pic_url_crypt fails! inputs: " . serialize($urlInfo['filename']) . ", outputs: " . serialize($picId));
            return false;
        }
        $picReq = array(
            'pic_id' => $picId,
        );
        $picRet = Space_Imgquery::queryPictureInfo($picReq);
        if (false === $picRet || $picRet['err_no'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("queryPictureInfo fails! inputs: " . serialize($picReq) . ", outputs: " . serialize($picRet));
            return false;
        }
        $whRate = round($picRet['width'] / $picRet['height'], 2);
        if ($picRet['width'] < 400 || $picRet['height'] < 224 || $picRet['data_len'] > 5000000 || $whRate > 2 || $whRate < 0.5) {
            return false;
        } else {
            return $picRet;
        }
    }

    /**
     * 转换为html结构
     * @param unknown $arrOut
     * @return string $key
     */
    private static function transHtml($arrContent) {
        $strContent = '';
        foreach ($arrContent as $key => $value) {
            if (!empty($value['content'])) {
                $strContent .= $value['content'];
            }
            if (!empty($value['pic_url']) && !empty($value['pic_url']['src'])) {
                $strContent .= '<img class="' . $value['pic_url']['class'] . '" pic_type="' . $value['pic_url']['pic_type'] . '" width="' . $value['pic_url']['width'] . '" height="' . $value['pic_url']['height'] . '" src="' . $value['pic_url']['src'] . '" unselectable="' . $value['pic_url']['unselectable'] . '" />';
            }
        }
        return $strContent;
    }
	
	/**
	 * 校验非空、非0
	 * @param unknown $arrOut
	 * @return string $key
	 */
	public function initParam() {
	    $this->intResourceId        = intval(Bingo_Http_Request::getNoXssSafe('resource_id', ''),0);
		$this->intResourceType      = intval(Bingo_Http_Request::getNoXssSafe('resource_type', ''),0);
		$this->intReplyNum          = intval(Bingo_Http_Request::getNoXssSafe('reply_num', ''),0);
		$this->intStreamId          = intval(Bingo_Http_Request::getNoXssSafe('stream_id', ''),0);
		$this->strResourceTitle     = Bingo_Http_Request::getNoXssSafe('resource_title', '');
		$this->strResourceAbstract  = Bingo_Http_Request::getNoXssSafe('resource_abstract', '');
		$this->arrResourceCover     = Bingo_Http_Request::getNoXssSafe('resource_data', '');
		$this->intAuthorId          = intval(Bingo_Http_Request::getNoXssSafe('resource_authorid', ''),0);
		$this->strResourceAuthor    = Bingo_Http_Request::getNoXssSafe('resource_author', '');
		$this->intId                = intval(Bingo_Http_Request::getNoXssSafe('id', ''),0);
		$this->strForumName         = Bingo_Http_Request::get('resource_forumname', '');
		$this->strResourceUrl       = Bingo_Http_Request::getNoXssSafe('resource_url', '');
		if(empty($this->intResourceId) || empty($this->intId)){
			return false;
		}
		return true;
	}
}

