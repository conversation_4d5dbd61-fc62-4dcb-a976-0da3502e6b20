<?php
/**
 * Created by PhpStorm.
 * User: lilinke
 * Date: 2019/2/18
 * Time: 下午5:51
 */
class getResourceThreadDetailAction extends Actions_Common_Content_Util_Actioncontent{
    private $_intStartId  = 1574296;
    private $_intStreamId = -1;
    private $_intThreadId = -1;
    private $_pn = 1;
    private $_rn = 10;
    private $_arrOutputRows = array();
    private $_intOutputCnt = 0;

    /**
     * 校验非空、非0
     * @param unknown $arrOut
     * @return string $key
     */
    private static function _checkOutput($arrOut, $key = 'errno'){
        if(null == $arrOut || empty($arrOut) || !isset($arrOut[$key]) || Tieba_Errcode::ERR_SUCCESS != $arrOut[$key]){
            return false;
        }
        return true;
    }


    /**
     * [_execute description]
     * @return [type] [description]
     */
    public function process()
    {
        if (!$this->_input()) {
            return false;
        }
        if($this->_getResourceThreadDetailList() === false){
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL,'', array());
            return false;
        }
        $this->_build();
        return true;
    }
    /**
     * [_input description]
     * @return [type] [description]
     */
    private function _input(){
        $this->_intStreamId = intval(Bingo_Http_Request::get('stream_id',0));
        $this->_intThreadId = intval(Bingo_Http_Request::get('thread_id',''));
        $this->_pn = intval(Bingo_Http_Request::get('pn', 1));
        $this->_rn = intval(Bingo_Http_Request::get('rn', 10));
        if($this->_intStreamId <= 0 || $this->_intThreadId <= 0 ){
            Bingo_Log::warning('param error!!! stream_id['.$this->_intStreamId.'] threadid['.$this->_intThreadId.']');
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        return true;
    }

    /**
     * @return bool
     */
    private function _getResourceThreadDetailList(){
        $arrInput = array(
            'stream_id' => $this->_intStreamId,
            'thread_id'  => $this->_intThreadId,
            'pn' => $this->_pn,
            'rn' => $this->_rn,
            'need_comment' => 1 ,
        );
        $arrRet = Tieba_Service::call('common','getResourceThreadDetailByconf',$arrInput);
        if(!self::_checkOutput($arrRet)){
            Bingo_Log::warning('call service[getResourceThreadDetailByconf] fail!! input['.serialize($arrInput).'] out ['.serialize($arrRet).']');
            return false;
        }
        foreach ($arrRet['data']['rows'] as $item){
            $arrThreadCommentList = $item['resource_comment'];
            foreach ($arrThreadCommentList as $arrComment) {
                $this->_arrOutputRows [] = array(
                    'resource_id' => $item['resource_id'],
                    'resource_type' => $item['resource_type'],
                    'stream_id' => $item['stream_id'],
                    'resource_title' => $item['resource_title'],
                    'resource_content' => $item['resource_content'],
                    'thread_id' => $item['thread_id'],
                    'post_id' => $arrComment['post_id'],
                    'post_content' => $arrComment['post_content'],
                    'post_status' => $arrComment['status'],//回复状态

                );
            }
        }
        return true;
    }

    /**
     * 组装
     * @return [type] [description]
     */
    private function _build()
    {
        $data['count'] = count($this->_arrOutputRows);
        $data['rows'] = $this->_arrOutputRows;
        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,'', $data);
    }
}