<?php

class updataQualityScoreAction extends Actions_Common_Content_Util_Actioncontent{
	/**
	 * inherit from parent and do nothing
	 * @param null
	 * @return boolean
	 */
	public function process(){
		$intResourceId = intval(Bingo_Http_Request::get('resource_id', ''),0);
		$intResourceType = intval(Bingo_Http_Request::get('resource_type', ''),0);
		$intPointRate  = intval(Bingo_Http_Request::get('point_rate', ''),0);
		$intStreamId = intval(Bingo_Http_Request::get('stream_id', ''),0);
		$intId = intval(Bingo_Http_Request::get('id', ''),0);
		$intThreadId = intval(Bingo_Http_Request::get('thread_id', ''),0);

		$intOpUid = $this->_intUserId;
		$strOpUname = $this->_strUserName;
		if($intId == 0){
            $arrInput = array('resource_id'=>$intResourceId,'resource_type'=>$intResourceType,'stream_id'=>$intStreamId);
            $arrRes = Tieba_Service::call('common', 'getResourceByResourceId',$arrInput, null, null, 'post',null);
            $intId = intval($arrRes['data'][0]['id']);
            if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
                Bingo_Log::warning("call service [getResourceByResourceId] fail ! input = ".serialize($arrInput)." output = ".serialize($arrRes));
                $this->_jsonRet(empty($arrRes['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrRes['errno']);
                return true;
            }
        }
        if(empty($intId) || empty($intThreadId)){
            Bingo_Log::warning('updataQualityScoreAction input param is invalid.threadid:['.$intThreadId.'] and id:['.$intId.']');
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return true;
        }

        //更新权值分数
        $arrInput = array(
            'condition'=>array(
                'thread_id'=>$intThreadId,
            ),
            'update_fields' =>array(
                'quality_score'=> $intPointRate,
            ),
        );
        $arrRes = Tieba_Service::call('common', 'editSwanResource', $arrInput, null, null, 'post', null);
        if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning("call service fail ! input = ".serialize($arrInput)." output = ".serialize($arrRes));
            $this->_jsonRet(empty($arrRes['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrRes['errno']);
            return true;
        }
        //更新状态
        $arrInput = array(
            'id'=>$intId,
            'quality_score'=>$intPointRate,
            'op_uid' =>$intOpUid,
            'op_name' =>$strOpUname,
        );
        $arrRes = Tieba_Service::call('common', 'changeResourceQualityScore', $arrInput, null, null, 'post', null);
        if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning("call service fail ! input = ".serialize($arrInput)." output = ".serialize($arrRes));
            $this->_jsonRet(empty($arrRes['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrRes['errno']);
            return true;
        }

		$this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,'', $arrRes['data']);
		return true;
	}
}