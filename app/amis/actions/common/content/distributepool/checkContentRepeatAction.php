<?php

class checkContentRepeatAction extends Actions_Common_Content_Util_Actioncontent{
	/**
	 * inherit from parent and do nothing
	 * @param null
	 * @return boolean
	 */
	public function process(){
		$strResourceContent = Bingo_Http_Request::getNoXssSafe('resource_content', '');
		$arrRet = array();

		// 和百家号内容进行重复度校验
		if (!empty($strResourceContent)) {
			$input = array(
				'content' => $strResourceContent,
			);
			$output = Tieba_Service::call('common', 'bjhCheckRepeat', $input, null, null, 'post', null);
			if (false == $output || Tieba_Errcode::ERR_SUCCESS !== $output['errno']) {
				Bingo_Log::warning("call service fail ! input = ".serialize($input)." output = ".serialize($output));
			} else {
				$arrRet['bjh_like_info_top'] = $output['data']['msg'];
			}
		}

		$this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,'', $arrRet);
		return true;
	}
}
