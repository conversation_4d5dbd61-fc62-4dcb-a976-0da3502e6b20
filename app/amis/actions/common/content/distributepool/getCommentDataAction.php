<?php
/**
 * Created by PhpStorm.
 * User: lilinke
 * Date: 2019/3/26
 * Time: 下午4:36
 */
class getCommentDataAction extends Actions_Common_Content_Util_Actioncontent{
    private $_arrResourceComment = array();
    private $_intPostNo          = -1;
    private $_intThreadId        = -1;
    /**
     * inherit from parent and do nothing
     * @param null
     * @return boolean
     */
    public function process(){
        if(!$this->_init()){
            return false;
        }
        $arrTargetComment = $this->_getTiebaCommentByPostNo();
        if($arrTargetComment == false || empty($arrTargetComment)){
            return false;
        }
        $this->_arrResourceComment[] = $arrTargetComment;
        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,'', array('resource_comment' => $this->_arrResourceComment));
        return true;
    }

    /**
     * @return bool
     */
    private function _init(){
        $arrResourceCommentFromAmis  = Bingo_Http_Request::getNoXssSafe('resource_comment', array());
        $intPostNo = intval(Bingo_Http_Request::get('post_no'));
        $intThreadId = intval(Bingo_Http_Request::get('thread_id'));
        if($intThreadId <= 0){
            Bingo_Log::warning('thread_id error!!!  tid['.$intThreadId.']');
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR,'帖子id有误');
            return false;
        }
        $this->_intThreadId = $intThreadId;
        if(!is_numeric($intPostNo) || $intPostNo <= 0){
            Bingo_Log::warning('post_no error!!!post_no['.serialize($intPostNo).']');
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR,'请输入正确的楼层号');
            return false;
        }
        $this->_intPostNo = $intPostNo;
        foreach ($arrResourceCommentFromAmis as $arrCommentInfo) {
            if(intval($arrCommentInfo['has_post_content']) == 1 || count($arrCommentInfo) > 2) {
                $this->_arrResourceComment[] = $arrCommentInfo;
            }
        }
        return true;
    }
    /**
     * @return array|bool
     */
    private function _getTiebaCommentByPostNo(){
        $arrCommentData = array();
        $intStepSize = 90;
        for($intOffset = 0; $intOffset <= $this->_intPostNo; $intOffset+= $intStepSize){
            $input = array(
                "thread_id" => $this->_intThreadId, //帖子id
                "offset" => $intOffset,
                "res_num" => $intStepSize,
                "see_author" => 0,
                "has_comment" => 0,
                "has_mask" => 0,
                "has_ext" => 1,
                "need_set_pv" => 0,
                "structured_content" => 0
            );
            $res   = Tieba_Service::call('post', 'getPostsByThreadId', $input, null, null, 'post', 'php', 'utf-8');
            if($res == false || $res['errno'] != Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning('call service[getPostsByThreadId] faile!!! input['.serialize($input).'] output['.serialize($res).']');
                $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL,'获取回复数据失败,请重试');
                return false;
            }
            $arrPostInfos = $res['output']['output'][0]['post_infos'];
            foreach ($arrPostInfos as $arrPostInfo){
                $intPostNo = $arrPostInfo['post_no'];
                if($intPostNo != $this->_intPostNo){
                    continue;
                }
                $arrCommentData['author_id'] = $arrPostInfo['user_id'];
                $arrCommentData['author_name'] = $arrPostInfo['username'];
                $arrCommentData['has_post_content'] = 0;
                $arrCommentData['id'] = count($this->_arrResourceComment) + 1;
                $arrCommentData['post_content'] = strval($arrPostInfo['content']);
                $arrCommentData['post_id'] = $arrPostInfo['post_id'];
                $arrCommentData['post_type'] = 1;
                $arrCommentData['status'] = false;
                break;
            }

        }
        if(empty($arrCommentData)){
            Bingo_Log::warning('the '.$this->_intPostNo.' floor comment not find');
            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,'comment not find');
            return false;
        }
        return $arrCommentData;
    }
}