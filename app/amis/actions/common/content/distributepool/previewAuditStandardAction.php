<?php
/**
 * Created by PhpStorm.
 * User: lilinke
 * Date: 2019/2/21
 * Time: 下午3:58
 */

class previewAuditStandardAction extends Actions_Common_Content_Util_Actioncontent{
    private $_intTeamId = -1;
    private $_arrOutput = array();
    private $_retMsg = '';
    private $_bolShowSaveButton = false;
    const DEAULT_AUDIT_STANDARD = '您所在团队的审核标准还未添加！！';
    const AUDIT_STANDARD_REDIS_PREFIX_KEY = 'pugongying_auidt_standard_key';


    /**
     * 校验非空、非0
     * @param unknown $arrOut
     * @return string $key
     */
    private static function _checkOutput($arrOut, $key = 'errno'){
        if(null == $arrOut || empty($arrOut) || !isset($arrOut[$key]) || Tieba_Errcode::ERR_SUCCESS != $arrOut[$key]){
            return false;
        }
        return true;
    }


    /**
     * [_execute description]
     * @return [type] [description]
     */
    public function process()
    {
        if (!$this->_input()) {
            return false;
        }
        if($this->getAuditStandard() === false){
            $this->_jsonRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL,'获取审核信息失败');
            return false;
        }
        $this->_build();
        return true;
    }
    /**
     * [_input description]
     * @return [type] [description]
     */
    private function _input(){
        include_once APP_PATH . "app/common/service/content/base/Role.php";
        if($this->_intUserId <= 0 || empty($this->_strUserName)){
            $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN,'', array());
            return false;
        }
        $this->_intTeamId = intval(Bingo_Http_Request::get('team_id',-1));
        if($this->_intTeamId <= 0){
            Bingo_Log::warning('param error!!! team_id['.$this->_intTeamId.']');
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        $this->_intRoleId = $this->_getUserRoleId($this->_intTeamId,$this->_strUserName);
        if($this->_intRoleId === false){
            Bingo_Log::warning('get user info fail role_id['.$this->_intRoleId.']');
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR,'获取用户信息失败');
            return false;
        }
        if(Service_Content_Base_Role::ROLE_MANAGER == $this->_intRoleId || Service_Content_Base_Role::ROLE_PM == $this->_intRoleId){
            $this->_bolShowSaveButton = true;
        }
        return true;
    }

    /**
     * @return bool
     */
    private function getAuditStandard(){
        //队首为最新版
        $strRedisKey = self::AUDIT_STANDARD_REDIS_PREFIX_KEY.'_'.$this->_intTeamId;
        $arrOpRedisInput = array(
            'function'      => 'callGrabRedis',
            'call_method'   => 'LRANGE',
            'param' => array(
                'key'   => $strRedisKey,
                'start' => 0,
                'stop'   => 0,
            ),
        );
        $arrOut = Tieba_Service::call('common', 'grabResource', $arrOpRedisInput);
        if (!self::_checkOutput($arrOut) || !self::_checkOutput($arrOut['data'],'err_no')) {
            Bingo_Log::warning('call redis failed!!!  input['.serialize($arrOpRedisInput).']'.'out['.serialize($arrOut).']');
            return false;
        }
        $strAuditStandardDocument = strval($arrOut['data']['ret'][$strRedisKey][0]);
        $this->_arrOutput =array(
            'audit_standard_document'   => empty($strAuditStandardDocument) ? self::DEAULT_AUDIT_STANDARD : $strAuditStandardDocument,
            'show_save_button'          => $this->_bolShowSaveButton,
        );
        return true;
    }

    /**
     * 组装
     * @return [type] [description]
     */
    private function _build()
    {
        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,$this->_retMsg,$this->_arrOutput);
    }
}