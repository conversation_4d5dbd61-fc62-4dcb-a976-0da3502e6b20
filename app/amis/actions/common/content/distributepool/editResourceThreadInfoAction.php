<?php

class editResourceThreadInfoAction extends Actions_Common_Content_Util_Actioncontent{
	/**
	 * inherit from parent and do nothing
	 * @param null
	 * @return boolean
	 */
	public function process(){
		$intResourceId = intval(Bingo_Http_Request::get('resource_id', ''),0);
		$intResourceType = intval(Bingo_Http_Request::get('resource_type', ''),0);
		$intReplyNum = intval(Bingo_Http_Request::get('reply_num', 0));
		$intStreamId = intval(Bingo_Http_Request::get('stream_id', ''),0);
		$strResourceTitle = Bingo_Http_Request::getNoXssSafe('resource_title', '');
		$strResourceAbstract = Bingo_Http_Request::getNoXssSafe('resource_abstract', '');
		$strResourceCover  = Bingo_Http_Request::get('resource_cover', '');
		$strResourceContent  = Bingo_Http_Request::getNoXssSafe('resource_content', '');
		$strResourceVideoContent  = Bingo_Http_Request::getNoXssSafe('resource_video_content', '');
		$intAuthorId  = intval(Bingo_Http_Request::get('resource_authorid', ''),0);
		$strResourceAuthor  = Bingo_Http_Request::get('resource_author', '');
		$strForumName  = Bingo_Http_Request::get('resource_forumname', '');
		$intId = intval(Bingo_Http_Request::get('id', ''),0);
		$intOpUid = $this->_intUserId;
		$strOpUname = $this->_strUserName;
        $intTeamId = intval(Bingo_Http_Request::get('team_id', 0));
        $arrResourceComment = Bingo_Http_Request::getNoXssSafe('resource_comment', array());//评论

        // 数据库是根据分类的父子关系进行存储,所以这里只需要存储最底层的分类id即可（理想情况下存第三级分类）
        $intCategoryId  = Bingo_Http_Request::get('category_id', 0);
        $intCategoryId  = Bingo_Http_Request::get('category_first_id', $intCategoryId);
        $intCategoryId  = Bingo_Http_Request::get('category_second_id', $intCategoryId);
        $intCategoryId  = Bingo_Http_Request::get('category_third_id', $intCategoryId);
        $strResourceTagList  = Bingo_Http_Request::get('resource_tag_list', '');
        $intQualitySore = Bingo_Http_Request::get('quality_score', 0);
        $intThreadId = Bingo_Http_Request::get('thread_id', '');
        $intThreadType = Bingo_Http_Request::get('thread_type', '');
        $strResourceUrl=Bingo_Http_Request::get('resource_url', '');

        Bingo_Log::warning('--row arrComment------'.serialize($arrResourceComment));
		try {
			$strResourceContent =Tieba_Text::ubb2html($strResourceContent);
            foreach($arrResourceComment as $key => $arrComment){
                $arrResourceComment[$key]['post_content'] =Tieba_Text::ubb2html($arrComment['post_content']);
            }
		}catch (Exception $e){
			$this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, 'ubb content parser fail.code:['.$e->getCode().'] errmsg:['.$e->getMessage().']',array());
			return true;
		}
        Bingo_Log::warning('--transform ubb arrComment------'.serialize($arrResourceComment));
		//$intOpUid=10011333;
		//$strOpUname='sdsdds';
		if(empty($intResourceId) ||empty($intId)){
			Bingo_Log::warning('previewResourceAction input param is invalid.resource_id:['.$intResourceId.'] resource_type:['.$intResourceType.'] id:['.$intId.']');
			$this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
			return true;
		}
		//从代码中获取角色配置
		$arrInput = array(
			'stream_id' => $intStreamId,
			'resource_id' => $intResourceId,
			'resource_type' => $intResourceType,
			'resource_title' => $strResourceTitle,
			'resource_abstract'=> $strResourceAbstract,
			'resource_cover'=> $strResourceCover,
			'resource_content'=> $strResourceContent,
			'reply_num'=> $intReplyNum,
			'resource_url'=> $strResourceUrl,
			'id'=> $intId,
			'op_uid' => intval($intOpUid),
			'op_name' => $strOpUname,
			'resource_authorid' => $intAuthorId,
			'resource_author' => $strResourceAuthor,
			'resource_forumname' => $strForumName,
            'category_id' => intval($intCategoryId),
            'resource_tag_list' => $strResourceTagList,
            'quality_score' => intval($intQualitySore),
            'thread_id' => intval($intThreadId),
            'thread_type' => intval($intThreadType),
			'resource_video_content' => strval($strResourceVideoContent),
		);
		if(is_array($arrResourceComment)&&!empty($arrResourceComment)){
		    foreach ($arrResourceComment as $arrComment){
                $arrInput['resource_comment'][] = array(
                    'parent_post_id' => 0,
                    'post_level' => 1,
                    'has_children' => 0,
                    'post_id' => $arrComment['post_id'],
                    'post_type' => $arrComment['post_type'],
                    'post_content' => $arrComment['post_content'],
                    'status' => (($arrComment['status'] === 'true' || $arrComment['status'] === 1 || $arrComment['status'] == '1' ) ? 1 : 0),
                    'author_id' => $arrComment['author_id'],
                    'author_name' => $arrComment['author_name'],
                );
            }
        }

        $arrRes = false;
        if($intThreadId !=$intResourceId){
            //更新回复
            if(!$this->updateCommentInfo($arrInput)){
                return false;
            }
            //更新主贴
            $arrRes = $this->updateResoureInfo($arrInput);
        }else{
            $arrInput['ret_data'] = 1;//addResourceThreadDetail 请求返回新生成的resource_id
            $arrRes = $this->addResoureAndCommentInfo($arrInput);
        }
		if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
			Bingo_Log::warning("call service fail ! input = ".serialize($arrInput)." output = ".serialize($arrRes));
			$this->_jsonRet(empty($arrRes['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrRes['errno']);
			return true;
		}
		$intNewResourceId = $intResourceId;
		$intNewResourceType = $intResourceType;
		//更新user_op_record中的old_resouce_id
        if(isset($arrInput['ret_data']) && intval($arrInput['ret_data']) == 1
            &&!empty($arrRes['data']) && intval($arrRes['data']['resource_id']) > 0) {
            $intNewResourceId = intval($arrRes['data']['resource_id']);
            $intNewResourceType =intval($arrRes['data']['resource_type']);
		    $arrUpdateFields = array('resource_id' => $intNewResourceId,'resource_type'=>$intNewResourceType);
		    $arrCondition = array('stream_id' => $intStreamId,'resource_id'=>$intResourceId,'resource_type' => $intResourceType);
            $arrInput = array('update_fields' => $arrUpdateFields, 'condition' => $arrCondition);
            $arrRes = Tieba_Service::call('common', 'updateUserOperation', $arrInput, null, null, 'post', 'php', 'gbk' );
            if($arrRes == false || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
                Bingo_Log::warning("call service[updateUserOperation] fail ! input = ".serialize($arrInput)." output = ".serialize($arrRes));
                $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                return true;
            }
        }
        $arrInput = array(
            'team_id' => $intTeamId,
            'op_uid' => $intOpUid,
            'op_uname'=> $strOpUname,
            'op_type' => 4,//编辑操作
            'stream_id' => $intStreamId,
            'resource_id' => $intNewResourceId,
            'resource_type' => $intNewResourceType,
        );
        $arrRes = Tieba_Service::call('common', 'addUserOperation', $arrInput, null, null, 'post', 'php', 'gbk' );
        if($arrRes == false || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call service[addUserOperation] fail! input['.serialize($arrInput).'] ouput['.serialize($arrRes).']');
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return true;
        }
		// 由于耗时严重,暂时先下掉去重逻辑
//		// 和百家号内容进行重复度校验
//		if (isset($strResourceContent)) {
//			$input = array(
//				'content' => $strResourceContent,
//			);
//			$output = Tieba_Service::call('common', 'bjhCheckRepeat', $input, null, null, 'post', null);
//			if (false == $output || Tieba_Errcode::ERR_SUCCESS !== $output['errno']) {
//				Bingo_Log::warning("call service fail ! input = ".serialize($input)." output = ".serialize($output));
//			} else {
//				$arrRes['data']['bjh_like_info_bottom'] = $output['data']['msg'];
//			}
//		}
		
		$this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,'', $arrRes['data']);
		return true;
	}

    /**
     * 添加内容和回复
     * @param $arrAllInput 入参中有resource_comment数据
     * @return bool|mixed|multitype
     */
	private function addResoureAndCommentInfo($arrResourceAndCommentInput){
        Bingo_Log::warning('wangxbd=======addResoureAndCommentInfo'.var_export($arrResourceAndCommentInput, true));
        $arrRes = Tieba_Service::call('common', 'addResourceThreadDetail', $arrResourceAndCommentInput);
        Bingo_Log::warning('wangxbd=======addResoureAndCommentInfo'.var_export($arrRes, true));
        return $arrRes;
    }

    /**
     * 更新主贴
     * @param $arrResourceInput
     * @return bool|mixed|multitype
     */
    private function updateResoureInfo($arrResourceInput){
	    unset($arrResourceInput['resource_comment']);
        Bingo_Log::warning('wangxbd=======updateResoureInfo'.var_export($arrResourceInput, true));
        $arrRes = Tieba_Service::call('common', 'updateResourceThreadDetail', $arrResourceInput);
        Bingo_Log::warning('wangxbd=======updateResoureInfo'.var_export($arrRes, true));
        return $arrRes;
    }

    /**
     * 更新回复
     * @param $arrResourceAndCommentInput
     * @return bool
     */
    private function updateCommentInfo($arrResourceAndCommentInput){
        $intMainPostId = $arrResourceAndCommentInput['resource_id'];
        $intOpUid = $arrResourceAndCommentInput['op_uid'];
        $strOpUname = $arrResourceAndCommentInput['op_name'];
        $arrCommentInfos = $arrResourceAndCommentInput['resource_comment'];
        foreach ($arrCommentInfos as $arrCommentInfo){
            $arrUpdateCommentInput = array(
                'update_fields' => array(
                    'status'       => $arrCommentInfo['status'],
                    'post_content' => $arrCommentInfo['post_content'],
                    'op_uid'       => $intOpUid,
                    'op_name'      => $strOpUname,
                ),
                'condition' => array(
                    'main_post_id' => $intMainPostId,
                    'post_id'      => $arrCommentInfo['post_id'],
                ),
            );
            $arrRes = Tieba_Service::call('common', 'updateEditedResourceComment', $arrUpdateCommentInput);
            if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
                Bingo_Log::warning("call service fail ! input = ".serialize($arrUpdateCommentInput)." output = ".serialize($arrRes));
                $this->_jsonRet(empty($arrRes['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrRes['errno'],'save comment failed!!main_post_id['.$intMainPostId.'] post_id['.$arrCommentInfo['post_id'].']');
                return false;
            }
        }
        return true;
    }
}