<?php
/**
 * Created by PhpStorm.
 * User: lilinke
 * Date: 2019/2/18
 * Time: 下午8:40
 */
class updateGodPostStatusAction extends Actions_Common_Content_Util_Actioncontent{
    private $_arrInput = array(
        'resource_id'   => -1,
        'resource_type' => -1,
        'thread_id'     => -1,
        'stream_id'     => -1,
        'source_id'     => -1,
        'post_id'       => -1,
        'team_id'       => -1,
    );
    private $_arrOutputRows = array();
    private $_intOutputCnt = 0;
    private $_retMsg = '';

    /**
     * 校验非空、非0
     * @param unknown $arrOut
     * @return string $key
     */
    private static function _checkOutput($arrOut, $key = 'errno'){
        if(null == $arrOut || empty($arrOut) || !isset($arrOut[$key]) || Tieba_Errcode::ERR_SUCCESS != $arrOut[$key]){
            return false;
        }
        return true;
    }


    /**
     * [_execute description]
     * @return [type] [description]
     */
    public function process()
    {
        if (!$this->_input()) {
            return false;
        }
        if(!$this->_changeGodPostStatus($this->_arrInput['resource_id'],$this->_arrInput['thread_id'],$this->_arrInput['post_id'],$this->_arrInput['new_status'])){
            return false;
        }
        $this->_build();
        return true;
    }
    /**
     * [_input description]
     * @return [type] [description]
     */
    private function _input(){
        if($this->_intUserId <= 0 || empty($this->_strUserName)){
            $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN,'', array());
            return false;
        }
        $intResourceId = intval(Bingo_Http_Request::get('resource_id',-1));
        $intResourceType = intval(Bingo_Http_Request::get('resource_type',-1));
        $intStreamId = intval(Bingo_Http_Request::get('stream_id',-1));
        $intThreadId = intval(Bingo_Http_Request::get('thread_id',-1));
        $intPostId = intval(Bingo_Http_Request::get('post_id',-1));
        $intNewStatus = intval(Bingo_Http_Request::get('post_status',-1));
        $intTeamId = intval(Bingo_Http_Request::get('team_id',-1));
        if($intStreamId <= 0 ||$intResourceId <= 0 || $intResourceType <= 0 || $intThreadId <= 0  || $intPostId <= 0 || ($intNewStatus != 1 && $intNewStatus != 0) || $intTeamId <= 0){
            Bingo_Log::warning('param error!!! stream_id['.$intStreamId.'] resource_id['.$intResourceId.'] resource_type['.$intResourceType.'] threadid['.$intThreadId.'] postid['.$intPostId.'] new_status['.$intNewStatus.'] team_id['.$intTeamId.']');
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        //校验 thread_id post_id
        $arrPostInfoList = self::_getPostInfo(array($intPostId));
        if($arrPostInfoList[$intPostId]['thread_id'] != $intThreadId){
            Bingo_Log::warning('param error!!! stream_id['.$intStreamId.'] threadid['.$intThreadId.'] postid['.$intPostId.'] new_status['.$intNewStatus.']');
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR,'回复不属于帖子',array());
            return false;
        }
        $this->_arrInput = array(
            'resource_id'   => $intResourceId,
            'resource_type' => $intResourceType,
            'stream_id'     => $intStreamId,
            'thread_id'     => $intThreadId,
            'post_id'       => $intPostId,
            'new_status'    =>  $intNewStatus,
            'team_id'       => $intTeamId,
        );
        return true;
    }

    /**
     * @param $intResourceId
     * @param $intThreadId
     * @param $intPostId
     * @param $intNewStatus
     * @return bool
     */
    private function _changeGodPostStatus($intResourceId,$intThreadId,$intPostId,$intNewStatus){
        //下线线上神回复
        $arrDeleteGodPostInput = array(
            'thread_id' => $intThreadId,
            'post_id' => $intPostId,
        );
        $arrRet = Tieba_Service::call('agree','updateRecommendPostToDelete',$arrDeleteGodPostInput);
        if(!self::_checkOutput($arrRet)){
            Bingo_Log::warning('call service[updateRecommendPostToDelete] failed!! input['.serialize($arrDeleteGodPostInput).'] out['.serialize($arrRet).']');
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL,'下线神回复失败',array());
            return false;
        }
        //修改状态
        $arrUpdateToInvalidDlReq = array(
            'main_post_id' => $intResourceId,
            'post_ids' => array($intPostId),
            'status' => $intNewStatus,
            'op_uid' => $this->_intUserId,
            'op_name' => $this->_strUserName,
        );
        $arrRet = Tieba_Service::call('common','updateEditedResourceCommentStatus',$arrUpdateToInvalidDlReq);
        if(!self::_checkOutput($arrRet)){
            Bingo_Log::warning('call service[updateEditedResourceCommentStatus] failed!! input['.serialize($arrUpdateToInvalidDlReq).'] out['.serialize($arrRet).']');
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL,'修改神回复状态',array());
            return false;
        }
        $this->_retMsg = '修改['.$arrRet['data']['affect_count'].']条数据';
        //紧急下线操作打点
        $arrInput = array(
            'team_id' => $this->_arrInput['team_id'],
            'op_uid' => $this->_intUserId,
            'op_uname'=> $this->_strUserName,
            'op_type' => 5,//1-一审 2-二审 3-不通过 4-编辑 5-紧急下线操作
            'stream_id' => $this->_arrInput['stream_id'],
            'resource_id' => $this->_arrInput['resource_id'],
            'resource_type' => $this->_arrInput['resource_type'],
        );
        $arrRes = Tieba_Service::call('common', 'addUserOperation', $arrInput, null, null, 'post', 'php', 'gbk' );
        if($arrRes == false || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call service[addUserOperation] fail! input['.serialize($arrInput).'] ouput['.serialize($arrRes).']');
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL,'记录紧急下线操作失败');
            return false;
        }
        return true;
    }
    /**
     * @param $arrPidList
     * @return array|bool
     */
    private static function _getPostInfo($arrPidList){
        $arrPostInfoLsit = array();
        $input = array(
            "post_ids" => $arrPidList,
        );
        $res  = Tieba_Service::call('post', 'getPostInfo', $input, null, null, 'post', 'php', 'utf-8');
        if(false == $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS){
            outLogLine('call service[getPostInfo] fail!! input['.serialize($input).'] out['.serialize($res).']');
            return false;
        }
        foreach ($res['output'] as $arrPostInfo){
            $arrPostInfoLsit[$arrPostInfo['post_id']] = array(
                'thread_id' => $arrPostInfo['thread_id'],
                'post_id' => $arrPostInfo['post_id'],
                'post_type' => 0,
                'content' => $arrPostInfo['content'],
                'title' => $arrPostInfo['title'],
                'username' => $arrPostInfo['username'],
                'user_id' => $arrPostInfo['user_id'],
            );
        }
        return $arrPostInfoLsit;
    }

    /**
     * 组装
     * @return [type] [description]
     */
    private function _build()
    {
//        $data['count'] = $this->_intOutputCnt;
//        $data['rows'] = $this->_arrOutputRows;
        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,$this->_retMsg);
    }
}