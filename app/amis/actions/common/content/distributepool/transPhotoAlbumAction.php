<?php
class transPhotoAlbumAction extends Actions_Common_Content_Util_Actioncontent {
	private $intResourceId      = 0;
	private $intResourceType    = 0;
    private $intStreamId        = 0;
    private $intThreadId        = 0;
	private $intReplyNum        = 0;
    private $intId              = 0;
    private $intOpUid           = 0;
    private $strOpUname         = '';

    // 图集使用该固定的resource_type=1999
    const PHOTO_ALBUM_RESOURCE_TYPE = 1999;
	
	/**
	 * 校验非空、非0
	 * @param unknown $arrOut
	 * @return string $key
	 */
	public function process() {
        // 获取输入参数
        if (!$this->initParam()) {
            $this->_jsonRet(Tieba_Errcode::ERR_NVOTE_INVALID_PARAMS,'', Tieba_Error::getErrmsg(Tieba_Errcode::ERR_NVOTE_INVALID_PARAMS));
            return false;
        }
        if(empty($this->intReplyNum)){
            $this->intReplyNum = 1;
        }
        $this->intReplyNum = 100;

        // 获取数据
        $getInput = array(
            'resource_id'	=> $this->intResourceId,
            'resource_type'	=> $this->intResourceType,
            'reply_num'		=> $this->intReplyNum,
            'content_type'  => 1,
            'structured_content'    => 1,
        );
        $getOutput = Tieba_Service::call('common', 'getResourceThreadDetail', $getInput, null, null, 'post', null);
        if (false == $getOutput || Tieba_Errcode::ERR_SUCCESS !== $getOutput['errno']) {
            Bingo_Log::warning("call service getResourceThreadDetail fail ! input = " . serialize($getInput) . " output = " . serialize($getOutput));
            $this->_jsonRet(empty($getOutput['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $getOutput['errno']);
            return false;
        }

        // 转换图集
        $arrContent = self::checkContent($getOutput['data']);
        if ($arrContent['errno'] == Tieba_Errcode::ERR_POST_CT_TAR_NUM_NOT_MATCH) {
            $this->_jsonRet($arrContent['errno'], $arrContent['errmsg']);
            return false;
        }

        // 整理图集数据结构
        $arrCover = array();
        $index = 0;
        foreach ($arrContent['data'] as $key => $value) {
            $arrCover[$index] = array(
                'cover'         => $value['pic_url']['src'],
                'pic_url'       => $value['pic_url']['src'],
                'pic_width'     => $value['pic_url']['width'],
                'pic_height'    => $value['pic_url']['height'],
                'desc'          => $value['content'],
                'is_cover'      => 0,
            );
            if ($index == 0) {
                $arrCover[$index]['is_cover'] = 1;
                $arrCover[$index]['cover_order'] = 0;
            }
            $index++;
        }

        // 整理富文本内容
        $strRecourceContent = self::transHtml($arrContent['data']);

        // 添加到图集表中
        $addInput = array(
            'reply_num'         => $this->intReplyNum,
            'resource_id'       => $this->intResourceId,
            'resource_type'     => self::PHOTO_ALBUM_RESOURCE_TYPE,
            'resource_data'     => $arrCover,
            'resource_content'  => $strRecourceContent,
            'stream_id'         => $this->intStreamId,
            'resource_title'    => $getOutput['data']['resource_title'],
            'resource_abstract' => $getOutput['data']['resource_abstract'],
            'resource_url'      => $getOutput['data']['resource_url'],
            'resource_authorid' => $getOutput['data']['resource_authorid'],
            'resource_author'   => $getOutput['data']['resource_author'],
            'resource_forumname'=> $getOutput['data']['resource_forumname'],
            'resource_threadid' => $this->intThreadId,
            'create_uid'        => $this->intOpUid,
            'create_name'       => $this->strOpUname,
        );
        Bingo_Log::warning("---hanpeiyan---" . var_export($addInput, true));
        $addOutput = Tieba_Service::call('common', 'addResourcePhotoAlbum', $addInput, null, null, 'post', null);
        if (false == $addOutput || Tieba_Errcode::ERR_SUCCESS !== $addOutput['errno']) {
            Bingo_Log::warning("call service addResourcePhotoAlbum fail ! input = " . serialize($addInput) . " output = " . serialize($addOutput));
            $this->_jsonRet(empty($addOutput['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $addOutput['errno']);
            return true;
        }

        // 修改审核表中的类型标记
        $setInput = array(
            'id'            => $this->intId,
            'resource_type' => self::PHOTO_ALBUM_RESOURCE_TYPE,
            'content_type'  => 1,
        );
        $setOutput = Tieba_Service::call('common', 'changeContentType', $setInput, null, null, 'post', null);
        if (false == $setOutput || Tieba_Errcode::ERR_SUCCESS !== $setOutput['errno']) {
            Bingo_Log::warning("call service changeContentType fail ! input = " . serialize($setInput) . " output = " . serialize($setOutput));
            $this->_jsonRet(empty($setOutput['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $setOutput['errno']);
            return true;
        }

        // 返回结果
        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS));
	}

    /**
     * 检测是否满足图集
     * @param unknown $arrOut
     * @return string $key
     */
    private static function checkContent($arrData) {
        // 判断标题是否满足
        if (strlen($arrData['resource_title']) < 24 || strlen($arrData['resource_title']) > 120) {
            return self::arrRet(Tieba_Errcode::ERR_POST_CT_TAR_NUM_NOT_MATCH, '标题字数不满足');
        }
        // 格式化内容
        $arrOut = self::transContent($arrData['resource_content']);
        // 判断图片格式
        if (count($arrOut) < 6) {
            return self::arrRet(Tieba_Errcode::ERR_POST_CT_TAR_NUM_NOT_MATCH, '图片个数不够');
        }
        foreach ($arrOut as $key => &$value) {
            // 没有内容则使用标题填充内容
            if (!isset($value['content']) || strlen($value['content']) <= 0) {
                $value['content'] = $arrData['resource_title'];
            }
            // 内容超过200个字则截取前200个字
            $value['content'] = mb_substr($value['content'], 0, 200, 'utf-8');
        }
        $arrOut = array_slice($arrOut, 0, 25);

        return self::arrRet(Tieba_Errcode::ERR_SUCCESS, '', $arrOut);
    }

    /**
     * 转换为图集结构
     * @param unknown $arrOut
     * @return string $key
     */
    private static function transContent($arrContent) {
        if (empty($arrContent) || !is_array($arrContent)) {
            return false;
        }
        $arrData    = array();
        $intPicNum  = 0;
        $item_num   = 0;
        $arrData[$item_num]['content'] = '';
        foreach ($arrContent as $key => $value) {
            if ($value['tag'] == 'plainText') {
                $arrData[$item_num]['content'] = $arrData[$item_num]['content'] . $value['value'];
            }
            if ($value['tag'] == 'img' && $value['class'] == 'BDE_Image') {
                $intPicNum++;
                $picInfo = self::checkPic($value['src']);
                if ($picInfo) {
                    if ($intPicNum > 1) {
                        $item_num++;
                    }
                    $arrData[$item_num]['pic_url'] = $value;
                }
            }
        }
        return $arrData;
    }

//    /**
//     * 转换为图集结构
//     * @param unknown $arrOut
//     * @return string $key
//     */
//    private static function transContent($arrContent) {
//        if (empty($arrContent) || !is_array($arrContent)) {
//            return false;
//        }
//        $arrData = array();
//        $strContent = '';
//        foreach ($arrContent as $key => $value) {
//            if ($value['tag'] == 'plainText') {
//                $strContent = $strContent . $value['value'];
//            }
//            if ($value['tag'] == 'img' && $value['class'] == 'BDE_Image') {
//                $picInfo = self::checkPic($value['src']);
//                if ($picInfo) {
//                    $arrData[] = array(
//                        'content' => $strContent,
//                        'pic_url' => $value,
//                    );
//                }
//                $strContent = '';
//            }
//        }
//        return $arrData;
//    }

    /**
     * 判断图片是否满足要求
     * @param: pic url
     * @return: bool (true | false)
     **/
    private static function checkPic($strPicUrl) {
        $obj = new Bd_Pic_UrlCrypt();
        $urlInfo = pathinfo($strPicUrl);
        $picId = $obj->decode_pic_url_crypt($urlInfo['filename']);
        if ($picId <= 0) {
            Bingo_Log::warning("decode_pic_url_crypt fails! inputs: " . serialize($urlInfo['filename']) . ", outputs: " . serialize($picId));
            return false;
        }
        $picReq = array(
            'pic_id' => $picId,
        );
        $picRet = Space_Imgquery::queryPictureInfo($picReq);
        if (false === $picRet || $picRet['err_no'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("queryPictureInfo fails! inputs: " . serialize($picReq) . ", outputs: " . serialize($picRet));
            return false;
        }
        $whRate = round($picRet['width'] / $picRet['height'], 2);
        if ($picRet['width'] < 400 || $picRet['height'] < 224 || $picRet['data_len'] > 5000000 || $whRate > 2 || $whRate < 0.5) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 转换为html结构
     * @param unknown $arrOut
     * @return string $key
     */
    private static function transHtml($arrContent) {
        $strContent = '';
        foreach ($arrContent as $key => $value) {
            if (!empty($value['content'])) {
                $strContent .= $value['content'];
            }
            if (!empty($value['pic_url']) && !empty($value['pic_url']['src'])) {
                $strContent .= '<img class="' . $value['pic_url']['class'] . '" pic_type="' . $value['pic_url']['pic_type'] . '" width="' . $value['pic_url']['width'] . '" height="' . $value['pic_url']['height'] . '" src="' . $value['pic_url']['src'] . '" unselectable="' . $value['pic_url']['unselectable'] . '" />';
            }
        }
        return $strContent;
    }
	
	/**
	 * 初始化参数
	 * @param
	 * @return true | false
	 */
	public function initParam() {
		$this->intResourceId    = intval(Bingo_Http_Request::getNoXssSafe('resource_id', ''), 0);
		$this->intResourceType  = intval(Bingo_Http_Request::getNoXssSafe('resource_type', ''), 0);
        $this->intStreamId      = intval(Bingo_Http_Request::getNoXssSafe('stream_id', ''), 0);
		$this->intReplyNum      = intval(Bingo_Http_Request::getNoXssSafe('reply_num', ''), 0);
        $this->intThreadId      = intval(Bingo_Http_Request::getNoXssSafe('thread_id', ''), 0);
        $this->intId            = intval(Bingo_Http_Request::getNoXssSafe('id', ''), 0);
        $this->intOpUid         = $this->_intUserId;
        $this->strOpUname       = $this->_strUserName;

		if (empty($this->intResourceId) || empty($this->intId)) {
			return false;
		}
		return true;
	}

    /**
     *  inherit from parent and do nothing
     * @param unknown $errno
     * @param unknown $arrData
     * @return string
     */
    protected function arrRet($errno, $errmsg = '', $data = array()){
        return array(
            'errno'     => $errno,
            'errmsg'    => $errmsg,
            'data'      => $data,
        );
    }
}

