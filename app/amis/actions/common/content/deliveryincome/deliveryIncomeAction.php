<?php
/**
 * Created by PhpStorm.
 * User: lilinke
 * Date: 2019/1/3
 * Time: 下午4:48
 */

class deliveryIncomeAction extends Actions_Common_Content_Util_Actioncontent{
    const DATA_INCOME = 1;
    const ACCOUNT_INCOME = 2;
    private $_intIncomeType = -1;
    private $_strChannelName = '';
    private $_strThirdPartAccountName = '';
    private $_pn = 1;
    private $_rn = 10;
    private $_begin_time = 0;
    private $_end_time = 0;
    private $_arrOutputRows = array();
    private $_intOutputCnt = 0;


    /**
     * [_execute description]
     * @return [type] [description]
     */
    public function process()
    {
        if (!$this->_input()) {
            return false;
        }
        if($this->_intIncomeType == self::DATA_INCOME){
            $this->_getDeliveryIncomeDetailData();
        }else if ($this->_intIncomeType == self::ACCOUNT_INCOME){
            $this->_getDeliveryIncomeDetailAccount();
        }
        $this->_build();
        return true;
    }
    /**
     * [_input description]
     * @return [type] [description]
     */
    private function _input(){
        $this->_intIncomeType = intval(Bingo_Http_Request::get('delivery_income_type',0));
        $this-> _strChannelName = strval(Bingo_Http_Request::get('channel_name',''));
        $this->_strThirdPartAccountName = strval(Bingo_Http_Request::get('third_account_name',''));
        $this->_begin_time = strval(Bingo_Http_Request::get('begin_time',''));
        $this->_end_time = strval(Bingo_Http_Request::get('end_time',''));
//        $this->_begin_time = intval($this->_begin_time) == 0 ? 0 : strtotime($this->_begin_time);
//        $this->_end_time = intval($this->_end_time) == 0 ? 0 : strtotime($this->_end_time)+86400;
        $this->_pn = intval(Bingo_Http_Request::get('pn', ''),0);
        $this->_rn = intval(Bingo_Http_Request::get('rn', ''),0);
        if(($this->_intIncomeType != self::DATA_INCOME && $this->_intIncomeType != self::ACCOUNT_INCOME) || empty($this->_strChannelName)){
            Bingo_Log::warning('param error!!! intIncomeType['.$this->_intIncomeType.'] _strChannelName['.$this->_strChannelName.']');
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        return true;
    }

    /**
     * 获取数据维度 分发收益
     * @return bool
     */
    private function _getDeliveryIncomeDetailData(){
        $arrQueryReq = array(
            'condition' => array(
                'channel_name' => $this->_strChannelName,
                'deliver_time' => array(
                    'range_query' => array(
                        'begin_time' => strtotime($this->_begin_time),
                        'end_time' => strtotime($this->_end_time)+86400,
                    ),
                ),
                'pn' => $this->_pn,
                'rn' => $this->_rn,
            ),
        );
        if(!empty($this->_strThirdPartAccountName)){
            $arrQueryReq['condition']['third_part_account_name'] = $this->_strThirdPartAccountName;
        }
        $arrOut = Tieba_Service::call('common', 'queryDeliveryIncomeDetailData', $arrQueryReq);
        if (false == $arrOut || !isset($arrOut['errno']) || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
            Bingo_Log::warning('call common queryDeliveryIncomeDetailData fail. arrinput:[' . serialize($arrQueryReq) . '] arrOut:[' . serialize($arrOut) . ']');
            $this->_jsonRet( Tieba_Errcode::ERR_CALL_SERVICE_FAIL );
            return false;
        }
        foreach ($arrOut['data']['rows'] as $row){
            $this->_arrOutputRows[] = array(
                'third_part_account_name' => $row['third_part_account_name'],
                'resource_id' => $row['resource_id'],
                'third_part_resource_id' => $row['third_part_resource_id'],
                'deliver_date' => date("Ymd",$row['deliver_time']),
                'resource_num' => 1,
                'display_num' => $row['display_num'],
                'read_play_num' => $row['read_play_num'],
                'comment_num' => $row['comment_num'],
                'zan_num' => $row['zan_num'],
                'share_num' => $row['share_num'],
                'type' => self::DATA_INCOME,
            );
        }
        $this->_intOutputCnt = $arrOut['data']['count'];
        return true;
    }

    /**
     * @return bool
     */
    private function _getDeliveryIncomeDetailAccount(){
        $arrQueryReq = array(
            'condition' => array(
                'channel_name' => $this->_strChannelName,
                'deliver_date' => array(
                    'range_query' => array(
                        'begin_time' => $this->_begin_time,
                        'end_time' => $this->_end_time,
                    ),
                ),
                'pn' => $this->_pn,
                'rn' => $this->_rn,
            ),
        );
        if(!empty($this->_strThirdPartAccountName)){
            $arrQueryReq['condition']['third_part_account_name'] = $this->_strThirdPartAccountName;
        }
        $arrOut = Tieba_Service::call('common', 'queryDeliveryIncomeDetailAccountData', $arrQueryReq);
        if (false == $arrOut || !isset($arrOut['errno']) || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
            Bingo_Log::warning('call common queryDeliveryIncomeDetailData fail. arrinput:[' . serialize($arrQueryReq) . '] arrOut:[' . serialize($arrOut) . ']');
            $this->_jsonRet( Tieba_Errcode::ERR_CALL_SERVICE_FAIL );
            return false;
        }
        foreach ($arrOut['data']['rows'] as $row){
            $this->_arrOutputRows[] = array(
                'third_part_account_name' => $row['third_part_account_name'],
                //'resource_id' => $row['resource_id'],
                'deliver_date' => $row['deliver_date'],
                'resource_num' => $row['resource_num'],
                'display_num' => $row['display_num'],
                'read_play_num' => $row['read_play_num'],
                'comment_num' => $row['comment_num'],
                'zan_num' => $row['zan_num'],
                'share_num' => $row['share_num'],
                'type' => self::ACCOUNT_INCOME,
            );
        }
        $this->_intOutputCnt = $arrOut['data']['count'];
        return true;
    }

    /**
     * 组装
     * @return [type] [description]
     */
    private function _build()
    {
        $data['count'] = $this->_intOutputCnt;
        $data['rows'] = $this->_arrOutputRows;
        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,'', $data);
    }
}

