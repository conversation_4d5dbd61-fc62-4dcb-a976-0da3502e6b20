<?php
define("BINGO_ENCODE_LANG", 'utf-8');

/**
 * @abstract getDeliveryConfAction 
 * <AUTHOR>
 *
 */
class getDeliveryConfigAction extends Actions_Common_Content_Util_Actioncontent{
	/**
	 * inherit from parent and do nothing
	 * @param null
	 * @return boolean
	 */
	public function process() {
		$rn = intval(Bingo_Http_Request::get('rn', 30));
		$pn = intval(Bingo_Http_Request::get('pn', 1));
		$teamId = intval(Bingo_Http_Request::get('team_id', 0));
		
		if($rn <= 0 || $pn <= 0 || $teamId == 0){
            Bingo_Log::warning('getDeliveryConfigAction input param is invalid.:[team_id:'.$teamId.' rn:'.$rn.' pn:'.$pn .']');
			$this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            Tieba_Error::getUserMsg();
			return true;
		}

		$arrInput = array (
			'rn' => $rn,
			'pn' => $pn,
			'teamId' => $teamId,
		);
		$arrRes = Tieba_Service::call('common', 'getDeliveryConfig', $arrInput);
		if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
			Bingo_Log::warning("call service common::getDeliveryConfig fail ! input = ".serialize($arrInput)." output = ".serialize($arrRes));
			$this->_jsonRet(empty($arrRes['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrRes['errno']);
			return true;
		}
        $rows = $arrRes['data'];
		//job_desc 字段 反序列化  之前有job_desc = '' 的数据
        foreach ($rows as &$row){
            $arrJobDesc = unserialize($row['job_desc']);
            if($arrJobDesc === false){
                $row['job_desc'] = '';
            }else{
                $row['job_desc']=$arrJobDesc;
            }
        }
		$arrInput = array (
			'teamId' => $teamId,
		);
		$arrRes = Tieba_Service::call('common', 'getDeliveryConfigCount', $arrInput, null, null, 'post', 'php', 'utf-8');
		if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
			Bingo_Log::warning("call service common::getDeliveryConfigCount fail ! input = ".serialize($arrInput)." output = ".serialize($arrRes));
			$this->_jsonRet(empty($arrRes['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrRes['errno']);
			return true;
		}
        $count = $arrRes['data'][0]['count(id)'];
        $data = array(
            'rows' => $rows,
            'count' => $count,
        );

		$this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,'', $data);
		return true;
	}
	
}
