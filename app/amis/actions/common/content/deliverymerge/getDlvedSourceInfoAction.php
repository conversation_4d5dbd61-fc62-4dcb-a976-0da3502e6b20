<?php
/**
 * Created by PhpStorm.
 * User: lilinke
 * Date: 2017/12/11
 * Time: 16:31
 */
define("BINGO_ENCODE_LANG", 'utf-8');
class getDlvedSourceInfoAction extends Actions_Common_Content_Util_Actioncontent{
    /**
     * inherit from parent and do nothing
     * @param null
     * @return boolean
     */
    public function process(){
        $accountId= intval(Bingo_Http_Request::get('account_id', ''));
        $intPn  = intval(Bingo_Http_Request::get('pn', ''));
        $intRn  = intval(Bingo_Http_Request::get('rn', ''));
        $intTeamId=intval(Bingo_Http_Request::get('team_id', ''));
        $strBeginTime= intval(Bingo_Http_Request::get('begin_time', ''));
        $strEndTime= intval(Bingo_Http_Request::get('end_time', ''));
        $intOpUid = $this->_intUserId;
        $strOpUname = $this->_strUserName;

        $intBeginTime = strtotime(strval($strBeginTime));
        $intEndTime = strtotime(strval($strEndTime))+86400;
        if( empty($accountId) || $intPn <=0 || $intRn < 0){
            Bingo_Log::warning('getResourceThreadInfoAction input param is invalid');
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        //获取delivery_id
        $arrInput = array('teamId'=> $intTeamId,'account_id'=>$accountId);
        $arrOutput = Tieba_Service::call('common', 'getDlvIdByacountId', $arrInput, null, null, 'post',null);
        if (false == $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
            Bingo_Log::warning("call service fail ! input = ".serialize($arrInput)." output = ".serialize($arrOutput));
            $this->_jsonRet(empty($arrOutput['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrOutput['errno']);
            return false;
        }
        $intDlvId = intval($arrOutput['data'][0]['delivery_id']);
        if($intDlvId <=0){
            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS);//account_id 不属于本team
            return false;
        }
        //从代码中获取角色配置
        $arrInput = array(
            'delivery_id'=>$intDlvId,
            'offset' => ($intPn - 1)* $intRn,
            'size' =>$intRn,
            'team_id' =>$intTeamId,
            'op_uid' =>$intOpUid,
            'op_name' =>$strOpUname,
        );
        if ($intBeginTime > 0 && $intEndTime > 0 && $intEndTime >= $intBeginTime) {
            $arrInput['begin_time'] = $intBeginTime;
            $arrInput['end_time'] = $intEndTime;
        }
        //获取个数
        $arrRes = Tieba_Service::call('common', 'getSourceCntByDlvId', $arrInput, null, null, 'post', null);
        if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning("call service fail ! input = ".serialize($arrInput)." output = ".serialize($arrRes));
            $this->_jsonRet(empty($arrRes['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrRes['errno']);
            return false;
        }
        $intCnt = $arrRes['data'][0]['cnt'];
        //获取sourceInfo
        $arrRes = Tieba_Service::call('common', 'getSourceInfoByDlvId', $arrInput, null, null, 'post', null);
        if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning("call service fail ! input = ".serialize($arrInput)." output = ".serialize($arrRes));
            $this->_jsonRet(empty($arrRes['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrRes['errno']);
            return true;
        }
        if(count($arrRes['data']) <= 0){
            $ret['count'] = $intCnt;
            $ret['rows'] = $arrRes['data'];
            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,'',$ret);
            return true;
        }
        foreach($arrRes['data'] as &$sourceInfo){
            $arrResourceIds[] = $sourceInfo['resource_id'];
        }
        $strRourseIds = implode(',',$arrResourceIds);
        $arrInput = array(
            'resource_ids'=>$strRourseIds,
            'account_id'=> $accountId,
        );
        //$arrInput['resource_ids'] = $strRourseIds;
        //$arrInput['account_id'] =  $accountId;
        $arrOutput = Tieba_Service::call('common', 'getThreadIdByResids', $arrInput, null, null, 'post',null);
        if(false == $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning("call service fail ! input = ".serialize($arrInput)." output = ".serialize($arrOutput));
            $this->_jsonRet(empty($arrOutput['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrOutput['errno']);
            return false;
        }
        $arrThreadInfo = array();
        foreach($arrOutput['data'] as $threadInfo){
            $arrThreadInfo[$threadInfo['resource_id']] = $threadInfo['thread_id'];
        }
        foreach($arrRes['data'] as &$sourceInfo){
            $sourceInfo['thread_id'] = $arrThreadInfo[$sourceInfo['resource_id']];
            if($sourceInfo['is_deliver'] == 1) {
                $sourceInfo['deliver_time'] = date('Ymd ', $sourceInfo['deliver_time']);
            }else{
                $sourceInfo['deliver_time'] = null;
            }
        }
        //获取资源详情
        $arrReqParamsByIndex = array();
        foreach ($arrRes['data'] as $index => $row){
            $intResourceId=$row['resource_id'];
            $intResourceType=$row['resource_type'];
            $arrResourceInput=array(
                'resource_id' =>$intResourceId,
                'resource_type' =>$intResourceType,
            );
            //获取资源详情
            $arrReqParam = array();
            $arrReqParam['serviceName'] = 'common';
            $arrReqParam['method'] = 'getResourceThreadDetail';
            $arrReqParam['input'] = $arrResourceInput;
            $arrReqParam['ie'] = 'gbk'; //'utf-8';
            $arrReqParamsByIndex[$index.'_'.$arrReqParam['method']] = $arrReqParam;
        }
        if(empty($arrReqParamsByIndex)){
            Bingo_Log::warning('getResourceThreadInfoAction no valid call data.input:['.serialize($arrReqParamsByIndex).']');
            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '',array());
            return true;
        }
        $arrOutList = Util_BatchMulticall::multiCallService($arrReqParamsByIndex);
        if(false === $arrOutList || empty($arrOutList) || !is_array($arrOutList)){
            Bingo_Log::warning('getResourceThreadInfoAction call service fail.input:['.serialize($arrReqParamsByIndex).'] outList:['.serialize($arrOutList).']');
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, '',array());
            return true;
        }
        foreach ($arrRes['data'] as $newIndex => &$row){
            $key = $newIndex.'_getResourceThreadDetail';
            $arrOut = $arrOutList[$key];
            $row['title'] = $arrOut['data']['resource_title'];
        };
        $ret['count'] = $intCnt;
        $ret['rows'] = $arrRes['data'];
        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '',$ret);
        return true;
    }
}


