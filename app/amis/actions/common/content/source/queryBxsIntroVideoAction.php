<?php
/**
 * content source ui
 */
define("BINGO_ENCODE_LANG", 'utf-8');
class queryBxsIntroVideoAction extends Util_Action{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
 
	/**
	 * @param bool
	 * @return bool
	 */
    public function _execute(){
        try {
            //参数获取
        	$intOpUid = intval(Util_User::$intUserId);
        	$strOpUname = strval(Util_User::$strUserName);
            	
       	 	if($intOpUid <= 0 || empty($strOpUname)){
            	Bingo_Log::warning('queryBxsIntroVideoAction invalid login.OpUid:['.$intOpUid.'] opuname:['.$strOpUname.']');
            	return $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN, '用户未登录，请先登录。');
            }
            $intUserType=$_SERVER['HTTP_AMIS_USER_TYPE'];
            $intStatus=Bingo_Http_Request::get('status');
            if($intStatus==''){
            	$intStatus=-1;
            }
            $strStar=strval(Bingo_Http_Request::get('resource_query'));
            $intType = intval(Bingo_Http_Request::get('resource_type', 0));
            $intResourceId=intval(Bingo_Http_Request::get('id', 0));
            $intPn = intval(Bingo_Http_Request::get('pn', 1));
            $intRn = intval(Bingo_Http_Request::get('rn', 10));
            $intStartDate = Bingo_Http_Request::get('query_date', 0);
            $intEndDate = Bingo_Http_Request::get('end_time', 0);
            //只有管理员和运营可以看白名单
            $strUserRole = $_SERVER['HTTP_AMIS_ROLES'];
            $strUserRole = urldecode($strUserRole);
            if(strpos($strUserRole,"管理员") === false && strpos($strUserRole,"运营") ===false)
            {
                return $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN, array(), '用户无权限');
            }
             
            $arrInput = array(
            	'resource_status' => $intStatus,
                'resource_type' => $intType,
            	'start_date' =>$intStartDate,
            	'end_date' =>  $intEndDate,
            	'resource_query' => $strStar,
            	'resource_id' => $intResourceId,
            	'pn' => $intPn,
            	'rn' => $intRn,
            );
            //print_r($arrInput);
            Bingo_Log::warning("zlinput==66666666===".var_export($arrInput,true));
            //外网用户只展示自己推荐的数据
            if($intUserType==1){
            	$arrInput['user_id']=$intOpUid;
            }
            $arrRet = Tieba_Service::call('common', 'queryBxsIntroVideo', $arrInput);
            //print_r( $arrRet);
            if($arrRet['errno'] != 0){
            	Bingo_Log::warning('call service common::queryBxsIntroVideo fail, input:['.serialize($arrInput).'],output:['.serialize($arrRet).']');
               	$this->_jsonRet($arrRet['errno'], $arrRet);
                return false;
            }

		    $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,$arrRet['data']);
        } catch (Exception $e) {
            Bingo_Log::warning( "errno =".$e->getCode() ." msg=".$e->getMessage() );
            $this->_jsonRet($e->getCode(), '未知错误');
        }
    }
	
	/**
	 * inherit from parent and do nothing
     * @param array
     * @return bool
	 */
	protected function _jsonRet($errno, $arrData=array()){
		$arrRet = array(
			'errno' => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
			'data' => $arrData,
		);
		echo json_encode($arrRet);
	}
}



?>
