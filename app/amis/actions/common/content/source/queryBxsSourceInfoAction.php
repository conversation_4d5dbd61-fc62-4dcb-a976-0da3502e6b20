<?php
/**
 * content source ui
 */
define("BINGO_ENCODE_LANG", 'utf-8');
class queryBxsSourceInfoAction extends Util_Action{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
 
	/**
	 * @param bool
	 * @return bool
	 */
    public function _execute(){

        try {
            //参数获取
        	$intOpUid = intval(Util_User::$intUserId);
        	$strOpUname = strval(Util_User::$strUserName);
        	
       	 	if($intOpUid <= 0 || empty($strOpUname)){
            	Bingo_Log::warning('queryBxsSourceInfoAction invalid login.OpUid:['.$intOpUid.'] opuname:['.$strOpUname.']');
            	return $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN, '用户未登录，请先登录。');
            }
            $intUserType=$_SERVER['HTTP_AMIS_USER_TYPE'];
            $intQueryDate = intval(Bingo_Http_Request::get('query_date', 0));
            $intStatus=Bingo_Http_Request::get('status');
            if($intStatus==''){
            	$intStatus=-1;
            }
            $intResourceType = intval(Bingo_Http_Request::get('resource_type', -1));
            $intResourceTemp=intval(Bingo_Http_Request::get('resource_temp', 0));
            $intResourceFrom=intval(Bingo_Http_Request::get('resource_from', 0));
            $strStar=strval(Bingo_Http_Request::get('resource_keyword'));
            $strKeyWord=strval(Bingo_Http_Request::get('keyword'));
            $intResourceId=intval(Bingo_Http_Request::get('id', 0));
            $intThreadId=intval(Bingo_Http_Request::get('thread_id', 0));
            $intPn = intval(Bingo_Http_Request::get('pn', 1));
            $intRn = intval(Bingo_Http_Request::get('rn', 10));
            $intStartDate = Bingo_Http_Request::get('start_time', 0);
            $intEndDate = Bingo_Http_Request::get('end_time', 0);

            /*$intStartDate=0;
            $intEndDate=0;
            if($intQueryDate >0){
            	$strQueryDate=date("Y-m-d ", $intQueryDate);
            	$intStartDate=strtotime($strQueryDate);
            	$intEndDate=$intStartDate+86399;
            }*/
            
            $arrInput = array(
            	'resource_status' => $intStatus,
            	'resource_temp' => $intResourceTemp,
                'resource_from' => $intResourceFrom,
            	'start_date' =>$intStartDate,
            	'end_date' =>  $intEndDate,
            	'resource_keyword' => $strStar,
            	'title_keyword' => $strKeyWord,
            	'resource_id' => $intResourceId,
                'resource_type' => $intResourceType,
                'thread_id' => $intThreadId,
            	'pn' => $intPn,
            	'rn' => $intRn,
            );
            $outPersonCheck = 0;
            $strUserRole = $_SERVER['HTTP_AMIS_ROLES'];
            Bingo_Log::warning("HTTP_AMIS_ROLES===".urldecode($strUserRole));
            $strUserRole = urldecode($strUserRole);
            if(strpos($strUserRole,"外包审核") !==false)
            {
                $outPersonCheck = 1;
            }
            Bingo_Log::warning("outPersonCheck====".$outPersonCheck);
            if($outPersonCheck <> 1)
            {
                //外网用户只展示自己推荐的数据
                if($intUserType==1){
                    $arrInput['user_id']=$intOpUid;
                }
            }
            $arrRet = Tieba_Service::call('common', 'queryBxsSourceInfo', $arrInput);
            if($arrRet['errno'] != 0){
            	Bingo_Log::warning('call service common::queryBxsSourceInfo fail, input:['.serialize($arrInput).'],output:['.serialize($arrRet).']');
               	$this->_jsonRet($arrRet['errno'], $arrRet);
                return false;
            }

		    $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,$arrRet['data']);
        } catch (Exception $e) {
            Bingo_Log::warning( "errno =".$e->getCode() ." msg=".$e->getMessage() );
            $this->_jsonRet($e->getCode(), '未知错误');
        }
    }
	
	/**
	 * inherit from parent and do nothing
     * @param array
     * @return bool
	 */
	protected function _jsonRet($errno, $arrData=array()){
		$arrRet = array(
			'errno' => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
			'data' => $arrData,
		);
		echo json_encode($arrRet);
	}
}



?>
