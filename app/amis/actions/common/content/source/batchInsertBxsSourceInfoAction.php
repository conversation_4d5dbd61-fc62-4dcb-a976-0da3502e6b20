<?php
/**
 * User: wangquanxiang
 * Date: 20180305
 * content source ui
 */
define("BINGO_ENCODE_LANG", 'utf-8');
define("TMP_PATH",dirname(__file__)."/tmp/");
class batchInsertBxsSourceInfoAction extends Util_Action{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;

    private static $arrMapType = array(
        "电视剧" => 0,
        "电影" => 1,
        "综艺" => 2,
        "动漫" => 3,
        "纪录片" => 4,
    );

	/**
	 * @param bool
	 * @return bool
	 */
	public function _execute(){

        try {

            //参数获取
            $intResourceTemp = intval(Bingo_Http_Request::get('resource_temp',0));
            $path = Bingo_Http_Request::get('file','');
            $path = str_replace(".cdn.",".bj.",$path);
            if(empty($path)){
                Bingo_Log::warning('batchInsertBxsSourceInfoAction input file invalid');
                $errMsg="请输入有效文件";
                return $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), $errMsg);
            }
            $intOpUid = intval(Util_User::$intUserId);
            $strOpUname = strval(Util_User::$strUserName);
            
            if($intOpUid <= 0 || empty($strOpUname)){
                Bingo_Log::warning('batchInsertBxsSourceInfoAction invalid login.OpUid:['.$intOpUid.'] opuname:['.$strOpUname.']');
                return $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN, array(), '用户未登录，请先登录。');
            }
            mkdir(TMP_PATH,0777,true);
            $cmd = "wget $path -O  ".TMP_PATH."batch.csv";
            exec($cmd);
            $path = TMP_PATH."batch.csv";
            $myfile = fopen($path,'r');
            $num = 0;
            $arrInputExcel = array();
            $arrTids = array();
            $arrTypes = array();
            $arrErrorId = array();
            $strImageUrl = "";
            while(!feof($myfile)){
                $line = trim(strval(fgets($myfile)));
                if (empty($line) || $num == 0){
                    $num++;
                    continue;
                }
                
                if($num > 300){//每次300个，大概20秒左右的响应时间，不会超时
                    break;
                }
                $arrLine = explode(",", trim($line));

                if (!empty($arrLine)){
                    if(empty($arrLine[0])){
                        $arrErrorId[] = $arrLine[1];
                        continue;
                    }
                    $arrInputExcel[trim($arrLine[1])] = Molib_Util_Encode::convertGBKToUTF8(trim($arrLine[0]));
                    //$arrInput[$num]['tid'] = $arrLine[1];
                    $arrTids[] = trim($arrLine[1]);
                    if($num == 1){
                        $strImageUrl = str_replace('https:','http:',trim($arrLine[2]));
                    }
                    $num++;
                    $arrLine[3] = Molib_Util_Encode::convertGBKToUTF8(trim($arrLine[3]));
                    //保存该贴子的类型
                    $arrTypes[trim($arrLine[1])] = intval(isset(self::$arrMapType[$arrLine[3]])?self::$arrMapType[$arrLine[3]]:0);
                }

            }
            $size = 50;
            $arrBatchInputId = array_chunk($arrTids, $size);

            $objMultiCall = new Tieba_Multi('post');
            Bingo_Timer::start("mgetThread");
            foreach ($arrBatchInputId as $index => $arrId) {
                    
                $input = array(
                    "thread_ids" => $arrId,
                    "need_abstract" => 0,
                    "forum_id" => 0,
                    "need_photo_pic" => 1,
                    "need_user_data" => 0,
                    "icon_size" => 0,
                    "need_mask_info" => 1,
                    "need_forum_name" =>0, //是否获取吧名
                    "call_from" => "pc_frs", //上游模块名
                );
                $strMethod = 'mgetThread_'.$index;
                $arrMultiInput = array(
                        'serviceName' => 'post',
                        'method' => 'mgetThread',
                        'input' => $input,
                        'ie' => 'utf-8',
                );
                $objMultiCall->register($strMethod,new Tieba_Service('post'),$arrMultiInput);
                //$arrRet = Tieba_Service::call('post', 'mgetThread', $input, null, null, 'post', 'php', 'utf-8');
                
            }

            $objMultiCall->call();
            $arrRet = $objMultiCall->results;
            Bingo_Timer::end("mgetThread");


            Bingo_Timer::start("getPostsByThreadId");
            $objMultiCallPost = new Tieba_Multi('post');
            $arrAbstract = array();
            foreach ($arrBatchInputId as $index => $arrId) {
                        
                foreach ($arrId as $key => $tid) {
                    $objMultiCallPost->register(
                        'getPostsByThreadId_'.$tid,
                        new Tieba_Service('post'),
                        array(
                            'serviceName'=>'post',
                            'method'=>'getPostsByThreadId',
                            'input'=> array(
                                'thread_id' => $tid,
                                'offset' => 0,
                                'res_num' => 20,
                                'see_author' => 0,
                                'has_comment' => 0,
                                'has_mask' => 1,
                                'has_ext' => 0,
                                'need_set_pv' => 0,
                                'structured_content' => 1,
                            ),
                            'ie' => 'utf-8',
                        )
                    );
                }
                $objMultiCallPost->call();
                $arrRetPosts = $objMultiCallPost->results;
                if(false === $arrRetPosts){
                    Bingo_Log::warning("failed to call service:[post], method:[getPostsByThreadId :$index]");                    
                }
                foreach ($arrRetPosts as $methodKey => $methodValue) {
                    $methodValue = $methodValue['output']['output'][0];
                    if(!isset($methodValue['err_no']) || $methodValue['err_no'] != 0){
                        Bingo_Log::warning("failed to call service:[post], method:[$methodKey]");
                        //continue;
                    }else{
                        $pickey = $methodValue['thread_id']."pic";
                        $arrAbstract[$methodValue['thread_id']] = $arrAbstract[$pickey] = "";
                        //处理图集
                        foreach ($methodValue['post_infos'] as $post_info) 
                        {
                            foreach ($post_info['content'] as $content) 
                            {
                                if( $content['tag'] == "img" && $content['class'] == 'BDE_Image')
                                    {
                                        $arrAbstract[$pickey] .= (substr($content['src'],39,-4)."-".$content['height']."-".$content['width'] . ",");
                                    }
                            }
                        }
                        foreach ($methodValue['post_infos'] as $post_info) {
                            foreach ($post_info['content'] as $content) {
                                if ( mb_strlen($arrAbstract[$methodValue['thread_id']],'utf-8') < 30 ){
                                    if( $content['tag'] == "plainText" ){
                                        $arrAbstract[$methodValue['thread_id']] .= ($content['value'] . " ");
                                    }
                                }else{
                                    break;//后面还要做截取，所以满足条件后提前退出，提高效率
                                }
                                
                            }

                            if( mb_strlen($arrAbstract[$methodValue['thread_id']],'utf-8') > 30 ){
                                break;
                            }
                            
                        }
                    }
                    
                }

            }
            Bingo_Timer::end("getPostsByThreadId");
            //var_dump($arrRet);exit;
            if(false === $arrRet){
                Bingo_Log::warning("failed to call service:[post], method:[mgetThread :all]");
                return false;
            }
            
            Bingo_Timer::start("insert");
            
            $errno = 0;
            foreach ($arrRet as $methodKey => $methodValue) {
                foreach ($methodValue['output']["thread_list"] as $key => $value) {
                    if($value['is_deleted'] == 1 || $value['is_partial_visible'] == 1){//过滤删贴
                        $arrErrorId[] = $key;
                        Bingo_Log::warning("deleted : [tid] = $key");
                        continue;
                    }
                    $input = array(
                        "user_id" => $value['user_id'],
                        "forum_id" => 0,
                        "ip" => 0,
                    );
                    $res = Tieba_Service::call('userstate', 'get_user_block_info', $input, null, null, 'post', 'php', 'gbk');
                    if(empty($res) || $res['errno'] != Tieba_Errcode::ERR_SUCCESS){
                        Bingo_Log::warning("Failed to call service:[userstate], method:[get_user_block_info],  param:[".serialize($input)."]");
                        
                    }
                    if(!empty($res['block_info']) && $res['block_info'][0]['block_type'] == 16){//过滤封禁屏蔽用户的贴子
                        $arrErrorId[] = $key;
                        Bingo_Log::warning("blocked : [tid] = $key");
                        continue;
                    }

                    //根据keyword、tid、resource_temp进行去重
                    $arrInput = array(
                        'resource_status' => -1,//全部
                        'resource_temp' => $intResourceTemp,
                        'start_date' => 0,
                        'end_date' =>  0,
                        'resource_keyword' => $arrInputExcel[$key],
                        'thread_id' => $key,
                        'title_keyword' => '',
                        'resource_id' => 0,
                        'pn' => 1,
                        'rn' => 1,
                    );

                    $arrRet = Tieba_Service::call('common', 'queryBxsSourceInfo', $arrInput);
                    if($arrRet['errno'] != 0){
                        Bingo_Log::warning('call service common::queryBxsSourceInfo fail, input:['.serialize($arrInput).'],output:['.serialize($arrRet).']');
                        $arrErrorId[] = $key;
                        continue;
                    }

                    if($arrRet['data']['count'] != 0){
                        Bingo_Log::warning("bxs-rec-batch tid exist: $key");
                        $arrErrorId[] = $key;
                        continue;
                    }

                    $arrThreadType = Tieba_Type_Thread::getTypeArray($value['thread_types']);//判断贴子类型，获取封面
                    $cover = "";
                    if( (isset($arrThreadType['is_movideo']) && true === $arrThreadType['is_movideo']) || (isset($arrThreadType['is_ala_video']) && true === $arrThreadType['is_ala_video']) ){
                        $cover = str_replace('https:','http:',$value['video_info']['thumbnail_url']);
                    }elseif(isset($value['media'][0])){
                        $cover = str_replace('https:','http:',$value['media'][0]['big_pic']);
                    }
                    $strUserRole = $_SERVER['HTTP_AMIS_ROLES'];
                    $strUserRole = urldecode($strUserRole);
                    $resource_from = 1;
                    if(strpos($strUserRole,"管理员") !== false && strpos($strUserRole,"运营") !==false)
                    {
                        $resource_from = 5;//resource_from 1:recommend 2:robot 3:idol 4:bazhu 5:yunying
                    }   
                    elseif(strpos($strUserRole,"普通") !== false)
                    {
                        $resource_from = 4;//resource_from 1:recommend 2:robot 3:idol 4:bazhu 5:yunying
                    }
                    
                    //增加9张图（从帖子中抓取）
                    $pickey = $key."pic";
                    $arrInput = array(
                        'thread_id' => $key,
                        'resource_url' => "http://tieba.baidu.com/p/".$key,
                        'resource_title' => $value['title'],
                        'resource_cover' => !empty($cover)?$cover:$strImageUrl,
                        'resource_abstract' => $arrAbstract[$key],
                        'resource_status' => 0,
                        'resource_keyword' => $arrInputExcel[$key],
                        'recommend_uid' => $intOpUid,
                        'resource_from' => $resource_from,
                        'resource_temp' => $intResourceTemp,//待调整
                        'op_uid' => $intOpUid,
                        'op_name' => $strOpUname,
                        'create_uid' => $intOpUid,
                        'create_name' => $strOpUname,
                        'resource_type' => $arrTypes[$key],
                        'resource_pic' => $arrAbstract[$pickey],
                    );
                
                    $arrRet = Tieba_Service::call('common', 'addBxsSourceInfo', $arrInput);
                    if($arrRet['errno'] != 0){
                        Bingo_Log::warning('call service common::addBxsSourceInfo fail, input:['.serialize($arrInput).'],output:['.serialize($arrRet).']');
                        $arrErrorId[] = $key;
                        $errno = $arrRet['errno'];
                    }
                
                }
                
            }
            Bingo_Timer::end("insert");
            if(!empty($arrErrorId)){
                $errMsg = "以下贴子导入失败：";
                foreach ($arrErrorId as $index => $id) {
                    $errMsg .= "$id ";
                }
                $this->_jsonRet($arrRet['errno'], $arrRet,$errMsg);
                return false;
            }
            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,$arrRet['data']);
        
        	
        } catch (Exception $e) {
            Bingo_Log::warning( "errno =".$e->getCode() ." msg=".$e->getMessage() );
            $this->_jsonRet($e->getCode(), array(), '未知错误');
        }
    }
	
	/**
	 * inherit from parent and do nothing
     * @param array
     * @return bool
	 */
	protected function _jsonRet($errno, $arrData=array(), $errmsg = ''){
		$arrRet = array(
			'errno' => $errno,
			'errmsg' => empty($errmsg)?Tieba_Error::getErrmsg($errno):$errmsg,
			'data' => $arrData,
		);
		echo json_encode($arrRet);
	}
}



?>
