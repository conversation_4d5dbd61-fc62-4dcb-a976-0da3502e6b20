<?php
/**
 * User: wangquanxiang
 * Date: 20180305
 * content source ui
 */
define("BINGO_ENCODE_LANG", 'utf-8');
define("TMP_PATH",dirname(__file__)."/tmp/");
class batchBxsInterstarPkPicAction extends Util_Action{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;


	/**
	 * @param bool
	 * @return bool
	 */
	public function _execute(){
        try {

            //参数获取
            $intResourceTemp = intval(Bingo_Http_Request::get('resource_temp',0));
            $path = Bingo_Http_Request::get('file','');
            $path = str_replace(".cdn.",".bj.",$path);
            Bingo_Log::warning("aaaeee---".var_export($path,true));
            if(empty($path)){
                Bingo_Log::warning('batchInsertBxsSourceInfoAction input file invalid');
                $errMsg="请输入有效文件";
                return $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), $errMsg);
            }
            $intOpUid = intval(Util_User::$intUserId);
            $strOpUname = strval(Util_User::$strUserName);
            /*if($intOpUid <= 0 || empty($strOpUname)){
                Bingo_Log::warning('batchInsertBxsSourceInfoAction invalid login.OpUid:['.$intOpUid.'] opuname:['.$strOpUname.']');
                return $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN, array(), '用户未登录，请先登录。');
            }*/
            //只有管理员、运营有权限
            $strUserRole = $_SERVER['HTTP_AMIS_ROLES'];
            $strUserRole = urldecode($strUserRole);
            if(strpos($strUserRole,"管理员") === false && strpos($strUserRole,"运营") ===false)
            {
                //return false;
            }
            mkdir(TMP_PATH,0777,true);
            Bingo_Log::warning("zhddanglin path==".$path);
            $cmd = "wget $path -O  ".TMP_PATH."batch.csv";
            Bingo_Log::warning("cmd==".$cmd);
            exec($cmd);
            $path = TMP_PATH."batch.csv";
            $myfile = fopen($path,'r');
            $num = 0;
            $arrInputExcel = array();
            $arrTids = array();
            $arrErrorId = array();
            $arrResult = array(); 
            while(!feof($myfile))
            {
                $line = trim(strval(fgets($myfile)));
                if (empty($line) || $num == 0){
                    $num++;
                    continue;
                }
                
                if($num > 300){//每次300个，大概20秒左右的响应时间，不会超时
                    break;
                }
                $arrLine = explode(",", trim($line));

                if (!empty($arrLine)){
                    Bingo_Log::warning("zhddanglineeeeeeeeeeee".var_export($arrLine,true));
                    $arrInput_insert = array(
                        'resource_pic' => $arrLine[1],
                        'vote' => 0,
                        'resource_query' => $arrLine[0],
                        'phase' => $arrLine[2],//第几期
                        'resource_from' => 8,//批量上传的来源是8，到service中不修改status
                        'op_uid' => $intOpUid,
                        'op_name' => $strOpUname,
                        'create_uid' => $intOpUid,
                        'create_name' => $strOpUname,
                    );
                    $arrRet_insert = Tieba_Service::call('common', 'addBxsInternetStarPk', $arrInput_insert);
                }
            }
            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,$arrRet['data']);
         
        } catch (Exception $e) {
            Bingo_Log::warning( "errno =".$e->getCode() ." msg=".$e->getMessage() );
            $this->_jsonRet($e->getCode(), array(), '未知错误');
        }
    }
	
	/**
	 * inherit from parent and do nothing
     * @param array
     * @return bool
	 */
	protected function _jsonRet($errno, $arrData=array(), $errmsg = ''){
		$arrRet = array(
			'errno' => $errno,
			'errmsg' => empty($errmsg)?Tieba_Error::getErrmsg($errno):$errmsg,
			'data' => $arrData,
		);
		echo json_encode($arrRet);
	}
}



?>
