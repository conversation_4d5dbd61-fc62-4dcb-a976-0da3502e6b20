<?php
/**
 * content source ui
 */
define("BINGO_ENCODE_LANG", 'utf-8');
class examineBxsIntroVideoAction extends Util_Action{

	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * @param bool
	 * @return bool
	 */
    public function _execute(){
        try {
            //参数获取
            $intResourceStatus = intval(Bingo_Http_Request::get('resource_status',0));
            $strResourceId = Bingo_Http_Request::get('id', '');
            $arrResourceId = explode(',', $strResourceId);
            $intModifyUid = intval(Util_User::$intUserId);
            $strModifyName = strval(Util_User::$strUserName);
            if($intModifyUid <= 0 || empty($strModifyName)){
            	Bingo_Log::warning('editBxsIntroVideoAction invalid login.OpUid:['.$intModifyUid.'] opuname:['.$strModifyName.']');
            	return $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN, '用户未登录，请先登录。');
            }
            //只有管理员、运营有权限
            $strUserRole = $_SERVER['HTTP_AMIS_ROLES'];
            $strUserRole = urldecode($strUserRole);
            if(strpos($strUserRole,"管理员") === false && strpos($strUserRole,"运营") ===false)
            {
                return $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN, array(), '用户无权限');
            }
            $arrInput = array(
            	'update_fields' => array(
					'status' => $intResourceStatus,
            		'modify_time'=> time(),
					'modify_uid' => $intModifyUid,
					'modify_name' => $strModifyName,
            	),
            	'condition' => array(
            		'id' => $arrResourceId,
            	),
            );
            $arrRet = Tieba_Service::call('common', 'editBxsIntroVideo', $arrInput);
            if($arrRet['errno'] != 0){
            	Bingo_Log::warning('call service common::editBxsIntroVideo fail, input:['.serialize($arrInput).'],output:['.serialize($arrRet).']');
               	$this->_jsonRet($arrRet['errno'], $arrRet);
                return false;
            }
		    $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,$arrRet['data']);
        } catch (Exception $e) {
            Bingo_Log::warning( "errno =".$e->getCode() ." msg=".$e->getMessage() );
            $this->_jsonRet($e->getCode(), '未知错误');
        }
    }
	
	/**
	 * inherit from parent and do nothing
     * @param array
     * @return bool
	 */
	protected function _jsonRet($errno, $arrData=array()){
		$arrRet = array(
			'errno' => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
			'data' => $arrData,
		);
		echo json_encode($arrRet);
	}
}



?>
