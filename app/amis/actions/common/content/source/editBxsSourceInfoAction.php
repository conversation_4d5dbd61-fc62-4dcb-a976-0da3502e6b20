<?php
/**
 * content source ui
 */
define("BINGO_ENCODE_LANG", 'utf-8');
class editBxsSourceInfoAction extends Util_Action{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	/**
	 * @param bool
	 * @return bool
	 */
    public function _execute(){

        try {
            //参数获取
      
        	$strKeyWord = strval(Bingo_Http_Request::get('resource_keyword',''));
            $strResourceUrl = strval(Bingo_Http_Request::get('resource_url', ''));
            $strResourceTitle = strval(Bingo_Http_Request::get('resource_title', ''));
            $strResourceAbstract = strval(Bingo_Http_Request::get('resource_abstract',''));
            $strResourceCover = strval(Bingo_Http_Request::get('resource_cover'));
            $intResourceTemp = intval(Bingo_Http_Request::get('resource_temp',0));
            $intResourceType = intval(Bingo_Http_Request::get('resource_type',0));
            $intResourceId = Bingo_Http_Request::get('id', 0);
            $arrResourceId = array($intResourceId);
            $intOpUid = intval(Util_User::$intUserId);
            $strOpUname = strval(Util_User::$strUserName);
            if($intOpUid <= 0 || empty($strOpUname)){
            	Bingo_Log::warning('editBxsSourceInfoAction invalid login.OpUid:['.$intOpUid.'] opuname:['.$strOpUname.']');
            	return $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN, array(), '用户未登录，请先登录。');
            }
            
            if(empty($strKeyWord) ||empty($strResourceUrl)){
            	Bingo_Log::warning('addBxsSourceInfoAction input resource_keyword invalid.ResList:['.$strKeyWord.']');
            	$errMsg="请输入有效的明星全称";
            	if($intResourceTemp==2){
            		$errMsg="请输入有效的影视全称";
            	}
            	return $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), $errMsg);
            }
            if(empty($strResourceUrl)){
            	Bingo_Log::warning('addBxsSourceInfoAction input resource_url invalid.ResList:['.$strResourceUrl.']');
            	$errMsg="请输入有效的url";
            	return $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), $errMsg);
            }
            
        	//判断url是否合法
            $thread_id=0;
            if(strpos($strResourceUrl, 'https://tieba.baidu.com/p/') === 0 || strpos($strResourceUrl, 'http://tieba.baidu.com/p/') === 0){
            	$thread_id=intval(str_replace("https://tieba.baidu.com/p/","",$strResourceUrl));
            	if($thread_id==0){
            		$thread_id=intval(str_replace("http://tieba.baidu.com/p/","",$strResourceUrl));
            	}
            }
            if($thread_id <=0){
            	Bingo_Log::warning('addBxsSourceInfoAction url invalid login.url:['.$strResourceUrl.']');
            	return $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN, array(), '帖子url无效');
            }
            //加入图集
            $resource_pic = '';
            $input = array(
                "thread_id" => $thread_id, //帖子id
                "offset" => 0,
                "res_num" => 20,
                "see_author" => 1,
                "has_comment" => 0,
                "has_mask" => 1,
                "has_ext" => 0,
                "need_set_pv" => 0,
                "structured_content" => 1,
            );
            $Ret   = Tieba_Service::call('post', 'getPostsByThreadId', $input, null, null, 'post', 'php', 'utf-8');
            foreach ($Ret['output']['output'][0]['post_infos'] as $post_info)
            {
                foreach ($post_info['content'] as $content)
                {
                    if( $content['tag'] == "img" && $content['class'] == 'BDE_Image')
                    {
                        $resource_pic .= (substr($content['src'],39,-4)."-".$content['height']."-".$content['width'] . ",");
                    }
                }
            }       
            $arrInput = array(
            	'update_fields' => array(
            		'thread_id' => $thread_id,
					'resource_url' => $strResourceUrl,
					'resource_title' => $strResourceTitle,
					'resource_cover' => $strResourceCover,
                    'resource_pic' => $resource_pic,
					'resource_abstract' => $strResourceAbstract,
					'resource_status' => 0,
					'resource_keyword' => $strKeyWord,
					'resource_temp' => $intResourceTemp,
                    'resource_type' => $intResourceType,
            		'modify_time'=> time(),
					'op_uid' => $intOpUid,
					'op_name' => $strOpUname,
            	),
            	'condition' => array(
            		'id' => $arrResourceId,
            	),
            );
            $arrRet = Tieba_Service::call('common', 'editBxsSourceInfo', $arrInput);
            if($arrRet['errno'] != 0){
            	Bingo_Log::warning('call service common::editBxsSourceInfo fail, input:['.serialize($arrInput).'],output:['.serialize($arrRet).']');
            	$errMsg=Tieba_Error::getErrmsg($errno);
            	if($arrRet['errno']==Tieba_Errcode::ERR_DIFANG_INFOBASE_USER_NOT_EXIST){
            		$errMsg="请输入有效的明星全称";
            		if($intResourceTemp==2){
            			$errMsg="请输入有效的影视全称";
            		}
            	}
            	$this->_jsonRet($arrRet['errno'], $arrRet,$errMsg);
                return false;
            }
		    $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,$arrRet['data']);
        } catch (Exception $e) {
            Bingo_Log::warning( "errno =".$e->getCode() ." msg=".$e->getMessage() );
            $this->_jsonRet($e->getCode(), array(), '未知错误');
        }
    }
	
	/**
	 * inherit from parent and do nothing
     * @param array
     * @return bool
	 */
	protected function _jsonRet($errno, $arrData=array(), $errmsg = ''){
		$arrRet = array(
			'errno' => $errno,
			'errmsg' => empty($errmsg)?Tieba_Error::getErrmsg($errno):$errmsg,
			'data' => $arrData,
		);
		echo json_encode($arrRet);
	}
}



?>
