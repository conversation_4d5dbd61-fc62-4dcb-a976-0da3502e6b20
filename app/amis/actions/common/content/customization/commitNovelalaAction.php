<?php
define("BINGO_ENCODE_LANG", 'utf-8');

/**
 * @abstract commitNovelala
 * <AUTHOR>
 *
 */
class commitNovelalaAction extends Actions_Common_Content_Util_Actioncontent{

    CONST OP_TYPE_ADD = 'add';
    CONST OP_TYPE_EDIT = 'edit';
    protected $_strUserName = '';
    private $arrRequest = array();
	/**
	 * inherit from parent and do nothing
	 * @param null
	 * @return boolean
	 */
	public function process(){
        $this->optype = Bingo_Http_Request::getNoXssSafe('op_type', '');
        $this->arrRequest['status'] = intval(Bingo_Http_Request::get('status', 0));
        $this->arrRequest['fid'] = intval(Bingo_Http_Request::get('fid', 0));
        $this->arrRequest['tid'] = intval(Bingo_Http_Request::get('tid', 0));
        $this->arrRequest['tid_weight'] = intval(Bingo_Http_Request::get('tid_weight', 0));
        $this->arrRequest['op_name'] = $this->_strUserName;
        if (empty($this->arrRequest['fid']) || empty($this->arrRequest['tid'])) {
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return true;
        }
        if (empty($this->arrRequest['tid_weight']) && empty($this->arrRequest['status'])) {
            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS);
            return true;
        }
        switch ($this->optype){
            case self::OP_TYPE_ADD :
                $arrRet = $this->_addNovelala();
                break;
            case self::OP_TYPE_EDIT :
                $arrRet = $this->_updateNovelala();
                break;
            default :
                Bingo_Log::warning("[]unknown op_type[$this->optype]!");
                $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
                return true;
        }

        if (false == $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            $this->_jsonRet(empty($arrRet['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrRet['errno']);
            return true;
        }
        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS);
        return true;
	}

    /**
     * add
     * @param
     * @return
     */
    private function _addNovelala() {
        return array('errno'=>Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * update
     * @param
     * @return
     */
    private function _updateNovelala() {
        $arrInput = array(
            'fid'       => $this->arrRequest['fid'],
            'tid'       => $this->arrRequest['tid'],
            'op_name'   => $this->arrRequest['op_name'],
        );
        if (!empty($this->arrRequest['status'])) {
           $arrInput['status']  = $this->arrRequest['status'];
        }
        if (!empty($this->arrRequest['tid_weight'])) {
            $arrInput['tid_weight'] = $this->arrRequest['tid_weight'];
        }
        $arrRes = Tieba_Service::call('common', 'updateNovelalaInfoByFidTid', $arrInput, null, null, 'post', 'php', 'gbk');
        if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning("call service common::updateNovelalaInfoByFidTid fail ! input = ".serialize($arrInput)." output = ".serialize($arrRes));
        }
        return $arrRes;
    }
}