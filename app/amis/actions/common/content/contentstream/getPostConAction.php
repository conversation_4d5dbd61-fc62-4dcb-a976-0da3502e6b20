<?php
/**
 * User: lining17
 * Date: 20170113
 * content stream ui
 */
define("BINGO_ENCODE_LANG", 'utf-8');
class getPostConAction extends Actions_Common_Content_Util_Actioncontent{
    private $team_id = '';
	/**
	 * inherit from parent and do nothing
	 * @return bool
	 */
	/**
	 * @param bool
	 * @return bool
	 */
    public function process(){

        try {
            $postCon= array(
                'topHasPic' => array(
                    'label' => '首楼是否有图',
                    'type' => '2'
                ),
                'postTime' => array(
                    'label' => '发帖时间（天）',
                    'type' => '3'
                ),
                'lastUpdateTime' => array(
                    'label' => '最后更新时间（天）',
                    'type' => '3'
                ),
                'floorNum' => array(
                    'label' => '楼层数',
                    'type' => '3'
                ),
                'replyNum' => array(
                    'label' => '总回复数',
                    'type' => '3'
                ),
                'authorPostRatio' => array(
                    'label' => '楼主发帖比例（0-1之间）',
                    'type' => '3'
                ),
                'othersPostRatio' => array(
                    'label' => '其他人发帖比例（0-1之间）',
                    'type' => '3'
                ),
                'postTextLenght' => array(
                    'label' => '帖子文本总长度',
                    'type' => '3'
                ),
                'postRichTextNum' => array(
                    'label' => '帖子总富媒体数量',
                    'type' => '3'
                ),
                'postVideoLength' => array(
                    'label' => '帖子视频长度（秒）',
                    'type' => '3'
                )
            );
            //参数获取
            /*
            $this->team_id = strval(Bingo_Http_Request::get('team_id', ''));
            if(is_null($this->team_id) || intval($this->team_id) <= 0) {
                Bingo_Log::warning('param error: team_id is null or does not exists.');
                $this->_jsonRet(Tieba_Errcode::ERR_MO_PARAM_INVALID, Tieba_Error::getUserMsg(Tieba_Errcode::ERR_MO_PARAM_INVALID));
                return false;
            }
             */
            $arrRet = Tieba_Service::call('common', 'contentQueryFeatureDesc', null);
            if($arrRet['errno'] != 0){
                Bingo_Log::warning('call service common::contentQueryFeatureDesc fail, input:['.serialize($arrInput).'],output:['.serialize($arrRet).']');
		        $this->_jsonRet($arrRet['errno'], '',$arrRet);
                return false;
            }
            $arrRes = array();
            foreach($arrRet['data']['postCon']as $key => $param){
                if($param['operate'] == '包含'){
                    $operator = 1;
                }else if($param['operate'] == '是,否'){
                    $operator = 2;
                }else{
                    $operator = 3;
                }
                $arrRes[] = array(
                    'value' => $key,
                    'label' => $param['desc'],
                    'type' => $operator
                );
            }
		    $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '',$arrRes);

        } catch (Exception $e) {
            Bingo_Log::warning( "errno =".$e->getCode() ." msg=".$e->getMessage() );
            $this->_jsonRet($e->getCode(), '未知错误');
        }
    }
}



?>
