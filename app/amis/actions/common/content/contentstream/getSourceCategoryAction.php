<?php
/**
 * User: lining17
 * Date: 20170113
 * content stream ui
 */
define("BINGO_ENCODE_LANG", 'utf-8');
class getSourceCategoryAction extends Actions_Common_Content_Util_Actioncontent{
    private $team_id = '';
	/**
	 * @param bool
	 * @return bool
	 */
    public function process(){

        try {
            /*
            $sourceCategory= array(
                array(
                    'label' => '活动',
                    'value' => 'activityPost',
                    'type' => '0'
                ),
                array(
                    'label' => '综艺',
                    'value' => 'varietyPost',
                    'type' => '0'
                ),
                array(
                    'label' => '电影',
                    'value' => 'moviePost',
                    'type' => '0'
                ),
                array(
                    'label' => '电视剧',
                    'value' => 'dramaPost',
                    'type' => '0'
                ),
                array(
                    'label' => '明星',
                    'value' => 'startsPost',
                    'type' => '0'
                ),
                array(
                    'label' => '热点',
                    'value' => 'hotPost',
                    'type' => '0'
                )
            );
             */
            //参数获取
            /*
            $this->team_id = strval(Bingo_Http_Request::get('team_id', ''));
            if(is_null($this->team_id) || intval($this->team_id) <= 0) {
                Bingo_Log::warning('param error: team_id is null or does not exists.');
                $this->_jsonRet(Tieba_Errcode::ERR_MO_PARAM_INVALID, Tieba_Error::getUserMsg(Tieba_Errcode::ERR_MO_PARAM_INVALID));
                return false;
            }
             */
            $arrRet = Tieba_Service::call('common','contentQueryTagType', null);
            if($arrRet['errno'] != 0){
                Bingo_Log::warning('call service common::contentQueryTagType fail,output:['.serialize($arrRet).']');
		        $this->_jsonRet($arrRet['errno'], '',$arrRet);
                return false;
            }
            foreach($arrRet['data'] as $id => $label){
                $sourceCategory[] = array(
                    'label' => $label,
                    'value' => $id,
                );
            }
		    $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '',$sourceCategory);

        } catch (Exception $e) {
            Bingo_Log::warning( "errno =".$e->getCode() ." msg=".$e->getMessage() );
            $this->_jsonRet($e->getCode(), '未知错误');
        }
    }
}



?>
