<?php
/**
 * User: lining17
 * Date: 20170113
 * content stream ui
 */
define("BINGO_ENCODE_LANG", 'utf-8');
class manageStreamAction extends Actions_Common_Content_Util_Actioncontent{
    const PAGE_ITEMS = 2;
    private $team_id = '';
    private $page_num = '';
    private $row_num = '';
	/**
	 * @param bool
	 * @return bool
	 */
    public function process(){

        try {
            $operatorMap = array(
                '1' => "包含",
                '2' => "是",
                '3' => "否",
                '4' => ">",
                '5' => ">=",
                '6' => "=",
                '7' => "<",
                '8' => "<="
            );
            $firstForumDirectory = array(
                'baiduServiceCenter' => array(
                    'label' => '百度服务中心',
                    'type' => '0'
                ),
                'contemporaryPeople' => array(
                    'label' => '当代人物',
                    'type' => '0'
                ),
                'district' => array(
                    'label' => '地区',
                    'type' => '0'
                ),
                'computerDigital' => array(
                    'label' => '电脑数码',
                    'type' => '0'
                ),
                'tvShow' => array(
                    'label' => '电视节目',
                    'type' => '0'
                ),
                'drama' => array(
                    'label' => '电视剧',
                    'type' => '0'
                ),
                'movie' => array(
                    'label' => '电影',
                    'type' => '0'
                ),
                'cartoon' => array(
                    'label' => '动漫',
                    'type' => '0'
                ),
                'college' => array(
                    'label' => '高等院校',
                    'type' => '0'
                ),
                'product' => array(
                    'label' => '工农业产品',
                    'type' => '0'
                ),
                'train' => array(
                    'label' => '教育培训',
                    'type' => '0'
                ),
                'finance' => array(
                    'label' => '金融',
                    'type' => '0'
                ),
                'science' => array(
                    'label' => '科学技术',
                    'type' => '0'
                ),
                'historalPeople' => array(
                    'label' => '历史人物',
                    'type' => '0'
                ),
                'enterprise' => array(
                    'label' => '企业',
                    'type' => '0'
                ),
                'emotion' => array(
                    'label' => '情感',
                    'type' => '0'
                ),
                'nature' => array(
                    'label' => '人文自然',
                    'type' => '0'
                ),
                'enterpriseService' => array(
                    'label' => '商业服务',
                    'type' => '0'
                ),
                'sociaty' => array(
                    'label' => '社会',
                    'type' => '0'
                ),
                'life' => array(
                    'label' => '生活',
                    'type' => '0'
                ),
                'liveGoods' => array(
                    'label' => '生活用品',
                    'type' => '0'
                ),
                'gym' => array(
                    'label' => '体育',
                    'type' => '0'
                ),
                'netClub' => array(
                    'label' => '网友俱乐部',
                    'type' => '0'
                ),
                'literature' => array(
                    'label' => '文学',
                    'type' => '0'
                ),
                'game' => array(
                    'label' => '游戏',
                    'type' => '0'
                ),
                'music' => array(
                    'label' => '音乐',
                    'type' => '0'
                ),
                'stars' => array(
                    'label' => '娱乐明星',
                    'type' => '0'
                ),
                'middleSchool' => array(
                    'label' => '中小学',
                    'type' => '0'
                )
            );
            $dataPeriod= array(
                '1' => array(
                    'label' => '1天',
                    'type' => '0'
                ),
                '2' => array(
                    'label' => '2天',
                    'type' => '0'
                ),
                '3' => array(
                    'label' => '3天',
                    'type' => '0'
                ),
                '4' => array(
                    'label' => '7天',
                    'type' => '0'
                )
            );
            $descMap = array(
                'data_time' => array(
                    'label' => '数据时效性',
                ),
                'job_time' => array(
                    'label' => '任务周期(每天)',
                ),
                'do_at_once' => array(
                    'label' => '是否立即执行',
                )
            );
            $auditMap = array(
                '0' => '免审核',
                '1' => '一次审核',
                '2' => '二次审核'
            );
            $baseMap = array(
                'sourceType' => 'resource_type',
                'sourceCategory' => 'resource_tag',
                'firstForumDirectory' => 'resource_fdir'
            );
            //参数获取
            $this->team_id = strval(Bingo_Http_Request::get('team_id', ''));
            $this->page_num = strval(Bingo_Http_Request::get('page_num', '1'));
            $this->row_num = strval(Bingo_Http_Request::get('row_num', '20'));
            if(is_null($this->team_id) || intval($this->team_id) <= 0 || is_null($this->page_num) || intval($this->page_num) <= 0|| is_null($this->row_num) || intval($this->row_num) <= 0) {
                Bingo_Log::warning('param error: team_id row_num or page_num is wrong.');
                $this->_jsonRet(Tieba_Errcode::ERR_MO_PARAM_INVALID);
                return false;
            }
            $opUid = $this->_intUserId;
            $opUname = $this->_strUserName;
            $arrInput = array(
                'team_id' => intval($this->team_id),
                'page_num' => intval($this->page_num),
                'row_num' => intval($this->row_num),
                'op_uid' => $opUid,
                'op_name' => $opUname
            );
            $arrRet = Tieba_Service::call('common', 'getContentStream', $arrInput);
            if($arrRet['errno'] != 0){
                Bingo_Log::warning('call service common::getContentStream fail, input:['.serialize($arrInput).'],output:['.serialize($arrRet).']');
		        $this->_jsonRet($arrRet['errno'],'', $arrRet);
                return false;
            }

            // 获取数目个数
            $arrInputCnt = array(
                'team_id' => intval($this->team_id),
            );
            $arrRetCnt = Tieba_Service::call('common', 'getContentStreamCnt', $arrInputCnt);
            if($arrRetCnt['errno'] != 0){
                Bingo_Log::warning('call service common::getContentStreamCnt fail, input:['.serialize($arrInputCnt).'],output:['.serialize($arrRetCnt).']');
            }
            $arrRes['count'] = intval($arrRetCnt['data']);

            if(count($arrRet['data']) == $this->row_num + 1){
                $arrRes['hasMore'] = 1;
                unset($arrRet['data'][$this->row_num]);
            }else{
                $arrRes['hasMore'] = 0;
            }

            //forTest
            //if($intOpUid <= 0 || empty($strOpUname)){
            //    Bingo_Log::warning('addStream invalid login.OpUid:['.$intOpUid.'] opuname:['.$strOpUname.']');
            //    return $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN, '用户未登录，请先登录。');
            //}
            //$opUid = 123;
            //$opUname = 'test123';
            $arrInput = array(
                'rn' => 1000,
                'pn' => 1,
                'user_id' => $opUid,
                'user_name' => $opUname,
                'team_id' => $this->team_id
            );
            $sourcesInfo = Tieba_Service::call('common', 'contentQuerySource', $arrInput);
            if($sourcesInfo['errno'] != 0){
                Bingo_Log::warning('call service common::contentQuerySource fail, output:['.serialize($sourcesInfo).']');
                return $this->_jsonRet($sourcesInfo['errno']);
            }
            $arrSourceInfoBySourceId = array();
            foreach ($sourcesInfo['data']['rows'] as $sourceInfo){
            	if(intval($sourceInfo['source_id']) <= 0 || empty($sourceInfo['source_name'])){
            		continue;
            	}
            	$arrSourceInfoBySourceId[intval($sourceInfo['source_id'])] = $sourceInfo;
            }
            $consInfo = Tieba_Service::call('common', 'contentQueryFeatureDesc', null);
            if($consInfo['errno'] != 0){
                Bingo_Log::warning('call service common::contentQueryFeatureDesc fail, output:['.serialize($consInfo).']');
		        $this->_jsonRet($consInfo['errno']);
                return false;
            }
            $sourcesType = Tieba_Service::call('common','contentQuerySourceType', null);
            if($sourcesType['errno'] != 0){
                Bingo_Log::warning('call service common::contentQuerySourceType fail, output:['.serialize($sourcesType).']');
		        $this->_jsonRet($sourcesType['errno']);
                return false;
            }
            $sourcesTag = Tieba_Service::call('common','contentQueryTagType', null);
            if($sourcesTag['errno'] != 0){
                Bingo_Log::warning('call service common::contentQueryTagType fail, output:['.serialize($sourcesTag).']');
		        $this->_jsonRet($sourcesTag['errno']);
                return false;
            }
            $arrRes['rows'] = array();
            foreach($arrRet['data'] as $stream){
            	$stream_id = $stream['stream_id'];
            	if($stream_id <= 0){
            		Bingo_Log::warning('invalid stream config data.stream:['.var_export($stream, true).']');
            		continue;
            	}
                $sources = array();
                $sourcesArr = unserialize($stream['sources']);
                foreach($sourcesArr as $label => $audit){
                    foreach($sourcesInfo['data']['rows'] as $sourceInfo){
                        if($sourceInfo['source_id'] == $label){
                            $sources[] = array(
                                'key' => $sourceInfo['source_name'],
                                'value' => $auditMap[strval($audit)]
                            );
                        }
                    }
                }
                $stream['sources'] = $sources;
                //获取编辑策略
                $arrSrcInfoBySrcId = array();
                foreach ($arrSourceInfoBySourceId as $src_id => $srcInfo){
                	if(isset($sourcesArr[$src_id])){
                		$arrSrcInfoBySrcId[$src_id] = $srcInfo;
                	}
                }
                $arrEditInput = array(
                	'team_id' => $this->team_id,
                	'stream_id' => $stream_id,
                );
                $arrEditOut = Tieba_Service::call('common', 'contentGetEditStrategyDetail', $arrEditInput);
                if(null != $arrEditOut && isset($arrEditOut['errno']) && Tieba_Errcode::ERR_SUCCESS == $arrEditOut['errno']){
                	$arrEditCfg = array();
                	foreach ($arrEditOut['data'] as $editid => $arrRow){
                		$source_id = $arrRow['source_id'];
                		$arrCfgList = $arrRow['config_details_struct'];
                		if($source_id <= 0 || empty($arrCfgList) || !is_array($arrCfgList)){
                			Bingo_Log::warning('source edit strategy invalid.arrCfg:['.var_export($arrCfgList, true).']');
                			continue;
                		}
                		$sourceName = $arrSrcInfoBySrcId[$source_id]['source_name'];
                		if(empty($sourceName)){
                			Bingo_Log::warning('source edit strategy source_name invalid.arrCfg:['.var_export($arrCfgList, true).']');
                			continue;
                		}
                		$editCfg = '';
                		//拼装编辑配置
                		foreach ($arrCfgList as $arrCfg){
                			if(empty($arrCfg['name']) || empty($arrCfg['desc'])){
                				Bingo_Log::warning('source edit strategy item invalid.arrCfg:['.var_export($arrCfg, true).']');
                				continue;
                			}
                			if('edit' == $arrCfg['ctrl_type']){
                				empty($editCfg) ? ($editCfg = ($arrCfg['desc'].':'.$arrCfg['value']).'; ') : ($editCfg .= ($arrCfg['desc'].':'.$arrCfg['value'].'; '));
                			}else if('switch' == $arrCfg['ctrl_type']){
                				$flag = (1 == $arrCfg['value'] ? '是' : '否');
                				empty($editCfg) ? ($editCfg = ($arrCfg['desc'].':'.$flag).'; ') : ($editCfg .= ($arrCfg['desc'].':'.$flag.'; '));
                			}
                		}
                		if(empty($editCfg)){
                			Bingo_Log::warning('source edit strategy edit_cfg is empty.arrCfg:['.var_export($arrCfgList, true).']');
                			continue;
                		}
                		$arrEditCfg[] = array(
                			'key' => $sourceName,
                			'value' => $editCfg,
                		);
                	}
                	$stream['edit_cfg_show'] = $arrEditCfg;
                }

                $select_condition = unserialize($stream['select_condition']);
                foreach($select_condition as $type => $condition){
                    foreach($condition as $key => $con){
                        $changedValue = '';
                        if($con['operator'] == 1){
                            $labels = explode(",", $con['value']);
                            if($con['label'] == 'resource_type'){
                                foreach($labels as $oneLabel){
                                    foreach($sourcesType['data'] as $index => $value){
                                        if($index == $oneLabel){
                                            $changedValue[] = $value;
                                        }
                                    }
                                }
                            }else if($con['label'] == 'resource_tag'){
                                foreach($labels as $oneLabel){
                                    foreach($sourcesTag['data'] as $index => $value){
                                        if($index == $oneLabel){
                                            $changedValue[] = $value;
                                        }
                                    }
                                }

                            }else if($con['label'] == 'resource_fdir'){
                                foreach($labels as $oneLabel){
                                    if(isset($firstForumDirectory[$oneLabel])){
                                        $changedValue[] = $firstForumDirectory[$oneLabel]['label'];
                                    }
                                }
                            }
                            $con['value'] = implode(",", $changedValue);
                        }

                        if($type != 'baseCon'){
                            foreach($consInfo['data'][$type] as $conLable => $conParam){
                                if($key == $con['label']){
                                    $conCh = $conParam['desc'];
                                    $con['label'] = $conCh;
                                }
                            }
                        }

                        $con['operator'] = $operatorMap[strval($con['operator'])];

                        if($type == 'baseCon'){
                            $select_condition[$type][$con['label']] = $con;
                            unset($select_condition[$type][$key]);
                        }else{
                            $select_condition[$type][$key] = $con;
                        }
                        //$condition[$key] = $con;
                    }
                }
                $stream['select_condition'] = $select_condition;

                $job_desc = unserialize($stream['job_desc']);
                foreach($job_desc as $job => $value){
                    //if(isset($descMap[$job])){
                    //    $changedJob = $descMap[$job]['label'];
                    //}
                    if ($job == 'job_time') {
                        unset($job_desc[$job]);
                        $arrTimes = explode(",", $value);
                        if (count($arrTimes) >= 1) {
                            foreach ($arrTimes as $item) {
                                $job_desc[$job][]['value'] = $item;
                            }
                        }
                    }
                    if(isset($dataPeriod[strval($value)]) && $job == 'data_time'){
                        $job_desc[$job] = $dataPeriod[strval($value)]['label'];
                    }else if($job == 'do_at_once'){
                        if($value == 'true'){
                            $job_desc[$job] = true;
                        }else{
                            $job_desc[$job] = false;
                        }

                    }
                    //$job_desc[$changedJob] = $changedVa;
                }
                $stream['job_desc'] = $job_desc;

                $deliverPools = unserialize($stream['deliver_pools']);
                $deliver_pools = array();
                $deliver_pools_list = array();
                $arrPoolids = array();
                foreach($deliverPools as $pool){
                	if($pool <= 0){
                		continue;
                	}
                	$arrPoolids[] = $pool;
                }
                if(!empty($arrPoolids)){
                	$arrInput = array(
                		'pool_ids' => $arrPoolids,
                	);
                	$arrRet = Tieba_Service::call('common', 'getDistriPoolInfoByIds', $arrInput);
                	if($arrRet['errno'] != 0){
                		Bingo_Log::warning('call service common::getDistriPoolInfoByIds fail, input:['.serialize($arrInput).'],output:['.serialize($arrRet).']');
                		$this->_jsonRet($arrRet['errno']);
                		return false;
                	}
                	foreach ($arrRet['data'] as $arrPoolInfo){
                		$deliver_pools[]['value'] = $arrPoolInfo['pool_name'];
                		//label、value用于显示分发池列表
                		$arrLabel = array(
                			'label' => $arrPoolInfo['pool_name'],
                			'value' => $arrPoolInfo['pool_id'],
                		);
                		$deliver_pools_list[] = $arrLabel;
                	}
                }
                $stream['deliver_pools'] = $deliver_pools;
                $stream['deliver_pools_list'] = $deliver_pools_list;
                $arrRes['rows'][] = $stream;
            }
		    $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '',$arrRes);

        } catch (Exception $e) {
            Bingo_Log::warning( "errno =".$e->getCode() ." msg=".$e->getMessage() );
            $this->_jsonRet($e->getCode(), '未知错误');
        }
    }
}



?>
