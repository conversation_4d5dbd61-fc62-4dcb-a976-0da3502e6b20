<?php

/**
 * @abstract delChannelAction
 * <AUTHOR>
 *
 */
class delChannelAction extends Actions_Common_Content_Util_Actioncontent{
	/**
	 * inherit from parent and do nothing
	 * @param null
	 * @return boolean
	 */
	public function process(){
		$channelid = intval(Bingo_Http_Request::get('channel_id', 0));
	
		if($channelid <= 0){
			Bingo_Log::warning('delChannelAction input param is invalid.channelid:['.$channelid.']');
			$this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
			return true;
		}
		
		$intOpUid = $this->_intUserId;
		$strOpUname = $this->_strUserName;
		if($intOpUid <= 0 || empty($strOpUname)){
			Bingo_Log::warning('delChannelAction invalid login.OpUid:['.$intOpUid.'] opuname:['.$strOpUname.']');
			return $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN, '用户未登录，请先登录。');
		}
	
		$arrInput = array(
			'channel_id' => $channelid,
			'op_uid' => $intOpUid,
			'op_name' => $strOpUname,
			'status' => 1,
		);
		$arrRes = Tieba_Service::call('common', 'contentDelChannel', $arrInput, null, null, 'post', 'php', 'gbk');
		if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
			Bingo_Log::warning("call service fail ! input = ".serialize($arrInput)." output = ".serialize($arrRes));
			$this->_jsonRet(empty($arrRes['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrRes['errno']);
			return true;
		}
		$this->_jsonRet(Tieba_Errcode::ERR_SUCCESS);
		return true;
	}
}