<?php
/**
 * content source ui
 */
define("BINGO_ENCODE_LANG", 'utf-8');
class changeMenuStageAction extends Util_Action{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	/**
	 * @param bool
	 * @return bool
	 */
    public function _execute(){
        try {
            //参数获取
            $arrInput = Bingo_Http_Request::getPostAll();
            $strOpUname = strval(Util_User::$strUserName);
            $editInput = array(
                'update_fields' => array(
                    'menu_stage' => $arrInput['changemenu_stage'],
                    'update_ctime' => time(),
                    'update_username' => $strOpUname,
                ),
                'condition' => array(
                    'id' => $arrInput['id'],
                ),
            );
            Bingo_log::warning("serviceinput==".var_export($editInput,1));
            $editRet = Tieba_Service::call('common', 'updateMenuInfo', $editInput, null, null, 'post', 'php', 'utf-8');
            Bingo_log::warning("service result==".var_export($editRet,1));
            if($editRet['errno'] != 0){
                Bingo_Log::warning('call service common::updateMenuInfo fail, input:['.serialize($editInput).'],output:['.serialize($editRet).']');
                $errMsg=Tieba_Error::getErrmsg($editRet['errno']);
                $this->_jsonRet($editRet['errno'], $editRet,$errMsg);
                return false;
            }


		    $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,$editRet['data']);
        } catch (Exception $e) {
            Bingo_Log::warning( "errno =".$e->getCode() ." msg=".$e->getMessage() );
            $this->_jsonRet($e->getCode(), array(), '未知错误');
        }
    }
	
	/**
	 * inherit from parent and do nothing
     * @param array
     * @return bool
	 */
	protected function _jsonRet($errno, $arrData=array(), $errmsg = ''){
		$arrRet = array(
			'errno' => $errno,
			'errmsg' => empty($errmsg)?Tieba_Error::getErrmsg($errno):$errmsg,
			'data' => $arrData,
		);
		echo json_encode($arrRet);
	}
}



?>
