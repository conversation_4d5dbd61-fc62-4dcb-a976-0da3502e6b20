<?php
/**
 * Created by PhpStorm.
 * User: lilinke
 * Date: 2019/2/25
 * Time: 下午2:39
 */
class getGodCommentAuditDataStatAction extends Actions_Common_Content_Util_Actioncontent{
    public static $intPn = 1;
    public static $intRn = 20;
    public static $intBeginTime = 0;
    public static $intEndTime =0;
    public static $intTeamId = 0;
    public static $intOpId = 0;
    public static $strOpName = '';
    public static $intStreamId = 0;

    /**
     * @return bool
     */
    protected function _init(){
        self::$intPn = intval(Bingo_Http_Request::get('pn', 1));
        self::$intRn = intval(Bingo_Http_Request::get('rn', 20));
        self::$intBeginTime = intval(Bingo_Http_Request::get('begin_time', ''));
        self::$intEndTime = intval(Bingo_Http_Request::get('end_time', ''));
        self::$intTeamId = intval(Bingo_Http_Request::get('team_id', ''));
        self::$intOpId = $this->_intUserId;
        self::$strOpName = $this->_strUserName;
        self::$intStreamId = intval(Bingo_Http_Request::get('stream_id', 0));
        if(self::$intBeginTime <= 0 || self::$intEndTime <= 0 || self::$intBeginTime > self::$intEndTime || (self::$intEndTime-self::$intBeginTime) > 31){
            Bingo_Log::warning('time param error!!!! begin_time['.self::$intBeginTime.']  end_time['.self::$intEndTime.']');
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, 'time param error');
            return false;
        }
        if(intval(self::$intRn) > 30 || self::$intTeamId <= 0 || self::$intStreamId <= 0){
            Bingo_Log::warning('rn param error!!!! or team_id  or stream_id error rn['.self::$intRn.']');
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        return true;
    }

    /**
     * @return array|bool
     */
    protected  function _getSourceInfoList(){
        $arrInput = array(
            'stream_id'=> self::$intStreamId,
            'op_uid' => self::$intOpId,
            'op_name' => self::$strOpName,
        );
        $arrRes = Tieba_Service::call('common', 'getSourceByStreamid', $arrInput, null, null, 'post', null);
        if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning("call service fail ! input = ".serialize($arrInput)." output = ".serialize($arrRes));
            //$this->_jsonRet(empty($arrRes['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrRes['errno']);
            return false;
        }
        $arrSourceInfoList =array();
        foreach ($arrRes['data'] as $key =>$value){
            if(intval($value['id']) <= 0 ){
                Bingo_Log::warning('error source_id['.$value['id'].']');
                continue;
            }
            $arrSourceInfoList[$value['id']]=$value['name'];
        }
        return $arrSourceInfoList;
    }
    /**
     * @param $arrSourceInfoList
     * @return array|bool
     */
    protected function _getAuditDataStat($arrSourceInfoList){
        if(empty($arrSourceInfoList) || !is_array($arrSourceInfoList)){
            Bingo_Log::warning('error arrSourceInfoList['.serialize($arrSourceInfoList).']');
            return false;
        }
        $arrAuditData = array();
        $arrSourceIdList = array_keys($arrSourceInfoList);
        $arrSourceInfoChunkList = array_chunk($arrSourceIdList,5);
        //并发调用
        foreach ($arrSourceInfoChunkList as $arrSouceIds) {
            // 注册并行调用接口
            $arrReqParamsByIndex = array();
            foreach ($arrSouceIds as $intSourceId) {
                //未审核数据量
                $arrUnAuditInput = array(
                    'stream_id' => self::$intStreamId,
                    'source_id' => $intSourceId,
                    'audit_status' => 0, //未审核
                    'page_num' => 1,
                    'rn' => 1,
                    'team_id' => self::$intTeamId,
                    'op_uid' => self::$intOpId,
                    'op_name' => self::$strOpName,
                    'begin_time' => self::$intBeginTime,
                    'end_time' => self::$intEndTime,
                );
                $arrUnAuditReqParam = array();
                $arrUnAuditReqParam['serviceName'] = 'common';
                $arrUnAuditReqParam['method'] = 'getResourceByCons';
                $arrUnAuditReqParam['input'] = $arrUnAuditInput;
                $arrUnAuditReqParam['ie'] = 'gbk'; //'utf-8';
                $strUnAuditReqkey = $intSourceId.'_0';
                $arrReqParamsByIndex[$strUnAuditReqkey] = $arrUnAuditReqParam;
                //一审数据量
                $arrFirstAuditInput = array(
                    'stream_id' => self::$intStreamId,
                    'source_id' => $intSourceId,
                    'audit_status' => 1, //一审
                    'page_num' => 1,
                    'rn' => 1,
                    'team_id' => self::$intTeamId,
                    'op_uid' => self::$intOpId,
                    'op_name' => self::$strOpName,
                    'begin_time' => self::$intBeginTime,
                    'end_time' => self::$intEndTime,
                );
                $arrFirstAuditReqParam = array();
                $arrFirstAuditReqParam['serviceName'] = 'common';
                $arrFirstAuditReqParam['method'] = 'getResourceByCons';
                $arrFirstAuditReqParam['input'] = $arrFirstAuditInput;
                $arrFirstAuditReqParam['ie'] = 'gbk'; //'utf-8';
                $strFirstAuditReqkey = $intSourceId.'_1';
                $arrReqParamsByIndex[$strFirstAuditReqkey] = $arrFirstAuditReqParam;
                //二审数据量
                $arrSecondAuditInput = array(
                    'stream_id' => self::$intStreamId,
                    'source_id' => $intSourceId,
                    'audit_status' => 2, //二审
                    'page_num' => 1,
                    'rn' => 1,
                    'team_id' => self::$intTeamId,
                    'op_uid' => self::$intOpId,
                    'op_name' => self::$strOpName,
                    'begin_time' => self::$intBeginTime,
                    'end_time' => self::$intEndTime,
                );
                $arrSecondAuditReqParam = array();
                $arrSecondAuditReqParam['serviceName'] = 'common';
                $arrSecondAuditReqParam['method'] = 'getResourceByCons';
                $arrSecondAuditReqParam['input'] = $arrSecondAuditInput;
                $arrSecondAuditReqParam['ie'] = 'gbk'; //'utf-8';
                $strSecondAuditReqkey = $intSourceId.'_2';
                $arrReqParamsByIndex[$strSecondAuditReqkey] = $arrSecondAuditReqParam;
                //不通过
                $arrRejectAuditInput = array(
                    'stream_id' => self::$intStreamId,
                    'source_id' => $intSourceId,
                    'audit_status' => 3, //二审
                    'page_num' => 1,
                    'rn' => 1,
                    'team_id' => self::$intTeamId,
                    'op_uid' => self::$intOpId,
                    'op_name' => self::$strOpName,
                    'begin_time' => self::$intBeginTime,
                    'end_time' => self::$intEndTime,
                );
                $arrRejectAuditReqParam = array();
                $arrRejectAuditReqParam['serviceName'] = 'common';
                $arrRejectAuditReqParam['method'] = 'getResourceByCons';
                $arrRejectAuditReqParam['input'] = $arrRejectAuditInput;
                $arrRejectAuditReqParam['ie'] = 'gbk'; //'utf-8';
                $strRejectAuditReqkey = $intSourceId.'_3';
                $arrReqParamsByIndex[$strRejectAuditReqkey] = $arrRejectAuditReqParam;

                //紧急下线量
                $arrOfflineCntInput = array(
                    'team_id' => self::$intTeamId,
                    'begin_date' => self::$intBeginTime,
                    'end_date' => self::$intEndTime,
                    'stream_id' => self::$intStreamId,
                    'source_id' => $intSourceId,
                );
                $arrOfflineCntReqParam = array();
                $arrOfflineCntReqParam['serviceName'] = 'common';
                $arrOfflineCntReqParam['method'] = 'getOfflineCntInAuditPool';
                $arrOfflineCntReqParam['input'] = $arrOfflineCntInput;
                $arrOfflineCntReqParam['ie'] = 'gbk'; //'utf-8';
                $strOfflineCntReqkey = $intSourceId.'_offline_cnt';
                $arrReqParamsByIndex[$strOfflineCntReqkey] = $arrOfflineCntReqParam;
            }
            $arrOutList = Util_BatchMulticall::multiCallService($arrReqParamsByIndex);
            if (false === $arrOutList || empty($arrOutList) || !is_array($arrOutList)) {
                Bingo_Log::warning('call service[getUserOperationCntByUserId] fail.input:[' . serialize($arrReqParamsByIndex) . '] outList:[' . serialize($arrOutList) . ']');
                //$this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array());
                continue;
            }
            foreach ($arrSouceIds as $intSourceId) {
                $arrData = array();
                $arrData['source_id'] = $intSourceId;
                $arrData['source_name'] = $arrSourceInfoList[$intSourceId];
                //未审核量
                $arrOut = $arrOutList[$intSourceId.'_0'];
                if(false==$arrOut ||Tieba_Errcode::ERR_SUCCESS !== $arrOut['errno']) {
                    Bingo_Log::warning('get source_id['.$intSourceId.'] zore audit data info failed');
                    continue;
                }
                $arrData['zero_audit_amount'] = $arrOutList[$intSourceId.'_0']['data']['count'];

                //一审核量
                $arrOut = $arrOutList[$intSourceId.'_1'];
                if(false==$arrOut ||Tieba_Errcode::ERR_SUCCESS !== $arrOut['errno']) {
                    Bingo_Log::warning('get source_id['.$intSourceId.'] first audit data info failed');
                    continue;
                }
                $arrData['first_audit_amount'] = $arrOutList[$intSourceId.'_1']['data']['count'];
                //二审量
                $arrOut = $arrOutList[$intSourceId.'_2'];
                if(false==$arrOut ||Tieba_Errcode::ERR_SUCCESS !== $arrOut['errno']) {
                    Bingo_Log::warning('get source_id['.$intSourceId.'] second audit data info failed');
                    continue;
                }
                $arrData['second_audit_amount'] = $arrOutList[$intSourceId.'_2']['data']['count'];
                //不通过数
                $arrOut = $arrOutList[$intSourceId.'_3'];
                if(false==$arrOut ||Tieba_Errcode::ERR_SUCCESS !== $arrOut['errno']) {
                    Bingo_Log::warning('get source_id['.$intSourceId.'] second audit data info failed');
                    continue;
                }
                $arrData['reject_audit_amount'] = $arrOutList[$intSourceId.'_3']['data']['count'];

                //紧急下线数
                $arrOut = $arrOutList[$intSourceId.'_offline_cnt'];
                if(false==$arrOut ||Tieba_Errcode::ERR_SUCCESS !== $arrOut['errno']) {
                    Bingo_Log::warning('get source_id['.$intSourceId.'] get offline data info failed');
                    continue;
                }
                $arrData['offline_amount'] = $arrOut['data']['count'];
                $arrAuditData[] = $arrData;
            }
        }
        return $arrAuditData;
    }

    /**
     * @param $arrRowData
     * @return array
     */
    protected function _dealData($arrRowData){
        $arrRetData = array();
        $arrTotalData = array(//总计
            'source_id'         => -1,
            'source_name'       => '总计',
            'total_amount'      => 0,//入库量
            'not_audit_amount'  => 0,//未审核量
            'audit_amount'      => 0,//审核量
            'audit_not_pass_amount' => 0,//审核未通过量
            'second_audit_pass_amount' => 0, //二审通过量
            'offline_amount'    => 0,//紧急下线量
            'usage_rate'        => 0,
        );
        foreach ($arrRowData as $row){
            $arrItem = array();
            $arrItem['source_id']           = $row['source_id'];
            $arrItem['source_name']         = $row['source_name'];
            $arrItem['total_amount']        = $row['zero_audit_amount'] + $row['first_audit_amount'] + $row['second_audit_amount']+$row['reject_audit_amount'];
            $arrItem['not_audit_amount']    = $row['zero_audit_amount'];
            $arrItem['audit_amount']        = $arrItem['total_amount'] - $arrItem['not_audit_amount'];
            $arrItem['second_audit_pass_amount'] = $row['second_audit_amount'];
            $arrItem['audit_not_pass_amount'] = $row['reject_audit_amount'];
            $arrItem['offline_amount']      = $row['offline_amount'];
            $arrItem['usage_rate']          = intval($arrItem['second_audit_amount'])/intval($arrItem['audit_amount']);// 二审通过量/审核量
            $arrItem['usage_rate']          = round($arrItem['usage_rate'],2);//四舍五入 保留2位有效数字

            $arrRetData['rows'][]           = $arrItem;

            $arrTotalData['total_amount']           += $arrItem['total_amount'];
            $arrTotalData['not_audit_amount']       += $arrItem['not_audit_amount'];
            $arrTotalData['audit_amount']           += $arrItem['audit_amount'];
            $arrTotalData['second_audit_pass_amount'] += $arrItem['second_audit_pass_amount'];
            $arrTotalData['audit_not_pass_amount']  += $arrItem['audit_not_pass_amount'];
            $arrTotalData['offline_amount']         += $arrItem['offline_amount'];
        }
        $arrTotalData['usage_rate'] = intval($arrTotalData['second_audit_amount']) / intval($arrTotalData['audit_amount']);
        $arrTotalData['usage_rate'] = round($arrTotalData['usage_rate'],2);
        $arrRetData['rows'][] = $arrTotalData;
        $arrRetData['count'] = count($arrRowData);
        return $arrRetData;
    }

    /**
     * inherit from parent and do nothing
     * @param null
     * @return boolean
     */
    public function process(){
        if(false == $this->_init()){
            return false;
        }
        $arrSourceInfoList = $this->_getSourceInfoList();
        if(false === $arrSourceInfoList){
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL,'call service failed!!!');
            return false;
        }
        $arrAuditData = $this->_getAuditDataStat($arrSourceInfoList);
        if(false == $arrAuditData){
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL,'call service failed!!!');
            return false;
        }
        $arrRetData = $this->_dealData($arrAuditData);
        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '',$arrRetData);
        return true;
    }
}