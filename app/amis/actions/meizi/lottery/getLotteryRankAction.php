<?
class getLotteryRankAction extends Util_Action {
	protected $strAmisGroup = 'meizi';
    protected $strAmisPerm = 'meizituan';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    protected $arrOtherParam = array(
        'pn',
        'rn',
    );

    public function _execute() {
    	$arrInput = $this->getInput();
        if(isset($arrInput['pn']) && isset($arrInput['rn'])){
            $arrParam   = array(
                'offset'    => ($arrInput['pn'] - 1) * $arrInput['rn'],
                'limit' => $arrInput['rn'],
            );
        }
    	$res   = Tieba_Service::call('marriage', 'getLotteryRank', $arrParam, null, null, 'post', 'php', 'utf-8');

		if (!$res || Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            Bingo_Log::warning(sprintf("call user::getPropsListByStatus fail. [input = %s] [ouput = %s]",
                serialize($arrInput), serialize($res)));
            $this->printOut(1, '获取中奖信息失败');
            return;
        }

        $this->printOut(0, '', $res['data']);
	}
}
?>