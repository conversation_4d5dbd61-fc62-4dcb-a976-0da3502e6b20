<?
class setLotteryPropsInfoAction extends Util_RecordAction {
    protected $strAmisGroup = 'meizi';
    protected $strAmisPerm = 'meizituan';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    protected $arrStrRequired = array(
        'gift_pic',
    );

    protected $arrIntRequired = array(
    );

    protected $arrOtherParam = array(
        'gift_list'
    );

    /**
     * @brief 基本数据
     * @param errno,errmsg,data
     * @return:  0.
     **/
    public function _execute() {
        $arrInput = $this->getInput();
        $arrRet = Tieba_Service::call('marriage', 'setLotteryPropsInfo', $arrInput, null, null,'post','php','utf-8');
        if (!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call marriage::addPropsInfo fail. [input = %s] [ouput = %s]",
                serialize($arrInput), serialize($arrRet)));
            $this->printOut(1, '设置抽奖道具信息失败');
            return;
        }

        $this->printOut(0, '设置抽奖道具成功');
    }
}
?>