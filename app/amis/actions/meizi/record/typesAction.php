<?
class typesAction extends Util_Action {
	protected $strAmisGroup = 'meizi';
    protected $strAmisPerm = 'meizituan';
    protected $bolOnlyAccessAmis = true;
    protected $bolOnlyInner = false;

    protected $arrOtherParam = array(
    );

    public function _execute() {
        $map = Util_RecordDefine::$map;

        $out = array();
        foreach ($map as $key => $value) {
            $out[] = array(
                'value' => $key,
                'label' => $value,
            );
        }

        $this->printOut(0, '', $out);
	}
}
?>