<?
class listAction extends Util_Action {
	protected $strAmisGroup = 'meizi';
    protected $strAmisPerm = 'meizituan';
    protected $bolOnlyAccessAmis = true;
    protected $bolOnlyInner = false;

    protected $arrOtherParam = array(
        'status',
        'pn',
        'time_start',
        'time_end',
        'user_name',
        'record_type',
    );

    public function _execute() {
    	$arrInput = $this->getInput();

    	$arrInput['pn'] = intval($arrInput['pn']) > 0 ? intval($arrInput['pn']) : 1;
        $arrInput['rn'] = intval($arrInput['rn']) > 0 ? intval($arrInput['rn']) : 30;

    	$res   = Tieba_Service::call('marriage', 'getRecordListByStatus', $arrInput, null, null, 'post', 'php', 'utf-8');

		if (!$res || Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            Bingo_Log::warning(sprintf("call user::getRecordListByStatus fail. [input = %s] [ouput = %s]",
                serialize($arrInput), serialize($res)));
            $this->printOut(1, '获取记录失败');
            return;
        }

        unset($res['data']['has_more']);

        $rows = $res['data']['rows'];

        $rows = Util_Util::getUserNames($rows);

        if ($rows === false) {
            return $this->printOut(1, '获取用户昵称失败');
        }

        foreach ($rows as $key => &$value) {
            $value['record_type_str'] = Util_RecordDefine::$map[intval($value['record_type'])];
            $value['tpl'] = Util_RecordDefine::getMessage($value['record_type'], $value);
        }

        $res['data']['rows'] = $rows;

        $this->printOut(0, '', $res['data']);
	}
}
?>