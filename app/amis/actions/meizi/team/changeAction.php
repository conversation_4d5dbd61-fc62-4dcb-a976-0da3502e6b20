<?
class changeAction extends Util_RecordAction {
    protected $strAmisGroup = 'meizi';
    protected $strAmisPerm = 'meizituan:admin';
    protected $bolOnlyAccessAmis = true;
    protected $bolOnlyInner = false;
    public $intRecordType = Util_RecordDefine::TEAM_CHANGE;

    protected $arrStrRequired = array(
        'team_name',
        'user_name',
    );

    protected $arrIntRequired = array(
        'user_id',
        'team_tuan_index',
        'team_dui_index',
        'job',
    );

    protected $arrOtherParam = array(
        'comment',
    );

    /**
     * @brief 基本数据
     * @param errno,errmsg,data
     * @return:  0.
     **/
    public function _execute() {
        $arrInput = $this->getInput();

        // 通过
        $input = array(
            'team_name' => $arrInput['team_name'],
            'team_tuan_index' => $arrInput['team_tuan_index'],
            'team_dui_index' => $arrInput['team_dui_index'],
        );

        $team_id = Util_Util::getTeamIdByInfo($input);

        if ($team_id === false) {
            return $this->printOut(1, '获取团队信息失败');
        }

        // 删除这个人在原来团队里面的角色
        if (!Util_Util::deleteUserManageRole($arrInput['user_id'])) {
            return $this->printOut(1, '删除用户在原来的团队中的角色失败');
        }

        $job = $arrInput['job'];

        $arrInput['team_id'] = $team_id;

        $arrInput['status'] = 1;

        $arrRet = Tieba_Service::call('marriage', 'handleUser', $arrInput, null, null,'post','php','utf-8');
        if (!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call marriage::handleUser fail. [input = %s] [ouput = %s]",
                serialize($arrInput), serialize($arrRet)));
            $this->printOut(1, '操作失败');
            return;
        }

        // 团长
        if ($job == 1) {
            $tuan_id = Util_Util::getTuanIdByInfo($input);

            if ($tuan_id === false) {
                $this->printOut(1, '获取团ID信息失败');
                return false;
            }

            $input = array(
                'team_id' => $tuan_id,
                'team_manage_uid' => $arrInput['user_id'],
            );
            $arrRet = Tieba_Service::call('marriage', 'updateTeam', $input, null, null,'post','php','utf-8');
            if (!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
                Bingo_Log::warning(sprintf("call marriage::updateTeam fail. [input = %s] [ouput = %s]",
                    serialize($input), serialize($arrRet)));
                $this->printOut(1, '任命团长失败');
                return false;
            }
        } elseif ($job == 2) {
            // 队长
            $input = array(
                'team_id' => $team_id,
                'team_manage_uid' => $arrInput['user_id'],
            );
            $arrRet = Tieba_Service::call('marriage', 'updateTeam', $input, null, null,'post','php','utf-8');
            if (!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
                Bingo_Log::warning(sprintf("call marriage::updateTeam fail. [input = %s] [ouput = %s]",
                    serialize($input), serialize($arrRet)));
                $this->printOut(1, '任命队长失败');
                return false;
            }
        }

        $this->printOut(0, '操作成功');
    }
}
?>