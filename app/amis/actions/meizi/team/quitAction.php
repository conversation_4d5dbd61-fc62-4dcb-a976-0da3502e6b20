<?
class quitAction extends Util_RecordAction {
    protected $strAmisGroup = 'meizi';
    protected $strAmisPerm = 'meizituan:admin';
    protected $bolOnlyAccessAmis = true;
    protected $bolOnlyInner = false;
    public $intRecordType = Util_RecordDefine::TEAM_QUIT;
    public $strRecordKey1 = '${team_id}';

    protected $arrStrRequired = array(
        'user_name',
    );

    protected $arrIntRequired = array(
        'user_id',
        'team_id',
    );

    protected $arrOtherParam = array(
        'comment',
    );

    /**
     * [getInput description]
     * @return [type] [description]
     */
    public function getInput() {
        $arrInput = parent::getInput();

        $team = Util_Util::getTeamById($arrInput['team_id']);

        if ($team) {
            $arrInput = array_merge($arrInput, array(
                'team_name' => $team['team_name'],
                'team_tuan_index' => $team['team_tuan_index'],
                'team_dui_index' => $team['team_dui_index'],
            ));
        }

        return $arrInput;
    }

    /**
     * @brief 基本数据
     * @param errno,errmsg,data
     * @return:  0.
     **/
    public function _execute() {
        $arrInput = $this->getInput();

        $arrRet = Tieba_Service::call('marriage', 'deleteUserTeam', $arrInput, null, null,'post','php','utf-8');
        if (!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call marriage::deleteUserTeam fail. [input = %s] [ouput = %s]",
                serialize($arrInput), serialize($arrRet)));
            $this->printOut(1, '操作失败');
            return;
        }

        // 删除这个人在原来团队里面的角色
        if (!Util_Util::deleteUserManageRole($arrInput['user_id'])) {
            if (!Util_Util::deleteUserManageRole($arrInput['user_id'])) {
                return $this->printOut(1, '删除用户在原来的团队中的角色失败');
            }
        }

        $this->printOut(0, '操作成功');
    }
}
?>