<?
class setStatusAction extends Util_RecordAction {
    protected $strAmisGroup = 'meizi';
    protected $strAmisPerm = 'meizituan';
    protected $bolOnlyAccessAmis = true;
    protected $bolOnlyInner = false;
    public $intRecordType = Util_RecordDefine::PROPS_SET_STATUS;

    protected $arrStrRequired = array(
    );

    protected $arrIntRequired = array(
        'status',
        'props_id'
    );

    protected $arrOtherParam = array(
        'props_name',
    );

    /**
     * @brief 基本数据
     * @param errno,errmsg,data
     * @return:  0.
     **/
    public function _execute() {
        $arrInput = $this->getInput();

        $arrRet = Tieba_Service::call('marriage', 'editPropsInfo', $arrInput, null, null,'post','php','utf-8');
        if (!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call marriage::editPropsInfo fail. [input = %s] [ouput = %s]",
                serialize($arrInput), serialize($arrRet)));
            $this->printOut(1, '修改状态失败');
            return;
        }

        $this->printOut(0, '修改状态成功');
    }
}
?>