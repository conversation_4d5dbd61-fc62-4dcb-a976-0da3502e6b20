<?
class editAction extends Util_RecordAction {
    protected $strAmisGroup = 'meizi';
    protected $strAmisPerm = 'meizituan';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;
    public $intRecordType = Util_RecordDefine::PROPS_EDIT;

    protected $arrStrRequired = array(
    );

    protected $arrIntRequired = array(
        'props_id'
    );

    protected $arrOtherParam = array(
        'intro',
        'pic',
        'props_name',
        'left_num',
        'icon_name',
        'icon_level',
        'icon_interval',
        'is_icon',
    );

    /**
     * @brief 基本数据
     * @param errno,errmsg,data
     * @return:  0.
     **/
    public function _execute() {
        $arrInput = $this->getInput();
        if ( isset($arrInput['is_icon']) && 'true' == $arrInput['is_icon'] ){
            $arrInput['other_info']['is_icon'] = 1;
            $arrInput['other_info']['icon_name'] = $arrInput['icon_name'];
            $arrInput['other_info']['icon_level'] = $arrInput['icon_level'];
            $arrInput['other_info']['icon_interval'] = $arrInput['icon_interval'];
        }

        $arrRet = Tieba_Service::call('marriage', 'editPropsInfo', $arrInput, null, null,'post','php','utf-8');
        if (!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call marriage::editPropsInfo fail. [input = %s] [ouput = %s]",
                serialize($arrInput), serialize($arrRet)));
            $this->printOut(0, '修改失败');
            return;
        }

        $this->printOut(1, '修改成功');
    }
}
?>