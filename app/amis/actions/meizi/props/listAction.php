<?
class listAction extends Util_Action {
	protected $strAmisGroup = 'meizi';
    protected $strAmisPerm = 'meizituan';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    protected $arrOtherParam = array(
        'status',
        'pn',
    );

    public function _execute() {
    	$arrInput = $this->getInput();

    	$arrInput['pn'] = intval($arrInput['pn']) > 0 ? intval($arrInput['pn']) : 1;
        $arrInput['rn'] = intval($arrInput['rn']) > 0 ? intval($arrInput['rn']) : 30;

    	$res   = Tieba_Service::call('marriage', 'getPropsListByStatus', $arrInput, null, null, 'post', 'php', 'utf-8');

		if (!$res || Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            Bingo_Log::warning(sprintf("call user::getPropsListByStatus fail. [input = %s] [ouput = %s]",
                serialize($arrInput), serialize($res)));
            $this->printOut(1, '获取商品列表失败');
            return;
        }

        unset($res['data']['has_more']);

        $this->printOut(0, '', $res['data']);
	}
}
?>