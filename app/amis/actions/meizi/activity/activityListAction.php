<?
class activityListAction extends Util_Action {
	protected $strAmisGroup = 'meizi';
    protected $strAmisPerm = 'meizituan:admin';
    protected $bolOnlyAccessAmis = true;
    protected $bolOnlyInner = false;

    protected $arrOtherParam = array(
        'status',
        'pn',
    );

    public function _execute() {
    	$arrInput = $this->getInput();

    	$arrInput['pn'] = intval($arrInput['pn']) > 0 ? intval($arrInput['pn']) : 1;

    	$input['limit'] = 30;
    	$input['offset'] = ($arrInput['pn'] - 1) * 30;

    	$res   = Tieba_Service::call('marriage', 'getActivityListByStatus', $input, null, null, 'post', 'php', 'utf-8');

		if (!$res || Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            Bingo_Log::warning(sprintf("call user::getUserTeamList fail. [input = %s] [ouput = %s]",
                serialize($input), serialize($res)));
            $this->printOut(1, '获取活动列表失败');
            return;
        }
        $res['data']['rows'] = $res['data']['activity_list'];
        foreach($res['data']['rows'] as &$item){
            if ($item['status'] == 0) {
                continue;
            }
            if ($item['end_time'] < time()){
                $item['status'] = 2;
            } elseif ($item['begin_time'] > time()) {
                $item['status'] = 4;
            }
        }

        $this->printOut(0, '', $res['data']);
	}
}
?>