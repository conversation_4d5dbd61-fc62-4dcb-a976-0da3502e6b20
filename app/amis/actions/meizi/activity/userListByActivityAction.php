<?
class userListByActivityAction extends Util_Action {
	protected $strAmisGroup = 'meizi';
    protected $strAmisPerm = 'meizituan:admin';
    protected $bolOnlyAccessAmis = true;
    protected $bolOnlyInner = false;

    protected $arrIntRequired = array(
        'activity_id',
    );

    protected $arrOtherParam = array(
        'pn',
    );

    public function _execute() {
    	$arrInput = $this->getInput();

    	$arrInput['pn'] = intval($arrInput['pn']) > 0 ? intval($arrInput['pn']) : 1;

    	$limit = 30;
    	$offset = ($arrInput['pn'] - 1) * 30;

        $input  = array(
            'cond'  => array(
                'activity_id'   => $arrInput['activity_id'],
            ),
            'append'    => " limit $offset , $limit",
        );

    	$res   = Tieba_Service::call('marriage', 'getUserActivityListByCond', $input, null, null, 'post', 'php', 'utf-8');

		if (!$res || Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            Bingo_Log::warning(sprintf("call user::getUserActivityListByCond fail. [input = %s] [ouput = %s]",
                serialize($input), serialize($res)));
            $this->printOut(1, '获取活动列表失败');
            return;
        }

        $this->printOut(0, '', $res['data']);
	}
}
?>