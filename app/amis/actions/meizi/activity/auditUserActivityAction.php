<?
class auditUserActivityAction extends Util_RecordAction {
    protected $strAmisGroup = 'meizi';
    protected $strAmisPerm = 'meizituan:admin';
    protected $bolOnlyAccessAmis = true;
    protected $bolOnlyInner = false;
    public $intRecordType = Util_RecordDefine::ACTIVITY_AUDIT;

    protected $arrStrRequired = array(
    );

    protected $arrIntRequired = array(
        'id',
        'raw_status',
    );

    protected $arrOtherParam = array(
        'reason',
    );

    /**
     * @brief 基本数据
     * @param errno,errmsg,data
     * @return:  0.
     **/
    public function _execute() {
        $arrInput = $this->getInput();
        $arrRet = Tieba_Service::call('marriage', 'auditUserActivityList', $arrInput, null, null,'post','php','utf-8');
        if (!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call marriage::auditUserActivityList fail. [input = %s] [ouput = %s]",
                serialize($arrInput), serialize($arrRet)));
            $this->printOut(1, '审核用户任务失败');
            return;
        }

		$this->printOut(0, '审核提交成功');
    }
}
?>
