<?php
/**
 *************************************************************************
 *
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [searchAction.php 报价]
 * <AUTHOR>
 * @DateTime 18/7/5 11:46
 * @brief
 */

class searchAction extends Util_Action {
    protected $strAmisGroup = 'resource';
    protected $strAmisPerm = 'admin:order';

    /**
     * @param
     * @return array
     */
    public function _execute() {
        $page = Bingo_Http_Request::get('page', 1);
        $perPage = Bingo_Http_Request::get('perPage', 20);

        $combineId = Bingo_Http_Request::get('combine_id', 0);
        $resourceName = Bingo_Http_Request::get('resource_name', '');
        $type = Bingo_Http_Request::get('type', -1);
        $payType = Bingo_Http_Request::get('pay_type', -1);
        $status = Bingo_Http_Request::get('status', -1);
        $quoteName = Bingo_Http_Request::get('quote_name', '');
        $quoteBeginTime = Bingo_Http_Request::get('quote_begin_time', 0);
        $quoteEndTime = Bingo_Http_Request::get('quote_end_time', 0);


        $arrInput = array();
        $arrInput['page'] = $page;
        $arrInput['perPage'] = $perPage;
        $combineId > 0 && $arrInput['combine_id'] = $combineId;
        ($type >= 0) && $arrInput['type'] = $type;
        ($payType >= 0) && $arrInput['pay_type'] = $payType;
        ($status >= 0) && $arrInput['status'] = $status;
        !empty($resourceName) && $arrInput['resource_name'] = $resourceName;
        !empty($quoteName) && $arrInput['quote_name'] = $quoteName;
        ($quoteBeginTime > 0) && $arrInput['quote_begin_time'] = $quoteBeginTime;
        ($quoteEndTime > $quoteBeginTime) && $arrInput['quote_end_time'] = $quoteEndTime;


        Bingo_Log::trace("input: " . serialize($arrInput));
        $arrRes = Tieba_Service::call('resource2', 'getOrderList', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        Bingo_Log::debug("output: " . serialize($arrRes));
        if (empty($arrRes) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call service failed, input=' . serialize($arrInput));
            return $this->_retJson(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $rows = $arrRes['data'];


        $arrRes = Tieba_Service::call('resource2', 'getOrderCount', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        Bingo_Log::debug("output: " . serialize($arrRes));
        if (empty($arrRes) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call service failed, input=' . serialize($arrInput));
            return $this->_retJson(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $data['rows'] = $rows;
        $data['count'] = $arrRes['data'];
        return $this->_retJson(Tieba_Errcode::ERR_SUCCESS, $data);
    }

    /**
     * [_retJson json格式返回数据]
     * @param
     * @param
     * @return
     */
    protected function _retJson($errno = Tieba_Errcode::ERR_SUCCESS, $data = array()){
        $this->_displayJson(array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        ));
    }

    /**
     * [_displayJson 按照 json 格式输出]
     * @param
     * @return
     */
    protected function _displayJson($arrValue = array()){
        if(!empty($arrValue)){
            foreach($arrValue as $strKey => $mixValue){
                Bingo_Page::assign($strKey, $mixValue);
            }
        }

        Bingo_Page::setOnlyDataType("json");
        Bingo_Http_Response::contextType('text/json', BINGO_ENCODE_LANG);
    }
}
