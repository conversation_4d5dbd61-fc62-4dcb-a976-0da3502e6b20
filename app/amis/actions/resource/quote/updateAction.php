<?php
/**
 *************************************************************************
 *
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [updateAction.php]
 * <AUTHOR>
 * @DateTime 18/7/11 11:46
 * @brief
 */

class updateAction extends Util_Action {
    protected $strAmisGroup = 'resource';
    protected $strAmisPerm = 'admin:order';

    /**
     * @param
     * @return array
     */
    public function _execute() {
        $orderId = Bingo_Http_Request::get('order_id', 0);
        $version = Bingo_Http_Request::get('version', -1);
        $type = Bingo_Http_Request::get('type', -1);
        $payType = Bingo_Http_Request::get('pay_type', '');
        $arrDate = Bingo_Http_Request::get('multi_date', '');
        $arrDimension = Bingo_Http_Request::get('multi_dimension', '');

        if ($orderId <= 0 || $version < 0 || empty($arrDate) || empty($arrDimension)) {
            Bingo_Log::warning("perem error: " . serialize(Bingo_Http_Request::getPostAll()));
            return $this->_retJson(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrResource[0]['resource_name'] = Bingo_Http_Request::get('resource_name', 0);
        $arrResource[0]['platform'] = Bingo_Http_Request::get('platform', '');
        $arrResource[0]['page_type'] = Bingo_Http_Request::get('page_type', '');
        $arrResource[0]['style_id'] = Bingo_Http_Request::get('style_id', 0);
        $arrResource[0]['premium'] = Bingo_Http_Request::get('premium', 0);

        $arrResource = $this->buildResourceId($arrResource);
        if (empty($arrResource)) {
            Bingo_Log::warning('get resource id failed, input=' . serialize($arrResource));
            return $this->_retJson(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrInput = array();
        $arrInput['order_id'] = $orderId;
        $arrInput['type'] = $type;
        $arrInput['pay_type'] = $payType;
        $arrInput['version'] = $version + 1;
        $arrInput['status'] = Util_Resource_Const::QUOTE_STATUS_QUOTE;
        $arrInput['quote_name'] = $this->getOpUserName();
        $arrInput['multi_resource'] = $arrResource;
        $arrInput['multi_date'] = $arrDate;
        $arrInput['multi_dimension'] = $arrDimension;

        Bingo_Log::trace("input: " . serialize($arrInput));
        $arrRes = Tieba_Service::call('resource2', 'updateOrder', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        Bingo_Log::debug("output: " . serialize($arrRes));
        if (empty($arrRes)) {
            Bingo_Log::fatal('call service failed, input=' . serialize($arrInput));
            return $this->_retJson(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        if ($arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call service failed, input=' . serialize($arrInput) . 'output=' . serialize($arrRes));
        }

        return $this->_displayJson($arrRes);
    }


    /**
     * @param
     * @return array | bool
     */
    protected function buildResourceId($arrResource) {
        $arrResourceName = array();
        foreach ($arrResource as $item) {
            $arrResourceName[] = $item['resource_name'];
        }

        $arrInput = array(
            'resource_name' => $arrResourceName,
        );
        $arrRes = Tieba_Service::call('resource2', 'getResourceIdByNames', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        if (empty($arrRes) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || empty($arrRes['data'])) {
            Bingo_Log::warning('call service failed, input=' . serialize($arrInput) . 'output=' . serialize($arrRes));
            return false;
        }

        $arrResourceId = $arrRes['data'];
        foreach ($arrResource as $key => $item) {
            $resourceName = $item['resource_name'];
            $arrResource[$key]['resource_id'] = $arrResourceId[$resourceName];
        }

        return $arrResource;
    }


    /**
     * [_retJson json格式返回数据]
     * @param
     * @param
     * @return
     */
    protected function _retJson($errno = Tieba_Errcode::ERR_SUCCESS, $data = array()){
        $this->_displayJson(array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        ));
    }

    /**
     * [_displayJson 按照 json 格式输出]
     * @param
     * @return
     */
    protected function _displayJson($arrValue = array()){
        if(!empty($arrValue)){
            foreach($arrValue as $strKey => $mixValue){
                Bingo_Page::assign($strKey, $mixValue);
            }
        }

        Bingo_Page::setOnlyDataType("json");
        Bingo_Http_Response::contextType('text/json', BINGO_ENCODE_LANG);
    }
}
