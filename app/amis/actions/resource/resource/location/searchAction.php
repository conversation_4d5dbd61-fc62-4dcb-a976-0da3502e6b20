<?php
/**
 *************************************************************************
 *
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [searchAction.php 角色api]
 * <AUTHOR>
 * @DateTime 18/7/5 11:46
 * @brief
 */

class searchAction extends Util_Action {
    protected $strAmisGroup = 'resource';
    protected $strAmisPerm = 'admin:resource';

    /**
     * @param
     * @return array
     */
    public function _execute() {
        $page = Bingo_Http_Request::get('page', 1);
        $perPage = Bingo_Http_Request::get('perPage', 20);

        $resourceId = Bingo_Http_Request::get('resource_id', 0);
        $resourceName = Bingo_Http_Request::get('resource_name', '');
        $platform = Bingo_Http_Request::get('platform', -1);
        $pageType = Bingo_Http_Request::get('page_type', -1);
        $opName = Bingo_Http_Request::get('op_name', '');
        $beginTime = Bingo_Http_Request::get('begin_time', 0);
        $endTime = Bingo_Http_Request::get('end_time', 0);

        $arrInput = array();
        $arrInput['page'] = $page;
        $arrInput['perPage'] = $perPage;
        $resourceId > 0 && $arrInput['resource_id'] = $resourceId;
        !empty($resourceName) && $arrInput['resource_name'] = $resourceName;
        !empty($opName) && $arrInput['op_name'] = $opName;
        ($platform >= 0) && $arrInput['platform'] = $platform;
        ($pageType >= 0) && $arrInput['page_type'] = $pageType;
        ($beginTime>0) && $arrInput['begin_time'] = $beginTime;
        ($endTime > $beginTime) && $arrInput['end_time'] = $endTime;

        Bingo_Log::trace("input: " . serialize($arrInput));
        $arrRes = Tieba_Service::call('resource2', 'getResourceList', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        Bingo_Log::debug("output: " . serialize($arrRes));
        if (empty($arrRes) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call service failed, input=' . serialize($arrInput));
            return $this->_retJson(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrResource = array();
        foreach ($arrRes['data'] as $row) {
            $arrResource[$row['resource_id']][] = $row;
        }

        $arrMerge = array();
        foreach ($arrResource as $rid => $item) {
            $merge = Lib_Resource_Array::merge($item);
            if (is_array($merge['id'])) {
                $merge['id'] = $merge['id'][0];
                $merge['children'] = $item;
            }
            if (is_array($merge['platform'])) {
                $merge['platform'] = implode(',', $merge['platform']);
            }
            if (is_array($merge['page_type'])) {
                $merge['page_type'] = implode(',', $merge['page_type']);
            }

            $arrMerge[$rid] = $merge;
        }

        $arrRes = Tieba_Service::call('resource2', 'getResourceCount', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        Bingo_Log::debug("output: " . serialize($arrRes));
        if (empty($arrRes) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call service failed, input=' . serialize($arrInput));
            return $this->_retJson(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $data['rows'] = array_values($arrMerge);
        $data['count'] = $arrRes['data'];

        return $this->_retJson(Tieba_Errcode::ERR_SUCCESS, $data);
    }

    /**
     * [_retJson json格式返回数据]
     * @param
     * @param
     * @return
     */
    protected function _retJson($errno = Tieba_Errcode::ERR_SUCCESS, $data = array()){
        $this->_displayJson(array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        ));
    }

    /**
     * [_displayJson 按照 json 格式输出]
     * @param
     * @return
     */
    protected function _displayJson($arrValue = array()){
        if(!empty($arrValue)){
            foreach($arrValue as $strKey => $mixValue){
                Bingo_Page::assign($strKey, $mixValue);
            }
        }

        Bingo_Page::setOnlyDataType("json");
        Bingo_Http_Response::contextType('text/json', BINGO_ENCODE_LANG);
    }
}
