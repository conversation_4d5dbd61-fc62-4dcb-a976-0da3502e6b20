<?php
/**
 *************************************************************************
 *
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [createAction.php 样式api]
 * <AUTHOR>
 * @DateTime 18/7/5 11:46
 * @brief
 */

class createAction extends Util_Action {
    protected $strAmisGroup = 'resource';
    protected $strAmisPerm = 'admin:resource';

    /**
     * @param
     * @return array
     */
    public function _execute() {
        $name = Bingo_Http_Request::get('name', '');
        $imgurl = Bingo_Http_Request::get('imgurl', '');
        $field = Bingo_Http_Request::get('field', '');
        if (empty($name) || empty($imgurl) || empty($field)) {
            Bingo_Log::warning("perem error: " . serialize(Bingo_Http_Request::getPostAll()));
            return $this->_retJson(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrInput = array();
        $arrInput['name'] = $name;
        $arrInput['imgurl'] = $imgurl;
        $arrInput['field'] = $field;
        $arrInput['op_name'] = $this->getOpUserName();

        Bingo_Log::trace("input: " . serialize($arrInput));
        $arrRes = Tieba_Service::call('resource2', 'addStyle', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        Bingo_Log::debug("output: " . serialize($arrRes));
        if (empty($arrRes) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call service failed, input=' . serialize($arrInput) . 'output=' . serialize($arrRes));
        }

        return $this->_displayJson($arrRes);
    }


    /**
     * [_retJson json格式返回数据]
     * @param
     * @param
     * @return
     */
    protected function _retJson($errno = Tieba_Errcode::ERR_SUCCESS, $data = array()){
        $this->_displayJson(array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        ));
    }

    /**
     * [_displayJson 按照 json 格式输出]
     * @param
     * @return
     */
    protected function _displayJson($arrValue = array()){
        if(!empty($arrValue)){
            foreach($arrValue as $strKey => $mixValue){
                Bingo_Page::assign($strKey, $mixValue);
            }
        }

        Bingo_Page::setOnlyDataType("json");
        Bingo_Http_Response::contextType('text/json', BINGO_ENCODE_LANG);
    }
}
