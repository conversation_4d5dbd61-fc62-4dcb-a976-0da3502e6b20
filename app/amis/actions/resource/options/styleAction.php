<?php
/**
 *************************************************************************
 *
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [styleAction.php]
 * <AUTHOR>
 * @DateTime 18/7/5 11:46
 * @brief
 */

class styleAction extends Util_Action {

    /**
     * @param
     * @return array
     */
    public function _execute() {
        $styleId = Bingo_Http_Request::get('style_id', 0);
        if ($styleId <= 0) {
            Bingo_Log::warning("perem error: " . serialize(Bingo_Http_Request::getGetAll()));
            return $this->_retJson(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrInput = array(
            'style_id' => $styleId,
        );
        Bingo_Log::trace("input: " . serialize($arrInput));
        $arrRes = Tieba_Service::call('resource2', 'getStyleFieldById', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        Bingo_Log::debug("output: " . serialize($arrRes));
        if (empty($arrRes) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call service failed, input=' . serialize($arrInput) . 'output=' . serialize($arrRes));
            return $this->_retJson(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return $this->_displayJson($arrRes);
    }

    /**
     * [_retJson json格式返回数据]
     * @param
     * @param
     * @return
     */
    protected function _retJson($errno = Tieba_Errcode::ERR_SUCCESS, $data = array()){
        $this->_displayJson(array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        ));
    }

    /**
     * [_displayJson 按照 json 格式输出]
     * @param
     * @return
     */
    protected function _displayJson($arrValue = array()){
        if(!empty($arrValue)){
            foreach($arrValue as $strKey => $mixValue){
                Bingo_Page::assign($strKey, $mixValue);
            }
        }

        Bingo_Page::setOnlyDataType("json");
        Bingo_Http_Response::contextType('text/json', BINGO_ENCODE_LANG);
    }
}
