<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [getFirstDirAction.php]
 * <AUTHOR>
 * @DateTime 18/3/12 23:42
 * @brief
 */

class getFirstDirAction extends Util_Action {

    /**
     * [run description]
     * @param
     * @return array
     */
    public function _execute() {
        $ret = Tieba_Service::call('forum', 'getAllDir', null, null, null, 'post', 'php', 'utf-8');
        if ($ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("get all dir failed, output: " . serialize($ret));
            return $this->_retJson(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $options = array();
        foreach ($ret['output']['all_dir'] as $item) {
            $options[] = array(
                'label' => $item['level_1_name'],
                'value' => $item['level_1_name'],
            );
        }
        $data['options'] = $options;
        $this->_retJson(Tieba_Errcode::ERR_SUCCESS, $data);
    }

    /**
     * [_retJson json格式返回数据]
     * @param
     * @param
     * @return
     */
    protected function _retJson($errno = Tieba_Errcode::ERR_SUCCESS, $data = array()){
        $this->_displayJson(array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        ));
    }

    /**
     * [_displayJson 按照 json 格式输出]
     * @param
     * @return
     */
    protected function _displayJson($arrValue = array()){
        if(!empty($arrValue)){
            foreach($arrValue as $strKey => $mixValue){
                Bingo_Page::assign($strKey, $mixValue);
            }
        }

        Bingo_Page::setOnlyDataType("json");
        Bingo_Http_Response::contextType('text/json', BINGO_ENCODE_LANG);
    }
}
