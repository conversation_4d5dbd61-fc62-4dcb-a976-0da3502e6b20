<?php
/**
 *************************************************************************
 *
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [getCommitListAction.php]
 * <AUTHOR>
 * @DateTime 18/7/19 17:46
 * @brief
 */

class getCommitListAction extends Util_Action {
    protected $strAmisGroup = 'resource';
    protected $strAmisPerm = 'admin:commit';

    /**
     * @param
     * @return array
     */
    public function _execute() {
        $page = Bingo_Http_Request::get('page', 1);
        $perPage = Bingo_Http_Request::get('perPage', 20);

        $combineId = Bingo_Http_Request::get('combine_id', 0);
        $type = Bingo_Http_Request::get('type', -1);
        $commitName = Bingo_Http_Request::get('commit_name', '');
        $commitBeginTime = Bingo_Http_Request::get('commit_begin_time', 0);
        $commitEndTime = Bingo_Http_Request::get('commit_end_time', 0);
        $status = Bingo_Http_Request::get('status', -1);

        $arrInput = array();
        $arrInput['page'] = $page;
        $arrInput['perPage'] = $perPage;
        $arrInput['status'] = Util_Resource_Const::$materialCommitStatus;
        $combineId > 0 && $arrInput['combine_id'] = $combineId;
        ($type >= 0) && $arrInput['type'] = $type;
        !empty($resourceName) && $arrInput['resource_name'] = $resourceName;
        !empty($commitName) && $arrInput['commit_name'] = $commitName;
        ($commitBeginTime>0) && $arrInput['commit_begin_time'] = $commitBeginTime;
        ($commitEndTime > $commitBeginTime) && $arrInput['commit_end_time'] = $commitEndTime;
        ($status >= 0) && $arrInput['status'] = $status;

        Bingo_Log::trace("input: " . serialize($arrInput));
        $arrRes = Tieba_Service::call('resource2', 'getOrderList', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        Bingo_Log::debug("output: " . serialize($arrRes));
        if (empty($arrRes) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call service failed, input=' . serialize($arrInput));
            return $this->_retJson(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $rows = $arrRes['data'];
        foreach ($rows as $key => $item) {
            $arrChildren = array();

            $startTime = 0;
            $endTime = 0;
            foreach ($item['show_time'] as $arrDate) {
                $children = $item;
                $children['start_show_time'] = $arrDate['start_time'];
                $children['end_show_time'] = $arrDate['end_time'];
                $arrChildren[] = $children;

                if ($arrDate['start_time'] < $startTime || $startTime == 0) {
                    $startTime = $arrDate['start_time'];
                }

                if ($arrDate['end_time'] > $endTime) {
                    $endTime = $arrDate['end_time'];
                }
            }

            $rows[$key] = $arrChildren[0];
            $rows[$key]['start_show_time'] = $startTime;
            $rows[$key]['end_show_time'] = $endTime;

            if ( count($arrChildren) > 1) {
                $rows[$key]['children'] = $arrChildren;
            }
        }

        $arrRes = Tieba_Service::call('resource2', 'getOrderCount', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        Bingo_Log::debug("output: " . serialize($arrRes));
        if (empty($arrRes) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call service failed, input=' . serialize($arrInput));
            return $this->_retJson(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $data['rows'] = $rows;
        $data['count'] = $arrRes['data'];
        return $this->_retJson(Tieba_Errcode::ERR_SUCCESS, $data);
    }

    /**
     * [_retJson json格式返回数据]
     * @param
     * @param
     * @return
     */
    protected function _retJson($errno = Tieba_Errcode::ERR_SUCCESS, $data = array()){
        $this->_displayJson(array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        ));
    }

    /**
     * [_displayJson 按照 json 格式输出]
     * @param
     * @return
     */
    protected function _displayJson($arrValue = array()){
        if(!empty($arrValue)){
            foreach($arrValue as $strKey => $mixValue){
                Bingo_Page::assign($strKey, $mixValue);
            }
        }

        Bingo_Page::setOnlyDataType("json");
        Bingo_Http_Response::contextType('text/json', BINGO_ENCODE_LANG);
    }

}
