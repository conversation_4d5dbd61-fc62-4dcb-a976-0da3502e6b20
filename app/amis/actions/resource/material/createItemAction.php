<?php
/**
 *************************************************************************
 *
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [createItemAction.php]
 * <AUTHOR>
 * @DateTime 18/7/5 11:46
 * @brief
 */

class createItemAction extends Util_Action {
    protected $strAmisGroup = 'resource';
    protected $strAmisPerm = 'admin:commit';

    /**
     * @param
     * @return array
     */
    public function _execute() {
        $orderId = Bingo_Http_Request::get('order_id', 0);
        $styleId = Bingo_Http_Request::get('style_id', 0);
        $adName = Bingo_Http_Request::get('ad_name', '');
        $dimension = Bingo_Http_Request::get('dimension', -1);
        $date = Bingo_Http_Request::get('date', '');
        $arrDate = explode(',', $date);

        if ($orderId <= 0 || $styleId <= 0 || empty($adName) || $dimension < 0 || count($arrDate) != 2) {
            Bingo_Log::warning("perem error: get=" . serialize(Bingo_Http_Request::getGetAll()));
            Bingo_Log::warning("perem error: post=" . serialize(Bingo_Http_Request::getPostAll()));
            return $this->_retJson(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $params = array(
            'style_id' => $styleId,
        );
        $arrRes = Tieba_Service::call('resource2', 'getStyleFieldById', $params, NULL, NULL, 'post', 'php', 'utf-8');
        if (empty($arrRes) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call service failed, input=' . serialize($params) . 'output=' . serialize($arrRes));
            return $this->_retJson(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrField = array();
        foreach ($arrRes['data']['controls'] as $item) {
            $arrField[] = $item['name'];
        }

        $data = array();
        foreach ($arrField as $field) {
            $value = Bingo_Http_Request::get($field, false);
            if ($value === false) {
                Bingo_Log::warning("perem error: get $field failed" . serialize(Bingo_Http_Request::getPostAll()));
                return $this->_retJson(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $data[$field] = $value;
        }

        $arrInput = array();
        $arrInput['order_id'] = $orderId;
        $arrInput['dimension'] = $dimension;
        $arrInput['start_time'] = $arrDate[0];
        $arrInput['end_time'] = $arrDate[1];

        $arrInput['name'] = $adName;
        $arrInput['data'] = json_encode($data);
        $arrInput['status'] = Util_Resource_Const::QUOTE_STATUS_COMMIT;
        $arrInput['commit_name'] = $this->getOpUserName();
        Bingo_Log::trace("input: " . serialize($arrInput));
        $arrRes = Tieba_Service::call('resource2', 'addMaterial', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        Bingo_Log::debug("output: " . serialize($arrRes));
        if (empty($arrRes) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call service failed, input=' . serialize($arrInput) . 'output=' . serialize($arrRes));
        }

        return $this->_displayJson($arrRes);
    }

    /**
     * [_retJson json格式返回数据]
     * @param
     * @param
     * @return
     */
    protected function _retJson($errno = Tieba_Errcode::ERR_SUCCESS, $data = array()){
        $this->_displayJson(array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        ));
    }

    /**
     * [_displayJson 按照 json 格式输出]
     * @param
     * @return
     */
    protected function _displayJson($arrValue = array()){
        if(!empty($arrValue)){
            foreach($arrValue as $strKey => $mixValue){
                Bingo_Page::assign($strKey, $mixValue);
            }
        }

        Bingo_Page::setOnlyDataType("json");
        Bingo_Http_Response::contextType('text/json', BINGO_ENCODE_LANG);
    }
}
