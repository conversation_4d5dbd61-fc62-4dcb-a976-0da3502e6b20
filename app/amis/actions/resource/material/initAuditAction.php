<?php
/**
 *************************************************************************
 *
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [initAuditAction.php]
 * <AUTHOR>
 * @DateTime 18/7/5 11:46
 * @brief
 */

class initAuditAction extends Util_Action {
    protected $strAmisGroup = 'resource';
    protected $strAmisPerm = 'admin:audit';

    /**
     * @param
     * @return array
     */
    public function _execute() {
        $orderId = Bingo_Http_Request::get('order_id', 0);
        $styleId = Bingo_Http_Request::get('style_id', 0);
        if ($orderId <= 0 || $styleId <= 0) {
            Bingo_Log::warning("perem error: " . serialize(Bingo_Http_Request::getGetAll()));
            return $this->_retJson(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrInput = array(
            'style_id' => $styleId,
        );
        Bingo_Log::trace("input: " . serialize($arrInput));
        $arrRes = Tieba_Service::call('resource2', 'getStyleFieldById', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        Bingo_Log::debug("output: " . serialize($arrRes));
        if (empty($arrRes) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call service failed, input=' . serialize($arrInput));
            return $this->_retJson(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrStyle = $arrRes['data'];

        $arrInput = array(
            'order_id' => $orderId,
        );
        Bingo_Log::trace("input: " . serialize($arrInput));
        $arrRes = Tieba_Service::call('resource2', 'getMaterialByOrderId', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        Bingo_Log::debug("output: " . serialize($arrRes));
        if (empty($arrRes) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call service failed, input=' . serialize($arrInput));
            return $this->_retJson(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        Bingo_Log::notice('lninl: ' . serialize($arrRes));

        $arrControls = array();
        $arrControls['controls']['type'] = "tabs";
        foreach ($arrRes['data'] as $key => $item) {
            $arrData = $item['data'];
            $arrMaterial = array();
            $arrMaterial[] = array(
                'type' => 'text',
                'label' => '物料ID',
                'name' => "material_id_$key",
                'value' => $item['id'],
                'hidden' => true,
                'disabled' => true,
            );
            $arrMaterial[] = array(
                'type' => 'text',
                'label' => '广告名',
                'name' => "ad_name_$key",
                'value' => $item['name'],
                'disabled' => true,
                'required' => true,
            );
            foreach ($arrStyle['controls'] as $style) {
                if ($style['type'] == 'image' || $style['type'] == 'url') {
                    $style['value'] = 'raw:' . $arrData[$style['name']];
                } else {
                    $style['value'] = $arrData[$style['name']];
                }
                $style['name'] = $style['name'] . '_' . $key;
                $style['disabled'] = true;
                $arrMaterial[] = $style;
            }

            $arrControls['controls']['tabs'][] = array(
                'title' => "物料$key",
                'controls' => $arrMaterial
            );
        }

        //$this->_setXssSafe(false);
        return $this->_retJson(Tieba_Errcode::ERR_SUCCESS, $arrControls);
    }

    /**
     * [_retJson json格式返回数据]
     * @param
     * @param
     * @return
     */
    protected function _retJson($errno = Tieba_Errcode::ERR_SUCCESS, $data = array()){
        $this->_displayJson(array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        ));
    }

    /**
     * [_displayJson 按照 json 格式输出]
     * @param
     * @return
     */
    protected function _displayJson($arrValue = array()){
        if(!empty($arrValue)){
            foreach($arrValue as $strKey => $mixValue){
                Bingo_Page::assign($strKey, $mixValue);
            }
        }

        Bingo_Page::setOnlyDataType("json");
        Bingo_Http_Response::contextType('text/json', BINGO_ENCODE_LANG);
    }

}
