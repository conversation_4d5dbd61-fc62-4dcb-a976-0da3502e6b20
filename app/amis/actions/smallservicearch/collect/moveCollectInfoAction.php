<?php
/***************************************************************************
 * 
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file moveCollectInfoAction.php
 * <AUTHOR>
 * @date 2018/09/28 10:52:22
 * @brief 
 *  
 **/


class moveCollectInfoAction extends Util_Action{

    /**
     * 主逻辑
     * @param
     * @return
     */
    public function _execute() {
        $intId          = Bingo_Http_Request::getNoXssSafe('id', 0);
        $intStatus      = Bingo_Http_Request::getNoXssSafe('status', 0);

        $intOpUid       = intval(Util_User::$intUserId);
        $strOpUname     = strval(Util_User::$strUserName);

        // 参数校验
        if ($intOpUid <= 0 || $intId <= 0) {
            Bingo_Log::warning('moveCollectInfoAction input param is invalid.opuid:[' . $intOpUid . '],id[' . $intId . ']');
            $this->_jsonRet(Tieba_Errcode::ERR_MO_PARAM_INVALID);
            return true;
        }

        $input = array(
            'id'            => $intId,
            'op_uid'        => $intOpUid,
            'op_uname'      => $strOpUname,
            'mis_status'    => $intStatus,
        );
        $output = Tieba_Service::call('smallservicearch', 'setCollectData', $input, null, null, 'post', 'php', 'utf-8');
        if ($output['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call smallservicearch:setCollectData fail. input[" . serialize($input) . "],output[" . serialize($output) . "]");
            return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 返回整理
     * @param
     * @return
     */
    protected function _jsonRet($errno, $arrData=array()){
        $arrRet = array(
            'no'    => $errno,
            'error' => Molib_Util_Encode::convertGBKToUTF8(Tieba_Errcode::$usercodes[$errno]),
            'data'  => $arrData,
        );
        echo json_encode($arrRet);
    }
}





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
