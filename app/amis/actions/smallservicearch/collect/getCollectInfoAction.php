<?php
/***************************************************************************
 * 
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file getCollectInfoAction.php
 * <AUTHOR>
 * @date 2018/09/28 10:52:22
 * @brief 
 *  
 **/


class getCollectInfoAction extends Util_Action{

    /**
     * 主逻辑
     * @param
     * @return
     */
    public function _execute() {
        $strMisStatus   = Bingo_Http_Request::getNoXssSafe('status', 0);
        $intServiceId   = Bingo_Http_Request::getNoXssSafe('service_id', 0);

        $intOpUid       = intval(Util_User::$intUserId);
        $strOpUname     = strval(Util_User::$strUserName);

        // 参数校验
        if ($intOpUid <= 0) {
            Bingo_Log::warning('getCollectInfoAction input param is invalid.opuid:[' . $intOpUid . ']');
            $this->_jsonRet(Tieba_Errcode::ERR_MO_PARAM_INVALID);
            return true;
        }

        $input = array(
            'mis_status' => $strMisStatus,
        );
        if (!empty($intServiceId)) {
            $input['service_id'] = intval($intServiceId);
        }
        $output = Tieba_Service::call('smallservicearch', 'getCollectData', $input, null, null, 'post', 'php', 'utf-8');
        if ($output['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call smallservicearch:getCollectData fail. input[" . serialize($input) . "],output[" . serialize($output) . "]");
            return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrRet = array();
        foreach ($output['data'] as $data) {
            $arrRet['rows'][] = $data;
        }
        $arrRet['count'] = count($output['data']);

        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }

    /**
     * 返回整理
     * @param
     * @return
     */
    protected function _jsonRet($errno, $arrData=array()){
        $arrRet = array(
            'no'    => $errno,
            'error' => Molib_Util_Encode::convertGBKToUTF8(Tieba_Errcode::$usercodes[$errno]),
            'data'  => $arrData,
        );
        echo json_encode($arrRet);
    }
}





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
