<?php
/***************************************************************************
 * 
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file addCollectInfoAction.php
 * <AUTHOR>
 * @date 2018/09/28 10:52:22
 * @brief 
 *  
 **/


class addCollectInfoAction extends Util_Action{

    // mis状态
    const MIS_STATUS_DEFAULT        = 0; // 表示mis是处于默认状态（正常显示状态）
    const MIS_STATUS_MOVE           = 1; // 表示mis是处于回收站状态
    const MIS_STATUS_DELETE         = 2; // 表示mis是处于可删除状态（可能用不上该状态）


    // 定义业务id
    const SERVICE_ID_FOR_DEFAULT            = 10000;
    const SERVICE_ID_FOR_TIEBA_TOOL         = 1;
    const SERVICE_ID_FOR_FORUM_MASTER       = 2;
    const SERVICE_ID_FOR_UEG                = 3;
    const SERVICE_ID_FOR_USER_CONSUME       = 4;
    const SERVICE_ID_FOR_USER_UP            = 5;
    const SERVICE_ID_FOR_TIMELINESS_OPER    = 6;
    const SERVICE_ID_FOR_TIEBA_GAME         = 7;
    const SERVICE_ID_FOR_VIDEO              = 8;

    // 根据id获取业务介绍信息
    public static $serviceinfo = array(
        1       => '贴吧工具',
        2       => 'UEG',
        3       => '吧主',
        4       => '用户消费',
        5       => '用户增长',
        6       => '时效性运营',
        7       => '贴吧游戏',
        8       => '视频',
        10000   => '其他',
    );

    /**
     * 主逻辑
     * @param
     * @return
     */
    public function _execute() {
        $strMisName         = Bingo_Http_Request::getNoXssSafe('mis_name', '');
        $strMisUrl          = Bingo_Http_Request::getNoXssSafe('mis_url', '');
        $strMisPic          = Bingo_Http_Request::getNoXssSafe('mis_pic', '');
        $strMisIntro        = Bingo_Http_Request::getNoXssSafe('mis_intro', '');
        $intServiceId       = Bingo_Http_Request::getNoXssSafe('service_id', 0);

        $intOpUid       = intval(Util_User::$intUserId);
        $strOpUname     = strval(Util_User::$strUserName);

        // 参数校验
        if ($intOpUid <= 0 || empty($strMisName) || empty($strMisUrl)) {
            Bingo_Log::warning('addCollectInfoAction input param is invalid.opuid:[' . $intOpUid . '],mis_name[' . $strMisName . '],mis_url[' . $strMisUrl . ']');
            $this->_jsonRet(Tieba_Errcode::ERR_MO_PARAM_INVALID);
            return true;
        }

        $input = array(
            'mis_name'      => $strMisName,
            'mis_url'       => $strMisUrl,
            'create_uid'    => $intOpUid,
            'create_uname'  => $strOpUname,
        );
        if (!empty($strMisPic)) {
            $input['mis_pic'] = $strMisPic;
        }
        if (!empty($strMisIntro)) {
            $input['mis_intro'] = $strMisIntro;
        }
        if (!empty($intServiceId)) {
            $input['service_id']    = $intServiceId;
            $input['service_intro'] = self::$serviceinfo[$intServiceId];
        }
        $output = Tieba_Service::call('smallservicearch', 'addCollectData', $input, null, null, 'post', 'php', 'utf-8');
        if ($output['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call smallservicearch:addCollectData fail. input[" . serialize($input) . "],output[" . serialize($output) . "]");
            return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 返回整理
     * @param
     * @return
     */
    protected function _jsonRet($errno, $arrData=array()){
        $arrRet = array(
            'no'    => $errno,
            'error' => Molib_Util_Encode::convertGBKToUTF8(Tieba_Errcode::$usercodes[$errno]),
            'data'  => $arrData,
        );
        echo json_encode($arrRet);
    }
}





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
