<?php
/***************************************************************************
 * 
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file setVersionConfAction.php
 * <AUTHOR>
 * @date 2018/07/29 12:12:13
 * @brief 版本配置修改接口
 *  
 **/

class setVersionConfAction extends Util_Action{

    /**
     * 主逻辑
     * @param
     * @return
     */
    public function _execute()
    {
        $intId              = Bingo_Http_Request::getNoXssSafe('id', 0);
        $intClientType      = Bingo_Http_Request::getNoXssSafe('client_type', -1);
        $strClientVersion   = Bingo_Http_Request::getNoXssSafe('version', '');
        $intStatus          = Bingo_Http_Request::getNoXssSafe('status', 0);
        $strValue           = Bingo_Http_Request::getNoXssSafe('value', '');
        $strIntro           = Bingo_Http_Request::getNoXssSafe('intro', '');
        $strExtInfo         = Bingo_Http_Request::getNoXssSafe('ext_info', '');
        $intOpUid           = intval(Util_User::$intUserId);;
        $strOpUname         = strval(Util_User::$strUserName);

        // 参数校验
        if ($intOpUid <= 0 || empty($intId)) {
            Bingo_Log::warning('setVersionConfAction input param is invalid.opuid:[' . $intOpUid . '],id[' . $intId. ']');
            $this->_jsonRet(Tieba_Errcode::ERR_MO_PARAM_INVALID);
            return true;
        }

        // 修改配置
        $input = array(
            'id'        => intval($intId),
            'op_uid'    => $intOpUid,
            'op_uname'  => $strOpUname,
        );
        if ($intClientType == 0 || $intClientType == 1 || $intClientType == 2) {
            $input['client_type'] = intval($intClientType);
        }
        if (!empty($intStatus)) {
            $input['status'] = strval($intStatus);
        }
        if (!empty($strClientVersion)) {
            $input['version'] = strval($strClientVersion);
        }
        if (!empty($strValue)) {
            $input['value'] = strval($strValue);
        }
        if (!empty($strIntro)) {
            $input['intro'] = strval($strIntro);
        }
        if (!empty($strExtInfo)) {
            $input['ext_info'] = strval($strExtInfo);
        }
        $output = Tieba_Service::call('smallservicearch', 'updateVersionConf', $input, null, null, 'post', 'php', 'utf-8');
        if ($output == false || $output['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call smallservicearch:updateVersionConf fail. input[" . serialize($input) . "],output[" . serialize($output) . "]");
            return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 返回整理
     * @param
     * @return
     */
    protected function _jsonRet($errno, $arrData=array()){
        $arrRet = array(
            'no'    => $errno,
            'error' => Molib_Util_Encode::convertGBKToUTF8(Tieba_Errcode::$usercodes[$errno]),
            'data'  => $arrData,
        );
        echo json_encode($arrRet);
    }
}






/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
