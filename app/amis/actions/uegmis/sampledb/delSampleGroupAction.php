<?php

/**
 * Del Sample Group
 * Class delSampleGroupAction
 */
class delSampleGroupAction extends Util_Action
{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;
    private static $_id;
    private static $_user;
    private static $_business_ib;

    public function _execute()
    {
        self::$_id      = intval(Bingo_Http_Request::get('group_id'));
        self::$_business_ib     = intval(Bingo_Http_Request::get('business_id'));
        self::$_user    = $_SERVER['HTTP_AMIS_USER'];

        $arrInput           = array();
        $arrInput['id']     = self::$_id;
        $arrInput['user']   = self::$_user;
        $arrInput['business_id'] = self::$_business_ib;
        $arrInput['status'] = 2;//1:normal 2:delete

        $res            = Tieba_Service::call('uegmis', 'editSampleGroup', $arrInput, null, null, 'post', null, 'utf-8');
        echo json_encode($res);
    }
}