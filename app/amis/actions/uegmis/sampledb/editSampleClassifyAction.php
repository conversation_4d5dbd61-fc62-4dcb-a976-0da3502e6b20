<?php

/**
 * Edit Sample Classify
 * Class editSampleClassifyAction
 */
class editSampleClassifyAction extends Util_Action
{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    private static $_user;
    private static $_id;
    private static $_name;

    public function _execute()
    {
        self::$_user = $_SERVER['HTTP_AMIS_USER'];
        self::$_id   = intval(Bingo_Http_Request::get('classify_id'));
        self::$_name = Bingo_Http_Request::get('name');

        $arrInput         = array();
        $arrInput['user'] = self::$_user;
        $arrInput['id']   = self::$_id;
        $arrInput['name'] = self::$_name;

        $arrQuery         = array();
        $arrQuery['name'] = self::$_name;

        //判断是否已存在
        $arrJudge = Tieba_Service::call('uegmis', 'getSampleClassifyDetail', $arrQuery, null, null, 'post', null, 'utf-8');
        if (empty($arrJudge['ret']['rows']) || !isset($arrJudge['ret']['rows'])) {
            $arrRes    = Tieba_Service::call('uegmis', 'editSampleClassify', $arrInput, null, null, 'post', null, 'utf-8');
            $arrOutput = $arrRes['ret'];
            echo json_encode($arrOutput);
        } else {
            $ret           = array();
            $ret['errmsg'] = "该分类已存在。";
            $ret['errno']  = 1;
            echo json_encode($ret);
        }
    }
}