<?php

/**
 * Edit Sample Group
 * Class editSampleGroupAction
 */
class editSampleGroupAction extends Util_Action
{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    private static $_user;
    private static $_name;
    private static $_group_id;
    private static $_business_id;
    private static $_ids;


    public function _execute()
    {
        self::$_user        = $_SERVER['HTTP_AMIS_USER'];
        self::$_business_id = intval(Bingo_Http_Request::get('business_id'));
        self::$_group_id    = intval(Bingo_Http_Request::get('group_id'));
        self::$_name        = Bingo_Http_Request::get('name');
        self::$_ids         = trim(Bingo_Http_Request::get('ids'));

        $arrInput                = array();
        $arrInput['id']          = self::$_group_id;
        $arrInput['user']        = self::$_user;
        $arrInput['name']        = self::$_name;
        $arrInput['ids']         = self::$_ids;

        $arrQuery                = array();
        $arrQuery['name']        = self::$_name;
        $arrInput['business_id'] = self::$_business_id;

        //是否修改样本组优先级
        if(empty($arrInput['ids'])){
            //判断样本组名是否已存在
            $arrJudge = Tieba_Service::call('uegmis', 'getSampleGroupDetail', $arrQuery, null, null, 'post', null, 'utf-8');
            if (empty($arrJudge['ret']['rows']) || !isset($arrJudge['ret']['rows'])) {
                $arrRes    = Tieba_Service::call('uegmis', 'editSampleGroup', $arrInput, null, null, 'post', null, 'utf-8');
                $arrOutput = $arrRes['ret'];
                echo json_encode($arrOutput);
            } else {
                $ret           = array();
                $ret['errmsg'] = "该业务下样本组已存在。";
                $ret['errno']  = 1;
                echo json_encode($ret);
            }
        }else{
            $arrRes    = Tieba_Service::call('uegmis', 'editSampleGroup', $arrInput, null, null, 'post', null, 'utf-8');
            $arrOutput = $arrRes['ret'];
            echo json_encode($arrOutput);
        }
    }
}