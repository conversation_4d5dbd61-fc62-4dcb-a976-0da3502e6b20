<?php

/**
 * Get Sample Role Info
 * Class initSampleRoleAction
 */
class initSampleRoleAction extends Util_Action
{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;
    private static $_id;
    private static $_user;

    public function _execute()
    {
        self::$_id   = intval(Bingo_Http_Request::get('role_id'));
        self::$_user = $_SERVER['HTTP_AMIS_USER'];

        $arrInput       = array();
        $arrInput['id'] = self::$_id;
        $res            = Tieba_Service::call('uegmis', 'getSampleRoleDetail', $arrInput, null, null, 'post', null, 'utf-8');
        $ret            = array();
        $ret['errmsg']  = $res['ret']['error'];
        $ret['errno']   = $res['ret']['no'];
        $ret['data']    = array();
        foreach ($res['ret']['rows'] as &$item) {
            $item['create_time'] = date("Y-m-d H:i:s",$item['create_time']);
            $item['edit_time'] = date("Y-m-d H:i:s",$item['edit_time']);
            $item['power_list'] = unserialize($item['power_list']);
        }
        $ret['data'] = $res['ret']['rows'][0];
        echo json_encode($ret);
    }
}