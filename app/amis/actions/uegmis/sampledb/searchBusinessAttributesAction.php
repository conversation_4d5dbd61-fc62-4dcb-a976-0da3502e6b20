<?php

/**
 * Search Business Attributes
 * Class searchBusinessAttributesAction
 */
class searchBusinessAttributesAction extends Util_Action
{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;
    private static $_name;
    private static $_pn;
    private static $_rn;

    public function _execute()
    {
        self::$_name = trim(Bingo_Http_Request::get('name'));
        self::$_pn   = intval(Bingo_Http_Request::get('pn'));
        self::$_rn   = intval(Bingo_Http_Request::get('rn'));
        $arrInput    = array();
        if (!empty(self::$_name)) {
            $arrInput['name'] = self::$_name;
        }
        $arrInput['pn'] = self::$_pn;
        $arrInput['rn'] = self::$_rn;

        //权限校验
        $arrPowerInput              = array();
        $arrPowerInput['user_name'] = $_SERVER['HTTP_AMIS_USER'];
        $arrResultPower             = Tieba_Service::call('uegmis', 'getSampleUserRole', $arrPowerInput, null, null, 'post', null, 'utf-8');
        if ($arrResultPower === false) {
            $ret           = array();
            $ret['errmsg'] = "获取权限失败。";
            $ret['errno']  = 1;
            echo json_encode($ret);
            die();
        }

        $intIsAdmin       = 0;
        $arrRole          = array();
        foreach ($arrResultPower['ret']['rows'] as $key => $item) {
            if ($item['is_admin'] == 1) {
                $intIsAdmin = 1;
            }
            $arrRole[$item['business_id']] = unserialize($item['power_list']);
        }

        if ($intIsAdmin == 0) {
            $arrInput['business'] = array();
            foreach ($arrResultPower['ret']['rows'] as $key => $item) {
                $arrInput['business'][] = $item['business_id'];
            }
            sort($arrInput['business']);
            if ((!empty($arrInput['pn'])) && (!empty($arrInput['rn']))) {
                $intBegin             = (self::$_pn - 1) * self::$_rn;
                $intEnd               = self::$_rn;
                $arrInput['business'] = array_slice($arrInput['business'], $intBegin, $intEnd);
            }
        }

        //获取所有业务属性
        $res           = Tieba_Service::call('uegmis', 'getBusinessAttributes', $arrInput, null, null, 'post', null, 'utf-8');
        $ret           = array();
        $ret['errmsg'] = $res['ret']['error'];
        $ret['errno']  = $res['ret']['no'];
        $ret['data']   = array();
        foreach ($res['ret']['rows'] as $key => $value) {
            $arrTemp                  = array();
            $arrTemp['value']         = $value['id'];
            $arrTemp['label']         = $value['name'];
            $ret['data']['options'][] = $arrTemp;
        }
        $ret['data']['is_admin'] = $intIsAdmin;
        $ret['data']['role']     = $arrRole;
        echo json_encode($ret);
        die();
    }
}
