<?php

/**
 * Created by PhpStorm.
 * User: v_luliqiang
 * Date: 2020-06-16
 * Time: 13:00
 */

class getSampleBookListAction extends Util_Action
{

    private static $_business_id;
    private static $_type;
    private static $_key_word;
    private static $_status;
    private static $_classify_id;
    private static $_tag_id;
    private static $_confidence_value;
    private static $_sample_group_id;
    private static $_creator;
    private static $_create_start_time;
    private static $_create_end_time;
    private static $_pagenum;
    private static $_shownum;

    private static $_intDownLoadFlag;
    private static $_selectDownLoad;
    private static $_beginDownLoad;
    private static $_endDownLoad;
    private static $_user;


    public function _execute()
    {
        self::$_business_id = intval(Bingo_Http_Request::get('business_id'));
        //英文名type=1,中文名type=2,备注关键词type=3
        self::$_type     = intval(Bingo_Http_Request::get('type'));
        self::$_key_word = trim(Bingo_Http_Request::get('key_word'));
        //1正常2删除
        self::$_status            = intval(Bingo_Http_Request::get('status'));
        self::$_classify_id       = intval(Bingo_Http_Request::get('classify_id'));
        self::$_tag_id            = intval(Bingo_Http_Request::get('tag_id'));
        self::$_confidence_value  = intval(Bingo_Http_Request::get('confidence_value'));
        self::$_sample_group_id   = intval(Bingo_Http_Request::get('sample_group_id'));
        self::$_creator           = trim(Bingo_Http_Request::get('creator'));
        self::$_create_start_time = intval(Bingo_Http_Request::get('create_start_time'));
        self::$_create_end_time   = intval(Bingo_Http_Request::get('create_end_time'));
        self::$_pagenum           = intval(Bingo_Http_Request::get('pn', 1));
        self::$_shownum           = intval(Bingo_Http_Request::get('rn', 20));

        self::$_intDownLoadFlag = intval(Bingo_Http_Request::get('intDownLoadFlag', 0));
        self::$_selectDownLoad  = intval(Bingo_Http_Request::get('selectDownLoad', 0));
        self::$_beginDownLoad   = intval(Bingo_Http_Request::get('beginDownLoad', 1));
        self::$_endDownLoad     = intval(Bingo_Http_Request::get('endDownLoad', 1));
        self::$_user            = $_SERVER['HTTP_AMIS_USER'];

        //权限校验
        $arrPowerInput['user_name'] = self::$_user;
        $arrResultPower             = Tieba_Service::call('uegmis', 'getSampleUserRole', $arrPowerInput, null, null, 'post', null, 'utf-8');

        if ($arrResultPower === false) {
            $ret           = array();
            $ret['errmsg'] = "获取权限失败。";
            $ret['errno']  = 1;
            echo json_encode($ret);
            die();
        } elseif (empty($arrResultPower['ret']['rows'])) {
            $ret           = array();
            $ret['errmsg'] = "no power";
            $ret['errno']  = 1;
            echo json_encode($ret);
            die();
        }

        $intIsAdmin = 0;
        $arrRole    = array();
        foreach ($arrResultPower['ret']['rows'] as $key => $item) {
            if ($item['is_admin'] == 1) {
                $intIsAdmin = 1;
            }
            $arrRole[$item['business_id']] = unserialize($item['power_list']);
        }

        $arrInput = array();
        if (self::$_business_id > 0) {
            $arrInput['business_id'] = self::$_business_id;
        }
        //关键词(模糊匹配)
        switch (self::$_type) {
            case 1:
                if (!empty(self::$_key_word)) {
                    $arrInput['table_name'] = self::$_key_word;
                }
                break;
            case 2:
                if (!empty(self::$_key_word)) {
                    $arrInput['name'] = self::$_key_word;
                }
                break;
            case 3:
                if (!empty(self::$_key_word)) {
                    //样本备注
                    $arrInput['remarks'] = self::$_key_word;
                }
                break;
        }
        if (self::$_status > 0) {
            $arrInput['status'] = self::$_status;
        }
        if (self::$_classify_id > 0) {
            $arrInput['classify_id'] = self::$_classify_id;
        }
        if (self::$_tag_id > 0) {
            $arrInput['tag_id'] = self::$_tag_id;
        }

        if ($intIsAdmin == 0 && self::$_business_id <= 0) {
            $arrInput['business'] = array();
            foreach ($arrResultPower['ret']['rows'] as $key => $item) {
                $arrInput['business'][] = $item['business_id'];
            }
            sort($arrInput['business']);
        }

        //置信度(相等匹配)
        if (self::$_confidence_value > 0) {
            $confidenceValue = self::_getConfidenceValue(self::$_confidence_value);
            if (empty($confidenceValue)) {
                Bingo_Log::warning('confidence_value is error' . self::$_confidence_value);
            } else {
                $arrInput['confidence_value'] = $confidenceValue['key'] . '_' . $confidenceValue['sign'] . '_' . $confidenceValue['value'];
            }
        }
        if (self::$_sample_group_id > 0) {
            $groupInput = array(
                'group_id' => self::$_sample_group_id,
            );
            //根据group_id查询出符合条件的样本ids
            $groupOut = Tieba_Service::call('uegmis', 'getSampleByGroupId', $groupInput, null, null, 'post', null, 'utf-8');
            if ($groupOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("getGroupByIds get  error" . serialize($groupOut));
                echo json_encode(self::_errRet($groupOut['errno'], $groupOut['errmsg']));
                die;
            }
            if (empty($groupOut['ret'])) {
                Bingo_Log::warning("getSampleByGroupId get error" . serialize($groupOut));
                $res['rows']  = array();
                $res['count'] = 0;
                echo json_encode(self::_succRet($res));
                die;
            }
            $ids = array();
            foreach ($groupOut['ret'] as $k => $v) {
                $ids[] = $v['sample_id'];
            }
            $ids = array_unique($ids);
            $arrInput['ids'] = implode(',', $ids);
        }
        //创建人(相等匹配)
        if (!empty(self::$_creator)) {
            $arrInput['creator'] = self::$_creator;
        }
        if (self::$_create_start_time > 0) {
            $arrInput['start_time'] = self::$_create_start_time;
        }
        if (self::$_create_end_time > 0) {
            $arrInput['end_time'] = self::$_create_end_time;
        }
        if (self::$_selectDownLoad != 1) {
            $arrInput['pn'] = self::$_pagenum;
            $arrInput['rn'] = self::$_shownum;
        } else {
            $arrInput['selectDownLoad'] = self::$_selectDownLoad;
            $arrInput['beginDownLoad']  = self::$_beginDownLoad;
            $arrInput['endDownLoad']    = self::$_endDownLoad;
        }
        //获取样本列表
        $sampleList = Tieba_Service::call('uegmis', 'getSampleBookList', $arrInput, null, null, 'post', null, 'utf-8');
        if ($sampleList['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("addSampleItemTable fail" . serialize($sampleList));
            echo json_encode(self::_errRet($sampleList['errno'], $sampleList['errmsg']));
            die;
        }
        //获取样本数量
        $sampleCount = Tieba_Service::call('uegmis', 'getSampleBookCount', $arrInput, null, null, 'post', null, 'utf-8');
        if ($sampleCount['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("getSampleBookCount fail" . serialize($sampleCount));
            echo json_encode(self::_errRet($sampleCount['errno'], $sampleCount['errmsg']));
            die;
        }
        $classifyArray = $tagArray = $groupArray = $businessArray = array();
        $classifyData  = $tagData = $groupData = $businessData = array();
        foreach ($sampleList['ret']['rows'] as $k => $v) {
            $classifyArray['ids'][] = $v['classify_id'];
            $tagArray['ids'][]      = $v['tag_id'];
            $groupArray['ids'][]    = $v['id'];
            $businessArray['ids'][] = $v['business_id'];
            //$sampleList['ret']['rows'][$k]['name'] = mb_convert_encoding($v['name'], 'gbk',array("ASCII",'UTF-8',"GB2312","GBK",'BIG5'));
        }
        if (!empty($businessArray['ids'])) {
            $businessArray['ids'] = array_unique($businessArray['ids']);
            //业务属性
            $business = Tieba_Service::call('uegmis', 'getBusinessById', $businessArray, null, null, 'post', null, 'utf-8');
            if ($business['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("getBusinessByIds get  error" . serialize($business));
                echo json_encode(self::_errRet($business['errno'], $business['errmsg']));
                die;
            }
            if (empty($business['ret'])) {
                Bingo_Log::warning("getBusinessByIds get error" . serialize($business));
            } else {
                foreach ($business['ret'] as $k => $v) {
                    $businessData[$v['id']] = $v['name'];
                }
            }

        }
        if (!empty($classifyArray['ids'])) {
            $classifyArray['ids'] = array_unique($classifyArray['ids']);
            //分类
            $classify = Tieba_Service::call('uegmis', 'getClassifyById', $classifyArray, null, null, 'post', null, 'utf-8');
            if ($classify['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("getClassifyByIds get  error" . serialize($classify));
                echo json_encode(self::_errRet($classify['errno'], $classify['errmsg']));
                die;
            }
            if (empty($classify['ret'])) {
                Bingo_Log::warning("getClassifyByIds get error" . serialize($classify));
            } else {
                foreach ($classify['ret'] as $k => $v) {
                    $classifyData[$v['id']] = $v['name'];
                }
            }
        }
        if (!empty($tagArray['ids'])) {
            $tagArray['ids'] = array_unique($tagArray['ids']);
            //标签
            $tag = Tieba_Service::call('uegmis', 'getTagById', $tagArray, null, null, 'post', null, 'utf-8');
            if ($tag['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("getTagByIds get  error" . serialize($tag));
                echo json_encode(self::_errRet($tag['errno'], $tag['errmsg']));
                die;
            }
            if (empty($tag['ret'])) {
                Bingo_Log::warning("getTagByIds get error" . serialize($tag));
            } else {
                foreach ($tag['ret'] as $k => $v) {
                    $tagData[$v['id']] = $v['name'];
                }
            }
        }
        if (!empty($groupArray['ids'])) {
            $groupArray['ids'] = array_unique($groupArray['ids']);
            //样本组
            $group = Tieba_Service::call('uegmis', 'getGroupById', $groupArray, null, null, 'post', null, 'utf-8');
            if ($group['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("getGroupByIds get  error" . serialize($group));
                echo json_encode(self::_errRet($group['errno'], $group['errmsg']));
                die;
            }
            if (empty($group['ret'])) {
                Bingo_Log::warning("getGroupByIds get error" . serialize($group));
            } else {
                foreach ($group['ret'] as $k => $v) {
                    $groupData[$v['sample_id']] = $v['name'];
                }
            }
        }
        //数据组合
        foreach ($sampleList['ret']['rows'] as $k => $v) {
            $sampleList['ret']['rows'][$k]['index']            = $k + 1;
            $sampleList['ret']['rows'][$k]['business_name']    = $businessData[$v['business_id']];
            $sampleList['ret']['rows'][$k]['classify_name']    = $classifyData[$v['classify_id']];
            $sampleList['ret']['rows'][$k]['tag_name']         = $tagData[$v['tag_id']];
            $sampleList['ret']['rows'][$k]['group_name']       = $groupData[$v['id']];
            $sampleList['ret']['rows'][$k]['create_time']      = $v['create_time'] == 0 ? 0 : date('Y-m-d H:i:s', $v['create_time']);
            $sampleList['ret']['rows'][$k]['edit_time']        = $v['edit_time'] == 0 ? 0 : date('Y-m-d H:i:s', $v['edit_time']);
            $confidence_value                                  = explode('_', $v['confidence_value']);
            $sign                                              = $confidence_value[1];
            $sampleList['ret']['rows'][$k]['confidence_value'] = $sign . $confidence_value[0];
        }

        //导出
        if (self::$_intDownLoadFlag == 1) {
            $arrData = self::_builDownLoadData($sampleList['ret']['rows']);
            return self::downLoadBigFile($arrData['head_line'], $arrData['data'], 'SampleBookList-' . date("YmdHi") . '.txt');
        }
        //固定返回格式
        $res['rows']     = $sampleList['ret']['rows'];
        $res['count']    = $sampleCount['ret']['rows'];
        $res['role']     = $arrRole;
        $res['is_admin'] = $intIsAdmin;
        echo json_encode(self::_succRet($res));
        die;
    }

    protected function _builDownLoadData($arrInput)
    {
        $arrHeadLine = array(
            'index',
            '中文名',
            '英文名',
            '匹配类型',
            '样本组',
            '分类',
            '标签',
            '业务属性',
            '样本量',
            '创建人',
            '创建时间',
            '备注',
        );
        $output      = array();
        foreach ($arrInput as $key => $value) {
            $output[] = array(
                $key + 1,
                $value['name'],
                $value['table_name'],
                $value['confidence_value'],
                $value['group_name'],
                $value['classify_name'],
                $value['tag_name'],
                $value['business_name'],
                $value['total'],
                $value['creator'],
                $value['create_time'],
                $value['remarks'],
            );
        }
        return array('head_line' => $arrHeadLine, 'data' => $output);
    }

    //下载文件
    protected function downLoadBigFile($arrHeadLine, $arrRowData, $strFileName = '', $delimiter = "\t", $strReplaceSymbol = ' ')
    {
        //1.检验数据格式
        foreach ($arrRowData as $rowKey => $rowData) {
            if (!is_array($rowData) || count($rowData) != count($arrHeadLine)) {
                Bingo_Log::warning('row data format error!!headLine:[' . serialize($arrHeadLine) . '] rowdate[' . serialize($rowData) . ']');
                return false;
            }
            foreach ($arrRowData as $itemKey => $item) {
                if (is_string($item)) {
                    $arrRowData[$rowKey][$itemKey] = str_replace($delimiter, $strReplaceSymbol, $item);
                }
            }
        }
        if (empty($strFileName)) {
            $strFileName = date("YmdHi") . ".txt";
        }
        header('Content-Description: File Transfer');
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $strFileName . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        $fp = fopen('php://output', 'a');//打开output流
        //mb_convert_variables('GBK', 'UTF-8', $arrHeadLine);//防止中文乱码
        fputcsv($fp, $arrHeadLine);//,$delimiter);//将数据格式化为CSV格式并写入到output流中
        foreach ($arrRowData as $key => $rowData) {
            $arrTempData = $rowData;
            //mb_convert_variables('GBK', 'UTF-8', $arrTempData);
            fputcsv($fp, $arrTempData);//,$delimiter);
            if ($key % 100 === 0) {
                //刷新输出缓冲到浏览器
                ob_flush();
                flush();//必须同时使用 ob_flush() 和flush() 函数来刷新输出缓冲。
            }
        }
        fclose($fp);
        return true;
    }

    private static function _getConfidenceValue($confidence_id)
    {
        $confidence_value = array(
            array('key' => '25', 'sign' => '≥', 'value' => '0.574'),
            array('key' => '35', 'sign' => '≥', 'value' => '0.662'),
            array('key' => '45', 'sign' => '≥', 'value' => '0.668'),
            array('key' => '55', 'sign' => '≥', 'value' => '0.71'),
            array('key' => '65', 'sign' => '≥', 'value' => '0.75'),
            array('key' => '75', 'sign' => '≥', 'value' => '0.79'),
            array('key' => '85', 'sign' => '≥', 'value' => '0.82'),
            array('key' => '95', 'sign' => '≥', 'value' => '0.85'),
            array('key' => '105', 'sign' => '≥', 'value' => '0.87'),
            array('key' => '115', 'sign' => '≥', 'value' => '0.89'),
            array('key' => '125', 'sign' => '≥', 'value' => '0.909'),
            array('key' => '135', 'sign' => '≥', 'value' => '0.924'),
            array('key' => '145', 'sign' => '≥', 'value' => '0.937'),
            array('key' => '155', 'sign' => '≥', 'value' => '0.948'),
            array('key' => '165', 'sign' => '≥', 'value' => '0.957'),
            array('key' => '175', 'sign' => '≥', 'value' => '0.964'),
            array('key' => '185', 'sign' => '≥', 'value' => '0.97'),
            array('key' => '195', 'sign' => '≥', 'value' => '0.976'),
            array('key' => '205', 'sign' => '≥', 'value' => '0.98'),
            array('key' => '215', 'sign' => '≥', 'value' => '0.984'),
            array('key' => '225', 'sign' => '≥', 'value' => '0.987'),
        );
        $new_confidence   = array();
        foreach ($confidence_value as $k => $v) {
            $new_confidence[$k + 1] = $v;
        }
        return $new_confidence[$confidence_id];
    }

    private static function _errRet($errno, $errmsg = '')
    {
        return array(
            'errno'  => $errno,
            'errmsg' => !empty($errmsg) ? $errmsg : Tieba_Error::getErrmsg($errno),
        );
    }

    private static function _succRet($ret = array())
    {
        $error = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data'   => $ret,
        );
    }
}