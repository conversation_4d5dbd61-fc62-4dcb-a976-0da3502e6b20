<?php

/**
 * Get Sample Tag Info
 * Class initSampleTagAction
 */
class initSampleTagAction extends Util_Action
{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;
    private static $_id;
    private static $_user;

    public function _execute()
    {
        self::$_id   = intval(Bingo_Http_Request::get('tag_id'));
        self::$_user = $_SERVER['HTTP_AMIS_USER'];

        $arrInput       = array();
        $arrInput['id'] = self::$_id;
        $res            = Tieba_Service::call('uegmis', 'getSampleTagDetail', $arrInput, null, null, 'post', null, 'utf-8');
        $ret            = array();
        $ret['errmsg']  = $res['ret']['error'];
        $ret['errno']   = $res['ret']['no'];
        $ret['data']    = array();
        $ret['data']    = $res['ret']['rows'][0];
        echo json_encode($ret);
    }
}