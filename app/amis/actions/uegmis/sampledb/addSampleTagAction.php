<?php

/**
 * Add Sample Tag
 * Class addSampleTagAction
 */
class addSampleTagAction extends Util_Action
{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    private static $_user;
    private static $_name;
    private static $_classify_id;

    public function _execute()
    {
        self::$_user        = $_SERVER['HTTP_AMIS_USER'];
        self::$_name        = trim(Bingo_Http_Request::get('name'));
        self::$_classify_id = intval(Bingo_Http_Request::get('classify_id'));

        $arrInput                = array();
        $arrInput['name']        = self::$_name;
        $arrInput['user']        = self::$_user;
        $arrInput['classify_id'] = self::$_classify_id;

        $arrQuery                = array();
        $arrQuery['name']        = self::$_name;

        //判断样本标签名是否已存在
        $arrJudge = Tieba_Service::call('uegmis', 'getSampleTagDetail', $arrQuery, null, null, 'post', null, 'utf-8');
        if (empty($arrJudge['ret']['rows']) || !isset($arrJudge['ret']['rows'])) {
            $arrRes    = Tieba_Service::call('uegmis', 'addSampleTag', $arrInput, null, null, 'post', null, 'utf-8');
            $arrOutput = $arrRes['ret'];
            echo json_encode($arrOutput);
        } else {
            $ret           = array();
            $ret['errmsg'] = "该标签已存在。";
            $ret['errno']  = 1;
            echo json_encode($ret);
        }
    }
}