<?php

/**
 * Created by PhpStorm.
 * User: v_luliqiang
 * Date: 2020-06-16
 * Time: 13:00
 */

class getSampleCreatorListAction extends Util_Action
{
    private static $_creator;
    private static $_user;


    public function _execute()
    {
        self::$_creator = trim(Bingo_Http_Request::get('creator'));
        self::$_user = $_SERVER['HTTP_AMIS_USER'];
        $res = array();
        if(!empty(self::$_creator)){
            $arrInput = array(
                'creator' => self::$_creator,
            );
            $creator = Tieba_Service::call('uegmis', 'getSampleCreatorList', $arrInput, null, null, 'post', null, 'utf-8');
            if($creator['errno']!==Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning("getSampleCreatorList fail".serialize($creator));
                echo json_encode(self::_errRet($creator['errno'],$creator['errmsg']));die;
            }
            $res = array();
            foreach ($creator['ret']['rows'] as $k => $v){
                $res['options'][] = array('value'=>$v['creator'],'label'=>$v['creator']);
            }

        }else{
            //固定返回格式
            $res['options'] = array();
        }
        echo json_encode(self::_succRet($res));die;

    }


    private static function _errRet($errno, $errmsg = '')
    {
        return array(
            'errno'  => $errno,
            'errmsg' => !empty($errmsg) ? $errmsg : Tieba_Error::getErrmsg($errno),
        );
    }

    private static function _succRet($ret = array())
    {
        $error = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $ret
        );
    }
}