<?php

/**
 * Del Sample Classify
 * Class delSampleClassifyAction
 */
class delSampleClassifyAction extends Util_Action
{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;
    private static $_id;
    private static $_user;

    public function _execute()
    {
        self::$_id   = intval(Bingo_Http_Request::get('classify_id'));
        self::$_user = $_SERVER['HTTP_AMIS_USER'];

        $arrInput           = array();
        $arrInput['id']     = self::$_id;
        $arrInput['user']   = self::$_user;
        $arrInput['status'] = 2;//1:normal 2:delete

        //权限校验
        $arrPowerInput['user_name'] = self::$_user;
        $arrResultPower             = Tieba_Service::call('uegmis', 'getSampleUserRole', $arrPowerInput, null, null, 'post', null, 'utf-8');
        if ($arrResultPower === false) {
            $ret           = array();
            $ret['errmsg'] = "获取权限失败。";
            $ret['errno']  = 1;
            echo json_encode($ret);
            die();
        } elseif (empty($arrResultPower['ret']['rows'])) {
            $ret           = array();
            $ret['errmsg'] = "no power";
            $ret['errno']  = 1;
            echo json_encode($ret);
            die();
        }

        $intIsAdmin = 0;
        foreach ($arrResultPower['ret']['rows'] as $key => $item) {
            if ($item['is_admin'] == 1) {
                $intIsAdmin = 1;
                break;
            }
        }

        if ($intIsAdmin == 0) {
            $ret           = array();
            $ret['errmsg'] = "非超级管理员，无法删除";
            $ret['errno']  = 1;
            echo json_encode($ret);
            die();
        }

        //检查是否存在生效样本
        $arrBookInput        = array(
            'classify_id' => self::$_id,
        );
        $arrStatisticsResult = Tieba_Service::call('uegmis', 'getSampleBookCount', $arrBookInput, null, null, 'post', null, 'utf-8');
        if ($arrStatisticsResult['ret']['rows'] > 0) {
            $ret           = array();
            $ret['errmsg'] = "请先删除样本后再进行分类删除";
            $ret['errno']  = 1;
            echo json_encode($ret);
            die();
        }

        $res = Tieba_Service::call('uegmis', 'editSampleClassify', $arrInput, null, null, 'post', null, 'utf-8');
        echo json_encode($res);
        die;
    }
}