<?php

/**
 * Created by PhpStorm.
 * User: v_luliqiang
 * Date: 2020-06-18
 * Time: 10:30
 */

class addTagToGroupAction extends Util_Action
{

    private static $_combination;
    private static $_group_id;
    private static $_punish_key;
    private static $_business_id;
    private static $_user;

    public function _execute()
    {
        //处罚匹配（标签id,是否命中（1,2),处罚方式）
        self::$_combination = Bingo_Http_Request::getNoXssSafe('combination');
        self::$_group_id = intval(Bingo_Http_Request::get('group_id'));
        self::$_business_id = intval(Bingo_Http_Request::get('business_id'));
        self::$_punish_key = trim(Bingo_Http_Request::getNoXssSafe('punish_key'));
        self::$_user = $_SERVER['HTTP_AMIS_USER'];


        if (empty(self::$_combination)) {
            echo json_encode(self::_errRet(1000, '标签组处罚数据不能为空'));
            die;
        }
        if (intval(self::$_group_id)<=0) {
            echo json_encode(self::_errRet(1000, '请选择正确的样本组'));
            die;
        }
        if (intval(self::$_business_id)<=0) {
            echo json_encode(self::_errRet(1000, '请选择正确的业务属性'));
            die;
        }
        if (empty(self::$_punish_key)) {
            echo json_encode(self::_errRet(1000, '标签组处罚key不能为空'));
            die;
        }
        //数据格式校验
        $combination = self::$_combination;
        if(!is_array($combination)){
            echo json_encode(self::_errRet(1000, '标签组处罚数据类型错误'));
            die;
        }else{
            $tagCombined = $tagCombinedRelation = $tagIds = array();
            foreach ($combination as $k => $v){
                if(!isset($v['tag_id'])||intval($v['tag_id'])<=0){
                    echo json_encode(self::_errRet(1000, '标签组处罚数据tag_id有误'));
                    die;
                }
                if(!isset($v['is_hit'])||!in_array(intval($v['is_hit']),array(1,2))){
                    echo json_encode(self::_errRet(1000, '标签组处罚数据is_hit有误'));
                    die;
                }
                //处罚记录表
                $tagCombined[$k]['tag_id'] = $v['tag_id'];
                $tagCombined[$k]['is_hit'] = $v['is_hit']==1?'命中':'不命中';
                //处罚记录关系表
                $tagCombinedRelation[$k]['tag_id'] = $v['tag_id'];
                $tagIds[] = $v['tag_id'];
            }
        }
        //根据sampleIds获取sample_name
        $tagInput = array(
            'ids' => implode(',',$tagIds),
        );
        $tagList = Tieba_Service::call('uegmis', 'getTagListByIds', $tagInput, null, null, 'post', null, 'utf-8');
        if($tagList['errno']!==Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("addSampleToGroup getTagListByIds fail".serialize($tagList));
            echo json_encode(self::_errRet($tagList['errno'],$tagList['errmsg']));die;
        }
        $tagMatch = array();
        foreach ($tagList['ret']['rows'] as $k => $v){
            $tagMatch[$v['id']] = $v['name'];
        }
        foreach ($tagCombined as $k => $v){
            $tagCombined[$k]['tag_name'] = $tagMatch[$v['tag_id']];
        }
        //根据业务属性获取处罚信息
        $businessInput = array(
            'business_id' => self::$_business_id,
        );
        $business = Tieba_Service::call('uegmis', 'getPunishByBusiness', $businessInput, null, null, 'post', null, 'utf-8');
        if($business['errno']!==Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("getBusinessByGroup get error".serialize($business));
            echo json_encode(self::_errRet($business['errno'],$business['errmsg']));die;
        }
        if(empty($business['ret']['rows'])){
            echo json_encode(self::_errRet(1000, '业务属性错误'));die;
        }else{
            $punishment = unserialize($business['ret']['rows'][0]['punishment']);
            if(empty($punishment)){
                //该业务属性下并未配置处罚信息
                $punishName = $punishKey = '';
            }else{
                foreach ($punishment as $k => $v){
                    if($v['key'] == self::$_punish_key){
                        $punishKey = self::$_punish_key;
                        $punishName = $v['name'];
                    }
                }
            }
        }
        //处罚信息匹配
        foreach ($tagCombined as $k => $v){
            $tagCombined[$k]['punish_name'] = $punishName;
            $tagCombined[$k]['punish_key'] = $punishKey;
        }
        //添加分组处罚记录
        $arrInput = array(
            'combination' => json_encode($tagCombined),
            'group_id' => self::$_group_id,
            'business_id' => self::$_business_id,
            'creator' => self::$_user,
        );
        $data = Tieba_Service::call('uegmis', 'addSampleGroupPunish', $arrInput, null, null, 'post', null, 'utf-8');
        if($data['errno']!==Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("addSampleGroupPunish get error".serialize($data));
            echo json_encode(self::_errRet($data['errno'],$data['errmsg']));die;
        }
        echo json_encode(self::_succRet());die;
    }


    private static function _errRet($errno, $errmsg = '')
    {
        return array(
            'errno'  => $errno,
            'errmsg' => !empty($errmsg) ? $errmsg : Tieba_Error::getErrmsg($errno),
        );
    }

    private static function _succRet($ret = array())
    {
        $error = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $ret
        );
    }
}