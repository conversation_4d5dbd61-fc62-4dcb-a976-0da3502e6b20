<?php

/**
 * Add Business Attributes
 * Class addBusinessAttributesAction
 */
class addBusinessAttributesAction extends Util_Action
{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    private static $_name;
    private static $_user;
    private static $_punishment;
    private static $_code;

    public function _execute()
    {
        self::$_user       = $_SERVER['HTTP_AMIS_USER'];
        self::$_name       = trim(Bingo_Http_Request::get('name'));
        self::$_punishment = Bingo_Http_Request::get('punishment');
        self::$_code       = trim(Bingo_Http_Request::get('code'));

        if (empty(self::$_name)) {
            $ret           = array();
            $ret['errmsg'] = "业务属性名不能为空";
            $ret['errno']  = 1;
            echo json_encode($ret);
            die();
        }
        if (empty(self::$_code)) {
            $ret           = array();
            $ret['errmsg'] = "业务标识码不能为空";
            $ret['errno']  = 1;
            echo json_encode($ret);
            die();
        }

        if (!empty(self::$_punishment)) {
            $arrKeys = array();
            $arNames = array();
            foreach (self::$_punishment as $item) {
                if (in_array($item['name'], $arNames)) {
                    $ret           = array();
                    $ret['errmsg'] = "该业务属性下处罚手段名称【{$item['name']}】重复";
                    $ret['errno']  = 1;
                    echo json_encode($ret);
                    die();
                }
                if (in_array($item['key'], $arrKeys)) {
                    $ret           = array();
                    $ret['errmsg'] = "该业务属性下处罚手段编码【{$item['key']}】重复";
                    $ret['errno']  = 1;
                    echo json_encode($ret);
                    die();
                }
                $arrKeys[] = $item['key'];
                $arNames[] = $item['name'];
            }
        }

        $arrInput               = array();
        $arrInput['name']       = self::$_name;
        $arrInput['user']       = self::$_user;
        $arrInput['code']       = self::$_code;
        $arrInput['punishment'] = serialize(self::$_punishment);

        $arrQuery               = array();
        $arrQuery['name']       = self::$_name;
        $arrCode                = array();
        $arrCode['code']        = self::$_code;

        //先判断业务属性添加上限100个
        $arrStatisticsResult = Tieba_Service::call('uegmis', 'statisticsBusinessAttributeNumber', array(), null, null, 'post', null, 'utf-8');
        if ($arrStatisticsResult['ret']['rows'] >= 100) {
            $ret           = array();
            $ret['errmsg'] = "业务属性个数已达上限";
            $ret['errno']  = 1;
            echo json_encode($ret);
            die();
        }
        //判断业务属性标识码是否已存在
        $arrCode = Tieba_Service::call('uegmis', 'getSampleBusinessAttributeDetail', $arrCode, null, null, 'post', null, 'utf-8');
        if (!empty($arrCode['ret']['rows'])) {
            $ret           = array();
            $ret['errmsg'] = "业务属性标识码已存在。";
            $ret['errno']  = 1;
            echo json_encode($ret);
            die();
        }
        //判断业务属性名是否已存在
        $arrJudge = Tieba_Service::call('uegmis', 'getSampleBusinessAttributeDetail', $arrQuery, null, null, 'post', null, 'utf-8');
        if (empty($arrJudge['ret']['rows']) || !isset($arrJudge['ret']['rows'])) {

            $arrRes    = Tieba_Service::call('uegmis', 'addBusinessAttributes', $arrInput, null, null, 'post', null, 'utf-8');
            $arrOutput = $arrRes['ret'];
            echo json_encode($arrOutput);
        } else {
            $ret           = array();
            $ret['errmsg'] = "业务属性已存在。";
            $ret['errno']  = 1;
            echo json_encode($ret);
        }
    }
}