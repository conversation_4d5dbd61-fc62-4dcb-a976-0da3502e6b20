<?php

/**
 * Get Sample User
 * Class getSampleUserAction
 */
class getSampleUserAction extends Util_Action
{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    private static $_userName;
    private static $_realName;
    private static $_email;
    private static $_creator;
    private static $_editor;
    private static $_roleName;
    private static $_businessId;
    private static $_beginTime;
    private static $_endTime;
    private static $_cBeginTime;
    private static $_cEndTime;
    private static $_pn;
    private static $_rn;

    public function _execute()
    {
        self::$_userName   = trim(Bingo_Http_Request::get('user_name'));
        self::$_realName   = trim(Bingo_Http_Request::get('real_name'));
        self::$_email      = trim(Bingo_Http_Request::get('email'));
        self::$_creator    = trim(Bingo_Http_Request::get('creator'));
        self::$_editor     = trim(Bingo_Http_Request::get('editor'));
        self::$_roleName   = trim(Bingo_Http_Request::get('role_name'));
        self::$_businessId = intval(Bingo_Http_Request::get('business_id'));
        self::$_beginTime  = intval(Bingo_Http_Request::get('begin_time'));
        self::$_endTime    = intval(Bingo_Http_Request::get('end_time'));
        self::$_cBeginTime = intval(Bingo_Http_Request::get('cBegin_time'));
        self::$_cEndTime   = intval(Bingo_Http_Request::get('cEnd_time'));
        self::$_pn         = intval(Bingo_Http_Request::get('pn'));
        self::$_rn         = intval(Bingo_Http_Request::get('rn'));

        $arrInput       = array();
        $arrInput['pn'] = self::$_pn;
        $arrInput['rn'] = self::$_rn;
        if (!empty(self::$_businessId)) {
            $arrInput['business_id'] = self::$_businessId;
        }
        if (!empty(self::$_roleName)) {
            $arrInput['role_name'] = self::$_roleName;
        }
        if (!empty(self::$_userName)) {
            $arrInput['user_name'] = self::$_userName;
        }
        if (!empty(self::$_realName)) {
            $arrInput['real_name'] = self::$_realName;
        }
        if (!empty(self::$_email)) {
            $arrInput['email'] = self::$_email;
        }
        if (!empty(self::$_creator)) {
            $arrInput['creator'] = self::$_creator;
        }
        if (!empty(self::$_editor)) {
            $arrInput['editor'] = self::$_editor;
        }
        if (!empty(self::$_beginTime)) {
            $arrInput['edit_begin_time'] = self::$_beginTime;
        }
        if (!empty(self::$_endTime)) {
            $arrInput['edit_end_time'] = self::$_endTime;
        }
        if (!empty(self::$_cBeginTime)) {
            $arrInput['create_begin_time'] = self::$_cBeginTime;
        }
        if (!empty(self::$_cEndTime)) {
            $arrInput['create_end_time'] = self::$_cEndTime;
        }

        //权限校验
        $arrPowerInput['user_name'] = $_SERVER['HTTP_AMIS_USER'];
        $arrResultPower             = Tieba_Service::call('uegmis', 'getSampleUserRole', $arrPowerInput, null, null, 'post', null, 'utf-8');
        if ($arrResultPower === false) {
            $ret           = array();
            $ret['errmsg'] = "获取权限失败。";
            $ret['errno']  = 1;
            echo json_encode($ret);
            die();
        } elseif (empty($arrResultPower['ret']['rows'])) {
            $ret           = array();
            $ret['errmsg'] = "no power";
            $ret['errno']  = 1;
            echo json_encode($ret);
            die();
        }

        $intIsAdmin = 0;
        $arrRole    = array();
        foreach ($arrResultPower['ret']['rows'] as $key => $item) {
            if ($item['is_admin'] == 1) {
                $intIsAdmin = 1;
            }
            $arrRole[$item['business_id']] = unserialize($item['power_list']);
        }

        if ($arrInput['business_id'] == 0) {
            if ($intIsAdmin == 0) {
                $arrInput['business'] = array();
                foreach ($arrResultPower['ret']['rows'] as $key => $item) {
                    $arrInput['business'][] = $item['business_id'];
                }
                sort($arrInput['business']);
            }
        }

        $res                 = Tieba_Service::call('uegmis', 'getSampleUser', $arrInput, null, null, 'post', null, 'utf-8');
        $ret                 = array();
        $ret['errmsg']       = $res['ret']['error'];
        $ret['errno']        = $res['ret']['no'];
        $ret['data']         = array();
        $ret['data']['rows'] = $res['ret']['rows'];

        $arrBusinessName = array();
        foreach ($ret['data']['rows'] as $key => &$value) {
            $value['index']       = (self::$_pn - 1) * self::$_rn + (intval($key) + 1);
            $value['create_time'] = date("Y-m-d H:i:s", $value['create_time']);
            $value['edit_time']   = date("Y-m-d H:i:s", $value['edit_time']);

            if (isset($arrBusinessName[$value['business_id']])) {
                $value['business_name'] = $arrBusinessName[$value['business_id']];
            } else {
                $arrBusInput                            = array();
                $arrBusInput['id']                      = $value['business_id'];
                $res                                    = Tieba_Service::call('uegmis', 'getSampleBusinessAttributeDetail', $arrBusInput, null, null, 'post', null, 'utf-8');
                $value['business_name']                 = $res['ret']['rows'][0]['name'];
                $arrBusinessName[$value['business_id']] = $value['business_name'];
            }
        }

        //统计分类数
        $arrStatisticsResult     = Tieba_Service::call('uegmis', 'statisticsSampleUserNumber', $arrInput, null, null, 'post', null, 'utf-8');
        $ret['data']['count']    = $arrStatisticsResult['ret']['rows'];
        $ret['data']['is_admin'] = $intIsAdmin;
        $ret['data']['role']     = $arrRole;

        echo json_encode($ret);
        die();
    }
}