<?php
/**
 * Created by PhpStorm.
 * User: v_jiamengmeng
 * Date: 
 * Time: 14:56
 */
class getActsListByGroupNameAction extends Util_Action{
    public function _execute(){
        //接收参数
        $arrInput = array();
        $status = Bingo_Http_Request::get('status',1); //表名
        $arrInput = array(
            'status'   => $status,
            'online_lock' => 0,
        );
        $res = Tieba_Service::call('uegmis', 'getActsListByGroupName', $arrInput, null, null, 'post', null, 'utf-8');
        if($res['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call uegmis-getActsListByGroupName fail input: ' . serialize($arrInput) . 'output: ' . serialize($res));
            $res = array(
                'errno' => $res['errno'],
                'errmsg' => '请求失败',
            );
            echo json_encode($res);
            return;
        }
        foreach ($res['ret']['data'] as $info) {                                                                                                           
            $option = array(
                "label" => $info['group_name'],
                "value" => $info['id'],
            );
            $options[] = $option;
        }
        $data = array(
           'options' => $options,
        );
        echo json_encode($data);
        return;     
    }
}
