<?php
/**
 * Created by PhpStorm.
 * User: 
 * Date: 
 * Time: 14:21
 */

class cancelOnlineAction extends Util_Action{
    const LOCK = 0;
    const CURBOS = '';
    public function _execute(){ 
        //获取用户信息
        $user_name = $_SERVER['HTTP_AMIS_USER'];
        $group_name = Bingo_Http_Request::get('group_name');
        $arrInput = array(
            'group_name' => $group_name,
        );
        //拉取规则组表信息,根据group_name
        $res = Tieba_Service::call('uegmis', 'getRuleGroupByGroupName', $arrInput, null, null, 'post', null, 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $res['errno']) {
            $res = array(
                'errno' => $res['errno'],
                'errmsg' => 'call db fail!',
                );
                echo json_encode($res);
                return;
        }
        //重置上线锁信息及待上线配置
        $id = $res['ret']['data'][0]['id'];
        $input = array(
            'id' => $id,
            'online_lock' => self::LOCK,
            'cur_bos'     => self::CURBOS,
        );
        $output = Tieba_Service::call('uegmis', 'resetOnline', $input, null, null, 'post', null, 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $output['errno']) {
            $res = array(
                'errno' => $output['errno'],
                'errmsg' => 'call db fail!',
                );
                echo json_encode($res);
                return;
        }
        echo json_encode($output);
        return;
    }
}
?>
