<?php
/**
 * Created by PhpStorm.
 * User: 
 * Date: 
 * Time: 14:56
 */
class getUserListAction extends Util_Action{
    public function _execute(){
        //接收参数
        $name = Bingo_Http_Request::get('user_name',null);
        $_pagenum = intval(Bingo_Http_Request::get('pn',1));
        $_shownum = intval(Bingo_Http_Request::get('rn',20));
        $arrInput = array(
            'user_name' => $name,
            'shownum' => $_shownum,
            'pagenum' => $_pagenum,
        );
        $res = Tieba_Service::call('uegmis', 'getPostUserList', $arrInput, null, null, 'post', null, 'utf-8'); 
        if($res['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call uegmis-getPostUserList fail input: ' . serialize($arrInput) . 'output: ' . serialize($res));
            $res = array(
                'errno' => $res['errno'],
                'errmsg' => 'call db fail!',
            );
            echo json_encode($res);
            return;
        }
        $count = Tieba_Service::call('uegmis', 'getPostUserCount', $arrInput, null, null, 'post', null, 'utf-8');    
        if($res['errno'] !== Tieba_Errcode::ERR_SUCCESS){ 
            Bingo_Log::warning('call uegmis-getPostUserCount fail input: ' . serialize($arrInput) . 'output: ' . serialize($count));
            $res = array(
                'errno' => $res['errno'],
                'errmsg' => 'call db fail!',
            );
            echo json_encode($res);
            return;
        }
        foreach($res['ret']['data'] as $key=>$value) {
            $value['auth_type_data'] = self::typeChange($value['auth_type']);
            $data[] = $value;
        }
        $res['ret']['data'] = $data;
        $data = array(
            'count' => $count['ret']['data'],
            'data' => $res['ret']['data'],
        );
        echo json_encode($data);
        return;
    }
    private static function typeChange($arrInput) {
        $type = explode(',',$arrInput);
        $array = array(
            '1' => '查看',
            '2' => '编辑权限',
            '3' => '上线权限',
            '4' => '用户管理'
        );
        foreach($type  as $v) {
            $v = $array[$v]; 
            $data[] = $v;
        }
        $auth_type = implode(',', $data);
        return $auth_type; 
    }
    
}
