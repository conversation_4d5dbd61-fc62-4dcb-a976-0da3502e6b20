<?php
/**
 * Created by PhpStorm.
 * User: v_jiamengmeng
 * Date: 
 * Time: 14:56
 */
class fieldListAction extends Util_Action{
    public function _execute(){
        //接收参数
        $arrInput = array();
        $name = trim(Bingo_Http_Request::get('name')); //字段名称
        $cname = trim(Bingo_Http_Request::get('cname')); //中文字段名称
        $status = Bingo_Http_Request::get('status'); //状态
        $remark = Bingo_Http_Request::get('remark'); //备注
        $creator = Bingo_Http_Request::get('creator'); //备注
        $_pagenum = intval(Bingo_Http_Request::get('pn',1));
        $_shownum = intval(Bingo_Http_Request::get('rn',20));
        $arrInput = array(
            'name' => $name,
            'cname' => $cname,
            'status' => $status,
            'remark' => $remark,
            'creator' => $creator,
            'shownum' => $_shownum,
            'pagenum' => $_pagenum,
        );
        $res = Tieba_Service::call('uegmis', 'fieldList', $arrInput, null, null, 'post', null, 'utf-8');
        if($res['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call uegmis-getfieldList fail input: ' . serialize($arrInput) . 'output: ' . serialize($res));
            $res = array(
                'errno' => $res['errno'],
                'errmsg'=> $res['errmsg'],
            );
            echo json_encode($res);
            return;
        }
        $count = Tieba_Service::call('uegmis', 'fieldListCount', $arrInput, null, null, 'post', null, 'utf-8');   
        if($count['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call uegmis-getfieldListCount fail input: ' . serialize($arrInput) . 'output: ' . serialize($count));
            $res = array(
                'errno' => $count['errno'],
                'errmsg'=> $count['errmsg'],
            );
            echo json_encode($res);
            return;
        }
        $data['data'] = array(
            'errno' => $res['errno'],
            'errmsg'=> $res['errmsg'],
            'count' => $count['ret']['data'],
            'rows' => $res['ret']['data'],
        );
        echo json_encode($data);
        return;     
    }
}
