<?php
/**
 * Created by PhpStorm.
 * User: 
 * Date: 2021-05-08
 * Time: 14:21
 */

class addFieldAction extends Util_Action{
    const ADMIN = 1;
    public function _execute(){
        //获取用户权限
        $input['user_name'] = $_SERVER['HTTP_AMIS_USER'];
        $output = Tieba_Service::call('uegmis', 'getUserRights', $input, null, null, 'post', null, 'utf-8');
        if(!$output){
            Bingo_Log::warning('call uegmis-getUserRights fail input:' . serialize($input) . 'output:' . serialize($output));
            $res = array(
                'errno' => '210006',
                'errmsg' => '获取用户权限失败',
            );
            echo json_encode($res);
            return;
        }
        $is_admin = $output['ret']['data'][0]['is_admin'];
        //is_admin=1可操作所有 
        if($is_admin != self::ADMIN){
                $res = array(
                    'errno' => '210006',
                    'errmsg' => '抱歉，您没有权限！',
                );
                echo json_encode($res);
                return; 
        }
        $arrInput = array();
        $name = trim(Bingo_Http_Request::get('fieldName')); //字段名称
        $table = Bingo_Http_Request::get('group_name'); //字段名称
        $type = Bingo_Http_Request::get('type'); //类型
        $status = Bingo_Http_Request::get('status'); //状态
        $creator = $editor = $_SERVER['HTTP_AMIS_USER'];
        $arrInput = array(
            'name' => $name,
            'type' => $type,
            'status' => $status,
            'creator' => $creator,
            'editor' => $editor,
            'table' => $table
        );
        $res = Tieba_Service::call('uegmis', 'addField', $arrInput, null, null, 'post', null, 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $res['errno']) {
            $res = array(
                'errno' => $res['errno'],
                'errmsg'=> $res['errmsg'],
            );
            echo json_encode($res);
            return;
        }
        echo json_encode($res);
        return;
    }
}
