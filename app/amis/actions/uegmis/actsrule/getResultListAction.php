<?php
/**
 * Created by PhpStorm.
 * User: v_jiamengmeng
 * Date: 2021-04-26
 * Time: 14:56
 */
class getResultListAction extends Util_Action{
    public function _execute(){
        //接收参数
        $arrInput = array();
        $_pagenum = intval(Bingo_Http_Request::get('pn',1));
        $_shownum = intval(Bingo_Http_Request::get('rn',20));
        $table = Bingo_Http_Request::get('group_name'); //字段名称
        $arrInput = array(
            'shownum' => $_shownum,
            'pagenum' => $_pagenum,
            'table'   => $table,
        );
        $res = Tieba_Service::call('uegmis', 'getResultList', $arrInput, null, null, 'post', null, 'utf-8');
        if($res['errno'] !== Tieba_Errcode::ERR_SUCCESS){ 
            Bingo_Log::warning('call uegmis-getResultList fail input: '. serialize($arrInput) . 'output: ' . serialize($res));
            $res = array(
                'errno' => '210006',
                'errmsg' => 'call uegmis-getResultList fail!',
            );
            echo json_encode($res);
            return;
        }
        $count = Tieba_Service::call('uegmis', 'getResultListCount', $arrInput, null, null, 'post', null, 'utf-8');   
        if($count['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call uegmis-getResultListCount fail input: '. serialize($arrInput) . 'output: ' . serialize($count));
            $res = array(
                'errno' => '210006',
                'errmsg' => 'call uegmis-getResultListCount fail!',
            );
            echo json_encode($res);
            return;
        }
        $data['data'] = array(
            'errno' => $res['errno'],
            'errmsg'=> $res['errmsg'],
            'count' => $count['ret']['data'],
            'rows' => $res['ret']['data'],
        );
        echo json_encode($data);
        return;  
    }
}
