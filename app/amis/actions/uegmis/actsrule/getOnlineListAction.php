<?php
/**
 * Created by PhpStorm.
 * User: 
 * Date: 
 * Time: 14:56
 */
class getOnlineListAction extends Util_Action{
    const COUNT = 10;
    public function _execute(){
        //接收参数
        $arrInput = array();
        $input['id'] = Bingo_Http_Request::get('group_name');
        //根据id查询group_name
        $output = Tieba_Service::call('uegmis', 'getActsListByGroupName', $input, null, null, 'post', null, 'utf-8');
        if($output['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call getActsListById fail input: ' . serialize($input) . 'output: ' . serialize($output));
            $res = array(
                'errno' => $output['errno'],
                'errmsg' => $output['errmsg'],
                );
                echo json_encode($res);
                return;
        }
        $group_name = $output['ret']['data'][0]['group_name'];
        $arrInput = array(
            'group_name' => $group_name,
        );
        $res = Tieba_Service::call('uegmis', 'getOnlineList', $arrInput, null, null, 'post', null, 'utf-8');
        if($res['errno'] !== Tieba_Errcode::ERR_SUCCESS){
        Bingo_Log::warning('call getOnlineList fail input: ' . serialize($arrInput) . 'output: ' . serialize($res));
            $res = array(
                'errno' => $res['errno'],
                'errmsg' => $res['errmsg'],
            );
            echo json_encode($res);
            return;
        }
        $count = Tieba_Service::call('uegmis', 'getOnlineListCount', $arrInput, null, null, 'post', null, 'utf-8');   
        if($count['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call uegmis-getOnlineListCount fail input: ' . serialize($arrInput) . 'output: ' . serialize($count));
            $res = array(
                'errno' => $count['errno'],
                'errmsg' => $count['errmsg'],
            );
            echo json_encode($res);
            return;
        }
        $count = $count['ret']['data'];
        if($count > self::COUNT){
            foreach ($res['ret']['data'] as $info) {
                $id = $info['id'];
                $online_bos = $info['online_bos'];
            }
            //调用删除接口，删除第一条数据与bos文件
            $arrInput = array(
                'id' => $id,
                'online_bos' => $online_bos,
            );
            $arrOutput = Tieba_Service::call('uegmis', 'updateProfile', $arrInput, null, null, 'post', null, 'utf-8');
            if($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning('call uegmis-updateProfile fail input: ' . serialize($arrInput) . 'output: ' . serialize($arrOutput));
                $res = array(
                    'errno' => $arrOutput['errno'],
                    'errmsg' => $arrOutput['errmsg'],
                );
                echo json_encode($res);
                return;
            }
        }
        $data = array(
            'count' => $count,
            'data' => $res['ret']['data'],
        );
        echo json_encode($data);
        return;    
    }
}
