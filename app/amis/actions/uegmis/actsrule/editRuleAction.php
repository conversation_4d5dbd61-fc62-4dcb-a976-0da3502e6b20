<?php
/**
 * Created by PhpStorm.
 * User: 
 * Date: 
 * Time: 14:21
 */

class editRuleAction extends Util_Action{
    const SPLIT = '-in-';
    private static $acts_dict = array(
        'confilter_mis_wordlist_global_noacts',
        'confilter_acts_ctrl_global_huomian',
        'confilter_Anti_PCsixin_noacts',
        'confilter_acts_ctrl_hottieba_2',
        'confilter_acts_ctrl_hottieba',
        'confilter_pcmsg_black_cuid',
        'confilter_old_post_only_fourm_owner',
        'confilter_spam01',
        'confilter_spam02',
        'confilter_spam03',
        'confilter_official_user_id',
        'confilter_acts_ctrl_global_no_acts',
        'confilter_vcode_prevent',
        'confilter_acts_ctrl_novcode_forum_id',
        'confilter_Anti_PostDelete_from_bawu_tuwenzhibo',
        'confilter_baoba_protect_forum_id',
        'confilter_acts_ctrl_wordlist_novcode_user',
        'confilter_kantie_white_user_id',
        'confilter_mis_wordlist_noreply_thread_id',
        'confilter_mis_wordlist_post_client',
        'confilter_mis_wordlist_post_complex',
        'confilter_mis_wordlist_fds_ygjk_actsctrl',
        'confilter_acts_ctrl_hottieba_tieba',
        'confilter_mis_wordlist_lockpost',
        'confilter_confilter_pinpai_forum',
        'confilter_beauty',
        'confilter_mis_thread_whitelist',
        'confilter_complex_vcode_fid',
        'confilter_mis_wordlist_zhangbai_iplist',
        'confilter_mis_wordlist_noreply_thread_id_wuxian',
        'confilter_test_acts',
    );
    public function _execute(){
        $arrInput = array();
        $id = Bingo_Http_Request::get('id');
        $table = trim(Bingo_Http_Request::get('group_name')); //group名称
        $rulelist_info = Bingo_Http_Request::get('rulelist_info'); //规则子项
        $result_id = Bingo_Http_Request::get('result_id'); //结果id
        $time = Bingo_Http_Request::get('time'); //时间
        $count = Bingo_Http_Request::get('count'); //次数
        $timer_absolutely = Bingo_Http_Request::get('timer_absolutely'); //定时清除
        $status = Bingo_Http_Request::get('status'); //状态
        $remark = Bingo_Http_Request::get('remark'); //备注
        $rule_priority = Bingo_Http_Request::get('rule_priority'); //规则优先级
        $editor = $_SERVER['HTTP_AMIS_USER'];
        //查询状态与优先级是否更改,根据id查询单条信息
        $input = array(
                'id'    => $id,
                'table' => $table,
        ); 
        $output = Tieba_Service::call('uegmis', 'getRuleByid', $input, null, null, 'post', 'json', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $output['errno']) {
            $res = array(
                'errno' => $output['errno'],
                'errmsg' => $output['errmsg'],
            );
            echo json_encode($res);
            return;
        }
        //判断是否修改生效信息
        $outputStatus = $output['ret']['data'][0]['status'];
        $outputPriority = $output['ret']['data'][0]['rule_priority'];
        //如果状态更改为0的话将优先级改为0,并判断是否是最大值将后面的数据依次减一
        if($status != $outputStatus && $status == 0){
            $input = array(
                'status' => $status,
                'id'     => $id,
                'table'  => $table,
                'rule_priority' => $outputPriority,
            );
            $res = Tieba_Service::call('uegmis', 'editRuleStatus', $input, null, null, 'post', null, 'utf-8');
            if (Tieba_Errcode::ERR_SUCCESS !== $res['errno']) {
                $res = array(
                    'errno' => $res['errno'],
                    'errmsg' => $res['errmsg'],
                );
                echo json_encode($res);
                return;
            }
        } 
        //如果状态更改为1(生效)的话默认走新增的逻辑
        if($status != $outputStatus && $status == 1){ 
            $input = array(
                'status' => $status,
                'id'     => $id,
                'table'  => $table,
            );
            $res = Tieba_Service::call('uegmis', 'editRuleStatus', $input, null, null, 'post', null, 'utf-8');
            if (Tieba_Errcode::ERR_SUCCESS !== $res['errno']) {
                $res = array(
                    'errno' => $res['errno'],
                    'errmsg' => $res['errmsg'],
                );
                echo json_encode($res);
                return;
            }    
        }
        //判断优先级是否更改
        if($rule_priority != $outputPriority && $outputStatus == 1 && $status == 1){
            $input = array(
                'id' => $id,
                'outputPriority' => $outputPriority,
                'rule_priority' => $rule_priority,
                'table' => $table,
            );
            $res = Tieba_Service::call('uegmis', 'editRulePriority', $input, null, null, 'post', null, 'utf-8');
            if (Tieba_Errcode::ERR_SUCCESS !== $res['errno']) {
                $res = array(
                    'errno' => $res['errno'],
                    'errmsg' => $res['errmsg'],
                );
                echo json_encode($res);
                return;
            }
        }
        foreach($rulelist_info as $info){ 
            $name = $info['name'];
            $split =  $info['split'];
            $value = $info['value'];
            //如果分割符是-in-的话，判断值是否在数组中
            if($split == self::SPLIT){
                $value = str_replace('../dict/', '', $value);
                if(!in_array($value,self::$acts_dict)){
                    $res = array(
                        'errno' => Tieba_Errcode::ERR_DB_QUERY_FAIL,
                        'errmsg' => "抱歉，所输入的词表【{$value}】不可用！",
                    );
                    echo json_encode($res);
                    return;
                }
                $value = '../dict/' . $value;
            }
            $rule_key[] = $name . $split . $value;
        }
        $rule_key_json = json_encode($rulelist_info);
        if(count($rule_key) > 1){
            $rule_key = implode(';' , $rule_key);
        }else{
            $rule_key = $rule_key[0];
        }
        $arrInput = array(
            'table'         => $table,
            'rule_key_json' => $rule_key_json,
            'rule_key'      => $rule_key,
            'result_id'     => $result_id,
            'time'          => $time,
            'count'         => $count,
            'timer_absolutely' => $timer_absolutely,
            'remark'        => $remark,
            'editor'        => $editor,
            'id'            => $id
        );
        //修改规则
        $res = Tieba_Service::call('uegmis', 'editRule', $arrInput, null, null, 'post', null, 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $res['errno']) {
            $res = array(
                'errno' => $res['errno'],
                'errmsg' => $res['errmsg'],
            );
            echo json_encode($res);
            return;
        }
        echo json_encode($res);
        return;
    }
}
