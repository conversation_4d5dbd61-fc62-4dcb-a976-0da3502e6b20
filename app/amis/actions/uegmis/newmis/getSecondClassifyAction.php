<?php
/**
 * Created by PhpStorm.
 * User: liyiran01
 * Date: 2019-07-09
 * Time: 17:57
 */

class getSecondClassifyAction extends Util_Action
{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    private static $_name;
    private static $_adder;
    private static $_editor;
    private static $_start_time;
    private static $_end_time;
    private static $_pagenum;
    private static $_shownum;
    private static $_user;
    private static $_businessid;
    private static $_firstid;

    public function _execute(){
        self::$_name = trim(Bingo_Http_Request::get('name'));
        self::$_adder = trim(Bingo_Http_Request::get('adder'));
        self::$_editor = trim(Bingo_Http_Request::get('editor'));
        self::$_start_time = trim(Bingo_Http_Request::get('starttime'));
        self::$_end_time = trim(Bingo_Http_Request::get('endtime'));
        self::$_pagenum = intval(Bingo_Http_Request::get('pn'));
        self::$_shownum = intval(Bingo_Http_Request::get('rn'));
        self::$_businessid = trim(Bingo_Http_Request::get('businessid'));
        self::$_firstid = trim(Bingo_Http_Request::get('firstid'));
        self::$_user = $_SERVER['HTTP_AMIS_USER'];

        $powerInput['user_name'] = self::$_user;
        $power = Tieba_Service::call('uegmis','getUserRole',$powerInput);
        if ($power === false){
            $ret = array();
            $ret['errmsg'] = "获取权限失败。";
            $ret['errno'] = 1;
            echo json_encode($ret);
            die();
        }
        $admin = 0;
        foreach ($power['ret']['data'] as $value){
            if ($value['is_admin'] == 1){
                $admin = 1;
            }
            if(self::$_businessid == $value['business_id']){
                $role = unserialize($value['power_list']);
            }
        }

        if(!empty(self::$_name)){
            $arrInput['name'] = self::$_name;
        }
        if(!empty(self::$_adder)) {
            $arrInput['adder'] = self::$_adder;
        }
        if(!empty(self::$_editor)) {
            $arrInput['editor'] = self::$_editor;
        }
        if(!empty(self::$_start_time)) {
            $arrInput['starttime'] = date('Y/m/d H:i:s',self::$_start_time);
        }
        if(!empty(self::$_end_time)) {
            $arrInput['endtime'] = date('Y/m/d H:i:s',self::$_end_time);
        }

        $arrInput['pagenum'] = self::$_pagenum;
        $arrInput['shownum'] = self::$_shownum;
        $arrInput['belong_business'] = self::$_businessid;
        $arrInput['belong_first'] = self::$_firstid;

        $res = Tieba_Service::call('uegmis', 'getSecondClassify', $arrInput, null, null, 'post', null, 'utf-8');
        $ret = array();
        $ret['errmsg'] = $res['ret']['error'];
        $ret['errno'] = $res['ret']['no'];
        $ret['output'] = array();
        $ret['output']['rows'] = $res['ret']['rows'];
        foreach ($ret['output']['rows'] as $key => &$value){
            $value['index'] = (self::$_pagenum-1) * self::$_shownum + (intval($key) + 1);
        }
        $ret['output']['user_power_list'] = $role;
        $ret['output']['is_admin'] = $admin;

        $count = Tieba_Service::call('uegmis', 'getSecondCount', $arrInput, null, null, 'post', null, 'utf-8');
        $ret['output']['count'] = $count['ret']['rows'];

        echo json_encode($ret);
    }
}
