<?php
/**
 * Created by PhpStorm.
 * User: liyiran01
 * Date: 2019-07-01
 * Time: 11:47
 */

class initUserAction extends Util_Action{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    private static $_user;

    public function _execute(){
        self::$_user = $_SERVER['HTTP_AMIS_USER'];
        $arrInput = $ret = array();
        $arrInput['id'] = Bingo_Http_Request::get('id');
        $arrInput['business_id'] = Bingo_Http_Request::get('business_id');
        $arrInput['shownum'] = 500;//取全部角色
        //先取出该业务下的全部角色列表
        $res = Tieba_Service::call('uegmis', 'getRoleList', $arrInput, null, null, 'post', null, 'utf-8');
        $output['role_list'] = $res['ret']['data'];
        if($arrInput['id']>0)
        {
            //如果传了id就是编辑页面的初始化
            $res = Tieba_Service::call('uegmis', 'getUserList', $arrInput, null, null, 'post', null, 'utf-8');
            $output['user_info'] = $res['ret']['data'][0];
        }
        $ret['errmsg'] = $res['ret']['error'];
        $ret['errno'] = $res['ret']['no'];
        $ret['output'] = $output;
        echo json_encode($ret);
        die();

        return $this->printOut(Tieba_Errcode::ERR_SUCCESS, '', $res['data']);

    }
}
