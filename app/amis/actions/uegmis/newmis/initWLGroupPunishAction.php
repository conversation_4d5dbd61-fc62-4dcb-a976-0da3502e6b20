<?php
/**
 * Created by PhpStorm.
 * User: liyiran01
 * Date: 2019-07-01
 * Time: 11:47
 */

class initWLGroupPunishAction extends Util_Action{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;


    public function _execute(){
        $ret = $punishmentlist = array();
        
        $getBusinessquery['id'] = intval(Bingo_Http_Request::get('business_id'));
        $resB = Tieba_Service::call('uegmis', 'getBusinessDetail', $getBusinessquery, null, null, 'post', null, 'utf-8');
        foreach($resB['ret']['rows'][0]['punishment'] as $k => $v)
        {
            $punishmentlist[$k]['label'] = $v['name'];
            $punishmentlist[$k]['value'] = $v['key'];
        }
        
        $ret['msg'] = $resB['ret']['error'];
        $ret['status'] = $resB['ret']['no'];
        $ret['data']['options'] = $punishmentlist;

        echo json_encode($ret);

    }
}
