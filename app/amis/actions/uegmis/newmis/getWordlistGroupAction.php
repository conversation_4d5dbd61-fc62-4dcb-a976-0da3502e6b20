<?php
/**
 * Created by PhpStorm.
 * User: liyiran01
 * Date: 2019-06-26
 * Time: 14:56
 */

class getWordlistGroupAction extends Util_Action{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    private static $_name;
    private static $_editor;
    private static $_creator;
    private static $_start_time;
    private static $_end_time;
    private static $_pagenum;
    private static $_shownum;
    private static $_businessid;

    public function _execute(){

        self::$_name = trim(Bingo_Http_Request::get('wlgroup_name'));
        self::$_editor = trim(Bingo_Http_Request::get('editor'));
        self::$_creator = trim(Bingo_Http_Request::get('creator'));
        self::$_start_time = trim(Bingo_Http_Request::get('starttime'));
        self::$_end_time = trim(Bingo_Http_Request::get('endtime'));
        self::$_pagenum = intval(Bingo_Http_Request::get('pn',1));
        self::$_shownum = intval(Bingo_Http_Request::get('rn',20));
        self::$_businessid  = Bingo_Http_Request::get('business_id');
        $arrInput = array();
        //如果传了bid，就用；如果没传bid，就先查bid再查该bid下面的power_list
        $powerInput['user_name'] = $_SERVER['HTTP_AMIS_USER'];
        if($powerInput['user_name']==null)
        {
            $powerInput['user_name'] = $_SERVER['HTTP_PASS_USER'];
        }
        //$powerInput['user_name'] ="zhanglin17";
        $is_admin = 0;
        $power = Tieba_Service::call('uegmis','getUserRole',$powerInput, null, null, 'post', null, 'utf-8');
        foreach($power['ret']['data'] as $kk => $vv)
        {
            if($vv['is_admin'] == 1)
            {
                $is_admin = 1;
                break;
            }
        }
        if(isset(self::$_businessid) && self::$_businessid>0){
            $arrInput['business_id'] = self::$_businessid;
        }
        else
        {
            if($power['ret']['data']['0']['is_admin']<>1)
            {
                $arrInput['business_id'] = $power['ret']['data']['0']['business_id'];
            }
        }
        foreach($power['ret']['data'] as $k => $v)
        {
            if($v['business_id'] == $arrInput['business_id'])
            {
                $power_list = unserialize($v['power_list']);
            }
        }


        if(isset(self::$_name)){
            $arrInput['wlgroup_name'] = self::$_name;
        }
        if(isset(self::$_editor)) {
            $arrInput['editor'] = self::$_editor;
        }
        if(isset(self::$_creator)) {
            $arrInput['creator'] = self::$_creator;
        }
        if(isset(self::$_start_time)) {
            $arrInput['starttime'] = self::$_start_time;
        }
        if(isset(self::$_end_time)) {
            $arrInput['endtime'] = self::$_end_time;
        }

        $arrInput['pagenum'] = self::$_pagenum;
        $arrInput['shownum'] = self::$_shownum;
        $res = Tieba_Service::call('uegmis', 'getWordlistGroupList', $arrInput, null, null, 'post', null, 'utf-8');
        foreach($res['ret']['data'] as $k => &$v)
        {
            $v['index']= $k+1;
        }
        $output = $res['ret'];
       
        $count = Tieba_Service::call('uegmis', 'getWordlistGroupCount', $arrInput, null, null, 'post', null, 'utf-8');
        $output['count'] = $count['ret']['data'];
        $output['user_power_list'] = $power_list;
        $output['user_is_admin'] = $is_admin;
        echo json_encode($output);

    }
}
