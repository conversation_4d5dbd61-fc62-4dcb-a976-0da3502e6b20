<?php
/**
 * Created by PhpStorm.
 * User: liyiran01
 * Date: 2019-06-28
 * Time: 14:21
 */

class addRoleAction extends Util_Action{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;


    public function _execute(){

        $arrInput = array();
        $query = array();
        $arrInput['role_name'] = trim(Bingo_Http_Request::get('role_name'));
        $arrInput['remark'] = trim(Bingo_Http_Request::get('remark'));
        $arrInput['creator'] = $_SERVER['HTTP_AMIS_USER'];
        $arrInput['business_id'] = intval(Bingo_Http_Request::get('business_id'));
        $arrInput['power_list'] = serialize(Bingo_Http_Request::get('permissions'));
        $arrInput['is_admin'] = Bingo_Http_Request::get('is_admin');
        $query['role_name'] = $arrInput['role_name'];
        $query['business_id'] = $arrInput['business_id'];
        $judge = Tieba_Service::call('uegmis', 'getRoleDetail', $query, null, null, 'post', null, 'utf-8');
        if(empty($judge['ret']['data']) || !isset($judge['ret']['data'])) {
            $res = Tieba_Service::call('uegmis', 'addRole', $arrInput, null, null, 'post', null, 'utf-8');
            $output = $res['ret'];
            echo json_encode($output);
        }
        else{
            $ret = array();
            $ret['errmsg'] = "本业务下该角色已存在";
            $ret['errno'] = 1;
            echo json_encode($ret);
        }
    }
}
