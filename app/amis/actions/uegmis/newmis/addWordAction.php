<?php
/**
 * Created by PhpStorm.
 * User: liyiran01
 * Date: 2019-07-17
 * Time: 14:20
 */
require_once 'BatchMulticall.php';

class addWordAction extends Util_Action
{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    private static $_wordlist;
    private static $_user;
    private static $_expired_time;
//    private static $_effect_time; //生效时间
//    private static $_if_right;
    private static $_if_forever;
    private static $_word;
    private static $_memo;
    private static $_match_type;
    private static $_wordlist_id;


    public function _execute()
    {
        self::$_word = Bingo_Http_Request::getNoXssSafe('word');
        self::$_user = $_SERVER['HTTP_AMIS_USER'];
//        self::$_if_right = intval(Bingo_Http_Request::get('if_right'));
        self::$_if_forever = intval(Bingo_Http_Request::get('if_forever'));
        self::$_expired_time = trim(Bingo_Http_Request::get('expired_time'));
//        self::$_effect_time = trim(Bingo_Http_Request::get('effect_time'));
        self::$_wordlist = trim(Bingo_Http_Request::get('wordlist_name'));
        self::$_memo = trim(Bingo_Http_Request::get('memo'));
        self::$_match_type = trim(Bingo_Http_Request::get('match_type'));
        self::$_wordlist_id = trim(Bingo_Http_Request::get('wordlist_id'));

        $now = strtotime(date("Y-m-d H:i:s"));
       /* if(self::$_if_right == 0) {
            $effcet_time = self::$_effect_time;
        } else {
            $effcet_time = $now;
        }*/

        if(self::$_if_forever == 0) {
            $expired_time = self::$_expired_time;
        } else {
            $expired_time = "4294967295";
        }

        if( $expired_time < $now ){
            $ret = array();
            $ret['errmsg'] = "失效时间不能小于当前时间";
            $ret['errno'] = 1;
            echo json_encode($ret);
            return false;
        }

        $wordLen = mb_strlen(str_replace("\n","",self::$_word),'UTF8');
        //判断词汇格式是否正确
        if ($wordLen > 10000){
            $ret = array();
            $ret['errmsg'] = "词汇总长度超过10000";
            $ret['errno'] = 1;
            echo json_encode($ret);
            return false;
        }
        if (strpos(self::$_word, "\\") !== false) {
            $ret = array();
            $ret['errmsg'] = "包含转义字符 请检查文本";
            $ret['errno'] = 1;
            echo json_encode($ret);
            return false;
        }
        $wordArr = explode("\n", self::$_word);
        foreach ($wordArr as $key => $value){
            $value = trim($value);
            $wordArr[$key] = $value;
            if(self::$_match_type == '多模')
            {
                if(!(stripos($value, '&')>0))
                {
                    $ret['errmsg'] = "多模匹配的词汇要以&符号分隔";
                    $ret['errno'] = 1;
                    echo json_encode($ret);
                    return false;
                }
            }
            $len = mb_strlen($value,'UTF8');
            if ($len > 60){
                $ret = array();
                $ret['errmsg'] = "单个词汇长度超过60";
                $ret['errno'] = 1;
                echo json_encode($ret);
                return false;
            }
            if ($len == 0){
                //  var_dump($len);
                $ret = array();
                $ret['errmsg'] = "添加词汇不能为空";
                $ret['errno'] = 1;
                echo json_encode($ret);
                return false;
            }
        }
        //判断备注是否超出长度
        $memoLen = mb_strlen(str_replace("\n","",self::$_memo),'UTF8');
        if ($memoLen > 200){
            $ret = array();
            $ret['errmsg'] = "备注总长度超过200";
            $ret['errno'] = 1;
            echo json_encode($ret);
            return false;
        }

        //判断词表长度是否超出限制
        $Input = array(
            "wordlist_id"   => self::$_wordlist_id,
            "wordlens"      => $wordLen,
            "type"          => 'select',
        );
        $res = Tieba_Service::call('uegmis', 'UpdateWordLens', $Input, null, null, 'post', null, 'utf-8');
        if ($res['errno'] != 0){
            $ret = array();
            $ret['errmsg'] = "词表总长度为".$res['ret']['data']['word_lens']."超过1500000";
            $ret['errno'] = 1;
            echo json_encode($ret);
            return false;
        }

        //判断词汇是否存在重复
        $word = array_unique($wordArr);
        foreach ($word as $key => $w){
            $word[$key] = base64_encode($w);
        }

        $tem = array_chunk($word,500,true);
        $serviceParam = array();
        foreach ($tem as &$value){
            $arrInput = array();
            $arrInput['tablename'] = self::$_wordlist;
            $arrInput['user'] = self::$_user;
            $arrInput['add_time'] = $now;
            $arrInput['memo'] = self::$_memo;
            $arrInput['effect_time'] = $now;
            $arrInput['expired_time'] = $expired_time;
            $arrInput['status'] = 0;
            $arrInput['word'] = $value;

            $Param = array();
            $Param['input'] = $arrInput;
            $Param['ie'] = 'utf-8';
            $Param['method'] = 'addWord';
            $Param['serviceName'] = 'uegmis';
            $serviceParam[] = $Param;
        }

        $arrOutList = BatchMulticall::multiCallService($serviceParam);
        if(false === $arrOutList || empty($arrOutList) || !is_array($arrOutList)){
            Bingo_Log::warning('examineResourceAction call service fail.input:['.serialize($serviceParam).'] outList:['.serialize($arrOutList).']');
            $ret['errmsg'] = "调取Service失败。";
            $ret['errno'] = 1;
            echo json_encode($ret);
            return false;
        }
        $ret = array();
        $sunnum = 0;
        $delnum = 0;
        $repnum = 0;
        foreach ($arrOutList as $val){
            if ($val['ret']['no'] != 0){
                $ret['errno'] = $val['ret']['no'];
                $ret['errmsg'] = $res['ret']['error'];
                echo json_encode($ret);
                return false;
            }
            $sunnum += $val['ret']['rows']['sunnum'];
            $delnum += $val['ret']['rows']['delnum'];
            $repnum += $val['ret']['rows']['repnum'];
        }
        $repnum = count($wordArr) - count(array_unique($wordArr)) + $repnum;

        $ret['errno'] = 0;
        $ret['errmsg'] = '已成功添加'. $sunnum .'个词汇，去重'. $repnum .'个，已存在词汇删除'.$delnum.'个。';
        echo json_encode($ret);
    }

}
