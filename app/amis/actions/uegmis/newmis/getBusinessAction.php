<?php
/**
 * Created by PhpStorm.
 * User: liyiran01
 * Date: 2019-06-26
 * Time: 14:56
 */

class getBusinessAction extends Util_Action{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    private static $_name;
    private static $_adder;
    private static $_editor;
    private static $_start_time;
    private static $_end_time;
    private static $_pagenum;
    private static $_shownum;
    private static $_user;
    private static $_type;


    public function _execute(){

        self::$_name = trim(Bingo_Http_Request::get('name'));
        self::$_adder = trim(Bingo_Http_Request::get('adder'));
        self::$_editor = trim(Bingo_Http_Request::get('editor'));
        self::$_start_time = trim(Bingo_Http_Request::get('starttime'));
        self::$_end_time = trim(Bingo_Http_Request::get('endtime'));
        self::$_pagenum = intval(Bingo_Http_Request::get('pn'));
        self::$_shownum = intval(Bingo_Http_Request::get('rn'));
        self::$_user = $_SERVER['HTTP_AMIS_USER'];
        self::$_type = trim(Bingo_Http_Request::get('type'));

        $arrInput = array();
        $powerInput = array();

        $arrInput['pagenum'] = self::$_pagenum;
        $arrInput['shownum'] = self::$_shownum;

        $powerInput['user_name'] = self::$_user;
        $power = Tieba_Service::call('uegmis','getUserRole',$powerInput);
        if ($power === false){
            $ret = array();
            $ret['errmsg'] = "获取权限失败。";
            $ret['errno'] = 1;
            echo json_encode($ret);
            die();
        }
        //Bingo_Log::warning("liyiranaaa".var_export($power,true));
        $role = array();
        $admin = 0;
        foreach ($power['ret']['data'] as $value){
            if ($value['is_admin'] == 1){
                $admin = 1;
            }
            $key = $value['business_id'];
            $val = unserialize($value['power_list']);
            $role[$key] = $val;
        }

        if($admin === 0){
            $arrInput['business'] = array();
            foreach ($power['ret']['data'] as $value){
                //add 兼容'命中内容定位'权限限制
                if(!empty(self::$_type) && self::$_type == 'wordsearch'){
                    if(strpos($value['power_list'],'9-2')){
                        $arrInput['business'][] = $value['business_id'];
                    }
                }else{
                    $arrInput['business'][] = $value['business_id'];
                }
            }
            sort($arrInput['business']);
            $bcount = count($arrInput['business']);
            if ((!empty($arrInput['pagenum'])) && (!empty($arrInput['shownum']))) {
                $begin = (self::$_pagenum - 1) * self::$_shownum;
                $end = self::$_shownum;
                $arrInput['business'] = array_slice($arrInput['business'], $begin, $end);
            }
        }

        if(!empty(self::$_name)){
            $arrInput['name'] = self::$_name;
        }
        if(!empty(self::$_adder)) {
            $arrInput['adder'] = self::$_adder;
        }
        if(!empty(self::$_editor)) {
            $arrInput['editor'] = self::$_editor;
        }
        if(!empty(self::$_start_time)) {
            $arrInput['starttime'] = date('Y/m/d H:i:s',self::$_start_time);
        }
        if(!empty(self::$_end_time)) {
            $arrInput['endtime'] = date('Y/m/d H:i:s',self::$_end_time);
        }
        if(!empty(self::$_type)){
            $arrInput['type'] = 'wordsearch';
        }
        $arrInput['queryfrom'] = trim(Bingo_Http_Request::get('queryfrom'));
        $res = Tieba_Service::call('uegmis', 'queryBusiness', $arrInput, null, null, 'post', null, 'utf-8');
        $ret = array();
        $ret['errmsg'] = $res['ret']['error'];
        $ret['errno'] = $res['ret']['no'];
        $ret['output'] = array();
        $ret['output']['rows'] = $res['ret']['rows'];

        foreach ($ret['output']['rows'] as $key => &$value){
            $value['index'] = (self::$_pagenum-1) * self::$_shownum + (intval($key) + 1);
        }
        
        if ($admin == 0){
            $ret['output']['count'] = $bcount;
        }
        else{
            $count = Tieba_Service::call('uegmis', 'getBusinessCount', $arrInput, null, null, 'post', null, 'utf-8');
            $ret['output']['count'] = $count['ret']['rows'];
        }

        $ret['output']['role'] = $role;
        $ret['output']['is_admin'] = $admin;

        echo json_encode($ret);
    }
}
