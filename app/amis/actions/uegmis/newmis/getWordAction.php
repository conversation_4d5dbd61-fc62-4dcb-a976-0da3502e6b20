<?php
/**
 * Created by PhpStorm.
 * User: liyiran01
 * Date: 2019-07-18
 * Time: 10:18
 */

class getWordAction extends Util_Action{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    private static $_keyword;
    private static $_adder;
    private static $_memo;
    private static $_add_start_time;
    private static $_add_end_time;
    private static $_deler;
    private static $_del_start_time;
    private static $_del_end_time;
    private static $_expired_start_time;
    private static $_expired_end_time;
    private static $_effect_start_time;
    private static $_effect_end_time;
    private static $_if_effective;
    private static $_pagenum;
    private static $_shownum;
    private static $_user;
    private static $_wordlist_name;
    private static $_wordlist_id;
    private static $_intDownLoadFlag;
    private static $_selectDownLoad;
    private static $_beginDownLoad;
    private static $_endDownLoad;

    public function _execute(){

        self::$_keyword = trim(Bingo_Http_Request::get('keyword'));
        self::$_adder = trim(Bingo_Http_Request::get('adder'));
        self::$_deler = trim(Bingo_Http_Request::get('deler'));
        self::$_memo = trim(Bingo_Http_Request::get('memo'));
        self::$_add_start_time = trim(Bingo_Http_Request::get('add_start_time'));
        self::$_del_start_time = trim(Bingo_Http_Request::get('del_start_time'));
        self::$_expired_start_time = trim(Bingo_Http_Request::get('expired_start_time'));
        self::$_effect_start_time = trim(Bingo_Http_Request::get('effect_start_time'));
        self::$_add_end_time = trim(Bingo_Http_Request::get('add_end_time'));
        self::$_del_end_time = trim(Bingo_Http_Request::get('del_end_time'));
        self::$_expired_end_time = trim(Bingo_Http_Request::get('expired_end_time'));
        self::$_effect_end_time = trim(Bingo_Http_Request::get('effect_end_time'));
        self::$_if_effective = intval(Bingo_Http_Request::get('is_effective'));
        self::$_wordlist_name = trim(Bingo_Http_Request::get('wordlist_name'));
        self::$_wordlist_id = trim(Bingo_Http_Request::get('wordlist_id'));
        self::$_pagenum = intval(Bingo_Http_Request::get('pn'));
        self::$_shownum = intval(Bingo_Http_Request::get('rn'));
        self::$_intDownLoadFlag = intval(Bingo_Http_Request::get('intDownLoadFlag',0));
        self::$_selectDownLoad = intval(Bingo_Http_Request::get('selectDownLoad',0));
        self::$_beginDownLoad = intval(Bingo_Http_Request::get('beginDownLoad',1));
        self::$_endDownLoad = intval(Bingo_Http_Request::get('endDownLoad',1));
        self::$_user = $_SERVER['HTTP_AMIS_USER'];

        if (self::$_beginDownLoad == 0){
            $ret = array();
            $ret['errmsg'] = "范围格式不正确：起始不能为0.";
            $ret['errno'] = 1;
            echo json_encode($ret);
            die();
        }
        if (self::$_beginDownLoad > self::$_endDownLoad){
            $ret = array();
            $ret['errmsg'] = "范围格式不正确：起始不能大于结束.";
            $ret['errno'] = 1;
            echo json_encode($ret);
            die();
        }
        if ((self::$_endDownLoad - self::$_beginDownLoad) > 5000){
            $ret = array();
            $ret['errmsg'] = "范围格式不正确：超过5000条.";
            $ret['errno'] = 1;
            echo json_encode($ret);
            die();
        }

        $arrInput = array();
        $powerInput = array();
        $WLInput = array();
        $classifyInput = array();
        if(!empty(self::$_keyword)){
            $word = array_unique(explode("\n", self::$_keyword));
            if (count($word) > 500) {
                $ret = array();
                $ret['errmsg'] = "查询词量超过上限500，请重新输入";
                $ret['errno'] = 1;
                echo json_encode($ret);
                die();
            }
            if (count($word) == 1) {
                $arrInput['keyword'] = $word[0];
            }else {
                $arrInput['keywordlist'] = $word;
            }
        }

        //获取词表详细属性
        $WLInput['id'] = self::$_wordlist_id;
        $res = Tieba_Service::call('uegmis', 'getWordlist', $WLInput, null, null, 'post', null, 'utf-8');
        $data = $res['ret']['data'][0];

        //获取用户管理员与权限列表
        $powerInput['user_name'] = self::$_user;
        $power = Tieba_Service::call('uegmis','getUserRole',$powerInput, null, null, 'post', null, 'utf-8');
        if ($power === false){
            $ret = array();
            $ret['errmsg'] = "获取权限失败。";
            $ret['errno'] = 1;
            echo json_encode($ret);
            die();
        }
        $admin = 0;
        foreach ($power['ret']['data'] as $value){
            if ($value['is_admin'] == 1){
                $admin = 1;
            }
            if($data['business_id'] == $value['business_id']){
                $role = unserialize($value['power_list']);
            }
        }


        if(!empty(self::$_adder)) {
            $arrInput['adder'] = self::$_adder;
        }
        if(!empty(self::$_memo)) {
            $arrInput['memo'] = self::$_memo;
        }
        if(self::$_if_effective >= 0) {
            $arrInput['if_effective'] = self::$_if_effective;
        }
        if(!empty(self::$_add_start_time)) {
            $arrInput['addstarttime'] = self::$_add_start_time;
        }
        if(!empty(self::$_add_end_time)) {
            $arrInput['addendtime'] = self::$_add_end_time;
        }
        if(!empty(self::$_deler)) {
            $arrInput['deler'] = self::$_deler;
        }
        if(!empty(self::$_del_start_time)) {
            $arrInput['delstarttime'] = self::$_del_start_time;
        }
        if(!empty(self::$_del_end_time)) {
            $arrInput['delendtime'] = self::$_del_end_time;
        }
        if(!empty(self::$_expired_start_time)) {
            $arrInput['expiredstarttime'] = self::$_expired_start_time;
        }
        if(!empty(self::$_expired_end_time)) {
            $arrInput['expiredendtime'] = self::$_expired_end_time;
        }
        if(!empty(self::$_effect_start_time)) {
            $arrInput['effectstarttime'] = self::$_effect_start_time;
        }
        if(!empty(self::$_effect_end_time)) {
            $arrInput['effectendtime'] = self::$_effect_end_time;
        }

        if (self::$_selectDownLoad != 1) {
            $arrInput['pagenum'] = self::$_pagenum;
            $arrInput['shownum'] = self::$_shownum;
        }
        else{
            $arrInput['selectDownLoad'] = self::$_selectDownLoad;
            $arrInput['beginDownLoad'] = self::$_beginDownLoad;
            $arrInput['endDownLoad'] = self::$_endDownLoad;
        }
        $arrInput['wordlist_name'] = $data['name'];

        $res = Tieba_Service::call('uegmis', 'getBatchWord', $arrInput, null, null, 'post', null, 'utf-8');
        $ret = array();
        $ret['errmsg'] = $res['ret']['error'];
        $ret['errno'] = $res['ret']['no'];
        $ret['output'] = array();
        $ret['output']['rows'] = $res['ret']['rows'];

        foreach ($ret['output']['rows'] as $key => &$value){
            $value['index'] = (self::$_pagenum-1) * self::$_shownum + (intval($key) + 1);
            $value['add_time'] = date('Y-m-d H:i:s',$value['add_time']);
            if (empty($value['del_time'])) {
                $value['del_time'] = "-";
            } else {
                $value['del_time'] = date('Y-m-d H:i:s',$value['del_time']);
            }
            if ($value['expired_time'] != 4294967295){
                $value['expired_time'] = date('Y-m-d H:i:s',$value['expired_time']);
            }
            else{
                $value['expired_time'] = "永久";
            }
            $value['effect_time'] = date('Y-m-d H:i:s',$value['effect_time']);
            
            /*$input = array(
                'word' => $value['keyword'],
            );
            $res = Tieba_Service::call('uegmis', 'backEmoji', $input, null, null, 'post', null, 'utf-8');
            $value['keyword'] = $res['ret'];*/
        }

        $ret['output']['user_power_list'] = $role;
        $ret['output']['is_admin'] = $admin;

        $count = Tieba_Service::call('uegmis', 'countBatchWord', $arrInput, null, null, 'post', null, 'utf-8');
        $ret['output']['count'] = $count['ret']['rows'];
        if (self::$_intDownLoadFlag == 1){
            $arrData = self::_builDownLoadData($ret['output']['rows']);
            return self::downLoadBigFile($arrData['head_line'],$arrData['data'],$arrInput['wordlist_name'].'-'.date("YmdHi").'.txt');
        }
        echo json_encode($ret);
    }

    protected function _builDownLoadData($arrInput)
    {
        $arrHeadLine = array(
            'id',
            '词汇',
            '状态',
            '创建人',
            '创建时间',
            '生效时间',
            '失效时间',
            '删除人',
            '删除时间',
            '备注',
        );
        $output = array();
        foreach ($arrInput as $value){
            $status = "";
            if ($value['if_effective'] == 0) {
                $status = "生效";
            } else if ($value['if_effective'] == 2) {
                $status = "删除";
            }
            $output[] = array(
                $value['index'],
                $value['keyword'],
                $status,
                $value['adder'],
                $value['add_time'],
                $value['effect_time'],
                $value['expired_time'],
                $value['deler'],
                $value['del_time'],
                $value['memo'],
            );
        }
        return array('head_line' => $arrHeadLine, 'data' => $output);
    }

    //下载文件
    public function downLoadBigFile($arrHeadLine, $arrRowData,$strFileName='',$delimiter="\t",$strReplaceSymbol=' ')
    {
        //1.检验数据格式
        foreach ($arrRowData as $rowKey => $rowData){
            if(!is_array($rowData) || count($rowData) != count($arrHeadLine)){
                Bingo_Log::warning('row data format error!!!!headLine:['.serialize($arrHeadLine).'] rowdate['.serialize($rowData).']');
                return false;
            }
            foreach ($arrRowData as $itemKey => $item){
                if(is_string($item)){
                    $arrRowData[$rowKey][$itemKey] = str_replace($delimiter,$strReplaceSymbol,$item);
                }
            }
        }
        if(empty($strFileName)){
            $strFileName = date("YmdHi").".txt";
        }
        header('Content-Description: File Transfer');
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $strFileName . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        $fp = fopen('php://output', 'a');//打开output流
        //mb_convert_variables('GBK', 'UTF-8', $arrHeadLine);//防止中文乱码
        fputcsv($fp, $arrHeadLine);//,$delimiter);//将数据格式化为CSV格式并写入到output流中
        foreach ($arrRowData as $key => $rowData){
            $arrTempData = $rowData;
            //mb_convert_variables('GBK', 'UTF-8', $arrTempData);
            fputcsv($fp, $arrTempData);//,$delimiter);
            if($key % 100 === 0){
                //刷新输出缓冲到浏览器
                ob_flush();
                flush();//必须同时使用 ob_flush() 和flush() 函数来刷新输出缓冲。
            }
        }
        fclose($fp);
        return true;
    }
}
