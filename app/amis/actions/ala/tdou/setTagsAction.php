<?php


/**
 * Created by PhpStorm.
 * User: kangqinmou
 * Date: 18-5-28
 * Time: 下午4:59
 */
class setTagsAction extends Util_Action{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;


    /**
     * 执行函数
     * @param  null
     * @return array
     */
    public function _execute(){

        // 操作人
        $intUserId = Util_User::$intUserId;
        $strUserName = Util_User::$strUserName;

        // 参数
        $strDefaultIcon = (string) Bingo_Http_Request::get('default_icon', '');
        $arrParams = array();
        foreach(Lib_Tdou::$_TDIconInfo as $strIconId => $arrIconInfo){
            $arrParams[$strIconId] = array(
                'icon_id'    => $strIconId,
                'tag_name'   => (string) Bingo_Http_Request::get($strIconId.'_tag_name', ''),
                'is_default' => ($strIconId == $strDefaultIcon) ? 1 : 0,
            );
        }

        // 保存到redis
        $arrRet = Tieba_Service::call('tbmall', 'setPaymentTags', $arrParams, null, null, 'post', 'php', 'utf-8');
        $strError = '来自rd的温馨提示';
        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' set tag infos failed by calling tbmall.setPaymentTags service, input: [' . json_encode($arrParams) . '], output: [' . json_encode($arrRet) . "]. \n");
            return self::jsonAmisRet(422, array('errors' => array('default_icon' => '保存失败')), $strError);
        }

        return self::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS);
    }



    /**
     * @param  $intErrno
     * @param  array $arrExtData
     * @param  string $strMsg
     * @return bool
     */
    public static function jsonAmisRet($intErrno, $arrExtData = array(), $strMsg = ''){
        $strMsg = $strMsg ? $strMsg : Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput = array(
            'status' => $intErrno,
            'msg'    => $strMsg,
            'data'   => $arrExtData,
        );
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrOutput, Bingo_Encode::ENCODE_UTF8);
        return true;
    }

}