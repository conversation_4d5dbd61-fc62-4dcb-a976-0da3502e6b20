<?php


/**
 * Created by PhpStorm.
 * User: kangqinmou
 * Date: 18-5-28
 * Time: 下午4:59
 */
class getUserPayListAction extends Util_Action{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    protected $_arrMapType = array(
        12 => 'pc',
        9  => 'tieba_android',
        8  => 'tieba_ios',
        33 => 'ala_ios',
        48 => '手百IOS',
        49 => '手百android',
        50 => 'haokan_ios',
        51 => 'haokan_android',
        52 => 'quanmin_ios',
        53 => 'quanmin_android',
    );

    /**
     * 执行函数
     * @param  null
     * @return array
     */
    public function _execute(){

        // 操作人
        $intUserId = Util_User::$intUserId;
        $strUserName = Util_User::$strUserName;
        $user_id = intval(Bingo_Http_Request::get("user_id", 0));
        // 获取全局设置
        $arrInput = array(
            'user_id' => $user_id,
        );
        $arrRet = Tieba_Service::call('tbmall', 'checkUserPay', $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($arrRet['errno'] == Tieba_Errcode::ERR_ANTI_ID_BLOCKED) {
            return self::jsonAmisRet(Tieba_Errcode::ERR_ANTI_ID_BLOCKED, array(), "贴吧封禁");
        }
        if ($arrRet['errno'] == Tieba_Errcode::ERR_TBMALL_USER_NOT_MATCH) {
            return self::jsonAmisRet(Tieba_Errcode::ERR_TBMALL_USER_NOT_MATCH, array(), "黑名单");
        }
        if ($arrRet['errno'] == Tieba_Errcode::ERR_FORBID_OPERATION) {
            return self::jsonAmisRet(Tieba_Errcode::ERR_FORBID_OPERATION, array(), "IOS充值策略");
        }

        foreach ($arrRet['data'] as $key => $value) {
            $arrRet['data'][$key]['type'] = strval($this->_arrMapType[$value['type']]);
            $arrRet['data'][$key]['finish_time'] = date('Y-m-d h:i:s', $value['finish_time']);
        }
        $arrReturn = array(
            'count' => 100,
            'rows'  => $arrRet['data'],
        );
        

        return self::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, $arrReturn);
    }



    /**
     * @param  $intErrno
     * @param  array $arrExtData
     * @param  string $strMsg
     * @return bool
     */
    public static function jsonAmisRet($intErrno, $arrExtData = array(), $strMsg = ''){
        $strMsg = $strMsg ? $strMsg : Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput = array(
            'status' => $intErrno,
            'msg'    => $strMsg,
            'data'   => $arrExtData,
        );
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrOutput, Bingo_Encode::ENCODE_UTF8);
        return true;
    }

}