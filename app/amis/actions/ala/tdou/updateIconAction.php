<?php


/**
 * Created by PhpStorm.
 * User: kangqinmou
 * Date: 18-5-28
 * Time: 下午4:59
 */
class updateIconAction extends Util_Action{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;


    /**
     * 执行函数
     * @param  null
     * @return array
     */
    public function _execute(){


        // 操作人
        $intUserId = Util_User::$intUserId;
        $strUserName = Util_User::$strUserName;

        // 参数
        $intId = (int) Bingo_Http_Request::get('id', 0);  // 自增id
        $strError = '来自rd的温馨提示';
        if($intId <= 0){
            return self::jsonAmisRet(422, array('errors' => array('icon_id' => '参数错误: 自增id不能小于等于0')), $strError);
        }
        $arrParams = array(
            'icon_id'       => (string) Bingo_Http_Request::get('icon_id', ''),             // 印记id
            'name'          => (string) Bingo_Http_Request::get('name', ''),                // 印记名称
            'non_member_t'  => (int)    Bingo_Http_Request::get('non_member_t', 0),         // t豆数量
            'non_member_i'  => (int)    Bingo_Http_Request::get('non_member_i', 0),         // ios端t豆数量
            'dubi'          => (int)    Bingo_Http_Request::get('dubi', 0),                 // 多少钱(单位:分)
            'duration'      => (int)    Bingo_Http_Request::get('duration', 0),             // 有效期(单位:天)
            'props_id'      => (int)    Bingo_Http_Request::get('props_id', 0),             // 道具id
            'hide'          => (int)    Bingo_Http_Request::get('hide', 0),                 // 是否隐藏
            'ios_icon_id'   => (string) Bingo_Http_Request::get('ios_icon_id', ''),         // ios端印记id
            'ios_icon_name' => (string) Bingo_Http_Request::get('ios_icon_name', ''),       // ios端印记名称
            'validity'      => (int)    Bingo_Http_Request::get('validity', 0),             // ios端印记有效期
            'dubi_ios'      => (int)    Bingo_Http_Request::get('dubi_ios', 0),             // ios端多少钱(单位:分)
            'ios_display'   => (int)    Bingo_Http_Request::get('ios_display', 0),          // ios端是否显示
            'op_uname'      => $strUserName,
        );
        foreach(array('duration', 'props_id') as $field){
            if(!isset($arrParams[$field]) || $arrParams[$field] <= 0){
                return self::jsonAmisRet(422, array('errors' => array($field => "参数错误: {$field}不能小于等于0")), $strError);
            }
        }
        foreach(array('name', 'icon_id') as $field){
            if(!isset($arrParams[$field]) || !$arrParams[$field]){
                return self::jsonAmisRet(422, array('errors' => array($field => "参数错误: {$field}不能为空")), $strError);
            }
        }

        if(isset(Lib_Tdou::$_TDIconInfo[$arrParams['icon_id']])){
            return self::jsonAmisRet(422, array('errors' => array('icon_id' => '印记不能是这4个：' . join(', ', array_keys(Lib_Tdou::$_TDIconInfo)))), $strError);
        }

        // 获取印记图片
        $arrInput = array(
            'name' => $arrParams['icon_id'],
        );
        $arrIconInfos = Tieba_Service::call('icon', 'getIcon', $arrInput, null, null, 'post', 'php', 'gbk');
        if(!$arrIconInfos || Tieba_Errcode::ERR_SUCCESS != $arrIconInfos['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' get icon list failed by calling icon.getIcon service, input: [' . json_encode($arrInput) . '], output: [' . json_encode($arrIconInfos) . "]. \n");
            return self::jsonAmisRet(422, array('errors' => array('icon_id' => "印记{$arrParams['icon_id']}不存在，请前往 http://imis.tieba.baidu.com/autorout/mis/icon#/icon_list 选择合适的印记")), $strError);
        }
        $arrPicUrls = isset($arrIconInfos['data']['pic']) ? json_decode($arrIconInfos['data']['pic'], true) : array();
        if(empty($arrPicUrls) || !is_array($arrPicUrls)){
            return self::jsonAmisRet(422, array('errors' => array('icon_id' => "印记{$arrParams['icon_id']}图片不存在，请前往 http://imis.tieba.baidu.com/autorout/mis/icon#/icon_list 上传")), $strError);
        }
        ksort($arrPicUrls);
        $arrParams['pic_url'] = reset($arrPicUrls);
        unset($arrPicUrls, $arrIconInfos);

        // 获取ios端的印记图片
        $arrParams['ios_icon_pic'] = '';
        if($arrParams['ios_icon_id']){
            $arrInput = array(
                'name' => $arrParams['ios_icon_id'],
            );
            $arrIconInfos = Tieba_Service::call('icon', 'getIcon', $arrInput, null, null, 'post', 'php', 'gbk');
            if(!$arrIconInfos || Tieba_Errcode::ERR_SUCCESS != $arrIconInfos['errno']){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' get icon list failed by calling icon.getIcon service, input: [' . json_encode($arrInput) . '], output: [' . json_encode($arrIconInfos) . "]. \n");
                return self::jsonAmisRet(422, array('errors' => array('ios_icon_id' => "印记{$arrParams['ios_icon_id']}不存在，请前往 http://imis.tieba.baidu.com/autorout/mis/icon#/icon_list 选择合适的印记")), $strError);
            }
            $arrPicUrls = isset($arrIconInfos['data']['pic']) ? json_decode($arrIconInfos['data']['pic'], true) : array();
            if(empty($arrPicUrls) || !is_array($arrPicUrls)){
                return self::jsonAmisRet(422, array('errors' => array('ios_icon_id' => "印记{$arrParams['ios_icon_id']}图片不存在，请前往 http://imis.tieba.baidu.com/autorout/mis/icon#/icon_list 上传")), $strError);
            }
            ksort($arrPicUrls);
            $arrParams['ios_icon_pic'] = reset($arrPicUrls);
            unset($arrPicUrls, $arrIconInfos);
        }

        // 更新tdou_recharge_icon
        $arrInput = array(
            'field' => $arrParams,
            'cond'  => array(
                'id' => $intId,
            ),
        );
        $arrRet = Tieba_Service::call('tbmall', 'updateTdouRechargeIcon', $arrInput, null, null, 'post', 'php', 'utf-8');
        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' update icon failed by calling tbmall.updateTdouRechargeIcon service, input: [' . json_encode($arrInput) . '], output: [' . json_encode($arrRet) . "]. \n");
            return self::jsonAmisRet(422, array('errors' => array('icon_id' => '更新失败')), $strError);
        }
        if(empty($arrRet['data'])){
            return self::jsonAmisRet(422, array('errors' => array('icon_id' => "记录{$intId}不存在")), $strError);
        }
        unset($arrRet);

        return self::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS);
    }



    /**
     * @param  $intErrno
     * @param  array $arrExtData
     * @param  string $strMsg
     * @return bool
     */
    public static function jsonAmisRet($intErrno, $arrExtData = array(), $strMsg = ''){
        $strMsg = $strMsg ? $strMsg : Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput = array(
            'status' => $intErrno,
            'msg'    => $strMsg,
            'data'   => $arrExtData,
        );
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrOutput, Bingo_Encode::ENCODE_UTF8);
        return true;
    }

}