<?php


/**
 * Created by PhpStorm.
 * User: kangqinmou
 * Date: 18-5-28
 * Time: 下午4:59
 */
class selectIconAction extends Util_Action{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;


    /**
     * 执行函数
     * @param  null
     * @return array
     */
    public function _execute(){


        // 操作人
        $intUserId = Util_User::$intUserId;
        $strUserName = Util_User::$strUserName;
        $intPs = (int) Bingo_Http_Request::get('ps', 30);
        $intPs = ($intPs < 1) ? 30 : $intPs;
        $intPn = (int) Bingo_Http_Request::get('pn', 1);
        $intPn = ($intPn < 1) ? 1 : $intPn;

        // 获取印记列表
        $arrParams = array(
            'cond'   => array(),
            'append' => 'order by id desc limit ' . ($intPs*$intPn - $intPs) . ", {$intPs}",
        );
        $arrIconInfos = Tieba_Service::call('tbmall', 'selectTdouRechargeIcon', $arrParams, null, null, 'post', 'php', 'utf-8');
        if(!$arrIconInfos || Tieba_Errcode::ERR_SUCCESS != $arrIconInfos['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' get icon list failed by calling tbmall.selectTdouRechargeIcon service, input: [' . json_encode($arrParams) . '], output: [' . json_encode($arrIconInfos) . "]. \n");
            return self::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrIconInfos = $arrIconInfos['data'];

        // format
        foreach($arrIconInfos as &$item){
            $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
            $item['update_time'] = date('Y-m-d H:i:s', $item['update_time']);
            $item['hide_map']    = (0 == $item['hide']) ? '上线' : '下线';
            if('tbxiaoniu' == $item['icon_id']){
                $item['name_html']  = "<div style='color:red;'>{$item['name']}<br/>(自定义金额的印记)</div>";
            }else{
                $item['name_html'] = $item['name'];
            }
        }
        unset($item);

        // 获取印记数量
        $arrParams = array(
            'cond'   => array(),
            'field'  => array('count(1) as num'),
        );
        $arrCount = Tieba_Service::call('tbmall', 'selectTdouRechargeIcon', $arrParams, null, null, 'post', 'php', 'utf-8');
        if(!$arrCount || Tieba_Errcode::ERR_SUCCESS != $arrCount['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' get icon count failed by calling tbmall.selectTdouRechargeIcon service, input: [' . json_encode($arrParams) . '], output: [' . json_encode($arrCount) . "]. \n");
            return self::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $intCount = isset($arrCount['data'][0]['num']) ? intval($arrCount['data'][0]['num']) : 0;

        // 返回结果(含当前页面的记录以及记录总量)
        return self::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, array(
            'rows'  => $arrIconInfos,
            'count' => $intCount,
        ), 'success');
    }



    /**
     * @param  $intErrno
     * @param  array $arrExtData
     * @param  string $strMsg
     * @return bool
     */
    public static function jsonAmisRet($intErrno, $arrExtData = array(), $strMsg = ''){
        $strMsg = $strMsg ? $strMsg : Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput = array(
            'status' => $intErrno,
            'msg'    => $strMsg,
            'data'   => $arrExtData,
        );
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrOutput, Bingo_Encode::ENCODE_UTF8);
        return true;
    }

}