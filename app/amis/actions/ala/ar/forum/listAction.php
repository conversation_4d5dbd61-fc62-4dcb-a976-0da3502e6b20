<?php

/**
 * Forum AR - 列表
 */
class listAction extends Util_Action
{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    /**
     *
     * @return int
     */
    public function _execute()
    {
        // 操作人
        $intUserId = Util_User::$intUserId;
        $strUserName = Util_User::$strUserName;
        
        // Request
        $intPage = intval(Bingo_Http_Request::get('page', 1));
        // 列表数据条数
        $intReqNum = 10;
        
        // 拉取列表
        $arrOutput = Dl_Ala_Ar_Forum::getList($intPage, $intReqNum);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Ar_Forum::getList fail. input:[" . $intPage . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL, "服务器出错");
        }
        $arrList = $arrOutput['data'];
        foreach ($arrList as $intKey => $arrOne) {
            if (isset($arrOne['json']) && strlen($arrOne['json']) > 0) {
                $arrJson = json_decode($arrOne['json'], true);
                $arrOne = array_merge($arrOne, $arrJson);
                
                unset($arrOne['json']);
                $arrList[$intKey] = $arrOne;
            }
        }
        
        // 总条数
        $arrOutput = Dl_Ala_Ar_Forum::getCount();
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Ar_Forum::getCount fail. input:[" . $intPage . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL, "服务器出错");
        }
        $intCount = intval($arrOutput['data']['count']);
        
        // 返回列表
        $arrRet = array(
            'rows' => $arrList,
            'count' => $intCount
        );
        // 返回
        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrRet);
    }

    /**
     * @brief _jsonRet
     *
     * @param
     *            errno,errmsg,data
     * @return : 0.
     *        
     */
    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => strval($errmsg),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }
}