<?php
/**
 * Created by PhpStorm.
 * “企业推广大使”二期：FRS头条贴
 * User: weiyan02
 * Date: 2019/8/15
 * Time: 下午5:45
 */

class addAction extends Util_Action
{
    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = true;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;


    const FORUM_MANAGER_FLAG = 1;//吧主配置的直播贴的标记
    const FRS_HEADLINE_ATTR = 'frsheadline';
    private $_intForumId = 0;


    /**
     *
     * @return int
     */
    public function _execute()
    {
        //操作人
        $strUserName = Util_User::$strUserName;
        if (empty($strUserName)) {
            return self::_jsonRet(Alalib_Conf_Error::ERR_USER_NOT_LOGIN, "请先登录");
        }

        $arrInput = Bingo_Http_Request::getPostAll();
        $arrInput['op_user'] = $strUserName;

        //0. 将输入的按指定符号（空格）拼接而成的吧名字符串转换成吧id列表
        if (!empty($arrInput['forum_name_list'])) {
            $fnameResult = Util_Schedule_Common::getFnameStr2FidList($arrInput['forum_name_list']);
            if (!empty($fnameResult['error_fname_list'])) {
                return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "请检查投放目标吧，输入的吧名不存在：".join(" , ", $fnameResult['error_fname_list']));
            }
            $arrInput['forum_ids'] = $fnameResult['fid_list'];
        }
        if (empty($arrInput['forum_ids'])) {
            return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "请输入正确的吧名");
        }

        //1、头条贴ID校验
        $arrInput['link_info_thread_id'] = $arrInput['thread_id'];
        if (!empty($arrInput['link_info_thread_id'])) {
            $validRet = $this->validThreadId($arrInput['link_info_thread_id']);
            if ($validRet == false) {
                Bingo_Log::warning("check thread id failed!");
                return self::_jsonRet(Tieba_Errcode::ERR_POST_THREAD_NOT_EXIST, "头条贴ID无效，请重新输入");
            }
        }

        //2、校验是否同一吧内广告贴是否互斥
        $arrOutput = Tieba_Service::call('tbmall', 'checkConflict', $arrInput, null, null, 'post', 'php', 'utf-8', 'local');
        if (false === $arrOutput) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call tbmall::createScheduleResource checkConflict fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "服务器出错");
        }
        if (Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call tbmall::createScheduleResource checkConflict fail, param error. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, $arrOutput['errmsg']);
        }
        //3、添加pb扩展属性
        foreach ($arrInput['forum_ids'] as $intForumId) {
            $arrPbInput = array(
                'forum_id' => $intForumId,
                'thread_id' => $arrInput['link_info_thread_id'],
                'ext_infos' => array(array('key' => 'is_b_headline', 'value' => 1)),
            );

            $strService = 'post';
            $strMethod = 'setExtAttr';
            $arrPbRes = Tieba_Service::call($strService, $strMethod, $arrPbInput, null, null, 'post', 'php', 'utf-8');
            if (false === $arrPbRes || Tieba_Errcode::ERR_SUCCESS !== $arrPbRes['errno']) {
                Bingo_Log::warning("call $strService $strMethod fail input[" . serialize($arrPbInput) . "] out[" . serialize($arrPbRes) . "]");
                return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "更新贴子属性失败，请重试");
            }
        }

        Bingo_Log::warning("call post setExtAttr success input[".serialize($arrInput['forum_ids'])."]");

        //4、同步帖子属性
        $arrForumIds = $arrInput['forum_ids'];

        $objRalMulti = new Tieba_Multi("act_multi_retry");
        $arrMultiCallParamMap = array();
        foreach($arrForumIds as $intKey) {
            $arrAttrInput = array(
                'forum_id' => $intKey,
                'attr_name' => self::FRS_HEADLINE_ATTR,
                'attr_value' => array(
//                    'start_time' => $arrInput['start_time'],
//                    'end_time' => $arrInput['end_time'],
//                    'thread_id' => $arrInput['link_info_thread_id'],
                'has_frsheadline'=> 1,
                ),
            );
            $arrMultiInput = array(
                'serviceName' => 'forum',
                'method' => 'setForumAttr',
                'input' => $arrAttrInput,
            );
            $arrMultiCallParamMap[$intKey] = $arrAttrInput;
            $objRalMulti->register($intKey, new Tieba_Service('forum'), $arrMultiInput);
        }

        $arrRes = $objRalMulti->call();
        Bingo_Log::pushNotice("multi_call_setattr_result", serialize($arrRes));
        foreach ($arrForumIds as $intForumId) {
            $strKey = $intForumId;
            $arrResult = $arrRes[$strKey];
            if ($arrResult === false || $arrResult['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("call forum setForumAttr fail and key=[" . $intForumId . "]");
                return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "同步帖子属性失败，请重试");
            }
        }

        //5、投放操作
        $arrOutput = Tieba_Service::call('tbmall', 'createBusinessHeadline', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrOutput) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call tbmall::createScheduleResource fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "服务器出错");
        }
        if (Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call tbmall::createScheduleResource fail, param error. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, $arrOutput['errmsg']);
        }
        
        // 返回
        return self::_jsonRet(Alalib_Conf_Error::ERR_SUCCESS, "创建投放计划成功");
    }


    /**
     * @param $threadId
     * @return mixed
     */
    private function validThreadId($threadId)
    {
        $arrParam = array(
            "thread_id" => $threadId, //帖子id
            "offset" => 0,
            "res_num" => 30,  // 取前50楼
            "see_author" => 1,
            "has_comment" => 0,
            "has_mask" => 1,
            "has_ext" => 1,
            "need_set_pv" => 1,
            "structured_content" => 0  // 非格式化数据
        );
        $arrRet = Tieba_Service::call('post', 'getPostsByThreadId', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (!$arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning("call post::getPostsByThreadId failed. input=[" . serialize($arrParam) . "],output=[" . serialize($arrRet) . "]");
            return false;
        }
        $arrPostInfo = $arrRet['output']['output'][0];
        // 帖子被删除
        if (($arrPostInfo['is_thread_deleted'] == 1) || ($arrPostInfo['user_mask'] == 1) || ($arrPostInfo['is_thread_mask'] == 1) || ($arrPostInfo['is_thread_exceed'] == 1) || ($arrPostInfo['is_thread_visible'] == 1) || empty($arrPostInfo['forum_id'])) {
            Bingo_Log::warning("thread is delete. thread_id=[" . $threadId . "] call post::getPostsByThreadId . input=[" . serialize($arrParam) . "],output=[" . serialize($arrRet) . "]");
            return false;
        }
        Bingo_Log::warning("add promote, getPostsByThreadId input:[" . serialize($arrParam) . "] output:[" . serialize($arrRet) . "]");

        $this->_intForumId = $arrPostInfo['forum_id'];

        return true;
    }

    /**
     * @brief _jsonRet
     * @param errno,errmsg,data
     * @param string $errmsg
     * @param array $arrExtData
     * @return int : 0.
     */
    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => urldecode($errmsg),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }

}