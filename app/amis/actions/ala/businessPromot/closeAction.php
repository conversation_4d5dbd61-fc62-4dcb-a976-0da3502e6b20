<?php

/**
 *  后台api - 下线计划
 */
class closeAction extends Util_Action
{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = true;

    /**
     *
     * @return int
     */
    public function _execute()
    {
        // 操作人
        $strUserName = Util_User::$strUserName;
        if (empty($strUserName)) {
            return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "请先登录");
        }
        $id = intval(Bingo_Http_Request::get('id', 0));
        if ($id < 1) {
            Bingo_Log::warning("loss param, id={$id}");
            return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //查询投放计划
        $arrInput = array(
            'id' => $id
        );
        $arrResInfoRet = Tieba_Service::call("tbmall", "getBusinessPromotResourceById", $arrInput, null,null,'post','php','utf-8');
        if ($arrResInfoRet ==  false || $arrResInfoRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("getBusinessPromotResourceById fail. input[".serialize($arrInput)." output[".serialize($arrResInfoRet)."]");
            return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "获取投放计划失败");
        }
        if (empty($arrResInfoRet['data'])) {
            Bingo_Log::warning("getBusinessPromotResourceById without data ,  input[".serialize($arrInput)." output[".serialize($arrResInfoRet)."]");
            return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "无此投放计划");
        }


        //更新投放资源状态
        $arrInput = array(
            'id' => $id,
            'status' => -1,
            'op_user' => $strUserName
        );
        $arrRet = Tieba_Service::call("tbmall", "updateBusinessPromotStatus", $arrInput, null,null,'post','php','utf-8');
        if ($arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("updateScheduleResourceStatus fail. ".serialize($arrRet));
            return self::_jsonRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, "更新失败");
        }

        //只对置顶热议贴进行解锁操作
        $type = $arrResInfoRet['data']['type'];
        if ($type == 1) {
            $linkInfo = json_decode($arrResInfoRet['data']['link_info'], true);
            if (empty($linkInfo['thread_id'])) {
                Bingo_Log::warning("schedule without thread_id, link_info[".serialize($linkInfo)."] ");
                return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "该投放计划未配置帖子id");
            }
            $lockRet = $this->_unlockLiveThread($linkInfo['thread_id']);
            if ($lockRet == false) {
                return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "帖子解锁失败");
            }

            //检查贴子是否在其他投放计划中，以确定是否需要清除pb贴子属性
            $arrCheckInput = array(
                'tids' => array($linkInfo['thread_id']),
                'neq_id' => $id
            );
            $resetMapRet = Tieba_Service::call('tbmall', 'checkOtherFrsPromoteTid', $arrCheckInput, null, null, 'post', 'php', 'utf-8', 'local');
            Bingo_Log::warning("checkOtherFrsPromoteTid ret=[".serialize($resetMapRet)."]");
            if (false === $resetMapRet || $resetMapRet['errno'] != Tieba_Errcode::ERR_SUCCESS || empty($resetMapRet['data'])) {
                Bingo_Log::warning("checkOtherFrsPromoteTid  fail ，input[".serialize($arrCheckInput)."], output[".serialize($resetMapRet)."] \n");
                return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "检查贴子是否在其他投放计划中失败，请重试");
            }
            //返回map值等于1才清除
            if ($resetMapRet['data'][$linkInfo['thread_id']] == 0) {
                Bingo_Log::warning("thread_id={$linkInfo['thread_id']} need not reset");
            } else {
                $forumId = $this->getFIdByTid($linkInfo['thread_id']);
                //清除贴子置顶热议贴属性
                $arrPbRes = $this->_setPbPromotAttr($forumId, $linkInfo['thread_id'], 0);
                if (false === $arrPbRes || Tieba_Errcode::ERR_SUCCESS !== $arrPbRes['errno']) {
                    Bingo_Log::warning("call _setPbPromotAttr (old) fail,id={$id} thread_id={$linkInfo['thread_id']}, value=0 out[".serialize($arrPbRes)."]");
                    return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "更新原贴子属性失败，请重试");
                }
                Bingo_Log::warning("call _setPbPromotAttr (old) , id={$id} thread_id={$linkInfo['thread_id']}, value=0 out[".serialize($arrPbRes)."]");
            }
        }

        //返回
        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, "更新成功");
    }


    /**
     * @param $threadId
     * @return mixed
     */
    private function getFIdByTid($threadId)
    {
        $arrParam = array(
            "thread_id" => $threadId, //帖子id
            "offset" => 0,
            "res_num" => 30,  // 取前50楼
            "see_author" => 1,
            "has_comment" => 0,
            "has_mask" => 1,
            "has_ext" => 1,
            "need_set_pv" => 1,
            "structured_content" => 0  // 非格式化数据
        );
        $arrRet = Tieba_Service::call('post', 'getPostsByThreadId', $arrParam, null, null, 'post', 'php', 'utf-8');
        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning("call post::getPostsByThreadId failed. input=[" . serialize($arrParam) . "],output=[" . serialize($arrRet)."]");
            return 0;
        }
        $arrPostInfo = $arrRet['output']['output'][0];
        return $arrPostInfo['forum_id'];
    }


    /**
     * @param $intForumId
     * @param $threadId
     * @param $value
     * @return bool|mixed|multitype
     */
    private function _setPbPromotAttr($intForumId, $threadId, $value)
    {
        $arrPbInput = array(
            'forum_id'  => $intForumId,
            'thread_id' => $threadId,
            'ext_infos' => array(array('key'=>'is_b_promot', 'value'=>$value)),
        );

        $strService = 'post';
        $strMethod  = 'setExtAttr';
        $arrPbRes = Tieba_Service::call($strService, $strMethod, $arrPbInput, null, null, 'post', 'php', 'utf-8');
        return $arrPbRes;
    }

    /**
     * 解锁贴子
     *
     * @param $tid
     *
     * @return bool
     */
    private static function _unlockLiveThread($tid)
    {
        //解锁
        $arrParams = array(
            'req' => array(
                'thread_infos' => array($tid),
            ),
        );
        $arrRet    = Tieba_Service::call("anti", "antiUnlockThreadNew", $arrParams, null, null, 'post', 'php', 'utf-8');
        if($arrRet === false || !isset($arrRet['errno'])){
            Bingo_Log::warning("service_anti_antiUnlockThread error. [input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
            return false;
        }
        Bingo_Log::warning("service_anti_antiUnlockThread success. [input=".serialize($arrParams)."][output=".serialize($arrRet)."]");

        return true;
    }

    /**
     * @brief _jsonRet
     *
     * @param
     *            errno,errmsg,data
     * @return : 0.
     *        
     */
    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => strval($errmsg),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }
}