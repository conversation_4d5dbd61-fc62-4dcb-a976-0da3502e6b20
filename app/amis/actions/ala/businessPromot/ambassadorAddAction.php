<?php

/**
 *  添加企业大使接口
 */
class ambassadorAddAction extends Util_Action
{
    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    /**
     *
     * @return int
     */
    public function _execute()
    {
        // 支持输入吧名
        $fid = Bingo_Http_Request::getGet("forum_id");
        $forumName = Bingo_Http_Request::getGet("forum_name", "");
        if (!empty($forumName)) {
            Bingo_Log::warning("loss forum_name param");
            $fidMap = Util_Schedule_Common::getFidMapByFname(array($forumName));
            $fid = $fidMap[$forumName]['forum_id'];
        }
        if (empty($fid)) {
            Bingo_Log::warning("forum_name is invalid, forum_name: {$forumName}, fid: {$fid}");
            return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "请输入有效的吧名");
        }

        // 支持输入用户名
        $uid = Bingo_Http_Request::getGet("uid");
        if (empty($uid)) {
            $userName = Bingo_Http_Request::getGet("user_name", "");
            if (empty($userName)) {
                Bingo_Log::warning("loss user_name param");
                return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "请输入用户名");
            }
            $uid = Util_Schedule_Common::getUidByUname($userName);
        }
        if (empty($uid)) {
            Bingo_Log::warning("forum_name is invalid, forum_name: {$forumName}, uid: {$uid}");
            return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "请输入有效的用户名");
        }

        $arrInput = array(
            'status' => 1,
            'forum_id' => $fid,
            'uid' => $uid,
            'op_user' => Util_User::$strUserName
        );
        $arrRet = Tieba_Service::call('tbmall', 'createAmbassadorAccount', $arrInput, null, null, 'post', 'php', 'utf-8', 'local');
        if (false === $arrRet || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call tbmall::getAmbassadorAccount fail. input:[".serialize($arrInput)."]  output:[".serialize($arrRet)."]");
            return self::_jsonRet($arrRet['errno'], $arrRet['errmsg']);
        }
        
        //返回列表
        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrRet['data']);
    }

    /**
     * @param $userName
     * @return bool|mixed
     */
    private static function getUidByUname($userName)
    {
        $arrInput = array(
            $userName
        );
        $arrRet = Tieba_Service::call('user', 'getUidByUnames', $arrInput, null, null, 'post', 'php', 'utf-8', 'local');
        return $arrRet;
    }

    /**
     * @brief _jsonRet
     *
     * @param
     *            errno,errmsg,data
     * @return : 0.
     *        
     */
    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => strval($errmsg),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }
}