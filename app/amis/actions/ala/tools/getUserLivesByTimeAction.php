<?php

/**
 * Created by PhpStorm.
 * User: nameless
 * Date: 18/3/21
 * Time: 下午4:48
 */
class getUserLivesByTimeAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * 执行函数
	 *
	 * @param null
	 *
	 * @return null
	 * */
	public function _execute()
	{
		//数据获取
		$user_name  = strval(Bingo_Http_Request::getNoXssSafe('user_name', ''));
		$user_id    = strval(Bingo_Http_Request::getNoXssSafe('user_id', ''));
		$user_from  = strval(Bingo_Http_Request::getNoXssSafe('user_from', ''));
		$start_time = intval(Bingo_Http_Request::getNoXssSafe('start_time', 0));
		$end_time   = intval(Bingo_Http_Request::getNoXssSafe('end_time', 0));
		if(empty($start_time) && empty($end_time)){
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), '请输入时间范围');
		}else{
			if(empty($start_time)){
				$start_time = $end_time;
			}
			if(empty($end_time)){
				$end_time = $start_time;
			}
		}
		if($user_from == 'tieba'){
			//如果是贴吧的 调用贴吧的方法
			if(empty($user_id)){
				$user_id = Util_Ala_Common::getUidByUname($user_name);
			}
		}else{
			//如果不是贴吧的 调用手百的方法
			if(empty($user_id)){
				$arrReq     = array(
					'nick_name' => $user_name,
				);
				$strService = 'ala';
				$strMethod  = 'userNickNameGetUid';
				$arrOutput  = Tieba_Service::call($strService, $strMethod, $arrReq, null, null, 'post', 'php', 'utf-8');
				if($arrOutput === false || $arrOutput['errno'] != Alalib_Conf_Error::ERR_SUCCESS || 200204 == $arrOutput['errno']){
					$strLog = __CLASS__."::".__FUNCTION__." call $strService::$strMethod fail. input[".serialize(array())."] output[".serialize($arrOutput)."]";
					Bingo_Log::warning($strLog);
				}
				$user_id = (int)$arrOutput['data']['uid'];
			}
		}
		if(empty($user_id)){
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), '查无此人!');
		}
		//获取直播信息
		$arrReq       = array(
			'user_id'    => $user_id,
			'start_time' => $start_time,
			'end_time'   => $end_time,
		);
		$arrUserLives = Tieba_Service::call('ala', 'getUserLiveIdsInTime', $arrReq, null, null, 'post', 'php', 'utf-8');
		if($arrUserLives['errno'] !== Tieba_Errcode::ERR_SUCCESS || empty($arrUserLives)){
			Bingo_Log::warning('get  user info fail,the input is '.serialize($arrReq).' the output is '.serialize($arrUserLives));
		}
		//返回
		return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, array(
			'rows' => $arrUserLives['data'],
		), '成功');
	}
	
}

?>
