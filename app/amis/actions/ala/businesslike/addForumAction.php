<?php

/**
 * Created by PhpStorm.
 * User: zhouyan08
 * Date: 18/4/27
 * Time: 下午4:48
 */
class addForumAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * 执行函数
	 *
	 * @param null
	 *
	 * @return null
	 * */
	public function _execute()
	{
		//数据获取
		$id          = intval(Bingo_Http_Request::get('id', 0));
		$status      = intval(Bingo_Http_Request::get('status', 0));
		$forum_names = str_ireplace(',', "\n", str_ireplace(' ', "\n", Bingo_Http_Request::get('forum_name_add', "")));
		$op_id       = Util_User::$intUserId;
		$op_name     = Util_User:: $strUserName;
		//拉取原来的直播任务信息
		$arrInput   = array(
			'id'     => $id,
			'status' => $status,
		);
		$arrOutput  = Tieba_Service::call('live', 'getLiveTaskList', $arrInput, null, null, 'post', 'php', 'utf-8', 'local');
		$arrOldInfo = $arrOutput['data']['list'][0];
		if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"] || empty($arrOldInfo)){
			Bingo_Log::warning(var_export($arrOutput, true));
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '获取旧数据失败!请重试!');
		}
		$old_forums  = $arrOldInfo['forum_names'];
		$old_forums  = self::remEmpty(explode("\n", $old_forums));
		$forum_names = self::remEmpty(explode("\n", $forum_names));
		foreach($forum_names as $forum){
			if(empty($forum)){
				continue;
			}
			if(!in_array($forum, $old_forums)){
				$old_forums[] = $forum;
			}
		}
		$forum_names = $old_forums;
		//更新直播任务信息
		$arrInput  = array(
			'id'           => $id,
			'op_user_name' => $op_name,
			'forum_names'  => implode("\n", $forum_names),
		);
		$arrOutput = Tieba_Service::call('live', 'updateLiveTaskByTid', $arrInput, null, null, 'post', null, 'utf-8');
		if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
			Bingo_Log::warning(serialize($arrInput)."          ".serialize($arrOutput));
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '编辑直播任务信息失败!请重试!');
		}
		//更新直播任务信息完成
		//更新任务资源信息和吧属性
		$forumInput = array('forum_names' => implode("\n", $forum_names));
		$arrRes     = Tieba_Service::call('live', 'getForumIds', $forumInput, null, null, 'post', 'php', 'utf-8');
		Bingo_Log::warning(var_export($forumInput, true));
		if(false === $arrRes || Alalib_Conf_Error::ERR_SUCCESS != $arrRes["errno"]){
			Bingo_Log::warning(var_export($arrRes, true));
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '获取吧信息失败!请重试!');
		}
		$forumInfoNew = $arrRes['data'];
		$arrInput     = array(
			'task_id'       => $id,
			'task_name'     => $arrOldInfo['title'],
			'status'        => 0,
			'start_time'    => $arrOldInfo['start_time'],
			'end_time'      => $arrOldInfo['end_time'],
			"forums"        => $forumInfoNew,
			"thread_id"     => $arrOldInfo['thread_id'],
			"show_filter"   => $arrOldInfo['show_filter'],
			"type"          => $arrOldInfo['type'],
			"operator_flag" => $arrOldInfo['content']['operator_flag'],
		);
		$arrOutput    = Tieba_Service::call('live', 'replaceLiveRelation', $arrInput, null, null, 'post', null, 'utf-8');
		if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
			Bingo_Log::warning(var_export($arrOutput, true));
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '更新任务资源信息失败!请重试!');
		}
		//更新任务资源信息和吧属性完成
		return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, array(), '编辑成功!');
	}
	
	/**
	 * @param $arrInput
	 *
	 * @return array
	 */
	private static function remEmpty($arrInput)
	{
		$ret = array();
		foreach($arrInput as $single){
			if(!empty($single)){
				$ret[] = $single;
			}
		}
		return $ret;
	}
}

?>
