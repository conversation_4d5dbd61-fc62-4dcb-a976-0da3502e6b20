<?php

/**
 * Created by PhpStorm.
 * User: zhouyan08
 * Date: 18/4/27
 * Time: 下午4:48
 */
class delForumAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * 执行函数
	 *
	 * @param null
	 *
	 * @return null
	 * */
	public function _execute()
	{
		//数据获取
		$id          = intval(Bingo_Http_Request::get('id', 0));
		$status      = intval(Bingo_Http_Request::get('status', 0));
		$forum_names = str_ireplace(',', "\n", str_ireplace(' ', "\n", Bingo_Http_Request::get('forum_name_del', "")));
		Bingo_Log::warning(var_export($forum_names, true));
		$op_id   = Util_User::$intUserId;
		$op_name = Util_User:: $strUserName;
		//拉取原来的直播任务信息
		$arrInput   = array(
			'id'     => $id,
			'status' => $status,
		);
		$arrOutput  = Tieba_Service::call('live', 'getLiveTaskList', $arrInput, null, null, 'post', 'php', 'utf-8', 'local');
		$arrOldInfo = $arrOutput['data']['list'][0];
		if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"] || empty($arrOldInfo)){
			Bingo_Log::warning(var_export($arrOutput, true));
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '获取旧数据失败!请重试!');
		}
		$old_forums  = $arrOldInfo['forum_names'];
		$old_forums  = self::remEmpty(explode("\n", $old_forums));
		$forum_names = self::remEmpty(explode("\n", $forum_names));
		foreach($forum_names as $forum_name){
			if(empty($forum_name)){
				continue;
			}
			if(in_array($forum_name, $old_forums)){
				for($i = 0; $i < count($old_forums); $i++){
					if($old_forums[$i] == $forum_name){
						unset($old_forums[$i]);
						$old_forums = array_values($old_forums);
						break;
					}
				}
			}
		}
		Bingo_Log::warning(var_export($old_forums, true));
		//更新直播任务信息
		$arrInput  = array(
			'id'           => $id,
			'op_user_name' => $op_name,
			'forum_names'  => implode("\n", $old_forums),
		);
		$arrOutput = Tieba_Service::call('live', 'updateLiveTaskByTid', $arrInput, null, null, 'post', null, 'utf-8');
		if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
			Bingo_Log::warning(serialize($arrInput)."          ".serialize($arrOutput));
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '编辑直播任务信息失败!请重试!');
		}
		//更新直播任务信息完成
		//删除旧吧任务
		if(!empty($forum_names)){
			$forumInput = array('forum_names' => implode("\n", $forum_names));
			$arrRes     = Tieba_Service::call('live', 'getForumIds', $forumInput, null, null, 'post', 'php', 'utf-8');
			if(false === $arrRes || Alalib_Conf_Error::ERR_SUCCESS != $arrRes["errno"]){
				Bingo_Log::warning(var_export($arrRes, true));
				return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '获取吧属性失败!请重试!');
			}
			$forumInfo = $arrRes['data'];
			foreach($forumInfo as $item){
				$arrInput = array(
					'id'              => $id,
					'forum_id'        => $item['forum_id'],
					'forum_name'      => $item['forum_name'],
					'close_user_name' => $op_name,
				);
				$arrRes   = Tieba_Service::call('live', 'closeLiveTaskByFid', $arrInput, null, null, 'post', 'php', 'utf-8');
				if(false === $arrRes || Alalib_Conf_Error::ERR_SUCCESS != $arrRes["errno"]){
					Bingo_Log::warning(var_export($arrRes, true));
					return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '删除旧吧任务失败!请重试!');
				}
			}
		}
		return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, array(), '编辑成功!');
	}
	
	/**
	 * @param $arrInput
	 *
	 * @return array
	 */
	private static function remEmpty($arrInput)
	{
		$ret = array();
		foreach($arrInput as $single){
			if(!empty($single)){
				$ret[] = $single;
			}
		}
		return $ret;
	}
}

?>
