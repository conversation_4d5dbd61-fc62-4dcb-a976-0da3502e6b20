<?php
/**
 * 删除合作运营的花瓣
 * Created by PhpStorm.
 * User: zhanghanqing
 * Date: 2018/1/20
 * Time: 17:28
 */


class delCooperatePetalAction extends Util_Action
{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    /**
     * @return
     */
    public function _execute()
    {
        $intLoginUserId = Util_User::$intUserId;
        $strOpUserName = Util_User::$strUserName;
        if (empty($intLoginUserId)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error.;";
            Bingo_Log::warning($strLog);
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        // 平台运营删除则传此字段
        $intIsPlatform= intval(Bingo_HTTP_Request::get('is_platform', 0));
        $intDelUserId = intval(Bingo_HTTP_Request::get('user_id', 0));
        $intDelNum = intval(Bingo_HTTP_Request::get('del_num', 0));
        $strType = strval(Bingo_HTTP_Request::get('type', ''));

        if(!empty($intIsPlatform)){
            $arrInput = array(
                'user_id' => $intLoginUserId,
                // 这里是平台运营接口，用amis权限进行控制
                'auth_id' => Lib_Ala_Define_Authority::AUTHORITY_LIVE_MANAGEMENT_LIVE_YUNYING_MANAGEMENT_FLOWER_OUT,
            );
            $arrOutput = Service_Ala_Authority_Authority::canUserAccess($arrInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Authority_Authority::canUserAccess input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, $arrOutput['usermsg']);
            }
            $bolCanAccess = $arrOutput['data'];
            if ($bolCanAccess === false) {
                //return self::_jsonRet(Tieba_Errcode::ERR_ACTION_FORBIDDEN, "无权限");
            }
        } else{
            $intDelUserId = $intLoginUserId;
        }

        if('all' == $strType) {
            return $this->_delAllHuaban();
        }

        // 18/3/22 gaojingjing03 PM邮件新需求，删除合作运营的花瓣时，同步针对合作运营已经发放到其他账户里的花瓣，也进行清零，如遇该帐号余额不足的情况则扣为0即可。
        // 查询出该合作运营发放的用户列表
        $intSendType = Lib_Ala_Define_Petal::SEND_TYPE_COOPERATE_TO_USER;

        // 不用传expire_time，所有花瓣只能本月有效，清理也是本月的
        $arrReq = array(
            'send_type'    => $intSendType,
            'send_user_id' => $intDelUserId,
        );

        $arrRes = Alalib_Util_Service::call("ala", "operatorGetSendPetalCount", $arrReq);

        if ($arrRes === false || $arrRes['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala::operatorGetSendPetalCount fail. input[" . serialize($arrReq) . "] output[" . serialize($arrRes) . "]";
            Bingo_Log::fatal($strLog);
            return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }

        $intCount = $arrRes['data']['count'];

        // PM说先简单粗暴一点，批量轮询删除，超时或者发生错误了让运营多重试几次
        $intLimit = 100;
        $intStart = 0;
        while ($intStart < $intCount) {
            $arrReq['pn'] = $intStart;
            $arrReq['ps'] = $intLimit;

            $arrRes = Alalib_Util_Service::call("ala", "operatorGetSendPetalList", $arrReq);

            if ($arrRes === false || $arrRes['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala::operatorGetSendPetalList fail. input[" . serialize($arrReq) . "] output[" . serialize($arrRes) . "]";
                Bingo_Log::fatal($strLog);
                return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
            }

            $arrList = $arrRes['data'];

            if (empty($arrList)) {
                break;
            }

            $serviceName = 'ala';
            $methodName  = 'operatorDelPetalOfUser';

            $strMultiCallKey = __CLASS__."::".__FUNCTION__."_".$intStart;
            $objMultiCall    = new Tieba_Multi($strMultiCallKey);

            foreach ($arrList as $key => $arrInfo) {
                // 已清空
                if (empty($arrInfo['current_num'])) {
                    continue;
                }

                $arrReq = array(
                    'send_type'    => $intSendType,
                    'send_user_id' => $arrInfo['send_user_id'],
                    'user_id'      => $arrInfo['user_id'],
                    'id'           => $arrInfo['id'],
                );

                $arrInput = array(
                    "serviceName" => $serviceName,
                    "method"      => $methodName,
                    'ie'          => 'utf-8',
                    "input"       => $arrReq,
                );

                $objMultiCall->register($methodName.'_'.$intStart.'_'.$key, new Tieba_Service($serviceName), $arrInput);
            }

            Bingo_Timer::start($strMultiCallKey);
            $objMultiCall->call();
            Bingo_Timer::end($strMultiCallKey);

            foreach ($arrList as $key => $arrInfo) {
                // 已清空
                if (empty($arrInfo['current_num'])) {
                    continue;
                }

                $methodResName = $methodName.'_'.$intStart.'_'.$key;
                $arrOutput = $objMultiCall->getResult($methodResName);

                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $arrReq = array(
                        'send_type'    => $intSendType,
                        'send_user_id' => $arrInfo['send_user_id'],
                        'user_id'      => $arrInfo['user_id'],
                        'id'           => $arrInfo['id'],
                    );

                    $strLog = __CLASS__."::".__FUNCTION__." call $serviceName $methodName fail. input:[".serialize($arrReq)."]  output:[".serialize($arrOutput)."]";
                    Bingo_Log::fatal($strLog);

                    // 有错即返回，让运营重试
                    return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
                }
            }

            $intStart += $intLimit;
        }
        // 18/3/22 gaojingjing03 PM邮件新需求 END

        $intSendType = Lib_Ala_Define_Petal::SEND_TYPE_PLATFORM_TO_COOPERATE;

        $arrReq = array(
            'send_type' => $intSendType,
            'send_user_id' => $intLoginUserId,
            'user_id' => $intDelUserId,
            'del_num' => $intDelNum,
        );
        $arrRes = Alalib_Util_Service::call("ala", "operatorDelPetalOfCooperate", $arrReq);
        if ($arrRes === false || $arrRes['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala::operatorDelPetalOfUser fail. input[" . serialize($arrReq) . "] output[" . serialize($arrRes) . "]";
            Bingo_Log::fatal($strLog);
            return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }

        Service_Ala_Log_Op::add("花瓣删除: $strOpUserName 删除合作运营 user_id : $intDelUserId 花瓣 $intDelNum 个");

        return $this->_jsonRet(Alalib_Conf_Error::ERR_SUCCESS);

    }

    /**
     * @return int
     */
    private function _delAllHuaban() {
        $intExpireTime   = intval(Bingo_HTTP_Request::get('expire_time', 0));
        $strOpUserName = Util_User::$strUserName;
        if(!empty($intExpireTime)){
            // 前端传的是天的开始，这里转成结束
            $intExpireTime += 86399;
        }
        $intSendType = Lib_Ala_Define_Petal::SEND_TYPE_PLATFORM_TO_COOPERATE;
        $intBeginSendTime = mktime(0,0,0,date('m'),1,date('Y'));
        $intEndSendTime = mktime(23,59,59,date('m'),date('t'),date('Y'));
        $arrReq = array(
            'send_type' => $intSendType,
            'begin_send_time' => $intBeginSendTime,
            'expire_time' => $intExpireTime,
        );
        $arrRes = Dl_Ala_Journal_Journal::getMainPageHuabanData($arrReq);
        $arrList = $arrRes['data'];

        foreach ($arrList as $value) {
            $intDelUserId = intval($value['user_id']);
            $intSendUserId = intval($value['send_user_id']);
            $intDelNum = intval($value['current_num']);

            if($intDelNum <= 0) {
                continue;
            }

            $intSendType = Lib_Ala_Define_Petal::SEND_TYPE_COOPERATE_TO_USER;
            // 不用传expire_time，所有花瓣只能本月有效，清理也是本月的
            $arrReq = array(
                'send_type'    => $intSendType,
                'send_user_id' => $intDelUserId,
            );
            $arrRes = Alalib_Util_Service::call("ala", "operatorGetSendPetalCount", $arrReq);
            if ($arrRes === false || $arrRes['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala::operatorGetSendPetalCount fail. input[" . serialize($arrReq) . "] output[" . serialize($arrRes) . "]";
                Bingo_Log::fatal($strLog);
                return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
            }
            $intCount = $arrRes['data']['count'];

            // PM说先简单粗暴一点，批量轮询删除，超时或者发生错误了让运营多重试几次
            $intLimit = 100;
            $intStart = 0;
            while ($intStart < $intCount) {
                $arrReq['pn'] = $intStart;
                $arrReq['ps'] = $intLimit;

                $arrRes = Alalib_Util_Service::call("ala", "operatorGetSendPetalList", $arrReq);

                if ($arrRes === false || $arrRes['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala::operatorGetSendPetalList fail. input[" . serialize($arrReq) . "] output[" . serialize($arrRes) . "]";
                    Bingo_Log::fatal($strLog);
                    return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
                }

                $arrList = $arrRes['data'];

                if (empty($arrList)) {
                    break;
                }

                $serviceName = 'ala';
                $methodName  = 'operatorDelPetalOfUser';

                $strMultiCallKey = __CLASS__."::".__FUNCTION__."_".$intStart;
                $objMultiCall    = new Tieba_Multi($strMultiCallKey);

                foreach ($arrList as $key => $arrInfo) {
                    // 已清空
                    if (empty($arrInfo['current_num'])) {
                        continue;
                    }

                    $arrReq = array(
                        'send_type'    => $intSendType,
                        'send_user_id' => $arrInfo['send_user_id'],
                        'user_id'      => $arrInfo['user_id'],
                        'id'           => $arrInfo['id'],
                    );

                    $arrInput = array(
                        "serviceName" => $serviceName,
                        "method"      => $methodName,
                        'ie'          => 'utf-8',
                        "input"       => $arrReq,
                    );

                    $objMultiCall->register($methodName.'_'.$intStart.'_'.$key, new Tieba_Service($serviceName), $arrInput);
                }

                Bingo_Timer::start($strMultiCallKey);
                $objMultiCall->call();
                Bingo_Timer::end($strMultiCallKey);

                foreach ($arrList as $key => $arrInfo) {
                    // 已清空
                    if (empty($arrInfo['current_num'])) {
                        continue;
                    }

                    $methodResName = $methodName.'_'.$intStart.'_'.$key;
                    $arrOutput = $objMultiCall->getResult($methodResName);

                    if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                        $arrReq = array(
                            'send_type'    => $intSendType,
                            'send_user_id' => $arrInfo['send_user_id'],
                            'user_id'      => $arrInfo['user_id'],
                            'id'           => $arrInfo['id'],
                        );

                        $strLog = __CLASS__."::".__FUNCTION__." call $serviceName $methodName fail. input:[".serialize($arrReq)."]  output:[".serialize($arrOutput)."]";
                        Bingo_Log::fatal($strLog);

                        // 有错即返回，让运营重试
                        return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
                    }
                }

                $intStart += $intLimit;
            }
            // 18/3/22 gaojingjing03 PM邮件新需求 END

            $intSendType = Lib_Ala_Define_Petal::SEND_TYPE_PLATFORM_TO_COOPERATE;
            $arrReq = array(
                'send_type' => $intSendType,
                'send_user_id' => $intSendUserId,
                'user_id' => $intDelUserId,
                'del_num' => $intDelNum,
            );
            $arrRes = Alalib_Util_Service::call("ala", "operatorDelPetalOfCooperate", $arrReq);
            if ($arrRes === false || $arrRes['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala::operatorDelPetalOfUser fail. input[" . serialize($arrReq) . "] output[" . serialize($arrRes) . "]";
                Bingo_Log::fatal($strLog);
                return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
            }

        }

        Service_Ala_Log_Op::add("花瓣删除: $strOpUserName 清空所有合作运营的花瓣");

        return $this->_jsonRet(Alalib_Conf_Error::ERR_SUCCESS);
    }

    /**
     * @param $errno
     * @param string $errmsg
     * @param array $arrExtData
     * @return int
     */
    private function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => Alalib_Conf_Error::getErrorMsg($errno),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }
}