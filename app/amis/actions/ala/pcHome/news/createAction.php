<?php

/**
 * Created by PhpStorm.
 * User: zhouyan08
 * Date: 17/4/27
 * Time: 下午4:48
 */
class createAction extends Util_Action
{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    /**
     * 执行函数
     *
     * @param null
     *
     * @return null
     * */
    public function _execute()
    {
        $strTile = (string)Bingo_Http_Request::get('title', '');
        $arrCover = Bingo_Http_Request::get('cover', '');
        if (is_array($arrCover)){
            // as json object
            $strCoverUrl = str_replace('hiphotos.baidu.com', 'imgsa.baidu.com', $arrCover['url']);
        } else {
            // as string url
            $strCoverUrl = str_replace('hiphotos.baidu.com', 'imgsa.baidu.com', $arrCover);
        }
        $strAbstract = (string)Bingo_Http_Request::get('abstract', '');
        $intBeginTime = (int)Bingo_Http_Request::get('begin_time', 0);
        $strSourceUrl = (string)Bingo_Http_Request::get('source_url', '');
        $arrInput = array(
            'title' => $strTile,
            'cover' => $strCoverUrl,
            'abstract' => $strAbstract,
            'begin_time' => $intBeginTime,
            'source_url' => $strSourceUrl,
        );
        $arrOutput = Tieba_Service::call('ala', 'addPCHomeNews', $arrInput, null, null, 'post', null, 'utf-8');
        if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__."::".__FUNCTION__." call ala::addPCHomeNews fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            Bingo_Http_Response::contextType('application/json');
            echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL), 'utf-8');
            return;
        }
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json(self::SqlResultsToRows($arrOutput), 'utf-8');

        return;
        // 默认成功返回值
        //            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, Tieba_Error::getUserMsg(Tieba_Errcode::ERR_SUCCESS), $retData);
    }

    /**
     * 返回值
     *
     * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
     * @param  {Array} $arrOutData default array()
     * @param  {String} $strMsg default ""
     *
     * @return {Array} :errno :errmsg :{Array}output
     * */
    private static function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS, $arrOutData = array(), $strMsg = "")
    {
        $arrOutput            = array();
        $arrOutput['status']  = $intErrno;
        $arrOutput['msg']     = Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput['usermsg'] = $strMsg;
        $arrOutput['data']    = $arrOutData;

        return $arrOutput;
    }

    /**
     * 将sql数据格式转换成amis能接受的数据格式
     *
     * @param null
     *
     * @return null
     * */
    private static function SqlResultsToRows($retData)
    {
        $arrayData = $retData['data']['data'];
        foreach($arrayData as &$item){
            if($item['state'] == 1){
                continue;
            }
            if($item['state'] == 0){
                if($item['expire_time'] > time()){
                    $item['state'] = 2;
                }else{
                    $item['state'] = 3;
                }
            }
        }
        $ret           = array();
        $ret['status'] = 0;
        $ret['msg']    = '';
        $ret['data']   = array(
            'rows'  => $arrayData,
            'count' => $retData['data']['count'],
        );

        return $ret;
    }

}
