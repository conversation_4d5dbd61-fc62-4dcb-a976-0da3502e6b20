<?php


/**
 * Created by PhpStorm.
 * User: kangqinmou
 * Date: 18-5-28
 * Time: 下午4:59
 */
class getUserInfoAction extends Util_Action{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    const PROPS_ID_MEMBER = 1050001;              // 普通会员
    const PROPS_ID_ADVANCE_MEMBER = 1050002;      // 超级会员


    /**
     * 执行函数
     * @param  null
     * @return array
     */
    public function _execute(){

        // 操作人
        // $intUserId = Util_User::$intUserId;
        // $strUserName = Util_User::$strUserName;

        // 页码
        $intPs = (int) Bingo_Http_Request::get('ps', 30);
        $intPs = ($intPs < 1) ? 30 : $intPs;
        $intPn = (int) Bingo_Http_Request::get('pn', 1);
        $intPn = ($intPn < 1) ? 1 : $intPn;

        $strPhone  = trim(Bingo_Http_Request::get('phone', ''));
        $intUserId = (int) trim(Bingo_Http_Request::get('user_id', 0));
        $strUserName = trim(Bingo_Http_Request::get('user_name', ''));
        $arrUserIds = array();

        // 根据手机号获取用户id
        if($strPhone){
            $arrParams = array(
                'phone' => $strPhone,
            );
            $ret = Tieba_Service::call('tbkey', 'getUidsByPhone', $arrParams, null, null, 'post', 'php', 'utf-8', 'local');
            if(!$ret || Tieba_Errcode::ERR_SUCCESS != $ret['errno'] || !isset($ret['data']) || !is_array($ret['data'])){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' get user ids failed by phone, input: [' . serialize($arrParams) . '], output: [' . serialize($ret) . "].\n");
            }
            foreach($ret['data'] as $item){
                if(!is_array($item) || !isset($item['user_id']) || $item['user_id'] <= 0){
                    continue;
                }
                $uid = (int) $item['user_id'];
                $arrUserIds[$uid] = isset($item['for_login']) ? intval($item['for_login']) : 0;
            }
        }

        // 用户id来自前端
        elseif($intUserId > 0){
            $arrUserIds[$intUserId] = 0;
        }

        // 根据用户名获取用户id
        elseif($strUserName){
            $arrParams = array(
                'user_name' => array($strUserName),
            );
            $ret = Tieba_Service::call('user', 'getUidByUnames', $arrParams, null, null, 'post', 'php', 'utf-8');
            if(!$ret || Tieba_Errcode::ERR_SUCCESS != $ret['errno']){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' get user_id failed by calling user.getUidByUnames service, input: [' . serialize($arrParams) . '], output: [' . serialize($ret) . "].\n");
                return self::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '根据用户名获取用户id失败');
            }
            $intUserId = isset($ret['output']['uids'][0]['user_id']) ? intval($ret['output']['uids'][0]['user_id']) : 0;
            if($intUserId > 0){
                $arrUserIds[$intUserId] = 0;
            }
        }

        if(!$arrUserIds){
            return self::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS);
        }

        // 根据用户ids获取用户名和用户昵称
        $arrParams = array(
            'user_id' => array_keys($arrUserIds),
        );
        $arrUserInfos = Tieba_Service::call('user', 'mgetUserData', $arrParams, null, null, 'post', 'php', 'utf-8');
        if(!$arrUserInfos || Tieba_Errcode::ERR_SUCCESS != $arrUserInfos['errno'] || !isset($arrUserInfos['user_info']) || !is_array($arrUserInfos['user_info'])){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' get user info failed, input: [' . serialize($arrParams) . '], output: [' . serialize($arrUserInfos) . "].\n");
            return self::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '根据用户ids获取用户信息失败');
        }
        $arrUserInfos = $arrUserInfos['user_info'];
        $arrReturn = array();
        foreach($arrUserInfos as $item){
            if(!is_array($item) || !isset($item['user_id'])){
                continue;
            }
            $uid = (int) $item['user_id'];
            if(!isset($arrUserIds[$uid])){
                continue;
            }
            $arrReturn[$uid] = array(
                'user_id'       => $uid,
                'for_login'     => $arrUserIds[$uid] ? '是' : '未知',
                'user_name'     => isset($item['user_name']) ? strval($item['user_name']) : '',
                'user_nickname' => isset($item['user_nickname']) ? strval($item['user_nickname']) : '',
            );
        }

        // 返回结果(含当前页面的记录以及记录总量)
        return self::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, array(
            'rows'  => array_values($arrReturn),
            'count' => count($arrReturn),
        ), 'success');
    }



    /**
     * @param  $intErrno
     * @param  array $arrExtData
     * @param  string $strMsg
     * @return bool
     */
    public static function jsonAmisRet($intErrno, $arrExtData = array(), $strMsg = ''){
        $strMsg = $strMsg ? $strMsg : Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput = array(
            'status' => $intErrno,
            'msg'    => $strMsg,
            'data'   => $arrExtData,
        );
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrOutput, Bingo_Encode::ENCODE_UTF8);
        return true;
    }

}