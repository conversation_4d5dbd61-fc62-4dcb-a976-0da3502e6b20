<?php


/**
 * Created by PhpStorm.
 * User: kangqinmou
 * Date: 18-5-28
 * Time: 下午4:59
 */
class updateUserAction extends Util_Action{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;


    /**
     * 执行函数
     * @param  null
     * @return array
     */
    public function _execute(){


        // 操作人
        $intUserId = Util_User::$intUserId;
        $strUserName = Util_User::$strUserName;

        // 参数
        $arrParams = array(
            'user_id'    => (int) Bingo_Http_Request::get('user_id', 0),           // 用户id
            'type'       => (int) Bingo_Http_Request::get('type', 0),              // 操作类型
            'total_cost' => (int) Bingo_Http_Request::get('total_cost', 0),        // 要操作的蓝钻数量
            'scene_id'   => (int) Bingo_Http_Request::get('scene_id', 0),          // 池子id
        );

        // 参数校验
        $strError = '来自server的提示';
        foreach($arrParams as $field => $value){
            if($value <= 0){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . " {$field}[{$value}] <= 0, input: [" . json_encode($arrParams) . "]. \n");
                return self::jsonAmisRet(422, array('errors' => array($field => "{$field}[{$value}] <= 0")), $strError);
            }
        }

        // $arrParams['type']: 1增加,2减少
        if(2 == $arrParams['type']){
            $arrInput = array(
                'from_type'  => 1,  // 1用户、2池子
                'from_id'    => $arrParams['user_id'],
                'to_type'    => 2,
                'to_id'      => $arrParams['scene_id'],
                'order_desc' => "amis后台：为用户{$arrParams['user_id']}减少蓝钻{$arrParams['total_cost']}枚 by {$strUserName}"
            );
        }else{
            $arrInput = array(
                'from_type'  => 2,
                'from_id'    => $arrParams['scene_id'],
                'to_type'    => 1,
                'to_id'      => $arrParams['user_id'],
                'order_desc' => "amis后台：为用户{$arrParams['user_id']}增加蓝钻{$arrParams['total_cost']}枚 by {$strUserName}"
            );
        }
        $arrInput['amount'] = $arrParams['total_cost'];
        $arrInput['out_order_id'] = Tbmall_Open_Order::genInnerOrderId($arrParams['user_id'], $arrParams['scene_id']);
        $arrRet = Tieba_Service::call('bluediamond', 'trade', $arrInput, null, null, 'post', 'php', 'utf-8');
        $strError = '来自server的提示';
        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' update user blue diamond failed by calling bluediamond.trade service, input: [' . json_encode($arrInput) . '], output: [' . json_encode($arrRet) . "]. \n");
            return self::jsonAmisRet(422, array('errors' => array('total_cost' => $arrRet['usermsg'])), $strError);
        }

        return self::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS);
    }



    /**
     * @param  $intErrno
     * @param  array $arrExtData
     * @param  string $strMsg
     * @return bool
     */
    public static function jsonAmisRet($intErrno, $arrExtData = array(), $strMsg = ''){
        $strMsg = $strMsg ? $strMsg : Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput = array(
            'status' => $intErrno,
            'msg'    => $strMsg,
            'data'   => $arrExtData,
        );
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrOutput, Bingo_Encode::ENCODE_UTF8);
        return true;
    }

}