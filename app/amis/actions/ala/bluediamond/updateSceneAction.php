<?php


/**
 * Created by PhpStorm.
 * User: kangqinmou
 * Date: 18-5-28
 * Time: 下午4:59
 */
class updateSceneAction extends Util_Action{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;


    /**
     * 执行函数
     * @param  null
     * @return array
     */
    public function _execute(){


        // 操作人
        $intUserId = Util_User::$intUserId;
        $strUserName = Util_User::$strUserName;

        // 参数
        $arrParams = array(
            'id'         => (int)    Bingo_Http_Request::get('id', 0),                // scene_info表的自增id
            'name'       => (string) Bingo_Http_Request::get('name', ''),             // 池子名称
            'op'         => (string) $strUserName,                                    // 操作人
            'type'       => (int)    Bingo_Http_Request::get('type', 0),              // 操作类型
            'total_cost' => (int)    Bingo_Http_Request::get('total_cost', 0),        // 要操作的蓝钻数量
        );

        // 参数校验
        $strError = '来自server的提示';
        if($arrParams['id'] <= 0 || !$arrParams['name']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' auto_increment id <= 0 or name is empty, input: [' . json_encode($arrParams) . "]. \n");
            return self::jsonAmisRet(422, array('errors' => array('name' => 'auto_increment id <= 0 or name is empty')), $strError);
        }

        // 调用service
        $arrRet = Tieba_Service::call('bluediamond', 'updateSceneForAmis', $arrParams, null, null, 'post', 'php', 'utf-8');
        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' update scene info and pool failed by calling bluediamond.updateSceneInfoById service, input: [' . json_encode($arrParams) . '], output: [' . json_encode($arrRet) . "]. \n");
            return self::jsonAmisRet(422, array('errors' => array('total_cost' => $arrRet['usermsg'])), $strError);
        }

        return self::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS);
    }



    /**
     * @param  $intErrno
     * @param  array $arrExtData
     * @param  string $strMsg
     * @return bool
     */
    public static function jsonAmisRet($intErrno, $arrExtData = array(), $strMsg = ''){
        $strMsg = $strMsg ? $strMsg : Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput = array(
            'status' => $intErrno,
            'msg'    => $strMsg,
            'data'   => $arrExtData,
        );
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrOutput, Bingo_Encode::ENCODE_UTF8);
        return true;
    }

}