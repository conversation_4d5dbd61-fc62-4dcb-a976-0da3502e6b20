<?php
/**
 * Created by PhpStorm.
 * 企业大使三期--发卡发号
 * User: weiyan02
 * Date: 2019/11/5
 * Time: 上午11:13
 */

class listAction extends Util_Action
{

    protected $strAmisGroup = '';
    protected $strAmisPerm = '';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;
    protected $bolNeedLogin = false;

    /**
     *
     * @return int
     */
    public function _execute()
    {
        // Request
        $intUserId = Util_User::$intUserId;

        $intPage = intval(Bingo_Http_Request::get('page', 1));
        $perPage = intval(Bingo_Http_Request::get('perPage', 10));
        $showStatus = intval(Bingo_Http_Request::get('showStatus', 0));
        $downCard = Bingo_Http_Request::get('downCard', '');

        //下载剩余卡号
        if(!empty($downCard) && $downCard == 'downCard'){
            $packetId = Bingo_Http_Request::get('id', '');
            $packetName = Bingo_Http_Request::get('packet_name', '');

            //查询礼包未领取卡号
            $arrInput = array(
                'pass_status'=> 0,
                'packet_id' => $packetId,
            );
            $arrRet = Tieba_Service::call('tbmall', 'getBusinessSendCardContentList', $arrInput, null, null, 'post', 'php', 'utf-8');
            if (false === $arrRet || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("getBusinessSendCardList fail.".serialize($arrRet));
                return self::_jsonRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, "获取失败");
            }
            $arrRetRes['rows'] = $arrRet['data']['rows'];
            return $this->_exportExcel($arrRetRes,$packetName,$packetId);
        }

        $arrInput = array(
            'status' => 1,
            'uid' => $intUserId,
        );
        //
        $arrRet = Tieba_Service::call('tbmall', 'getAmbassadorAccountByUid', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrRet || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("send card getAmbassadorAccountByUid fail.".serialize($arrRet));
        }
        $intForum = $arrRet['data']['rows'][0]['forum_id'];
        //测试专用
//        $intForum =35;
//        $intUserId = *********;
        // 列表数据条数
        //默认审核员
        $arrInput = array(
            'show_status' => $showStatus,
            'page' => $intPage,
            'page_size' => $perPage,
        );

        //企业大使
        if(!empty($intForum)){
            $arrInput['user_id'] = $intUserId;
        }
        $arrRet = Tieba_Service::call('tbmall', 'getBusinessSendCardList', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrRet || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("getBusinessSendCardList fail.".serialize($arrRet));
            return self::_jsonRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, "获取失败");
        }
        $arrForumIds  = array();
        $arrUserIds   = array();
        $arrThreadIds = array();
        if (!empty($arrRet['data']['rows'])) {
            foreach ($arrRet['data']['rows'] as &$item) {
                $arrUserIds[] = $item['user_id'];
                $arrForumIds[] = $item['forum_id'];
                $arrThreadIds[] = $item['thread_id'];
                $item['get_pro'] = $item['card_get'].'、'.$item['card_total'].'、'.round($item['card_get'] / $item['card_total'] * 100, 2).'%';
                $item['expire']  = Date("Y-m-d H:i:s",$item['expiry_start']).'至'.Date("Y-m-d H:i:s",$item['expiry_end']);
                if(!empty($item['get_conditions_level'])){
                    $item['get_con'] = '用户等级到达'.$item['get_conditions_level'].'级领取';
                }else{
                    $item['get_con'] = '关注吧用户可领取';
                }
            }
            $arrForumNames =  $this->getFullForumList($arrForumIds);
            $arrThreadInfo =  $this->getThreadInfoList($arrThreadIds);
            //吧名
            foreach ($arrRet['data']['rows'] as &$item) {
                $item['forum_name'] = $arrForumNames[$item['forum_id']]['forum_name'];
                $item['how2get_info'] = $arrThreadInfo[$item['thread_id']]['abstract'];
            }
            //审核
            if(empty($intForum) && !empty($arrUserIds)){
                $arrUserIdMapUserInfos = self::_userGetInfo($arrUserIds);
                foreach ($arrRet['data']['rows'] as &$item) {
                    $item['user_info'] = '账号：'.$arrUserIdMapUserInfos[$item['user_id']]['user_name'].'--Id:'.$item['user_id'];
//                    $item['how2get_info'] = $arrThreadInfo[$item['thread_id']]['abstract'];
                }
            }
        }

        //返回列表
        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrRet['data']);
    }

    /**
     * @param $forumIds
     * @return array
     */
    private function getFullForumList($forumIds)
    {
        $forumName = $this->getForumNames($forumIds);
        return $forumName;
    }

    /**
     * @param $arrUserIds
     * @return array
     */
    private function _userGetInfo($arrUserIds){
        $arrUsersReq = array();
        $arrUserIds  = array_unique($arrUserIds);
        $offset      = 0;
        for ($i = 0; $i < (count($arrUserIds)/100); $i++){
            $arrUsersReq[] = array_slice($arrUserIds,$offset,100);
            $offset = $offset + 100;
        }

        $Obj = new Tieba_Multi('live_to_shoubai');
        $strServiceKey = 'ala';
        $strUserMethod = 'userGetInfo';
        $caller = new Tieba_Service($strServiceKey);
        foreach ($arrUsersReq as $index => $item){
            $arrServiceParams = array(
                'uids'        => $item,
            );
            $strKey =  $strUserMethod .'_'. $index;
            $arrInput = array(
                'serviceName' => $strServiceKey,
                'method'      => $strUserMethod,
                'ie'          => 'utf-8',
                'input'       => $arrServiceParams,
            );
            $Obj->register($strKey,$caller,$arrInput);
        }
        $Obj->call();

        $arrRet = array();
        foreach ($arrUsersReq as $index => $item){
            $strKey     =  $strUserMethod .'_'. $index;
            $resultUser = $Obj->getResult($strKey);
            foreach ($resultUser['data'] as $k => $v){
                $arrRet[$k] =  $v;
            }
        }
        return $arrRet;
    }

    /**
     * @param $arrThreadIds
     * @return array
     */
    private function getThreadInfoList($arrThreadIds)
    {
        $res = array();
        if(!empty($arrThreadIds)) {
            $arrParam = array(
                "thread_ids" => $arrThreadIds,
                "need_abstract" => 1,
                "forum_id" => 0,
                "need_photo_pic" => 1,
                "need_user_data" => 0,
                "icon_size" => 0,
                "need_forum_name" => 1,       //是否获取吧名
            );
            $arrRes = Tieba_Service::call('post', 'mgetThread', $arrParam, null, null, 'post', 'php', 'utf-8');
            if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
                Bingo_Log::warning('call post::mgetThread fail. input:' . serialize($arrParam) . ' output:' . serialize($arrRes));
            }
            if(!empty($arrRes['output']['thread_list'])) {
                foreach ($arrRes['output']['thread_list'] as $ThreadInfoTmp) {
                    if($ThreadInfoTmp['thread_types'] == 40){
                        $res[$ThreadInfoTmp['thread_id']]['abstract'] = '视频';
                        continue;
                    }
                    if(!empty($ThreadInfoTmp['abstract'])){
                        $res[$ThreadInfoTmp['thread_id']]['abstract'] = substr($ThreadInfoTmp['abstract'],0,100);
                    }else if(!empty($ThreadInfoTmp['raw_abstract_media'])){
                        foreach ($ThreadInfoTmp['raw_abstract_media'] as $isVideo){
                            if($isVideo['type'] == "pic"){
                                $res[$ThreadInfoTmp['thread_id']]['abstract'] = '图片';
                                break;
                            }
                        }
                    }else{
                    }
                }
            }
        }
//        $forumName = $this->getForumNames($forumIds);

        /*
        $isEnterpriseMap = $this->getForumIsEnterprise($forumIds);
        $namesList = array();
        foreach ($forumIds as $forumId) {
            if (empty($forumName[$forumId])) {
                Bingo_Log::warning("not found ");
                continue;
            }
            $isEnterprise = !empty($isEnterpriseMap[$forumId]) ? ',本吧': '';
            $namesList[] = "{$forumName[$forumId]['forum_name']}({$forumId}{$isEnterprise})";
        }
        */

        return $res;
    }

    /**
     * @param $resourceId
     * @return array|int
     */
    private function getForumIds($resourceId)
    {
        $arrInput = array(
            'resource_id' => $resourceId
        );
        $arrRet = Tieba_Service::call('tbmall', 'getForumIdsByResourceIdHeadline', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false == $arrRet || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS || empty($arrRet['data'])) {
            Bingo_Log::warning("getForumIdsByResourceId fail.".serialize($arrRet));
            return array();
        }

        $forumIds = array();
        foreach ($arrRet['data'] as $item) {
            $forumIds[] = $item['forum_id'];
        }

        return $forumIds;
    }


    /**
     * @param $forumIds
     * @return array|int
     */
    private function getForumNames($forumIds)
    {
        $arrInput = array(
            'forum_id' => $forumIds
        );
        $arrRet = Tieba_Service::call('forum', 'getFnameByFid', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false == $arrRet || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call forum::getFnameByFid fail.".serialize($arrInput)." output:".serialize($arrRet));
            return array();
        }

        return $arrRet['forum_name'];
    }


    /**
     * 文件导出，支持excel、csv格式
     * @param $arrRet
     * @param $intFilterStartTimeDate
     * @param $intFilterEndTimeDate
     * @return bool
     */
    private function _exportExcel($arrRet,$intFilterStartTimeDate,$intFilterEndTimeDate) {

        $arrExcelTableHead = array();
        $arrExcelTableHead['card_id'] = "礼包卡号";
        // 创建excel
        $strExcelName = $intFilterStartTimeDate.'-'.$intFilterEndTimeDate;
        $strExcelName = urlencode($strExcelName);
        //$strExcelContent = Util_Ala_Excel::getExcel($strExcelName, $arrExcelTableHead, $arrRet['rows']);
        $strExcelContent = self::_putcsv($strExcelName,$arrExcelTableHead,$arrRet['rows']);
        header("Content-type: application/octet-stream");
        header("Accept-Ranges: bytes");
        header("Accept-Length: " . strlen($strExcelContent));
        header("Content-Disposition: attachment; filename=" .$strExcelName . '.csv');
        echo $strExcelContent;
        return true;
    }

    /**
     * @param $strFileName
     * @param $arrExcelTableHead
     * @param $listMemberInfo
     */
    private function _putcsv($strFileName,$arrExcelTableHead,$listMemberInfo){
        header("Content-Type: application/vnd.ms-excel; charset=utf8");
        header("Content-Disposition: attachment; filename=\"" . $strFileName);
        header('Cache-Control: max-age=0');

        $html = "\xEF\xBB\xBF";
        foreach ($arrExcelTableHead as $k => $v) {
            $html .= $v . "\t,";
        }
        $html .= "\n";
        foreach($listMemberInfo as $index => $input){
            foreach($arrExcelTableHead as $k => $v){
                $html .= $input[$k] . "\t ,";
            }
            $html .= "\n";
        }
        echo $html;
    }

    /**
     * @brief _jsonRet
     * @param errno,errmsg,data
     * @return : 0.
     *
     */
    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => strval($errmsg),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }


}