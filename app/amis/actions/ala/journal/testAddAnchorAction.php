<?php
/**
 *  非签约主播流水
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 17/7/17
 */


class testAddAnchorAction extends Util_Action
{

    protected $strAmisGroup = '';
    protected $strAmisPerm = '';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;
    protected $bolNeedLogin = false;

    /**
     * execute
     * @param null
     * @return null
     * */
    public function _execute() {
        //$intUserId    = Util_User::$intUserId;
        //$strUserName  = Util_User::$strUserName;

        $intUserId      = intval(Bingo_HTTP_Request::get('user_id', 0));
        $strUserName  = strval(Bingo_HTTP_Request::get('user_name', ''));
        $intAnchorType  = strval(Bingo_HTTP_Request::get('anchor_type', ''));
        $intUnionId  = strval(Bingo_HTTP_Request::get('union_id', ''));
        $intDate  = strval(Bingo_HTTP_Request::get('date', ''));
        $intIncomeTd  = strval(Bingo_HTTP_Request::get('income_td', ''));
        $intLiveDuration  = intval(Bingo_HTTP_Request::get('live_duration', ''));

        $arrInput = array(
            'user_id' => $intUserId,
            'user_name' => $strUserName,
            'anchor_type' => $intAnchorType,
            'union_id' => $intUnionId,
            'live_duration' => $intLiveDuration,
            'date' => $intDate,
            'income_td' => $intIncomeTd,
        );
        $arrOutput = Service_Ala_Journal_Journal::addAnchorJournal($arrInput);
        $this->_jsonRet(0,'aaa',$arrOutput);
    }


    /**
     * 新建Excel表并直接输出到浏览器
     * @param null
     * @return null
     * */
    protected static function _createExcel($retArr)
    {
        echo self::_utf8ToGbk("用户id\t");
        echo self::_utf8ToGbk("用户名\t");
        echo self::_utf8ToGbk("用户收益（T豆）\t");
        echo self::_utf8ToGbk("折合RMB\t\n");

        foreach ($retArr as $arrValue) {
            echo self::_utf8ToGbk($arrValue['user_id'] . "\t");
            echo self::_utf8ToGbk($arrValue['user_name'] . "\t");
            echo self::_utf8ToGbk($arrValue['income_td'] . "\t");
            echo self::_utf8ToGbk($arrValue['income_rmb'] . "\t\n");
        }
    }

    /**
     * @param $errno
     * @param string $errmsg
     * @param array $arrExtData
     * @return int
     */
    private function _jsonRet($errno, $errmsg = '', array $arrExtData = array()) {
        $arrRet = array(
            'no' => intval($errno),
            'error' => strval($errmsg),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }

    /**
     * 将utf8转为gbk
     * @param null
     * @return null
     * */
    protected static function _utf8ToGbk($strValue)
    {
        return iconv("UTF-8", "GBK", $strValue);
    }
}