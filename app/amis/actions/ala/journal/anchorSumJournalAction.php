<?php
/**
 * Created by PhpStorm.
 * User: zhanghanqing
 * Date: 2017/8/6
 * Time: 13:44
 */

class anchorSumJournalAction extends Util_Action
{

    protected $strAmisGroup = '';
    protected $strAmisPerm = '';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;
    protected $bolNeedLogin = false;

    /**
     * execute
     * @param null
     * @return null
     * */
    public function _execute()
    {
        $intUserId = Util_User::$intUserId;
        // $intUserId    = 221543;
        $intAnchorType = intval(Bingo_HTTP_Request::get('anchor_type', 0));
        $intUnionType = (int)Bingo_HTTP_Request::get('s_union_type', 0);
        // 时间
        $intYesterdayTime = strtotime('-1 day');   // 获取昨天的起始和今天的开始时间
        $intStartTime = intval(Bingo_HTTP_Request::get('start_time', $intYesterdayTime));
        $intEndTime = intval(Bingo_HTTP_Request::get('end_time', time()));
        // 类型
        if (empty($intStartTime) || empty($intEndTime) || empty($intAnchorType)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. start_time:[$intStartTime] end_time[$intEndTime];";
            Bingo_Log::warning($strLog);
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "请选择开始时间和结束时间");
            return false;
        }
        if ($intAnchorType == Util_Ala_Journal::ANCHOR_TYPE_UNION) {
            $intAuthId = Lib_Ala_Define_Authority::AUTHORITY_JOURNAL_VIEW_TEAM_ANCHOR;
        } else {
            $intAuthId = Lib_Ala_Define_Authority::AUTHORITY_JOURNAL_VIEW_NORMAL_ANCHOR;
        }
        $arrInput = array(
            'user_id' => $intUserId,
            'auth_id' => $intAuthId,
        );
        $arrOutput = Service_Ala_Authority_Authority::canUserAccess($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Authority_Authority::canUserAccess input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, $arrOutput['usermsg']);
        }
        $bolCanAccess = $arrOutput['data'];
        if ($bolCanAccess === false) {
            return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "抱歉,您没有权限");
        }
        unset($arrOutput);

        $intPageNumber = intval(Bingo_HTTP_Request::get('pn', 1));
        $intPageSize = intval(Bingo_HTTP_Request::get('ps', 5));

        $intCooperationUserId = intval(Bingo_HTTP_Request::get('s_cooperation_user_id', 0));

        //角色相关
        $arrUnionId = array();
        //是否屏蔽座驾信息标示  true：不屏蔽；false：屏蔽
        $bolShield = true;
        $intRole = Util_Ala_Common::getUserRoleId();
        switch ($intRole) {
            case Lib_Ala_Define_Role::ROLE_ADMIN:
                if (!empty($intCooperationUserId)) {
                    $arrOutput = Dl_Ala_Union_Union::getListByCreateUserId($intCooperationUserId, 1, Lib_Ala_Define_Role::ROLE_OPERATOR_MANAGE_MAX);
                    if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                        $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getListByCreateUserId fail. input:[]; output:[" . serialize($arrOutput) . "]";
                        Bingo_Log::warning($strLog);
                        return $this->_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL, "服务器出错");
                    }
                    $arrUnionList = $arrOutput['data'];
                    unset($arrOutput);
                    foreach ($arrUnionList as $arrUnionInfo) {
                        $arrUnionId[] = $arrUnionInfo['union_id'];
                    }
                }
                break;
            case Lib_Ala_Define_Role::ROLE_OPERATOR:
                $arrOutput = Dl_Ala_Union_Union::getListByCreateUserId($intUserId, 1, Lib_Ala_Define_Role::ROLE_OPERATOR_MANAGE_MAX);
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getListByCreateUserId fail. input:[]; output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                    return $this->_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL, "服务器出错");
                }
                $arrUnionList = $arrOutput['data'];
                unset($arrOutput);
                foreach ($arrUnionList as $arrUnionInfo) {
                    $arrUnionId[] = $arrUnionInfo['union_id'];
                }

                if (empty($arrUnionId) && ($intAnchorType == 1)) {
                    return $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', array());
                }
                break;
            case Lib_Ala_Define_Role::ROLE_UNION_PRESIDENT:
                $arrOutput = Dl_Ala_Union_Union::getListByUnionUserId($intUserId, 1, Lib_Ala_Define_Role::ROLE_TEAM_LEADER_MANAGE_MAX);
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getListByUnionUserId fail. input:[]; output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                    return $this->_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL, "服务器出错");
                }
                $arrUnionList = $arrOutput['data'];
                unset($arrOutput);
                foreach ($arrUnionList as $arrUnionInfo) {
                    $arrUnionId[] = $arrUnionInfo['union_id'];
                }
                if (empty($arrUnionId) && ($intAnchorType == 1)) {
                    return $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', array());
                }
                //20190516 pm川妈屏蔽工会长座驾信息
                $bolShield = false;
                break;
            default:
                break;
        }

        //2 代表非公会主播
        if ($intAnchorType == 2) {
            unset($arrUnionId);
        }

        // 检索条件
        $intSearchUserId = intval(Bingo_HTTP_Request::get('search_user_id', 0));
        $strSearchUserName = strval(Bingo_HTTP_Request::get('search_user_name', ''));
        $intSearchUnionId = intval(Bingo_HTTP_Request::get('search_union_id', 0));
        // 递增 1  递减 2
        $intSearchOrder = intval(Bingo_HTTP_Request::get('search_order', 2));
        if (!isset($intSearchOrder, Util_Ala_Journal::$ORDER_TYPE)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. search_order:[$intSearchOrder];";
            Bingo_Log::warning($strLog);
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }

        if (!empty($intSearchUnionId)) {
            //检查权限
            if ($arrRet = Util_Ala_Perm::checkUnionPermFailedPlus($intSearchUnionId)) {
                return $this->_jsonRet($arrRet['no'], $arrRet['error'], $arrRet['data']);
            }
        }

        // 17/12/1 gaojingjing03 运营zhaoxiaoyan01需要增加按公会类型搜索
        if ($intAnchorType == 1 && !empty($intUnionType)) {
            $strCond = 'union_type = ' . $intUnionType;
            $arrOutput = Dl_Ala_Union_Union::getCountFromUnion($strCond);

            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getCountFromUnion fail. input:[]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL, "服务器出错");
            }
            $arrUnionCount = (int)$arrOutput['data']['count'];
            unset($arrOutput);

            if ($arrUnionCount > 1000) {
                return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL, "公会数量过多，按公会类型搜索的功能暂停使用，请联系RD进行功能升级");
            }

            // 无对应类型的公会，直接返回空
            if (!$arrUnionCount) {
                $arrRet = array(
                    'rows' => array(),
                    'count' => 0,
                );

                return $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrRet);
            }

            // 获取公会id
            $arrOutput = Dl_Ala_Union_Union::getList(1, $arrUnionCount, $strCond);

            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getList fail. input:[]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL, "服务器出错");
            }
            $arrUnionList = $arrOutput['data'];
            unset($arrOutput);

            $arrUnionIds = array();

            foreach ($arrUnionList as $arrUnionInfo) {
                //检查权限 --是否是本公会长代运营
                if (!Util_Ala_Perm::checkUnionPermFailedPlus($arrUnionInfo['union_id'])) {
                    $arrUnionIds[] = (int)$arrUnionInfo['union_id'];
                }
            }

            if ($arrUnionId) {
                $arrUnionId = array_intersect($arrUnionId, $arrUnionIds);
            } else {
                $arrUnionId = $arrUnionIds;
            }
        }

        // 导出 2 为导出excel
        $intExcel = intval(Bingo_HTTP_Request::get('excel', 0));

        // 按天检索
        $intStartDayDate = date("Ymd", $intStartTime);
        $intEndDayDate = date("Ymd", $intEndTime);

        $intOffset = ($intPageNumber - 1) * $intPageSize;

        // weiyan02 2018-12-26 pm:zhouchuan要求添加日纬度的统计 start

        // 获取每日流水总数，用于计算当日流水占比
        $arrServiceInput = array(
            'type' => 5,
            'start_time' => $intStartDayDate,
            'end_time' => $intEndDayDate,
        );
        $arrOutput = Service_Ala_Journal_Journal::getAnchorPlatformJournal($arrServiceInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Journal_Journal getAnchorPlatformJournal fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
//                    return $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $totalIncomeTD = array();
        foreach ($arrOutput['data'] as $v) {
            $totalIncomeTD[$v['date']] = intval($v['income_td']);
        }
        unset($arrOutput);


        // 获取记录数量
        $arrServiceInput = array(
            'user_id' => $intSearchUserId,
            'user_name' => $strSearchUserName,
            'anchor_type' => $intAnchorType,
            'search_order' => $intSearchOrder,
            'union_id' => $intSearchUnionId,
            'start_time' => $intStartDayDate,
            'end_time' => $intEndDayDate,
        );
        if (!empty($arrUnionId)) {
            $arrServiceInput['union_ids'] = implode(',', $arrUnionId);
        }
        // 调取列表总数
        $arrOutput = Service_Ala_Journal_Journal::getAnchorJournalCount($arrServiceInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Journal_Journal getAnchorJournalCount fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $intListCount = $arrOutput['data']['count'];
        unset($arrOutput);


        //2019.1.22 weiyan增加有效天 pm：周川
        $arrServiceInput['offset'] = 0;
        $arrServiceInput['limit'] = $intListCount;

        $arrOutput = Service_Ala_Journal_Journal::getAnchorJournalList($arrServiceInput);
        $arrListForValidityDay = $arrOutput['data'];
        unset($arrOutput);
        $anchorValidityInfo = array();
        foreach ($arrListForValidityDay as $v) {
            if ($v['live_duration'] > 3600) {   //每天连续开播时长超过1小时，算一个有效天
                $anchorValidityInfo[$v['user_id']] += 1;
            }
        }

        // 获取用户每日流水
        // $arrUserIds
        $arrServiceInput['order_by'] = 'date';
        if (2 == $intExcel) {
            $arrServiceInput['offset'] = 0;
            $arrServiceInput['limit'] = $intListCount;
        } else {
            $arrServiceInput['offset'] = $intOffset;
            $arrServiceInput['limit'] = $intPageSize;
        }

        $arrOutput = Service_Ala_Journal_Journal::getAnchorJournalList($arrServiceInput);

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Journal_Journal getAnchorJournalList fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrListForDay = $arrOutput['data'];
        unset($arrOutput);
        Bingo_Log::warning('anchorSumJournalAction res day is :' . serialize($arrListForDay) . 'input param is:' . serialize($arrServiceInput));
        $anchorJournalInfo = array();
        $arrUnionIds = array();
        $anchorUids = array();
        foreach ($arrListForDay as $v) {
            $anchorJournalInfo[$v['date']][$v['user_id']] = $v;
            $arrUnionIds[] = $v['union_id'];
            $anchorUids[] = $v['user_id'];
        }
        krsort($anchorJournalInfo);

        // 获取公会及公会创建者信息
        $arrUnionIds = array_filter(array_unique($arrUnionIds));
        $arrUnionListRet = Dl_Ala_Union_Union::getUnionArr($arrUnionIds);
        $arrUnionList = $arrUnionListRet['data'];
        $arrUnionInfoList = array();
        $arrUnionIdMapCreateUserIds = array();
        foreach ($arrUnionList as $arrUnionInfo) {
            $arrUnionInfoList[$arrUnionInfo['union_id']] = $arrUnionInfo;
            $arrUnionIdMapCreateUserIds[intval($arrUnionInfo['union_id'])] = intval($arrUnionInfo['create_user_id']);
        }
        if (!empty($arrUnionIdMapCreateUserIds)) {
            $arrInput = array(
                "uids" => array_values($arrUnionIdMapCreateUserIds),
            );
            $arrOutput = Tieba_Service::call('ala', 'userGetInfo', $arrInput, null, null, 'post', 'php', 'utf-8');
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala/userGetInfo fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "服务器出错");
            }
            $arrUserInfos = $arrOutput['data'];
            unset($arrOutput);
        }

        // 获取主播在这个时间范围的总和数据
        $arrServiceInput = array(
            'user_id' => $anchorUids,
            'anchor_type' => $intAnchorType,
            'start_time' => $intStartDayDate,
            'end_time' => $intEndDayDate,
            // 没有limit会默认为limit是5，坑
            'limit' => 100000,
        );
        if (!empty($arrUnionId)) {
            $arrServiceInput['union_ids'] = implode(',', $arrUnionId);
        }
        $arrOutput = Service_Ala_Journal_Journal::getAnchorSumJournalList($arrServiceInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Journal_Journal getAnchorJournalList fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $anchorRangeData = array();
        foreach ($arrOutput['data'] as $v) {
            $anchorRangeData[$v['user_id']]['income_td'] = $v['income_td'];
            $anchorRangeData[$v['user_id']]['live_duration'] = self::Sec2Time($v['live_duration']);
            $anchorRangeData[$v['user_id']]['prop_count'] = $v['prop_count'];
            $anchorRangeData[$v['user_id']]['prop_tdou'] = $v['prop_tdou'];
        }
        unset($arrOutput);

        // 组合返回数据
        $anchorResultData = array();
        foreach ($anchorJournalInfo as $itemDate => $v) {
            foreach ($v as $userId => $d) {
                $unionName = empty($arrUnionInfoList[$d['union_id']]['union_name']) ? '' : $arrUnionInfoList[$d['union_id']]['union_name'];
                $journal_day_rate = $totalIncomeTD[$itemDate] > 0 ? sprintf("%01.5f", (doubleval($d['income_td']) / $totalIncomeTD[$itemDate]) * 100) . '%' : 0;
                $createUserId = intval($arrUnionIdMapCreateUserIds[$d['union_id']]);
                $createUserName = empty($arrUserInfos[$createUserId]['user_name']) ? strval($arrUserInfos[$createUserId]['user_nickname']) : $arrUserInfos[$createUserId]['user_name'];
                if(empty($anchorValidityInfo[$userId])){
                    $interval_validity_day = 0;
                }else{
                    $interval_validity_day = $anchorValidityInfo[$userId];
                }
                $item = array(
                    'date'          => $itemDate,
                    'user_id'       => $userId,
                    'user_name'     => $d['user_name'],
                    'anchor_type'   => $d['anchor_type'],
                    'union_id'      => $d['union_id'],
                    'union_name'    => $unionName,
                    'income_td'     => $anchorRangeData[$userId]['income_td'],
                    'live_duration' => $anchorRangeData[$userId]['live_duration'],
                    'income_rmb'    => $anchorRangeData[$userId]['income_td'] / Util_Ala_Journal::TD_TO_RMB,
                    'filter_income_td'      => $d['income_td'],
                    'filter_income_rmb'     => $d['income_td'] / Util_Ala_Journal::TD_TO_RMB,
                    'filter_live_duration'  => self::Sec2Time($d['live_duration']),
                    'journal_day_rate'      => $journal_day_rate,
                    'create_user_id'        => $createUserId,
                    'create_user_name'      => $createUserName,
                    'interval_validity_day' => $interval_validity_day,
                    'prop_count'            => $anchorRangeData[$userId]['prop_count'],
                    'prop_tdou'             => $anchorRangeData[$userId]['prop_tdou'],
                );
                $anchorResultData[] = $item;
            }
        }

//        todo weiyan02 2018-12-26 pm:zhouchuan要求添加日纬度的统计 end


        $arrRet = array(
            'rows' => $anchorResultData,
            'count' => $intListCount,
        );

        if(2 == $intExcel) {
            $arrExportData = $arrRet;
            if ($intStartDayDate != $intEndDayDate) {
                $strFileName = '主播总收益 ' . $intStartDayDate . '-' . $intEndDayDate;
            } else {
                $strFileName = '主播总收益 ' . '-' . $intEndDayDate;
            }
            header("Content-type: text/html; charset=gbk");
            header("Content-type:application/vnd.ms-excel");
            header("Content-Disposition:filename=$strFileName");
            if(1 == $intAnchorType) {
                $this->_exportExcel($arrExportData, $strFileName, $intRole);
            }
            else {
                $this->_exportExcel2($arrExportData, $strFileName);
            }

            return 0;
        }

        return $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrRet);
    }

    /**
     * @brief 根据主播id获取座驾信息
     * @param  $arrInput
     * @return array().
     */
    protected  static function getAnchorPropGiftInfo($arrInput)
    {
        //筛选时间
        $intYesterdayTime = strtotime('-1 day');   // 获取昨天的起始和今天的开始时间
        $intStartTime = intval(Bingo_HTTP_Request::get('start_time', $intYesterdayTime));
        $intEndTime   = intval(Bingo_HTTP_Request::get('end_time', time()));
        $arrUsersReq = array();
        $arrRet = array();

        $arrUserIds = array_unique($arrInput['user_ids']);
        if (empty($arrUserIds)) {
            return $arrRet;
        }
        $offset = 0;
        for ($i = 0; $i < (count($arrUserIds) / 100); $i++) {
            $arrUsersReq[] = array_slice($arrUserIds, $offset, 100);
            $offset = $offset + 100;
        }
        for ($timestamp = $intStartTime;$timestamp <= $intEndTime;$timestamp = $lastday) {
        $firstday = strtotime(date('Y-m-d', $timestamp));
        $lastday = $firstday + 24*60*60;
        $Obj = new Tieba_Multi('getAnchorPropGiftInfo');
        $strServiceKey = 'present';
        $strUserMethod = 'getAnchorPropGiftInfo';
        $caller = new Tieba_Service($strServiceKey);
        foreach ($arrUsersReq as $index => $item) {
            $arrServiceParams = array(
                'user_ids' => $item,
                'start_time' => $firstday,
                'end_time' => $lastday,
            );
            $strKey = $strUserMethod . '_' . $index;
            $arrInput = array(
                'serviceName' => $strServiceKey,
                'method' => $strUserMethod,
                'ie' => 'utf-8',
                'input' => $arrServiceParams,
            );

            $Obj->register($strKey, $caller, $arrInput);
        }
        $Obj->call();
        foreach ($arrUsersReq as $index => $item) {
            $strKey = $strUserMethod . '_' . $index;
            $resultUser = $Obj->getResult($strKey);
            foreach ($resultUser['data'] as $k => $v) {
                $resultTmp = array();
                $resultTmp[$k] = $v;
                $arrRet[] = $resultTmp;
            }
        }
    }
        return $arrRet;
    }

    /**
     * 秒转换为时间
     * @param $intTime  sec
     * @return string
     */
    private static function Sec2Time($intTime){
        $intTime = (int)$intTime;
        $arrValue = array(
            "years" => 0, "days" => 0, "hours" => 0,
            "minutes" => 0, "seconds" => 0,
        );
        /*
        if($intTime >= 31556926){
            $arrValue["years"] = floor($intTime/31556926);
            $intTime = ($intTime%31556926);
        }
        if($intTime >= 86400){
            $arrValue["days"] = floor($intTime/86400);
            $intTime = ($intTime%86400);
        }
         */
        if($intTime >= 3600){
            $arrValue["hours"] = floor($intTime/3600);
            $intTime = ($intTime%3600);
        }
        if($intTime >= 60){
            $arrValue["minutes"] = floor($intTime/60);
            $intTime = ($intTime%60);
        }
        $arrValue["seconds"] = floor($intTime);
        return $arrValue["hours"] ."小时". $arrValue["minutes"] ."分".$arrValue["seconds"]."秒";
    }

    /**
     * @param $arrRet
     * @param string $strExcelName
     * @return bool
     */
    private function _exportExcel2($arrRet, $strExcelName = '主播流水') {
        echo self::_utf8ToGbk("日期\t");
        echo self::_utf8ToGbk("用户id\t");
        echo self::_utf8ToGbk("用户名\t");
        echo self::_utf8ToGbk("日直播时长\t");
        echo self::_utf8ToGbk("日收益（T豆）\t");
        echo self::_utf8ToGbk("区间总收益（T豆）\t");
        echo self::_utf8ToGbk("区间直播时长 \t");
        echo self::_utf8ToGbk("区间有效天 \t");
        echo self::_utf8ToGbk("收入座驾个数 \t");
        echo self::_utf8ToGbk("收入座驾金额（T豆）\t");


        // 创建excel
        $arrExcelTableHead = array(
            'date'      => '日期',
            'user_id'   => '用户id',
            'user_name' => '用户名',
            'filter_live_duration'  => '日直播时长',
            'filter_income_td'      => '日收益（T豆)',
            'income_td' => '区间总收益（T豆）',
            'live_duration' => '区间直播时长',
            'interval_validity_day' => '区间有效天',
            'prop_count' => '收入座驾个数',
            'prop_tdou' => '收入座驾金额（T豆）',

        );
        $strExcelContent = Util_Ala_Excel::getExcel($strExcelName, $arrExcelTableHead, $arrRet['rows']);

        header("Content-type: application/octet-stream");
        header("Accept-Ranges: bytes");
        header("Accept-Length: " . strlen($strExcelContent));
        header("Content-Disposition: attachment; filename=" . $strExcelName . '.xls');
        echo $strExcelContent;

        return true;
    }

    /**
     * @param $arrRet
     * @param $strExcelName
     * @return bool
     */
    private function _exportExcel($arrRet, $strExcelName = '主播流水', $roleType = Lib_Ala_Define_Role::ROLE_UNION_PRESIDENT) {
        echo self::_utf8ToGbk("日期\t");
        echo self::_utf8ToGbk("用户id\t");
        echo self::_utf8ToGbk("用户名\t");
        echo self::_utf8ToGbk("公会id\t");
        if ($roleType != Lib_Ala_Define_Role::ROLE_UNION_PRESIDENT) {
            echo self::_utf8ToGbk("公会名称\t");
            echo self::_utf8ToGbk("所属合作运营\t");
        }
        echo self::_utf8ToGbk("日直播时长\t");
        echo self::_utf8ToGbk("日收益（T豆）\t");
        echo self::_utf8ToGbk("区间总收益（T豆）\t");
        echo self::_utf8ToGbk("区间直播时长 \t");
        echo self::_utf8ToGbk("区间有效天 \t");
        echo self::_utf8ToGbk("收入座驾个数 \t");
        echo self::_utf8ToGbk("收入座驾金额（T豆）\t");

        // 创建excel
        $arrExcelTableHead = array(
            'date'             => '日期',
            'user_id'         => '用户id',
            'user_name'       => '用户名',
            'union_id'       => '公会id',
        );
        if ($roleType != Lib_Ala_Define_Role::ROLE_UNION_PRESIDENT) {
            $arrExcelTableHead['union_name'] = '公会名称';
            $arrExcelTableHead['create_user_name'] = '所属合作运营';
        }
        $arrExcelTableHead['filter_live_duration'] = '日直播时长';
        $arrExcelTableHead['filter_income_td'] = '日收益（T豆)';
        $arrExcelTableHead['income_td'] = '区间总收益（T豆）';
        $arrExcelTableHead['live_duration'] = '区间直播时长';
        $arrExcelTableHead['interval_validity_day'] = '区间有效天';
        $arrExcelTableHead['prop_count'] = '收入座驾个数';
        $arrExcelTableHead['prop_tdou'] = '收入座驾金额（T豆）';

        $strExcelContent = Util_Ala_Excel::getExcel($strExcelName, $arrExcelTableHead, $arrRet['rows']);

        header("Content-type: application/octet-stream");
        header("Accept-Ranges: bytes");
        header("Accept-Length: " . strlen($strExcelContent));
        header("Content-Disposition: attachment; filename=" . $strExcelName . '.xls');
        echo $strExcelContent;

        return true;
    }

    /**
     * 新建Excel表并直接输出到浏览器
     * @param null
     * @return null
     * */
    protected static function _createExcel2($retArr) {
        echo self::_utf8ToGbk("日期\t");

        echo self::_utf8ToGbk("用户id\t");
        echo self::_utf8ToGbk("用户名\t");

        echo self::_utf8ToGbk("日直播时长\t");
        echo self::_utf8ToGbk("日收益（T豆）\t");

        //echo self::_utf8ToGbk("总收益（T豆）\t");
//        echo self::_utf8ToGbk("昨日流水（T豆）\t\n");
//        echo self::_utf8ToGbk("折合RMB（元）\t");
//        echo self::_utf8ToGbk("发放蓝钻数\t");
//        echo self::_utf8ToGbk("发放状态\t\n");

        foreach ($retArr as $arrValue) {
            echo self::_utf8ToGbk($arrValue['date'] . "\t");

            echo self::_utf8ToGbk($arrValue['user_id'] . "\t");
            echo self::_utf8ToGbk($arrValue['user_name'] . "\t");

            echo self::_utf8ToGbk($arrValue['filter_live_duration'] . "\t");
            echo self::_utf8ToGbk($arrValue['filter_income_td'] . "\t");
            //echo self::_utf8ToGbk($arrValue['income_td'] . "\t");
//            echo self::_utf8ToGbk($arrValue['journal_yesterday'] . "\t\n");
//            echo self::_utf8ToGbk($arrValue['income_rmb'] . "\t");
//            echo self::_utf8ToGbk($arrValue['send_td_android'] . "\t");
//            echo self::_utf8ToGbk($arrValue['send_td_status'] . "\t\n");
        }
    }

    /**
     * 新建Excel表并直接输出到浏览器
     * @param null
     * @return null
     * */
    protected static function _createExcel($retArr)
    {
        echo self::_utf8ToGbk("日期\t");
        echo self::_utf8ToGbk("用户id\t");
        echo self::_utf8ToGbk("用户名\t");
        echo self::_utf8ToGbk("公会id\t");
        echo self::_utf8ToGbk("公会名称\t");
        echo self::_utf8ToGbk("所属合作运营\t");
        echo self::_utf8ToGbk("日直播时长\t");
        echo self::_utf8ToGbk("日收益（T豆）\t");
        echo self::_utf8ToGbk("区间总收益（T豆）\t");
        echo self::_utf8ToGbk("区间直播时长 \t");
//        echo self::_utf8ToGbk("昨日流水（T豆）\t\n");
//        echo self::_utf8ToGbk("折合RMB（元）\t");
//        echo self::_utf8ToGbk("发放蓝钻数\t");
//        echo self::_utf8ToGbk("发放状态\t\n");

        foreach ($retArr as $arrValue) {

            echo self::_utf8ToGbk($arrValue['date'] . "\t");
            echo self::_utf8ToGbk($arrValue['user_id'] . "\t");
            echo self::_utf8ToGbk($arrValue['user_name'] . "\t");
            echo self::_utf8ToGbk($arrValue['union_id'] . "\t");
            echo self::_utf8ToGbk($arrValue['union_name'] . "\t");
            echo self::_utf8ToGbk($arrValue['create_user_name'] . "\t");
            echo self::_utf8ToGbk($arrValue['filter_live_duration'] . "\t");
            echo self::_utf8ToGbk($arrValue['filter_income_td'] . "\t");
            echo self::_utf8ToGbk($arrValue['income_td'] . "\t");
            echo self::_utf8ToGbk($arrValue['live_duration'] . "\t");
//            echo self::_utf8ToGbk($arrValue['journal_yesterday'] . "\t\n");
//            echo self::_utf8ToGbk($arrValue['income_rmb'] . "\t");
//            echo self::_utf8ToGbk($arrValue['send_td_android'] . "\t");
//            echo self::_utf8ToGbk($arrValue['send_td_status'] . "\t\n");
        }
    }

    /**
     * @param $errno
     * @param string $errmsg
     * @param array $arrExtData
     * @return int
     */
    private function _jsonRet($errno, $errmsg = '', array $arrExtData = array()) {
        $arrRet = array(
            'no' => intval($errno),
            'error' => empty($errmsg) ? Alalib_Conf_Error::getErrorMsg($errno) : $errmsg,
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }

    /**
     * 将utf8转为gbk
     * @param null
     * @return null
     * */
    protected static function _utf8ToGbk($strValue)
    {
        return iconv("UTF-8", "GBK", $strValue);
    }
}