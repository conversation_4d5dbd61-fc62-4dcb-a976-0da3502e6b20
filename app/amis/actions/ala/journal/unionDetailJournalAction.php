<?php
/**
 * 公会流水月份汇总
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 17/7/17
*/


class unionDetailJournalAction extends Util_Action
{

    protected $strAmisGroup = '';
    protected $strAmisPerm = '';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;
    protected $bolNeedLogin = false;

    /**
     * execute
     * @param null
     * @return null
     * */
    public function _execute() {
        $intUserId    = Util_User::$intUserId;
        $arrInput = array(
            'user_id' => $intUserId,
            'auth_id' => Lib_Ala_Define_Authority::AUTHORITY_JOURNAL_VIEW_TEAM
        );
        $arrOutput = Service_Ala_Authority_Authority::canUserAccess($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Authority_Authority::canUserAccess input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, $arrOutput['usermsg']);
        }
        $bolCanAccess = $arrOutput['data'];
        if ($bolCanAccess === false) {
            return self::_jsonRet(Tieba_Errcode::ERR_ACTION_FORBIDDEN, "无权限");
        }
        $intSearchUnionId = intval(Bingo_HTTP_Request::get('search_union_id', 0));

        if (!empty($intSearchUnionId))  {
            //检查权限 --是否是本公会长代运营
            if ($arrRet = Util_Ala_Perm::checkUnionPermFailedPlus($intSearchUnionId)) {
                return $this->_jsonRet($arrRet['no'], $arrRet['error'], $arrRet['data']);
            }
        }

        $arrServiceInput = array(
            'union_id' => $intSearchUnionId,
            'offset' => 0,
            // 1天1条，取一年
            'limit' => 400,

            'start_time' => 1,
            'end_time' => time(),
        );

        // 调取列表服务
        $arrOutput = Service_Ala_Journal_Journal::getUnionJournalList($arrServiceInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call Service_Ala_Journal_Journal getAnchorJournalList fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrUnionList = $arrOutput['data'];
        $arrMonthDate = array();
        $arrYearMonth = array();
        // 按月整理数据
        $intSmallestMonth =  date('Ym',time());
        foreach ($arrUnionList as $arrUnion) {
            $intDate = $arrUnion['date'];
            $intMonth = date('Ym',strtotime($intDate));
            if(!isset($arrMonthDate[$intMonth])) {
                $arrMonthDate[$intMonth]['income_td'] = 0;
            }
            $arrMonthDate[$intMonth]['income_td'] += $arrUnion['income_td'];
            if($intMonth < $intSmallestMonth) {
                $intSmallestMonth = $intMonth;
            }
        }
        foreach ($arrMonthDate as $intMonth => $arrMonth) {
            $arrYearMonth[] = $intMonth;
            $arrMonthDate[$intMonth]['month_date'] = $intMonth;
            $arrMonthDate[$intMonth]['income_rmb'] = $arrMonthDate[$intMonth]['income_td'] / Util_Ala_Journal::TD_TO_RMB;
        }
        // 最后一个月可能取不全天数，所以去掉
        if(count($arrMonthDate) > 12){
            unset($arrMonthDate[$intSmallestMonth]);
        }

        $arrMonthDate = array_values($arrMonthDate);
        array_multisort($arrYearMonth,SORT_DESC,$arrMonthDate);

        $arrRet = array(
            'rows' => $arrMonthDate,
        );
        return $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrRet);
    }

    /**
     * @param $errno
     * @param string $errmsg
     * @param array $arrExtData
     * @return int
     */
    private function _jsonRet($errno, $errmsg = '', array $arrExtData = array()) {
        $arrRet = array(
            'no' => intval($errno),
            'error' => strval($errmsg),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }
}
