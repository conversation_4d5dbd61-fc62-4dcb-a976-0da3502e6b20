<?php
/**
 * 获取手百官方推荐配置
 * Created by PhpStorm.
 * User: zhanghanqing
 * Date: 2018/12/10
 * Time: 9:46 PM
 */


class getShoubaiMisConfAction extends Util_Action
{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    /**
     * @return
     */
    public function _execute()
    {
        $intUserId = Util_User::$intUserId;
        $strOpUserName = Util_User::$strUserName;

        // Request
        $intPage = intval(Bingo_Http_Request::get('pn', 1));
        // 列表数据条数
        $intReqNum = intval(Bingo_Http_Request::get('ps', 10));
        $intConfType = intval(Bingo_Http_Request::get('conf_type', 0));

        $strOrderBy  = strval(Bingo_Http_Request::get('orderBy', 'id'));
        $strOrderDir = strval(Bingo_Http_Request::get('orderDir', 'DESC'));

        if ($strOrderBy == 'status_text'){
            $strOrderBy = 'status';
        }
        $strCond = ' conf_type = ' . $intConfType;
        $arrInput = array(
            'cond' => $strCond,
        );
        $arrOutput = Alalib_Util_Service::call("ala", "getShoubaiMisConfCountByCond", $arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call user::getUidByUnames fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "服务器出错");
        }
        $intCount = $arrOutput['data']['count'];
        $intOffset = ($intPage - 1) * $intReqNum;

        if (empty($strOrderBy)){
            $strOrderBy = 'id';
        }
        $strCond .= ' order by ' . $strOrderBy . ' ' . $strOrderDir . ' ';
        $strCond .= " limit $intOffset,$intReqNum";

        $arrReq = array(
            'cond' => $strCond,
        );
        $arrRes = Alalib_Util_Service::call("ala", "getShoubaiMisConfByCond", $arrReq);
        if ($arrRes === false || $arrRes['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala::operatorSendPetalToCooperate fail. input[" . serialize($arrReq) . "] output[" . serialize($arrRes) . "]";
            Bingo_Log::fatal($strLog);
            return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $arrList = $arrRes['data'];
        foreach ($arrList as $index => $item){
            $arrList[$index]['status_text'] = 0 == $item['status'] ? '正常' : '被删';
        }

        // 返回列表
        $arrRet = array(
            'rows' => $arrList,
            'count' => $intCount
        );
        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, "", $arrRet);
    }

    /**
     * @param $errno
     * @param string $errmsg
     * @param array $arrExtData
     * @return int
     */
    private function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => Alalib_Conf_Error::getErrorMsg($errno),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }
}