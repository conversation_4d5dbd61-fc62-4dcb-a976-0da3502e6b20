<?php

/**
 * Created by PhpStorm.
 * User: b<PERSON><PERSON><PERSON>
 * Date: 18/3/21
 * Time: 下午4:48
 */
class auditAction extends Util_Action
{

	protected $bolNeedLogin = false;
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;

	/**
	 * @param  [int]   错误码
	 * @param  [str]   错误信息
	 *
	 * @return [array] 返回数组
	 */
	public static function getErrRet($intErrNo, $strMsg = '')
	{
		$arrErrRet        = array(
			'no' => $intErrNo,
		);
		$strMsg           = Bingo_Encode::convert($strMsg, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
		$arrErrRet['msg'] = empty($strMsg) ? Tieba_Error::getUserMsg($intErrNo) : $strMsg;
		
		return $arrErrRet;
	}
	
	/**
	 * @param  [int]  错误码
	 * @param  string 错误信息
	 * @param  array  返回数据
	 *
	 * @return [bool]
	 */
	public static function outputJson($intErrNo, $strErrMsg = '', $arrOutput = array())
	{
		$arrRet         = self::getErrRet($intErrNo, $strErrMsg);
		$arrRet['data'] = $arrOutput;
		echo Bingo_String::array2json($arrRet);
		//return true;
	}
	
	/**
	 * @param  [array] 返回数组
	 *
	 * @return [bool]
	 */
	public static function output($arrRet)
	{
		echo Bingo_String::array2json($arrRet);
		//return true;
	}
	
	/**
	 * 执行函数
	 *
	 * @param null
	 *
	 * @return null
	 * 
	 */
	public function _execute()
	{
		$intStatus = intval(Bingo_Http_Request::get("status", -1));
		$intId = intval(Bingo_Http_Request::get("id", 0));
		if ($intId <= 0) {
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, $arrOut, 'success');
		}
		//$op_id     = Util_User::$intUserId;
		//$op_name   = Util_User::$strUserName;
		$arrInput = array(
			'id' => $intId,
			'status' => $intStatus,
		);
		//$arrRes = Service_Gameforum_GameforumInv::getOfficialInfoListForHome($arrInput);
	    $arrRes = Tieba_Service::call('game', 'auditOfficial', $arrInput, null, null, 'post', 'php', 'utf-8');
	    if (false === $arrRes || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning("getOfficialInfoListForHome fail.".serialize($arrRes));
            return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_UNKOWN, Tieba_Error::getErrmsg(Tieba_Errcode::ERR_UNKOWN));
		}
		
		//$arrOut['rows'][] = array('name' => 1);
		return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, array(), 'success');
	}
	
}

