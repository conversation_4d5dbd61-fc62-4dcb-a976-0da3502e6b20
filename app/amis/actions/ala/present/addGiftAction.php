<?php

class addGiftAction extends Util_Action
{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;
    //礼物新分类
    const GIFT_ID_PLATFORM_TYPE = 'gift_id_platform_type_';

    //权限部分
    //接口权限
    const INTERFACE_PERMISSION = 3.00001;
    //接口类型
    const INTERFACE_TYPE = 1; //操作类型， 0 查询  1 新增操作  2 更新操作

    /**
     * @return bool
     */
    public function _execute()
    {
        //权限校验
        $strOpName = Util_User::$strUserName;
        $arrOutput = Lib_Platform::checkPermission($strOpName,self::INTERFACE_PERMISSION,self::INTERFACE_TYPE);
        if ($arrOutput === false || $arrOutput['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Lib_Platform::checkPermission fail. input[" . serialize(array()) . "] output[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            $this->setErroInfo($arrOutput['errno'],$arrOutput['usermsg']);
            return false;
        }

		$arrInput = $this->_initParam();
		if(false == $arrInput){
			return false;
		}
        Bingo_Log::warning(" _initParam return:" . json_encode($arrInput));
        //如果是动态轨道Mp4礼物，将动态礼物标示置为0，轨道动态礼物作为小礼物下发
        //用动态礼物，主要是为了上传mp4文件
        if ((int)$arrInput['is_track_mp4'] === 1) {
            $arrInput['is_dynamic_gift'] = 0;
        }
		$arrOut   = Tieba_Service::call('present', 'addGift', $arrInput, null, null, 'post', 'php', 'utf-8');
		$intErrNo = (isset($arrOut['errno'])) ? $arrOut['errno'] : -1;
		if(empty($arrOut) || Tieba_Errcode::ERR_SUCCESS != $intErrNo){
			$this->setErroInfo($intErrNo, 'Tieba_Service_call_present_error['.serialize($arrOut).']');
			return false;
		}
		$giftId =  $arrOut['data']['gift_id'];
        //2020.6.8 礼物新增分类
        $platformType = intval(Bingo_Http_Request::get('platform_type', 1)); // 1-直播平台在线;2-直播其他在线;3-贴吧礼物;
        /*
        贴吧	1-10273	10278-10337	10340-10342	10344-10363	10369-10386
        平台直播	10276-10277	10338-10339	10343	10365-10367	10387-10685	10704	10816-10829	10836	10840-10846	10848	10850-10851	10860-10861	10863-10895	10905-10925
        外部直播	10686-10702	10705-10815	10830	10837-10839	10847	10849	10852-10859	10862	10896-10904
        */
        $key = self::GIFT_ID_PLATFORM_TYPE;
        $arrFeedTag = array();
        $arrInputForHand = array(
            'key' => $key,
            'field' => $platformType,
            'redis' => 'ala_new',
        );
        $strPushTagResult = Util_Redis::hgetFromRedis($arrInputForHand);
        if (false === $strPushTagResult) {
            Bingo_Log::warning("getTagForAlaDingUser call redis err. input param is:" . serialize($arrInputForHand));
        }

        if (!empty($strPushTagResult)) {
            $arrFeedTag = explode(',', $strPushTagResult);
        }
        //其中没有则附加最后
        if (!empty($giftId) && !in_array($giftId, $arrFeedTag)) {
            $arrFeedTag[] = $giftId;
            //去重空
//        $arrFeedTag = array_unique(array_filter($arrFeedTag));
            $strAttachRes = implode(',', $arrFeedTag);
            $redisInput = array(
                "key" => $key,
                "field" => $platformType,
                "value" => $strAttachRes,
                'redis' => 'ala_new',
            );
            $arrRedisOutput = Util_Redis::hsetToRedis($redisInput);
            if (false === $arrRedisOutput) {
                Bingo_Log::warning("Insert call redis fail, the input is " . serialize($redisInput));
            }
        }
        $this->setErroInfo($intErrNo, 'success', $arrOut['data']);
        return true;
    }

    /**
     * @return array|bool
     */
    private function _initParam()
    {
        $subapp_type = trim(strval(Bingo_Http_Request::get('subapp_type', 'tieba')));
        $gift_name = base64_encode(trim(Bingo_Http_Request::get('gift_name', '')));   //present模块编码问题,可能有中文的先base64过去,再解码
        $price = intval(Bingo_Http_Request::get('price', 0));
        $ios_price = intval(Bingo_Http_Request::get('ios_price', 0));
        $thumbnail_url = trim(strval(Bingo_Http_Request::get('thumbnail_url', '')));
        $large_thumbnail_url = trim(strval(Bingo_Http_Request::get('large_thumbnail_url', '')));
        $pay_thumbnail_url = trim(strval(Bingo_Http_Request::get('pay_thumbnail_url', '')));
        $play_url = trim(strval(Bingo_Http_Request::get('play_url', '')));
        $gift_desc = base64_encode(trim(strval(Bingo_Http_Request::get('gift_desc', ''))));
        $activity_type = intval(Bingo_Http_Request::get('activity_type', 0));//1限时 2限量 3打折 4会员
        $mark_url = trim(strval(Bingo_Http_Request::get('mark_url', '')));
        $gift_count = intval(Bingo_Http_Request::get('gift_count', 0));
        $begin_time = intval(Bingo_Http_Request::get('begin_time', 0));
        $end_time = intval(Bingo_Http_Request::get('end_time', 0));
        $discount = intval(Bingo_Http_Request::get('discount', 0));
        $ios_discount = intval(Bingo_Http_Request::get('ios_discount', 0));
        $bind_tbkey = intval(Bingo_Http_Request::get('bind_tbkey', 0));
        $active_tbkey = intval(Bingo_Http_Request::get('active_tbkey', 0));
        $tbkey_price = intval(Bingo_Http_Request::get('tbkey_price', 0));
        $is_combo = intval(Bingo_Http_Request::get('is_combo', 0));
        $animation_type = intval(Bingo_Http_Request::get('animation_type', 0));
        //广播礼物
        $intIsBroadcastGift = intval(Bingo_Http_Request::get('is_broadcast_gift', 0));
        $isSupportBroadcastJump = intval(Bingo_Http_Request::get('is_support_broadcast_jump', 0));
        //贴纸礼物
        $intStickerGift = intval(Bingo_Http_Request::get('is_sticker_gift', 0));
        $strStickerFile = trim((string)Bingo_Http_Request::get('sticker_file', ''));
        $strStickerFileMd5 = trim((string)Bingo_Http_Request::get('sticker_file_md5', ''));
        $strStickerFileLiveapp = trim((string)Bingo_Http_Request::get('sticker_file_liveapp', ''));
        $strStickerFileMd5Liveapp = trim((string)Bingo_Http_Request::get('sticker_file_md5_liveapp', ''));

        $intStickerDuration = (int)Bingo_Http_Request::get('sticker_duration', 0);
        $intStickerType = (int)Bingo_Http_Request::get('sticker_type', 0);
        //动态礼物
        $intIsDynamicGift = intval(Bingo_Http_Request::get('is_dynamic_gift', 0));
        //大礼物所属业务线
        $intBranchDynamicGift = intval(Bingo_Http_Request::get('branch', 0));
        //是否为轨道mp4礼物
        $isTrackMp4 = intval(Bingo_Http_Request::get('is_track_mp4', 0));
        $is_broadcast_score_reach = intval(Bingo_Http_Request::get('is_broadcast_score_reach', 0));
        $broadcast_score_reach_score = intval(Bingo_Http_Request::get('broadcast_score_reach_score', 599000));
        $broadcast_score_reach_broad_type = intval(Bingo_Http_Request::get('broadcast_score_reach_broad_type', 0));
        $true_love_multiple = doubleval(Bingo_Http_Request::get('true_love_multiple', 1));
        $yybi_price 	= intval(Bingo_Http_Request::get('yybi_price', 0));
        $ios_yybi_price = intval(Bingo_Http_Request::get('ios_yybi_price', 0));
        //直播礼物权限
        $intGiftType = intval(Bingo_Http_Request::get('gift_type', 0));   //    无限制/FAN团礼物/经验值11级/经验值18级/经验值25级/经验值31级
        //竖屏
        $strZipMd5 = trim(strval(Bingo_Http_Request::get('zip_md5', '')));
        $strZipUrl = trim(strval(Bingo_Http_Request::get('zip_url', '')));
        if (empty($strZipUrl)) {
            $strZipUrl = trim(strval(Bingo_Http_Request::get('zip_url_two', '')));
        }
        $strMp4Md5 = trim(strval(Bingo_Http_Request::get('video_md5', '')));
        $strMp4Url = trim(strval(Bingo_Http_Request::get('video_url', '')));
        if (empty($strMp4Url)) {
            $strMp4Url = trim(strval(Bingo_Http_Request::get('video_url_two', '')));
        }
        $arrTemp = explode('.', end(explode('/', $strZipUrl)));
        $strZipName = urldecode(strval($arrTemp[0]));
        $arrMp4Temp = explode('.', end(explode('/', $strMp4Url)));
        $strMp4Name = urldecode(strval($arrMp4Temp[0]));
        $intFrameRate = intval(Bingo_Http_Request::get('frame_rate', -1));
        $intFrameCount = intval(Bingo_Http_Request::get('frame_count', -1));
        $intRepeatCount = intval(Bingo_Http_Request::get('repeat_count', -1));
        $intIsBottomMargin = intval(Bingo_Http_Request::get('is_bottom_margin', -1));
        $doubleWidth = doubleval(Bingo_Http_Request::get('width', -1));
        $doubleHeight = doubleval(Bingo_Http_Request::get('height', -1));
        $doubleX = doubleval(Bingo_Http_Request::get('opposite_x', -1));
        $doubleY = doubleval(Bingo_Http_Request::get('opposite_y', -1));
        $doubleUserX = doubleval(Bingo_Http_Request::get('user_info_x', -1));
        $doubleUserY = doubleval(Bingo_Http_Request::get('user_info_y', -1));
        //横屏
        $issetHeng = intval(Bingo_Http_Request::get('isset_heng', 0)); // 是否有横屏资源，默认是0:无横屏资源，1：有横屏资源
        $strZipMd5Heng = trim(strval(Bingo_Http_Request::get('zip_md5_heng', '')));
        $strZipUrlHeng = trim(strval(Bingo_Http_Request::get('zip_url_heng', '')));
        if (empty($strZipUrlHeng)) {
            $strZipUrlHeng = trim(strval(Bingo_Http_Request::get('zip_url_heng_two', '')));
        }
        $strMp4Md5Heng = trim(strval(Bingo_Http_Request::get('video_md5_heng', '')));
        $strMp4UrlHeng = trim(strval(Bingo_Http_Request::get('video_url_heng', '')));
        if (empty($strMp4UrlHeng)) {
            $strMp4UrlHeng = trim(strval(Bingo_Http_Request::get('video_url_heng_two', '')));
        }
        $arrTempHeng = explode('.', end(explode('/', $strZipUrlHeng)));
        $strZipNameHeng = urldecode(strval($arrTempHeng[0]));
        $arrMp4TempHeng = explode('.', end(explode('/', $strMp4UrlHeng)));
        $strMp4NameHeng = urldecode(strval($arrMp4TempHeng[0]));
        $intFrameRateHeng = intval(Bingo_Http_Request::get('frame_rate_heng', -1));
        $intFrameCountHeng = intval(Bingo_Http_Request::get('frame_count_heng', -1));
        $intRepeatCountHeng = intval(Bingo_Http_Request::get('repeat_count_heng', -1));
        $intIsBottomMarginHeng = intval(Bingo_Http_Request::get('is_bottom_margin_heng', -1));
        $doubleWidthHeng = doubleval(Bingo_Http_Request::get('width_heng', -1));
        $doubleHeightHeng = doubleval(Bingo_Http_Request::get('height_heng', -1));
        $doubleXHeng = doubleval(Bingo_Http_Request::get('opposite_x_heng', -1));
        $doubleYHeng = doubleval(Bingo_Http_Request::get('opposite_y_heng', -1));
        $doubleUserXHeng = doubleval(Bingo_Http_Request::get('user_info_x_heng', -1));
        $doubleUserYHeng = doubleval(Bingo_Http_Request::get('user_info_y_heng', -1));
        $intOnlineStatus = trim(strval(Bingo_Http_Request::get('online_status', 1)));  // 1,默认全量上线   2,白名单可见
        //点击弹窗展示
        $intToastGift = intval(Bingo_Http_Request::get('is_toast_gift', 0));
        $strToastText = base64_encode(trim(Bingo_Http_Request::get('toast_text', '')));
        $strToastJumpUrl = strval(Bingo_Http_Request::get('toast_jump_url', ''));
        if(empty($strZipName)) { // 和端上同学确认，这个字段一定不能为空，否则会导致端上创建目录失败
            $strZipName = $strMp4Name;
        }
        if (empty($strZipNameHeng)) { // 和端上同学确认，这个字段一定不能为空，否则会导致端上创建目录失败
            $strZipNameHeng = $strMp4NameHeng;
        }

        if (1 == $intIsDynamicGift) {
            $arrGiftZip = array(
                'shu_ping' => array(
                    "zip_md5" => $strZipMd5,
                    "zip_url" => $strZipUrl,
                    "zip_name" => $strZipName,
                    "is_landscape" => 0,     //是否为横屏资源 默认为0 ，1为横屏
                    "video_url" => $strMp4Url,
                    "video_md5" => $strMp4Md5,
                    "video_name" => $strMp4Name,
                ),
                'heng_ping' => array(),
//                'heng_ping' => array(
//                    "zip_md5" => $strZipMd5Heng,
//                    "zip_url" => $strZipUrlHeng,
//                    "zip_name" => $strZipNameHeng,
//                    "is_landscape" => 1,     //是否为横屏资源 默认为0 ，1为横屏
//                    "video_url" => $strMp4UrlHeng,
//                    "video_md5" => $strMp4Md5Heng,
//                    "video_name" => $strMp4NameHeng,
//                ),
                'online_status' => $intOnlineStatus,
            );
            $arrGiftConfigInfo = array(
                'shu_ping' => array(
                    "frame_rate" => $intFrameRate,      //帧率
                    "frame_count" => $intFrameCount,    //帧序列图片数量
                    "repeat_count" => $intRepeatCount,
                    "is_bottom_margin" => $intIsBottomMargin,   //是否底部对其，默认不返回或0，当返回为1时，opposite_y 为帧图片相对于底部的y值
                    "width" => $doubleWidth,            //图片宽
                    "height" => $doubleHeight,          //图片高
                    "opposite_x" => $doubleX,           //相对位置x
                    "opposite_y" => $doubleY,           //相对位置 y
                    "user_info_x" => $doubleUserX,      //用户信息区域相对坐标x
                    "user_info_y" => $doubleUserY,      //用户信息区域相对坐标y
                ),
                'heng_ping' => array(),
//                'heng_ping' => array(
//                    "frame_rate" => $intFrameRateHeng,      //帧率
//                    "frame_count" => $intFrameCountHeng,    //帧序列图片数量
//                    "repeat_count" => $intRepeatCountHeng,
//                    "is_bottom_margin" => $intIsBottomMarginHeng,   //是否底部对其，默认不返回或0，当返回为1时，opposite_y 为帧图片相对于底部的y值
//                    "width" => $doubleWidthHeng,            //图片宽
//                    "height" => $doubleHeightHeng,          //图片高
//                    "opposite_x" => $doubleXHeng,           //相对位置x
//                    "opposite_y" => $doubleYHeng,           //相对位置 y
//                    "user_info_x" => $doubleUserXHeng,      //用户信息区域相对坐标x
//                    "user_info_y" => $doubleUserYHeng,      //用户信息区域相对坐标y
//                ),
            );
            if ($issetHeng == 1) { // 如果
                $arrGiftZip['heng_ping'] = array(
                    "zip_md5" => $strZipMd5Heng,
                    "zip_url" => $strZipUrlHeng,
                    "zip_name" => $strZipNameHeng,
                    "is_landscape" => 1,     //是否为横屏资源 默认为0 ，1为横屏
                    "video_url" => $strMp4UrlHeng,
                    "video_md5" => $strMp4Md5Heng,
                    "video_name" => $strMp4NameHeng,
                );
                $arrGiftConfigInfo['heng_ping'] =  array(
                    "frame_rate" => $intFrameRateHeng,      //帧率
                    "frame_count" => $intFrameCountHeng,    //帧序列图片数量
                    "repeat_count" => $intRepeatCountHeng,
                    "is_bottom_margin" => $intIsBottomMarginHeng,   //是否底部对其，默认不返回或0，当返回为1时，opposite_y 为帧图片相对于底部的y值
                    "width" => $doubleWidthHeng,            //图片宽
                    "height" => $doubleHeightHeng,          //图片高
                    "opposite_x" => $doubleXHeng,           //相对位置x
                    "opposite_y" => $doubleYHeng,           //相对位置 y
                    "user_info_x" => $doubleUserXHeng,      //用户信息区域相对坐标x
                    "user_info_y" => $doubleUserYHeng,      //用户信息区域相对坐标y
                );
            }
        } else if ($intStickerGift == 1) {
            $arrGiftZip = array(
                'sticker_file' => $strStickerFile,
                'sticker_file_md5' => $strStickerFileMd5,
                'sticker_file_liveapp' => $strStickerFileLiveapp,
                'sticker_file_md5_liveapp' => $strStickerFileMd5Liveapp,
            );
            $arrGiftConfigInfo = array(
                'sticker_duration' => $intStickerDuration,
                'sticker_type' => $intStickerType,
            );
        } else {
            $arrGiftZip = array();
            $arrGiftConfigInfo = array();
        }
        $strGiftZip = Bingo_String::array2json($arrGiftZip);
        $strConfigInfo = Bingo_String::array2json($arrGiftConfigInfo);

        //add at 20190521  begin
        $intIsTimeLimitGift = intval(Bingo_Http_Request::get('is_time_limit_gift', 0));
        $intIsShowForAll = intval(Bingo_Http_Request::get('is_show_for_all', 0));        // 特权礼物是否全量展示，0-是，1-仅已解锁用户展示
        $strGiftTag = base64_encode(strval(Bingo_Http_Request::get('gift_tag', '')));    // 礼物标签：特权、贵族，等级...
        $strGiftTipText = base64_encode(strval(Bingo_Http_Request::get('gift_tip_text', '')));
        $intIsWhiteListGift = intval(Bingo_Http_Request::get('is_white_list_gift', 0));
        $strUserWhiteListAdd = strval(Bingo_Http_Request::get('user_white_list_add', ''));
        $arrUserWhiteListAdd = array();
        if (1 == $intIsWhiteListGift) {
            $arrUserWhiteListAdd = explode(' ', $strUserWhiteListAdd);
            $arrUserWhiteListAdd = array_filter(array_unique($arrUserWhiteListAdd));
            if (count($arrUserWhiteListAdd) > 100) {
                Bingo_Log::warning("error params!too many white list");
                $this->setErroInfo(Tieba_Errcode::ERR_PARAM_ERROR, '参数错误,白名单一次最多100个用户ID');
                return false;
            }
            foreach ($arrUserWhiteListAdd as $value) {
                if (!is_numeric($value)) {
                    Bingo_Log::warning("error params!not numeric");
                    $this->setErroInfo(Tieba_Errcode::ERR_PARAM_ERROR, '参数错误,白名单里面有非数字');
                    return false;
                }
            }
        }
        //add at 20190521  end

        $intMarkPicWidth = intval(Bingo_Http_Request::get('mark_pic_width', 0));
        $intMarkPicHeight = intval(Bingo_Http_Request::get('mark_pic_height', 0));
        $arrGiftExtString = array(
            'mark_pic_width' => $intMarkPicWidth,
            'mark_pic_height' => $intMarkPicHeight,
        );

        //add at 20190119 start
        $is_discount = intval(Bingo_Http_Request::get('is_discount', 0));
        $original_price = intval(Bingo_Http_Request::get('original_price', 0));
        $discount_price = intval(Bingo_Http_Request::get('discount_price', 0));
        $gift_tag_type = intval(Bingo_Http_Request::get('gift_tag_type', 0));
        //add at 20190119 end

        //add at 2020.8.5 融合媒体直播 start
        $serviceType = intval(Bingo_Http_Request::get('service_type', 0)); // 业务类型 0-秀场, 1-媒体直播
        //add at 2020.8.5 融合媒体直播 end

        //长按礼物设置
        $bol_long_press          = intval(Bingo_Http_Request::get('bol_long_press', 0));
        $bol_long_press_dynamic  = intval(Bingo_Http_Request::get('bol_long_press_dynamic', 0));
        $long_press_dynamic_text = base64_encode(trim(strval(Bingo_Http_Request::get('long_press_dynamic_text', ''))));
        $long_press_screen_type  = intval(Bingo_Http_Request::get('long_press_screen_type', 0));
        $long_press_screen_url   = htmlspecialchars_decode(strval(Bingo_Http_Request::get('long_press_screen_url', '')));

        //运营活动礼物
        $activity_redirect_url_options = intval(Bingo_Http_Request::get('activity_redirect_url_options', 0));
        $activity_redirect_url = htmlspecialchars_decode(trim(strval(Bingo_Http_Request::get('activity_redirect_url', ''))));
        $activity_introduce_image = trim(strval(Bingo_Http_Request::get('activity_introduce_image', '')));

        //轨道背景色
        $track_color_start = trim(strval(Bingo_Http_Request::get('track_color_start', '')));

        //独有礼物
        $bol_assign_gift   = intval(Bingo_Http_Request::get('bol_assign_gift', 0));
        $assign_uids       = strval(Bingo_Http_Request::get('assign_uids', ''));
        //头条礼物
        $bol_toutiao_gift = intval(Bingo_Http_Request::get('is_toutiao', 0));

        // 福袋相关配置
        $max_send_num = intval(Bingo_Http_Request::get('max_send_num', -1));
        $luck_draw_id = intval(Bingo_Http_Request::get('luck_draw_id', 0));
        //互动礼物配置
        $cooperate_gift_type = intval(Bingo_Http_Request::get('cooperate_gift_type', 0));
        $anchor_white_list = Bingo_Http_Request::get('anchor_white_list', '');
        $room_white_list = Bingo_Http_Request::get('room_white_list', '');

        $arrParam = array(
            'gift_name' => $gift_name,
            'price' => $price,
            'ios_price' => $ios_price,
            'thumbnail_url' => $thumbnail_url,
            'large_thumbnail_url' => $large_thumbnail_url,
            'pay_thumbnail_url' => $pay_thumbnail_url,
            'play_url' => $play_url,
            'gift_desc' => $gift_desc,
            'activity_type' => $activity_type,
            'mark_url' => $mark_url,
            'gift_count' => $gift_count,
            'begin_time' => $begin_time,
            'end_time' => $end_time,
            'discount' => $discount,
            'ios_discount' => $ios_discount,
            'bind_tbkey' => $bind_tbkey,
            'active_tbkey' => $active_tbkey,
            'tbkey_price' => $tbkey_price,
            'is_combo' => $is_combo,
            'animation_type' => $animation_type,
            'is_dynamic_gift' => $intIsDynamicGift,
            'branch' => $intBranchDynamicGift,
            'gift_zip' => $strGiftZip,
            'config_info' => $strConfigInfo,
            'is_broadcast_gift' => $intIsBroadcastGift,
            'is_support_broadcast_jump' => $isSupportBroadcastJump,
            'gift_type' => $intGiftType,
            'is_sticker_gift' => $intStickerGift,
            'subapp_type' => $subapp_type,
            'is_time_limit_gift' => $intIsTimeLimitGift,
            'is_show_for_all' => $intIsShowForAll,    // 特权礼物是否全量展示，0-是，1-仅已解锁用户展示
            'gift_tag' => $strGiftTag,        // 礼物标签：特权、贵族，等级...
            'gift_tip_text' => $strGiftTipText,
            'is_white_list_gift' => $intIsWhiteListGift,
            'user_white_list_add' => $arrUserWhiteListAdd,
            'gift_ext_str' => json_encode($arrGiftExtString),
            'is_toast_gift' => $intToastGift,
            'toast_text' => $strToastText,
            'toast_jump_url' => $strToastJumpUrl,
            'is_discount' => $is_discount,
            'original_price' => $original_price,
            'discount_price' => $discount_price,
            'gift_tag_type' => $gift_tag_type,
            'service_type' => $serviceType,
            'bol_long_press'          => $bol_long_press,
            'bol_long_press_dynamic'  => $bol_long_press_dynamic,
            'long_press_dynamic_text' => $long_press_dynamic_text,
            'long_press_screen_type'  => $long_press_screen_type,
            'long_press_screen_url'   => $long_press_screen_url,
            'bol_assign_gift'         => $bol_assign_gift,
            'assign_uids'             => $assign_uids,
            'is_toutiao' => $bol_toutiao_gift,
            'max_send_num' => $max_send_num,
            'luck_draw_id' => $luck_draw_id,
            'is_track_mp4' => $isTrackMp4,
            'is_broadcast_score_reach'          => $is_broadcast_score_reach,
            'broadcast_score_reach_score'       => $broadcast_score_reach_score,
            'broadcast_score_reach_broad_type'  => $broadcast_score_reach_broad_type,
            'true_love_multiple'                => $true_love_multiple,
            'yybi_price'                        => $yybi_price,
            'ios_yybi_price'                    => $ios_yybi_price,
            'activity_redirect_url_options' => $activity_redirect_url_options,
            'activity_redirect_url'         => $activity_redirect_url,
            'activity_introduce_image'      => $activity_introduce_image,
            'track_color_start'             => $track_color_start,
            'cooperate_gift_type' => $cooperate_gift_type,
            'anchor_white_list' => $anchor_white_list,
            'room_white_list' => $room_white_list,
        );
        //Bingo_Log::warning("====>" . var_export($arrParam, true));   //for test
        if (empty($gift_name) || $price < 0 || empty($thumbnail_url) || empty($large_thumbnail_url) || empty($pay_thumbnail_url) || $activity_type < 0 || $gift_count < 0 || $discount < 0 || $ios_discount < 0) {
            $this->setErroInfo(Tieba_Errcode::ERR_PARAM_ERROR, 'param is wrong', $arrParam);
            return false;
        }
        //限时活动，起止时间不能为空
        if ((1 === $activity_type || 1 == $intIsTimeLimitGift) && ((empty($begin_time)) || (empty($end_time)))) {
            Bingo_Log::warning("error params!");
            $this->setErroInfo(Tieba_Errcode::ERR_PARAM_ERROR, '开始时间或者结束时间是空的', $arrParam);
            return false;
        }
        if (1 == $intIsDynamicGift) {   //是动态礼物
            //if (empty($strZipUrl) || empty($strZipMd5) || empty($strZipName) || $intFrameRate < 0 || $intFrameCount < 0 || $intRepeatCount < 0 || $intIsBottomMargin < 0 || $doubleWidth < 0 || $doubleHeight < 0 || $doubleX < 0 || $doubleY < 0 || $doubleUserX < 0 || $doubleUserY < 0) {
            if ( empty($strZipName) || $intBranchDynamicGift < 0 || $intRepeatCount < 0 || $intIsBottomMargin < 0 || $doubleWidth < 0 || $doubleHeight < 0 || $doubleX < 0 || $doubleY < 0 || $doubleUserX < 0 || $doubleUserY < 0) {
                Bingo_Log::warning("error params!");
                $this->setErroInfo(Tieba_Errcode::ERR_PARAM_ERROR, '动态礼物配置参数错误');
                return false;
            }
        }
        if (1 == $intStickerGift) {
            if (empty($strStickerFile) || empty($strStickerFileMd5) || $intStickerDuration < 0 || $intStickerType < 0) {
                Bingo_Log::warning("error params!");
                $this->setErroInfo(Tieba_Errcode::ERR_PARAM_ERROR, '贴纸礼物配置参数错误');
                return false;
            }
        }
        return Bingo_Encode::convert($arrParam, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
    }

    /**
     * @param int $errno
     * @param string $errmsg
     * @param array $data
     *
     * @return bool
     */
    public function setErroInfo($errno = -1, $errmsg = '', $data = array())
    {
        $intErrno = $errno;
        $strError = $errmsg;
        if ($errno != 0) {
            Bingo_Log::warning('no:' . $intErrno . ' errmsg:' . $strError . ' data:' . serialize($data));
        } else {
            Bingo_Log::debug('no:' . $intErrno . ' errmsg:' . $strError . ' data:' . serialize($data));
        }
        Bingo_Page::assign('no', $intErrno);
        Bingo_Page::assign('errMsg', $strError);
        Bingo_Page::assign('data', $data);
        Bingo_Page::setOnlyDataType("json");
        return true;
    }

}

?>
