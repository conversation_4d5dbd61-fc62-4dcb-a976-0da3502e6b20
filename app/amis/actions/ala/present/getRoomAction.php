<?php
/**
 * Created by PhpStorm.
 * User: weiyan02
 * Date: 2020/8/5
 * Time: 下午1:52
 */

class getRoomAction extends Util_Action
{
    protected $strAmisGroup = '';
    protected $strAmisPerm = '';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;
    protected $bolNeedLogin = false;

    /**
     * @return bool
     */
    public function _execute()
    {

        $scene_id = intval(Bingo_Http_Request::get('scene_id', 0));
        if(empty($scene_id)){
            $this->setErroInfo(Tieba_Errcode::ERR_PARAM_ERROR, 'param is wrong');
            return false;
        }
        $arrInput = array(
            'scene_id' => $scene_id,
        );
        $arrOut   = Tieba_Service::call('present', 'getRoomBySceneId', $arrInput, null, null, 'post', 'php', 'utf-8');
        $intErrNo = (isset($arrOut['errno'])) ? $arrOut['errno'] : -1;
        if(empty($arrOut) || Tieba_Errcode::ERR_SUCCESS != $intErrNo){
            $this->setErroInfo($intErrNo, 'Tieba_Service_call_present_error['.serialize($arrOut).']');
            return false;
        }
        $this->setErroInfo($intErrNo, 'success', $arrOut['data']);
        return true;
    }

    /**
     * @param int $errno
     * @param string $errmsg
     * @param array $data
     *
     * @return bool
     */
    public function setErroInfo($errno = -1, $errmsg = '', $data = array())
    {
        $intErrno = $errno;
        $strError = $errmsg;
        if($errno != 0){
            Bingo_Log::warning('no:'.$intErrno.' errmsg:'.$strError.' data:'.serialize($data));
        }else{
            Bingo_Log::debug('no:'.$intErrno.' errmsg:'.$strError.' data:'.serialize($data));
        }
        Bingo_Page::assign('no', $intErrno);
        Bingo_Page::assign('errMsg', $strError);
        Bingo_Page::assign('data', $data);
        Bingo_Page::setOnlyDataType("json");
        return true;
    }
}

?>