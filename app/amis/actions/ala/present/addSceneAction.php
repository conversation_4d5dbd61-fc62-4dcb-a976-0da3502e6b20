<?php

class addSceneAction extends Util_Action
{
	protected $strAmisGroup = '';
	
	protected $strAmisPerm = '';
	
	protected $bolOnlyAccessAmis = false;
	
	protected $bolOnlyInner = false;
	
	protected $bolNeedLogin = false;
	
	/**
	 * @return bool
	 */
	public function _execute()
	{
		$arrInput = $this->_initParam();
		if(false == $arrInput){
			return false;
		}
		$arrOut   = Tieba_Service::call('present', 'addScene', $arrInput, null, null, 'post', 'php', 'utf-8');
		$intErrNo = (isset($arrOut['errno'])) ? $arrOut['errno'] : -1;
		if(empty($arrOut) || Tieba_Errcode::ERR_SUCCESS != $intErrNo){
			$this->setErroInfo($intErrNo, 'Tieba_Service_call_present_error['.serialize($arrOut).']');
			return false;
		}
		$this->setErroInfo($intErrNo, 'success', $arrOut['data']);
		return true;
	}
	
	/**
	 * @return array|bool
	 */
	private function _initParam()
	{
		$subapp_type = trim(strval(Bingo_Http_Request::get('subapp_type', 'tieba')));
		$scene_name  = trim(strval(Bingo_Http_Request::get('scene_name', '')));
		$scene_id    = intval(Bingo_Http_Request::get('scene_id', 0));
		$scene_desc  = trim(strval(Bingo_Http_Request::get('scene_desc', '')));
		$proportion  = intval(Bingo_Http_Request::get('proportion', 0));
		$currency    = intval(Bingo_Http_Request::get('currency', 0));
		$callback    = trim(strval(Bingo_Http_Request::get('callback', '')));
		$addfree_url = trim(strval(Bingo_Http_Request::get('addfree_url', '')));
		$arrParam    = array(
			'scene_id'    => $scene_id,
			'scene_name'  => $scene_name,
			'scene_desc'  => $scene_desc,
			'proportion'  => $proportion,
			'currency'    => $currency,
			'callback'    => $callback,
			'addfree_url' => $addfree_url,
			'subapp_type' => $subapp_type,
		);
		if(empty($scene_name) || empty($scene_id) || empty($scene_desc)){
			$this->setErroInfo(Tieba_Errcode::ERR_PARAM_ERROR, 'param is wrong', $arrParam);
			return false;
		}
		$arrParam['free_num'] = intval(Bingo_Http_Request::get('free_num', 0));
		if(0 < $arrParam['free_num']){
			$arrParam['has_free'] = 1;
		}
		return Bingo_Encode::convert($arrParam, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
	}
	
	/**
	 * @param int $errno
	 * @param string $errmsg
	 * @param array $data
	 *
	 * @return bool
	 */
	public function setErroInfo($errno = -1, $errmsg = '', $data = array())
	{
		$intErrno = $errno;
		$strError = $errmsg;
		if($errno != 0){
			Bingo_Log::warning('no:'.$intErrno.' errmsg:'.$strError.' data:'.serialize($data));
		}else{
			Bingo_Log::debug('no:'.$intErrno.' errmsg:'.$strError.' data:'.serialize($data));
		}
		Bingo_Page::assign('no', $intErrno);
		Bingo_Page::assign('errMsg', $strError);
		Bingo_Page::assign('data', $data);
		Bingo_Page::setOnlyDataType("json");
		return true;
	}
}

?>
