<?php

class modifyGiftListOrderAction extends Util_Action
{
    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    /**
     * @return bool
     * @throws Exception
     */
    public function _execute()
    {
        $orderArr = Bingo_Http_Request::get('order', '');
        $categoryID = intval(Bingo_Http_Request::get('category_id', 0));
        $anchorCategory = Bingo_Http_Request::get('anchor_category', '');

        $input = array(
            'order'           => $orderArr,
            'category_id'     => $categoryID,
            'anchor_category' => $anchorCategory,
        );
        $output = Tieba_Service::call('present', 'modifyGiftListOrder', $input, null, null, 'post', 'php', 'utf-8');

        $errno = (isset($output['errno'])) ? $output['errno'] : -1;
        if (empty($output) || Tieba_Errcode::ERR_SUCCESS != $errno) {
            $this->setErroInfo($errno, 'Tieba_Service_call_present_error[' . json_encode($output) . ']');
            return false;
        }

        $this->setErroInfo($errno, 'success', $output['data']);
        return true;
    }

    /**
     * @param int $errno
     * @param string $errmsg
     * @param array $data
     *
     * @return bool
     */
    public function setErroInfo($errno = -1, $errmsg = '', $data = array())
    {
        if ($errno != 0) {
            Bingo_Log::warning('no:' . $errno . ' errmsg:' . $errmsg . ' data:' . serialize($data));
        } else {
            Bingo_Log::debug('no:' . $errno . ' errmsg:' . $errmsg . ' data:' . serialize($data));
        }
        Bingo_Page::assign('no', $errno);
        Bingo_Page::assign('errMsg', $errmsg);
        Bingo_Page::assign('data', $data);
        Bingo_Page::setOnlyDataType("json");
        return true;
    }

}
