<?php

/**
 * Created by PhpStorm.
 * User: caowu
 * Date: 17/12/12
 * Time: 下午7:31
 */
class uploadFileAction extends Util_Action
{
	protected $strAmisGroup = '';
	
	protected $strAmisPerm = '';
	
	protected $bolOnlyAccessAmis = false;
	
	protected $bolOnlyInner = false;
	
	protected $bolNeedLogin = false;
	
	static $OSSP_PATH = '';
	static $OSSP_PATH_ONLINE_PRE = "/tb/tieba/ala_live/";
	static $OSSP_PATH_OFFLINE_PRE = "/tb/tieba/ala_live/offline/";
	static $strFileUrl = '';
	static $strFileMd5 = '';
	
	/**
	 * @return bool
	 */
	public function _execute()
	{
		$strChildDirectory = trim(strval(Bingo_Http_Request::get('child_directory', 'temp')));
		$arrServer         = Bingo_Http_Request::getServer();
		$strHost           = strval($arrServer['HTTP_HOST']);
		if(false === strpos($strHost, 'tieba.baidu.com')){   //线下
			self::$OSSP_PATH = self::$OSSP_PATH_OFFLINE_PRE.$strChildDirectory;
		}else{     //线上
			self::$OSSP_PATH = self::$OSSP_PATH_ONLINE_PRE.$strChildDirectory;
		}
		$strFileName = $_FILES['file']['name'];
		$strFilePath = $_FILES['file']['tmp_name'];
		if(empty($strFileName) || empty($strFilePath)){
			Bingo_Log::warning("upload file error!!");
			$this->setErroInfo(Tieba_Errcode::ERR_PARAM_ERROR, 'upload file error!');
			return false;
		}
		$boolUploadFileSuccess = $this->uploadOssp($strFilePath, $strFileName);
		if(false === $boolUploadFileSuccess){
			Bingo_Log::warning("upload file to ossp error!!");
			$this->setErroInfo(Tieba_Errcode::ERR_PARAM_ERROR, 'upload file to ossp error!');
			return false;
		}
		$arrRet = array(
			'filename' => $strFileName,
			'url'      => self::$strFileUrl,
			'value'    => self::$strFileUrl,
			'md5'      => self::$strFileMd5,
		);
		//Bingo_Log::warning("====>" . var_export($arrRet, true));  //for test
		$this->setErroInfo(Tieba_Errcode::ERR_SUCCESS, 'success', $arrRet);
		return true;
	}
	
	/**
	 * @param $filePath
	 * @param $strFileName
	 *
	 * @return bool
	 */
	private function uploadOssp($filePath, $strFileName)
	{
		if(!file_exists($filePath)){
			Bingo_Log::warning("file  tmp_name[$filePath], not exists");
			return false;
		}
		$fd          = fopen($filePath, "rb");
		$fileContent = fread($fd, filesize($filePath));
		fclose($fd);
		self::$strFileMd5 = md5($fileContent);
		$param            = array(
			'pname'    => "tieba", //线上
			'token'    => '5Pm4xjGMUml9OBFk1EsOsJJra765jUYn', //线上
			'path'     => self::$OSSP_PATH, //线上
			//            'pname' => "demo",
			//            'token' => "test",
			//            'path'     => "/",
			'fileName' => $strFileName,    //basename($fileName),
			'md5'      => 0, // 默认为1:文件名添加md5戳;     0:md5文件名字不添加md5戳
		);
		$query_string     = http_build_query($param);
		//$uri = "http://cp01-rdqa-dev411.cp01.baidu.com:8080/operationmenu/api/trans?";
		$uri = "http://ossp.baidu.com/operationmenu/api/trans?"; //线上
		$url = $uri.$query_string;
		//curl
		$header   = array('Content-Type: application/octet-stream');
		$resource = curl_init();
		curl_setopt($resource, CURLOPT_URL, $url);
		curl_setopt($resource, CURLOPT_HTTPHEADER, $header);
		curl_setopt($resource, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($resource, CURLOPT_POST, 1);
		curl_setopt($resource, CURLOPT_POSTFIELDS, $fileContent);

        Bingo_Log::warning("uploadFileInfo Resource " . var_export($resource,true) ."\n");

		$result = json_decode(curl_exec($resource), true);
		Bingo_Log::warning("uploadFileInfo result " . var_export($result,true)."\n");

		curl_close($resource);
		if($result['result'] != 1 && empty($result['data']['url'])){
			Bingo_Log::warning("uploadOssp failed :".var_export()."\n");
			return false;
		}
		self::$strFileUrl = $result['data']['url'];
		return true;
	}
	
	/**
	 * @param int $errno
	 * @param string $errmsg
	 * @param array $data
	 *
	 * @return bool
	 */
	public function setErroInfo($errno = -1, $errmsg = '', $data = array())
	{
		$intErrno = $errno;
		$strError = $errmsg;
		if($errno != 0){
			Bingo_Log::warning('no:'.$intErrno.' errmsg:'.$strError.' data:'.serialize($data));
		}else{
			Bingo_Log::debug('no:'.$intErrno.' errmsg:'.$strError.' data:'.serialize($data));
		}
		Bingo_Page::assign('status', $intErrno);
		Bingo_Page::assign('msg', $strError);
		Bingo_Page::assign('data', $data);
		header("Access-Control-Allow-Origin:http://amis.baidu.com");
		header("Access-Control-Allow-Credentials:true");
		Bingo_Page::setOnlyDataType("json");
		return true;
	}
	
}