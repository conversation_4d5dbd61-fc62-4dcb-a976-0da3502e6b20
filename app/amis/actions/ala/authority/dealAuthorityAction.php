<?php

/**
 * Created by PhpStorm.
 * User: caowu
 * Date: 17/7/17
 * Time: 上午11:16
 */
class dealAuthorityAction extends Util_Action {

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = true;

    /**
     * @return bool
     */
    public function _execute() {
        // 操作人
        $intOwnUserId = Util_User::$intUserId;
        $strOwnUserName = Util_User::$strUserName;
        $strMethod = Bingo_Http_Request::get('method', '');

        //只给代运营显示他管理的公会长
        if('search' == $strMethod) {
            //权限校验  start
            $arrServiceInput = array(
                'user_id' => $intOwnUserId,
                'auth_id' => Lib_Ala_Define_Authority::AUTHORITY_AUTHORITY_MANAGEMENT,
            );
            $arrOutput = Service_Ala_Authority_Authority::canUserAccess($arrServiceInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Authority_Authority::canUserAccess fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, $arrOutput['usermsg']);
            }
            else {
                $boolCanAccess = $arrOutput['data'];
                if(false === $boolCanAccess) {
                    Bingo_Log::warning("user has no authority. [" . serialize($arrServiceInput) . "]");
                    return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "抱歉,您没有权限");
                }
            }
            //权限校验  end


            $strKeyWords = Bingo_Http_Request::get('keywords', '');
            $intSearchType = Bingo_Http_Request::get('search_type', 0);
            $intPn = Bingo_Http_Request::get('page', 1);
            $intPs = Bingo_Http_Request::get('ps', 10);
            $intOffset = ($intPn - 1) * $intPs;

            //角色
            $intRole = Util_Ala_Common::getUserRoleId();
            $cond = '';
            switch ($intRole) {
                case Lib_Ala_Define_Role::ROLE_ADMIN:
                    break;
                case Lib_Ala_Define_Role::ROLE_OPERATOR:
                    $arrOutput = Dl_Ala_Union_Union::getListByCreateUserId($intOwnUserId, 1, Lib_Ala_Define_Role::ROLE_OPERATOR_MANAGE_MAX);
                    if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                        $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getListByCreateUserId fail. input:[]; output:[" . serialize($arrOutput) . "]";
                        Bingo_Log::warning($strLog);
                        return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "服务器出错");
                    }
                    $arrUnionList = $arrOutput['data'];
                    if (empty($arrUnionList)) {
                        return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_SUCCESS, array());
                    }
                    $arrUnionPresident = array();
                    foreach ($arrUnionList as $arrUnionInfo) {
                        $arrUnionPresident[] = $arrUnionInfo['union_user_id'];
                    }
                    $cond = implode(',', $arrUnionPresident);
                    $cond = ' and user_id in ( '. $cond .  ' ) ';
                    break;
                case Lib_Ala_Define_Role::ROLE_UNION_PRESIDENT:
                    return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "抱歉,您没有权限");
                default:
                    break;
            }

            if(!empty($intSearchType)) {
                if(empty($strKeyWords)) {
                    Bingo_Log::warning("error params!");
                    return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "关键字为空");
                }
                $strCondition = '';
                if(1 == $intSearchType) {   //
                    $strCondition = " user_id = " . intval($strKeyWords);
                }
                else if(2 == $intSearchType) {
                    $intSearchUserId = Util_Ala_Common::getUidByNames($strKeyWords);
                    if(false === $intSearchUserId) {
                        return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                    }
                    else if(empty($intSearchUserId)) {
                        return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "用户名不存在");
                    }

                    $strCondition = " user_id = " . $intSearchUserId;
                }
                else if(3 == $intSearchType) {
                    $strCondition = " add_user_id = " . intval($strKeyWords);
                }
                else if(4 == $intSearchType) {
                    $intSearchUserId = Util_Ala_Common::getUidByNames($strKeyWords);
                    if(false === $intSearchUserId) {
                        return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                    }
                    else if(empty($intSearchUserId)) {
                        return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "用户名不存在");
                    }

                    $strCondition = " add_user_id = " . $intSearchUserId;
                }
                else if(5 == $intSearchType) {
                    $strCondition = " phone_number = '" . mysql_escape_string(strval($strKeyWords)) . "'" ;
                }
                else {
                    Bingo_Log::warning("input param error!!!!!");
                    return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR);
                }

                $arrDlInput = array(
                    'offset' => 0,
                    'ps' => 100,
                    'cond' => (empty($cond) ? $strCondition: $strCondition . $cond),
                );

            }

            else {
                $arrDlInput = array(
                    'offset' => $intOffset,
                    'ps' => $intPs,
                    'cond' =>  (empty($cond) ? '1=1': '1=1'. $cond),
                );
            }

            $arrOutput = Dl_Ala_Authority_Authority::search($arrDlInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Authority_Authority::search fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            $arrFinalRet = $arrOutput['data'];

            //获取user_name
            $arrUserIds = array();
            foreach ($arrFinalRet['rows'] as $value) {
                $arrUserIds[] = intval($value['user_id']);
                $arrUserIds[] = intval($value['add_user_id']);
                $arrUserIds[] = intval($value['final_op_user_id']);
            }
            $arrUserIds = array_unique($arrUserIds);

            $arrInput = array(
                'user_id' => $arrUserIds,
            );
            $arrOutput = Tieba_Service::call('user', 'getUnameByUids', $arrInput, null, null, 'post', 'php', 'utf-8');
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call user::getUnameByUids fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $arrIdMapName = array();
            foreach ($arrOutput['output']['unames'] as $value) {
                $arrIdMapName[intval($value['user_id'])] = strval($value['user_name']);
            }

            foreach ($arrFinalRet['rows'] as &$value) {
                $value['user_name'] = strval($arrIdMapName[$value['user_id']]);
                $value['add_user_name'] = strval($arrIdMapName[$value['add_user_id']]);
                $value['final_op_user_name'] = strval($arrIdMapName[$value['final_op_user_id']]);
            }

            return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_SUCCESS, $arrFinalRet);

        }

        else if('add' == $strMethod) {   //添加管理员
            //权限校验  start
            $arrServiceInput = array(
                'user_id' => $intOwnUserId,
                'auth_id' => Lib_Ala_Define_Authority::AUTHORITY_AUTHORITY_MANAGEMENT_ADD_ADMIN,
            );
            $arrOutput = Service_Ala_Authority_Authority::canUserAccess($arrServiceInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Authority_Authority::canUserAccess fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            else {
                $boolCanAccess = $arrOutput['data'];
                if(false === $boolCanAccess) {
                    Bingo_Log::warning("user has no authority. [" . serialize($arrServiceInput) . "]");
                    return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "抱歉,您没有权限😝");
                }
            }
            //权限校验  end

            $strUserName = strval(Bingo_Http_Request::get('user_name', ''));
            $strPhoneNumber = strval(Bingo_Http_Request::get('phone_number', ''));
            $intRole = Util_Ala_Common::getUserRoleId();
            switch ($intRole) {
                case Lib_Ala_Define_Role::ROLE_ADMIN:
                    break;
                case Lib_Ala_Define_Role::ROLE_OPERATOR:
                    return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "抱歉,您没有权限");
                case Lib_Ala_Define_Role::ROLE_UNION_PRESIDENT:
                    return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "抱歉,您没有权限");
                default:
                    return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "抱歉,您没有权限");
            }

            if(empty($strUserName)) {
                Bingo_Log::warning("error params!");
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $intUserId = Util_Ala_Common::getUidByNames($strUserName);
            if(false === $intUserId) {
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            else if(empty($intUserId)) {
                return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "用户名不存在");
            }

            $arrDlInput = array(
                'offset' => 0,
                'ps' => 100,
                'cond' => 'user_id = ' . $intUserId,
            );

            $arrOutput = Dl_Ala_Authority_Authority::search($arrDlInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Authority_Authority::search fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            if(!empty($arrOutput['data']['rows'])) {
                return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "用户id[$intUserId]从前已被添加进来了");
            }

            $addRet = Util_Ala_Common::addExternalUser($strUserName, Lib_Ala_Define_Role::$arrAmisRoleId[Lib_Ala_Define_Role::ROLE_ADMIN]);
            if(false === $addRet || 0 != $addRet['status']) {
                Bingo_Log::warning("addExternalUser failed! user_name = $strUserName");
                return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "添加失败,请重试");
            }

            $arrDlInput = array(
                'user_id' => $intUserId,
                'phone_number' => $strPhoneNumber,
                'authority' => '',
                'add_user_id' => $intOwnUserId,
                'add_time' => time(),
                'final_op_user_id'  => $intOwnUserId,
                'final_op_time' => time(),
                'role_id' => Lib_Ala_Define_Role::ROLE_ADMIN,
            );

            $arrOutput = Dl_Ala_Authority_Authority::add($arrDlInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Authority_Authority::add fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            Service_Ala_Log_Op::add("新增管理员, 用户id:$intUserId");

            return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_SUCCESS);

        }else if('addPushCheck' == $strMethod){

            $strUserName = strval(Bingo_Http_Request::get('user_name', ''));
            $strPhoneNumber = strval(Bingo_Http_Request::get('phone_number', ''));


            //权限校验  start
            $arrServiceInput = array(
                'user_id' => $intOwnUserId,
                'auth_id' => Lib_Ala_Define_Authority::AUTHORITY_AUTHORITY_MANAGEMENT_ADD_ADMIN,
            );
            $arrOutput = Service_Ala_Authority_Authority::canUserAccess($arrServiceInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Authority_Authority::canUserAccess fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            else {
                $boolCanAccess = $arrOutput['data'];
                if(false === $boolCanAccess) {
                    Bingo_Log::warning("user has no authority. [" . serialize($arrServiceInput) . "]");
                    return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "抱歉,您没有权限😝");
                }
            }
            //权限校验  end

            $intRole = Util_Ala_Common::getUserRoleId();
            switch ($intRole) {
                case Lib_Ala_Define_Role::ROLE_ADMIN:
                    break;
                case Lib_Ala_Define_Role::ROLE_OPERATOR:
                    return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "抱歉,您没有权限");
                case Lib_Ala_Define_Role::ROLE_UNION_PRESIDENT:
                    return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "抱歉,您没有权限");
                default:
                    return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "抱歉,您没有权限");
            }


            if(empty($strUserName)) {
                Bingo_Log::warning("error params!");
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $intUserId = Util_Ala_Common::getUidByNames($strUserName);

            if(false === $intUserId) {
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

        else if(empty($intUserId)) {
                return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "用户名不存在");
            }

            $arrDlInput = array(
                'offset' => 0,
                'ps' => 100,
                'cond' => 'user_id = ' . $intUserId,
            );

            $arrOutput = Dl_Ala_Authority_Authority::search($arrDlInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Authority_Authority::search fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            if(!empty($arrOutput['data']['rows'])) {
                return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "用户id[$intUserId]从前已被添加进来了");
            }
            $addRet = Util_Ala_Common::addExternalUser($strUserName, Lib_Ala_Define_Role::$arrAmisRoleId[Lib_Ala_Define_Role::ROLE_CHECK_PUSH_SHOUBAI]);
            if(false === $addRet || 0 != $addRet['status']) {
                Bingo_Log::warning("addExternalUser failed! user_name = $strUserName");
                return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "添加失败,请重试");
            }

            $arrDlInput = array(
                'user_id' => $intUserId,
                'phone_number' => $strPhoneNumber,
                'authority' => '',
                'add_user_id' => $intOwnUserId,
                'add_time' => time(),
                'final_op_user_id'  => $intOwnUserId,
                'final_op_time' => time(),
                'role_id' => Lib_Ala_Define_Role::ROLE_CHECK_PUSH_SHOUBAI,
            );

            $arrOutput = Dl_Ala_Authority_Authority::add($arrDlInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Authority_Authority::add fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            Service_Ala_Log_Op::add("新增手百推送人员, 用户id:$intUserId");

            return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_SUCCESS);
        }

        else if ('update' == $strMethod) {

            //权限校验  start
            $arrServiceInput = array(
                'user_id' => $intOwnUserId,
                'auth_id' => Lib_Ala_Define_Authority::AUTHORITY_AUTHORITY_MANAGEMENT_UPDATE,
            );
            $arrOutput = Service_Ala_Authority_Authority::canUserAccess($arrServiceInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Authority_Authority::canUserAccess fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            else {
                $boolCanAccess = $arrOutput['data'];
                if(false === $boolCanAccess) {
                    Bingo_Log::warning("user has no authority. [" . serialize($arrServiceInput) . "]");
                    return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "抱歉,您没有权限😝");
                }
            }
            //权限校验  end

            $intUserId = intval(Bingo_Http_Request::get('user_id', ''));
            $strPhoneNumber = strval(Bingo_Http_Request::get('phone_number', ''));
            $strAuthority = strval(Bingo_Http_Request::get('authority', ''));
            $intRoleId = intval(Bingo_Http_Request::get('role_id', 0));

            if(empty($intUserId)) {
                Bingo_Log::warning("error params!");
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }

            $intRole = Util_Ala_Common::getUserRoleId();
            switch ($intRole) {
                case Lib_Ala_Define_Role::ROLE_ADMIN:
                    break;
                case Lib_Ala_Define_Role::ROLE_OPERATOR:
                    if ($arrRet = Util_Ala_Perm::checkAuthPermFailed($intUserId)) {
                        return Util_Ala_Common::jsonAmisRetMsg($arrRet['no'], $arrRet['error'], $arrRet['data']);
                    }
                    break;
                case Lib_Ala_Define_Role::ROLE_UNION_PRESIDENT:
                    return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "抱歉,您没有权限");
                default:
                    return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "抱歉,您没有权限");
            }

            $arrDlInput = array(
                'user_id' => $intUserId,
                'phone_number' => $strPhoneNumber,
                'authority' => $strAuthority,
                'final_op_user_id'  => $intOwnUserId,
                'final_op_time' => time(),
                'role_id' => $intRoleId,
            );

            $arrOutput = Dl_Ala_Authority_Authority::update($arrDlInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Authority_Authority::update fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            Service_Ala_Log_Op::add("修改权限, 用户id:$intUserId, 禁用权限串[$strAuthority]");

            return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_SUCCESS);
        }

        else if ('delete' == $strMethod) {
            //权限校验  start
            $arrServiceInput = array(
                'user_id' => $intOwnUserId,
                'auth_id' => Lib_Ala_Define_Authority::AUTHORITY_AUTHORITY_MANAGEMENT_DELETE,
            );
            $arrOutput = Service_Ala_Authority_Authority::canUserAccess($arrServiceInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Authority_Authority::canUserAccess fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            else {
                $boolCanAccess = $arrOutput['data'];
                if(false === $boolCanAccess) {
                    Bingo_Log::warning("user has no authority. [" . serialize($arrServiceInput) . "]");
                    return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "抱歉,您没有权限😝");
                }
            }
            //权限校验  end

            $intUserId = intval(Bingo_Http_Request::get('user_id', ''));
            $intInputRoleId = intval(Bingo_Http_Request::get('role_id', 0));

            $intRole = Util_Ala_Common::getUserRoleId();
            switch ($intRole) {
                case Lib_Ala_Define_Role::ROLE_ADMIN:
                    break;
                case Lib_Ala_Define_Role::ROLE_OPERATOR:
                    if ($arrRet = Util_Ala_Perm::checkAuthPermFailed($intUserId)) {
                        return Util_Ala_Common::jsonAmisRetMsg($arrRet['no'], $arrRet['error'], $arrRet['data']);
                    }
                    break;
                case Lib_Ala_Define_Role::ROLE_UNION_PRESIDENT:
                    return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "抱歉,您没有权限");
                default:
                    return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "抱歉,您没有权限");
            }

            $arrDlInput = array(
                'user_id' => $intUserId,
            );
            $arrOutput = Dl_Ala_Authority_Authority::delete($arrDlInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Authority_Authority::delete fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            if(Lib_Ala_Define_Role::ROLE_ADMIN == $intInputRoleId) {
                Service_Ala_Log_Op::add("删除管理员, 用户id:$intUserId");
            }
            else if(Lib_Ala_Define_Role::ROLE_OPERATOR == $intInputRoleId) {
                $arrDlInput = array(
                    'user_id' => $intUserId,
                );
                $arrOutput = Dl_Ala_Authority_Authority::deleteCooperation($arrDlInput);
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Authority_Authority::deleteCooperation fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                    return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }

                $arrOutput = Dl_Ala_Union_Union::getListByCreateUserId($intUserId, 1, Lib_Ala_Define_Role::ROLE_OPERATOR_MANAGE_MAX);
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getListByCreateUserId fail. input:[]; output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                    return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
                $arrUnionList = $arrOutput['data'];
                if(!empty($arrUnionList)) {
                    foreach ($arrUnionList as $arrUnionInfo) {
                        $intTempUnionId = intval($arrUnionInfo['union_id']);
                        $arrOutput = Dl_Ala_Union_Union::del($intTempUnionId);
                        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::del fail. input:[" . serialize($intTempUnionId) . "]; output:[" . serialize($arrOutput) . "]";
                            Bingo_Log::warning($strLog);
                            return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                        }
                    }
                }

                Service_Ala_Log_Op::add("删除合作运营, 用户id:$intUserId");
            }
            else {
                $arrOutput = Dl_Ala_Union_Union::getListByUnionUserId($intUserId, 1, 10);
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getListByUnionUserId fail. input:[]; output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                    return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
                $arrUnionList = $arrOutput['data'];
                $arrUnionId = array();
                foreach ($arrUnionList as $arrUnionInfo) {
                    $arrUnionId[] = $arrUnionInfo['union_id'];
                }

                foreach ($arrUnionId as $intUnionId) {
                    $arrOutput = Dl_Ala_Union_Union::del($intUnionId);
                    if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                        $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::del fail. input:[" . serialize($intUnionId) . "]; output:[" . serialize($arrOutput) . "]";
                        Bingo_Log::warning($strLog);
                        return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                    }
                }

                Service_Ala_Log_Op::add("删除工会长, 用户id:$intUserId");
            }

            Service_Ala_Log_Op::add("删除全部权限, 用户id:$intUserId");
            return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_SUCCESS);
        }

        return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR);


    }


    /**
     * @param $strUserName
     * @return bool|mixed|multitype
     */
    private function addExternalUser($strUserName, $strAssignRoles){
        $intRole = Util_Ala_Common::getUserRoleId();

        $strToken = "token_alam_woshinibaba";
        $url = 'http://amis.baidu.com/api/openApi/user/external/add?' . "users=" . urlencode($strUserName) . "&groupKey=alam&token=" . $strToken. "&roles=". strval($strAssignRoles);
        $ch = curl_init();
        $this_header = array(
            "content-type: application/x-www-form-urlencoded; charset=UTF-8",
        );

        $curlPost='AMIS_GROUP_KEY=alam&AMIS_TOKEN=token_alam_woshinibaba';

        curl_setopt($ch, CURLOPT_HTTPHEADER, $this_header);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $curlPost);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_VERBOSE, true);

        $result = curl_exec($ch);
        curl_close($ch);

        if(false === $result || empty($result)) {
            Bingo_Log::warning("addExternalUser fail");
            return false;
        }
        $arrResult = Bingo_String::json2array($result);
        Bingo_Log::warning("curl res ====>" . var_export($arrResult, true)); // for test
        return $arrResult;
    }

}
