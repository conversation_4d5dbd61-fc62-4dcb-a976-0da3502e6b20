<?php
/**
 * Created by PhpStorm.
 * User: kangqinmou
 * Date: 17-8-31
 * Time: 下午3:57
 */



class addAnchorTailAction extends Util_Action{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;


    /**
     * 执行函数
     * @param  null
     * @return array
     */
    public function _execute(){
        // 操作人
        $intUserId = Util_User::$intUserId;
        $strUserName = Util_User::$strUserName;
        Bingo_Log::warning("用户user_id{$intUserId}, 用户名{$strUserName}");

        // 权限
        $arrInput = array(
            'user_id' => $intUserId,
            'auth_id' => Lib_Ala_Define_Authority::AUTHORITY_LIVE_MANAGEMENT_LIVE_YUNYING_MANAGEMENT_SHOW_ANCHOR_TYPE_SET,
        );
        $arrOutput = Service_Ala_Authority_Authority::canUserAccess($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Authority_Authority::canUserAccess input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::jsonAmisRet(422, array('errors' => array('user_id' => '服务器开小差了')), $arrOutput['usermsg']);
        }
        $bolCanAccess = $arrOutput['data'];
        if ($bolCanAccess === false) {
            return self::jsonAmisRet(422, array('errors' => array('user_id' => '无权限')), '无权限');
        }

        // 长尾id
        $intTailId = (int) Bingo_Http_Request::get('tail_id', 0);
        $strError = '';
        if($intTailId <= 0){
            $strError .= '主播分类为必填项；';
        }

        // 主播ids (ps: 低版本的php不支持匿名函数)
        $arrUserIds = explode(';', str_replace('；', ';', Bingo_Http_Request::get('user_id', '')));
        foreach($arrUserIds as &$strUserId){
            $strUserId = intval($strUserId);
        }
        unset($strUserId);
        $arrUserIds = array_unique(array_filter($arrUserIds));
        if(!$arrUserIds){
            $strError .= '主播ids为必填项；';
        }elseif(count($arrUserIds) > 999){
            $strError .= '已填写超量，请先进行提交后再继续添加；';
        }

        // 如果输入有误，则直接返回
        if($strError){
            return self::jsonAmisRet(422, array('errors' => array('user_id' => $strError)), $strError);
        }

        // 调用ala addAnchorTail service为主播设置分类
        $arrInput = array(
            'user_ids'     => $arrUserIds,      // 若干主播id
            'tail_id'      => $intTailId,       // 长尾id
            'operator'     => $strUserName,     // 当前登录用户
        );
        $arrOutput = Tieba_Service::call('ala', 'addAnchorTail', $arrInput, null, null, 'get', null, 'utf-8');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']){
            $strLog = __CLASS__.'.'.__FUNCTION__.'call ala addAnchorTail service fail. input: ['.serialize($arrInput).']; output: ['.serialize($arrOutput).']';
            Bingo_Log::fatal($strLog);
            return self::jsonAmisRet(422, array('errors' => array('user_id' => $arrOutput['usermsg'])), $arrOutput['usermsg']);
        }
        return self::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput, $arrOutput['usermsg']);
    }



    /**
     * @param $intErrno
     * @param array $arrExtData
     * @param string $strMsg
     * @return bool
     */
    public static function jsonAmisRet($intErrno, $arrExtData = array(), $strMsg = ''){
        $strMsg = $strMsg ? $strMsg : Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput = array(
            'status' => $intErrno,
            'msg'    => $strMsg,
            'data'   => $arrExtData,
        );
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrOutput, Bingo_Encode::ENCODE_UTF8);
        return true;
    }

}
