<?php
/**
 * Created by PhpStorm.
 * User: kangqinmou
 * Date: 17-9-29
 * Time: 下午7:51
 */




class deleteTailForUserAction extends Util_Action{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    /**
     * 执行函数
     * @param  null
     * @return array
     */
    public function _execute(){
        // 操作人
        $intUserId = Util_User::$intUserId;
        $strUserName = Util_User::$strUserName;
        Bingo_Log::warning("用户user_id{$intUserId}, 用户名{$strUserName}");

        // 权限
        $arrInput = array(
            'user_id' => $intUserId,
            'auth_id' => Lib_Ala_Define_Authority::AUTHORITY_LIVE_MANAGEMENT_LIVE_YUNYING_MANAGEMENT_SHOW_ANCHOR_TYPE_SET,
        );
        $arrOutput = Service_Ala_Authority_Authority::canUserAccess($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Authority_Authority::canUserAccess input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), $arrOutput['usermsg']);
        }
        $bolCanAccess = $arrOutput['data'];
        if ($bolCanAccess === false) {
            return self::jsonAmisRet(Tieba_Errcode::ERR_ACTION_FORBIDDEN, '无权限');
        }

        // 调用ala deleteTailForUser service，获取所有长尾
        $arrInput = array(
            'user_id'     => (int) Bingo_Http_Request::get('user_id', 0),               // 主播id
            'id'          => (int) Bingo_Http_Request::get('id', 0),                    // mis_live_tail_detail的自增id
            'operator'    => $strUserName,                                              // 当前登录用户
        );
        $arrOutput = Tieba_Service::call('ala', 'deleteTailForUser', $arrInput, null, null, 'get', null, 'utf-8');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']){
            $strLog = __CLASS__.'.'.__FUNCTION__.'call ala deleteTailForUser service fail. input: ['.serialize($arrInput).']; output: ['.serialize($arrOutput).']';
            Bingo_Log::fatal($strLog);
            return self::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), $arrOutput['usermsg']);
        }
        return self::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, array(), $arrOutput['usermsg']);
    }


    /**
     * @param $intErrno
     * @param array $arrExtData
     * @param string $strMsg
     * @return bool
     */
    public static function jsonAmisRet($intErrno, $arrExtData = array(), $strMsg = ''){
        $strMsg = $strMsg ? $strMsg : Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput = array(
            'status' => $intErrno,
            'msg'    => $strMsg,
            'data'   => $arrExtData,
        );
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrOutput, Bingo_Encode::ENCODE_UTF8);
        return true;
    }
}
