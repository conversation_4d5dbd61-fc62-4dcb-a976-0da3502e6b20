<?php
/**
 * Created by PhpStorm.
 * 主播管理后台-底薪主播页
 * User: weiyan02
 * Date: 2019/4/17
 * Time: 上午10:23
 */

class basicSalaryAnchorAction extends Util_Action
{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    /**
     * @return array
     */
    public function _execute()
    {
        // 操作人
        $intUserId = Util_User::$intUserId;
        $strUserName = Util_User::$strUserName;

        $bolFixNoJournalAnchorFlag = false;
        $arrAnchorJournalIds = array();
        $arrAnchorUserIds = array();
        $arrResList = array();


        //主播详细信息
        $strDetailDataOfAnchor = (string)Bingo_HTTP_Request::get('detail_data_anchor', '');
        $intDetailAnchorId = (int)Bingo_HTTP_Request::get('detail_anchor_id', 0);

        if ('detailDataOfAnchor' == $strDetailDataOfAnchor) {
            $arrResParam = array(
                'user_id' => $intDetailAnchorId,
            );
            $arrAnchorUserIds = array($intDetailAnchorId);
        } else {
            $arrResParam = self::retrievalLoginDeal();
            if (isset($arrResParam['msg'])) {
                if (!empty($arrResParam['msg'])) {
                    return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, $arrResParam['msg']);
                }
            }
            if (isset($arrResParam['union_ids'])) {
                if (empty($arrResParam['union_ids'])) {
                    return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, '无检索数据!');
                }
            }

        }

        //没有筛选条件时补充无流水主播信息
        if (isset($arrResParam['is_select'])) {
            if (!$arrResParam['is_select']) {
                $bolFixNoJournalAnchorFlag = true;
                //获取总主播id(有/无流水)
                if (isset($arrResParam['user_ids'])) {
                    if (!empty($arrResParam['user_ids'])) {
                        $arrAnchorUserIds = $arrResParam['user_ids'];
                    }
                } else {
                    //非管理员
                    $arrUserIds = array();
                    $arrUnionId = $arrResParam['union_ids'];
                    $arrInput = array(
                        'union_ids' => implode(',', $arrUnionId),
                    );
                    $arrReturnData = Dl_Ala_Union_Anchor::getListByTime($arrInput, 1, Lib_Ala_Define_Role::ROLE_OPERATOR_MANAGE_MAX * Lib_Ala_Define_Role::ROLE_TEAM_LEADER_MANAGE_MAX);
                    $arrReturnList = $arrReturnData['data'];
                    foreach ($arrReturnList as $arrDetail) {
                        $arrUserIds[] = $arrDetail['anchor_user_id'];
                    }
                    $arrAnchorUserIds = $arrUserIds;

                }

            }
        }
        //获取主播月流水信息
        $arrAnchorJournalDealResData = self::getAnchorJournalInfo($arrResParam);
        $arrAnchorJournalDealRes = $arrAnchorJournalDealResData['data'];

        $arrAnchorJournalDealResCount = $arrAnchorJournalDealResData['count'];

        //详细主播数据中不显示主播工会，合作运营信息
        if ('detailDataOfAnchor' == $strDetailDataOfAnchor) {
            $arrAnchorUserInfo = array();
        } else {
            //管理员下无全部主播id
            if (empty($arrAnchorUserIds)) {
                foreach ($arrAnchorJournalDealRes as $arrAnchorJournalDealResTmp) {
                    $arrAnchorUserIds[] = $arrAnchorJournalDealResTmp['user_id'];
                }
            }
            $arrAnchorUserIds = array_unique($arrAnchorUserIds);

            $arrAnchorUserParam = array(
                'user_ids' => $arrAnchorUserIds,
            );

            $arrAnchorUserInfo = self::getAnchorUserInfo($arrAnchorUserParam);

        }


        if (0 != $arrAnchorJournalDealResCount) {
            $arrAnchorGiftParam = array(
                'user_ids' => $arrAnchorUserIds,
            );
            $arrMonthDate = array();
            $arrMonthDateDuration = array();
            $arrUsersReqForDuration = array();
            //获取主播天直播时长信息
            $arrUserIdsForDuration = array_unique($arrAnchorUserIds);
            $offsetForDuration = 0;
            for ($i = 0; $i < (count($arrUserIdsForDuration) / 100); $i++) {
                $arrUsersReqForDuration[] = array_slice($arrUserIdsForDuration, $offsetForDuration, 100);
                $offsetForDuration = $offsetForDuration + 100;
            }
            foreach ($arrUsersReqForDuration as $index => $item) {
                $arrAnchorGiftParamForDuration = array(
                    'user_ids' => $item,
                );
                $arrAnchorJournalDayDurationInfo = self::getAnchorDurationOfDay($arrAnchorGiftParamForDuration);
                $arrAnchorJournalDayDurationList = $arrAnchorJournalDayDurationInfo['data'];
                foreach ($arrAnchorJournalDayDurationList as $arrAnchorJournalDayDuration) {
                    $intAnchorId = $arrAnchorJournalDayDuration['user_id'];
                    $intMonth = $arrAnchorJournalDayDuration['month'];
                    $arrMonthDateDuration[$intMonth][$intAnchorId]['effective_day'] = $arrAnchorJournalDayDuration['effective_day'];
                }
                unset($arrAnchorJournalDayDurationInfo);
            }

            $arrAnchorList = $arrAnchorJournalDealRes;
            // 按月整理流水数据
            $arrJournalAnchorIds = array();
            $arrJournalMonths = array();
            $intSmallestMonth = date('Ym', time());
            foreach ($arrAnchorList as $arrAnchor) {
                $intAnchorId = $arrAnchor['user_id'];
                $intMonth = $arrAnchor['month'];
                if (!isset($arrMonthDate[$intMonth][$intAnchorId])) {
                    $arrMonthDate[$intMonth][$intAnchorId]['income_td'] = 0;
                    $arrMonthDate[$intMonth][$intAnchorId]['prop_count'] = 0;
                    $arrMonthDate[$intMonth][$intAnchorId]['prop_tdou'] = 0;
                    $arrMonthDate[$intMonth][$intAnchorId]['live_duration'] = 0;
                    $arrMonthDate[$intMonth][$intAnchorId]['income_td_total'] = 0;
                }
                $arrMonthDate[$intMonth][$intAnchorId]['prop_count'] += $arrAnchor['prop_count'];
                $arrMonthDate[$intMonth][$intAnchorId]['prop_tdou'] += $arrAnchor['prop_tdou'];
                $arrMonthDate[$intMonth][$intAnchorId]['income_td'] += $arrAnchor['income_td'];
                $arrMonthDate[$intMonth][$intAnchorId]['income_td_total'] += ($arrAnchor['income_td']+$arrAnchor['prop_tdou']);
                $arrMonthDate[$intMonth][$intAnchorId]['live_duration'] += $arrAnchor['live_duration'];
                $arrMonthDate[$intMonth][$intAnchorId]['effective_day'] = $arrMonthDateDuration[$intMonth][$intAnchorId]['effective_day'];
                $intAnchorJournal = $arrMonthDate[$intMonth][$intAnchorId]['income_td'];
                $intEffectDay = $arrMonthDate[$intMonth][$intAnchorId]['effective_day'];
                $intDuration = $arrMonthDate[$intMonth][$intAnchorId]['live_duration'];
                $arrBasicSalaryParam = array(
                    'journal' => $intAnchorJournal,
                    'effect_day' => $intEffectDay,
                    'duration' => $intDuration,
                );
                $arrMonthDate[$intMonth][$intAnchorId]['basic_salary'] = self::getAnchorBasicSalary($arrBasicSalaryParam);

                if ($intMonth < $intSmallestMonth) {
                    $intSmallestMonth = $intMonth;
                }
                $arrJournalAnchorIds[] = $intAnchorId;
                $arrJournalMonths[] = $intMonth;
            }

            // 最后一个月可能取不全天数，所以去掉
            if (count($arrMonthDate) > 12) {
                unset($arrMonthDate[$intSmallestMonth]);
            }

            //结果整理
            foreach ($arrMonthDate as $month => $anchorInfo) {
                foreach ($anchorInfo as $anchorId => $journal) {
                    $arrResListTmp['month'] = $month;
                    $arrResListTmp['user_id'] = $anchorId;
                    $arrResListTmp['user_name'] = $arrAnchorUserInfo[$anchorId]['anchor_user_name'];
//                    $arrResListTmp['union_id'] = $arrAnchorUserInfo[$anchorId]['union_id'];
                    $arrResListTmp['union_name'] = $arrAnchorUserInfo[$anchorId]['union_name'];
//                    $arrResListTmp['create_user_id'] = $arrAnchorUserInfo[$anchorId]['create_user_id'];
                    $arrResListTmp['create_user_name'] = $arrAnchorUserInfo[$anchorId]['create_user_name'];
                    $arrResListTmp['effective_day'] = $journal['effective_day'];
                    $arrResListTmp['live_duration'] = self::Sec2Time($journal['live_duration']);
                    $arrResListTmp['prop_count'] = $journal['prop_count'];
                    $arrResListTmp['prop_tdou'] = $journal['prop_tdou'];
                    $arrResListTmp['income_td_total'] = empty($journal['income_td_total']) ? $journal['income_td'] : $journal['income_td_total'];
                    $arrResListTmp['basic_salary'] = $journal['basic_salary'];
                    $arrResList[] = $arrResListTmp;
                    $arrAnchorJournalIds[] = $anchorId;
                }
            }
            unset($arrMonthDate);
        }

        $strCsv = (string)Bingo_HTTP_Request::get('csv', '');

        //导出数据
        if ('csv' == $strCsv) {
            return self::csv($arrAnchorUserIds, $arrAnchorJournalIds, $arrAnchorUserInfo, $arrResList, $bolFixNoJournalAnchorFlag);
        }

        $intPage = (int)Bingo_HTTP_Request::get('page', 1);
        $intPerPage = (int)Bingo_HTTP_Request::get('perPage', 30);

        //无流水时，正常截断
        if (0 == ceil($arrAnchorJournalDealResCount / $intPerPage) && $bolFixNoJournalAnchorFlag) {
            $arrAnchorJournalIdsEmpty = array();
            //结果数据全为无流水数据，调用函数处理无流水主播数据
            $arrResListInfo = self::getNoJournalAnchorInfo($arrAnchorUserIds, $arrAnchorJournalIdsEmpty, $arrAnchorUserInfo);
            $arrResList = $arrResListInfo['data'];
            $arrResListCount = $arrResListInfo['count'];
            $arrAnchorJournalDealResCount += $arrResListCount;
            $intOffset = ($intPage - 1) * $intPerPage;
            $arrResList = array_slice($arrResList, $intOffset, $intPerPage);
        } //有流水时，最后一页截断
        else if ($intPage >= ceil($arrAnchorJournalDealResCount / $intPerPage) && $bolFixNoJournalAnchorFlag) {
            //获取无流水主播信息
            $arrResListInfo = self::getNoJournalAnchorInfo($arrAnchorUserIds, $arrAnchorJournalIds, $arrAnchorUserInfo);
            $arrResListTmp = $arrResListInfo['data'];
            $arrResListCount = $arrResListInfo['count'];
            $arrAnchorJournalDealResCount += $arrResListCount;
            foreach ($arrResListTmp as $arrResListTmpInfo) {
                $arrResList[] = $arrResListTmpInfo;
            }
            $intOffset = ($intPage - ceil($arrAnchorJournalDealResCount / $intPerPage)) * $intPerPage;
            if (ceil($arrAnchorJournalDealResCount / $intPerPage) == $intPage) {
                $arrResList = array_slice($arrResList, $intOffset, $intPerPage);
            } else {
                $intCurrentPageCount = $arrAnchorJournalDealResCount % $intPerPage;
                $arrResList = array_slice($arrResList, $intOffset - $intCurrentPageCount, $intPerPage);
            }
        }

        if (!empty($arrResList)) {
            foreach ($arrResList as $arrUserListResultSort) {
                $sort[] = $arrUserListResultSort["month"];
            }
            array_multisort($sort, SORT_DESC, $arrResList);

            //排序后原先id变乱重新赋值，用于前段去重  --start
            $intFlagNumShow = ($intPage - 1) * $intPerPage;

            foreach ($arrResList as &$arrRetResultTmp) {
                $intFlagNumShow++;
                $arrRetResultTmp['id'] = $intFlagNumShow;
            }
            //排序后原先id变乱重新赋值，用于前段去重  --end
        }

        $arrRetRes = array(
            'rows' => $arrResList,
            'count' => $arrAnchorJournalDealResCount,
        );
        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrRetRes);

    }

    /**
     * @brief 参数校验，产生主播id或工会id
     * @return array : 0.
     */
    public static function retrievalLoginDeal()
    {

        // 筛选条件
        $intUnionType = (int)Bingo_HTTP_Request::get('s_union_type', 0);
        $intCooperationUserId = (int)Bingo_HTTP_Request::get('cooperation_user_id', 0);
        $intInputUnionId = (int)Bingo_HTTP_Request::get('union_id', 0);
        $intInputUserId = (int)Bingo_HTTP_Request::get('user_id', 0);
        $intFilterStartTime = (int)Bingo_HTTP_Request::get('filter_start_time', 0);
        $intFilterEndTime = (int)Bingo_HTTP_Request::get('filter_end_time', 0);

        $intNowTime = time();
        $firstdayMd = date('Y-m-01', $intNowTime);
        $intMonthNowTime = strtotime(date("Y-m-01", strtotime("$firstdayMd +1 month ")));
        $intFilterStartTime = strtotime(date("Y-m-01", $intFilterStartTime));
        $intFilterEndTime = date("Y-m-01", $intFilterEndTime);
        $intFilterEndTime = strtotime(date("Y-m-d", strtotime("$intFilterEndTime +1 month -1 day")));

        if (($intFilterStartTime > $intMonthNowTime) || ($intFilterEndTime > $intMonthNowTime)) {
            $arrRetParam = array(
                'msg' => '检索日期不能超过本月!',
            );
            return $arrRetParam;
        }
        if (empty($intFilterStartTime) ||
            empty($intFilterEndTime) ||
            !($intFilterEndTime > $intFilterStartTime)) {

            $arrRetParam = array(
                'msg' => '请核对检索日期是否合法',
            );
            return $arrRetParam;
        }

        $intUserId = Util_User::$intUserId;

        $intRole = Util_Ala_Common::getUserRoleId();
        switch ($intRole) {
            case Lib_Ala_Define_Role::ROLE_ADMIN:
                break;
            case Lib_Ala_Define_Role::ROLE_OPERATOR:
                break;
            case Lib_Ala_Define_Role::ROLE_UNION_PRESIDENT:
                break;
            default:
                $arrRetParam = array(
                    'msg' => '无权限',
                );
                return $arrRetParam;
        }

        if (!empty($intInputUserId)) {
            $arrInputAnchor = array(
                'user_ids' => $intInputUserId,
            );
            $arrAnchorInfo = Dl_Ala_Union_Anchor::getAnchorInfo($arrInputAnchor);

            if (empty($arrAnchorInfo['data'])) {
                $arrAnchorInfoNoUnion = Dl_Ala_Union_Anchor::_userGetInfo(array($intInputUserId));
                if(empty($arrAnchorInfoNoUnion[$intInputUserId])) {
                    $arrRetParam = array(
                        'msg' => '主播ID不存在!',
                    );
                    return $arrRetParam;
                }
                //覆盖非工会主播
                $arrAnchorInfo['data'][0]['anchor_user_id'] = $intInputUserId;
            }
            if (!empty($arrAnchorInfo['data'][0]['anchor_user_id'])) {

                if ((Lib_Ala_Define_Role::ROLE_UNION_PRESIDENT == $intRole) && $arrAnchorInfo['data'][0]['audit_user_id'] != $intUserId) {
                    $arrRetParam = array(
                        'msg' => '主播不在当前工会!',
                    );
                    return $arrRetParam;
                }
                if ((Lib_Ala_Define_Role::ROLE_OPERATOR == $intRole) && $arrAnchorInfo['data'][0]['create_user_id'] != $intUserId) {
                    $arrRetParam = array(
                        'msg' => '主播不在当前合作运营!',
                    );
                    return $arrRetParam;
                }
                if ((Lib_Ala_Define_Role::ROLE_OPERATOR == $intRole) || (Lib_Ala_Define_Role::ROLE_ADMIN == $intRole)) {
                    if (!empty($intInputUnionId) && ($arrAnchorInfo['data'][0]['union_id'] != $intInputUnionId)) {
                        $arrRetParam = array(
                            'msg' => '主播不在检索工会!',
                        );
                        return $arrRetParam;
                    }
                    if (!empty($intUnionType) && ($arrAnchorInfo['data'][0]['union_type'] != $intUnionType)) {
                        $arrRetParam = array(
                            'msg' => '主播所属工会不在检索工会类型!',
                        );
                        return $arrRetParam;
                    }
                }
                if (Lib_Ala_Define_Role::ROLE_ADMIN == $intRole) {
                    if (!empty($intCooperationUserId) && ($arrAnchorInfo['data'][0]['create_user_id'] != $intCooperationUserId)) {
                        $arrRetParam = array(
                            'msg' => '主播不在检索合作运营!',
                        );
                        return $arrRetParam;
                    }
                }
                $arrRetParam = array(
                    'user_id' => $intInputUserId,
                );
                return $arrRetParam;
            }
        }
        if (!empty($intInputUnionId)) {

            $arrInputAnchor = array(
                'union_id' => $intInputUnionId,
            );
            $arrUnionInfo = Dl_Ala_Union_Anchor::getUnionInfo($arrInputAnchor);
            if ((Lib_Ala_Define_Role::ROLE_OPERATOR == $intRole) && !empty($arrUnionInfo['data'][0]['create_user_id']) && ($arrUnionInfo['data'][0]['create_user_id'] != $intUserId)) {
                $arrRetParam = array(
                    'msg' => '工会不在当前合作运营!',
                );
                return $arrRetParam;
            }
            if (!empty($intUnionType) && ($arrUnionInfo['data'][0]['union_type'] != $intUnionType)) {
                $arrRetParam = array(
                    'msg' => '工会所属类型不在检索工会类型!',
                );
                return $arrRetParam;
            }
            if (Lib_Ala_Define_Role::ROLE_ADMIN == $intRole) {
                if (!empty($intCooperationUserId) && ($arrUnionInfo['data'][0]['create_user_id'] != $intCooperationUserId)) {
                    $arrRetParam = array(
                        'msg' => '工会不在检索合作运营!',
                    );
                    return $arrRetParam;
                }
            }
            $arrRetParam = array(
                'union_id' => $intInputUnionId,
            );
            return $arrRetParam;
        }
        if (!empty($intUnionType)) {
            $arrUnionIds = array();
            if (Lib_Ala_Define_Role::ROLE_OPERATOR == $intRole) {
                $arrInputUnionType = array(
                    'union_type' => $intUnionType,
                    'create_user_id' => $intUserId,
                );
                $arrUnionIds = self::getUnionIdByTypeCor($arrInputUnionType);
            }
            if (Lib_Ala_Define_Role::ROLE_ADMIN == $intRole) {
                if (!empty($intCooperationUserId)) {
                    $arrInputUnionType = array(
                        'union_type' => $intUnionType,
                        'create_user_id' => $intCooperationUserId,
                    );
                    $arrUnionIds = self::getUnionIdByTypeCor($arrInputUnionType);
                } else {
                    $strCond = 'union_type = ' . $intUnionType;
                    $arrOutput = Dl_Ala_Union_Union::getCountFromUnion($strCond);

                    if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                        $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getCountFromUnion fail. input:[]; output:[" . serialize($arrOutput) . "]";
                        Bingo_Log::warning($strLog);
                        $arrRetParam = array(
                            'msg' => '服务器出错!',
                        );
                        return $arrRetParam;
                    }
                    $arrUnionCount = (int)$arrOutput['data']['count'];

                    if ($arrUnionCount > 1000) {
                        $arrRetParam = array(
                            'msg' => '公会数量过多，按公会类型搜索的功能暂停使用，请联系RD进行功能升级!',
                        );
                        return $arrRetParam;
                    }

                    // 无对应类型的公会，直接返回空
                    if (!$arrUnionCount) {
                        $arrRetParam = array(
                            'msg' => '无检索数据!',
                        );
                        return $arrRetParam;
                    }
                    $arrInputUnionType = array(
                        'union_type' => $intUnionType,
                    );
                    $arrUnionIds = self::getUnionIdByTypeCor($arrInputUnionType);

                }
            }
            $arrRetParam = array(
                'union_ids' => $arrUnionIds,
            );
            return $arrRetParam;
        }
        if (!empty($intCooperationUserId)) {
            $arrUnionIds = array();
            $arrOutput = Dl_Ala_Union_Union::getListByCreateUserId($intCooperationUserId, 1, Lib_Ala_Define_Role::ROLE_OPERATOR_MANAGE_MAX);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getListByCreateUserId fail. input:[]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                $arrRetParam = array(
                    'msg' => '服务器出错!',
                );
                return $arrRetParam;
            }

            $arrUnionList = $arrOutput['data'];
            foreach ($arrUnionList as $arrUnionInfo) {
                $arrUnionIds[] = $arrUnionInfo['union_id'];
            }
            $arrRetParam = array(
                'union_ids' => $arrUnionIds,
            );
            return $arrRetParam;
        }
        //以下是无检索条件
        $bolIsSelect = false;
        $arrUnionId = array();
        switch ($intRole) {
            case Lib_Ala_Define_Role::ROLE_ADMIN:

                /*
                //目前支持贴吧侧--由于数据量过大，取白名单中前10000主播，否则接口会超时
                $condition = array();
                $strSubappType = 'tieba';
                $condition[] = "subapp_type ="."\"".$strSubappType."\"";
                $condition []= "subapp_type != '' ";
                $strlimit = " limit 0,1000";

                $strCondition = join(' and ', $condition);
                $condition = array(
                    'con' => $strCondition,
                    'limit' => $strlimit,
                );
//        获取白名单数据
                $arrOutput = Tieba_Service::call('alamis', 'selectAuthenticationWhite', $condition, null, null, "post", "php",'utf-8');
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call alamis selectAuthenticationWhite fail. input:[" . serialize($strCondition) . "]; output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                }
                $arrList = $arrOutput['data'];
                foreach ($arrList as &$item) {
                    $arrUserIds[] = $item['create_time'];
                }
                */

                /*
                //实名认证中的主播
                $arrInput = array(
                    'start_time' => '',
                    'end_time'   => '',
                    'offset'     => 0,
                    'limit'      => 100000000000,
                );

                //改成获取全部数据 被注释--end
                $arrReturnList  = Util_Ala_Service::call('getAuthenticationList', $arrInput);
                $arrUserIds = array();
                foreach ($arrReturnList as $arrDetail) {
                    $arrUserIds[] = $arrDetail['user_id'];
                }
                */

                //这里是直接取流水数据，而不在从白名单/实名认证表中取主播数据[存在数据量过大的问题,接口超时]
                $arrUserIds = array();
                $arrRetParam = array(
                    'user_ids' => $arrUserIds,
                    'is_select' => $bolIsSelect,
                );

                return $arrRetParam;
            case Lib_Ala_Define_Role::ROLE_OPERATOR:
                $arrOutput = Dl_Ala_Union_Union::getListByCreateUserId($intUserId, 1, Lib_Ala_Define_Role::ROLE_OPERATOR_MANAGE_MAX);
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getListByCreateUserId fail. input:[]; output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                    $arrRetParam = array(
                        'msg' => '服务器出错!',
                    );
                    return $arrRetParam;
                }

                $arrUnionList = $arrOutput['data'];
                foreach ($arrUnionList as $arrUnionInfo) {
                    $arrUnionId[] = $arrUnionInfo['union_id'];
                }
                $arrRetParam = array(
                    'union_ids' => $arrUnionId,
                    'is_select' => $bolIsSelect,
                );
                return $arrRetParam;
            case Lib_Ala_Define_Role::ROLE_UNION_PRESIDENT:
                //获取全部数据 --start
                $arrOutputTotal = Dl_Ala_Union_Union::getListByUnionUserId($intUserId, 1, Lib_Ala_Define_Role::ROLE_TEAM_LEADER_MANAGE_MAX);
                if (false === $arrOutputTotal || Tieba_Errcode::ERR_SUCCESS != $arrOutputTotal['errno']) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getListByUnionUserId fail. input:[]; output:[" . serialize($arrOutputTotal) . "]";
                    Bingo_Log::warning($strLog);
                    $arrRetParam = array(
                        'msg' => '服务器出错!',
                    );
                    return $arrRetParam;
                }
                $arrUnionList = $arrOutputTotal['data'];
                //获取全部数据 --end
                foreach ($arrUnionList as $arrUnionInfo) {
                    $arrUnionId[] = $arrUnionInfo['union_id'];
                }
                $arrRetParam = array(
                    'union_ids' => $arrUnionId,
                    'is_select' => $bolIsSelect,
                );
                return $arrRetParam;
            default:
                break;
        }

    }

    /**
     * @brief 获取无流水主播信息
     * @param  $arrAnchorUserIds
     * @param  $arrAnchorJournalIds
     * @param  $arrAnchorUserInfo
     * @return array().
     */
    protected static function getNoJournalAnchorInfo($arrAnchorUserIds, $arrAnchorJournalIds, $arrAnchorUserInfo)
    {
        $arrResList = array();
        $arrAnchorNoJournalCount = 0;
        $arrdiffer = array_diff($arrAnchorUserIds,$arrAnchorJournalIds);
        foreach ($arrdiffer as $intNoJournalAnchorId){
            $intAnchorUserId = $intNoJournalAnchorId;
            $arrResListTmp = array();
            $arrResListTmp['user_id'] = $intAnchorUserId;
            $arrResListTmp['user_name'] = $arrAnchorUserInfo[$intAnchorUserId]['anchor_user_name'];
            $arrResListTmp['union_id'] = $arrAnchorUserInfo[$intAnchorUserId]['union_id'];
            $arrResListTmp['union_name'] = $arrAnchorUserInfo[$intAnchorUserId]['union_name'];
            $arrResListTmp['create_user_id'] = $arrAnchorUserInfo[$intAnchorUserId]['create_user_id'];
            $arrResListTmp['create_user_name'] = $arrAnchorUserInfo[$intAnchorUserId]['create_user_name'];
            $arrAnchorNoJournalCount++;
            $arrResList[] = $arrResListTmp;
        }
        $arrAnchorNoJournalCount = count($arrdiffer);

        /*
         *
        foreach ($arrAnchorUserIds as $intAnchorUserId) {
            if (empty($arrAnchorJournalIds)) {
                $arrAnchorJournalIds[] = 0;
            }
            if (!in_array($intAnchorUserId, $arrAnchorJournalIds)) {
                $arrResListTmp = array();
                $arrResListTmp['user_id'] = $intAnchorUserId;
                $arrResListTmp['user_name'] = $arrAnchorUserInfo[$intAnchorUserId]['anchor_user_name'];
                $arrResListTmp['union_id'] = $arrAnchorUserInfo[$intAnchorUserId]['union_id'];
                $arrResListTmp['union_name'] = $arrAnchorUserInfo[$intAnchorUserId]['union_name'];
                $arrResListTmp['create_user_id'] = $arrAnchorUserInfo[$intAnchorUserId]['create_user_id'];
                $arrResListTmp['create_user_name'] = $arrAnchorUserInfo[$intAnchorUserId]['create_user_name'];
                $arrAnchorNoJournalCount++;
                $arrResList[] = $arrResListTmp;
            }
        }
        */

        $arrRes = array(
            'data' => $arrResList,
            'count' => $arrAnchorNoJournalCount,
        );
        return $arrRes;
    }

    /**
     * @brief 根据工会类型及合作运营找出旗下的工会
     * @param  $arrInputUnionType
     * @return array().
     */
    protected static function getUnionIdByTypeCor($arrInputUnionType)
    {
        $arrUnionIds = array();
        $arrUnionTypeInfo = Dl_Ala_Union_Anchor::getUnionInfoByTypeCor($arrInputUnionType);
        if (!empty($arrUnionTypeInfo['data'])) {
            foreach ($arrUnionTypeInfo['data'] as $intUnionId) {
                $arrUnionIds[] = $intUnionId['union_id'];
            }
        }
        return $arrUnionIds;
    }

    /**
     * @brief 根据主播ID获取工会、合作运营信息
     * @param  $arrAnchorUserParam
     * @return array().
     */
    protected static function getAnchorUserInfo($arrAnchorUserParam)
    {
        $arrAnchorIds = $arrAnchorUserParam['user_ids'];
        $arrInput = array(
            'user_ids' => $arrAnchorUserParam['user_ids'],
        );

        $arrOutput = Dl_Ala_Union_Anchor::getAnchorUserInfo($arrInput);
        $arrCreates = array();
        foreach ($arrOutput['data'] as $arrAnchorInfo) {
            $arrCreates[] = $arrAnchorInfo['create_user_id'];
        }
        $arrUserIds = array_merge($arrCreates, $arrAnchorIds);
        //合作运营用户信息
        $arrCreateInfos = Dl_Ala_Union_Anchor::_userGetInfo($arrUserIds);

        $arrRes = array();
        $arrUnionAnchor = array();
        foreach ($arrOutput['data'] as &$arrAnchorInfo) {
            $arrUnionAnchor[] = $arrAnchorInfo['anchor_user_id'];
            $arrAnchorInfo['anchor_user_name'] = empty($arrCreateInfos[$arrAnchorInfo['anchor_user_id']]['user_name']) ? strval($arrCreateInfos[$arrAnchorInfo['anchor_user_id']]['user_nickname']) : $arrCreateInfos[$arrAnchorInfo['anchor_user_id']]['user_name'];
            $arrAnchorInfo['create_user_name'] = empty($arrCreateInfos[$arrAnchorInfo['create_user_id']]['user_name']) ? strval($arrCreateInfos[$arrAnchorInfo['create_user_id']]['user_nickname']) : $arrCreateInfos[$arrAnchorInfo['create_user_id']]['user_name'];
            $arrRes[$arrAnchorInfo['anchor_user_id']] = $arrAnchorInfo;
        }
        //非工会主播赋予个人信息
        foreach ($arrAnchorIds as $intAnchorId){
            if(!in_array($intAnchorId,$arrUnionAnchor)){
                $arrAnchorInfo = array();
                $arrAnchorInfo['anchor_user_name'] = empty($arrCreateInfos[$intAnchorId]['user_name']) ? strval($arrCreateInfos[$intAnchorId]['user_nickname']) : $arrCreateInfos[$intAnchorId]['user_name'];
                $arrRes[$intAnchorId] = $arrAnchorInfo;
            }
        }

        return $arrRes;
    }

    /**
     * @brief 计算主播基本底薪
     * @param  $arrBasicSalaryParam
     * @return int.
     */
    protected static function getAnchorBasicSalary($arrBasicSalaryParam)
    {
        $intAnchorJournal = $arrBasicSalaryParam['journal'];
        $intEffectDay = $arrBasicSalaryParam['effect_day'];
        $intDuration = $arrBasicSalaryParam['duration'];

        if ($intAnchorJournal >= 10000000 && $intAnchorJournal < 50000000) {
            $intBasicSalary = 500000;
        } else if ($intAnchorJournal >= 50000000 && $intAnchorJournal < 100000000) {
            $intBasicSalary = 2500000;
        } else if ($intAnchorJournal >= 100000000 && $intAnchorJournal < 200000000) {
            $intBasicSalary = 5000000;
        } else if ($intAnchorJournal >= 200000000) {
            $intBasicSalary = 10000000;
        } else {
            $intBasicSalary = 0;
        }
        /*
        享受责任底薪条件：
            1、每月累计直播时长不少于40h
            2、每月有效天不少20天（每天连续直播1小时算1有效天）
            3、每月流水不少5000000T豆可享受底薪
        */
        if (($intDuration < 40 * 3600) ||
            ($intEffectDay < 20) || ($intAnchorJournal < 5000000)) {
            $intBasicSalary = 0;
        }
        return $intBasicSalary;
    }

    /**
     * @brief 根据主播id获取时间段直播时长信息
     * @param  $arrInput
     * @return array().
     */
    protected static function getAnchorDurationOfDay($arrInput)
    {
        //筛选时间
        $intFilterStartTime = (int)Bingo_HTTP_Request::get('filter_start_time', 0);
        $intFilterEndTime = (int)Bingo_HTTP_Request::get('filter_end_time', 0);
        $arrAnchorIds = array();

        $intStartTime = date('Ym01', $intFilterStartTime);
        $intLastDay = date('Y-m-01', $intFilterEndTime);
        $intEndTime = date("Ymd", strtotime("$intLastDay +1 month -1 day"));

        if (empty($arrInput['user_ids'])) {
            return array();
        }
        $arrAnchorIds = $arrInput['user_ids'];

        $arrServiceInput = array(
            'user_ids' => $arrAnchorIds,
            'start_time' => $intStartTime,
            'end_time' => $intEndTime,
        );

        $arrOutput = Service_Ala_Journal_Journal::getAnchorDurationOfDay($arrServiceInput);

        $arrRes = array(
            'data' => $arrOutput['data'],
        );

        return $arrRes;
    }

    /**
     * @brief 根据主播id获取时间段流水信息
     * @param  $arrInput
     * @return array().
     */
    protected static function getAnchorJournalInfo($arrInput)
    {
        //筛选时间
        $intFilterStartTime = (int)Bingo_HTTP_Request::get('filter_start_time', 0);
        $intFilterEndTime = (int)Bingo_HTTP_Request::get('filter_end_time', 0);
        $intPage = (int)Bingo_HTTP_Request::get('page', 0);
        $intPerPage = (int)Bingo_HTTP_Request::get('perPage', 30);
        $strCsv = (string)Bingo_HTTP_Request::get('csv', '');
        $strDetailDataOfAnchor = (string)Bingo_HTTP_Request::get('detail_data_anchor', '');

        $intAnchorId = 0;
        $intUnionId = 0;
        $strUnionIds = '';
        $arrAnchorIds = '';

        $intStartTime = date('Ym01', $intFilterStartTime);
        $intLastDay = date('Y-m-01', $intFilterEndTime);
        $intEndTime = date("Ymd", strtotime("$intLastDay +1 month -1 day"));

        if (!empty($arrInput['user_id'])) {
            $intAnchorId = $arrInput['user_id'];
        }
        if (!empty($arrInput['user_ids'])) {
            $arrAnchorIds = $arrInput['user_ids'];
        }
        if (!empty($arrInput['union_id'])) {
            $intUnionId = $arrInput['union_id'];
        }
        if (!empty($arrInput['union_ids'])) {
            $strUnionIds = implode(',', $arrInput['union_ids']);
        }
        $intOffset = ($intPage - 1) * $intPerPage;
        $arrServiceInput = array(
            'user_id' => $intAnchorId,
            'user_ids' => $arrAnchorIds,
            'union_id' => $intUnionId,
            'union_ids' => $strUnionIds,
            'offset' => $intOffset,
            'limit' => $intPerPage,
            'start_time' => $intStartTime,
            'end_time' => $intEndTime,
        );
            $arrServiceInput['count'] = true;
            $arrOutputCount = Service_Ala_Journal_Journal::getAnchorJournalListOfMonth($arrServiceInput);
            $intCountAll = $arrOutputCount['data'][0]['count'];

            // 调取列表服务
            unset($arrServiceInput['count']);
            if ('csv' == $strCsv) {
                //导出逻辑
                $arrServiceInput['offset'] = 0;
                $arrServiceInput['limit'] = $intCountAll;
            }
            if ('detailDataOfAnchor' == $strDetailDataOfAnchor) {
                $arrServiceInput['offset'] = 0;
                $arrServiceInput['limit'] = $intCountAll;
            }

            $arrOutput = Service_Ala_Journal_Journal::getAnchorJournalListOfMonth($arrServiceInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Journal_Journal getAnchorJournalList fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

        $arrRes = array(
            'data' => $arrOutput['data'],
            'count' => $intCountAll,
        );

        return $arrRes;
    }

    /**
     * @brief 根据主播id获取座驾信息
     * @param  $arrInput
     * @return array().
     */
    protected static function getAnchorPropGiftInfo($arrInput)
    {
        //筛选时间
        $intFilterStartTime = (int)Bingo_HTTP_Request::get('filter_start_time', 0);
        $intFilterEndTime = (int)Bingo_HTTP_Request::get('filter_end_time', 0);
        $arrUsersReq = array();
        $arrUserIds = array_unique($arrInput['user_ids']);
        if (empty($arrUserIds)) {
            return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "");
        }
        $offset = 0;
        for ($i = 0; $i < (count($arrUserIds) / 100); $i++) {
            $arrUsersReq[] = array_slice($arrUserIds, $offset, 100);
            $offset = $offset + 100;
        }
        $arrRet = array();
        $intStartMonthTime = strtotime(date("Y-m-01", $intFilterStartTime));
        $intEndMd = date('Y-m-01', $intFilterEndTime);
        $intEndMonthTime = strtotime(date("Y-m-d", strtotime("$intEndMd +1 month -1 day")));
        for ($timestamp = $intStartMonthTime; $timestamp <= $intEndMonthTime; $timestamp = strtotime("$firstdayMd +1 month ")) {
            $firstdayMd = date('Y-m-01', $timestamp);
            $lastday = strtotime(date("Y-m-d", strtotime("$firstdayMd +1 month -1 day")));
            $firstday = strtotime($firstdayMd);

            $Obj = new Tieba_Multi('getAnchorPropGiftInfo');
            $strServiceKey = 'present';
            $strUserMethod = 'getAnchorPropGiftInfo';
            $caller = new Tieba_Service($strServiceKey);
            foreach ($arrUsersReq as $index => $item) {
                $arrServiceParams = array(
                    'user_ids' => $item,
                    'start_time' => $firstday,
                    'end_time' => $lastday,
                );

                $strKey = $strUserMethod . '_' . $index;
                $arrInput = array(
                    'serviceName' => $strServiceKey,
                    'method' => $strUserMethod,
                    'ie' => 'utf-8',
                    'input' => $arrServiceParams,
                );
                $Obj->register($strKey, $caller, $arrInput);
            }
            $Obj->call();
            foreach ($arrUsersReq as $index => $item) {
                $strKey = $strUserMethod . '_' . $index;
                $resultUser = $Obj->getResult($strKey);
                foreach ($resultUser['data'] as $k => $v) {
                    $resultTmp = array();
                    $resultTmp[$k] = $v;
                    $arrRet[] = $resultTmp;
                }
            }
        }
        return $arrRet;
    }

    /**
     * @brief 导出csv文件
     * @param $arrAnchorUserIds
     * @param $arrAnchorJournalIds
     * @param $arrAnchorUserInfo
     * @param $arrResList
     * @param $bolFixNoJournalAnchorFlag
     * @return array.
     */
    protected static function Csv($arrAnchorUserIds, $arrAnchorJournalIds, $arrAnchorUserInfo, $arrResList, $bolFixNoJournalAnchorFlag)
    {

        if ($bolFixNoJournalAnchorFlag) {

            $arrResListInfo = self::getNoJournalAnchorInfo($arrAnchorUserIds, $arrAnchorJournalIds, $arrAnchorUserInfo);
            unset($arrAnchorUserIds);
            unset($arrAnchorJournalIds);
            unset($arrAnchorUserInfo);
            $arrResListTmp = $arrResListInfo['data'];
            foreach ($arrResListTmp as $arrResListTmpInfo) {
                $arrResList[] = $arrResListTmpInfo;
            }
        }

        if (!empty($arrResList)) {
            foreach ($arrResList as $arrUserListResultSort) {
                $sort[] = $arrUserListResultSort["month"];
            }
            array_multisort($sort, SORT_DESC, $arrResList);
        }

        /*
        //数据量过大，会导致内存溢出，限制每张表数据10000；
        $arrResReq = array();
        $offset      = 0;
        for ($i = 0; $i < (count($arrResList)/10000); $i++){
            $arrResReq[] = array_slice($arrResList,$offset,10000);
            $offset = $offset + 10000;
        }

        foreach ($arrResReq as $arrResReqTmp){
            $arrRes = array(
                'rows' => $arrResReqTmp,
            );

            self::_exportExcel($arrRes);
            unset($arrResReqTmp);
        }
        */
        $arrRes = array(
            'rows' => $arrResList,
        );

        self::_exportExcel($arrRes);

        return array();
    }

    /**
     * 文件导出，支持excel、csv格式
     * @param $arrRet
     * @return bool
     */
    public static function _exportExcel($arrRet)
    {

        $intExcelFlag = false;
        $arrExcelTableHeadDetail = array();
        $arrExcelTableHead = array();

        $strDetailDataOfAnchor = (string)Bingo_HTTP_Request::get('detail_data_anchor', '');
        if ('detailDataOfAnchor' == $strDetailDataOfAnchor) {
            $arrExcelTableHeadDetail = array();
            $arrExcelTableHeadDetail['month'] = "日期";
            $arrExcelTableHeadDetail['prop_count'] = "月收入座驾个数";
            $arrExcelTableHeadDetail['prop_tdou'] = "月收入座驾金额（T豆）";
            $arrExcelTableHeadDetail['income_td_total'] = "总流水（T豆）";
            $arrExcelTableHeadDetail['basic_salary'] = "月底薪";
        }
        $intRole = Util_Ala_Common::getUserRoleId();
        switch ($intRole) {
            case Lib_Ala_Define_Role::ROLE_ADMIN:
                $intExcelFlag = true;
                break;
            case Lib_Ala_Define_Role::ROLE_OPERATOR:

            case Lib_Ala_Define_Role::ROLE_UNION_PRESIDENT:

            default:
                break;
        }

        $intFilterStartTime = (int)Bingo_HTTP_Request::get('filter_start_time', 0);
        $intFilterEndTime = (int)Bingo_HTTP_Request::get('filter_end_time', 0);
        $intFilterStartTimeDate = date('Ym', $intFilterStartTime);
        $intFilterEndTimeDate = date('Ym', $intFilterEndTime);

        $arrExcelTableHead['month'] = "月份";
        $arrExcelTableHead['user_id'] = "用户ID";
        $arrExcelTableHead['user_name'] = "用户名";
        $arrExcelTableHead['union_name'] = "所属工会";

        $csv_header_list = array('月份','用户ID','用户名','所属工会','月直播有效天数','月直播时长','月收入座驾个数','月收入座驾金额','月流水','月底薪');

        if ($intExcelFlag) {
            foreach ($arrRet['rows'] as $row){
                unset($row['create_user_name']);
            }
            $csv_header_list = array('月份','用户ID','用户名','所属工会','所属合作运营','月直播有效天数','月直播时长','月收入座驾个数','月收入座驾金额','月流水','月底薪');

            $arrExcelTableHead['create_user_name'] = "所属合作运营";
        }
        $arrExcelTableHead['effective_day'] = "月直播有效天数";
        $arrExcelTableHead['live_duration'] = "月直播时长";
        $arrExcelTableHead['prop_count'] = "月收入座驾个数";
        $arrExcelTableHead['prop_tdou'] = "月收入座驾金额（T豆）";
        $arrExcelTableHead['income_td_total'] = "月流水（T豆）";
        $arrExcelTableHead['basic_salary'] = "月底薪";

        // 创建excel
        $csv_fileName = '主播底薪' . $intFilterStartTimeDate . '-' . $intFilterEndTimeDate;

//        $strExcelName = '主播底薪' . $intFilterStartTimeDate . '-' . $intFilterEndTimeDate . '.csv';
        if ('detailDataOfAnchor' == $strDetailDataOfAnchor) {
            //详细数据导出数据
            $strExcelContent = self::csv_export($arrRet['rows'],$csv_header_list,$csv_fileName);
//            $strExcelContent = self::_putcsv($strExcelName, $arrExcelTableHeadDetail, $arrRet['rows']);
        } else {
            $strExcelContent = self::csv_export($arrRet['rows'],$csv_header_list,$csv_fileName);
//            $strExcelContent = self::_putcsv($strExcelName, $arrExcelTableHead, $arrRet['rows']);
        }
        /*
        header("Content-type: application/octet-stream");
        header("Accept-Ranges: bytes");
        header("Accept-Length: " . strlen($strExcelContent));
        header("Content-Disposition: attachment; filename=" . $strExcelName . '.csv');
        */
        echo $strExcelContent;
        return true;
    }

    /**
     * 秒转换为时间
     * @param $intTime  sec
     * @return string
     */
    private static function Sec2Time($intTime)
    {
        $intTime = (int)$intTime;
        $arrValue = array(
            "years" => 0, "days" => 0, "hours" => 0,
            "minutes" => 0, "seconds" => 0,
        );
        /*
        if($intTime >= 31556926){
            $arrValue["years"] = floor($intTime/31556926);
            $intTime = ($intTime%31556926);
        }
        if($intTime >= 86400){
            $arrValue["days"] = floor($intTime/86400);
            $intTime = ($intTime%86400);
        }
         */
        if ($intTime >= 3600) {
            $arrValue["hours"] = floor($intTime / 3600);
            $intTime = ($intTime % 3600);
        }
        if ($intTime >= 60) {
            $arrValue["minutes"] = floor($intTime / 60);
            $intTime = ($intTime % 60);
        }
        $arrValue["seconds"] = floor($intTime);
        return $arrValue["hours"] . "小时" . $arrValue["minutes"] . "分" . $arrValue["seconds"] . "秒";
    }

    /**
     * @param cvs
     * @param $arrExcelTableHead
     * @param $listMemberInfo
     */
    private static function _putcsv($strFileName, $arrExcelTableHead, $listMemberInfo)
    {
        header("Content-Type: application/vnd.ms-excel; charset=GBK");
        header("Content-Disposition: attachment; filename=\"" . $strFileName);
        header('Cache-Control: max-age=0');

        $html = "\xEF\xBB\xBF";
        foreach ($arrExcelTableHead as $k => $v) {
            $html .= $v . "\t,";
        }
        $html .= "\n";
        foreach ($listMemberInfo as $index => $input) {
            foreach ($arrExcelTableHead as $k => $v) {
                $html .= $input[$k] . "\t ,";
            }
            $html .= "\n";
        }
        echo $html;
    }

    /**
     * 导出excel(csv)
     * @data 导出数据
     * @headlist 第一行,列名
     * @fileName 输出Excel文件名
     * @param $data
     * @param $headList
     * @param $fileName
     */
    private static function csv_export($data, $headList, $fileName) {

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="'.$fileName.'.csv"');
        header('Cache-Control: max-age=0');

        //打开PHP文件句柄,php://output 表示直接输出到浏览器
        $fp = fopen('php://output', 'a');

        //输出Excel列名信息
        foreach ($headList as $key => $value) {
            //CSV的Excel支持GBK编码，一定要转换，否则乱码
            $headList[$key] = iconv('utf-8', 'gbk', $value);
        }

        //将数据通过fputcsv写到文件句柄
        fputcsv($fp, $headList);

        //计数器
        $num = 0;

        //每隔$limit行，刷新一下输出buffer，不要太大，也不要太小
        $limit = 15000;

        //逐行取出数据，不浪费内存
        $count = count($data);
        for ($i = 0; $i < $count; $i++) {

            $num++;

            //刷新一下输出buffer，防止由于数据过多造成问题
            if ($limit == $num) {
                ob_flush();
                flush();
                $num = 0;
            }

            $row = $data[$i];
            foreach ($row as $key => $value) {
                $row[$key] = iconv('utf-8', 'gbk', $value);
            }

            fputcsv($fp, $row);
        }
    }

    /**
     * @brief 返回结果
     * @param  $errno
     * @param string $errmsg
     * @param array $arrExtData
     * @return array().
     * @throws Exception
     */
    protected static function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => strval($errmsg),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return array();
    }

}
