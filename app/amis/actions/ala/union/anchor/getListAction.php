<?php

/**
 * 主播-列表
 */
class getListAction extends Util_Action
{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    /**
     * @return int
     */
    public function _execute()
    {
        // 操作人
        $intUserId = Util_User::$intUserId;
        $strUserName = Util_User::$strUserName;

        // 权限
        $arrInput = array(
            'user_id' => $intUserId,
            'auth_id' => Lib_Ala_Define_Authority::AUTHORITY_LIVE_MANAGEMENT_TEAM_MANAGEMENT_TEAM_ANCHOR,
        );
        $arrOutput = Service_Ala_Authority_Authority::canUserAccess($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Authority_Authority::canUserAccess input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, $arrOutput['usermsg']);
        }
        $bolCanAccess = $arrOutput['data'];
        if ($bolCanAccess === false) {
            return $this->_jsonRet(Tieba_Errcode::ERR_ACTION_FORBIDDEN, '无权限');
        }
        
        // Request
        $intUnionId = intval(Bingo_Http_Request::get('union_id', 0));
        $intPage = intval(Bingo_Http_Request::get('page', 1));
        $strSearchAnchorUserName = strval(Bingo_Http_Request::get('s_anchor_user_name', ''));
        
        // 公会ID
        if (intval($intUnionId) == 0) {
            return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "公会ID不能为空");
        }
        
        // 列表数据条数
        $intReqNum = 10;
        
        // 获取工会信息
        $arrOutput = Dl_Ala_Union_Union::getOne($intUnionId);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getOne fail. input:[]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL, "服务器出错");
        }
        $arrUnionInfo = $arrOutput['data'];
        if (is_null($arrUnionInfo)) {
            return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "公会不存在");
        }

        //检查权限 --是否是本公会会长 或者 本公会代运营
        if ($arrRet = Util_Ala_Perm::checkAnchorPermFailed($intUnionId)) {
            return self::_jsonRet($arrRet['no'], $arrRet['error'], $arrRet['data']);
        }
        // 获取主播列表
        if (strlen($strSearchAnchorUserName) > 0) {
            // 查询主播用户名的有效性
            $intAnchorUserId = Util_Ala_Common::getUidByNames($strSearchAnchorUserName);
            if(false === $intAnchorUserId) {
                return $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            // 主播用户名不存在
            if (empty($intAnchorUserId)) {
                Bingo_Log::warning(__CLASS__.'::'.__FUNCTION__.' empty $intAnchorUserId. $arrOutput'.serialize($arrOutput));
                return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "主播用户名不存在");
            }


            
            $arrOutput = Dl_Ala_Union_Anchor::getUnionAnchor($intUnionId, $intAnchorUserId, array_keys(Lib_Ala_Define_UnionAnchor::$arrStatusNames));
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getUnionAnchor fail. input:[]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL, "服务器出错");
            }
            $arrAnchorList = is_null($arrOutput['data']) ? array() : array(
                $arrOutput['data']
            );
            
            // 获取主播个数
            $intAnchorCount = count($arrAnchorList);
        } else {
            $arrOutput = Dl_Ala_Union_Anchor::getList($intUnionId, $intPage, $intReqNum);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getList fail. input:[]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL, "服务器出错");
            }
            $arrAnchorList = $arrOutput['data'];
            
            // 获取主播个数
            // 17/11/2 gaojingjing03 分页数据包含未加入和已拒绝的数据，不能使用union表中记录的已加入的作为分页总数
            $arrStatus = array_keys(Lib_Ala_Define_UnionAnchor::$arrStatusNames);

            $arrOutput = Dl_Ala_Union_Anchor::getUnionAnchorCount($intUnionId, $arrStatus);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getUnionAnchorCount fail. input:[$intUnionId, ".serialize($arrStatus)."]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL, "服务器出错");
            }
            $intAnchorCount = $arrOutput['data'];
        }
        
        // 获取主播和审核员的用户名
        $arrNeedNameUId = array();
        $arrAnchorUId = array();
        foreach ($arrAnchorList as $arrAnchorOne) {
            $intAnchorUId = intval($arrAnchorOne['anchor_user_id']);
            $intAuditUId = intval($arrAnchorOne['audit_user_id']);
            
            $arrNeedNameUId[$intAnchorUId] = $intAnchorUId;
            $arrNeedNameUId[$intAuditUId] = $intAuditUId;
            $arrAnchorUId[] = $intAnchorUId;
        }

        // 17/11/2 gaojingjing03 有user_id为0时会导致service不返回数据
        $arrNeedNameUId = array_filter(array_unique($arrNeedNameUId));
        
        $arrInput = array(
            "user_id" => array_values($arrNeedNameUId)
        );
        $arrOutput = Tieba_Service::call('user', 'getUnameByUids', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call user::getUnameByUids fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "服务器出错");
        }
        
        $arrUIdName = $arrOutput['output']['unames'];
        $arrUIdNameIndex = array();
        foreach ($arrUIdName as $arrUIdNameOne) {
            $intNeedNameUId = intval($arrUIdNameOne['user_id']);
            $strNeedNameUId = strval($arrUIdNameOne['user_name']);
            $arrUIdNameIndex[$intNeedNameUId] = $strNeedNameUId;
        }

        Bingo_Log::warning('Amis ala union getList::$arrAnchorList:'.serialize($arrAnchorList).' $arrNeedNameUId:'.serialize($arrNeedNameUId).' $arrOutput:'.serialize($arrOutput).' $arrUIdNameIndex:'.serialize($arrUIdNameIndex));
        
        // 主播详细数据
        $arrOutput = Service_Ala_Union_Anchor::getAnchorInfo($arrNeedNameUId);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Union_Anchor::getAnchorInfo fail. input:[" . serialize($arrNeedNameUId) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "服务器出错");
        }
        $arrAnchorInfoIndex = $arrOutput['data'];

        // 17/9/18 gaojingjing03 二期增加所属分类，调用@kangqinmou在ala里新增的getTailInfoByUserIds服务
        $arrInput = array(
            'user_ids' => $arrNeedNameUId,
        );
        $arrAnchorCategoryIndex = Util_Ala_Service::call('getTailInfoByUserIds', $arrInput);

        // 17/9/18 gaojingjing03 二期新增播放时长
        $intStartDayDate = date('Ymd', $arrUnionInfo['create_time']);
        $intEndDayDate   = date('Ymd');
        $intOffset       = 0;
        $intPageSize     = count($arrNeedNameUId);

        $arrServiceInput = array(
            'user_id' => $arrNeedNameUId,
            'anchor_type' => Util_Ala_Journal::ANCHOR_TYPE_UNION,
            'search_order' => 2,
            'start_time' => $intStartDayDate,
            'union_id' => $intUnionId,
            'end_time' => $intEndDayDate,
            'offset' => $intOffset,
            'limit' => $intPageSize,
        );

        $arrOutput = Service_Ala_Journal_Journal::getAnchorSumJournalList($arrServiceInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call Service_Ala_Journal_Journal::getAnchorSumJournalList fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrDurationIndex = array();

        foreach ($arrOutput['data'] as $v) {
            $arrDurationIndex[$v['user_id']] = $v['live_duration'];
        }


        // 17/12/07 wangyang66 按月有限天、T豆
        $arrEdayTd = Util_Ala_User::getEDayTdLiveDuration($arrAnchorUId, null, false);
        // 17/12/07 wangyang66 直播时长
        $arrLiveDuration = $arrEdayTd; //Util_Ala_User::getEDayTdLiveDuration($arrAnchorUId);


        // 主播数据 trans
        foreach ($arrAnchorList as $intKey => $arrAnchorOne) {
            $intAnchorUId = intval($arrAnchorOne['anchor_user_id']);
            $intAuditUId = intval($arrAnchorOne['audit_user_id']);
            $arrJson = (strlen($arrAnchorOne['json']) == 0) ? array() : json_decode($arrAnchorOne['json'], true);
            
            $arrAnchorList[$intKey]['anchor_user_name'] = isset($arrUIdNameIndex[$intAnchorUId]) ? $arrUIdNameIndex[$intAnchorUId] : '空';
            $arrAnchorList[$intKey]['audit_user_name'] = isset($arrUIdNameIndex[$intAuditUId]) ? $arrUIdNameIndex[$intAuditUId] : '空';
            
            $arrAnchorList[$intKey]['photo'] = empty($arrAnchorInfoIndex[$intAnchorUId]) ? '' : $arrAnchorInfoIndex[$intAnchorUId]['photo'];
            $arrAnchorList[$intKey]['note'] = empty($arrAnchorInfoIndex[$intAnchorUId]) ? '' : $arrAnchorInfoIndex[$intAnchorUId]['note'];
//            $arrAnchorList[$intKey]['phone_number'] = empty($arrAnchorInfoIndex[$intAnchorUId]) ? '' : $arrAnchorInfoIndex[$intAnchorUId]['phone_number'];
            $arrAnchorList[$intKey]['phone_num'] = empty($arrJson['anchor_phone_num']) ? '' : $arrJson['anchor_phone_num'];

            $arrAnchorList[$intKey]['anchor_level_previous'] = $arrAnchorInfoIndex[$intAnchorUId]['anchor_level_previous'];
            $arrAnchorList[$intKey]['anchor_level_present'] = $arrAnchorInfoIndex[$intAnchorUId]['anchor_level_present'];

            // 扩展字段
            foreach ($arrJson as $strJsonKey => $strJsonValue) {
                $arrAnchorList[$intKey][$strJsonKey] = $strJsonValue;
            }

            // 17/9/18 gaojingjing03 二期新增播放时长、所属分类
            $arrAnchorList[$intKey]['total_duration'] = isset($arrDurationIndex[$intAnchorUId]) ? $arrDurationIndex[$intAnchorUId] : '0';

            $arrAnchorList[$intKey]['category'] = isset($arrAnchorCategoryIndex[$intAnchorUId]) ? $arrAnchorCategoryIndex[$intAnchorUId]['tail_name'] : '无';

            $arrAnchorList[$intKey]['status_name'] = Lib_Ala_Define_UnionAnchor::$arrStatusNames[$arrAnchorList[$intKey]['status']];

            // 17/12/07 wangyang66 按月有效天、 T豆
            $arrAnchorList[$intKey]['effective_day'] = isset($arrEdayTd[$intAnchorUId]) ? $arrEdayTd[$intAnchorUId]['effective_day'] : '无';
            $arrAnchorList[$intKey]['income_td'] = isset($arrEdayTd[$intAnchorUId]) ? $arrEdayTd[$intAnchorUId]['income_td'] : '无';
            $arrAnchorList[$intKey]['new_fans'] = isset($arrEdayTd[$intAnchorUId]) ? $arrEdayTd[$intAnchorUId]['new_fans'] : '无';
            $arrAnchorList[$intKey]['live_duration'] = isset($arrLiveDuration[$intAnchorUId]) ? $arrLiveDuration[$intAnchorUId]['live_duration'] : '无';
        }
        
        // 返回列表
        $arrRet = array(
            'rows' => $arrAnchorList,
            'count' => $intAnchorCount
        );
        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, "", $arrRet);
    }

    /**
     * @brief _jsonRet
     * @param errno,errmsg,data
     * @param string $errmsg
     * @param array $arrExtData
     * @return int : 0.
     * @throws Exception
     */
    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => strval($errmsg),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }
}
