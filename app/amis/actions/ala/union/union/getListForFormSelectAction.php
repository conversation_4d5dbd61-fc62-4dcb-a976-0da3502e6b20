<?php
/**
 * getListForFormSelectAction.php 为select下拉菜单提供公会列表的数据源
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 17/9/25 下午3:46
*/

class getListForFormSelectAction extends Util_Action
{
    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    /**
     *
     * @return int
     */
    public function _execute() {
        // 操作人
        $intUserId = Util_User::$intUserId;
        $strUserName = Util_User::$strUserName;

        // Request
        $intPage = intval(Bingo_Http_Request::get('page', 1));
        $strSearchUnionName = strval(Bingo_Http_Request::get('s_union_name', ''));
        $intDefaultUnionId = intval(Bingo_Http_Request::get('default_union_id', 0));
        $intCooperationUserId = intval(Bingo_Http_Request::get('cooperation_user_id', 0));
        $intType = intval(Bingo_Http_Request::get('type', 0));    //type  1、获取合作运营   2、获取工会 , 3、获取发放花瓣失效时间

        if(1 == $intType) {
            return self::_getAllCooperationList();
        }
        else if(3 == $intType) {
            return self::_getEndTime();
        }

        //根据不同角色返回不同数据
        $intRole = Util_Ala_Common::getUserRoleId();
        $cond = true;
        $intUnionUserId = 0;
        switch ($intRole) {
            case Lib_Ala_Define_Role::ROLE_ADMIN:
                if($intCooperationUserId > 0 ) {
                    $cond = 'create_user_id = ' . $intCooperationUserId;
                }

                break;
            case Lib_Ala_Define_Role::ROLE_OPERATOR:
                $cond = 'create_user_id = ' . $intUserId;
                if($intCooperationUserId > 0 ) {
                    $cond = $cond . ' and ' . 'create_user_id = ' . $intCooperationUserId;
                }
                break;
            case Lib_Ala_Define_Role::ROLE_UNION_PRESIDENT:
                $intUnionUserId = $intUserId;
                // 不允许搜索
                $strSearchUnionName = '';
                break;
            default:
                break;
        }

        // 列表数据条数
        $intReqNum = 1000;
        
        // 获取工会列表
        if (strlen($strSearchUnionName) > 0) {
            // 通过工会名，搜索工会
            $arrOutput = Dl_Ala_Union_Union::getListByUnionName($strSearchUnionName, $intPage, $intReqNum, $cond);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getList fail. input:[]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL, "服务器出错");
            }
            $arrUnionList = $arrOutput['data'];
        } elseif (!empty($intUnionUserId)) {
            // 通过会长ID，搜索工会
            $arrOutput = Dl_Ala_Union_Union::getListByUnionUserId($intUnionUserId, $intPage, $intReqNum, $cond);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getListByUnionUserId fail. input:[]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL, "服务器出错");
            }
            $arrUnionList = $arrOutput['data'];
        } else {
            // 工会列表
            $arrOutput = Dl_Ala_Union_Union::getList($intPage, $intReqNum, $cond);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getList fail. input:[]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL, "服务器出错");
            }
            $arrUnionList = $arrOutput['data'];
        }
        
        // 工会数据 trans
        $arrOptions = array();
        foreach ($arrUnionList as $intKey => $arrUnionOne) {
            $arrOptions[] = array(
                'label' => $arrUnionOne['union_name'],
                'value' => $arrUnionOne['union_id'],
            );
        }

        $intDefaultValue = $intDefaultUnionId;
        
        // 返回列表
        return self::_jsonRetForSelect(Tieba_Errcode::ERR_SUCCESS, "", $arrOptions, $intDefaultValue);
    }


    /**
     * @return bool
     */
    private static function _getEndTime() {
        $t2 = strtotime(date('Y-m-t'));
        $intEndTime = date('Y-m-d',$t2);
        return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, array('huaban_end_time' => $intEndTime));
    }


    /**
     * @return bool|int
     */
    private static function _getAllCooperationList() {
        $intOptionType = intval(Bingo_Http_Request::get('option_type', 0));
        //根据不同角色返回不同数据
        $intRole = Util_Ala_Common::getUserRoleId();

        switch ($intRole) {
            case Lib_Ala_Define_Role::ROLE_ADMIN:
                break;
            case Lib_Ala_Define_Role::ROLE_CHECK_PUSH_SHOUBAI:
                break;
            default:

                return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL, "没权限");
        }

        $arrDlInput = array(
            'offset' => 0,
            'ps' => 1000,
            'cond' => "1=1",
        );

        $arrOutput = Dl_Ala_Authority_Authority::searchCooperation($arrDlInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Authority_Authority::searchCooperation fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrCooperationList = $arrOutput['data']['rows'];
        $arrOptions = array();
        foreach ($arrCooperationList as $intKey => $arrCooperationOne) {
            if(1 == $intOptionType) {
                $arrOptions[] = array(
                    'label' => $arrCooperationOne['cooperation_name'],
                    'value' => $arrCooperationOne['cooperation_name'],
                );
            }
            else {
                $arrOptions[] = array(
                    'label' => $arrCooperationOne['cooperation_name'],
                    'value' => $arrCooperationOne['user_id'],
                );
            }

        }

        // 返回列表
        return self::_jsonRetForSelect(Tieba_Errcode::ERR_SUCCESS, "", $arrOptions);
    }

    /**
     *
     * @param int $errno
     * @param string $errmsg
     * @param array $arrOptions array(array('label'=>'', 'value'=>''), ....);
     * @param string $mixedDefaultValue
     * @return int
     */
    protected function _jsonRetForSelect($errno, $errmsg = '', $arrOptions = array(), $mixedDefaultValue = '') {
       /**
        * @link http://amis.baidu.com/docs/api#select
        * {
               status: 0,  // 0 表示成功，非0 表示失败
               msg: '',    // 提示信息
               data: {
                   options: [
                       {
                           label: 'Option Name', // 选项说明
                           value: 'value', // 选项值
                           disabled: false // 是否可选。
                       },
                        ...
                   ],
                   value: 'value' // 同时可以指定默认值。
               }
          }
        */
        $arrRet = array(
            'status' => intval($errno),
            'msg'    => strval($errmsg),
            'data'   => array(
                'options' => $arrOptions,
                //'value'   => $mixedDefaultValue,
            ),
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }

    /**
     * @brief _jsonRet
     *
     * @param
     *            errno,errmsg,data
     * @return : 0.
     *        
     */
    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => strval($errmsg),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }
}
