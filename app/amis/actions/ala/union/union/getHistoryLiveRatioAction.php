<?php

/**
 * Created by PhpStorm.
 * User: caowu
 * Date: 18/11/11
 * Time: 下午8:15
 */
class getHistoryLiveRatioAction extends Util_Action {

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = true;

    /**
     * @return bool
     */
    public function _execute() {
        // 操作人
        $intOwnUserId = Util_User::$intUserId;
        $strOwnUserName = Util_User::$strUserName;
        $strMethod = Bingo_Http_Request::get('method', '');
        $intType = intval(Bingo_Http_Request::get('type', 0));
        $strName = strval(Bingo_Http_Request::get('name', ''));
        $intCreateUserId = intval(Bingo_Http_Request::get('create_user_id', 0));
        $intUnionId = intval(Bingo_Http_Request::get('union_id', 0));
        $intStartTime = intval(Bingo_Http_Request::get('start_time', time()));
        $intEndTime   = intval(Bingo_Http_Request::get('end_time', time()));
        $intStartDayDate = date("Ymd",$intStartTime);
        $intEndDayDate = date("Ymd",$intEndTime);
        if((($intEndTime - $intStartTime) / 86400) > 30) {
            return Util_Ala_Common::jsonAmisRetMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "每次查询跨度不能大于一个月");
        }

        $booIsViewOperator = false;
        $arrUnionIds = array();
        if(!empty($intCreateUserId) && 1 == $intType) {  //查合作运营开播率
            $booIsViewOperator = true;
            $arrOutput = Dl_Ala_Union_Union::getListByCreateUserId($intCreateUserId, 1, Lib_Ala_Define_Role::ROLE_OPERATOR_MANAGE_MAX);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getListByCreateUserId fail. input:[]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $arrUnionList = $arrOutput['data'];
            foreach ($arrUnionList as $arrUnionInfo) {
                $arrUnionIds[] = $arrUnionInfo['union_id'];
            }
        }
        else if(!empty($intUnionId) && is_numeric($intUnionId) && 2 == $intType) {  //查工会开播率
            $arrUnionIds[] = $intUnionId;
        }
        else {
            Bingo_Log::warning("error params");
            return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if(empty($arrUnionIds)) {
            Bingo_Log::warning("no data!!");
            return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS);
        }
        foreach ($arrUnionIds as $intUnId) {
            if ($arrRet = Util_Ala_Perm::checkUnionPermFailedPlus($intUnId)) {
                return Util_Ala_Common::jsonAmisRetMsg($arrRet['no'], $arrRet['error'], $arrRet['data']);
            }
        }


        //按date查出当日开播率
        $strCondition = " union_id in (" . implode(',', $arrUnionIds) . ") and date >= " . $intStartDayDate . " and date <= " . $intEndDayDate ;
        $arrOutput = Dl_Ala_Union_Union::getHistoryLiveRatio($strCondition);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getHistoryLiveRatio fail. input:[" . serialize($strCondition) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrRetData = $arrOutput['data'];

        //按date查出当日流水总和, 合作运营或者工会长
        $strCondition = " union_id in (" . implode(',', $arrUnionIds) . ") and date >= " . $intStartDayDate . " and date <= " . $intEndDayDate ;
        $arrOutput = Dl_Ala_Journal_Journal::getHistoryJournalWithCondition($strCondition);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getHistoryJournalWithCondition fail. input:[" . serialize($strCondition) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrJournalData = $arrOutput['data'];
        $arrDateMapJournal = array();
        foreach ($arrJournalData as $value) {
            $arrDateMapJournal[intval($value['date'])] = intval($value['income_td']);
        }

        $arrDateMapTotalJournal = array();
        if(true === $booIsViewOperator) {   //合作运营数据,按date查找pgc总流水
            $arrServiceInput = array(
                'type' => 1,
                'start_time' => $intStartDayDate,
                'end_time' => $intEndDayDate,
            );
            $arrOutput = Service_Ala_Journal_Journal::getAnchorPlatformJournal($arrServiceInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__."::".__FUNCTION__." call Service_Ala_Journal_Journal getAnchorPlatformJournal fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            foreach ($arrOutput['data'] as $value) {
                $arrDateMapTotalJournal[intval($value['date'])] = intval($value['income_td']);
            }
        }
        else {  //工会数据,按date查找所属合作运营总流水
            $arrOutput = Dl_Ala_Union_Union::getOne($intUnionId); // 获取工会信息
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getOne fail. input:[]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $intUnionCreateUserId = intval($arrOutput['data']['create_user_id']); // 获取工会所属合作运营
            if (!empty($intUnionCreateUserId)) {
                $arrTempUnionIds = array();
                $arrOutput = Dl_Ala_Union_Union::getListByCreateUserId($intUnionCreateUserId, 1, Lib_Ala_Define_Role::ROLE_OPERATOR_MANAGE_MAX);
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getListByCreateUserId fail. input:[]; output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                    return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
                $arrUnionList = $arrOutput['data'];
                foreach ($arrUnionList as $arrUnionInfo) {
                    $arrTempUnionIds[] = $arrUnionInfo['union_id'];
                }
                $strCondition = " union_id in (" . implode(',', $arrTempUnionIds) . ") and date >= " . $intStartDayDate . " and date <= " . $intEndDayDate ;
                $arrOutput = Dl_Ala_Journal_Journal::getHistoryJournalWithCondition($strCondition);
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getHistoryJournalWithCondition fail. input:[" . serialize($strCondition) . "]; output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                    return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
                foreach ($arrOutput['data'] as $value) {
                    $arrDateMapTotalJournal[intval($value['date'])] = intval($value['income_td']);
                }
            }
        }

        foreach ($arrRetData as &$arrValue) {
            $intTempDate = intval($arrValue['date']);
            $arrValue['live_ratio'] = doubleval($arrValue['anchor_num']) > 0 ? sprintf("%01.2f", doubleval($arrValue['valid_anchor_num']) / doubleval($arrValue['anchor_num']) * 100 ) . "%" : "-";
            $arrValue['valid_anchor_num_rate'] = strval($arrValue['valid_anchor_num']) . "/" . strval($arrValue['anchor_num']);
            $arrValue['journal'] = doubleval($arrDateMapJournal[$intTempDate]) / 1000;
            $arrValue['journal_rate'] = doubleval($arrDateMapTotalJournal[$intTempDate]) > 0 ? sprintf("%01.2f", doubleval($arrDateMapJournal[$intTempDate]) / doubleval($arrDateMapTotalJournal[$intTempDate]) * 100 ) . "%" : "-";
        }

        // 导出 2 为导出excel
        $intExcel = intval(Bingo_Http_Request::get('excel', 0));
        if(2 == $intExcel) {
            $strFileName =  "($strName)的历史开播率" .$intStartDayDate.'-'.$intEndDayDate.'查询结果.xls';
            header("Content-type: text/html; charset=gbk");
            header("Content-type:application/vnd.ms-excel");
            header("Content-Disposition:filename=$strFileName");
            self::_createExcel($arrRetData, $intType);
            return 0;
        }

        return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);

    }

    /**
     * 新建Excel表并直接输出到浏览器
     * @param null
     * @return null
     * */
    protected static function _createExcel($retArr, $intType){
        echo self::_utf8ToGbk("日期\t");
        if(1 == $intType) {
            echo self::_utf8ToGbk("当日工会数\t");
        }
        echo self::_utf8ToGbk("当日主播数\t");
        echo self::_utf8ToGbk("当日有效开播主播数/工会主播\t");
        echo self::_utf8ToGbk("当日开播率\t");
        echo self::_utf8ToGbk("当日流水（RMB)\t");
        echo self::_utf8ToGbk("当日贡献占比\t\n");
        foreach ($retArr as $arrValue) {
            echo self::_utf8ToGbk($arrValue['date'] . "\t");
            if(1 == $intType) {
                echo self::_utf8ToGbk($arrValue['union_num'] . "\t");
            }
            echo self::_utf8ToGbk($arrValue['anchor_num'] . "\t");
            echo self::_utf8ToGbk($arrValue['valid_anchor_num_rate'] . "\t");
            echo self::_utf8ToGbk($arrValue['live_ratio'] . "\t");
            echo self::_utf8ToGbk($arrValue['journal'] . "\t");
            echo self::_utf8ToGbk($arrValue['journal_rate'] . "\t\n");
        }
    }

    /**
     * 将utf8转为gbk
     * @param null
     * @return null
     * */
    protected static function _utf8ToGbk($strValue)
    {
        return iconv("UTF-8", "GBK", $strValue);
    }

}