<?php

/**
 * feed list type
 */
class FeedListTypeAction extends Util_Action
{
    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    /**
     * @return
     */
    public function _execute()
    {


        $arrTypeIdName = array();

        $arrInput = array();
        $arrOutput = Tieba_Service::call('beyond', 'getChannelListForThreadPush', $arrInput, null, null, 'post', null, 'utf-8');
        if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__."::".__FUNCTION__." call game::addInfoTopic fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            Bingo_Http_Response::contextType('application/json');
            echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL), 'utf-8');

            return;
        }

        //Bingo_Log::warning("====>" . var_export($arrOutput, true));
        $arrChannel = $arrOutput['data'];
        $arrRet = array();
        foreach ($arrChannel as $key => $value) {
            $strName = $value['name'].'@';
            foreach ($value['category'] as $k => $v ) {
                if ($v['name'] == '资讯') {
                    $strName = $strName. '资讯';

                    $temp['label'] = $strName;
                    $temp['value'] = $v['id'];
                    $temp['disable'] = false;
                    $arrTypeIdName[$v['id']] = $strName;
                    $arrRet[] = $temp;
                    break;
                }
            }
        }

        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, "", $arrRet);
    }

    /**
     * @brief _jsonRet 【注意】这里json格式，与amis要求一致
     * @param
     * @return : 0.
     *
     */
    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'status' => intval($errno),
            'msg' => strval($errmsg),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }
}
