<?php

/**
 * Created by PhpStorm.
 * User: wangyang66
 * Date: 18/01/29
 * Time: 凌晨00:24
 */
class InfoTopicAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * 执行函数
	 *
	 * @param null
	 *
	 * @return null
	 * */
	public function _execute()
	{
		//数据获取
		$topic_id              = intval(Bingo_Http_Request::get('topic_id', 0));
		$ps                    = intval(Bingo_Http_Request::get('page_size', 10));
		$pn                    = intval(Bingo_Http_Request::get('page', 1));
		$topic_name            = strval(Bingo_Http_Request::get('topic_name', ''));
		$feed_list_type        = intval(Bingo_Http_Request::get('feed_list_type', 0));
		$stick_img             = strval(Bingo_Http_Request::get('stick_img', ''));
		$title_big             = strval(<PERSON><PERSON>_Http_Request::get('title_big', ''));
		$title_small           = strval(Bingo_Http_Request::get('title_small', ''));
		$method                = strval(Bingo_Http_Request::get('method', ''));
		$op_name               = Util_User::$strUserName;
		$topic_tyep            = intval(Bingo_Http_Request::get('topic_type', 0));
		$parent_id             = intval(Bingo_Http_Request::get('parent_id', 0));
		Bingo_Log::warning(serialize($pre_url));
		if($method == 'add'){
			$arrInput = array(
				'topic_name'            => $topic_name,
				'feed_list_type'        => $feed_list_type,
				'stick_img'             => $stick_img,
				'title_big'             => $title_big,
				'title_small'           => $title_small,
				'op_name'               => $op_name,
                'topic_type'            => $topic_tyep,
                'parent_id'             => $parent_id,
			);
			$arrOutput = Tieba_Service::call('game', 'addInfoTopic', $arrInput, null, null, 'post', null, 'gbk');
			if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
				$strLog = __CLASS__."::".__FUNCTION__." call game::addInfoTopic fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
				Bingo_Log::warning($strLog);
				Bingo_Http_Response::contextType('application/json');
				echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL), 'utf-8');

				return;
			}
			Bingo_Http_Response::contextType('application/json');
			echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_SUCCESS), 'utf-8');

			return;
		}
		if($method == 'delete'){
			$arrInput  = array('topic_id' => $topic_id,);
			$arrOutput = Tieba_Service::call('game', 'deleteInfoTopicById', $arrInput, null, null, 'post', null, 'gbk');
			if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
				$strLog = __CLASS__."::".__FUNCTION__." call ala::getLiveAuditBlack fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
				Bingo_Log::warning($strLog);
				Bingo_Http_Response::contextType('application/json');
				echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL), 'utf-8');

				return;
			}
			Bingo_Http_Response::contextType('application/json');
			echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_SUCCESS), 'utf-8');

			return;
		}
		if($method == 'alter'){
			$arrInput = array(
				'topic_id'              => $topic_id,
                'topic_name'            => $topic_name,
				'stick_img'             => $stick_img,
				'title_big'             => $title_big,
				'title_small'           => $title_small,
				'op_name'               => $op_name,
                'topic_type'            => $topic_tyep,
                'parent_id'             => $parent_id,
			);
			$arrOutput = Tieba_Service::call('game', 'updateInfoTopic', $arrInput, null, null, 'post', null, 'gbk');
			if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
				$strLog = __CLASS__."::".__FUNCTION__." call game::updateInfoTopic fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
				Bingo_Log::warning($strLog);
				Bingo_Http_Response::contextType('application/json');
				echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL), 'utf-8');

				return;
			}
			Bingo_Http_Response::contextType('application/json');
			echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_SUCCESS), 'utf-8');

			return;
		}else{
			$arrInput = array(
				'pn'       => $pn - 1,
			);
            if (!empty($feed_list_type)) {
                $arrInput['feed_list_type'] = $feed_list_type;
            }
            if (!empty($topic_id)) {
                $arrInput['topic_id'] = $topic_id;
            }
			array_filter($arrInput);
			$arrOutput = Tieba_Service::call('game', 'selectInfoTopic', $arrInput, null, null, 'post', null, 'gbk');
			if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
				$strLog = __CLASS__."::".__FUNCTION__." call game::selectInfoTopic fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
				Bingo_Log::warning($strLog);
				Bingo_Http_Response::contextType('application/json');
				echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL), 'utf-8');

				return;
			}

            //赋予feed_list_type 中文名
            $arrTypeIdName =  $this->_getTypeIdName();
            foreach( $arrOutput['data']['list'] as $key => $value ) {
                $intFeedListType = $value['feed_list_type'];
                if (isset($arrTypeIdName[$intFeedListType])) {
                    $arrOutput['data']['list'][$key]['feed_list_type_name'] = $arrTypeIdName[$intFeedListType];
                }
            }


			Bingo_Http_Response::contextType('application/json');

			return self::resultSender(self::errRet(Alalib_Conf_Error::ERR_SUCCESS, Bingo_Encode::convert(array(
				'rows'  => self::DataFormatter($arrOutput['data']['list']),
				'count' => $arrOutput['data']['count'],
				Bingo_Encode::ENCODE_GBK,
				Bingo_Encode::ENCODE_UTF8,
			))));
		}
		// 默认成功返回值
		//            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, Tieba_Error::getUserMsg(Tieba_Errcode::ERR_SUCCESS), $retData);
	}


    /**
     * 获取类目及名字对应关系
     * @param
     * @return
     */
    private function _getTypeIdName() {
        $arrTypeIdName = array();
        $arrInput = array();
        $arrOutput = Tieba_Service::call('beyond', 'getChannelListForThreadPush', $arrInput, null, null, 'post', null, 'utf-8');
        if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__."::".__FUNCTION__." call game::addInfoTopic fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            Bingo_Http_Response::contextType('application/json');
            echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL), 'utf-8');

            return;
        }
        $arrChannel = $arrOutput['data'];
        $arrRet = array();
        foreach ($arrChannel as $key => $value) {
            $strName = $value['name'].'@';
            foreach ($value['category'] as $k => $v ) {
                if ($v['name'] == '资讯') {
                    $strName = $strName. '资讯';

                    $temp['label'] = $strName;
                    $temp['value'] = $v['id'];
                    $temp['disable'] = false;
                    $arrTypeIdName[$v['id']] = $strName;
                    break;
                }
            }
            $arrRet[] = $temp;
        }
        return $arrTypeIdName;
    }




	
	/**
	 * 返回值
	 *
	 * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
	 * @param  {Array} $arrOutData default array()
	 * @param  {String} $strMsg default ""
	 *
	 * @return {Array} :errno :errmsg :{Array}output
	 * */
	private static function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS, $arrOutData = array(), $strMsg = "")
	{
		$arrOutput            = array();
		$arrOutput['status']  = $intErrno;
		$arrOutput['msg']     = Alalib_Conf_Error::getErrorMsg($intErrno);
		$arrOutput['usermsg'] = $strMsg;
		$arrOutput['data']    = $arrOutData;
		
		return $arrOutput;
	}
	
	/**
	 * 将sql数据格式转换成amis能接受的数据格式
	 *
	 * @param null
	 *
	 * @return null
	 * */
	private static function DataFormatter($arrInput = array())
	{
		foreach($arrInput as &$item){
			$item['pre_url']      = self::arrayToString(json_decode($item['pre_url'], true));
			$item['h5_settings']  = self::arrayToString(json_decode($item['h5_settings'], true));
			$item['activity_ids'] = self::arrayToString(json_decode($item['activity_ids'], true));
		}
		unset($item);
		
		return $arrInput;
	}
	
	/**
	 * @param $arrInput
	 *
	 * @return string
	 */
	private static function arrayToString($arrInput)
	{
		$strRet = '';
		$i      = 0;
		$count  = count($arrInput);
		while($i < $count){
			$strRet .= strval($arrInput[$i]);
			if($i < $count - 1){
				$strRet .= ",";
			}
			$i++;
		}
		
		return $strRet;
	}
	
	/**
	 * 结果返回函数
	 *
	 * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
	 * @param  {Array} $arrOutData default array()
	 * @param  {String} $strMsg default ""
	 *
	 * @return {Array} :errno :errmsg :{Array}output
	 * */
	private static function resultSender($arrInput, $code_type = 'utf-8')
	{
		echo Bingo_String::array2json($arrInput, $code_type);
		
		return true;
	}
}

?>
