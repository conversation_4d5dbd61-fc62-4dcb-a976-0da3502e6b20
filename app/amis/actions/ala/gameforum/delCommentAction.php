<?php

/**
 * Created by PhpStorm.
 * User: zhouyan08
 * Date: 18/3/21
 * Time: 下午4:48
 */
class delCommentAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * 执行函数
	 *
	 * @param null
	 *
	 * @return null
	 * */
	public function _execute()
	{
		//amis数据获取
		$op_id     = Util_User::$intUserId;
		$op_name   = Util_User::$strUserName;
		$user_id   = intval(Bingo_Http_Request::get("user_id", 0));
		$forum_id  = intval(Bingo_Http_Request::get("forum_id", 0));
		$arrParams = array(
			'user_id'  => $user_id,
			'forum_id' => $forum_id,
		);
		Bingo_Log::warning(var_export($arrParams, true));
		$arrRet    = Tieba_Service::call('game', 'delComment', $arrParams, null, null, 'post', 'php', 'utf-8');
		if(false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
			Bingo_Log::warning('service game.delComment error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
			$arrRet['errno'] = isset($arrRet['errno']) ? $arrRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
			
			return Util_Ala_Common::jsonAmisRet($arrRet['errno'], array(), '删除评论失败!');
		}else{
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, array(), '删除评论成功!');
		}
	}
	
}

?>
