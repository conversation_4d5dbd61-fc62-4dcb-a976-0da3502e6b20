<?php

/**
 * Created by PhpStorm.
 * User: zhouyan08
 * Date: 17/10/27
 * Time: 下午4:48
 */
class specialBarAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * 执行函数
	 *
	 * @param null
	 *
	 * @return null
	 * */
	public function _execute()
	{
		//数据获取
		$id                    = intval(Bingo_Http_Request::get('id', 0));
		$ps                    = intval(Bingo_Http_Request::get('page_size', 10));
		$pn                    = intval(Bingo_Http_Request::get('page', 1));
		$demo                  = intval(Bingo_Http_Request::get('demo', ''));
		$forum_id              = intval(Bingo_Http_Request::get('forum_id', ''));
		$game_sdk_id           = intval(Bingo_Http_Request::get('game_sdk_id', ''));
		$method                = strval(Bingo_Http_Request::get('method', ''));
		$game_name             = strval(Bingo_Http_Request::get('game_name', ''));
		$stick_img             = strval(Bingo_Http_Request::get('stick_img', ''));
		$title_big             = strval(Bingo_Http_Request::get('title_big', ''));
		$title_small           = strval(Bingo_Http_Request::get('title_small', ''));
		$gift_link             = strval(Bingo_Http_Request::get('gift_link', ''));
		$try_link              = strval(Bingo_Http_Request::get('try_link', ''));
		$pre_url               = strval(Bingo_Http_Request::get('pre_url', ''));
		$game_name_ios         = strval(Bingo_Http_Request::get('game_name_ios', ''));
		$game_down_url_ios     = strval(Bingo_Http_Request::get('game_down_url_ios', ''));
		$icon_down_ios         = strval(Bingo_Http_Request::get('icon_down_ios', ''));
		$game_down_url_android = strval(Bingo_Http_Request::get('game_down_url_android', ''));
		$icon_down_android     = strval(Bingo_Http_Request::get('icon_down_android', ''));
		$game_name_android     = strval(Bingo_Http_Request::get('game_name_android', ''));
		$activity_ids          = strval(Bingo_Http_Request::get('activity_ids', ''));
		$h5_settings           = Bingo_Http_Request::get('h5_settings', '');
		$op_id                 = Util_User::$intUserId;
		$op_name               = Util_User::$strUserName;
		Bingo_Log::warning(serialize($pre_url));
		if($method == 'add'){
			$arrInput = array(
				'demo'                  => $demo,
				'forum_id'              => $forum_id,
				'game_sdk_id'           => $game_sdk_id,
				'game_name'             => $game_name,
				'stick_img'             => $stick_img,
				'title_big'             => $title_big,
				'title_small'           => $title_small,
				'gift_link'             => $gift_link,
				'try_link'              => $try_link,
				'pre_url'               => explode(',', $pre_url),
				'game_name_ios'         => $game_name_ios,
				'game_down_url_ios'     => $game_down_url_ios,
				'icon_down_ios'         => $icon_down_ios,
				'game_name_android'     => $game_name_android,
				'game_down_url_android' => $game_down_url_android,
				'icon_down_android'     => $icon_down_android,
				'activity_ids'          => explode(',', $activity_ids),
				'h5_settings'           => explode(',', $h5_settings),
				'op_name'               => $op_name,
			);
			//		$arrOutput = Service_Video_VideoSquareActivitySubPage::getVideoSquareActivitySub($arrInput);
			$arrOutput = Tieba_Service::call('game', 'addSpecialGameBarInfo', $arrInput, null, null, 'post', null, 'gbk');
			if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
				$strLog = __CLASS__."::".__FUNCTION__." call ala::getLiveAuditBlack fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
				Bingo_Log::warning($strLog);
				Bingo_Http_Response::contextType('application/json');
				echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL), 'utf-8');
				
				return;
			}
			Bingo_Http_Response::contextType('application/json');
			echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_SUCCESS), 'utf-8');
			
			return;
		}
		if($method == 'delete'){
			$arrInput  = array('id' => $id,);
			$arrOutput = Tieba_Service::call('game', 'deleteSpecialGameBarById', $arrInput, null, null, 'post', null, 'gbk');
			if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
				$strLog = __CLASS__."::".__FUNCTION__." call ala::getLiveAuditBlack fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
				Bingo_Log::warning($strLog);
				Bingo_Http_Response::contextType('application/json');
				echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL), 'utf-8');
				
				return;
			}
			Bingo_Http_Response::contextType('application/json');
			echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_SUCCESS), 'utf-8');
			
			return;
		}
		if($method == 'alter'){
			$arrInput = array(
				'id'                    => $id,
				'demo'                  => $demo,
				'forum_id'              => $forum_id,
				'game_sdk_id'           => $game_sdk_id,
				'game_name'             => $game_name,
				'stick_img'             => $stick_img,
				'title_big'             => $title_big,
				'title_small'           => $title_small,
				'gift_link'             => $gift_link,
				'pre_url'               => explode(',', $pre_url),
				'try_link'              => $try_link,
				'game_name_ios'         => $game_name_ios,
				'game_down_url_ios'     => $game_down_url_ios,
				'icon_down_ios'         => $icon_down_ios,
				'game_name_android'     => $game_name_android,
				'game_down_url_android' => $game_down_url_android,
				'icon_down_android'     => $icon_down_android,
				'activity_ids'          => explode(',', $activity_ids),
				'h5_settings'           => explode(',', $h5_settings),
				'op_name'               => $op_name,
			);
			//		$arrOutput = Service_Video_VideoSquareActivitySubPage::getVideoSquareActivitySub($arrInput);
			$arrOutput = Tieba_Service::call('game', 'updateSpecialGameBar', $arrInput, null, null, 'post', null, 'gbk');
			if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
				$strLog = __CLASS__."::".__FUNCTION__." call ala::getLiveAuditBlack fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
				Bingo_Log::warning($strLog);
				Bingo_Http_Response::contextType('application/json');
				echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL), 'utf-8');
				
				return;
			}
			Bingo_Http_Response::contextType('application/json');
			echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_SUCCESS), 'utf-8');
			
			return;
		}else{
			$arrInput = array(
				'forum_id' => $forum_id,
				'op_name'  => $op_name,
				'pn'       => $pn - 1,
			);
			array_filter($arrInput);
			$arrOutput = Tieba_Service::call('game', 'selectSpecialGameBar', $arrInput, null, null, 'post', null, 'gbk');
			if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
				$strLog = __CLASS__."::".__FUNCTION__." call ala::getLiveAuditBlack fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
				Bingo_Log::warning($strLog);
				Bingo_Http_Response::contextType('application/json');
				echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL), 'utf-8');
				
				return;
			}
			Bingo_Http_Response::contextType('application/json');
			
			return self::resultSender(self::errRet(Alalib_Conf_Error::ERR_SUCCESS, Bingo_Encode::convert(array(
				'rows'  => self::DataFormatter($arrOutput['data']['list']),
				'count' => $arrOutput['data']['count']['total'],
				Bingo_Encode::ENCODE_GBK,
				Bingo_Encode::ENCODE_UTF8,
			))));
		}
		// 默认成功返回值
		//            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, Tieba_Error::getUserMsg(Tieba_Errcode::ERR_SUCCESS), $retData);
	}
	
	/**
	 * 返回值
	 *
	 * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
	 * @param  {Array} $arrOutData default array()
	 * @param  {String} $strMsg default ""
	 *
	 * @return {Array} :errno :errmsg :{Array}output
	 * */
	private static function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS, $arrOutData = array(), $strMsg = "")
	{
		$arrOutput            = array();
		$arrOutput['status']  = $intErrno;
		$arrOutput['msg']     = Alalib_Conf_Error::getErrorMsg($intErrno);
		$arrOutput['usermsg'] = $strMsg;
		$arrOutput['data']    = $arrOutData;
		
		return $arrOutput;
	}
	
	/**
	 * 将sql数据格式转换成amis能接受的数据格式
	 *
	 * @param null
	 *
	 * @return null
	 * */
	private static function DataFormatter($arrInput = array())
	{
		foreach($arrInput as &$item){
			$item['pre_url']      = self::arrayToString(json_decode($item['pre_url'], true));
			$item['h5_settings']  = self::arrayToString(json_decode($item['h5_settings'], true));
			$item['activity_ids'] = self::arrayToString(json_decode($item['activity_ids'], true));
		}
		unset($item);
		
		return $arrInput;
	}
	
	/**
	 * @param $arrInput
	 *
	 * @return string
	 */
	private static function arrayToString($arrInput)
	{
		$strRet = '';
		$i      = 0;
		$count  = count($arrInput);
		while($i < $count){
			$strRet .= strval($arrInput[$i]);
			if($i < $count - 1){
				$strRet .= ",";
			}
			$i++;
		}
		
		return $strRet;
	}
	
	/**
	 * 结果返回函数
	 *
	 * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
	 * @param  {Array} $arrOutData default array()
	 * @param  {String} $strMsg default ""
	 *
	 * @return {Array} :errno :errmsg :{Array}output
	 * */
	private static function resultSender($arrInput, $code_type = 'utf-8')
	{
		echo Bingo_String::array2json($arrInput, $code_type);
		
		return true;
	}
}

?>
