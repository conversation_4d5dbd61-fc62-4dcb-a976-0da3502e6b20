<?php

/**
 * Created by PhpStorm.
 * User: zhouyan08
 * Date: 18/3/21
 * Time: 下午4:48
 */
class getListAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	private $_arrPerm = array();
	private $_intUid = -1;
	private $_word_need = 'content';
	const ERR_PARAM_CHECK_FORUM_ERROR = '检查吧名失败';
	const ERR_PARAM_FORUM_NOT_EXIST_ERROR = '吧名不存在';
	const ERR_PARAM_ERROR = '参数错误';
	const ERR_PARAM_DATE_ERROR = '日期格式错误';
	const ERR_PARAM_EMPTY_ERROR = '参数为空';
	const ERR_PARAM_LEN_ERROR = '参数长度错误';
	const ERR_SERVICE_ERROR = '服务错误';
	const ERR_THREAD_EXIST_ERROR = '贴子删除或者不存在';
	const ERR_SUCCESS_MSG = '恭喜: 操作成功!';
	const ERR_SAME_ERROR = '有重复项';
	const ERR_FOCUS_INDEX_ERROR = '焦点图的排序错误';
	const ERR_NUM_LESS_ERROR = '数量少于%s, 操作失败';
	const ERR_NUM_MORE_ERROR = '数量多于%s, 操作失败';
	const ERR_NUM_ERROR = '焦点图数量大于%s';
	// 频道类目TAB：1:用户管理 2:频道类目管理 3:垂类频道管理 4:权限管理'
	const TAB_CHANNEL_USER_MANAGEMENT = 1;
	const TAB_CHANNEL_CATEGORY = 2;
	const TAB_CHANNEL_MANAGEMENT = 3;
	const TAB_CHANNEL_PERM_MANAGEMENT = 4;
	const DEFAULT_PN = 1;
	const DEFAULT_SZ = 20;
	const DEFAULT_STRING_MAX = 300;
	const FOCUS_MAX_NUM = 4;
	const FOCUS_MIN_NUM = 1;
	const ADMIN_ROLE_ID = 1;
	public static $arrMaxMin = array(
		'rank' => array(
			'max' => 20,
			'min' => 5,
		),
		'tv'   => array(
			'max' => 3,
			'min' => 0,
		),
	);
	
	const BEYOND_TYPE_STAR = 1;
	const BEYOND_TYPE_MOVIE = 2;
	const BEYOND_TYPE_TV = 3;
	const BEYOND_TYPE_SHOW = 4; // 综艺
	const BEYOND_TYPE_ACTIVITY = 5;
	public static $arrValidType = array(
		self::BEYOND_TYPE_STAR     => 1,
		self::BEYOND_TYPE_MOVIE    => 1,
		self::BEYOND_TYPE_TV       => 1,
		self::BEYOND_TYPE_SHOW     => 1,
		self::BEYOND_TYPE_ACTIVITY => 1,
	);
	public static $arrCanHaveFocusNum = array(
		self::BEYOND_TYPE_STAR  => 4,
		self::BEYOND_TYPE_MOVIE => 4,
		self::BEYOND_TYPE_TV    => 4,
		self::BEYOND_TYPE_SHOW  => 4,
		'hot'                   => 4,
	);
	
	const BEYOND_STATUS_AUDIT = 0;
	const BEYOND_STATUS_PASS = 1;
	const BEYOND_STATUS_UNPASS = 2;
	const BEYOND_STATUS_DELETE = 3;
	public static $arrPass2Status = array(
		0 => self::BEYOND_STATUS_UNPASS,
		1 => self::BEYOND_STATUS_PASS,
		2 => self::BEYOND_STATUS_PASS,
	);
	
	public static $arrValidStatus = array(
		self::BEYOND_STATUS_AUDIT  => 1,
		self::BEYOND_STATUS_PASS   => 1,
		self::BEYOND_STATUS_UNPASS => 1,
		self::BEYOND_STATUS_DELETE => 1,
	);
	
	const BEYOND_SUBTYPE_FEED = 0;
	const BEYOND_SUBTYPE_FOCUS = 1;
	const BEYOND_SUBTYPE_SPECIAL_ACTIVITY = 2;
	public static $arrValidSubType = array(
		self::BEYOND_SUBTYPE_FEED             => 1,
		self::BEYOND_SUBTYPE_FOCUS            => 1,
		self::BEYOND_SUBTYPE_SPECIAL_ACTIVITY => 1,
	);
	
	const BEYOND_USER_TYPE_MIS = 1;
	const BEYOND_USER_TYPE_BAIDU = 0;
	
	public static $arrValidUserType = array(
		self::BEYOND_USER_TYPE_MIS   => 1,
		self::BEYOND_USER_TYPE_BAIDU => 1,
	);
	
	const BEYOND_AUDIT_UNPASS = 0;
	const BEYOND_AUDIT_PASS = 1;
	const BEYOND_AUDIT_RECOMMENTED = 2;
	
	public static $arrValidPassParam = array(
		self::BEYOND_AUDIT_UNPASS      => 1,
		self::BEYOND_AUDIT_PASS        => 1,
		self::BEYOND_AUDIT_RECOMMENTED => 1,
	);
	
	/**
	 * @param  [int]   错误码
	 * @param  [str]   错误信息
	 *
	 * @return [array] 返回数组
	 */
	public static function getErrRet($intErrNo, $strMsg = '')
	{
		$arrErrRet        = array(
			'no' => $intErrNo,
		);
		$strMsg           = Bingo_Encode::convert($strMsg, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
		$arrErrRet['msg'] = empty($strMsg) ? Tieba_Error::getUserMsg($intErrNo) : $strMsg;
		
		return $arrErrRet;
	}
	
	/**
	 * @param  [int]  错误码
	 * @param  string 错误信息
	 * @param  array  返回数据
	 *
	 * @return [bool]
	 */
	public static function outputJson($intErrNo, $strErrMsg = '', $arrOutput = array())
	{
		$arrRet         = self::getErrRet($intErrNo, $strErrMsg);
		$arrRet['data'] = $arrOutput;
		echo Bingo_String::array2json($arrRet);
		//return true;
	}
	
	/**
	 * @param  [array] 返回数组
	 *
	 * @return [bool]
	 */
	public static function output($arrRet)
	{
		echo Bingo_String::array2json($arrRet);
		//return true;
	}
	
	/**
	 * @param  str $strDate
	 *
	 * @return bool
	 */
	public static function checkDate($strDate)
	{
		$intTime = strtotime($strDate);
		$newData = date('Y-m-d', $intTime);
		if($strDate == $newData){
			return true;
		}else{
			return false;
		}
	}
	
	/**
	 * 执行函数
	 *
	 * @param null
	 *
	 * @return null
	 * */
	public function _execute()
	{
		$this->_intUid    = Util_User::$intUserId;
		$this->_word_need = strval(Bingo_Http_Request::get("type", 'content'));
		self::_buildUserPerm();
		$arrTpl = $this->_buildResult();
		
		return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, $arrTpl, 'success');
	}
	
	/**
	 * @param null
	 *
	 * @return null
	 * */
	private function _buildUserPerm()
	{
		// get role id
		$arrGetInput = array(
			'user_id' => (int)$this->_intUid,
		);
		$arrGetRes   = Tieba_Service::call('beyond', 'getRoleByUid', $arrGetInput, null, null, 'post', 'php', 'utf-8');
		if(false === $arrGetRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetRes['errno']){
			Bingo_Log::warning("call service beyond/getRoleByUid failed. input:".serialize($arrGetInput)."_output:".serialize($arrGetRes));
			//return false;
		}
		if(0 == count($arrGetRes['data'])){
			//return true;
		}
		$intRoleId       = self::ADMIN_ROLE_ID;
		$arrUserTids     = array();
		$arrCategoryTids = array();
		if(self::ADMIN_ROLE_ID != $intRoleId){
			// get perm by role id
			$arrGetInput = array(
				'role_ids' => array(
					$intRoleId,
				),
			);
			$arrGetRes   = Tieba_Service::call('beyond', 'mgetRolePermByRids', $arrGetInput, null, null, 'post', 'php', 'utf-8');
			if(false === $arrGetRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetRes['errno']){
				Bingo_Log::warning("call service beyond/mgetRolePermByRids failed. input:".serialize($arrGetInput)."_output:".serialize($arrGetRes));
				
				return false;
			}
			$arrRes = Bingo_Encode::convert($arrGetRes['data'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
			foreach($arrRes as $value){
				$intType = (int)$value['type'];
				$intId   = (int)$value['source_id'];
				switch($intType){
					case self::TAB_CHANNEL_USER_MANAGEMENT:
						$arrUserTids[]                  = $intId;
						$this->_arrPerm['user'][$intId] = array(
							'id'   => $intId,
							'name' => '',
						);
						break;
					case self::TAB_CHANNEL_CATEGORY:
						$arrCategoryTids[]                 = $intId;
						$this->_arrPerm['content'][$intId] = array(
							'id'   => $intId,
							'name' => '',
						);
						break;
					case self::TAB_CHANNEL_MANAGEMENT:
						$this->_arrPerm['channel'] = (int)$value['status'];
						break;
					case self::TAB_CHANNEL_PERM_MANAGEMENT:
						$this->_arrPerm['perm'] = (int)$value['status'];
						break;
					default:
						break;
				}
			}
		}else{
			$this->_arrPerm = array(
				'user'    => array(),
				'content' => array(),
				'channel' => 1,
				'perm'    => 1,
			);
			$arrRes         = Tieba_Service::call('beyond', 'getRolePermTemplate', array(), null, null, 'post', 'php', 'utf-8');
			if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
				Bingo_Log::warning("call service beyond/getRolePermTemplate failed. output: ".serialize($arrRes));
				
				return false;
			}
			$arrResData    = Bingo_Encode::convert($arrRes['data'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
			$arrChannelIds = array();
			foreach($arrResData['user'] as $value){
				$intChannelId                   = (int)$value['channel_id'];
				$arrChannelIds[]                = $intChannelId;
				$intId                          = (int)$value['id'];
				$arrUserTids[]                  = $intId;
				$this->_arrPerm['user'][$intId] = array(
					'id'        => $intId,
					'channelId' => $intChannelId,
					'name'      => "error",
					'status'    => 1,
				);
			}
			if(count($arrChannelIds) > 0){
				// get channel_name
				$arrGetInput = array(
					'channel_ids' => $arrChannelIds,
				);
				$arrRes      = Tieba_Service::call('beyond', 'mgetChannelInfoByIds', $arrGetInput, null, null, 'post', 'php', 'utf-8');
				if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
					Bingo_Log::warning("call service beyond/mgetChannelInfoByIds failed. output: ".serialize($arrRes));
					
					return false;
				}
				$arrChannelInfo = array();
				$arrRes['data'] = Bingo_Encode::convert($arrRes['data'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
				foreach($arrRes['data'] as $key => $value){
					$intChannelId                          = (int)$value['id'];
					$arrChannelInfo[$intChannelId]['name'] = (string)$value['name'];
				}
				foreach($this->_arrPerm['user'] as $key => $value){
					$intChannelId                         = $value['channelId'];
					$this->_arrPerm['user'][$key]['name'] = $arrChannelInfo[$intChannelId]['name'];
				}
			}
			foreach($arrResData['category'] as $value){
				$intId                             = (int)$value['id'];
				$arrCategoryTids[]                 = $intId;
				$this->_arrPerm['content'][$intId] = array(
					'id'        => $intId,
					'channelId' => (int)$value['channel_id'],
					'name'      => (string)$value['name'],
					'status'    => 1,
				);
			}
		}
		$objRalMulti   = new Tieba_Multi('user_role_perm');
		$arrMultiInput = array();
		$arrKeys       = array();
		if(count($arrUserTids) > 0){
			$arrKeys[]     = "channel_info";
			$arrMultiInput = array(
				'serviceName' => 'beyond',
				'method'      => 'mgetChannelInfoByUserTabIds',
				'input'       => array('id' => $arrUserTids),
				'ie'          => 'utf-8',
			);
			$objRalMulti->register('channel_info', new Tieba_Service('user_role_perm'), $arrMultiInput);
		}
		if(count($arrCategoryTids) > 0){
			$arrKeys[]     = "category_info";
			$arrMultiInput = array(
				'serviceName' => 'beyond',
				'method'      => 'mgetCategoryInfoByCateIds',
				'input'       => array('id' => $arrCategoryTids),
				'ie'          => 'utf-8',
			);
			$objRalMulti->register('category_info', new Tieba_Service('user_role_perm'), $arrMultiInput);
		}
		if(count($arrKeys) > 0){
			$arrResult      = $objRalMulti->call();
			$arrChannelName = array();
			foreach($arrKeys as $key){
				$arrResult = $objRalMulti->getResult($key);
				if(false === $arrResult || Tieba_Errcode::ERR_SUCCESS !== $arrResult['errno']){
					continue;
				}else{
					//$arrResult['data'] = Bingo_Encode::convert($arrResult['data'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
					switch($key){
						case "channel_info":
							foreach($arrResult['data'] as $value){
								$intId                                       = (int)$value['id'];
								$this->_arrPerm['user'][$intId]['channelId'] = (int)$value['channel_id'];
								$this->_arrPerm['user'][$intId]['name']      = (string)$value['channel_info']['name'];
								$arrChannelName[$value['channel_id']]        = (string)$value['channel_info']['name'];
							}
							break;
						case "category_info":
							foreach($arrResult['data'] as $value){
								$intId                                          = (int)$value['id'];
								$this->_arrPerm['content'][$intId]['name']      = (string)$value['name'];
								$this->_arrPerm['content'][$intId]['channelId'] = (int)$value['channel_id'];
								$this->_arrPerm['content'][$intId]['cateId']    = (int)$value['id'];
								$this->_arrPerm['content'][$intId]['template']  = (int)$value['sub_type'];
							}
							break;
						default:
							break;
					}
				}
			}
			foreach($this->_arrPerm['content'] as $id => $content){
				$this->_arrPerm['content'][$id]['name'] = $arrChannelName[$content['channelId']]."@".$content['name'];
			}
		}
	}
	
	/**
	 * @param null
	 *
	 * @return null
	 * */
	private function _buildResult()
	{
		$arrBuildPerm = $this->_arrPerm;
		if($this->_word_need == 'content'){
			if(isset($arrBuildPerm['content'])){
				$arrBuildPerm['options'] = array_values($arrBuildPerm['content']);
				foreach($arrBuildPerm['options'] as &$option){
					$option['value']    = $option['cateId'];
					$option['label']    = $option['name'];
					$option['disabled'] = false;
				}
				unset($arrBuildPerm['user']);
				unset($arrBuildPerm['content']);
			}
		}else{
			if(isset($arrBuildPerm['user'])){
				$arrBuildPerm['options'] = array_values($arrBuildPerm['user']);
				foreach($arrBuildPerm['options'] as &$option){
					$option['value']    = $option['channelId'];
					$option['label']    = $option['name'];
					$option['disabled'] = false;
				}
				unset($arrBuildPerm['user']);
				unset($arrBuildPerm['content']);
			}
		}
		
		return $arrBuildPerm;
	}
}

?>
