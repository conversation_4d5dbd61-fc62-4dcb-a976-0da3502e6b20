<?php

/**
 * Created by PhpStorm.
 * User: zhouyan08
 * Date: 18/3/21
 * Time: 下午4:48
 */
class infoAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * 执行函数
	 *
	 * @param null
	 *
	 * @return null
	 * */
	public function _execute()
	{
		$id             = intval(Bingo_Http_Request::get("id", 0));
		$only_need_cate = intval(Bingo_Http_Request::get("chooseCate", 0));
		if($id <= 0){
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), "错误的入参!");
		}
		$arrInput  = array(
			'id' => $id,
		);
		$arrOutput = Tieba_Service::call('beyond', 'getChannelInfoById', $arrInput, null, null, 'post', 'php', 'utf-8');
		if(false === $arrOutput){
			Bingo_Log::warning("call service beyond/getChannelInfoById failed. input: ".serialize($arrInput)." output: ".serialize($arrOutput));
			
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), "call service beyond/getChannelInfoById failed");
		}
		$ret = self::_buildData($arrOutput['data']);
		if($only_need_cate == 1){
			$ret = $ret['category'];
		}
		else{
			//unset($ret['category']);
		}
		
		return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, $ret, 'success');
	}
	
	/**
	 * @param $arrInput
	 *
	 * @return array
	 */
	private static function makeRet($arrInput)
	{
		$ret = array(
			'rows'  => $arrInput['list'],
			'count' => $arrInput['page']['totalData'],
		);
		
		return $ret;
	}
	
	/**
	 * @param  array $arrForumIds
	 *
	 * @return array
	 */
	private function _getFnameByFid($arrForumIds)
	{
		$arrResult = array();
		$arrParam  = array(
			'forum_id' => $arrForumIds,
		);
		$arrRes    = Tieba_Service::call('forum', 'getFnameByFid', $arrParam, null, null, 'post', 'php', 'utf-8');
		if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
			return $arrResult;
		}
		foreach($arrRes['forum_name'] as $value){
			$arrResult[$value['forum_id']] = $value['forum_name'];
		}
		
		return $arrResult;
	}
	
	/**
	 * @param array $arrInput
	 *
	 * @return array
	 */
	private function _buildChannelInfo($arrInput = array())
	{
		$cateId = -1;
		$pageId = -1;
		$itemId = -1;
		if($arrInput['id'] == 1){
			//娱乐频道
			$pageId = '10001001';
			$itemId = '10001001';
		}else{
			if(count($arrInput['category']) <= 0){
				Bingo_Log::warning("The channel category info is null ".serialize($arrInput));
			}else{
				$categoryInfo = $arrInput['category'][0];
				$cateId       = $categoryInfo['id'];
				if($categoryInfo['type'] == 0){
					//静态页面
					$pageId = '90001';
					//通过静态url获取itemId
					$query      = substr(strrchr($categoryInfo['ext_info']['url'], "?"), 1);
					$queryParts = explode('&', $query);
					foreach($queryParts as $param){
						$item = explode('=', $param);
						if($item[0] == 'item_id'){
							$itemId = $item[1];
							break;
						}
					}
				}else if($categoryInfo['type'] == 1){
					//动态模板
					if($categoryInfo['sub_type'] == 1){
						//热点
						$pageId = '10001200';
					}else if($categoryInfo['sub_type'] == 2){
						//普通
						$pageId = '10001201';
					}else if($categoryInfo['sub_type'] == 3){
						//活动
						$pageId = '10001202';
					}else{
						Bingo_Log::warning("The channel category sub_type error ".serialize($categoryInfo));
					}
					$itemId = $arrInput['id'].'_'.$categoryInfo['id'];
				}else{
					Bingo_Log::warning('category type is error '.json_encode($categoryInfo));
				}
			}
		}
		$arrList = array(
			'id'     => (int)$arrInput['id'],
			'name'   => (string)$arrInput['name'],
			'logo'   => (string)$arrInput['ext_info']['logo'],
			'status' => (int)$arrInput['status'],
			'cateId' => $cateId,
			'pageId' => $pageId,
			'itemId' => $itemId,
		);
		
		return $arrList;
	}
	
	/**
	 * @param array $arrInput
	 *
	 * @return array
	 */
	private function _buildData($arrInput = array())
	{
		$arrChannelInfo = $arrInput;
		// 频道信息
		$arrOutput = array(
			'id'       => (int)$arrChannelInfo['id'],
			'name'     => (string)$arrChannelInfo['name'],
			'logo'     => $arrChannelInfo['ext_info']['logo'],
			'forum'    => "",
			'category' => array(),
		);
		// 类目信息
		foreach($arrChannelInfo['category'] as $value){
			$arrOutput['category'][] = array(
				'id'       => (int)$value['id'],
				'name'     => (string)$value['name'],
				'type'     => (int)$value['type'],
				'template' => (int)$value['sub_type'],
				'url'      => (string)$value['ext_info']['url'],
				'icon'     => (string)$value['ext_info']['icon'],
			);
		}
		// 吧目录信息
		$strForumDir = "";
		foreach($arrChannelInfo['forum'] as $value){
			if($value['forum_id'] > 0){
				$forumName     = self::_getFnameByFid(array($value['forum_id']));
				$arrForumDir[] = $forumName[$value['forum_id']];
			}else{
				$strLevel1Name = (string)$value['level_1_name'];
				$strLevel2Name = (string)$value['level_2_name'];
				if("" == $strLevel2Name){
					$strTmp = $strLevel1Name;
				}else{
					$strTmp = $strLevel1Name."/".$strLevel2Name;
				}
				if("" == $strForumDir){
					$strForumDir = $strTmp;
				}else{
					$strForumDir = $strForumDir.", ".$strTmp;
				}
			}
		}
		if(count($arrForumDir) > 0){
			$strForumDir = implode(",", $arrForumDir);
		}
		$arrOutput['forum'] = $strForumDir;
		
		return $arrOutput;
	}
	
}

?>
