<?php

/**
 * Created by PhpStorm.
 * User: zhouyan08
 * Date: 18/3/21
 * Time: 下午4:48
 */
class indexAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * 执行函数
	 *
	 * @param null
	 *
	 * @return null
	 * */
	public function _execute()
	{
		$pn = intval(Bingo_Http_Request::get("page", 1));
		if($pn <= 0){
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), "错误的页码!");
		}
		$ps        = 10;
		$kw        = strval(Bingo_Http_Request::get("kw", ''));
		$arrInput  = array(
			'pn' => $pn,
			'sz' => $ps,
			'kw' => $kw,
		);
		$arrOutput = Tieba_Service::call('beyond', 'getChannelList', $arrInput, null, null, 'post', 'php', 'utf-8');
		if(false === $arrOutput){
			Bingo_Log::warning("call service beyond/getChannelList failed. input: ".serialize($arrInput)." output: ".serialize($arrOutput));
			
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), "call service beyond/getChannelList failed");
		}
		$arrBuildData         = $this->_buildData($arrOutput['data']);
		$arrBuildData         = self::makeRet($arrBuildData);
		$arrBuildData['rows'] = array_slice($arrBuildData['rows'], ($pn - 1) * $ps, $ps);
		
		return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, $arrBuildData, 'success');
	}
	
	/**
	 * @param $arrInput
	 *
	 * @return array
	 */
	private static function makeRet($arrInput)
	{
		$ret = array(
			'rows'  => $arrInput['list'],
			'count' => $arrInput['page']['totalData'],
		);
		
		return $ret;
	}
	
	/**
	 * @param null
	 *
	 * @return null
	 * */
	private function _buildData($arrInput = array())
	{
		//$arrChannelData = Bingo_Encode::convert($arrInput, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
		$arrChannelData = $arrInput;
		$arrList        = array();
		$index          = 0;
		foreach($arrChannelData['list'] as $key => $value){
			// 频道信息
			$arrList[$index] = self::_buildChannelInfo($value);
			// 类目信息
			$arrCategory = "";
			foreach($value['category'] as $subKey => $subVal){
				$arrCategory[] = (string)$subVal['name'];
				/*
				$arrList[$index]['category'][] = array(
					'id'          => (int)$subVal['id'],
					'name'        => (string)$subVal['name'],
					'status'      => (int)$subVal['status'],
					'url'         => (string)$subVal['ext_info']['url'],
					'icon'        => (string)$subVal['ext_info']['icon'],
				);
				*/
			}
			$arrList[$index]['category'] = implode(",", $arrCategory);
			// 吧目录信息
			$arrForumDir = array();;
			foreach($value['forum'] as $val){
				if($val['forum_id'] > 0){
					$forumName     = self::_getFnameByFid(array($val['forum_id']));
					$arrForumDir[] = $forumName[$val['forum_id']];
				}else{
					$strLevel1Name = (string)$val['level_1_name'];
					$strLevel2Name = (string)$val['level_2_name'];
					if("" == $strLevel2Name){
						$arrForumDir[] = $strLevel1Name;
					}else{
						$arrForumDir[] = $strLevel1Name."/".$strLevel2Name;
					}
				}
			}
			$arrList[$index]['forum'] = implode(",", $arrForumDir);
			$index++;
		}
		$arrOutput = array(
			'list' => $arrList,
			'page' => $arrChannelData['page'],
		);
		
		return $arrOutput;
	}
	
	/**
	 * @param  array $arrForumIds
	 *
	 * @return array
	 */
	private function _getFnameByFid($arrForumIds)
	{
		$arrResult = array();
		$arrParam  = array(
			'forum_id' => $arrForumIds,
		);
		$arrRes    = Tieba_Service::call('forum', 'getFnameByFid', $arrParam, null, null, 'post', 'php', 'utf-8');
		if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
			return $arrResult;
		}
		foreach($arrRes['forum_name'] as $value){
			$arrResult[$value['forum_id']] = $value['forum_name'];
		}
		
		return $arrResult;
	}
	
	/**
	 * @param array $arrInput
	 *
	 * @return array
	 */
	private function _buildChannelInfo($arrInput = array())
	{
		$cateId = -1;
		$pageId = -1;
		$itemId = -1;
		if($arrInput['id'] == 1){
			//娱乐频道
			$pageId = '10001001';
			$itemId = '10001001';
		}else{
			if(count($arrInput['category']) <= 0){
				Bingo_Log::warning("The channel category info is null ".serialize($arrInput));
			}else{
				$categoryInfo = $arrInput['category'][0];
				$cateId       = $categoryInfo['id'];
				if($categoryInfo['type'] == 0){
					//静态页面
					$pageId = '90001';
					//通过静态url获取itemId
					$query      = substr(strrchr($categoryInfo['ext_info']['url'], "?"), 1);
					$queryParts = explode('&', $query);
					foreach($queryParts as $param){
						$item = explode('=', $param);
						if($item[0] == 'item_id'){
							$itemId = $item[1];
							break;
						}
					}
				}else if($categoryInfo['type'] == 1){
					//动态模板
					if($categoryInfo['sub_type'] == 1){
						//热点
						$pageId = '10001200';
					}else if($categoryInfo['sub_type'] == 2){
						//普通
						$pageId = '10001201';
					}else if($categoryInfo['sub_type'] == 3){
						//活动
						$pageId = '10001202';
					}else{
						Bingo_Log::warning("The channel category sub_type error ".serialize($categoryInfo));
					}
					$itemId = $arrInput['id'].'_'.$categoryInfo['id'];
				}else{
					Bingo_Log::warning('category type is error '.json_encode($categoryInfo));
				}
			}
		}
		$arrList = array(
			'id'     => (int)$arrInput['id'],
			'name'   => (string)$arrInput['name'],
			'logo'   => (string)$arrInput['ext_info']['logo'],
			'status' => (int)$arrInput['status'],
			'cateId' => $cateId,
			'pageId' => $pageId,
			'itemId' => $itemId,
		);
		
		return $arrList;
	}
}

?>
