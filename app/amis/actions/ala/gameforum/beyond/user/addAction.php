<?php

/**
 * Created by PhpStorm.
 * User: zhouyan08
 * Date: 18/3/21
 * Time: 下午4:48
 */
class addAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * 执行函数
	 *
	 * @param null
	 *
	 * @return null
	 * */
	public function _execute()
	{
		$user_name = strval(Bingo_Http_Request::get("user_name", ''));
		$show_name = strval(Bingo_Http_Request::get("show_name", ''));
		$role_id   = intval(Bingo_Http_Request::get("role_id", 0));
		$op_id     = Util_User::$intUserId;
		$op_name   = Util_User::$strUserName;
		$arrInput  = array(
			'user_name' => $user_name,
			'show_name' => $show_name,
			'role_id'   => $role_id,
			'op_uname'  => $op_name,
			'op_uid'    => $op_id,
		);
		$arrOutput = Tieba_Service::call('beyond', 'addUserRole', $arrInput, null, null, 'post', 'php', 'utf-8');
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
			Bingo_Log::warning("call service beyond/updateUserRole failed. input: ".serialize($arrInput)." output: ".serialize($arrOutput));
			
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), "call service beyond/addUserRole failed");
		}else{
			return Util_Ala_Common::jsonAmisRet($arrOutput['errno'], array(), $arrOutput['errmsg']);
		}
	}
	
	/**
	 * @param $arrInput
	 *
	 * @return array
	 */
	private static function makeRet($arrInput)
	{
		$ret = array(
			'rows'  => $arrInput['list'],
			'count' => $arrInput['page']['totalData'],
		);
		
		return $ret;
	}
	
	/**
	 * @param null
	 *
	 * @return null
	 * */
	private function _buildData($arrUserList, $arrRoleList)
	{
		//$arrUserList = Bingo_Encode::convert($arrUserList, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
		//$arrRoleList = Bingo_Encode::convert($arrRoleList, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
		// 角色信息
		$arrBuildRoleInfo = array();
		$arrTmp           = array();
		foreach($arrRoleList as $key => $value){
			$intRoleId          = (int)$value['id'];
			$arrBuildRoleInfo[] = array(
				'id'   => $intRoleId,
				'name' => (string)$value['name'],
			);
			$arrTmp[$intRoleId] = (string)$value['name'];
		}
		// 用户信息
		$arrBuildUserInfo = array();
		foreach($arrUserList['list'] as $key => $value){
			$intRoleId          = (int)$value['role_id'];
			$arrBuildUserInfo[] = array(
				'id'       => (int)$value['id'],
				'userId'   => (string)$value['user_name'],
				'name'     => (string)$value['show_name'],
				'roleId'   => $intRoleId,
				'roleName' => $arrTmp[$intRoleId],
			);
		}
		$arrOutput = array(
			'list'     => $arrBuildUserInfo,
			'page'     => $arrUserList['page'],
			'roleList' => $arrBuildRoleInfo,
		);
		
		return $arrOutput;
	}
}

?>
