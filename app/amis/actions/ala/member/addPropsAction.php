<?php
/**
 * addPropsAction.php 补发道具
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 18/11/13 下午4:54
*/

class addPropsAction extends Util_Action
{
    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = true;

    protected $bolOnlyInner = true;

    protected $bolNeedLogin = true;

    const TBMALL_APPRAISE1_PROPS = 1070001;
    const TBMALL_APPRAISE2_PROPS = 1070002;
    const TBMALL_SIGN_PROPS = 1080001;
    const TBMALL_SIGN_CONTINUOUS_PROPS = 1080002;
    const TBMALL_REBUILDNAMEPLATE_PROPS = 1140001;
    const TBMALL_SAVEFASE1_PROPS = 1170001;
    const TBMALL_SAVEFASE2_PROPS = 1170002;
    const TBMALL_SAVEFASE3_PROPS = 1170003;
    const TBMALL_SAVEFASE4_PROPS = 1170004;
    const TBMALL_SAVEFASE5_PROPS = 1170005;
    const TBMALL_MEMBERTOP_PROPS = 1180001;

    protected static $arrPropsTitle = array(
        self::TBMALL_APPRAISE1_PROPS        => '膜拜',
        self::TBMALL_APPRAISE2_PROPS        => '魔蛋',
        self::TBMALL_SIGN_PROPS             => '补签卡',
        self::TBMALL_SIGN_CONTINUOUS_PROPS  => '连续补签卡',
        self::TBMALL_REBUILDNAMEPLATE_PROPS => '铭牌重铸卡',
        self::TBMALL_SAVEFASE1_PROPS        => '破旧的挽尊卡',
        self::TBMALL_SAVEFASE2_PROPS        => '普通的挽尊卡',
        self::TBMALL_SAVEFASE3_PROPS        => '闪光的挽尊卡',
        self::TBMALL_SAVEFASE4_PROPS        => '耀眼的挽尊卡',
        self::TBMALL_SAVEFASE5_PROPS        => '神圣的挽尊卡',
    );

    /**
     * 执行函数
     * @param  null
     * @return bool
     */
    public function _execute(){
        // 操作人
        $intUserId = Util_User::$intUserId;
        $strUserName = Util_User::$strUserName;

        $intAddUserId = (int)Bingo_Http_Request::get('user_id', 0);
        $intPropsId   = (int)Bingo_Http_Request::get('props_id', 1080001);
        $intNum       = (int)Bingo_Http_Request::get('num', 0);
        $strNote      = (string)Bingo_Http_Request::get('note', '');

        if (!$intAddUserId) {
            return self::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), '请填写用户id');
        }

        if (!$intPropsId) {
            return self::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), '请选择道具类型');
        }

        if (!$intNum) {
            return self::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), '请填写道具数量');
        }

        // http://nj.service.tieba.baidu.com/service/tbmall?method=buyOneProps&user_id=3129272597&props_id=1080001&buy_num=1&props_scores=0&format=json
        $arrInput = array(
            'user_id'  => $intAddUserId,
            'props_id' => $intPropsId,
            'buy_num'  => $intNum,
            'props_scores' => 0,
        );

        $strServiceName = 'tbmall';
        $strMethodName  = 'buyOneProps';

        $arrOut = Tieba_Service::call($strServiceName, $strMethodName, $arrInput, null, null, 'post', 'php', 'utf-8');

        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . " call $strServiceName $strMethodName fail, input: [".json_encode($arrInput).'], output: ['.json_encode($arrOut)."]");
            return self::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '调用添加道具服务失败，请手动查询是否发放成功，防止重复发放');
        }

        // 记录操作
        $strMemberTypeStr = isset(self::$arrPropsTitle[$intPropsId]) ? self::$arrPropsTitle[$intPropsId] : '道具id:'.$intPropsId;

        $strContent = "$strUserName 为 user_id : $intAddUserId 补发了 $intNum 个的 $strMemberTypeStr ，备注: $strNote";

        $arrInput = array(
            'user_id' => $intAddUserId,
            'type'    => Lib_Ala_Define_Op::OP_TYPE_MEMBER,
            'content' => $strContent,
        );

        $arrOutput = Service_Ala_Log_Op::addLog($arrInput);

        if ($arrOutput === false || (isset($arrOutput['errno']) && intval($arrOutput['errno']) !== 0)) {
            Bingo_Log::warning(__FUNCTION__ . " call service Service_Ala_Log_Op::addLog, input:" . serialize($arrInput) . " output:" . serialize($arrOutput));
        }

        return self::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param  $intErrno
     * @param  array $arrExtData
     * @param  string $strMsg
     * @return bool
     */
    public static function jsonAmisRet($intErrno, $arrExtData = array(), $strMsg = ''){
        $strMsg = $strMsg ? $strMsg : Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput = array(
            'status' => $intErrno,
            'msg'    => $strMsg,
            'data'   => $arrExtData,
        );
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrOutput, Bingo_Encode::ENCODE_UTF8);
        return true;
    }

}