<?php
/**
 * addMemberAction.php 补发会员
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 18/10/19 下午3:13
*/

class addMemberAction extends Util_Action
{
    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = true;

    protected $bolOnlyInner = true;

    protected $bolNeedLogin = true;

    /**
     * 执行函数
     * @param  null
     * @return bool
     */
    public function _execute(){
        // 操作人
        $intUserId = Util_User::$intUserId;
        $strUserName = Util_User::$strUserName;

        $intAddUserId = (int)Bingo_Http_Request::get('user_id', 0);
        $intPropsId   = (int)Bingo_Http_Request::get('props_id', 0);
        $intNum       = (int)Bingo_Http_Request::get('num', 0);
        $strNote      = (string)Bingo_Http_Request::get('note', '');

        if (!$intAddUserId) {
            return self::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), '请填写用户id');
        }

        if (!$intPropsId) {
            return self::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), '请选择会员类型');
        }

        if (!$intNum) {
            return self::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), '请填写月份');
        }

        // http://nj.service.tieba.baidu.com/service/tbmall?method=buyOneProps&user_id=3129272597&props_id=1050002&buy_num=1&props_scores=0&format=json
        $arrInput = array(
            'user_id'  => $intAddUserId,
            'props_id' => $intPropsId,
            'buy_num'  => $intNum,
            'props_scores' => 0,
        );

        $strServiceName = 'tbmall';
        $strMethodName  = 'buyOneProps';

        $arrOut = Tieba_Service::call($strServiceName, $strMethodName, $arrInput, null, null, 'post', 'php', 'utf-8');

        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . " call $strServiceName $strMethodName fail, input: [".json_encode($arrInput).'], output: ['.json_encode($arrOut)."]");
            return self::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '调用添加会员服务失败，请手动查询是否发放成功，防止重复发放');
        }

        // 记录操作
        $strMemberTypeStr = $intPropsId == 1050002 ? '超级会员' : '普通会员';

        $strContent = "$strUserName 为 user_id : $intAddUserId 补发了 $intNum 个月的 $strMemberTypeStr ，备注: $strNote";

        $arrInput = array(
            'user_id' => $intAddUserId,
            'type'    => Lib_Ala_Define_Op::OP_TYPE_MEMBER,
            'content' => $strContent,
        );

        $arrOutput = Service_Ala_Log_Op::addLog($arrInput);

        if ($arrOutput === false || (isset($arrOutput['errno']) && intval($arrOutput['errno']) !== 0)) {
            Bingo_Log::warning(__FUNCTION__ . " call service Service_Ala_Log_Op::addLog, input:" . serialize($arrInput) . " output:" . serialize($arrOutput));
        }

        return self::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param  $intErrno
     * @param  array $arrExtData
     * @param  string $strMsg
     * @return bool
     */
    public static function jsonAmisRet($intErrno, $arrExtData = array(), $strMsg = ''){
        $strMsg = $strMsg ? $strMsg : Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput = array(
            'status' => $intErrno,
            'msg'    => $strMsg,
            'data'   => $arrExtData,
        );
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrOutput, Bingo_Encode::ENCODE_UTF8);
        return true;
    }

}