<?php

/**
 * Created by PhpStorm.
 * User: caowu
 * Date: 19/1/8
 * Time: 下午3:21
 */
class dealLiveappNoticeAction extends Util_Action {

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = true;

    const NOTICE_STATUS_ONLINE = 1;
    const NOTICE_STATUS_OFFLINE = 2;
    const NOTICE_STATUS_DELETE = 3;

    /**
     * @return bool
     */
    public function _execute() {
        // 操作人
        $intOwnUserId = Util_User::$intUserId;
        $strOwnUserName = Util_User::$strUserName;
        $strMethod = Bingo_Http_Request::get('method', '');

        //获取参数
        $strMethod = Bingo_Http_Request::getNoXssSafe('method','');

        if('add' == $strMethod) {
            $strTitle = strval(Bingo_Http_Request::get('title', ''));
            $strAbstract = strval(Bingo_Http_Request::get('abstract', ''));
            $strPicUrl = strval(Bingo_Http_Request::get('pic_url', ''));
            $strJumpUrl = strval(Bingo_Http_Request::get('jump_url', ''));
            $strToUserIds = strval(Bingo_Http_Request::get('to_user_ids', ''));
            $intStartTime = intval(Bingo_Http_Request::get('start_time', 0));
            $intEndTime = intval(Bingo_Http_Request::get('end_time', 0));

            if(empty($strTitle) || empty($strAbstract) || empty($strPicUrl) || empty($strJumpUrl) || empty($intStartTime) || empty($intEndTime)) {
                Bingo_Log::warning("error params!");
                return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
            }

            if($intStartTime >= $intEndTime) {
                Bingo_Log::warning("error params!");
                return Util_Ala_Common::jsonAmisRetMsg(Alalib_Conf_Error::ERR_PARAM_ERROR, "开始时间大于结束时间");
            }

            $arrToUserIds = preg_split("/( |\n|\r|,)/", $strToUserIds);
            $arrToUserIds = array_values(array_unique(array_filter($arrToUserIds)));
            if(count($arrToUserIds) > 100) {
                Bingo_Log::warning("error params!");
                return Util_Ala_Common::jsonAmisRetMsg(Alalib_Conf_Error::ERR_PARAM_ERROR, "配置的用户id数量不能大于100个!");
            }
            foreach ($arrToUserIds as $value) {
                if(!is_numeric($value)) {
                    Bingo_Log::warning("error params!");
                    return Util_Ala_Common::jsonAmisRetMsg(Alalib_Conf_Error::ERR_PARAM_ERROR, "配置的用户id有非数字的!");
                }
            }

            $arrServiceInput = array(
                'title'   => $strTitle,
                'abstract' => $strAbstract,
                'pic_url'   => $strPicUrl,
                'jump_url'  => $strJumpUrl,
                'to_user_ids' => implode(' ', $arrToUserIds),
                'start_time'   => $intStartTime,
                'end_time'    => $intEndTime,
                'status' => self::NOTICE_STATUS_ONLINE,
                'final_op_time'  => time(),
                'operator'   => $strOwnUserName,
            );

            $arrRet = Dl_Ala_Liveapp_Liveapp::addLiveAppNoticeToDb($arrServiceInput);
            if(false === $arrRet || Alalib_Conf_Error::ERR_SUCCESS != $arrRet['errno']) {
                $strLog = __CLASS__."::".__FUNCTION__. " call ala service::addLiveAppNoticeToDb fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrRet)."]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
            }
            return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_SUCCESS);
        }

        else if('update' == $strMethod) {
            $intId = intval(Bingo_Http_Request::get('id', 0));
            $strTitle = strval(Bingo_Http_Request::get('title', ''));
            $strAbstract = strval(Bingo_Http_Request::get('abstract', ''));
            $strPicUrl = strval(Bingo_Http_Request::get('pic_url', ''));
            $strJumpUrl = strval(Bingo_Http_Request::get('jump_url', ''));
            $strToUserIds = strval(Bingo_Http_Request::get('to_user_ids', ''));
            $intStartTime = intval(Bingo_Http_Request::get('start_time', 0));
            $intEndTime = intval(Bingo_Http_Request::get('end_time', 0));

            if(empty($intId) || empty($strTitle) || empty($strAbstract) || empty($strPicUrl) || empty($strJumpUrl) || empty($intStartTime) || empty($intEndTime)) {
                Bingo_Log::warning("error params!");
                return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
            }

            if($intStartTime >= $intEndTime) {
                Bingo_Log::warning("error params!");
                return Util_Ala_Common::jsonAmisRetMsg(Alalib_Conf_Error::ERR_PARAM_ERROR, "开始时间大于结束时间");
            }

            $arrToUserIds = preg_split("/( |\n|\r|,)/", $strToUserIds);
            $arrToUserIds = array_values(array_unique(array_filter($arrToUserIds)));
            if(count($arrToUserIds) > 100) {
                Bingo_Log::warning("error params!");
                return Util_Ala_Common::jsonAmisRetMsg(Alalib_Conf_Error::ERR_PARAM_ERROR, "配置的用户id数量不能大于100个!");
            }
            foreach ($arrToUserIds as $value) {
                if(!is_numeric($value)) {
                    Bingo_Log::warning("error params!");
                    return Util_Ala_Common::jsonAmisRetMsg(Alalib_Conf_Error::ERR_PARAM_ERROR, "配置的用户id有非数字的!");
                }
            }

            $arrServiceInput = array(
                'id' => $intId,
                'title'   => $strTitle,
                'abstract' => $strAbstract,
                'pic_url'   => $strPicUrl,
                'jump_url'  => $strJumpUrl,
                'to_user_ids' => implode(' ', $arrToUserIds),
                'start_time'   => $intStartTime,
                'end_time'    => $intEndTime,
                'final_op_time'  => time(),
                'operator'   => $strOwnUserName,
            );

            $arrRet = Dl_Ala_Liveapp_Liveapp::updateLiveAppNotice($arrServiceInput);
            if(false === $arrRet || Alalib_Conf_Error::ERR_SUCCESS != $arrRet['errno']) {
                $strLog = __CLASS__."::".__FUNCTION__. " call ala service::updateLiveAppNotice fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrRet)."]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
            }
            return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_SUCCESS);
        }

        else if('delete' == $strMethod) {
            $intId = Bingo_Http_Request::get('id', 0);
            if(empty($intId)) {
                Bingo_Log::warning("error params!");
                return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
            }
            $arrServiceInput = array(
                'id' => $intId,
                'status' => self::NOTICE_STATUS_DELETE,
                'final_op_time'  => time(),
                'operator'   => $strOwnUserName,
            );
            $arrRet = Dl_Ala_Liveapp_Liveapp::deleteLiveAppNotice($arrServiceInput);
            if(false === $arrRet || Alalib_Conf_Error::ERR_SUCCESS != $arrRet['errno']) {
                $strLog = __CLASS__."::".__FUNCTION__. " call ala service::deleteLiveAppNotice fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrRet)."]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
            }
            return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_SUCCESS);
        }

        else if('get' == $strMethod){
            $intPn = Bingo_Http_Request::get('page', 1);
            $intPs = Bingo_Http_Request::get('ps', 10);
            $intType = Bingo_Http_Request::get('type', 0);
            $intOffset = ($intPn - 1) * $intPs;

            $strCond = "";
            if(1 == $intType) {
                $strCond = " start_time <= " . time() . " and end_time > " . time() . " and ";
            }
            $strCond = $strCond . " status = " . self::NOTICE_STATUS_ONLINE;
            $arrServiceInput = array(
                'offset' => $intOffset,
                'ps'  => $intPs,
                'cond' => $strCond,
            );
            $arrRet = Dl_Ala_Liveapp_Liveapp::selectLiveAppNotice($arrServiceInput);
            if(false === $arrRet || Alalib_Conf_Error::ERR_SUCCESS != $arrRet['errno']) {
                $strLog = __CLASS__."::".__FUNCTION__. " call ala service::selectLiveAppNotice fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrRet)."]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
            }
            return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet['data']);
        }

        return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR);
    }


}