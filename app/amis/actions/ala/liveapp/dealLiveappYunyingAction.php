<?php

/**
 * Created by PhpStorm.
 * User: caowu
 * Date: 19/1/15
 * Time: 上午11:22
 */
class dealLiveappYunyingAction extends Util_Action {

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = true;

    const REDIS_KEY_AMIS_TB_LIVE_APP_YUNYING_INFO = 'redis_key_amis_tb_live_app_yunying_info_';
    /**
     * @return bool
     */
    public function _execute() {
        // 操作人
        $intOwnUserId = Util_User::$intUserId;
        $strOwnUserName = Util_User::$strUserName;
        $strMethod = Bingo_Http_Request::get('method', '');

        //获取参数
        $strMethod = Bingo_Http_Request::getNoXssSafe('method','');

        if('update' == $strMethod) {
            // Request 初步数据校验
            $arrReq = Bingo_Http_Request::get('combo', array());

            if(count($arrReq) > 6) {
                Bingo_Log::warning('error params!');
                return Util_Ala_Common::jsonAmisRetMsg(Alalib_Conf_Error::ERR_PARAM_ERROR, "最多能添加6个运营位");
            }
            $arrPosition = array();
            foreach ($arrReq as $key => &$value) {
                if(empty($value['start_time'])) {
                    $value['start_time'] = time();
                }
                if(empty($value['end_time'])) {
                    $value['end_time'] = 33103180800;  //一千年以后
                }

                if( $value['end_time'] <= $value['start_time'] ) {
                    Bingo_Log::warning('error params!');
                    return Util_Ala_Common::jsonAmisRetMsg(Alalib_Conf_Error::ERR_PARAM_ERROR, "参数错误,第" . ($key + 1) . "个位置开始时间大于等于过期时间");
                }

                $arrPosition[] = intval($value['position']);
            }

            array_multisort($arrPosition, SORT_ASC, $arrReq);

            $arrDlInput = array(
                'key' => self::REDIS_KEY_AMIS_TB_LIVE_APP_YUNYING_INFO,
                'value' => $arrReq,
            );
            $arrOutput = Util_Redis::setToRedis($arrDlInput);
            if (false === $arrOutput) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Util_Redis::setToRedis fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }


            return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_SUCCESS);
        }

        else if('get' == $strMethod) {
            $arrDlInput = array(
                'key' => self::REDIS_KEY_AMIS_TB_LIVE_APP_YUNYING_INFO,
            );
            $arrOutput = Util_Redis::getFromRedis($arrDlInput);
            if (false === $arrOutput) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Util_Redis::getFromRedis fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $arrRetData = array(
                'combo' => $arrOutput,
            );

            return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRetData);
        }



        return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR);
    }


}