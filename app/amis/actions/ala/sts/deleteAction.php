<?php
/**
 * deleteAction.php
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 18/3/20 下午5:09
*/

class deleteAction extends Util_Ala_STS_Delete
{
    protected $_isGetNoXSSSafe = true;

    public function init() {
        $this->_strSTSName = (string)Bingo_HTTP_Request::get('sts_module', '');
        $strExtra          = (string)Bingo_HTTP_Request::get('sts_extra', '');

        if (!empty($strExtra)) {
            $this->_strSTSExtra = $strExtra;
        }

        return parent::init();
    }
}