<?php

/**
 * todo 查询特型卡片：游戏推荐
 * Created by PhpStorm.
 * User: kangqinmou
 * Date: 18-3-15
 * Time: 下午4:44
 */
class selectGameAction extends Util_Action{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;



    /**
     * @brief   执行入口
     * @param   null
     * @return  array
     */
    public function _execute(){

        // 参数校验
        $intCardType = Lib_FeatureCard::CARD_TYPE_GAME;
        $intPn = (int) Bingo_Http_Request::get('pn', 1);
        $intPn = $intPn < 1 ? 1 : $intPn;
        $intPs = (int) Bingo_Http_Request::get('ps', 30);
        $intPs = $intPs < 1 ? 30 : $intPs;

        // 查询feature_card的记录
        $arrInput = array(
            'type' => $intCardType,
            'pn'   => $intPn,
            'ps'   => $intPs,
        );
        $arrOutput = Tieba_Service::call('game', 'selectFeatureCard', $arrInput, null, null, 'post', null, 'gbk');
        if(!$arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' select feature card failed by calling game.selectFeatureCard service, input: [' . serialize($arrInput) . '], output: [' . serialize($arrOutput) . "]. \n");
            return self::jsonAmisRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL, array(), $arrOutput['errmsg']);
        }
        $arrCardInfos = $arrOutput['data'];

        // 查询节点对应的吧ids
        $arrCardForumMap = array();
        if($arrCardInfos){
            $arrParams = array(
                'card_id' => array('card_id', 'in', array_keys($arrCardInfos)),
            );
            $arrForums = Tieba_Service::call('game', 'selectForumCardRel', $arrParams, null, null, 'post', null, 'gbk');
            if(!$arrForums || Tieba_Errcode::ERR_SUCCESS != $arrForums['errno']){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' select forum_card_rel failed by calling game.selectForumCardRel service, input: [' . serialize($arrParams) . '], output: [' . serialize($arrForums) . "]. \n");
                return self::jsonAmisRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL, array(), $arrForums['errmsg']);
            }
            foreach($arrForums['data'] as $item){
                $arrCardForumMap[$item['card_id']][] = $item['forum_id'];
            }
        }

        // 格式化子节点的数据
        $arrForumIds = array();
        foreach($arrCardInfos as &$item){
            $item['forum_ids'] = isset($arrCardForumMap[$item['id']]) ? join(' ', $arrCardForumMap[$item['id']]) : '';
            $item['parent_title'] = (!empty($item['ext']['parent_title']) && is_array($item['ext'])) ? $item['ext']['parent_title'] : '';

            // 吧id
            $intForumId = (!empty($item['address']['forum_id']) && is_array($item['address'])) ? intval($item['address']['forum_id']) : '';
            $item['forum_id'] = $intForumId;
            if($intForumId > 0){
                $arrForumIds[$intForumId] = $intForumId;
            }
        }
        unset($item);

        // 获取吧信息
        // http://tc.service.tieba.baidu.com/service/forum?method=mgetBtxInfo&format=json&ie=ut-8&forum_id[]=25686086
        if($arrForumIds){
            $arrParams = array(
                'forum_id' => array_values($arrForumIds),
            );
            $arrForumInfos = Tieba_Service::call('forum', 'mgetBtxInfo', $arrParams, null, null, 'post', null, 'utf-8');
            if(!$arrForumInfos || Tieba_Errcode::ERR_SUCCESS != $arrForumInfos['errno']){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' get forum infos failed by calling forum.mgetBtxInfo service, intput: [' . serialize($arrParams) . '], output: [' . serialize($arrForumInfos) . "]. \n");
                return self::jsonAmisRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL, array(), 'get forum infos failed.');
            }
            $arrForumInfos = $arrForumInfos['output'];

            // 获取吧名
            foreach($arrCardInfos as &$item){
                $item['forum_name'] = '';
                if($item['forum_id'] > 0){
                    if(!empty($arrForumInfos[$item['forum_id']]['forum_name']['forum_name'])){
                        $item['forum_name'] = $arrForumInfos[$item['forum_id']]['forum_name']['forum_name'];
                    }
                }
            }
            unset($item);
        }

        // 查询feature_card的记录数量
        $arrInput = array(
            'type'   => $intCardType,
            'fields' => 'count(1) as num',
        );
        $arrCount = Tieba_Service::call('game', 'selectFeatureCard', $arrInput, null, null, 'post', null, 'gbk');
        if(!$arrCount || Tieba_Errcode::ERR_SUCCESS != $arrCount['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' select feature_card count failed by calling game.selectFeatureCard service, input: [' . serialize($arrInput) . '], output: [' . serialize($arrCount) . "]. \n");
            return self::jsonAmisRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL, array(), 'select feature_card count failed.');
        }

        return self::jsonAmisRet(Alalib_Conf_Error::ERR_SUCCESS, array(
            'rows'  => array_values($arrCardInfos),
            'count' => intval($arrCount['data'][0]['num']),
        ));
    }


    /**
     * @param $intErrno
     * @param array $arrExtData
     * @param string $strMsg
     * @return bool
     */
    public static function jsonAmisRet($intErrno, $arrExtData = array(), $strMsg = ''){
        $strMsg = $strMsg ? $strMsg : Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput = array(
            'status' => $intErrno,
            'msg'    => $strMsg,
            'data'   => $arrExtData,
        );
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrOutput, Bingo_Encode::ENCODE_UTF8);
        return true;
    }
}