<?php
/**
 * todo 更新特型卡片：专题
 * User: kangqinmou
 * Date: 18-4-9
 */
class updateTopicAction extends Util_Action
{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;


    /**
     * @brief   执行入口
     * @param   null
     * @return  array
     */
    public function _execute()
    {
        // 参数校验
        $intId = (int) Bingo_Http_Request::get('id', 0);
        if($intId <= 0){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ", card id must be bigger than zero, card id [{$intId}]. \n");
            return self::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'id <= 0');
        }

        /* 获取详细参数
        $arrInput = array(
            'title'     => '精选专题',
            'floor'     => 5,
            'forum_ids' => array(234, 423),
        );
        */
        $arrInput = self::getParams();
        if(!$arrInput){
            return false;
        }

        // 更新
        $arrParams = array(
            'id'       => $intId,
            'title'    => $arrInput['title'],
            'floor'    => $arrInput['floor'],
            'op_uname' => Util_User::$strUserName,
        );
        $arrRet = Tieba_Service::call('game', 'updateFeatureCardById', $arrParams, null, null, 'get', null, 'gbk');
        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' update feature card\'s status failed by calling game.updateFeatureCardById service, input: [' . serialize($arrParams) . '], output: [' . serialize($arrRet) . "]. \n");
            return self::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), 'update feature card\'s status failed');
        }

        // 查询特型卡片对应的吧id
        $arrParams = array(
            'card_id' => $intId,
        );
        $arrForums = Tieba_Service::call('game', 'selectForumCardRel', $arrParams, null, null, 'post', null, 'gbk');
        if(!$arrForums || Tieba_Errcode::ERR_SUCCESS != $arrForums['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' select forum_card_rel failed by calling game.selectForumCardRel service, input: [' . serialize($arrParams) . '], output: [' . serialize($arrForums) . "]. \n");
            return self::jsonAmisRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL, array(), $arrForums['errmsg']);
        }
        $arrOldForums = array();
        foreach($arrForums['data'] as $item){
            if($intId == $item['card_id'] && $item['forum_id'] > 0){
                $intForumId = (int) $item['forum_id'];
                $arrOldForums[$intForumId] = $intForumId;
            }
        }

        // 更新吧id：多余的删除，少了的插入，(头大)
        foreach($arrInput['forum_ids'] as $intForumId){
            if(isset($arrOldForums[$intForumId])){
                unset($arrOldForums[$intForumId]);
                continue;
            }
            $arrParams = array(
                'card_id'  => $intId,
                'forum_id' => $intForumId,
                'op_uname' => Util_User::$strUserName,
            );
            $arrRet = Tieba_Service::call('game', 'insertForumCardRel', $arrParams, null, null, 'post', null, 'gbk');
            if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' insert forum_card_rel failed by calling game.insertForumCardRel service, input: [' . serialize($arrParams) . '], output: [' . serialize($arrRet) . "]. \n");
                return self::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), $arrRet['msg']);
            }
        }
        foreach($arrOldForums as $intForumId){
            $arrParams = array(
                'card_id'  => $intId,
                'forum_id' => $intForumId,
            );
            $arrRet = Tieba_Service::call('game', 'deleteForumCardRel', $arrParams, null, null, 'post', null, 'gbk');
            if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' delete forum_card_rel failed by calling game.deleteForumCardRel service, input: [' . serialize($arrParams) . '], output: [' . serialize($arrRet) . "]. \n");
                return self::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), $arrRet['msg']);
            }
        }

        Lib_FeatureCard::delCache();
        return self::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS);
    }










    /**
     * @brief   获取参数
     * @param   null
     * @return  boolean|array
     */
    private static function getParams(){

        $strTitle    = (string) Bingo_Http_Request::get('title', '');               // 标题
        $intFloor    = (int)    Bingo_Http_Request::get('floor', 0);                // 特型卡片要插入的楼层
        $strForumIds = (string) Bingo_Http_Request::get('forum_ids', '');           // 吧ids(以空格隔开)

        // 获取吧ids
        $arrForumIds = explode(' ', $strForumIds);
        foreach($arrForumIds as $key => &$item){
            $item = (int) $item;
            if($item <= 0){
                unset($arrForumIds[$key]);
            }
        }
        unset($item);

        // 返回结果
        if(!$strTitle){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ", 标题为空. \n");
            self::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), '标题为空');
            return false;
        }
        return array(
            'title'     => $strTitle,
            'floor'     => $intFloor,
            'forum_ids' => array_values(array_unique($arrForumIds)),
        );
    }






    /**
     * @param $intErrno
     * @param array $arrExtData
     * @param string $strMsg
     * @return bool
     */
    public static function jsonAmisRet($intErrno, $arrExtData = array(), $strMsg = ''){
        $strMsg = $strMsg ? $strMsg : Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput = array(
            'status' => $intErrno,
            'msg'    => $strMsg,
            'data'   => $arrExtData,
        );
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrOutput, Bingo_Encode::ENCODE_UTF8);
        return true;
    }
}