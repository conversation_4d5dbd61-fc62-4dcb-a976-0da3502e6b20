<?php

/**
 * todo 插入特型卡片：大神推荐
 * Created by PhpStorm.
 * User: kangqinmou
 * Date: 18-4-2
 * Time: 下午8:09
 */
class insertGodAction extends Util_Action
{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    /**
     * @brief   执行入口
     * @param   null
     * @return  array
     */
    public function _execute(){

        /* 获取参数
        array(
            'parent_title'     => '大神推荐',
            'floor'            => 8,
            'forum_ids'        => array(1, 2),
            'user_ids'         => array(3, 4),
        );
        */
        $arrInput = self::getParams();
        if(!$arrInput){
            return false;
        }
        $intCardType = Lib_FeatureCard::CARD_TYPE_GOD;

        // 先查询这些user_id是否存在，如果已经存在，则不再插入
        $arrParams = array(
            'type' => $intCardType,
        );
        foreach($arrInput['user_ids'] as $intUserId){
            $arrParams['address'] = json_encode(array('user_id'=>$intUserId));
        }
        $arrParams['address'] = array('address', 'in', $arrParams['address']);
        $arrOutput = Tieba_Service::call('game', 'selectFeatureCard', $arrParams, null, null, 'post', null, 'gbk');
        if(!$arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' select feature card failed by calling game.selectFeatureCard service, input: [' . serialize($arrParams) . '], output: [' . serialize($arrOutput) . "]. \n");
            return self::jsonAmisRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL, array(), $arrOutput['errmsg']);
        }
        $arrCardInfos = $arrOutput['data'];

        // 获取用户id对应的卡片id
        $arrUserCardMap = array();
        foreach($arrCardInfos as $item){
            if(!empty($item['address']['user_id']) && is_array($item['address']) && $item['address']['user_id'] > 0){
                $intUserId = (int) $item['address']['user_id'];
                $arrUserCardMap[$intUserId] = $item['id'];
            }
        }

        // 没有入库的用户，插入数据库中，已入库的用户，则被更新
        foreach($arrInput['user_ids'] as $intUserId){
            if(isset($arrUserCardMap[$intUserId])){
                $arrParams = array(
                    'id'       => $arrUserCardMap[$intUserId],
                    'floor'    => $arrInput['floor'],
                    'op_uname' => Util_User::$strUserName,
                );
                $arrExt = $arrCardInfos[$arrUserCardMap[$intUserId]]['ext'];
                if(!is_array($arrExt)){
                    $arrExt = array();
                }
                $arrExt['parent_title'] = $arrInput['parent_title'];
                $arrParams['ext'] = json_encode($arrExt);
                $arrRet = Tieba_Service::call('game', 'updateFeatureCardById', $arrParams, null, null, 'get', null, 'gbk');
                if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                    Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' update feature_card failed by calling game.updateFeatureCardById service, input: [' . serialize($arrParams) . '], output: [' . serialize($arrRet) . "]. \n");
                    return self::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), $arrRet['msg']);
                }
                continue;
            }
            $arrParams = array(
                'status'    => Lib_FeatureCard::CARD_STATUS_OFFLINE,
                'type'      => $intCardType,
                'address'   => json_encode(array('user_id' => $intUserId)),
                'floor'     => $arrInput['floor'],
                'ext'       => json_encode(array('parent_title' => $arrInput['parent_title'])),
                'op_uname'  => Util_User::$strUserName,
            );
            $arrRet = Tieba_Service::call('game', 'insertFeatureCard', $arrParams, null, null, 'get', null, 'gbk');
            if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' insert feature_card failed by calling game.insertFeatureCard service, input: [' . serialize($arrParams) . '], output: [' . serialize($arrRet) . "]. \n");
                return self::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), $arrRet['msg']);
            }
            $arrUserCardMap[$intUserId] = intval($arrRet['data']['id']);
        }

        // 插入forum_card_rel：吧id与卡片id之间的对应关系
        if($arrInput['forum_ids']){
            foreach($arrInput['forum_ids'] as $intForumId){
                foreach($arrUserCardMap as $intCardId){
                    $arrParams = array(
                        'card_id'  => $intCardId,
                        'forum_id' => $intForumId,
                        'op_uname' => Util_User::$strUserName,
                        'options'  => 'ignore',
                    );
                    $arrRet = Tieba_Service::call('game', 'insertForumCardRel', $arrParams, null, null, 'get', null, 'gbk');
                    if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                        Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' insert forum_card_rel failed by calling game.insertForumCardRel service, input: [' . serialize($arrParams) . '], output: [' . serialize($arrRet) . "]. \n");
                        return self::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), $arrRet['msg']);
                    }
                }
            }
        }

        Lib_FeatureCard::delCache();
        return self::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS);

    }




    /**
     * @brief   获取参数
     * @param   null
     * @return  boolean|array
     */
    private static function getParams(){

        $strParentTitle = (string) Bingo_Http_Request::get('parent_title', '');        // title
        $intFloor       = (int)    Bingo_Http_Request::get('floor', 0);                // 楼层

        // 获取吧ids
        $strForumIds    = (string) Bingo_Http_Request::get('forum_ids', '');
        $arrForumIds = explode(' ', $strForumIds);
        foreach($arrForumIds as $key => &$item){
            $item = (int) $item;
            if($item <= 0){
                unset($arrForumIds[$key]);
            }
        }
        unset($item);

        // 获取用户ids
        $strUserIds = (string) Bingo_Http_Request::get('user_ids', '');
        $arrUserIds = explode(' ', $strUserIds);
        foreach($arrUserIds as $key => &$item){
            $item = (int) $item;
            if($item <= 0){
                unset($arrUserIds[$key]);
            }
        }
        unset($item);

        // 参数校验
        if($intFloor <= 0){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ", 楼层不能 <= 0. \n");
            self::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), '楼层不能 <= 0');
            return false;
        }
        if(!$strParentTitle){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ", 必需字段不能为空. \n");
            self::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), '必需字段不能为空');
            return false;
        }
        if(!$arrUserIds){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ", 用户id为空. \n");
            self::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), '用户id为空');
            return false;
        }

        // 返回结果
        return array(
            'parent_title'     => $strParentTitle,
            'floor'            => $intFloor,
            'forum_ids'        => array_values(array_unique($arrForumIds)),
            'user_ids'         => array_values(array_unique($arrUserIds)),
        );
    }



    /**
     * @param $intErrno
     * @param array $arrExtData
     * @param string $strMsg
     * @return bool
     */
    public static function jsonAmisRet($intErrno, $arrExtData = array(), $strMsg = ''){
        $strMsg = $strMsg ? $strMsg : Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput = array(
            'status' => $intErrno,
            'msg'    => $strMsg,
            'data'   => $arrExtData,
        );
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrOutput, Bingo_Encode::ENCODE_UTF8);
        return true;
    }
}