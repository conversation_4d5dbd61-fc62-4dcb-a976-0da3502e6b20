<?php

/**
 * todo 更新特型卡片：游戏推荐
 * User: kangqinmou
 * Date: 18-4-9
 */
class updateGameAction extends Util_Action
{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;


    /**
     * @brief   执行入口
     * @param   null
     * @return  array
     */
    public function _execute(){

        // 参数校验
        $intId = (int) Bingo_Http_Request::get('id', 0);
        if($intId <= 0){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ", card id must be bigger than zero, card id [{$intId}]. \n");
            return self::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'id <= 0');
        }

        /* 获取入参
        array(
            'parent_title'     => '大神推荐',
            'title'            => '大吉大利',
            'floor'            => 3,
            'forum_ids'        => array(32, 9921),
            'forum_id'         => 998,
        );
        */
        $arrInput = self::getParams();
        if(!$arrInput){
            return false;
        }
        $intCardType = Lib_FeatureCard::CARD_TYPE_GAME;

        // 根据自增id查询特型卡片
        $arrParams = array(
            'id' => $intId,
        );
        $arrOutput = Tieba_Service::call('game', 'selectFeatureCard', $arrParams, null, null, 'post', null, 'gbk');
        if(!$arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno'] || empty($arrOutput['data'][$intId])){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' select feature card failed by calling game.selectFeatureCard service, input: [' . serialize($arrParams) . '], output: [' . serialize($arrOutput) . "]. \n");
            return self::jsonAmisRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL, array(), $arrOutput['errmsg']);
        }
        $arrCardInfo = $arrOutput['data'][$intId];

        // 更新feature_card
        $arrParams = array(
            'id'        => $intId,
            'title'     => $arrInput['title'],
            'floor'     => $arrInput['floor'],
            'op_uname'  => Util_User::$strUserName,
        );
        $arrExt = $arrCardInfo['ext'];
        if(!is_array($arrExt)){
            $arrExt = array();
        }
        $arrExt['parent_title'] = $arrInput['parent_title'];
        $arrParams['ext'] = json_encode($arrExt);
        $arrRet = Tieba_Service::call('game', 'updateFeatureCardById', $arrParams, null, null, 'get', null, 'gbk');
        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' update feature_card failed by calling game.updateFeatureCardById service, input: [' . serialize($arrParams) . '], output: [' . serialize($arrRet) . "]. \n");
            return self::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), $arrRet['msg']);
        }

        // 查询特型卡片对应的吧id
        $arrParams = array(
            'card_id' => $intId,
        );
        $arrForums = Tieba_Service::call('game', 'selectForumCardRel', $arrParams, null, null, 'post', null, 'gbk');
        if(!$arrForums || Tieba_Errcode::ERR_SUCCESS != $arrForums['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' select forum_card_rel failed by calling game.selectForumCardRel service, input: [' . serialize($arrParams) . '], output: [' . serialize($arrForums) . "]. \n");
            return self::jsonAmisRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL, array(), $arrForums['errmsg']);
        }
        $arrOldForums = array();
        foreach($arrForums['data'] as $item){
            if($intId == $item['card_id'] && $item['forum_id'] > 0){
                $intForumId = (int) $item['forum_id'];
                $arrOldForums[$intForumId] = $intForumId;
            }
        }

        // 更新吧id：多余的删除，少了的插入，(头大)
        foreach($arrInput['forum_ids'] as $intForumId){
            if(isset($arrOldForums[$intForumId])){
                unset($arrOldForums[$intForumId]);
                continue;
            }
            $arrParams = array(
                'card_id'  => $intId,
                'forum_id' => $intForumId,
                'op_uname' => Util_User::$strUserName,
            );
            $arrRet = Tieba_Service::call('game', 'insertForumCardRel', $arrParams, null, null, 'post', null, 'gbk');
            if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' insert forum_card_rel failed by calling game.insertForumCardRel service, input: [' . serialize($arrParams) . '], output: [' . serialize($arrRet) . "]. \n");
                return self::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), $arrRet['msg']);
            }
        }
        foreach($arrOldForums as $intForumId){
            $arrParams = array(
                'card_id'  => $intId,
                'forum_id' => $intForumId,
            );
            $arrRet = Tieba_Service::call('game', 'deleteForumCardRel', $arrParams, null, null, 'post', null, 'gbk');
            if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' delete forum_card_rel failed by calling game.deleteForumCardRel service, input: [' . serialize($arrParams) . '], output: [' . serialize($arrRet) . "]. \n");
                return self::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), $arrRet['msg']);
            }
        }

        Lib_FeatureCard::delCache();
        return self::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS);
    }




    /**
     * @brief   获取参数
     * @param   null
     * @return  boolean|array
     */
    private static function getParams(){

        $intForumId = (int) Bingo_Http_Request::get('forum_id', '');
        $strParentTitle = (string) Bingo_Http_Request::get('parent_title', '');              // 标题
        $intFloor       = (int)    Bingo_Http_Request::get('floor', 0);                      // 特型卡片要插入的楼层
        $strForumIds    = (string) Bingo_Http_Request::get('forum_ids', '');                 // 吧ids(以空格隔开)
        $strTitle       = (string) Bingo_Http_Request::get('title', '');                     // 一句话说明

        // 获取吧ids
        $arrForumIds = explode(' ', $strForumIds);
        foreach($arrForumIds as $key => &$item){
            $item = (int) $item;
            if($item <= 0){
                unset($arrForumIds[$key]);
            }
        }
        unset($item);

        // 参数校验
        if($intFloor <= 0){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ", 楼层不能 <= 0. \n");
            self::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), '楼层不能 <= 0');
            return false;
        }
        if($intForumId <= 0 || !$strParentTitle || !$strTitle){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ", 必需字段不能为空. \n");
            self::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), '必需字段不能为空');
            return false;
        }

        // 返回结果
        return array(
            'parent_title'     => $strParentTitle,
            'floor'            => $intFloor,
            'forum_ids'        => $arrForumIds,
            'forum_id'         => $intForumId,
            'title'            => $strTitle,
        );


    }





    /**
     * @param $intErrno
     * @param array $arrExtData
     * @param string $strMsg
     * @return bool
     */
    public static function jsonAmisRet($intErrno, $arrExtData = array(), $strMsg = ''){
        $strMsg = $strMsg ? $strMsg : Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput = array(
            'status' => $intErrno,
            'msg'    => $strMsg,
            'data'   => $arrExtData,
        );
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrOutput, Bingo_Encode::ENCODE_UTF8);
        return true;
    }
}