<?php
/**
 * Created by PhpStorm.
 * 直播回放接口mis
 * User: weiyan02
 * Date: 2019/1/14
 * Time: 下午2:28
 */

class getRecordsForMisAction extends Util_Action
{

    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    public function _execute()
    {
        $strTitle = strval(Bingo_Http_Request::get('title', ''));
        //转化为user_id筛选
        $strUserName = strval(Bingo_Http_Request::get('user_name', ''));
        $strNickName = strval(Bingo_Http_Request::get('nick_name', ''));
        $intUserId = intval(Bingo_Http_Request::get('user_id', 0));
        $intLiveId = intval(Bingo_Http_Request::get('live_id', 0));
        $intStartTime = intval(Bingo_Http_Request::get('start_live', strtotime('-1 day')));
        $intEndTime = intval(Bingo_Http_Request::get('end_live', time()));
        $intLiveType = intval(Bingo_Http_Request::get('live_type', 0));
        $strLiveDuration = strval(Bingo_Http_Request::get('live_duration', ''));

        $strLiveDuration = explode(',',$strLiveDuration);
        $intLiveDurationStart =$strLiveDuration[0]*60;
        $intLiveDurationEnd =$strLiveDuration[1]*60;

        $intPn = intval(Bingo_Http_Request::get('page', 1));
        $intPs = intval(Bingo_Http_Request::get('perPage', 20));


        $intStartTime = strtotime(date('Y-m-d',$intStartTime));
        $intEndTime = strtotime(date('Y-m-d',$intEndTime));

        if($intStartTime >$intEndTime){
            return $this->_jsonRet(Tieba_Errcode::ERR_EMS_ERR_PARAM, '开始时间大于结束时间！');
        }
        //通过主播用户名取用户id---start
        if (empty($intUserId) && (!empty($strUserName) || !empty($strNickName))) {
            Bingo_Log::warning('getuidByname param:');

            if (!empty($strUserName)) {
                $strUserNameRes = $strUserName;
            } else {
                $strUserNameRes = $strNickName;
            }
            $arrServiceInput = array(
                'names' => array(
                    $strUserNameRes,
                ),
            );
            $arrRetAnchorName = Tieba_Service::call('user', 'getUidByNames', $arrServiceInput, null, null, 'post', 'php', 'utf-8');
            if (false === $arrRetAnchorName || Alalib_Conf_Error::ERR_SUCCESS != $arrRetAnchorName['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call user service::getUidByNames fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrRetAnchorName) . "]";
                Bingo_Log::warning($strLog);
            }

            $intUserId = intval($arrRetAnchorName["data"][$strUserNameRes][0]['user_id']);
        }
        //通过主播用户名取用户id---end
        Bingo_Log::warning('getuidByname:'.var_export($arrRetAnchorName,1).'uid:'.var_export($intUserId,1));


        $strCondition = " 1=1 ";
//        $strCondition = $strCondition . " and media_url !='' ";

        if(!empty($intUserId)) {
            $strCondition = $strCondition . " and user_id=$intUserId ";
        }
        if(!empty($intLiveId)) {
            $strCondition = $strCondition . " and live_id=$intLiveId ";
        }
        if(!empty($strTitle)) {
            $strTitle = "'".$strTitle."'";
            $strCondition = $strCondition . " and description= $strTitle ";
        }
        if(!empty($intStartTime)) {
            $strCondition = $strCondition . " and start_time >=$intStartTime ";
        }
        if(!empty($intEndTime)) {
            $strCondition = $strCondition . " and start_time <=$intEndTime ";
        }
        if(!empty($intLiveType)) {
            $strCondition = $strCondition . " and live_type= $intLiveType ";
        }
        if($intLiveDurationStart >=0 ) {
            $strCondition = $strCondition . " and  live_duration >= $intLiveDurationStart";
        }
        if(!empty($intLiveDurationEnd)) {
            $strCondition = $strCondition . " and  live_duration <= $intLiveDurationEnd";
        }


        //获取分页总数--start
        $arrInputForPage = array(
            'cond' => $strCondition,
            'offset' => 0,
            'limit' => 10000,
        );
        $strService = 'ala';
        $strMethod = 'getVodLiveInfoByTimeMis';
        $arrOutputForPage = Tieba_Service::call($strService, $strMethod, $arrInputForPage, null, null, "post", null, "utf-8");
        if (false === $arrOutputForPage || Tieba_Errcode::ERR_SUCCESS != $arrOutputForPage["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strService::$strMethod fail. output:[" . serialize($arrOutputForPage) . "]";
            Bingo_Log::warning($strLog);
        }
        $arrLiveDataForPageCount= count($arrOutputForPage['data']);
        //获取分页总数--start

        $arrInput = array(
            'cond' => $strCondition,
            'offset' => ($intPn - 1) * $intPs,
            'limit' => $intPs,
        );
        $strService = 'ala';
        $strMethod = 'getVodLiveInfoByTimeMis';

        $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, "post", null, "utf-8");
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strService::$strMethod fail. output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
        }
        $arrLiveData = $arrOutput['data'];

          $arrLiveIds = array();
        foreach ($arrLiveData as $tmpLiveInfo){
                $intLiveId = intval($tmpLiveInfo['live_id']);
                 $arrLiveIds[] = $intLiveId;
            }
            if(!empty($arrLiveIds)) {
                $arrServiceInput = array(
                    "live_ids" => $arrLiveIds,
                );
                $strServiceName = "ala";
                $strServiceMethod = "liveGetInfo";
                $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                }
            }

        $inFlagNum = ($intPn - 1) * $intPs;
            foreach ($arrLiveData as &$arrLiveDataTmp){
                $inFlagNum++;
                $intLiveId = intval($arrLiveDataTmp['live_id']);
                $arrLiveInfo = $arrOutput['data'][$intLiveId]["live_info"];
                $arrLiveDataTmp['id'] = $inFlagNum;
                $arrLiveDataTmp['description'] = $arrLiveInfo['description'];
                $arrLiveDataTmp['user_name'] = $arrLiveInfo['user_name'];
                $arrLiveDataTmp['cover'] = $arrLiveInfo['media_pic'];
                $arrLiveDataTmp['live_duration'] = self::Sec2Time($arrLiveInfo['live_duration']);
                if(!empty($arrLiveInfo['forum_id']) && 0 != $arrLiveInfo['forum_id']) {
                    $arrLiveDataTmp['forum_id'] = $arrLiveInfo['media_id'];
                }
                $arrLiveDataTmp['start_time'] = date("Y-m-d H:i:s",$arrLiveInfo['start_time']);
                $arrLiveDataTmp['end_time'] = date("Y-m-d H:i:s",$arrLiveInfo['end_time']);

            }


        $arrRetRes = array(
            'rows' => $arrLiveData,
            'count' => $arrLiveDataForPageCount,
        );

        return $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrRetRes);
    }

    /**
     * 秒转换为时间
     * @param $intTime  sec
     * @return string
     */
    private static function Sec2Time($intTime){
        $intTime = (int)$intTime;
        $arrValue = array(
            "years" => 0, "days" => 0, "hours" => 0,
            "minutes" => 0, "seconds" => 0,
        );
        /*
        if($intTime >= 31556926){
            $arrValue["years"] = floor($intTime/31556926);
            $intTime = ($intTime%31556926);
        }
        if($intTime >= 86400){
            $arrValue["days"] = floor($intTime/86400);
            $intTime = ($intTime%86400);
        }
         */
        if($intTime >= 3600){
            $arrValue["hours"] = floor($intTime/3600);
            $intTime = ($intTime%3600);
        }
        if($intTime >= 60){
            $arrValue["minutes"] = floor($intTime/60);
            $intTime = ($intTime%60);
        }
        $arrValue["seconds"] = floor($intTime);
        return $arrValue["hours"] ."小时". $arrValue["minutes"] ."分".$arrValue["seconds"]."秒";
    }

    /**
     * @brief _jsonRet
     *
     * @param
     *            errno,errmsg,data
     * @return : 0.
     *
     */
    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => strval($errmsg),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }

}