<?php

/**
 * 给用户分配靓号
 * Created by PhpStorm.
 * User: zhanghanqing
 * Date: 2018/7/16
 * Time: 17:57
 */
class allocAlaIdToUserAction extends Util_Action
{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    /**
     * @param
     * @return array
     */
    public function _execute()
    {
        $intLoginUserId = Util_User::$intUserId;
        $strOpUserName = Util_User::$strUserName;
        if (empty($intLoginUserId)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error.;";
            Bingo_Log::warning($strLog);
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        $strUserId = strval(Bingo_HTTP_Request::get('user_id', ''));
        $strAlaId = strval(Bingo_HTTP_Request::get('ala_id', ''));
        $intExpireTime = intval(Bingo_HTTP_Request::get('expire_time', 0));
        $intNowTime = Bingo_Timer::getNowTime();
        $strExpireTime = !empty($intExpireTime) ? date('Y/m/d',$intExpireTime) : "永久";
        // 这是批量传的，批量设置
        $arrUserId = explode("\n", $strUserId);
        $arrAlaId = explode("\n", $strAlaId);
        // 因为是循环调service，所以控制下 单次小于50
        if (count($arrUserId) != count($arrAlaId) || count($arrUserId) > 50) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error.;";
            Bingo_Log::warning($strLog);
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        $arrFailUser = array();
        $arrUserAlaId = array();
        foreach ($arrUserId as $index => $tmpId){
            $arrUserAlaId[intval($tmpId)] = intval($arrAlaId[$index]);
        }

        foreach ($arrUserAlaId as $tmpUserId => $tmpAlaId){
            $arrInput = array(
                'user_id' => $tmpUserId,
                'ala_id' => $tmpAlaId,
                'alloc_type' => Lib_Ala_Define_AlaId::ALLOC_TYPE_NICE,
                'expire_time' => $intExpireTime,
                'send_time' => Bingo_Timer::getNowTime(),
                'send_user' => $strOpUserName,
            );
            $strService = "ala";
            $strMethod = "userAllocAlaId";
            $arrOutput = Tieba_Service::call($strService,$strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
            if ($arrOutput === false || (isset($arrOutput['errno']) && intval($arrOutput['errno']) !== 0)) {
                Bingo_Log::warning(__FUNCTION__ . " call service $strService : $strMethod, input =" . serialize($arrInput) . " output =" . serialize($arrOutput));
                $arrFailUser[] = $tmpUserId;
                continue;
            }

            // 记录操作
            $strContent = "$strOpUserName($intLoginUserId) 为 user_id : $tmpUserId 添加靓号 $tmpAlaId ，过期时间: $strExpireTime";
            $arrInput = array(
                'user_id' => $intLoginUserId,
                'type' => Lib_Ala_Define_Op::OP_TYPE_ALAID,
                'content' => $strContent,
                'create_time' => $intNowTime,
            );
            $arrOutput = Service_Ala_Log_Op::addLog($arrInput);
            if ($arrOutput === false || (isset($arrOutput['errno']) && intval($arrOutput['errno']) !== 0)) {
                Bingo_Log::warning(__FUNCTION__ . " call service Service_Ala_Log_Op : addLog, input =" . serialize($arrInput) . " output =" . serialize($arrOutput));
            }
        }
        if (empty($arrFailUser)){
            return $this->_jsonRet(Alalib_Conf_Error::ERR_SUCCESS);
        }else{
            return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL,'添加失败用户:'.implode(',',$arrFailUser));
        }
    }

    /**
     * @param $errno
     * @param string $errmsg
     * @param array $arrExtData
     * @return int
     */
    private function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => Alalib_Conf_Error::getErrorMsg($errno),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }
}
