<?php
/**
 * 已经被分配的ala_id列表
 * Created by PhpStorm.
 * User: zhanghanqing
 * Date: 2018/7/20
 * Time: 15:39
 */

class getAllocedAlaIdListAction extends Util_Action
{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    /**
     * @param
     * @return array
     */
    public function _execute()
    {
        // 操作人
        $intUserId = Util_User::$intUserId;
        $strUserName = Util_User::$strUserName;

        // Request
        $intPage = intval(Bingo_Http_Request::get('pn', 1)) - 1;
        // 列表数据条数
        $intReqNum = intval(Bingo_Http_Request::get('ps', 10));
        $intIsNice = ("true" == Bingo_Http_Request::get('search_is_nice')) ? 1 : 0;
        $intSearchUserId = intval(Bingo_Http_Request::get('search_user_id', 0));
        $intAlaid = intval(Bingo_Http_Request::get('search_ala_id', 0));

        $arrInput = array(
            'is_nice' => $intIsNice,
            'user_id' => $intSearchUserId,
            'ala_id' => $intAlaid,
        );
        $strService = "ala";
        $strMethod = "getAllocedNiceIdCountByConditions";
        $arrRes = Tieba_Service::call($strService,$strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($arrRes === false || $arrRes['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strService::$strMethod fail. input[" . serialize($arrInput) . "] output[" . serialize($arrRes) . "]";
            Bingo_Log::warning($strLog);
            return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $intCount = $arrRes['data']['count'];

        $arrInput = array(
            'is_nice' => $intIsNice,
            'user_id' => $intSearchUserId,
            'ala_id' => $intAlaid,
            'pn' => $intPage,
            'ps' => $intReqNum,
        );
        $strService = "ala";
        $strMethod = "getAllocedNiceIdListByConditions";
        $arrRes = Tieba_Service::call($strService,$strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($arrRes === false || $arrRes['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strService::$strMethod fail. input[" . serialize($arrInput) . "] output[" . serialize($arrRes) . "]";
            Bingo_Log::warning($strLog);
            return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $arrList = $arrRes['data']['id_list'];

        foreach ($arrList as &$item){
            if (empty($item['send_time'])){
                $item['send_time_format'] = '未知';
            }else{
                $item['send_time_format'] = date('Y-m-d H:s:i',$item['send_time']);
            }
            if (empty($item['expire_time'])){
                $item['expire_time_format'] = '永久';
            }else{
                $item['expire_time_format'] = date('Y-m-d H:s:i',$item['expire_time']);
            }
        }
        // 返回列表
        $arrRet = array(
            'rows' => $arrList,
            'count' => $intCount
        );
        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, "", $arrRet);
    }

    /**
     * @brief _jsonRet
     *
     * @param
     *            errno,errmsg,data
     * @return : 0.
     *
     */
    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => strval($errmsg),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }
}



