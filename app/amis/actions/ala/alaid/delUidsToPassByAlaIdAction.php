<?php
/**
 * 对会员对权益进行注销：吧务、大神、印记、会员、T豆、蓝钻、T码、自动会员
 * Created by PhpStorm.
 * User: weiyan02
 * Date: 2018/9/21
 * Time: 下午3:03
 */

class delUidsToPassByAlaIdAction extends Util_Action
{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    public function _execute()
    {
        $intLoginUserId = Util_User::$intUserId;
        $strOpUserName = Util_User::$strUserName;

        $strUserId = strval(Bingo_HTTP_Request::get('input', ''));
        $intAppId = strval(Bingo_HTTP_Request::get('app_id', ''));
//        $intNowTime = Bingo_Timer::getNowTime();

        // 这是批量传的，批量设置
        $arrUserId = explode("\n", $strUserId);
        // 因为是循环调service，所以控制下 单次小于50
        if (count($arrUserId) > 50) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error.;";
            Bingo_Log::warning($strLog);
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        $input = array();
        foreach ($arrUserId as $tmpUserId) {
            $input[] = array(
                'user_id' => $tmpUserId,
            );
        }
        $arrInput = array(
            "input" => $input,
            "app_id" => $intAppId,
        );
        Bingo_Log::warning("tdou delUidsToPass arrInput, input: [". serialize($arrInput)."]");
        $arrOutput = Tieba_Service::call('icon', 'delUidsToPass', $arrInput, null, null, 'post', 'php', 'utf-8');
        Bingo_Log::warning("tdou addUidsToPass arrOutput, input: [". serialize($arrOutput)."]");
        if($arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS || false === $arrOutput ) {
            Bingo_Log::warning("call service icon_delUidsToPass failed, input: [". serialize($arrInput). "] output: [". serialize($arrOutput)."]");
        }

        // 记录操作
        $strContent = "$strOpUserName 注销了用户ID：".implode(',', $arrUserId);

        $arrInput = array(
            'user_id' => $intLoginUserId,
            'type'    => Lib_Ala_Define_Op::OP_TYPE_MEMBER,
            'content' => $strContent,
        );

        $arrOutput = Service_Ala_Log_Op::addLog($arrInput);

        if ($arrOutput === false || (isset($arrOutput['errno']) && intval($arrOutput['errno']) !== 0)) {
            Bingo_Log::warning(__FUNCTION__ . " call service Service_Ala_Log_Op::addLog, input:" . serialize($arrInput) . " output:" . serialize($arrOutput));
        }

        return $this->_jsonRet(Alalib_Conf_Error::ERR_SUCCESS);

    }

    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => strval($errmsg),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }
}