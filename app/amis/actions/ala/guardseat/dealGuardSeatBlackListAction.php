<?php
/**
 * Created by PhpStorm.
 * User: sunzhexuan
 * Date: 2019/11/4
 * Time: 下午9:21
 */
class dealGuardSeatBlackListAction extends Util_Action
{
    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = true;

    /**
     *
     */
    public function _execute(){
        $strMethod = Bingo_Http_Request::get('method', '');
        switch ($strMethod){
            case 'update' : self::_update(); break;
            case 'select' : self::_select(); break;
            case 'delete' : self::_delete(); break;
        }
    }

    /**
     * @return int
     * @throws Exception
     */
    private static function _update(){
        $intUserId = intval(Bingo_Http_Request::get('user_id', ''));
        $intStatus = intval(Bingo_Http_Request::get('status', 0));
        $strOpName = Util_User::$strUserName;
        $intOpTime = Bingo_Timer::getNowTime();

        $arrInput = array(
            'user_id' => $intUserId,
            'status'  => $intStatus,
            'op_time' => $intOpTime,
            'op_name' => $strOpName,
        );
        $arrOutput = Service_Ala_User_User::updateGuardSeat($arrInput);
        if ($arrOutput === false || $arrOutput['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_User_User::updateGuardSeat fail. input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS,'',$arrOutput);
    }

    /**
     * @return int
     * @throws Exception
     */
    private static function _select(){
        $intPn = intval(Bingo_Http_Request::get('pn', 0));
        $intPs = intval(Bingo_Http_Request::get('ps', 10));
        $intUserId = intval(Bingo_Http_Request::get('user_id', ''));

        $arrInput = array(
            'offset'  => ($intPn - 1)*$intPs,
            'limit'   => $intPs,
            'user_id' => $intUserId,
        );
        $arrOutput = Service_Ala_User_User::getGuardSeatBlackList($arrInput);
        if ($arrOutput === false || $arrOutput['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_User_User::getGuardSeatBlackList fail. input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS,'',$arrOutput['data']);
    }

    /**
     * @return int
     * @throws Exception
     */
    private static function _delete(){
        $intUserId = intval(Bingo_Http_Request::get('user_id', ''));

        $arrInput = array(
            'user_id' => $intUserId,
        );
        $arrOutput = Service_Ala_User_User::deleteGuardSeat($arrInput);
        if ($arrOutput === false || $arrOutput['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_User_User::updateGuardSeat fail. input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS,'',$arrOutput);
    }

    /**
     * @param $errno
     * @param string $errmsg
     * @param array $arrExtData
     * @return int
     * @throws Exception
     */
    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => strval($errmsg),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }
}