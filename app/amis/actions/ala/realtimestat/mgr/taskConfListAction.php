<?php
/**
 *
 * <AUTHOR>
 * @date 2019/5/8
 */

class taskConfListAction extends Util_Action
{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    /**
     * @return
     */
    public function _execute()
    {
        $intUserId = Util_User::$intUserId;
        $strOpUserName = Util_User::$strUserName;
       /* if (empty($intUserId)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error.;";
            Bingo_Log::warning($strLog);
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }*/

        $_COOKIE['pub_env'] = 1;

        $intAnchorUserId = intval(Bingo_HTTP_Request::get('user_id', 0));
        $pageNum = intval(Bingo_HTTP_Request::get('page', 0));
        $perPage = intval(Bingo_HTTP_Request::get('perPage', 0));
        $id = Bingo_HTTP_Request::get('id', 0);
        $cond = array();
        if ($id > 0) {
            $cond['id'] = $id;
        }
        $arrInput = array(
            'cond' => $cond,
            'page' => $pageNum,
            'page_size' => $perPage,
        );
        $arrOutput = Tieba_Service::call('standalone', 'getTaskConfList', $arrInput, null, null, 'post', 'php', 'gbk');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call standalone::getTaskConfList fail. input:[" . serialize($arrOutput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return Util_Ala_Showx::_showxRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (empty($arrOutput['data']['list'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call standalone::getTaskConfList no data. input:[" . serialize($arrOutput) . "]; output:[" . serialize($arrOutput) . "]";
            return Util_Ala_Showx::_showxRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $rows = $arrOutput['data']['list'];
        foreach ($rows as &$row) {
            $row['create_time_fmt'] = date("Y-m-d H:i:s", $row['create_time']);
            $row['update_time_fmt'] = date("Y-m-d H:i:s ", $row['update_time']);
        }
        $data = array(
            'rows' => $rows,
            'count' => $arrOutput['data']['total']
        );

        return $this->_jsonRet(Alalib_Conf_Error::ERR_SUCCESS, 'ok', $data);
    }

    /**
     * @param $errno
     * @param string $errmsg
     * @param array $arrExtData
     * @return int
     */
    private function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => !empty($errmsg) ? $errmsg : Alalib_Conf_Error::getErrorMsg($errno),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }

}