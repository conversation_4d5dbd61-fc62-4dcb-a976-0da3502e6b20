<?php
/**
 *
 * <AUTHOR>
 * @date 2019/5/8
 */

class taskConfSaveAction extends Util_Action
{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    /**
     * @return
     */
    public function _execute()
    {
        $_COOKIE['pub_env'] = 1;
        $intUserId = Util_User::$intUserId;
        $strOpUserName = Util_User::$strUserName;
        $allData = Bingo_HTTP_Request::getPostRaw();
        $arrInput['cond']['id'] = (int)$allData['id'];
        unset($arrInput['data']['id']);
        $allData['update_time'] = time();
        $arrInput['field'] = $allData;
        $arrOutput = Tieba_Service::call('standalone', 'updateTaskConf', $arrInput, null, null, 'post', 'php', 'gbk');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call standalone::getTaskConfList fail. input:[" . serialize($arrOutput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        return $this->_jsonRet(Alalib_Conf_Error::ERR_SUCCESS, 'ok', $data);
    }

    /**
     * @param $errno
     * @param string $errmsg
     * @param array $arrExtData
     * @return int
     */
    private function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => !empty($errmsg) ? $errmsg : Alalib_Conf_Error::getErrorMsg($errno),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }

}