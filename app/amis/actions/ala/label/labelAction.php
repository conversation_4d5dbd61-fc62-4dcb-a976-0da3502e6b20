<?php

/**
 * Class LabelAction
 */
class LabelAction extends Util_Action
{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;
    
    /**
     * 执行函数
     *
     * @param null
     *
     * @return null
     * */
    public function _execute()
    {
        //数据获取
        $label_id              = intval(Bingo_Http_Request::get('label_id', 0));
        $pn                    = intval(Bingo_Http_Request::get('page', 1));
        $ps                    = intval(Bingo_Http_Request::get('page_size', 20));
        $label_name            = strval(Bingo_Http_Request::get('label_name', ''));
        $label_rgb             = strval(Bingo_Http_Request::get('label_rgb', ''));
        $begin_time            = intval(Bingo_Http_Request::get('begin_time', ''));
        $end_time              = intval(Bingo_Http_Request::get('end_time', ''));
        $method                = strval(Bingo_Http_Request::get('method', ''));

        $label_img             = strval(Bingo_Http_Request::get('label_img', ''));
        $label_img_night       = strval(Bingo_Http_Request::get('label_img_night', ''));
        $label_width         = intval(Bingo_Http_Request::get('width', 0));
        $label_height          = intval(Bingo_Http_Request::get('height', 0));
        // Bingo_Log::warning(serialize($pre_url));
        if($method == 'add'){
            $arrInput = array(
                'label_name'    => $label_name,
                'label_rgb'     => $label_rgb,
                'label_img'     => $label_img,
                'label_img_night'  => $label_img_night,
                'begin_time'    => $begin_time,
                'end_time'      => $end_time,
                'width'      => $label_width,
                'height'      => $label_height,
            );
            $arrOutput = Tieba_Service::call('ala', 'addLabel', $arrInput, null, null, 'post', null, 'utf-8');
            if(false === $arrOutput){
                $strLog = __CLASS__."::".__FUNCTION__." call ala::addLabel fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
                Bingo_Http_Response::contextType('application/json');
                echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL), 'utf-8');

                return;
            } else if (Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]) {
                Bingo_Http_Response::contextType('application/json');
                echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL, array(),$arrOutput['errmsg']), 'utf-8');
                return;
            }

            Bingo_Http_Response::contextType('application/json');
            echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_SUCCESS), 'utf-8');

            return;
        } else if($method == 'delete'){
            $arrInput  = array('label_id' => $label_id,);
            $arrOutput = Tieba_Service::call('ala', 'delLabel', $arrInput, null, null, 'post', null, 'utf-8');
            if(false === $arrOutput){
                $strLog = __CLASS__."::".__FUNCTION__." call ala::delLabel fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
                Bingo_Http_Response::contextType('application/json');
                echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL), 'utf-8');

                return;
            } else if (Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]) {
                Bingo_Http_Response::contextType('application/json');
                echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL, array(),$arrOutput['errmsg']), 'utf-8');
                return;
            }
            Bingo_Http_Response::contextType('application/json');
            echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_SUCCESS), 'utf-8');

            return;
        } else if($method == 'update'){

            $strRelateUserAdd           = strval(Bingo_Http_Request::get('relate_user_add', ''));
            $strRelateUserDelete        = strval(Bingo_Http_Request::get('relate_user_delete', ''));
            $arrInput = array(
                'label_id'              => $label_id,
                'label_name'            => $label_name,
                'label_rgb'             => $label_rgb,
                'label_img'             => $label_img,
                'label_img_night'       => $label_img_night,
                'begin_time'            => $begin_time,
                'end_time'              => $end_time,
                'width'              => $label_width,
                'height'              => $label_height,
                'relate_user_add'       => $strRelateUserAdd,
                'relate_user_delete'    => $strRelateUserDelete,
            );
            $arrOutput = Tieba_Service::call('ala', 'updateLabel', $arrInput, null, null, 'post', null, 'utf-8');
            if(false === $arrOutput){
                $strLog = __CLASS__."::".__FUNCTION__." call ala::updateLabel fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
                Bingo_Http_Response::contextType('application/json');
                echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL), 'utf-8');

                return;
            } else if (Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]) {
                Bingo_Http_Response::contextType('application/json');
                echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL, array(),$arrOutput['errmsg']), 'utf-8');
                return;


            }

            Bingo_Http_Response::contextType('application/json');
            echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_SUCCESS), 'utf-8');

            return;
        } else if ($method == 'detail'){

            $arrInput  = array('label_id' => $label_id,);
            $arrOutput = Tieba_Service::call('ala', 'selectLabelRelateUserExt', $arrInput, null, null, 'post', null, 'utf-8');
            if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
                $strLog = __CLASS__."::".__FUNCTION__." call ala::selectLabelRelateUserExt fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
                Bingo_Http_Response::contextType('application/json');
                echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL), 'utf-8');

                return;
            }

            $arrRelateUserId = array();
            foreach ($arrOutput['data'] as $item) {
                $arrRelateUserId[] = $item['user_id'];
            }
            $arrRet = array(
                'relate_user' => implode(',', $arrRelateUserId),
            );
            Bingo_Http_Response::contextType('application/json');
            echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet), 'utf-8');

            return;

        } else{
            $arrInput = array(
                'offset'       => ($pn - 1) * $ps,
                'limit'        => $ps,
                'order'        => 'DESC',
                'order_item'   => 'label_id',
            );

            $arrOutput = Tieba_Service::call('ala', 'selectLabelList', $arrInput, null, null, 'post', null, 'utf-8');
            if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
                $strLog = __CLASS__."::".__FUNCTION__." call ala::selectLabelList fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
                Bingo_Http_Response::contextType('application/json');
                echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL), 'utf-8');

                return;
            }


            Bingo_Http_Response::contextType('application/json');

            return self::resultSender(self::errRet(Alalib_Conf_Error::ERR_SUCCESS, array(
                'rows'  => self::DataFormatter($arrOutput['data']['list']),
                'count' => $arrOutput['data']['count'],
            )));
        }
    }


    /**
     * 将sql数据格式转换成amis能接受的数据格式
     *
     * @param null
     *
     * @return null
     * */
    private static function DataFormatter($arrInput = array())
    {
        foreach($arrInput as &$item){
            $item['begin_time_show'] =  date('Y-m-d H:i:s', $item['begin_time']);
            $item['end_time_show'] =  date('Y-m-d H:i:s', $item['end_time']);
            $item['label_rgb_show'] = "<p style=\"background:" . $item['label_rgb'] .";\">" . $item['label_name']. "</p>";
        }
        unset($item);

        return $arrInput;
    }



    /**
     * 返回值
     *
     * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
     * @param  {Array} $arrOutData default array()
     * @param  {String} $strMsg default ""
     *
     * @return {Array} :errno :errmsg :{Array}output
     * */
    private static function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS, $arrOutData = array(), $strMsg = "")
    {
        $arrOutput            = array();
        $arrOutput['status']  = $intErrno;
        $arrOutput['msg']     = empty($strMsg) ? Alalib_Conf_Error::getErrorMsg($intErrno) : $strMsg;
        $arrOutput['usermsg'] = $strMsg;
        $arrOutput['data']    = $arrOutData;
        
        return $arrOutput;
    }


    /**
     * 结果返回函数
     *
     * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
     * @param  {Array} $arrOutData default array()
     * @param  {String} $strMsg default ""
     *
     * @return {Array} :errno :errmsg :{Array}output
     * */
    private static function resultSender($arrInput, $code_type = 'utf-8')
    {
        echo Bingo_String::array2json($arrInput, $code_type);
        
        return true;
    }
}

?>
