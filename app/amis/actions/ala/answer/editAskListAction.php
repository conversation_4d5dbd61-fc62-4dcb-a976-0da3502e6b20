<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date      2018-1-10
 * @version
 */
class editAskListAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * @return bool
	 */
	public function _execute()
	{
		$guess_id          = intval(Bingo_Http_Request::get('guess_id', -1));
		$arr_question_info = Bingo_Http_Request::get('question_info', '');
		if($guess_id <= 0){
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), '参数错误!');
		}
		//获取旧的题目数据
		$arrServiceInput  = array(
			'guess_id' => $guess_id,
		);
		$strServiceName   = "ala";
		$strServiceMethod = "getGuessQuesListByGuessId";
		$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
		if(false === $arrOutput){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
		}
		if(Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			$arrOutput['data'] = array();
		}
		if(empty($arrOutput['data'])){
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array());
		}else{
			$arrOldQuestions = $arrOutput['data'];
			$arrTem          = array();
			foreach($arrOldQuestions as $item){
				$arrTem[$item['id']] = $item;
			}
			$arrOldQuestions = $arrTem;
			unset($arrTem);
		}
		//用旧的数据和新的进行比较
		foreach($arr_question_info as $singleQuestion){
			if(isset($arrOldQuestions[$singleQuestion['id']])){
				$singleQuestion['options'] = serialize(explode('|', $singleQuestion['options']));
			}else{
				return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array());
			}
			//如果有修改,更新表的数据
			if($arrOldQuestions[$singleQuestion['id']]['question'] == $singleQuestion['question'] && $arrOldQuestions[$singleQuestion['id']]['options'] == $singleQuestion['options'] && $arrOldQuestions[$singleQuestion['id']]['answer'] == $singleQuestion['answer']){
				continue;
			}else{
				$arrOldQuestions[$singleQuestion['id']]['question'] = $singleQuestion['question'];
				$arrOldQuestions[$singleQuestion['id']]['options']  = $singleQuestion['options'];
				$arrOldQuestions[$singleQuestion['id']]['answer']   = $singleQuestion['answer'];
				$arrOldQuestions[$singleQuestion['id']]['id']       = $singleQuestion['id'];
				$arrServiceInput                                    = $arrOldQuestions[$singleQuestion['id']];
				$strServiceName                                     = "ala";
				$strServiceMethod                                   = "updateGuessTopicById";
				$arrOutput                                          = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
				Bingo_Log::warning(var_export($arrOutput, true));
				if(false === $arrOutput){
					$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
					Bingo_Log::fatal($strLog);
				}
				if(Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
					$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
					Bingo_Log::fatal($strLog);
					$arrOutput['data'] = array();
				}
			}
		}
		$intSetToReids = self::setFrontMemory($guess_id);
		if($intSetToReids == false){
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '向Redis存入题目失败!需要重试!');
		}
		
		return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, array());
	}
	
	/**
	 * @param $arrRet
	 */
	public function outputJson($arrRet)
	{
		header("Content-type: application/json");
		echo json_encode($arrRet);
	}
	
	/**
	 * @param
	 *
	 * @return bool
	 */
	private static function setFrontMemory($intGuessId)
	{
		$arrServiceInput  = array(
			'guess_id' => $intGuessId,
		);
		$strServiceName   = "ala";
		$strServiceMethod = "getGuessQuesListByGuessId";
		$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = "call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			echo $strLog."\n";
			
			return false;
		}
		$arrQuestionList = $arrOutput['data'];
		if(!empty($arrQuestionList)){
			$arrServiceInput  = array(
				'guess_id'  => $intGuessId,
				'questions' => $arrQuestionList,
			);
			$strServiceName   = "alaguess";
			$strServiceMethod = "prepareQuestions";
			$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
			if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
				$strLog = "call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
				echo $strLog."\n";
				
				return false;
			}
		}
		
		return true;
	}
}

?>
