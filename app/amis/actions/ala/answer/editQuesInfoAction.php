<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date      2018-1-10
 * @version
 */
class editQuesInfoAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * @return bool
	 */
	public function _execute()
	{
		$guess_id         = intval(Bingo_Http_Request::get('guess_id', 0));
		$money            = intval(Bingo_Http_Request::get('money', 0));
		$start_time       = intval(Bingo_Http_Request::get('start_time', 0));
		$live_id          = intval(Bingo_Http_Request::get('live_id', 0));
		$head_pic         = self::_https2http(strval(Bingo_Http_Request::get('head_pic', 0)));
		$banner_pic       = strval(Bingo_Http_Request::get('banner_pic', 0));
		$float_start_time = intval(Bingo_Http_Request::get('float_start_time', 0));
		$float_end_time   = intval(Bingo_Http_Request::get('float_end_time', 0));
		$float_url        = strval(Bingo_Http_Request::get('float_url', 0));
		$ad_v_pic         = strval(Bingo_Http_Request::get('ad_v_pic', ''));
		$ad_v_jump        = strval(Bingo_Http_Request::get('ad_v_jump', ''));
		$ad_h_pic         = strval(Bingo_Http_Request::get('ad_h_pic', ''));
		$ad_h_jump        = strval(Bingo_Http_Request::get('ad_h_jump', ''));
		$time             = time();
		if($time < $start_time){
			$arrServiceInput  = array(
				'guess_id'   => $guess_id,
				'start_time' => $start_time,
				'award'      => $money,
				'head_pic'   => $head_pic,
			);
			$strServiceName   = "alaguess";
			$strServiceMethod = "punishGuess";
			$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
			if(false === $arrOutput){
				$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
				Bingo_Log::fatal($strLog);
				
				return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '推送竞答信息失败!'."input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
			}
			if(Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
				$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
				Bingo_Log::fatal($strLog);
				$arrOutput['data'] = array();
				
				return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '推送竞答信息失败!请检查输入的开始时间不能比当前时间早或者比当前时间晚7天以上'."input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
			}
		}
		$arrServiceInput  = array(
			'guess_id'         => $guess_id,
			'money'            => $money,
			'start_time'       => $start_time,
			'head_pic'         => $head_pic,
			'banner_pic'       => $banner_pic,
			'live_id'          => $live_id,
			'float_start_time' => $float_start_time,
			'float_end_time'   => $float_end_time,
			'float_url'        => $float_url,
			'ad_v_pic'         => $ad_v_pic,
			'ad_v_jump'        => $ad_v_jump,
			'ad_h_pic'         => $ad_h_pic,
			'ad_h_jump'        => $ad_h_jump,
		);
		$strServiceName   = "ala";
		$strServiceMethod = "updateGuessInfoRecord";
		$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
		if(false === $arrOutput){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '更新表单失败!'."input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
		}
		if(Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			$arrOutput['data'] = array();
			
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '更新表单失败!'."input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
		}
		if(!empty($live_id)){
			$arrServiceInput  = array(
				'live_ids' => array($live_id),
			);
			$strServiceName   = "ala";
			$strServiceMethod = "liveGetInfo";
			$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
			if(false === $arrOutput){
				$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
				Bingo_Log::fatal($strLog);
				
				return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '获取直播信息失败!'."input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
			}
			if(Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
				$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
				Bingo_Log::fatal($strLog);
				$arrOutput['data'] = array();
				
				return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '获取直播信息失败!'."input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
			}
			$arrLiveInfo      = $arrOutput['data'][$live_id]['live_info'];
			$arrServiceInput  = array(
				'live_id'   => $live_id,
				'live_info' => $arrLiveInfo,
			);
			$strServiceName   = "alaguess";
			$strServiceMethod = "guessLiveSetInfo";
			$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
			if(false === $arrOutput){
				$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
				Bingo_Log::fatal($strLog);
				
				return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '直播信息设置到Redis失败!'."input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
			}
			if(Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
				$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
				Bingo_Log::fatal($strLog);
				$arrOutput['data'] = array();
				
				return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '直播信息设置到Redis失败!'."input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
			}
			//文韬的
			$arrServiceInput  = array(
				'guess_id' => $guess_id,
				'live_id'  => $live_id,
			);
			$strServiceName   = "alaguess";
			$strServiceMethod = "mapGuessIdLiveId";
			$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
			if(false === $arrOutput){
				$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
				Bingo_Log::fatal($strLog);
				
				return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '直播ID与guess id 绑定失败!'."input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
			}
			if(Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
				$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
				Bingo_Log::fatal($strLog);
				$arrOutput['data'] = array();
				
				return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '直播ID与guess id 绑定失败!'."input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
			}
		}
		if(!empty($ad_h_pic) || !empty($ad_v_pic)){
			$arrServiceInput  = array(
				'ad_h' => array(
					'pic_url'  => $ad_h_pic,
					'jump_url' => $ad_h_jump,
				),
				'ad_v' => array(
					'pic_url'  => $ad_v_pic,
					'jump_url' => $ad_v_jump,
				),
			);
			$strServiceName   = "alaguess";
			$strServiceMethod = "guessSetAdInfo";
			$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
			if(false === $arrOutput){
				$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
				Bingo_Log::fatal($strLog);
				
				return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '广告信息设置失败!'."input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
			}
			if(Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
				$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
				Bingo_Log::fatal($strLog);
				$arrOutput['data'] = array();
				
				return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '广告信息设置失败!'."input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
			}
		}
		if($time >= $start_time){
			return Util_Ala_Common::jsonAmisRet($arrOutput["errno"], $arrOutput['data'], '设定的开始时间比当前时间早!活动信息未推送!其他设置正常使用!');
		}
		
		return Util_Ala_Common::jsonAmisRet($arrOutput["errno"], $arrOutput['data']);
	}
	
	/**
	 * 将https 替换成http，因为现在客户端目前暂不支持https，图片显示有问题 by haoyunfeng
	 *
	 * @param  $strUrl
	 *
	 * @return string new url
	 */
	private static function _https2http($strUrl)
	{
		$strPos = strpos($strUrl, 'https');
		if(0 === $strPos){
			$strNewUrl = str_ireplace('https', 'http', $strUrl);
			
			return $strNewUrl;
		}
		
		return $strUrl;
	}
	
	/**
	 * @param $arrRet
	 */
	public function outputJson($arrRet)
	{
		header("Content-type: application/json");
		echo json_encode($arrRet);
	}
}

?>
