<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date      2018-1-10
 * @version
 */
class getAskListExcelAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * @return bool
	 */
	public function _execute()
	{
		$guess_id         = intval(Bingo_Http_Request::get('guess_id', -1));
		$arrServiceInput  = array(
			'guess_id' => $guess_id,
		);
		$strServiceName   = "ala";
		$strServiceMethod = "getGuessQuesListByGuessIdForAmis";
		$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
		if(false === $arrOutput){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array());
		}
		if(Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			$arrOutput['data'] = array();
			
			return Util_Ala_Common::jsonAmisRet($arrOutput["errno"], $arrOutput['data']);
		}
		header("Content-type: text/html; charset=gbk");
		header("Content-type:application/vnd.ms-excel");
		header("Content-Disposition:filename=题目信息_比赛编号_$guess_id.xls");
		
		return self::createExcel($arrOutput['data']);
	}
	
	/**
	 * @param $arrInput
	 *
	 * @return null
	 */
	public static function createExcel($arrInput)
	{
		echo iconv("UTF-8", "GBK", "question\t");
		echo iconv("UTF-8", "GBK", "option_1\t");
		echo iconv("UTF-8", "GBK", "option_2\t");
		echo iconv("UTF-8", "GBK", "option_3\t");
		echo iconv("UTF-8", "GBK", "option_4\t");
		echo iconv("UTF-8", "GBK", "answer\t\n");
		foreach($arrInput as $value){
			echo iconv("UTF-8", "GBK", str_replace(array("\r\n", "\r", "\n", "\t"), "", $value['question'])."\t");
			$options = unserialize($value['options']);
			for($i = 0; $i < 4; $i++){
				if(isset($options[$i])){
					$str = $options[$i];
				}else{
					$str = '';
				}
				echo iconv("UTF-8", "GBK", str_replace(array("\r\n", "\r", "\n", "\t"), "", $str)."\t");
			}
			echo iconv("UTF-8", "GBK", str_replace(array("\r\n", "\r", "\n", "\t"), "", $value['answer'])."\t");
			echo "\n";
		}
	}
	
	/**
	 * @param $arrRet
	 */
	public function outputJson($arrRet)
	{
		header("Content-type: application/json");
		echo json_encode($arrRet);
	}
}

?>
