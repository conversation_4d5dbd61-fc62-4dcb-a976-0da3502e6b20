<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date      2018-1-10
 * @version
 */
class setHPicAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * @return bool
	 */
	public function _execute()
	{
		$title            = strval(Bingo_Http_Request::get('title', ''));
		$backgroup_pic    = strval(Bingo_Http_Request::get('backgroup_pic', ''));
		$bottom_pic       = strval(Bingo_Http_Request::get('bottom_pic', ''));
		$bottom_url       = strval(Bingo_Http_Request::get('bottom_url', ''));
		$arrServiceInput  = array(
			'h_info' => array(
				'title'         => $title,
				'backgroup_pic' => $backgroup_pic,
				'bottom_pic'    => $bottom_pic,
				'bottom_url'    => $bottom_url,
			),
		);
		$strServiceName   = "ala";
		$strServiceMethod = "setGuessHPic";
		$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
		if(false === $arrOutput){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), 'H5图片设置失败!');
		}
		if(Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			$arrOutput['data'] = array();
			
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), 'H5图片设置失败!');
		}
		self::setFrontMemory();
		
		return Util_Ala_Common::jsonAmisRet($arrOutput["errno"], array());
	}
	
	/**
	 * @param null
	 *
	 * @return bool
	 */
	private function setFrontMemory()
	{
		$arrServiceInput  = array('status' => 3);
		$strServiceName   = "ala";
		$strServiceMethod = "getNewsestSubGuessId";
		$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
		if(false === $arrOutput){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array());
		}
		if(Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			$arrOutput['data'] = array();
			
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array());
		}
		$intNewGuessId = $arrOutput['data'];
		if(empty($intNewGuessId)){
			return false;
		}
		$arrServiceInput  = array(
			'guess_id' => $intNewGuessId,
		);
		$strServiceName   = "ala";
		$strServiceMethod = "getGuessQuesListByGuessId";
		$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = "call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			echo $strLog."\n";
			
			return false;
		}
		$arrQuestionList  = $arrOutput['data'];
		$arrServiceInput  = array();
		$strServiceName   = "ala";
		$strServiceMethod = "getGuessHPic";
		$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
		if(false === $arrOutput){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array());
		}
		if(Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			$arrOutput['data'] = array();
			
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array());
		}
		$arrSource = $arrOutput['data'];
		if(!empty($arrQuestionList)){
			$arrServiceInput  = array(
				'ques_version' => $intNewGuessId,
				'questions'    => $arrQuestionList,
				'source'       => $arrSource,
			);
			$strServiceName   = "alaguess";
			$strServiceMethod = "prepareSubQuestions";
			$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
			if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
				$strLog = "call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
				echo $strLog."\n";
				
				return false;
			}
		}
		
		return true;
	}
	
	/**
	 * 处理题目信息
	 *
	 * @param $arrAskInfo
	 *
	 * @return array
	 */
	private static function dealAskInfo($arrAskInfo)
	{
		$arrTem = array();
		foreach($arrAskInfo as $item){
			$tem             = array();
			$tem['id']       = $item['id'];
			$tem['question'] = $item['question'];
			$str             = '';
			$arr             = unserialize($item['options']);
			for($i = 0; $i < count($arr); $i++){
				$str .= $arr[$i];
				if($i != count($arr) - 1){
					$str .= '|';
				}
			}
			$tem['options'] = $str;
			$tem['answer']  = $item['answer'];
			$arrTem[]       = $tem;
		}
		
		return $arrTem;
	}
	
	/**
	 * @param $arrRet
	 */
	public function outputJson($arrRet)
	{
		header("Content-type: application/json");
		echo json_encode($arrRet);
	}
}

?>
