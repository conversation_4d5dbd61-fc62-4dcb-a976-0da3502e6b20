<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date      2018-1-10
 * @version
 */
class getHPicAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * @return bool
	 */
	public function _execute()
	{
		$arrServiceInput  = array();
		$strServiceName   = "ala";
		$strServiceMethod = "getGuessHPic";
		$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
		if(false === $arrOutput){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array());
		}
		if(Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			$arrOutput['data'] = array();
			
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array());
		}
		
		return Util_Ala_Common::jsonAmisRet($arrOutput["errno"], $arrOutput['data']);
	}
	
	/**
	 * 处理题目信息
	 *
	 * @param $arrAskInfo
	 *
	 * @return array
	 */
	private static function dealAskInfo($arrAskInfo)
	{
		$arrTem = array();
		foreach($arrAskInfo as $item){
			$tem             = array();
			$tem['id']       = $item['id'];
			$tem['question'] = $item['question'];
			$str             = '';
			$arr             = unserialize($item['options']);
			for($i = 0; $i < count($arr); $i++){
				$str .= $arr[$i];
				if($i != count($arr) - 1){
					$str .= '|';
				}
			}
			$tem['options'] = $str;
			$tem['answer']  = $item['answer'];
			$arrTem[]       = $tem;
		}
		
		return $arrTem;
	}
	
	/**
	 * @param $arrRet
	 */
	public function outputJson($arrRet)
	{
		header("Content-type: application/json");
		echo json_encode($arrRet);
	}
}

?>
