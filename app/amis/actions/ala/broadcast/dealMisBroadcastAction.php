<?php
/**
 * Created by PhpStorm.
 * User: sunzhexuan
 * Date: 2019/6/2
 * Time: 下午4:11
 */

class dealMisBroadcastAction extends Util_Action
{
    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = true;

    const TYPE_ALL_LIVE = 1;
    const TYPE_PART_LIVE = 2;

    const SYS_UID = 852517826;

    const LIVE_OPEN = 1;

    /**
     * @throws Exception
     */
    public function _execute(){
        $strMethod = Bingo_Http_Request::get('method', '');

        switch ($strMethod) {
            case 'bc_add' :
                self::_addBroadCast();
                break;
            case 'bc_query' :
                self::_queryBroadCast();
                break;
            case 'bc_delete' :
                self::_deleteBroadCast();
                break;
            case 'bc_push' :
                self::_pushBroadCast();
                break;
            case 'record_query':
                self::_queryRecord();
                break;
        }
    }

    /**
     * 新增  广播mis配置信息
     * @return int
     * @throws Exception
     */
    private function _addBroadCast(){
        $strName        = strval(Bingo_Http_Request::get('name', ''));
        $strText        = strval(Bingo_Http_Request::get('text', ''));
        $intPushType    = intval(Bingo_Http_Request::get('push_type', 0));
        $strLiveIds     = strval(Bingo_Http_Request::get('live_ids', ''));
        $strSubAppType  = strval(Bingo_Http_Request::get('subapp_type', ''));
        $intJumpLiveId  = intval(Bingo_Http_Request::get('jump_live_id', 0));
        $strRemark      = strval(Bingo_Http_Request::get('remark', ''));
        $intOperateTime = Bingo_Timer::getNowTime();
        $strOperator    = Util_User::$strUserName;

        if(self::TYPE_PART_LIVE == $intPushType && empty($strLiveIds)){
            return $this->_jsonRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }

        $arrInput = array(
            'name'         => $strName,
            'text'         => $strText,
            'push_type'    => $intPushType,
            'live_ids'     => $strLiveIds,
            'jump_live_id' => $intJumpLiveId,
            'operate_time' => $intOperateTime,
            'operator'     => $strOperator,
            'subapp_type'  => $strSubAppType,
            'remark'       => $strRemark,
        );
        $arrOutput = Service_Ala_Prop_Prop::insertNoticeBroadcastInfo($arrInput);
        if ($arrOutput === false || $arrOutput['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Prop_Prop::insertNoticeBroadcastInfo fail. input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS,'',$arrOutput);
    }

    /**
     * 查询 广播mis配置信息
     * @return int
     * @throws Exception
     */
    private function _queryBroadCast(){
        $intPn          = intval(Bingo_Http_Request::get('pn', 0));
        $intPs          = intval(Bingo_Http_Request::get('ps', 0));
        $strSubAppType  = strval(Bingo_Http_Request::get('subapp_type', ''));

        $arrInput = array(
            'offset' => ($intPn - 1)*$intPs,
            'limit'  => $intPs,
            'subapp_type' => $strSubAppType,
        );
        $arrOutput = Service_Ala_Prop_Prop::selectNoticeBroadcastInfo($arrInput);
        if ($arrOutput === false || $arrOutput['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Prop_Prop::selectNoticeBroadcastInfo fail. input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS,'',$arrOutput['data']);
    }

    /**
     * 删除  广播mis配置信息
     * @return int
     * @throws Exception
     */
    private function _deleteBroadCast(){
        $intId         = intval(Bingo_Http_Request::get('id', 0));
        $strSubAppType = strval(Bingo_Http_Request::get('subapp_type', ''));
        $arrInput = array(
            'id'    => $intId,
            'subapp_type' => $strSubAppType,
        );
        $arrOutput = Service_Ala_Prop_Prop::deleteNoticeBroadcastInfo($arrInput);
        if ($arrOutput === false || $arrOutput['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Prop_Prop::deleteNoticeBroadcastInfo fail. input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }

        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS,'',$arrOutput);
    }

    /**
     * 推送
     * @return int
     * @throws Exception
     */
    private function _pushBroadCast(){
        $intId          = intval(Bingo_Http_Request::get('id', 0));
        $strName        = strval(Bingo_Http_Request::get('name', ''));
        $strText        = strval(Bingo_Http_Request::get('text', ''));
        $intPushType    = intval(Bingo_Http_Request::get('push_type', 0));
        $strLiveIds     = strval(Bingo_Http_Request::get('live_ids', ''));
        $strSubAppType  = strval(Bingo_Http_Request::get('subapp_type', ''));
        $intJumpLiveId  = intval(Bingo_Http_Request::get('jump_live_id', 0));
        $intOperateTime = Bingo_Timer::getNowTime();
        $strOperator    = Util_User::$strUserName;


        if(self::TYPE_ALL_LIVE == $intPushType){
            $arrReq = array(
                'text' => $strText,
                'jump_live_id' => $intJumpLiveId,
                'subapp_type'  => $strSubAppType,
            );
            $arrOutput = Tieba_Service::call('ala', 'pushMisBroadCastNotify', $arrReq, null, null, 'post', 'php', 'utf-8');
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala::pushMisBroadCastNotify fail. input:[" . serialize($arrReq) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);

                //add record
                $arrInput = array(
                    'notice_id' => $intId,
                    'notice_name' => $strName,
                    'operate_time' => $intOperateTime,
                    'operator'     => $strOperator,
                    'subapp_type'  => $strSubAppType,
                    'status'       => 2,
                );
                self::addPushRecord($arrInput);
                return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL,'推送失败');
            }

            //add record
            $arrInput = array(
                'notice_id' => $intId,
                'notice_name' => $strName,
                'operate_time' => $intOperateTime,
                'operator'     => $strOperator,
                'subapp_type'  => $strSubAppType,
                'status'       => 1,
            );
            self::addPushRecord($arrInput);
        }
        if(self::TYPE_PART_LIVE == $intPushType){
            if(!empty($strLiveIds)){
                $arrLiveIds = explode(',' , $strLiveIds);
                $arrReq    = array(
                    'live_ids' => $arrLiveIds,
                );
                $arrOutput = Tieba_Service::call('ala', 'liveGetInfo', $arrReq, null, null, 'post', 'php', 'utf-8');
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala::liveGetInfo fail. input:[" . serialize($arrReq) . "]; output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                    return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL,'推送失败,直播信息未查到');
                }

                $arrPushInfo = array();
                foreach ($arrOutput['data'] as $k => $v){
                    if(self::LIVE_OPEN != $v['live_info']['live_status']){
                        continue;
                    }
                    $arrItem = array(
                        'group_id' => $v['live_info']['group_id'],
                        'text' => $strText,
                        'jump_live_id' => $intJumpLiveId,
                        'user_id' => self::SYS_UID,
                    );
                    $arrPushInfo[] = $arrItem;
                }

                if(!empty($arrPushInfo)){
                    $serviceName = 'ala';
                    $methodName  = 'imPushLiveBroadcastNotice';
                    $objMultiCall    = new Tieba_Multi('push_broadcast');

                    foreach($arrPushInfo  as $item  => $value){
                        $methodParam = $value;
                        $arrInput = array(
                            "serviceName" => $serviceName,
                            "method"      => $methodName,
                            'ie'          => 'utf-8',
                            "input"       => $methodParam,

                        );
                        $objMultiCall->register($methodName.'_'.$item, new Tieba_Service($serviceName), $arrInput);
                    }

                    $objMultiCall->call();

                    foreach ($arrPushInfo as $item => $value) {
                        $methodResName = $methodName . '_' . $item;
                        $arrOutput = $objMultiCall->getResult($methodResName);
                        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                            $methodParam = $arrPushInfo[$item];
                            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $serviceName $methodName fail. input:[" . serialize($methodParam) . "]  output:[" . serialize($arrOutput) . "]";
                            Bingo_Log::warning($strLog);
                        }
                    }
                }

                //add record
                $arrInput = array(
                    'notice_id' => $intId,
                    'notice_name' => $strName,
                    'operate_time' => $intOperateTime,
                    'operator'     => $strOperator,
                    'subapp_type'  => $strSubAppType,
                    'status'       => 1,
                );
                self::addPushRecord($arrInput);
            }
        }

        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS,'推送成功');
    }

    /**
     * 查询 广播mis推送记录
     * @return int
     * @throws Exception
     */
    private function _queryRecord(){
        $intPn          = intval(Bingo_Http_Request::get('pn', 0));
        $intPs          = intval(Bingo_Http_Request::get('ps', 0));
        $strSubAppType  = strval(Bingo_Http_Request::get('subapp_type', ''));

        $arrInput = array(
            'offset' => ($intPn - 1)*$intPs,
            'limit'  => $intPs,
            'subapp_type' => $strSubAppType,
        );
        $arrOutput = Service_Ala_Prop_Prop::selectNoticeBroadcastRecord($arrInput);
        if ($arrOutput === false || $arrOutput['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Prop_Prop::selectNoticeBroadcastRecord fail. input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS,'',$arrOutput['data']);
    }

    /**
     * @param $arrInput
     */
    private function addPushRecord($arrInput){
        $arrOutput = Service_Ala_Prop_Prop::insertNoticeBroadcastRecord($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Prop_Prop::insertNoticeBroadcastRecord fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
        }
    }

    /**
     * @param $errno
     * @param string $errmsg
     * @param array $arrExtData
     * @return int
     * @throws Exception
     */
    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => strval($errmsg),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }
}