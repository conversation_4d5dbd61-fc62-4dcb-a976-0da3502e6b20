<?php

/**
 * Created by PhpStorm.
 * User: zhouyan08
 * Date: 18/4/27
 * Time: 下午4:48
 */
class editBigDayAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * 执行函数
	 *
	 * @param null
	 *
	 * @return null
	 * */
	public function _execute()
	{
		//数据获取
		$id                    = intval(Bingo_Http_Request::get('id', ''));
		$type                  = intval(Bingo_Http_Request::get('type', ''));
		$name                  = strval(Bingo_Http_Request::get('name', ''));
		$start_time            = intval(Bingo_Http_Request::get('start_time', ''));
		$end_time              = intval(Bingo_Http_Request::get('end_time', ''));
		$ios_pic_host_1        = self::_https2http(strval(Bingo_Http_Request::get('ios_pic_host_1', '')));
		$ios_pic_host_2        = self::_https2http(strval(Bingo_Http_Request::get('ios_pic_host_2', '')));
		$android_pic_host_1    = self::_https2http(strval(Bingo_Http_Request::get('android_pic_host_1', '')));
		$android_pic_host_2    = self::_https2http(strval(Bingo_Http_Request::get('android_pic_host_2', '')));
		$ios_pic_publish_1     = self::_https2http(strval(Bingo_Http_Request::get('ios_pic_publish_1', '')));
		$ios_pic_publish_2     = self::_https2http(strval(Bingo_Http_Request::get('ios_pic_publish_2', '')));
		$android_pic_publish_1 = self::_https2http(strval(Bingo_Http_Request::get('android_pic_publish_1', '')));
		$android_pic_publish_2 = self::_https2http(strval(Bingo_Http_Request::get('android_pic_publish_2', '')));
		$ios_pic_search_1      = self::_https2http(strval(Bingo_Http_Request::get('ios_pic_search_1', '')));
		$ios_pic_search_2      = self::_https2http(strval(Bingo_Http_Request::get('ios_pic_search_2', '')));
		$android_pic_search_1  = self::_https2http(strval(Bingo_Http_Request::get('android_pic_search_1', '')));
		$android_pic_search_2  = self::_https2http(strval(Bingo_Http_Request::get('android_pic_search_2', '')));
		$forum_id = intval(Bingo_Http_Request::get('forum_id', ''));
		$op_id                 = Util_User::$intUserId;
		$op_name               = Util_User::$strUserName;
		$arrInput              = array(
			'forum_id'              => $forum_id,
			'id'                    => $id,
			'type'                  => $type,
			'name'                  => $name,
			'start_time'            => $start_time,
			'end_time'              => $end_time,
			'ios_pic_host_1'        => $ios_pic_host_1,
			'ios_pic_host_2'        => $ios_pic_host_2,
			'android_pic_host_1'    => $android_pic_host_1,
			'android_pic_host_2'    => $android_pic_host_2,
			'ios_pic_publish_1'     => $ios_pic_publish_1,
			'ios_pic_publish_2'     => $ios_pic_publish_2,
			'android_pic_publish_1' => $android_pic_publish_1,
			'android_pic_publish_2' => $android_pic_publish_2,
			'ios_pic_search_1'      => $ios_pic_search_1,
			'ios_pic_search_2'      => $ios_pic_search_2,
			'android_pic_search_1'  => $android_pic_search_1,
			'android_pic_search_2'  => $android_pic_search_2,
			'operator'              => $op_name,
			'operator_id'           => $op_id,
		);
		//需要补全service 修改bigday信息
		$arrOutput = Tieba_Service::call('tbmall', 'editBigDayList', $arrInput, null, null, 'post', null, 'utf-8');
		if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '编辑失败!');
		}
		
		return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, array(), '编辑成功!');
	}
	
	/**
	 * 将https 替换成http，因为现在客户端目前暂不支持https，图片显示有问题
	 *
	 * @param  $strUrl
	 *
	 * @return string new url
	 */
	private static function _https2http($strUrl)
	{
		$strPos = strpos($strUrl, 'https');
		if(0 === $strPos){
			$strNewUrl = str_ireplace('https', 'http', $strUrl);
			
			return $strNewUrl;
		}
		
		return $strUrl;
	}
	
}

?>
