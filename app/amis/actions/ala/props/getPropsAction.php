<?php
/**
 * Created by PhpStorm.
 * User: zhanghanqing
 * Date: 2019/4/30
 * Time: 7:12 PM
 */


class getPropsAction extends Util_Action
{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    /**
     * @param
     * @return array
     */
    public function _execute()
    {
        // 操作人
        $intUserId = Util_User::$intUserId;
        $strUserName = Util_User::$strUserName;

        // Request
        $intPropsId = intval(Bingo_Http_Request::get('props_id', 0));

        $arrServiceInput = array(
            "props" => array($intPropsId),
        );
        $strServiceName = "tbmall";
        $strServiceMethod = "mgetPropsByIds";
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,'post',null,'utf-8');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__. "::". __FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $arrItem = $arrOutput['props'][0];
        if (empty($arrItem)){
            $arrItem['props_id'] = 0;
            $arrItem['title'] = '不存在';
        }
        $arrList[] = $arrItem;

        // 返回列表
        $arrRet = array(
            'rows' => $arrList,
            // 'count' => $intCount
        );
        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, "", $arrRet);
    }

    /**
     * @brief _jsonRet
     *
     * @param
     *            errno,errmsg,data
     * @return : 0.
     *
     */
    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => strval($errmsg),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }
}



