<?php
/**
 * incomeAction.php
 * author:zhangxu38
 * Created on 2021-03-29 20:41
 * 收入管理
 */

class incomeAction extends Util_Action
{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner      = false;
    protected $bolNeedLogin      = false;
    const PageSize = 10;


    public function _execute()
    {
        $strMethod = Bingo_Http_Request::get('method', '');
        switch ($strMethod) {
            case 'get' :
                self::_get();
                break;
            case 'export' :
                self::_export();
                break;
            case 'getDetail' :
                self::_getDetail();
                break;
            case 'exportDetail' :
                self::_exportDetail();
                break;
            case 'getGuest' :
                self::_getGuest();
                break;
            case 'exportGuest' :
                self::_exportGuest();
                break;
            case 'getDetailGuest' :
                self::_getDetailGuest();
                break;
            case 'exportDetailGuest' :
                self::_exportDetailGuest();
                break;
            default :
                break;
        }
    }

    // 主播收入管理
    private static function _get()
    {
        $intPn           = intval(Bingo_Http_Request::get('page', 0));
        $intPs           = intval(Bingo_Http_Request::get('ps', self::PageSize));
        $guild_id        = intval(Bingo_Http_Request::get('guild_id', 0));
        $divide_type     = intval(Bingo_Http_Request::get('divide_type', 0));  //后续可能会换
        $rule_id         = intval(Bingo_Http_Request::get('rule_id', 0));  //后续可能会换
        $begin_time      = intval(Bingo_Http_Request::get('begin_time', 0));
        $end_time        = intval(Bingo_Http_Request::get('end_time', 0));
        $passport_id     = intval(Bingo_Http_Request::get('passport_id', 0));
        $anchor_type     = intval(Bingo_Http_Request::get('anchor_type', 0));   //主播类型：1为百家号，2为手百
        $settlement_type = intval(Bingo_Http_Request::get('settlement_type', 0)); //结算关系：1主播，2公会

        $arrInput = array(
            'pn'              => $intPn,
            'ps'              => $intPs,
            'settlement_type' => $settlement_type,
            'guild_id'        => $guild_id,
            'rule_id'         => $rule_id,
            'divide_type'     => $divide_type,
            'passport_id'     => $passport_id,
            'begin_time'      => $begin_time,
            'end_time'        => $end_time,
            'anchor_type'     => $anchor_type,
        );

        //收入记录
        $arrOutput = Service_Ala_Media_Media::getIncomeList($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Media_Media::getIncomeList fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        //去查规则详情
        $rule_id = array();
        foreach ($arrOutput['data']['rows'] as $item) {
            $rule_id[] = intval($item['rule_id']);
        }
        $arrInput = array(
            'rule_id' => $rule_id,
        );

        $arrOutput1 = Service_Ala_Media_Media::getDivideRatio($arrInput);
        if (false === $arrOutput1 || Tieba_Errcode::ERR_SUCCESS != $arrOutput1['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Media_Media::getDivideRatio fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrRules = $arrOutput1['data']['rows'];

        //合并数据
        $arrItem = array();
        if (!empty($arrRules)) {
            foreach ($arrRules as $item) {
                $arrItem[$item['rule_id']] = $item;
            }
        }
        $arrEmpty = array(
            'rule_id'              => 1,
            'rule_name'            => '默认分成',
            'reward'               => 0.5,
            'chat'                 => 0.7,
            'written_consultation' => 0.7,
            'chat_consultation'    => 0.7,
            // 相亲配置
            'chat_date'            => 0.5, // 相亲连麦
            'reward_anchor_date'   => 0.5, // 相亲主播收礼(含背包)
            'reward_guest1_date'   => 0.3, // 相亲嘉宾收礼（含背包）-主播分成
            'reward_guest2_date'   => 0.4, // 相亲嘉宾收礼（含背包）-嘉宾分成
        );

        /**
         * 相亲直播间新增计费项：
         * 20-百家号送礼给主播 21-手百送礼给主播 22-送背包给主播(目前仅pgc有背包)
         * 23-送背包给嘉宾_主播分成 24-送背包给嘉宾_嘉宾分成
         * 25-百家号送礼给嘉宾_主播分成 26-百家号送礼给嘉宾_嘉宾分成
         * 27-手百送礼给嘉宾_主播分成 28-手百送礼给嘉宾_嘉宾分成
         * 29-相亲连麦
         */

        if (!empty($arrItem)) {
            foreach ($arrOutput['data']['rows'] as &$value) {
                if (isset($arrItem[$value['rule_id']])) {
                    $value = array_merge($value, $arrItem[$value['rule_id']]);
                } else {
                    $value = array_merge($value, $arrEmpty);   //兜底
                }

                if (in_array($value['scene_id'], array(11, 12))) {
                    $value['base'] = $value['reward'];
                    $value['billingItems'] = '普通礼物打赏';
                }

                if (in_array($value['scene_id'], array(20, 21))) {
                    $value['base'] = $value['reward_anchor_date'];
                    $value['billingItems'] = '相亲主播礼物打赏';
                }

                // 目前相亲直播间，只能给主播送背包礼物
                if (in_array($value['scene_id'], array(22, 23))) {
                    $value['base']         = $value['reward_anchor_date'];
                    $value['billingItems'] = '相亲背包礼物';
                }

                if (in_array($value['scene_id'], array(25, 27))) {
                    $value['base'] = $value['reward_guest1_date'];
                    $value['billingItems'] = '相亲嘉宾礼物打赏';
                }

                if (in_array($value['scene_id'], array(29))) {
                    $value['base']         = $value['chat_date'];
                    $value['billingItems'] = '相亲连麦';
                }

                if (in_array($value['scene_id'], array(13))) {
                    $value['base']         = $value['reward'];
                    $value['billingItems'] = '普通背包礼物';
                }
                if (in_array($value['scene_id'], array(2))) {
                    $value['base']         = $value['chat'];
                    $value['billingItems'] = '付费连麦';
                }
                if (in_array($value['scene_id'], array(3))) {
                    $value['base']         = $value['written_consultation'];
                    $value['billingItems'] = '文字咨询';
                }
                if (in_array($value['scene_id'], array(4))) {
                    $value['base']         = $value['chat_consultation'];
                    $value['billingItems'] = '连麦咨询';
                }

                $value['order_money'] = $value['money'] / $value['base'];


            }
        }


        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrOutput['data']);
    }

    // 嘉宾收入管理
    private static function _getGuest()
    {
        $intPn           = intval(Bingo_Http_Request::get('page', 0));
        $intPs           = intval(Bingo_Http_Request::get('ps', self::PageSize));
        $guild_id        = intval(Bingo_Http_Request::get('guild_id', 0));
        $divide_type     = intval(Bingo_Http_Request::get('divide_type', 0));  //后续可能会换
        $rule_id         = intval(Bingo_Http_Request::get('rule_id', 0));  //后续可能会换
        $begin_time      = intval(Bingo_Http_Request::get('begin_time', 0));
        $end_time        = intval(Bingo_Http_Request::get('end_time', 0));
        $passport_id     = intval(Bingo_Http_Request::get('passport_id', 0));
        $anchor_type     = intval(Bingo_Http_Request::get('anchor_type', 0));   //主播类型：1为百家号，2为手百
        $settlement_type = intval(Bingo_Http_Request::get('settlement_type', 0)); //结算关系：1主播，2公会

        $arrInput = array(
            'pn'              => $intPn,
            'ps'              => $intPs,
            'settlement_type' => $settlement_type,
            'guild_id'        => $guild_id,
            'rule_id'         => $rule_id,
            'divide_type'     => $divide_type,
            'passport_id'     => $passport_id,
            'begin_time'      => $begin_time,
            'end_time'        => $end_time,
            'anchor_type'     => $anchor_type,
        );

        //收入记录
        $arrOutput = Service_Ala_Media_Media::getGuestIncomeList($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Media_Media::getIncomeList fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        //去查规则详情
        $rule_id = array();
        foreach ($arrOutput['data']['rows'] as $item) {
            $rule_id[] = intval($item['rule_id']);
        }
        $arrInput = array(
            'rule_id' => $rule_id,
        );

        $arrOutput1 = Service_Ala_Media_Media::getDivideRatio($arrInput);
        if (false === $arrOutput1 || Tieba_Errcode::ERR_SUCCESS != $arrOutput1['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Media_Media::getDivideRatio fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrRules = $arrOutput1['data']['rows'];

        //合并数据
        $arrItem = array();
        if (!empty($arrRules)) {
            foreach ($arrRules as $item) {
                $arrItem[$item['rule_id']] = $item;
            }
        }
        $arrEmpty = array(
            'rule_id'              => 1,
            'rule_name'            => '默认分成',
            'reward'               => 0.5,
            'chat'                 => 0.7,
            'written_consultation' => 0.7,
            'chat_consultation'    => 0.7,
            // 相亲配置
            'chat_date'            => 0.5, // 相亲连麦
            'reward_anchor_date'   => 0.5, // 相亲主播收礼(含背包)
            'reward_guest1_date'   => 0.3, // 相亲嘉宾收礼（含背包）-主播分成
            'reward_guest2_date'   => 0.4, // 相亲嘉宾收礼（含背包）-嘉宾分成
        );

        /**
         * 相亲直播间新增计费项：
         * 20-百家号送礼给主播 21-手百送礼给主播 22-送背包给主播(目前仅pgc有背包)
         * 23-送背包给嘉宾_主播分成 24-送背包给嘉宾_嘉宾分成
         * 25-百家号送礼给嘉宾_主播分成 26-百家号送礼给嘉宾_嘉宾分成
         * 27-手百送礼给嘉宾_主播分成 28-手百送礼给嘉宾_嘉宾分成
         * 29-相亲连麦
         */

        if (!empty($arrItem)) {
            foreach ($arrOutput['data']['rows'] as &$value) {
                if (isset($arrItem[$value['rule_id']])) {
                    $value = array_merge($value, $arrItem[$value['rule_id']]);
                } else {
                    $value = array_merge($value, $arrEmpty);   //兜底
                }

                if (in_array($value['scene_id'], array(26, 28))) {
                    $value['base']         = $value['reward_guest2_date'];
                    $value['billingItems'] = '礼物打赏';
                }

                if (in_array($value['scene_id'], array(24))) {
                    $value['base']         = $value['reward_guest2_date'];
                    $value['billingItems'] = '背包打赏';

                }

                $value['order_money'] = $value['money'] / $value['base'];

            }
        }

        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrOutput['data']);
    }


    private static function _export()
    {
        $intPn           = intval(Bingo_Http_Request::get('page', 0));
        $intPs           = intval(Bingo_Http_Request::get('ps', self::PageSize));
        $guild_id        = intval(Bingo_Http_Request::get('guild_id', 0));
        $divide_type     = intval(Bingo_Http_Request::get('divide_type', 0));  //后续可能会换
        $rule_id         = intval(Bingo_Http_Request::get('rule_id', 0));  //后续可能会换
        $begin_time      = intval(Bingo_Http_Request::get('begin_time', 0));
        $end_time        = intval(Bingo_Http_Request::get('end_time', 0));
        $passport_id     = intval(Bingo_Http_Request::get('passport_id', 0));
        $anchor_type     = intval(Bingo_Http_Request::get('anchor_type', 0));   //主播类型：1为百家号，2为手百
        $settlement_type = intval(Bingo_Http_Request::get('settlement_type', 0)); //结算关系：1主播，2公会

        $arrInput = array(
            'pn'              => $intPn,
            'ps'              => 50000,
            'settlement_type' => $settlement_type,
            'guild_id'        => $guild_id,
            'rule_id'         => $rule_id,
            'divide_type'     => $divide_type,
            'passport_id'     => $passport_id,
            'begin_time'      => $begin_time,
            'end_time'        => $end_time,
            'anchor_type'     => $anchor_type,
        );

        $arrOutput = Service_Ala_Media_Media::getIncomeList($arrInput);

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Media_Media::getIncomeList fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        //去查规则详情
        $rule_id = array();
        foreach ($arrOutput['data']['rows'] as $item) {
            $rule_id[] = intval($item['rule_id']);
        }
        $arrInput = array(
            'rule_id' => $rule_id,
        );

        $arrOutput1 = Service_Ala_Media_Media::getDivideRatio($arrInput);
        if (false === $arrOutput1 || Tieba_Errcode::ERR_SUCCESS != $arrOutput1['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Media_Media::getDivideRatio fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrRules = $arrOutput1['data']['rows'];

        //合并数据
        $arrItem = array();
        if (!empty($arrRules)) {
            foreach ($arrRules as $item) {
                $arrItem[$item['rule_id']] = $item;
            }
        }
        $arrEmpty = array(
            'rule_id'              => 1,
            'rule_name'            => '默认分成',
            'reward'               => 0.5,
            'chat'                 => 0.7,
            'written_consultation' => 0.7,
            'chat_consultation'    => 0.7,
            // 相亲配置
            'chat_date'            => 0.5, // 相亲连麦
            'reward_anchor_date'   => 0.5, // 相亲主播收礼(含背包)
            'reward_guest1_date'   => 0.3, // 相亲嘉宾收礼（含背包）-主播分成
            'reward_guest2_date'   => 0.4, // 相亲嘉宾收礼（含背包）-嘉宾分成
        );
        if (!empty($arrItem)) {
            foreach ($arrOutput['data']['rows'] as &$value) {
                if (isset($arrItem[$value['rule_id']])) {
                    $value = array_merge($value, $arrItem[$value['rule_id']]);
                } else {
                    $value = array_merge($value, $arrEmpty);   //兜底
                }

                if (in_array($value['scene_id'], array(20))) {
                    $value['base']         = $value['reward_anchor_date'];
                    $value['billingItems'] = '相亲礼物打赏';
                    $value['anchorType']   = '百家号';
                }

                if (in_array($value['scene_id'], array(21))) {
                    $value['base']         = $value['reward_anchor_date'];
                    $value['billingItems'] = '相亲礼物打赏';
                    $value['anchorType']   = '手百';
                }

                if (in_array($value['scene_id'], array(22))) {
                    $value['base']         = $value['reward_guest1_date'];
                    $value['billingItems'] = '相亲背包礼物';
                    $value['anchorType']   = '百家号';
                }

                if (in_array($value['scene_id'], array(25))) {
                    $value['base']         = $value['reward_guest1_date'];
                    $value['billingItems'] = '相亲礼物打赏-嘉宾';
                    $value['anchorType']   = '百家号';
                }

                if (in_array($value['scene_id'], array(27))) {
                    $value['base']         = $value['reward_guest1_date'];
                    $value['billingItems'] = '相亲礼物打赏-嘉宾';
                    $value['anchorType']   = '手百';
                }

                if (in_array($value['scene_id'], array(29))) {
                    $value['base']         = $value['chat_date'];
                    $value['billingItems'] = '相亲连麦';
                    $value['anchorType']   = '百家号';
                }

                // 此注释上方为相亲直播间相关数据
                if (in_array($value['scene_id'], array(11))) {
                    $value['base']         = $value['reward'];
                    $value['billingItems'] = '礼物打赏';
                    $value['anchorType']   = '百家号';
                }
                if (in_array($value['scene_id'], array(12))) {
                    $value['base']         = $value['reward'];
                    $value['billingItems'] = '礼物打赏';
                    $value['anchorType']   = '手百';
                }
                if (in_array($value['scene_id'], array(13))) {
                    $value['base']         = $value['reward'];
                    $value['billingItems'] = '背包礼物';
                    $value['anchorType']   = '百家号';
                }

                if (in_array($value['scene_id'], array(2))) {
                    $value['base']         = $value['chat'];
                    $value['billingItems'] = '付费连麦';
                    $value['anchorType']   = '百家号';

                }
                if (in_array($value['scene_id'], array(3))) {
                    $value['base']         = $value['written_consultation'];
                    $value['billingItems'] = '文字咨询';
                    $value['anchorType']   = '百家号';

                }
                if (in_array($value['scene_id'], array(4))) {
                    $value['base']         = $value['chat_consultation'];
                    $value['billingItems'] = '连麦咨询';
                    $value['anchorType']   = '百家号';

                }

                if ($value['divide_type'] == 1) {
                    $value['divide_type'] = '基础';
                } else {
                    $value['divide_type'] = '激励';
                }


                if ($value['settlement_type'] == 1) {
                    $value['settlement_type'] = '主播';
                } else {
                    $value['settlement_type'] = '公会';
                }

                $value['order_money'] = $value['money'] / $value['base'];

            }
        }

        header("Content-type: text/html; charset=gbk");
        header("Content-type:application/vnd.ms-excel");
        header("Content-Disposition:filename=daily_sum.xls");
        self::createExcel($arrOutput);

        return;
        //return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, '导出完成');

    }

    // 导出嘉宾收入管理汇总数据
    private static function _exportGuest()
    {
        $intPn           = intval(Bingo_Http_Request::get('page', 0));
        $intPs           = intval(Bingo_Http_Request::get('ps', self::PageSize));
        $guild_id        = intval(Bingo_Http_Request::get('guild_id', 0));
        $divide_type     = intval(Bingo_Http_Request::get('divide_type', 0));  //后续可能会换
        $rule_id         = intval(Bingo_Http_Request::get('rule_id', 0));  //后续可能会换
        $begin_time      = intval(Bingo_Http_Request::get('begin_time', 0));
        $end_time        = intval(Bingo_Http_Request::get('end_time', 0));
        $passport_id     = intval(Bingo_Http_Request::get('passport_id', 0));
        $anchor_type     = intval(Bingo_Http_Request::get('anchor_type', 0));   //主播类型：1为百家号，2为手百
        $settlement_type = intval(Bingo_Http_Request::get('settlement_type', 0)); //结算关系：1主播，2公会

        $arrInput = array(
            'pn'              => $intPn,
            'ps'              => 50000,
            'settlement_type' => $settlement_type,
            'guild_id'        => $guild_id,
            'rule_id'         => $rule_id,
            'divide_type'     => $divide_type,
            'passport_id'     => $passport_id,
            'begin_time'      => $begin_time,
            'end_time'        => $end_time,
            'anchor_type'     => $anchor_type,
        );

        $arrOutput = Service_Ala_Media_Media::getGuestIncomeList($arrInput);

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Media_Media::getIncomeList fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        //去查规则详情
        $rule_id = array();
        foreach ($arrOutput['data']['rows'] as $item) {
            $rule_id[] = intval($item['rule_id']);
        }
        $arrInput = array(
            'rule_id' => $rule_id,
        );

        $arrOutput1 = Service_Ala_Media_Media::getDivideRatio($arrInput);
        if (false === $arrOutput1 || Tieba_Errcode::ERR_SUCCESS != $arrOutput1['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Media_Media::getDivideRatio fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrRules = $arrOutput1['data']['rows'];

        //合并数据
        $arrItem = array();
        if (!empty($arrRules)) {
            foreach ($arrRules as $item) {
                $arrItem[$item['rule_id']] = $item;
            }
        }
        $arrEmpty = array(
            'rule_id'              => 1,
            'rule_name'            => '默认分成',
            'reward'               => 0.5,
            'chat'                 => 0.7,
            'written_consultation' => 0.7,
            'chat_consultation'    => 0.7,
            // 相亲配置
            'chat_date'            => 0.5, // 相亲连麦
            'reward_anchor_date'   => 0.5, // 相亲主播收礼(含背包)
            'reward_guest1_date'   => 0.3, // 相亲嘉宾收礼（含背包）-主播分成
            'reward_guest2_date'   => 0.4, // 相亲嘉宾收礼（含背包）-嘉宾分成
        );

        if (!empty($arrItem)) {
            foreach ($arrOutput['data']['rows'] as &$value) {
                if (isset($arrItem[$value['rule_id']])) {
                    $value = array_merge($value, $arrItem[$value['rule_id']]);
                } else {
                    $value = array_merge($value, $arrEmpty);   //兜底
                }

                if (in_array($value['scene_id'], array(26))) {
                    $value['base']         = $value['reward_guest2_date'];
                    $value['billingItems'] = '百家号礼物打赏';
                    $value['anchorType']   = '百家号';
                }

                if (in_array($value['scene_id'], array(28))) {
                    $value['base']         = $value['reward_guest2_date'];
                    $value['billingItems'] = '手百礼物打赏';
                    $value['anchorType']   = '手百';
                }

                if (in_array($value['scene_id'], array(24))) {
                    $value['base']         = $value['reward_guest2_date'];
                    $value['billingItems'] = '背包打赏';
                    $value['anchorType']   = '百家号';
                }

                if ($value['divide_type'] == 1) {
                    $value['divide_type'] = '基础';
                } else {
                    $value['divide_type'] = '激励';
                }

                if ($value['settlement_type'] == 1) {
                    $value['settlement_type'] = '主播';
                } else {
                    $value['settlement_type'] = '公会';
                }

                $value['order_money'] = $value['money'] / $value['base'];

            }
        }


        header("Content-type: text/html; charset=gbk");
        header("Content-type:application/vnd.ms-excel");
        header("Content-Disposition:filename=daily_sum.xls");
        self::createExcel($arrOutput);

        return;
        //return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, '导出完成');

    }

    private static function _getDetail()
    {
        $intPn       = intval(Bingo_Http_Request::get('page', 0));
        $intPs       = intval(Bingo_Http_Request::get('ps', self::PageSize));
        $order_id    = strval(Bingo_Http_Request::get('order_id', ''));
        $client_type = intval(Bingo_Http_Request::get('client_type', 0));  //后续可能会换
        $kaibo_type  = intval(Bingo_Http_Request::get('kaibo_type', 0));  //后续可能会换
        $begin_time  = intval(Bingo_Http_Request::get('begin_time', 0));
        $end_time    = intval(Bingo_Http_Request::get('end_time', 0));
        $passport_id = intval(Bingo_Http_Request::get('user_id', 0));
        $scene_id    = intval(Bingo_Http_Request::get('scene_id', 0));
        $one_day     = intval(Bingo_Http_Request::get('one_day', 0));
        if ($one_day == 1) {
            $end_time = $begin_time + 86400;
        }


        $arrInput = array(
            'pn'          => $intPn,
            'ps'          => $intPs,
            'order_id'    => strval($order_id),
            'client_type' => $client_type,
            'kaibo_type'  => $kaibo_type,
            'passport_id' => $passport_id,
            'begin_time'  => $begin_time,
            'end_time'    => $end_time,
            'scene_id'    => $scene_id
        );

        //收入记录
        $arrOutput = Service_Ala_Media_Media::getDetailList($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Media_Media::getDetailList fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        //去查规则详情
        $rule_id = array();
        foreach ($arrOutput['data']['rows'] as $item) {
            $rule_id[] = intval($item['rule_id']);
        }
        $arrInput = array(
            'rule_id' => $rule_id,
        );

        $arrOutput1 = Service_Ala_Media_Media::getDivideRatio($arrInput);
        if (false === $arrOutput1 || Tieba_Errcode::ERR_SUCCESS != $arrOutput1['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Media_Media::getDivideRatio fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrRules = $arrOutput1['data']['rows'];

        //合并数据
        $arrItem = array();
        if (!empty($arrRules)) {
            foreach ($arrRules as $item) {
                $arrItem[$item['rule_id']] = $item;
            }
        }
        $arrEmpty = array(
            'rule_id'              => 1,
            'rule_name'            => '默认分成',
            'reward'               => 0.5,
            'chat'                 => 0.7,
            'written_consultation' => 0.7,
            'chat_consultation'    => 0.7,
            // 相亲配置
            'chat_date'            => 0.5, // 相亲连麦
            'reward_anchor_date'   => 0.5, // 相亲主播收礼(含背包)
            'reward_guest1_date'   => 0.3, // 相亲嘉宾收礼（含背包）-主播分成
            'reward_guest2_date'   => 0.4, // 相亲嘉宾收礼（含背包）-嘉宾分成
        );
        if (!empty($arrItem)) {
            foreach ($arrOutput['data']['rows'] as &$value) {
                if (isset($arrItem[$value['rule_id']])) {
                    $value = array_merge($value, $arrItem[$value['rule_id']]);
                } else {
                    $value = array_merge($value, $arrEmpty);   //兜底
                }
                $value['tdou'] = $value['order_money'] - $value['discount'];
            }
        }
        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrOutput['data']);
    }

    private static function _getDetailGuest()
    {
        $intPn       = intval(Bingo_Http_Request::get('page', 0));
        $intPs       = intval(Bingo_Http_Request::get('ps', self::PageSize));
        $order_id    = strval(Bingo_Http_Request::get('order_id', ''));
        $client_type = intval(Bingo_Http_Request::get('client_type', 0));  //后续可能会换
        $kaibo_type  = intval(Bingo_Http_Request::get('kaibo_type', 0));  //后续可能会换
        $begin_time  = intval(Bingo_Http_Request::get('begin_time', 0));
        $end_time    = intval(Bingo_Http_Request::get('end_time', 0));
        $passport_id = intval(Bingo_Http_Request::get('user_id', 0));
        $scene_id    = intval(Bingo_Http_Request::get('scene_id', 0));
        $one_day     = intval(Bingo_Http_Request::get('one_day', 0));
        if ($one_day == 1) {
            $end_time = $begin_time + 86400;
        }

        $arrInput = array(
            'pn'          => $intPn,
            'ps'          => $intPs,
            'order_id'    => strval($order_id),
            'client_type' => $client_type,
            'kaibo_type'  => $kaibo_type,
            'passport_id' => $passport_id,
            'begin_time'  => $begin_time,
            'end_time'    => $end_time,
            'scene_id'    => $scene_id
        );

        //收入记录
        $arrOutput = Service_Ala_Media_Media::getDetailListGuest($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Media_Media::getDetailList fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        //去查规则详情
        $rule_id = array();
        foreach ($arrOutput['data']['rows'] as $item) {
            $rule_id[] = intval($item['rule_id']);
        }
        $arrInput = array(
            'rule_id' => $rule_id,
        );

        $arrOutput1 = Service_Ala_Media_Media::getDivideRatio($arrInput);
        if (false === $arrOutput1 || Tieba_Errcode::ERR_SUCCESS != $arrOutput1['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Media_Media::getDivideRatio fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrRules = $arrOutput1['data']['rows'];

        //合并数据
        $arrItem = array();
        if (!empty($arrRules)) {
            foreach ($arrRules as $item) {
                $arrItem[$item['rule_id']] = $item;
            }
        }
        $arrEmpty = array(
            'rule_id'              => 1,
            'rule_name'            => '默认分成',
            'reward'               => 0.5,
            'chat'                 => 0.7,
            'written_consultation' => 0.7,
            'chat_consultation'    => 0.7,
            // 相亲配置
            'chat_date'            => 0.5, // 相亲连麦
            'reward_anchor_date'   => 0.5, // 相亲主播收礼(含背包)
            'reward_guest1_date'   => 0.3, // 相亲嘉宾收礼（含背包）-主播分成
            'reward_guest2_date'   => 0.4, // 相亲嘉宾收礼（含背包）-嘉宾分成
        );

        if (!empty($arrItem)) {
            foreach ($arrOutput['data']['rows'] as &$value) {
                if (isset($arrItem[$value['rule_id']])) {
                    $value = array_merge($value, $arrItem[$value['rule_id']]);
                } else {
                    $value = array_merge($value, $arrEmpty);   //兜底
                }
                $value['tdou'] = $value['order_money'] - $value['discount'];
            }
        }
        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrOutput['data']);
    }

    private static function _exportDetail()
    {
        $intPn       = intval(Bingo_Http_Request::get('page', 0));
        $intPs       = intval(Bingo_Http_Request::get('ps', self::PageSize));
        $order_id    = strval(Bingo_Http_Request::get('order_id', ''));
        $client_type = intval(Bingo_Http_Request::get('client_type', 0));  //后续可能会换
        $kaibo_type  = intval(Bingo_Http_Request::get('kaibo_type', 0));  //后续可能会换
        $begin_time  = intval(Bingo_Http_Request::get('begin_time', 0));
        $end_time    = intval(Bingo_Http_Request::get('end_time', 0));
        $passport_id = intval(Bingo_Http_Request::get('user_id', 0));
        $scene_id    = intval(Bingo_Http_Request::get('scene_id', 0));
        $one_day     = intval(Bingo_Http_Request::get('one_day', 0));
        if ($one_day == 1) {
            $end_time = $begin_time + 86400;
        }

        $arrInput = array(
            'pn'          => $intPn,
            'ps'          => 50000,
            'order_id'    => strval($order_id),
            'client_type' => $client_type,
            'kaibo_type'  => $kaibo_type,
            'passport_id' => $passport_id,
            'begin_time'  => $begin_time,
            'end_time'    => $end_time,
            'scene_id'    => $scene_id

        );

        //收入记录
        $arrOutput = Service_Ala_Media_Media::getDetailList($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Media_Media::getDetailList fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        //去查规则详情
        $rule_id = array();
        foreach ($arrOutput['data']['rows'] as $item) {
            $rule_id[] = intval($item['rule_id']);
        }
        $arrInput = array(
            'rule_id' => $rule_id,
        );

        $arrOutput1 = Service_Ala_Media_Media::getDivideRatio($arrInput);
        if (false === $arrOutput1 || Tieba_Errcode::ERR_SUCCESS != $arrOutput1['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Media_Media::getDivideRatio fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrRules = $arrOutput1['data']['rows'];

        //合并数据
        $arrItem = array();
        if (!empty($arrRules)) {
            foreach ($arrRules as $item) {
                $arrItem[$item['rule_id']] = $item;
            }
        }
        $arrEmpty = array(
            'rule_id'              => 1,
            'rule_name'            => '默认分成',
            'reward'               => 0.5,
            'chat'                 => 0.7,
            'written_consultation' => 0.7,
            'chat_consultation'    => 0.7,
            // 相亲配置
            'chat_date'            => 0.5, // 相亲连麦
            'reward_anchor_date'   => 0.5, // 相亲主播收礼(含背包)
            'reward_guest1_date'   => 0.3, // 相亲嘉宾收礼（含背包）-主播分成
            'reward_guest2_date'   => 0.4, // 相亲嘉宾收礼（含背包）-嘉宾分成
        );
        if (!empty($arrItem)) {
            foreach ($arrOutput['data']['rows'] as &$value) {
                if (isset($arrItem[$value['rule_id']])) {
                    $value = array_merge($value, $arrItem[$value['rule_id']]);
                } else {
                    $value = array_merge($value, $arrEmpty);   //兜底
                }

                if (in_array($value['scene_id'], array(8000301, 8000302, 8000307, 8000308))) {
                    $value['base']         = $value['reward'];
                    $value['billingItems'] = '礼物打赏';

                }
                if (in_array($value['scene_id'], array(8000321, 8000322))) {
                    $value['base']         = $value['reward'];
                    $value['billingItems'] = '背包礼物';

                }

                if (in_array($value['scene_id'], array(8000328, 8000329))) {
                    $value['base']         = $value['chat_date'];
                    $value['billingItems'] = '相亲连麦';

                }

                if (in_array($value['scene_id'], array(8000305, 8000306))) {
                    $value['base']         = $value['chat'];
                    $value['billingItems'] = '付费连麦';

                }
                if (in_array($value['scene_id'], array(8000311, 8000312))) {
                    $value['base']         = $value['written_consultation'];
                    $value['billingItems'] = '文字咨询';

                }
                if (in_array($value['scene_id'], array(8000314, 8000315))) {
                    $value['base']         = $value['chat_consultation'];
                    $value['billingItems'] = '连麦咨询';

                }
                if (in_array($value['scene_id'], array(8000301, 8000302, 8000305, 8000306, 8000311, 8000312, 8000314, 8000315, 8000321, 8000322, 8000328, 8000329))) {
                    $value['kaibo_type'] = '百家号';

                }
                if (in_array($value['scene_id'], array(8000307, 8000308))) {
                    $value['kaibo_type'] = '手百';

                }
                if (in_array($value['scene_id'], array(8000301, 8000305, 8000307, 8000311, 8000314, 8000321, 8000328))) {
                    $value['client_type'] = 'ios';

                }
                if (in_array($value['scene_id'], array(8000302, 8000306, 8000308, 8000312, 8000315, 8000322, 8000329))) {
                    $value['client_type'] = '安卓';
                }
                $value['tdou'] = $value['order_money'] - $value['discount'];

            }
        }

        header("Content-type: text/html; charset=gbk");
        header("Content-type:application/vnd.ms-excel");
        header("Content-Disposition:filename=detail_sum.xls");
        self::createDetailExcel($arrOutput);
        return;
//        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrOutput['data']);
    }

    // 嘉宾收入管理详情信息导出
    private static function _exportDetailGuest()
    {
        $intPn       = intval(Bingo_Http_Request::get('page', 0));
        $intPs       = intval(Bingo_Http_Request::get('ps', self::PageSize));
        $order_id    = strval(Bingo_Http_Request::get('order_id', ''));
        $client_type = intval(Bingo_Http_Request::get('client_type', 0));  //后续可能会换
        $kaibo_type  = intval(Bingo_Http_Request::get('kaibo_type', 0));  //后续可能会换
        $begin_time  = intval(Bingo_Http_Request::get('begin_time', 0));
        $end_time    = intval(Bingo_Http_Request::get('end_time', 0));
        $passport_id = intval(Bingo_Http_Request::get('user_id', 0));
        $scene_id    = intval(Bingo_Http_Request::get('scene_id', 0));
        $one_day     = intval(Bingo_Http_Request::get('one_day', 0));
        if ($one_day == 1) {
            $end_time = $begin_time + 86400;
        }

        $arrInput = array(
            'pn'          => $intPn,
            'ps'          => 50000,
            'order_id'    => strval($order_id),
            'client_type' => $client_type,
            'kaibo_type'  => $kaibo_type,
            'passport_id' => $passport_id,
            'begin_time'  => $begin_time,
            'end_time'    => $end_time,
            'scene_id'    => $scene_id
        );

        //收入记录
        $arrOutput = Service_Ala_Media_Media::getDetailListGuest($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Media_Media::getDetailList fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        //去查规则详情
        $rule_id = array();
        foreach ($arrOutput['data']['rows'] as $item) {
            $rule_id[] = intval($item['rule_id']);
        }
        $arrInput = array(
            'rule_id' => $rule_id,
        );

        $arrOutput1 = Service_Ala_Media_Media::getDivideRatio($arrInput);
        if (false === $arrOutput1 || Tieba_Errcode::ERR_SUCCESS != $arrOutput1['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Ala_Media_Media::getDivideRatio fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrRules = $arrOutput1['data']['rows'];

        //合并数据
        $arrItem = array();
        if (!empty($arrRules)) {
            foreach ($arrRules as $item) {
                $arrItem[$item['rule_id']] = $item;
            }
        }
        $arrEmpty = array(
            'rule_id'              => 1,
            'rule_name'            => '默认分成',
            'reward'               => 0.5,
            'chat'                 => 0.7,
            'written_consultation' => 0.7,
            'chat_consultation'    => 0.7,
            // 相亲配置
            'chat_date'            => 0.5, // 相亲连麦
            'reward_anchor_date'   => 0.5, // 相亲主播收礼(含背包)
            'reward_guest1_date'   => 0.3, // 相亲嘉宾收礼（含背包）-主播分成
            'reward_guest2_date'   => 0.4, // 相亲嘉宾收礼（含背包）-嘉宾分成
        );
        if (!empty($arrItem)) {
            foreach ($arrOutput['data']['rows'] as &$value) {
                if (isset($arrItem[$value['rule_id']])) {
                    $value = array_merge($value, $arrItem[$value['rule_id']]);
                } else {
                    $value = array_merge($value, $arrEmpty);   //兜底
                }

                if (in_array($value['scene_id'], array(8000301, 8000302, 8000307, 8000308))) {
                    $value['base']         = $value['reward_guest2_date'];
                    $value['billingItems'] = '礼物打赏';
                }
                if (in_array($value['scene_id'], array(8000321, 8000322))) {
                    $value['base']         = $value['reward_guest2_date'];
                    $value['billingItems'] = '背包礼物';
                }

                if (in_array($value['scene_id'], array(8000301, 8000302, 8000305, 8000306, 8000311, 8000312, 8000314, 8000315, 8000321, 8000322))) {
                    $value['kaibo_type'] = '百家号';

                }
                if (in_array($value['scene_id'], array(8000307, 8000308))) {
                    $value['kaibo_type'] = '手百';

                }
                if (in_array($value['scene_id'], array(8000301, 8000305, 8000307, 8000311, 8000314, 8000321))) {
                    $value['client_type'] = 'ios';

                }
                if (in_array($value['scene_id'], array(8000302, 8000306, 8000308, 8000312, 8000315, 8000322))) {
                    $value['client_type'] = '安卓';
                }
                $value['tdou'] = $value['order_money'] - $value['discount'];

            }
        }

        header("Content-type: text/html; charset=gbk");
        header("Content-type:application/vnd.ms-excel");
        header("Content-Disposition:filename=detail_sum.xls");
        self::createDetailExcel($arrOutput);
        return;
//        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrOutput['data']);
    }

    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {

        $arrRet = array(
            'no'    => intval($errno),
            'error' => strval($errmsg),
            'data'  => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }


    /**
     * 输出excel内容
     * @param null
     * @return null
     * */
    private static function createExcel($arrInput)
    {
        $data = $arrInput['data']['rows'];
        echo iconv("UTF-8", "GBK", "时间\t");
        echo iconv("UTF-8", "GBK", "主播uid\t");
        echo iconv("UTF-8", "GBK", "主播名称\t");
        echo iconv("UTF-8", "GBK", "计费项\t");
        echo iconv("UTF-8", "GBK", "分成类型\t");
        echo iconv("UTF-8", "GBK", "分成规则\t");
        echo iconv("UTF-8", "GBK", "分成名称\t");
        echo iconv("UTF-8", "GBK", "总金额/元\t");
        echo iconv("UTF-8", "GBK", "T豆消耗\t");
        echo iconv("UTF-8", "GBK", "Y币消耗\t");
        echo iconv("UTF-8", "GBK", "基础\t");
//        echo iconv("UTF-8", "GBK", "月累计\t");
        echo iconv("UTF-8", "GBK", "激励分成\t");
        echo iconv("UTF-8", "GBK", "主播分账\t");
        echo iconv("UTF-8", "GBK", "主播类\t");
        echo iconv("UTF-8", "GBK", "公会uid\t");
        echo iconv("UTF-8", "GBK", "公会名称\t");
        echo iconv("UTF-8", "GBK", "结算关系\t\n");

        foreach ($data as $value) {
            echo iconv("UTF-8", "GBK", date("Y-m-d", $value['date']) . "\t");
            echo iconv("UTF-8", "GBK", $value['user_id'] . "\t");
            echo iconv("UTF-8", "GBK", $value['user_name'] . "\t");
            echo iconv("UTF-8", "GBK", $value['billingItems'] . "\t");
            echo iconv("UTF-8", "GBK", $value['divide_type'] . "\t");
            echo iconv("UTF-8", "GBK", $value['rule_id'] . "\t");
            echo iconv("UTF-8", "GBK", $value['rule_name'] . "\t");
            echo iconv("UTF-8", "GBK", $value['order_money'] . "\t");
            echo iconv("UTF-8", "GBK", $value['tdou_sum'] . "\t");
            echo iconv("UTF-8", "GBK", $value['ycoin_sum'] . "\t");
            echo iconv("UTF-8", "GBK", $value['base'] . "\t");
//            echo iconv("UTF-8", "GBK", '' . "\t");
            echo iconv("UTF-8", "GBK", $value['incentive'] . "\t");
            echo iconv("UTF-8", "GBK", $value['money'] . "\t");
            echo iconv("UTF-8", "GBK", $value['anchorType'] . "\t");
            echo iconv("UTF-8", "GBK", $value['guild_id'] . "\t");
            echo iconv("UTF-8", "GBK", $value['guild_name'] . "\t");
            echo iconv("UTF-8", "GBK", $value['settlement_type'] . "\t\n");


        }
    }


    private static function createDetailExcel($arrInput)
    {
        $data = $arrInput['data']['rows'];
        echo iconv("UTF-8", "GBK", "订单号\t");
        echo iconv("UTF-8", "GBK", "时间\t");
        echo iconv("UTF-8", "GBK", "主播uid\t");
        echo iconv("UTF-8", "GBK", "计费项\t");
        echo iconv("UTF-8", "GBK", "开播端\t");
        echo iconv("UTF-8", "GBK", "客户端\t");
        echo iconv("UTF-8", "GBK", "总金额/元\t");
        echo iconv("UTF-8", "GBK", "券类型/元\t");
        echo iconv("UTF-8", "GBK", "券减扣/元\t");
        echo iconv("UTF-8", "GBK", "T豆消耗\t");
        echo iconv("UTF-8", "GBK", "Y币消耗\t");
        echo iconv("UTF-8", "GBK", "主播分成比例\t");
        echo iconv("UTF-8", "GBK", "激励分成\t");
        echo iconv("UTF-8", "GBK", "主播分账\t\n");


        foreach ($data as $value) {
            echo "=\"" . $value['rm_order_id'] . "\"" . "\t";;
//            echo iconv("UTF-8", "GBK", "'" . $value['rm_order_id'] . "\t");
            echo iconv("UTF-8", "GBK", date("Y-m-d", $value['order_time']) . "\t");
//            echo iconv("UTF-8", "GBK", $value['passport_id'] . "\t");
            echo "=\"" . $value['passport_id'] . "\"" . "\t";
            echo iconv("UTF-8", "GBK", $value['billingItems'] . "\t");
            echo iconv("UTF-8", "GBK", $value['kaibo_type'] . "\t");
            echo iconv("UTF-8", "GBK", $value['client_type'] . "\t");
            echo iconv("UTF-8", "GBK", $value['order_money'] . "\t");
            echo iconv("UTF-8", "GBK", "平台券" . "\t");
            echo iconv("UTF-8", "GBK", $value['discount'] . "\t");
            echo iconv("UTF-8", "GBK", $value['price_tdou'] . "\t");
            echo iconv("UTF-8", "GBK", $value['price_ycoin'] . "\t");
            echo iconv("UTF-8", "GBK", $value['base'] . "\t");
            echo iconv("UTF-8", "GBK", $value['incentive'] . "\t");
            echo iconv("UTF-8", "GBK", $value['money'] . "\t\n");

        }
    }
}