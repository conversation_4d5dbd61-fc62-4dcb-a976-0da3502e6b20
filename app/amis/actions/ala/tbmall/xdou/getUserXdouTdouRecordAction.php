<?php
/**
 * getUserXdouTdouRecordAction.php 获取用户独立货币的消费记录
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 19/3/29 下午3:07
*/

class getUserXdouTdouRecordAction extends Util_Action
{
    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;

    const SUPER_BDUSS = '123456tbclient654321';

    /**
     * 执行函数
     * @param  null
     * @return bool
     */
    public function _execute(){
        // 操作人
        $intUserId   = Util_User::$intUserId;
        $strUserName = Util_User::$strUserName;

        $intSearchUserId = (int)Bingo_Http_Request::get('user_id', 0);
        $intSceneId      = (int)Bingo_Http_Request::get('scene_id', 0);
        $intPage         = (int)Bingo_Http_Request::get('page', 1);
        $intPerPage      = (int)Bingo_Http_Request::get('perPage', 30);

        if (!$intSearchUserId && !$intSceneId) {
            return self::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, array(), 'success');
        }

        $arrInput = array(
            'user_id'  => $intSearchUserId,
            'scene_id' => $intSceneId,
            'pn'       => $intPage,
            'limit'    => $intPerPage,
            'bduss'    => self::SUPER_BDUSS,
        );

        $strServiceName = 'tbmall';
        $strMethodName  = 'getConsumeOrder';

        $arrOut = Tieba_Service::call($strServiceName, $strMethodName, $arrInput, null, null, 'post', 'php', 'utf-8');

        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . " call $strServiceName $strMethodName fail, input: [".json_encode($arrInput).'], output: ['.json_encode($arrOut)."]");
            return self::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrConsumeList = $arrOut['data']['order_list'];

        if (!empty($arrConsumeList)) {
            foreach ($arrConsumeList as $k => $arrConsumeInfo) {
                $arrConsumeList[$k]['create_time'] = date('Y-m-d H:i:s', $arrConsumeInfo['create_time']);
            }
        }

        $intCount = (int)$arrOut['data']['total_count'];

        $arrRet = array(
            'rows'  => $arrConsumeList,
            'count' => $intCount,
        );

        // 返回结果(含当前页面的记录以及记录总量)
        return self::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, $arrRet, 'success');
    }

    /**
     * jsonAmisRet
     * @param  $intErrno
     * @param  array $arrExtData
     * @param  string $strMsg
     * @return bool
     */
    public static function jsonAmisRet($intErrno, $arrExtData = array(), $strMsg = ''){
        $strMsg = $strMsg ? $strMsg : Alalib_Conf_Error::getErrorMsg($intErrno);

        $arrOutput = array(
            'status' => $intErrno,
            'msg'    => $strMsg,
            'data'   => $arrExtData,
        );

        Bingo_Http_Response::contextType('application/json');

        echo Bingo_String::array2json($arrOutput, Bingo_Encode::ENCODE_UTF8);

        return true;
    }
}