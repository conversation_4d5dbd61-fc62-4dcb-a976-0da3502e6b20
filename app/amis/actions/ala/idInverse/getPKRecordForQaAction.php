<?php
/**
 * Created by PhpStorm.
 * 根据获取礼物和魅力值增长信息
 * User: yin<PERSON><PERSON>
 * Date: 2019/5/5
 * Time: 上午10:34
 */

class getPkRecordForQaAction extends Util_Action
{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;


    public function _execute()
    {

        $intUserId = intval(Bingo_HTTP_Request::get('user_id', 0));
        $intPage         = (int)Bingo_Http_Request::get('page', 0);
        $intPerPage      = (int)Bingo_Http_Request::get('perPage', 30);
        if(empty($intUserId)){
            $arrRetResult = array();
            return $this->_jsonRet(Alalib_Conf_Error::ERR_SUCCESS,'',$arrRetResult);
        }

        $arrInput = array(
            'user_id'    => $intUserId,
            'pn'       => $intPage,
            'ps'    => $intPerPage,
        );

        $arrOutput = Tieba_Service::call('ala', 'getUserPkHistory', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala::getUserPkHistory fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrList = array();

        foreach ($arrOutput['data'] as $item){

            /*switch ($item['result']) {
                case 0:
                    $item['result'] = '失败';
                    break;
                case 1:
                    $item['result'] = '平局';
                    break;
                case 2:
                    $item['result'] = '成功';
                    break;
            }*/

            $arrList[] = array(
                'uid1'=>$item['uid1'],
                'uid2'=>$item['uid2'],
                'live_id1'=>$item['live_id1'],
                'live_id2'=>$item['live_id2'],
                'tdou1'=>$item['tdou1'],
                'tdou2' =>$item['tdou2'],
                'create_time' =>$item['create_time'] . '(' . date('Y-m-d H:i:s',$item['create_time']) .')',
                'expire_time' =>$item['expire_time'] . '(' . date('Y-m-d H:i:s',$item['expire_time']) .')',

            );
        }

        $arrRetData = array(
            'rows' => $arrList,
            'count' => sizeof($arrOutput['data']),
        );

        return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRetData);

    }

    /**
     * @param
     * Mis标准输出函数
     * @return
     */
    private function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => Alalib_Conf_Error::getErrorMsg($errno),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }

}