<?php

/**
 * Created by PhpStorm.
 * User: caowu
 * Date: 19/4/22
 * Time: 下午3:38
 */
class dealUserCharmCountAction extends Util_Action {

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = true;

    const NOTICE_STATUS_ONLINE = 1;
    const NOTICE_STATUS_OFFLINE = 2;
    const NOTICE_STATUS_DELETE = 3;

    /**
     * @return bool
     */
    public function _execute() {
        // 操作人
        $intOwnUserId = Util_User::$intUserId;
        $strOwnUserName = Util_User::$strUserName;
        $strMethod = Bingo_Http_Request::get('method', '');

        //获取参数
        $strMethod = Bingo_Http_Request::getNoXssSafe('method','');

        if('update' == $strMethod) {
            $intUserId = intval(Bingo_Http_Request::get('user_id', 0));
            $intIncrCharmCount = intval(Bingo_Http_Request::get('incr_charm_count', 0));

            if(empty($intUserId) || empty($intIncrCharmCount)) {
                Bingo_Log::warning("error params!");
                return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
            }
            $arrInput = array(
                'user_id' => $intUserId,
                'charm_count' => $intIncrCharmCount,
            );
            $arrOutput = Tieba_Service::call('ala', 'userUpdateExtLiveInfo', $arrInput, null, null, 'post', 'php', 'utf-8');
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala::userUpdateExtLiveInfo fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_SUCCESS);
        }

        else if('get' == $strMethod){
            $intUserId = Bingo_Http_Request::get('user_id', 0);
            if(empty($intUserId)) {
                Bingo_Log::warning("error params!");
                return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_SUCCESS);
            }

            $arrInput = array(
                'uids' => array(
                    $intUserId,
                )
            );
            $arrOutput = Tieba_Service::call('ala', 'userGetInfo', $arrInput, null, null, 'post', 'php', 'utf-8');
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala::userGetInfo fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $intCharmCount = intval($arrOutput["data"][$intUserId]['charm_count']);

            $arrInput = array(
                'user_id' => $intUserId,
            );
            $arrOutput = Tieba_Service::call('ala', 'getUserRealCharmCount', $arrInput, null, null, 'post', 'php', 'utf-8');
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala::getUserRealCharmCount fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $intRealCharmCount = intval($arrOutput["data"]['charm_count']);

            $arrRetData = array(
                'charm_count' => $intCharmCount,
                'real_charm_count' => $intRealCharmCount,
            );

            return Util_Ala_Common::jsonAmisRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRetData);
        }

        return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR);
    }


}