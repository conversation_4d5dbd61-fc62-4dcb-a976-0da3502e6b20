<?php
/**
 * Created by PhpStorm.
 * 根据ala_id获取live_id,user_id,live_status,room_id等其他直播信息
 * User: weiyan02
 * Date: 2018/9/5
 * Time: 上午10:34
 */

class getIdInfoByAlaIdAction extends Util_Action
{

    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;
    
    public function _execute()
    {

        Bingo_Log::warning('ala getIdInfoByAlaIdAction into  !');
        $intSelectType = intval(Bingo_HTTP_Request::get('select_type', ''));
        $intInput = intval(Bingo_HTTP_Request::get('input', ''));
        if(empty($intInput)){
            $arrRetResult = array();
            return $this->_jsonRet(Alalib_Conf_Error::ERR_SUCCESS,'',$arrRetResult);
        }
        if($intSelectType == 1) {
            $intAlaId = $intInput;
            $arrInput = array(
                'ala_id' => $intAlaId,
            );

            $strService = "ala";
            $strMethod = "getUserIdByAlaIdService";
            Bingo_Log::warning(" ala getUserIdByAlaIdService input params:".serialize($arrInput));
            $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
            if ($arrOutput === false || (isset($arrOutput['errno']) && intval($arrOutput['errno']) !== 0)) {
                Bingo_Log::warning(__FUNCTION__ . " call service $strService : $strMethod, input =" . serialize($arrInput) . " output =" . serialize($arrOutput));
                return $this->_jsonRet(Alalib_Conf_Error::ERR_ALAID_INVALID);
            }
            $intUserId = $arrOutput['data'];
            $arrRetResult = $this->getLiveIdWhiteByUid($intUserId);
            return $this->_jsonRet(Alalib_Conf_Error::ERR_SUCCESS,'',$arrRetResult);

        }else if($intSelectType == 2){

            $intUserId = $intInput;
            $arrRetResult = $this->getLiveIdWhiteByUid($intUserId);
            return $this->_jsonRet(Alalib_Conf_Error::ERR_SUCCESS,'',$arrRetResult);

        }else if($intSelectType == 3){

        }else if($intSelectType == 4){
            $intLiveId = $intInput;
            $arrOutput = Tieba_Service::call("ala", "liveGetInfo", array('live_ids' => array($intLiveId),), null, null, 'post', 'php', 'utf-8');
            if($arrOutput != false && $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS){
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala userGetInfo fail. input:[" . serialize($intLiveId) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::fatal($strLog);
                return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
            }

            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala userGetInfo fail. input:[" . serialize($intLiveId) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::fatal($strLog);
                return $this->_jsonRet(Alalib_Conf_Error::ERR_LIVE_ID_IS_INVALID);
            }

            $intLiveId = $arrOutput['data'][$intLiveId]['live_info']['live_id'];
            $intUserIdTmp = $arrOutput['data'][$intLiveId]['live_info']['user_id'];

            $intUserId = $intUserIdTmp;
            $arrRetResult = $this->getLiveIdWhiteByUid($intUserId);

            Bingo_Log::warning('ala getIdInfoByAlaIdAction 4 arrRet new :'. serialize($arrRetResult));

            return $this->_jsonRet(Alalib_Conf_Error::ERR_SUCCESS,'',$arrRetResult);

        }else{
            return $this->_jsonRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }

    }

    /**
     * @param
     * Mis标准输出函数
     * @return
     */
    private function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => Alalib_Conf_Error::getErrorMsg($errno),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }
    /**
     * @param $user_id
     * 根据uid获取ala_id\live_id\uid\Tdou_Num\场景实名认证信息
     * @return array
     */
    private function getLiveIdWhiteByUid($user_id)
    {
        $intUserId = $user_id;
        //tdou
        $arrParams = array(
            "user_id" => $intUserId,
        );
        $arrRet = Tieba_Service::call("user","getUserData",$arrParams,NULL, NULL, 'post', 'php', 'utf-8');
        if($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("call getUserData error.[input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
            return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }

        $intTdouNum = $arrRet['user_info'][0]['Parr_scores']['scores_total'];
        $intUserIdTmp = $arrRet['user_info'][0]['user_id'];
        $arrTdou = $arrRet['user_info'];

        //userInfo
        $arrInput = array(
            "uids" => array($intUserId),
        );
        $strServiceName = "ala";
        $strServiceMethod = "userGetInfo";
        $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrInput, null, null, 'post', null, 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
        }

        $intTieBa = $arrOutput['data'][$intUserId]['verify_info_scene']['tieba'];
        $intQuanMin = $arrOutput['data'][$intUserId]['verify_info_scene']['quanmin'];
        $intHaoKan = $arrOutput['data'][$intUserId]['verify_info_scene']['haokan'];
        $intUserNickNameTmp = $arrOutput['data'][$intUserId]['user_nickname'];
//        $intLiveId = $arrOutput['data'][$intUserId]['live_id'];
        $intAlaId = $arrOutput['data'][$intUserId]['ala_id'];
        $arrUserInfo = $arrOutput['data'][$intUserId];

        $arrInput = array(
            'user_id' => $intUserId,
        );

        $arrRet = Alalib_Util_Service::call('ala', 'selectMaxLiveIdByUser', $arrInput);
        // 查询失败，或者查询live_id不为空，则不处理
        if (false === $arrRet || $arrRet['errno'] != Alalib_Conf_Error::ERR_SUCCESS || empty($arrRet['data']['live_id'])) {
            Bingo_Log::warning('ala selectMaxLiveIdByUser arrRet error :'. serialize($arrRet));
        }

        $intLiveId = $arrRet['data']['live_id'];
        $intLiveStatus = $arrRet['data']['live_status'];

//        feed_id
        $arrServiceInput  = array("live_id" =>$intLiveId,);
        $strServiceName   = "ala";
        $strServiceMethod = "liveGetRedisInfoByLiveId";
        $arrOutputRedisInfo        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
        if(false === $arrOutputRedisInfo || Tieba_Errcode::ERR_SUCCESS != $arrOutputRedisInfo["errno"]){
            $strLog = "call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutputRedisInfo)."]";
            Bingo_Log::warning($strLog);
        }

        $strFeedIdResult = $arrOutputRedisInfo['data']['feed_id'];

        //liveInfo
        $arrOutputLiveStatus = Tieba_Service::call("ala", "liveGetInfo", array('live_ids' => array($intLiveId),), null, null, 'post', 'php', 'utf-8');
        if($arrOutputLiveStatus != false && $arrOutputLiveStatus['errno'] != Tieba_Errcode::ERR_SUCCESS){
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala userGetInfo fail. input:[" . serialize($intLiveId) . "]; output:[" . serialize($arrOutputLiveStatus) . "]";
            Bingo_Log::fatal($strLog);
            return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }


//        $intLiveStatus = $arrOutputLiveStatus['data'][$intLiveId]['live_info']['live_status'];
        $intRoomId = $arrOutputLiveStatus['data'][$intLiveId]['live_info']['room_id'];
        $intUserNameTmp = $arrOutputLiveStatus['data'][$intLiveId]['live_info']['user_name'];
        $intUserSubappTypeTmp = $arrOutputLiveStatus['data'][$intLiveId]['live_info']['subapp_type'];
        $arrLiveInfo = $arrOutputLiveStatus['data'][$intLiveId]['live_info'];

        if(empty($intUserSubappTypeTmp)){
            $intUserSubappTypeTmp = "tieba";
        }

        if(empty($intUserNickNameTmp)){
            $intUserName = $intUserNameTmp;
            Bingo_Log::warning('ala username nickname is not null new :'. serialize($intUserNameTmp));

        }else{
            $intUserName = $intUserNickNameTmp;
            Bingo_Log::warning('ala username arrRet new :'. serialize($intUserNickNameTmp));

        }
        $arrList = array(
            'ala_id'=>$intAlaId,
            'live_id' => $intLiveId,
            'live_status' => $intLiveStatus,
            'room_id' => $intRoomId,
            'user_id' => $intUserIdTmp,
            'user_name' => $intUserName,
            'tieba' => $intTieBa,
            'quanmin' => $intQuanMin,
            'haokan' => $intHaoKan,
            'tdou_num' => $intTdouNum,
            'subapp_type' => $intUserSubappTypeTmp,
            'feed_id' => $strFeedIdResult,
            'tdou_info' => $arrTdou,
            'user_info' => $arrUserInfo,
            'live_info' => $arrLiveInfo,
        );

        Bingo_Log::warning('ala userGetInfoByAlaId result new :'. serialize($arrOutput));

        $arrRet = array(
            'rows' => array($arrList),
        );
        return $arrRet;
    }
}