<?php

/**
 * Created by PhpStorm.
 * User: zhouyan08
 * Date: 18/4/27
 * Time: 下午4:48
 */
class getResourcesIdAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	private $_arrReqParam = array();
	private $_intResId = 0;
	protected $_arrReqKeyFields = array(
		'resourceId',
		'type',
	);
	
	/**
	 * @brief  _getReqParam
	 * <AUTHOR>
	 * @time   2019-08-22 16:03
	 * @return bool
	 */
	protected function _getReqParam()
	{
		
		if(!empty($this->_arrReqKeyFields)){
			foreach($this->_arrReqKeyFields as $strField){
				$objFieldValue = Bingo_Http_Request::get($strField, null);
				if(is_null($objFieldValue)){
					Bingo_Log::warning('param empty field:'.$strField);
					return false;
				}
				$this->_arrReqParam[$strField] = $this->_fieldValueFilter($objFieldValue);
			}
		}
		if(!empty($this->_arrReqOptFields)){
			foreach($this->_arrReqOptFields as $strOptField){
				$this->_arrReqParam[$strOptField] = $this->_fieldValueFilter(Bingo_Http_Request::get($strOptField, null));
			}
		}
		return true;
	}
	
	/**
	 * @brief  _execute
	 * <AUTHOR>
	 * @time   2019-08-22 16:03
	 * @return bool
	 */
	public function _execute()
	{
		$this->_getReqParam();
		$this->_intResId = intval($this->_arrReqParam['resourceId']);
		if($this->_intResId == 0){
			return self::retResList();
		}else{
			return self::retResDetail($this->_intResId, $this->_arrReqParam['type']);
		}
	}
	
	/**
	 * @brief  retResList
	 * <AUTHOR>
	 * @time   2019-08-22 16:03
	 * @return bool
	 */
	private static function retResList()
	{
		$ret   = array();
		$ret[] = array(
			'label' => '皮肤',
			'value' => 1,
		);
		$ret[] = array(
			'label' => '贴吧顶部高级头图',
			'value' => 13,
		);
		$ret[] = array(
			'label' => '客户端frs页顶部banner',
			'value' => 16,
		);
		$ret   = array(
			'options' => $ret,
		);
		return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, $ret);
	}
	
	/**
	 * @brief  retResDetail
	 * <AUTHOR>
	 * @time   2019-08-22 16:03
	 *
	 * @param $resId
	 * @param $type
	 *
	 * @return bool
	 */
	private static function retResDetail($resId, $type)
	{
		$arrInput = array(
			'resource_id' => $resId,
		);
		//get res list
		$arrAttrRes = Tieba_Service::call('resource', 'getResourceQuoteConfigByResourceId', $arrInput);
		if(Tieba_Errcode::ERR_SUCCESS !== $arrAttrRes['errno']){
			Bingo_Log::warning('call resource getResourceQuoteConfigByResourceId error '.serialize($arrInput).'_'.serialize($arrAttrRes));
			Util_Schedule_Common::jsonOutput(self::CUSTOM_ERR_GET_RESOURCE_ATTR, 'get resource attr fail');
			return false;
		}
		$arrRet = self::_getRetData($arrAttrRes['data'], $type);
		return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
	}
	
	/**
	 * @brief  _getRetData
	 * <AUTHOR>
	 * @time   2019-08-22 16:03
	 *
	 * @param $arrResAttr
	 * @param $type
	 *
	 * @return array
	 */
	private static function _getRetData($arrResAttr, $type)
	{
		//platform map
		// '1' => 'PC',
		// '2' => '客户端',
		// '3' => '智能版',
		//location map
		//'1' => 'FRS',
		//'2' => 'PB',
		//'3' => '全局',
		//'4' => 'pc端首页',
		//'5' => '客户端广场',
		$platformMap    = array(
			'1' => 'PC',
			'2' => '客户端',
			'3' => '智能版',
		);
		$locationMap    = array(
			'1' => 'FRS',
			'2' => 'PB',
			'3' => '全局',
			'4' => 'pc端首页',
			'5' => '客户端广场',
		);
		$arrPlatform    = explode(',', $arrResAttr['platform']);
		$arrPlatformNew = array(
			'1' => 0,
			'2' => 0,
			'3' => 0,
		);
		foreach($arrPlatform as $value){
			if(2 === intval($value)){
				$arrPlatformNew['4'] = 1;
				$arrPlatformNew['5'] = 1;
				continue;
			}
			$arrPlatformNew[$value] = 1;
		}
		$arrPage    = explode(',', $arrResAttr['location']);
		$arrPageNew = array(
			'1' => 0,
			'2' => 0,
			'3' => 0,
			'4' => 0,
			'5' => 0,
		);
		foreach($arrPage as $value){
			$arrPageNew[$value] = 1;
		}
		$plat = array();
		foreach($arrPlatformNew as $k => $v){
			if($v != 1){
				continue;
			}
			$plat[] = array(
				'label' => $platformMap[$k],
				'value' => intval($k),
			);
		}
		$loc = array();
		foreach($arrPageNew as $k => $v){
			if($v != 1){
				continue;
			}
			$loc[] = array(
				'label' => $locationMap[$k],
				'value' => intval($k),
			);
		}
		if($type == 'location'){
			$arrRet = array(
				'options' => $loc,
			);
		}else{
			$arrRet = array(
				'options' => $plat,
			);
		}
		return $arrRet;
	}
	
	/**
	 * @brief  _fieldValueFilter
	 * <AUTHOR>
	 * @time   2019-08-22 16:03
	 *
	 * @param $objFieldValue
	 *
	 * @return string|null
	 */
	private function _fieldValueFilter($objFieldValue)
	{
		$objRet = $objFieldValue;
		if(is_null($objFieldValue)){
			return null;
		}
		if(is_numeric($objFieldValue)){
			$objRet = ($objRet);
		}
		if(is_string($objFieldValue)){
			$objRet = strval(trim($objRet));
		}
		return $objRet;
	}
}
