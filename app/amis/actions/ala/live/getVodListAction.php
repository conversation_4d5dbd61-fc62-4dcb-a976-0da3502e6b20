<?php

/**
 * Created by PhpStorm.
 * User: zhouyan08
 * Date: 2019-03-11
 * Time: 21:15
 */
class getVodListAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * 执行函数
	 *
	 * @param null
	 *
	 * @return null
	 * */
	public function _execute()
	{
		//数据获取
		$page      = intval(Bingo_Http_Request::get('page', 0));
		$user_id   = intval(Bingo_Http_Request::get('user_id', 0));
		$user_name = strval(Bingo_Http_Request::get('user_name', 0));
		$subapp    = strval(Bingo_Http_Request::get('subapp', 'tieba'));
		if(empty($user_id) && !empty($user_name)){
			$user_id = Util_Ala_Common::getUidByUname($user_name);
		}
		if(empty($user_id)){
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
		}
		$ps          = 10;
		$subapp_type = $subapp;
		$arrInput    = array(
			'user_id'     => $user_id,
			'subapp_type' => $subapp_type,
			'offset'      => ($page - 1) * $ps,
			'limit'       => $ps,
		);
		$arrOutput   = Tieba_Service::call('ala', 'getSdkVodListByUserIdForMis', $arrInput, null, null, 'post', null, 'utf-8');
		if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '获取列表失败!');
		}
		$list_info = self::getDuration($arrOutput['data']);
		if(false === $list_info){
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '获取直播信息失败!');
		}
		$arrInput  = array(
			'user_id'     => $user_id,
			'subapp_type' => $subapp_type,
		);
		$arrOutput = Tieba_Service::call('ala', 'getSdkVodListCountByUserIdForMis', $arrInput, null, null, 'post', null, 'utf-8');
		if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
			return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '获取列表失败!');
		}
		$count = $arrOutput['data'];
		return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_SUCCESS, array(
			'rows'  => $list_info,
			'count' => $count[0]['count(*)'],
		));
	}
	
	/**
	 *
	 * @param $arrLive
	 *
	 * @return bool
	 */
	private static function getDuration($arrLive)
	{
		$arrLiveIds = array();
		foreach($arrLive as $value){
			$arrLiveIds[] = $value['live_id'];
		}
		$arrServiceInput  = array(
			"live_ids" => $arrLiveIds,
		);
		$strServiceName   = "ala";
		$strServiceMethod = "liveGetInfo";
		$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = "call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::warning($strLog);
			return false;
		}
		foreach($arrLive as &$value){
			$value['duration'] = (int)$arrOutput['data'][$value['live_id']]["live_info"]['live_duration'];
		}
		return $arrLive;
	}
}