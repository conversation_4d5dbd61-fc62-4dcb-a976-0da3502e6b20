<?php
/**
 * Created by PhpStorm.
 * User: sunzhexuan
 * Date: 2018/11/26
 * Time: 下午1:06
 */

class BXSinfoAction extends Util_Action
{
    protected $strAmisGroup = '';

    protected $strAmisPerm = '';

    protected $bolOnlyAccessAmis = false;

    protected $bolOnlyInner = false;

    protected $bolNeedLogin = false;
    /**
     * @return int
     */
    public function _execute(){
        $strkey    = trim(Bingo_Http_Request::get('key',''));
        $intUid    = intval(Bingo_Http_Request::get('user_id',0));
        $strmethod = Bingo_Http_Request::get('method','');

        $arrOutput = array();

        switch($strmethod){
            case 'add' :
                $arrOutput = self::_Add($strkey,$intUid);
                break;
            case 'delete' :
                $arrOutput = self::_Delete($strkey);
                break;
            case 'select' :
                $arrOutput = self::_Select();
                break;
            default:
                break;
        }

        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS,"true",$arrOutput);
    }

    /**
     * @return int
     */
    private function _Select(){
        $strService = 'ala';
        $strMethod = 'getBXSRecordfromRedis';
        $arrOutput = Tieba_Service::call($strService, $strMethod, array(), null, null, 'post', 'php', 'utf-8');
        if ($arrOutput === false || $arrOutput['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strService::$strMethod fail. input[" . serialize(array()) . "] output[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return $arrOutput['data'];
    }

    /**
     * @param $key
     * @return bool|int
     */
    private function _Delete($key){
        if(empty($key)){
            return false;
        }
        $arrInput = array(
            'field' => $key,
        );
        $strService = 'ala';
        $strMethod = 'delBXSRecordByKey';
        $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($arrOutput === false || $arrOutput['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strService::$strMethod fail. input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return $arrOutput['data'];
    }

    /**
     * @param $key
     * @param $field
     * @return int
     */
    private function _Add($key,$field){
        if(empty($key)||empty($field)){
            return false;
        }
        $arrInput['key'] = $key;
        $arrInput['user_id'] = $field;

        $strService = 'ala';
        $strMethod = 'addBXSRecordToRedis';
        $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($arrOutput === false || $arrOutput['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strService::$strMethod fail. input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return $this->_jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return $arrOutput['data'];
    }
    /**
     * @brief _jsonRet
     *
     * @param
     *            errno,errmsg,data
     * @return : 0.
     *
     */
    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no' => intval($errno),
            'error' => strval($errmsg),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }
}