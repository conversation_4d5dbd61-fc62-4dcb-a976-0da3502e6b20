<?php

/**
 * Created by PhpStorm.
 * User: zhouyan08
 * Date: 17/4/27
 * Time: 下午4:48
 */
class blackListAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * 执行函数
	 *
	 * @param null
	 *
	 * @return null
	 * */
	public function _execute()
	{
		//数据获取
		$uid        = intval(Bingo_Http_Request::get('uid', ''));
		$name       = strval(Bingo_Http_Request::get('name', ''));
		$reason     = intval(Bingo_Http_Request::get('reason', ''));
		$time       = intval(Bingo_Http_Request::get('time', ''));
		$comment    = strval(Bingo_Http_Request::get('comment', ''));
		$page       = intval(Bingo_Http_Request::get('page', ''));
		$time_begin = intval(Bingo_Http_Request::get('time_begin', ''));
		$time_end   = intval(Bingo_Http_Request::get('time_end', ''));
		$id         = intval(Bingo_Http_Request::get('id', ''));
		$uid_name   = intval(Bingo_Http_Request::get('uid_name', ''));
		$type       = intval(Bingo_Http_Request::get('type', ''));
		$method     = strval(Bingo_Http_Request::get('method', ''));
		$op_id      = Util_User::$intUserId;
		$op_name    = Util_User::$strUserName;
		$arrInput   = array('uid'        => $uid,
		                    'name'       => $name,
		                    'reason'     => $reason,
		                    'time'       => $time,
		                    'comment'    => $comment,
		                    //519
		                    'page'       => $page,
		                    'time_begin' => $time_begin,
		                    'time_end'   => $time_end,
		                    'id'         => $id,
		                    'uid_name'   => $uid_name,
		                    'type'       => $type,
		                    'methodtype' => $method,
		                    'op_name'    => $op_name,);
		//		$arrOutput = Service_Video_VideoSquareActivitySubPage::getVideoSquareActivitySub($arrInput);
		$arrOutput = Tieba_Service::call('ala', 'getLiveAuditBlack', $arrInput, null, null, 'post', null, 'utf-8');
		if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = __CLASS__."::".__FUNCTION__." call ala::getLiveAuditBlack fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::warning($strLog);
			Bingo_Http_Response::contextType('application/json');
			echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL), 'utf-8');
			
			return;
		}
        $arrResTmp = $arrOutput['data']['data'];
        $arrUid = array();
        foreach ($arrResTmp as $arrResTmpNick) {
            $arrUid[] = $arrResTmpNick['uid'];
        }

        $arrInput = array(
            "uids" => $arrUid,
        );
        $strServiceName = "ala";
        $strServiceMethod = "userGetInfo";

        $arrOutputUserInfo = Tieba_Service::call($strServiceName, $strServiceMethod, $arrInput, null, null, 'post', null, 'utf-8');
        if (false === $arrOutputUserInfo || Tieba_Errcode::ERR_SUCCESS != $arrOutputUserInfo["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutputUserInfo) . "]";
            Bingo_Log::warning($strLog);
        }

        foreach ($arrOutput['data']['data'] as &$arrResTmpRes){
            $intUid = $arrResTmpRes['uid'];
            $arrResTmpRes['nickname'] = $arrOutputUserInfo['data'][$intUid]['user_nickname'];
        }

        Bingo_Http_Response::contextType('application/json');
		echo Bingo_String::array2json(self::SqlResultsToRows($arrOutput), 'utf-8');
		
		return;
		// 默认成功返回值
		//            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, Tieba_Error::getUserMsg(Tieba_Errcode::ERR_SUCCESS), $retData);
	}
	
	/**
	 * 参数检查程序
	 *
	 * @param unknown $arrInput
	 *
	 * @return array
	 */
	private static function paramChecker($arrInput)
	{
		if(empty($arrInput['op_name'])){
			return false;
		}
		if($arrInput['methodtype'] == 'add'){
			if(empty($arrInput['activity_name']) || empty($arrInput['activity_describe']) || empty($arrInput['cover_pic']) || empty($arrInput['banner_pic']) || empty($arrInput['enable_time']) || empty($arrInput['expire_time']) || !isset($arrInput['place'])){
				return false;
			}
		}else if($arrInput['methodtype'] == 'delete'){
			if(empty($arrInput['id'])){
				return false;
			}
		}else if($arrInput['methodtype'] == 'alter'){
			if(empty($arrInput['id']) || empty($arrInput['activity_name']) || empty($arrInput['activity_describe']) || empty($arrInput['cover_pic']) || empty($arrInput['banner_pic']) || empty($arrInput['enable_time']) || empty($arrInput['expire_time']) || !isset($arrInput['place'])){
				return false;
			}
		}else{
			return true;
		}
		
		return true;
	}
	
	/**
	 * 返回值
	 *
	 * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
	 * @param  {Array} $arrOutData default array()
	 * @param  {String} $strMsg default ""
	 *
	 * @return {Array} :errno :errmsg :{Array}output
	 * */
	private static function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS, $arrOutData = array(), $strMsg = "")
	{
		$arrOutput            = array();
		$arrOutput['status']  = $intErrno;
		$arrOutput['msg']     = Alalib_Conf_Error::getErrorMsg($intErrno);
		$arrOutput['usermsg'] = $strMsg;
		$arrOutput['data']    = $arrOutData;
		
		return $arrOutput;
	}
	
	/**
	 * 将sql数据格式转换成amis能接受的数据格式
	 *
	 * @param null
	 *
	 * @return null
	 * */
	private static function SqlResultsToRows($retData)
	{
		$arrayData     = $retData['data']['data'];
		$ret           = array();
		$ret['status'] = 0;
		$ret['msg']    = '';
		$ret['data']   = array('rows'  => $arrayData,
		                       'count' => $retData['data']['count']);
		
		return $ret;
	}
	
}

?>
