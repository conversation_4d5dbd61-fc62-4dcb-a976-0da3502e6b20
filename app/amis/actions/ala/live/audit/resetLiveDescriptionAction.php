<?php
/**
 * resetLiveDescriptionAction.php 重置直播间标题
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 17/12/12 下午4:59
*/

class resetLiveDescriptionAction extends Util_Action
{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    /**
     * @return bool
     */
    public function _execute() {
        // amis数据获取
        $live_id     = intval(Bingo_Http_Request::get('live_id', 0));
        $uid         = intval(Bingo_Http_Request::get('uid', 0));
        $strUserName = strval(Bingo_Http_Request::get('user_name', ''));
        $comment     = strval(Bingo_Http_Request::get('comment', ''));
        $groupId     = intval(Bingo_Http_Request::get('groupId', 0));

        $op_id     = Util_User::$intUserId;
        $op_name   = Util_User::$strUserName;

        $strDesc   = $strUserName.'的直播间';

        $arrInput  = array(
            "live_id"     => $live_id,
            "description" => $strDesc,
        );

        $arrAlaRes = Tieba_Service::call("ala", "liveEditDescription", $arrInput, null, null, 'post', null, 'utf-8');

        if (!$arrAlaRes || $arrAlaRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(__CLASS__.'::'.__FUNCTION__."Call Service[ala] Interface[liveEditDescription] ERROR; input[".serialize($arrInput)."] output[".serialize($arrAlaRes)."]");

            return self::resultSender(self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array()));
        }

        // 附加警告
        if ($groupId > 0) {
            $arrInput = array(
                "group_id"  => intval($groupId),
                "msg_type"  => 13,
                "user_id"   => 852517826,
                "user_name" => "系统管理员",
                'to_uids'   => array($uid),
                "content"   => json_encode(array(
                    "content_type" => "remove_video",
                    // 下一版换新类型
                    "text"         => strval($comment),
                )),//"duration" => 600,
            );

            $arrAlaIMRes = Tieba_Service::call('alaim', 'commitPushGroupMsg', $arrInput, null, null, 'post', 'php', 'utf-8');

            if (!$arrAlaIMRes || $arrAlaIMRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("Call Service[alaim] Interface[commitPushGroupMsg] ERROR; input[".serialize($arrInput)."]; output[".serialize($arrAlaIMRes)."]");

                return self::resultSender(self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array()));
            }
        }

        // 写入处罚记录
        $arrServiceInput  = array(
            'uid'             => $uid,
            'punish_type'     => 7,
            'op_name'         => $op_name,
            'punish_time_log' => 0,
            'comment'         => $comment,
        );

        $strServiceName   = "ala";
        $strServiceMethod = "addPunishLiveUserRecord";

        $arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            $arrOutput["data"] = array();
        }

        return self::resultSender(self::errRet(Tieba_Errcode::ERR_SUCCESS, array()));
    }

    /**
	 * 返回值
     * @param int $intErrno
     * @param array $arrOutData
     * @param string $strMsg
     * @return array
     */
    private static function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS, $arrOutData = array(), $strMsg = "") {
        $arrOutput            = array();
        $arrOutput['status']  = $intErrno;
        $arrOutput['msg']     = Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput['usermsg'] = $strMsg;
        $arrOutput['data']    = $arrOutData;

        return $arrOutput;
    }

    /**
	 * 结果返回函数
     * @param array $arrInput
     * @param string $code_type
     * @return bool
     */
    private static function resultSender($arrInput, $code_type = 'utf-8') {
        echo Bingo_String::array2json($arrInput, $code_type);

        return true;
    }
}