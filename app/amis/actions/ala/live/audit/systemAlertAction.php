<?php

/**
 * Created by PhpStorm.
 * User: zhouyan08
 * Date: 17/4/27
 * Time: 下午4:48
 */
class systemAlertAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * 执行函数
	 *
	 * @param null
	 *
	 * @return null
	 * */
	public function _execute()
	{
		$userId  = Bingo_Http_Request::get('userId', 0);
		$groupId = intval(Bingo_Http_Request::get("groupId", 0));
		$message = Bingo_Http_Request::get("message", "");
        $liveId = Bingo_Http_Request::get("liveId", "");

        $op_id   = Util_User::$intUserId;
		$op_name = Util_User::$strUserName;
		if(empty($userId)){
			return self::resultSender(self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array()));
		}
		$user = self::getAlaUserData($userId);
		if(empty($user)){
			return self::resultSender(self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array()));
		}
		$user = array_shift($user);
		if($groupId <= 0 || empty($message)){
			return self::resultSender(self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array()));
		}
		$arrInput    = array(
			"group_id"  => intval($groupId),
			"msg_type"  => 13,
			"user_id"   => 852517826,
			"user_name" => "系统管理员",
			"content"   => json_encode(array(
				"content_type" => "remove_video",
				//下一版换新类型
				"text"         => strval($message),
			)),//"duration" => 600,
			'admin_message' => 1,
		);
		$arrAlaIMRes = Tieba_Service::call('alaim', 'commitPushGroupMsg', $arrInput, null, null, 'post', 'php', 'utf-8');
		if(!$arrAlaIMRes || $arrAlaIMRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
			//发警告消息失败先打个Log
			Bingo_Log::warning("Call Service[alaim] Interface[commitPushGroupMsg] ERROR; input[".serialize($arrInput)."]; output[".serialize($arrAlaIMRes)."]");
			//然后尝试解主播对发消息管理员帐号的封禁
			$arrServiceInput  = array(
				"ban_type"  => 2,
				"op_uid"    => $userId,
				"anchor_id" => $userId,
				"group_id"  => 1,
				"uid"       => 852517826,
			);
			$strServiceName   = "ala";
			$strServiceMethod = "liveTalkEnable";
			$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
			//再次重试发消息给主播,再出现问题就报错
			$arrInput    = array(
				"group_id"  => intval($groupId),
				"msg_type"  => 13,
				"user_id"   => 852517826,
				"user_name" => "系统管理员",
				"content"   => json_encode(array(
					"content_type" => "remove_video",
					//下一版换新类型
					"text"         => strval($message),
				)),//"duration" => 600,
                'admin_message' => 1,
			);
			$arrAlaIMRes = Tieba_Service::call('alaim', 'commitPushGroupMsg', $arrInput, null, null, 'post', 'php', 'utf-8');
			if(!$arrAlaIMRes || $arrAlaIMRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
				Bingo_Log::warning("Call Service[alaim] Interface[commitPushGroupMsg] ERROR; input[".serialize($arrInput)."]; output[".serialize($arrAlaIMRes)."]");
				
				return self::resultSender(self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array()));
			}
		}
		//写入数据库记录
        //        liveInfo
        $arrOutputLiveSub = Tieba_Service::call("ala", "liveGetInfo", array('live_ids' => array($liveId),), null, null, 'post', 'php', 'utf-8');
        if($arrOutputLiveSub != false && $arrOutputLiveSub['errno'] != Tieba_Errcode::ERR_SUCCESS){
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala userGetInfo fail. input:[" . serialize($liveId) . "]; output:[" . serialize($arrOutputLiveSub) . "]";
            Bingo_Log::warning($strLog);
        }

        $strSubAppType = $arrOutputLiveSub['data'][$liveId]['live_info']['subapp_type'];
        if(empty($strSubAppType)){
            $strSubAppType='tieba';
		}

        $arrServiceInput  = array(//TODO输入参数
		                          'uid'             => $userId,
		                          'live_id'         => $liveId,
		                          'subapp_type'     => $strSubAppType,
		                          'punish_type'     => 3,
		                          'op_name'         => $op_name,
		                          'punish_time_log' => 0,
		                          'comment'         => $message,
		);
		$strServiceName   = "ala";
		$strServiceMethod = "addPunishLiveUserRecord";
		$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			$arrOutput["data"] = array();
			return self::resultSender(self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL));
		}
		
		return self::resultSender(self::errRet(Tieba_Errcode::ERR_SUCCESS, array()));
	}
	
	/**
	 * 返回值
	 *
	 * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
	 * @param  {Array} $arrOutData default array()
	 * @param  {String} $strMsg default ""
	 *
	 * @return {Array} :errno :errmsg :{Array}output
	 * */
	private static function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS, $arrOutData = array(), $strMsg = "")
	{
		$arrOutput            = array();
		$arrOutput['status']  = $intErrno;
		$arrOutput['msg']     = Alalib_Conf_Error::getErrorMsg($intErrno);
		$arrOutput['usermsg'] = $strMsg;
		$arrOutput['data']    = $arrOutData;
		
		return $arrOutput;
	}
	
	/**
	 * 将sql数据格式转换成amis能接受的数据格式
	 *
	 * @param null
	 *
	 * @return null
	 * */
	private static function SqlResultsToRows($retData)
	{
		$arrayData     = $retData['data']['data'];
		$ret           = array();
		$ret['status'] = 0;
		$ret['msg']    = '';
		$ret['data']   = array(
			'rows'  => $arrayData,
			'count' => $retData['data']['count'],
		);
		
		return $ret;
	}
	
	/**
	 * 结果返回函数
	 *
	 * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
	 * @param  {Array} $arrOutData default array()
	 * @param  {String} $strMsg default ""
	 *
	 * @return {Array} :errno :errmsg :{Array}output
	 * */
	private static function resultSender($arrInput, $code_type = 'utf-8')
	{
		echo Bingo_String::array2json($arrInput, $code_type);
		
		return true;
	}
	
	/**
	 * 获取ala用户信息
	 *
	 * @param unknown $userId
	 *
	 * @return boolean|unknown
	 */
	public static function getAlaUserData($userId)
	{
		$req = array('uids' => array($userId,),);
		$res = Tieba_Service::call('ala', 'userGetInfo', $req, null, null, 'post', 'php', 'utf-8');
		if(false === $res || !isset($res['errno']) || Tieba_Errcode::ERR_SUCCESS !== $res['errno']){
			Bingo_Log::warning(__FUNCTION__." call service failed : userGetInfo, input =".serialize($req)." output =".serialize($res));
			
			return false;
		}
		
		return $res['data'];
	}
}

?>
