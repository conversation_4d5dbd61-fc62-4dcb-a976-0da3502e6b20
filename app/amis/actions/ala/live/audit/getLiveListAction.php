<?php

/**
 * Created by PhpStorm.
 * User: zhouyan08
 * Date: 17/4/27
 * Time: 下午4:48
 */
class getLiveListAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	const APP_TYPE_TIEBA = 'tieba';
	/**
	 * 执行函数
	 *
	 * @param null
	 *
	 * @return null
	 * */
	public function _execute()
	{
		//amis数据获取
		$page       = intval(Bingo_Http_Request::get('page', ''));
		$page_size  = intval(Bingo_Http_Request::get('page_size', 10));
		$time_begin = intval(Bingo_Http_Request::get('time_begin', ''));
		$time_end   = intval(Bingo_Http_Request::get('time_end', ''));
		$user_name  = strval(Bingo_Http_Request::get('user_name', ''));
		$user_id    = intval(Bingo_Http_Request::get('user_id', 0));
		$tab        = intval(Bingo_Http_Request::get('tab', ''));//1.普通 2.黑名单
		$type       = intval(Bingo_Http_Request::get('type', ''));//0.所有用户 1.非游戏主播 2.游戏主播
		$method     = strval(Bingo_Http_Request::get('method', ''));
		$ala_id    = intval(Bingo_Http_Request::get('ala_id',''));
		$subapp_type = strval(Bingo_Http_Request::get('subapp_type',''));//分开播源
		//$op_id      = Util_User::$intUserId;
		//$op_name    = Util_User::$strUserName;

		if($page != 0){
			$page = $page - 1;
		}
		//数据处理,根据输入的uname获取uid
		if(!empty($user_id)){
			$uid = intval($user_id);
		}else if(!empty($user_name)){
			$input['user_name'] = array($user_name);
			$ret                = Tieba_Service::call('user', 'getUidByUnames', $input, null, null, 'post', 'php', 'utf-8');
			if($ret === false || (isset($ret['errno']) && intval($ret['errno']) !== 0)){
				Bingo_Log::warning(__FUNCTION__." call service failed : getUidByUnames, input =".serialize($input)." output =".serialize($ret));
			}
			$uid = $ret['output']['uids'][0]['user_id'];
		}else if(!empty($ala_id)){
            $arrInput = array(
                'ala_id' => $ala_id,
            );
            $uidRet                = Tieba_Service::call('ala', 'getUserIdByAlaIdService', $arrInput, null, null, 'post', 'php', 'utf-8');
            $uid = $uidRet['data'];
        }
		//获取黑名单列表
		$ret = Tieba_Service::call('ala', 'getBlackList', array(), null, null, 'post', 'php', 'utf-8');
		if($ret === false || (isset($ret['errno']) && intval($ret['errno']) !== 0)){
			Bingo_Log::warning(__FUNCTION__." call service failed : getUidByUnames, input =".serialize($input)." output =".serialize($ret));

			return self::resultSender(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL));
		}
		$black_list = $ret['data'];
		//获取临时黑名单列表
		$ret = Tieba_Service::call('ala', 'getTemAduidtBlackLive', array(), null, null, 'post', 'php', 'utf-8');
		if($ret === false || (isset($ret['errno']) && intval($ret['errno']) !== 0)){
			Bingo_Log::warning(__FUNCTION__." call service failed : getUidByUnames, input =".serialize($input)." output =".serialize($ret));

			return self::resultSender(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL));
		}
		$black_list_tem = $ret['data'];
		if(empty($uid)){
			//如果uid为空
			//取出所有符合条件的直播间
            $arrServiceInput  = array(//TODO输入参数
                    'time_begin' => $time_begin,
                    'time_end'   => $time_end,
                    'live_type'  => $type,
                    'subapp_type'=> $subapp_type,
                );

            $LiveInfoList = array();
            if(!empty($subapp_type)&&self::APP_TYPE_TIEBA == $subapp_type){
                //由于tieba包含触手、pc等开播，所以把haokan、quanmin过滤
                $LiveInfoList = self::getLiveListByParams($arrServiceInput);
                //过滤haokan、quanmin直播间
                $LiveInfoList = self::getExceptHaokanQuanminLiveList($LiveInfoList,$arrServiceInput);
            }
            else{
                $LiveInfoList = self::getAppTypeLiveList($arrServiceInput);//根据subapp_type查询
            }
			//过滤黑名单
			$LiveInfoList = self::blackListFilter($LiveInfoList, $black_list, $black_list_tem, $tab);
		}else{
			//如果uid不为空
			//取出直播间
            $arrServiceInput  = array(//TODO输入参数
                    'uid'        => $uid,
                    'time_begin' => $time_begin,
                    'time_end'   => $time_end,
                    'live_type'  => $type,
                );
            $LiveInfoList = self::getLiveListByParams($arrServiceInput);
			//判断条件
			//过滤黑名单
			$LiveInfoList = self::blackListFilter($LiveInfoList, $black_list, $black_list_tem, $tab);
		}
		//截取
		$count        = count($LiveInfoList);
		$LiveInfoList = array_slice($LiveInfoList, empty($page) ? 0 : $page_size * intval($page), $page_size, false);
		//获取通过live_id获取所有需要的信息
		$arrLiveIds = self::arrayColumn($LiveInfoList, 'live_id');
		if(!empty($arrLiveIds)){
			$input     = array(
				'live_ids'     => $arrLiveIds,
				'fetch_fields' => array(
					"user_id",
					"user_name",
				),
				'need_user'    => 1,
				'from'         => 'pc',
			);
			$arrAlaRes = Tieba_Service::call('ala', 'liveGetInfo', $input, null, null, 'post', 'php', 'utf-8');
			if(!$arrAlaRes || $arrAlaRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
				Bingo_Log::warning("Call Service[ala] Interface[liveGetInfo] ERROR; input[".serialize($input)."] output[".serialize($arrAlaRes)."]");
				
				return self::resultSender(self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL));
			}
			$arrAlaRes['data'] = self::removeHaoKanQuanming($arrAlaRes['data']);
			
			$input    = array('live_ids' => $arrLiveIds,);
			$arrPhoto = Tieba_Service::call('ala', 'getLivePhotoAudit', $input, null, null, 'post', 'php', 'utf-8');
			if(!$arrPhoto || $arrPhoto['errno'] != Tieba_Errcode::ERR_SUCCESS){
				Bingo_Log::warning("Call Service[ala] Interface[getLivePhotoAudit] ERROR; input[".serialize($input)."] output[".serialize($arrPhoto)."]");
				
				return self::resultSender(self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL));
			}
			$arrPhoto     = $arrPhoto['data'];
			$lives        = array();
			$objMultiCall = new Tieba_Multi('get_live_tag_info');
			foreach($arrLiveIds as $liveId){
				$arrReq      = array('live_id' => $liveId,);
				$arrReqParam = array(
					'serviceName' => 'uegvideo',
					'method'      => 'alaGetLiveDetail',
					'input'       => $arrReq,
					'ie'          => 'utf-8',
				);
				$objMultiCall->register('get_live_tag_info_'.$liveId, new Tieba_Service('uegvideo'), $arrReqParam);
			}
			$objMultiCall->call();
			foreach($arrLiveIds as $liveId){
				$arrOut = $objMultiCall->getResult('get_live_tag_info_'.$liveId);
				if($arrOut === false || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
					$strLog = "uegvideo::alaGetLiveDetail call fail. input[".serialize($arrReq)."] output[".serialize($arrOut)."]";
					Bingo_Log::warning($strLog);
					continue;
				}
				$arrVideoRes[$liveId] = $arrOut['data'];
			}
			foreach($arrLiveIds as $liveid){
				$live = $arrAlaRes['data'][$liveid];
				if(empty($live)){
					continue;
				}
				if(isset($arrVideoRes[$liveid])){
					$live['tag_info'] = $arrVideoRes[$liveid];
				}else{
					$live['tag_info'] = array(
						'report_count'   => 0,
						'system_sexy'    => 0,
						'system_guaji'   => 0,
						'audience_count' => 0,
					);
				}
				/*if($live['live_info']['third_app_name'] == '触手'){
					//$live['pic']='http://hiphotos.baidu.com/fex/pic/item/80cb39dbb6fd5266822b7639a018972bd407369e.jpg';
					if(isset($live['live_info']['audit_screen_url']) && !empty($live['live_info']['audit_screen_url'])){
						$live['pic'] = $live['live_info']['audit_screen_url'];
					}else{
						$live['pic'] = $live['live_info']['cover'];
						if(isset($arrPhoto[$liveid])){
							$live['pic'] = $arrPhoto[$liveid];
						}
					}
				}else{*/
				if(isset($arrPhoto[$liveid])){
					$live['pic'] = $arrPhoto[$liveid];
				}else if($live['live_info']['third_app_name'] == '触手'){
					//$live['pic']='http://hiphotos.baidu.com/fex/pic/item/80cb39dbb6fd5266822b7639a018972bd407369e.jpg';
					if(isset($live['live_info']['audit_screen_url']) && !empty($live['live_info']['audit_screen_url'])){
						$live['pic'] = $live['live_info']['audit_screen_url'];
					}else{
						$live['pic'] = $live['live_info']['cover'];
					}
				}
				//}
				$arrSearch                                   = array(
					'http:',
					'https:',
				);
				$arrReplace                                  = array(
					'',
					'',
				);
				$live['live_info']['session_info']['hlsUrl'] = str_replace($arrSearch, $arrReplace, $live['live_info']['session_info']['hlsUrl']);
				$live['live_info']['session_info']['flvUrl'] = str_replace($arrSearch, $arrReplace, $live['live_info']['session_info']['flvUrl']);
				$live['black_icon']                          = 0;
				if($tab == 2 && in_array($live['user_info']['user_id'], $black_list)){
					$live['black_icon'] = 1;
				}
				// 17/12/8 gaojingjing03 被标记为挂机或飙车后，直播间被置顶到第一页第一行
				if($live['tag_info']['system_sexy'] > 0 || $live['tag_info']['system_guaji'] > 0){
					array_unshift($lives, $live);
				}else{
					$lives[] = $live;
				}
			}
			
			return self::resultSender(self::errRet(Alalib_Conf_Error::ERR_SUCCESS, array(
				'rows'  => $lives,
				'count' => $count,
			)));
		}else{
			return self::resultSender(self::errRet(Alalib_Conf_Error::ERR_SUCCESS, array(
				'rows'  => array(),
				'count' => $count,
			)));
		}
	}

    /**
     * 通过传的参数获取展示列表
     *
     * @param  {Array} $arrOutData default array()
     *
     * @return {Array} :errno :errmsg :{Array}output
     * */
	private static function getLiveListByParams($arrServiceInput){
        $strServiceName   = "ala";
        $strServiceMethod = "getLiveOnlineForAudit";
        $arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            $arrOutput["data"] = array();
        }
        $LiveInfoList = $arrOutput['data'];
        return  $LiveInfoList;

    }
	
	/**
	 * @param $LiveInfoList
	 *
	 * @return array
	 */
	private static function removeHaoKanQuanming($LiveInfoList)
	{
		$ret = array();
		foreach($LiveInfoList as $k => $v){
		    //去掉直播审核内的筛选好看全民信息，改为筛选is_private
			/**if(Alalib_Conf_Sdk::isIsolateToTieba($v['live_info']['subapp_type'])){
				continue;
			}**/
            if($v['live_info']['is_private'] == 1){
                continue;
            }
			$ret[$k] = $v;
		}
		
		return $ret;
	}
	
	/**
	 * 过滤黑名单
	 *
	 * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
	 * @param  {Array} $arrOutData default array()
	 * @param  {String} $strMsg default ""
	 *
	 * @return {Array} :errno :errmsg :{Array}output
	 * */
	private static function blackListFilter($LiveInfoList, $blackList, $black_list_tem, $tab)
	{
		$tem_ret_list = array();
		if($tab == 1){
			//取普通
			foreach($LiveInfoList as $liveInfo){
				if(in_array($liveInfo['user_id'], $blackList) || in_array($liveInfo['live_id'], $black_list_tem)){
					continue;
				}else{
					$tem_ret_list[] = $liveInfo;
				}
			}
		}else if($tab == 2){
			foreach($LiveInfoList as $liveInfo){
				if(in_array($liveInfo['user_id'], $blackList) || in_array($liveInfo['live_id'], $black_list_tem)){
					$tem_ret_list[] = $liveInfo;
				}
			}
		}
		//foreach($LiveInfoList as $liveInfo){
		//	$tem_live_list[$liveInfo['user_id']] = $liveInfo;
		//}
		//if($tab == 1){//取普通
		//	foreach($blackList as $black_user_id){
		//		if(isset($tem_live_list[$black_user_id])){
		//			unset($tem_live_list[$black_user_id]);
		//		}
		//	}
		//	$ret = array();
		//	foreach($tem_live_list as $live){
		//		$ret[] = $live;
		//	}
		//
		//	return $ret;
		//}else if($tab == 2){//取黑名单
		//	$tem_black_list = array();
		//	foreach($blackList as $black_user_id){
		//		if(isset($tem_live_list[$black_user_id])){
		//			$tem_black_list[] = $tem_live_list[$black_user_id];
		//		}
		//	}
		//
		//	return $tem_black_list;
		//}
		return $tem_ret_list;
	}
	
	/**
	 * 返回值
	 *
	 * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
	 * @param  {Array} $arrOutData default array()
	 * @param  {String} $strMsg default ""
	 *
	 * @return {Array} :errno :errmsg :{Array}output
	 * */
	private static function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS, $arrOutData = array(), $strMsg = "")
	{
		$arrOutput            = array();
		$arrOutput['status']  = $intErrno;
		$arrOutput['msg']     = Alalib_Conf_Error::getErrorMsg($intErrno);
		$arrOutput['usermsg'] = $strMsg;
		$arrOutput['data']    = $arrOutData;
		
		return $arrOutput;
	}
	
	/**
	 * 将sql数据格式转换成amis能接受的数据格式
	 *
	 * @param null
	 *
	 * @return null
	 * */
	private static function SqlResultsToRows($retData)
	{
		$arrayData     = $retData['data']['data'];
		$ret           = array();
		$ret['status'] = 0;
		$ret['msg']    = '';
		$ret['data']   = array(
			'rows'  => $arrayData,
			'count' => $retData['data']['count'],
		);
		
		return $ret;
	}
	
	/**
	 * 结果返回函数
	 *
	 * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
	 * @param  {Array} $arrOutData default array()
	 * @param  {String} $strMsg default ""
	 *
	 * @return {Array} :errno :errmsg :{Array}output
	 * */
	private static function resultSender($arrInput, $code_type = 'utf-8')
	{
		echo Bingo_String::array2json($arrInput, $code_type);
		
		return true;
	}
	
	/**
	 * 获得column
	 *
	 * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
	 * @param  {Array} $arrOutData default array()
	 * @param  {String} $strMsg default ""
	 *
	 * @return {Array} :errno :errmsg :{Array}output
	 * */
	private static function arrayColumn($arrInput, $str)
	{
		$ret = array();
		foreach($arrInput as $item){
			if(isset($item[$str])){
				$ret[] = $item[$str];
			}
		}
		
		return $ret;
	}

    /**
     * 根据类型获取开播列表
     * @param $arrInput
     * @return array
     */
	private static function getAppTypeLiveList($arrInput){
        $strServiceName   = "ala";
        $strServiceMethod = "getAppTypeLiveOnline";
        $arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrInput, null, null, "post", null, "utf-8");
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            $arrOutput["data"] = array();
        }
        $LiveInfoList = $arrOutput['data'];
        return  $LiveInfoList;
    }

    /**
     * 过滤haokan,quanmin开播直播间
     * @param $LiveInfoList
     * @param $exceptHaokanQuanminList
     * @return array
     */
    private static function getExceptHaokanQuanminLiveList($LiveInfoList){
        $strServiceName   = "ala";
        $strServiceMethod = "getHaokanQuanminLiveOnlineList";
        $arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrInput, null, null, "post", null, "utf-8");
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            $arrOutput["data"] = array();
        }
        $exceptHaokanQuanminList = $arrOutput['data'];

        $tem_ret_list = array();
        foreach($LiveInfoList as $liveInfo) {
            if (in_array($liveInfo['user_id'], $exceptHaokanQuanminList) || in_array($liveInfo['live_id'], $exceptHaokanQuanminList)) {
                continue;
            } else {
                $tem_ret_list[] = $liveInfo;
            }
        }
        return $tem_ret_list;
    }
}