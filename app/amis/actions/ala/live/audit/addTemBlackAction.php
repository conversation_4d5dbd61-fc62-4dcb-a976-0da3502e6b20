<?php

/**
 * Created by PhpStorm.
 * User: zhouyan08
 * Date: 17/4/27
 * Time: 下午4:48
 */
class addTemBlackAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * 执行函数
	 *
	 * @param null
	 *
	 * @return null
	 * */
	public function _execute()
	{
		//amis数据获取
		$live_id   = intval(Bingo_Http_Request::get('live_id', ''));
		$op_id     = Util_User::$intUserId;
		$op_name   = Util_User::$strUserName;
		$arrInput  = array('live_id' => $live_id);
		$arrOutput = Tieba_Service::call('ala', 'setTemAduidtBlackLive', $arrInput, null, null, 'post', null, 'utf-8');
		if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = __CLASS__."::".__FUNCTION__." call ala::setTemAduidtBlackLive fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::warning($strLog);
			Bingo_Http_Response::contextType('application/json');
			echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL), 'utf-8');
			
			return false;
		}
		Bingo_Http_Response::contextType('application/json');
		
		return self::resultSender(self::errRet(Tieba_Errcode::ERR_SUCCESS, array()));
	}
	
	/**
	 * 返回值
	 *
	 * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
	 * @param  {Array} $arrOutData default array()
	 * @param  {String} $strMsg default ""
	 *
	 * @return {Array} :errno :errmsg :{Array}output
	 * */
	private static function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS, $arrOutData = array(), $strMsg = "")
	{
		$arrOutput            = array();
		$arrOutput['status']  = $intErrno;
		$arrOutput['msg']     = Alalib_Conf_Error::getErrorMsg($intErrno);
		$arrOutput['usermsg'] = $strMsg;
		$arrOutput['data']    = $arrOutData;
		
		return $arrOutput;
	}
	
	/**
	 * 将sql数据格式转换成amis能接受的数据格式
	 *
	 * @param null
	 *
	 * @return null
	 * */
	private static function SqlResultsToRows($retData)
	{
		$arrayData     = $retData['data']['data'];
		$ret           = array();
		$ret['status'] = 0;
		$ret['msg']    = '';
		$ret['data']   = array('rows'  => $arrayData,
		                       'count' => $retData['data']['count']);
		
		return $ret;
	}
	
	/**
	 * 结果返回函数
	 *
	 * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
	 * @param  {Array} $arrOutData default array()
	 * @param  {String} $strMsg default ""
	 *
	 * @return {Array} :errno :errmsg :{Array}output
	 * */
	private static function resultSender($arrInput, $code_type = 'utf-8')
	{
		echo Bingo_String::array2json($arrInput, $code_type);
		
		return true;
	}
}

?>
