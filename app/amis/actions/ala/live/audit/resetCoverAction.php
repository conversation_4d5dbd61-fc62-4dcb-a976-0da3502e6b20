<?php

/**
 * Created by PhpStorm.
 * User: zhouyan08
 * Date: 17/4/27
 * Time: 下午4:48
 */
class resetCoverAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	const HAO_KAN ='haokan';
	const QUAN_MING = 'quanmin';
	/**
	 * 执行函数
	 *
	 * @param null
	 *
	 * @return null
	 * */
	public function _execute()
	{
		//amis数据获取
		$live_id   = intval(Bingo_Http_Request::get('live_id', ''));
		$uid       = intval(Bingo_Http_Request::get('uid', ''));
		$comment   = strval(Bingo_Http_Request::get('comment', ''));
		$strOldCover   = strval(Bingo_Http_Request::get('cover', ''));
		$groupId   = intval(Bingo_Http_Request::get('groupId', 0));
		$intResetBack   = intval(<PERSON><PERSON>_Http_Request::get('reset_back', 0));
		$strSubappType = strval(Bingo_Http_Request::get('subapp_type', ''));
		$op_id     = Util_User::$intUserId;
		$op_name   = Util_User::$strUserName;

        $cover     = "http://imgsrc.baidu.com/forum/pic/item/bf7d69d3fd1f4134a71a923a2c1f95cad1c85e2c.jpg";
		if (self::HAO_KAN == $strSubappType) {
            $cover = "https://imgsrc.baidu.com/forum/w%3D580/sign=4e97f094a664034f0fcdc20e9fc27980/13d61524bc315c6031c455a680b1cb13485477cb.jpg";
        }
		if (self::QUAN_MING == $strSubappType) {
            $cover = 'https://imgsrc.baidu.com/forum/w%3D580/sign=5af4cb85cecec3fd8b3ea77de689d4b6/aaaa3eb6d0a20cf4d24bc8967b094b36adaf994e.jpg';
        }

		// $cover     = "http://hiphotos.baidu.com/fex/%70%69%63/item/d53f8794a4c27d1ee993820010d5ad6edcc438b8.jpg";
        if ($intResetBack == 1) {
            // 说明是撤销重置
            $arrInput = array(
                "live_id" => $live_id,
            );
            $arrAlaRes = Tieba_Service::call("ala", "liveGetOldCover", $arrInput, null, null, 'post', null, 'utf-8');
            if (!$arrAlaRes || $arrAlaRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("Call Service[ala] Interface[liveGetOldCover] ERROR; input[" . serialize($arrInput) . "] output[" . serialize($arrAlaRes) . "]");
                return self::resultSender(self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array()));
            }
            $cover = $arrAlaRes['data']['cover'];
            if (empty($cover)) {
                return self::resultSender(self::_errRetMsg(Tieba_Errcode::ERR_PARAM_ERROR, array(), '该直播间未重置过封面', '该直播间未重置过封面'));
            }
        }

		$arrInput  = array(//"user_id" => $userId,
		                   //"user_name" => $userName,
		                   //"description" => $strUserInfo["description"],
		                   //"portrait" => $portrait,
		                   //"sex" => $strUserInfo["sex"],
		                   //"pass_name" => $strPassName,
		                   "live_id" => $live_id,
		                   "cover"   => $cover,
		);
		$arrAlaRes = Tieba_Service::call("ala", "liveEditCover", $arrInput, null, null, 'post', null, 'utf-8');
		if(!$arrAlaRes || $arrAlaRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning("Call Service[ala] Interface[liveEditCover] ERROR; input[".serialize($arrInput)."] output[".serialize($arrAlaRes)."]");
			
			return self::resultSender(self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array()));
		}

        if ($intResetBack != 1 && $strOldCover != '') {
            $arrInput = array(
                "live_id" => $live_id,
                "cover" => $strOldCover,
            );
            $arrAlaRes = Tieba_Service::call("ala", "liveSaveOldCover", $arrInput, null, null, 'post', null, 'utf-8');
            if (!$arrAlaRes || $arrAlaRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("Call Service[ala] Interface[liveSaveOldCover] ERROR; input[" . serialize($arrInput) . "] output[" . serialize($arrAlaRes) . "]");
            }
        }

		//附加警告
		if($groupId > 0){
			$arrInput    = array(
				"group_id"  => intval($groupId),
				"msg_type"  => 13,
				"user_id"   => 852517826,
				"user_name" => "系统管理员",
				//'to_uids'   => array($uid),
				"content"   => json_encode(array(
					"content_type" => "remove_video",
					//下一版换新类型
					"text"         => strval($comment),
				)),//"duration" => 600,
			);
			$arrAlaIMRes = Tieba_Service::call('alaim', 'commitPushGroupMsg', $arrInput, null, null, 'post', 'php', 'utf-8');
			if(!$arrAlaIMRes || $arrAlaIMRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
				Bingo_Log::warning("Call Service[alaim] Interface[commitPushGroupMsg] ERROR; input[".serialize($arrInput)."]; output[".serialize($arrAlaIMRes)."]");
				
				return self::resultSender(self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array()));
			}
		}
		//写入数据库记录
		$arrServiceInput  = array(//TODO输入参数
		                          'uid'             => $uid,
		                          'punish_type'     => 5,
		                          'op_name'         => $op_name,
		                          'punish_time_log' => 0,
		                          'comment'         => $comment,
		);
		$strServiceName   = "ala";
		$strServiceMethod = "addPunishLiveUserRecord";
		$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			$arrOutput["data"] = array();
		}
		
		return self::resultSender(self::errRet(Tieba_Errcode::ERR_SUCCESS, array()));
	}
	
	/**
	 * 返回值
	 *
	 * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
	 * @param  {Array} $arrOutData default array()
	 * @param  {String} $strMsg default ""
	 *
	 * @return {Array} :errno :errmsg :{Array}output
	 * */
	private static function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS, $arrOutData = array(), $strMsg = "")
	{
		$arrOutput            = array();
		$arrOutput['status']  = $intErrno;
		$arrOutput['msg']     = Alalib_Conf_Error::getErrorMsg($intErrno);
		$arrOutput['usermsg'] = $strMsg;
		$arrOutput['data']    = $arrOutData;
		
		return $arrOutput;
	}

    /**
     * 返回值
     * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
     * @param  {Array} $arrOutData default array()
     * @param  {String} $strMsg default ""
     * @return {Array} :errno :errmsg :{Array}output
     * */
    private static function _errRetMsg($intErrno = Alalib_Conf_Error::ERR_SUCCESS, $arrOutData = array(), $strMsg = "", $strUserMsg = "")
    {
        $arrOutput            = array();
        $arrOutput['status']  = $intErrno;
        $arrOutput['msg']     = $strMsg;
        $arrOutput['usermsg'] = $strUserMsg;
        $arrOutput['data']    = $arrOutData;

        return $arrOutput;
    }
	
	/**
	 * 将sql数据格式转换成amis能接受的数据格式
	 *
	 * @param null
	 *
	 * @return null
	 * */
	private static function SqlResultsToRows($retData)
	{
		$arrayData     = $retData['data']['data'];
		$ret           = array();
		$ret['status'] = 0;
		$ret['msg']    = '';
		$ret['data']   = array(
			'rows'  => $arrayData,
			'count' => $retData['data']['count'],
		);
		
		return $ret;
	}
	
	/**
	 * 结果返回函数
	 *
	 * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
	 * @param  {Array} $arrOutData default array()
	 * @param  {String} $strMsg default ""
	 *
	 * @return {Array} :errno :errmsg :{Array}output
	 * */
	private static function resultSender($arrInput, $code_type = 'utf-8')
	{
		echo Bingo_String::array2json($arrInput, $code_type);
		
		return true;
	}
	
	/**
	 * [getLive  通过单个 liveid 返回直播信息]
	 * <AUTHOR>
	 *
	 * @param
	 *
	 * @return
	 */
	public static function getLive($liveid)
	{
		$arrInput  = array(
			'live_ids'     => array($liveid),
			//'user_id' => 10238797,//主播id,如果是需要获取推流session,需要带有主播的id，为了保护推流地址
			'fetch_fields' => array(
				"user_id",
				"user_name",
			),
			//需要返回的数据，不写默认全返回
			'need_user'    => 1,// 需要返回的数据，如果需要全部返还，写1即可('need_user' => 1)，
		);
		$arrAlaRes = Tieba_Service::call('ala', 'liveGetInfo', $arrInput, null, null, 'post', 'php', 'utf-8');
		if(!$arrAlaRes || $arrAlaRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning(__FUNCTION__." Call Service[ala] Interface[liveGetInfo] ERROR; input: ".serialize($arrInput)."; output: ".serialize($arrAlaRes));
			
			return false;
		}
		
		return isset($arrAlaRes['data'][$liveid]) ? $arrAlaRes['data'][$liveid] : false;
	}
}

?>
