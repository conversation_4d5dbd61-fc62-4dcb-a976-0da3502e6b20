<?php
/**
 * getListAction.php
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 17/11/23 下午4:59
*/

class getListAction extends Util_Ala_STS_GetList
{
    protected $_strSTSName = 'liveAchievement';

    /**
     * 处理列表数据
     * @param array $arrList
     * @return array
     */
    protected function _handleList($arrList) {
        foreach ($arrList as &$v) {
            if (!empty($v['icon'])) {
                $arrIcons = unserialize($v['icon']);
                $v['icon_ios']           = $arrIcons['ios'];
                $v['icon_android']       = $arrIcons['android'];
                $v['icon_ios_close']     = $arrIcons['ios_close'];
                $v['icon_android_close'] = $arrIcons['android_close'];
                unset($v['icon']);
            }
        }

        return $arrList;
    }
}