<?php
/**
 * addAction.php
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 17/11/23 下午7:18
*/

class addAction extends Util_Ala_STS_Add
{
    protected $_strSTSName = 'liveAchievement';

    protected $_isGetNoXSSSafe = true;

    /**
     * @param $arrSTSParam
     * @return mixed
     */
    protected function _preHandleParam($arrSTSParam) {
        $strIconIOS          = $arrSTSParam['icon_ios'];
        $strIconAndroid      = $arrSTSParam['icon_android'];
        $strIconIOSClose     = $arrSTSParam['icon_ios_close'];
        $strIconAndroidClose = $arrSTSParam['icon_android_close'];

        $arrIcon = array(
            'ios'           => $strIconIOS,
            'android'       => $strIconAndroid,
            'ios_close'     => $strIconIOSClose,
            'android_close' => $strIconAndroidClose,
        );

        $arrSTSParam['icon'] = serialize($arrIcon);

        $arrSTSParam['create_time'] = Bingo_Timer::getNowTime();
        $arrSTSParam['update_time'] = Bingo_Timer::getNowTime();

        unset($arrSTSParam['icon_ios'], $arrSTSParam['icon_android'], $arrSTSParam['icon_ios_close'], $arrSTSParam['icon_android_close']);

        return $arrSTSParam;
    }
}