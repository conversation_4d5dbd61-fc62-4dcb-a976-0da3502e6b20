<?php

/**
 * Created by PhpStorm.
 * User: zhouyan08
 * Date: 17/4/27
 * Time: 下午4:48
 */
class livePunishRecordAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * 执行函数
	 *
	 * @param null
	 *
	 * @return null
	 * */
	public function _execute()
	{
		//数据获取
		$uid         = intval(Bingo_Http_Request::get('uid', ''));
		$name        = intval(Bingo_Http_Request::get('name', ''));
		$punish_type = strval(Bingo_Http_Request::get('punish_type', ''));
		$strSource   = strval(Bingo_Http_Request::get('source', ''));
		$time        = intval(Bingo_Http_Request::get('time', ''));
		$comment     = strval(Bingo_Http_Request::get('comment', ''));
		$page        = intval(Bingo_Http_Request::get('page', 1));
		$time_begin  = intval(Bingo_Http_Request::get('time_begin', ''));
		$time_end    = intval(Bingo_Http_Request::get('time_end', ''));
		$id          = intval(Bingo_Http_Request::get('id', ''));
		$uid_name    = intval(Bingo_Http_Request::get('uid_name', ''));
		$status      = intval(Bingo_Http_Request::get('status', 2));
		$state       = intval(Bingo_Http_Request::get('state', 2));
		$type        = intval(Bingo_Http_Request::get('type', ''));
		$method      = strval(Bingo_Http_Request::get('method', ''));
		$op          = strval(Bingo_Http_Request::get('op_name', ''));
		$freeOpName  = strval(Bingo_Http_Request::get('free_op_name', ''));
        $subApp  = strval(Bingo_Http_Request::get('sub_app', ''));

        $op_id       = Util_User::$intUserId;
		$op_name     = Util_User::$strUserName;
		if($status == 3){
			$status = 2;
			$state  = 1;
		}
		if($status == 0 || $status == 1){
			$state = 0;
		}
		$arrInput = array(
			'uid'          => $uid,
			'name'         => $name,
			'punish_type'  => $punish_type,
			'source'       => $strSource,
			'time'         => $time,
			'comment'      => $comment,
			'page'         => $page,
			'time_begin'   => $time_begin,
			'time_end'     => $time_end,
			'id'           => $id,
			'uid_name'     => $uid_name,
			'type'         => $type,
			'methodtype'   => $method,
			'op_name'      => $op_name,
			'op_id'        => $op_id,
			'status'       => $status,
			'op'           => $op,
			'free_op_name' => $freeOpName,
			'state'        => $state,
			'subapp_type'  => $subApp,
		);
		//		$arrOutput = Service_Video_VideoSquareActivitySubPage::getVideoSquareActivitySub($arrInput);
		$arrOutput = Tieba_Service::call('ala', 'getLivePunishRecord', $arrInput, null, null, 'post', null, 'utf-8');
		if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
			if($method == 'delete'){
				return Util_Ala_Common::jsonAmisRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '如果是解封禁言+禁直播报错,请连续点三次解封的叉就好,虽然会报这个提示但是实际上会成功,如果单独解禁言或者禁直播报错那就是真的报错了');
			}
			$strLog = __CLASS__."::".__FUNCTION__." call ala::getLivePunishRecord fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::warning($strLog);
			Bingo_Http_Response::contextType('application/json');
			echo Bingo_String::array2json(self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL), 'utf-8');
			
			return;
		}
        $arrResTmp = $arrOutput['data']['data'];
        $arrUid = array();
        foreach ($arrResTmp as $arrResTmpNick) {
            $arrUid[] = $arrResTmpNick['uid'];
        }

        $arrInput = array(
            "uids" => $arrUid,
        );
        $strServiceName = "ala";
        $strServiceMethod = "userGetInfo";

        $arrOutputUserInfo = Tieba_Service::call($strServiceName, $strServiceMethod, $arrInput, null, null, 'post', null, 'utf-8');
        if (false === $arrOutputUserInfo || Tieba_Errcode::ERR_SUCCESS != $arrOutputUserInfo["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutputUserInfo) . "]";
            Bingo_Log::warning($strLog);
        }

        foreach ($arrOutput['data']['data'] as &$arrResTmpRes){
            $intUid = $arrResTmpRes['uid'];
            $arrResTmpRes['nickname'] = $arrOutputUserInfo['data'][$intUid]['user_nickname'];
        }

        // 解除禁言发IM
        if ($method == 'delete' && ($punish_type == 1 || $punish_type == 6)) {
            $arrReq = array(
                'uids' => array($uid),
            );
            $strService = 'ala';
            $strMethod = 'userGetInfo';
            $arrRes = Alalib_Util_Service::call($strService, $strMethod, $arrReq);
            if ($arrRes === false || $arrRes['errno'] != 0) {
                $strLog = __CLASS__."::".__FUNCTION__. "  call $strService::$strMethod fail. input:[".serialize($arrReq)."]; output:[".serialize($arrRes)."]";
                Bingo_Log::fatal($strLog);
            }
            $arrUserInfo = $arrRes['data'][$uid];
            Bingo_Log::notice(__CLASS__ . "::" . __FUNCTION__ . ' ban userinfo is : ' . serialize($arrUserInfo));

            $arrLiveInput = array();

            // 如果是观众,获取当前用户所在直播间
            if ($arrUserInfo['enter_live'] > 0) {// 如果用户当前在直播间内 再发IM
                $imLiveId = $arrUserInfo['enter_live'];
            } else {
                // enter_live为空时，从Redis中获取用户当前所在直播间
                $strKey = 'user_enter_live_list_' . $uid;
                $arrInput = array(
                    "key" => $strKey,
                    "index" => -1,
                );
                $arrOutPut= Util_Redis::LINDEX($arrInput);
                $userEnterInfo = array();

                if ($arrOutPut !== false) {
                    $userEnterInfo = unserialize($arrOutPut);
                }
                $imLiveId = $userEnterInfo['live_id'];
            }

            if ($imLiveId > 0) {
                $arrLiveInput['live_ids'][] = $imLiveId;
            }

            // 如果是主播,获取当前的主态直播间
            if ($arrUserInfo['anchor_live'] > 0 && $arrUserInfo['anchor_live'] != $imLiveId) {
                $arrLiveInput['live_ids'][] = $arrUserInfo['anchor_live'];
            }


            if (!empty($arrLiveInput)) {
                $toUserId = array($uid);

                $strServiceName = "ala";
                $strServiceMethod = "liveGetInfo";
                $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrLiveInput,null,null,'post',null,'utf-8');
                if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__."::".__FUNCTION__." uid : " . $uid . " get liveInfo fail. input: " . serialize($arrLiveInput) . " output is : " . serialize($arrOutput);
                    Bingo_Log::fatal($strLog);
                } else {
                    // 发送封禁IM
                    foreach ($arrLiveInput['live_ids'] as $liveId) {
                        $strService = "ala";
                        $strMethod = "imCommitAdminTalkBanMsg";
                        $intBlockGroupId = $arrOutput['data'][$liveId]["live_info"]['group_id'];
                        $arrReq = array(
                                'user_id' => $uid,
                                'group_id' => $intBlockGroupId,
                                'user_name' =>  empty($arrUserInfo["user_name"]) ? strval($arrUserInfo["user_nickname"]) : strval($arrUserInfo["user_name"]),
                                'level_id' => $arrUserInfo['level_id'],
                                'opt_type' => 2, // 1禁言/2解禁
                                'to_uids' => array($uid),
                                //'op_role' => $intAnchorId == $intUserId ? Libs_Define_Live::LIVETALK_ROLE_ANCHOR : Libs_Define_Live::LIVETALK_ROLE_ADMIN,
                                //'sys_notify' => 1, //禁言后第一条系统通知,绕过禁言限制
                        );
                        $arrRes = Tieba_Service::call($strService, $strMethod, $arrReq, null, null, 'post', null, 'utf-8');
                        if ($arrRes === false || $arrRes['errno'] == Tieba_Errcode::ERR_DL_CALL_FAIL ||
                            $arrRes['errno'] == Tieba_Errcode::ERR_RPC_CALL_FAIL || $arrRes['errno'] == Tieba_Errcode::ERR_CALL_SERVICE_FAIL) {
                            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  call $strService::$strMethod fail. input:[" . serialize($arrReq) . "]; output:[" . serialize($arrRes) . "]";
                            Bingo_Log::fatal($strLog);
                        }
                    }
                }
            }
        }


        Bingo_Http_Response::contextType('application/json');
		echo Bingo_String::array2json(self::SqlResultsToRows($arrOutput), 'utf-8');
		
		return;
		// 默认成功返回值
		//            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, Tieba_Error::getUserMsg(Tieba_Errcode::ERR_SUCCESS), $retData);
	}
	
	/**
	 * 参数检查程序
	 *
	 * @param unknown $arrInput
	 *
	 * @return array
	 */
	private static function paramChecker($arrInput)
	{
		if(empty($arrInput['op_name'])){
			return false;
		}
		if($arrInput['methodtype'] == 'add'){
			if(empty($arrInput['activity_name']) || empty($arrInput['activity_describe']) || empty($arrInput['cover_pic']) || empty($arrInput['banner_pic']) || empty($arrInput['enable_time']) || empty($arrInput['expire_time']) || !isset($arrInput['place'])){
				return false;
			}
		}else if($arrInput['methodtype'] == 'delete'){
			if(empty($arrInput['id'])){
				return false;
			}
		}else if($arrInput['methodtype'] == 'alter'){
			if(empty($arrInput['id']) || empty($arrInput['activity_name']) || empty($arrInput['activity_describe']) || empty($arrInput['cover_pic']) || empty($arrInput['banner_pic']) || empty($arrInput['enable_time']) || empty($arrInput['expire_time']) || !isset($arrInput['place'])){
				return false;
			}
		}else{
			return true;
		}
		
		return true;
	}
	
	/**
	 * 返回值
	 *
	 * @param  {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
	 * @param  {Array} $arrOutData default array()
	 * @param  {String} $strMsg default ""
	 *
	 * @return {Array} :errno :errmsg :{Array}output
	 * */
	private static function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS, $arrOutData = array(), $strMsg = "")
	{
		$arrOutput            = array();
		$arrOutput['status']  = $intErrno;
		$arrOutput['msg']     = Alalib_Conf_Error::getErrorMsg($intErrno);
		$arrOutput['usermsg'] = $strMsg;
		$arrOutput['data']    = $arrOutData;
		
		return $arrOutput;
	}
	
	/**
	 * 将sql数据格式转换成amis能接受的数据格式
	 *
	 * @param null
	 *
	 * @return null
	 * */
	private static function SqlResultsToRows($retData)
	{
		$arrayData = $retData['data']['data'];
		foreach($arrayData as &$item){
			if($item['state'] == 1){
				continue;
			}
			if($item['state'] == 0){
				if($item['expire_time'] > time()){
					$item['state'] = 2;
				}else{
					$item['state'] = 3;
				}
			}
		}
		$ret           = array();
		$ret['status'] = 0;
		$ret['msg']    = '';
		$ret['data']   = array(
			'rows'  => $arrayData,
			'count' => $retData['data']['count'],
		);
		
		return $ret;
	}
	
}

?>
