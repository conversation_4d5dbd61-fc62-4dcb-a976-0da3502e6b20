<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date      2013-01-05 14:23:53
 * @version
 */
class closeLiveAction extends Util_Action
{
	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * @return bool
	 */
	public function _execute()
	{
		$limit     = 8;
		$liveId    = intval(Bingo_Http_Request::get("live_id"));
		$userId    = intval(Bingo_Http_Request::get("user_id"));
		$reason    = Bingo_Http_Request::get("reason", '');
		$remarks   = Bingo_Http_Request::get("remarks", '');
		$closetext = "你触犯贴吧直播的内容管理规范，直播间被关闭!";
		$op_id     = Util_User::$intUserId;
		$op_name   = Util_User::$strUserName;
		if($reason == "其他" && !empty($remarks)){
			$closetext = $remarks;
		}
		$input     = array("live_id"      => $liveId,
		                   "close_type"   => 2, // 1正常，2，UEG，3,异常
		                   "close_reason" => $closetext,);
		$arrOutput = Tieba_Service::call("ala", "liveClose", $input, null, null, 'post', 'php', 'utf-8');
		if($arrOutput != false && $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS){
			$this->outputJson($arrOutput);
			
			return false;
		}
		$logInput = array('operator'       => $op_name,
		                  'target_user_id' => $userId,
		                  'reason'         => $reason,
		                  'remarks'        => $remarks,);
		Bingo_Log::warning(json_encode($logInput));
		$arrOutput = Tieba_Service::call("alamis", "addLiveOpLog", $logInput, null, null, 'post', 'php', 'utf-8');
		$this->outputJson(array('errno' => Tieba_Errcode::ERR_SUCCESS,
		                        'data'  => true,));
		
		return true;
	}
	
	/**
	 * @param $arrRet
	 */
	public function outputJson($arrRet)
	{
		header("Content-type: application/json");
		echo json_encode($arrRet);
	}
}

?>
