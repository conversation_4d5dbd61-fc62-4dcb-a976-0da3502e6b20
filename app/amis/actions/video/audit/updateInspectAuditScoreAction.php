<?php

/**
 * Created by PhpStorm.
 * User: shi<PERSON><PERSON>
 * Date: 6/22 2017
 * Time: 11:00
 */
define('BASE_PATH', dirname(__FILE__));

require_once(BASE_PATH.'/../../../service/AuditQuality.php');
class updateInspectAuditScoreAction extends Util_Action {
    protected $strAmisGroup = 'video-verify';
    protected $strAmisPerm = 'video:check';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;
    /**
     * [_execute description]
     * @return [type] [description]
     */
    public function _execute() {
        $auditId = Bingo_Http_Request::get("audit_id");
        $weekkey = Bingo_Http_Request::get("weekkey");
        $auditScore = Bingo_Http_Request::get("audit_score");
        $intOpUid = Util_User::$intUserId;
        $strOpUname = Util_User::$strUserName;
        Bingo_Log::pushNotice("audit_id", $auditId);
        Bingo_Log::pushNotice("audit_score", $auditScore);
        Bingo_Log::pushNotice("op_uname", $strOpUname);
        Bingo_Log::pushNotice("weekkey", $weekkey);
        $arrServiceInput = array(
            "audit_id" => $auditId,
            "weekkey" => $weekkey,
            "audit_score" => $auditScore,
            "op_uname" => $strOpUname,
            "op_uid" => $intOpUid,
        );
        $arrOutput = Service_AuditQuality::updateInspectAuditScore($arrServiceInput);
        Bingo_Log::warning("updateInspectAuditScore: ". serialize($arrServiceInput). " out: ".var_export($arrOutput,true));
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            Bingo_Log::warning('amis updateInspectAuditScore Failed,input:['.serialize($arrServiceInput).'].output:['.serialize($arrOutput).']');
            $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "", array("rows" => array()));
            return;
        }
        $this->printOut(Tieba_Errcode::ERR_SUCCESS, "", $arrOutput['output']);
    }
}
?>