<?php
/**
 * 这个代码已经不用了
 * 视频未通过审核
 * Created by PhpStorm.
 * User: zhanghanqing
 * Date: 2017/6/13
 * Time: 14:35
 */
define('BASE_PATH', dirname(__FILE__));

require_once(BASE_PATH.'/../../../service/Xiaoying.php');

class deleteThreadAction extends Util_Action {

    protected $strAmisGroup = 'video-verify';
    protected $strAmisPerm = 'video:status';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner =false;
    private static $_arrAuditWhiteUsers = array(
        'huangling02' => 1,
        'dongliang04' => 1,
        'donghao02'   => 1,
    );

    /**
     * @brief 删除  
     * @param errno,errmsg,data
     * @return:  0.
     **/
    public function _execute() {
        return self::_jsonRet(0,'success but no in use');
    }

    

    /**
     * @brief _jsonRet
     * @param errno,errmsg,data
     * @return:  0.
     **/
    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array()){
        $arrRet = array(
            'no'=>intval($errno),
            'error'=>strval($errmsg),
            'data'=>$arrExtData,
        );
        Bingo_Log::pushNotice("errno",$errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet,Bingo_Encode::ENCODE_UTF8);
        return 0;
    }

    /**
     * @brief 删除视频
     * @param errno,errmsg,data
     * @return:  0.
     **/
    protected function _delVideoFile($strDelReason,$strVideoUrl){
        $arrVideoInfo = explode("/", $strVideoUrl);
        $strFileName = end($arrVideoInfo);
        $input = array(
            'product'   => 'tieba-smallvideo',
            'url' 		=> $strVideoUrl,
            'file_name'	=> $strFileName,
        );

        Bingo_Log::warning(' delete video file  ~,reason:['.$strDelReason.'].url:['.$strVideoUrl.']');
        $output = Tieba_Service::call('video', 'delMoVideo', $input, null, null, 'post', 'php', 'utf-8');
        if (false === $output || Tieba_Errcode::ERR_SUCCESS !== $output['errno']) {
            Bingo_Log::warning('call video:delMoVideo error. input:' . serialize($input) . 'output:' . serialize($output));
            return false;
        }
        return true;
    }

}