<?php
/**
 * date: 2018-03-20 10:33:10
 * author : dongliang04
 **/

define('BASE_PATH', dirname(__FILE__));

require_once(BASE_PATH. '/../../../service/AuditQuality.php');

class getInspectJobTotalAction extends Util_Action{
    protected $strAmisGroup = 'video-verify';
    protected $strAmisPerm = 'video:check';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    public function _execute(){
        $strStartTime = Bingo_Http_Request::get("start_time", strtotime(date('Y-m-d')));

        $strEndTime = Bingo_Http_Request::get("end_time", $strStartTime + 86399);
        Bingo_Log::pushNotice("start_time", $strStartTime);
        Bingo_Log::pushNotice("end_time", $strEndTime);


        $intOpUid = Util_User::$intUserId;
        $strOpUname = Util_User::$strUserName;

        $arrServiceInput = array(
            'start_time' => $strStartTime, 
            'end_time' => $strEndTime,
        );


        $arrOutput = Service_AuditQuality::getInspectJobTotal($arrServiceInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            Bingo_Log::warning('amis getInspectJobTotal Failed,input:['.serialize($arrServiceInput).'].output:['.serialize($arrOutput).']');
            $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, "", array("rows" => array()));
            return;
        }

        //接下来记一下抽取的人 一个inspect_id对应一个
        $this->printOut(Tieba_Errcode::ERR_SUCCESS, "", array("rows" => $arrOutput['output']));

    }
}