<?php
/**
 * Created by PhpStorm.
 * User: echo
 * Date: 2018/3/20
 * Time: 09:21
 */

class getBlackVideoListAction extends Util_Action{

    protected $strAmisGroup = 'video-verify';
    protected $strAmisPerm = 'video:status';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    /**
     * @brief execute
     * @return mixed
     */
    public function _execute()
    {

        $intPn = intval(Bingo_Http_Request::get('pn', 1));
        $intRn = intval(Bingo_Http_Request::get('rn', 10));
        $strVideoMd5 = strval(Bingo_Http_Request::get('video_md5'));

        // param check
        if (empty($strVideoMd5)) {
            Bingo_Log::warning("param error. video_md5 is empty");
            return $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS);
        }

        // get video md5
        $arrParam = array(
            'pn'        => $intPn,
            'rn'        => $intRn,
            'video_md5' => $strVideoMd5,
        );
        $arrRet = Tieba_Service::call('video', 'getBlackVideoByMd5', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call video getBlackVideoByMd5 failed. [input = %s][output = %s]",
                serialize($arrParam), serialize($arrRet)));
            return $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrTids = array();
        $arrThread = $arrRet['data']['rows'];
        foreach ($arrThread as $thread) {
            if ($thread['thread_id'] != 0) {
                $arrTids[] = $thread['thread_id'];
            }
        }
        $arrTids = array_unique($arrTids);

        $arrStatusMap = array();
        if (!empty($arrTids)) {
            $input = array(
                "thread_ids" => $arrTids,
                "need_abstract" =>0,
                "forum_id" => 0,
                "need_photo_pic" => 0,
                "need_user_data" => 0,
                "icon_size" => 0,
                "need_forum_name" => 0, //是否获取吧名
                "call_from" => "pc_frs" //上游模块名
            );
            $arrRes   = Tieba_Service::call('post', 'mgetThread', $input, null, null, 'post', 'php', 'utf-8');
            if (Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
                return $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            foreach ($arrRes['output']['thread_list'] as $thread) {
                $arrStatusMap[$thread['thread_id']] = $thread['is_deleted'];
            }
        }
        foreach ($arrRet['data']['rows'] as &$arrThread) {
            $arrThread['status'] = empty($arrStatusMap[$arrThread['thread_id']]) ? 0 : 1;
        }

        return $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, 'success', $arrRet['data']);
    }

    /**
     * @brief jsonRet
     * @param $errno
     * @param $errmsg
     * @param $arrExtData
     **/
    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array())
    {
        $arrRet = array(
            'no'    => intval($errno),
            'error' => strval($errmsg),
            'data'  => $arrExtData,
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
    }
}