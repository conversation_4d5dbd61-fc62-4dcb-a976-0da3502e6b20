<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2017/10/20
 * Time: 16:18
 */
class restoreVideoThumbnailAction extends Util_Action {

    protected $strAmisGroup = 'video-verify';
    protected $strAmisPerm = 'video:status';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    /**
     * 子类必须实现
     * @return [type] [description]
     */
    public function _execute()
    {
        $video_audit_id = (int)Bingo_Http_Request::get('video_audit_id', 0);
        $weekkey = Bingo_Http_Request::get('weekkey', '');

        if ($video_audit_id <= 0 || empty($weekkey)) {
            Bingo_Log::warning("param error, video_audit_id=$video_audit_id, weekkey=$weekkey");
            $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
            return;
        }

        $arrParams = array(
            'video_audit_id' => $video_audit_id,
            'weekkey' => $weekkey,
        );
        $arrRet = Tieba_Service::call('video', 'restoreVideoThumbnail', $arrParams, null, null, 'post', 'php', 'utf-8');
        if (!$arrRet) {
            Bingo_Log::warning('call service video::restoreVideoThumbnail fail');
            $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service fail');
            return;
        }
        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('service video::restoreVideoThumbnail return error, params='.json_encode($arrParams)
                .', ret=' .json_encode($arrRet));
            $this->printOut($arrRet['errno'], $arrRet['errmsg']);
            return;
        }

        $this->printOut(Tieba_Errcode::ERR_SUCCESS, 'success', $arrRet['data']);
    }

}
