<?php
/**
 * 视频审核通过/拒绝/恢复
 * User: huangling02
 * Date: 2017/10/23
 * Time: 14:35
 */
define('BASE_PATH', dirname(__FILE__));
define ( 'ROOT_PATH', dirname ( __FILE__ ) . '/../../../../..' );

require_once(BASE_PATH.'/../../../service/Xiaoying.php');
require_once (ROOT_PATH.'/app/video/lib/Redis.php');

class auditThreadAction extends Util_Action {

    const OP_TYPE_PASS = 1;
    const OP_TYPE_DELETE = 3;

    protected $strAmisGroup = 'video-verify';
    protected $strAmisPerm = 'video:status';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;
    private static $_arrAuditWhiteUsers = array(
        'huangling02' => 1,
        'dongliang04' => 1,
        'donghao02'   => 1,
    );
    /**
     * @brief 通过审核
     * @param errno,errmsg,data
     * @return:  0.
     **/
    public function _execute() {
        $intOpUid   = Util_User::$intUserId;
        $strOpUname = Util_User::$strUserName;
        $intOpType  = intval(Bingo_Http_Request::getNoXssSafe('op_type', 0));
        $intPushToRecom = intval(Bingo_Http_Request::getNoXssSafe('push2recom', 0));
        $strDeleteReason = Bingo_Http_Request::getNoXssSafe('delete_reason');
        $intAuditFlag = intval(Bingo_Http_Request::getNoXssSafe('audit_flag',1));

        if($intOpUid <= 0 || empty($strOpUname) || $intOpType<=0 ){
            Bingo_Log::warning("no op_info op_userid: $intOpUid ,op_uname:$strOpUname, op_type:$intOpType");
            return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "缺少操作人员信息");
        }
        $intUserIp = Bingo_Http_Ip::newip2long(Bingo_Http_Ip::getConnectIp());
        $arrThreadsData = Bingo_Http_Request::getNoXssSafe('data');
        if (empty($arrThreadsData)) {
            Bingo_Log::warning("no data");
            return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "缺少必要参数");
        }

        $date = $arrThreadsData[0]['now_time'];
        if(empty($date)){
            $date=time();
        }

        $date = strtotime(date("Ymd", $date));

        $arrInput = array(
            'post_ids'  => array(),
            'mask_info' => array(),
            'op_uid'    => $intOpUid,
            'op_uname'  => $strOpUname,
            'op_ip'     => $intUserIp,
            'op_type'   => 1, // 1 通过(非恢复的通过)
            'status'    => '1', // 1 通过
            'date'      => $date,
        );

        $arrForbidUids = array();

        foreach ($arrThreadsData as $key => $arrItem) {
            //正在裁剪黑边

            // addThread
            if (self::OP_TYPE_PASS ==  $intOpType && ($arrItem['video_status'] == 7 || $arrItem['video_status'] == 111 || $arrItem['extract_state'] == 99)) {
                // 过滤黑边检测中/视频确认损坏的视频
                continue;
            }
            // deleteThread
            if (self::OP_TYPE_DELETE ==  $intOpType && (empty($arrItem['delete_reason']) && empty($strDeleteReason))) {
                continue;
            }
            
            $arrInput['threads_info'][] = array(
                'video_audit_id' => $arrItem['video_audit_id'],
                'post_id'        => $arrItem['post_id'],
                'thread_id'      => $arrItem['thread_id'],
                'weekkey'        => $arrItem['weekkey'],
                'forum_id'       => $arrItem['forum_id'],

                //支持多吧发帖 start
                'v_forum_ids'    => $arrItem['v_forum_ids'],
                'is_multi_forum' => $arrItem['is_multi_forum'],
                //支持多吧发帖 end

                'forum_name'     => $arrItem['forum_name'],
                'user_id'        => $arrItem['user_id'],
                'user_name'      => $arrItem['user_name'],
                'user_ip'        => $arrItem['user_ip'],
                'title'          => $arrItem['title'],
                'title_base64'   => $arrItem['title_base64'],
                'create_time'    => $arrItem['create_time'],
                'pre_status'     => $arrItem['audit_status'],
                'video_url'      => $arrItem['video_url'],
                'delete_reason'  => (!empty($arrItem['delete_reason'])) ? $arrItem['delete_reason'] : $strDeleteReason,
                'video_log_id'   => intval($arrItem['video_log_id']),
                'op_uid'         => $intOpUid,
                'op_uname'       => $strOpUname,
                'op_ip'          => $intUserIp,
                'op_type'        => $intOpType,
                'audit_flag'     => $intAuditFlag,
                //'status'   => $arrItem['status'], // 1 通过
                'push_to_recom'  => $intPushToRecom, // 1 通过
                'date'           => $date,
                'call_from'      => 'xiaoying_mis',
            );

            if ($intOpType == self::OP_TYPE_DELETE && $arrItem['video_status'] != 111 && $arrItem['extract_state'] != 99) {
                // 20170914 如果是由于视频格式错误引起的审核不通过，很可能并不是用户的问题
                $arrForbidUids[$arrItem['user_id']] = $arrItem['user_name'];
            }

            //大搜旅游合作视频审核过后直接写redis
            if ($intAuditFlag == 1 && ($arrThreadsData[0]['video_type'] == Molib_Util_Video::TEMP_VIDEO_FROM_ES_DY)) {
                $arrValue = array(
                    'thread_id' => $arrItem['thread_id'],
                    'create_time' => time(),
                );
                $arrRedisInput = array(
                    'key' => 'douyin_video_from_es_to_cache',   //redis key
                    'value' => json_encode($arrValue),
                );
                $arrRedisOutput = Lib_Redis::call('rpush', $arrRedisInput, 'ala');

                if ($arrRedisOutput == false) {
                    Bingo_log::warning('call ala redis failed! $arrRedisInput=' . serialize($arrRedisInput));
                    continue;
                }
            }
        }
        $arrOutput = self::newAudit($arrInput);

//        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
//            Bingo_Log::warning('amis  approveThread~,input:['.serialize($arrInput).'].output:['.serialize($arrOutput).']');
//            return self::_jsonRet($arrOutput['errno'], $arrOutput['errmsg']);
//        }

        if ($intOpType == self::OP_TYPE_DELETE && !empty($arrForbidUids)) {
            // 原有逻辑未改动 同一个用户的贴子24小时内被视频mis审核删贴超过三次就封禁
            $arrInput = array(
                'user_ids' => array_keys($arrForbidUids),
            );
            $arrRet = Service_Xiaoying_Xiaoying::checkForbidWhiteListUsers($arrInput);
            if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) { // 20170914 改为宽松策略，即获取白名单失败不进行封禁处理
                Bingo_Log::warning("checkForbidWhiteListUsers fail, input=".json_encode($arrInput).',ret='.json_encode($arrRet));
            } else {
                $arrWhiteListUsers = $arrRet['output'];
                foreach ($arrForbidUids as $intUserId => $strUsername) {
                    if(isset($arrWhiteListUsers[$intUserId]) && $arrWhiteListUsers[$intUserId]) {
                        continue;
                    }
                    $arrParam = array(
                        'op_uid'      => $intOpUid,
                        'op_uname'    => $strOpUname,
                        'op_ip'       => $intUserIp,
                        'user_id'     => $intUserId,
                        'forbid_days' => 7,
                        'deal_reason' => "视频贴作弊超过3次",
                    );
                    $arrRet = Service_Xiaoying_Xiaoying::isForbidUser($arrParam);
                    if(false === $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
                        Bingo_Log::warning(sprintf("amis isForbidUser~, input:[%s]. output:[%s]", serialize($arrParam), serialize($arrRet)));
                    }
                }
            }
        }

        return self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, 'success');
    }

    /**
     * [newAudit description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function newAuditSerial($arrInput){
        $arrThreadInfosNoKey = $arrInput['threads_info'];
        $intErrno = Tieba_Errcode::ERR_SUCCESS;
        foreach ($arrThreadInfosNoKey as $arrThreadInfo) {
            $arrInput = $arrThreadInfo;
            $strService = 'video';
            $strMethod  = 'amisThreadAudit';
            $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php','utf-8');
            $strLog = sprintf(' amis  approveThread~, input:[%s].output:[%s]',serialize($arrInput), serialize($arrOutput));
            if(false === $arrOutput){
                Bingo_Log::fatal($strLog);
                $intErrno = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
                continue;
            }else if(Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                Bingo_Log::warning($strLog);
                $intErrno = $arrOutput['errno'];
                continue;
            }
        }
        return array('errno' => $intErrno);
    }
    /**
     * [newAudit description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function newAudit($arrInput){
        $arrThreadInfosNoKey = $arrInput['threads_info'];
        $arrErrnos  = array();
        $arrErrmsgs = array();
        $strService = 'video';
        $strMethod  = 'amisThreadAudit';
        $objRalMulti = new Tieba_Multi('video_amis_audit');
        foreach ($arrThreadInfosNoKey as $k =>  $arrThreadInfo) {
            $arrInput = $arrThreadInfo;
            
            $arrMultiInput = array(
                'service_name' => $strService,
                'method'       => $strMethod,
                'input'        => $arrInput,
                'ie' => 'utf-8',
            );
            $objRalMulti -> register('amisNewAudit_' . $k, new Tieba_Service($strService), $arrMultiInput);
        }
        $objRalMulti -> call();
        foreach ($arrThreadInfosNoKey as $k =>  $arrThreadInfo) {
            $arrOutput = $objRalMulti -> getResult('amisNewAudit_' . $k);
            $strLog = sprintf(' amis  approveThread~, input:[%s].output:[%s]',serialize($arrInput), serialize($arrOutput));
            if(false === $arrOutput){
                Bingo_Log::fatal($strLog);
                $arrErrnos []= Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
                $arrErrmsgs[]= 'service-failed';//.$arrThreadInfo['video_audit_id'];
                continue;
            }else if(Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                Bingo_Log::warning($strLog);
                $arrErrnos []= $arrOutput['errno'];
                $arrErrmsgs[]= Tieba_Error::getErrmsg($arrOutput['errno']);
                continue;
            }
        }
        return array('errno' => $arrErrnos, 'errmsg'=> $arrErrmsgs);
    }

    /**
     * @brief _jsonRet
     * @param errno,errmsg,data
     * @return:  0.
     **/
    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array()){
        $arrRet = array(
            'no'=>intval($errno),
            'error'=>strval($errmsg),
            'data'=>$arrExtData,
        );
        Bingo_Log::pushNotice("errno",$errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet,Bingo_Encode::ENCODE_UTF8);
        return 0;
    }
}
