<?php

/**
 * Created by PhpStorm.
 * User: yumingrui01
 * Date: 2018/8/23
 * Time: 16:19
 */
class manageClubEditAction extends Util_Action {

    protected $bolNeedLogin = false;
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = true;
    const CLUB_STATUS_REFUSE = 3;

    /**
     * @brief ִ�����
     * @param null
     * @return bool
     * */
    public function _execute() {
        $arrOutput = array();
        //���ò���
        $intClubId        = intval(Bingo_Http_Request::get('club_id'));
        $intClubUserId    = intval(Bingo_Http_Request::get('create_uid'));
        $intClubUserIdNew = intval(Bingo_Http_Request::get('new_create_uid'));
        $strClubName      = strval(Bingo_Http_Request::get('club_name'));
        $strClubIntro     = strval(Bingo_Http_Request::get('club_intro'));
        $strClubChannel   = strval(Bingo_Http_Request::get('club_category'));
        $intClubCover     = intval(Bingo_Http_Request::get('club_cover'));
        $strClubLogo      = strval(Bingo_Http_Request::get('club_logo'));

        $arrInfoParam = array(
            'club_id'       => $intClubId,
            'create_uid'    => $intClubUserId,
            'club_name'     => $strClubName,
            'club_category' => $strClubChannel,
            'club_intro'    => $strClubIntro,
            'club_logo'     => $strClubLogo,
        );
        //�������
        if (empty($intClubUserId) || empty($strClubName) || empty($strClubIntro) || empty($strClubChannel) || empty($strClubLogo)) {
            Bingo_Log::warning(sprintf("param error"));

            return $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, '�����Ƿ�', $arrInfoParam);
        }

        if (mb_strlen($strClubName, 'utf-8') > 18 || mb_strlen($strClubIntro, 'utf-8') > 30) {
            Bingo_Log::warning(sprintf("param error. the length of club name or intro is invalid. [name = %s][intro = %s]", $strClubName, $strClubIntro));

            return $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, '�������ƻ����ż�����', $arrInfoParam);
        }

        // ���޸ĵ�����
        if(isset($intClubUserIdNew) && $intClubUserIdNew >0 && $intClubUserIdNew != $intClubUserId ){
            $arrInfoParam['new_create_uid'] = $intClubUserIdNew;
        }

        //��������Info
        $arrInfoRet = Tieba_Service::call('nani', 'setNaniClubAllInfo', $arrInfoParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrInfoRet['errno']) {
            Bingo_Log::warning(sprintf("call nani:setNaniClubAllInfo failed. [input = %s][output = %s]", serialize($arrInfoParam), serialize($arrInfoRet)));

            return $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, '�޸���Ϣʧ�ܣ�������', $arrInfoRet);
        }
        //�������ŷ���
        //�������
        if (empty($intClubCover)) {
            $arrOutput = array(
                'data' => array(
                    'club_id' => $arrInfoRet['data'],
                ),
            );

            return $this->printOut(Tieba_Errcode::ERR_SUCCESS, '�޸���Ϣ�ɹ�', $arrOutput);
        }
        $arrInfoParam = array(
            'club_id'    => $intClubId,
            'attr_name'  => 'cover_thread',
            'attr_value' => $intClubCover,
        );
        $arrRet       = Tieba_Service::call('nani', 'setClubAttr', $arrInfoParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call nani:setNaniClubInfo failed. [input = %s][output = %s]", serialize($arrInfoParam), serialize($arrRet)));

            return $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, '�޸ķ���ʧ�ܣ�������', $arrRet);
        }

        $arrOutput = array(
            'data' => array(
                'club_id' => $arrRet['data'],
            ),
        );

        return $this->printOut(Tieba_Errcode::ERR_SUCCESS, '�޸���Ϣ������ɹ�', $arrOutput);
    }

}