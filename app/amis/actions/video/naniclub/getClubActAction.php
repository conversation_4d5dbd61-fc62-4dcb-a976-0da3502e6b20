<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2018/9/6
 * Time: 4:53 PM
 */

class getClubActAction extends Util_Action
{

    protected $bolNeedLogin = false;
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = true;
    const CLUB_STATUS_REFUSE = 3;

    /**
     * @brief 执行入口
     * @param null
     * @return bool
     * */
    public function _execute()
    {
        $intClubId          = intval(Bingo_Http_Request::get('club_id', ''));
        $intPn              = intval(Bingo_Http_Request::get('pn', 1));
        $intRn              = intval(Bingo_Http_Request::get('rn', 20));

        $arrAllInput = array(
            'club_id'    => $intClubId,
            'pn'         => $intPn,
            'rn'         => $intRn,
            'need_total' => 1,
        );

        if ($intClubId <= 0) {
            Bingo_Log::warning(sprintf("param error. [input = %s]", serialize($arrAllInput)));
            $arrSelectOutput = array();
            return $this->printOut(Tieba_Errcode::ERR_SUCCESS, 'success', $arrSelectOutput);
        }

        $arrRet = Tieba_Service::call('nani', 'getClubActivityList', $arrAllInput, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call nani:getClubActivityList failed. [input = %s][output = %s]", serialize($arrAllInput), serialize($arrRet)));
            return $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return $this->printOut(Tieba_Errcode::ERR_SUCCESS, 'success', $arrRet['data']);
    }

}