<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2018/8/21
 * Time: 7:20 PM
 */

class setHumanContralSortAction extends Util_Action
{

    protected $bolNeedLogin = false;
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = true;

    /**
     * @return mixed
     */
    public function _execute()
    {

        $intClubId = intval(Bingo_Http_Request::get('club_id', ''));
        $intType   = intval(Bingo_Http_Request::get('type', ''));

        if ($intClubId <= 0 ) {
            Bingo_Log::warning("param error");
            return $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrParam = array(
            'member'    => $intClubId,
            'type'      => $intType,
        );
        $arrRet = Tieba_Service::call('nani', 'humanControlSort', $arrParam, null, null, 'post', 'php', 'utf-8');

        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning(' call nani.humanControlSort service failed. input: [' . serialize($arrParam) . ']; output: [' . serialize($arrRet) . ']');
            return $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }



        return $this->printOut(Tieba_Errcode::ERR_SUCCESS);
    }


}
