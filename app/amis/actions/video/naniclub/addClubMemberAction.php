<?php
/**
 * Created by PhpStorm.
 * User: franklin
 * Date: 2018/8/22
 * Time: 7:47 PM
 */

class addClubMemberAction extends Util_Action
{

    protected $bolNeedLogin = false;
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = true;

    /**
     * @brief 执行入口
     * @return mixed
     * */
    public function _execute()
    {

        $intUserId = intval(Bingo_Http_Request::get('user_id', ''));
        $intClubId = intval(Bingo_Http_Request::get('club_id', ''));
        if ($intClubId <= 0 || $intUserId <= 0) {
            Bingo_Log::warning("param error");
            return $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrParam = array(
            'club_id' => $intClubId,
            'user_id' => $intUserId,
        );
        $arrRet   = Tieba_Service::call('nani', 'addGroupMember', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call nani.addGroupMember service failed. input: [' . serialize($arrParam) . ']; output: [' . serialize($arrRet) . ']');
            return $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return $this->printOut(Tieba_Errcode::ERR_SUCCESS);
    }
}
