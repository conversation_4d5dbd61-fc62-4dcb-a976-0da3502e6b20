<?php

/*
 * @brief auti the club
 * @user lipanpan02
 * @date:2018-08-07
 * */

class auditClubInfoModifyAction extends Util_Action {

    protected $bolNeedLogin         = false;
    protected $bolOnlyAccessAmis    = false;
    protected $bolOnlyInner         = true;
    const CLUB_INFO_STATUS_PASS     = 2;
    const CLUB_INFO_STATUS_REFUSE   = 3;
    const CLUB_LOGO_REFUSE1         = 1;
    const CLUB_LOGO_REFUSE2         = 2;
    const CLUB_LOGO_REFUSE3         = 3;
    const CLUB_INTRO_REFUSE1        = 4;
    const CLUB_INTRO_REFUSE2        = 5;
    const CLUB_INTRO_REFUSE3        = 6;
    /**
     * @brief 执行入口
     * @param null
     * @return
     * */
    public function _execute(){

        $intOpUid            = Util_User::$intUserId;
        $strOpUname          = Util_User::$strUserName;
        $intAuditStatus      = intval(Bingo_Http_Request::get('audit_status', ''));
        $intClubId           = intval(Bingo_Http_Request::get('club_id', ''));
        $strClubLogo         = trim((string)Bingo_Http_Request::get('club_logo', ''));
        $intId               = intval(Bingo_Http_Request::get('id', ''));
        $intRefuseReasonType = intval(Bingo_Http_Request::get('refuse_reason', ''));
        $intCreateUid        = intval(Bingo_Http_Request::get('create_uid', ''));
        $strClubName         = strval(Bingo_Http_Request::get('club_name', ''));
        $strClubIntro        = strval(Bingo_Http_Request::get('club_intro', ''));

        $arrInput =array(
            'id'            => $intId,
            'club_id'       => $intClubId,
            'op_uid'        => $intOpUid,
            'op_uname'      => $strOpUname,
            'status'        => $intAuditStatus,
            'club_logo'     => $strClubLogo,
            'club_intro'    => $strClubIntro,
            'refuse_reason' => $intRefuseReasonType,
        );

        if (intval($arrInput['club_id']) <= 0 || intval($arrInput['status']) <= 0
            || intval($arrInput['op_uid']) <= 0 || empty($arrInput['op_uname']) || (empty($arrInput['club_logo']) && empty($arrInput['club_intro']))) {
            Bingo_Log::warning(sprintf("param error. [input = %s]", serialize($arrInput)));
            return $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrRet = Tieba_Service::call('nani','auditClubLogo',$arrInput,null,null,'post','php','utf-8');
        if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call nani.auditClubLogo service failed. input: [' . serialize($arrInput) . ']; output: [' . serialize($arrRet) . ']');
            return $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrPushInput = array(
            'user_id'         => $intCreateUid,   //创建者uid
            'op_uid'          => 3559714525,        // 官方uid
            'msg_type'        => 11,
            'msg_title'       => strval($strClubName),
            'op_time'         => time(),
            'ext2'            => serialize(array(
                'button_type' => 2,
                'button_data' => array(
                    'club_id' => $intClubId,
                )
            )),
            'close_nani_push' => 1,
        );
        if($intAuditStatus === self::CLUB_INFO_STATUS_PASS){
            $arrPushInput['msg_content'] = sprintf("您上传的修改信息已通过审核",strval($strClubName));
            $arrRet = Tieba_Service::call('video', 'pushHudongMsg', $arrPushInput, null, null, 'post', 'php', 'utf-8');
            if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call video::pushHudongMsg service failed. input: [' . serialize($arrPushInput) . ']; output: [' . serialize($arrRet) . ']');
                return $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

        }

        if($intAuditStatus === self::CLUB_INFO_STATUS_REFUSE && ($intRefuseReasonType === self::CLUB_LOGO_REFUSE1 || $intRefuseReasonType === self::CLUB_LOGO_REFUSE2 || $intRefuseReasonType === self::CLUB_LOGO_REFUSE3)){
            $arrPushInput['msg_content'] = sprintf("您好，由于您上传的%s伙星头像不合格，暂时不能通过审核，可能原因如下：
①	头像不清晰美观，不具备传播价值
②	头像中含有个人或商业或其他黄反等不合适内容
③	头像与本吧讨论内容不符或无关
请修改后再次提交申请。",strval($strClubName));
            $arrRet = Tieba_Service::call('video', 'pushHudongMsg', $arrPushInput,null, null, 'post', 'php', 'utf-8');
            if(false !== $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call video::pushHudongMsg service failed. input: [' . serialize($arrPushInput) . ']; output: [' . serialize($arrRet) . ']');
                return $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }

        if($intAuditStatus === self::CLUB_INFO_STATUS_REFUSE && ($intRefuseReasonType === self::CLUB_INTRO_REFUSE1 || $intRefuseReasonType === self::CLUB_INTRO_REFUSE2 || $intRefuseReasonType === self::CLUB_INTRO_REFUSE3)){
            $arrPushInput['msg_content'] = sprintf("您好，由于您上传的%s伙星简介不合格，暂时不能通过审核，可能原因如下：
①	简介不清晰美观，不具备传播价值
②	简介中含有个人或商业或其他黄反等不合适内容
③	简介与本吧讨论内容不符或无关
请修改后再次提交申请。",strval($strClubName));
            $arrRet = Tieba_Service::call('video', 'pushHudongMsg', $arrPushInput,null, null, 'post', 'php', 'utf-8');
            if(false !== $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call video::pushHudongMsg service failed. input: [' . serialize($arrPushInput) . ']; output: [' . serialize($arrRet) . ']');
                return $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }
        return $this->printOut(Tieba_Errcode::ERR_SUCCESS);
    }

}