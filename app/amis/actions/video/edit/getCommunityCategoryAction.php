<?php

class getCommunityCategoryAction extends Util_Action {
    protected $strAmisGroup = 'video';
    protected $strAmisPerm = 'video:edit';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner =false;

    protected $arrOtherParam = array(
        'class_id',
        'sub_class_id',
    );

    /**
     * [_execute description]
     * @return [type] [description]
     */
    public function _execute() {
        $this->getInput();

        $arrData = array();
        $arrClassList = Tieba_Video_CommunityConst::getClassInfo();
        if (isset($this->_input['sub_class_id'])) {
            $arrCommunityTagList = $arrClassList[$this->_input['class_id']]['sub_class_list'][$this->_input['sub_class_id']]['tag_list'];
            foreach ($arrCommunityTagList as $strTag) {
                //$strTag = Bingo_Encode::convert($strTag, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
                $arrData[] = array(
                    'label' =>  $strTag,
                    'value' =>  $strTag,
                );
            }
        } else if (isset($this->_input['class_id'])) {
            $arrSubClassList = $arrClassList[$this->_input['class_id']]['sub_class_list'];
            foreach ($arrSubClassList as $arrSubClassInfo) {
                //$strSubClassName = Bingo_Encode::convert($arrSubClassInfo['sub_class_name'], Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
                $strSubClassName = $arrSubClassInfo['sub_class_name'];
                $arrData[] = array(
                    'label' =>  $strSubClassName,
                    'value' =>  $arrSubClassInfo['sub_class_id'],
                );
            }
        }
        $arrError = $this->_error(Tieba_Errcode::ERR_SUCCESS, 'success');
        $this->printOut($arrError['errno'], $arrError['errmsg'], $arrData);
        return true;
    }

    private function _error($intErrno, $strErrmsg) {
        $arrError = array(
            'errno'     =>  $intErrno,
            'errmsg'    =>  $strErrmsg,
        );
        return $arrError;
    }

}
?>
