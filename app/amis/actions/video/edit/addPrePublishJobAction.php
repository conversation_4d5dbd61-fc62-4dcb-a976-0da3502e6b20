<?php
/**
 * User: huangling02
 * Date: 17/6/7
 * Time: 下午2:32
 */

class addPrePublishJobAction extends Util_Action {

    protected $strAmisGroup = 'video';
    protected $strAmisPerm = 'video:search';
    protected $bolOnlyAccessAmis = false;

    private static $_arrStatusMap = array(
        'online'  => 1,
        'offline' => 2,
    );
    private static $_arrTimeTypes = array(
        'thread_create_time',
        'create_time',
        'edit_finish_time',
        'pre_sync_time',
    );
    private static $_arrSearchTypes = array(
        'thread_id' => array('type' => 'int', ), 
        'user_name' => array('type' => 'string', 'convert' => 'getFidByFname'),
        'op_uname'  => array('type' => 'string',),
    );
    private static $_arrParamsRequired = array(
        'pre_publish_cnt'   => array('type' => 'int', 'default' => -1, ),
        'pre_publish_time'  => array('type' => 'int', 'default' => -1, ),
    );
    private static $_arrParamsOptional = array(
        'create_time_from'       => array('type' => 'int', 'default' => 0, ),
        'create_time_to'         => array('type' => 'int', 'default' => 0, ),
        //'thread_id'         => array('type' => 'int', 'default' => 0, ),
        //'tbTitle'             => array('type' => 'int', 'default' => 0, ),
        'thread_create_time_from'       => array('type' => 'int', 'default' => 0, ),
        'thread_create_time_to'         => array('type' => 'int', 'default' => 0, ),
        'thread_create_time_sort_type'  => array('type' => 'int', 'default' => 0, ),
        //'page_flag'         => array('type' => 'string', 'default' => array(), ),
        'user_id'                => array('type' => 'int'),
        'bjh_user_name'          => array('type' => 'string'),
        'edit_status'            => array('type' => 'int'),
        'edit_result'            => array('type' => 'int'),
        'dispatch_status'        => array('type' => 'int'),
        'video_from'             => array('type' => 'string'),
        'video_site_id'          => array('type' => 'int'),
        'video_type'             => array('type' => 'int', 'default' => 0, 'convert' => 'transVideoType'),
        'fir_category'           => array('type' => 'string'),
        'sec_category'           => array('type' => 'string'),
        'video_quality'          => array('type' => 'int'),
        'is_video_square'        => array('type' => 'int'),
        'video_duration_from'    => array('type' => 'int'),
        'video_duration_to'      => array('type' => 'int'),
        'tag'                    => array('type' => 'string'),
        'level_1_name'           => array('type' => 'string'),
        'level_2_name'           => array('type' => 'int'),
        'op_uname'               => array('type' => 'string' ),
        'video_score_from'       => array('type' => 'int'),
        'video_score_to'         => array('type' => 'int'),
        'edit_finish_time_from'  => array('type' => 'int'),
        'edit_finish_time_to'    => array('type' => 'int'),
        'reject_field'           => array('type' => 'string'),
        'reject_reason'          => array('type' => 'string'),
        'pre_sync_time_from'     => array('type' => 'int'),
        'pre_sync_time_to'       => array('type' => 'int'),
        'is_download'            => array('type' => 'int'),
        'is_page_up'             => array('type' => 'int'),
        'size'                   => array('type' => 'int'),
    );
    private $_arrParams = array();
    /**
     * @param
     * @return [type] [description]
     */
    private function _initParam(){
        $this->_arrParams = array();
        // parse required fields
        foreach(self::$_arrParamsRequired as $k => $v){
            $this->_arrParams[$k] = Bingo_Http_Request::getNoXssSafe($k, $v['default']);
            if($this->_arrParams[$k]<=0){
                Bingo_Log::warning('no field:'.$k);
                return false;
            }
            switch($v['type']){
                case 'int':
                    $this->_arrParams[$k] = (int)$this->_arrParams[$k];
                    break;
                case 'string':
                    //$arrParams[$k] = (int)$arrParams[$k];
                    break;
                case 'stringGBK':
                    $this->_arrParams[$k] = Bingo_Encode::convert($this->_arrParams[$k], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
                    break;
            }

            if(isset($v['convert'])){
                $ret = call_user_method($v['convert'], $this, $arrParams[$k]);
            }
        }
        // parse optional fields
        foreach(self::$_arrParamsOptional as $k => $v){
            $tempParam = Bingo_Http_Request::getNoXssSafe($k, -1);
            if($tempParam > 0){
                $this->_arrParams[$k] = $tempParam;
                switch($v['type']){
                    case 'int':
                        $this->_arrParams[$k] = (int)$this->_arrParams[$k];
                        break;
                    case 'string':
                        //$arrParams[$k] = (int)$arrParams[$k];
                        break;
                    case 'stringGBK':
                        $this->_arrParams[$k] = Bingo_Encode::convert($this->_arrParams[$k], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
                        break;
                }
            }
        }
        // end: parse optional 

        if(!$this->_checkTimes()){
            return false;
        }

        return true;
    }
    /**
     * @param
     * @return [type] [description]
     */
    private function _checkTimes(){
        
        $existFlag = 0;
        foreach(self::$_arrTimeTypes as $k => $field){
            // Bingo_Log::warning(print_r($field,1));
            // Bingo_Log::warning(print_r($this->_arrParams[$field . '_from'],1));
            // Bingo_Log::warning(print_r($this->_arrParams[$field . '_to'],1));
            if (!empty($this->_arrParams[$field . '_from']) && !empty($this->_arrParams[$field . '_to'] )) {
                $existFlag = 1;
                break;
            }
        }
        if(!$existFlag){
            Bingo_Log::warning("time span not choose");
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, '请至少选择一个时间范围');
            return false;
        }
        foreach(self::$_arrTimeTypes as $k => $field){
            if(isset($this->_arrParams[$field.'_from'])&&isset($this->_arrParams[$field.'_from'])){
                if ($this->_arrParams[$field.'_from'] > $this->_arrParams[$field.'_to']){
                    Bingo_Log::warning("time span not true");
                    $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, '选择的时间范围不对');
                    return false;
                }
                $intMonDist = (date("Y", $this->_arrParams[$field.'_to']) - date("Y", $this->_arrParams[$field.'_from'])) * 12 + (date("m", $this->_arrParams[$field.'_to']) - date("m", $this->_arrParams[$field.'_from']));
                if ($intMonDist > 2) {
                    Bingo_Log::warning("time span not true, more than three month");
                    $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, '选择的时间范围不对,跨度超过三个月份');
                    return false;
                }
            }
        }

        
        return true;
    }

    /**
     * @param
     * @return [type] [description]
     */
    private function transVideoType(){
        $strVideoFrom = $this->_arrParams['video_type'];
        $arrVideoFrom = array();
        if('pgc' == $strVideoFrom) {   //PGC机筛：10 PGC策略：20 UGC策略：30 UGC机筛：40
            $arrVideoFrom = array(10,20);
        }
        else if('ugc' == $strVideoFrom){
            $arrVideoFrom = array(30,40);
        }
        $this->_arrParams['video_type'] = $arrVideoFrom;
        return true;
    } 

    /**
     * @param
     * @return [type] [description]
     */
    public function _execute(){
        if(!$this->_initParam()){
            Bingo_Log::warning('param error');
            return false;
        }
        $arrInput['op_uname'] = Util_User::$strUserName;
        $arrInput['op_time'] = time();
        $arrInput['exp_exe_cnt'] = $this->_arrParams['pre_publish_cnt'];
        $arrInput['exp_exe_time'] = $this->_arrParams['pre_publish_time'];
        unset($this->_arrParams['pre_publish_cnt']);
        unset($this->_arrParams['pre_publish_time']);
        $arrInput['conds'] = $this->_arrParams;
        $arrInput['conds']['dispatch_status'] = 2;
        $arrInput['need_total_count'] = 1;
        $arrInput['need_data'] = 1;
        $strService = 'video';
        $strMethod = 'addVideoEditJob';
        $arrOut = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
        if(false === $arrOut || !isset($arrOut['errno']) || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']){
            Bingo_Log::warning("call  $strService::$strMethod fail. input:[".serialize($arrInput)."] out:[".serialize($arrOut)."]");
            return $this->_jsonRet(isset($arrOut['errno']) ? $arrOut['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrRetData = $arrOut['data'];
        return  $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrRetData);
    
    }
    
   

    /**
     * @param int $errno
     * @param string $errmsg
     * @param array $data
     */
    private function _jsonRet($errno = 0, $errmsg = '' , $data = array()) {
        $this->printOut($errno, $errmsg, $data);
    }


}
