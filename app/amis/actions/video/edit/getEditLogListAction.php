<?php

/**
 * 获取某一视频的操作日志
 * User: 
 * Date: 2017-07-07
 */
class getEditLogListAction extends Util_Action {
    protected $strAmisGroup = 'video';
    protected $strAmisPerm = 'video:edit';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner =false;

    /**
     * @param
     * @return
     */
    public function _execute() {
        $op_uid = Util_User::$intUserId;
        $op_uname = Util_User::$strUserName;
        $thread_id = Bingo_Http_Request::get("thread_id", 0);
        Bingo_Log::pushNotice("op_uid", $op_uid);
        Bingo_Log::pushNotice("op_uname", $op_uname);
        Bingo_Log::pushNotice("thread_id", $thread_id);
        $arrServiceInput = array(
            "thread_id" => $thread_id,
        );
        $strServiceName = "video";
        $strServiceMethod = "getEditLogList";
        $arrOutputFromDB = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrOutputFromDB || Tieba_Errcode::ERR_SUCCESS != $arrOutputFromDB["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "call $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutputFromDB) . "]";
            Bingo_Log::warning($strLog);
            $this->printOut($arrOutputFromDB["errno"],$arrOutputFromDB["errmsg"], array());
            return;
        }
        
        $this->printOut(Tieba_Errcode::ERR_SUCCESS, "", $arrOutputFromDB["data"]);
    }

}