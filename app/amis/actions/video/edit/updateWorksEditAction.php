<?php

/**
 * 编辑审核人员在页面上编辑视频、审核或拒绝并且保持
 * Created by PhpStorm.
 * User: shi<PERSON><PERSON>
 * Date: 6/2 2017
 * Time: 13:59
 * 重构 by <PERSON><PERSON><PERSON><PERSON><PERSON>@baidu.com
 */

define ( 'ROOT_PATH', dirname ( __FILE__ ) . '/../../../../..' );
require_once (ROOT_PATH.'/app/video/lib/Redis.php');

class updateWorksEditAction extends Util_Action {

    protected $strAmisGroup = 'video';
    protected $strAmisPerm = 'video:edit';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner =false;
    protected $arrStrRequired = array("opaction", "thread_id");

    private $_strEditType = '';

    const NANI_VIDEO_THREAD_REDIS_KEY = 'nani_video_thread_redis_queue_es';  //新清晰度打分
    const NO_NEED_TO_EDIT_TEMPORARILY_IN_REDIS_KEY = 'not_need_to_edit_temporarily_in_'; //为了节约成本暂时限制每天编辑的video的数量
    const NO_NEED_TO_EDIT_TEMPORARILY_REDIS_KEY    = 'not_need_to_edit_temporarily_'; //为了节约成本暂时限制每天编辑的video的数量
    const EDIT_TYPE_CLASSIFY = 'classify';
    const EDIT_TYPE_QUALITY = 'quality';
    const EDIT_TYPE_TAG = 'tag';
    const IS_VIDEO_GOOD_TRUE = 1;
    const IS_VIDEO_GOOD_FALSE = 2;
    const VIDEO_TYPE_WORKS = 20;

    /**
     * [_execute description]
     * @return [type] [description]
     */
    public function _execute() {
        $this->_strEditType = Bingo_Http_Request::get('edit_type', '');
        $opAction = Bingo_Http_Request::get("opaction", "");
        $op_uid = Util_User::$intUserId;
        $op_uname = Util_User::$strUserName;

        if(empty($op_uname)){
            $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, "缺少必要参数");
            Bingo_Log::warning("no op_uname Param");
            return;
        }

        $intThreadCreateTime = Bingo_Http_Request::get("thread_create_time", 0);
        $threadId = Bingo_Http_Request::get("thread_id", 0);
        if(  0 ==$intThreadCreateTime  || 0 ==$threadId){
            $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, "缺少必要参数,请联系管理员或检查页面");
            Bingo_Log::warning("request Param: $threadId :　$opAction : $intThreadCreateTime from $op_uname");
            return;
        }
        
        $intIsVideoGood = Bingo_Http_Request::get("is_video_good", 0);
        if(empty($intIsVideoGood)){
            Bingo_Log::warning('param error. [is_video_good: '.$intIsVideoGood.']');
            return $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR,"缺少必要参数:是否符合精选");
        }
        $arrIsVideoGood = array(
            self::IS_VIDEO_GOOD_TRUE,
            self::IS_VIDEO_GOOD_FALSE,
        );
        if(!in_array($intIsVideoGood, $arrIsVideoGood)){
            Bingo_Log::warning('param error. [is_video_good: '.$intIsVideoGood.']');
            return $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR,"参数错误");
        }
        $bolIs2nd = Bingo_Http_Request::get("is_2nd",0);
        $intVideoScore = Bingo_Http_Request::get("video_score", 0);
        $intVideoType = Bingo_Http_Request::get("video_type", 0);
        $strVideoQuality = Bingo_Http_Request::get("video_quality", '');
        $firCate = Bingo_Http_Request::get("fir_category", '');
        $secCate = Bingo_Http_Request::get("sec_category", '');
        $oriTag = Bingo_Http_Request::get("tag", '');
        $chooseTag = Bingo_Http_Request::get("choose_tag", '');
        $titleQuality = Bingo_Http_Request::get("title_quality", '');
        $coverQuality = Bingo_Http_Request::get("cover_quality", '');
        $intWatermarkType = (int) Bingo_Http_Request::get('video_watermark_type', 0); // 水印类型：1无水印，2抖音水印，3其它平台水印
        $intCutHead =  Bingo_Http_Request::get('ori_cut_head',0); //前切片秒数
        $intCutTail =  Bingo_Http_Request::get('ori_cut_tail',0); //后切片秒数
        $strCutType =  Bingo_Http_Request::get('ori_cut',''); //切片类型： 1无切片 2前切片 3后切片
        $arrCutType   = explode(',',strval(Bingo_Http_Request::get('ori_cut','')));  
        $intRealtimeType = Bingo_Http_Request::get("realtime_type", 0); //时效性类型 见Molib_Util_Video_Edit::$arrRealtimeType
        
        $oriTagUpddate = !empty($oriTag) ? $oriTag.',' : '';
        $tag = empty($chooseTag) ? $oriTag : $oriTagUpddate.$chooseTag;
        $needMark = 0; 
       $editStatus = 4; // 应该都是初编
        // $editStatus = 14; // 已打新标签 待打旧标签 
        $editResult = 1; // 初始值默认编辑审核通过

        //视频不符合符合精选：检查内容质量，低质原因
        $bolIsVideoGood = false;                    
        if($intIsVideoGood == self::IS_VIDEO_GOOD_FALSE){
            if(empty($intVideoScore)  || empty($strVideoQuality) || $intVideoScore>1){
                Bingo_Log::warning('param error. [video_score: '.$intVideoScore.' video_quality:'.$strVideoQuality.']');
                return $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR,"参数错误,请检查选项是否正确");
            }
            $editStatus = 4; // 应该都是初编
            $editResult = 2; //拒绝
             
        }else{
            $bolIsVideoGood = true;
            if($intVideoType != self::VIDEO_TYPE_WORKS){
                //视频符合符合精选：检查参数
                if(empty($intVideoScore) || $intVideoScore<=1  ||
                 empty($firCate) || empty($secCate) || empty($tag) || empty($intRealtimeType) )
                 {
                    Bingo_Log::warning('param error. [video_score: '.$intVideoScore.' video_watermark_type:'.$intWatermarkType.'ori_cut:'.$strCutType.' realtime_type:'.$intRealtimeType.' fir_category:'.$firCate.' sec_category:'.$secCate.' tag:'.$tag. ']');
                    return $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR,"参数错误,请检查选项是否正确");  
                 }
                if($bolIs2nd != 1 &&( empty($strCutType) || empty($intWatermarkType) )){
                    Bingo_Log::warning('param error. [ video_watermark_type:'.$intWatermarkType.'ori_cut:'.$strCutType);
                    return $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR,"参数错误,请检查选项是否正确");  
                }
                $cutTypeNum = count($arrCutType);
                if(!empty($arrCutType) && in_array(1, $arrCutType) && $cutTypeNum>1 ){
                    Bingo_Log::warning('input params error');
                    return $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, '请选择正确的切片类型');
                }
                
            }else{
                //作品符合符合精选：检查参数
                if(empty($intVideoScore) || $intVideoScore<=1  || 
                 empty($coverQuality)  ||
                 empty($firCate) || empty($secCate) || empty($tag) || empty($intRealtimeType) )
                 {
                    Bingo_Log::warning('param error. [video_score: '.$intVideoScore.' video_quality:'.$strVideoQuality.' ori_title_quality:'.$titleQuality.' cover_quality:'.$coverQuality.' realtime_type:'.$intRealtimeType.' fir_category:'.$firCate.' sec_category:'.$secCate.' tag:'.$tag. ']');
                    return $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR,"参数错误,请检查选项是否正确");  
                 }
                if($bolIs2nd != 1 &&( empty($strCutType) || empty($intWatermarkType)  || empty($titleQuality))){
                    Bingo_Log::warning('param error. [ video_watermark_type:'.$intWatermarkType.'ori_cut:'.$strCutType);
                    return $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR,"参数错误,请检查选项是否正确");  
                }
                $cutTypeNum = count($arrCutType);
                if(!empty($arrCutType) && in_array(1, $arrCutType) && $cutTypeNum>1 ){
                    Bingo_Log::warning('input params error');
                    return $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, '请选择正确的切片类型');
                }
            }
            
            //去水印or切片 则need_mark=1
            if($bolIs2nd !=1 && ($intWatermarkType !=1 || !in_array(1, $arrCutType))){
                $needMark = 1;
            }
            
        }

        if($bolIs2nd == 1){  //复编 不更改水印和切片
            $editStatus       = 5;
            $intWatermarkType = 0;
            $strCutType       = 0;
        }

        $arrServiceInput = array(
            "thread_create_time" => $intThreadCreateTime,
            "for_edit" => false,
            "tids" => array($threadId),
            "op_uname" => $op_uname,
            "op_uid" => $op_uid,
        );
        $strServiceName = "video";
        $strServiceMethod = "getVideoEditListByIds";
        $arrOutputFromDB = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrOutputFromDB || Tieba_Errcode::ERR_SUCCESS != $arrOutputFromDB["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "call $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutputFromDB) . "]";
            Bingo_Log::fatal($strLog);
            $this->printOut($arrOutputFromDB["errno"], $arrOutputFromDB["errmsg"]);
            return;
        }
        if (empty($arrOutputFromDB["data"])) {
            Bingo_Log::warning("No thread for $threadId in $intThreadCreateTime");
            $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, "No thread for $threadId in $intThreadCreateTime");
            return;
        }
        $oldVideoEdit = $arrOutputFromDB["data"][0];
   
        //作品 重复提交拒绝时提示错误信息
        if($intVideoType == self::VIDEO_TYPE_WORKS && $oldVideoEdit['edit_result'] == 2 && $intIsVideoGood == self::IS_VIDEO_GOOD_FALSE ){
            $this->printOut(2, '不能重复拒绝~');
            return;
        }

        if('approvedBjh' == $opAction){
            //video_type为111时(知识视频)，不能提交百家号
            if(111 == intval($oldVideoEdit['video_type'])){
                $this->printOut(2, '知识视频不能提交百家号');
                return;
            }
        }

        Bingo_Log::pushNotice('opaction', $opAction);
        Bingo_Log::pushNotice('thread_id', $oldVideoEdit["thread_id"]);
        Bingo_Log::pushNotice('op_uname', $op_uname);
        $strServiceName = "video";
        
        if("approvedBjh" != $opAction && "approvedTb" == $opAction && "reject" == $opAction){
            $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR);
            return;
        }

        $ori_op_uid = 0;
        $ori_edit_result = 0;
        $ori_dispatch_path = 0;
        $ori_dispath_status = 0;
        if( $oldVideoEdit["edit_result"] == 2 || $oldVideoEdit["edit_result"] == 1 ){
            //查询是否是复编
            $arrParams = array(
                'thread_id' => $threadId,
            );
            $arrRet = Tieba_Service::call('video', 'getEditLogList', $arrParams, null, null, 'post', 'php', 'utf-8');
            if( Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                Bingo_Log::warning(__FUNCTION__.' service video.getEditLogList error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, '操作失败请重试');
                return;
            }

            $editStatus = empty($arrRet['data']) ? $editStatus : 5;

        }


        $dispatchStatus = 1;
//        if("approvedBjh" == $opAction){
//            $dispatchStatus = 2; //确认编辑，并且内容可同步百家号，待提交预发表池
//        }else if("approvedTb" == $opAction){
//            $dispatchStatus = 1; //确认编辑，并且内容不同步百家号
//        }else if("rejectClassify" == $opAction) {
//            $editResult = 3;    // 分类错误
//            $editStatus = 2;    // 返回待分类状态
//        }else{ // 拒绝
//            $editResult = 2;
//        }

        if($op_uname != $oldVideoEdit["op_uname"]){
            Bingo_Log::warning("EditorErr: $threadId: $op_uname but ori is:".$oldVideoEdit["op_uname"]);
        }
        
        //上传的封面
        $strVideoCoverUrl = $this->get("video_cover", $oldVideoEdit['video_cover']);
        $strUploadVideoCover = Bingo_Http_Request::get("upload_video_cover", "");
        if(!empty($strUploadVideoCover) && filter_var($strUploadVideoCover, FILTER_VALIDATE_URL)){
            $strVideoCoverUrl = $strUploadVideoCover;
        }

        $arrServiceInput = array(
            'op_time' => time(),
            'op_uname' => $op_uname,
            'op_uid' => $op_uid,
            'thread_id'=> $threadId,
            'thread_create_time' => $intThreadCreateTime,
            'title' => $bolIsVideoGood ? $this->get('title', $oldVideoEdit['title']) : $oldVideoEdit['title'],
            'description' => $bolIsVideoGood ? $this->get('description', $oldVideoEdit['description']) : $oldVideoEdit['description'],
            'video_cover' => $bolIsVideoGood ? $strVideoCoverUrl : $oldVideoEdit['video_cover'],
            'video_name' => $bolIsVideoGood ? $this->get('video_name', $oldVideoEdit['video_name']) : $oldVideoEdit['video_name'],
            'dispatch_path' => $bolIsVideoGood ? $this->get('dispatch_path', $oldVideoEdit['dispatch_path']) : $oldVideoEdit['dispatch_path'],
            'dispatch_status' => $dispatchStatus,
            'edit_result' => $editResult,
            'edit_status' => $editStatus,
            'edit_finish_time' => time(),
            'video_from' => $oldVideoEdit['video_from'],
            'video_type' => $oldVideoEdit['video_type'],
            //'publish_time' => $this->get('publish_time', $oldVideoEdit['']),
            //'pre_sync_time' => $this->get('pre_sync_time', $oldVideoEdit['']),
            'video_quality' =>  $this->get('video_quality', $oldVideoEdit['video_quality']) ,
            'cover_quality' => $bolIsVideoGood ? $this->get('cover_quality', $oldVideoEdit['cover_quality']) : $oldVideoEdit['cover_quality'],
            'other_quality' => $bolIsVideoGood ? $this->get('other_quality', $oldVideoEdit['other_quality']) : $oldVideoEdit['other_quality'],
            'title_quality' => $bolIsVideoGood ? $this->get('title_quality', $oldVideoEdit['title_quality']) : $oldVideoEdit['title_quality'],
            'video_score' =>  $this->get('video_score', $oldVideoEdit['video_score']) ,
            'is_video_square' => $bolIsVideoGood ? $this->get('is_video_square', $oldVideoEdit['is_video_square']) : $oldVideoEdit['is_video_square'],
            'is_video_good' => $intIsVideoGood,
            'fir_category' => $bolIsVideoGood ? $this->get('fir_category', $oldVideoEdit['fir_category']) : $oldVideoEdit['fir_category'],
            'sec_category' => $bolIsVideoGood ? $this->get('sec_category', $oldVideoEdit['sec_category']) : $oldVideoEdit['sec_category'],
            'tag' => (empty($tag) || !$bolIsVideoGood)?$oldVideoEdit['tag'] : $tag ,
            'frame_status' => $bolIsVideoGood ? $this->get('frame_status', $oldVideoEdit['frame_status']) : $oldVideoEdit['frame_status'],
            'bjh_user_name' => $bolIsVideoGood ? $this->get('bjh_user_name', $oldVideoEdit['bjh_user_name']) : $oldVideoEdit['bjh_user_name'],
            'reject_field' => 1 == $editResult ? "" : $this->get('reject_field', $oldVideoEdit['reject_field']),
            'reject_reason' => 1 == $editResult ? "" : $this->get('reject_reason', $oldVideoEdit['reject_reason']),
            'title_base64' => $bolIsVideoGood ? $this->get('title_base64', $oldVideoEdit['title_base64']) : $oldVideoEdit['title_base64'],
            'ori_op_uid' => $ori_op_uid,
            'ori_edit_result' => $ori_edit_result,
            'ori_dispatch_path' => $ori_dispatch_path,
            'ori_dispath_status' => $ori_dispath_status,
            'forum_id' => $oldVideoEdit['forum_id'],// by huangling02
            'user_id' => $oldVideoEdit['user_id'],// by huangling02
            'video_watermark_type' => $intWatermarkType, // 水印类型  by kangqinmou
            'video_cut_type' => $strCutType, // 切片类型  
            'video_cut_head' => $intCutHead, // 前切片秒数
            'video_cut_tail' => $intCutTail, // 后切片秒数
            'need_mark' => $needMark,   // 是否需要校验操作人标记过水印 by kangqinmou
            'video_width' => $bolIsVideoGood ? $this->get('video_width', 0) : 0,
            'video_height' => $bolIsVideoGood ? $this->get('video_height', 0) : 0,
            'search_type' => $bolIsVideoGood ? $this->get('search_type', $oldVideoEdit['search_type']) : $oldVideoEdit['search_type'],
            'ext1' => $oldVideoEdit['ext1'],
            'ext2' => $oldVideoEdit['ext2'],
            'class_id' => $bolIsVideoGood ? $this->get('class_id', $oldVideoEdit['class_id']) : $oldVideoEdit['class_id'],
            'sub_class_id' => $bolIsVideoGood ? $this->get('sub_class_id', $oldVideoEdit['sub_class_id']) : $oldVideoEdit['sub_class_id'],
            'realtime_type' => $bolIsVideoGood ? $this->get('realtime_type', $oldVideoEdit['realtime_type']) : $oldVideoEdit['realtime_type'], //时效性类型 见Molib_Util_Video_Edit::$arrRealtimeType
            'sound_quality' => $bolIsVideoGood ? $this->get('sound_quality', $oldVideoEdit['sound_quality']) : $oldVideoEdit['sound_quality'], //音画质量 见Molib_Util_Video_Edit::$arrVideoSoundPicQuality
            'first_publish_time' => $bolIsVideoGood ? $this->get('first_publish_time', $oldVideoEdit['first_publish_time']) : $oldVideoEdit['first_publish_time'], //首发时间
            'vulgar_type' => $bolIsVideoGood ? $this->get('vulgar_type', $oldVideoEdit['vulgar_type']) : $oldVideoEdit['vulgar_type'], //低俗内容类型
        );
//        $strGameCommunityTag = $this->get('game_community_tag', '');
//        if (!empty($strGameCommunityTag)) {
//            $arrServiceInput['tag'] = $arrServiceInput['tag'].','.$strGameCommunityTag;
//        }
        
        $strServiceName = "video";
        $strServiceMethod = 'updateVideoWorksEditByOp'; //分类 打分 打标签 三步合为一步

        $arrOutputFromDB = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', 'php', 'utf-8');
        $intErrno = $arrOutputFromDB ? $arrOutputFromDB["errno"] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
        if (Tieba_Errcode::ERR_SUCCESS != $intErrno) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "call $strServiceMethod fail. input:[" . serialize($arrServiceInput)
                . "]; output:[" . serialize($arrOutputFromDB) . "]";
            Bingo_Log::warning($strLog);
            $this->printOut($intErrno, 1 == $intErrno ? '没有进行去水印操作' : '操作失败请重试');
            return;
        }
        
        Bingo_Log::pushNotice('updatedResult', $intErrno);
        $this->printOut(Tieba_Errcode::ERR_SUCCESS);
    }
}
