<?php
/**
 * Created by PhpStorm.
 * User: caowu
 * Date: 17/6/12
 * Time: 下午2:16
 */

class pushVideoPositionAction extends Util_Action {

    protected $strAmisGroup = 'video';
    protected $strAmisPerm = 'video:edit';
    protected $bolOnlyAccessAmis = false;

    /**
     * @param null
     * @return array
     */
    public function _execute() {

        $intThreadId    = intval(Bingo_Http_Request::get('thread_id',0));
        $intCreateTime  = intval(Bingo_Http_Request::get('thread_create_time',0));
        $intWatermarkWidth  = intval(Bingo_Http_Request::get('watermark_width',0));
        $intWatermarkHeight = intval(Bingo_Http_Request::get('watermark_height',0));
        $arrWatermarkDot    = Bingo_Http_Request::get('watermark_dot',array());

        if ($intThreadId == 0 || $intCreateTime == 0 || $intWatermarkWidth == 0  || $intWatermarkHeight == 0 || empty($arrWatermarkDot) ) {
            Bingo_Log::warning("input params error");
            return $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, '参数错误');
        }

//        $intPositionX = intval($arrWatermarkDot['x']);
//        $intPositionY = intval($arrWatermarkDot['y']);

        $arrWatermarkInput = array(
            'op_uname'              => Util_User::$strUserName,               // 操作人名称
            'op_uid'                => Util_User::$intUserId,                 // 操作人user_id
            'thread_id'             => $intThreadId,
            'thread_create_time'    => $intCreateTime,
            'watermark_width'       => $intWatermarkWidth,
            'watermark_height'      => $intWatermarkHeight,
            'watermark_dot'         => $arrWatermarkDot,
        );

        $strServiceName = "video";
        $strServiceMethod = "pushWatermarkInfoIntoList";
        $arrWatermarkOut = Tieba_Service::call($strServiceName, $strServiceMethod, $arrWatermarkInput, null, null, 'post', 'php', 'utf-8');

        if (false === $arrWatermarkOut || Tieba_Errcode::ERR_SUCCESS != $arrWatermarkOut["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "call $strServiceMethod fail. input:[" . serialize($arrWatermarkInput) . "]; output:[" . serialize($arrWatermarkOut) . "]";
            Bingo_Log::warning($strLog);
            return $this->_jsonRet(isset($arrOut['errno']) ? $arrOut['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);

        }

        return  $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * @param int $errno
     * @param string $errmsg
     * @param array $data
     */
    private function _jsonRet($errno = 0, $errmsg = '' , $data = array()) {
        $this->printOut($errno, $errmsg, $data);
    }

}
