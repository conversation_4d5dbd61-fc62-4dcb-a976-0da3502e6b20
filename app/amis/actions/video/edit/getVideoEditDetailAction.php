<?php

/**
 * Created by PhpStorm.
 * User: shi<PERSON><PERSON>
 * Date: 6/2 2017
 * Time: 13:59
 * http://kqm.imis.tieba.otp.baidu.com/amis/video/edit/getVideoEditDetail?thread_id=5470322104&for_edit=0&thread_create_time=1512701992
 */
class getVideoEditDetailAction extends Util_Action {
    protected $strAmisGroup = 'video';
    protected $strAmisPerm = 'video:edit';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner =false;

    /**
     * [_execute description]
     * @return [type] [description]
     */
    public function _execute() {
        $op_uid = Util_User::$intUserId;
        $op_uname = Util_User::$strUserName;
        $intThreadCreateTime = Bingo_Http_Request::get("thread_create_time", time());
        //$endTime = Bingo_Http_Request::get("end_time", strtotime(date('Y-m-d')) + 86399);
        $threadId = Bingo_Http_Request::get("thread_id", 0);
        $boolForEdit = Bingo_Http_Request::get("for_edit", false);

        // 步骤1：并行请求
        $strWeekKey   = $this->getThatDayMonday($intThreadCreateTime);
        $arrMultiCall = array(
            0 => array(
                'serviceName' => 'video',
                'method'      => 'getVideoEditListByIds',
                'ie'          => 'utf-8',
                'input'       => array(
                    "thread_create_time" => $intThreadCreateTime,
                    "for_edit"           => $boolForEdit,
                    "tids"               => array($threadId),
                    "op_uname"           => $op_uname,
                    "op_uid"             => $op_uid,
                ),
            ),
            1 => array(
                'serviceName' => 'video',
                'method'      => 'getVideoFromAudit',
                'ie'          => 'utf-8',
                'input'       => array(
                    'cond' => array(
                        'thread_id'  => $threadId,
                        'start_time' => $intThreadCreateTime - 86400,
                        'end_time'   => $intThreadCreateTime + 86400
                    ),
                    'weekkey'   => $strWeekKey,
                ),
            )
        );

        $multiCall = new Tieba_Multi('amis-video::getVideoEditDetailAction');
        foreach ($arrMultiCall as $key => $value) {
            $multiCall->register($key, new Tieba_Service($value['serviceName']), $value);
        }
        $multiCall->call();

        // 步骤2：从编辑库中获取视频信息
        $arrOutputFromDB = $multiCall->getResult(0);
        if (false === $arrOutputFromDB || Tieba_Errcode::ERR_SUCCESS != $arrOutputFromDB["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "call getVideoEditListByIds fail. input:[" . serialize($arrMultiCall[0]['input']) . "]; output:[" . serialize($arrOutputFromDB) . "]";
            Bingo_Log::fatal($strLog);
            $this->printOut($arrOutputFromDB["errno"],$arrOutputFromDB["errmsg"], array());
            return;
        }
        if (empty($arrOutputFromDB["data"]) || !isset($arrOutputFromDB["data"][0]["thread_id"])) {
            Bingo_Log::warning("no video for $threadId  in $intThreadCreateTime from user $op_uname ");
            $this->printOut(Tieba_Errcode::ERR_SUCCESS, "没找到视频贴,根据$threadId $intThreadCreateTime", array("thread_id" => 0));
            return;
        }
        $arrOut = $arrOutputFromDB["data"][0];
        $strVideoUrl = strval($arrOut['video_url']);

        // 步骤3：从审核库中获取视频信息
        $arrAuditInfo = $multiCall->getResult(1);
        if(false == $arrAuditInfo || Tieba_Errcode::ERR_SUCCESS != $arrAuditInfo['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' get video audit info failed by calling video getVideoFromAudit service. input: [' . serialize($arrMultiCall[1]['input']) . ']; output: [' . serialize($arrAuditInfo) . ']');
            $this->printOut($arrAuditInfo['errno'], $arrAuditInfo['errmsg'], array());
            return;
        }
        $arrOut['delogo_status'] = isset($arrAuditInfo['data'][0]['delogo_status']) ? intval($arrAuditInfo['data'][0]['delogo_status']) : 0;

        // 步骤4：取视频宽高、去水印之后的url
        $arrThreadInput = array(
            "thread_ids" => array($threadId,),
            "need_abstract" => 0,
            "forum_id" => 0,
            "need_photo_pic" => 0,
            "need_user_data" => 0,
            "icon_size" => 0,
            "need_forum_name" => 0,  //是否获取吧名
            "call_from" => "client_frs", //上游模块名,这里只能用client_frs
        );
        $arrThreadOutput = Tieba_Service::call('post', 'mgetThread', $arrThreadInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrThreadOutput || Tieba_Errcode::ERR_SUCCESS != $arrThreadOutput['errno']) {
            Bingo_Log::warning('call post/mgetThread err! [' . serialize(compact('arrThreadInput', 'arrThreadOutput')) . ']');
        }else{
            $arrData = $arrThreadOutput['output']['thread_list'][$threadId];
            $arrOut['video_width'] = $arrData['video_info']['video_width'];
            $arrOut['video_height'] = $arrData['video_info']['video_height'];
            $arrOut['video_url_without_watermark'] = '';
            $arrOut['video_url_with_watermark'] = '';
            if(!empty($arrData['video_info']['video_desc'])){
                $arrOut['video_url_without_watermark'] = $arrData['video_info']['video_desc'][0]['video_url'];
                $arrOut['video_url_with_watermark'] = $arrOut['video_url'];
                $arrOut['video_url'] = $arrData['video_info']['video_desc'][0]['video_url'];
                if ($arrData['create_time'] < 1517587200 && $arrData['video_info']['video_type'] == Molib_Util_Video::TEMP_VIDEO_UPLOAD_FROM_PC
                    && preg_match('/tieba-smallvideo-transcode/', $strVideoUrl)) {
                    // 较早的PC上传视频video_info中video_width&height和video_url并不对应，2018年1月份的某天修正了这个问题
                    foreach ($arrData['video_info']['video_desc'] as $v) {
                        if ($v['video_id'] != 0) {
                            $arrOut['video_width'] = $v['video_width'];
                            $arrOut['video_height'] = $v['video_height'];
                        }
                    }
                }
            }
        }

        $arrExtParam = json_decode($arrAuditInfo['data'][0]['ext_param'], true);
        $arrOut['abstract'] = $arrExtParam['ui_trans_params']['video_abstract'];
        $arrExtAttr = $arrExtParam['ext_attr'];
        Tieba_Video_Process::getAmisViewVideoUrl($arrOut, 'edit');
        foreach ($arrExtAttr as $arrValue) {
            if ($arrValue['key'] == 'video_info') {
                Tieba_Video_Process::getAmisViewVideoUrl($arrValue['value'], 'edit');
                $arrOut['video_url'] = $arrValue['value']['video_url'];
            }
        }

        // 步骤5：取帧
        $arrFileName    = explode("/", $strVideoUrl);
        $strFileName    = end($arrFileName);
        $strTemp        = explode(".", $strFileName);
        $strVideoName   = $strTemp[0];
        $arrFrame = $this->getFrameList($strVideoName);
        $arrOut['video_frame_list']     = $arrFrame['video_frame_list'];
        $arrOut['video_frame_width']    = $arrFrame['video_frame_width'];
        $arrOut['video_frame_height']   = $arrFrame['video_frame_height'];

        // 步骤6：是否符合游戏社区标准
        if ($arrOut["fir_category"] == '游戏' && $arrOut["video_width"] > $arrOut["video_height"] && $arrOut["video_duration"] < 360) {
            $arrOut["community_video"] = 1;
        }

        // 获取审核平台删除理由
        $extInfo = $arrOut['ext_info'];
        $auditDelMsg = "";
        if ("" != $extInfo) {
            $extInfoArr = json_decode($extInfo, 1);
            if ( !empty($extInfoArr) && isset($extInfoArr['audit_del_msg'])) {
                $auditDelMsg = $extInfoArr['audit_del_msg'];
            }
        }
        $arrOut['audit_del_msg'] = $auditDelMsg;

        // end
        $this->printOut(Tieba_Errcode::ERR_SUCCESS, "", $arrOut);
    }

    /**
     * @param 
     * [_execute description]s
     * @return [type] [description]
     */
    private function getFrameList($videoName) 
    {
        $arrInput = array(
            'video_name' => $videoName,

        );
        $strServiceName = "video";
        $strServiceMethod = "getVideoFrameFromEditMis";
        $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "call $strServiceMethod fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return false;
        }

        $arrOut = array(
            'video_frame_list'      => $arrOutput['data']['video_frame_list'],
            'video_frame_width'     => $arrOutput['data']['video_frame_width'],
            'video_frame_height'    => $arrOutput['data']['video_frame_height'],
        );

        return $arrOut;
    }


    /**
     * @brief 根据日期获取当前表名
     * @param {int} $thatDayStamp 存储数据
     * @return: timestamp.
     **/
    public static function getThatDayMonday($thatDayStamp) {
        $MonDayDateStamp = strtotime("Monday");
        $skipWeek = ceil(($MonDayDateStamp - $thatDayStamp) / 86400 / 7);
        $thatMonday = $MonDayDateStamp - $skipWeek * 7 * 86400;
        return date("Ymd", $thatMonday);
    }

}
?>
