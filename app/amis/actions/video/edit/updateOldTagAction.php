<?php

/**
 * 编辑审核人员在页面上打旧标签
 * Created by PhpStorm.
 * Date: 2021-03-01
 * Time: 13:59
 */

define ( 'ROOT_PATH', dirname ( __FILE__ ) . '/../../../../..' );

class updateOldTagAction extends Util_Action {

    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner =false;

    /**
     * [_execute description]
     * @return [type] [description]
     */
    public function _execute() {
        $opAction = Bingo_Http_Request::get("opaction", "");
        $op_uid = Util_User::$intUserId;
        $op_uname = Util_User::$strUserName;

        if(empty($op_uname)){
            $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, "缺少必要参数");
            Bingo_Log::warning("no op_uname Param");
            return;
        }

        $intThreadCreateTime = Bingo_Http_Request::get("thread_create_time", 0);
        $threadId = Bingo_Http_Request::get("thread_id", 0);
        if(  0 ==$intThreadCreateTime  || 0 ==$threadId){
            $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, "缺少必要参数,请联系管理员或检查页面");
            Bingo_Log::warning("request Param: $threadId :　$opAction : $intThreadCreateTime from $op_uname");
            return;
        }
 
        $firCate = Bingo_Http_Request::get("old_fir_category", '');
        $secCate = Bingo_Http_Request::get("old_sec_category", '');
        $tag = Bingo_Http_Request::get("old_tag", '');
        if(empty($firCate)  || empty($secCate) ||empty($tag)){
            Bingo_Log::warning('param error. [old_fir_category: '.$firCate.' old_sec_category:'.$secCate. ' old_tag:'.$tag. ']');
            return $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR,"参数错误");
        }

        $arrServiceInput = array(
            "thread_create_time" => $intThreadCreateTime,
//            "for_edit" => false,
            "tids" => array($threadId),
            "op_uname" => $op_uname,
            "op_uid" => $op_uid,
        );
        $strServiceName = "video";
        $strServiceMethod = "getVideoEditListByIds";
        $arrOutputFromDB = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrOutputFromDB || Tieba_Errcode::ERR_SUCCESS != $arrOutputFromDB["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "call $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutputFromDB) . "]";
            Bingo_Log::fatal($strLog);
            $this->printOut($arrOutputFromDB["errno"], $arrOutputFromDB["errmsg"]);
            return;
        }
        if (empty($arrOutputFromDB["data"])) {
            Bingo_Log::warning("No thread for $threadId in $intThreadCreateTime");
            $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, "No thread for $threadId in $intThreadCreateTime");
            return;
        }
        $oldVideoEdit = $arrOutputFromDB["data"][0];
        
        if($op_uname != $oldVideoEdit["op_uname"]){
            Bingo_Log::warning("EditorErr: $threadId: $op_uname but ori is:".$oldVideoEdit["op_uname"]);
        }
        $editStatus = 4; // 已打新标签 待打旧标签
        $editResult = 1; // 初始值默认编辑审核通过
        $arrServiceInput = array(
            'op_time' => time(),
            'op_uname' => $op_uname,
            'op_uid' => $op_uid,
            'thread_id'=> $threadId, 
            'thread_create_time' => $intThreadCreateTime,
            'edit_status' => $editStatus,
            'edit_result' => $editResult,
            'fir_category' => $firCate,
            'sec_category' => $secCate,
            'tag' => $tag,
        );
        
        $strServiceName = "video";
        $strServiceMethod = 'updateVideoWorksOldTagByOp'; //打旧标签
        $arrOutputFromDB = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', 'php', 'utf-8');
        $intErrno = $arrOutputFromDB ? $arrOutputFromDB["errno"] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
        if (Tieba_Errcode::ERR_SUCCESS != $intErrno) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "call $strServiceMethod fail. input:[" . serialize($arrServiceInput)
                . "]; output:[" . serialize($arrOutputFromDB) . "]";
            Bingo_Log::warning($strLog);
            $this->printOut($intErrno, 1 == $intErrno ? '操作失败请重试' : '操作失败请重试');
            return;
        }
        
        Bingo_Log::pushNotice('updatedResult', $intErrno);
        $this->printOut(Tieba_Errcode::ERR_SUCCESS);
    }
}
