<?php

/**
 * 编辑审核人员在页面上编辑视频、审核或拒绝并且保持
 * Created by PhpStorm.
 * User: shijiwen
 * Date: 6/2 2017
 * Time: 13:59
 */
class rollbackVideoWatermarkAction extends Util_Action {
    protected $strAmisGroup = 'video';
    protected $strAmisPerm = 'video:edit';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner =false;
    protected $arrStrRequired = array("opaction", "thread_id");

    /**
     * [_execute description]
     * @return [type] [description]
     * http://kqm.imis.tieba.otp.baidu.com/amis/video/edit/rollbackVideoWatermark?thread_create_time=1513172932&thread_id=1513172932&opaction=kangqinmou
     */
    public function _execute() {
        $op_uid = Util_User::$intUserId;
        $op_uname = Util_User::$strUserName;

        $intThreadCreateTime = Bingo_Http_Request::get("thread_create_time", 0);
        $intThreadId = Bingo_Http_Request::get("thread_id", 0);

        if(empty($op_uname) || $intThreadCreateTime == 0 || $intThreadId == 0){
            $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, "缺少必要参数");
            Bingo_Log::warning("no op_uname Param");
            return;
        }

        Bingo_Log::pushNotice("op_uid", $op_uid);
        Bingo_Log::pushNotice("op_uname", $op_uname);
        Bingo_Log::pushNotice("thread_id", $intThreadId);


        $strWeekKey = $this->getThatDayMonday($intThreadCreateTime);
        $arrVideoInput = array(
            'cond' => array(
                'thread_id' => $intThreadId,
                'start_time' => $intThreadCreateTime - 86400,
                'end_time' => $intThreadCreateTime + 86400
            ),
            'weekkey'   => $strWeekKey,
        );

        $strServiceName = "video";
        $strServiceMethod = "getVideoFromAudit";
        $arrVideoOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrVideoInput,null,null,"post",null,"utf-8");
        if(false === $arrVideoOutput || Tieba_Errcode::ERR_SUCCESS != $arrVideoOutput["errno"]){
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "call $strServiceMethod fail. input:[" . serialize($arrVideoInput) . "]; output:[" . serialize($arrVideoOutput) . "]";
            Bingo_Log::warning($strLog);
            return $this->_jsonRet(isset($arrVideoOutput['errno']) ? $arrVideoOutput['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrVideoInfo = $arrVideoOutput['data'][0];

        $intVideoAuditId = $arrVideoInfo['id'];

        if($intVideoAuditId > 0){
            $arrServiceInput = array(
                "weekkey" => $strWeekKey,
                "video_audit_id" => $intVideoAuditId,
            );
            $strServiceName = "video";
            $strServiceMethod = "rollbackVideoDelogo";
            $arrOutputFromDB = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', 'php', 'utf-8');
            if (false === $arrOutputFromDB || Tieba_Errcode::ERR_SUCCESS != $arrOutputFromDB["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . "call $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutputFromDB) . "]";
                Bingo_Log::fatal($strLog);
            }
            Bingo_Log::pushNotice('rollbackVideoDelogo'. $arrOutputFromDB["errno"]);
        }


        $this->printOut(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief 根据日期获取当前表名
     * @param {int} $thatDayStamp 存储数据
     * @return: timestamp.
     **/
    public static function getThatDayMonday($thatDayStamp) {
        $MonDayDateStamp = strtotime("Monday");
        $skipWeek = ceil(($MonDayDateStamp - $thatDayStamp) / 86400 / 7);
        $thatMonday = $MonDayDateStamp - $skipWeek * 7 * 86400;
        return date("Ymd", $thatMonday);
    }
    /**
     * @param int $errno
     * @param string $errmsg
     * @param array $data
     */
    private function _jsonRet($errno = 0, $errmsg = '' , $data = array()) {
        $this->printOut($errno, $errmsg, $data);
    }
}
?>