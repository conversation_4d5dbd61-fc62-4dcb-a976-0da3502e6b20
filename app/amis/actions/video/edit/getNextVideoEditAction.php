<?php

/**
 * 根据预定优先级，随机获取下一条视频交给当前登陆人来审核，涉及加锁
 * Created by PhpStorm.
 * User: shi<PERSON><PERSON>
 * Date: 6/2 2017
 * Time: 13:59
 * http://kqm.imis.tieba.otp.baidu.com/amis/video/edit/getNextVideoEdit?start_time=1512057600&end_time=1513785600&fir_category=%E5%85%B6%E4%BB%96&search_type=
 */
class getNextVideoEditAction extends Util_Action {
    protected $strAmisGroup = 'video';
    protected $strAmisPerm = 'video:edit';
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner =false;

    const SEARCH_TYPE_CRM_DEMO = 13;
    const SEARCH_TYPE_CRM_VIDEO = 14;
    const SEARCH_TYPE_WORKS = 18;

    /**
     * [_execute description]
     * @return [type] [description]
     */
    public function _execute() {
        $op_uid = Util_User::$intUserId;
        $op_uname = Util_User::$strUserName;
        Bingo_Log::pushNotice("op_uname", $op_uname);
        $startTime = Bingo_Http_Request::get("start_time", strtotime(date('Y-m-d')));
        $firCategory = Bingo_Http_Request::get("fir_category", "未分类");
        $endTime = Bingo_Http_Request::get("end_time", $startTime + 86399);
        $intSearchType = Bingo_Http_Request::get('search_type', 0);
        $intMonths = intval(Bingo_Http_Request::get('months',1));
        $intEditStatus = intval(Bingo_Http_Request::get('edit_status'), 0);
        $intThreadId = intval(Bingo_Http_Request::get('thread_id'));
        $strCallFrom = intval(Bingo_Http_Request::get('call_from')); //1入口来源于查询平台
        $intIsJump = intval(Bingo_Http_Request::get('is_jump')); //1跳过本条进行下一条
        Bingo_Log::pushNotice("startTime", $startTime);
        Bingo_Log::pushNotice("endTime", $endTime);
        Bingo_Log::pushNotice("months", $intMonths);
        Bingo_Log::notice("thread_id=".$intThreadId);
        Bingo_Log::notice("call_from=".$strCallFrom);
        Bingo_Log::notice("edit_status=".$intEditStatus);
        Bingo_Log::notice("is_jump=".$intIsJump);

        if (($intSearchType == self::SEARCH_TYPE_CRM_DEMO || $intSearchType == self::SEARCH_TYPE_CRM_VIDEO)
            && !(Util_User::havePerm('crm:edit') || Util_User::havePerm('crm:edit_2nd')))
        {
            $this->printOut(5, '对不起，您的权限不足');
            return;
        }

        $arrServiceInput = array(
            "start_time" => $startTime,
            "end_time" => $endTime,
            "fir_category" => $firCategory,
            "op_uname" => $op_uname,
            "op_uid" => $op_uid,
            "search_type" => $intSearchType,
            'months' => $intMonths,
            'edit_status' =>  $intEditStatus,
            'thread_id' =>  $intThreadId,
            'is_jump' => $intIsJump,
        );
        $strServiceName = "video";
        $strServiceMethod = "getNextVideoEditForUpdate";
        $arrOutputFromDB = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', 'php', 'utf-8');

        if (false === $arrOutputFromDB || Tieba_Errcode::ERR_SUCCESS != $arrOutputFromDB["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "call $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutputFromDB) . "]";
            Bingo_Log::fatal($strLog);
            $this->printOut($arrOutputFromDB?$arrOutputFromDB["errno"]:Tieba_Errcode::ERR_CALL_SERVICE_FAIL,"服务异常请重试", array());
            return;
        }
        if (empty($arrOutputFromDB["data"]) || !isset($arrOutputFromDB["data"]["thread_id"])) {
            Bingo_Log::warning("no video for edit between $startTime ~  $endTime in $firCategory for $op_uname ");
            $this->printOut(Tieba_Errcode::ERR_SUCCESS, "该分类最后一条内容，返回换个分类", array("thread_id" => 0));
            return;
        }

        $strVideoUrl = strval($arrOutputFromDB["data"]['video_url']);
        $arrExtParams = $arrOutputFromDB['data']['ext_param'];
        $arrFileName    = explode("/", $strVideoUrl);
        $strFileName    = end($arrFileName);
        $strTemp        = explode(".", $strFileName);
        $strVideoName     = $strTemp[0];
        $arrFrame = $this->getFrameList($strVideoName);
        $arrOutputFromDB['data']['video_frame_list']    = $arrFrame['video_frame_list'];
        $arrOutputFromDB['data']['video_frame_width']   = $arrFrame['video_frame_width'];
        $arrOutputFromDB['data']['video_frame_height']  = $arrFrame['video_frame_height'];

        $arrCountEditedInput = array(
            "start_time" => strtotime(date('Y-m-d')),
            "end_time" => strtotime(date('Y-m-d')) + 86399,
            "op_uname" => $op_uname,
        );
        $strServiceName = "video";
        $strServiceMethod = "getCountVideoEditByOpUname";
        $arrCountEditedOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrCountEditedInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrCountEditedOutput || Tieba_Errcode::ERR_SUCCESS != $arrCountEditedOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "call $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrCountEditedOutput) . "]";
            Bingo_Log::fatal($strLog);
            $this->printOut($arrOutputFromDB?$arrOutputFromDB["errno"]:Tieba_Errcode::ERR_CALL_SERVICE_FAIL,"服务异常请重试", array());
            return;
        }
        if(empty($arrOutputFromDB['data']['first_publish_time'])){
            unset($arrOutputFromDB['data']['first_publish_time']);
        }
        $intThreadId = $arrOutputFromDB['data']['thread_id'];
        if($intSearchType != self::SEARCH_TYPE_WORKS){
        
            //取视频宽高
            $arrThreadInput = array(
                "thread_ids" => array($intThreadId,),
                "need_abstract" => 0,
                "forum_id" => 0,
                "need_photo_pic" => 0,
                "need_user_data" => 0,
                "icon_size" => 0,
                "need_forum_name" => 1,  //是否获取吧名
                "call_from" => "client_frs", //上游模块名,这里只能用client_frs
            );
            $arrThreadOutput = Tieba_Service::call('post', 'mgetThread', $arrThreadInput, null, null, 'post', 'php', 'utf-8');
            if (false === $arrThreadOutput || Tieba_Errcode::ERR_SUCCESS != $arrThreadOutput['errno']) {
                Bingo_Log::warning('call post/mgetThread err! [' . serialize(compact('arrThreadInput', 'arrThreadOutput')) . ']');
            }else{
                $arrData = $arrThreadOutput['output']['thread_list'][$intThreadId];
                $arrOutputFromDB['data']['video_width'] = $arrData['video_info']['video_width'];
                $arrOutputFromDB['data']['video_height'] = $arrData['video_info']['video_height'];
                $arrOutputFromDB['data']['forum_name']   = $arrData['forum_name'];
                if($arrData['create_time'] < 1517587200 && $arrData['video_info']['video_type'] == Molib_Util_Video::TEMP_VIDEO_UPLOAD_FROM_PC
                    && preg_match('/tieba-smallvideo-transcode/', $strVideoUrl) && !empty($arrData['video_info']['video_desc'])) {
                    // 较早的PC上传视频video_info中video_width&height和video_url并不对应，2018年1月份的某天修正了这个问题
                    foreach ($arrData['video_info']['video_desc'] as $v) {
                        if ($v['video_id'] != 0) {
                            $arrOutputFromDB['data']['video_width'] = $v['video_width'];
                            $arrOutputFromDB['data']['video_height'] = $v['video_height'];
                        }
                    }
                }
            }
        }else{
            $arrOutputFromDB['data']['video_width'] = $arrOutputFromDB['data']['video_frame_width'];
            $arrOutputFromDB['data']['video_height'] = $arrOutputFromDB['data']['video_frame_height'];
        }
        $arrOutputFromDB['data']['edited_count_byme'] = isset($arrCountEditedOutput["data"]) ? $arrCountEditedOutput["data"] : 0;
        Bingo_Log::pushNotice('countVideoEditByOpUname', $arrCountEditedOutput["data"]);

        if($arrOutputFromDB['data']['audit_flag'] == 2){
            $arrOutputFromDB['data']['video_quality'] = '低俗';
        } else if ($arrOutputFromDB['data']['audit_flag'] == 3){
            $arrOutputFromDB['data']['video_quality'] = '内容不适';
        }

        // 获取审核平台删除理由
        $extInfo = $arrOutputFromDB['data']['ext_info'];
        $auditDelMsg = "";
        if ("" != $extInfo) {
            $extInfoArr = json_decode($extInfo, 1);
            if ( !empty($extInfoArr) && isset($extInfoArr['audit_del_msg'])) {
                $auditDelMsg = $extInfoArr['audit_del_msg'];
            }
        }
        $arrOutputFromDB['data']['audit_del_msg'] = $auditDelMsg;

        if(empty($arrOutputFromDB['data']['forum_name']) && !empty($arrOutputFromDB['data']['forum_id'])){
            $arrOutputFromDB['data']['forum_name'] = $this->getFnameByFid($arrOutputFromDB['data']['forum_id']);
        }

        if ($arrOutputFromDB["data"]["fir_category"] == '游戏' && $arrOutputFromDB["data"]["video_width"] > $arrOutputFromDB["data"]["video_height"] && $arrOutputFromDB["data"]["video_duration"] < 360) {
            $arrOutputFromDB["data"]["community_video"] = 1;
        }

        //获取吧一二级目录
        if (empty($arrOutputFromDB['data']['level_1_name']) && empty($arrOutputFromDB['data']['level_2_name']) && !empty($arrOutputFromDB['data']['forum_id'])) {
            $arrDir = $this->getForumDir($arrOutputFromDB['data']['forum_id']);
            $arrOutputFromDB['data']['level_1_name'] = isset($arrDir['level_1_name']) ? $arrDir['level_1_name'] : $arrOutputFromDB['data']['level_1_name'];
            $arrOutputFromDB['data']['level_2_name'] = isset($arrDir['level_2_name']) ? $arrDir['level_2_name'] : $arrOutputFromDB['data']['level_2_name'];
        }


        $strWeekkey = $this->getWeek($arrOutputFromDB['data']['thread_create_time']);
        $arrOutput = Tieba_Service::call('video', 'getAuditInfoByUrl' , array('weekkeys' => array($strWeekkey), 'video_url' => $arrOutputFromDB['data']['video_url']), null, null, 'post', 'php', 'utf-8');
        if (!$arrOutput || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('query audit info failed! output:' . json_encode($arrOutput));
        }
        $arrExtParam = json_decode($arrOutput['data'][0]['ext_param'], true);
        $arrOutputFromDB['data']['abstract'] = $arrExtParam['ui_trans_params']['video_abstract'];
        $arrExtAttr = $arrExtParam['ext_attr'];
        Tieba_Video_Process::getAmisViewVideoUrl($arrOutputFromDB['data'], 'edit');
        foreach ($arrExtAttr as $arrValue) {
            if ($arrValue['key'] == 'video_info') {
                Tieba_Video_Process::getAmisViewVideoUrl($arrValue['value'], 'edit');
                $arrOutputFromDB['data']['video_url'] = $arrValue['value']['video_url'];
            }
        }
        $arrOutputFromDB["data"]['call_from'] = $strCallFrom;

        $this->printOut(Tieba_Errcode::ERR_SUCCESS, "", $arrOutputFromDB["data"]);
    }

    private function getWeek($thatDayStamp) {
        $MonDayDateStamp = strtotime("Monday");
        $skipWeek = ceil(($MonDayDateStamp - $thatDayStamp) / 86400 / 7);
        
        $thatMonday = $MonDayDateStamp - $skipWeek * 7 * 86400;

        return date("Ymd", $thatMonday);
    }

    /**
     * @param 
     * [_execute description]
     * @return [type] [description]
     */
    private function getFrameList($videoName) {
        $arrInput = array(
            'video_name' => $videoName,

        );
        $strServiceName = "video";
        $strServiceMethod = "getVideoFrameFromEditMis";
        $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "call $strServiceMethod fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return false;
        }
        $arrOut = array(
            'video_frame_list'      => $arrOutput['data']['video_frame_list'],
            'video_frame_width'     => $arrOutput['data']['video_frame_width'],
            'video_frame_height'    => $arrOutput['data']['video_frame_height'],
        );

        return $arrOut;
    }

    /**
     * @param $intFid
     * @return string
     */
    private function getFnameByFid($intFid) {
        $strFname = '';

        $arrInput = array(
            'forum_id'   => array(
                $intFid,
            ),
        );
        $strService = 'forum';
        $strMethod  = 'getFnameByFid';
        $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
        $strLog = sprintf("call $strService::$strMethod failed! input[%s] output[%s]", serialize($arrInput), serialize($arrOutput));
        if ($arrOutput == false){
            Bingo_Log::fatal($strLog);
            return $strFname;
        }elseif($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning($strLog);
            return $strFname;
        }
        $strFname =  $arrOutput['forum_name'][$intFid]['forum_name'];

        return $strFname;
    }

    /**
     * 获取吧目录
     * @param $intForumId
     * @return array
     */
    private function getForumDir($intForumId) {
        $arrInput = array(
            'forum_id'   => $intForumId,
            'ie' => 'utf-8',
        );
        $strService = 'forum';
        $strMethod  = 'getForumDir';
        $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
        $strLog = sprintf(" call $strService::$strMethod failed! input[%s] output[%s]", serialize($arrInput), serialize($arrOutput));
        Bingo_Log::warning($strLog);
        if (false == $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            Bingo_Log::warning($strLog);
            return array();
        }

        return $arrOutput['output'];
    }

}
