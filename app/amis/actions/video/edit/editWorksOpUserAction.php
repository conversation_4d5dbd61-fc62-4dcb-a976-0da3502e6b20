<?php
/**
 * Created by PhpStorm.
 * Date: 2021/2/20
 * Time: 下午2:16.
 * 编辑作品分类操作人
 */

class editWorksOpUserAction extends Util_Action {


    const WORKS_OPERATOR_WORDLIST_NAME = 'tb_wordlist_redis_video_works_operator'; 
   

    /**
     * @brief   执行函数
     * @param   null
     * @return  array
     */
    public function _execute() {
        $firstCateName   = strval(Bingo_Http_Request::get('first_cate_name',''));
        $strOpUnames   = strval(Bingo_Http_Request::get('cate_op_uname',''));
        $op_uname = Util_User::$strUserName;

        if(empty($op_uname) || empty($firstCateName) || empty($strOpUnames)){
            $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, "缺少必要参数");
            Bingo_Log::warning("input params fail!");
            return;
        }
        
        $this->addOpUname($firstCateName,$strOpUnames);
        return;
    }
    

    private function addOpUname($firstCateName,$strOpUnames){
        
        $handleWordServer = Wordserver_Wordlist::factory();   
        $strTableName     = self::WORKS_OPERATOR_WORDLIST_NAME; 
        $key = 'op_uname_'.$firstCateName;
          
        $arrAddKeyInput = array(
            'key' => $key,
            'table' => $strTableName,
            'value' => strval($strOpUnames),
            'replaced' => true,
        );
        $output = $handleWordServer->addKey($arrAddKeyInput);
        if (!$output || $output['err_no'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf("call wordlist::addKey failed! input[%s] output[%s]", serialize($arrAddKeyInput), serialize($output)));
            return $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS);
    }
    

    /**
     * @param int $errno
     * @param string $errmsg
     * @param array $data
     */
    private function _jsonRet($errno = 0, $errmsg = '' , $data = array()) {
        $this->printOut($errno, $errmsg, $data);
    }

}
