<?php
/**
 * date: 2018-07-18 18:07:13
 * author : dongliang04
 **/
class delUserAction extends Util_Action{
    protected $bolOnlyAccessAmis = true;
    protected $bolOnlyInner = true;   

    protected $arrStrRequired = array(
    );

    protected $arrIntRequired = array(
        'vote_act_id',
        'user_id',
    );

    public function _getInput(){
        $this->getInput();
        foreach($this->arrStrOptional as $value){
            $this->_input[$value] = $this->get($value, '');
        }
        foreach($this->arrIntOptional as $value){
            $this->_input[$value] = $this->get($value, 0);
        }
        return true;
    }

    public function _execute(){

        $this->_getInput();

        $arrServiceInput = array(
            'user_id' => $this->_input['user_id'],
            'vote_act_id' => $this->_input['vote_act_id'],
        );
        $strService = 'video';
        $strMethod = 'removeUserFromCJ';
        $arrServiceOutput = Tieba_Service::call($strService, $strMethod, $arrServiceInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrServiceOutput || Tieba_Errcode::ERR_SUCCESS != $arrServiceOutput["errno"]) {
            $strLog = sprintf("call %s:%s fail, input:[%s], output:[%s]", $strService, $strMethod, json_encode($arrServiceInput), json_encode($arrServiceOutput));
            Bingo_Log::warning($strLog);
            return $this->printOut(isset($arrServiceOutput['errno']) ? $arrServiceOutput['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return $this->printOut(Tieba_Errcode::ERR_SUCCESS);
    }
}