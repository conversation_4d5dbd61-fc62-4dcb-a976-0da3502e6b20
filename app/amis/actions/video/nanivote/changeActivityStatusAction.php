<?php
/**
 * Brief: 修改活动的状态.
 * User: xiangwenchao
 * Date: 2018/10/31
 * Time: 10:37 AM
 */
class changeActivityStatusAction extends Util_Action {

    /**
     * @brief   执行函数
     * @param   null
     * @return  null
     */
    public function _execute() {

        $intActId       = (int)Bingo_Http_Request::get('id', 0);
        $intActStatus   = (int)Bingo_Http_Request::get('status', '');      //活动需要切换到到状态，0:下线 1:上线

        $arrValidActStatus = array(0, 1);         //活动有效的状态，0:下线 1:上线


        if (!in_array($intActStatus, $arrValidActStatus) || empty($intActId)) {
            Bingo_Log::warning("wrong task status!\n");
            return $this->printOut(Tieba_ErrCode::ERR_PARAM_ERROR, '参数错误');
        }

        // 调用video.editNewUserPushTask service
        $arrParams = array(
            'field'    => array(
                'status'      => $intActStatus,
            ),
            'cond'    => array(
                'id'          => $intActId,
            ),
        );

        $arrRet = Tieba_Service::call('video', 'updateVoteActivity4Mis', $arrParams, null, null, 'post', 'php', 'utf-8');

        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call video.updateVoteActivity4Mis service failed. input: [' . serialize($arrParams) . ']; output: [' . serialize($arrRet) . ']');
            return $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return $this->printOut(Tieba_Errcode::ERR_SUCCESS);
    }
}