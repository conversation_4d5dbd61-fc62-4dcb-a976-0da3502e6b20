<?php

/**
 * 编辑兑换奖品状态
* Date: 2018-3-16
* Time: 18:15
*/
define("BINGO_ENCODE_LANG", 'utf-8');

class editChangeRewardStatusAction extends Util_Action{

	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = true;

	/**
	 * 子类必须实现
	 * @return [type] [description]
	 */
	public function _execute(){
		$rewardId = intval(Bingo_Http_Request::getNoXssSafe('id', 0));
		$status = intval(Bingo_Http_Request::getNoXssSafe('status', 0));
		$opUid = Util_User::$intUserId;
		$opName = Util_User::$strUserName;

		if ( empty($rewardId) ) {
			Bingo_Log::warning("param error");
			$this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
			return;
		}

		$arrParams = array(
				'id'				=> $rewardId,
				'status'			=> $status, //状态,0表示下线, 1表示在线上
				'op_uid'			=> $opUid,
				'op_uname'			=> $opName,
		);
		$arrRet = Tieba_Service::call('video', 'editChangeRewardStatus', $arrParams, null, null, 'post', 'php', 'utf-8');
		if (!$arrRet) {
			Bingo_Log::warning('call service video:editChangeRewardStatus fail');
			$this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service fail');
			return;
		}
		if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
			Bingo_Log::warning('service video:editChangeRewardStatus return error, ret=' .json_encode($arrRet));
			$this->printOut($arrRet['errno'], $arrRet['errmsg']);
			return;
		}

		$this->printOut(Tieba_Errcode::ERR_SUCCESS, 'success');
	}
}