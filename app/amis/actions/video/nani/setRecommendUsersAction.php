<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2017/11/27
 * Time: 17:27
 */
class setRecommendUsersAction extends Util_Action {

    protected $bolOnlyAccessAmis = false;

    /**
     * 子类必须实现
     * @return [type] [description]
     */
    public function _execute()
    {
        $strUserIds = (string)Bingo_Http_Request::getNoXssSafe('user_ids', '');

        $arrParams = array(
            'user_ids' => $strUserIds,
        );
        $arrRet = Tieba_Service::call('video', 'setNaniRecommendUsers', $arrParams, null, null, 'post', 'php', 'utf-8');
        if (!$arrRet) {
            Bingo_Log::warning('call service video::setNaniRecommendUsers fail');
            $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service fail');
            return;
        }
        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('service video::setNaniRecommendUsers return error, ret=' .json_encode($arrRet));
            $this->printOut($arrRet['errno'], $arrRet['errmsg']);
            return;
        }

        $this->printOut(Tieba_Errcode::ERR_SUCCESS, 'success');
    }

}
