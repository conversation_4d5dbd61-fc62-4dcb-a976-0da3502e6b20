<?php

/**
 * 添加暖贴员
 * User: ml 
 * Date: 2018-1-14
 * Time: 18:15
 */
define("BINGO_ENCODE_LANG", 'utf-8');

class addWarmerAction extends Util_Action{

	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * 子类必须实现
	 * @return [type] [description]
	 */
	public function _execute(){
		$strUids = strval(Bingo_Http_Request::getNoXssSafe('uids', 0));
		$status = intval(Bingo_Http_Request::getNoXssSafe('status', 0));
		$opName = Util_User::$strUserName;
		
		if (empty($strUids) || $status > 1) {
			Bingo_Log::warning("param error, uids = $strUids, opName = $opName, status = $status");
			$this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
			return;
		}
		
		$arrUserList = array();
		$arrUids = explode(PHP_EOL, $strUids);
		$arrUids = array_slice($arrUids, 0, 100);
		$arrParams = array(
				"user_id" => $arrUids,
		);
		$arrRet = Tieba_Service::call('user', 'getUnameByUids', $arrParams, null, null, 'post', 'php', 'utf-8');
		if (false === $arrRet) {
			Bingo_Log::warning('call service user:getUnameByUids fail');
			$this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service fail');
			return;
		}
		if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
			Bingo_Log::warning('service user:getUnameByUids return error, input='.json_encode($arrParams).'ret=' .json_encode($arrRet));
			$this->printOut($arrRet['errno'], $arrRet['errmsg']);
			return;
		}
		
		$arrUserList = empty($arrRet['output']['unames']) ? array() : $arrRet['output']['unames'];
		
		if (empty($arrUserList)){
			Bingo_Log::warning("param error, uids = $strUids, opName = $opName, status = $status");
			$this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
			return;
		}
	
		$arrParams = array(
				'user_list' => $arrUserList,
				'op_name'   => $opName,
				'status'  	=> $status,
		);
		$arrRet = Tieba_Service::call('video', 'addWarmer', $arrParams, null, null, 'post', 'php', 'utf-8');
		if (!$arrRet) {
			Bingo_Log::warning('call service video:addWarmer fail');
			$this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service fail');
			return;
		}
		if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
			Bingo_Log::warning('service video:addWarmer return error, ret=' .json_encode($arrRet));
			$this->printOut($arrRet['errno'], $arrRet['errmsg']);
			return;
		}
	
		$this->printOut(Tieba_Errcode::ERR_SUCCESS, 'success');
	}
}