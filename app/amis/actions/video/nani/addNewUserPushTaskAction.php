<?php
/**
 * Brief: 添加新用户推送任务
 * User: x<PERSON>gwenchao
 * Date: 2018/7/25
 * Time: 上午11:47
 */
class addNewUserPushTaskAction extends Util_Action {

    /**
     * @brief   执行函数
     * @param   null
     * @return  null
     */
    public function _execute() {

        $intTaskType     = (int)Bingo_Http_Request::get('task_type', '');          //推送任务类型任务，0:激活内一小时，1:次日，2:三日，3:五日，4:七日，5:十五日，6:二十二日，7:三十日
        $strPushInfoId   =  trim((string)Bingo_Http_Request::get('info_id', ''));  //推送素id，使用英文逗号分隔（1,2,3）

        $intOpUid        = Util_User::$intUserId;
        $intStatus       = 0;                                                     //推送任务状态，0:编辑中（默认），1:待推送

        //入参校验
        if (!isset($intTaskType) || empty($strPushInfoId)) {
            Bingo_Log::warning("some param empty");
            return $this->printOut(Tieba_ErrCode::ERR_PARAM_ERROR, '参数错误');
        }

        //字段的长度控制 (源自mysql，而非前端)
        $arrFieldsLen = array(
            'info_id'   => 50,
        );

        foreach($arrFieldsLen as $strField => $intMaxLen){
            if(mb_strlen(Bingo_Http_Request::get($strField, '')) > $intMaxLen){
                return $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, "{$strField}长度不能大于{$intMaxLen}");
            }
        }

        // 调用video.insertNewUserPushTask service
        $arrParams = array(
            'type'        => $intTaskType,
            'pushinfo_id' => $strPushInfoId,
            'create_uid'  => $intOpUid,
            'update_uid'  => $intOpUid,
            'status'      => $intStatus,
        );

        $arrRet = Tieba_Service::call('video', 'insertNewUserPushTask', $arrParams, null, null, 'post', 'php', 'utf-8');

        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call video.insertNewUserPushTask service failed. input: [' . serialize($arrParams) . ']; output: [' . serialize($arrRet) . ']');
            return $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return $this->printOut(Tieba_Errcode::ERR_SUCCESS);

    }
}