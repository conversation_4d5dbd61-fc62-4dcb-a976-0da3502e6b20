<?php

/**
 * 查询抽奖奖品list
* Date: 2018-3-16
* Time: 18:15
*/
define("BINGO_ENCODE_LANG", 'utf-8');

class getDrawRewardListAction extends Util_Action{

	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;

	/**
	 * 子类必须实现
	 * @return [type] [description]
	 */
	public function _execute(){

		$actId = (int)Bingo_Http_Request::getNoXssSafe('act_id', 0);
		$intPn = Bingo_Http_Request::getNoXssSafe('pn', 1);
		$intRn = Bingo_Http_Request::getNoXssSafe('rn', 20);

		$arrParams = array(
				'act_id' => $actId,
				'pn' 	 => $intPn,
				'rn' 	 => $intRn,
		);

		$arrRet = Tieba_Service::call('video', 'getRewardListForMis', $arrParams, null, null, 'post', 'php', 'utf-8');
		if (!$arrRet) {
			Bingo_Log::warning('call service video:getRewardListForMis fail'.json_encode($arrParams));
			$this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service fail');
			return;
		}
		if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
			Bingo_Log::warning('service video:getRewardListForMis return error, ret=' .json_encode($arrRet));
			$this->printOut($arrRet['errno'], $arrRet['errmsg']);
			return;
		}

		$this->printOut(Tieba_Errcode::ERR_SUCCESS, 'success', $arrRet['data']);
	}
}