<?php

/**
 * 查询兑奖奖品list
* Date: 2018-3-16
* Time: 18:15
*/
define("BINGO_ENCODE_LANG", 'utf-8');

class getChangeRewardAction extends Util_Action{

	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;

	/**
	 * 子类必须实现
	 * @return [type] [description]
	 */
	public function _execute(){

		$strOpName = Bingo_Http_Request::getNoXssSafe('op_uname', ''); // 操作人
		$status = Bingo_Http_Request::getNoXssSafe('status', ''); // 奖品状态 0表示下线, 1表示在线上
		$type = Bingo_Http_Request::getNoXssSafe('type', ''); // 奖品类型 0代表虚拟 1代表真实
		
		$intPn = Bingo_Http_Request::getNoXssSafe('pn', 1);
		$intRn = Bingo_Http_Request::getNoXssSafe('rn', 20);

		$arrParams = array(
				'act_id' => 1,
				'pn' 	 => $intPn,
				'rn' 	 => $intRn,
		);

		//op_name
		if (!empty($strOpName)){
			$arrParams['op_uname'] = $strOpName;
		}
		//status
		if (is_numeric($status)){
			$arrParams['status'] = $status;
		}
		//type
		if (is_numeric($type)){
			$arrParams['type'] = $type;
		}
		
		$arrRet = Tieba_Service::call('video', 'getRewardListForMis', $arrParams, null, null, 'post', 'php', 'utf-8');
		if (!$arrRet) {
			Bingo_Log::warning('call service video:getRewardListForMis fail'.json_encode($arrParams));
			$this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service fail');
			return;
		}
		if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
			Bingo_Log::warning('service video:getRewardListForMis return error, ret=' .json_encode($arrRet));
			$this->printOut($arrRet['errno'], $arrRet['errmsg']);
			return;
		}

		$this->printOut(Tieba_Errcode::ERR_SUCCESS, 'success', $arrRet['data']);
	}
}