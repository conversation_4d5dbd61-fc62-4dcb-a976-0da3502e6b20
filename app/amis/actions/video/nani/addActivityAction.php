<?php

/**
 * 添加活动
* Date: 2018-3-20
* Time: 18:15
*/
define("BINGO_ENCODE_LANG", 'utf-8');

class addActivityAction extends Util_Action{

	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = true;

	/**
	 * 子类必须实现
	 * @return [type] [description]
	 */
	public function _execute(){
		$activityName = strval(Bingo_Http_Request::getNoXssSafe('activity_name', ''));
		$dayDrawTimes = intval(Bingo_Http_Request::getNoXssSafe('day_draw_times', 0));
		$perChanceUseScore = intval(Bingo_Http_Request::getNoXssSafe('per_chance_use_score', 0));
		$urlBackGround = strval(Bingo_Http_Request::getNoXssSafe('url_back_ground', ''));
		$urlChanceBackGround = strval(Bingo_Http_Request::getNoXssSafe('url_chance_back_ground', ''));
		$urlPopTop = strval(Bingo_Http_Request::getNoXssSafe('url_pop_top', ''));
		$urlRewardButton = strval(Bingo_Http_Request::getNoXssSafe('url_reward_button', ''));
		$urlTurntablePic = strval(Bingo_Http_Request::getNoXssSafe('url_turntable_pic', ''));
		$status = intval(Bingo_Http_Request::getNoXssSafe('status', 0));
		$opName = Util_User::$strUserName;

		if( empty($activityName) || empty($dayDrawTimes) || empty($perChanceUseScore) || empty($urlBackGround)
				|| empty($urlChanceBackGround) || empty($urlPopTop) || empty($urlRewardButton) || empty($urlTurntablePic) ) {
			Bingo_Log::warning("param error");
			$this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
			return;
		}
		
		$arrParams = array(
				'activity_name'				=> $activityName, 		//活动名
				'activity_type' 			=> 1, 					//活动类型,1:抽奖,2:兑换
				'day_draw_times' 			=> $dayDrawTimes, 		//每日抽奖次数上限
				'per_chance_use_score'		=> $perChanceUseScore,	//抽奖消耗积分设置
				'url_back_ground' 			=> $urlBackGround, 		//抽奖页背景图
				'url_chance_back_ground'	=> $urlChanceBackGround,//抽奖页抽奖次数背景图
				'url_pop_top' 				=> $urlPopTop,			//抽奖弹窗背景图
				'url_reward_button' 		=> $urlRewardButton,	//抽奖页抽奖按钮图
				'url_turntable_pic' 		=> $urlTurntablePic,	//转盘图url
				'status' 					=> 0, 					//状态,0:线下,1:线上
				'op_name' 					=> $opName,
		);
		$arrRet = Tieba_Service::call('video', 'addRewardActivity', $arrParams, null, null, 'post', 'php', 'utf-8');
		if (!$arrRet) {
			Bingo_Log::warning('call service video:addRewardActivity fail');
			$this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service fail');
			return;
		}
		if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
			Bingo_Log::warning('service video:addRewardActivity return error, ret=' .json_encode($arrRet));
			$this->printOut($arrRet['errno'], $arrRet['errmsg']);
			return;
		}

		$this->printOut(Tieba_Errcode::ERR_SUCCESS, 'success');
	}
}