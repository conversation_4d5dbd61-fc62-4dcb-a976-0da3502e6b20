<?php
/**
 * 展示定向推荐任务
 * User: liuheyi
 * Date: 2018/3/26
 * Time: 17:27
 */
class listDeliveryTaskAction extends Util_Action {

    protected $bolOnlyAccessAmis = false;

    /**
     * 子类必须实现
     * @return [type] [description]
     */
    public function _execute()
    {
        $intThreadId   = intval(Bingo_Http_Request::get('thread_id',0));
        $strTimeType   = Bingo_Http_Request::get('search_time_type','');
        $intStartTime  = intval(Bingo_Http_Request::get('start_time',0));
        $intEndTime    = intval(Bingo_Http_Request::get('end_time',0));
        $intPn         = intval(Bingo_Http_Request::get('pn',1));
        $intRn         = intval(Bingo_Http_Request::get('rn',20));
        $strOpName     = Bingo_Http_Request::get('op_name','');
        $intStatus     = intval(Bingo_Http_Request::get('status', -1));


 //       $strOpName = Util_User::$strUserName;
   //u     $intOpUid = Util_User::$intUserId;
        $arrSelect = array();
        if (!empty($intThreadId)) {
           $arrSelect['thread_id'] = $intThreadId;
        }
        if (!empty($strOpName)) {
           $arrSelect['op_name'] = $strOpName;
        }
        if ($intStatus >= 0) {
           $arrSelect['status'] = $intStatus;
        }

        if(!empty($strTimeType)){
            $arrSelect['search_time_type'] = $strTimeType;
            if ( !empty($intStartTime) ) {
               $arrSelect['start_time'] = $intStartTime;
            }
            if ( !empty($intEndTime) ) {
               $arrSelect['end_time']= $intEndTime;
            }
        }
        $arrSelect['pn'] = $intPn;
        $arrSelect['rn'] = $intRn;
      
        $arrSelectOut = Tieba_Service::call('video', 'getTaskListByCondition', $arrSelect, null, null, 'post', 'php', 'utf-8');

        if (false === $arrSelectOut || Tieba_Errcode::ERR_SUCCESS != $arrSelectOut["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "call service getTaskListByCondition fail. input:[" . serialize($arrSelect) . "]; output:[" . serialize($arrSelectOut) . "]";
            Bingo_Log::warning($strLog);
            return $this->printOut(isset($arrOut['errno']) ? $arrOut['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);

        }
        $this->printOut(Tieba_Errcode::ERR_SUCCESS, 'success', $arrSelectOut['data']);
    }
}
