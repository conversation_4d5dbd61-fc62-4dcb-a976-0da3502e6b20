<?php

/**
 * 审核暖贴员
 * User: ml 
 * Date: 2018-1-14
 * Time: 18:15
 */
define("BINGO_ENCODE_LANG", 'utf-8');

class auditWarmerAction extends Util_Action{

	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * 子类必须实现
	 * @return [type] [description]
	 */
	public function _execute(){
		
		$uid = intval(Bingo_Http_Request::getNoXssSafe('user_id', 0));
		$status = intval(Bingo_Http_Request::getNoXssSafe('status', 0));
		$opName = Util_User::$strUserName;
		
		if ($uid <= 0 || empty($opName) || ( $status != 1 && $status != 2 ) ) {
			Bingo_Log::warning("param error, user_id = $uid, opName = $opName, status = $status");
			$this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
			return;
		}
	
		$arrParams = array(
				'user_id' => $uid,
				'op_name' => $opName,
				'status'  => $status,
		);
		$arrRet = Tieba_Service::call('video', 'auditWarmer', $arrParams, null, null, 'post', 'php', 'utf-8');
		if (!$arrRet) {
			Bingo_Log::warning('call service video:auditWarmer fail'.json_encode($arrParams));
			$this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service fail');
			return;
		}
		if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
			Bingo_Log::warning('service video:auditWarmer return error, ret=' .json_encode($arrRet));
			$this->printOut($arrRet['errno'], $arrRet['errmsg']);
			return;
		}
	
		$this->printOut(Tieba_Errcode::ERR_SUCCESS, 'success');
	}
}