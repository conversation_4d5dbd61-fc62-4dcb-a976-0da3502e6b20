<?php

/**
 * 编辑获奖用户奖品领取状态
 * Date: 2018-1-14
 * Time: 18:15
 */
define("BINGO_ENCODE_LANG", 'utf-8');

class editUserRewardStatusAction extends Util_Action{

	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * 子类必须实现
	 * @return [type] [description]
	 */
	public function _execute(){
		$intUserRewardId = intval(Bingo_Http_Request::getNoXssSafe('user_reward_id', 0));
		$status = intval(Bingo_Http_Request::getNoXssSafe('status', 0));
		$opName = Util_User::$strUserName;
		
		if (empty($intUserRewardId) || $status < 1) {
			Bingo_Log::warning("param error, user_reward_id = $intUserRewardId, opName = $opName, status = $status");
			$this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
			return;
		}
	
		$arrParams = array(
				'id'		=> $intUserRewardId,
				'op_name' 	=> $opName,
				'status' 	=> $status,
		);
		$arrRet = Tieba_Service::call('video', 'updateUserRewardInfoById', $arrParams, null, null, 'post', 'php', 'utf-8');
		if (!$arrRet) {
			Bingo_Log::warning('call service video:updateUserRewardInfoById fail');
			$this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service fail');
			return;
		}
		if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
			Bingo_Log::warning('service video:updateUserRewardInfoById return error, ret=' .json_encode($arrRet));
			$this->printOut($arrRet['errno'], $arrRet['errmsg']);
			return;
		}
	
		$this->printOut(Tieba_Errcode::ERR_SUCCESS, 'success');
	}
}