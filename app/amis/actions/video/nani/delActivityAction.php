<?php

/**
 * 删除活动
* Date: 2018-3-20
* Time: 18:15
*/
define("BINGO_ENCODE_LANG", 'utf-8');

class delActivityAction extends Util_Action{

	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = true;

	/**
	 * 子类必须实现
	 * @return [type] [description]
	 */
	public function _execute(){

		$activityId = intval(Bingo_Http_Request::getNoXssSafe('id', 0));
		$opName = Util_User::$strUserName;

		if ($activityId <= 0 || empty($opName)) {
			Bingo_Log::warning("param error, activity_id = $activityId, opName = $opName");
			$this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
			return;
		}

		$arrParams = array(
				'activity_id' => $activityId,
		);
		$arrRet = Tieba_Service::call('video', 'delActivity', $arrParams, null, null, 'post', 'php', 'utf-8');
		if (!$arrRet) {
			Bingo_Log::warning('call service video:delActivity fail'.json_encode($arrParams));
			$this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service fail');
			return;
		}
		if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
			Bingo_Log::warning('service video:delActivity return error, ret=' .json_encode($arrRet));
			$this->printOut($arrRet['errno'], $arrRet['errmsg']);
			return;
		}

		$this->printOut(Tieba_Errcode::ERR_SUCCESS, 'success');
	}
}