<?php

/**
 * 删除暖贴员
 * User: ml 
 * Date: 2018-1-14
 * Time: 18:15
 */
define("BINGO_ENCODE_LANG", 'utf-8');

class delWarmerAction extends Util_Action{

	protected $bolOnlyAccessAmis = false;
	protected $bolOnlyInner = false;
	
	/**
	 * 子类必须实现
	 * @return [type] [description]
	 */
	public function _execute(){
		
		$uid = (int)Bingo_Http_Request::getNoXssSafe('user_id', 0);
		$opName = Util_User::$strUserName;
		
		if ($uid <= 0 || empty($opName)) {
			Bingo_Log::warning("param error, user_id = $uid, opName = $opName");
			$this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
			return;
		}
	
		$arrParams = array(
				'user_id' => $uid,
				'op_name' => $opName,
		);
		$arrRet = Tieba_Service::call('video', 'delWarmer', $arrParams, null, null, 'post', 'php', 'utf-8');
		if (!$arrRet) {
			Bingo_Log::warning('call service video:delWarmer fail'.json_encode($arrParams));
			$this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service fail');
			return;
		}
		if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
			Bingo_Log::warning('service video:delWarmer return error, ret=' .json_encode($arrRet));
			$this->printOut($arrRet['errno'], $arrRet['errmsg']);
			return;
		}
	
		$this->printOut(Tieba_Errcode::ERR_SUCCESS, 'success');
	}
}