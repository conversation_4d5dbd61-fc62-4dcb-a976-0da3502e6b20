<?php
/**
 * Brief: 修改新用户推送任务的状态.
 * User: xiangwenchao
 * Date: 2018/7/25
 * Time: 下午7:12
 */
class changeNewUserPushTaskStatusAction extends Util_Action {

    /**
     * @brief   执行函数
     * @param   null
     * @return  null
     */
    public function _execute() {

        $intTaskId       = (int)Bingo_Http_Request::get('id', '');
        $intTaskStatus   = (int)Bingo_Http_Request::get('status', '');      //推送任务需要切换到到状态，0:下线（编辑中）1:上线（待推送）

        $arrValidTaskStatus = array(0, 1);         //推送任务有效的状态，0:下线（编辑中）1:上线（待推送）


        if (!in_array($intTaskStatus, $arrValidTaskStatus)) {
            Bingo_Log::warning("wrong task status!\n");
            return $this->printOut(Tieba_ErrCode::ERR_PARAM_ERROR, '参数错误');
        }

        // 调用video.editNewUserPushTask service
        $arrParams = array(
            'id'          => $intTaskId,
            'status'        => $intTaskStatus,
        );

        $arrRet = Tieba_Service::call('video', 'changeNewUserPushTaskStatus', $arrParams, null, null, 'post', 'php', 'utf-8');

        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call video.changeNewUserPushTaskStatus service failed. input: [' . serialize($arrParams) . ']; output: [' . serialize($arrRet) . ']');
            return $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return $this->printOut(Tieba_Errcode::ERR_SUCCESS);
    }
}