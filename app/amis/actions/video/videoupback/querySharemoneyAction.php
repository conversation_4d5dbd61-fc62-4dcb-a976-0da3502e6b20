<?php
/**
 * 视频号分润查询后台-数据查询 1633017600
 */
class querySharemoneyAction extends Util_Action{

    public function _execute(){
       
        $userType = Bingo_Http_Request::get('user_type','');
        $arrUserType = empty($userType) ? Util_WorkConf::$arrWorkUserType : array(intval($userType));
        $uids        = Bingo_Http_Request::get('uids','');
        $arrUids     = !empty($uids) ? explode(',',$uids) : array();
        $verticalField = Bingo_Http_Request::get('vertical_field',0);
        $paintStyle  = Bingo_Http_Request::get('paint_style',0);
        $incomeMonth = Bingo_Http_Request::get('income_month',0);
        $pn          = intval(Bingo_Http_Request::get('pn', 1));
        $rn          = intval(Bingo_Http_Request::get('rn', 100));
        $isExport    = intval(Bingo_Http_Request::get('is_export', 0));
        $offset      = ($pn - 1) * $rn;
        
        if($incomeMonth <= 0){
            Bingo_Log::warning('month must be selected');
            return self::jsonRet(Tieba_Errcode::ERR_MO_PARAM_INVALID);
        }
        
        //组装入参
        $month = intval(date('Ym', $incomeMonth));
        $arrInput    = array(
            'user_type'      => $arrUserType ,
            'income_month'   => $month ,
            'page_num'       => $rn ,
            'offset'         =>  $offset  
        );
        if(sizeof($arrUids) > 0){
            $arrInput['uids'] = $arrUids;
        }
        if( $verticalField > 0){
            $arrInput['vertical_field'] = intval($verticalField);
        }
        if( $paintStyle > 0){
            $arrInput['paint_style'] = intval($paintStyle);
        }
        
        //请求查询本月收益service
        $arrOutput = Tieba_Service::call('video', 'getCalculatedIncome', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (!$arrOutput || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf("call getCalculatedIncome failed! input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return self::jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        //请求查询上月收益
        $arrLastMonthIncome = array();
        if(isset($arrOutput['data']['income']) &&!empty($arrOutput['data']['income'])){
            $lastMonth = intval(date('Ym',strtotime('-1 month',strtotime(intval(strval($month).'01')))));
            $arrInput['income_month'] = $lastMonth ;
            $arrLastMonthIncome = self::getLastMonthIncome($arrInput,$arrOutput['data']['income']);
        }
        
        
        //构造返回
        $arrRet   = array();
        $arrRows  = array();
        $intCount = 0;
        if(!empty($arrOutput['data']['income']) && $arrOutput['data']['total']>0 ){
            $arrRows  = $arrOutput['data']['income'];
            $intCount = $arrOutput['data']['total'];
        }
        //获取用户信息
        $arrUserInfo = self::getUserInfo($arrRows );

        foreach($arrRows  as $k => $val){
            $userinfo = $arrUserInfo[$val['uid']];
            $lastMonthIncome = $arrLastMonthIncome[$val['uid']];
            $arrRows[$k]['tieba_uid'] =  $userinfo['tieba_uid'];
            $arrRows[$k]['user_name'] =  $userinfo['user_name'];
            $arrRows[$k]['last_month_income'] =  $lastMonthIncome['income'];
            $arrRows[$k]['diff_income']       =  $val['income']-$lastMonthIncome['income'];
            $arrRows[$k]['user_nickname']  =  isset($userinfo['user_nickname_v2']) ?$userinfo['user_nickname_v2'] : $userinfo['user_nickname'] ;
            $arrRows[$k]['vertical_field'] = Molib_Util_Video_Income::$arrVerticalField[$val['vertical_field']];
            $arrRows[$k]['paint_style']    = Molib_Util_Video_Income::$arrPaintStyle[$val['paint_style']];
            $arrRows[$k]['advise_field']   = Molib_Util_Video_Income::$arrVerticalField[$val['vertical_field']];
            $arrRows[$k]['user_type']      = Molib_Util_Video_Income::$arrUserType[$val['author_type']];
            $allCoe = json_decode($val['ext_param'],true); //解析出各项系数
            $arrRows[$k]['vertical_field_coe'] = !empty($allCoe) ? $allCoe['vertical_field_coe'] : '-';
            $arrRows[$k]['paint_style_coe']    = !empty($allCoe) ? $allCoe['paint_style_coe'] : '-';
            $arrRows[$k]['interact_coe']       = !empty($allCoe) ? $allCoe['interact_coe'] : '-';
            $arrRows[$k]['head_suppress_coe']  = !empty($allCoe) ? $allCoe['head_suppress_coe'] : '-';
        } 
        
        //导出
        if($isExport==1){  
            $strFileName = 'query_income_page_'.$pn.'.csv';
            $strOut = "uid,用户名,作者类型,有效播放量,本月收益,上月收益,差值,垂类系数,画风系数,互动系数,头部打压系数,作者贴吧id,认证信息,所属垂类,建议划分垂类,作者画风\n";
            $strOut = Molib_Util_Encode::convertUTF8ToGBK($strOut);
            foreach ($arrRows as $value) {
                foreach ($value as $key => $element) {
                    $value[$key] = Molib_Util_Encode::convertUTF8ToGBK($element);
                }
                $strOut .= "\"".$value['uid']."\",\"".$value['user_name']."\",\"".$value['user_type'].
                "\",\"".$value['valid_play']."\",\"".$value['income']."\",\"".$value['last_month_income'].
                "\",\"".$value['diff_income']."\",\"".$value['vertical_field_coe'].
                "\",\"".$value['paint_style_coe']."\",\"".$value['interact_coe']."\",\"".$value['head_suppress_coe'].
                "\",\"".$value['tieba_uid']."\",\"".$value['auth_desc'].
                "\",\"".$value['vertical_field']."\",\"".$value['advise_field']."\",\"".$value['paint_style']."\"\n";
            }
            header('Content-type:text/csv;charset=GB2312');
            header("Content-Type:application/download");
            header("Content-Disposition: attachment;filename=$strFileName"); 
            echo $strOut;
            return true;
        }

        $arrRet = array(
            'rows'  => $arrRows,
            'count' => $intCount,
        );
        return self::jsonRet(Tieba_Errcode::ERR_SUCCESS,$arrRet);
    }

    /**
     * 根据uid获取上月收益，用于展示
     */
    public static function getLastMonthIncome($arrInput,$arrIncome){
        // amis没迁移hhvm，当前php版本不支持array_column
        // $arrUids = array_column($arrIncome,'uid');
        $arrUids = array();
        foreach($arrIncome as $k=>$val){
            $arrUids[] = $val['uid'];
        }
        $arrAllUids = array_chunk($arrUids,100);
        $objMulti = new Molib_Tieba_Multi('getCalculatedIncomes');
        foreach($arrAllUids as $k => $uids){
            $strKey = 'getCalculatedIncome_'.$k;
            $arrInput['uids'] = $uids;
            $arrMultiInput[$strKey] = array(
                'serviceName' => 'video',
                'method'      => 'getCalculatedIncome',
                'input'       => $arrInput,
                'format'      => 'php',
                'ie'          => 'utf-8',
            );
            $objMulti->register($strKey, $arrMultiInput[$strKey]);
        }
        $objMulti->call();
        $arrIncomeInfo = array();
        foreach($arrAllUids as $k=>$uids){
            $strKey    = 'getCalculatedIncome_'.$k;
            $arrOutput = $objMulti->getResult($strKey);
            if (!$arrOutput || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call video::getCalculatedIncome fail. key='.$strKey.' output='. serialize($arrOutput));
                continue;
            } 
            $arrIncomeInfo = $arrIncomeInfo + $arrOutput['data']['income'];
        }
        $arrIncomeRet = array();
        foreach($arrIncomeInfo as $k=>$val){
            $arrIncomeRet[$val['uid']] = $val;
        }
        return $arrIncomeRet;
        
    }

    /**
     * 获取用户信息
     */
    public static function getUserInfo($arrRows){
        $arrAllUids = array();
        foreach($arrRows as $k=>$v){
            $arrAllUids[$k] = $v['uid'];
        }
        $arrUids  = array_chunk($arrAllUids,30);
        $objMulti = new Molib_Tieba_Multi('mgetUserInfos');
        foreach($arrUids as $k => $uids){
            $strKey = 'sharemoney_getUserinfo_'.$k;
            $arrMultiInput[$strKey] = array(
                'serviceName' => 'user',
                'method'      => 'mgetUserData',
                'input' => array(
                    'user_id' => $uids,
                ),
                'format'      => 'php',
                'ie'          => 'utf-8',
            );
            $objMulti->register($strKey, $arrMultiInput[$strKey]);
        }
        $objMulti->call();
        $arrUserInfo = array();
        foreach($arrUids as $k=>$v){
            $strKey    = 'sharemoney_getUserinfo_'.$k;
            $arrOutput = $objMulti->getResult($strKey);
            if (!$arrOutput || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call user::mgetUserData fail. key='.$strKey.' output='. serialize($arrOutput));
                continue;
            } 
            $arrUserInfo = $arrUserInfo + $arrOutput['user_info'] ;
        }
        return $arrUserInfo;
    }

    public static function jsonRet($errno, $arrData = array() ,$errmsg = false){
        $arrRet = array(
            'status' => $errno,
            'msg'    => $errmsg === false ? Tieba_Error::getErrmsg($errno) : $errmsg,
            'data'   => $arrData,
        );
        echo json_encode($arrRet);
    }
}