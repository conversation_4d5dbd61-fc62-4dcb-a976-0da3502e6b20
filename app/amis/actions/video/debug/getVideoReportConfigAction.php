<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2017/9/29
 * Time: 14:47
 */
class getVideoReportConfigAction extends Util_Action {

    protected $bolOnlyAccessAmis = false;

    /**
     * 子类必须实现
     * @return [type] [description]
     */
    public function _execute()
    {
        $strKey = strtolower(trim((string)Bingo_Http_Request::get('key', '*')));
        if (empty($strKey)) {
            $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, 'param invalid');
            return;
        }

        $objWordList = Wordserver_Wordlist::factory();
        $arrKeys = array($strKey);
        $arrValues = $objWordList->getValueByKeys($arrKeys, Util_Def::VIDEO_REPORT_CONFIG_WORDLIST_FULL_NAME);
        if (isset($arrValues[$strKey]) && $arrConfig = Bingo_String::json2array($arrValues[$strKey])) {
            $arrOutput['config'] = Util_Format::pretty_json($arrConfig);
        } else {
            $arrOutput['config'] = '';
        }


        $this->printOut(Tieba_Errcode::ERR_SUCCESS, 'success', $arrOutput);
    }

}
