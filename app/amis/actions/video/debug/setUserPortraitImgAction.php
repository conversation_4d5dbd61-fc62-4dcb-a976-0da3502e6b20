<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2017/9/29
 * Time: 14:47
 */
class setUserPortraitImgAction extends Util_Action {

    protected $bolOnlyAccessAmis = false;

    private static $_arrAllowedOpUnames = array(
        'shangshuai02' => 1,
    );

    const ALBUM_NAME = '默认相册';
    const FILE_NAME = 'image.jpg';
    const URL_PREFIX = '/upload';
    const SRV_PORTRAIT_HOST = '************';
    const SRV_PORTRAIT_PORT = '80';
    const SRV_PORTRAIT_CONN_TIMEOUT = 1000;
    const SRV_PORTRAIT_READ_TIMEOUT = 5000;
    const SRV_PORTRAIT_RETRY = 2;

    /**
     * 子类必须实现
     * @return [type] [description]
     */
    public function _execute()
    {
        $intUserId = (int)Bingo_Http_Request::get('user_id', 0);
        $strPicUrl = trim(Bingo_Http_Request::getNoXssSafe('pic_url', ''));
        if ($intUserId <= 0 || empty($strPicUrl)) {
            Bingo_Log::warning('param error');
            $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
            return;
        }

        $strOpUname = $this->getOpUserName();
        if (!isset(self::$_arrAllowedOpUnames[$strOpUname])) {
            Bingo_Log::warning('not allowed, op_uname='.$strOpUname);
            $this->printOut(Tieba_Errcode::ERR_USER_NO_PERM, 'no perm');
            return;
        }

        $strPicData = Util_Rpc::fetchUrl($strPicUrl);
        if (!$strPicData) {
            Bingo_Log::warning('fail to load pic data for user '. $intUserId);
            $this->printOut(Tieba_Errcode::ERR_RPC_CALL_FAIL, 'load pic data fail');
            return;
        }
        if(!imagecreatefromstring($strPicData)) {
            Bingo_Log::warning('pic data invalid');
            $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, 'pic data invalid');
            return;
        }

        $strTmpFile = "/tmp/$intUserId.jpg";
        if (!file_put_contents($strTmpFile, $strPicData, FILE_BINARY)) {
            Bingo_Log::warning('fail to create pic file for user ', $intUserId);
            $this->printOut(Tieba_Errcode::ERR_FILE_NOT_FOUND, 'create pic file fail');
            return;
        }

        $arrPostVars = array(
            'spIsBlogPicAdd'    =>  2,
            'ct'                =>  4,
            'spAlbumName'       =>  self::ALBUM_NAME,
            'Filename'          =>  self::FILE_NAME,
            'spRefURL'          =>  '',
            'BrowserType'       =>  1,
            'cm'                =>  1,
            'portrait_data'     =>  '@' . $strTmpFile,
            'tb_clientip'       =>  Bingo_Http_Ip::getConnectIp(),
            //'bduss'             => $_COOKIE['BDUSS'],
            'user_id'           => $intUserId,
        );

        $strUrl = self::URL_PREFIX;
        $arrHost = array(
            array(
                'host'  =>  self::SRV_PORTRAIT_HOST,
                'port'  =>  self::SRV_PORTRAIT_PORT,
            ),
        );

        $strCallId = 'client_portrait_upload';
        $objRpc = new Bd_Rpc_Http($strCallId, $arrHost);
        $objRpc->setOptions(array(
            'connect_timeout'   =>  self::SRV_PORTRAIT_CONN_TIMEOUT,
            'read_timeout'      =>  self::SRV_PORTRAIT_READ_TIMEOUT,
        ));

        $arrParams = array(
            'url'           =>  $strUrl,
            'method'        =>  'post',
            'cookie'        =>  $_COOKIE,
            'post_vars'     =>  $arrPostVars,
            'curl_opts'     =>  array(
                CURLOPT_NOSIGNAL => 1,
            ),
        );

        $strRet = $objRpc->call($arrParams, self::SRV_PORTRAIT_RETRY);
        @unlink($strTmpFile);

        $intErrno = $objRpc->getErrno();
        $strErrMsg = $objRpc->getError();
        $arrConnectInfo = $objRpc->getConnectInfo();
        $intHttpCode   = $arrConnectInfo['http_code'];
        if (!$strRet || $intErrno != 0 || $intHttpCode != 200) {
            Bingo_Log::warning('fail to call portrait img upload service, user_id=' . $intUserId
                . ', errno=' . $intErrno . ', errmsg=' . $strErrMsg . ', http_code=' . $intHttpCode);
            $this->printOut(Tieba_Errcode::ERR_RPC_CALL_FAIL, 'call portrait img upload service fail');
            return;
        }

        $arrRet = json_decode($strRet, true);
        if ($arrRet['errno'] != 0) {
            Bingo_Log::warning('portrait img upload service return error, user_id=' . $intUserId
                . ', errno=' . $arrRet['errno'] . ', errmsg=' . $arrRet['errmsg']);
            $this->printOut(Tieba_Errcode::ERR_RPC_CALL_FAIL, $arrRet['errmsg']);
            return;
        }

        $this->printOut(Tieba_Errcode::ERR_SUCCESS, 'success');
    }

}
