<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2017/9/29
 * Time: 14:47
 */
class setVideoReportConfigAction extends Util_Action {

    protected $bolOnlyAccessAmis = false;

    /**
     * 子类必须实现
     * @return [type] [description]
     */
    public function _execute()
    {
        $strKey = strtolower(trim((string)Bingo_Http_Request::get('key', '*')));
        $strConfig = Util_Format::trim_json((string)Bingo_Http_Request::getNoXssSafe('config', ''));
        if (empty($strKey) || (!empty($strConfig) && !Bingo_String::json2array($strConfig))) {
            $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, 'param invalid', $strConfig);
            return;
        }

        $objWordList = Wordserver_Wordlist::factory();
        $arrKeys = array($strKey);
        $arrValues = $objWordList->getValueByKeys($arrKeys, Util_Def::VIDEO_REPORT_CONFIG_WORDLIST_FULL_NAME);
        if (!isset($arrValues[$strKey]) && empty($strConfig)) {
            $this->printOut(Tieba_Errcode::ERR_SUCCESS, 'success');
        }

        $arrParams['wl_item_info'] = array(
            'key'                 => $strKey,
            'value'               => $strConfig,
            'op_username'         => Util_User::$strUserName,
            'expired_time'        => 0,
            'memo'                => '',
            'app_name'            => Util_Def::VIDEO_REPORT_CONFIG_APP_NAME,
            'wordlist_name'       => Util_Def::VIDEO_REPORT_CONFIG_WORDLIST_NAME,
            'app_token'           => Util_Def::VIDEO_REPORT_CONFIG_APP_TOKEN,
        );

        if (!isset($arrValues[$strKey])) {
            $strMethod = 'addWLItem';
        } else if (empty($strConfig)) {
            $strMethod = 'delWLItem';
        } else{
            $strMethod = 'modifyWLItem';
        }
        $arrRet = Tieba_Service::call('wordlist', $strMethod, $arrParams, null, null, 'post', 'php', 'utf-8');
        if (!$arrRet) {
            Bingo_Log::warning("call service wordlist::$strMethod fail");
            $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service fail');
            return;
        }
        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning("service wordlist::$strMethod return error, params=".json_encode($arrParams).', ret='
                .json_encode($arrRet));
            $this->printOut($arrRet['errno'], $arrRet['errmsg']);
            return;
        }

        $this->printOut(Tieba_Errcode::ERR_SUCCESS, 'success');
    }

}
