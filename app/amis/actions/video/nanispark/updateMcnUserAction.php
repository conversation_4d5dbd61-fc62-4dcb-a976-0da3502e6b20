<?php
/**
 * Created by PhpStorm.
 * User: yumingrui01
 * Date: 2018/10/18
 * Time: 下午3:55
 */

class updateMcnUserAction extends Util_Action {

    protected $bolNeedLogin = false;
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = true;

    /**
     * @brief 执行入口
     * @param null
     * @return
     * */
    public function _execute()
    {
        $arrParam = array(
            'user_id'       => intval(Bingo_Http_Request::get('user_id')),
            'nani_id'       => intval(Bingo_Http_Request::get('nani_id')),
            'status'        => intval(Bingo_Http_Request::get('status')),
            'type'          => strval(Bingo_Http_Request::get('type')),
            'intro_org'     => strval(Bingo_Http_Request::get('intro_org')),
            'intro_time'    => intval(Bingo_Http_Request::get('intro_time')),
            'pre_level'    => strval(Bingo_Http_Request::get('pre_level')),
            //'op_uid'   => Util_User::$intUserId,
            //'op_name' => Util_User::$strUserName,
        );
        $arrRet = Tieba_Service::call('nani', 'updateNaniMcnMember', $arrParam, null, null, 'post', 'php', 'utf-8');

        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call nani:addSparkPool failed,input:[%s],output:[%s]",serialize($arrParam),serialize($arrRet)));
            return $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return $this->printOut(Tieba_Errcode::ERR_SUCCESS,'添加成功',$arrRet['data']);
    }
}