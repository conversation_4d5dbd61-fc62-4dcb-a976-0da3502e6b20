<?php
/**
 * Created by PhpStorm.
 * User: yumingrui01
 * Date: 2018/9/19
 * Time: 20:31
 */

class addPoolSparkAction extends Util_Action {

    protected $bolNeedLogin = false;
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = true;

    /**
     * @brief 执行入口
     * @param null
     * @return
     * */
    public function _execute()
    {
        $arrParam = array(
            'behavior_type' => strval(Bingo_Http_Request::get('behavior_type')),
            'pool_spark' => intval(Bingo_Http_Request::get('spark')),
            'op_uid'   => Util_User::$intUserId,
            'op_name' => Util_User::$strUserName,
        );
        $arrRet = Tieba_Service::call('nani', 'addSparkPoolSpark', $arrParam, null, null, 'post', 'php', 'utf-8');

        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call nani:addSparkPool failed,input:[%s],output:[%s]",serialize($arrParam),serialize($arrRet)));
            return $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return $this->printOut(Tieba_Errcode::ERR_SUCCESS,'添加成功',$arrRet['data']);
    }
}