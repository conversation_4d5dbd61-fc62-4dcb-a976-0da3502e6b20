<?php
/**
 * <AUTHOR>
 * @date 20180628
 */
define('BINGO_ENCODE_LANG', 'UTF-8');
class listUnionListInfoAction extends Util_Action {
    

    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;


    private static $_arrParamsRequired = array(
        'pn'          => array('type' => 'int',    'default' => 1,           ),
        'rn'          => array('type' => 'int',    'default' => 20,          ),
    );
    private static $_arrParamsOptional = array(//支持操作人和分类名的search
        'start_time'   => array('type' => 'int', ),
        'end_time'     => array('type' => 'int', ), 
        'union_name'   => array('type' => 'string', ),
    );
    private $_arrParams = array();

    private static $_strOutFormat = 'json';
  

    /**
     * @param
     * @return [type] [description]
     */
    public function initParam(){
        self::$_strOutFormat = Bingo_Http_Request::getNoXssSafe('format', 'json');
        $this->_arrParams = array();
        // parse required fields
        foreach(self::$_arrParamsRequired as $k => $v){
            $this->_arrParams[$k] = Bingo_Http_Request::getNoXssSafe($k, $v['default']);
        }

        // parse optional fields
        foreach(self::$_arrParamsOptional as $k => $v){
            $tempParam = Bingo_Http_Request::getNoXssSafe($k, -1);
            if(-1 != $tempParam){
                $this->_arrParams[$k] = $tempParam;
            }
        }
        // end: parse optional
        
        return true;
    }
    /**
     * @param
     * @return [type] [description]
     */
    public function _execute(){
        if(!$this->initParam()){
            Bingo_Log::warning('param error');
            return $this->errRet(-1, 'param error');
        }
        $arrInput = $this->_arrParams;
        $strService = 'video';

        $strMethod = 'getUnionTotalInfo4Mis';

        if(isset($arrInput['union_name'])){
            $arrInput['union_name'] = Bingo_Encode::convert($arrInput['union_name'] ,Bingo_Encode::ENCODE_UTF8 ,Bingo_Encode::ENCODE_GBK);
        }

        $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null, null,'post','php', 'utf-8');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call $strService::$strMethod failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }       

        $arrRet  = array(
            'rows'  => $arrOutput['list'],
            'count' => $arrOutput['total'],
        );
        return $this->errRet(0, 'success', $arrRet);
    }

    
    /**
     * @param $no
     * @param $error
     * @param array $data
     * @return array
     */
    public function errRet($no, $error='', $data = array()) {
        $arrRet = array(
            'status' => $no,
            'msg' => Bingo_Encode::convert(empty($error)?Tieba_Error::getErrmsg($no):$error,
                Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8),
            'data' => $data,
        );
        echo Bingo_String::array2json($arrRet);
        return true;
    }

}