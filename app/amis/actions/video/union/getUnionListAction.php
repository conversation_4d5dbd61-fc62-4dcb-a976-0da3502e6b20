<?php
/**
 * @Desc: 获取已入驻工会列表概要
 * <AUTHOR>
 * @date 201807
 */
define('BINGO_ENCODE_LANG', 'UTF-8');
class getUnionListAction extends Util_Action {

    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;
    
    private static $_arrStatusMap = array(
        'test'    => 3,
        'online'  => 1,
        'offline' => 2,
    );
    private static $_arrTimeTypes = array(
        'create_time',
        'enable_time',
        'expire_time',
        'update_time',
    );
    private static $_arrSearchTypes = array(
      //  'forum_name'  => array('type' => 'string','convert' => 'getStatusMap'),
    );
    private static $_arrParamsRequired = array(
        //'status'      => array('type' => 'string', 'default' => 'online', 'convert' => 'getWidgetMap'),
        'pn'          => array('type' => 'int',    'default' => 1,        ),
        'rn'          => array('type' => 'int',    'default' => 20,       ),
    );
    private static $_arrParamsOptional = array(
        'union_name'       => array('type' => 'string', ),
       // 'status'           => array('type' => 'int', ),
        //'op_name'          => array('type' => 'string',),
    );
    private $_arrParams = array();

    private static $_strOutFormat = 'json';
    private $_strOderBy = '';
    private $_strOderDir = '';
    const DOWNLOAD_FILENAME = 'union_list_';
    private static $_arrExcelHead = array(
        'union_name'       => '公会名称',
        'member_num'       => '达人数',
        'video_num'        => '总视频数',
        'enter_time'       => '入驻时间',
        'status'           => '状态',
    );

    /**
     * @param
     * @return [type] [description]
     */
    public function initParam(){
        self::$_strOutFormat = Bingo_Http_Request::getNoXssSafe('format', 'json');
        $this->_arrParams = array();
        // parse required fields
        foreach(self::$_arrParamsRequired as $k => $v){
            $this->_arrParams[$k] = Bingo_Http_Request::getNoXssSafe($k, $v['default']);
            self::adaptType($this->_arrParams[$k],$v['type']);
            //$ret = call_user_method($v['convert'], $this, $arrParams[$k]);
        }
        // parse optional fields
        foreach(self::$_arrParamsOptional as $k => $v){
            $tempParam = Bingo_Http_Request::getNoXssSafe($k, -1);
            self::adaptType($tempParam,$v['type']);
            if(-1 != $tempParam){
                $this->_arrParams[$k] = $tempParam;
            }
        }
        // end: parse optional
        
        // parse search value 
        $this->_strSearchType = Bingo_Http_Request::getNoXssSafe('search_type',-1);
        $this->_strSearchValue = Bingo_Http_Request::getNoXssSafe('search_value',-1);
        if( -1 !== $this->_strSearchType 
        &&  isset(self::$_arrSearchTypes[$this->_strSearchType]) 
        &&  -1 !== $this->_strSearchValue){
            $arrValueType = self::$_arrSearchTypes[$this->_strSearchType];
            self::adaptType($this->_strSearchValue,$arrValueType['type'], $arrValueType['default'],$arrValueType['convert']);
            if(-1 == $this->_strSearchValue){
                $this->_strSearchType = -1;
            }
        }else{
            $this->_strSearchType = -1;
        }
        if( -1 !== $this->_strSearchType){
            $this->_arrParams[$this->_strSearchType] = $this->_strSearchValue;
        }
        // end: parse search

        // parse time info
        $this->_strTimeType = Bingo_Http_Request::getNoXssSafe('time_type', -1);
        $this->_intStartTime = (int)Bingo_Http_Request::getNoXssSafe('start_time', -1);
        $this->_intEndTime = (int)Bingo_Http_Request::getNoXssSafe('end_time', -1);

        if(-1 !== $this->_strTimeType && isset(self::$_arrTimeTypes)){
            if(-1 !== $this->_intStartTime){
                $this->_arrParams[$this->_strTimeType]['mine'] = $this->_intStartTime;
            }
            if(-1 !== $this->_intEndTime){
                $this->_arrParams[$this->_strTimeType]['maxe'] = $this->_intEndTime;
            }
        }
        // end : parse time info
        
        $this->_strOderBy = Bingo_Http_Request::getNoXssSafe('orderBy', '');
        if('' !== $this->_strOderBy){
            $this->_strOderDir = Bingo_Http_Request::getNoXssSafe('orderDir', 'desc');
            $this->_arrParams['orderby'] = array(
                'field' => $this->_strOderBy,
                'sort'  => $this->_strOderDir,
            );
        }
        //Bingo_Log::warning(print_r($this->_arrParams,1));
        return true;
    }
    /**
     * @param
     * @return [type] [description]
     */
    public function _execute(){
        if(!$this->initParam()){
            Bingo_Log::warning('param error');
            return $this->errRet(-1, 'param error');
        }
        $arrInput = $this->_arrParams;
        $strService = 'video';
        //获取工会单详细页面mis
        $strMethod = 'getEnteredUnionOutlineInfo4Mis';
        if('excel' == self::$_strOutFormat){
            $strMethod = 'downloadEnteredUnionOutlineInfo4Mis';
            unset($arrInput['pn']);
            unset($arrInput['rn']);
        }
        if(isset($arrInput['union_name'])){
            $arrInput['union_name'] = Bingo_Encode::convert($arrInput['union_name'] ,Bingo_Encode::ENCODE_UTF8 ,Bingo_Encode::ENCODE_GBK);
        }
        //$arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput);
        $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null, null,'post','php', 'utf-8');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call $strService::$strMethod failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrList = $arrOutput['data']['rows'];
        //Bingo_Log::warning(print_r($arrList,1));
      //  $arrList = self::joinMore($arrList);
        if('excel' == self::$_strOutFormat){
            $arrList = $arrOutput['data']['list'];
            return self::outExcel($arrList);
        }
        $intCount = (int)$arrOutput['data']['count'];
        $arrRet = array(
            'rows' => $arrList,
            'count' => $intCount,
            'total_people' => (int)$arrOutput['data']['total_people'],
            'total_union'  => (int)$arrOutput['data']['total_union'],
        );
        return $this->errRet(0, 'success', $arrRet);
    }
  
    /**
     * [joinMore description]
     * @param  [type] $arrList [description]
     * @return [type]          [description]
     * | id | user_id    | game_id | status | op_uname | op_time | op_uid |
     */
    private static function joinMore($arrList){
        $arrFids = array();
        $arrTids = array();
        foreach($arrList as $k => $item){
            $arrUids []= (int)$item['create_user_id'];
            $arrTids []= (int)$item['topic_id'];
        }
        $arrUids = array_unique($arrUids);
        $arrTids = array_unique($arrTids);

        $arrUsers = Util_Userhandle::getUnameByUids($arrUids);
        if(false === $arrUsers){
            Bingo_Log::warning('get uname failed');
            return array();
        }
        $arrUmap = array();
        foreach($arrUsers as $k => $v){
            $arrUmap[(int)$v['user_id']] = $v['user_name'];
        }
        $arrTmap = Util_Videoactive_Topic::getAllTopic(1);
        if(false === $arrTmap){
            Bingo_Log::warning('get game failed');
            //return array();
        }
        foreach($arrList as $k => $item){
            $ext = $item['ext'];
            //Bingo_Log::warning($ext);
            $ext = Bingo_Encode::convert($ext, Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);

            //Bingo_Log::warning($ext);
            $ext = unserialize($ext);
            $ext = Bingo_Encode::convert($ext, Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);


            //Bingo_Log::warning(print_r($ext,1));
            $arrList[$k]['ext'] = $ext;
            foreach($ext as $key => $value){
                $arrList[$k]['ext_'.$key] = $value;
            }
            //$item['activity_describe'] = urldecode($item['activity_describe']);

            //$item['activity_describe'] = Bingo_Encode::convert($item['activity_describe'], Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);

            //$arrList[$k]['activity_describe'] = $item['activity_describe'];
            $intUid = (int)$item['user_id'];
            $strUname = $arrUmap[$intUid];
            //$strFname = Bingo_Encode::convert($strFname, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
            $arrList[$k]['user_name'] = $strUname;

            $intTid = (int)$item['topic_id'];
            $strTname = $arrTmap[$intTid]['name'];
            //$strTname = Bingo_Encode::convert($strTname, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
            $arrList[$k]['topic_name'] = $strTname;

            $arrList[$k]['enable_time_str'] = date('Ymd H:i:s',$item['enable_time']);
            $arrList[$k]['expire_time_str'] = date('Ymd H:i:s',$item['expire_time']);
            $arrList[$k]['update_time_str'] = date('Ymd H:i:s',$item['update_time']);
            //$arrList[$k]['create_time_str'] = date('Ymd H:i:s',$item['create_time']);
        }


        return $arrList;
    }
    /**
     * [outExcel description]
     * @param  [type] $arrData [description]
     * @return [type]          [description]
     */
    private static function outExcel($arrData){
        $strFilename = self::DOWNLOAD_FILENAME . date('Ymd',time()).'.csv';
        header("Content-Type: application/vnd.ms-excel; charset=GBK");
        header("Content-Disposition: attachment; filename=\"" . $strFilename);
        header('Cache-Control: max-age=0');
        $fp = fopen('php://output', 'a');
        $head= array();
        foreach (self::$_arrExcelHead as $k => $v) {  
            // CSV的Excel支持GBK编码，一定要转换，否则乱码  
            $head[] = Bingo_Encode::convert($v, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8); 
        }
        $row = array();
        fputcsv($fp, $head);
        foreach($arrData as $input){
            $line = array();
            foreach(self::$_arrExcelHead as $k => $v){
                $line []= Bingo_Encode::convert($input[$k], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
            }
            fputcsv($fp, $line);
        } 
        return true;
    }
    
    /**
     * @param $no
     * @param $error
     * @param array $data
     * @return array
     */
    public function errRet($no, $error='', $data = array()) {
        $arrRet = array(
            'status' => $no,
            'msg' => Bingo_Encode::convert(empty($error)?Tieba_Error::getErrmsg($no):$error,
                Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8),
            'data' => $data,
        );
        echo Bingo_String::array2json($arrRet);
        return true;
    }
    /**
     * [adaptType description]
     * @param  [type] &$value  [description]
     * @param  [type] $type    [description]
     * @param  [type] $convert [description]
     * @return [type]          [description]
     */
    private static function adaptType(&$value,$type='int',$default=-1,$convert=''){
        switch($type){
            case 'int':
                $value = (int)$value;
                if($value <= 0){
                    $value = -1;
                } 
                break;
            case 'uint':
                $value = (int)$value;
                if($value < 0){
                    $value = -1;
                } 
                break;
            case 'stringGBK':
                $value = Bingo_Encode::convert(trim($value), Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
                if($value == ''){
                    $value = -1;
                }
                break;
            case 'string':
                if($value == ''){
                    $value = -1;
                }
                break;
            default :
                break;
        }
    }

}