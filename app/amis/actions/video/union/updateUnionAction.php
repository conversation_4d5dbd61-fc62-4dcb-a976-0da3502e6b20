<?php
/**
 * <AUTHOR>
 * @date 20180424
 */

define('BINGO_ENCODE_LANG', 'UTF-8');
class updateUnionAction extends Util_Action {

    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;
    const UNION_AUDIT_STATUS_REJECT = 1; //审核未通过
    const UNION_AUDIT_STATUS_PASS = 2; //审核通过，未入驻

    private static $_arrParamsRequired = array(
        'union_id'            => array('type' => 'int',       'default' => 0,  ),
        'status'              => array('type' => 'int',       'default' => 0,  ),
        'union_name'          => array('type' => 'string',    'default' => '', ),
        'contact_phone'       => array('type' => 'string',    'default' => '', ),
    );

    /**
     * @param
     * @return [type] [description]
     */
    private function initParam(){
        $this->_intOpUid   = Util_User::$intUserId;
        $this->_strOpUname = Util_User::$strUserName;

        $arrParamConf = self::$_arrParamsRequired;

        $param = array();
        foreach($arrParamConf as $field => $conf){
            $param[$field] = Bingo_Http_Request::getNoXssSafe($field, $conf['default']);
            //  self::adaptType($param[$field], $conf['type'], $conf['default'], $conf['convert']);
        }
        $this->_arrParam  = $param;

        if(empty($this->_arrParam)){
            Bingo_Log::warning('empty param');
            return false;
        }
        return true;
    }

    /**
     * [process description]
     * @param  [type] $arrInupt [description]
     * @return [type]           [description]
     */
    public function _execute(){

        if(!$this->initParam()){
            Bingo_Log::warning("param error");
            $this->errRet(-1, '参数错误');
            return false;
        }

        if ($this->_arrParam['status'] != self::UNION_AUDIT_STATUS_PASS
            && $this->_arrParam['status'] != self::UNION_AUDIT_STATUS_REJECT){
            return $this->errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        $strService = 'video';
        $strMethod = 'updateUnionInfo';

        $arrInput = array(
            'op_name' => $this->_strOpUname,
            'op_uid'  => $this->_intOpUid,
            'status'  => $this->_arrParam['status'],
            'op_time' => time(),
            'union_id'=> $this->_arrParam['union_id'],
        );

        $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null,null,'post','php', 'utf-8');

        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call $strService::$strMethod failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $strMsg = "很抱歉的通知你，『 %s 』资质未通过审核，请登录公会后台查看。" ;
        if($this->_arrParam['status'] == self::UNION_AUDIT_STATUS_PASS){
            $strMsg = "恭喜你申请的『 %s 』资质已经通过审核，欢迎入驻伙拍小视频。请前往公会后台 - 提交成员列表中添加达人进行入驻审核。\n请特别注意：\n 1. 添加的达人需要关注你的伙拍账号；\n 2. 达人同意邀请之后，才能进入后台审核。\n伙拍小视频陪你合力打造流量小生，一起成长。" ;
        }
        $union_name = Bingo_Encode::convert($this->_arrParam['union_name'], Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
        $strCompleteMsg = sprintf($strMsg , $union_name);
        $bolSmsRet = Sms_Msg::sendMsg(array($this->_arrParam['contact_phone']) , $strCompleteMsg);
        if(!$bolSmsRet){
            Bingo_Log::warning("call service SmsRet::sendMsg error! input= $this->_arrParam['contact_phone']");
        }

        return $this->errRet(Tieba_Errcode::ERR_SUCCESS);
    }
    /**
     * @param $no
     * @param $error
     * @param array $data
     * @return array
     */
    public function errRet($no, $error='', $data = array()) {
        $arrRet = array(
            'status' => $no,
            'msg' => Bingo_Encode::convert(empty($error)?Tieba_Error::getErrmsg($no):$error,
                Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8),
            'data' => $data,
        );
        echo Bingo_String::array2json($arrRet);
        return true;
    }

}
