<?php
/**
 * Desc: 达人审核工单列表
 * User: l<PERSON><PERSON><PERSON>@baidu.com
 * Date: 201807
 */

define('BINGO_ENCODE_LANG', 'UTF-8');
class getMemberCenterAction extends Util_Action
{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    private static $_arrParamsRequired = array(
        'pn'          => array('type' => 'int',    'default' => 1,           ),
        'rn'          => array('type' => 'int',    'default' => 20,          ),

     /*   'nick_name'   => array('type' => 'string', 'default' => '',          ),
        'category_id' => array('type' => 'string', 'default' => '',          ),
        'video_num'   => array('type' => 'int',    'default' => 0,          ),
        'good_num'    => array('type' => 'int',    'default' => 0,          ),
        'remark'      => array('type' => 'string', 'default' => '',          ),
     */
    );
    private static $_arrParamsOptional = array(//支持操作人和分类名的search
        'member_id'   => array('type' => 'int',    'default' => 0,          ),
        'nani_id'     => array('type' => 'int',  ),
        'cmonth'       => array('type' => 'int', ),
    );

    private static $_arrStatusMap = array(
        'test'    => 3,
        'online'  => 1,
        'offline' => 2,
    );
    private static $_arrTimeTypes = array(
        'create_time',
        'enable_time',
        'expire_time',
        'update_time',
    );
    private static $_arrSearchTypes = array(
        //  'forum_name'  => array('type' => 'string','convert' => 'getStatusMap'),
    );

    private $_arrParams = array();

    private static $_strOutFormat = 'json';
    private $_strOderBy = '';
    private $_strOderDir = '';

    /**
     * @param
     * @return [type] [description]
     */
    public function initParam(){
        self::$_strOutFormat = Bingo_Http_Request::getNoXssSafe('format', 'json');
        $this->_arrParams = array();
        // parse required fields
        foreach(self::$_arrParamsRequired as $k => $v){
            $this->_arrParams[$k] = Bingo_Http_Request::getNoXssSafe($k, $v['default']);
            self::adaptType($this->_arrParams[$k],$v['type']);
            //$ret = call_user_method($v['convert'], $this, $arrParams[$k]);
        }
        // parse optional fields
        foreach(self::$_arrParamsOptional as $k => $v){
            $tempParam = Bingo_Http_Request::getNoXssSafe($k, -1);
            self::adaptType($tempParam,$v['type']);
            if(-1 != $tempParam){
                $this->_arrParams[$k] = $tempParam;
            }
        }
        // end: parse optional

        // parse search value
        $this->_strSearchType = Bingo_Http_Request::getNoXssSafe('search_type',-1);
        $this->_strSearchValue = Bingo_Http_Request::getNoXssSafe('search_value',-1);
        if( -1 !== $this->_strSearchType
            &&  isset(self::$_arrSearchTypes[$this->_strSearchType])
            &&  -1 !== $this->_strSearchValue){
            $arrValueType = self::$_arrSearchTypes[$this->_strSearchType];
            self::adaptType($this->_strSearchValue,$arrValueType['type'], $arrValueType['default'],$arrValueType['convert']);
            if(-1 == $this->_strSearchValue){
                $this->_strSearchType = -1;
            }
        }else{
            $this->_strSearchType = -1;
        }
        if( -1 !== $this->_strSearchType){
            $this->_arrParams[$this->_strSearchType] = $this->_strSearchValue;
        }

        $this->_strTimeType = Bingo_Http_Request::getNoXssSafe('time_type', -1);
        $this->_intTime = (int)Bingo_Http_Request::getNoXssSafe('time', -1);
        //$this->_intEndTime = (int)Bingo_Http_Request::getNoXssSafe('end_time', -1);

        if(-1 !== $this->_strTimeType && isset(self::$_arrTimeTypes)){
            if(-1 !== $this->_intTime){
                $this->_arrParams['cmonth']= $this->_intTime;
            }
        }
        // end: parse search


        // end : parse time info

        $this->_strOderBy = Bingo_Http_Request::getNoXssSafe('orderBy', '');
        if('' !== $this->_strOderBy){
            $this->_strOderDir = Bingo_Http_Request::getNoXssSafe('orderDir', 'desc');
            $this->_arrParams['orderby'] = array(
                'field' => $this->_strOderBy,
                'sort'  => $this->_strOderDir,
            );
        }
        //Bingo_Log::warning(print_r($this->_arrParams,1));
        return true;
    }

    /**
     * @param
     * @return [type] [description]
     */
    public function _execute(){
        if(!$this->initParam()){
            Bingo_Log::warning('param error');
            return $this->errRet(-1, 'param error');
        }

        $arrInput = $this->_arrParams;
        // member video
        if(empty($arrInput['cmonth'])){
            $arrInput['cmonth'] =  date("Ym");
        }
        $strService = 'video';
        if(!empty($arrInput['nani_id'])) {
            $strMethod = 'getUserDataByNaniId';
            $arrNaniId = array(
                'nani_id' => $arrInput['nani_id'],
            );

            $arrOutput = Tieba_Service::call($strService, $strMethod, $arrNaniId, null, null, 'post', 'php', 'utf-8');
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
                Bingo_Log::warning(sprintf("call $strService::$strMethod failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
                return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            if(!empty($arrOutput['data'])) {
                $intUserId = $arrOutput['data'][0]['user_id'];
                $strMethod = 'getMemberIdByUserId';
                $arrNaniId = array(
                    'user_id' => $intUserId,
                );

                $arrOutput = Tieba_Service::call($strService, $strMethod, $arrNaniId, null, null, 'post', 'php', 'utf-8');
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
                    Bingo_Log::warning(sprintf("call $strService::$strMethod failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
                    return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
                if(!empty($arrOutput['data'])) {
                    $arrInput['member_id'] = $arrOutput['data'][0]['member_id'];
                }
            }
        }
        if(empty($arrInput['member_id']) || empty($arrInput['cmonth'])){
            $arrRet = array(
                'memberInfo' => array(),
                'rows' =>  array(),
                'count' => 0,
            );
            return $this->errRet(0, Tieba_Errcode::ERR_SUCCESS, $arrRet);
        }

        $strMethod = 'mgetMemberInfoByMid';
        $intMemberId = $arrInput['member_id'] ;
        $intPn = $arrInput['pn'] ;
        $intRn = $arrInput['rn'] ;
        $arrInput['member_id'] = array($intMemberId);

        //$arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput);
        $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null, null,'post','php', 'utf-8');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call $strService::$strMethod failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrMid2Info = $arrOutput['data'];

        $intUnionId = $arrOutput['data'][$intMemberId]['union_id'];

        $arrInput = array(
            'mask' => 0,
            'member_id' => $intMemberId,
            'cmonth' => $arrInput['cmonth'],
            'union_id' => $intUnionId,
        );
        $strMethod = 'getMemberAccountList';

        $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null, null,'post','php', 'utf-8');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call $strService::$strMethod failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrMemInfo = array(
            'nick_name'   => $arrMid2Info[$intMemberId]['nick_name'],
            'nani_id'     => $arrOutput['data'][0]['nani_id'],
            'category_id' => $arrMid2Info[$intMemberId]['category_id'],
            'video_num'   => $arrOutput['data'][0]['video_num'],
            'good_num'    => $arrOutput['data'][0]['good_num'],
            'remark'      => $arrOutput['data'][0]['remark'],
        );
        $arrInput = array(
            'member_id' => intval($arrInput['member_id']),
            'cmonth' => $arrInput['cmonth'],
            'pn' => $intPn,
            'rn' => $intRn,
        );
        $arrRet = Tieba_Service::call('video', 'getMemberVideoList', $arrInput, null, null, 'post', 'php', 'utf-8');
        if(false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
            Bingo_Log::warning(sprintf('service call failed! service=[%s], input=[%s], output=[%s]', 'video::getMemberVideoList', serialize($arrInput), serialize($arrRet)));
            return $this->errRet(-1, Tieba_Errcode::ERR_CALL_SERVICE_FAIL, $arrRet);
        }
        $arrData = $arrRet['data'];
        if(empty($arrData) || $arrData['total'] <= 0){
            $arrRet = array(
                'memberInfo' => array(),
                'rows' =>  array(),
                'count' => 0,
            );
            return $this->errRet(0, Tieba_Errcode::ERR_SUCCESS, $arrRet);
        }
        $arrThreadInfo = $this->getThreadInfo($arrData['list']);

        $arrRet = array(
            'memberInfo' => $arrMemInfo,
            'rows' => $arrThreadInfo,
            'count' => $arrData['total'],
        );
        return $this->errRet(0, 'success', $arrRet);
    }

    /**
     * [joinMore description]
     * @param  [type] $arrList [description]
     * @return [type]          [description]
     * | id | user_id    | game_id | status | op_uname | op_time | op_uid |
     */
    private static function joinMore($arrList){

        foreach($arrList as $k => $value){
            $arrList[$k]['edit_process']  = $value['demo_edit_num'].' / '.$value['demo_num'];
            $arrList[$k]['audit_process'] = $value['audit_num']. ' / '.$value['accept_num'];
        }

        return $arrList;
    }

    /**
     * @param $no
     * @param $error
     * @param array $data
     * @return array
     */
    public function errRet($no, $error='', $data = array()) {
        $arrRet = array(
            'status' => $no,
            'msg' => Bingo_Encode::convert(empty($error)?Tieba_Error::getErrmsg($no):$error,
                Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8),
            'data' => $data,
        );
        echo Bingo_String::array2json($arrRet);
        return true;
    }
    /**
     * [adaptType description]
     * @param  [type] &$value  [description]
     * @param  [type] $type    [description]
     * @param  [type] $convert [description]
     * @return [type]          [description]
     */
    private static function adaptType(&$value,$type='int',$default=-1,$convert=''){
        switch($type){
            case 'int':
                $value = (int)$value;
                if($value <= 0){
                    $value = -1;
                }
                break;
            case 'uint':
                $value = (int)$value;
                if($value < 0){
                    $value = -1;
                }
                break;
            case 'stringGBK':
                $value = Bingo_Encode::convert(trim($value), Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
                if($value == ''){
                    $value = -1;
                }
                break;
            case 'string':
                if($value == ''){
                    $value = -1;
                }
                break;
            default :
                break;
        }
    }


    /**
     * @param  [type] $list [description]
     * @return [type] [description]
     */
    private function getThreadInfo($arrThreadList){
        if(empty($arrThreadList)){
            return array();
        }
        $arrTids = array();
        foreach($arrThreadList as $val){
            $arrTids[] = $val['thread_id'];
        }
        $arrInput = array(
            'thread_ids' => $arrTids,
            'need_abstract' => 0,
            'forum_id' => 0,
            'need_photo_pic' => 0,
            'need_user_data' => 0,
            'icon_size' => 0,
            'need_forum_name' => 0, //是否获取吧名
            'call_from' => 'pc_frs', //上游模块名
        );
        $arrOutput = Tieba_Service::call('post', 'mgetThread', $arrInput, null, null, 'post', 'php', 'utf-8');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf('call post::mgetThread failed input=[%s], output=[%s]', serialize($arrInput), serialize($arrOutput)));
            return false;
        }
        if(empty($arrOutput['output']['thread_list'])){
            return array();
        }
        $arrThread = $arrOutput['output']['thread_list'];
        foreach($arrThreadList as &$val){
            $arrVideoInfo = $arrThread[$val['thread_id']]['video_info'];
            $val['title'] = $arrThread[$val['thread_id']]['title'];
            $val['thumbnail_url'] = $arrVideoInfo['thumbnail_url'];
            $val['video_url'] = $arrVideoInfo['video_url'];
            $val['video_play'] = $arrThread[$val['thread_id']]['video_play']['count'];
            $val['agree_num'] = $arrThread[$val['thread_id']]['agree_num'];
            $val['create_time'] = $arrThread[$val['thread_id']]['create_time'];
        }
        return $arrThreadList;
    }
}
