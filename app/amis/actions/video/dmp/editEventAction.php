<?php
/**
 * Created by PhpStorm.
 * User: xiangwenchao
 * Date: 2018/10/21
 * Time: 10:26 PM
 */

class editEventAction extends Util_Action {

    /**
     * @brief 执行入口
     * @param null
     * @return
     * */
    public function _execute() {

        $strFunnelId = Bingo_Http_Request::get('id', '');
        $strBrief  = Bingo_Http_Request::get('brief', '');
        $strIds    = Bingo_Http_Request::get('action_ids', '');

        if(empty($strFunnelId) || empty($strBrief) || empty($strIds)){
            return $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrParam = array(
            'op_uid'     => Util_User::$intUserId,
            'op_uname'    => Util_User::$strUserName,
            'funnel_id'  => $strFunnelId,
            'brief'      => $strBrief,
            'action_ids' => $strIds,
        );

        Bingo_Log::warning(var_export($arrParam, true));

        $arrRet = Tieba_Service::call('nani', 'editFunnel', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call nani::addFunnel service failed. input: [' . serialize($arrParam) . ']; output: [' . serialize($arrRet) . ']');
            return $this->printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return $this->printOut(Tieba_Errcode::ERR_SUCCESS);
    }
}