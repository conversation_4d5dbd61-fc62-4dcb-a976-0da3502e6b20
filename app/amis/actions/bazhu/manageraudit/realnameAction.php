<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-01-28 23:04:49
 * @version
 */
class realnameAction extends Util_Action{
	protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

	/**
	 * [init description]
	 * @param [type] $[name] [<description>]
	 * @return [type] [description]
	 */
	// function init(){
	// 	self::setUiAttr('BROWSE_UI');
	// 	if (false === parent::init()){
	// 		if(0 === $this->_intErrorNo){
	// 			$this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
	// 			$this->_strErrorMsg = "init return error!";
	// 			Bingo_Log::warning($this->_strErrorMsg);
	// 		}
	// 	}
	// 	return true;
	// }

	/**
	 * [_execute description]
	 * @param [type] $[name] [<description>]
	 * @return [type] [description]
	 */
	public function _execute(){       

		$arrInput = array();

		//search_what
		$name 		  = trim(Bingo_Http_Request::getNoXssSafe("name",''));
		$id 	 	  = trim(Bingo_Http_Request::getNoXssSafe('id',''));
		$directory 	  = trim(Bingo_Http_Request::getNoXssSafe('directory',''));

		$name  = Bingo_Encode::convert($name, Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
		$directory  = Bingo_Encode::convert($directory, Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);

		$arrInput = array();
		$arrInput['name'] = $name;
		$arrInput['id'] = $id;
		$arrInput['directory'] = $directory;
		$arrInput['op_uname']  = Util_User::$strUserName;
		
		$arrRet  = self::$MisService->call("fmanagerpower","realname",$arrInput);

		$this->_arrTplVar = $arrRet['result'];
		Bingo_Page::getView()->setOnlyDataType("json");
		Bingo_Page::setTpl("bazhushenhe.php");///home/<USER>/tieba-odp/template/mis/control/airborne.php
        return true;

	}


}

?>