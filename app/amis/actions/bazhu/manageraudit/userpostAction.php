<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file userpostAction.php
 * <AUTHOR>
 * @date 2015/11/19 20:38:11
 * @brief
 *
 **/
class UserpostAction extends Util_Action
{
    const DEFAULT_PN = 1;
    const DEFAULT_SZ = 20;
    private $_intPn;
    private $_intSz;
    private $_intUid;
    private $_intFid;
    private $_intDelType;
    private $_errMap;
    private $_arrPostIds;
    private $_intTime;
    private $_intStartTime;
    private $_intEndTime;
    private $_intApplyId;
    private $_intAuditId;
    private $_strOpUname = '';
    private $_intOpTime =0;
    private static $_status_map = array(
        1 => 'pass',
        2 => 'mark',
    );

    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;
    /**
     * [init description]
     * @return [type] [description]
     */
    // public function init(){
    //     self::setUiAttr('BROWSE_UI');
    //     if (false === parent::init()) {
    //         if (0 === $this->_intErrorNo) {
    //             $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
    //             $this->_strErrorMsg = "init return error!";
    //             Bingo_Log::warning($this->_strErrorMsg);
    //         }
    //     }
    //     $this->_errMap = array();
    //     return true;
    // }
    /**
     * [errRet description]
     * @param  integer $errno [description]
     * @param  [type]  $data  [description]
     * @return [type]         [description]
     */
    private function errRet($errno = 0,$msg = 'success',$data = array()){
        $ret = array(
            'no'     => $errno,
            'errmsg' => $msg,
            'data'   => $data,
        );
        echo Bingo_String::array2json($ret,Bingo_Encode::ENCODE_GBK);
        return;
    }
    /**
     * [_initParam description]
     * @return [type] [description]
     */
    private function _initParam(){
        $this->_intPn      = intval(Bingo_Http_Request::getNoXssSafe('pn', self::DEFAULT_PN));
        $this->_intSz      = intval(Bingo_Http_Request::getNoXssSafe('sz', self::DEFAULT_SZ));
        $this->_intFid     = intval(Bingo_Http_Request::getNoXssSafe('fid', 0));
        $this->_intUid     = intval(Bingo_Http_Request::getNoXssSafe('uid', 0));
        $this->_intDelType = intval(Bingo_Http_Request::getNoXssSafe('delete_type', 2));
        $this->_intOrder   = intval(Bingo_Http_Request::getNoXssSafe('order', 1));
        $this->_intThread  = intval(Bingo_Http_Request::getNoXssSafe('is_thread', 2));
        $this->_intStartTime = (int)Bingo_Http_Request::get('start_time', -1);
        $this->_intEndTime = (int)Bingo_Http_Request::get('end_time', -1);
        $this->_arrPostIds = Bingo_Http_Request::getNoXssSafe('post_ids','');
        $this->_intApplyId = (int)Bingo_Http_Request::get('apply_id', -1);
        $this->_intAuditId = (int)Bingo_Http_Request::get('audit_id', -1);
        $this->_intAuditStatus = 1;
        if(!in_array($this->_intDelType, array(0,1,2)) || !in_array($this->_intOrder, array(0,1)) || !in_array($this->_intThread, array(0,1,2))){
           $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR);
           return false;
        }
        $strUsername  = Bingo_Http_Request::getNoXssSafe('user_name', '');
        if('' !== $strUsername){
            $this->_intUid = $this->_getUserid($strUsername);
        }
        $strForumname  = Bingo_Http_Request::getNoXssSafe('forum_name', '');
        if('' !== $strForumname){
            $this->_intFid = $this->_getForumid($strForumname);
        }
        return true;
    }
    /**
     * [process description]
     * @return [type] [description]
     */
    public function _execute(){
        if(!$this->_initParam()){
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR,'param error');
            return false;
        }

        if(-1 === $this->_intApplyId){
            $arrPageInfo = $this->_getPostCount();
        }else{
            $arrPageInfo = array(
                'total_count' => 20,
                'total_pn'    => 1,
                'current_pn'  => 1,
            );
        }

        if(-1 === $this->_intApplyId){
            $this->_getUserPost();
        }else{
            if(-1 === $this->_intAuditId){
                $arrPns = $this->_getUnAuditPns();
                if(false === $arrPns){
                    return false;
                }
                if(empty($arrPns)){
                    $strMsg = Bingo_Encode::convert('已经审完',"GBK","UTF-8");
                    return $this->errRet(-1,'已经审完');
                }
                $this->_intPn = (int)current($arrPns);
                $this->_getUserPost();
            }else {
                $this->_getAuditInfo();
            }
        }

        if(empty($this->_arrPostIds)){
            return $this->errRet(-1, 'no pids');
        }

        $arrList = $this->_getPostsByPids();
        $arrList['list'] = $this->_joinMore($arrList['list']);
        $arrRet = array(
            'list' => $arrList['list'],
            'page' => $arrPageInfo,
            'real_pn' => $this->_intPn,
        );
        return $this->errRet(Tieba_Errcode::ERR_SUCCESS,'success',$arrRet);
    }
    /**
     * [_joinMore description]
     * @param  [type] $arrList [description]
     * @return [type]          [description]
     */
    private function _joinMore($arrList){
        if(empty($arrList)){
            return $arrList;
        }
        $strStatus = self::$_status_map[$this->_intAuditStatus];
        foreach($arrList as $k => $v){
            $arrList[$k]['result'] = $strStatus;
            $arrList[$k]['op_user_name'] = $this->_strOpUname;
            $arrList[$k]['op_time'] = $this->_intOpTime;
        }
        return $arrList;
    }
    /**
     * [_getUnAuditPn description]
     * @param
     * @return [type] [description]
     */
    private function _getUnAuditPns(){
        $arrInput = array(
            'apply_id' => (int)$this->_intApplyId,
        );
        $arrOutput = Tieba_Service::call('managerapply','getSpecialApplyInfo',$arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call managerapply::getSpecialApplyInfo failed input[%s] output[%s]",serialize($arrInput),serialize($arrOutput)));
            $this->errRet($arrOutput['errno'],'请刷新');
            return false;
        }

        $ext = $arrOutput['output']['ext'];
        $ext = Bingo_Encode::convert($ext, 'UTF-8','GBK');
        $ext = unserialize($ext);
        $pns = $ext['pns'];


        $arrOutput = Tieba_Service::call('managerapply', 'getMarkInfo',$arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call managerapply::getSpecialApplyInfo failed input[%s] output[%s]",serialize($arrInput),serialize($arrOutput)));
            $this->errRet($arrOutput['errno'],'请刷新');
            return false;
        }
        $arrAuditPns = array();
        foreach($arrOutput['output'] as $k => $v){
            $arrAuditPns[] = $v['pn'];
        }
        foreach($pns as $k => $v){
            if(in_array($v,$arrAuditPns)){
                unset($pns[$k]);
            }
        }
        return $pns;
    }
    /**
     * [_getAuditInfo description]
     * @param
     * @return [type] [description]
     */
    private function _getAuditInfo(){
        $arrInput = array(
            'audit_id' => (int)$this->_intAuditId,
        );
        $arrOutput = Tieba_Service::call('managerapply', 'getMarkInfo',$arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call managerapply::getSpecialApplyInfo failed input[%s] output[%s]",serialize($arrInput),serialize($arrOutput)));
            $this->errRet($arrOutput['errno'],'请刷新');
            return false;
        }
        $audit = $arrOutput['output'][0];
        $this->_intAuditStatus = $audit['status'];
        $this->_strOpUname = $audit['op_user_name'];
        $this->_intOpTime = $audit['op_time'];
        $ext = unserialize($audit['ext']);
        $this->_arrPostIds = $ext['pid'];
        return true;
    }
    /**
     * [_getPostsByPids description]
     * @param
     * @return [type] [description]
     */
    private function _getPostsByPids(){
        $arrDataList = array();
        $arrUserInfo = array();
        $arrPageInfo = array();
        $arrPostIds = $this->_arrPostIds;//explode("_", $this->_arrPostIds);
        if(empty($arrPostIds)){
            $arrRet = array(
                'list' => $arrDataList,
                'page' => $arrPageInfo,
            );
            Bingo_Log::warning("empty post_ids");
            $this->errRet(Tieba_Errcode::ERR_SUCCESS,$arrRet);
            return false;
        }
        $arrInput = array(
            'post_ids' => $arrPostIds,
        );
        $arrOutput = Service_Manageraudit_Manageraudit::getPostByPids($arrInput);
        // $arrOutput = self::$MisService->call('manageraudit','getPostByPids',$arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call mis-manageraudit::getPostByPids failed input[%s] output[%s]",serialize($arrInput),serialize($arrOutput)));
            $arrDataList = array();
        }else{
            $arrDataList = $arrOutput['data'];
        }
        foreach($arrDataList as $k => $v){
            $arrDataList[$k]['result'] = self::$_status_map[$this->_intAuditStatus];
        }
        $arrRet = array(
            'list' => $arrDataList,
        );
        return $arrRet;
    }
    /**
     * [_getUserPost description]
     * @param
     * @return [type] [description]
     */
    private function _getUserPost(){
        $arrDataList = array();
        $arrUserInfo = array();
        $arrPageInfo = array();
        $arrInput = array(
            'user_id'  => $this->_intUid,
            'offset'   => $this->_intSz * ($this->_intPn - 1),
            'res_num'  => $this->_intSz,
            'delete_type' => $this->_intDelType,
            'order_type'  => $this->_intOrder,
        );
        if(2 !== $this->_intThread){
            $arrInput['is_thread'] = $this->_intThread;
        }
        if(0 !== $this->_intFid){
            $arrInput['forum_id'] = $this->_intFid;
        }
        if($this->_intStartTime > 0){
            $arrInput['begin_time'] = $this->_intStartTime;
        }
        if($this->_intEndTime > 0){
            $arrInput['end_time'] = $this->_intEndTime;
        }
        // $arrOutput = self::$MisService->call('manageraudit','getUserPostIds',$arrInput);
        $arrOutput = Service_Manageraudit_Manageraudit::getUserPostIds($arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call mis-manageraudit::getUserPostIds failed input[%s] output[%s]",serialize($arrInput),serialize($arrOutput)));
        }
        $arrPids = $arrOutput['data'];  
        $this->_arrPostIds = $arrPids;
        return true;
    }
    /**
     * [_getPostCount description]
     * @param
     * @return [type] [description]
     */
    private function _getPostCount(){
        $arrPageInfo = array();
        if(0 !== $this->_intFid){
            $arrInput = array(
                'user_id'  => $this->_intUid,
                'forum_id' => $this->_intFid,
            );
            $strMethod = 'queryUserForumCount';
        }else{
            $arrInput = array(
                'user_id' => $this->_intUid,
            );
            $strMethod = 'queryUserCount';
        }
        $arrOutput = Tieba_Service::call('post',$strMethod,$arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            $strMsg = sprintf('call service failed [%s]input[%s]output[%s]','post::queryUserCount', serialize($arrInput),serialize($arrOutput));
            Bingo_Log::warning($strMsg);
            return $arrPageInfo;
        }
        $arrUserInfo = $arrOutput['count'];
        $intTotal = $arrUserInfo['thread_num'] + $arrUserInfo['post_num'];
        if($intTotal > 20000){
            $intTotal = 20000;
        }
        $arrPageInfo = array(
            'total_count' => $intTotal,
            'total_pn'    => ceil($intTotal / $this->_intSz),
            'current_pn'  => $this->_intPn,
        );
        return $arrPageInfo;
    }
    /**
     * @param $strUname
     * @return bool
     */
    private static function _getUserid($strUname) {
        $arrInput = array(
            'user_name' => array($strUname),
        );
        $arrOutput = Tieba_Service::call('user', 'getUidByUnames', $arrInput);
        if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('get uid error');
            return 0;
        }
        return (int)$arrOutput['output']['uids'][0]['user_id'];
    }
    /**
     * @param $arrForumIds
     * @return bool
     */
    private static function _getForumid($strFname) {
        
        $arrInput = array(
            'query_words' => array($strFname),
        );
        $arrOutput = Tieba_Service::call('forum', 'getFidByFname', $arrInput);
        if (false === $arrOutput||$arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call forum getFnameByFid fail input[' . serialize($arrInput) . "] out[" . serialize($arrOutput) . "]");
            return $arrOutput;
        }
        $intFid = (int)$arrOutput['forum_id'][0]['forum_id'];
        $fname  = $arrOutput['forum_id'][0]['forum_name'];
        $qw     = $arrOutput['forum_id'][0]['qword'];
        
        return $intFid;
    }
    
}





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
