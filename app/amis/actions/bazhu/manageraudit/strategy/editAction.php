<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file editAction.php
 * <AUTHOR>
 * @date 2016/11/08 19:14:56
 * @brief 
 *  
 **/
class editAction extends Util_Action
{
	private $_intUserId;
    private $_strUserName;
    private $_arrStrategyList;

    private static $_arrDirs = array(
        'high_risk_dir',
        'low_risk_dir',
    );
    private static $_arrVars = array(
        'PostNum',
        'Pns',
    );
    private static $_arrOps = array(
        'GE',
        'LE',
        'GT',
        'LT',
        'EQ',
        'NE',
        'SP',
    );
	/**
     * @return bool
     */
    // public function init() {
    //     self::setUiAttr('COMMIT_UI');
    //     if (false === parent::init()) {
    //         if (0 === $this->_intErrorNo) {
    //             $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
    //             $this->_strErrorMsg = "init return error!";
    //             Bingo_Log::warning($this->_strErrorMsg);
    //         }
    //     }
    //     return true;
    // }

    /**
     * @return bool
     */
    private function _initParam() {
        $this->_intUserId = Util_Actionbaseext::getCurUserId();
        $this->_strUserName = Util_Actionbaseext::getCurUserName();
        $arrStrategys = Bingo_Http_Request::getNoXssSafe('rows', 0);
        foreach($arrStrategys as $k => $v){
        	$this->_arrStrategyIds[]= (int)$v['strategy_id'];
            if(!in_array($v['dir'],self::$_arrDirs)){
                Bingo_Log::warning('param error: dir'.$this->_strDir);
                return false;
            }
            if(!in_array($v['var'],self::$_arrVars)){
                Bingo_Log::warning('param error: var'.$this->_strVar);
                return false;
            }
            if(!in_array($v['operator'],self::$_arrOps)){
                Bingo_Log::warning('param error: op'.$this->_strOp);
                return false;
            }
            if('SP' !== $v['operator']){
                $strStandard = (int)$v['standard'];
                if($strStandard<=0){
                    Bingo_Log::warning('param error: standard'.$this->_strStandard);
                    return false;
                }
            }
            $arrMgrNum = $v['manager_num'];
            if(empty($arrMgrNum)){
                Bingo_Log::warning('param error: manager_num'.serialize($arrMgrNum));
                return false;
            }
            $intMgrNum = 0;
            foreach($arrMgrNum as $mgrnum){
                $intMgrNum = (int)$mgrnum;
                $intMgrNum |= (1 << $intMgrNum);
            }
            $arrStrategys[$k]['manager_num'] = $intMgrNum;
        }
        $this->_arrStrategyList = $arrStrategys;
        return true;
    }

    /**
     * @return bool
     */
    public function _execute() {
        $this->_initParam();
        $objRalMulti = new Tieba_Multi('manageraudit_strategyedit');
        foreach($this->_arrStrategyList as $k => $v){
            $strMethod = 'editStrategy';
            $arrInput = array(
                'service_name' => 'managerapply',
                'method'       => $strMethod,
                'input'        => array(
                    'strategy_id' => $v['strategy_id'],
                    'dir' => $v['dir'],
                    'manager_num' => $v['manager_num'],
                    'min_mem' => $v['min_mem'],
                    'max_mem' => $v['max_mem'],
                    'var' => $v['var'],
                    'operator' => $v['operator'],
                    'standard' => $v['standard'],
                    'op_user_name' => $this->_strUserName,
                ),
                'ie' => 'utf-8',
            );
            $objRalMulti -> register('mgra_stredit_'.$k, new Tieba_Service('managerapply'), $arrInput);
        }
        $objRalMulti -> call();

        $arrFail = array();
        $arrSucc = array();
        foreach($this->_arrStrategyList as $k => $v){
            $arrOutput = $objRalMulti -> getResult('mgra_stredit_'.$k);
            if (!$arrOutput || !isset($arrOutput['errno']) || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("call service managerapply editStrategy fail, input = " . serialize($v) . "; output = " . serialize($arrOutput));
                $arrFail[] = $v['strategy_id'];
            }else{
                $arrSucc[] = $v['strategy_id'];

            }
        }
        if(!empty($arrFail)){
            return $this->errRet(-1,'some failed',$arrFail);
        }
        return $this->errRet(0,'success',array());
    }
    /**
     * [errRet description]
     * @param  [type] $no    [description]
     * @param  [type] $error [description]
     * @param  array  $data  [description]
     * @return [type]        [description]
     */
    public function errRet($no, $error, $data = array()) {
        $arrOutput =  array('no' => $no, 'errmsg' => $error, 'data' => $data);
        echo Bingo_String::array2json($arrOutput);
        return true;
    }

}





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
