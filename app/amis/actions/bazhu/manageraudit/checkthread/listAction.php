<?php

/**
 * Created by PhpStorm.
 * User: 
 * Date: 2018/1/27
 * Time: 11:04
 */
class listAction extends Util_Action
{
    const PAGE_PER_NUM = 20;

    private $_intStatus;
    private $_strFirstLevel;
    private $_strSecondLevel;
    private $_intRankStart;
    private $_intRankEnd;
    private $_strStrategy;
    private $_intIsThread;
    private $_intStartTime;
    private $_intEndTime;
    private $_intPn;
    private $_intSz;

    private static $_arrThreadIds = array();
    private static $_arrPostIds = array();

    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;

    /**
     * @return bool
     */
    // public function init() {
    //     self::setUiAttr('BROWSE_UI');
    //     if (false === parent::init()) {
    //         if (0 === $this->_intErrorNo) {
    //             $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
    //             $this->_strErrorMsg = "init return error!";
    //             Bingo_Log::warning($this->_strErrorMsg);
    //         }
    //     }
    //     return true;
    // }

    /**
     * @return bool
     */
    private function _initParam() {
        $this->_intStatus = intval(Bingo_Http_Request::getNoXssSafe('status', 0));
        $this->_intPn = intval(Bingo_Http_Request::getNoXssSafe('pn', 0));
        $this->_intSz = intval(Bingo_Http_Request::getNoXssSafe('sz', 0));
        $this->_intIsThread = intval(Bingo_Http_Request::getNoXssSafe('is_thread', 0));
        $this->_strStrategy = strval(Bingo_Http_Request::getNoXssSafe('strategy', ''));
        $this->_strFirstLevel = strval(Bingo_Http_Request::getNoXssSafe('first_dir', ''));
        $this->_strSecondLevel = strval(Bingo_Http_Request::getNoXssSafe('second_dir', ''));
        $this->_intStartTime = intval(Bingo_Http_Request::getNoXssSafe('startTime', 0));
        $this->_intEndTime = intval(Bingo_Http_Request::getNoXssSafe('endTime', 0));
        $this->_intRankStart = intval(Bingo_Http_Request::getNoXssSafe('rank_from', 0));
        $this->_intRankEnd = intval(Bingo_Http_Request::getNoXssSafe('rank_to', 0));
        $this->_intPacket = intval(Bingo_Http_Request::getNoXssSafe('packet', 0));

        return true;
    }

    /**
     * @return bool
     */
    public function _execute() {

        $this->_initParam();
        $page_count = self::PAGE_PER_NUM;
        if( $this->_intSz > 0 ){
            $page_count = $this->_intSz;
        }
        $arrInput = array(
            'offset' => ($this->_intPn-1) * $page_count,
            'page_count' => $page_count,   
        );

        if (isset($this->_intStatus)) {
            $arrInput['status'] = $this->_intStatus;
        }
        if ( $this->_intIsThread > 0 ) {
            $arrInput['is_thread'] = $this->_intIsThread;
        }
        if ( $this->_intRankStart > 0 ) {
            $arrInput['rank_from'] = $this->_intRankStart;
        }
        if ( $this->_intRankEnd > 0 ) {
            $arrInput['rank_to'] = $this->_intRankEnd;
        }
        
        if (""!==$this->_strFirstLevel) {
            $arrInput['first_dir'] = $this->_strFirstLevel;
        }
        if (""!==$this->_strSecondLevel) {
            $arrInput['second_dir'] = $this->_strSecondLevel;
        }
        if (""!==$this->_strStrategy) {
            $arrInput['strategy'] = $this->_strStrategy;
        }

        if (!empty($this->_intStartTime)) {
            $arrInput['begin_time'] = $this->_intStartTime;
        }else{
            $arrInput['begin_time'] = intval(time() - 86400*7);
        }

        if (!empty($this->_intEndTime)) {
            $arrInput['end_time'] = $this->_intEndTime;
        }else{
            $arrInput['end_time'] = time();
        }
        if( $this->_intPacket > 0 ){
            $arrInput['packet'] = $this->_intPacket;
        }
        $arrOutput = Tieba_Service::call('managerapply', 'getCheckThreadList', $arrInput);
        $intTotal = isset($arrOutput['data']['total']) ? $arrOutput['data']['total'] : 0;

        unset($arrOutput['data']['total']);
        $arrDataList =  array();
        //需要查额外一些内容
        if( !empty( $arrOutput['data'] ) ){
            $arrDataList = self::_joinMoreInfo($arrOutput['data']);
        

            if( $arrDataList == false ){
                $arrOutput = $this->errRet(0, 'get list fail');
                echo Bingo_String::array2json($arrOutput);
                return false;
            }
        }

        $data = array(
            'list' => $arrDataList,
            'page' => array(
                'current_pn' => $this->_intPn,
                'total_pn' => ceil($intTotal / $page_count),
                'total_count' => $intTotal,
            ),
        );

        $arrOutput = $this->errRet(0, 'get catalog list success', $data);
        echo Bingo_String::array2json($arrOutput);
        return true;
    }

    

    /**
     * @param $arrInfo
     * @return mixed
     */
    private static function _convertTpl($arrInfo) {
        return $arrInfo;
    }

    /**
     * @param $arrInfo
     * @return mixed
     */
    private static function _joinMoreInfo($arrInfo) {
        $arrRet = array();
        foreach( $arrInfo as $key => $item ){
            $arrUids[] = intval($item['delete_uid']);
            $arrUids[] = intval($item['user_id']);
            //删除的是回复           
            if( intval($item['is_thread']) == 2 ){
                self::$_arrPostIds[] = intval($item['post_id']);
            }
            self::$_arrThreadIds[] = intval($item['thread_id']);           
        }

        $objMulti = new Molib_Tieba_Multi('manager_checkthread');
        if ( !is_object($objMulti) ){
            Bingo_Log::warning('init multi service object for [manager] failure.');
            return false;
        }
        //删贴人信息      
        $arrInputUser = array(
            "user_id" => $arrUids,
        );
        $arrMultiInput = array(
            'serviceName'   => 'user',
            'method'        => 'mgetUserData',
            'ie'            => 'utf-8',
            'input'         => $arrInputUser,
        );
        $objMulti->register('mgetUserData',$arrMultiInput,null);
            
        //贴子详情      
        $arrInputThread = array(
            "thread_ids" => self::$_arrThreadIds,
            "need_abstract" => 1,
            "forum_id" => 59099,
            "need_photo_pic" => 1,
            "need_user_data" => 0,
            "need_forum_name" => 0,
            "call_from" => "mis" //上游模块名
        );
        $arrMultiInput = array(
            'serviceName'   => 'post',
            'method'        => 'mgetThread',
            'ie'            => 'utf-8',
            'input'         => $arrInputThread,
        );
        $objMulti->register('mgetThread',$arrMultiInput,null);
        //回复详情
        if( !empty(self::$_arrPostIds) ){
            $arrInputPost = array(
                "post_ids" => self::$_arrPostIds,
            );
            $arrMultiInput = array(
                'serviceName'   => 'post',
                'method'        => 'getPostInfo',
                'ie'            => 'utf-8',
                'input'         => $arrInputPost,
            );
            $objMulti->register('getPostInfo', $arrMultiInput,null);
        }
        
        
        $strUip = Bingo_Http_Ip::getUserClientIp('0.0.0.0');
        $intUip = intval(Bingo_Http_Ip::ip2long($strUip));
         
        foreach( $arrInfo as $key => $item ){
            $intFid = intval($item['forum_id']);
            $intManagerId = intval($item['delete_uid']);
            $arrInputManager = array(
                'forum_id' => $intFid,
            );
            //吧角色
            $arrMultiInput = array(
                'serviceName'   => 'perm',
                'method'        => 'getManagerAndAssistList',
                'ie'            => 'utf-8',
                'input'         => $arrInputManager,
            );
            $objMulti->register('getManagerAndAssistList'.$intFid,  $arrMultiInput,null);

            $arrInputLevel = array(
                'user_id' => intval($item['user_id']),
                'forum_ids' => array(
                    $intFid,
                ),
            );
            //用户等级
            $arrMultiInputLevel = array(
                'serviceName'   => 'perm',
                'method'        => 'mgetUserLevel',
                'ie'            => 'utf-8',
                'input'         => $arrInputLevel,
            );
            $objMulti->register('mgetUserLevel'.$intFid.intval($item['user_id']), $arrMultiInputLevel,null);      

            //一周内删贴、删回复总数  
            $arrInputBawu = array(
                'forum_id' => $intFid,
                'user_id' => $intManagerId,
                'start_time' => time() - 86400*7,
                'end_time' => time(),
                'cmd_core_no' => array(12,11), //11为删回复，12为删主题
            );
            $arrMultiInputBawu = array(
                'serviceName'   => 'bawu',
                'method'        => 'getRecordCnt',
                'ie'            => 'utf-8',
                'input'         => $arrInputBawu,
            );
            $objMulti->register('getRecordCnt'.$intFid.$intManagerId, $arrMultiInputBawu,null);
        
        }
       
        $objMulti->call();

        $arrMultiOutput = $objMulti->getAllResult();

        $arrOutputThread = $arrMultiOutput['mgetThread'];
        $arrOutputUser =  $arrMultiOutput['mgetUserData'];
        $arrOutputPost =  array();

        if( !empty(self::$_arrPostIds) ){
            $arrOutputPost =  $arrMultiOutput['getPostInfo'];
            if( false == $arrOutputPost || $arrOutputPost['errno'] != Tieba_Errcode::ERR_SUCCESS ){
                Bingo_Log::warning('call post getPostInfo fail input[' . serialize($arrInputPost) . "] out[" . serialize($arrOutputPost) . "]");
                return false;
            }
        }

        if( false == $arrOutputThread || $arrOutputThread['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('call post mgetThread fail input[' . serialize($arrInputThread) . "] out[" . serialize($arrOutputThread) . "]");
            return false;
        }
        
        if( false == $arrOutputUser || $arrOutputUser['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('call user mgetUserData fail input[' . serialize($arrInputUser) . "] out[" . serialize($arrOutputUser) . "]");
            return false;
        }

        $arrPostList =  array();
        foreach( $arrOutputPost['output'] as $key => $value){
            $arrPostList[intval($value['post_id'])] = $value;
        }

        $arrThreadList = $arrOutputThread['output']['thread_list'];


        foreach( $arrInfo as $key => $item ){
            $fid = intval($item['forum_id']);
            $uid = intval($item['user_id']);
            $del_uid = intval($item['delete_uid']);

            $temp = $item;
            $temp['title'] = $arrThreadList[$item['thread_id']]['title'] ;
            $temp['abstract'] = $arrThreadList[$item['thread_id']]['abstract'];
            $temp['first_pic'] = $arrThreadList[$item['thread_id']]['raw_abstract_media'][0]['small_pic'];
            $temp['add_time'] = $arrThreadList[$item['thread_id']]['create_time'];
            if( intval($item['is_thread']) == 2 ){
                $temp['post_content'] = $arrPostList[$item['post_id']]['content'];
                $temp['post_title'] = $arrPostList[$item['post_id']]['title'];
            }
            
            $arrOutputCnt = $arrMultiOutput['getRecordCnt'.$fid.$del_uid];
            if( false == $arrOutputCnt || $arrOutputCnt['errno'] != Tieba_Errcode::ERR_SUCCESS ){
                Bingo_Log::warning('call bawu getRecordCnt fail input[' . serialize($item) . "] out[" . serialize($arrOutputCnt) . "]");
                return false;
            }
            $temp['del_count'] = intval($arrOutputCnt['data']['cnt']);

            $arrOutputLevel = $arrMultiOutput['mgetUserLevel'.$fid.$uid];
            if( false == $arrOutputLevel || $arrOutputLevel['errno'] != Tieba_Errcode::ERR_SUCCESS ){
                Bingo_Log::warning('call perm mgetUserLevel fail input[' . serialize($item) . "] out[" . serialize($arrOutputLevel) . "]");
                return false;
            }
            $temp['level'] = intval($arrOutputLevel['score_info']['level_id']);

            $arrOutputManager = $arrMultiOutput['getManagerAndAssistList'.$fid];
            if( false == $arrOutputManager || $arrOutputManager['errno'] != Tieba_Errcode::ERR_SUCCESS ){
                Bingo_Log::warning('call perm getManagerAndAssistList fail input[' . serialize($item) . "] out[" . serialize($arrOutputManager) . "]");
                return false;
            }
            $arrManagerList = array();
            $arrAssistList = array();
            foreach( $arrOutputManager['output']['manager'] as $user ){
                $arrManagerList[$user['user']['user_id']]  = $user['user']['update_time'];
            }
            foreach( $arrOutputManager['output']['assist'] as $user ){
                $arrAssistList[$user['user']['user_id']]  = $user['user']['update_time'];
            }
            if( isset($arrManagerList[$del_uid]) ){
                $temp['role'] = 'manager';
                $temp['begin_time'] =  $arrManagerList[$del_uid];
            }else if( isset($arrAssistList[$del_uid]) ){
                $temp['role'] = 'assist';
                $temp['begin_time'] =  $arrAssistList[$del_uid];
            }else{
                $temp['role'] = 'other';
                $temp['begin_time'] = '';
            }
            $temp['delete_uname'] = strval($arrOutputUser['user_info'][$del_uid]['user_name']);
            $temp['uname'] = strval($arrOutputUser['user_info'][$uid]['user_name']);
            
            $arrRet[] = $temp;

        }  
        return $arrRet;
    }

    /**
     * @param $no
     * @param $error
     * @param array $data
     * @return array
     */
    public function errRet($no, $error, $data = array()) {
        return array('no' => $no, 'error' => $error, 'data' => $data);
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
