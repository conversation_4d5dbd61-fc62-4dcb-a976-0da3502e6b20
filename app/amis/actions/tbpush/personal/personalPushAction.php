<?php
class personalPushAction extends Util_Action {

	/**
	 * 主逻辑
	 * @param
	 * @return
	 */
	public function _execute(){
        $intDate    = intval(Bingo_Http_Request::getNoXssSafe('push_date', 0));
        $intRow     = intval(Bingo_Http_Request::getNoXssSafe('push_row', 0));
		$intOpUid   = intval(Util_User::$intUserId);
		$intNowDate = date('Ymd');
		$intNowTime = time();
		if (empty($intDate) || !isset($intRow) || empty($intOpUid)) {
			Bingo_Log::warning('personalPushAction param error, date[' . $intDate . '], row[' . $intRow . '], uid[' . $intOpUid . ']');
			$this->jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
			return false;
		}
		if ($intDate < $intNowDate) {
			Bingo_Log::warning('personalPushAction param error, input_date need to be more than than now, input date[' . $intDate . '], now[' . $intNowDate . ']');
			$this->jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, 'add_date[' . $intDate . '] need to be more than now_date[' . $intNowDate . ']');
			return false;
		}
		if ($intDate > $intNowDate) {
			Bingo_Log::warning('personalPushAction param error, input_date need to be less than than now, input date[' . $intDate . '], now[' . $intNowDate . ']');
			$this->jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, 'add_date[' . $intDate . '] need to be less than now_date[' . $intNowDate . ']');
			return false;
		}
		
		$strKey = 'push_switch_' . $intDate;
		$input  = array(
            'key'      => $strKey,
        );
        $output = Tieba_Service::call('tbpush', 'getRedisData', $input, null, null, 'post', null, 'utf-8');
        if (!$output || $output['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('call tbpush::getRedisData fail, input:[' . serialize($input) . '],output:[' . serialize($output) . ']');
            $this->jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return false;
		}
		$arrValue = json_decode($output['data'], true);
		if (!empty($output['data']) && is_array($arrValue) && isset($arrValue[$intRow])) {
			$arrRow = $arrValue[$intRow];
			$strMsg = "日期:" . $intDate . "   列:" . $intRow . " 已添加";
			$this->jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, $strMsg);
            return true;
		}
		
		$arrTemp = array(
			$intRow => array(
				'row' 	 => $intRow,
				'status' => 0,
				'time'	 => $intNowTime,
				'op_user'=> $intOpUid,
			),
		);
		$strKey 	= 'push_switch_' . $intDate;
		$strValue	= json_encode($arrTemp);
        $input = array(
            'key'       => $strKey,
            'value'   	=> $strValue,
        );
        $output = Tieba_Service::call('tbpush', 'setRedisData', $input, null, null, 'post', null, 'utf-8');
        if (!$output || $output['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call tbpush::setRedisData fail, input:[' . serialize($input) . '],output:[' . serialize($output) . ']');
            $this->jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return false;
		}

		$input  = array(
			'push_index'=> 'ftp://cp01-forum-dm00.cp01.baidu.com/home/<USER>/tbdc/data/tmp_uid/' . date('Ymd', strtotime($intDate) - 86400 * 2) . '_tieba_uid',
		);
        $output = Tieba_Service::call('tbpush', 'getData', $input, null, null, 'post', null, 'utf-8');
        if (!$output || $output['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('call tbpush::getData fail, input:[' . serialize($input) . '],output:[' . serialize($output) . ']');
            $this->jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return false;
		}
		$intId = intval($output['data'][0]['id']);
		if ($intId > 0) {
			$input  = array(
				'id'		=> $intId,
				'op_record' => $arrTemp[$intRow],
			);
			$output = Tieba_Service::call('tbpush', 'setDataRecord', $input, null, null, 'post', null, 'utf-8');
			if (!$output || $output['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
				Bingo_Log::warning('call tbpush::setDataRecord fail, input:[' . serialize($input) . '],output:[' . serialize($output) . ']');
				$this->jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
				return false;
			}
		}
        
		$this->jsonRet(Tieba_Errcode::ERR_SUCCESS);
		return true;
	}

	/**
	 * 返回结果
	 * @param
	 * @return
	 */
	protected function jsonRet($errno, $strmsg = '', $arrData = ''){
		$arrRet = array(
			'no'    => $errno,
			'error' => !empty($strmsg) ?  $strmsg : Tieba_Error::getErrmsg($errno),
			'data'  => $arrData,
		);
		echo json_encode($arrRet);
	}
}

?>