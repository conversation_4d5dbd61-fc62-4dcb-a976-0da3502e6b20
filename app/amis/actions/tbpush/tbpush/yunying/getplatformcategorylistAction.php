<?php
class getplatformcategorylistAction extends Util_Action
{
    public function _execute(){
        $type = Bingo_Http_Request::get('type', '');
        $ret = Tieba_Service::call("opmsg","getPlatformCategoryList", null, null, null, 'get', null, 'utf-8');
        $data = array();
        $data['options'] = array();
        if ($type == 'search') {
            $option = array(
                "label" => '全部',
                "value" => 0,
            );
            $data['options'][] = $option;
        }
        foreach ($ret['result'] as $category_info) {
            $option = array(
                "label" => $category_info['category_name'],
                "value" => $category_info['category_id'],
            );
            $data['options'][] = $option;
        }
        $result = array(
            "status" => $ret["errno"],
            "msg" => $ret["errmsg"],
            "data" => $data,
        );
        echo json_encode($result);
        return;
    }    
}
?>

