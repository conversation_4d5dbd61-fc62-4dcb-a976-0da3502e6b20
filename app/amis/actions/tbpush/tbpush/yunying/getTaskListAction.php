<?php
class getTaskListAction extends  Util_Action
{
    public function _execute(){
        $pn = Bingo_Http_Request::get('pn', '0');
        $rn = Bingo_Http_Request::get('rn', '10');
        $task_id = Bingo_Http_Request::get('task_id', '');
        $title = Bingo_Http_Request::get('title', '');
        $start_time = Bingo_Http_Request::get('time_begin', '');
        $end_time = Bingo_Http_Request::get('time_end', '');
        $push_title = Bingo_Http_Request::get('push_title', '');
        $category_id = Bingo_Http_Request::get('category_id', '');
        if (intval($rn) > 30) {
            Bingo_Log::warning('Parameter out of limit input:[' . serialize($rn) . ']');
            $result = array(
                'status' => 210009,
                'msg'    => 'param error',
           );
           echo Bingo_String::array2json($result);
           return false;
        }
        $arrInput = array(
           "pn" => $pn,
           "rn" => $rn,
           "start_time" => $start_time,
           "end_time" => $end_time,
           "task_id" => $task_id,
           'title' => $title,
           'push_title' => $push_title,
           'category_id' => $category_id,
        );
        Bingo_Log::notice("opmsg getTaskList, input: ". serialize($arrInput));
        $ret = Tieba_Service::call("opmsg","getTaskList",$arrInput,null,null,'post','php', 'utf-8');
        //通过task_id批量获取topic_id
        foreach ($ret['result'] as $key=>$value){
            $reqInput['task_ids'][] = $value['task_id'];
        }
        $reqOutput = Tieba_Service::call('tbpush','mgetPlatformRelationTaskids',$reqInput,null,null,'post','php','utf-8');
        if (!$reqOutput || $reqOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call tbpush mgetPlatformRelationTaskids fail, input:[' . serialize($value['task_id']) . '],output:[' . serialize($reqOutput) . ']');
        }
        $arrTemp = array();
        foreach ($reqOutput['data'] as $key => $value) {
        $arrTemp[$value['task_id']] = $value;
        }
        foreach( $ret['result'] as &$row) {     
            $push_content = unserialize($row['push_content']);
            $push_account_info = unserialize($row['push_account_info']);
            $row["operator"] = $push_account_info["user_id"];
            foreach($push_content as $key => $value) {
                $content[$key] = base64_decode($value);
            }
            $conditions = json_decode($row['conditions'],true);
            $row['push_content'] = $content;
            $row['task_title']  = $row['task_title'];
            $row['conditions'] = $conditions;
            $row["plan_begin_date"] = date("m/d H:i",$row["plan_begin_time"]);
            $row["plan_end_date"] = date("m/d H:i",$row["plan_end_time"]);
            $row['topic_id'] = $arrTemp[$row['task_id']]['topic_id'];
            $row['category_name'] = "";
        }
        $data = array(
            "rows"  => $ret['result'],
            "count" => $ret['count'],
        );
        $result = array(
            "status" => $ret["errno"],
            "msg"    => $ret["errmsg"],
            "data"   => $data,
        );
        $category_id = array();
        $num = count($result['data']['rows']);
        for ($i = 0; $i < $num; ++$i) {
            if ($result['data']['rows'][$i]['user_type'] == "6" && $result['data']['rows'][$i]['category_id'] != "0")
            {
                $category_id[$i] = $result['data']['rows'][$i]['category_id'];
            }
        }
        $cateidInput = array(
            'category_id'        => $category_id,
        );

        $cateidOutput = Tieba_Service::call("tbpush","getInfoByCateid",$cateidInput,null,null,'post','php','utf-8');
        if (!$cateidOutput || $cateidOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db getInfoByCateid fail, input[' . serialize($cateidInput) . '],output[' . serialize($cateidOutput) . ']');
        }

        $cate_id2cate_name = array();
        foreach ($cateidOutput['results'][0] as $key => $value) {
            $cate_id2cate_name[$value['category_id']] = $value['category_name'];
        }

        for ($i = 0; $i < $num; ++$i) {
            if ($result['data']['rows'][$i]['user_type'] == "6" && $result['data']['rows'][$i]['category_id'] != "0")
            {
                $result['data']['rows'][$i]['category_name'] = $cate_id2cate_name[$result['data']['rows'][$i]['category_id']];
            }
        }
        echo Bingo_String::array2json($result);
        return true;
    }
    
}
?>
