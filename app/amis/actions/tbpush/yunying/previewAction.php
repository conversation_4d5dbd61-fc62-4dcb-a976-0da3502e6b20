<?php
class previewAction extends Util_Action
{
    public function _execute(){
        $intUserId  = Bingo_Http_Request::get('user_id', 0);
        $intGroupId = Bingo_Http_Request::get('group_id', 0);
        $intTaskId  = Bingo_Http_Request::get('task_id', 0);
        $operator =  $_SERVER["HTTP_AMIS_USER"];
        // 参数校验
        if (($intUserId == 0 && $intGroupId == 0) || $intTaskId == 0 || $operator == '') {
            Bingo_Log::warning('params error, user_id:' . $intUserId . ', group_id:' . $intGroupId . ', task_id:' . $intTaskId . ', operator:' . $operator);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 1、获取group_id
        if ($intGroupId == 0) {
            $groupInput = array(
                'user_ids' => array($intUserId),
            );
            $groupOutput = Tieba_Service::call('im', 'queryPlatformGroupByUid', $groupInput, null, null, 'post', 'php', 'utf-8');
            Bingo_Log::notice('call im::queryPlatformGroupByUid fail, input:[' . serialize($groupInput) . '],output:[' . serialize($groupOutput) . ']');
            if (!$groupOutput || $groupOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call im::queryPlatformGroupByUid fail, input:[' . serialize($groupInput) . '],output:[' . serialize($groupOutput) . ']');
                $errno = intval($groupOutput['errno']) > 0 ? intval($groupOutput['errno']) : Tieba_Errcode::ERR_FSTAR_DB_CALL_FAIL;
                return self::errRet($errno);
            }
            $intGroupId = intval($groupOutput['groups'][0]['group_id']);
        }
        if ($intGroupId == 0) {
            Bingo_Log::warning('params error, user_id:' . $intUserId . ', group_id:' . $intGroupId . ', task_id:' . $intTaskId . ', operator:' . $operator);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 2、获取任务详情
        $taskInput = array(
            'task_id' => $intTaskId,
        );
        Bingo_log::notice('previewAction querySubtask params:' . json_encode($taskInput));
        $taskOutput = Tieba_Service::call('opmsg', 'querySubtask', $taskInput, null, null, 'post', 'php', 'utf-8');
        if (!$taskOutput || $taskOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call opmsg::querySubtask fail, input:[' . serialize($taskInput) . '],output:[' . serialize($taskOutput) . ']');
            $errno = intval($taskOutput['errno']) > 0 ? intval($taskOutput['errno']) : Tieba_Errcode::ERR_FSTAR_DB_CALL_FAIL;
            return self::errRet($errno);
        }
        $arrPushContent = unserialize($taskOutput['result']["push_content"]);
        $strTitle = base64_decode($arrPushContent['title']);
        if (!is_utf8($strTitle)) {
            $strTitle = iconv('GBK', 'UTF-8', $strTitle);
        }
        $strAbstract = base64_decode($arrPushContent['abstract']);
        if (!is_utf8($strAbstract)) {
            $strAbstract = iconv('GBK', 'UTF-8', $strAbstract);
        }
        $strUrl         = base64_decode($arrPushContent['url']);
        $strImageUrl    = base64_decode($arrPushContent['src']);
        $arrAccountInfo = unserialize($taskOutput['result']["push_account_info"]);
        $strAccountName = base64_decode($arrAccountInfo['user_name']);
        if (!is_utf8($strAbstract)) {
            $strAccountName = iconv('GBK', 'UTF-8', $strAccountName);
        }

        // 3、添加操作记录日志
        $logInput = array(
            'task_id'       => $intTaskId,
            'operator'      => $operator,
            'operate_time'  => time(),
            'operation'     => 'preview',
        );
        Bingo_log::notice('previewAction addOperateLog params:' . json_encode($logInput));
        $logOutput = Tieba_Service::call('opmsg', 'addOperateLog', $logInput);
        if (!$logOutput || $logOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call opmsg::addOperateLog fail, input:[' . serialize($logInput) . '],output:[' . serialize($logOutput) . ']');
            $errno = intval($logOutput['errno']) > 0 ? intval($logOutput['errno']) : Tieba_Errcode::ERR_FSTAR_DB_CALL_FAIL;
            return self::errRet($errno);
        }

        // 4、推送消息逻辑
        $arrMsgContent = array(
            'title'     => $strTitle,
            'text'      => $strAbstract,
            'url'       => $strUrl,
            'image_url' => $strImageUrl,
        );
        $arrAccountInfo = array(
            'user_id'   =>  strval($arrAccountInfo['user_id']),
            'user_name' =>  $strAccountName,
            'user_type' =>  strval($arrAccountInfo['user_type']),
        );
        $msgInput = array(
            "task_id"             => strval($intTaskId),
            "service_id"          => '97',
            "msglogic_content"    => $arrMsgContent,
            "group_ids"           => array('group_id' => array(strval($intGroupId))),
            "account_info"        => $arrAccountInfo,
            "msglogic_conf"       => array('msg_type' => '7'),
        );
        $msgOutput = Tieba_Service::call('tieba_push', 'pushMsgLogic', $msgInput, null, null, 'post', 'json', 'utf-8');
        Bingo_log::notice('previewAction pushMsgLogic params:' . json_encode($msgInput));
        if (!$msgOutput || $msgOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call tieba_push::pushMsgLogic fail, input:[' . serialize($msgInput) . '],output:[' . serialize($msgOutput) . ']');
            $errno = intval($msgOutput['errno']) > 0 ? intval($msgOutput['errno']) : Tieba_Errcode::ERR_FSTAR_DB_CALL_FAIL;
            return self::errRet($errno);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }  

    private static function errRet($errno, $data = array()) {
        $arrRet = array(
            'status'    => $errno,
            'msg'       => Tieba_Error::getErrmsg($errno),
        );
        if (!empty($data)) {
            $arrRet['data'] = $data;
        }
        echo json_encode($arrRet);
    }
}
?>
