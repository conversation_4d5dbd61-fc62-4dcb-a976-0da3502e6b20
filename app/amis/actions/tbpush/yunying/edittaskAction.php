<?php
class edittaskAction extends Util_Action
{
    public function _execute(){
        $params = Bingo_Http_Request::getPostAllNoXssSafe();
        unset($params["push_content"]);
        unset($params["conditions"]);
        unset($params["push_account_info"]);
        unset($params["log"]);
        $params['url'] =  trim($params['url']);
		Bingo_Log::warning("TRACE_LOG_POST".serialize($params));
        $task_id = Bingo_Http_Request::get('task_id', null);
        $subtask_id = Bingo_Http_Request::get('subtask_id', null);
        if(!isset($task_id)){
            echo '需要传入task_id和subtask_id';
            return;
        }
        //增加判断逻辑，已上线的任务不允许编辑 @zhouzhongtao
        $req = array('task_id' => $task_id);
        $res = Tieba_Service::call('opmsg', 'querySubtask', $req,null,null,'post','php');
        if($res['errno'] === 0){
            $status = $res['result']['subtask_status'];
            if ($status != 0) {
                $errno = Tieba_Errcode::ERR_PROTO_FIELD_CAN_NOT_BE_EDITED;
                $ret = array(
                    "status" => $errno,
                    "msg" => "只允许编辑下线状态的任务！",
                );
                echo json_encode($ret);
                return;
            }
        } else {
            $ret = array(
                "status" => $res['errno'],
                "msg" => $res['errmsg'],
            );
            echo json_encode($ret);
            return;
        }
        $operator =  $_SERVER["HTTP_AMIS_USER"];;
        $priority = Bingo_Http_Request::get('priority', null);
        $size = Bingo_Http_Request::get('size', "0");
        $plan_begin_time = Bingo_Http_Request::get('plan_begin_time', null);
        $plan_end_time = Bingo_Http_Request::get('plan_end_time', null);
        $title = Bingo_Http_Request::get('push_title', null);
        $url = trim(Bingo_Http_Request::get('url', null));
        $abstract = iconv('GBK','UTF-8',Bingo_Http_Request::get('abstract', null));
        $src = Bingo_Http_Request::get('src', null);
        $task_title = iconv('GBK','UTF-8',Bingo_Http_Request::get('task_title', 'test'));
        $user_type = Bingo_Http_Request::get('target_user', null);
        $task_type = Bingo_Http_Request::get('task_type', null);
        $restriction = 0; // 下线强制触达策略, 默认为0 ********
        $push_account = Bingo_Http_Request::get('push_account', '1');
        $push_type = Bingo_Http_Request::get('push_type', '1');
        $intSubtaskId = Bingo_Http_Request::get('subtask_id', '');
        $operator_telephone = Bingo_Http_Request::get('operator_telephone', '');
		$push_app_alias = Bingo_Http_Request::get("push_app","tieba,a0");
		$push_app_alias = explode(",",$push_app_alias);
        $category_id = Bingo_Http_Request::get('category_id');
        $task_flow = Bingo_Http_Request::get('task_flow', '0');
        $push_app = $push_app_alias[0];
		$alias = $push_app_alias[1];
		$subTaskId = base_convert($subtask_id,10,32);
		$priKey = $alias.sprintf("%06s", strval($subTaskId));
        $push_account = explode(",",$push_account);
        //时间限制，预定义用户可以配三天内的任务，请求DC任务可以配一天内任务，计划推送时间比当前时间晚至少一分钟
        $now = time();
        if ($plan_begin_time <= $now) {
            $res = array(
                'status' => 210009,
                'msg' => "计划推送时间必须在当前时间之后",
            );
            echo json_encode($res);
            return;
        }

        if ($plan_begin_time - $now > 259200) {
            $res = array(
                'status' => 210009,
                'msg' => "计划推送时间不能晚于三天后",
            );
            echo json_encode($res);
            return; 
        }
        
        $timer_type = 0;  // 循环周期下线,仅循环一次  ********
		if(empty($title)) {
			$title = iconv('UTF-8','GBK',$_REQUEST["title"]);
		}

		if(empty($abstract)) {
			$abstract = iconv('UTF-8','GBK',$_REQUEST["abstract"]);
		}

        //参数校验
        if(empty($title) || empty($url) || empty($abstract)) {
            $res = array(
                'status' => 210006,
                'msg' => "title,url,abstract can not be empty",
            );
            echo Bingo_String::array2json($res);
            return;
        }
        // title 字数不超过30个
        if (mb_strlen($title, 'gbk') > 30) {
            $res = array(
                'status' => 2102003,
                'msg' => "标题最多输入30个中文字符",
            );
            echo json_encode($res);
            return;
        }

        if (mb_strlen($abstract, 'gbk') > 100) {
            $res = array(
                'status' => 2102003,
                'msg' => "摘要最多输入100个中文字符",
            );
            echo json_encode($res);
            return;
        }
        // 如果plan_begin_time > plan_end_time 返回错误
        if ($plan_begin_time > $plan_end_time) {
            $res = array(
                'status' => 210007,
                'msg' => "wrong plan time",
            );
            echo Bingo_String::array2json($res);
            return;                    
        }
        if($task_type == "") {
            $task_type = 0;
        }
        $conditions = array();
        if($push_app == "tieba") {
			$condition = array(
				'key' => "switch",
				'value' => 'is_push_on',
				'logic' => 'switch',
			);
			if (!empty(Tieba_PushLib_AccountSwitch::$accountSwitchMap[$push_account[0]]["tag_name"]) && $push_account[0] != **********){
				$condition["value"] = $condition["value"].",".Tieba_PushLib_AccountSwitch::$accountSwitchMap[$push_account[0]]["tag_name"];
			}else {
				$condition = array(
                    'key' => "is_push_on",
                    'value' => '0',
                    'logic' => '=',
				);

			}
            $conditions[] = $condition;
		}

        //自定义用户逻辑
        if($user_type=="3") {
            $user_file = Bingo_Http_Request::get("user_file");
			$a = parse_url($user_file);
			if($a["host"] == "amis.cdn.bcebos.com"){
				$a["host"] = "amis.bj.bcebos.com";

				$a["scheme"] = $a["scheme"]."://";
				$user_file = implode($a);
			}
            $active_condition = array(
                'key' => 'user_id',
                'value' => $user_file,
                'logic' => "file",
            );
            $conditions[] = $active_condition;
            if ($size == "1") {
                $condition = array(
                    'key' => 'size',
                    'value' => "1",
                    'logic' => "=",
                );
                $conditions[] = $condition;
            }
        }
        //预定义逻辑
        if ($user_type == "6") {
            $category_condition = array(
                'key' => 'category_id',
                'value' => $category_id,
                'logic' => '=',
            );
            $conditions[] = $category_condition;
        }

        $ext = $params;
        $push_content = serialize(array(
            'title' => base64_encode($title),
            'url' => base64_encode($url),
            'abstract' => base64_encode($abstract),
            'src' => base64_encode($src),
            'text' => base64_encode($abstract),
            'ext' => base64_encode(serialize($ext)),
        ));
        $log_record = array(
            'task_id' => $task_id,
            'operator' => $operator,
            'operate_time' => time(),
            'operation' => 'editSubtask',
        );
        $res = Tieba_Service::call('opmsg','addOperateLog',$log_record);
        //推送账号信息
        $push_account_info = array(
            "user_id" => $push_account[0],
            "user_name" => base64_encode(iconv('GBK','UTF-8',$push_account[1])),
            "user_type" => 4,
            "group_type" => 30,
        );
        $push_account_info = serialize($push_account_info);
        $arrInput = array(
            'default' => "1",
            'task_id' => $task_id,
            'subtask_id' => $intSubtaskId,
            'pri_key' => $priKey,
            'operator' => $operator,
            'telephone' => $operator_telephone,
            'priority' => $priority,
            'conditions' => Bingo_String::array2json($conditions),
            'push_content' => $push_content,
            'plan_begin_time' => $plan_begin_time,
            'plan_end_time' => $plan_end_time,
            'user_type' => $user_type,
            'task_title' => $task_title,
            'task_type' => $task_type,
            'push_type' => $push_type,
            'restriction' => $restriction,
            'push_account_info' => $push_account_info,
            'push_title' => iconv('GBK','UTF-8', $title),
            'category_id' => $category_id,
        );
        if (intval($task_flow) == 1) {
            $arrInput['task_flow'] = 1;
        }
        if($push_app == 'tieba' || $push_app == 'lite') {
            $arrInput['app_type'] = $push_app;
        }
		Bingo_Log::warning("MIS_TRACE call opmsg editSubtask".json_encode($arrInput));
        $res = Tieba_Service::call('opmsg', 'editSubtask', $arrInput,null,null,'post','php','utf-8');
        $ret = array(
            "status" => $res['errno'],
            "msg" => $res['errmsg'],
        );
        echo json_encode($ret);
    }
}

?>
