<?php
class modifyDlvryCfgItemStatusAction extends Util_Action{
	
	/**
	 * (non-PHPdoc)
	 * @see Util_Action::_execute()
	 * @param null
	 * @return null
	 */
	public function _execute(){
		$cfgid = intval(Bingo_Http_Request::get('cfg_id',0));
		$status = intval(Bingo_Http_Request::get('status', 0));
		
		if($cfgid <= 0){
			Bingo_Log::warning('modifyDlvryCfgItemStatusAction invalid param.cfgResid:['.serialize($cfgid).']');
			return $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR, '请选择有效的数据。');
		}
		$intOpUid = intval(Util_User::$intUserId);
		$strOpUname = strval(Util_User::$strUserName);
		if($intOpUid <= 0 || empty($strOpUname)){
			Bingo_Log::warning('modifyDlvryCfgItemStatusAction invalid login.OpUid:['.$intOpUid.'] opuname:['.$strOpUname.']');
			return $this->printOut(Tieba_Errcode::ERR_USER_NOT_LOGIN, '用户未登录，请先登录。');
		}
		
		$arrInput = array(
			'cfg_id' => $cfgid,
			'status' => $status,
			'op_uid' => $intOpUid,
			'op_name' => $strOpUname,
		);
		$arrOut = Tieba_Service::call('content', 'modifyCfgItemStatus', $arrInput, null, null, 'post', 'php', 'utf-8');
		if(false == $arrOut || !isset($arrOut['errno'])
				|| Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']){
			Bingo_Log::warning('call content modifyCfgItemStatus fail.input:['.serialize($arrInput).'] out:['.serialize($arrOut).']');
			return $this->printOut(isset($arrOut['errno'])?$arrOut['errno']:Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}
		return $this->printOut(Tieba_Errcode::ERR_SUCCESS);
	}
	
}