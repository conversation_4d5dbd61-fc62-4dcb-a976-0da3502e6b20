<?php
class getDlvrySelectCfgResAction extends Util_Action{
	
	/**
	 * (non-PHPdoc)
	 * @see Util_Action::_execute()
	 * @param null
	 * @return null
	 */
	public function _execute(){
		$siteid = strval(Bingo_Http_Request::get('site_id',''));
		$cfgResType = intval(Bingo_Http_Request::get('cfgres_type',0));
		
		if(empty($siteid) || $cfgResType <= 0){
			Bingo_Log::warning('getSelectCfgResAction invalid param.cfgResType:['.serialize($cfgResType).'] siteid:['.$siteid.']');
			return $this->printOut(Tieba_Errcode::ERR_SUCCESS);
		}
		
		/*$arrInput = array(
			'site_id' => $siteid,
			'cfgres_type' => $cfgResType,
		);*/
		$condition = '(site_id in ('.$siteid.') or can_use_all_site = 1) and cfgres_type = '.$cfgResType;
		$arrInput = array(
			'condition' => $condition,
			'not_need_append_field' => 1,
		);
		$arrOut = Tieba_Service::call('content', 'getDlvryCfgResByCondition', $arrInput, null, null, 'post', 'php', 'utf-8');
		if(false == $arrOut || !isset($arrOut['errno'])
				|| Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']){
			Bingo_Log::warning('call content getDlvryCfgResBySiteid fail.input:['.serialize($arrInput).'] out:['.serialize($arrOut).']');
			return $this->printOut(isset($arrOut['errno'])?$arrOut['errno']:Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}
		$arrData = array();
		foreach ($arrOut['data'] as $arrItem){
			if($arrItem['cfgres_id'] <= 0 || empty($arrItem['cfgres_name'])){
				continue;
			}
			$arrData[] = array(
				'label' => $arrItem['cfgres_name'],
				'value' => $arrItem['cfgres_id'],
			);
		}
		return $this->printOut(Tieba_Errcode::ERR_SUCCESS, '', $arrData);
	}
	
}