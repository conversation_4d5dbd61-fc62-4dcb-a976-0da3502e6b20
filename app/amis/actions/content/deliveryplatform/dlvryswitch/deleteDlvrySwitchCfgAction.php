<?php
class deleteDlvrySwitchCfgAction extends Util_Action{

	/**
	 * (non-PHPdoc)
	 * @see Util_Action::_execute()
	 * @param null
	 * @return null
	 */
	public function _execute(){
		$switchid = intval(Bingo_Http_Request::get('switch_id',0));
		$switchType = strval(Bingo_Http_Request::get('switch_type', ''));
		$arrInput = array(
			'switch_id' => $switchid,
			'switch_type' => $switchType,
		);
		$arrOut = Tieba_Service::call('content', 'deleteDlvrySwitchCfg', $arrInput, null, null, 'post', 'php', 'utf-8');
		if(false == $arrOut || !isset($arrOut['errno'])
				|| Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']){
			Bingo_Log::warning('call content getDlvrySwitchType fail.input:['.serialize($arrInput).'] out:['.serialize($arrOut).']');
			return $this->printOut(isset($arrOut['errno'])?$arrOut['errno']:Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}
		return $this->printOut(Tieba_Errcode::ERR_SUCCESS, '', $arrOut['data']);
	}

}