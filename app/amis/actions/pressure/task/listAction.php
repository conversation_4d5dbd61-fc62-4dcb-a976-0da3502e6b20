<?php

/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2018/02/11
 * Time: 16:13
 */
class listAction extends Util_Action
{

    public function _execute() {
        $intPn = intval(Bingo_HTTP_Request::getNoXssSafe('pn', 1));
        $intRn = intval(Bingo_HTTP_Request::getNoXssSafe('rn', 30));
        $intTaskId = intval(Bingo_HTTP_Request::getNoXssSafe('task_id', 0));
        $intSceneId = intval(Bingo_HTTP_Request::getNoXssSafe('scene_id', 0));

        $arrInput = array(
            'pn' => $intPn,
            'rn' => $intRn,
        );
        if ($intTaskId > 0) {
            $arrInput['ids'] = $intTaskId;
        }
        if ($intSceneId > 0) {
            $arrInput['scene_id'] = $intSceneId;
        }

        $strOut = Tbapi_Core_Midl_Http::httpcall('tbpressure', '/queryTask', $arrInput);
        if (empty($strOut)) {
            Bingo_Log::warning(__METHOD__ . ' call tbpressure:queryTask failed! input:[' . serialize($arrInput) . ']' . 'output:[' . serialize($arrOut) . ']');
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return false;
        }
        $arrOut = Bingo_String::json2array($strOut);
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrOut['errno']) {
            Bingo_Log::warning(__METHOD__ . ' call tbpressure:queryTask failed! input:[' . serialize($arrInput) . ']' . 'output:[' . serialize($arrOut) . ']');
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return false;
        }
        $arrRows = array();
        foreach ($arrOut['data']['list'] as $data) {
            $arrInterface = array();
            $arrInterfaceList = Bingo_String::json2array($data['wordlist_scene']['scene_interfaces']);
            foreach ($arrInterfaceList as $item) {
                $arrInterface[] = array(
                    'title' => sprintf("接口：%s", $item['interface']),
                    'description' => sprintf("返回条数: %s  特殊查询条件: %s", $item['limit'], !empty($item['conditions']) ? $item['conditions'] : "无"),
                );
            }
            $row = array(
                'id' => $data['wordlist_task']['id'],
                'scene_id' => $data['wordlist_scene']['id'],
                'scene_name' => $data['wordlist_scene']['scene_name'],
                'scene_interfaces' => $data['wordlist_scene']['scene_interfaces'],
                'interface_list' => $arrInterface,
                'scene_protocol' => $data['wordlist_scene']['scene_protocol'],
                'hive_task_id' => $data['wordlist_task']['hive_task_id'],
                'hive_task_name' => $data['wordlist_task']['hive_task_name'],
                'hive_task_result_file' => $data['wordlist_task']['hive_task_result_file'],
                'wordlist_file' => $data['wordlist_task']['wordlist_file'],
                'task_status' => $data['wordlist_task']['task_status'],
                'op_uid' => $data['wordlist_task']['op_uid'],
                'op_uname' => $data['wordlist_task']['op_uname'],
                'update_time' => $data['wordlist_task']['update_time'],
            );
            $arrRows[] = $row;

        }
        $arrOut = array(
            'rows' => $arrRows,
            'count' => $arrOut['data']['total_cnt'],
        );

        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, $arrOut);
        return true;
    }

    /**
     * @param $intErrno
     * @param array $arrData
     */
    private function _jsonRet($intErrno, $arrData = array()){
        $arrRet = array(
            'errno' => $intErrno,
            'errmsg' => Tieba_Error::getErrmsg($intErrno),
            'data' => $arrData,
        );
        echo Bingo_String::array2json($arrRet);
    }

}