<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2017-07-12 21:38:00
 * @version
 */
class countAction extends Util_Action{
	protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;
    
    const CALL_SERVICE_SUCCESS = 0;
	const CALL_SERVICE_FAILURE = -1;
    const CLAIMED = 3; 
	
	const EXAMINING = 0;
	const SUCCESS = 1;
	const FAILURE = 2;

	private $_intOpUid = 0;
    private $_strOpUname = '';

	/**
	 * [process description]
	 * @param
	 * @return [type] [description]
	 */
	public function _execute(){       
        $this->_intOpUid   = Util_User::$intUserId;
       // $this->_strOpUname = Bingo_Encode::convert(Util_User::$strUserName,Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);		
		
        $arrInput['result'] = self::CLAIMED;
        $arrInput['op_user_id']  = $this->_intOpUid;

		$arrOutput = Tieba_Service::call('bawu', 'listForumTitleCntByUid', $arrInput,null, null, 'post', 'php', 'gbk');
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
			Bingo_Log::warning(sprintf("call bawu::listForumTitle failed input[%s] output[%s]",serialize($arrInput), serialize($arrOutput)));
            $this->errRet(0,'success',array('num' => array()));
            return true;
		}
		$ret = $arrOutput['data']['num'];
		
		$arrRet = array(
			'num_claimed' => $ret['num_claimed'],
            'num_checked' => $ret['num_checked'],
		);
		$this->errRet(0,'success', $arrRet);
        return true;
	}
    /*
     * @param $no
     * @param $error
     * @param array $data
     * @return array
     */
    public function errRet($no, $error, $data = array()) {
        $arrRet = array(
            'errno' => $no,
            //'errmsg' => $error,
            'errmsg' => Bingo_Encode::convert($error, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8),
            'data' => $data,
        );
        //Bingo_Log::warning(print_r($arrRet,1));
        echo Bingo_String::array2json($arrRet);
        return true;
    }
}

?>
