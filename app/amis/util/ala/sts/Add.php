<?php
/**
 * Add.php STS的Add Action基类
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 17/11/21 下午8:06
*/

/**
 * STS Add action的基类
 **/
class Util_Ala_STS_Add extends Util_Ala_Base
{
    protected $_isCheckTbs = false;

    protected $_arrSTSParam = array();

    protected $_strSTSName = '';

    protected $_strSTSExtra = '';

    protected $_isGetNoXSSSafe = false;

    /**
     * @return bool
     */
    public function init() {
        if (empty($this->_strSTSName)) {
            Bingo_Log::warning(__CLASS__.'::'.__FUNCTION__." code error, no strSTSName");
            $this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), '未配置$_strSTSName属性');

            return false;
        }

        if ($this->_isGetNoXSSSafe) {
            $this->_arrSTSParam = (array)Bingo_Http_Request::getNoXssSafe('sts', array());
        } else {
            $this->_arrSTSParam = (array)Bingo_Http_Request::get('sts', array());
        }

        if (empty($this->_arrSTSParam)) {
            Bingo_Log::warning(__CLASS__.'::'.__FUNCTION__." param error, no sts data");
            $this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), '参数错误，无sts数据');
            return false;
        }

        Util_User::init();
        $intUserId = Util_User::$intUserId;

        //角色权限
        $intRole = Util_Ala_Common::getUserRoleId();
        switch ($intRole) {
            case Lib_Ala_Define_Role::ROLE_ADMIN:
                break;
            case Lib_Ala_Define_Role::ROLE_OPERATOR:
                $arrOutputUnion = Dl_Ala_Union_Union::getListByCreateUserId($intUserId, 1, Lib_Ala_Define_Role::ROLE_OPERATOR_MANAGE_MAX);
                if (false === $arrOutputUnion || Tieba_Errcode::ERR_SUCCESS != $arrOutputUnion['errno']) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Union_Union::getListByCreateUserId fail. input:[]; output:[" . serialize($arrOutputUnion) . "]";
                    Bingo_Log::warning($strLog);
                    return $this->_jsonRet(Tieba_Errcode::ERR_DL_CALL_FAIL, "服务器出错");
                }
                $arrUnionList = $arrOutputUnion['data'];
                $arrUnionId = array();
                foreach ($arrUnionList as $arrUnionInfo) {
                    $arrUnionId[] = $arrUnionInfo['union_id'];
                }

                $arrUserId = array();
                if(!empty($arrUnionId)) {
                    $arrInputAnchor = array(
                        'union_ids' => implode(',', $arrUnionId),
                    );
                    $intAllAnchorMax = Lib_Ala_Define_Role::ROLE_OPERATOR_MANAGE_MAX * Lib_Ala_Define_Role::ROLE_TEAM_LEADER_MANAGE_MAX;
                    $arrReturnData = Dl_Ala_Union_Anchor::getListByTime($arrInputAnchor, 1, $intAllAnchorMax);
                    $arrReturnList = $arrReturnData['data'];
                    if (!empty($arrReturnList)) {
                        foreach ($arrReturnList as $arrDetail) {
                            $arrUserId[] = $arrDetail['anchor_user_id'];
                        }
                    }
                }
                $intExtUserId = $this->_arrSTSParam['user_id'];
                if(empty($arrUserId) || !in_array($intExtUserId,$arrUserId)){
                    return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "主播不在合作运营内！");
                }
                break;
            case Lib_Ala_Define_Role::ROLE_UNION_PRESIDENT:
                return $this->_jsonRet(Tieba_Errcode::ERR_ACTION_FORBIDDEN, '无权限');
            default:
                return $this->_jsonRet(Tieba_Errcode::ERR_ACTION_FORBIDDEN, '无权限');
        }


        require_once(ROOT_PATH.'/app/ala/util/STS.php');

        return parent::init();
    }

    /**
     * execute
     * @return bool
     */
    public function execute() {
        $strSTSName  = $this->_strSTSName;
        $strSTSExtra = $this->_strSTSExtra;
        $arrSTSParam = $this->_arrSTSParam;

        $arrSTSParam = $this->_preHandleParam($arrSTSParam);

        $arrRet = Util_STS::call($strSTSName)->add($arrSTSParam, $strSTSExtra);

        if (false === $arrRet) {
            return $this->_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $this->_errRet(Tieba_Errcode::ERR_SUCCESS, array(), '添加成功');

        return true;
    }

    /**
     * @param $arrSTSParam
     * @return mixed
     */
    protected function _preHandleParam($arrSTSParam) {
        return $arrSTSParam;
    }
}