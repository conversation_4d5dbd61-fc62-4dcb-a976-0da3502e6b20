<?php
/**
 *
 * <AUTHOR>
 * @date 2019/4/29
 */

abstract class Util_Ala_Showx extends Util_Ala_Base
{
    protected $_isCheckTbs = false;
    private $_showx_token = 'shoxtokenfortiebabizstat';

    public function execute()
    {
        //$_COOKIE['pub_env'] = 1;
        $_SERVER['HTTP_X_BD_FORCE_IDC'] = 'nj';
        $_SERVER['HTTP_X_BD_NO_INROUTER'] = 1;
        $_SERVER['HTTP_X_BD_IDC'] = 'nj';
        $token = Bingo_Http_Request::get("showx_token");
        if ($token != $this->_showx_token) {
            return self::_showxRet(-1, 'auth error');
        } else {
            return $this->_execute();
        }
    }

    abstract public function _execute();


    /**
     * @return
     */

    /**
     * @return array
     */
    public static function getShowxConditions()
    {
        $conditions = Bingo_Http_Request::getGetNoXssSafe("conditions");
        $result = array();
        if (empty($conditions)) {
            return $result;
        }
        $conditions = json_decode($conditions, true);
        if (empty($conditions)) {
            return $result;
        }
        foreach ($conditions as $condition) {
            $result[$condition['k']] = $condition['v'];
        }

        return $result;
    }

    /**
     * @param $errno
     * @param string $errmsg
     * @param array $arrExtData
     * @return int
     * @throws Exception
     */
    public static function _showxRet($errno, $errmsg = '', array $arrExtData = null)
    {
        $arrRet = array(
            'status' => intval($errno),
            'msg' => strval($errmsg),
            'data' => $arrExtData
        );
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        exit;
    }

}