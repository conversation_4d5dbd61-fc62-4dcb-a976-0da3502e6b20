<?php
/**
 * Created by PhpStorm.
 * User: zhanghanqing
 * Date: 2017/7/17
 * Time: 20:26
 */
class Util_Ala_Journal
{
    // 递减
    const SEARCH_ORDER_DESC = 2;
    const SEARCH_ORDER_ASC = 1;

    // 1000 t豆 1元
    const TD_TO_RMB = 1000.0;

    public static $ORDER_TYPE = array(
        self::SEARCH_ORDER_DESC => 'DESC',
        self::SEARCH_ORDER_ASC => 'ASC',
    );

    // 签约
    const ANCHOR_TYPE_UNION = 1;
    // 非签约
    const ANCHOR_TYPE_SINGLE = 2;

    public static $ANCHOR_TYPE = array(
        self::ANCHOR_TYPE_UNION => 1,
        self::ANCHOR_TYPE_SINGLE => 1,
    );


    /**
     * 将主播收入数据写入
     * @param array
     * @param int
     * @return bool
     */
    public static function addAnchorJournal($arrUserData,$intDate) {

        if (empty($arrUserData)) {
            return true;
        }

        foreach ($arrUserData as $intUserId => $arrAnchorData) {
            $arrInput = array(
                'user_id' => $intUserId,
                'user_name' => $arrAnchorData['user_name'],
                'anchor_type' => $arrAnchorData['anchor_type'],
                'union_id' => $arrAnchorData['union_id'],
                'live_duration' => $arrAnchorData['live_duration'],
                'date' => $intDate,
                'income_td' => $arrAnchorData['income_td'],
            );
            echo $intUserId.'  ';
            $arrOutput = Service_Ala_Journal_Journal::addAnchorJournal($arrInput);
            Bingo_Log::warning('Service_Ala_Journal_Journal::addAnchorJournal  input(:'.serialize($arrInput)."). output[:".serialize($arrOutput)."]");
            usleep(300);
        }
        echo "\n";
        return true;
    }

    /**
     * 将主播收入数据写入 rpc
     * @param array
     * @param int
     * @return bool
     */
    public static function addCallAnchorJournal($arrUserData,$intDate) {

        if (empty($arrUserData)) {
            return true;
        }
        $arrFailData = array();

        foreach ($arrUserData as $intUserId => $arrAnchorData) {
            $arrInput = array(
                'user_id' => $intUserId,
                'user_name' => $arrAnchorData['user_name'],
                'anchor_type' => $arrAnchorData['anchor_type'],
                'union_id' => $arrAnchorData['union_id'],
                'live_duration' => $arrAnchorData['live_duration'],
                'date' => $intDate,
                'income_td' => $arrAnchorData['income_td'],
            );
            $strServiceName   = 'ala';
            $strServiceMethod = 'addAnchorJournal';
            $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrInput, null, null, "post", null, "utf-8");
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
                // 记录失败项
                $arrFailData[] = $arrInput;
            }
            usleep(100);
        }
        // $intFailCount = 0;
        // $arrSaveData = array();
        if (!empty($arrFailData)){
            Bingo_Log::warning(serialize($arrFailData));
        }
       /* // orp跑此脚本超时，先注掉
       while (!empty($arrFailData)){
            foreach ($arrFailData as $tmpIndex => $tmpFailItem){
                // 先看违反表唯一性没
                $arrAnchorJournalInput = array(
                    'start_time' => $tmpFailItem['date'],
                    'end_time' => $tmpFailItem['date'],
                    'user_id' => $tmpFailItem['user_id'],
                    'limit' => 1,
                );
                $arrAnchorJournalOutput = Service_Ala_Journal_Journal::getAnchorJournalList($arrAnchorJournalInput);
                $arrAnchorJournal = $arrAnchorJournalOutput['data'][0];
                if (!empty($arrAnchorJournal)){
                    // 记录td不一样的话，就记录
                    if ($tmpFailItem['income_td'] != $arrAnchorJournal['income_td']){
                        $arrSaveData[] = $tmpFailItem;
                    }
                    unset($arrFailData[$tmpIndex]);
                    continue;
                }

                $arrInput = $tmpFailItem;
                $strServiceName   = 'ala';
                $strServiceMethod = 'addAnchorJournal';
                $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrInput, null, null, "post", null, "utf-8");
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
                    Bingo_Log::fatal($strLog);
                    continue;
                }
                unset($arrFailData[$tmpIndex]);
            }
            if ($intFailCount > 3){
                // 死活写不进去,可能是服务就是有问题,存到redis里
                $arrSaveData = array_merge($arrSaveData,$arrFailData);
                break;
            }
            $intFailCount++;
        }
        if (!empty($arrSaveData)){
            $arrInput = array(
                // 1是主播流水 ，2是公会流水
                'journal_type' => 1,
                'fail_items' => $arrSaveData,
            );
            $strServiceName   = 'ala';
            $strServiceMethod = 'saveFailJournal';
            $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrInput, null, null, "post", null, "utf-8");
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::fatal($strLog);
                // 这里要也失败的话，那真是只能查日志了。。
            }
        }*/
        return true;
    }

    /**
     * 将主播收入数据写入 rpc 带有max_live_duration
     * @param array
     * @param int
     * @return bool
     */
    public static function addCallAnchorJournalMaxLiveDuration($arrUserData,$intDate) {

        if (empty($arrUserData)) {
            return true;
        }
        $arrFailData = array();

        foreach ($arrUserData as $intUserId => $arrAnchorData) {
            $arrInput = array(
                'user_id' => $intUserId,
                'user_name' => $arrAnchorData['user_name'],
                'anchor_type' => $arrAnchorData['anchor_type'],
                'union_id' => $arrAnchorData['union_id'],
                'live_duration' => $arrAnchorData['live_duration'],
                'max_live_duration' => $arrAnchorData['max_live_duration'],
                'date' => $intDate,
                'income_td' => $arrAnchorData['income_td'],
                'prop_count' => $arrAnchorData['prop_count'],
                'prop_tdou' => $arrAnchorData['prop_tdou'],

            );
            $strServiceName   = 'ala';
            $strServiceMethod = 'addCallAnchorJournalMaxLiveDuration';
            $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrInput, null, null, "post", null, "utf-8");
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
                // 记录失败项
                $arrFailData[] = $arrInput;
            }
            usleep(100);
        }
        if (!empty($arrFailData)){
            Bingo_Log::warning(serialize($arrFailData));
        }
        return true;
    }


    /**
     * 将主播收入按场景id 写入 rpc
     * @param array
     * @param int
     * @return bool
     */
    public static function addCallAnchorSceneJournal($arrUserSceneData,$intDate) {

        if (empty($arrUserSceneData)) {
            return true;
        }
        $arrFailData = array();

        foreach ($arrUserSceneData as $intUserId => $arrAnchorData) {
            $arrSceneData = $arrAnchorData['scene_income_td'];
            foreach ($arrSceneData as $intSceneId => $intIncome) {
                if(intval($intIncome) <= 0)
                    continue;
                $arrInput = array(
                    'user_id' => $intUserId,
                    'user_name' => $arrAnchorData['user_name'],
                    'anchor_type' => $arrAnchorData['anchor_type'],
                    'union_id' => $arrAnchorData['union_id'],
                    'date' => $intDate,
                    'scene_id' => $intSceneId,
                    'income_td' => $intIncome,
                );
                $strServiceName   = 'ala';
                $strServiceMethod = 'addAnchorSceneJournal';
                $arrOutput = Dl_Ala_Journal_Journal::addAnchorSceneJournal($arrInput);
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
                    Bingo_Log::warning($strLog);
                    // 记录失败项
                    $arrFailData[] = $arrInput;
                }
                usleep(100);
            }
        }
        // $intFailCount = 0;
        // $arrSaveData = array();
        if (!empty($arrFailData)){
            Bingo_Log::warning(serialize($arrFailData));
        }
        return true;
    }

    /**
     * 将公会收入数据写入
     * @param array
     * @param int
     * @return bool
     */
    public static function addUnionJournal($arrUnionData,$intDate) {

        if (empty($arrUnionData)) {
            return true;
        }

        foreach ($arrUnionData as $intUnionId => $arrUnion) {
            $arrInput = array(
                'union_id' => $intUnionId,
                'union_name' => $arrUnion['union_name'],
                'date' => $intDate,
                'income_td' => $arrUnion['income_td'],
            );
            echo $intUnionId.'  ';
            $arrOutput = Service_Ala_Journal_Journal::addUnionJournal($arrInput);
            Bingo_Log::warning('Service_Ala_Journal_Journal::addUnionJournal  input(:'.serialize($arrInput)."). output[:".serialize($arrOutput)."]");
            usleep(300);
        }
        echo "\n";
        return true;
    }

    /**
     * 将公会收入数据写入 rpc
     * @param array
     * @param int
     * @return bool
     */
    public static function addCallUnionJournal($arrUnionData,$intDate) {

        if (empty($arrUnionData)) {
            return true;
        }
        $arrFailData = array();
        foreach ($arrUnionData as $intUnionId => $arrUnion) {
            $arrInput = array(
                'union_id' => $intUnionId,
                'union_name' => $arrUnion['union_name'],
                'date' => $intDate,
                'income_td' => $arrUnion['income_td'],
            );
            echo $intUnionId.'  ';
            $strServiceName   = 'ala';
            $strServiceMethod = 'addUnionJournal';
            $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrInput, null, null, "post", null, "utf-8");
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
                $arrFailData[] = $arrInput;
            }
            usleep(100);
        }

        if (!empty($arrFailData)){
            Bingo_Log::warning(serialize($arrFailData));
        }
        /*$intFailCount = 0;
        $arrSaveData = array();
        while (!empty($arrFailData)){
            foreach ($arrFailData as $tmpIndex => $tmpFailItem){
                // 先看违反表唯一性没
                $arrAnchorJournalInput = array(
                    'start_time' => $tmpFailItem['date'],
                    'end_time' => $tmpFailItem['date'],
                    'union_id' => $tmpFailItem['union_id'],
                    'limit' => 1,
                );
                $arrUnionJournalOutput = Service_Ala_Journal_Journal::getUnionJournalList($arrAnchorJournalInput);
                $arrUnionJournal = $arrUnionJournalOutput['data'][0];
                if (!empty($arrUnionJournal)){
                    // 记录td不一样的话，就记录
                    if ($tmpFailItem['income_td'] != $arrUnionJournal['income_td']){
                        $arrSaveData[] = $tmpFailItem;
                    }
                    unset($arrFailData[$tmpIndex]);
                    continue;
                }

                $arrInput = $tmpFailItem;
                $strServiceName   = 'ala';
                $strServiceMethod = 'addUnionJournal';
                $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrInput, null, null, "post", null, "utf-8");
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
                    Bingo_Log::fatal($strLog);
                    continue;
                }
                unset($arrFailData[$tmpIndex]);
            }
            if ($intFailCount > 3){
                // 死活写不进去,可能是服务就是有问题,存到redis里
                $arrSaveData = array_merge($arrSaveData,$arrFailData);
                break;
            }
            $intFailCount++;
        }
        if (!empty($arrSaveData)){
            $arrInput = array(
                // 1是主播流水 ，2是公会流水
                'journal_type' => 2,
                'fail_items' => $arrSaveData,
            );
            $strServiceName   = 'ala';
            $strServiceMethod = 'saveFailJournal';
            $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrInput, null, null, "post", null, "utf-8");
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::fatal($strLog);
                // 这里要也失败的话，那真是只能查日志了。。
            }
        }*/
        return true;
    }

    /**
     * 获取过去很久指定时间内有开播过的所有主播user_id（获取过去很久指定时间使用该方法不会超时）
     * @param $start_time
     * @param $end_time
     * @param $limit 分批获取每批的数量，默认500
     * @return array|bool
     */
    public static function getLiveUserIdsForPassedTime($start_time, $end_time, $limit = 500) {
        // 获取指定时间内播过的所有主播总数，以开始和结束时间同时限制，分批获取
        $arrServiceInput = array(
            'start_time' => $start_time,
            'end_time'   => $end_time,
        );

        if($end_time - $start_time > 86401 || empty($end_time) || empty($start_time)){
            return false;
        }

        // 获取符合条件的总数
        $strServiceName   = 'ala';
        $strServiceMethod = 'getClosedLiveIdsCountInTime';

        $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);

            return false;
        }

        Bingo_Log::warning(__CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");

        $intLiveCount = (int)$arrOutput['data'][0]['count'];

        Bingo_Log::warning(__CLASS__."::".__FUNCTION__.'$intLiveCount:'.$intLiveCount."\r\n");

        if (!$intLiveCount) {
            Bingo_Log::warning(__CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod no count. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
            return false;
        }

        $pages = ceil($intLiveCount/$limit);

        $strServiceName   = 'ala';
        $strServiceMethod = 'getClosedLiveInfoListInTime';

        $strMultiCallKey = __CLASS__."::".__FUNCTION__."_1";
        $objMultiCall    = new Tieba_Multi($strMultiCallKey);

        // 分批获取列表
        for ($i = 0; $i <= $pages - 1; $i++) {
            $offset = $i * $limit;
            $arrServiceInput['offset'] = $offset;
            $arrServiceInput['limit']  = $limit;
            $methodParam = $arrServiceInput;
            $arrInput = array(
                "serviceName" => $strServiceName,
                "method"      => $strServiceMethod,
                'ie'          => 'utf-8',
                "input"       => $methodParam,
            );
            $objMultiCall->register($strServiceMethod.'_'.$i, new Tieba_Service($strServiceName), $arrInput);
            Bingo_Log::warning('$objMultiCall->register(:'.$strServiceMethod.'_'.$i."). pages:".$pages."\r\n");
        }
        Bingo_Timer::start(__CLASS__."::".__FUNCTION__);
        $objMultiCall->call();
        Bingo_Timer::end(__CLASS__."::".__FUNCTION__);
        $arrLiveList = array();

        for ($i = 0; $i <= $pages - 1; $i++) {
            $methodResName = $strServiceMethod.'_'.$i;
            $arrOutput = $objMultiCall->getResult($methodResName);
            Bingo_Log::warning('$objMultiCall->getResult(:'.$strServiceMethod.'_'.$i."). pages:".$pages."\r\n");
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $offset = $i * $limit;
                $arrServiceInput['offset'] = $offset;
                $arrServiceInput['limit']  = $limit;
                $methodParam = $arrServiceInput;
                $strLog = __CLASS__."::".__FUNCTION__." call $strServiceMethod $strServiceMethod fail. input:[".serialize($methodParam)."]  output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
            }
            if (!empty($arrOutput['data'])) {
                $arrLiveList = array_merge($arrLiveList, $arrOutput['data']);
            }
//            sleep(1);
        }

        // 获取开播所有主播user_id
        $arrLiveUserIds = array();

        foreach ($arrLiveList as $k=>$v) {
            $arrLiveUserIds[] = $v['user_id'];
        }

        return array_unique($arrLiveUserIds);
    }


    /**
     * 获取指定时间内有开播过的所有主播user_id
     * @param $start_time
     * @param $end_time
     * @param $limit 分批获取每批的数量，默认500
     * @return array|bool
     */
    public static function getLiveUserIds($start_time, $end_time, $limit = 500) {
        // 获取指定时间内播过的所有主播总数，以开始和结束时间同时限制，分批获取
        $arrServiceInput = array(
            'start_time' => $start_time,
            'end_time'   => $end_time,
        );

        if($end_time - $start_time > 86401 || empty($end_time) || empty($start_time)){
            return false;
        }

        // 获取符合条件的总数
        $strServiceName   = 'ala';
        $strServiceMethod = 'getUserLiveIdsCountInTime';

        $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);

            return false;
        }

        Bingo_Log::warning(__CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");

        $intLiveCount = (int)$arrOutput['data'][0]['count'];

        Bingo_Log::warning(__CLASS__."::".__FUNCTION__.'$intLiveCount:'.$intLiveCount."\r\n");

        if (!$intLiveCount) {
            Bingo_Log::warning(__CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod no count. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
            return false;
        }

        $pages = ceil($intLiveCount/$limit);

        $strServiceName   = 'ala';
        $strServiceMethod = 'getUserLiveInfoListInTime';

        $strMultiCallKey = __CLASS__."::".__FUNCTION__."_1";
        $objMultiCall    = new Tieba_Multi($strMultiCallKey);

        // 分批获取列表
        for ($i = 0; $i <= $pages - 1; $i++) {
            $offset = $i * $limit;
            $arrServiceInput['offset'] = $offset;
            $arrServiceInput['limit']  = $limit;
            $methodParam = $arrServiceInput;
            $arrInput = array(
                "serviceName" => $strServiceName,
                "method"      => $strServiceMethod,
                'ie'          => 'utf-8',
                "input"       => $methodParam,
            );
            $objMultiCall->register($strServiceMethod.'_'.$i, new Tieba_Service($strServiceName), $arrInput);
            Bingo_Log::warning('$objMultiCall->register(:'.$strServiceMethod.'_'.$i."). pages:".$pages."\r\n");
        }
        Bingo_Timer::start(__CLASS__."::".__FUNCTION__);
        $objMultiCall->call();
        Bingo_Timer::end(__CLASS__."::".__FUNCTION__);
        $arrLiveList = array();

        for ($i = 0; $i <= $pages - 1; $i++) {
            $methodResName = $strServiceMethod.'_'.$i;
            $arrOutput = $objMultiCall->getResult($methodResName);
            Bingo_Log::warning('$objMultiCall->getResult(:'.$strServiceMethod.'_'.$i."). pages:".$pages."\r\n");
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $offset = $i * $limit;
                $arrServiceInput['offset'] = $offset;
                $arrServiceInput['limit']  = $limit;
                $methodParam = $arrServiceInput;
                $strLog = __CLASS__."::".__FUNCTION__." call $strServiceMethod $strServiceMethod fail. input:[".serialize($methodParam)."]  output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
            }
            if (!empty($arrOutput['data'])) {
                $arrLiveList = array_merge($arrLiveList, $arrOutput['data']);
            }
//            sleep(1);
        }

        // 获取开播所有主播user_id
        $arrLiveUserIds = array();

        foreach ($arrLiveList as $k=>$v) {
            $arrLiveUserIds[] = $v['user_id'];
        }

        return array_unique($arrLiveUserIds);
    }

    /**
     * 获取指定时间内有开播过的所有主播user_id---过滤贴吧端 分批获取每批的数量，默认500
     * @param $start_time
     * @param $end_time
     * @param $limit
     * @return array|bool
     */
    public static function getLiveUserIdsNew($start_time, $end_time, $limit = 500) {
        if( empty($end_time) || empty($start_time)){
            return false;
        }
        // 获取指定时间内播过的所有主播总数，以开始和结束时间同时限制，分批获取
        $arrServiceInput = array(
            'start_time' => $start_time,
            'end_time'   => $end_time,
        );
        // 获取符合条件的总数
        $strServiceName   = 'ala';
        $strServiceMethod = 'getUserLiveIdsCountInTime';

        $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return false;
        }

        Bingo_Log::warning(__CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod success. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
        $intLiveCount = (int)$arrOutput['data'][0]['count'];
        Bingo_Log::warning(__CLASS__."::".__FUNCTION__.'$intLiveCount:'.$intLiveCount."\r\n");
        if (!$intLiveCount) {
            Bingo_Log::warning(__CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod no count. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
            return false;
        }

        $pages = ceil($intLiveCount/$limit);
        $strServiceName   = 'ala';
        $strServiceMethod = 'getUserLiveInfoListInTime';
        $strMultiCallKey = __CLASS__."::".__FUNCTION__."_1";
        $objMultiCall    = new Tieba_Multi($strMultiCallKey);
        // 分批获取列表
        for ($i = 0; $i <= $pages - 1; $i++) {
            $offset = $i * $limit;
            $arrServiceInput['offset'] = $offset;
            $arrServiceInput['limit']  = $limit;
            $methodParam = $arrServiceInput;
            $arrInput = array(
                "serviceName" => $strServiceName,
                "method"      => $strServiceMethod,
                'ie'          => 'utf-8',
                "input"       => $methodParam,
            );
            $objMultiCall->register($strServiceMethod.'_'.$i, new Tieba_Service($strServiceName), $arrInput);
            Bingo_Log::warning('$objMultiCall->register(:'.$strServiceMethod.'_'.$i."). pages:".$pages."\r\n");
        }
        Bingo_Timer::start(__CLASS__."::".__FUNCTION__);
        $objMultiCall->call();
        Bingo_Timer::end(__CLASS__."::".__FUNCTION__);
        $arrLiveList = array();

        for ($i = 0; $i <= $pages - 1; $i++) {
            $methodResName = $strServiceMethod.'_'.$i;
            $arrOutput = $objMultiCall->getResult($methodResName);
            Bingo_Log::warning('$objMultiCall->getResult(:'.$strServiceMethod.'_'.$i."). pages:".$pages."\r\n");
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $offset = $i * $limit;
                $arrServiceInput['offset'] = $offset;
                $arrServiceInput['limit']  = $limit;
                $methodParam = $arrServiceInput;
                $strLog = __CLASS__."::".__FUNCTION__." call $strServiceMethod $strServiceMethod fail. input:[".serialize($methodParam)."]  output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
            }
            if (!empty($arrOutput['data'])) {
                $arrLiveList = array_merge($arrLiveList, $arrOutput['data']);
            }
//            sleep(1);
        }

        // 获取开播所有主播user_id
        $arrLiveUserIds = array();
        $arrLiveIds = array();
        $arrUserLive = array();
        foreach ($arrLiveList as $k=>$v) {
            $arrLiveUserIds[] = $v['user_id'];
            $arrLiveIds[] = $v['live_id'];
            $arrUserLive[$v['user_id']][] =  $v['live_id'];
        }

        //过滤非贴吧直播
        $arrLiveReq = array();
        $arrLiveIds  = array_unique($arrLiveIds);
        $offset      = 0;
        for ($i = 0; $i < (count($arrLiveIds)/100); $i++){
            $arrLiveReq[] = array_slice($arrLiveIds,$offset,100);
            $offset = $offset + 100;
        }

        $Obj = new Tieba_Multi('get_subapp_by_live_id');
        $strServiceKey = 'ala';
        $strUserMethod = 'selectRoomInfoByLiveId';
        $caller = new Tieba_Service($strServiceKey);
        foreach ($arrLiveReq as $index => $item){
            $arrServiceParams = array(
                'live_ids'        => $item,
            );
            $strKey =  $strUserMethod .'_'. $index;
            $arrInput = array(
                'serviceName' => $strServiceKey,
                'method'      => $strUserMethod,
                'ie'          => 'utf-8',
                'input'       => $arrServiceParams,
            );
            $Obj->register($strKey,$caller,$arrInput);
        }
        $Obj->call();
        $arrRoomRet = array();
        foreach ($arrLiveReq as $index => $item){
            $strKey     =  $strUserMethod .'_'. $index;
            $resultUser = $Obj->getResult($strKey);
            foreach ($resultUser['data'] as $k => $v){
                $arrRoomRet[$k] =  $v;
            }
        }
        $arrUserIdRes = array();
        $arrUserLiveIds = array();
        foreach ($arrLiveUserIds as $intLiveUserId){
            //取贴吧端开播的用户
            foreach ($arrUserLive[$intLiveUserId] as $tmp) {
                if ($arrRoomRet[$tmp]['subapp_type'] == 'tieba' && $arrRoomRet[$tmp]['live_id'] > 0) {
                    $arrUserIdRes[] = $intLiveUserId;
                    if(!in_array($arrRoomRet[$tmp]['live_id'],$arrUserLiveIds[$intLiveUserId])) {
                        $arrUserLiveIds[$intLiveUserId][] = $arrRoomRet[$tmp]['live_id'];
                    }
                }
            }
        }

        $arrRes = array(array_unique($arrUserIdRes),$arrUserLiveIds);
        return $arrRes;
    }

    /**
     * 获取所有开播主播的收入
     * @param $arrInputLiveUserIds
     * @param $start_time
     * @param $end_time
     * @param array $arrSceneIds
     * @return array
     */
    public static function getLiveIncomes($arrInputLiveUserIds, $start_time, $end_time, $arrSceneIds=array()) {
        if(empty($arrSceneIds)){
            $arrSceneIds = array(6200003, 6200004, 6200005, 6200006, 8000001, 8000002,
                //背包
                //tieba
                8000041,
                8000042,
                //quanmin
                8000037,
                8000038,
                //haokan
                8000039,
                8000040,
                //->shoubai
                8000043,
                8000044,
                8000049,
                8000050,
                //tieba->shoubai
                8000051,
                8000052,
                //haokan
                8000003,
                8000004,
                //quanmin
                8000005,
                8000006,
                //shoubai
                8000019,
                8000020,
                8000021,
                8000022,
            );
        }
        $arrIncomes = $arrSceneIncomes = array();
        $intEveryTime = 20;
        $intTotalCount = count($arrInputLiveUserIds);
        $intBeginIndex = 0;
        while($intBeginIndex < $intTotalCount) {
            $arrLiveUserIds = array_slice($arrInputLiveUserIds, $intBeginIndex, $intEveryTime, true);
            $intBeginIndex = $intBeginIndex + $intEveryTime;

            foreach ($arrSceneIds as $index => $intSceneId) {
                $serviceName = 'present';
                $methodName  = 'getBenefitUserScoresByTimeGroupByScene';

                $strMultiCallKey = __CLASS__."::".__FUNCTION__."_" . ($index + 1);
                $objMultiCall    = new Tieba_Multi($strMultiCallKey);

                $scene_ids = array($intSceneId,);
                $arrRows = array();
                foreach ($arrLiveUserIds as $key=>$user_id) {
                    $methodParam = array(
                        'user_id'    => intval($user_id),
                        'scene_ids'  => $scene_ids,
                        'start_time' => $start_time,
                        'end_time'   => $end_time,
                    );
                    $arrInput = array(
                        "serviceName" => $serviceName,
                        "method"      => $methodName,
                        'ie'          => 'utf-8',
                        "input"       => $methodParam,

                    );
                    $objMultiCall->register($methodName.'_'.$key, new Tieba_Service($serviceName), $arrInput);

                    Bingo_Log::warning('$objMultiCall->register(:'.$methodName.'_'.$key."). pages:".count($arrLiveUserIds)."\r\n");

                    usleep(100);
                }

                Bingo_Timer::start(__CLASS__."::".__FUNCTION__);
                $objMultiCall->call();
                Bingo_Timer::end(__CLASS__."::".__FUNCTION__);

                foreach ($arrLiveUserIds as $key=>$user_id) {
                    $methodResName = $methodName.'_'.$key;
                    $arrOutput = $objMultiCall->getResult($methodResName);

                    if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {

                        $methodParam = array(
                            'user_id'    => intval($user_id),
                            'scene_ids'  => $scene_ids,
                            'start_time' => $start_time,
                            'end_time'   => $end_time,
                        );

                        $strLog = __CLASS__."::".__FUNCTION__." call $serviceName $methodName fail. input:[".serialize($methodParam)."]  output:[".serialize($arrOutput)."]";
                        Bingo_Log::warning($strLog);
                    }
                    $arrRows[] = $arrOutput['data'];

                    Bingo_Log::warning('$objMultiCall->getResult(:'.$methodName.'_'.$key."). pages:".count($arrLiveUserIds)."\r\n");

//            sleep(1);
                }

                $arrChallengeIncomes = self::getChallengeIncomes($arrLiveUserIds, $start_time, $end_time, $scene_ids);

                $arrTempIncome = array();
                foreach ($arrRows as $v) {
                    $arrTempIncome[$v['user_id']] = intval($arrTempIncome[$v['user_id']]) + ($v['ios_scores'] + $v['android_scores']);
                    if (isset($arrChallengeIncomes[$v['user_id']])) {
                        $arrTempIncome[$v['user_id']] += $arrChallengeIncomes[$v['user_id']];
                    }
                }

                foreach ($arrTempIncome as $intUserId => $intIncome) {
                    $arrIncomes[$intUserId] = intval($arrIncomes[$intUserId]) + $intIncome;
                    $arrSceneIncomes[$intUserId][$intSceneId] = intval($arrSceneIncomes[$intUserId][$intSceneId]) + $intIncome;
                }
            }
        }

        Bingo_Log::warning('$arrIncomes:'.serialize($arrIncomes)."\r\n");

        return array($arrIncomes,$arrSceneIncomes);
    }

    /**
     * 获取所有开播主播的收入--new
     * @param $arrInputLiveUserIds
     * @param array $arrUserLiveIds
     * @return array
     */
    public static function getLiveIncomesNew($arrInputLiveUserIds,  $start_time, $end_time,$arrUserLiveIds=array()) {
        $arrIncomes = array();
        $intEveryTime = 20;
        $intTotalCount = count($arrInputLiveUserIds);
        $intBeginIndex = 0;

        while($intBeginIndex < $intTotalCount) {
            $arrLiveUserIds = array_slice($arrInputLiveUserIds, $intBeginIndex, $intEveryTime, true);
            $intBeginIndex = $intBeginIndex + $intEveryTime;
                $serviceName = 'present';
                $methodName  = 'getBenefitUserScoresByTimeBlue';

                $strMultiCallKey = __CLASS__."::".__FUNCTION__."_" ;
                $objMultiCall    = new Tieba_Multi($strMultiCallKey);

                foreach ($arrLiveUserIds as $key=>$user_id) {
                    $methodParam = array(
                        'user_id'    => intval($user_id),
                        'live_ids'  => $arrUserLiveIds[$user_id],
                        'start_time' => $start_time,
                        'end_time'   => $end_time,
                    );
                    $arrInput = array(
                        "serviceName" => $serviceName,
                        "method"      => $methodName,
                        'ie'          => 'utf-8',
                        "input"       => $methodParam,

                    );

                    $objMultiCall->register($methodName.'_'.$key, new Tieba_Service($serviceName), $arrInput);
                    Bingo_Log::warning('$objMultiCall->register(:'.$methodName.'_'.$key."). pages:".count($arrLiveUserIds)."\r\n");
                    usleep(100);
                }

                Bingo_Timer::start(__CLASS__."::".__FUNCTION__);
                $objMultiCall->call();
                Bingo_Timer::end(__CLASS__."::".__FUNCTION__);

                foreach ($arrLiveUserIds as $key=>$user_id) {
                    $methodResName = $methodName.'_'.$key;
                    $arrOutput = $objMultiCall->getResult($methodResName);

                    if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                        $methodParam = array(
                            'user_id'    => intval($user_id),
                            'live_ids'  => $arrUserLiveIds[$user_id],
                            'start_time' => $start_time,
                            'end_time'   => $end_time,
                        );

                        $strLog = __CLASS__."::".__FUNCTION__." call $serviceName $methodName fail. input:[".serialize($methodParam)."]  output:[".serialize($arrOutput)."]";
                        Bingo_Log::warning($strLog);
                    }
                    $arrRows[] = $arrOutput['data'];

                    Bingo_Log::warning('$objMultiCall->getResult(:' . $methodName . '_' . $key . "). pages:" . count($arrLiveUserIds) . "\r\n");
                }
            $arrIncomes = array();
            foreach ($arrRows as $v) {
                $arrIncomes[$v['user_id']] = $v['scores'];
            }

        }


        Bingo_Log::warning('$arrIncomes:'.serialize($arrIncomes)."\r\n");
        return array($arrIncomes);
    }


    /**
     * 获取所有开播主播的收入,挑战
     * @param $arrLiveUserIds
     * @param $start_time
     * @param $end_time
     * @param $scene_ids
     * @return array
     */
    public static function getChallengeIncomes($arrLiveUserIds, $start_time, $end_time, $scene_ids = array(6200003, 6200004, 6200005, 6200006)) {
        $arrIncomes = $arrRows = array();

        $serviceName = 'present';
        $methodName  = 'getBenefitUserChallengeScoresByTimeGroupByScene';

        $strMultiCallKey = __CLASS__."::".__FUNCTION__."_1";
        $objMultiCall    = new Tieba_Multi($strMultiCallKey);

        foreach ($arrLiveUserIds as $key=>$user_id) {
            $methodParam = array(
                'user_id'    => intval($user_id),
                'scene_ids'  => $scene_ids,
                'start_time' => $start_time,
                'end_time'   => $end_time,
            );
            $arrInput = array(
                "serviceName" => $serviceName,
                "method"      => $methodName,
                'ie'          => 'utf-8',
                "input"       => $methodParam,

            );
            $objMultiCall->register($methodName.'_'.$key, new Tieba_Service($serviceName), $arrInput);

            Bingo_Log::warning('$objMultiCall->register(:'.$methodName.'_'.$key."). pages:".count($arrLiveUserIds)."\r\n");

            usleep(100);
        }

        Bingo_Timer::start(__CLASS__."::".__FUNCTION__);
        $objMultiCall->call();
        Bingo_Timer::end(__CLASS__."::".__FUNCTION__);

        foreach ($arrLiveUserIds as $key=>$user_id) {
            $methodResName = $methodName.'_'.$key;
            $arrOutput = $objMultiCall->getResult($methodResName);

            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {

                $methodParam = array(
                    'user_id'    => intval($user_id),
                    'scene_ids'  => $scene_ids,
                    'start_time' => $start_time,
                    'end_time'   => $end_time,
                );

                $strLog = __CLASS__."::".__FUNCTION__." call $serviceName $methodName fail. input:[".serialize($methodParam)."]  output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
            }
            $arrRows[] = $arrOutput['data'];

            Bingo_Log::warning('$objMultiCall->getResult(:'.$methodName.'_'.$key."). pages:".count($arrLiveUserIds)."\r\n");

//            sleep(1);
        }

        foreach ($arrRows as $v) {
            $arrIncomes[$v['user_id']] = $v['ios_scores'] + $v['android_scores'];
        }

        Bingo_Log::warning('$arrIncomes:'.serialize($arrIncomes)."\r\n");

        return $arrIncomes;
    }



//
//    /**
//     * @param $arrLiveUserIds
//     * @param $start_time
//     * @param $end_time
//     * @return array
//     */
//    public static function getChallengeIncomes($arrLiveUserIds, $start_time, $end_time) {
//
//        $arrIncomes = $arrRows = array();
//
//        $serviceName = 'ala';
//        $methodName  = 'getAnchorChallengeScoreInTime';
//
//        $strMultiCallKey = __CLASS__."::".__FUNCTION__."_1";
//        $objMultiCall    = new Tieba_Multi($strMultiCallKey);
//
//        foreach ($arrLiveUserIds as $key=>$user_id) {
//            $methodParam = array(
//                'anchor_id'    => intval($user_id),
//                'start_time' => $start_time,
//                'end_time'   => $end_time,
//            );
//            $arrInput = array(
//                "serviceName" => $serviceName,
//                "method"      => $methodName,
//                'ie'          => 'utf-8',
//                "input"       => $methodParam,
//
//            );
//            $objMultiCall->register($methodName.'_'.$key, new Tieba_Service($serviceName), $arrInput);
//
//            Bingo_Log::warning('$objMultiCall->register(:'.$methodName.'_'.$key."). pages:".count($arrLiveUserIds)."\r\n");
//
//            usleep(100);
//        }
//
//        Bingo_Timer::start(__CLASS__."::".__FUNCTION__);
//        $objMultiCall->call();
//        Bingo_Timer::end(__CLASS__."::".__FUNCTION__);
//
//        foreach ($arrLiveUserIds as $key=>$user_id) {
//            $methodResName = $methodName.'_'.$key;
//            $arrOutput = $objMultiCall->getResult($methodResName);
//
//
//
//            $intUserIncomeChallenge = 0;
//            if ($arrOutput === false || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
//
//                $methodParam = array(
//                    'anchor_id'    => intval($user_id),
//                    'start_time' => $start_time,
//                    'end_time'   => $end_time,
//                );
//
//                $strLog = __CLASS__."::".__FUNCTION__." call $serviceName $methodName fail. input:[".serialize($methodParam)."]  output:[".serialize($arrOutput)."]";
//                Bingo_Log::warning($strLog);
//
//            }
//
//            if (!empty($arrOutput['data'])) {
//                foreach ($arrOutput['data'] as $key => $value) {
//                    //比赛获胜并且allin
//                    $tmpUserIncome = intval($value['p1_score']);
//                    if ($value['allin'] == 1) {
//                        if ($value['result'] == 2) {
//                            $tmpUserIncome = intval($value['p1_score']) + intval($value['p2_score']);
//                        }
//                        if ($value['result'] == 0) {
//                            $tmpUserIncome = 0;
//                        }
//                    }
//                    $intUserIncomeChallenge += $tmpUserIncome;
//                }
//            }
//
//            $arrRows[$user_id] = $intUserIncomeChallenge;
//
//            Bingo_Log::warning('$objMultiCall->getResult(:'.$methodName.'_'.$key."). pages:".count($arrLiveUserIds)."\r\n");
//
////            sleep(1);
//        }
//
//        $arrIncomes = $arrRows;
//
//        Bingo_Log::warning('Challenge $arrIncomes:'.serialize($arrIncomes)."\r\n");
//
//        return $arrIncomes;
//
//    }





}