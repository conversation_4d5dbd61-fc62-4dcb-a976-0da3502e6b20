<?
class Util_RecordAction extends Util_Action {
	public $bolNeedRecord = true;
	public $intRecordType = 0;
	public $strRecordKey1 = '';
	/**
	 * 入口
	 * @return [type] [description]
	 */
	public function execute() {
		$this->_execute();
		if ($this->bolAutoLog) {
			$this->log();
		}

		if ($this->bolNeedRecord && $this->intRecordType) {
			$arrInput = array(
				'user_id' => Util_User::$intUserId,
				'user_name' => Util_User::$strUserName,
				'user_type' => Util_User::$intUserType,
				'status' => $this->_errno,
				'record_input' => $this->getInput(),
				'record_type' => $this->intRecordType,
				'record_key1' => Util_RecordDefine::compile($this->strRecordKey1, $this->getInput()),
			);

			$arrRet = Tieba_Service::call('marriage', 'addOpRecord', $arrInput, null, null,'post','php','utf-8');
	        if (!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
	            Bingo_Log::warning(sprintf("call marriage::addOpRecord fail. [input = %s] [ouput = %s]",
	                serialize($arrInput), serialize($arrRet)));
	            return;
	        }
		}
	}

	public function _execute() {

	}
}
?>