<?php
/**
 * Created by PhpStorm.
 * User: weiyan02    用户uid转换uk
 * Date: 2020/11/13
 * Time: 下午3:05
 */

class Util_Account
{

    /**
     * uk加密私钥
     */
    private static $_psnl_key = 'baiduuid_';
    /**
     * 手百AES加密 公钥
     */
    private static $_pub_key  = 'bdsearchbox_2011@mic';
    /**
     *
     */
    private static $_iv       = '****************';
    /**
     *
     */
    private static $_aes_ins  = null;

    /**
     *
     * @param null
     * @return object acs
     */
    private static function _aseIns(){

        if(null === self::$_aes_ins){
            $pub_key = self::$_pub_key;
            $iv = self::$_iv;
            $aes = new Alalib_Baidu_Crypt_AES();
            $enc_key = substr(md5(self::$_psnl_key . $pub_key), -16);
            $aes->setKey(strtoupper($enc_key));
            $aes->setIV($iv);
            self::$_aes_ins  = $aes;
        }

        return self::$_aes_ins;
    }

    /**
     * 根据uk 获取uid
     * @param  string uk uid加密数据
     * @return int uid
     **/
    public static function getUidByUk($uk){
        return self::_getUidByUk($uk);
    }

    /**
     * 根据uks 获取uid
     * @param  array uks = array('xxx','xxxx')
     * @return array uids
     */
    public static function getUidsByUks($uks){
        if (empty($uks)) {
            return array();
        }

        $ret = array();
        foreach ($uks as $uk) {
            $uk = strval($uk);
            $ret[$uk] = self::_getUidByUk($uk);
        }
        return $ret;
    }


    /**
     * 根据uid 获取uk（uid加密数据）
     * @param  string uid
     * @return string uk uid加密数据
     **/
    public static function getUkByUid($uid){
        return self::_getUkByUid($uid);
    }


    /**
     * 根据uids 获取uks（uid加密数据）
     * @param  array $uids = array('xxx','xxxx')
     * @return array uks uid加密数据
     */
    public static function getUksByUids($uids){
        if (empty($uids)) {
            return array();
        }

        $ret = array();
        foreach ($uids as $uid) {
            $uid = intval($uid);
            $ret[$uid] = self::_getUkByUid($uid);
        }
        return $ret;
    }

    /**
     * 根据uk 获取uid
     * @param  string uk uid加密数据
     * @return int uid
     **/
    private static function _getUidByUk($uk) {
        if (strlen($uk) <= 0) {
            return 0;
        }

        $aes = self::_aseIns();
        $ret = $aes->decrypt(self::base64url_decode($uk));
        return intval($ret);
    }

    /**
     * 根据uid 获取uk（uid加密数据）
     *
     * @param int uid
     * @return string uk uid加密数据
     **/
    private static function _getUkByUid($uid) {
        if (! $uid) {
            return "";
        }
        $aes = self::_aseIns();
        $enc = $aes->encrypt($uid);
        return self::base64url_encode($enc);
    }

    /**
     * 处理特殊字符
     * @param string
     * @return string
     **/
    function base64url_encode($str) {
        return rtrim(strtr(base64_encode($str), '+/', '-_'), '=');
    }

    /**
     * 处理特殊字符
     * @param string
     * @return string
     **/
    function base64url_decode($str) {
        return base64_decode(str_pad(strtr($str, '-_', '+/'), strlen($str) % 4, '=', STR_PAD_RIGHT));
    }
}