<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2017/12/19
 * Time: 17:19
 */
class Util_Rpc {

    const FETCH_TIMEOUT = 5;
    const FETCH_CONNECTTIMEOUT = 1;

    /**
     * @param $url
     * @param string $method
     * @param array $params
     * @return bool|mixed
     */
    public static function fetchUrl($url, $method = 'get', $params = array())
    {
        $options = array(
            'timeout' => self::FETCH_TIMEOUT * 1000,
            'conn_timeout' => self::FETCH_CONNECTTIMEOUT * 1000,
        );

        $proxy = Orp_FetchUrl::getInstance($options);
        if('get' == $method) {
            $ret = $proxy->get($url);
        } else {
            $ret = $proxy->post($url, $params);
        }

        if(false === $ret) {
            Bingo_Log::warning('fetch '. $url. ' fail, errno='. $proxy->errno(). ', error='. $proxy->errmsg());
        }
        return $ret;
    }


}
