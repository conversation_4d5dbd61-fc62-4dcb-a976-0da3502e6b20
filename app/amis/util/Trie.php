<?php
/**
 * @file 一个权限认证的库
 * @date 2017-05-27
 * <AUTHOR>
 * 使用教程:
 * $trie = new Util_Trie();
 * $trie.add(
 * 	array(
 * 	   "admin:user:edit:*"
 * 	   "admin:user:view"
 *  )
 * );
 * $trie.check('admin:user:view'); // true
 * $trie.check('admin:user:edit:name'); // true
 * 
 */
class Util_Trie{
	protected $data = array();

	/**
	 * 将已有的权限清空
	 * @return [type] [description]
	 */
	public function reset() {
		$this->data = array();
		return $this;
	}

	/**
	 * 往对象里面加权限，入参类型为任意个string
	 * @return [type] [description]
	 */
	public function add() {
		$args = func_get_args();

		foreach ($args as $key => $arg) {
			if (empty($arg)) {
				continue;
			}

			if (is_string($arg)) {
				$this->data = self::_add($this->data, explode(':', $arg));
			} else if(is_array($arg)) {
				foreach ($arg as $key => $value) {
					$this->data = self::_add($this->data, explode(':', $value));
				}
			}
		}

		return $this;
	}

	/**
	 * 检查是否有某个权限
	 * @param  [type] $string [description]
	 * @return [type]         [description]
	 */
	public function check($string) {
		if (empty($string) || !is_string($string)) {
			return false;
		}

		if (strpos($string, ',') !== false) {
			$arrExpandString = self::_expand($string);
			$newExpandString = array();
			foreach ($arrExpandString as $key => $permission) {
				$newExpandString[$key] = self::_check($this->data, explode(':', $permission));
			}
		}

		return self::_check($this->data, explode(':', $string));
	}

	/**
	 * 得到权限的数据
	 * @return [type] [description]
	 */
	public function get() {
		return $this->data;
	}

	/**
	 * [permissions description]
	 * @param  [type] $string [description]
	 * @return [type]         [description]
	 */
	public function permissions($string) {
		if (!is_string($string)) {
			return array();
		}

		return self::_permissions($this->data, explode(':', $string));
	}

	/**
	 * [_add description]
	 * @param [type] $trie  [description]
	 * @param [type] $array [description]
	 */
	private static function _add($trie, $array) {
		$i = 0; $j = 0; $node = null; $prevNode = null; $values = null; $goRecursive = null;

		$node = &$trie;
		$goRecursive = false;

		$intArrCount = count($array);
		for ($i = 0; $i < $intArrCount; $i++) {
			$values = explode(',', $array[$i]);

			$intValueCount = count($values);
			for ($j = 0; $j < $intValueCount; $j++) {
				$strPerm = $values[$j];
				if (!isset($node[$strPerm])) {
					$node[$strPerm] = array();
			    }

			    if ($intValueCount > 1) {

			    	$goRecursive = $goRecursive ? $goRecursive : array_slice($array, $i + 1);

  					$node[$strPerm] = self::_add($node[$strPerm], $goRecursive);

  					$i = $intArrCount;
  				} else {
  					$prevNode = &$node;
  					$node = &$node[$strPerm];
  				}
			}
		}

		if (!$goRecursive && (!$prevNode || !$prevNode['*'])) {
			$node['*'] = array();
		}

		return $trie;
	}

	/**
	 * [_check description]
	 * @param  [type] $trie  [description]
	 * @param  [type] $array [description]
	 * @return [type]        [description]
	 */
	private static function _check($trie, $array) {
		$node = $trie;

		$intArrCount = count($array);
		if ($intArrCount < 1 || $array[$intArrCount - 1] !== '*') {
			array_push($array, '*');
			$intArrCount++;
		}

		for ($i = 0; $i < $intArrCount; $i++) {
			$value = $array[$i];

			if (isset($node['*'])) {
			  
			  if (count(array_keys($node['*'])) === 0) {
			    return true;
			  }

			  // otherwise we have to go deeper
			  $node = $node['*'];
			} else {
			  // if the wanted permission is not found, we return false
			  if (!isset($node[$value])) {
			    return false;
			  }

			  // otherwise we go deeper
			  $node = $node[$value];
			}
		}
		// word (array) was found in the trie. all good!
		return true;
	}

	/**
	 * [_permissions description]
	 * @param  [type] $trie  [description]
	 * @param  [type] $array [description]
	 * @return [type]        [description]
	 */
	private static function _permissions($trie, $array) {
		if (!$trie || !$array || !is_array($trie) || !is_array($array) || count($trie) < 1 || count($array) < 1) {
			return array();
		}

		$array = array_merge($array);

		if (isset($trie['*'])) {
			return array('*');
		}

		$current = array_shift($array);

		if ($current === '?') {
			$results = array_keys($trie);
			if (count($array) > 0) {
		  		$anyObj = array();

		  		$newResults = array();
		  		foreach ($results as $k => $node) {
		  			$anyObj[$node] = self::_expandTrie($trie[$node], $array);
				}

		  		foreach ($results as $k => $node) {
		  			if (count($anyObj[$node]) > 0) {
		  				$newResults[] = $node;
		  			}
		  		}

		  		return $newResults;
			}
			
			return $results;
		}

		if ($current === '$') {
			$results = array();

			foreach ($trie as $node => $v) {
				$results = array_merge($results, self::_permissions($trie[$node], array_merge($array)));
			}

			$u = array_unique($results);

		    for ($i = count($u) - 1; $i >= 0; $i--) {
		      if ($u[$i] === '*') {
		      	array_splice($u, $i, 1);
		      }
		    }
		    return $u;
		}

		if (isset($trie[$current])) {
			// we have to go deeper!
			return self::_permissions($trie[$current], $array);
		}

		return array();
	}
	/**
	 * [_expand description]
	 * @param  [type] $permission [description]
	 * @return [type]             [description]
	 */
	private static function _expand($permission) {
		$results = array();
		$parts = explode(':', $permission);
		$parCount = count($parts);
		for ($i = 0; $i < $parCount; $i++) { 
			$alternatives = explode(',', $parts[$i]);

			if (count($results) === 0) {
				$results = $alternatives;
			} else {
				$newAlternatives = array();
				foreach ($alternatives as $k1 => $alternative) {
					$newResults = array();
					foreach ($results as $k2 => $perm) {
						$newResults[$k2] = $perm.':'.$alternative;
					}

					$newAlternatives[$k1] = $newResults;
				}

				$alternatives = $newAlternatives;

				$results = array_merge(array_unique($alternatives));
			}
		}
		return $results;
	}

	/**
	 * [_expandTrie description]
	 * @param  [type] $trie  [description]
	 * @param  [type] $array [description]
	 * @return [type]        [description]
	 */
	private static function _expandTrie($trie, $array) {
		$a = array_merge($array);

		$newTrie = array();
		foreach ($trie as $node => $v) {
			if ($node === '*') {
		      $newTrie = array($node);
		    }

		    if ($array[0] === $node || $array[0] === '$') {
		    	if (count($array) <= 1) {
		    		$newTrie = array($node);
		    	}

		    	$child = self::_expandTrie($trie[$node], array_splice($array, 1));

		    	$newChild = array();

		    	foreach ($child as $key => $inner) {
		    		$newChild[$key] = $node.':'.$inner;
		    	}

		    	$newTrie = $newChild;
		    }
		}

		$last = array();
		foreach ($newTrie as $key => $value) {
			$last = array_merge($last, $value);
		}

		return $last;
	}
}
?>