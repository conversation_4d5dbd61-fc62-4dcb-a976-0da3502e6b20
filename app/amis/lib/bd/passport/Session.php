<?php

/* * *************************************************************************
 * 
 * Copyright (c) 2011 Baidu.com, Inc. All Rights Reserved
 * 
 * ************************************************************************ */
/**
 * @file Bd/Passport/Session.php
 * <AUTHOR>
 * @date 2011/05/23 21:43:49
 * @brief
 *
 * */

/**
 * @brief    Bd_Passport Session服务交互实现
 */
class Bd_Passport_Session implements Bd_Passport_IError {
    // /* session2 command start */
    // /* 普通的SESSION操作，以SID为交互标识 */

    const CMD_SSN2_GET_SID = 0x01001;  //获取空用户信息的session标识
    const CMD_SSN2_LOGIN = 0x01002;  //登陆，创建用户相关session
    const CMD_SSN2_LOGOUT = 0x01003;  //退出登陆，session失效
    const CMD_SSN2_GET_DATA = 0x01004;  //获取session信息
    const CMD_SSN2_MOD_DATA = 0x01005;  //修改session数据

    // /* 普通SESSION操作，以用户ID为交互标识 */
    const CMD_SSN2_UID_GET_INFO = 0x01101;  //获取用户登陆信息（全）
    const CMD_SSN2_UID_GET_INFO_STAT = 0x01102;  //获取用户登陆状态
    const CMD_SSN2_UID_GET_INFO_TIME = 0x01103;  //获取用户登陆时间

    // /* 管理员操作，根据用户ID为教务标识 */
    const CMD_SSN2_ADMIN_OFFLINE = 0x01201;  //对一个用户ID执行下线操作
    const CMD_SSN2_ADMIN_MOD_DATA = 0x01203;  //对一个用户ID修改session数据

    // /* 统一安全登录相关 */
    const CMD_SSN2_AUTH_LOGIN = 0x01006;  //安全认证，产品线隐含登陆
    const CMD_SSN2_AUTH_GET_DATA = 0x01007;  //安全认证，获取session数据
    const CMD_SSN2_AUTH_MOD_DATA = 0x01008;  //安全认证，修改session数据
    // /* session2 command end */


    // /* session2 server return start */
    const RET_SSN2_OK = 0x000;
    const RET_SSN2_IP_UNAUTHORIZED = 0x001;
    const RET_SSN2_INVALID_SID = 0x002;
    const RET_SSN2_INVALID_PARAM = 0x003;
    const RET_SSN2_LOGIN_EXCEED = 0x004;
    const RET_SSN2_SERVER_BUSY = 0x010;
    const RET_SSN2_UNKNOWN = 0x012;
    const RET_SSN2_OFFLINE = 0x020;
    const RET_SSN2_ONLINE = 0x021;
    const RET_SSN2_ONLINE_MULTI = 0x022;
    const RET_SSN2_TPL_UNAUTHORIZED = 0x030;
    const RET_SSN2_AUTH_LOGIN_ERROR_EXCEED = 0x031;
    const RET_SSN2_CHECK_TOKEN_FAILED = 0x032;
    // /* session server return end */
    const PUBLIC_DATA_LEN = 32;
    const IS_REAL_NAME = 2;
    const IS_UNREAL_NAME = 1;
    const IS_REAL_NAME_UNSET = 0;

    public static $errmsgMap = array(
        0  => '[Session]OK',
        1  => '[Session]Unauthorized Ip and Apid',
        2  => '[Session]Invalid SID',
        3  => '[Session]Invalid Params',
        4  => '[Session]Login Exceed',
        16 => '[Session]Server Busy',
        18 => '[Session]Internal Error',
        32 => '[Session]Offline',
        33 => '[Session]Online',
        34 => '[Session]Multi Online',
        48 => '[Session]Unauthorized TPL',
        49 => '[Session]Auth Login Exceed',
        50 => '[Session]Check Token Failed',
    );
    protected static $_instance = null;
    protected static $_apid = null;
    protected static $_tpl = null;
    protected static $_pass = null;
    // /** use $_pass @ bae-env */
    protected static $_is_orp = false;
    protected static $_errno = 0;
    protected static $_errmsg = '';

    /**
     * [isError description]
     * @return boolean [description]
     */
    public function isError() {
        return self::$_errno === 0 ? false : true;
    }

    /**
     * [getCode description]
     * @return [type] [description]
     */
    public function getCode() {
        return self::$_errno;
    }

    /**
     * [getMessage description]
     * @return [type] [description]
     */
    public function getMessage() {
        if (isset(self::$errmsgMap[self::$_errno])) {
            self::$_errmsg = self::$errmsgMap[self::$_errno];
        } else {
            self::$_errmsg = 'Unknown Error';
        }

        return self::$_errmsg;
    }

    /**
     * [__construct description]
     */
    protected function __construct() {
        self::$_apid = Bd_Passport_Conf::getConf('apid');
        self::$_tpl = Bd_Passport_Conf::getConf('tpl');
        self::$_pass = Bd_Passport_Conf::getConf('pass');
        self::$_is_orp = intval(Bd_Passport_Conf::getConfEx('is_orp', 0)) === 1 ? true : false;

        if (is_null(self::$_apid)) {
            Bd_Passport_Log::warning("Apid for session Not Found!", -1);
        }

        if (!defined('LOG_ID')) {
            define('LOG_ID', Bd_Passport_Util::getLogId());
        }

        if (!defined('CLIENT_IP')) {
            define('CLIENT_IP', Bd_Passport_Util::getClientIp());
        }
    }

    /**
     * [getInstance description]
     * @return [type] [description]
     */
    public static function getInstance() {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }

    /**
     * [query description]
     * @param  [type] $strMethod [description]
     * @param  [type] $arrInput  [description]
     * @return [type]            [description]
     */
    protected static function query($strMethod, $arrInput) {
        $ins = Bd_Passport_Interact::getEngine('Ral');

        //Fix Clientip . NET-ORDER , Int32(Neither UInt32 nor Long)
        $arrInput['clientip'] = Bd_Passport_Util::ip2int(CLIENT_IP);

        //If @ORP , Need Pass(ie, Session Key)
        if (self::$_is_orp) {
            $arrInput['pass'] = self::$_pass;
        }

        //If the session server is busy , Just retry once.
        $retry = 1;

        while ($retry >= 0) {
            // Do Interaction CALL HERE !!!
            $ret = $ins->Call('session', $strMethod, $arrInput);

            if (is_array($ret) && isset($ret['status'])) {
                self::$_errno = $ret['status'];
                //if (self::$_errno != 0) {
                //	Bd_Passport_Log::warning(self::$errmsgMap[self::$_errno], self::$_errno);
                //}
                if (self::RET_SSN2_SERVER_BUSY === $ret['status']) {
                    Bd_Passport_Log::warning(self::$errmsgMap[self::$_errno], self::$_errno);
                    --$retry;
                    continue;
                } else {
                    return $ret;
                }
            } else {
                //Let errno not defined , getMessage will return 'Unknown error'.
                Bd_Passport_Log::warning('Unknown Error', self::$_errno);
                self::$_errno = -1;

                return false;
            }
        }

        return false;
    }


    /**
     * 获取用户信息
     *
     * @param string $bduss 登录状态编号
     * @param bool $incomplete_user 是否支持半帐号（第三方登录帐号）
     * @param bool $need_cinfo 是否获取自定义字段：
     *                                        loginfrom  登录来源产品线标示，如：tb （贴吧）
     * @param bool $quick_user 是否支持快推注册用户（快推用户仅有用户名和密码）
     * @param bool $voice_user 是否支持声纹登录用户（声纹登录安全性较低，切勿用在对安全性有要求的功能内）
     * @param bool $weak_bduss 是否支持弱bduss（表示安全级别较低的登录状态）
     * @param int $out_link 统计标识字段（使用前请与Pass数据组联系），是否为外链查询，标记为外链将不计入统计
     *                          0 : 默认
     *                          1 ：外链，不计入统计
     *                          2 : 非外链，计入统计
     * @param string $fromtype 统计标识字段（使用前请与Pass数据组联系），访问来源类型，为一下指定字符串之一：
     *                          PC : 从pc浏览器访问
     *                          WAP : 从手机浏览器访问
     *                          NACLIENT : 从手机客户端访问
     *                          NA_ANDROID : android应用，不区分平台使用NACLIENT
     *                          NA_IOS : ios应用，不区分平台使用NACLIENT
     *                          NA_WP : winphone应用，不区分平台使用NACLIENT
     *                          PCCLIENT : 从电脑客户端访问
     *                          BS : 从百度框访问应用，不区分平台使用WAP
     * @return array/null
     */
    public function getData($bduss, $incomplete_user = 0, $need_cinfo = 0,
                            $quick_user = 0, $voice_user = 0, $weak_bduss = 0,
                            $out_link = 0, $fromtype = '') {
        $arrInput = array(
            'cmd'             => self::CMD_SSN2_GET_DATA,
            'apid'            => self::$_apid,
            'clientip'        => ip2long(CLIENT_IP),
            'bduss'           => $bduss,
            'incomplete_user' => $incomplete_user,
            'quick_user'      => $quick_user,
            'need_cinfo'      => $need_cinfo,
            'voice_user'      => $voice_user,
            'weak_bduss'      => $weak_bduss,
            'out_link'        => $out_link,
            'fromtype'        => $fromtype,
        );

        return self::query(__FUNCTION__, $arrInput);
    }

    /**
     * 修改gdata
     *
     * @param $bduss
     * @param $gdata
     * @param $gmask
     * @param $pdata
     * @param $pmask
     * @return bool
     */
    public function modData($bduss, $gdata, $gmask, $pdata, $pmask) {
        $arrInput = array(
            'cmd'        => self::CMD_SSN2_MOD_DATA,
            'apid'       => self::$_apid,
            'clientip'   => ip2long(CLIENT_IP),
            'bduss'      => $bduss,
            '(raw)gdata' => $gdata,
            '(raw)gmask' => $gmask,
            '(raw)pdata' => $pdata,
            '(raw)pmask' => $pmask,
        );

        return self::query(__FUNCTION__, $arrInput);
    }

    /**
     * [uidGetInfo description]
     * @param  [type] $arrUids [description]
     * @return [type]          [description]
     */
    public function uidGetInfo($arrUids) {
        foreach ($arrUids as $id) {
            if (!is_int($id)) {
                return false;
            }
        }
        $id_ct = count($arrUids);
        $arrInput = array(
            'cmd'      => self::CMD_SSN2_UID_GET_INFO,
            'apid'     => self::$_apid,
            'clientip' => ip2long(CLIENT_IP),
            'uid_cnt'  => $id_ct,
            'uids'     => $arrUids,
        );

        return self::query(__FUNCTION__, $arrInput);
    }

    /**
     * [uidGetInfoStat description]
     * @param  [type] $arrUids [description]
     * @return [type]          [description]
     */
    public function uidGetInfoStat($arrUids) {
        foreach ($arrUids as $id) {
            if (!is_int($id)) {
                return false;
            }
        }
        $id_ct = count($arrUids);
        $arrInput = array(
            'cmd'      => self::CMD_SSN2_UID_GET_INFO_STAT,
            'apid'     => self::$_apid,
            'clientip' => ip2long(CLIENT_IP),
            'uid_cnt'  => $id_ct,
            'uids'     => $arrUids,
        );

        return self::query(__FUNCTION__, $arrInput);
    }

    /**
     * [uidGetInfoTime description]
     * @param  [type] $arrUids [description]
     * @return [type]          [description]
     */
    public function uidGetInfoTime($arrUids) {
        foreach ($arrUids as $id) {
            if (!is_int($id)) {
                return false;
            }
        }
        $id_ct = count($arrUids);
        $arrInput = array(
            'cmd'      => self::CMD_SSN2_UID_GET_INFO_TIME,
            'apid'     => self::$_apid,
            'clientip' => ip2long(CLIENT_IP),
            'uid_cnt'  => $id_ct,
            'uids'     => $arrUids,
        );

        return self::query(__FUNCTION__, $arrInput);
    }


    /**
     * [authGetData description]
     * @param  [type]  $strBduss        [description]
     * @param  [type]  $stoken          [description]
     * @param  integer $incomplete_user [description]
     * @param  integer $need_cinfo      [description]
     * @return [type]                   [description]
     */
    public function authGetData($strBduss, $stoken, $incomplete_user = 0, $need_cinfo = 0) {

        $arrInput = array(
            'cmd'             => self::CMD_SSN2_AUTH_GET_DATA,
            'apid'            => self::$_apid,
            'clientip'        => ip2long(CLIENT_IP),
            'tpl'             => self::$_tpl,
            'bduss'           => strval($strBduss),
            'stoken'          => strval($stoken),
            'incomplete_user' => $incomplete_user,
            'need_cinfo'      => $need_cinfo,
        );

        return self::query(__FUNCTION__, $arrInput);
    }

    /**
     * [authModData description]
     * @param  [type] $strBduss [description]
     * @param  [type] $stoken   [description]
     * @param  [type] $gdata    [description]
     * @param  [type] $gmask    [description]
     * @param  [type] $pdata    [description]
     * @param  [type] $pmask    [description]
     * @return [type]           [description]
     */
    public function authModData($strBduss, $stoken, $gdata, $gmask, $pdata, $pmask) {

        $arrInput = array(
            'cmd'        => self::CMD_SSN2_AUTH_MOD_DATA,
            'apid'       => self::$_apid,
            'clientip'   => ip2long(CLIENT_IP),
            'tpl'        => self::$_tpl,
            'bduss'      => strval($strBduss),
            'stoken'     => strval($stoken),
            '(raw)gdata' => $gdata,
            '(raw)gmask' => $gmask,
            '(raw)pdata' => $pdata,
            '(raw)pmask' => $pmask,
        );

        return self::query(__FUNCTION__, $arrInput);
    }

    /**
     * [parseGData description]
     * @param  [type] $gdata [description]
     * @return [type]        [description]
     */
    public function parseGData($gdata) {
        $arrData = unpack('C*', $gdata);

        if (count($arrData) != self::PUBLIC_DATA_LEN) {
            return false;
        }
        $arrTest = array();
        foreach ($arrData as $key => $val) {
            $arrTest[] = $val;
        }
        $arrData = $arrTest;

        $arrGData = array(
            'verifyQuestion'   => ($arrData[0] & 0x01) >> 0,
            'verifyMobile'     => ($arrData[0] & 0x02) >> 1,
            'verifyEmail'      => ($arrData[0] & 0x04) >> 2,
            'passwordWeak'     => ($arrData[0] & 0x08) >> 3,
            'passwordRemember' => ($arrData[0] & 0x10) >> 4,
            'openSpace'        => ($arrData[0] & 0x20) >> 5,
            'openApp'          => ($arrData[3] & 0x01) >> 0,
            'passwordExweak'   => ($arrData[3] & 0x02) >> 1,
            'openFavo'         => ($arrData[3] & 0x04) >> 2,
            'openSuperPC'      => ($arrData[3] & 0x08) >> 3,
            'pwd_protected'    => ($arrData[0] & 0x01) >> 0,
            'verified_mobil'   => ($arrData[0] & 0x02) >> 1,
            'verified_email'   => ($arrData[0] & 0x04) >> 2,
            'weak_pwd'         => ($arrData[0] & 0x08) >> 3,
            'rem_pwd'          => ($arrData[0] & 0x10) >> 4,
            'space'            => ($arrData[0] & 0x20) >> 5,
            'sex'              => ($arrData[0] & 0xc0) >> 6,
            'appstore'         => ($arrData[3] & 0x01) >> 0,
            'weakest_pwd'      => ($arrData[3] & 0x02) >> 1,
            'search'           => ($arrData[3] & 0x04) >> 2, // favo
            'super'            => ($arrData[3] & 0x08) >> 3,
            'search2'          => ($arrData[3] & 0x10) >> 4,
            'new_super'        => ($arrData[3] & 0x20) >> 5,
            'old_user'         => ($arrData[3] & 0x40) >> 6,
            'usersource'       => ($arrData[4] & 0x3f),
            'account'          => ($arrData[4] & 0xff),
            'device'           => ($arrData[5] & 0xff),
            'incomplete_user'  => ($arrData[6] & 0x01) >> 0,
            'user_type'        => $arrData[7], //  	0：老版注册账户；1：真实注册账户；2：半账号；3：快推账号
            
            // /**
            //  * 用户实名化标记,用于判定用户是否进行过实名化.
            //  *
            //  * int isRealName
            //  *     2: 实名用户 Bd_Passport_Session::IS_REAL_NAME
            //  *     1: 非实名用户
            //  *     0: 尚未判定
            //  *
            //  * Passport 判定方式: 有验证手机或有绑定手机即返回2.
            //  * 注意事项: 如产品线有其他实名化渠道(请联系法务确定),能够将帐号与真实用户关联,也可作为实名化依据.
            //  */
            
            'isRealName'       => ($arrData[8] & 0xc0) >> 6,
        );

        return $arrGData;
    }

}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100 */
