<?php

/**
 * 用户角色相关, 与amis角色管理中的角色对应
 */
class Lib_Ala_Define_Role {

    const ROLE_ADMIN            = 1;   //管理员
    const ROLE_OPERATOR         = 2;   //合作运营
    const ROLE_UNION_PRESIDENT  = 3;   //公会会长
    const ROLE_CHECK_PUSH_SHOUBAI = 4;   //手百推送主播审核人员

    const ROLE_OPERATOR_MANAGE_MAX        = 10000;   //代运营最多管理的公会长数

    const ROLE_TEAM_LEADER_MANAGE_MAX     = 100000;   //公会主播数

    //顺序需要从权限最高到最低排列
    public static $arrRole = array(
        '管理员'   => self::ROLE_ADMIN,
        '合作运营'   => self::ROLE_OPERATOR,
        '公会会长' => self::ROLE_UNION_PRESIDENT,
        '审核人员' =>self::ROLE_CHECK_PUSH_SHOUBAI,
    );

    //角色id 对应的amis里的角色id
    public static $arrAmisRoleId = array(
         self::ROLE_ADMIN           => 554,
         self::ROLE_OPERATOR        => 1278,
         self::ROLE_UNION_PRESIDENT => 885,
        self::ROLE_CHECK_PUSH_SHOUBAI => 3056,

    );

    const ROLE_OPERATE_USER_ANCHOR_LEVEL_PERCENT = 0.05;      //用户修改主播等级次数占管理人数比例

}

