<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Service.php
 * <AUTHOR>
 * @date 2014/01/04 14:23:37
 * @brief 
 *  
 **/

class Libs_Data_Multi {
    private $_strRalMultiKey = '';
    private $_objRalMulti = null;
    private $_arrMultiOutput = array();

    function __construct($strRalMultiKey) {
        $this->_strRalMultiKey = $strRalMultiKey;
        $this->_objRalMulti = new Tieba_Multi($this->_strRalMultiKey);
    }

    public function register($strKey, $arrInput, $objCaller=NULL) {
        if ($this->_objRalMulti === NULL) {
            Bingo_Log::warning('initializing ral_multi failure');
            return false;
        }
        if ($strKey === '' ) {
            Bingo_Log::warning('null multi key error');
            return false;
        }
        if (isset($objCaller)) {
            $this->_objRalMulti->register($strKey, $objCaller, $arrInput);
        } else {
            $strServiceName = $arrInput['service_name'];
            if (!isset($arrInput['ie'])) {
                $arrInput['ie'] = 'utf-8';
            }
            $this->_objRalMulti->register($strKey, new Tieba_Service($strServiceName), $arrInput);
        }

        return true;
    }

    public function call() {
        if ( $this->_objRalMulti === NULL ){
            Bingo_Log::warning('initializing ral_multi failure');
        } else {
            Bingo_Timer::start('multi_'.$this->_strRalMultiKey);
            $this->_arrMultiOutput = $this->_objRalMulti->call();
            Bingo_Timer::end('multi_'.$this->_strRalMultiKey);
        }
    }

    public function getResult($strKey) {
        return $this->_arrMultiOutput[$strKey];
    }

    public function getAllResult() {
        return $this->_arrMultiOutput;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
