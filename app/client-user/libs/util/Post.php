<?php


class Libs_Util_Post{
    /**
     * @desc   处理Media信息
     * @param
     * @return array
     */
    public static function procThreadMedia($arrMedia) {
        $arrMediaResult = array();
        if (isset($arrMedia) && !empty($arrMedia)) {
            $arrMediaItem = $arrMedia[0];
            $arrSpec = array(
                'pic_spec'  =>  'whfpf=360,360,50;q=80;g=0',
            );
            if ('pic' == $arrMediaItem['type']) {
                $arrMediaResult['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_IMG;
                $arrMediaResult['small_pic'] = Molib_Util_PicUrl::getPicUrl($arrMediaItem['big_pic'], $arrSpec);
            } else if ('flash' == $arrMediaItem['type']) {
                $arrMediaResult['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_EMBED;
                $arrMediaResult['small_pic'] = Molib_Util_PicUrl::getPicUrl($arrMediaItem['vpic'], $arrSpec);
            }
        }
        return $arrMediaResult;
    }

    // 百家号文章
    public static function procUgcMedia($arrUgcid, $arrUgcInfo) {
        // 选一张图
        $arrMediaResult = array();
        if ($arrUgcid['type'] == Molib_Util_ShareThread::BJH_CONTENT_TYPE_ARTICLE) {
            // 文章获取封面图
            $media          = Molib_Util_ShareThread::getBjhArticleCover($arrUgcInfo);
            $arrMediaResult = array(
                'type'      => Molib_Util_RichText_Parser::SLOT_TYPE_IMG,
                'small_pic' => $media['small_pic'],
            );
        } else if ($arrUgcid['type'] == Molib_Util_ShareThread::BJH_CONTENT_TYPE_TW_UCG) {
            $media = Molib_Util_ShareThread::getTwUcgImageList($arrUgcInfo);
            if (!empty($media[0])) {
                $arrMediaResult = array(
                    'type' => Molib_Util_RichText_Parser::SLOT_TYPE_IMG,
                    'small_pic' => $media[0]['small_pic'],
                );
            }
        } else if ($arrUgcid['type'] == Molib_Util_ShareThread::BJH_CONTENT_TYPE_VIDEO_UCG ||
            $arrUgcid['type'] == Molib_Util_ShareThread::BJH_CONTENT_TYPE_VIDEO) {
            $arrMediaResult = array(
                'type' => Molib_Util_RichText_Parser::SLOT_TYPE_IMG,
                'small_pic' => $arrUgcInfo['video_info']['thumbnail_url'],
            );
        }

        return $arrMediaResult;
    }

         //处理引用帖子富文本，add by llm
    /**
     * @param void
     * @return boolean
     */
    public static  function _processQuoteContent($strContent,$arrClassicSmileTransData,$arrSmile2TextTransData,$strClientVersion,$oriContentStyle='str'){
        $parserCondition = new Molib_Util_RichTextParserCondition();
        $parserCondition->intNewLineCount = 1;

        if ($oriContentStyle == 'str') { // html格式的文本
            $objParser = new Molib_Util_RichTextParser();
            $objResult = $objParser->process($parserCondition,$strContent);
        } else { // 解析出来的结构化文本
            $objParserStructured = new Molib_Util_RichText_ParserStructured();
            $objParserStructured->setClientVersion($strClientVersion);

            $objResult     = $objParserStructured->process($parserCondition, $strContent, true, 0, 0, true, true);
        }

        $arrContent = $objResult->arrContent;
        if(empty($arrContent)){
            return $strContent;
        }else{
            $strProcessedContent = '';
            //$strClientVersion = $this->_objRequest->getCommonAttr('client_version');
            $needReplaceFaceText = Molib_Util_Version::compare("7.1.0", $strClientVersion) >= 0;
            foreach ($arrContent as $slot){
                if ($slot['type'] == Molib_Util_RichTextParser::SLOT_TYPE_SMILE){//表情
                    $strFaceName = '';
                    if (isset ( $arrClassicSmileTransData [$slot['text']] )) {
                        $strFaceName = $needReplaceFaceText ? '#('.$arrClassicSmileTransData [$slot['text']].')' : '['.$arrClassicSmileTransData [$slot['text']].']';
                    }
                    if (isset ( $arrSmile2TextTransData [$slot['text']] )) {
                        $strFaceName = $needReplaceFaceText ? '#('.$arrSmile2TextTransData [$slot['text']].')' : '['.$arrSmile2TextTransData [$slot['text']].']';
                    }
                    if ('' == $strFaceName) {
                        $strFaceName = "[表情]";
                    }
                    $strProcessedContent .= $strFaceName;
                }else if($slot['type'] == Molib_Util_RichTextParser::SLOT_TYPE_IMG){//图片
                    $strProcessedContent .= "[图片]";
                }else if($slot['type'] == Molib_Util_RichTextParser::SLOT_TYPE_AT){//@某人
                    $strProcessedContent .= $slot['text'];
                }else if($slot['type'] == Molib_Util_RichTextParser::SLOT_TYPE_TEXT){//文本
                    $strProcessedContent .= $slot['text'];
                }else if($slot['type'] == Molib_Util_RichTextParser::SLOT_TYPE_LINK){//链接
                    $strProcessedContent .= "[网址]";
                }else if($slot['type'] == Molib_Util_RichTextParser::SLOT_TYPE_BDHD){//视频
                    $strProcessedContent .= "[视频]";
                }else if($slot['type'] == Molib_Util_RichTextParser::SLOT_TYPE_EMBED){//flash
                    $strProcessedContent .= "[视频]";
                } else if ($slot['type'] == Molib_Util_RichTextParser::SLOT_TYPE_SMILE3) {//表情商店新表情
                    $strProcessedContent .= "[" . $slot['c'] . "]";
                } else if ($slot['type'] == Molib_Util_RichTextParser::SLOT_TYPE_GRAFFITI) {//涂鸦
                    $strProcessedContent .= "[涂鸦]";
                }
            }
            return $strProcessedContent;
        }
    }
}
    
