<?php
//app/search/forum/client/server/mocuser/actions/user/search_double_followsAction.php
///c/u/user/search_double_follows
class search_double_followsAction extends Molib_Client_BaseAction
{
	private $arrUnameList = array();
	private $_res_user_infos = array();
	private $_error = array();

    /**
     * @return array
     */
	protected function _getPrivateInfo()
	{
		$arrPrivateInfo = array();
		$arrPrivateInfo['limit'] = intval(Bingo_Http_Request::get('limit', 100));
		$arrPrivateInfo['offset'] = intval(Bingo_Http_Request::get('offset', 0));
		$strQuery = strval(Bingo_Http_Request::get('user_name',''));
		$arrPrivateInfo['user_name'] = Molib_Util_Encode::convertGBKToUTF8($strQuery);
		$arrPrivateInfo['check_login'] = true;
		$arrPrivateInfo['need_login'] = true;
		return $arrPrivateInfo;
	}

    /**
     * @return bool
     */
	protected function _checkPrivate()
	{
		return true;
	}
	
	protected function _execute()
	{
		$ret = $this->_coreExecute();
		if($ret == false) {
			return false;
		}
		//设置结果
		$this->_setResponse();
	}

    /**
     * @return bool
     */
	private function _coreExecute() {

		if ($this->_getUserSug()){
			if (!empty($this->arrUnameList) && is_array($this->arrUnameList)){
				$arrRequest['user_name'] = $this->arrUnameList;
				
				$arrOut = Tieba_Service::call('user', 'getUidByUnames', $arrRequest, NULL, NULL, 'post', 'php', 'utf-8');
				if ($arrOut === false || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS){
					Bingo_Log::warning("call user:getUidByUnames fail. input =".serialize($arrRequest)."output=".serialize($arrOut));
				}
				foreach ($arrOut['output']['uids'] as $arrItem) {
					$arrRequest['user_id'][] = $arrItem['user_id'];
				}
				$arrRes = Molib_Tieba_Service::call('user', 'mgetUserData', $arrRequest);
				if ($arrRes === false || $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS){
					Bingo_Log::fatal("call user:mgetUserData fail. input =".serialize($arrRequest)."output=".serialize($arrRes));
				}
				foreach ($arrRes['user_info'] as $intKey => $arrItem) {
					$intPortraitTime = intval($arrItem['portrait_time']);
					$strPortrait = Tieba_Ucrypt::encode(intval($intKey), $arrItem['user_name'], $intPortraitTime);
					$arrUser = array();
					$arrUser['user_id'] = $intKey;
					$arrUser['user_name'] = $arrItem['user_name'];
					$arrUser['portrait'] = $strPortrait;
					$arrUser['sex'] = $arrItem['user_sex'];
					$this->_res_user_infos[] = $arrUser;
				}
			}
		}
		
		$this->_error['errno'] = Tieba_Errcode::ERR_SUCCESS;
		$usermsg = Tieba_Error::getUserMsg($arrRes['errno']);
		$usermsg = Bingo_Encode::convert($usermsg,Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
		$this->_error['errmsg'] = '';
		$this->_error['usermsg'] = $usermsg;
		
        return true;       
	}

    /**
     * @return bool
     */
	private function _getUserSug(){
		$intUid = $this->_objRequest->getCommonAttr('user_id',0);
		$strQuery = strtolower($this->_objRequest->getPrivateAttr('user_name', ''));
		if (!empty($strQuery) && !empty($intUid)){
			$strQuery = Molib_Util_Encode::convertUTF8ToGBK($strQuery);
			$strUrl = "/at-sug?query={$strQuery}&uid={$intUid}&from=tbapi";
            if($this->getUserInfo($intUid,$strUrl) === false){
                return false;
            }
			$strOut = Tbapi_Core_Midl_Http::httpcall('at_suggest_http',$strUrl,array());
			if ($strOut === false){
				Bingo_Log::warning('httpcall:at_suggest_http error. input[query:'.$strQuery.'uid:'.$intUid.'] output['.serialize($strOut).']');
				return false;
			}else {
				$strOut = Molib_Util_Encode::convertGBKToUTF8($strOut);
				$arrOut = Bingo_String::json2array($strOut,Bingo_Encode::ENCODE_UTF8);
				if (empty($arrOut) || !is_array($arrOut)){
					Bingo_Log::warning('json to array error. input[query:'.$strQuery.'uid:'.$uid);
					return false;
				}else if (is_array($arrOut['msg']) && !empty($arrOut['msg'])){
					foreach ($arrOut['msg'] as $strUname){
						$this->arrUnameList[] = $strUname;
					}
					return true;
				}
			}
		}
		return false;
	}

    /**
     * @param $uid
     * @param $url
     * @return bool|string
     */
    private function getUserInfo($uid, &$url){
        $input=array(
            "user_id" => $uid,
            "type"    => 2,
            "offset"  => 0,
            "limit"   => 500,
        );
        $urRes = Tieba_Service::call('user', 'getFollowAndFollowedByUid',$input, null, null, 'post', 'php');
        if($urRes['errno'] != 0){
            return false;
        }
        if(empty($urRes['concern']['user_infos'])){
            return false;
        }

        foreach($urRes['concern']['user_infos'] as $val){
            $url.='&un='.$val['user_name'];
        }
        return $url;
    }

    private function _setResponse()
	{
		$arrRes = array('error' => $this->_error);
		if (!empty($this->_res_user_infos)){
			$arrRes['res_user_infos'] = $this->_res_user_infos;
		}
		$this->_objResponse->setOutData($arrRes);
		$this->_stLog();
	}
	
	private function _stLog()
	{
	}
}