<?php

class Actions_Component_Lottery_Util {

    /**
     * @brief 
     *
     * @param $award_info
     *
     * @return 
     */
    public static function transferAwardInfoRd2Fe($award_info) {
        //Bingo_Log::warning(print_r($award_info, true));
        $award_info['award_rate'] = $award_info['calc_rule']['probability'];
        unset($award_info['calc_rule']);
        $award_info['award_desc'] = $award_info['desc_text'];
        $award_info['award_num'] = (int)$award_info['award_count'];
        $award_info['award_cur_count'] = (int)$award_info['award_cur_count'];
        $award_info['award_pic'] = $award_info['desc_pic'];
        return $award_info;
    }

    /**
     * @brief 
     *
     * @param $award_info
     *
     * @return 
     */
    public static function transferAwardInfoFe2Rd($award_info) {
        $award_id = (int)$award_info['award_id'];
        if (0 < $award_id) {
            // edit award
            $award_info['award_cur_count'] = $award_info['award_num'];
            $award_info['desc_text'] = $award_info['award_desc'];
            $award_info['calc_rule']['probability'] = $award_info['award_rate'];
            $strKeys = $award_info['award_code'];
            if (!empty($strKeys)) {
                $strKeys = str_replace("\r\n", "\n", $strKeys);
                $award_info['keys'] = explode("\n", $strKeys);
            }
        } else {
            // add award
            $award_info['award_count'] = $award_info['award_num'];
            $award_info['desc_text'] = $award_info['award_desc'];
            $award_info['calc_rule']['probability'] = $award_info['award_rate'];
            $award_info['desc_pic'] = $award_info['award_pic'];
            $award_info['calc_type'] = 1;
            $strKeys = $award_info['award_code'];
            if (!empty($strKeys)) {
                $strKeys = str_replace("\r\n", "\n", $strKeys);
                $award_info['keys'] = explode("\n", $strKeys);
            }
        }
        return $award_info;
    }

}
