Security reminder: If you have PHP's register_globals option set, you must
turn it off. MediaWiki will no longer work with it enabled.

== MediaWiki 1.24 ==

MediaWiki 1.24.0 is the stable branch and is recommended for use in production.

MediaWiki 1.24 is a large release that contains many new features and bug
fixes. This is the full list of changes in this version.

Our thanks go to everyone who helped to improve MediaWiki by testing the beta
release and submitting bug reports.

=== Configuration changes in 1.24 ===
* Setting $wgAllowSiteCSSOnRestrictedPages to true is necessary if you want to
  use on-wiki CSS modifications (e.g MediaWiki:Common.css) on
  Special:UserLogin or Special:Preferences.
* MediaWiki will no longer run if register_globals is enabled. It has been
  deprecated for 5 years now, and was removed in PHP 5.4. For more information
  about why, see <https://www.mediawiki.org/wiki/register_globals>.
* MediaWiki now requires PHP's iconv extension. openSUSE users may need to
  install the php5-iconv package. Users of other systems may need to add
  extension=iconv.so to php.ini or recompile PHP without --without-iconv.
* MediaWiki will no longer function if magic quotes are enabled. It has
  been deprecated for 5 years now, and was removed in PHP 5.4.
* The server's canonical hostname is available as $wgServerName, which is
  exposed in both mw.config and ApiQuerySiteInfo.
* Introduced $wgPagePropsHaveSortkey as a backwards-compatibility switch,
  for using the old schema of the page_props table, in case the respective
  schema update was not applied.
* $wgSearchEverythingOnlyLoggedIn was removed as the 'searcheverything'
  user option was removed. Use $wgNamespacesToBeSearchedDefault instead or
  if you used to have $wgDefaultUserOptions['searcheverything'] = 1.
* $wgMasterWaitTimeout has been deprecated.
* $wgDBClusterTimeout has been removed.
* $wgProxyKey has been removed. It is no longer used by MediaWiki core.
  Ensure $wgSecretKey is set in LocalSettings.php.
* $wgExtraInterlanguageLinkPrefixes is a new configuration variable that
  contains an array of interwiki prefixes that should be treated as language
  prefixes (i.e. turned into interlanguage links when $wgInterwikiMagic is set
  to true).
* $wgParserTestRemote has been removed.
* $wgCountTotalSearchHits has been removed. If you're concerned about efficiency
  of search, you should use something like CirrusSearch instead of built in
  search.
* Users in the 'sysop' group have access to Special:MergeHistory by default.
* $wgFileStore was removed after having been deprecated in 1.17. Alternative
  configurations are $wgDeletedDirectory and $wgHashedUploadDirectory.
* The deprecated $wgUseCommaCount variable has been removed.
* $wgEnableSorbs and $wgSorbsUrl have been removed.
* The UserCryptPassword and UserComparePassword hooks are no longer called.
  Any extensions using them must be updated to use the Password Hashing API.
* $wgCompiledFiles has been removed.
* $wgSortSpecialPages was removed, the listing on Special:SpecialPages is
  now always sorted.
* $wgSpecialPages may now use callback functions as an alternative to plain class names.
  This allows more control over constructor parameters.
* $wgHTCPMulticastAddress, $wgHTCPMulticastRouting and $wgHTCPPort were removed.
* $wgRC2UDPAddress, $wgRC2UDPInterwikiPrefix, $wgRC2UDPOmitBots, $wgRC2UDPPort
  and $wgRC2UDPPrefix have been removed.
* The default password type for MediaWiki has been changed from MD5 to PBKDF2.
  Password hashes will automatically be updated as users log in. If necessary, the
  old MD5 hashing can be restored by changing $wgPasswordDefault to 'B'. In addition,
  there is a maintenance script wrapOldPassword.php that can wrap all passwords in
  PBKDF2 (or the hashing algorithm of your choice) if you don't want to wait for your
  users to log in.
* $wgImportSources can now either be a regular array, or an associative map
  specifying subprojects on the interwiki map of the target wiki, or a mix of
  the two. Existing configurations will still work.
* Users must be able to edit through a page's protection to be able to delete it.
* The default thumb size ($wgDefaultUserOptions['thumbsize']) is now 300px, up from
  180px. If you have altered the number of entries in $wgThumbLimits for your wiki, you
  may need to adjust your default user settings to compensate for the index change.
* $wgDeferredUpdateList is now deprecated, you should use DeferredUpdates::addUpdate()
  instead.
* $wgCanonicalLanguageLinks has been removed. Per Google recommendations, we
  will not send a rel=canonical pointing to a variant-neutral page, however
  we will send rel=alternate.
* $wgResourceLoaderLESSFunctions has been deprecated and will be removed in the future.
* $wgGoToEdit has been removed. Use the SpecialSearchNogomatch hook for similar
  functionality.
* $wgMangleFlashPolicy was added to make MediaWiki's mangling of anything that
  might be a flash policy directive configurable.

=== New features in 1.24 ===
* Added new hook WatchlistEditorBeforeFormRender, allowing subscribers to
  manipulate the list of pages and/or preload lots of data at once.
* Added new argument &$link in hook WatchlistEditorBuildRemoveLine, allowing the
  link to the title to be changed.
* Added a new hook, "WhatLinksHereProps", to allow extensions to annotate
  WhatLinksHere entries.
* Added a new hook, "ContentGetParserOutput", to customize parser output for
  a given content object.
* Deprecated the hook "ShowRawCssJs", use "ContentGetParserOutput" instead.
* HTMLForm's HTMLTextField now supports the 'url' type.
* HTMLForm fields may now be dynamically hidden based on the values of other
  fields in the form.
* HTMLForm now supports multiple copies of an input field or set of input
  fields, e.g. the form may request "one or more usernames" without having to
  have the user enter delimited list of names into a text field.
* Added a new hook, "SidebarBeforeOutput", to allow to edit the structure of
  the sidebar just before its display.
* (bug 49156) Added the mediawiki.cookie ResourceLoader module, which wraps
  jquery.cookie so that getting/setting a cookie is syntactically and
  functionally similar to using the WebRequest::getCookie() and
  WebResponse::setcookie() methods.
* (bug 44740) jQuery upgraded from 1.8.3 to 1.11.1. A new configuration option,
  $wgIncludejQueryMigrate, also loads the jQuery Migrate hack to let extensions
  and gadgets use the long-deprecated functions that were removed in jQuery 1.9.
  This option is turned off by default, and will be removed in MediaWiki 1.25.
* (bug 47076) jQuery UI upgraded from 1.8.24 to 1.9.2.
* Changes to content typography (fonts, etc.). See
  https://www.mediawiki.org/wiki/Typography_refresh for further information.
* WikitextContent will now render redirects with the expected "redirect"
  header, rather than as an ordered list. Code calling Article::viewRedirect
  can probably be changed to no longer special-case redirects.
* Header font set to a serif font stack. See
  https://www.mediawiki.org/wiki/Typography_refresh for further information.
* (bug 65567) Added a new hook, "BeforeHttpsRedirect", to allow cancellation of
  the HTTP to HTTPS redirect due to forceHTTPS cookie, userRequires, etc. This
  is only for page views, since this hook doesn't affect UserLogin, OAuth,
  CentralAuth, etc. ATTENTION: This hook is likely to be removed soon due to
  overall design of the system.
* (bug 17367) It is now possible to add pages to your watchlist from
  Special:UnwatchedPages without reloading the special page.
* New methods setVolatile and isVolatile are added to PPFrame, so that
  extensions such as Cite.php can mark that their output is volatile and
  shouldn't be cached.
* (bug 52817) Advanced search options are now saved on the search page itself,
  rather than in a dedicated pane in the preferences panel.
* (bug 44591) The dropdown actions menu (little triangle next to page tabs) in
  the Vector skin has gained a label that should make it more discoverable.
* MWCryptHKDF added for fast, cryptographically secure random number generation
  that won't deplete openssl's entropy pool.
* ResourceLoader: File modules can now provide a skip function that uses an
  inline feature test to bypass loading of the module.
* (bug 20210) Special pages may now provide autocompletion of their subpage
  names in search suggestions. Right now the only useful implementation is in
  Special:Log, but more are to come.
* Special:MostLinkedTemplates is no longer limited to transclusions from the
  Template namespace.
* Skins can now use 'remoteSkinPath' when defining ResourceLoader modules.
  This works the same as 'remoteExtPath' but is relative to the skins/ folder
  instead of the extensions/ folder.
* Added the json2.js polyfill for the ES5 JSON.stringify and JSON.parse methods.
  Exposed as module "json" with a skip function to optimise loading.
* Extensions and skins may now use 'namemsg' in $wgExtensionCredits in addition
  to 'name', to allow for the name to be localizable. 'name' should still be
  specified for backwards-compatibility and to define the path Special:Version
  uses to find extension license information.
* Browser tests are now included to verify basic wiki functionality in developer
  environments. For details on running tests, see tests/browser/README.mediawiki.
* Upgrade jStorage to v0.4.10.
* {{!}} is now a magic word that produces the | character. This removes the need
  for Template:! for purposes such as passing pipes inside of parameters.
* (bug 20790) The block log snippet on Special:Contributions and while
  editing user and user talk pages now works for IP range blocks.
* (bug 9360) Added ability to change the page language for MediaWiki pages using
  Special:PageLanguage. All pages are set to wiki language by default.
  The feature needs to be enabled with $wgPageLanguageUseDB=true and
  permission needs to be set for 'pagelang'.
* Upgrade Moment.js to v2.8.3.
* (bug 67042) Added support for the HTML5 <rtc> tag for East Asian typography.
* Upgrade Sinon.JS to 1.10.3.
* Added the es5-shim polyfill for older or non-compliant javascript engines.
* Upgrade jQuery Cookie to v1.3.1.
* (bug 20476) Add a "viewsuppressed" user right to be able to view
  suppressed content but not suppress it ("suppressrevision" right).
* (bug 66440) The MediaWiki web installer will now allow you to choose the skins
  to enable (from the ones included in download tarball) and decide which one
  should be the default.
* (bug 68085, 68802) Links like [[localInterwikiPrefix:languageCode:pageTitle]],
  where localInterwikiPrefix is a member of the $wgLocalInterwikis array, will
  no longer be displayed in the sidebar when $wgInterwikiMagic is true. In a
  similar way, links like [[localInterwikiPrefix:File:Image.png]] and
  [[localInterwikiPrefix:Category:Hello]] will now render as regular links, and
  will not include the file or add the page to the category.
* New special page, MyLanguage, to redirect users to subpages with localised
  versions of a page. (Integrated from Extension:Translate)
* MediaWiki now supports multiple password types, including bcrypt and PBKDF2.
  The default type can be changed with $wgPasswordDefault and the type
  configurations can be changed with $wgPasswordConfig.
* Skins can now define custom styles for default ResourceLoader modules using
  the $wgResourceModuleSkinStyles global. See the Vector skin for examples.
* (bug 4488) There is now a preference to watch pages where the user has
  rollbacked an edit by default.
* (bug 15484) Users will now be redirected to the login page when they need to
  log in, rather than being shown a page asking them to log in and having to click
  another link to actually get to the login page.
* A JsonContent and JsonContentHandler were added for extensions to extend.
* (bug 35045) Redirects to sections will now update the URL in browser's address
  bar using the HTML5 History API. When [[Dog]] redirects to [[Animals#Dog]],
  the user will now see "Animals#Dog" in their browser instead of "Dog#Dog".
* API token handling has been rewritten. Any API module using tokens will need
  to be updated. See the entry below under "Action API internal changes".
* Added HTMLAutoCompleteSelectField.
* Added a new hook, "SkinPreloadExistence", to allow extensions to add titles to
  link existence cache before the page is rendered.
* Config::set() was moved to its own interface, MutableConfig. GlobalVarConfig::set()
  is now deprecated, does not implement MutableConfig.
* A MutableConfig named HashConfig was added, that stores an array of configuration
  settings.
* (bug 69418) A MultiConfig implementation was added that supports fallback
  to multiple Config instances.
* Update CSSJanus to v1.1.0.
* Added FormatJson::parse() returning status with result or localized error message

=== Bug fixes in 1.24 ===
* (bug 70686) More sensible behavior when special page aliases conflict
* (bug 71040) Add Oracle version of update-keys.sql
* (bug 64912, 64922) mw.Title: Add new static methods `newFromFileName`,
  `newFromUserInput`
* MediaWikiVersionFetcher::fetchVersion() now supports semantic versions.
* (bug 50572) MediaWiki:Blockip should support gender
* (bug 49116) Footer copyright notice is now always displayed in user language
  rather than content language (same as copyright notice for editing interface).
* (bug 62258) A bug was fixed in File::getUnscaledThumb when a height
  restriction was present in the parameters. Images with both the "frame"
  option and a size specification set will now always ignore the provided
  size and display an unscaled image, as the documentation has always
  claimed it would.
* (bug 39035) Improved Vector skin performance by removing collapsibleNav,
  which used to collapse some sidebar elements by default.
  This removes -list id suffixes like p-lang-list: instead of using things like
  #p-lang-list, you can do #p-lang .body ul.
* (bug 890) Links in Special:RecentChanges and Special:Watchlist no longer
  follow redirects to their target pages.
* Parser now dies early if called recursively, instead of producing subtle bugs.
* (bug 14323) Redirect pages, when viewed with redirect=no, no longer hide the
  remaining page content.
* (bug 52587) Maintenance script deleteBatch.php no longer follows redirects
  in the file namespace and delete the file on the target page. It will still
  however delete the redirect page.
* (bug 22683) {{msgnw:}} and other uses of PPFrame::RECOVER_ORIG will correctly
  recover the original code of extension tags.
* (bug 65757) MSSQL: Update script drops unnamed constraints to be prepared
  for future updates. Because it's doing so heuristically, it may fail or drop
  wrong constraints.
* (bug 67870) wfShellExec() cuts off stdout at multiples of 8192 bytes.
* $wgRunJobsAsync now works with private wikis (e.g. read requires login).
* (bugs 57238, 65206) Blank pages can now be directly created.
* (bug 69789) Title::getContentModel() now loads from the database when
  necessary instead of incorrectly returning the default content model.
* (bug 69249) wfBaseConvert() now works around PHP Bug #50175 when using GMP.
* (bug 57909) URLs in the externallinks table will no longer have certain
  characters decoded in the query string.
* (bug 67368) LESS mixins like .background-image() correctly flip image
  references for RTL stylesheets now.
* (bugs 66776, 71478) SECURITY:  User PleaseStand reported a way to inject code
  into API clients that used format=php to process pages that underwent flash
  policy mangling. This was fixed along with improving how the mangling was done
  for format=json, and allowing sites to disable the mangling using
  $wgMangleFlashPolicy.
* (bug 70901) SECURITY: User Jackmcbarn reported that the ability to update
  the content model for a page could allow an unprivileged attacker to edit
  another user's common.js under certain circumstances. The user right
  "editcontentmodel" was added, and is needed to change a revision's content
  model.
* (bug 71111) SECURITY: User PleaseStand reported that on wikis that allow raw
  HTML, it is not safe to preview wikitext coming from an untrusted source such
  as a cross-site request. Thus add an edit token to the form, and when raw HTML
  is allowed, ensure the token is provided before showing the preview. This
  check is not performed on wikis that both allow raw HTML and anonymous
  editing, since there are easier ways to exploit that scenario.
* (bug 72222) SECURITY: Do not show log action when the entry is revdeleted with
  DELETED_ACTION. NOTICE: this may be reverted in a future release pending a
  public RFC about the desired functionality. This issue was reported by user
  Bawolff.
* (bug 71621) Make allowing site-wide styles on restricted special pages a
  config option.
* (bug 42723) Added updated version history from 1.19.2 to 1.22.13

=== Action API changes in 1.24 ===
* action=parse API now supports prop=modules, which provides the list of
  ResourceLoader modules that should be used to enhance the parsed content.
* action=query&meta=siteinfo&siprop=interwikimap returns a new "protorel"
  field which is true if protocol-relative urls can be used to access
  a particular interwiki map entry.
* list=logevents now provides logpage, which is the page ID from the
  logging table, if ids are requested and the user has the permissions.
* action=edit now requires that appendtext, prependtext, or section=new be used
  when using the 'redirect' parameter, to prevent clients accidentally
  overwriting the target page with the content of the redirect.
* list=logevents will now return an error if both letitle and leprefix are
  specified.
* list=logevents has a new parameter, lenamespace, to allow filtering by
  namespace.
* action=expandtemplates has a new parameter, prop, and a new output format.
  The old format is still used if prop isn't provided, but this is deprecated.
* meta=userinfo can now return the count of unread pages on the watchlist.
* list=watchlist can now filter by unread status.
* The deprecated action=parse&prop=languageshtml has been removed.
* (bug 48071) action=setnotificationtimestamp no longer throws PHP or database
  errors when no pages are given.
* (bug 60734) Actions that use ApiPageSet (e.g. purge, watch,
  setnotificationtimestamp) will now include continuation information when
  using a generator.
* Removed 'props' and 'errors' from action=paraminfo, as they have extremely
  limited use and are generally inaccurate, unmaintained, and impossible to
  properly maintain.
* Formats dbg, dump, txt, wddx, and yaml are now deprecated.
* action=paraminfo now indicates when a parameter is specifying a submodule.
* The iwurl parameter to prop=iwlinks is deprecated in favor of iwprop=url, for
  parallelism with prop=langlinks.
* All tokens should be fetched from action=query&meta=tokens; all other methods
  of fetching tokens are deprecated. The value needed for meta=tokens's 'type'
  parameter for each module is documented in the action=help output and is
  returned from action=paraminfo.
* New action ClearHasMsg that can be used to clear HasMsg flag.
* The cmstartsortkey and cmendsortkey parameters to list=categorymembers are
  deprecated in favor of cmstarthexsortkey and cmendhexsortkey.
* (bug 63326) Add blockedtimestamp field to output of blockinfo property for
  the list=allusers and list=users modules.
* prop=imageinfo no longer requires iiurlwidth to be set when using iiurlparam.
* Added prop=linkshere, prop=fileusage, and prop=transcludedin, which are
  roughly equivalent to list=backlinks, list=imageusage, and list=embeddedin
  but can work on a list of titles (including titles from a generator).
* prop=redirects can now filter returned redirects by namespace.

=== Action API internal changes in 1.24 ===
* Methods for handling continuation are added to ApiResult, so actions other
  than query that use generators can easily support continuation.
* $wgAPIModules (and the related $wgAPIFormatModules, $wgAPIMetaModules,
  $wgAPIPropModules, and $wgAPIListModules settings) now allow API modules
  to be specified using a "module spec" array instead of a plain class name.
  A "module spec" is an associative array containing at least the 'class' key
  for the module's class, and optionally a 'factory' key for the factory function
  to use for the module. This is intended for extensions that want control over
  the instantiation of their API modules, to allow for proper dependency
  injection.
* A new param type 'submodule' is available. Parameters of this type will take
  the list of valid values from the module's ApiModuleManager for the group
  corresponding to the parameter name.
* The 'APIGetPossibleErrors' and 'APIGetResultProperties' hooks are no longer used.
* API token handling has been rewritten. Any API module using tokens will need
  to be updated:
  * ApiBase::needsToken now returns a token type instead of boolean true when a
    token is needed. Returning true will throw an exception. See documentation
    of that method for details.
  * Information for the 'token' parameter is automatically set by ApiBase
    getFinalParams and getFinalParamDescription.
  * ApiBase::getTokenSalt has been removed.
  * The hooks APIQueryInfoTokens, APIQueryRevisionsTokens,
    APIQueryRecentChangesTokens, APIQueryUsersTokens, and
    ApiTokensGetTokenTypes are deprecated, but are still called to support
    backwards-compatible token access.
* ApiBase::validateLimit and ApiBase::validateTimestamp are now protected.
* ApiQueryRedirects was removed; prop=redirects is now implemented by
  ApiQueryBacklinksProp along with the newly-added prop modules.
* The following methods have been deprecated and may be removed in a future
  release:
  * ApiBase::getResultProperties
  * ApiBase::getFinalResultProperties
  * ApiBase::addTokenProperties
  * ApiBase::getRequireOnlyOneParameterErrorMessages
  * ApiBase::getRequireMaxOneParameterErrorMessages
  * ApiBase::getRequireAtLeastOneParameterErrorMessages
  * ApiBase::getTitleOrPageIdErrorMessage
  * ApiBase::getPossibleErrors
  * ApiBase::getFinalPossibleErrors
  * ApiBase::parseErrors
  * ApiQuery::setGeneratorContinue
  * ApiQueryBase::checkRowCount
  * ApiQueryBase::titleToKey
  * ApiQueryBase::keyToTitle
  * ApiQueryBase::keyPartToTitle
  * ApiQueryInfo::getTokenFunctions
  * ApiQueryInfo::resetTokenCache
  * ApiQueryInfo::getEditToken
  * ApiQueryInfo::getDeleteToken
  * ApiQueryInfo::getProtectToken
  * ApiQueryInfo::getMoveToken
  * ApiQueryInfo::getBlockToken
  * ApiQueryInfo::getUnblockToken
  * ApiQueryInfo::getEmailToken
  * ApiQueryInfo::getImportToken
  * ApiQueryInfo::getWatchToken
  * ApiQueryInfo::getOptionsToken
  * ApiQueryRecentChanges::getTokenFunctions
  * ApiQueryRecentChanges::getPatrolToken
  * ApiQueryRevisions::getTokenFunctions
  * ApiQueryRevisions::getRollbackToken
  * ApiQueryUsers::getTokenFunctions
  * ApiQueryUsers::getUserrightsToken
* The following classes have been deprecated and may be removed in a future
  release:
  * ApiFormatDbg
  * ApiFormatDump
  * ApiFormatTxt
  * ApiFormatWddx
  * ApiFormatYaml
  * ApiTokens
* The following class constants have been deprecated and may be removed in a
  future release:
  * ApiBase::PROP_ROOT
  * ApiBase::PROP_LIST
  * ApiBase::PROP_TYPE
  * ApiBase::PROP_NULLABLE

=== Languages updated in 1.24 ===

MediaWiki supports over 350 languages. Many localisations are updated
regularly. Below only new and removed languages are listed, as well as
changes to languages because of Bugzilla reports.

=== Other changes in 1.24 ===
* The deprecated jquery.delayedBind ResourceLoader module was removed.
* The deprecated function mw.util.toggleToc was removed.
* The Special:Search hooks SpecialSearchGo and SpecialSearchResultsAppend
  were removed as they were unused.
* (bug 65477) User::pingLimiter() now has an additional profile point varying
  by action being used.
* mediawiki.util.$content no longer supports old versions of the Vector,
  Monobook, Modern and CologneBlue skins that don't yet implement the "mw-body"
  and/or "mw-body-primary" class name in their html.
* Added pp_sortkey column to page_props table, so pages can be efficiently
  queried and sorted by property value (bug 58032).
  See $wgPagePropsHaveSortkey if you want to postpone the schema change.
* BREAKING CHANGE: All four built-in MediaWiki skins (Vector, MonoBook, Modern
  and Cologne Blue) were moved out of MediaWiki core to their own respective
  repositories. They will be installed with the release tarball, but you must
  install them separately if installing MediaWiki from source code. A warning
  message displayed until you do it should guide you through the process. See
  also <https://www.mediawiki.org/wiki/Manual:Skin_configuration>.
* BREAKING CHANGE: Skins built for MediaWiki 1.15 and earlier that do not use
  the "headelement" template key are no longer supported. Setting
  $useHeadElement = false; is no longer supported and will not cause old keys
  like "headlinks", "skinnameclass", etc. to be defined.
* BREAKING CHANGE: The files commonElements.css, commonContent.css and
  commonInterface.css (in skins/common/) have been removed. Skins may no longer
  rely on their presence and include them in their style modules. ResourceLoader
  modules introduced in MediaWiki 1.23 should be loaded instead:
  - skins/common/commonElements.css  → 'mediawiki.skinning.elements' module
  - skins/common/commonContent.css   → 'mediawiki.skinning.content' module
  - skins/common/commonInterface.css → 'mediawiki.skinning.interface' module
* The deprecated 'SpecialVersionExtensionTypes' hook was removed.
* (bug 63891) Add 'X-Robots-Tag: noindex' header in action=render pages.
* SpecialPage no longer supports the syntax for invoking wfSpecial*() functions.
  Special pages should subclass SpecialPage and implement the execute() method.
* (bug 63755) The deprecated constants RC_MOVE and RC_MOVE_OVER_REDIRECT were
  removed.
* Special:MostLinkedTemplates has been renamed to Special:MostTranscludedPages.
* The skin autodiscovery mechanism has been deprecated and will be removed in
  MediaWiki 1.25. See https://www.mediawiki.org/wiki/Manual:Skin_autodiscovery
  for migration guide for creators and users of custom skins that relied on it.
* ResourceLoaderFileModule#getAllStyleFiles now returns all style files and all
  skin style files used by the module.
* Removed getLang() from IContextSource and subclasses. (deprecated since 1.19)
* Removed setLang() from subclasses of IContextSource. (deprecated since 1.19)
* Removed WebRequest::escapeAppendQuery(). (deprecated since 1.20)
* Removed info(), purge(), revert() and rollback() from the Article class; they
  have since become subclasses of the Action class. (deprecated since 1.19)
* SearchEngineReplacePrefixesComplete hook was removed.
* The "jquery.json" module has been deprecated. Use the "json" module instead.
* Removed HTMLForm::addJS(). (deprecated since 1.18)
* Removed LogEventsList::showHeader(). (deprecated since 1.19)
* Removed ImageGalleryBase::useSkin(). (deprecated since 1.18)
* Removed DatabaseMysqlBase::getLagFromProcesslist(). (deprecated since 1.19)
* Removed LoadBalancer::closeConnecton(). (deprecated since 1.18)
* Removed ApiBase::createContext(). (deprecated since 1.19)
* BREAKING CHANGE: The undocumented Special{$this->getName()}BeforeFormDisplay
  set of hooks has been removed and replaced by a single new hook
  SpecialPageBeforeFormDisplay.
* (bug 65781) Removed block warning on included {{Special:Contributions}}
* Removed Skin::makeGlobalVariablesScript(). (deprecated since 1.19)
* Removed MWNamespace::isMain(). (deprecated since 1.19)
* Removed Preferences::loadOldSearchNs(). (deprecated since 1.19)
* Removed OutputPage::getStatusMessage(). (deprecated since 1.18)
* Removed OutputPage::isUserJsAllowed(). (deprecated since 1.18)
* Removed Title::updateTitleProtection(). (deprecated since 1.19)
* Removed ParserOptions::setSkin(). (deprecated since 1.19)
* Removed Title::escapeCanonicalURL(). (deprecated since 1.19)
* Removed Title::escapeLocalURL(). (deprecated since 1.19)
* Removed Title::escapeFullURL(). (deprecated since 1.19)
* Removed User::isValidEmailAddr(). (deprecated since 1.18)
* Removed Title::getEscapedText(). (deprecated since 1.19)
* Removed Language::getFallbackLanguageCode(). (deprecated since 1.19)
* Removed WikiPage::isBigDeletion(). (deprecated since 1.19)
* Removed MWInit class which contained functions related to a now discontinued
  PHP compiler called hphpc. (deprecated since 1.22)
* ApiResult::enableSizeCheck() and disableSizeCheck() are now obsolete.
* Removed ResourceLoaderGetStartupModules hook. (deprecated since 1.23)
* Removed getFormFields(), onSubmit() and onSuccess() from FormlessAction, as
  these were meant specifically for FormAction instead.
* Removed Action::execute().
* Removed AjaxAddScript which has been obsolete since ResourceLoader and
  is unused by any modern extension.
* Removed maintenance/nextJobDB.php; no longer in use.
* Removed global function wfViewPrevNext(). (deprecated since 1.19)
* Removed global function xmlsafe() from Export.php. (moved to OAIRepo extension)
* Removed Title::userCanRead(). (deprecated since 1.19)
* Removed maintenance script importTextFile.php. Use edit.php script instead.
* A _from_namespace field has been added to the templatelinks, pagelinks,
  and filelinks tables. Run update.php to apply this change to the schema.
* Removed File::sha1Base36(). (deprecated since 1.19)
* Removed File::getPropsFromPath(). (deprecated since 1.19)
* Removed functions blockedPage(), noCreatePermission(), readOnlyPage() and
  userNotLoggedInPage() from EditPage.php. (deprecated since 1.19)
* Removed functions getContent(), getPreloadedText(), mergeChangesInto() and
  setPreloadedText() from EditPage.php. (deprecated since 1.21)
* Removed global functions wfArrayLookup(), wfArrayMerge(), wfDebugDieBacktrace()
  and wfTime(). (deprecated since 1.22)
* Browser support for Internet Explorer 6 and 7 lowered from Grade A to Grade C,
  meaning that JavaScript is no longer executed in these browser versions.
* Browser support for Opera 11 lowered from Grade A to Grade C.
* Removed IEFixes module which existed purely to provide support for MSIE versions
* Deprecated SpecialPageFactory::getList() in favor of
  SpecialPageFactory::getNames()
  below 7 (conditionally loaded only for those browsers).
* Action::checkCanExecute() no longer has a return value.
* Removed cleanupForIRC(), loadFromCurRow(), newFromCurRow(), notifyRC2UDP()
  and sendToUDP() from RecentChange.php. (deprecated since 1.22)
* Removed EnhancedChangesList::arrow(), sideArrow(), downArrow(), spacerArrow().
* Removed Xml::namespaceSelector(). (deprecated since 1.19)
* Removed WikiPage::estimateRevisionCount(). (deprecated since 1.19)
* MYSQL: Enum item added to "major MIME type" columns.
  Running update.php on MySQL < v5.1 may result in heavy processing.
* RSS and Atom feeds generated by MediaWiki no longer include a fallback
  stylesheet. It was ignored by most browsers these days anyway.
* SpecialSearchNoResults hook has been removed. SpecialSearchResults is now
  called unconditionally.
* TablePager::getBody() is now 'final' and can't be overridden in subclasses.
* TablePager::getBody() is deprecated, use getBodyOutput() or getFullOutput().
* Added $outputPage parameter to the SkinTemplateGetLanguageLink hook.
* log_page for move log entries store the original page ID, rather than that
  of the new redirect page. This is not retroactive.
* LCStoreAccel was removed. $wgLocalisationCacheConf can no longer be set to
  use this store class.
* Html::infoBox() no longer accepts paths relative to skins/common/images/.
* Deprecated defunct Skin::getCommonStylePath().
* Some extensions had their ResourceLoader modules depend on the "mediawiki"
  and "jquery" modules. In the past, this behavior was undefined, now it will
  throw an error.
* Removed BagOStuff::replace(). (deprecated since 1.23)
* In Linker.php, link(), linkText() and makeBrokenImageLinkObj() now display
  warnings if their first parameter is not a Title object. Also makeImageLink()
  now requires a Parser as its first parameter.
* (bug 67368) LESS functions embed() and embeddable(), added in MediaWiki 1.23
  and broken by design, have been removed. Use appropriate LESS mixins instead.
* Removed cssjanus.py from maintenance directory as it was unused.
* Removed maintenance/purgeOldText.inc and the PurgeRedundantText() function
  it contained (superseded by Maintenance::purgeRedundantText() in 1.16).
  The purgeOldText.php maintenance script has been retained.
* (Bug 70630) PHPUnit tests can be found by directory discovery, by adding the
  directory path from your UnitTestsList callback. Older versions of
  MediaWiki core will barf at this usage.

==== Renamed classes ====
* CLDRPluralRuleConverter_Expression to CLDRPluralRuleConverterExpression
* CLDRPluralRuleConverter_Fragment to CLDRPluralRuleConverterFragment
* CLDRPluralRuleConverter_Operator to CLDRPluralRuleConverterOperator
* CLDRPluralRuleEvaluator_Range to CLDRPluralRuleEvaluatorRange
* CSSJanus_Tokenizer to CSSJanusTokenizer
* MediaWiki_I18N to MediaWikiI18N
* Parser_DiffTest to ParserDiffTest
* RevDel_ArchiveItem to RevDelArchiveItem
* RevDel_ArchiveList to RevDelArchiveList
* RevDel_ArchivedFileItem to RevDelArchivedFileItem
* RevDel_ArchivedFileList to RevDelArchivedFileList
* RevDel_ArchivedRevisionItem to RevDelArchivedRevisionItem
* RevDel_FileItem to RevDelFileItem
* RevDel_FileList to RevDelFileList
* RevDel_Item to RevDelItem
* RevDel_List to RevDelList
* RevDel_LogItem to RevDelLogItem
* RevDel_LogList to RevDelLogList
* RevDel_RevisionItem to RevDelRevisionItem
* RevDel_RevisionList to RevDelRevisionList
* WebInstaller_Complete to WebInstallerComplete
* WebInstaller_Copying to WebInstallerCopying
* WebInstaller_DBConnect to WebInstallerDBConnect
* WebInstaller_DBSettings to WebInstallerDBSettings
* WebInstaller_Document to WebInstallerDocument
* WebInstaller_ExistingWiki to WebInstallerExistingWiki
* WebInstaller_Install to WebInstallerInstall
* WebInstaller_Language to WebInstallerLanguage
* WebInstaller_Name to WebInstallerName
* WebInstaller_Options to WebInstallerOptions
* WebInstaller_Readme to WebInstallerReadme
* WebInstaller_ReleaseNotes to WebInstallerReleaseNotes
* WebInstaller_Restart to WebInstallerRestart
* WebInstaller_Upgrade to WebInstallerUpgrade
* WebInstaller_UpgradeDoc to WebInstallerUpgradeDoc
* WebInstaller_Welcome to WebInstallerWelcome

==== Removed classes ====
* IPBlockForm - Use SpecialBlock directly
* WatchlistEditor - Use SpecialEditWatchlist directly
* FormatExif - Use FormatMetadata directly
* RevertFileAction - Use RevertAction directly
* HistoryPage - Use HistoryAction directly
* RawPage - Use RawAction directly
* StubContLang - Use Language::factory() instead
* XMLReader2 - Use XMLReader directly
* ResourceLoaderLESSFunctions - No longer in use, not intended for public usage

==== Removed files ====
The skins/common/ directory, previously containing some assets intended to be
used by skins and a number of legacy styles and scripts, has been removed. Its
contents have been deleted or relocated into the resources/ directory. Full list
of files that are no longer available follows.

* skins/common/ajax.js
* skins/common/commonContent.css
* skins/common/commonElements.css
* skins/common/commonInterface.css
* skins/common/commonPrint.css
* skins/common/config-cc.css
* skins/common/config.css
* skins/common/config.js
* skins/common/feed.css
* skins/common/IEFixes.js
* skins/common/oldshared.css
* skins/common/protect.js
* skins/common/shared.css
* skins/common/upload.js
* skins/common/wikibits.js
* skins/common/images/add.png
* skins/common/images/ajax-loader.gif
* skins/common/images/arrow_disabled_first_25.png
* skins/common/images/arrow_disabled_last_25.png
* skins/common/images/arrow_disabled_left_25.png
* skins/common/images/arrow_disabled_right_25.png
* skins/common/images/arrow_first_25.png
* skins/common/images/arrow_last_25.png
* skins/common/images/arrow_left_25.png
* skins/common/images/arrow_right_25.png
* skins/common/images/Arr_.png
* skins/common/images/Arr_d.png
* skins/common/images/Arr_l.png
* skins/common/images/Arr_r.png
* skins/common/images/Arr_u.png
* skins/common/images/bullet.gif
* skins/common/images/button_bold.png
* skins/common/images/button_extlink.png
* skins/common/images/button_headline.png
* skins/common/images/button_hr.png
* skins/common/images/button_image.png
* skins/common/images/button_italic.png
* skins/common/images/button_link.png
* skins/common/images/button_media.png
* skins/common/images/button_nowiki.png
* skins/common/images/button_sig.png
* skins/common/images/button_template.png
* skins/common/images/cc-0.png
* skins/common/images/cc-by-nc-sa.png
* skins/common/images/cc-by-sa.png
* skins/common/images/cc-by.png
* skins/common/images/Checker-16x16.png
* skins/common/images/closewindow.png
* skins/common/images/closewindow19x19.png
* skins/common/images/critical-32.png
* skins/common/images/diffunderline.gif
* skins/common/images/download-32.png
* skins/common/images/feed-icon.png
* skins/common/images/feed-icon.svg
* skins/common/images/gnu-fdl.png
* skins/common/images/help-question-hover.gif
* skins/common/images/help-question.gif
* skins/common/images/info-32.png
* skins/common/images/link_icon.gif
* skins/common/images/magnify-clip-rtl.png
* skins/common/images/magnify-clip.png
* skins/common/images/mediawiki.png
* skins/common/images/nextredirectltr.png
* skins/common/images/nextredirectrtl.png
* skins/common/images/poweredby_mediawiki_88x31.png
* skins/common/images/public-domain.png
* skins/common/images/question-small.png
* skins/common/images/question.svg
* skins/common/images/redirectltr.png
* skins/common/images/redirectrtl.png
* skins/common/images/remove.png
* skins/common/images/spinner.gif
* skins/common/images/tick-32.png
* skins/common/images/tipsy-arrow.gif
* skins/common/images/tooltip_icon.png
* skins/common/images/warning-32.png
* skins/common/images/wiki.png
* skins/common/images/Zoom_sans.gif
* skins/common/images/ar/button_bold.png
* skins/common/images/ar/button_headline.png
* skins/common/images/ar/button_italic.png
* skins/common/images/ar/button_link.png
* skins/common/images/ar/button_nowiki.png
* skins/common/images/be-tarask/button_bold.png
* skins/common/images/be-tarask/button_italic.png
* skins/common/images/be-tarask/button_link.png
* skins/common/images/cyrl/button_bold.png
* skins/common/images/cyrl/button_italic.png
* skins/common/images/cyrl/button_link.png
* skins/common/images/de/button_bold.png
* skins/common/images/de/button_italic.png
* skins/common/images/fa/button_bold.png
* skins/common/images/fa/button_headline.png
* skins/common/images/fa/button_italic.png
* skins/common/images/fa/button_link.png
* skins/common/images/fa/button_nowiki.png
* skins/common/images/icons/fileicon-c.png
* skins/common/images/icons/fileicon-cpp.png
* skins/common/images/icons/fileicon-deb.png
* skins/common/images/icons/fileicon-djvu.png
* skins/common/images/icons/fileicon-djvu.xcf
* skins/common/images/icons/fileicon-dvi.png
* skins/common/images/icons/fileicon-exe.png
* skins/common/images/icons/fileicon-h.png
* skins/common/images/icons/fileicon-html.png
* skins/common/images/icons/fileicon-iso.png
* skins/common/images/icons/fileicon-java.png
* skins/common/images/icons/fileicon-mid.png
* skins/common/images/icons/fileicon-mov.png
* skins/common/images/icons/fileicon-o.png
* skins/common/images/icons/fileicon-ogg.png
* skins/common/images/icons/fileicon-ogg.xcf
* skins/common/images/icons/fileicon-pdf.png
* skins/common/images/icons/fileicon-ps.png
* skins/common/images/icons/fileicon-psd.png
* skins/common/images/icons/fileicon-rm.png
* skins/common/images/icons/fileicon-rpm.png
* skins/common/images/icons/fileicon-svg.png
* skins/common/images/icons/fileicon-tar.png
* skins/common/images/icons/fileicon-tex.png
* skins/common/images/icons/fileicon-ttf.png
* skins/common/images/icons/fileicon-txt.png
* skins/common/images/icons/fileicon.png
* skins/common/images/ksh/button_S_italic.png

== Compatibility ==

MediaWiki 1.24 requires PHP 5.3.2 or later. There is experimental support for
HHVM 3.3.0.

MySQL is the recommended DBMS. PostgreSQL or SQLite can also be used, but
support for them is somewhat less mature. There is experimental support for
Oracle and Microsoft SQL Server.

The supported versions are:

* MySQL 5.0.2 or later
* PostgreSQL 8.3 or later
* SQLite 3.3.7 or later
* Oracle 9.0.1 or later
* Microsoft SQL Server 2005 (9.00.1399)

== Upgrading ==

1.24 has several database changes since 1.23, and will not work without schema
updates. Note that due to changes to some very large tables like the revision
table, the schema update may take quite long (minutes on a medium sized site,
many hours on a large site).

If upgrading from before 1.11, and you are using a wiki as a commons
repository, make sure that it is updated as well. Otherwise, errors may arise
due to database schema changes.

If upgrading from before 1.7, you may want to run refreshLinks.php to ensure
new database fields are filled with data.

If you are upgrading from MediaWiki 1.4.x or earlier, you should upgrade to
1.5 first. The upgrade script maintenance/upgrade1_5.php has been removed
with MediaWiki 1.21.

Don't forget to always back up your database before upgrading!

See the file UPGRADE for more detailed upgrade instructions.

For notes on 1.23.x and older releases, see HISTORY.

== Online documentation ==

Documentation for both end-users and site administrators is available on
MediaWiki.org, and is covered under the GNU Free Documentation License (except
for pages that explicitly state that their contents are in the public domain):

	https://www.mediawiki.org/wiki/Documentation

== Mailing list ==

A mailing list is available for MediaWiki user support and discussion:

	https://lists.wikimedia.org/mailman/listinfo/mediawiki-l

A low-traffic announcements-only list is also available:

	https://lists.wikimedia.org/mailman/listinfo/mediawiki-announce

It's highly recommended that you sign up for one of these lists if you're
going to run a public MediaWiki, so you can be notified of security fixes.

== IRC help ==

There's usually someone online in #mediawiki on irc.freenode.net.
