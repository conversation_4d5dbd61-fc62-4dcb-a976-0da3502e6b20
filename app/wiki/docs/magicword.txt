magicword.txt

Magic Words are some phrases used in the wikitext. They are used for two things:
* Variables (like {{PAGENAME}}, {{SERVER}}, ...): part of wikitext, that looks
  like templates but that don't accept any parameter.
* Parser functions (like {{fullurl:...}}, {{#special:...}}): behaves like 
  functions and accepts parameters.

The localized arrays keys are the internal name, and the values are an array, 
whose include their case-sensitivity and their alias forms. The first form 
defined is used by the program, for example, when moving a page and its old name
should include #REDIRECT.

They can be added in several arrays:
* By adding a file to $wgExtensionMessagesFiles and defining there $magicWords.
  This array is associative with the language code in the first dimension key
  and then a "normal" array of magic words.
* Localized arrays (languages/messages/LanguageXX.php) include their different 
  names to be used by the users.

To add a new variable, you should use the "MagicWordwgVariableIDs" hook to add
the internal name to the $magicWords array. You'll need to define the value of
the variable with the "ParserGetVariableValueSwitch" hook.

For example to add a new variable:

Create a file called ExtensionName.i18n.magic.php with the following contents:
----
<?php

$magicWords = array();

$magicWords['en'] = array(
	// Case sensitive.
	'mag_custom' => array( 1, 'CUSTOM' ),
);

$magicWords['es'] = array(
	'mag_custom' => array( 1, 'ADUANERO' ),
);
----

$wgExtensionMessagesFiles['ExtensionNameMagic'] = __DIR__ . '/ExtensionName.i18n.magic.php';
$wgHooks['MagicWordwgVariableIDs'][] = 'wfAddCustomMagicWordID';
$wgHooks['ParserGetVariableValueSwitch'][] = 'wfGetCustomMagicWordValue';

function wfAddCustomMagicWordID( &$magicWords ) {
	$magicWords[] = 'mag_custom';
	return true;
}

function wfGetCustomMagicWordValue( &$parser, &$varCache, &$index, &$ret ){
	if( $index == 'mag_custom' ){
		$ret = $varCache['mag_custom'] = "Custom value";
	}
	return true;
}

And to add a new parser function:

Create a file called ExtensionName.i18n.magic.php with the following contents:
----
<?php

$magicWords = array();

$magicWords['en'] = array(
	// Case insensitive.
	'mag_custom' => array( 0, 'custom' ),
);

$magicWords['es'] = array(
	'mag_custom' => array( 0, 'aduanero' ),
);
----

$wgExtensionMessagesFiles['ExtensionNameMagic'] = __DIR__ . '/ExtensionName.i18n.magic.php';
$wgHooks['ParserFirstCallInit'][] = 'wfRegisterCustomMagicWord';

function wfRegisterCustomMagicWord( &$parser ){
	$parser->setFunctionHook( 'mag_custom', 'wfGetCustomMagicWordValue' );
	return true;
}

function wfGetCustomMagicWordValue( &$parser, $var1, $var2 ){
	return "custom: var1 is $var1, var2 is $var2";
}

Note: the 'ParserFirstCallInit' hook is only available since 1.12. To work with
an older version, you'll need to use an extension function.

Online documentation (contains more informations):
Magic words: https://www.mediawiki.org/wiki/Manual:Magic_words
Variables: https://www.mediawiki.org/wiki/Manual:Variable
Parser functions: https://www.mediawiki.org/wiki/Manual:Parser_functions
