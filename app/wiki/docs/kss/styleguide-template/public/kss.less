.container {
	width: 100%;
}

nav {
	display: none;
}

.content {
	.example {
		blockquote {
			margin-top: 20px;
		}
	}
}

body {
	margin: 0;
	padding: 0;
	padding-top: 3px;
	padding-bottom: 40px;

	// FIXME: Remove when typography module in mediawiki-ui
	font-family: "Nimbus Sans L", "Liberation Sans", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.kss-no-margin {
	// FIXME: Is this being used anywhere? Remove if not.
	margin: 0;
}

.container {
	margin: 0 auto;
	display: -webkit-flex;
	display: flex;

}

header {
	padding: 0;
	margin: 0;
	border-bottom: 1px solid #eee;

	hgroup {
		min-width: 149px;

		h1 {
			padding: 16px 28px;
			font-size: 15px;
			text-transform: uppercase;
			margin: 0;
			width: 92px;
			border-right: 1px solid #eee;
		}
	}
}

nav {
	-webkit-flex: initial;
	flex: initial;
	min-width: 139px;
	margin-top: 25px;

	ul {
		list-style: none;
		padding: 0;

		li {
			margin-left: 10px;
			margin-bottom: 20px;

			a {
				text-transform: uppercase;
				color: #aaa;
				font-size: 12px;
				font-weight: bold;
				text-decoration: none;

				&:hover {
					color: #538DF8;
				}

				span {
					display: inline-block;
					width: 35px;
				}
			}

			ul {
				li {
					margin: 0;
				}

				li a {
					text-transform: none;
					font-weight: normal;
				}
			}
		}
	}
}

.content {
	-webkit-flex: 1;
	flex: 1;

	h1, h2, h3, h4, h5, h6, p {
		margin-left: 20px;
	}

	p {
		width: 338px;
	}

	h1 {
		margin-bottom: 0;
	}

	.example {
		display: -webkit-flex;
		display: flex;
		flex-wrap: wrap;

		pre {
			-webkit-flex: initial;
			flex: initial;
			background: #f8f8f8;
			padding: 20px;
			color: #999;
			word-wrap: break-word;
			// word-wrap in pre not affecting Firefox, so add white-space.
			white-space: pre-wrap;
			float: left;
			margin: 0;
			margin-right: 22px;
		}

		blockquote {
			-webkit-flex: 1;
			flex: 1;
			display: block;
			margin: 0;
			margin-left: 20px;

			div {
				margin-bottom: 5px;
			}
		}
	}
}

@media (min-width: 768px) {
	nav {
		display: block;
		width: 100px;
	}

	@columnWidth: (768px - 100px ) / 2;
	.example {
		pre,
		blockquote {
			width: @columnWidth;
		}
	}
}

@media (min-width: 980px) {
	nav {
		width: auto;
	}

	.content {
		margin-left: 30px;
	}

	.container {
		width: 980px;
	}

	.example {
		pre {
			width: 338px;
		}
		blockquote {
			width: auto;
		}
	}
}
