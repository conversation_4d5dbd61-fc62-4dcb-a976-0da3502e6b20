/**
 * MediaWiki Print style sheet for CSS2-capable browsers.
 * Copyright <PERSON>, http://www.aulinx.de/
 *
 * Derived from the plone (http://plone.org/) styles
 * Copyright <PERSON>
 */

/* Thanks to A List Apart (http://alistapart.com/) for useful extras */

/**
 * Hide all the elements irrelevant for printing
 */
.noprint,
div#jump-to-nav,
.mw-jump,
div.top,
div#column-one,
#colophon,
.mw-editsection,
.mw-editsection-like,
.toctoggle,
#toc.tochidden,
div#f-poweredbyico,
div#f-copyrightico,
li#viewcount,
li#about,
li#disclaimer,
li#mobileview,
li#privacy,
#footer-places,
.mw-hidden-catlinks,
tr.mw-metadata-show-hide-extended,
span.mw-filepage-other-resolutions,
#filetoc,
.usermessage,
.patrollink,
#mw-navigation,
#siteNotice {
	display: none;
}

/**
 * Pagination
 */
.wikitable, .thumb, img {
	page-break-inside: avoid;
}

h2, h3, h4, h5, h6 {
	page-break-after: avoid;
}

p {
	widows: 3;
	orphans: 3;
}

/**
 * Generic HTML elements
 */
body {
	background: white;
	color: black;
	margin: 0;
	padding: 0;
}

ul {
	list-style-type: square;
}

h1, h2, h3, h4, h5, h6 {
	font-weight: bold;
}

dt {
	font-weight: bold;
}

p {
	margin: 1em 0;
	line-height: 1.2em;
}

pre, .mw-code {
	border: 1pt dashed black;
	white-space: pre;
	font-size: 8pt;
	overflow: auto;
	padding: 1em 0;
	background: white;
	color: black;
}

/**
 * MediaWiki-specific elements
 */
#globalWrapper {
	width: 100% !important;
	min-width: 0 !important;
}

.mw-body {
	background: white;
	border: none !important;
	padding: 0 !important;
	margin: 0 !important;
	direction: ltr;
	color: black;
}

#column-content {
	margin: 0 !important;
}

#column-content .mw-body {
	padding: 1em;
	margin: 0 !important;
}

#toc {
	border: 1px solid #aaaaaa;
	background-color: #f9f9f9;
	padding: 5px;
	display: -moz-inline-block;
	display: inline-block;
	display: table;
	/* IE7 and earlier */
	zoom: 1;
	*display: inline;
}

#footer {
	background: white;
	color: black;
	margin-top: 1em;
	border-top: 1px solid #AAA;
	direction: ltr;
}

img {
	border: none;
	vertical-align: middle;
}

/* math */
span.texhtml {
	font-family: serif;
}

/**
 * Links
 */
a.stub,
a.new {
	color: #ba0000;
	text-decoration: none;
}

a {
	color: black !important;
	background: none !important;
	padding: 0 !important;
}

a:link, a:visited {
	color: #520;
	background: transparent;
	text-decoration: underline;
}

/* Expand URLs for printing */
.mw-body a.external.text:after,
.mw-body a.external.autonumber:after {
	content: " (" attr(href) ")";
}

/* Expand protocol-relative URLs for printing */
.mw-body a.external.text[href^='//']:after,
.mw-body a.external.autonumber[href^='//']:after {
	content: " (https:" attr(href) ")";
}

/* MSIE/Win doesn't understand 'inherit' */
a,
a.external,
a.new,
a.stub {
	color: black !important;
	text-decoration: none !important;
}

/* Continue ... */
a,
a.external,
a.new,
a.stub {
	color: inherit !important;
	text-decoration: inherit !important;
}

/**
 * Floating divs
 */
div.floatright {
	float: right;
	clear: right;
	position: relative;
	margin: 0.5em 0 0.8em 1.4em;
}

div.floatright p {
	font-style: italic;
}

div.floatleft {
	float: left;
	clear: left;
	position: relative;
	margin: 0.5em 1.4em 0.8em 0;
}

div.floatleft p {
	font-style: italic;
}

div.center {
	text-align: center;
}

/**
 * Thumbnails
 */
div.thumb {
	border: none;
	width: auto;
	margin-top: 0.5em;
	margin-bottom: 0.8em;
	background-color: transparent;
}

div.thumbinner {
	border: 1px solid #cccccc;
	padding: 3px !important;
	background-color: White;
	font-size: 94%;
	text-align: center;
	overflow: hidden;
}

html .thumbimage {
	border: 1px solid #cccccc;
}

html .thumbcaption {
	border: none;
	text-align: left;
	line-height: 1.4em;
	padding: 3px !important;
	font-size: 94%;
}

div.magnify {
	display: none;
}

/* @noflip */
div.tright {
	float: right;
	clear: right;
	margin: 0.5em 0 0.8em 1.4em;
}

/* @noflip */
div.tleft {
	float: left;
	clear: left;
	margin: 0.5em 1.4em 0.8em 0;
}

img.thumbborder {
	border: 1px solid #dddddd;
}

/**
 * Galleries (see shared.css for more info)
 */
li.gallerybox {
	vertical-align: top;
	display: inline-block;
}

ul.gallery, li.gallerybox {
	zoom: 1;
	*display: inline;
}

ul.gallery {
	margin: 2px;
	padding: 2px;
	display: block;
}

li.gallerycaption {
	font-weight: bold;
	text-align: center;
	display: block;
	word-wrap: break-word;
}

li.gallerybox div.thumb {
	text-align: center;
	border: 1px solid #ccc;
	margin: 2px;
}

div.gallerytext {
	overflow: hidden;
	font-size: 94%;
	padding: 2px 4px;
	word-wrap: break-word;
}

/**
 * Diff rendering
 */
table.diff {
	background: white;
}

td.diff-otitle {
	background: #ffffff;
}

td.diff-ntitle {
	background: #ffffff;
}

td.diff-addedline {
	background: #ccffcc;
	font-size: smaller;
	border: solid 2px black;
}

td.diff-deletedline {
	background: #ffffaa;
	font-size: smaller;
	border: dotted 2px black;
}

td.diff-context {
	background: #eeeeee;
	font-size: smaller;
}

.diffchange {
	color: silver;
	font-weight: bold;
	text-decoration: underline;
}

/**
 * Table rendering
 * As on shared.css but with white background.
 */
table.wikitable,
table.mw_metadata {
	margin: 1em 0;
	border: 1px #aaa solid;
	background: white;
	border-collapse: collapse;
}

table.wikitable > tr > th, table.wikitable > tr > td,
table.wikitable > * > tr > th, table.wikitable > * > tr > td,
.mw_metadata th, .mw_metadata td {
	border: 1px #aaa solid;
	padding: 0.2em;
}

table.wikitable > tr > th,
table.wikitable > * > tr > th,
.mw_metadata th {
	text-align: center;
	background: white;
	font-weight: bold;
}

table.wikitable > caption,
.mw_metadata caption {
	font-weight: bold;
}

table.listing,
table.listing td {
	border: 1pt solid black;
	border-collapse: collapse;
}

a.sortheader {
	margin: 0 0.3em;
}

/**
 * Categories
 */
.catlinks ul {
	display: inline;
	margin: 0;
	padding: 0;
	list-style: none;
	list-style-type: none;
	list-style-image: none;
	vertical-align: middle !ie;
}

.catlinks li {
	display: inline-block;
	line-height: 1.15em;
	padding: 0 .4em;
	border-left: 1px solid #AAA;
	margin: 0.1em 0;
	zoom: 1;
	display: inline !ie;
}

.catlinks li:first-child {
	padding-left: .2em;
	border-left: none;
}

.printfooter {
	padding: 1em 0 1em 0;
}
