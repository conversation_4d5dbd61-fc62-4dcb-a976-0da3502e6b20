<?php
/**
 * JSON Schema Content Handler
 *
 * @file
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */

/**
 * @since 1.24
 */
class JsonContentHandler extends CodeContentHandler {

	public function __construct( $modelId = CONTENT_MODEL_JSON ) {
		parent::__construct( $modelId, array( CONTENT_FORMAT_JSON ) );
	}

	/**
	 * @return string
	 */
	protected function getContentClass() {
		return 'JsonContent';
	}
}
