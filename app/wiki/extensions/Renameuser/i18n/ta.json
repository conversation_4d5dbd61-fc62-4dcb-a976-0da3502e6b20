{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Karth<PERSON>.dr", "Shanmugamp7", "TRYPPN", "மதனாஹரன்"]}, "renameuser": "பயனரை பெயர்மாற்று", "renameuser-linkoncontribs": "பயனரை பெயர்மாற்று", "renameuser-linkoncontribs-text": "இந்த பயனரை பெயர்மாற்று", "renameuserold": "தற்போதைய பயனர் பெயர்:", "renameusernew": "புதிய பயனர் பெயர்:", "renameuserreason": "மறுபெயருக்கான காரணம்:", "renameusermove": "பயனர் பக்கம் மற்றும் பேச்சுப் பக்கங்களை (அவற்றின் துணைப்பக்கங்களுடன்) புதிய பெயருக்கு நகர்த்து", "renameusersuppress": "புதுப் பெயருக்கு வழிமாற்றுகளை உருவாக்க வேண்டாம்", "renameuserreserve": "எதிர்காலப் பயன்பாட்டிலிருந்து பழைய பயனர் பெயரைத் தடை செய்யவும்", "renameuserwarnings": "எச்சரிக்கை:", "renameuserconfirm": "சரி, பயனருக்கு மாற்றுப்பெயர் கொடுக்கவும்", "renameusersubmit": "சமர்ப்பி", "renameuser-submit-blocklog": "பயனாளரின் தடை உள்ளீட்டை காட்டு", "renameusererrordoesnotexist": "\"<nowiki>$1</nowiki>\" என்ற பெயரிலான பயனர் இல்லை.", "renameusererrorexists": "\"<nowiki>$1</nowiki>\" என்ற பெயரில் ஏற்கனவே பயனர் ஒருவர் உள்ளார்.", "renameusererrorinvalid": "\"<nowiki>$1</nowiki>\" என்ற பயனர் பெயர் செல்லாது.", "renameuser-error-request": "வேண்டுகோளைப் பெறுவதில் ஒரு சிக்கல்.\nதயவு செய்து பின்சென்று மீண்டும் முயலவும்.", "renameuser-error-same-user": "பயனர் பெயரை மாற்றும் போது அதே பெயரை நீங்கள் தரமுடியாது.", "renameuser-page-exists": "பக்கம் $1 ஏற்கனவே  உள்ளது. தானாக மேலெழுத இயலாது.", "renameuser-page-moved": "பக்கம் $1 $2 எனுந்தலைப்புக்கு நகர்த்தப்பட்டுள்ளது.", "renameuser-page-unmoved": "பக்கம் $1 என்பதை $2 என்பதற்கு நகர்த்த முடியவில்லை.", "log-name-renameuser": "பயனரை பெயர்மாற்றுதல் குறிப்பேடு", "log-description-renameuser": "இது பயனர் பெயர் மாற்றத்திற்கான குறிப்பேடு", "action-renameuser": "பயனரை பெயர்மாற்று", "right-renameuser": "பயனர்களை மாற்று பெயரிடு", "renameuser-renamed-notice": "இந்த பயனர் பெயர் மாற்றப்பட்டது.\nமாற்றுப்பெயரிடுதல் குறிப்பேடு குறிப்புதவிக்காக கீழே வழங்கப்பட்டுள்ளது"}