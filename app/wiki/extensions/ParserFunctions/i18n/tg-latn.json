{"@metadata": {"authors": ["<PERSON><PERSON>"]}, "pfunc_desc": "<PERSON>, das<PERSON><PERSON><PERSON> manti<PERSON> me<PERSON>", "pfunc_time_error": "Xato: zamoni ƣajrimiçoz", "pfunc_time_too_long": "Xato: #time farox<PERSON>ji be<PERSON> az had", "pfunc_rel2abs_invalid_depth": "Xato: <PERSON><PERSON><PERSON><PERSON>ji ƣajrimiçoz dar nişonī: \"$1\" (ta<PERSON><PERSON> baroi dastrasi ba jak nişonī bolotar az nişoniji reşa)", "pfunc_expr_stack_exhausted": "Xatoi ibora: <PERSON><PERSON><PERSON><PERSON> az dast raftaast", "pfunc_expr_unexpected_number": "Xatoi ibora: <PERSON><PERSON> ƣajrimuntazir", "pfunc_expr_preg_match_failure": "Xatoi ibora: Xatoi ƣajrimuntaziri preg_match", "pfunc_expr_unrecognised_word": "Xatoi ibora: <PERSON><PERSON><PERSON> \"$1\"", "pfunc_expr_unexpected_operator": "Xatoi ibora: Amalgari ƣajrimuntaziri $1", "pfunc_expr_missing_operand": "Xatoi ibora: Amalgari gumşuda baroi  $1", "pfunc_expr_unexpected_closing_bracket": "Xatoi ibora: <PERSON><PERSON><PERSON> bastai no<PERSON>", "pfunc_expr_unrecognised_punctuation": "Xatoi ibora: <PERSON><PERSON><PERSON> \"$1\"", "pfunc_expr_unclosed_bracket": "Xatoi ibora: <PERSON><PERSON><PERSON>", "pfunc_expr_division_by_zero": "<PERSON><PERSON><PERSON><PERSON> bar sifr", "pfunc_expr_unknown_error": "Xatoi ibora: <PERSON><PERSON><PERSON> ($1)", "pfunc_expr_not_a_number": "Dar $1: nati<PERSON> adad nest"}