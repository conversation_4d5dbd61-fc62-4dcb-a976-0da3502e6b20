# Force the test runner to ensure the extension is loaded
!! hooks
ref
references
!! endhooks

!! test
Simple <ref>, no <references/>
!! input
Wikipedia rocks!<ref>Proceeds of Rockology, vol. XXI</ref>
!! result
Wikipedia rocks!<sup id="cite_ref-1" class="reference"><a href="#cite_note-1">[1]</a></sup><ol class="references">
<li id="cite_note-1"><span class="mw-cite-backlink"><a href="#cite_ref-1">↑</a></span> <span class="reference-text">Proceeds of Rockology, vol. XXI</span>
</li>
</ol>

!! end

!! test
Simple <ref>, with <references/>
!! input
Wikipedia rocks!<ref>Proceeds of Rockology, vol. XXI</ref>

<references/>
!! result
<p>Wikipedia rocks!<sup id="cite_ref-1" class="reference"><a href="#cite_note-1">[1]</a></sup>
</p>
<ol class="references">
<li id="cite_note-1"><span class="mw-cite-backlink"><a href="#cite_ref-1">↑</a></span> <span class="reference-text">Proceeds of Rockology, vol. XXI</span>
</li>
</ol>

!! end


!! article
Template:Simple template
!! text
A ''simple'' template.
!! endarticle


!! test
<ref> with a simple template
!! input
Templating<ref>{{simple template}}</ref>

<references/>
!! result
<p>Templating<sup id="cite_ref-1" class="reference"><a href="#cite_note-1">[1]</a></sup>
</p>
<ol class="references">
<li id="cite_note-1"><span class="mw-cite-backlink"><a href="#cite_ref-1">↑</a></span> <span class="reference-text">A <i>simple</i> template.</span>
</li>
</ol>

!! end

!! test
<ref> with a <nowiki>
!! input
Templating<ref><nowiki>{{simple template}}</nowiki></ref>

<references/>
!! result
<p>Templating<sup id="cite_ref-1" class="reference"><a href="#cite_note-1">[1]</a></sup>
</p>
<ol class="references">
<li id="cite_note-1"><span class="mw-cite-backlink"><a href="#cite_ref-1">↑</a></span> <span class="reference-text">{{simple template}}</span>
</li>
</ol>

!! end


!! test
<ref> in a <nowiki>
!! input
Templating<nowiki><ref>{{simple template}}</ref></nowiki>

<references/>
!! result
<p>Templating&lt;ref&gt;{{simple template}}&lt;/ref&gt;
</p><p><br />
</p>
!! end

!! test
<ref> in a <!--comment-->
!! input
Templating<!--<ref>{{simple template}}</ref>-->

<references/>
!! result
<p>Templating
</p><p><br />
</p>
!! end

!! test
<!--comment--> in a <ref> (bug 5384)
!! input
Templating<ref>Text<!--comment--></ref>

<references/>
!! result
<p>Templating<sup id="cite_ref-1" class="reference"><a href="#cite_note-1">[1]</a></sup>
</p>
<ol class="references">
<li id="cite_note-1"><span class="mw-cite-backlink"><a href="#cite_ref-1">↑</a></span> <span class="reference-text">Text</span>
</li>
</ol>

!! end

!! test
<references> after <gallery> (bug 6164)
!! input
<ref>one</ref>

<gallery>Image:Foobar.jpg</gallery>

<references/>
!! result
<p><sup id="cite_ref-1" class="reference"><a href="#cite_note-1">[1]</a></sup>
</p>
<ul class="gallery mw-gallery-traditional">
		<li class="gallerybox" style="width: 155px"><div style="width: 155px">
			<div class="thumb" style="width: 150px;"><div style="margin:68px auto;"><a href="/wiki/File:Foobar.jpg" class="image"><img alt="Foobar.jpg" src="http://example.com/images/thumb/3/3a/Foobar.jpg/120px-Foobar.jpg" width="120" height="14" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
</ul>
<ol class="references">
<li id="cite_note-1"><span class="mw-cite-backlink"><a href="#cite_ref-1">↑</a></span> <span class="reference-text">one</span>
</li>
</ol>

!! end

!! test
{{REVISIONID}} on page with <ref> (bug 6299)
!! input
{{REVISIONID}}<ref>elite</ref>
!! result
1337<sup id="cite_ref-1" class="reference"><a href="#cite_note-1">[1]</a></sup><ol class="references">
<li id="cite_note-1"><span class="mw-cite-backlink"><a href="#cite_ref-1">↑</a></span> <span class="reference-text">elite</span>
</li>
</ol>

!! end

!! test
{{REVISIONID}} on page without <ref> (bug 6299 sanity check)
!! input
{{REVISIONID}}
!! result
<p>1337
</p>
!! end

!! test
Blank ref followed by ref with content
!! input
<ref name="blank"/>

<ref name="blank">content</ref>

<references/>
!! result
<p><sup id="cite_ref-blank_1-0" class="reference"><a href="#cite_note-blank-1">[1]</a></sup>
</p><p><sup id="cite_ref-blank_1-1" class="reference"><a href="#cite_note-blank-1">[1]</a></sup>
</p>
<ol class="references">
<li id="cite_note-blank-1"><span class="mw-cite-backlink">↑ <sup><a href="#cite_ref-blank_1-0">1.0</a></sup> <sup><a href="#cite_ref-blank_1-1">1.1</a></sup></span> <span class="reference-text">content</span>
</li>
</ol>

!! end

!! test
Regression: non-blank ref "0" followed by ref with content
!! input
<ref name="blank">0</ref>

<ref name="blank">content</ref>

<references/>
!! result
<p><sup id="cite_ref-blank_1-0" class="reference"><a href="#cite_note-blank-1">[1]</a></sup>
</p><p><sup id="cite_ref-blank_1-1" class="reference"><a href="#cite_note-blank-1">[1]</a></sup>
</p>
<ol class="references">
<li id="cite_note-blank-1"><span class="mw-cite-backlink">↑ <sup><a href="#cite_ref-blank_1-0">1.0</a></sup> <sup><a href="#cite_ref-blank_1-1">1.1</a></sup></span> <span class="reference-text">0</span>
</li>
</ol>

!! end

!! test
Regression sanity check: non-blank ref "1" followed by ref with content
!! input
<ref name="blank">1</ref>

<ref name="blank">content</ref>

<references/>
!! result
<p><sup id="cite_ref-blank_1-0" class="reference"><a href="#cite_note-blank-1">[1]</a></sup>
</p><p><sup id="cite_ref-blank_1-1" class="reference"><a href="#cite_note-blank-1">[1]</a></sup>
</p>
<ol class="references">
<li id="cite_note-blank-1"><span class="mw-cite-backlink">↑ <sup><a href="#cite_ref-blank_1-0">1.0</a></sup> <sup><a href="#cite_ref-blank_1-1">1.1</a></sup></span> <span class="reference-text">1</span>
</li>
</ol>

!! end

!! test
Ref names containing a number
!! input
<ref name="test123test">One</ref>
<ref name="123test">Two</ref>
<ref name="test123">Three</ref>

<references />
!! result
<p><sup id="cite_ref-test123test_1-0" class="reference"><a href="#cite_note-test123test-1">[1]</a></sup>
<sup id="cite_ref-123test_2-0" class="reference"><a href="#cite_note-123test-2">[2]</a></sup>
<sup id="cite_ref-test123_3-0" class="reference"><a href="#cite_note-test123-3">[3]</a></sup>
</p>
<ol class="references">
<li id="cite_note-test123test-1"><span class="mw-cite-backlink"><a href="#cite_ref-test123test_1-0">↑</a></span> <span class="reference-text">One</span>
</li>
<li id="cite_note-123test-2"><span class="mw-cite-backlink"><a href="#cite_ref-123test_2-0">↑</a></span> <span class="reference-text">Two</span>
</li>
<li id="cite_note-test123-3"><span class="mw-cite-backlink"><a href="#cite_ref-test123_3-0">↑</a></span> <span class="reference-text">Three</span>
</li>
</ol>

!! end

!! test
Erroneous refs
!! input
<ref name="0">Zero</ref>

<ref>Also zero, but differently! (Normal ref)</ref>

<ref />

<ref name="foo" name="bar" />

<ref name="blankwithnoreference" />

<references name="quasit" />

<references />
!! result
<p><strong class="error mw-ext-cite-error">Cite error: Invalid <code>&lt;ref&gt;</code> tag;
name cannot be a simple integer. Use a descriptive title</strong>
</p><p><sup id="cite_ref-1" class="reference"><a href="#cite_note-1">[1]</a></sup>
</p><p><strong class="error mw-ext-cite-error">Cite error: Invalid <code>&lt;ref&gt;</code> tag;
refs with no content must have a name</strong>
</p><p><sup id="cite_ref-bar_2-0" class="reference"><a href="#cite_note-bar-2">[2]</a></sup>
</p><p><sup id="cite_ref-blankwithnoreference_3-0" class="reference"><a href="#cite_note-blankwithnoreference-3">[3]</a></sup>
</p><p><strong class="error mw-ext-cite-error">Cite error: Invalid <code>&lt;references&gt;</code> tag;
parameter "group" is allowed only.
Use <code>&lt;references /&gt;</code>, or <code>&lt;references group="..." /&gt;</code></strong>
</p>
<ol class="references">
<li id="cite_note-1"><span class="mw-cite-backlink"><a href="#cite_ref-1">↑</a></span> <span class="reference-text">Also zero, but differently! (Normal ref)</span>
</li>
<li id="cite_note-bar"><span class="mw-cite-backlink"><a href="#cite_ref-bar_0">↑</a></span> <strong class="error mw-ext-cite-error">Cite error: Invalid <code>&lt;ref&gt;</code> tag;
no text was provided for refs named <code>bar</code></strong></li>
<li id="cite_note-blankwithnoreference"><span class="mw-cite-backlink"><a href="#cite_ref-blankwithnoreference_0">↑</a></span> <strong class="error mw-ext-cite-error">Cite error: Invalid <code>&lt;ref&gt;</code> tag;
no text was provided for refs named <code>blankwithnoreference</code></strong></li>
</ol>

!! end


!! test
Simple <ref>, with <references/> in group
!! input
Wikipedia rocks!<ref>Proceeds of Rockology, vol. XXI</ref>
Wikipedia rocks!<ref group=note>Proceeds of Rockology, vol. XXI</ref>

<references/>
<references group=note/>
!! result
<p>Wikipedia rocks!<sup id="cite_ref-1" class="reference"><a href="#cite_note-1">[1]</a></sup>
Wikipedia rocks!<sup id="cite_ref-2" class="reference"><a href="#cite_note-2">[note 1]</a></sup>
</p>
<ol class="references">
<li id="cite_note-1"><span class="mw-cite-backlink"><a href="#cite_ref-1">↑</a></span> <span class="reference-text">Proceeds of Rockology, vol. XXI</span>
</li>
</ol>
<ol class="references">
<li id="cite_note-2"><span class="mw-cite-backlink"><a href="#cite_ref-2">↑</a></span> <span class="reference-text">Proceeds of Rockology, vol. XXI</span>
</li>
</ol>

!! end

!! test
Simple <ref>, with <references/> in group, with groupname in chinese 
!! input
AAA<ref group="参">ref a</ref>BBB<ref group="注">note b</ref>CCC<ref group="参">ref c</ref>

;refs
<references group="参" />
;notes
<references group="注" />
!! result
<p>AAA<sup id="cite_ref-1" class="reference"><a href="#cite_note-1">[参 1]</a></sup>BBB<sup id="cite_ref-2" class="reference"><a href="#cite_note-2">[注 1]</a></sup>CCC<sup id="cite_ref-3" class="reference"><a href="#cite_note-3">[参 2]</a></sup>
</p>
<dl><dt>refs</dt></dl>
<ol class="references">
<li id="cite_note-1"><span class="mw-cite-backlink"><a href="#cite_ref-1">↑</a></span> <span class="reference-text">ref a</span>
</li>
<li id="cite_note-3"><span class="mw-cite-backlink"><a href="#cite_ref-3">↑</a></span> <span class="reference-text">ref c</span>
</li>
</ol>
<dl><dt>notes</dt></dl>
<ol class="references">
<li id="cite_note-2"><span class="mw-cite-backlink"><a href="#cite_ref-2">↑</a></span> <span class="reference-text">note b</span>
</li>
</ol>

!! end

!! test
<ref> defined in <references>
!! input
<ref name="foo"/>

<references>
<ref name="foo">BAR</ref>
</references>
!! result
<p><sup id="cite_ref-foo_1-0" class="reference"><a href="#cite_note-foo-1">[1]</a></sup>
</p>
<ol class="references">
<li id="cite_note-foo-1"><span class="mw-cite-backlink"><a href="#cite_ref-foo_1-0">↑</a></span> <span class="reference-text">BAR</span>
</li>
</ol>

!! end

!! test
<ref> defined in <references> called with #tag
!! input
<ref name="foo"/>

{{#tag:references|
<ref name="foo">BAR</ref>
}}
!! result
<p><sup id="cite_ref-foo_1-0" class="reference"><a href="#cite_note-foo-1">[1]</a></sup>
</p>
<ol class="references">
<li id="cite_note-foo-1"><span class="mw-cite-backlink"><a href="#cite_ref-foo_1-0">↑</a></span> <span class="reference-text">BAR</span>
</li>
</ol>

!! end

!! test
<ref> defined in <references> error conditions
!! input
<ref name="foo" group="2"/>

<references group="2">
<ref name="foo"/>
<ref name="unused">BAR</ref>
<ref name="foo" group="1">bad group</ref>
<ref>BAR BAR</ref>
</references>
!! result
<p><sup id="cite_ref-foo_1-0" class="reference"><a href="#cite_note-foo-1">[2 1]</a></sup>
</p>
<ol class="references">
<li id="cite_note-foo"><span class="mw-cite-backlink"><a href="#cite_ref-foo_0">↑</a></span> <strong class="error mw-ext-cite-error">Cite error: Invalid <code>&lt;ref&gt;</code> tag;
no text was provided for refs named <code>foo</code></strong></li>
</ol>
<p><strong class="error mw-ext-cite-error">Cite error: <code>&lt;ref&gt;</code> tag with name "unused" defined in <code>&lt;references&gt;</code> is not used in prior text.</strong><br />
<strong class="error mw-ext-cite-error">Cite error: <code>&lt;ref&gt;</code> tag in <code>&lt;references&gt;</code> has conflicting group attribute "1".</strong><br />
<strong class="error mw-ext-cite-error">Cite error: <code>&lt;ref&gt;</code> tag defined in <code>&lt;references&gt;</code> has no name attribute.</strong>
</p>
!! end

!! article
MediaWiki:cite_link_label_group-klingon
!! text
wa' cha' wej loS vagh jav Soch chorgh Hut wa'maH
!! endarticle

!! test
<ref> with custom group link
!! input
Wikipedia rocks!<ref group="klingon">Proceeds of Rockology, vol. XXI</ref>

<references group="klingon"/>
!! result
<p>Wikipedia rocks!<sup id="cite_ref-1" class="reference"><a href="#cite_note-1">[wa']</a></sup>
</p>
<ol class="references">
<li id="cite_note-1"><span class="mw-cite-backlink"><a href="#cite_ref-1">↑</a></span> <span class="reference-text">Proceeds of Rockology, vol. XXI</span>
</li>
</ol>

!! end

!! test
Bug 31374 regression check: nested strip items
!! input
{{#tag:ref|note<ref>reference</ref>|group=Note}}
<references group=Note />
<references />
!! result
<p><sup id="cite_ref-2" class="reference"><a href="#cite_note-2">[Note 1]</a></sup>
</p>
<ol class="references">
<li id="cite_note-2"><span class="mw-cite-backlink"><a href="#cite_ref-2">↑</a></span> <span class="reference-text">note<sup id="cite_ref-1" class="reference"><a href="#cite_note-1">[1]</a></sup></span>
</li>
</ol>
<ol class="references">
<li id="cite_note-1"><span class="mw-cite-backlink"><a href="#cite_ref-1">↑</a></span> <span class="reference-text">reference</span>
</li>
</ol>

!! end

!! test
Bug 13073 regression check: wrapped <references>
!! input
<ref>
foo
</ref>
<div><references /></div>
!! result
<p><sup id="cite_ref-1" class="reference"><a href="#cite_note-1">[1]</a></sup>
</p>
<div><ol class="references">
<li id="cite_note-1"><span class="mw-cite-backlink"><a href="#cite_ref-1">↑</a></span> <span class="reference-text">
foo</span>
</li>
</ol></div>

!! end
