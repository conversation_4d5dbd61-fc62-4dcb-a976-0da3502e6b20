/*
** MediaWiki 'monobook' style sheet for CSS2-capable browsers.
** Copyright <PERSON> - http://wikidev.net/
** License: GPL (http://www.gnu.org/copyleft/gpl.html)
**
** Loosely based on http://www.positioniseverything.net/ordered-floats.html by <PERSON>
** and the Plone 2.0 styles, see http://plone.org/ (<PERSON>,<PERSON> & <PERSON>,
** <PERSON> and <PERSON><PERSON><PERSON>)
** All you guys rock :)
*/

div#column-content {
	width: 100%;
	float: right;
	margin: 0 0 .6em -12.2em;
	padding: 0;
}

.mw-body {
	margin: 2.8em 0 0 12.2em;
	padding: 1em;
	position: relative;
	z-index: 2;
	background: white;
	color: black;
	border: 1px solid #aaa;
	border-right: none;
	line-height: 1.5em;
}

div#column-one {
	padding-top: 160px;
}

/* Hide, but keep accessible for screen-readers */
#column-one h2 {
	position: absolute;
	top: -9999px;
}

/* the left column width is specified in class .portlet */

/* Font size:
** We take advantage of keyword scaling- browsers won't go below 9px
** More at http://www.w3.org/2003/07/30-font-size
** http://style.cleverchimp.com/font_size_intervals/altintervals.html
*/

body {
	font: x-small sans-serif;
	/* @embed */
	background: #f9f9f9 url(headbg.jpg) 0 0 no-repeat;
	color: black;
	margin: 0;
	padding: 0;
	direction: ltr; /* Needed for RTL flipping */
	unicode-bidi: embed;
}

/* scale back up to a sane default */
div#globalWrapper {
	font-size: 127%;
	width: 100%;
	margin: 0;
	padding: 0;
}

/* general styles */
a {
	color: #002bb8;
}

a:visited {
	color: #5a3696;
}

a.new,
#p-personal a.new {
	color: #cc2200;
}

ul {
	list-style-type: square;
	/* @embed */
	list-style-image: url(bullet.gif);
}

pre, .mw-code {
	line-height: 1.1em;
}

#firstHeading {
	padding-top: 0;
}

/*
** the main content area
*/

#siteNotice {
	font-size: 95%;
	padding: 0 0.9em;
}

#localNotice {
	margin: 0;
}

#siteNotice p {
	margin: 0;
	padding: 0;
}

/*
** classes for special content elements like town boxes
** intended to be referenced directly from the wiki src
*/

/*
** User styles
*/
/* table standards */
table.rimage {
	float: right;
	position: relative;
	margin-left: 1em;
	margin-bottom: 1em;
	text-align: center;
}

/*
** edit views etc
*/
.special li {
	line-height: 1.4em;
	margin: 0;
	padding: 0;
}

/*
** Structural Elements
*/

/*
** general portlet styles (elements in the quickbar)
*/
.portlet {
	border: none;
	margin: 0 0 .5em;
	padding: 0;
	float: none;
	width: 11.6em;
	overflow: hidden;
}

.portlet h3 {
	background: transparent;
	padding: 0 1em 0 .5em;
	display: inline;
	height: 1em;
	text-transform: lowercase;
	font-size: 91%;
	font-weight: normal;
	white-space: nowrap;
}

.pBody {
	font-size: 95%;
	background-color: white;
	color: black;
	border-collapse: collapse;
	border: 1px solid #aaa;
	padding: 0 .8em .3em .5em;
}

/* allows .pBody styles to wrap around content added via BaseTemplateAfterPortlet hook */
.pBody:after {
	content: '';
	clear: both;
	display: block;
}

.portlet ul {
	line-height: 1.5em;
	font-size: 95%;
}

.portlet li {
	padding: 0;
	margin: 0;
}

/*
** Logo properties
*/

#p-logo {
	top: 0;
	left: 0;
	position: absolute; /*needed to use z-index */
	z-index: 3;
	height: 155px;
	width: 12em;
	overflow: visible;
}

#p-logo h3 {
	display: none;
}

#p-logo a,
#p-logo a:hover {
	display: block;
	height: 155px;
	width: 12.2em;
	background-repeat: no-repeat;
	background-position: 35% 50% !important;
	text-decoration: none;
}

/*
** Search portlet
*/
#p-search {
	position: relative;
	z-index: 3;
}

input.searchButton {
	margin-top: 1px;
	font-size: 95%;
}

#searchGoButton {
	padding-left: .5em;
	padding-right: .5em;
	font-weight: bold;
}

#searchInput {
	width: 10.9em;
	margin: 0;
	font-size: 95%;
}

#p-search .pBody {
	padding: .5em .4em .4em .4em;
	text-align: center;
}

#p-search #searchform div div {
	margin-top: .4em;
	font-size: 95%;
}

/*
** the personal toolbar
*/
#p-personal {
	position: absolute;
	left: 0;
	top: 0;
	z-index: 3;
}

#p-personal {
	width: 100%;
	white-space: nowrap;
	padding: 0;
	margin: 0;
	border: none;
	background: none;
	overflow: visible;
	line-height: 1.2em;
}

#p-personal h3 {
	display: none;
}

#p-personal .portlet,
#p-personal .pBody {
	z-index: 0;
	padding: 0;
	margin: 0;
	border: none;
	overflow: visible;
	background: none;
}

/* this is the ul contained in the portlet */
#p-personal ul {
	border: none;
	line-height: 1.4em;
	color: #2f6fab;
	padding: 0 2em 0 3em;
	margin: 0;
	text-align: right;
	list-style-type: none;
	list-style-image: none;
	z-index: 0;
	background: none;
	cursor: default;
}

#p-personal li {
	z-index: 0;
	border: none;
	padding: 0;
	display: inline;
	color: #2f6fab;
	margin-left: 1em;
	line-height: 1.2em;
	background: none;
}

#p-personal li a {
	text-decoration: none;
	color: #005896;
	padding-bottom: .2em;
	background: none;
}

#p-personal li a:hover {
	background-color: white;
	padding-bottom: .2em;
	text-decoration: none;
}

#p-personal li.active a:hover {
	background-color: transparent;
}

/* The icon in front of the username / login link */
li#pt-userpage,
li#pt-anonuserpage,
li#pt-login {
	/* @embed */
	background: url(user.gif) top left no-repeat;
	padding-left: 20px;
}

#p-personal ul {
	text-transform: lowercase;
}

/* Don't lowercase username or IP addresses (IPv6) */
li#pt-userpage,
li#pt-anonuserpage {
	text-transform: none;
}

#p-personal li.active {
	font-weight: bold;
}

/*
** the page-related actions- page/talk, edit etc
*/
#p-cactions {
	position: absolute;
	top: 1.3em;
	left: 11.5em;
	margin: 0;
	white-space: nowrap;
	width: 76%;
	line-height: 1.1em;
	overflow: visible;
	background: none;
	border-collapse: collapse;
	padding-left: 1em;
	font-size: 95%;
}

#p-cactions ul {
	list-style-type: none;
	list-style-image: none;
}

#p-cactions li {
	display: inline;
	border: 1px solid #aaa;
	border-bottom: none;
	padding: 0 0 1em 0;
	margin: 0 .3em 0 0;
	overflow: visible;
	background: white;
}

#p-cactions li.selected {
	border-color: #fabd23;
	font-weight: bold;
}

#p-cactions li a {
	background-color: #fbfbfb;
	color: #002bb8;
	border: none;
	padding: 0 .8em .3em;
	position: relative;
	z-index: 0;
	margin: 0;
	text-decoration: none;
}

#p-cactions li.selected a {
	z-index: 3;
	background-color: white;
}

#p-cactions .new a {
	color: #ba0000;
}

#p-cactions li a:hover {
	z-index: 3;
	text-decoration: none;
	background-color: white;
}

#p-cactions h3 {
	display: none;
}

#p-cactions li.istalk {
	margin-right: 0;
}

#p-cactions li.istalk a {
	padding-right: .5em;
}

#p-cactions #ca-addsection a {
	padding-left: .4em;
	padding-right: .4em;
}

/* offsets to distinguish the tab groups */
li#ca-talk {
	margin-right: 1.6em;
}

li#ca-watch,
li#ca-unwatch,
li#ca-varlang-0,
li#ca-print {
	margin-left: 1.6em;
}

#p-cactions .pBody {
	font-size: 1em;
	background-color: transparent;
	color: inherit;
	border-collapse: inherit;
	border: 0;
	padding: 0;
}

#p-cactions li a {
	text-transform: lowercase;
}

#p-lang {
	position: relative;
	z-index: 3;
}

/* Override text-transform on languages where capitalization is significant */
.capitalize-all-nouns .portlet h3,
.capitalize-all-nouns #p-personal ul,
.capitalize-all-nouns #p-cactions ul li a {
	text-transform: none;
}

/* TODO: #t-iscite is only used by the Cite extension, come up with some
 * system which allows extensions to add to this file on the fly
 */
#t-ispermalink, #t-iscite {
	color: #999;
}

/*
** footer
*/
div#footer {
	background-color: white;
	border-top: 1px solid #fabd23;
	border-bottom: 1px solid #fabd23;
	margin: .6em 0 1em 0;
	overflow: hidden;
	padding: .4em 0 .3em 0;
	text-align: center;
	font-size: 90%;
}

div#footer li {
	display: inline;
	margin: 0 1.3em;
}

#f-poweredbyico, #f-copyrightico {
	margin: 0 8px;
	position: relative;
	top: -2px; /* Bump it up just a tad */
}

#f-poweredbyico {
	float: right;
	height: 1%;
}

#f-copyrightico {
	float: left;
	height: 1%;
}

.mw-htmlform-submit {
	font-weight: bold;
	padding-left: .3em;
	padding-right: .3em;
	margin-right: 2em;
}

/* js pref toc */
#preftoc {
	margin: 0;
	padding: 0;
	width: 100%;
	clear: both;
}

#preftoc li {
	background-color: #f0f0f0;
	color: #000;
}

#preftoc li {
	margin: 1px -2px 1px 2px;
	float: left;
	padding: 2px 0 3px 0;
	border: 1px solid #fff;
	border-right-color: #716f64;
	border-bottom: 0;
	position: relative;
	white-space: nowrap;
	list-style-type: none;
	list-style-image: none;
	z-index: 3;
}

#preftoc li.selected {
	font-weight: bold;
	background-color: #f9f9f9;
	border: 1px solid #aaa;
	border-bottom: none;
	cursor: default;
	top: 1px;
	padding-top: 2px;
	margin-right: -3px;
}

#preftoc > li.selected {
	top: 2px;
}

#preftoc a,
#preftoc a:active {
	display: block;
	color: #000;
	padding: 0 .7em;
	position: relative;
	text-decoration: none;
}

#preftoc li.selected a {
	cursor: default;
	text-decoration: none;
}

#preferences {
	margin: 0;
	border: 1px solid #aaa;
	clear: both;
	padding: 1.5em;
	background-color: #F9F9F9;
}

.prefsection {
	border: none;
	padding: 0;
	margin: 0;
}

.prefsection legend {
	font-weight: bold;
}

.prefsection table, .prefsection legend {
	background-color: #F9F9F9;
}

.mainLegend {
	display: none;
}

td.htmlform-tip {
	font-size: x-small;
	padding: .2em 2em;
	color: #666;
}

.preferences-login {
	clear: both;
	margin-bottom: 1.5em;
}

.prefcache {
	font-size: 90%;
	margin-top: 2em;
}

#userloginprompt, #languagelinks {
	font-size: 85%;
}

#login-sectiontip {
	font-size: 85%;
	line-height: 1.2;
	padding-top: 2em;
}

#userloginlink a, #wpLoginattempt, #wpCreateaccount {
	font-weight: bold;
}

/**
 * This was originally added by Gabriel Wicke in r3681 (committed on 25 May 2004)
 * with the commit message "tweaks to page history".
 * Unlike the other IE/Mac fixes that used to be present here, this seems to get
 * applied on more modern browsers, so let's keep it here until someone has the
 * time to properly test it out.
 */
#pagehistory li.selected {
	position: relative;
}

.redirectText {
	font-size: 150%;
	margin: 5px;
}

div.patrollink {
	clear: both;
}

.sharedUploadNotice {
	font-style: italic;
}

span.updatedmarker {
	color: black;
	background-color: #0f0;
}

.editExternally {
	border: 1px solid gray;
	background-color: #ffffff;
	padding: 3px;
	margin-top: 0.5em;
	float: left;
	font-size: small;
	text-align: center;
}

.editExternallyHelp {
	font-style: italic;
	color: gray;
}

.toggle {
	margin-left: 2em;
	text-indent: -2em;
}

/* @bug 1714 */
input#wpSave,
input#wpDiff {
	margin-right: 0.33em;
}

#wpSave {
	font-weight: bold;
}

/* noarticletext */
div.noarticletext {
	border: 1px solid #ccc;
	background: #fff;
	padding: .2em 1em;
	color: #000;
}

div#searchTargetContainer {
	left: 10px;
	top: 10px;
	width: 90%;
	background: white;
}

div#searchTarget {
	padding: 3px;
	margin: 5px;
	background: #F0F0F0;
	border: solid 1px blue;
}

div#searchTarget ul li {
	list-style-type: none;
	list-style-image: none;
}

div#searchTarget ul li:before {
	color: orange;
	content: "\00BB \0020";
}

div#searchTargetHide {
	float: right;
	border: solid 1px black;
	background: #DCDCDC;
	padding: 2px;
}

#powersearch p {
	margin-top: 0;
}

div.multipageimagenavbox {
	border: solid 1px silver;
	padding: 4px;
	margin: 1em;
	background: #f0f0f0;
}

div.multipageimagenavbox div.thumb {
	border: none;
	margin-left: 2em;
	margin-right: 2em;
}

div.multipageimagenavbox hr {
	margin: 6px;
}

table.multipageimage td {
	text-align: center;
}

.templatesUsed {
	margin-top: 1.5em;
}

.mw-summary-preview {
	margin: 0.1em 0;
}

/* Friendlier slave lag warnings */
div.mw-lag-warn-normal,
div.mw-lag-warn-high {
	padding: 3px;
	text-align: center;
	margin: 3px auto;
}

div.mw-lag-warn-normal {
	border: 1px solid #FFCC66;
	background-color: #FFFFCC;
}

div.mw-lag-warn-high {
	font-weight: bold;
	border: 2px solid #FF0033;
	background-color: #FFCCCC;
}

.MediaTransformError {
	background-color: #ccc;
	padding: 0.1em;
}

.MediaTransformError td {
	text-align: center;
	vertical-align: middle;
	font-size: 90%;
}

/* Sometimes people don't want personal tools to be lowercase! */
.no-text-transform {
	text-transform: none;
}

/* Tooltips are outside of the normal body code, so this helps make the size of the text sensible */
.tipsy {
	font-size: 127%;
}

/* mediawiki.notification */
.skin-monobook .mw-notification {
	box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.125);
}
