{"@metadata": {"authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Lloffiwr", "<PERSON><PERSON><PERSON>", "Umher<PERSON>render", "<PERSON><PERSON>"]}, "skinname-tieba": "{{optional}}", "tieba-skin-desc": "{{desc|what=skin|name=<PERSON><PERSON><PERSON>|url=https://www.mediawiki.org/wiki/Skin:Tie<PERSON>}}", "tieba.css": "{{optional}}", "tieba.js": "{{optional}}", "tieba-action-addsection": "Used in the Tieba skin. See for example {{canonicalurl:Talk:Main_Page|useskin=tieba}}", "tieba-action-delete": "Used in the Tieba skin, as the name of a tab at the top of the page. See for example {{canonicalurl:Main_Page|useskin=tieba}}\n\n{{Identical|Delete}}", "tieba-action-move": "Used in the Tieba skin, on the tabs at the top of the page. See for example {{canonicalurl:Talk:Main_Page|useskin=tieba}}\n\n{{Identical|Move}}", "tieba-action-protect": "Tab at top of page, in tieba skin\n\n{{Identical|Protect}}", "tieba-action-undelete": "Tab at top of page, in tieba skin.\n{{Identical|Undelete}}", "tieba-action-unprotect": "Tab at top of page, in tieba skin.\n{{Identical|Change protection}}", "tieba-view-create": "Tab label in the Tieba skin. See for example {{canonicalurl:Foo|useskin=tieba}}\n{{Identical|Create}}", "tieba-view-edit": "Tab label in the Tieba skin. See for example {{canonicalurl:Main_Page|useskin=tieba}}\n{{Identical|Edit}}", "tieba-view-history": "Tab label in the Tieba skin. See for example {{canonicalurl:Main_Page|useskin=tieba}}\n{{Identical|View history}}", "tieba-view-view": "Tab label in the Tieba skin (verb). See for example {{canonicalurl:Main_Page|useskin=tieba}}.\n{{Identical|Read}}", "tieba-view-viewsource": "Tab label in the Tieba skin.\n{{Identical|View source}}", "tieba-more-actions": "Label in the Tieba skin's menu for the less-important or rarer actions which are not shown as tabs (like moving the page, or for sysops deleting or protecting the page), as well as (for users with a narrow viewing window in their browser) the less-important tab actions which the user's browser is unable to fit in. {{Identical|More}}"}