@import "../variables";

/* mediawiki.notification */

// This wrapper class is needed to ensure these rules have larger CSS
// selector specificity than default styles
.mediawiki {
	.mw-notification-area {
		font-size: 0.8em;
	}

	.mw-notification-area-layout {
		top: 7em;
	}

	.mw-notification {
		background-color: #fff;
		background-color: rgba(255, 255, 255, 0.93);
		padding: 0.75em 1.5em;
		border: solid 1px @content-border-color;
		border-radius: 0.75em;
		box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.125);
	}
}
