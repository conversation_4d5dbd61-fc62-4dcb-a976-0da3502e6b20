<?php
/**
 * Author: tianwen
 * Date: 2013-09-26
 * Time: 下午17:13
 * Description: ueg帐号提交
 */

class prisonreasonAction extends Mo_Core_Action{
    protected $strTplName = 'prison_reason.php';
    protected $strTplPath = 'spb';

    private $forumId = 0;

    private $ntn = 0;
    private $day = 0;
    private $un = 0;
    private $tbs = 0;
    private $word = "";
    private $fid = 0;
    private $z = 0;
    private $reason = "";
    private $reasonListArray = array();

    private function _input() {
        $this->forumId = Mo_Request::get('forum_id',0);
        $this->ntn = Mo_Request::get('ntn',0);
        $this->day = Mo_Request::get('day',0);
        $this->un = Mo_Request::get('un',0);
        $this->tbs = Mo_Request::get('tbs',0);
        $this->word = Mo_Request::get('word',"");
        $this->fid = Mo_Request::get('fid',0);
        $this->z = Mo_Request::get('z',0);
        return true;
    }
    
    protected  function _execute(){
        $this->_input();
        $this->_process();
        $this->_build();
    }

    private function _process(){
        $arrOut = array();
        $arrInput = array(
            'forum_id' => $this->forumId
        );
        $arrOut =  Mo_Data::get('antispam','listreason',$arrInput);
        if(!empty($arrOut)) {
          $this->reasonListArray = $arrOut['list_reason'];
        } 
    }
    
    protected  function _build(){
        Mo_Response::addViewData('wreq',Mo_Service_Wap::getTplWreq());
        Mo_Response::addViewData('base',Mo_Service_Wap::getTplBase());
        Mo_Response::addViewData('user',Mo_Service_Wap::getTplUser());
        Mo_Response::addViewData('reason_list',$this->reasonListArray);
        Mo_Response::addViewData('ntn',$this->ntn);
        Mo_Response::addViewData('day',$this->day);
        Mo_Response::addViewData('un',$this->un);
        Mo_Response::addViewData('tbs',$this->tbs);
        Mo_Response::addViewData('word',$this->word);
        Mo_Response::addViewData('fid',$this->fid);
        Mo_Response::addViewData('z',$this->z);
    }

    public function _log(){

    }
}