<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>  <EMAIL>
 * @date 2011-3-25
 * @version 
 */
require_once MOBILE_UI_PATH.'/../mowcommon/libs/Mowcommon/Service/Commit.php';
class committopAction extends Mo_Core_Action{
    private $_strNtn = '';
    private $_strFname = '';
    private $_intFid = 0;
    private $_intTid = 0;
    private $_strTbs = '';
    
    protected function _execute() {
        $ret = $this->_input();
        if ($ret){
            $ret = $this->_process();
        }
		//错误号中既有Errno又有OpErrno，后续需要和模板一起排查，统一成一种
        $this->_build($ret);
        
        return true;       
    }
    
    private function _input(){
        $this->_strNtn = Mo_Request::get('ntn', '');
        $this->_strFname = Mo_Request::get('word', '');
        $this->_intFid = Mo_Request::get('fid', 0);
        $this->_intTid = Mo_Request::get('z', 0);
        $this->_strTbs = Mo_Request::get('tbs', '');
        
        if(false === Mo_Request::$bolLogin|| true === Mo_Request::$bolLoginBySsid){
            Bingo_Log::warning('user not login');
            Mo_Response::$intOpErrno = Mo_Errno::USER_NOT_LOGIN;
            Mo_Response::$strOpError = Mo_Error::USER_OTHER_PRODUCT_LOGIN;     
            return false;
        }

        //实名认证开始
        if($this->_checkRealName() == false){
          Bingo_Log::warning('check real name fail');
          Mo_Response::$intOpErrno = Mo_Errno::ERR_CLIENT_USER_HAS_NO_REALNAME;
          Mo_Response::$strOpError = Mo_Error::ERR_CLIENT_USER_HAS_NO_REALNAME;
          return false;
        }
        //实名认证结束
        
        if(empty($this->_strNtn)){
            Bingo_Log::warning('ntn not found');
            Mo_Response::$intOpErrno = Mo_Errno::ERR_COMMIT_TOP;
            Mo_Response::$strOpError = Mo_Error::ERR_COMMIT_TOP;     
            return false;
        }
        return true;
    }
    
    private function _process(){
       $arrOut = array();
       $arrInput = array(
       	   'forum_name'	=> $this->_strFname,
           'forum_id'	=> $this->_intFid,
           'thread_id'	=> $this->_intTid,
           'tbs' 		=> $this->_strTbs,
       );
      
       $arrInput = array_merge($arrInput, Mowcommon_Service_Commit::getCommonCommitInfo());
       if ($this->_strNtn == 'set'){
           $arrOut = Tbapi_Core_Server::mocall('post', 'add_top', $arrInput);
       }
       else{
           $arrOut = Tbapi_Core_Server::mocall('post', 'del_top', $arrInput);
       }     
		//失败设置不同错误号
       if (false === $arrOut || $arrOut['error']['errno'] != Tieba_Errcode::ERR_SUCCESS){
           Bingo_Log::warning("commit error,cmd[{$this->_strNtn}] input[".serialize($arrInput)."] output[".serialize($arrOut)."]");
           Mo_Response::$intOpErrno = $arrOut['error']['errno'];
           Mo_Response::$strOpError = $arrOut['error']['usermsg'];
           //针对错误号进行一些特殊处理
           if (Mo_Response::$intOpErrno == 2270021) {
            Mo_Response::$strOpError = '该贴为会员专属置顶贴，请在电脑上进行操作';
           }
           if($arrOut['error']['errno'] != Tieba_Errcode::ERR_MO_PARAM_INVALID){
           		Mo_Response::error(Mo_Errno::INTERNAL_ERROR, Mo_Error::INTERNAL_ERROR);
           }
           return false;
       }
       //成功设置不同错误号
       if ($this->_strNtn == 'set') {
           Mo_Response::$intOpErrno = Mo_Errno::MSG_COMMIT_SET_TOP_SUCCESS;
           Mo_Response::$strOpError = Mo_Error::MSG_COMMIT_SET_TOP_SUCCESS;
       }
       else {
           Mo_Response::$intOpErrno = Mo_Errno::MSG_COMMIT_UNSET_TOP_SUCCESS;
           Mo_Response::$strOpError = Mo_Error::MSG_COMMIT_UNSET_TOP_SUCCESS;
       }
       return true;
    }

    private function _build($result){
        //根据不同错误号做不同版本处理
        if (Mo_Request::$intBdPageType == Mo_Define::BD_PAGE_TYPE_WEBAPP){
            Mo_Response::$bolAjaxCommit = true;
            Mo_Response::$intErrno = Mo_Response::$intOpErrno;
            if (Mo_Response::$intErrno == Mo_Errno::MSG_COMMIT_SET_TOP_SUCCESS
                || Mo_Response::$intErrno == Mo_Errno::MSG_COMMIT_UNSET_TOP_SUCCESS){
                Mo_Response::$intErrno = 0;    
            }
            Mo_Response::$strError = Mo_Response::$strOpError;
            return true;
        }
        else{
            Mo_Core_Controller::forward(Mo_Core_Url::ROUTER_PB); 
        }
        
        return true;
    }
    
    protected function _log(){
        Mo_Log::addNode('req_commitTop', 'tn', $this->_strNtn);
        Mo_Log::addNode('req_commitTop', 'fn', $this->_strFname);
        Mo_Log::addNode('req_commitTop', 'tid', $this->_intTid);
        Mo_Log::addNode('res_commitTop', 'err', Mo_Response::$intOpErrno);
        
		    Mo_Log::addNode('req_commitTop_tn', $this->_strNtn);
		    Mo_Log::addNode('res_commitTop_err', Mo_Response::$intOpErrno);
		
        Tieba_Stlog::addNode('ispv',0);	
        Tieba_Stlog::addNode('fname', $this->_strFname);
       	Tieba_Stlog::addNode('fid', $this->_intFid);
    	  Tieba_Stlog::addNode('tid', $this->_intTid);
    }
}