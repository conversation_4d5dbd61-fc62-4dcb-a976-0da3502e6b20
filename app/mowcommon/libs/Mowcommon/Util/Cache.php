<?php

/**
 * Created by PhpStorm.
 * User: yang<PERSON><PERSON><PERSON>
 * Date: 2015/1/13
 * Time: 20:45
 */

/**
 * nginx层的cache
 */
class Mowcommon_Util_Cache
{
    const CACHE_OK = 0;
    const CACHE_PID = 'forum_nginx';
    const EXPIRE_TIME = 10;
    const SWITCH_OF_CACHE = true;
    private static $_cache;
    //初始化cache
    public static function initCache() {
        if (false === self::SWITCH_OF_CACHE) {
            return null;
        }
        if (self::$_cache) {
            return self::$_cache;
        }
        Bingo_Timer::start('memcached_init');
        self::$_cache = new Bingo_Cache_Memcached(self::CACHE_PID);
        Bingo_Timer::end('memcached_init');
        if (!self::$_cache || !self::$_cache->isEnable()) {
            Bingo_Log::warning("init cache fail.");
            self::$_cache = null;
            return null;
        }
        return self::$_cache;
    }

    //获取单个cache
    public static function getCache($strKey) {
        if (false === self::SWITCH_OF_CACHE) {
            return null;
        }
        if (!self::$_cache) {
            self::initCache();
            if (!self::$_cache->isEnable()) {
                return null;
            }
        }
        Bingo_Timer::start('memcached_get');
        $mixRes = self::$_cache->get($strKey);
        Bingo_Timer::end('memcached_get');
        return $mixRes;
    }

    //删除cache
    public static function removeCache($strKey) {
        if (false === self::SWITCH_OF_CACHE) {
            return true;
        }
        if (!self::$_cache) {
            self::initCache();
            if (!self::$_cache->isEnable()) {
                return false;
            }
        }
        Bingo_Timer::start('memcached_del');
        $mixRes = self::$_cache->remove(strval($strKey));
        Bingo_Timer::end('memcached_del');
        if ($mixRes == self::CACHE_OK) {
            return true;
        } else {
            Bingo_Log::warning('remove cache err no : ' . $mixRes . " key[$strKey]");
            return false;
        }
    }

    //设置cache
    public static function addCache($strKey, $mixValue, $intLifeTime = self::EXPIRE_TIME) {
        if (false === self::SWITCH_OF_CACHE) {
            return true;
        }
        if (!self::$_cache) {
            self::initCache();
            if (!self::$_cache->isEnable()) {
                return false;
            }
        }
        $intLifeTime = intval($intLifeTime);
        Bingo_Timer::start('memcached_add');
        $mixRes = self::$_cache->add(strval($strKey), $mixValue, $intLifeTime);
        Bingo_Timer::end('memcached_add');
        if ($mixRes == self::CACHE_OK) {
            return true;
        } else {
            Bingo_Log::warning('add cache err no : ' . $mixRes . " key[$strKey]  time[$intLifeTime]");
            return false;
        }
    }
}

