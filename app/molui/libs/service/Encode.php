<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>  <EMAIL>
 * @date 2012-3-24
 * @version 
 */
class Mo_Service_Encode {
    public static function convertGBKToUTF8($str)
    {
    	$ret = $str;
    	if ($ret == null)
    	   return $ret;
    	   
    	$objEncode = new Bingo_Encode_Uconv();
        if (!$objEncode->isUtf8($str))
        {
            $ret = mb_convert_encoding($str, 'UTF-8', 'GBK');
        }
        return $ret;
    }
    
    public static function convertUTF8ToGBK($str)
    {
    	$ret = $str;
    	if ($ret == null)
    	   return $ret;
    
    	$objEncode = new Bingo_Encode_Uconv();
        if ($objEncode->isUtf8($str))
        {
    		$ret = mb_convert_encoding($str, 'GBK','UTF-8');
        }
        
        return $ret;
    }
    
    private static $arrChr = array();
    
    public static function replaceControlChars($str)
    {    	
    	if (empty(self::$arrChr)){
        	for ($i = 0; $i < 32; $i++)
    		{
    			$c = chr($i);
    			if ($c == "\t" || $c == "\r" || $c == "\n")
    				continue;
    			self::$arrChr[] = $c;
    		}    	    
    	}
		
		return str_replace(self::$arrChr,"",$str);
	}    
}