  <?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/
 
 
 
/**
 * @file Router.php
 * <AUTHOR>
 * @date 2013/03/04 10:17:46
 * @version $Revision$ 
 * @brief 
 *  
 **/

class Core_Router extends Bingo_Http_Router_Abstract {
	const DEFAULT_ROUTE = 'agent';//默认路由
	private static $arrArgs = array();
	private $_strAppName = 'mowpad'; //框架所调度的应用名称，默认为mowpad

	/**
	 * @param $strAppName 应用名称
	 */
	public function __construct($strAppName = null){
		if(is_string($strAppName)){
			$this->_strAppName = $strAppName;
		}
	}

	public function getHttpRouter() {
		$strHttpRouter = self::getRouter();
		$arrHttpRouter = split('/', $strHttpRouter);
		Bingo_Http_Request::setHttpRouter($strHttpRouter, $arrHttpRouter);
		return $strHttpRouter;	                        
    }

	/**
	 * 路由处理
	 * 在PAD版控制范围内的路由时，返回 真实路由，否则执行pad版的agent路由，
	 * @return string
	 */
	public static function getRouter() {
		$isMatchPad = true; //是否命中PAD贴吧的路由
		$strTargetRoute = self::DEFAULT_ROUTE; //目标路由，默认为default

		//加载应用定义的路由规则，包含两个变量 $arrArgsRouter
		$strRoutesConfFile = MOLUI_APP_ROOT_PATH . '/conf/routes.php';
		if(is_file($strRoutesConfFile)){
			$rules = include($strRoutesConfFile);
			$arrUrlRouter = $rules['path_based_rules'];
			$arrArgsRouter = $rules['param_based_rules'];
		}


		$strUrl = $_SERVER['PATH_INFO'];
		$strUrl = strip_tags($strUrl);
		$arrUrl = explode('/', $strUrl);
		$arrDevice = array ('pad');
		if (in_array($arrUrl[1], $arrDevice)){ //以 /pad/前缀开头的路由
			unset($arrUrl[1]);
			$isMatchPad = true;
			$strTargetRoute = implode('/', $arrUrl);
			return $strTargetRoute;
		}

		//route by args
		self::$arrArgs = array_merge($_GET, $_POST);
		if(is_array($arrArgsRouter)){
			foreach($arrArgsRouter as $value){
				if(self::matchArgs(array ($value[0] => $value[1]))){
					//命中参数路由规则，返回路由
					$strUrl = $value[2];
					$isMatchPad = true;
					$strTargetRoute = $strUrl;
					return $strTargetRoute;
				}
			}
		}

		//route by url
		if(is_array($arrUrlRouter)) {
			foreach(array_keys($arrUrlRouter) as $key){
				$key = trim($key, "^$");
				$pattern = '/^' . $key . '$/';
				if(preg_match($pattern, $strUrl)){
					//命中url路由规则
					$strUrl = $arrUrlRouter[$key];
					$isMatchPad = true;
					$strTargetRoute = $strUrl;
					return $strTargetRoute;
				}
			}
		}

		return $strTargetRoute;
	}

    public static function matchArgs($arrMatch = array()){
        $arrArgs = self::$arrArgs;
        $ret = true;
        foreach ($arrMatch as $strKey => $strValue){
    		// Ö»Òªº¬ÓÐ×Ö¶Î¾Í¿ÉÒÔÁË 
    		if ($strValue === false) {
    			if (isset($arrArgs[$strKey])) {
                    continue;
    			}else{
    			    $ret = false;
    			    break;
    			}
    		}elseif ($strValue === true){ //º¬ÓÐ×Ö¶Î²¢ÇÒÖµ²»ÄÜÎª??    			
    			if (isset($arrArgs[$strKey]) && strval($arrArgs[$strKey]) !== '') {
                    continue;
    			}else{
    			    $ret = false;
    			    break;
    			}			    
    		} else {		
    		// º¬ÓÐ×Ö¶Î, ÇÒÖµ²»Çø·Ö´óÐ¡Ð´Ïà??
    			if (isset($arrArgs[$strKey]) && strcasecmp($arrArgs[$strKey], $strValue) == 0) {
                    continue;
    			}else{
    			    $ret = false;
    			    break;
    			}
    		}            
        }
        return $ret;
    }       
}




/* vim: set ts=4 sw=4 sts=4 tw=100 */
?>
