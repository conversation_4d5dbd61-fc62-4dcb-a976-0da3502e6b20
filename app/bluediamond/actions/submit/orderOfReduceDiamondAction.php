<?php


/**
 * 订单：减少蓝钻
 * Created by PhpStorm.
 * User: kangqinmou
 */
class orderOfReduceDiamondAction extends Util_Base{


    /**
     * 执行函数
     * @param null
     * @return null
     */
    public function execute(){
        // 输入参数
        $intOpenId		= Bingo_Http_Request::get('open_id', 0);			// open id
        $intSceneId		= Bingo_Http_Request::get('scene_id', 0);			// 场景id
        $strTbSign		= Bingo_Http_Request::get('tb_sign', 0);			// 签名
        $strOrderId		= Bingo_Http_Request::get('order_id', 0);			// 第3方的订单id
        $intAmount		= Bingo_Http_Request::get('amount', 0);				// 蓝钻数量
        $strTbTimestamp	= Bingo_Http_Request::get('tb_timestamp', 0);		// 有效时间戳
        $strMsg			= Bingo_Http_Request::get('msg', '');				// msg
        $userName		= Bingo_Http_Request::get('user_name', '');			// 用户名
        $strOrderDesc	= Bingo_Http_Request::get('order_desc', '');		// 订单描述

        // 转码
        $strMsg 	  = Bingo_Encode::convert($strMsg, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
        $userName 	  = Bingo_Encode::convert($userName, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
        $strOrderDesc = Bingo_Encode::convert($strOrderDesc, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);

        // 通过用户名或open_id得到user_id
        if($userName !== ''){
            $arrUserId = $this->getUserIdByName($userName);
            if(false === $arrUserId || Tieba_Errcode::ERR_SUCCESS != $arrUserId['errno'] || empty($arrUserId['data'])){
                Bingo_Log::warning(__CLASS__.'::'.__FUNCTION__.' call self::getUserIdByName failed. $userName: ['.$userName.']; output: ['.serialize($arrUserId).']');
                $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, '通过用户名获取用户id失败');
                return;
            }
            $intUserId = $arrUserId['data'];
            $arrParam = array(
                'user_name'	   => $userName,
                'scene_id'	   => $intSceneId,
                'tb_sign'	   => $strTbSign,
                'order_id'	   => $strOrderId,
                'amount'	   => $intAmount,
                'order_desc'   => $strOrderDesc,
                'msg'		   => $strMsg,
                'tb_timestamp' => $strTbTimestamp,
            );
        }else{
            $intUserId = Tieba_Openapi::api_decode_uid($intOpenId);
            if(!$intUserId){
                Bingo_Log::warning(__CLASS__.'::'.__FUNCTION__.' call Tieba_Openapi::api_decode_uid failed. $intOpenId: ['.$intOpenId.']; output: ['.serialize($intUserId).']');
                $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, '通过open_id获取用户id失败');
                return;
            }
            $arrParam = array(
                'open_id'	   => $intOpenId,
                'scene_id'	   => $intSceneId,
                'tb_sign'	   => $strTbSign,
                'order_id'	   => $strOrderId,
                'amount'	   => $intAmount,
                'order_desc'   => $strOrderDesc,
                'msg'	       => $strMsg,
                'tb_timestamp' => $strTbTimestamp,
            );
        }

        // 校验签名
        if(false == self::checkApiSign($arrParam)){
            Bingo_Log::warning('check api sign failed. input: ['.serialize($arrParam).']');
            $this->_jsonRet(Tieba_Errcode::ERR_BUSI_INVALID_SIGN, '校验签名失败');
            return;
        }

        // 调用bluediamond orderOfReduceDiamond service，生成订单
        $arrInput = array(
            'user_id'	   => $intUserId,
            'scene_id'	   => $intSceneId,
            'tb_sign'	   => $strTbSign,
            'order_id'	   => $strOrderId,
            'amount'	   => $intAmount,
            'order_desc'   => $strOrderDesc,
            'msg'		   => $strMsg,
            'tb_timestamp' => $strTbTimestamp,
            'double_write' => true,

            // 用于service中的签名校验
            'user_name'    => $userName,
            'open_id'      => $intOpenId,
        );
        $arrInput['check_sign']	= 1;
        $arrOutput = Tieba_Service::call('bluediamond', 'orderOfReduceDiamond', $arrInput, null, null, 'post', 'php', 'utf-8');

        // 调用service的结果
        if(false == $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']){
            Bingo_Log::warning(__CLASS__.'::'.__FUNCTION__.' call bluediamond orderOfReduceDiamond service failed. input: ['.serialize($arrInput)."]; output:[".serialize($arrOutput).']');
            $this->_jsonRet($arrOutput['errno'], $arrOutput['errmsg'], $arrOutput['data']);
            return;
        }
        $intOpenId = Tieba_Openapi::api_encode_uid($intUserId);
        $arrOutput['data']['open_id'] = $intOpenId;
        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, 'success', $arrOutput['data']);
    }

}