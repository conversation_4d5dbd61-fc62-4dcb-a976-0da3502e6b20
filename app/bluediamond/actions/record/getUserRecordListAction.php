<?php
/**
 * Author: <EMAIL>
 * Date: 2016-04-22
 * Time: 18:33
 * Desc: get the user order List
 */

class getUserRecordListAction extends Util_Base {

    /**
     * 1 blue diamond flow in transaction type
     * 2 blue diamond flow out transaction type
     * 3 blue diamond withdraw transaction
     */

    const FLOW_IN_TRANSACTION  = 'income';
    const FLOW_OUT_TRANSACTION = 'consume';
    const WITHDRAW_TRANSACTION = 'withdraw';

    private $_withdraw_scene_id = 1000002;

    /**
     * @brief  execute
     */
    public function execute()
    {
        $strType = Bingo_Http_Request::get('type', '');

        if (empty($strType)) {
            Bingo_Log::warning(sprintf("invalid param.[param key : type]"));
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return ;
        }
        // check login
        if (!$this->_arrUserInfo['user_id']) {
            $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN);
            return ;
        }

        //check tbs
        if (!$this->_checkTbs()) {
            $this->_jsonRet(Tieba_Errcode::ERR_NVOTE_INVALID_TBS);
            return ;
        }

        $intPn = intval(Bingo_Http_Request::get('pn', 0));
        $intRn = intval(Bingo_Http_Request::get('rn', 0));
        $intPn = ($intPn <= 0) ? 1 : $intPn;
        $intRn = ($intRn <= 0) ? 10 : $intRn;
        $intStartTime = intval(Bingo_Http_Request::get('start_time', 0));
        $intEndTime   = intval(Bingo_Http_Request::get('end_time', 0));

        $arrInput = array(
            'pn'   => $intPn,
            'rn'   => $intRn,
        );

        // set the transaction type that need to query
        $intUserId = $this->_arrUserInfo['user_id'];
        if ($strType == self::FLOW_IN_TRANSACTION) {
            $arrInput['to_id'] = $intUserId;
        }
        if ($strType == self::FLOW_OUT_TRANSACTION) {
            $arrInput['from_id'] = $intUserId;
            $arrInput['filter'] = 'withdraw';
        }
        if ($strType == self::WITHDRAW_TRANSACTION) {
            $arrInput['from_id'] = $intUserId;
            $arrInput['to_id'] = $this->_withdraw_scene_id;
        }

        if (($intStartTime > 0) && ($intEndTime > 0)) {
            $arrInput['start_time'] = $intStartTime;
            $arrInput['end_time']   = $intEndTime;
        }
		$arrInput['type']	=0;	//订单类型 0 为自动订单,8为手动订单

        $arrRet = Tieba_Service::call('bluediamond', 'getBlueDiamondRecord', $arrInput, null, null, 'post', 'php', 'utf-8');
        $arrOutput = array();
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            if (Tieba_Errcode::ERR_NO_RECORD !==  $arrRet['errno']) {
                Bingo_Log::warning(sprintf("fail to call getBlueDiamondRecord. [input = %s] [output = %s]",
                    serialize($arrInput), serialize($arrRet)));
                $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                return ;
            }
            else {
                $arrPage = array(
                    'pn' => $intPn,
                    'rn' => $intRn,
                    'count_pn' => 0,
                );
                if ($strType == self::FLOW_IN_TRANSACTION) {
                    $arrOutput['income'] = array();
                    $arrOutput['income']['order_list'] = array();
                    $arrOutput['income']['pagination'] = $arrPage;
                    $arrOutput['income']['total_count'] = 0;

                }
                if ($strType == self::FLOW_OUT_TRANSACTION) {
                    $arrOutput['consume'] = array();
                    $arrOutput['consume']['order_list'] = array();
                    $arrOutput['consume']['pagination'] = $arrPage;
                    $arrOutput['consume']['total_count'] = 0;
                }
                if ($strType == self::WITHDRAW_TRANSACTION) {
                    $arrOutput['withdraw'] = array();
                    $arrOutput['withdraw']['order_list'] = array();
                    $arrOutput['withdraw']['pagination'] = $arrPage;
                    $arrOutput['withdraw']['total_count'] = 0;
                }
                $arrOutput['total_count'] = 0;
                $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrOutput);
                return ;
            }
        }
        $arrPage = array(
            'pn' => $arrRet['data']['page']['current_pn'],
            'rn' => $intRn,
            'count_pn' => $arrRet['data']['page']['total_pn'],
        );
        $intTotalCount = $arrRet['data']['page']['total_count'];

        $arrSceneIds = array();
        $arrOrderData = $arrRet['data']['list'];
        foreach ($arrRet['data']['list'] as $_order) {
            $arrSceneIds[] = $_order['scene_id'];
        }
        $arrInput = array(
            'query_words' => $arrSceneIds,
        );

        $arrRet = Tieba_Service::call('bluediamond', 'getSceneInfoBySceneIds', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("fail to call getSceneInfoBySceneIds. [input = %s] [output = %s]",
                serialize($arrInput), serialize($arrRet)));
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, '获取蓝钻明细列表失败');
            return ;
        }
        $arrSceneInfo = array();
        foreach ($arrRet['data'] as $_scene_Info_item) {
            $arrSceneInfo[$_scene_Info_item['scene_id']] = $_scene_Info_item['name'];
        }

        $arrOrderList = array();
        foreach ($arrOrderData as $_order) {
            $arrOrderList[] = array(
                'description' => $arrSceneInfo[$_order['scene_id']],
                'time'        => $_order['create_time'],
                'str_ext'     => $_order['str_ext'],
            );
        }
        if ($strType == self::FLOW_IN_TRANSACTION) {
            $arrOutput['income'] = array();
            $arrOutput['income']['order_list'] = $arrOrderList;
            $arrOutput['income']['pagination'] = $arrPage;
            $arrOutput['income']['total_count'] = $intTotalCount;
        }
        if ($strType == self::FLOW_OUT_TRANSACTION) {
            $arrOutput['consume'] = array();
            $arrOutput['consume']['order_list'] = $arrOrderList;
            $arrOutput['consume']['pagination'] = $arrPage;
            $arrOutput['consume']['total_count'] = $intTotalCount;
        }
        if ($strType == self::WITHDRAW_TRANSACTION) {
            $arrOutput['withdraw'] = array();
            $arrOutput['withdraw']['order_list'] = $arrOrderList;
            $arrOutput['withdraw']['pagination'] = $arrPage;
            $arrOutput['withdraw']['total_count'] = $intTotalCount;
        }
        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrOutput);
        return ;
    }
}
