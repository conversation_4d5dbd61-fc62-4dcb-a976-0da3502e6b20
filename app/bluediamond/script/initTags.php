<?php

/**
 * todo 初始化各个池子的标签的蓝钻数量
 * User: kangqin<PERSON>u
 * Date: 18-4-27
 */
ini_set ('memory_limit', '-1');
define('MODULE_NAME', 'bluediamond');
date_default_timezone_set ( "Asia/Chongqing" );
define ( 'APP_NAME', 'bluediamond' );
define ( 'SCRIPT_NAME', 'initTags' );
define ( 'ROOT_PATH', dirname ( __FILE__ ) . '/../../..' );
define ( 'SCRIPT_ROOT_PATH', ROOT_PATH . '/app/' . APP_NAME . '/script' );
define ( 'SCRIPT_LOG_PATH', ROOT_PATH . '/log/app/' . APP_NAME );
define ( 'SCRIPT_CONF_PATH', ROOT_PATH . '/conf/app/' . APP_NAME );
define('BASE_PATH', dirname(__FILE__));
define('IS_ORP_RUNTIME', true);
set_time_limit(0);

require_once BASE_PATH . '/../service/libs/Define.php';

if (! defined ( 'REQUEST_ID' )) {
    $requestTime = gettimeofday ();
    define ( 'REQUEST_ID', (intval ( $requestTime ['sec'] * 100000 + $requestTime ['usec'] / 10 ) & 0x7FFFFFFF) );
}

if (function_exists ( 'camel_set_logid' )) {
    camel_set_logid ( REQUEST_ID );
}
Bingo_Log::init(array(
    LOG_SCRIPT => array(
        'file' => SCRIPT_LOG_PATH . '/' . SCRIPT_NAME . '/' . SCRIPT_NAME . "_$requestTime.log",
        'level' => 0x01 | 0x02 | 0x04 | 0x08,
    ),
), LOG_SCRIPT );




initTags::execute();
class initTags{

    /**
     * @brief   执行入口
     * @param   null
     * @return  boolean
     */
    public static function execute(){

        // 获取数据库句柄
        $db = self::getDB();
        if(!$db){
            self::debug('获取蓝钻数据库的句柄失败');
            return false;
        }

        $intOldId = 0;
        while(true){

            // 开启事务
            if(!($db->startTransaction())){
                self::debug('开始数据库事务失败');
                break;
            }

            // 查询池子的蓝钻信息(加锁)
            $sql = "select id, scene_id, scores_total from scene_pool where id > {$intOldId} order by id asc limit 1 for update";
            $arrScenePoolInfo = $db->query($sql);
            if(!is_array($arrScenePoolInfo)){
                self::debug($sql);
                self::debug('获取池子的蓝钻信息失败');
                $db->rollback();
                $intOldId++;
                continue;
            }
            if(empty($arrScenePoolInfo)){
                break;
            }
            $arrScenePoolInfo = reset($arrScenePoolInfo);
            $intOldId = intval($arrScenePoolInfo['id']);

            // 获取蓝钻标签
            $arrScenePoolInfo['scores_total'] = intval($arrScenePoolInfo['scores_total']);
            switch($arrScenePoolInfo['scene_id']){
                case '2000192':
                    $arrTags = array(1 => $arrScenePoolInfo['scores_total']);
                    break;
                case '1000002':
                    $arrTags = self::getTags($arrScenePoolInfo['scores_total']);
                    break;
                case '1000001';
                    $arrTags = self::getTags($arrScenePoolInfo['scores_total']);
                    break;
                case '1000010':
                    $arrTags = array();
                    break;
                case '6200001':
                    $arrTags = array(2 => $arrScenePoolInfo['scores_total']);
                    break;
                case '1000012':
                    $arrTags = array(8 => $arrScenePoolInfo['scores_total']);
                    break;
                case '2030001':
                    $arrTags = self::getTags($arrScenePoolInfo['scores_total']);
                    break;
                case '1000013':
                    $arrTags = array(3 => $arrScenePoolInfo['scores_total']);
                    break;
                case '1000016':
                    $arrTags = array(8 => $arrScenePoolInfo['scores_total']);
                    break;
                case '1000017':
                    $arrTags = array(8 => $arrScenePoolInfo['scores_total']);
                    break;
                case '1000018':
                    $arrTags = array();
                    break;
                case '1000019':
                    $arrTags = array();
                    break;
                case '1000020':
                    $arrTags = array(6 => $arrScenePoolInfo['scores_total']);
                    break;
                case '1000021':
                    $arrTags = array();
                    break;
                case '1000022':
                    $arrTags = array(4 => $arrScenePoolInfo['scores_total']);
                    break;
                case '1000024':
                    $arrTags = array(5 => $arrScenePoolInfo['scores_total']);
                    break;
                default:
                    $arrTags = array();
                    break;
            }
            if(empty($arrTags)){
                self::debug("获取池子{$arrScenePoolInfo['scene_id']}的标签失败");
                $db->rollback();
                continue;
            }

            // 更新蓝钻标签
            $intNow = time();
            $strTags = addslashes(json_encode($arrTags));
            $sql = "update scene_pool set tags = '{$strTags}', update_time = {$intNow} where id = {$arrScenePoolInfo['id']}";
            $boolRet = $db->query($sql);
            if(!$boolRet){
                self::debug('更新蓝钻标签失败');
                $db->rollback();
                continue;
            }

            // 提交事务
            if(!($db->commit())){
                $db->rollback();
                self::debug('提交事务失败');
                continue;
            }
            self::debug("池子{$arrScenePoolInfo['scene_id']}处理成功");
        }

        self::debug('处理完毕');
        return true;

    }



    /**
     * @brief   获取标签
     * @param   array  $arrInput
     * @return  array
     */
    private static function getTags($intAmount){

        // 获取动态比例
        $arrRates = array();
        foreach(Service_Libs_Define::$arrTags as $intTag => $arrTagInfo){
            $arrRates[$intTag] = $arrTagInfo['rate'];
        }

        // 归一化动态比例，并计算各个标签的蓝钻数量，四舍五入
        $doubleSum = array_sum($arrRates);
        $arrTags = array();
        foreach($arrRates as $intTag => &$doubleRate){
            $doubleRate /= $doubleSum;
            $intPart = intval(round($doubleRate*$intAmount));
            if($intPart > 0){
                $arrTags[$intTag] = $intPart;
            }
        }
        unset($doubleRate);

        $intSum = array_sum($arrTags);

        // 超了
        if($intSum > $intAmount){
            $intDiff = $intSum - $intAmount;
            foreach($arrTags as $intTag => &$intValue){
                if($intDiff <= 0){
                    break;
                }
                if($intValue <= 0){
                    continue;
                }
                $intValue--;
                $intDiff--;
            }
            unset($intValue);
        }

        // 少了
        elseif($intSum < $intAmount){
            $intDiff = $intAmount - $intSum;
            foreach($arrTags as $intTag => &$intValue){
                if($intDiff <= 0){
                    break;
                }
                $intValue++;
                $intDiff--;
            }
            unset($intValue);
        }

        $intSum = 0;
        foreach($arrTags as $intTag => $intValue){
            if($intValue <= 0){
                unset($arrTags[$intTag]);
            }
            $intSum += $intValue;
        }
        if($intSum != $intAmount){
            Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' get tags failed, result: [' . json_encode($arrTags) . '], input: [' . json_encode($arrInput) . "]. \n");
            return array();
        }

        return $arrTags;
    }



    /**
     * @brief   调试
     * @param   string  $strMsg
     * @return  null
     */
    private static function debug($strMsg){
        $strMsg .= "\n";
        echo $strMsg;
        Bingo_Log::warning($strMsg);
    }


    /**
     * @brief   获取数据库句柄
     * @param   null
     * @return  object
     */
    private static function getDB(){
        $mysqlDB = Tieba_Mysql::getDB('forum_blue_diamond');
        if($mysqlDB && $mysqlDB->isConnected()) {
            $mysqlDB->charset('utf8');
            return $mysqlDB;
        } else {
            Bingo_Log::warning('db connect fail.');
            return null;
        }
    }
}