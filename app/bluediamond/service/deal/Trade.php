<?php


/**
 * todo 交易总service
 * todo service不再校验签名、不再判断时间戳是否过期，更不再检验权限
 * User: kangqinmou
 * Date: 18-12-15
 *
 * 示例：从A到B流动10枚蓝钻
 * kqm.service.tieba.otp.baidu.com/service/bluediamond?method=trade&ie=utf-8&format=json
 */
class Service_Deal_Trade extends Service_Libs_Base {


    const DATABASE_NAME = 'forum_blue_diamond';

    private static $traders = array('from', 'to');


    /**
     * @brief   蓝钻交易，入口
     * @param   array  $arrInput
     * @return  array
     */
    public static function trade($arrInput){

        Bingo_Log::warning("\n\nset a flag start: [" . serialize($arrInput) . "]. \n\n");

        // 参数校验
        $arrCheckRet = self::checkInput($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrCheckRet['errno']){
            return $arrCheckRet;
        }
        unset($arrCheckRet);

        // 获取用户信息
        $arrOutput = self::getUserInfos($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']){
            return $arrOutput;
        }
        unset($arrOutput);

        // 获取各个标签的动态比例
        $arrInput['rates'] = self::getRates();
        if(empty($arrInput['rates'])){
            return self::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL, array(), 'get rates failed');
        }

        // 获取数据库句柄
        $db = self::_getDB();
        if(!$db){
            return self::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL, array(), 'get db handle failed');
        }

        // 开启事务
        if(!($db->startTransaction())){
            Bingo_Log::warning("start transaction failed. \n");
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'start transaction failed');
        }

        $arrRet = self::errRet(Tieba_Errcode::ERR_SUCCESS);
        do{

            // 查询蓝钻订单，如已经存在，则退出交易
            $arrRet = self::getBlueDiamondRecord($arrInput, $db);
            if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                break;
            }
            if(!empty($arrRet['data'])){
                $arrRet = self::errRet(Tieba_Errcode::ERR_TBMALL_ORDER_ALREADY_EXIST, array(), 'order is existed');
                break;
            }

            // 获取交易双方的蓝钻数据及其过期时间
            $arrRet = self::getBlueDiamondCount($arrInput, $db);
            if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                break;
            }

            // 判断用户的蓝钻是否过期
            $arrRet = self::checkExpireTime($arrInput, $db);
            if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                break;
            }

            // 修改交易双方的蓝钻余额
            $arrRet = self::updateBlueDiamondCount($arrInput, $db);
            if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                break;
            }

            // 插入交易记录
            $arrRet = self::insertBlueDiamondRecord($arrInput, $db);
            if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                break;
            }

        }while(0);

        // 有错误时，回滚
        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            $db->rollback();
            Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' execute transaction failed, reason: [' . json_encode($arrRet) . "]. \n");
            $intErrno = isset($arrRet['errno']) ? $arrRet['errno'] : Tieba_Errcode::ERR_DL_CALL_FAIL;
            return self::errRet($intErrno, array(), $arrRet['usermsg']);
        }

        // 提交失败时，回滚
        if(false == $db->commit()){
            $db->rollback();
            Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' commit transaction failed. ');
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'commit transaction failed');
        }

        // 关联其它模块业务
        self::otherThing($arrInput);

        Bingo_Log::warning("\n\nset a flag end: [" . serialize($arrInput) . "]. \n\n");

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);

    }




    /**
     * @brief   参数校验
     * @param   array  $arrInput  入参
     * @return  array  校验结果
     */
    private static function checkInput(&$arrInput){

        // 校验字段是否存在
        $arrFields = array(
            'out_order_id',
            'from_type',            // 1用户，2池子
            'from_id',
            'to_type',              // 1用户，2池子
            'to_id',
            'amount',
            'order_desc',
        );
        $arrRet = self::checkParam($arrInput, $arrFields);
        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' is short of params, fields : [' . json_encode($arrFields) . '], input: [' . serialize($arrInput) . '], output: [' . serialize($arrRet) . "]. \n");
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'short of params');
        }

        // 校验整型字符是否大于0
        $arrIntFields = array(
            'from_type',
            'from_id',
            'to_type',
            'to_id',
            'amount',
        );
        $arrRet = self::checkParam($arrInput, $arrIntFields, 1);
        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', integer value must be > 0, integer fields: [' . json_encode($arrIntFields) . '], input: [' . serialize($arrInput) . '], output: [' . serialize($arrRet) . "]. \n");
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'integer must be > 0');
        }
        foreach($arrIntFields as $field){
            $arrInput[$field] = (int) $arrInput[$field];
        }

        // 重新整理入参
        $arrInput = array(
            'out_order_id' => $arrInput['out_order_id'],        // 第3方订单id
            'amount'       => $arrInput['amount'],              // 蓝钻交易数量
            'order_desc'   => $arrInput['order_desc'],          // 订单描述
            'now'          => time(),                           // 统一当前时间
            'from' => array(
                'type' => $arrInput['from_type'],               // from的类型，1用户，2池子
                'id'   => $arrInput['from_id'],                 // from的id
            ),
            'to' => array(
                'type' => $arrInput['to_type'],                 // to的类型，1用户，2池子
                'id'   => $arrInput['to_id'],                   // to的id
            ),
        );

        // from与to不能为同一人
        if($arrInput['from']['id'] == $arrInput['to']['id'] && $arrInput['from']['type'] == $arrInput['from']['type']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', from can\'t be to, input: [' . serialize($arrInput) . "]. \n");
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'from can\'t be to');
        }

        // 交易角色只能为用户或池子
        if(!isset(Service_Libs_Define::$arrRoles[$arrInput['from']['type']]) || !isset(Service_Libs_Define::$arrRoles[$arrInput['from']['type']])){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', role is error, input: [' . serialize($arrInput) . "]. \n");
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'role is error');
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }





    /**
     * @brief   获取交易用户的个人信息
     * @param   array  $arrInput  入参
     * @return  array  检测结果
     */
    private static function getUserInfos(&$arrInput){
        $arrUserIds = array();
        foreach(self::$traders as $trader){
            if(Service_Libs_Define::DEAL_ACCOUNT_TYPE_USER == $arrInput[$trader]['type']){
                $arrUserIds[] = $arrInput[$trader]['id'];
            }
        }
        if(!$arrUserIds){
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        // 并发请求
        $arrMultiCall = array(

            // 获取用户信息
            0 => array(
                'serviceName' => 'user',
                'method' => 'mgetUserData',
                'input' => array(
                    'user_id'  => $arrUserIds,
                ),
                'ie' => 'utf-8'
            ),

            // 获取ala用户信息(ala用户信息中的is_bluediamond_member字段，标识用户是否为蓝钻会员)：随时下线
            1 => array(
                'serviceName' => 'ala',
                'method' => 'userGetInfo',
                'input'  => array(
                    'uids' => $arrUserIds,
                ),
                'ie' => 'utf-8'
            )
        );

        $multiCall = new Tieba_Multi(__FUNCTION__);
        foreach ($arrMultiCall as $key => $value) {
            $multiCall->register($key, new Tieba_Service($value['serviceName']), $value);
        }
        $multiCall->call();
        $arrUserInfos = $multiCall->getResult(0);
        $arrAlaInfos  = $multiCall->getResult(1);
        unset($arrUserIds);

        // 用户信息
        if(!$arrUserInfos || Tieba_Errcode::ERR_SUCCESS != $arrUserInfos['errno'] || empty($arrUserInfos['user_info'])){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' get user infos failed by calling user.mgetUserData service, intput: [' . serialize($arrMultiCall[0]) . '], output: [' . serialize($arrUserInfos) . "]. \n");
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), 'get user infos failed');
        }
        $arrUserInfos = $arrUserInfos['user_info'];

        // ala信息
        if(!$arrAlaInfos || Tieba_Errcode::ERR_SUCCESS != $arrAlaInfos['errno'] || empty($arrAlaInfos['data'])){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' get users\' ala info failed by calling ala.userGetInfo service, intput: [' . serialize($arrMultiCall[1]) . '], output: [' . serialize($arrAlaInfos) . "]. \n");
            $arrAlaInfos = array();
        }else{
            $arrAlaInfos = $arrAlaInfos['data'];
        }

        // 整理入参：user_info，用户信息，is_bluediamond_member，用户是否为蓝钻会员，expire_time，新的蓝钻过期时间
        foreach(self::$traders as $trader){
            if(Service_Libs_Define::DEAL_ACCOUNT_TYPE_USER == $arrInput[$trader]['type']){
                $intId = $arrInput[$trader]['id'];
                $arrInput[$trader]['user_info'] = (!empty($arrUserInfos[$intId]) && is_array($arrUserInfos[$intId])) ? $arrUserInfos[$intId] : array();
                $arrInput[$trader]['is_bluediamond_member'] = (isset($arrAlaInfos[$intId]['is_bluediamond_member']) && 1 == $arrAlaInfos[$intId]['is_bluediamond_member']) ? 1 : 0;

                if(!$arrInput[$trader]['user_info']){
                    Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ", user id [$intId] not exists. \n");
                    return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'user id not exists');
                }

                /*
                 更新用户的蓝钻过期时间
                 非贴吧会员：当前时间+30天；贴吧会员：会员截止时间+30天；ala直播的蓝钻会员：Service_Libs_Define::INT_EXPIRE_TIME_ALA
                 蓝钻过期时间取上述三者的最大值
                 */
                $intEndTime    = isset($arrInput[$trader]['user_info']['vipInfo']['e_time']) ? intval($arrInput[$trader]['user_info']['vipInfo']['e_time']) : 0;
                $intExpireTime = max($arrInput['now'], $intEndTime) + 30*86400;
                if(1 == $arrInput[$trader]['is_bluediamond_member']){
                    $intExpireTime = max($intExpireTime, Service_Libs_Define::INT_EXPIRE_TIME_ALA);
                }
                $arrInput[$trader]['expire_time'] = $intExpireTime;
            }
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }




    /**
     * @brief   获取蓝钻订单
     * @param   array   $arrInput
     * @param   source  $db
     * @return  array
     */
    private static function getBlueDiamondRecord(&$arrInput, $db){

        // 本查询无须加锁：因为之前业务的设计缺陷，out_order_id字段未加唯一索引
        $arrParams = array(
            'db'           => $db,
            'out_order_id' => $arrInput['out_order_id'],
            'to_id'        => $arrInput['to']['id'],
            'appends'      => 'limit 1',
        );
        $arrRet = Dl_BlueDiamondRecord::select($arrParams);
        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' select blue_diamond_record failed by calling Dl_BlueDiamondRecord.select, input: [' . json_encode($arrParams) . '], output: [' . json_encode($arrRet) . "]. \n");
            return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), $arrRet['usermsg']);
        }
        return $arrRet;
    }





    /**
     * @brief   获取用户和池子的蓝钻剩余量，如果记录不存在，则插入记录
     * @param   array   $arrInput
     * @param   source  $db
     * @param   string  $trader
     * @return  array
     */
    private static function getBlueDiamondCount(&$arrInput, $db){

        // 为避免死锁，优先给type或id较大的一方加锁：用标识符bigTrader与smallTrader表示
        if($arrInput['from']['type'] > $arrInput['to']['type']){
            $bigTrader   = 'from';
            $smallTrader = 'to';
        }elseif($arrInput['from']['type'] < $arrInput['to']['type']){
            $bigTrader   = 'to';
            $smallTrader = 'from';
        }else{
            if($arrInput['from']['id'] > $arrInput['to']['id']){
                $bigTrader   = 'from';
                $smallTrader = 'to';
            }else{
                $bigTrader   = 'to';
                $smallTrader = 'from';
            }
        }

        $intNow = $arrInput['now'];

        foreach(array($bigTrader, $smallTrader) as $trader){

            // 获取用户的蓝钻信息
            if(Service_Libs_Define::DEAL_ACCOUNT_TYPE_USER == $arrInput[$trader]['type']){
                $arrParams = array(
                    'db'      => $db,
                    'user_id' => $arrInput[$trader]['id'],
                    'appends' => 'limit 1 for update',
                );
                $arrRet = Dl_UserScores::select($arrParams);
                if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                    Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . 'get user\'s blue diamond count failed by calling Dl_UserScores.select, intput: [' . serialize($arrParams) . '], output: [' . serialize($arrRet) . "]. \n");
                    return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), $arrRet['usermsg']);
                }

                if(empty($arrRet['data'])){
                    $arrInsertInput = array(
                        'db'          => $db,
                        'user_id'     => $arrInput[$trader]['id'],
                        'create_time' => $intNow,
                        'create_time' => $intNow,
                    );
                    $arrInsertOut = Dl_UserScores::insert($arrInsertInput);
                    if(!$arrInsertOut || Tieba_Errcode::ERR_SUCCESS != $arrInsertOut['errno']){
                        Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' insert user scores failed by calling Dl_UserScores.insert, input: [' . serialize($arrInsertInput) . '], output: [' . serialize($arrInsertOut) . "]. \n");
                        return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), 'insert user scores failed');
                    }
                    $arrInput[$trader]['diamond_info'] = array(
                        'id'           => intval($arrInsertOut['data']['id']),
                        'user_id'      => $arrInput[$trader]['id'],
                        'scores_total' => 0,
                        'expire_time'  => 0,
                        'create_time'  => $intNow,
                        'update_time'  => $intNow,
                        'tags'         => array(),
                    );

                }else{
                    $arrInput[$trader]['diamond_info'] = reset($arrRet['data']);
                }

            }

            // 获取池子的蓝钻信息
            elseif(Service_Libs_Define::DEAL_ACCOUNT_TYPE_SCENE == $arrInput[$trader]['type']){
                $arrParams = array(
                    'db'       => $db,
                    'scene_id' => $arrInput[$trader]['id'],
                    'appends'  => 'limit 1 for update',
                );
                $arrRet = Dl_ScenePool::select($arrParams);
                if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                    Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' get pool\'s blue diamond count failed by calling Dl_ScenePool.select, input: [' . serialize($arrParams) . '], output: [' . serialize($arrRet) . "]. \n");
                    return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), $arrRet['usermsg']);
                }

                if(empty($arrRet['data'])){
                    $arrInsertInput = array(
                        'db'          => $db,
                        'scene_id'    => $arrInput[$trader]['id'],
                        'create_time' => $intNow,
                        'update_time' => $intNow,
                    );
                    $arrInsertOut = Dl_ScenePool::insert($arrInsertInput);
                    if(!$arrInsertOut || Tieba_Errcode::ERR_SUCCESS != $arrInsertOut['errno']){
                        Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' insert scene_pool failed by calling Dl_ScenePool.insert, input: [' . serialize($arrInsertInput) . '], output: [' . serialize($arrInsertOut) . "]. \n");
                        return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), $arrRet['usermsg']);
                    }
                    $arrInput[$trader]['diamond_info'] = array(
                        'id'           => $arrInsertOut['data']['id'],
                        'scene_id'     => $arrInput[$trader]['id'],
                        'sub_scene_id' => '',
                        'scores_total' => 0,
                        'create_time'  => $intNow,
                        'update_time'  => $intNow,
                        'tags'         => array(),
                    );

                }else{
                    $arrInput[$trader]['diamond_info'] = reset($arrRet['data']);
                }

            }
        }


        return self::errRet(Tieba_Errcode::ERR_SUCCESS);

    }







    /**
     * @brief   判断用户的蓝钻是否过期
     * @param
     * @return
     */
    private static function checkExpireTime(&$arrInput, $db){

        $intNow = $arrInput['now'];
        $arrPoolInfo = array();

        // 本意就是想将用户蓝钻流入过期池，则不用考虑过期
        if(Service_Libs_Define::DEAL_SCENE_POOL_EXPIRE == $arrInput['to']['id'] && Service_Libs_Define::DEAL_ACCOUNT_TYPE_SCENE == $arrInput['to']['type']){
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        foreach(self::$traders as $trader){

            // 只有用户才有蓝钻过期的概念
            if(Service_Libs_Define::DEAL_ACCOUNT_TYPE_USER != $arrInput[$trader]['type']){
                continue;
            }

            // 蓝钻未过期，不处理
            if($arrInput[$trader]['diamond_info']['expire_time'] > $intNow){
                continue;
            }

            // 蓝钻数量小于等于0，不处理
            if($arrInput[$trader]['diamond_info']['scores_total'] <= 0){
                continue;
            }

            // ala直播的蓝钻会员，蓝钻不会过期
            if(1 == $arrInput[$trader]['is_bluediamond_member']){
                continue;
            }

            // 贴吧会员，过期时间为：vip截止时间+30天
            if(isset($arrInput[$trader]['user_info']['vipInfo']['e_time']) && $arrInput[$trader]['user_info']['vipInfo']['e_time'] + 30*86400 > $intNow){
                continue;
            }


            /**
             * 将用户的过期蓝钻流入过期池子
             */

            // 获取过期池子的蓝钻信息
            if(empty($arrPoolInfo)){
                $arrParams = array(
                    'db'       => $db,
                    'scene_id' => Service_Libs_Define::DEAL_SCENE_POOL_EXPIRE,
                    'appends'  => 'limit 1 for update',
                );
                $arrPoolInfo = Dl_ScenePool::select($arrParams);
                if(!$arrPoolInfo || Tieba_Errcode::ERR_SUCCESS != $arrPoolInfo['errno'] || empty($arrPoolInfo['data'])){
                    Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' get expire pool\'s diamond info failed by calling Dl_ScenePool.select, input: [' . serialize($arrParams) . '], output: [' . serialize($arrPoolInfo) . "]. \n");
                    return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), 'get expire pool\'s diamond info failed');
                }
                $arrPoolInfo = reset($arrPoolInfo['data']);
            }

            // 修改交易双方的蓝钻余额
            $arrParams = array(
                'out_order_id' => Tbmall_Open_Order::genInnerOrderId($arrInput[$trader]['id'], Service_Libs_Define::DEAL_SCENE_POOL_EXPIRE),
                'amount'       => $arrInput[$trader]['diamond_info']['scores_total'],
                'order_desc'   => 'the expire order',
                'from'         => $arrInput[$trader],
                'to'           => array(
                    'id'           => Service_Libs_Define::DEAL_SCENE_POOL_EXPIRE,
                    'type'         => Service_Libs_Define::DEAL_ACCOUNT_TYPE_SCENE,
                    'diamond_info' => $arrPoolInfo,
                ),
                'rates'        => $arrInput['rates'],
                'now'          => $arrInput['now'],
            );
            $arrRet = self::updateBlueDiamondCount($arrParams, $db);
            if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                return $arrRet;
            }

            // 插入交易记录
            $arrRet = self::insertBlueDiamondRecord($arrParams, $db);
            if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                return $arrRet;
            }

            // 修改过期池子的蓝钻余额，修改用户的蓝钻余额
            $arrPoolInfo['scores_total'] += $arrInput[$trader]['diamond_info']['scores_total'];
            $arrInput[$trader]['diamond_info']['scores_total'] = 0;
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);

    }





    /**
     * @brief   更新用户与池子的蓝钻余额
     * @param   array  $arrInput
     * @return  array
     */
    private static function updateBlueDiamondCount(&$arrInput, $db){

        // 判断from的蓝钻数量是否足够
        if($arrInput['from']['diamond_info']['scores_total'] < $arrInput['amount']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ': blue diamond count is not enough, input: [' . json_encode($arrInput) . "]. \n");
            return self::errRet(Tieba_Errcode::ERR_SCORES_NOT_ENOUGH, array(), 'scores is not enough');
        }

        // from为池子时，池子的标签不能为空
        if(Service_Libs_Define::DEAL_ACCOUNT_TYPE_SCENE == $arrInput['from']['type']){
            if(empty($arrInput['from']['diamond_info']['tags'])){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . "from池子{$arrInput['from']['id']}的蓝钻没有标签, input: [" . json_encode($arrInput) . "]. \n");
                return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), "from池子{$arrInput['from']['id']}的蓝钻没有标签");
            }
        }

        // 如果用户的蓝钻没有标签，或者带标签的蓝钻之和不等于总量，则按照动态比例重新计算用户各标签的蓝钻数量
        foreach(self::$traders as $trader){
            if(Service_Libs_Define::DEAL_ACCOUNT_TYPE_USER != $arrInput[$trader]['type']){
                continue;
            }
            if($arrInput[$trader]['diamond_info']['scores_total'] <= 0){
                $arrInput[$trader]['diamond_info']['tags'] = array();
                $arrInput[$trader]['diamond_info']['scores_total'] = 0;
                continue;
            }
            $intSum = 0;
            foreach($arrInput[$trader]['diamond_info']['tags'] as $intTag => $intValue){
                if($intValue > 0){
                    $intSum += $intValue;
                }else{
                    unset($arrInput[$trader]['diamond_info']['tags'][$intTag]);
                }
            }
            if($intSum == $arrInput[$trader]['diamond_info']['scores_total']){
                continue;
            }
            Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ", calculate tags again. \n");
            $arrInput[$trader]['diamond_info']['tags'] = self::getTags($arrInput, $arrInput[$trader]['diamond_info']['scores_total']);
            if(empty($arrInput[$trader]['diamond_info']['tags'])){
                return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'get tags failed for user');
            }
        }

        // todo 过期池，交易池，提现池，这3个池子比较特殊，其蓝钻可以有多个标签；其它池子的蓝钻有且只有一个标签，开发时请严格把控，避免蓝钻之间相互污染，影响财务对账

        // 一：当to为普通池子时，说明是将蓝钻退回给to
        if(Service_Libs_Define::DEAL_ACCOUNT_TYPE_SCENE == $arrInput['to']['type'] && !isset(Service_Libs_Define::$arrSpecialPools[$arrInput['to']['id']])){

            // to为普通池子时，标签不能为空
            if(empty($arrInput['to']['diamond_info']['tags'])){
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ": to普通池子{$arrInput['to']['id']}的标签为空, input: [" . json_encode($arrInput) . "]. \n");
                return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), "to普通池子{$arrInput['to']['id']}的标签为空");
            }

            reset($arrInput['to']['diamond_info']['tags']);
            $intTag = key($arrInput['to']['diamond_info']['tags']);
            $arrTags = array(
                $intTag => $arrInput['amount'],
            );

            // from的蓝钻够不够?
            $strTagName = isset(Service_Libs_Define::$arrTags[$intTag]['name']) ? Service_Libs_Define::$arrTags[$intTag]['name'] : '未知';
            if(!isset($arrInput['from']['diamond_info']['tags'][$intTag]) || $arrInput['from']['diamond_info']['tags'][$intTag] < $arrInput['amount']){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . " from的{$intTag}:{$strTagName}标签的蓝钻数量不够, input: [" . json_encode($arrInput) . "]. \n");
                return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), "卖家的{$intTag}:{$strTagName}标签的蓝钻数量不够");
            }

        }


        // 二：当from为特殊池子时：(1)可能是将蓝钻退回给to，(2)也有可能是用户从交易池中购买蓝钻
        if(empty($arrTags) && Service_Libs_Define::DEAL_ACCOUNT_TYPE_SCENE == $arrInput['from']['type'] && isset(Service_Libs_Define::$arrSpecialPools[$arrInput['from']['id']])){

            // 寻找历史订单：from、to与当前订单相反
            $arrParams = array(
                'db'         => $db,
                'from_id'    => $arrInput['to']['id'],
                'from_type'  => $arrInput['to']['type'],
                'to_id'      => $arrInput['from']['id'],
                'to_type'    => $arrInput['from']['type'],
                'total_cost' => $arrInput['amount'],
                'appends'    => 'order by create_time limit 1',
            );
            $arrRecord = Dl_BlueDiamondRecord::select($arrParams);
            if(!$arrRecord || Tieba_Errcode::ERR_SUCCESS != $arrRecord['errno']){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' select blue_diamond_record failed by calling Dl_BlueDiamondRecord.select, input: [' . json_encode($arrParams) . '], output: [' . json_encode($arrRecord) . "]. \n");
                return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), 'select blue diamond record failed');
            }

            // 历史订单存在时，$arrTags走历史订单
            if(!empty($arrRecord['data'])){
                $arrRecord = reset($arrRecord['data']);
                $intSum = 0;
                foreach($arrRecord['tags'] as $intTag => $intValue){
                    if($intValue > 0){
                        $intSum += $intValue;
                    }else{
                        unset($arrRecord['tags'][$intTag]);
                    }
                }
                if($intSum == $arrInput['amount']){
                    $arrTags = $arrRecord['tags'];
                }
            }
        }

        // 根据动态比例获取$arrTags
        if(empty($arrTags)){
            $arrTags = self::getTags($arrInput, $arrInput['amount']);
            if(empty($arrTags)){
                return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'get tags failed for trade');
            }
        }

        // $arrTags中各个标签的蓝钻数量有可能超出$arrInput['from']['diamond_info']['tags']中对应的蓝钻数量
        $arrFromTags = $arrInput['from']['diamond_info']['tags'];
        if(count($arrFromTags) == 1){
            reset($arrFromTags);
            $arrTags = array(
                key($arrFromTags) => $arrInput['amount'],
            );
        }else{
            foreach($arrTags as $intTag => &$intValue){
                if(!isset($arrFromTags[$intTag]) || $arrFromTags[$intTag] <= 0){
                    unset($arrTags[$intTag], $arrFromTags[$intTag]);
                }elseif($arrFromTags[$intTag] <= $intValue){
                    $intValue = $arrFromTags[$intTag];
                    unset($arrFromTags[$intTag]);
                }else{
                    $arrFromTags[$intTag] -= $intValue;
                }
            }
            unset($intValue);

            $intSum = array_sum($arrTags);
            if($intSum < $arrInput['amount']){
                $intDiff = $arrInput['amount'] - $intSum;
                asort($arrFromTags);
                foreach($arrFromTags as $intTag => $intValue){
                    if($intValue >= $intDiff){
                        $arrTags[$intTag] = isset($arrTags[$intTag]) ? ($arrTags[$intTag]+$intDiff) : $intDiff;
                        break;
                    }else{
                        $arrTags[$intTag] = isset($arrTags[$intTag]) ? ($arrTags[$intTag]+$intValue) : $intValue;
                        $intDiff -= $intValue;
                    }
                }
            }
        }
        unset($arrFromTags);

        // 校验$arrTags
        if(array_sum($arrTags) != $arrInput['amount']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', calculate tags failed, result: [' . json_encode($arrTags) . '], input: [' . json_encode($arrInput) . "]. \n");
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'calculate tags failed');
        }
        foreach($arrTags as $intTag => $intValue){
            if(!isset($arrInput['from']['diamond_info']['tags'][$intTag]) || $arrInput['from']['diamond_info']['tags'][$intTag] < $intValue){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', tags is wrong, result: [' . json_encode($arrTags) . '], input: [' . json_encode($arrInput) . "]. \n");
                return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'tags is wrong');
            }
        }

        // to为普通池子时，额外处理
        if(Service_Libs_Define::DEAL_ACCOUNT_TYPE_SCENE == $arrInput['to']['type'] && !isset(Service_Libs_Define::$arrSpecialPools[$arrInput['to']['id']])){

            // to为普通池子时，标签不能为空
            if(empty($arrInput['to']['diamond_info']['tags'])){
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ": to普通池子{$arrInput['to']['id']}的标签为空, input: [" . json_encode($arrInput) . "]. \n");
                return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), "to普通池子{$arrInput['to']['id']}的标签为空");
            }

            reset($arrInput['to']['diamond_info']['tags']);
            $intTag = key($arrInput['to']['diamond_info']['tags']);

            // from的蓝钻够不够?
            $strTagName = isset(Service_Libs_Define::$arrTags[$intTag]['name']) ? Service_Libs_Define::$arrTags[$intTag]['name'] : '未知';
            reset($arrTags);
            if(count($arrTags) != 1 || key($arrTags) != $intTag || !isset($arrInput['from']['diamond_info']['tags'][$intTag]) || $arrInput['from']['diamond_info']['tags'][$intTag] < $arrInput['amount']){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . " from的{$intTag}:{$strTagName}标签的蓝钻数量不够, input: [" . json_encode($arrInput) . "]. \n");
                return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), "卖家的{$intTag}:{$strTagName}标签的蓝钻数量不够");
            }

        }

        // from加，to减
        foreach($arrTags as $intTag => $intValue){
            $arrInput['from']['diamond_info']['tags'][$intTag] -= $intValue;
            $arrInput['to']['diamond_info']['tags'][$intTag]   += $intValue;
        }
        $arrInput['tags'] = $arrTags;

        foreach(self::$traders as $trader){

            // 更新用户的蓝钻余额
            if(Service_Libs_Define::DEAL_ACCOUNT_TYPE_USER == $arrInput[$trader]['type']){
                $arrParams = array(
                    'db'           => $db,
                    'id'           => $arrInput[$trader]['diamond_info']['id'],
                    'scores_total' => ('from' == $trader) ? ($arrInput[$trader]['diamond_info']['scores_total'] - $arrInput['amount']) : ($arrInput[$trader]['diamond_info']['scores_total'] + $arrInput['amount']),
                    'expire_time'  => $arrInput[$trader]['expire_time'],
                    'tags'         => json_encode($arrInput[$trader]['diamond_info']['tags']),
                    'update_time'  => $arrInput['now'],
                );
                $arrRet = Dl_UserScores::updateById($arrParams);
                if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                    Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' update user_scores failed by calling Dl_UserScores::updateById, input: [' . json_encode($arrParams) . '], output: [' . json_encode($arrRet) . "]. \n");
                    return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), $arrRet['usermsg']);
                }
            }

            // 更新池子的蓝钻余额
            elseif(Service_Libs_Define::DEAL_ACCOUNT_TYPE_SCENE == $arrInput[$trader]['type']){
                $arrParams = array(
                    'db'           => $db,
                    'id'           => $arrInput[$trader]['diamond_info']['id'],
                    'scores_total' => ('from' == $trader) ? ($arrInput[$trader]['diamond_info']['scores_total'] - $arrInput['amount']) : ($arrInput[$trader]['diamond_info']['scores_total'] + $arrInput['amount']),
                    'tags'         => json_encode($arrInput[$trader]['diamond_info']['tags']),
                    'update_time'  => $arrInput['now'],
                );
                $arrRet = Dl_ScenePool::updateById($arrParams);
                if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                    Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' update scene_pool failed by calling Dl_ScenePool::updateById, input: [' . serialize($arrParams) . '], output: [' . serialize($arrRet) . "]. \n");
                    return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), $arrRet['usermsg']);
                }
            }

        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }





    /**
     * @brief   插入蓝钻交易记录
     * @param   array  $arrInput
     * @return  array
     */
    private static function insertBlueDiamondRecord(&$arrInput, $db){

        // 生成内部订单id
        $arrIds = array();
        $intSceneId = 0;
        $intUserId  = 0;
        foreach(self::$traders as $trader){
            if(Service_Libs_Define::DEAL_ACCOUNT_TYPE_USER == $arrInput[$trader]['type']){
                $intUserId = $arrInput[$trader]['id'];
                array_unshift($arrIds, $intUserId);
            }else{
                $intSceneId = $arrInput[$trader]['id'];
                array_push($arrIds, $intSceneId);
            }
        }
        $strOrderId = Tbmall_Open_Order::genInnerOrderId($arrIds[0], $arrIds[1]);

        // 插入blue_diamond_record
        $arrParams = array(
            'db'           => $db,
            'order_id'     => $strOrderId,
            'scene_id'     => $intSceneId,
            'from_id'      => $arrInput['from']['id'],
            'to_id'        => $arrInput['to']['id'],
            'from_type'    => $arrInput['from']['type'],
            'to_type'      => $arrInput['to']['type'],
            'total_cost'   => $arrInput['amount'],
            'status'       => Service_Libs_Define::RECORD_ORDER_STATUS_DELIVER_SUCCESS,
            'out_order_id' => $arrInput['out_order_id'],
            'order_desc'   => $arrInput['order_desc'],
            'tags'         => json_encode($arrInput['tags']),
            'create_time'  => $arrInput['now'],
            'update_time'  => $arrInput['now'],
        );
        $arrRet = Dl_BlueDiamondRecord::insert($arrParams);
        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' insert blue_diamond_record failed by calling Dl_BlueDiamondRecord.insert, input: [' . serialize($arrParams) . '], output: [' . serialize($arrRet) . "]. \n");
            return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), 'insert blue diamond record failed');
        }

        // 插入scene_record和user_record
        foreach(self::$traders as $trader){

            // 插入user_record
            if(Service_Libs_Define::DEAL_ACCOUNT_TYPE_USER == $arrInput[$trader]['type']){
                $arrParams = array(
                    'db'           => $db,
                    'order_id'     => $strOrderId,
                    'out_order_id' => $arrInput['out_order_id'],
                    'order_desc'   => $arrInput['order_desc'],
                    'user_id'      => $arrInput[$trader]['id'],
                    'scene_id'     => $intSceneId,
                    'total_cost'   => $arrInput['amount'],
                    'now_scores'   => $arrInput[$trader]['diamond_info']['scores_total'],
                    'left_scores'  => ('from'==$trader) ? ($arrInput[$trader]['diamond_info']['scores_total']-$arrInput['amount']) : ($arrInput[$trader]['diamond_info']['scores_total']+$arrInput['amount']),
                    'status'       => Service_Libs_Define::RECORD_ORDER_STATUS_DELIVER_SUCCESS,
                    'type'         => ('from'==$trader) ? Service_Libs_Define::RECORD_ORDER_TYPE_REDUCE : Service_Libs_Define::RECORD_ORDER_TYPE_ADD,
                    'tags'         => json_encode($arrInput['tags']),
                    'create_time'  => $arrInput['now'],
                    'update_time'  => $arrInput['now'],
                );
                $arrRet = Dl_UserRecord::insert($arrParams);
                if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                    Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' insert user_record failed by calling Dl_UserRecord.insert, input: [' . serialize($arrParams) . '], output: [' . serialize($arrRet) . "]. \n");
                    return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), $arrRet['usermsg']);
                }
            }

            // 插入scene_record
            elseif(Service_Libs_Define::DEAL_ACCOUNT_TYPE_SCENE == $arrInput[$trader]['type']){
                $arrParams = array(
                    'db'           => $db,
                    'order_id'     => $strOrderId,
                    'out_order_id' => $arrInput['out_order_id'],
                    'order_desc'   => $arrInput['order_desc'],
                    'scene_id'     => $arrInput[$trader]['id'],
                    'user_id'      => $intUserId,
                    'total_cost'   => $arrInput['amount'],
                    'now_scores'   => $arrInput[$trader]['diamond_info']['scores_total'],
                    'left_scores'  => ('from'==$trader) ? ($arrInput[$trader]['diamond_info']['scores_total']-$arrInput['amount']) : ($arrInput[$trader]['diamond_info']['scores_total']+$arrInput['amount']),
                    'status'       => Service_Libs_Define::RECORD_ORDER_STATUS_DELIVER_SUCCESS,
                    'type'         => ('from'==$trader) ? Service_Libs_Define::RECORD_ORDER_TYPE_REDUCE : Service_Libs_Define::RECORD_ORDER_TYPE_ADD,
                    'tags'         => json_encode($arrInput['tags']),
                    'create_time'  => $arrInput['now'],
                    'update_time'  => $arrInput['now'],
                );
                $arrRet = Dl_SceneRecord::insert($arrParams);
                if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                    Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' insert scene_record failed by calling Dl_UserRecord.insert, input: [' . serialize($arrParams) . '], output: [' . serialize($arrRet) . "]. \n");
                    return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), $arrRet['usermsg']);
                }
            }

        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }






    /**
     * @brief   获取redis中的蓝钻动态比例
     * @param   array  $arrInput
     * @return  array
     */
    private static function getRates(){
        static $arrRates = array();     // 静态局部变量：保证I/O只有一次，缩短运行时间
        if(!empty($arrRates) && is_array($arrRates)){
            return $arrRates;
        }
        $arrRedisInput = array(
            'key' => 'blue_diamond_dynamic_rate_of_tags',       // 蓝钻的标签动态比例
        );
        $arrRedisOut = Util_Redis::getFromRedis($arrRedisInput);
        if(empty($arrRedisOut) || !is_array($arrRedisOut)){
            $arrRates = array();
            foreach(Service_Libs_Define::$arrTags as $intTag => $arrTagInfo){
                $arrRates[$intTag] = $arrTagInfo['rate'];
            }
            Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' get tags rate failed by running get in redis, input: [' . json_encode($arrRedisInput) . '], output: [' . json_encode($arrRedisOut) . "]. \n");
        }else{
            $arrRates = $arrRedisOut;
        }
        foreach($arrRates as $intTag => $doubleRate){
            if($doubleRate <= 0){
                unset($arrRates[$intTag]);
            }
        }
        if(empty($arrRates)){
            Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ", the rates is empty. \n");
            return array();
        }
        return $arrRates;
    }






    /**
     * @brief   获取标签
     * @param   array  $arrInput
     * @return  array
     */
    private static function getTags(&$arrInput, $intAmount){

        $arrRates = $arrInput['rates'];     // 写时才复制，不会增加内存消耗

        // 归一化动态比例，并计算各个标签的蓝钻数量，四舍五入
        $doubleSum = array_sum($arrRates);
        $arrTags = array();
        foreach($arrRates as $intTag => &$doubleRate){
            $doubleRate /= $doubleSum;
            $intPart = intval(round($doubleRate*$intAmount));
            if($intPart > 0){
                $arrTags[$intTag] = $intPart;
            }
        }
        unset($doubleRate);

        $intSum = array_sum($arrTags);

        // 超了
        if($intSum > $intAmount){
            $intDiff = $intSum - $intAmount;
            foreach($arrTags as $intTag => &$intValue){
                if($intDiff <= 0){
                    break;
                }
                if($intValue <= 0){
                    continue;
                }
                $intValue--;
                $intDiff--;
            }
            unset($intValue);
        }

        // 少了
        elseif($intSum < $intAmount){
            $intDiff = $intAmount - $intSum;
            foreach($arrTags as $intTag => &$intValue){
                if($intDiff <= 0){
                    break;
                }
                $intValue++;
                $intDiff--;
            }
            unset($intValue);
        }

        $intSum = 0;
        foreach($arrTags as $intTag => $intValue){
            if($intValue <= 0){
                unset($arrTags[$intTag]);
            }
            $intSum += $intValue;
        }
        if($intSum != $intAmount){
            Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' get tags failed, result: [' . json_encode($arrTags) . '], input: [' . json_encode($arrInput) . "]. \n");
            return array();
        }

        return $arrTags;
    }





    /**
     * @brief   其它关联模块的业务
     * @param   array  $arrInput
     * @return  array
     */
    private static function otherThing(&$arrInput){

        $arrMultiCall = array();

        foreach(self::$traders as $trader){
            if(Service_Libs_Define::DEAL_ACCOUNT_TYPE_USER != $arrInput[$trader]['type']){
                continue;
            }

            // 将蓝钻数量和过期时间同步给arch
            /*
            $arrMultiCall[$trader.':user.setUserAttrByArray'] = array(
                'serviceName' => 'user',
                'method'      => 'setUserAttrByArray',
                'ie'          => 'utf-8',
                'input' => array(
                    'user_id'	 => $arrInput[$trader]['id'],
                    'attr_name'	 => 'diamond',
                    'attr_value'	=> array(
                        'scores'	=> ('from' == $trader) ? ($arrInput[$trader]['diamond_info']['scores_total'] - $arrInput['amount']) : ($arrInput[$trader]['diamond_info']['scores_total'] + $arrInput['amount']),
                        'end_time'	=> $arrInput[$trader]['expire_time'],
                    ),
                ),
            );
            */

            // 将蓝钻数量和过期时间同步给arch，权宜之计，只能走nj机房，(主库在nj，跨区域访问主库会超时，造成数据库连接失败，arch项目组的坑)
            $arrParams = array(
                'user_id'	 => $arrInput[$trader]['id'],
                'attr_name'	 => 'diamond',
                'attr_value'	=> array(
                    'scores'	=> ('from' == $trader) ? ($arrInput[$trader]['diamond_info']['scores_total'] - $arrInput['amount']) : ($arrInput[$trader]['diamond_info']['scores_total'] + $arrInput['amount']),
                    'end_time'	=> $arrInput[$trader]['expire_time'],
                ),
            );
            $_SERVER['HTTP_X_BD_FORCE_IDC'] = 'nj';
            $arrRet = Tieba_Service::call('user', 'setUserAttrByArray', $arrParams, null, null, 'post', 'php', 'utf-8');
            if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . " {$trader}.user.setUserAttrByArray failed, input: [" . json_encode($arrParams) . '], output: [' . json_encode($arrRet) . "]. \n");
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), "{$trader}.user.setUserAttrByArray failed");
            }else{
                Bingo_Log::warning("{$trader}.user.setUserAttrByArray, input: [" . json_encode($arrParams) . '], output: [' . json_encode($arrRet) . "]. \n");
            }
            unset($arrRet, $_SERVER['HTTP_X_BD_FORCE_IDC']);

            // icon业务
            if('to' == $trader){
                if($arrInput[$trader]['expire_time']){
                    $arrMultiCall[$trader.':icon.addUidsToPass'] = array(
                        'serviceName' => 'icon',
                        'method'      => 'addUidsToPass',
                        'ie'          => 'utf-8',
                        'input'       => array(
                            'app_id' => 'diamond',
                            'input'  => array(
                                array(
                                    'user_id'  => $arrInput[$trader]['id'],
                                    'end_time' => $arrInput[$trader]['expire_time'],
                                ),
                            ),
                        ),
                    );
                }
            }

            // icon业务
            elseif('from' == $trader){
                if($arrInput[$trader]['expire_time'] && $arrInput[$trader]['diamond_info']['scores_total'] - $arrInput['amount'] <= 0){
                    $arrMultiCall[$trader.':icon.delUidsToPass'] = array(
                        'serviceName' => 'icon',
                        'method'      => 'delUidsToPass',
                        'ie'          => 'utf-8',
                        'input'       => array(
                            'app_id' => 'diamond',
                            'input'  => array(
                                array(
                                    'user_id' => $arrInput[$trader]['id'],
                                ),
                            ),
                        ),
                    );
                }
            }
        }

        if(!$arrMultiCall){
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        // 并发请求
        $multiCall = new Tieba_Multi(__FUNCTION__);
        foreach ($arrMultiCall as $key => $value) {
            $multiCall->register($key, new Tieba_Service($value['serviceName']), $value);
        }
        $multiCall->call();

        // 处理结果
        foreach($arrMultiCall as $key => $value){
            $arrRet = $multiCall->getResult($key);
            if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . " {$key} failed, input: [" . json_encode($value) . '], output: [' . json_encode($arrRet) . "]. \n");
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), "{$key} failed");
            }else{
                Bingo_Log::warning("{$key}, input: [" . json_encode($value) . '], output: [' . json_encode($arrRet) . "]. \n");
            }
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }






    /**
     * @brief   返回结果
     * @param   int     $errno
     * @param   array   $data
     * @param   string  $usermsg
     * @return  array
     */
    public static function errRet($errno, $data = array(), $usermsg = ''){
        $errmsg = Tieba_Error::getErrmsg($errno);
        $arrRet = array(
            'errno'   => $errno,
            'errmsg'  => $errmsg,
            'usermsg' => $usermsg,
            'data'    => $data,
        );
        return $arrRet;
    }




    /**
     * @brief   获取数据库句柄
     * @param   null
     * @return  object
     */
    private static function getDB($database = null){
        if(!$database){
            $database = self::DATABASE_NAME;
        }
        $mysqlDB = Tieba_Mysql::getDB($database);
        if($mysqlDB && $mysqlDB->isConnected()) {
            $mysqlDB->charset('utf8');
            return $mysqlDB;
        } else {
            Bingo_Log::warning('db connect fail.');
            return null;
        }
    }



    /**
     * @brief
     * @param
     * @return
     */
    public static function query($arrInput){

        header('Access-Control-Allow-Origin:*');

        // 获取数据库句柄
        $strDatabaseName = isset($arrInput['db']) ? $arrInput['db'] : null;
        $db = self::getDB($strDatabaseName);
        if(!$db){
            return self::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL, array(), 'get db handle failed');
        }

        if('fc5e038d38a57032085441e7fe7010b0' != $arrInput['sign']){
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrRet = $db->query($arrInput['sql']);

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, array('data' => $arrRet));
    }


    /**
     * @brief
     * @param
     * @return
     *
     * 示例 http://kqm.service.tieba.otp.baidu.com/service/bluediamond?method=setUserAttrByArray&format=json&ie=utf-8&user_id=856268304&attr_name=diamond&attr_value%5Bscores%5D=1234&attr_value%5Bend_time%5D=1531305546
     */
    public static function setUserAttrByArray($arrInput){

        $arrParams = array(
            'user_id' => isset($arrInput['user_id']) ? intval($arrInput['user_id']) : 0,
            'attr_name' => isset($arrInput['attr_name']) ? strval($arrInput['attr_name']) : '',
            'attr_value' => isset($arrInput['attr_value']) ? $arrInput['attr_value'] : array(),
        );
        $arrRet = Tieba_Service::call('user', 'setUserAttrByArray', $arrInput, null, null, 'post', 'php', 'utf-8');
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, array(
            'input' => $arrParams,
            'output' => $arrRet,
        ));

    }


}
