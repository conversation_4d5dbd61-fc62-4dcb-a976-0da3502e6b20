<?php 
/***************************************************************************
 *
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   Bluediamond.php
 * <AUTHOR>
 * @date   2016/04/14 14:09:16
 * @brief  blue diamond Service
 *
 **/

define("MODULE","Bluediamond_service");

class Service_Bluediamond{

	public static $service_ie = 'utf-8';
	private static $boolReloaded = false;

	/**
		*
		*	返回错误值
		* 	@param
		* 	@return
		*
	 */

	private static function _errRet($errno){
		return array(
			'errno' => $errno, 
			'errmsg' => Tieba_Error::getErrmsg($errno),
		);
	}

	/**
		*
		*	获得函数配置
		* 	@param
		* 	@return
		*
	 */
	private static function _getMethodsArr(){
		$strCacheFile = self::_getCacheFile();
		if(file_exists($strCacheFile)){
			include ($strCacheFile);
			return $methods_to_subservices;
		}
		
		return self::_reloadMethodsArr();
	}

	/**
		*
		*	自动加载函数
		* 	@param
		* 	@return
		*
	 */
	private static function _reloadMethodsArr(){
		$path = dirname(__FILE__);
		$dirs = scandir($path);
		//var_dump($dirs);	
		$methods_to_subservices = array();
		foreach ($dirs as $dir){
			if($dir === '.' || $dir === '..'){
				continue;
			}
			if(is_dir("$path/$dir")){
				Bingo_Log::debug("find sub dir [$dir]");
				$udir = ucfirst($dir);
				$classFile = "$path/$dir/$udir.php";
				$className = "Service_$udir"."_$udir";
				if(file_exists($classFile)){
					require_once ($classFile);
					if(class_exists($className)){
						$ref = new ReflectionClass($className);
						$methods = $ref->getMethods(ReflectionMethod::IS_PUBLIC);
						//var_dump($methods);
						foreach ($methods as $method){
							$methods_to_subservices[$method->name] = $dir;
						}
						
					}else{
						Bingo_Log::warning("class name not exist [$className]");
						continue;
					}
					
				}else{
					Bingo_Log::warning("invalid dir found [$dir]");
					continue;
				}			
			}
			
			
		}
		
		if(count($methods_to_subservices) == 0){
			Bingo_Log::warning("no valid method found. something wrong?");
		}
		$str = var_export ($methods_to_subservices,true);
		$str = "<?php\n\$methods_to_subservices = \n".$str.";\n?>";
		$final_file = self::_getCacheFile();
		$tmp_class_file = $final_file.".bak".rand();
		file_put_contents ($tmp_class_file,$str);
		
		//以下将临时文件修改为实际文件
		if (file_exists($tmp_class_file)) {
			rename ($tmp_class_file,$final_file);
		}
		if (file_exists ($tmp_class_file)) {
			unlink ($tmp_class_file);
		}
		
		self::$boolReloaded = true;
		return $methods_to_subservices;
	}
	/**
		*
		*	获得cache文件
		* 	@param
		* 	@return
		*
	 */
	private static function _getCacheFile(){
		return dirname(__FILE__).'/methods.php';
	}

	/**
		*
		*	调用函数
		* 	@param
		* 	@return
		*
	 */
	public static function call($name, $arguments){
		
		$methods_to_subservices = self::_getMethodsArr();
		if(!array_key_exists($name, $methods_to_subservices)){
			if(self::$boolReloaded){
				Bingo_Log::warning("methods call not found in service.[$name]");
				return self::_errRet(Tieba_Errcode::ERR_METHOD_NOT_FOUND);					
			}else{
				$methods_to_subservices = self::_reloadMethodsArr();
				if(!array_key_exists($name, $methods_to_subservices)){
					Bingo_Log::warning("methods call not found in service.[$name]");
					return self::_errRet(Tieba_Errcode::ERR_METHOD_NOT_FOUND);			
				}
			}
		}
		$subService = ucfirst(strtolower($methods_to_subservices[$name]));
		$subServiceFile = dirname(__FILE__)."/".strtolower($subService)."/$subService.php";
		$subServiceClass = "Service_$subService"."_$subService";
		
		if(!file_exists($subServiceFile)){
			Bingo_Log::warning("file call not found in service.[$subServiceFile]");
			return self::_errRet(Tieba_Errcode::ERR_FILE_NOT_FOUND);			
		}
		require_once ($subServiceFile);
		
		if(method_exists($subServiceClass, 'preCall')){
			call_user_func_array(array($subServiceClass, 'preCall'),array($arguments));
		}
		$res = call_user_func_array(array($subServiceClass, $name),array($arguments));
		
		if(method_exists($subServiceClass, 'postCall')){
			call_user_func_array(array($subServiceClass, 'postCall'),array($arguments));
		}
		if(isset($arguments['nmq']) && 1 == $arguments['nmq'] && 0 != $res['errno'] ){
			Bingo_Log::warning('pzb.'.print_r($res,true));
			return header('','',302);
		}
		if($res){
			//$jsRes = Bingo_String::array2json($res);
			return  $res;
		}else{
			Bingo_Log::warning("call user func failed. [$subServiceClass::$name] .");
			if(!self::$boolReloaded){
				self::_reloadMethodsArr();
			}
			return self::_errRet(Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL);
		}
				
	} 

	/**
		*
		*	获得编码
		* 	@param
		* 	@return
		*
	 */
	public static function getIE(){
		return self::$service_ie;
	}


	/**
	 *	添加蓝钻(重构后的版本)
	 * 	@param   array  $arrInput
	 * 	@return  array
	 */
	public static function addInnerDiamondFromPoolV2($arrInput){
		return Service_Deal_DealV2::addInnerDiamondFromPoolV2($arrInput);
	}


	/**
	 *	减少蓝钻(重构后的版本)
	 * 	@param   array  $arrInput
	 * 	@return  array
	 */
	public static function reduceInnerDiamondFromPoolV2($arrInput){
		return Service_Deal_DealV2::reduceInnerDiamondFromPoolV2($arrInput);
	}



	/**
	 * @brief   订单：添加蓝钻
	 * @param   array  $arrInput  输入
	 * @return  array
	 */
	public static function orderOfAddDiamond($arrInput){
		return Service_Deal_Order::orderOfAddDiamond($arrInput);
	}


	/**
	 * @brief   订单：减少蓝钻
	 * @param   array  $arrInput  输入
	 * @return  array
	 */
	public static function orderOfReduceDiamond($arrInput){
		return Service_Deal_Order::orderOfReduceDiamond($arrInput);
	}


	/**
	 * @brief   支付：添加蓝钻
	 * @param   array  $arrInput  输入
	 * @return  array
	 */
	public static function payOfAddDiamond($arrInput){
		return Service_Deal_Pay::payOfAddDiamond($arrInput);
	}



	/**
	 * @brief   支付：减少蓝钻
	 * @param   array  $arrInput  输入
	 * @return  array
	 */
	public static function payOfReduceDiamond($arrInput){
		return Service_Deal_Pay::payOfReduceDiamond($arrInput);
	}



	/**
	 * @brief   流转蓝钻
	 * @param   array  $arrInput
	 * @return  array
	 */
	public static function trade($arrInput){
		return Service_Deal_Trade::trade($arrInput);
	}

	/**
	 * @brief
	 * @param   array  $arrInput
	 * @return  array
	 */
	public static function query($arrInput){
		return Service_Deal_Trade::query($arrInput);
	}


	/**
	 * @brief   查询scene_pool表
	 * @param   array  $arrInput
	 * @return  array
	 */
	public static function selectScenePool($arrInput){
		return Service_Scene_ScenePool::select($arrInput);
	}


	/**
	 * @brief   通过自增id更新scene_pool表
	 * @param   array  $arrInput
	 * @return  array
	 */
	public static function updateScenePoolById($arrInput){
		return Service_Scene_ScenePool::updateById($arrInput);
	}


	/**
	 * @brief   插入scene_pool表
	 * @param   array  $arrInput
	 * @return  array
	 */
	public static function insertScenePool($arrInput){
		return Service_Scene_ScenePool::insert($arrInput);
	}


	/**
	 * @brief   删除scene_pool表
	 * @param   array  $arrInput
	 * @return  array
	 */
	public static function deleteScenePool($arrInput){
		return Service_Scene_ScenePool::delete($arrInput);
	}





	/**
	 * @brief   查询user_scores表
	 * @param   array  $arrInput
	 * @return  array
	 */
	public static function selectUserScores($arrInput){
		return Service_User_User::select($arrInput);
	}


	/**
	 * @brief   通过自增id更新user_scores表
	 * @param   array  $arrInput
	 * @return  array
	 */
	public static function updateUserScoresById($arrInput){
		return Service_User_User::updateById($arrInput);
	}


	/**
	 * @brief   插入user_scores表
	 * @param   array  $arrInput
	 * @return  array
	 */
	public static function insertUserScores($arrInput){
		return Service_User_User::insert($arrInput);
	}


	/**
	 * @brief   删除user_scores表
	 * @param   array  $arrInput
	 * @return  array
	 */
	public static function deleteUserScores($arrInput){
		return Service_User_User::delete($arrInput);
	}



	/**
	 * @brief   查询blue_diamond_record
	 * @param   array  $arrInput
	 * @return  array
	 */
	public static function selectBlueDiamondRecord($arrInput){
		return Service_Record_BlueDiamondRecord::select($arrInput);
	}



	/**
	 * @brief   查询user_record
	 * @param   array  $arrInput
	 * @return  array
	 */
	public static function selectUserRecord($arrInput){
		return Service_Record_UserRecord::select($arrInput);
	}


	/**
	 * @brief   查询user_record
	 * @param   array  $arrInput
	 * @return  array
	 */
	public static function selectSceneRecord($arrInput){
		return Service_Record_SceneRecord::select($arrInput);
	}



	/**
	 * @brief
	 * @param
	 * @return
	 */
	public static function setUserAttrByArray($arrInput){
		return Service_Deal_Trade::setUserAttrByArray($arrInput);
	}


	/**
	 * @brief   查询scene_info
	 * @param   array  $arrInput
	 * @return  array
	 */
	public static function selectSceneInfo($arrInput){
		return Service_Scene_SceneInfo::select($arrInput);
	}


	/**
	 * @brief   更新scene_info
	 * @param   array  $arrInput
	 * @return  array
	 */
	public static function updateSceneInfoById($arrInput){
		return Service_Scene_SceneInfo::updateById($arrInput);
	}


	/**
	 * @brief   修改池子信息(只用于amis后台)
	 * @param   array  $arrInput
	 * @return  array
	 */
	public static function updateSceneForAmis($arrInput){
		return Service_Scene_ScenePool::updateSceneForAmis($arrInput);
	}
}





