<?php
/**
 * Created by PhpStorm.
 * User: pang<PERSON><PERSON><PERSON>
 * Date: 2019/8/13
 * Time: 下午2:58
 */

class Util_Service_Wap
{
    public static function getTplUser($objRequest)
    {
        $arrUser = array(
            'id'           => $objRequest->getCommonAttr('user_id'),           //用户id
            'name'         => $objRequest->getCommonAttr('user_name'),      //用户名称
            'name_show'    => $objRequest->getPrivateAttr('nick_name'),      //用户名称(认证用户则为昵称)
            'name_link'    => '',                              //用户i贴吧链接key(/i/name_link/all)
            'is_login'     => $objRequest->getCommonAttr('login'),       //是否已登录
            'no_un'        => $objRequest->getCommonAttr('no_un'),
            'is_verify'    => false,
            'portrait'     => 'http://tb.himg.baidu.com/sys/portrait/item/' . $objRequest->getCommonAttr('portrait') . '.jpg',//用户头像url
            'uid'          => $objRequest->getCommonAttr('user_id'),
        );
        if (empty($arrUser['name_show'])) {
            $arrUser['name_show'] = $arrUser['name'];
        }
        $arrCookie = $objRequest->getCommonAttr('cookie');
        $strBduss = $arrCookie['BDUSS'];
        if ($strBduss) {
            $arrUser['sid'] = substr($strBduss, 0, 64);//登陆用户的注销请求需要增加sid参数，防止csrf漏洞
        }
        return $arrUser;
    }
}