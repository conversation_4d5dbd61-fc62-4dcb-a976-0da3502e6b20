<?php
/**
 * Created by PhpStorm.
 * User: pangzengyao
 * Date: 2019/7/30
 * Time: 下午7:18
 */

class Util_Errno {
    const WAP_PB_THREAD_NOT_EXIST = 3000;   // 访问的主题不存在
    // 限制访问2017年以前贴子
    const ERR_POST_OLD_REFUSE_VISIT = 3001; // 贴子暂时无法访问
    const USER_NOT_LOGIN = 20000;//用户未登录
    const PARAM_NOT_ENOUGH = 20001;
    const THREAD_STORE_ERROR = 20002;
    const CLEAR_MSG_ERROR = 20002;
    const ERR_FAIL_USERINFO = 20003;// 获取用户信息失败
    const INTERNAL_ERROR = 28888;//内部错误
    const VIEW_INTERNAL_ERROR = 28890;//内部出错，主要是调用mos层浏览接口出现问题
    const COMMIT_INTERNAL_ERROR = 28891;//内部错误，主要是调用mos层提交接口出现错误

    //FRS error no
    const ERR_FRS_CLUB_FORBIDDEN  = 2000;       // 无法访问俱乐�?   const ERR_FRS_FORUM_NOT_EXIST = 2001;       // 吧不存在
    const ERR_FRS_FORUM_NOT_EXIST = 2001;       // 吧不存在
    const ERR_FRS_HAS_NO_GOOD_THREADS = 2002;   // 没有精品贴
    const ERR_FRS_UNIVERSITY_FORBIDDEN = 2003;  // 大学类贴吧
    const ERR_FRS_PAGE_NOT_EXIST = 2004;        // 吧不存在
    const ERR_FRS_FORUM_NO_THREAD = 2005;       // 无贴子的吧
    const ERR_FRS_FORUM_FORBIDDEN = 2006;       // 吧被封禁
    const FRS_FORUM_READABLE = 2007;            // 吧可读
    const ERR_FORUM_NOT_READABLE = 2008;        // 吧不可读

    //检索页
    const ERR_SEARCH_NO_DATA = 4008;   //没有检索数据


    //favolike error no
    const ERR_LIKE_FORBIDDEN = 4000;
    const ERR_LIKE_BLACK     = 4001;
    const ERR_LIKE_HAS_LIKED  = 4003;

    const PHP_ERROR = 4;

    const ERR_PUB_TITLE_IS_NULL = 20;   // 标题为空
    const ERR_PUB_CONTENT_IS_NULL = 10; // 内容为空
    const ERR_PUB_VCODE_ERROR = 40;     // 输入验证码错误
    const ERR_INPUT_VCODE_ERROR = 37;   //验证码超出使用次数阀值
    const ERR_PUB_VCODE_TIME_OUT = 38;  //验证码超时
    const ERR_PUB_REQUERY_VCODE = 19;       // 需要输入验证码

    const ERR_PUB_INPUT_ERROR = 15;     // 提交输入错误
    const MSG_PUB_COMMIT_FORUM_NAME_IS_WRONG = 17;  //发贴提交，吧名错误
    const PUB_POST_TITLE_LEN_TOO_LONG = 4008; //标题过长
    const PUB_POST_CONTENT_LEN_TOO_LONG = 4009;//内容过长
    const ERR_PUB_COMMIT_CONTENT_CHECK_FIRST = 9;
    const ERR_USER_ID_IS_FORBIDDEN = 12;
    const ERR_USER_IP_IS_FORBIDDEN = 13;
    const ERR_PUB_COMMIT_IS_REPEATED = 14;
    const ERR_PUB_COMMIT_INCLUDES_WRONG_INFO = 15;
    const ERR_PUB_SMILES_IS_TOO_MANY = 814;
    const ERR_PUB_COMMIT_CEATE_CHECK_FIRST = 23;
    const ERR_PUB_OPERATION_IS_TOO_FREQUENT = 42;//35
    const ERR_PUB_AT_VCODE = 35;//@召唤术过多使用
    const ERR_PUB_RESPONSE_VCODE = 10101;
    const ERR_POST_LIMIT_IN_THIS_FORUM = 900;//静止蛙
    const ERR_NEED_BAND_PHONE_NUMBER = 4010;//需要绑定手机密码
    const ERR_NEED_CHANGE_PASSWORD = 4011;//需要修改密码
    const ERR_ANTI_ACTSCTRL_REJECT = 901;//该吧或者该贴仅限特定用户可以发贴

    const ERR_PUB_PHONE_GBLOCK_ERROR = 3250012; //ueg用户短信验证 add by gaoweizhen01
    const ERR_POST_CT_REPLY_BY_AUTHOR_FOLLOW_ONLY = 238009; // 只有楼主关注的人才能发回复
    const ERR_POST_CT_REPLY_BY_FOLLOW_AUTHOR_ONLY = 238010; // 只有关注楼主的人才能发回复

    const UPlOAD_PICTURE_SUCESS = 10001;
    const UPlOAD_PICTURE_NET_ERROR = 10002;
    const UPlOAD_PICTURE_TOO_LARGE = 10003;
    const UPlOAD_PICTURE_TYPE_ERROR = 10004;
    const UPlOAD_PICTURE_NOT_LOGIN = 10005;
    const UPlOAD_PICTURE_TITLE_EMPTY = 10006;
    const UPlOAD_PICTURE_RPC_ERROR = 10007;

    const INTERNAL_SIGN_ERROR = 11000; //sign failed
    const ERR_TBS = 11001;             //itb_tbs is wrong
    const SIGN_SUCCESS = 0;
    const ACTS_CTRL_ERROR = 1007;
    const USER_IS_BLOCK =9; //用户被封禁
    const USER_IS_LOGOUT = 4;  //用户已退出登录
    const GET_GOODS_ERROR = 1001;
    const READ_MYSQL_ERROR = 1002;
    const SOMETHING_ERROR = 1003;
    const COMMIT_CHECK_INVALID = 1012;
    const ERR_COMMIT_TOUSU = 41;  //投诉

    const ONLY_LZ_NOT_LOGIN = 12000;
    const PB_NOT_LOGIN      = 12001;

    //creat forum err
    const FNAME_IS_EMPTY    = 50000;
    const FORUM_HAS_EXISTED = 50001;
    const FNAME_IS_INVALID  = 50002;
    const FNAME_INCLUDE_INVALID_WORD = 50003;

    const MSG_COMMIT_SET_TOP_SUCCESS = 60;
    const MSG_COMMIT_UNSET_TOP_SUCCESS = 61;
    const ERR_COMMIT_TOP = 62;
    const MSG_COMMIT_SET_GOOD_SUCCESS = 63;
    const MSG_COMMIT_UNSET_GOOD_SUCCESS = 64;
    const ERR_COMMIT_GOOD = 65;
    const MSG_COMMIT_FILTER_USER_SUCCESS = 66;
    const ERR_COMMIT_FILTER_USER= 67;
    const MSG_COMMIT_FILTER_IP_SUCCESS = 68;
    const ERR_COMMIT_FILTER_IP= 69;
    const ERR_GET_GOODCATES = 70;
    const ERR_DEL_THREAD = 71;

    // 明星垂直化,粉丝编号相关
    const FANS_INTERNAL_ERROR   = 28889;        // 调用接口错误
    const FANS_NO_OPEN          = 120000;       // 未开启粉丝编号功能
    const FANS_NOT_BEGIN        = 120001;       // 领取活动未开始
    const FANS_GET_ALREADY      = 120002;       // 已领取

    const PHOTO_INTERNAL_ERROR  = 28887;        //获取图片list内部错误


    //吧目录
    const CATALOG_LEVEL1_LIST_NOT_EXIST=40001; //一级吧目录列表不存在
    const CATALOG_LEVEL2_LIST_NOT_EXIST=40002; //二级吧目录列表不存在
    const CATALOG_DETAIL_LIST_NOT_EXIST=40003; //吧目录详情页列表不存在

    //实名制
    const ERR_CLIENT_USER_HAS_NO_REALNAME = 1990055; //未实名认证
}