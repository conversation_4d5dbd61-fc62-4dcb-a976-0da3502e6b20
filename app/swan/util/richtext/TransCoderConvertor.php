<?php
/**
 * Created by PhpStorm.
 * User: pangzengyao
 * Date: 2019/7/31
 * Time: 下午5:35
 */

class Util_Richtext_TransCoderConvertor
{
    public static $TRANSCODER_IMG_BASE_URL = 'http://gss3.bdstatic.com/84oSdTum2Q5BphGlnYG';
    public static $URL = "/timg?wapp&amp;quality=%d&amp;size=%s&amp;cut_x=%d&amp;cut_w=%d&amp;cut_y=%d&amp;cut_h=%d&amp;sec=%d&amp;di=%s&amp;wh_rate=null&amp;src=%s";
    public static $notEscapeURl = "/timg?mowebapp&quality=%d&size=%s&cut_x=%d&cut_w=%d&cut_y=%d&cut_h=%d&sec=%d&di=%s&wh_rate=null&src=%s";
    public static $URL_Light = "/timg?wapp&amp;quality=%d&amp;size=%s&amp;sec=%d&amp;di=%s&amp;wh_rate=null&amp;src=%s";
    public static $notEscapeURl_Light = "/timg?mowebapp&quality=%d&size=%s&sec=%d&di=%s&wh_rate=null&src=%s";

    /**
     * @param $baseURL
     */
    public static function setImgBaseURL($baseURL)
    {
        self::$TRANSCODER_IMG_BASE_URL = $baseURL;
    }

    /**
     * @param $strUrl
     * @param $intPageType
     * @return string
     */
    public static function getGateUrl($strUrl, $intPageType)
    {
        $intTm  = Bingo_Timer::getNowTime();
        $strMd5 = self::signURL($strUrl, $intTm);
        $strTpl = "http://gate.baidu.com/tc?t=wapp&amp;ssid=0&amp;from=0&amp;bd_page_type=%d&amp;uid=&amp;pu=&amp;sec=%d&amp;di=%s&amp;src=%s";
        return sprintf($strTpl, $intPageType, $intTm, $strMd5, rawurlencode($strUrl));
    }

    /**
     * 获取压缩图片地址
     * @modified by xiashanshan 20171023
     * 目前看除头像外的其他图片访问都会迁移到hiphotos服务
     * @param string $baseUrl 压缩图片url的规则
     * @param string $imgUrl 源图片url
     * @param int $width 压缩后图片的最大宽度
     * @param int $height 压缩后图片的最大高度
     * @param int $quality 压缩后图片的质量
     * @param int $cutX 裁切图片的X坐标
     * @param int $cutW 裁切图片的宽度
     * @param int $cutY 裁切图片的Y坐标
     * @param int $cutH 裁切图片的高度
     * @return
     */
    public static function getImageUrl($url, $width, $height = 0, $quality = 45, $notEscape = 0, $cutX = 0, $cutW = 0, $cutY = 0, $cutH = 0)
    {
        // 正文内容图片
        if (strpos($url, 'imgsrc.baidu.com') || strpos($url, 'imgsa.baidu.com')) {
            // hiphotos图片服务的在线处理尺寸大小限制为980
            $width = $width > 980 ? 980 : $width;
            $str   = self::getHiphotosImageUrl($url, $width, $height, $quality, $notEscape);
            return $str;
        }
        $tm     = 1369815402;//Bingo_Timer::getNowTime();
        $md5    = self::signURL($url, $tm);
        $height = intval($height);
        $width  = intval($width);
        if ($height == 0) {
            $size = 'w' . $width;
        } else {
            $size = 'b' . $width . '_' . $height;
        }
        if (0 == $notEscape) {
            $str = sprintf(self::$TRANSCODER_IMG_BASE_URL . self::$URL, $quality, $size, $cutX, $cutW, $cutY, $cutH, $tm, $md5, rawurlencode($url));
        } else {//frsabs frs页摘要
            $str = sprintf(self::$TRANSCODER_IMG_BASE_URL . self::$notEscapeURl, $quality, $size, $cutX, $cutW, $cutY, $cutH, $tm, $md5, rawurlencode($url));
        }
        return $str;
    }

    /**
     * @param  url
     * @param  width
     * @param  height
     * @param  quality
     * @return [type]
     */
    public static function getHiphotosImageUrl($url, $width, $height, $quality, $notEscape = 0)
    {
        $picSign = self::getPicSignFromUrl($url);
        //url spec gif
        $picSpecGif = 'g=0';
        //url spec size
        $picSpecWH = 'w=' . $width;
        // quality
        $picSpecQuality = 'q=' . $quality;
        //url params
        $picSpec = $picSpecWH . ';' . $picSpecQuality . ';' . $picSpecGif;
        $str     = self::picSign2Url($picSign, $picSpec);
        if (0 == $notEscape) {
            $str .= '?&amp;src=' . urlencode($url);
        } else {
            $str .= '?&src=' . urlencode($url);
        }
        return $str;
    }

    /**
     * @param $url
     * @param int $screenWidth
     * @param int $quality
     * @param int $notEscape
     * @return null|string|string[]
     */
    public static function getLightappImageUrl($url, $screenWidth = 720, $quality = 100, $notEscape = 0)
    {
        $screenWidth = intval($screenWidth);
        //url sign
        $picSign = self::getPicSignFromUrl($url);
        //url spec gif
        $picSpecGif = 'g=' . 0;
        //url spec size
        if ($screenWidth <= 480) {
            //$picSpecWH = 'whfpf=' . 101 . ',' . 101 . ',' . 40;
            $picSpecWH = 'whfpf=' . 106 . ',' . 106 . ',' . 40;
        } elseif ($screenWidth <= 720) {
            $picSpecWH = 'whfpf=' . 210 . ',' . 210 . ',' . 40;
        } else {
            //$picSpecWH = 'whfpf=' . 306 . ',' . 306 . ',' . 40;
            $picSpecWH = 'whfpf=' . 315 . ',' . 315 . ',' . 40;
        }
        $picSpecQuality = 'q=100';
        //url params
        $picSpec = $picSpecWH . ';' . $picSpecQuality . ';' . $picSpecGif;
        $str     = self::picSign2Url($picSign, $picSpec);
        return $str;
    }

    /**
     * frs imgcdn get sign form "http://imgsrc.baidu.com/forum/pic/item/c91373f082025aafd815170ffaedab64024f1aca.jpg
     * @param $strUrl string
     * @return $picSign string
     */
    public static function getPicSignFromUrl($strUrl)
    {
        $arrTmp     = explode('/', $strUrl);
        $intLength  = count($arrTmp);
        $strPicName = $arrTmp[$intLength - 1]; // c91373f082025aafd815170ffaedab64024f1aca.jpg
        $arrTmp2    = array_filter(explode('.', $strPicName));
        $picSign    = '';
        if (isset($arrTmp2[0])) {
            $picSign = $arrTmp2[0];
        }
        return $picSign;
    }

    /**
     * @param $picSign string
     * @return $picSign string
     */
    public static function picSign2Url($picSign, $picSpec)
    {
        $arrOutput = Space_Urlcrypt::decodePicUrlCrypt($picSign);
        if ($arrOutput === false || !isset($arrOutput[0])) {
            Bingo_Log::warning("decode pic id error! pic_sign:" . $picSign . " pic_spec:" . $picSpec);
            return '';
        }
        $foreignKey = $arrOutput[0];
        $picId      = $arrOutput[1];
        $obj        = new Bd_Pic_UrlCrypt();
        $arrIput    = array(
            "pic_id"       => $picId,
            'foreign_key'  => $foreignKey,
            "product_name" => "forum",
            "pic_spec"     => $picSpec,//图片处理特征，abpic表示小图，其他参加文档或wiki
        );
        $arrReqs[]  = $arrIput;
        $ret        = $obj->BatPid2Url($arrReqs);
        $strUrl     = '';
        if (isset($ret['resps'][0])) {
            $strUrl = $ret['resps'][0];
            //域名统一 *.hiphotos.baidu.com 统一转换成c.hiphotos.baidu.com
            $strUrl = preg_replace('/http:\/\/[a-z].hiphotos/', 'http://c.hiphotos', $strUrl);
        }
        return $strUrl;
    }

    /**
     * @param $url
     * @param $tm
     * @param string $key
     * @return string
     */
    private static function signURL($url, $tm, $key = 'wisetimgkey_noexpire_3f60e7362b8c23871c7564327a31d9d7')
    {
        // 加密 'wisetimgkey' + 时间 + url
        $md5 = md5($key . $tm . $url);
        return $md5;
    }
}