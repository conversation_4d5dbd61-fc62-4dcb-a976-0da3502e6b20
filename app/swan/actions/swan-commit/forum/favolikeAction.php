<?php
/**
 * Created by PhpStorm.
 * User: pang<PERSON><PERSON>ao
 * Date: 2019/8/12
 * Time: 上午11:38
 */

class favolikeAction extends Base_Action
{
    private $_strTbs = '';
    private $_strkw = '';
    private $_intfid = 0;
    private $_arrOut = array();
    private $_intUserId = 0;

    private $_intErrno = 0;
    private $_strErrMsg = 'success';

    /**
     * @return array
     */
    public function _getPrivateInfo()
    {
        $arrPrivateInfo = array(
            'check_real_name' => true,
            'check_login'     => true,

            'tbs' => strval($this->_getInput('tbs', '')),
            'kw'  => strval($this->_getInput('kw', '')),
            'fid' => intval($this->_getInput('fid', 0)),
        );

        return $arrPrivateInfo;
    }

    /**
     * @return bool
     */
    public function _checkPrivate()
    {
        $this->_intUserId = $this->_objRequest->getCommonAttr('user_id');
        $this->_strTbs    = $this->_objRequest->getPrivateAttr('tbs');

        // tbs校验
        if ($this->_strTbs == '' || !Tieba_Tbs::check($this->_strTbs, true)) {
            Bingo_Log::warning("check Tbs failed!tbs:" . $this->_strTbs);
            $this->_log();
            $this->_error(Util_Errno::PARAM_NOT_ENOUGH, Util_Error::PARAM_NOT_ENOUGH);
            return false;
        }
        $this->_strTbs = Tieba_Tbs::gene(true);

        // 获取吧名
        $this->_strkw  = $this->_objRequest->getPrivateAttr('kw');
        $this->_strkw  = str_replace(chr(194) . chr(160), " ", $this->_strkw);
        $this->_strkw  = Util_Service_Encode::convertGBKToUTF8($this->_strkw);
        $this->_strkw  = trim($this->_strkw);
        $this->_intfid = $this->_objRequest->getPrivateAttr('fid');

        if (empty($this->_strkw) || $this->_intfid == 0) {
            Bingo_Log::warning('kw is null');
            $this->_log();
            $this->_error(Util_Errno::PARAM_NOT_ENOUGH, Util_Error::PARAM_NOT_ENOUGH);
            return false;
        } else if (24981790 == $this->_intfid || 'me0407' == $this->_strkw) {
            Bingo_Log::warning('kw or fid is invaild!');
            $errno  = Tieba_Errcode::ERR_FORUM_LIKE_INVALID;
            $errmsg = Bingo_Encode::convert(Tieba_Error::getUserMsg($this->_intErrno), Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
            $this->_error($errno, $errmsg);
            $this->_log();
            return false;
        }

        return true;
    }


    public function _execute()
    {

        $this->_process();
        $this->_buildResp();

        return true;
    }


    private function _process()
    {
        $inputArray      = array(
            'uid' => $this->_intUserId,
            'kw'  => $this->_strkw,
            'tbs' => $this->_strTbs,
            'fid' => $this->_intfid,
        );
        $this->_arrOut   = Tbapi_Core_Server::mocall('forum', 'like_commit', $inputArray);
        $this->_intErrno = $this->_arrOut['error']['errno'];
        if ($this->_intErrno != 0) {
            Bingo_Log::warning("call like commit failed! input:" . serialize($inputArray) . " output:" . serialize($this->_arrOut));
            $this->_setError($this->_intErrno);
            return false;
        }

        return true;
    }

    private function _buildResp()
    {
        //普通版forward frs
        $arrResult = array(
            'no'    => $this->_intErrno,
            'error' => $this->_strErrMsg,
            'data'  => $this->_arrOut['info']['data'],
        );
        $this->_log();
        $this->_objResponse->setOutData($arrResult);
        return true;
    }

    private function _setError($intErrno)
    {
        switch ($intErrno) {
            case Tieba_Errcode::ERR_ANTI_ID_BLOCKED:
            case Tieba_Errcode::ERR_MO_FORUM_UID_PRISON:
            case Tieba_Errcode::ERR_MO_FORUM_UIP_PRISON:
                $this->_intErrno  = Util_Errno::ERR_LIKE_FORBIDDEN;
                $this->_strErrMsg = Util_Error::LIKE_FORBIDDEN_ERROR;
                return false;
            case Tieba_Errcode::ERR_MO_FORUM_USER_IS_BLACK:
                $this->_intErrno  = Util_Errno::ERR_LIKE_BLACK;
                $this->_strErrMsg = Util_Error::LIKE_BLACK_ERROR;
                return false;
            case Tieba_Errcode::ERR_MO_FORUM_USER_HAS_LIKE_FORUM:
                $this->_intErrno  = Util_Errno::ERR_LIKE_HAS_LIKED;
                $this->_strErrMsg = Util_Error::LIKE_HAS_LIKED_ERROR;
                break;
            case Tieba_Errcode::ERR_PHONE_GBLOCK_ERROR:
                $this->_intErrno  = Tieba_Errcode::ERR_PHONE_GBLOCK_ERROR;
                $this->_strErrMsg = '您的帐号有被盗风险，请点击设置中的帐号状态按钮进行验证';
                break;
            default:
                $this->_intErrno  = Util_Errno::INTERNAL_ERROR;
                $this->_strErrMsg = Util_Error::LIKE_INTERNAL_ERROR;
                return false;
        }
        return false;
    }

    private function _log()
    {
        Tieba_Stlog::addNode('ispv', 0);
        Tieba_Stlog::addNode('favolike', 1);
        Tieba_Stlog::addNode('like_uid', $this->_intUserId);
        Tieba_Stlog::addNode('fid', $this->_intfid);
        //日志中统一加上is_repeat参数标识是否重复请求，在数据统计时is_repeat=1会被过滤掉 add by wangjin 20141217
        Tieba_Stlog::addNode('is_repeat', $this->_arrOut['is_repeat']);
        Tieba_Stlog::addNode('kw', $this->_strkw);
        Tieba_Stlog::addNode('err', $this->_intErrno);
    }
}
