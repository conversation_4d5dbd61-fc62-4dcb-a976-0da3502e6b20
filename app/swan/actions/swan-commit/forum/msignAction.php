<?php
/**
 * Created by PhpStorm.
 * User: pang<PERSON><PERSON><PERSON>
 * Date: 2019/8/15
 * Time: 上午11:21
 */

class msignAction extends Base_Action
{
    const MULTI_KEY = 'msign_multi_key';
    const MULTI_SIGN_KEY = 'msign_multi_fid_key';
    const TERM_TYPE = 4;
    const MEM_TYPE = 11;
    const TIMEOUT_THRESHOLD = 1;
    const SIGN_NOTICE = '零点到一点为签到高峰期，一键签到失败机率较大，请错开高峰期再来签到！';
    const CLIENT_TYPE = 'APP';
    private $_intIsOpen;
    private $_intSignTimeLimit;
    private $_intSignLevel;
    private $_intMaxSignNum;
    private $_intSignedNum;
    private $_intUserId;
    private $_intUserIp;
    private $_arrForumId;
    private $_strTbs;
    private $_signFid;
    private $_signEfid;
    private $_signPage;
    private $_isTestUser = false;    //后端获取吧id状态，由于修改地方很多，故没有下掉。
    private $_isLittleTest = false;     //真正的小流量状态，以后小流量都可以用它。
    private $_intClientType; // 客户端类型
    private $_strClientVersion; // 客户端版本号
    private $_strForumId = '';

    private $_objMulti;

    private $_arrSignResult;
    private $_arrOutData;
    private $_isMem;
    private static $_needMemberStragegy;
    private static $_newNeedMemberStragegy;
    private static $_isSuperMember;
    private $_vipStatus;
    private $_vipEndtime;
    private $_intRealSignMaxNum = 200;
    private $_intVipLevel = 1;
    private $_arrSignMaxNum = array(
        '1' => 200,
        '2' => 250,
        '3' => 300,
        '4' => 350,
        '5' => 400,
    );

    private $_blockAndAppealInfo = array();

    public function _getPrivateInfo()
    {
        return array(
            'check_login'     => true,
            'check_real_name' => true,
            'tbs'             => Bingo_Http_Request::get('tbs', ''),
            'forum_ids'       => Bingo_Http_Request::get('forum_ids', ''),
            'ispv'            => 0,
        );
    }

    public function _checkPrivate()
    {
        // this interface is not scientific, cannot use this function to check
        return true;
    }

    public function _execute()
    {
        Bingo_Timer::start('m_sign_in_exe');
        Bingo_Timer::start('m_sign_in_init');
        $this->_init();
        Bingo_Timer::end('m_sign_in_init');
        Bingo_Timer::start('m_sign_in_check');
        if (!Tieba_Tbs::check($this->_strTbs, true)) {
            $this->_arrOutData['error'] = $this->_getErrData(Tieba_Errcode::ERR_MO_POST_TBS_CHECK_FAIL);
            $this->_objResponse->setOutData($this->_arrOutData);
            return false;
        }
        if (!is_array($this->_arrForumId) || empty($this->_arrForumId)) {
            $this->_arrOutData['error'] = $this->_getErrData(Tieba_Errcode::ERR_MO_PARAM_INVALID);
            $this->_objResponse->setOutData($this->_arrOutData);
            return false;
        }

        if (count($this->_arrForumId) > $this->_intMaxSignNum) {
            $this->_arrOutData['error'] = $this->_getErrData(Tieba_Errcode::ERR_MO_PARAM_INVALID);
            $this->_objResponse->setOutData($this->_arrOutData);
            return false;
        }
        if ($this->_intIsOpen !== 1) {
            return false;
        }
        if (!$this->_checkTimeLimit()) {
            $this->_arrOutData['error'] = $this->_getErrData(Tieba_Errcode::ERR_MO_FORUM_SIGN_TOO_OFTEN);
            $this->_objResponse->setOutData($this->_arrOutData);
            return false;
        }
        if (Molib_Util_Version::compare('9.7', $this->_strClientVersion) >= 0) {
            $error = $this->_getAntiState();
            if ($error !== Tieba_Errcode::ERR_SUCCESS) {
                if ($error == Tieba_Errcode::ERR_CLIENT_BLOCK_IS_APPEALED_ERROR) {
                    $errmsg = empty($this->_blockAndAppealInfo['appeal_msg']) ? Molib_Client_Error::getErrMsg($error) : $this->_blockAndAppealInfo['appeal_msg'];
                    $this->_error($error, $errmsg);
                    $arrRes['error'] = array(
                        'errno'  => $error,
                        'errmsg' => $errmsg,
                    );
                    $this->_objResponse->setOutData($arrRes);
                } else {
                    $this->_error($error, Molib_Client_Error::getErrMsg($error));
                }
                return false;

            }
        }
        Bingo_Timer::end('m_sign_in_check');
        Bingo_Timer::start('check_phase');
        Bingo_Timer::start('m_sign_in_callserver');
        if (!$this->_callServices()) {
            $this->_arrOutData['error'] = $this->_getErrData(Tieba_Errcode::ERR_MO_INTERNAL_ERROR);
            $this->_objResponse->setOutData($this->_arrOutData);
            $this->_error(Tieba_Errcode::ERR_MO_INTERNAL_ERROR);
            Molib_Util_Log::uiFatal($this->_objRequest, $this->_objResponse);
            return false;
        }
        Bingo_Timer::end('m_sign_in_callserver');
        Bingo_Timer::end('check_phase');
        Bingo_Timer::start('m_sign_in_rest');
        // check actsctrl
        $arrAntiInfo = $this->_objMulti->getResult('anti:antiActsctrlQuery');
        if ($arrAntiInfo['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('check actsctrl failed');
            $this->_arrOutData['error'] = $this->_getErrData(Tieba_Errcode::ERR_MO_FORUM_SIGN_TOO_OFTEN);
            $this->_objResponse->setOutData($this->_arrOutData);
            return false;
        }
        // get forum info
        $arrForumInfo = $this->_objMulti->getResult('forum:mgetBtxInfo');
        if ($arrForumInfo['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            $this->_arrOutData['error'] = $this->_getErrData(Tieba_Errcode::ERR_MO_FORUM_GET_FID_ERROR);
            $this->_objResponse->setOutData($this->_arrOutData);
            return false;
        }
        // filter signed forums
        $arrSignInfo = $this->_objMulti->getResult('sign:getUserSignForums');
        $arrForumId  = array();
        foreach ($this->_arrForumId as $intForumId) {
            if ($arrSignInfo['arr_user_info'][$intForumId]['is_sign_in'] === 0) {
                $arrForumId[] = $intForumId;
            }
        }
        /*******to do new*********/
        if ($this->_isTestUser === true) {
            if (empty($arrForumId) && !empty($this->_arrForumId)) {
                Bingo_Timer::end('m_sign_in_exe');
                return true;
            }
        }
        /********************/
        // if empty
        if (empty($arrForumId)) {
            Bingo_Log::warning('no forum to be signed');
            $this->_arrOutData['error'] = $this->_getErrData(Tieba_Errcode::ERR_MO_FORUM_SIGN_TOO_OFTEN);
            $this->_objResponse->setOutData($this->_arrOutData);
            return false;
        }
        /*******to do  new*********/
        if ($this->_isTestUser === true) {
            //需要签到的吧id
            $this->_signFid = $arrForumId;
            /********************/
        }
        // filter forum that not reach certain level
        /**************old need*******************/
        if ($this->_isTestUser === false) {
            $arrLevelInfo = $this->_objMulti->getResult('perm:mgetUserLevel');
            if ($arrLevelInfo === false) {
                $this->_arrOutData['error'] = $this->_getErrData(Tieba_Errcode::ERR_MO_INTERNAL_ERROR);
                $this->_objResponse->setOutData($this->_arrOutData);
                $this->_error(Tieba_Errcode::ERR_MO_INTERNAL_ERROR);
                Molib_Util_Log::uiFatal($this->_objRequest, $this->_objResponse);
                return false;
            }
            foreach ($arrLevelInfo['score_info'] as $arrLevel) {
                if ($arrLevel['level_id'] < $this->_intSignLevel) {
                    Bingo_Log::warning('found forum under sign level. level_id:' . $arrLevel['level_id'] . '  intSignLevel:' . $this->_intSignLevel);
                    $this->_arrOutData['error'] = $this->_getErrData(Tieba_Errcode::ERR_MO_PARAM_INVALID);
                    $this->_objResponse->setOutData($this->_arrOutData);
                    $this->_error(Tieba_Errcode::ERR_MO_INTERNAL_ERROR);
                    return false;
                }
            }
        }
        /*********************************/
        Bingo_Timer::end('m_sign_in_rest');

        Bingo_Timer::start('m_sign_in');
        /*********to do*****************/
        if ($this->_isTestUser === true) {
            if (!$this->_callMsignIn()) {
                Bingo_Log::warning('signIn is fail');
                return false;
            }
        } else {
            $arrResult = $this->_msign($arrForumId);
        }
        Bingo_Timer::end('m_sign_in');
        if ($this->_isTestUser === true) {
            $arrAllSignResult = array();
            for ($i = 0; $i < $this->_signPage; $i++) {
                $arrResult             = $this->_objMulti->getResult("sign:msignIn$i");
                $arrAllRet['errno']    = $arrResult['errno'];
                $arrAllRet['errmsg']   = $arrResult['errmsg'];
                $arrAllRet['fail_num'] += $arrResult['fail_num'];
                $arrAllRet['succ_num'] += $arrResult['succ_num'];
                if (!empty($arrResult['fail_info'])) {
                    foreach ($arrResult['fail_info'] as $key => $value) {
                        $arrAllRet['fail_info'][$key] = $value;
                    }
                }
                if (!empty($arrResult['succ_info'])) {
                    foreach ($arrResult['succ_info'] as $key => $value) {
                        $arrAllRet['succ_info'][$key] = $value;
                        $arrSuccForumIds[]            = $key;
                    }
                }
                $arrAllRet['ie'] = $arrResult['ie'];
            }
            $arrResult = $arrAllRet;
        }

        //2016粉丝节活动 add by lihuan
        $handleWordServer = Wordserver_Wordlist::factory();
        $arrItemInfo      = $handleWordServer->getValueByKeys($arrSuccForumIds, 'tb_wordlist_redis_FansFestival_Sign');
        foreach ($arrItemInfo as $key => $value) {
            if (intval($value) > 0) {
                $arrTaskIds[] = intval($value);
            }
        }
        if (!empty($arrTaskIds)) {
            //根据$task_ids批量完成任务
            $arrInput   = array(
                'user_id'  => $this->_intUserId,
                'task_ids' => $arrTaskIds,
            );
            $arrOutTask = Tieba_Service::call('usertasks', 'msetTaskScore', $arrInput, null, null, 'post', 'php', 'utf8');
            if (false == $arrOutTask || $arrOutTask['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("call usertasks::msetTaskScore failed. input=" . serialize($arrInput) . " output=" . serialize($arrOutTask));
                //return Tieba_Errcode::ERR_MO_INTERNAL_ERROR;
            } else if (count($arrOutTask['data']['finished_task_ids']) > 0) {
                foreach ($arrOutTask['data']['finished_task_ids'] as $key => $item) {
                    if (empty($strTaskIds)) {
                        $strTaskIds = $item;
                    } else {
                        $strTaskIds .= '_' . $item;
                    }
                }
                list($t1, $t2) = explode(' ', microtime());
                $msecond  = (float)sprintf('%.0f', (floatval($t1) + floatval($t2)) * 1000);
                $arrParam = array(
                    'ranzhi'   => 5 * count($arrOutTask['data']['finished_task_ids']),
                    'user_id'  => $this->_intUserId,
                    'from'     => 'task_' . $strTaskIds,
                    'ext'      => '',
                    'msectime' => $msecond,
                );
                $arrOut   = Tieba_Service::call('ranzhi', 'addUserRanzhi', $arrParam, null, null, 'post', 'php', 'utf8');
                if (false == $arrOut || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning("call ranzhi::addUserRanzhi failed. input=" . serialize($arrParam) . " output=" . serialize($arrOut));

                } else {
                    $arrInput   = array(
                        'user_id'  => $this->_intUserId,
                        'task_ids' => $arrOutTask['data']['finished_task_ids'],
                    );
                    $arrOutTask = Tieba_Service::call('usertasks', 'msetTaskStatus', $arrInput, null, null, 'post', 'php', 'utf8');
                    if (false == $arrOutTask || $arrOutTask['errno'] != Tieba_Errcode::ERR_SUCCESS || !empty($arrOutTask['data']['failed_task_ids'])) {
                        Bingo_Log::warning("call usertasks::msetTaskStatus failed. input=" . serialize($arrInput) . " output=" . serialize($arrOutTask));
                    }
                }
            }
        }
        /*----  end  ----*/
        Bingo_Timer::start('m_sign_in_hand');
        $intErrno = $this->_handleResult($arrResult, $arrForumId, $arrForumInfo['output']);
        if ($intErrno !== Tieba_Errcode::ERR_SUCCESS) {
            $this->_arrOutData['error'] = $this->_getErrData($intErrno);
            $this->_objResponse->setOutData($this->_arrOutData);
            if ($intErrno != Tieba_Errcode::ERR_USER_IS_BLOCK_NO_SIGN) {
                $this->_error(Tieba_Errcode::ERR_MO_INTERNAL_ERROR);
            }
            Molib_Util_Log::uiFatal($this->_objRequest, $this->_objResponse);
            return false;
        }
        Bingo_Timer::end('m_sign_in_hand');
        /*******/
        /******************************/
        Bingo_Timer::start('m_sign_in_end');
        //粒度控制提交
        if ($this->_intSignedNum > 0) {
            $arrInput  = array(
                'req' => array(
                    'rulegroup' => array('app'),
                    'cmd'       => 'multisign',
                    'app'       => 'msign',
                    'uid'       => $this->_intUserId,
                ),
            );
            $arrOutput = Tieba_Service::call('anti', 'antiActsctrlSubmit', $arrInput);
            if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('submit to actsctrl failed');
            }

            //超级会员提交次数粒度控制
            if (self::$_newNeedMemberStragegy && self::$_isSuperMember) {
                $arrInput  = array(
                    'req' => array(
                        'rulegroup' => array('app'),
                        'cmd'       => 'multisign',
                        'app'       => 'sign',
                        'uid'       => $this->_intUserId,
                    ),
                );
                $arrOutput = Tieba_Service::call('anti', 'antiActsctrlSubmit', $arrInput);
                if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('submit to actsctrl failed');
                }
            }

        }
        $this->_arrOutData['info']  = $this->_arrSignResult;
        $this->_arrOutData['error'] = $this->_getErrData(Tieba_Errcode::ERR_SUCCESS);
        $this->_objResponse->setOutData($this->_arrOutData);

        //数据统计
        Tieba_Stlog::addNode('fid_list_cnt', count($this->_arrForumId));
        Bingo_Timer::end('m_sign_in_end');
        Bingo_Timer::end('m_sign_in_exe');
        return true;
    }

    private function _init()
    {
        $this->_strForumId       = trim($this->_objRequest->getPrivateAttr('forum_ids'));
        $this->_arrForumId       = explode(',', $this->_strForumId);
        $this->_strTbs           = trim($this->_objRequest->getPrivateAttr('tbs', ''));
        $this->_intUserId        = intval($this->_objRequest->getCommonAttr('user_id'));
        $this->_intUserIp        = $this->_objRequest->getCommonAttr('ip_int');
        $this->_intClientType    = intval($this->_objRequest->getCommonAttr('client_type'));
        $this->_strClientVersion = $this->_objRequest->getCommonAttr('client_version');
        $this->_intIsOpen        = intval($this->_objRequest->getStrategy('m_sign', 0));
        $this->_intSignTimeLimit = intval($this->_objRequest->getStrategy('sign_time_threshold', 1));
        $this->_intMaxSignNum    = intval($this->_objRequest->getStrategy('max_sign_num', 50));
        $this->_intSignLevel     = intval($this->_objRequest->getStrategy('m_sign_level', 7));

        $intClientType    = intval($this->_objRequest->getCommonAttr('client_type'));
        $strClientVersion = strval($this->_objRequest->getCommonAttr('client_version'));

        $arrUserInfo       = self::_getUserInfoByUserId($this->_intUserId);
        $this->_isMem      = intval($arrUserInfo[$this->_intUserId]['pay_member_info']['props_id']);
        $this->_vipStatus  = intval($arrUserInfo[$this->_intUserId]['vipInfo']['v_status']);
        $this->_vipEndtime = intval($arrUserInfo[$this->_intUserId]['vipInfo']['e_time']);
        /************小流量测试多吧情况*****************/
        self::littleTest();
        if ($this->_isLittleTest === true) {
            self::signMaxNum($arrUserInfo);
        }
        /*******************************************/
        //如果是 ios>5.8， 普通会员允许6级以上签到，高级会员允许5级以上签到-----//
        $strConf = $this->_objRequest->getStrategy('msign_use_member_strage');
        if (Molib_Util_VersionMatch::checkClient($strConf, $intClientType, $strClientVersion)) {
            self::$_needMemberStragegy = true;
        }
        if (true === self::$_needMemberStragegy) {
            $this->_intSignLevel = $this->_getMsignLvWithMemberLevel($arrUserInfo[$this->_intUserId]['pay_member_info']['props_id']);
        }

        //----- 如果是 ios>5.8， 普通会员允许6级以上签到，高级会员允许5级以上签到//

        //----- 如果是 andirod = 6.2 高级会员用户签到200个吧 ,普通会员还是签到50个吧//
        $strConf = $this->_objRequest->getStrategy('msign_use_new_member_strage');
        if (Molib_Util_VersionMatch::checkClient($strConf, $intClientType, $strClientVersion)) {
            self::$_newNeedMemberStragegy = true;
        }
        if (self::$_newNeedMemberStragegy == true) {
            //区分高级会员和非高级会员
            $props_id = $arrUserInfo[$this->_intUserId]['pay_member_info']['props_id'];
            if ($props_id == 2 && $arrUserInfo[$this->_intUserId]['pay_member_info']['end_time'] >= time()) {
                self::$_isSuperMember = true;
                $this->_intMaxSignNum = intval($this->_objRequest->getStrategy('max_member_sign_num')) > 0 ? intval($this->_objRequest->getStrategy('max_member_sign_num')) : $this->_intRealSignMaxNum;
                /*******************小流量签到400吧*******************************/
                if ($this->_isLittleTest === true) {
                    $this->_intMaxSignNum = $this->_intRealSignMaxNum;
                }
                /***************************************************************/
                $this->_intSignLevel = intval($this->_objRequest->getStrategy('m_sign_s_member_level', 1));
            } else {
                self::$_isSuperMember = false;
            }
        }
        //----- 如果是 andirod = 6.2 //
        $this->_objResponse->addLog('is_mem', intval($arrUserInfo[$this->_intUserId]['pay_member_info']['props_id']));
        $this->_intSignedNum = 0;
        $this->_arrOutData   = array(
            'info'           => null,
            'show_dialog'    => 0,
            'sign_notice'    => '',
            'is_timeout'     => 0,
            'timeout_notice' => '',
        );
        self::isTestUid();
        /**********to do new********/
        if ($this->_isTestUser === true) {
            Bingo_Timer::start('m_sign_in_getfid');
            $this->_arrForumId = self::_getUserMsignForumId($this->_intUserId, $this->_intMaxSignNum);
            Bingo_Timer::end('m_sign_in_getfid');
        }
        /*********************/
    }

    private function _checkTimeLimit()
    {
        $arrMagicUid = array(
            47838960  => 1,
            852960778 => 1,
        );
        if (isset($arrMagicUid[$this->_intUserId])) {
            return true;
        }
        $arrTime       = getdate();
        $intHour       = $arrTime['hours'];
        $intMin        = $arrTime['minutes'];
        $boolIsAllowed = true;
        if ($intHour === 23 && $intMin === 59) {
            $boolIsAllowed = false;
        }
        if ($intHour >= 0 && $intHour < $this->_intSignTimeLimit) {
            $boolIsAllowed = false;
        }

        if (!$boolIsAllowed) {
            $this->_arrOutData['sign_notice'] = self::SIGN_NOTICE;
        }
        $this->_arrOutData['show_dialog'] = !$boolIsAllowed;

        return $boolIsAllowed;
    }

    private function _getErrData($intErrno)
    {
        if ($intErrno === Tieba_Errcode::ERR_SUCCESS) {
            return array(
                'errno'   => $intErrno,
                'errmsg'  => '',
                'usermsg' => '成功',
            );
        }
        return array(
            'errno'   => $intErrno,
            'errmsg'  => '',
            'usermsg' => Molib_Client_Error::getErrMsg($intErrno),
        );
    }

    private function _callServices()
    {
        $this->_objMulti                     = new Util_Data_Multi(self::MULTI_KEY);
        $arrServices                         = array();
        $arrServices['forum']['mgetBtxInfo'] = array(
            'forum_id' => $this->_arrForumId,
        );
        /**********old*********/
        if ($this->_isTestUser === false) {
            $arrServices['perm']['mgetUserLevel'] = array(
                'forum_ids' => $this->_arrForumId,
                'user_id'   => $this->_intUserId,
            );
        }
        /*****************/
        $arrServices['sign']['getUserSignForums'] = array(
            'forum_id' => $this->_arrForumId,
            'user_id'  => $this->_intUserId,
        );
        if (self::$_newNeedMemberStragegy && self::$_isSuperMember) {//粒度控制为一天5次
            $arrServices['anti']['antiActsctrlQuery'] = array(
                'req' => array(
                    'rulegroup' => array('app'),
                    'cmd'       => 'multisign',
                    'app'       => 'sign',
                    'uid'       => $this->_intUserId,
                ),
            );
        } else {//粒度控制为一天1次
            $arrServices['anti']['antiActsctrlQuery'] = array(
                'req' => array(
                    'rulegroup' => array('app'),
                    'cmd'       => 'multisign',
                    'app'       => 'msign',
                    'uid'       => $this->_intUserId,
                ),
            );
        }

        if (!is_object($this->_objMulti)) {
            Bingo_Log::warning('init service object failure');
            return false;
        }
        foreach ($arrServices as $strService => $arrService) {
            foreach ($arrService as $strMethod => $arrMultiInput) {
                $strKey   = $strService . ':' . $strMethod;
                $arrInput = array(
                    'serviceName' => $strService,
                    'method'      => $strMethod,
                    'ie'          => 'utf-8',
                    'input'       => $arrMultiInput,
                );
                $this->_objMulti->register($strKey, $arrInput);
            }
        }
        $this->_objMulti->call();
        return true;
    }

    private function _msign($arrFids)
    {
        if ($this->_isMem == 2) {
            $arrInput = array(
                'forum_id'    => $arrFids,
                'user_id'     => $this->_intUserId,
                'user_ip'     => $this->_intUserIp,
                'term_type'   => self::MEM_TYPE,
                'client_type' => self::CLIENT_TYPE,
                'app_version' => $this->_strClientVersion,
                'os'          => $this->_intClientType,
            );
        } else {
            $arrInput = array(
                'forum_id'    => $arrFids,
                'user_id'     => $this->_intUserId,
                'user_ip'     => $this->_intUserIp,
                'term_type'   => self::TERM_TYPE,
                'client_type' => self::CLIENT_TYPE,
                'app_version' => $this->_strClientVersion,
                'os'          => $this->_intClientType,
            );
        }

        //临时小流量日志
        $userId  = $this->_intUserId;
        $arrUids = array(1065785409, 948368128, 1213613023);
        if (in_array($userId, $arrUids)) {
            Bingo_Log::warning("sign::msignIn arrInput=" . var_export($arrInput, true));
        }
        $arrOutput = Tieba_Service::call('sign', 'msignIn', $arrInput, NULL, 'service_sign_commit', 'post', 'php', 'utf-8');
        return $arrOutput;
    }

    private function _handleResult($arrResult, $arrFids, $arrForumInfo)
    {
        if ($arrResult['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call msign failed ' . $arrResult['errno']);
            return $arrResult['errno'];
        }
        $this->_objResponse->addLog('succ_num', $arrResult['succ_num']);
        $this->_objResponse->addLog('fail_num', $arrResult['fail_num']);
        $arrSuccInfo         = $arrResult['succ_info'];
        $arrFailInfo         = $arrResult['fail_info'];
        $this->_intSignedNum = count($arrSuccInfo);
        // call getUserSignForums again
        $arrInput  = array(
            'forum_id' => $arrFids,
            'user_id'  => $this->_intUserId,
        );
        $arrOutput = Molib_Tieba_Service::call('sign', 'getUserSignForums', $arrInput);
        $this->_objResponse->addLog('wangbogetsignforumIn', serialize($arrInput));
        $this->_objResponse->addLog('wangbogetsignforumOut', serialize($arrOutput));
        $arrUserInfo   = $arrOutput['arr_user_info'];
        $arrSignResult = array();
        $logStatus     = 0;
        foreach ($arrFids as $intForumId) {
            if ($arrUserInfo[$intForumId]['is_sign_in'] != $arrSuccInfo[$intForumId]['user_info']['is_sign_in']) {
                if ($logStatus === 0) {
                    Bingo_Log::warning('wangbo16: get success sign is fail;forum_id[' . $intForumId . ']user_id[' . $this->_intUserId . ']');
                    $logStatus = 1;
                }
            }
            if ($arrSuccInfo[$intForumId]['user_info']['is_sign_in']) {
                $arrUserInfo[$intForumId]['is_sign_in']    = true;
                $arrUserInfo[$intForumId]['cont_sign_num'] = $arrSuccInfo[$intForumId]['user_info']['cont_sign_num'];
            }
            $arrSignInfo                   = array();
            $arrSignInfo['forum_id']       = $intForumId;
            $arrSignInfo['forum_name']     = $arrForumInfo[$intForumId]['forum_name']['forum_name'];
            $arrSignInfo['signed']         = $arrUserInfo[$intForumId]['is_sign_in'] ? true : false;
            $arrSignInfo['is_on']          = 1;
            $arrSignInfo['is_filter']      = 0;
            $arrSignInfo['sign_day_count'] = $arrUserInfo[$intForumId]['cont_sign_num'];
            if ($this->_isMem == 2) {//此处去掉对 $_newNeedMemberStragegy 的判断，不区分版本，只要是高级会员，就为12   目前 _isMem=2 时为高级会员
                $intCurScore = 12;
            } else {
                $intCurScore = 6;
            }
            if ($this->_isLittleTest === true) {
                if ($this->_vipStatus === 3) {
                    $intCurScore = 14;
                }
            }
            if (1 < $arrSignInfo['sign_day_count']) {
                $intCurScore += 2;
            }
            $arrSignInfo['cur_score'] = $intCurScore;
            if (array_key_exists($intForumId, $arrFailInfo)) {
                $arrSignInfo['error']['err_no']  = $arrFailInfo[$intForumId]['errno'];
                $strErrorMsg                     = Tieba_Errcode::$usercodes[$arrFailInfo[$intForumId]['errno']];
                $strErrorMsg                     = Bingo_Encode::convert($strErrorMsg, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
                $arrSignInfo['error']['usermsg'] = $strErrorMsg;
                $arrSignInfo['error']['errmsg']  = $arrFailInfo[$intForumId]['errmsg'];
                $arrSignInfo['cur_score']        = 0;
            } else if (array_key_exists($intForumId, $arrSuccInfo)) {
                $arrSignInfo['error']['err_no']  = 0;
                $arrSignInfo['error']['usermsg'] = '';
                $arrSignInfo['error']['errmsg']  = '';
            } else {
                $arrSignInfo['error']['err_no']  = Tieba_Errcode::ERR_SIGN_IN_FAIL;
                $strErrorMsg                     = Tieba_Errcode::$usercodes[Tieba_Errcode::ERR_SIGN_IN_FAIL];
                $strErrorMsg                     = Bingo_Encode::convert($strErrorMsg, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
                $arrSignInfo['error']['usermsg'] = $strErrorMsg;
                $arrSignInfo['error']['errmsg']  = '';
                $arrSignInfo['cur_score']        = 0;
            }
            $this->_arrSignResult[] = $arrSignInfo;
        }
        $this->_objResponse->addLog('wangbogetsignforumRet', serialize($this->_arrSignResult));
        return Tieba_Errcode::ERR_SUCCESS;
    }

    /**
     * @input
     * $intUserId = 23456;
     *
     */
    private static function _getUserInfoByUserId($intUserId)
    {
        $strServiceName = 'user';
        $strMethod      = 'getUserData';
        $arrInput       = array(
            'user_id' => $intUserId,
        );
        Bingo_Log::debug(sprintf('talk to servicename:[%s] method:[%s] input:[%s] ', $strServiceName, $strMethod, serialize($arrInput)));
        Bingo_Timer::start("{$strServiceName}_{$strMethod}");
        $arrOutput = Tieba_Service::call($strServiceName, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
        Bingo_Timer::end("{$strServiceName}_{$strMethod}");
        if (false === $arrOutput) {
            Bingo_Log::warning(sprintf('Failed to call servicename:[%s] method:[%s][user_name:%s]', $strServiceName, $strMethod, serialize($arrInput)));
            return array();
        }
        //check err_no
        if (isset($arrOutput['errno']) && (0 == intval($arrOutput['errno']))) {
            //success nothing to do
            $arrRet = $arrOutput;
            foreach ($arrRet['user_info'] as $arrUserInfoTmp) {
                $arrTmp = Util_User::getUserInfo($arrUserInfoTmp);
                $arrTmp = Molib_Util_Array::fetchArray($arrTmp,
                    array('name', 'id', 'portrait', 'name_show', 'user_type', 'is_verify', 'pay_member_info', 'vipInfo')
                );

                $arrUserInfos[$arrUserInfoTmp['user_id']] = $arrTmp;

            }
            return $arrUserInfos;
        } else {
            //err,print log
            Bingo_Log::warning(sprintf('Err to call servicename:[%s] method:[%s] [input:%s] [output:%s]', $strServiceName, $strMethod, serialize($arrInput), serialize($arrOutput)));
            return array();
        }
    }

    private function _getUserMsignForumId($userId, $signNum)
    {
        $arrInput = array(
            'user_id'   => $userId,
            'page_size' => $signNum,
        );
        Bingo_Timer::start('m_sign_in_likeforum');
        $arrForumList = Tieba_Service::call('perm', 'getLikeForumList', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        Bingo_Timer::end('m_sign_in_likeforum');
        if (empty($arrForumList) || $arrForumList['errno'] != 0) {
            Bingo_Log::warning("call getLikeForumList failed");
            return false;
        }
        $nAllForumNum = count($arrForumList['output']['member_list']);
        if ($nAllForumNum <= 0) {
            Bingo_Log::warning("there is no forum");
            return false;
        }
        $arrForumIds = array();
        foreach ($arrForumList['output']['member_list'] as $forumIndex => $forumInfo) {
            if ($forumInfo['level_id'] >= $this->_intSignLevel) {
                $arrForumIds[] = $forumInfo['forum_id'];
            }

        }

        //小流量，第二批
        if (true === $this->_isLittleTest && $this->_intVipLevel > 1) {
            if ($this->_intVipLevel > 1) {
                Bingo_Timer::start('little_fid');
                $arrInput     = array(
                    'user_id'   => $userId,
                    'page_size' => $this->_intRealSignMaxNum,
                    'page_no'   => 2,
                );
                $arrOutByPage = Molib_Tieba_Service::call('perm', 'getLikeForumList', $arrInput);
                if (false === $arrOutByPage) {
                    Bingo_Log::fatal('call perm failed.  [sevice_name:perm] [method:getLikeForumList] [input:' . serialize($arrInput) . ']');
                    return $arrForumIds;
                }
                if (is_array($arrOutByPage['output']['member_list']) && count($arrOutByPage['out']['member_list']) > 0) {
                    foreach ($arrOutByPage['output']['member_list'] as $value) {
                        $arrForumIds[] = $value['forum_id'];
                    }
                }
                $arrForumIds = array_slice($arrForumIds, 0, $this->_intRealSignMaxNum);
                Bingo_Timer::end('little_fid');
            }
        }
        return $arrForumIds;
    }

    private function _getMsignLvWithMemberLevel($intLevel)
    {
        if (2 == $intLevel) {//高级会员
            return 5;
        }
        if (1 == $intLevel) {//普通会员
            return 6;
        }
        return 7;
    }

    private function _callMsignIn()
    {
        $eNum     = 30;
        $termType = self::TERM_TYPE;
        $num      = count($this->_signFid);
        if ($num < 30) {
            $eNum = $num;
        }
        $this->_signPage = ceil($num / 30);
        $this->_objMulti = new Util_Data_Multi(self::MULTI_SIGN_KEY);
        if (!is_object($this->_objMulti)) {
            Bingo_Log::warning('init service object failure');
            return false;
        }
        if ($this->_isMem == 2) {
            $termType = self::MEM_TYPE;
        }
        if ($this->_isLittleTest === true) {
            if (3 === $this->_vipStatus && $this->_vipEndtime > time()) {  //超级年费会员的判断
                $termType = 12;
            }
        }
        for ($i = 0; $i < $this->_signPage; $i++) {
            $startNum            = $i * $eNum;
            $this->_signEfid[$i] = array_slice($this->_signFid, $startNum, $eNum);
            $arrMultiInput       = array(
                'forum_id'    => $this->_signEfid[$i],
                'user_id'     => $this->_intUserId,
                'user_ip'     => $this->_intUserIp,
                'term_type'   => $termType,
                'client_type' => self::CLIENT_TYPE,
                'app_version' => $this->_strClientVersion,
                'os'          => $this->_intClientType,
            );

            $strKey   = 'sign:msignIn' . $i;
            $arrInput = array(
                'serviceName' => 'sign',
                'method'      => 'msignIn',
                'ie'          => 'utf-8',
                'input'       => $arrMultiInput,
            );
            //临时小流量日志
            $userId  = $this->_intUserId;
            $arrUids = array(1065785409, 948368128, 1213613023);
            if (in_array($userId, $arrUids)) {
                Bingo_Log::warning("_objMulti register [" . $strKey . "] arrInput=" . var_export($arrInput, true));
            }
            $this->_objMulti->register($strKey, $arrInput);
        }
        $this->_objMulti->call();
        return true;
    }

    private function isTestUid()
    {
        $this->_isTestUser = true;
        return true;

    }

    private function signMaxNum($arrUserInfo)
    {
        if (1 < $this->_vipStatus && time() < $this->_vipEndtime) {
            $this->_intVipLevel = intval($arrUserInfo[$this->_intUserId]['vipInfo']['v_level']);
            if ($this->_intVipLevel < 1 || $this->_intVipLevel > 5) {     //当前最高为5级，最低为1级
                $this->_intVipLevel = 1;
            }
            $this->_intRealSignMaxNum = $this->_arrSignMaxNum[$this->_intVipLevel];
        }
    }

    private function littleTest()
    {
        $this->_isLittleTest = true;
        return true;
    }

    /**
     * @param
     * @return
     */
    private function _getAntiState()
    {
        $intUserId       = $this->_objRequest->getCommonAttr('user_id');
        $arrUserStateReq = array(
            'reqs' => array(
                'check_block' => array(
                    'service_type' => 'blockid',
                    'key'          => $intUserId,
                    'forum_id'     => 0,
                ),
            )
        );

        $arrOut = Tieba_Service::call("userstate", "queryBlockAndAppealInfo", $arrUserStateReq, null, null, 'post', 'php', 'utf-8');
        if (false == $arrOut || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call userstate:queryBlockAndAppealInfo failed');
            return Tieba_Errcode::ERR_MO_INTERNAL_ERROR;
        }
        $this->_blockAndAppealInfo = $arrOut['res'];
        if (1 == $this->_blockAndAppealInfo['appeal_status']) {
            return Tieba_Errcode::ERR_CLIENT_BLOCK_IS_APPEALED_ERROR;
        }
        return Tieba_Errcode::ERR_SUCCESS;

    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
