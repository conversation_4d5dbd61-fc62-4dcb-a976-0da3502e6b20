<?php
/**
 * Created by PhpStorm.
 * User: pang<PERSON>gyao
 * Date: 2019/8/23
 * Time: 下午5:36
 */

class Util_User
{
    public static function getUserInfo($arrUserInfo)
    {
        $arrResult       = $arrUserInfo;
        $intUid          = intval($arrUserInfo['user_id']);
        $strUname        = strval($arrUserInfo['user_name']);
        $strNickName     = strval($arrUserInfo['user_nickname']);
        $intPortraitTime = intval($arrUserInfo['portrait_time']);

        $arrResult['name']      = $strUname;
        $arrResult['id']        = $intUid;
        $arrResult['portrait']  = Tieba_Ucrypt::encode($intUid, Molib_Util_Encode::convertUTF8ToGBK($strUname), $intPortraitTime);
        $arrResult['name_show'] = empty($strNickName) ? $strUname : $strNickName;
        $arrResult['user_type'] = $arrUserInfo['user_type'] > 0 ? 2 : 1;
        $arrResult['is_verify'] = $arrUserInfo['user_type'] > 0 ? true : false;
        $arrResult['mParr_props'] = $arrUserInfo['mParr_props'];

        $intPropsId = $arrResult['mParr_props']['level']['props_id'];
        if(empty($intPropsId) || time() > $arrResult['mParr_props']['level']['end_time']){
            $intPropsId = 0;
            $intEndTime = 0;
            $strPicUrl = '';
        }else{
            $intPropsId = $arrResult['mParr_props']['level']['props_id'];
            $intEndTime = $arrResult['mParr_props']['level']['end_time'];
            $strPicUrl  = $arrResult['mParr_props']['level']['pic_url'];
        }

        $arrResult['pay_member_info']['props_id'] = $intPropsId;
        $arrResult['pay_member_info']['end_time'] = $intEndTime;
        $arrResult['pay_member_info']['pic_url'] = $strPicUrl;

        return $arrResult;
    }

    public static function maskIP($strIP)
    {
        $intPos  = strrpos($strIP, '.');
        $strHalf = substr($strIP, 0, $intPos);
        return $strHalf.'.*';
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>