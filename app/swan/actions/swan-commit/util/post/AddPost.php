<?php
/**
 * Created by PhpStorm.
 * User: pang<PERSON><PERSON><PERSON>
 * Date: 2019/8/6
 * Time: 下午8:07
 */

class Util_Post_AddPost {
    public static function getExtPreMultiParam($objRequest, $arrExtParam, $strCallFrom) {
        $arrParam = array();
        if ($strCallFrom == 'flutter') {
            $arrParam = Util_Post_Flutter_AddPost::getExtPreMultiParam($objRequest, $arrExtParam);
        } else if ($strCallFrom == 'smallapp') {
            $arrParam = Util_Post_Smallapp_AddPost::getExtPreMultiParam($objRequest, $arrExtParam);
        }

        return $arrParam;
    }

    public static function getExtPreMultiRes($objRequest, $arrExtParam, $strCallFrom) {
        $arrRes = array();
        if ($strCallFrom == 'flutter') {
            $arrRes = Util_Post_Flutter_AddPost::getExtPreMultiRes($objRequest, $arrExtParam);
        } else if ($strCallFrom == 'smallapp') {
            $arrRes = Util_Post_Smallapp_AddPost::getExtPreMultiRes($objRequest, $arrExtParam);
        }

        return $arrRes;
    }
}