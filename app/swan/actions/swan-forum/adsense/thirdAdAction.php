<?php

/**
 * 三方广告异步请求接口（afd/凤巢）
 * Class thirdAdAction
 */
class thirdAdAction extends Base_Action
{

    //广告类型对应的枚举值
    const ASP_TYPE_PB = 1;
    const ASP_TYPE_FRS = 2;
    const AFD_TYPE_PB = 3;
    const AFD_TYPE_FRS = 4;

    /**
     * @return array
     */
    public function _getPrivateInfo() {
        $this->_objRequest->setStrategy(array('check_nonu' => false, 'check_sign' => true));
        $arrPrivateInfo = array(
            'cuid'            => $this->_getInput('cuid', ''),
            'tid'             => $this->_getInput('tid', ''),
            'first_dir'       => $this->_getInput('first_dir', ''),
            'second_dir'      => $this->_getInput('second_dir', ''),
            'fid'             => $this->_getInput('fid', ''),
            'forum_name'      => $this->_getInput('forum_name', ''),
            'source_platform' => $this->_getInput('source_platform', ''),
            'type'            => $this->_getInput('type', ''),
            'title'           => $this->_getInput('title', ''),
            'uid'             => $this->_objRequest->getCommonAttr('user_id'),
            'scene'           => $this->_getInput('scene', ''),
            'place_type'      => $this->_getInput('place_type', ''),
        );
        foreach ($arrPrivateInfo as $key => $value) {
            Tieba_Stlog::addNode($key, $value);
        }
        $arrIps = Bingo_Http_Ip::getConnectIpExt();
        if ($arrIps['type'] == 'IPv4') {
            Tieba_Stlog::addNode('uip', Bingo_Http_Ip::newip2long($arrIps['ip']));
            Tieba_Stlog::addNode('uip6', '');
        } else {
            Tieba_Stlog::addNode('uip', 0);
            Tieba_Stlog::addNode('uip6', $arrIps['ip']);
        }
        Tieba_Stlog::addNode('uip_str', Bingo_Http_Ip::getConnectIp());
        return $arrPrivateInfo;
    }

    /**
     * @return bool
     */
    public function _checkPrivate() {
        return true;
    }

    /**
     * 入口
     */
    public function _execute() {
        $data = array();
        if ($this->isNeedAd()) {
            switch ($this->_objRequest->getPrivateAttr('type', '')) {
                case self::ASP_TYPE_PB:
                    $data = $this->getAspRes();
                    break;
                case self::AFD_TYPE_PB:
                    $obj = new Service_Jifengad_PbRequest();
                    $data = $obj->getJfAdRes($this->_objRequest, $this->_arrRequest);
                    break;
                case self::AFD_TYPE_FRS:
                    Service_Jifengad_FrsRequest::init($this->_objRequest, $this->_arrRequest);
                    //获取请求数据参数
                    $this->_inputData = Service_Jifengad_FrsRequest::getInputs();
                    //执行请求广告接口
                    $data = Service_Jifengad_FrsRequest::execute($this->_inputData);
                    break;
                default:
                    $data = array();
            }
            if (!empty($data)) {
                $this->_error(Tieba_Errcode::ERR_SUCCESS);
            } else {
                $this->_error(Tieba_Errcode::ERR_MO_PROCESS_FAIL);
            }
        }else{
            $this->_error(Tieba_Errcode::ERR_SUCCESS);
        }

        $this->_objResponse->addOutData('data', $data);
    }

    //广告开放的条件
    private function isNeedAd() {
        $pageOrigin = isset($this->_arrRequest['page_origin']) ? $this->_arrRequest['page_origin'] : '';
        //大搜高考项目不显示广告 一跳页面不显示疾风广告
        if ($pageOrigin == 'gaokaoala') {
            $configRet = self::getDictionaryConfig();
            $startTime = isset($configRet['ret']['start_time']) ? intval($configRet['ret']['start_time']) : 0;
            $endTime = isset($configRet['ret']['end_time']) ? intval($configRet['ret']['end_time']) : 0;
            //满足时间要求,小程序一跳下掉疾风广告,时间过了之后按之前线上逻辑执行
            if (time() > $startTime && time() < $endTime) {
                Bingo_Log::warning('current time  ad req is forbidden because :'.$pageOrigin);

                return false;
            }
        }

        //<AUTHOR> 通过吧id判断是否是官方吧 如果是官方吧则不请求广告，如果不是官方吧则请求广告
        //frs我能拿到的就是 吧名
        //pb页能拿到的就是帖子id
        $forumName = $this->_getInput('forum_name', '');
        $tid       = $this->_getInput('tid', '');
       // $tid       = '7457569191';
        if (!empty($forumName)) {
            $input = array(
                "query_words" => $forumName,
            );
            $forumRet = Tieba_Service::call('forum', 'getFidByFname', $input, null, null, 'post', 'php', 'utf-8');
            if (!empty($forumRet) && !$forumRet['errno'] && isset($forumRet['forum_id'][0]['forum_id'])) {
                $forumId = $forumRet['forum_id'][0]['forum_id']; //吧id
            }
        } else if (!empty($tid)) {
            $arrInput = array (
                'thread_ids' => array($tid),
                'need_abstract' => 0,
                'forum_id' => 0,
                'need_photo_pic' => 1,
                'need_user_data' => 0,
                'call_from' => 'swan-forum',
                'need_post_content' => 0,
                'structured_content' => 0,
            );
            $tidResult = Tieba_Service::call('post', 'mgetThread', $arrInput, null, null, 'post', 'php', 'utf-8');
            if (isset($tidResult['output']['thread_list'][$tid]['forum_id'])) {
                $forumId = $tidResult['output']['thread_list'][$tid]['forum_id'];
            }
        }

        if (isset($forumId) && !empty($forumId)) {
            $mutliObj  = new Tieba_Multi('FRS_MULTI_KEY');
            $arrInput = array(
                'forum_id' => array(
                    $forumId,
                ),
            );
            $arrMultiInput = array(
                'serviceName' => 'forum',
                'method'      => 'mgetBtxInfoEx',
                'input'       => $arrInput,
                'ie'          => 'utf-8',
            );
            $mutliObj->register('forum_GetBtxForArea', new Tieba_Service('forum'), $arrMultiInput);
            $mutliObj->call();
            $forumResult = $mutliObj->getResult('forum_GetBtxForArea');
//var_dump($forumResult['output'][$forumId]['attrs']['official']['official_common_switch']['official_activity']);exit();
            if ($forumResult['output'][$forumId]['attrs']['official']['official_common_switch']['official_activity']
                && $forumResult['output'][$forumId]['attrs']['official']['official_common_switch']['official_activity'] == 1) {
                //官方吧不请求广告
                return false;
            }
        }

        return true;
    }

    /**
     * 获取词典配置
     */
    private static function getDictionaryConfig() {
        $handleWordServer = Wordserver_Wordlist::factory();
        $configKeyTable = 'tb_wordlist_redis_no_display_third_party_ad_key_config';
        $configInput = array(
            'table' => $configKeyTable,//英文表名
            'start' => 0,
            'stop'  => -1,
        );
        $configRet = $handleWordServer->getTableContents($configInput);
        if ($configRet['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('get smallapp ad config failed [method:getDictionaryConfig] [input:' . serialize($configInput) . '] [output:' . serialize($configRet) . ']');
            return false;
        }

        return $configRet;
    }

    private function getAspRes() {
        $arrInput = [
            'title'           => $this->_objRequest->getPrivateAttr('title', ''),
            'pre_query'       => $this->_objRequest->getPrivateAttr('forum_name', ''),
            'original_query'  => $this->_objRequest->getPrivateAttr('second_dir', ''),
            'source_platform' => $this->_objRequest->getPrivateAttr('source_platform', ''),
            'place_type' => $this->_objRequest->getPrivateAttr('place_type', ''),
        ];
        return Service_Fengchaoad_Request::getAspAdRes($arrInput);
    }
}

