<?php
/**
 * Created by PhpStorm.
 * User: pang<PERSON>gy<PERSON>
 * Date: 2019/8/8
 * Time: 上午10:26
 */

class floorAction extends Base_Action
{
    const USER_ICON_SIZE = 2;

    //请求参数
    private $_intPid = 0;
    private $_intTid = 0;
    private $_strNickName = '';
    private $_strFr = '';
    private $_strItbTbs = '';
    private $_strRandomid = '';
    private $_strSign = '';
    private $_intTimeStamp = 0;
    private $_strTbs = '';
    private $_intUserId = 0;
    private $_intIp = 0;
    private $_strUserName = '';

    // 调用
    private $_arrMultiService = array();
    private $_objMultiCall = null;

    // 内部变量
    private $_arrThreads = array();
    private $_arrUserIds = array();
    private $_intForumId = 0;
    private $_arrUserData = array();
    private $_arrForumData = array();
    private $_arrThreadData = array();
    private $_arrThreadInfo = array();
    private $_strFirstPic = array();
    private $_arrThreadAgreeInfo = array();
    private $_arrPostAgreeInfo = array();


    /**
     * @return array
     */
    public function _getPrivateInfo()
    {
        $this->_objRequest->setStrategy(array('check_nonu' => false, 'check_sign' => false));
        $arrPrivateInfo = array(
            'itb_tbs'   => strval($this->_getInput('itb_tbs', '')),
            'nick_name' => strval($this->_getInput('nick_name', '')),
            'fr'        => strval($this->_getInput('fr', '')),
            'pid'       => intval($this->_getInput('pid', 0)),
            'tid'       => intval($this->_getInput('tid', 0)),
            'randomid'  => strval($this->_getInput('randomid', '')),
            'sign'      => strval($this->_getInput('sign', '')),
            'timestamp' => intval($this->_getInput('timestamp', 0)),
            'tbs'       => strval($this->_getInput('tbs', '')),
        );

        return $arrPrivateInfo;
    }

    /**
     * @return bool
     */
    public function _checkPrivate()
    {
        $this->_intPid = $this->_objRequest->getPrivateAttr('pid');
        $this->_intTid = $this->_objRequest->getPrivateAttr('tid');
        if ($this->_intPid <= 0 || $this->_intTid <= 0) {
            Bingo_Log::warning('params error! pid or tid is null!');
            $this->_error(Tieba_Errcode::ERR_PARAM_ERROR, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_PARAM_ERROR));
            return false;
        }

        $this->_strNickName  = $this->_objRequest->getPrivateAttr('nick_name');
        $this->_strItbTbs    = $this->_objRequest->getPrivateAttr('itb_tbs');
        $this->_strFr        = $this->_objRequest->getPrivateAttr('fr');
        $this->_strRandomid  = $this->_objRequest->getPrivateAttr('randomid');
        $this->_strSign      = $this->_objRequest->getPrivateAttr('sign');
        $this->_intTimeStamp = $this->_objRequest->getPrivateAttr('timestamp');
        $this->_strTbs       = $this->_objRequest->getPrivateAttr('tbs');
        $this->_intUserId    = $this->_objRequest->getCommonAttr('user_id');
        $this->_intIp        = $this->_objRequest->getCommonAttr('ip_int');
        $this->_strUserName  = $this->_objRequest->getCommonAttr('user_name');

        return true;
    }

    /**
     * [_process description]
     * @return [type] [description]
     *
     */
    public function _execute()
    {
        // 获取信息
        if (!$this->_getThreadsInfo()) {
            $this->_log();
            Bingo_Log::warning("multi get info error!");
            return false;
        }

        // 获取人吧贴核心数据
        if (!$this->_getCoreInfo()) {
            $this->_log();
            Bingo_Log::warning("multi get info error!");
            return false;
        }

        // 构建数据返回
        $arrRes = $this->_buildResp();
        $this->_log();
        $this->_objResponse->setOutData($arrRes);
        return true;
    }

    private function _reIntegrateUser($arrUserInfo)
    {
        $arrUser = array(
            'id'        => $this->_intUserId,
            'name'      => $arrUserInfo['name'],
            'name_show' => $arrUserInfo['name_show'],
            'is_login'  => $this->_objRequest->getCommonAttr('login'),
            'no_un'     => $arrUserInfo['no_un'],
            'is_verify' => false,
            'portrait'  => 'http://tb.himg.baidu.com/sys/portrait/item/' . $arrUserInfo['portrait'] . '.jpg',//用户头像url
            'uid'       => $this->_intUserId,
            'admintype' => $arrUserInfo['admintype'],
            'is_vipdel' => $arrUserInfo['is_vipdel'],
            'userhide'  => isset($arrUserInfo['userhide']) ? $arrUserInfo['userhide'] : 0,
        );

        return $arrUser;
    }

    private function _buildResp()
    {
        // 构建user的返回
        $arrUserInfo  = $this->_integrateUser();
        $arrForumInfo = $this->_integrateForum();
        $arrThreadAll = $this->_integrateThread();
        $arrMeta      = $this->_buildMetaInfo();

        $arrResult = Util_Logic_StructedPb::convert($this->_objRequest, $arrThreadAll['post_list'], $arrThreadAll['sub_post_list'], $arrUserInfo, $arrThreadAll['thread'], $arrForumInfo, 1);

        $arrUserRes     = $this->_reIntegrateUser($arrResult['user']);
        $arrForumRes    = $arrResult['forum'];
        $arrThreadRes   = $arrResult['thread'];
        $arrPostListRes = $arrResult['post_list'];

        $arrRes = array(
            'no'    => 0,
            'error' => "",
            'data'  => array(
                'user'      => $arrUserRes,
                'forum'     => $arrForumRes,
                'thread'    => $arrThreadRes,
                'post_list' => $arrPostListRes,
                'meta'      => $arrMeta,
            )
        );

        return $arrRes;
    }

    private function _buildMetaInfo()
    {
        $arrPostInfo = $this->_arrThreads['post_infos'];
        $arrTidInfo  = $arrPostInfo[0];
        $intLzUid    = $arrPostInfo[0]['user_id'];

        // 生成keywords
        $strKeywords = '百度贴吧,' . strval($this->_arrForumData['forum_name']['forum_name']) . ',';
        $strTitle    = isset($arrTidInfo['title']) ? strval($arrTidInfo['title']) : $arrPostInfo[0]['title'];
        if (!empty($strTitle)) {
            $arrTitleKeywords = Util_String::splitString($strTitle);
            $strKeywords      .= implode(',', $arrTitleKeywords);
        }
        // 生成description
        $strDesc        = '';
        $strNoDesc      = '';
        $arrPostComment = array(); // 楼中楼
        foreach ($arrPostInfo as $post) {
            // 获取每个楼层的楼中楼
            if (!empty($post['comment_info'])) {
                $arrPostComment = array_merge($arrPostComment, $post['comment_info']);
            }

            $strTxtContent = trim(strip_tags($post['content']));
            if (empty($strTxtContent)) {
                continue;
            }
            // 内容长度>200
            if (strlen($strTxtContent) > 200) {
                // 此楼层为楼主
                if ($post['user_id'] == $intLzUid) {
                    $strDesc = Util_String::mb_substr($strTxtContent, 0, 100);
                    break;
                } else {
                    $strNoDesc = Util_String::mb_substr($strTxtContent, 0, 100);
                }
            } else {
                // 拼接楼主的各个楼层内容
                if (strlen($strDesc) > 200) {
                    continue;
                }
                if ($post['user_id'] == $intLzUid) {
                    $strDesc .= $strTxtContent;
                }
            }
        }

        if (strlen($strDesc) < 200 && $strNoDesc != '') {
            $strDesc = $strNoDesc;
        }
        if ($strDesc == '') {
            // 楼中楼内容不为空
            if (!empty($arrPostComment)) {
                foreach ($arrPostComment as $comment) {
                    $strTxtContent = trim(strip_tags($comment['content']));
                    if (empty($strTxtContent)) {
                        continue;
                    }
                    if (strlen($strTxtContent) > 100) {
                        $strDesc = Util_String::mb_substr($strTxtContent, 0, 100);
                        break;
                    } else {
                        if (strlen($strDesc) > 100) {
                            continue;
                        }
                        $strDesc .= $strTxtContent;
                    }
                }
            }
        }

        // 取首楼图片
        $arrPostList = $this->_arrThreadData['post_list'];

        $firstFloor = $arrPostList[0];
        $strImg     = '';
        if (!empty($firstFloor['first_pic'])) {
            $strImg = Molib_Util_ClientImgLogic::generateNewUrl($firstFloor['first_pic'], true, "whcrop=375,210;g=0");
        }
        if (empty($strImg) && !empty($this->_strFirstPic)) {
            $strImg = Molib_Util_ClientImgLogic::generateNewUrl($this->_strFirstPic, true, "whcrop=375,210;g=0");
        }

        // end
        $strSubTitle = Util_String::mb_substr($strTitle, 0, 10);
        $arrMetaInfo = array(
            'keywords'    => $strKeywords,
            'description' => $strSubTitle . '..' . $strDesc,
            'image'       => $strImg,
        );
        return $arrMetaInfo;
    }

    private function _integrateThread()
    {
        $arrUserInfo = $this->_arrUserData['all_forum_user'];

        $arrThreadRes = array(
            'id'             => intval($this->_arrThreads['thread_id']),
            'reply_num'      => $this->_arrThreads['total_post_num'],
            'valid_post_num' => $this->_arrThreads['valid_post_num'],
            'repost_num'     => 0, //转贴数目
            'thread_type'    => $this->_arrThreads['thread_type'],
            'comment_num'    => 0, //赞功能已下线
        );

        //贴子收藏相关
        $strTitle              = isset($this->_arrThreads[0]['title']) ? strval($this->_arrThreads[0]['title']) : '';
        $arrThreadRes['title'] = Util_Tool::ltrimStr($strTitle, '回复：');
        //author
        $intUserIdTmp = intval($this->_arrThreads['first_post_userid']);

        $author       = array(
            'name'        => $this->_arrThreads['first_post_username'],
            'mParr_props' => $arrUserInfo[$this->_arrThreads['first_post_userid']]['mParr_props'],
            'name_show'   => empty($arrUserInfo[$this->_arrThreads['first_post_userid']]['user_nickname']) ? $this->_arrThreads['first_post_username'] : $arrUserInfo[$this->_arrThreads['first_post_userid']]['user_nickname'],
            'id'          => $this->_arrThreads['first_post_userid'],
            'portrait'    => Tieba_Ucrypt::encode($this->_arrThreads['first_post_userid'], Molib_Util_Encode::convertUTF8ToGBK($this->_arrThreads['first_post_username']), $arrUserInfo[$intUserIdTmp]['portrait_time']),
            'level_id'    => $arrUserInfo[$this->_arrThreads['first_post_userid']]['level_id'],
            'is_like'     => $arrUserInfo[$this->_arrThreads['first_post_userid']]['is_like'] == 1 ? true : false,
        );
        if (!isset($arrUserInfo[$this->_arrThreads['first_post_userid']]['user_type'])) {
            $author['type'] = 0;
        } else {
            $author['type'] = $arrUserInfo[$this->_arrThreads['first_post_userid']]['user_type'] > 0 ? 2 : 1;
        }
        $author['is_verify']    = intval($arrUserInfo[$this->_arrThreads['first_post_userid']]['user_type']) > 0 ? true : false;
        $arrThreadRes['author'] = $author;

        //拼接pid
        $strPids = '';
        foreach ($this->_arrThreads['post_infos'] as $postItem) {
            $strPids .= $postItem['id'] . ',';
        }
        $arrThreadRes['pids'] = $strPids;

        $arrThreadRes['agree'] = $this->_arrThreadAgreeInfo;

        $arrThreadAll['thread'] = $arrThreadRes;

        // 富文本
        $intPortraitQuality = 80;   //头像压缩质量比
        $objPbCondition     = Util_RichText::getPbContentCondition();
        $objFloorCondition  = Util_RichText::getFloorContentCondition();

        $arrResult = Util_Logic_StructedPb::processWebAppPostListContent($this->_arrThreadData['post_list'], $this->_arrThreadData['sub_post_list'], $objPbCondition, $objFloorCondition, $arrThreadAll['thread_type'], $intPortraitQuality);

        // 加入点赞数据
        foreach ($arrResult['post_list'] as $key => &$info) {
            $pid                        = $info['id'];
            $info['agree']['agree_num'] = isset($info['agree_num']) ? $info['agree_num'] : 0;
            if (isset($this->_arrPostAgreeInfo['map'][$pid])
                && $this->_arrPostAgreeInfo['map'][$pid]['status'] == 0
                && $info['agree_num'] > 0) {
                $info['agree']['has_agree'] = 1;
            } else {
                $info['agree']['has_agree'] = 0;
            }
        }

        $arrThreadAll['post_list']     = $arrResult['post_list'];
        $arrThreadAll['sub_post_list'] = $arrResult['sub_post_list'];


        return $arrThreadAll;
    }

    private function _integrateForum()
    {
        $arrUserPerm = $this->_arrUserData['perm'];

        $arrForumRes = array(
            'name'         => $this->_arrForumData['forum_name']['forum_name'],
            'id'           => $this->_arrForumData['forum_name']['forum_id'],
            'first_class'  => $this->_arrForumData['dir']['level_1_name'],
            'second_class' => $this->_arrForumData['dir']['level_2_name'],
            'forbid_flag'  => isset($arrUserPerm['block_type']) ? $arrUserPerm['block_type'] : 1,
            'is_readonly'  => isset($arrUserPerm['block_type']) && $arrUserPerm['block_type'] == 5 ? 1 : 0,
            'is_exists'    => isset($this->_arrForumData['forum_name']['forum_name']) ? true : false,
            'attrs'        => $this->_arrForumData['attrs'], //透传吧属性
            'is_like'      => isset($arrUserPerm['grade']['is_like']) ? intval($arrUserPerm['grade']['is_like']) : 0,
        );

        return $arrForumRes;
    }

    private function _integrateUser()
    {
        $arrUserInfo = $this->_arrUserData['all_forum_user'];
        $arrUserPerm = $this->_arrUserData['perm'];
        $arrUserMask = $this->_arrUserData['mask'];
        $arrUserRole = $this->_arrUserData['role'];

        $arrUserRes = array();
        if ($this->_intUserId <= 0) {//未登录处理
            $arrUserRes = array(
                'is_login'     => false,
                'id'           => 0,
                'name'         => '',
                'sid'          => '',
                'no_un'        => false,
                'type'         => 0,
                'meizhi_level' => -1,
                'power'        => array(
                    'delete'         => false, 'vip_complain' => false, 'ban_id' => false, 'ban_ip' => false, 'top' => false,
                    'good'           => false, 'post' => false, 'user_manager' => false, 'idisk_manager' => false,
                    'topic'          => false, 'topic_pic' => false, 'user_perms' => false, 'user_roles' => false,
                    'user_post_perm' => false, 'forever_ban' => 0
                ),
            );
            return $arrUserRes;
        }

        $strUserName = Molib_Util_Encode::convertGBKToUTF8($this->_strUserName);
        $nameShow    = !empty($arrUserInfo[$this->_intUserId]['user_nickname'])
            ? $arrUserInfo[$this->_intUserId]['user_nickname']
            : $strUserName;
        if (!empty($arrUserInfo[$this->_intUserId]['profession_manager_nick_name'])) {
            $nameShow = $arrUserInfo[$this->_intUserId]['profession_manager_nick_name'];
        }

        $arrUserRes = array(
            'is_login'      => true,
            'id'            => $this->_intUserId,
            'name'          => $strUserName,
            'sid'           => Tieba_Session_Socket::getSessionId(),
            'no_un'         => (boolean)Tieba_Session_Socket::getNo_un(), //无username相关
            'type'          => $arrUserInfo[$this->_intUserId]['user_type'] > 0 ? 2 : 1,
            'portrait'      => Tieba_Ucrypt::encode(intval($this->_intUserId), Molib_Util_Encode::convertUTF8ToGBK($strUserName), intval($arrUserInfo[$this->_intUserId]['portrait_time'])),
            'name_show'     => $nameShow,
            'level_id'      => $arrUserPerm['grade']['level_id'],
            'level_name'    => $arrUserPerm['grade']['level_name'],
            'cur_score'     => $arrUserPerm['grade']['cur_score'],
            'is_like'       => $arrUserPerm['grade']['is_like'],
            'outer_id'      => $arrUserInfo[$this->_intUserId]['outer_id'],
            'all_user_info' => $arrUserInfo[$this->_intUserId],
        );

        //mask接口获取userhide
        $arrUserRes['userhide'] = $arrUserMask[0]['anti_browse'];

        //power构建
        $arrPower                   = array();
        $arrPower['delete']         = $arrUserRole['is_forum_manager'] || $arrUserRole['is_forum_assist'] || $arrUserRole['is_forum_pm'] || $arrUserRole['is_forum_post_deleter'];
        $arrPower['vip_complain']   = $arrUserRole['is_forum_vip'] || $arrUserRole['is_forum_vip2'];
        $arrPower['ban_id']         = $arrUserRole['is_forum_manager'] || $arrUserRole['is_forum_assist'] || $arrUserRole['is_forum_pm'];
        $arrPower['ban_ip']         = $arrUserRole['is_forum_manager'] || $arrUserRole['is_forum_pm'];
        $arrPower['top']            = $arrUserRole['is_forum_manager'] || $arrUserRole['is_forum_pm'];
        $arrPower['good']           = $arrUserRole['is_forum_manager'] || $arrUserRole['is_forum_pm'];
        $arrPower['user_manager']   = $arrUserRole['is_forum_pm'];
        $arrPower['idisk_manager']  = $arrUserRole['is_forum_disk_editor'] || $arrUserRole['is_forum_manager'] || $arrUserRole['is_forum_pm'];
        $arrPower['topic']          = false;//是否有设置话题贴权限,无线无此功能
        $arrPower['topic_pic']      = false;//是否有设置话题贴权限,无线无此功能
        $arrPower['user_perms']     = $arrUserPerm['perm'];
        $arrPower['user_roles']     = $arrUserRole;
        $arrPower['user_post_perm'] = array(
            'can_post'   => $arrUserPerm['perm']['can_post'],
            'block_type' => $arrUserPerm['block_type'],
        );
        // $arrPower['forever_ban'] = $arrCupidConfig['forever_ban']; TODO:永久封禁
        $arrUserRes['power'] = $arrPower;
        return $arrUserRes;
    }

    private function _getCoreInfo()
    {
        $this->_objMultiCall = new Tieba_Multi("floor_core_multi");
        $this->_getCoreInput();
        foreach ($this->_arrMultiService as $strServiceKey => $arrServiceInfo) {
            foreach ($arrServiceInfo as $strMethod => $arrServiceParams) {
                $arrInput = array(
                    'serviceName' => $strServiceKey,
                    'method'      => $strMethod,
                    'ie'          => 'utf-8',
                    'input'       => $arrServiceParams,
                );
                $this->_objMultiCall->register($strMethod, new Tieba_Service($strServiceKey), $arrInput);
            }
        }

        $this->_objMultiCall->call();

        // 获取结果
        $res = $this->_buildCoreResp();
        return $res;
    }

    private function _getCoreInput()
    {
        $this->_arrMultiService = array();

        // 获取用户数据
        $this->_arrMultiService['user']['mgetUserForumInfo'] = array(
            'user_id'  => $this->_arrUserIds,
            'forum_id' => $this->_intForumId,
        );

        $this->_arrMultiService['user']['mgetUserData'] = array(
            'user_id'  => $this->_arrUserIds,
            'get_icon' => self::USER_ICON_SIZE,
            'data_tpl' => 'user_client_multi_tpl',
        );

        if ($this->_intUserId > 0) {
            $this->_arrMultiService['perm']['getRole']         = array(
                'forum_id' => 0 === $this->_intForumId ? 24981790 : $this->_intForumId,
                'user_id'  => $this->_intUserId,
                'user_ip'  => $this->_intIp,
            );
            $this->_arrMultiService['perm']['getPerm']         = array(
                'forum_id' => 0 === $this->_intForumId ? 24981790 : $this->_intForumId,
                'user_id'  => $this->_intUserId,
                'user_ip'  => $this->_intIp,
            );

            $this->_arrMultiService['anti']['antiTbmaskQuery'] = array(
                'req' => array(
                    'id_list'   => array($this->_intUserId),
                    'mask_list' => array('anti_browse'), //abstract_hide 是否隐藏用户发表的内容; anti_browse 查询用户是否被屏蔽
                    'strMethod' => 'tbmask_query',
                ),
            );
        }

        // 获取吧数据
        if ($this->_intForumId > 0) {
            $this->_arrMultiService['forum']['mgetBtxInfoEx'] = array(
                'forum_id' => array($this->_intForumId),
            );
        }
        if ($this->_arrThreads['is_multi_pb'] && !empty($this->_arrThreads['forum_id_shared'])) {
            $this->_arrMultiService['forum']['getFnameByFid'] = array(
                'forum_id' => $this->_arrThreads['forum_id_shared'],
            );
        }
    }

    private function _buildCoreResp()
    {
        $arrAllResults = $this->_objMultiCall->results;

        // 校验返回
        foreach ($arrAllResults as $serviceName => $info) {
            if (false === $info || Tieba_Errcode::ERR_SUCCESS !== intval($info['errno'])) {
                Bingo_Log::warning('call' . $serviceName . ' fail input[' . serialize($this->_arrMultiService['post']['getCommentSrchByThreadId']) . ']' . 'out[' . serialize($info) . ']');
                $this->_error(Util_Errno::INTERNAL_ERROR, Util_Error::INTERNAL_ERROR);
                return false;
            }
        }

        // 构建人信息
        $this->_arrUserData['all_forum_user'] = $arrAllResults['mgetUserForumInfo']['user_info'];
        $this->_arrUserData['all_post_user']  = $arrAllResults['mgetUserData']['user_info'];
        if ($this->_intUserId > 0) {
            $this->_arrUserData['role'] = $arrAllResults['getRole']['output']['role'];
            $this->_arrUserData['perm'] = $arrAllResults['getPerm']['output'];
            $this->_arrUserData['mask'] = $arrAllResults['antiTbmaskQuery']['res']['res_list'];
        }

        // 构建贴子信息
        $this->_arrThreadData = $this->_coreThreadBuild($this->_arrThreads, $this->_arrUserData['all_forum_user'], $this->_arrUserData['all_post_user']);

        // 构建吧信息
        $this->_arrForumData = $arrAllResults['mgetBtxInfoEx']['output'][$this->_intForumId];

        return true;
    }

    private function _coreThreadBuild($arrThread, $arrUserForumInfo, $arrUserData, $isShowUserIcon = 0, $intUserIconLimit = 0)
    {
        $arrVoicePid = array();
        $arrPostList = array();
        $arrSubList  = array();
        $arrList     = $arrThread['post_infos'];
        $intTid      = $arrThread['thread_id'];
        if (!is_array($arrList)) {
            return array();
        }

        foreach ($arrList as $key => $item) {
            $tmp = array(
                'id'               => $item['post_id'],
                'title'            => $item['title'],
                'floor'            => $item['post_no'],
                'time'             => $item['now_time'],
                'content'          => $item['content'],
                'lbs_info'         => $item['lbs_info'],
                'is_vote'          => $item['is_vote'],
                'ptype'            => $item['ptype'], //贴子类型标识
                'is_voice'         => 1 === intval($item['ptype']) ? 1 : 0,  // 是否是语音贴
                'content_resourse' => $item['content_resourse'],
                'add_first_floor'  => $item['add_first_floor'],
                'agree_num'        => $item['agree_num'],//加入每一层的点赞数
            );
            if ($key === 0 && isset($arrThread['third_app_info'])) {
                $tmp['thread_type']    = $arrThread['thread_type'];
                $tmp['third_app_info'] = $arrThread['third_app_info'];
            }

            if ($tmp['is_voice']) {
                $arrVoicePid[] = array('thread_id' => $intTid, 'post_id' => $tmp['id']);
            }
            // 无标题贴标题清空
            if ($tmp['floor'] == 1 && $item['is_ntitle'] == 1) {
                $tmp['title'] = '';
            }

            if (isset($item['vote_id'])) {
                $tmp['vote_id'] = $item['vote_id'];
            }

            // 转贴，保留原贴id
            if (isset($item['from_thread_id'])) {
                $tmp['from_thread_id'] = intval($item['from_thread_id']);
            }

            //wap视频URL优化相关的处理
            $arrVideo = array();
            if (isset($item['res_url'])) {
                $wapVideo = $item['res_url'];
                $counter  = count($wapVideo);
                for ($indicator = 0; $indicator < $counter; $indicator++) {
                    $arrVideo[$indicator] = $wapVideo[$indicator];
                }
            }

            $tmp['arr_video'] = $arrVideo;
            if (isset($item['user_id'])) {//匿名用户没有id
                // 如果是职业化吧主就显示 马甲
                $intPortraitTime              = intval($arrUserForumInfo[$item['user_id']]['portrait_time']);
                $userNickName                 = $arrUserForumInfo[$item['user_id']]['user_nickname'];
                $strProfessionManagerNickName = self::getProfessionManagerName($arrUserForumInfo, $item['user_id']);
                $nameShow                     = strlen($strProfessionManagerNickName) > 0 ? $strProfessionManagerNickName : $userNickName;
                $tmp['author']                = array(
                    'name'        => $item['username'],
                    'id'          => $item['user_id'],
                    'portrait'    => Tieba_Ucrypt::encode(intval($item['user_id']), Molib_Util_Encode::convertUTF8ToGBK($item['username']), $intPortraitTime),
                    'name_show'   => !empty($nameShow) ? $nameShow : $item['username'],
                    'type'        => $arrUserForumInfo[$item['user_id']]['user_type'] > 0 ? 2 : 1,
                    'level_id'    => $arrUserForumInfo[$item['user_id']]['level_id'],
                    'level_name'  => $arrUserForumInfo[$item['user_id']]['level_name'],
                    'is_like'     => $arrUserForumInfo[$item['user_id']]['is_like'] == 1 ? true : false,
                    'mParr_props' => $arrUserForumInfo[$item['user_id']]['mParr_props'],
                );
                if (Tieba_Util::isAnonymousUserName($item['username'])) {
                    $arrTmp                = explode(".", $item['username']);
                    $arrTmp[3]             = "*";
                    $strName               = implode(".", $arrTmp);
                    $tmp['author']['name'] = $strName;
                }
                $tmp['author']['is_verify'] = intval($arrUserForumInfo[$item['user_id']]['user_type']) > 0 ? true : false;

                if ($isShowUserIcon) {
                    if ($arrUserData[$item['user_id']]['iconinfo']) {
                        $tmp['author']['iconinfo'] = array_slice($arrUserData[$item['user_id']]['iconinfo'], 0, $intUserIconLimit);
                    } else {
                        $tmp['author']['iconinfo'] = array();
                    }
                }
                //新用户icon
                if ($isShowUserIcon) {
                    if ($arrUserData[$item['user_id']]['new_iconinfo']) {
                        $tmp['author']['new_iconinfo'] = array_slice($arrUserData[$item['user_id']]['new_iconinfo'], 0, $intUserIconLimit);
                    } else {
                        $tmp['author']['new_iconinfo'] = array();
                    }
                }
                //vip信息
                if ($arrUserData[$item['user_id']]['vipInfo']) {
                    $tmp['author']['vipInfo'] = $arrUserData[$item['user_id']]['vipInfo'];
                } else {
                    $tmp['author']['vipInfo'] = array();
                }
            } else {//匿名用户需要单独处理
                $tmp['author'] = array(
                    'ip'        => $item['ip'],
                    'type'      => 0,
                    'name_show' => $item['username'],
                    'name'      => $item['username'],
                );
            }

            //Brandzone nick name
            if (!empty($arrThread['firstUsr']) && !empty($arrThread['nickName']) && $arrThread['firstUsr'] == $item['username']) {
                $tmp['author']['name_show'] = $arrThread['nickName'];
            }

            //获取楼中楼列表
            $arrPostList[] = $tmp;

            if (isset($item['comment_num']) && $item['comment_num'] > 0 && is_array($item['comment_info'])) {//含有楼中楼
                $intFloorNum                  = count($item['comment_info']);
                $arrSubList[$item['post_id']] = array(
                    'pid'          => $item['post_id'],
                    'total_num'    => $item['comment_num'],
                    'total_count'  => $item['comment_num'],
                    'current_page' => 1, //字段不正确，win8统一解析使用，其他端不需要
                    'total_page'   => 1, //字段不正确，win8统一解析使用，其他端不需要
                );
                //处理楼中楼
                $subTmp = array();
                foreach ($item['comment_info'] as $sub) {
                    $subTmp                   = array(
                        'id'       => $sub['comment_id'],
                        'content'  => $sub['content'],
                        'time'     => $sub['now_time'],
                        'ptype'    => $sub['ptype'], //语音贴标识
                        'is_voice' => 1 === intval($sub['ptype']) ? 1 : 0  // 是否是语音贴
                    );
                    $userNickName             = $arrUserForumInfo[$sub['user_id']]['user_nickname'];
                    $subProfessionManagerName = self::getProfessionManagerName($arrUserForumInfo, $sub['user_id']);
                    $subNameShow              = strlen($subProfessionManagerName) > 0 ? $subProfessionManagerName : $userNickName;
                    $subTmp['author']         = array(
                        'name'      => $sub['username'],
                        'id'        => $sub['user_id'],
                        'portrait'  => Tieba_Ucrypt::encode(intval($sub['user_id']), Molib_Util_Encode::convertUTF8ToGBK($sub['username'])),
                        'name_show' => !empty($subNameShow) ? $subNameShow : $sub['username'],
                    );

                    //新用户icon
                    if ($isShowUserIcon) {
                        if ($arrUserData[$sub['user_id']]['new_iconinfo']) {
                            $subTmp['author']['new_iconinfo'] = array_slice($arrUserData[$sub['user_id']]['new_iconinfo'], 0, $intUserIconLimit);
                        } else {
                            $subTmp['author']['new_iconinfo'] = array();
                        }
                        if (empty($subTmp['author']['new_iconinfo'][0])) {
                            unset($subTmp['author']['new_iconinfo'][0]);
                        }

                    }

                    //vip信息
                    if ($arrUserData[$sub['user_id']]['vipInfo']) {
                        $subTmp['author']['vipInfo'] = $arrUserData[$sub['user_id']]['vipInfo'];
                    } else {
                        $subTmp['author']['vipInfo'] = array();
                    }

                    //匿名用户处理
                    if (Tieba_Util::isAnonymousUserName($subTmp['author']['name'])) {
                        $arrTmp                        = explode(".", $subTmp['author']['name']);
                        $arrTmp[3]                     = "*";
                        $strName                       = implode(".", $arrTmp);
                        $subTmp['author']['name']      = $strName;
                        $subTmp['author']['name_show'] = $strName;
                    }
                    if ($subTmp['is_voice']) {
                        $arrVoicePid[] = array('thread_id' => $intTid, 'post_id' => $subTmp['id']);
                    }
                    $arrSubList[$item['post_id']]['sub_post_list'][] = $subTmp;
                }
            }//楼中楼列表处理结束
        }
        if (!empty($arrThread['post_infos'][0]['video_info'])) {
            Tieba_Video_Process::execute($arrThread['post_infos'][0]['video_info'], '', Tieba_Video_Process::REQUEST_FROM_CLIENT);
        }
        $arrPost = array(
            'thread'        => $arrThread,
            'extra'         => $this->_arrThreadInfo,
            'voice_pids'    => $arrVoicePid,
            'post_list'     => $arrPostList,
            'sub_post_list' => $arrSubList,
        );
        return $arrPost;
    }

    /**
     *
     * @param  [type] $arrUserForum [帖子里涉及的人吧对应]
     * @param  [type] $userId       [用户id]
     * @return [type]               [职业化吧主的马甲]
     */
    public static function getProfessionManagerName($arrUserForumInfo, $userId)
    {
        if (empty($userId)) {
            return '';
        }
        if (empty($arrUserForumInfo)) {
            return '';
        }
        if (empty($arrUserForumInfo[$userId])) {
            return '';
        }
        $strProfessionManagerName = $arrUserForumInfo[$userId]['profession_manager_nick_name'];
        return strlen($strProfessionManagerName) > 0 ? $strProfessionManagerName : '';
    }

    /**
     * @return array
     */
    private function _getThreadsInfo()
    {
        $this->_objMultiCall = new Tieba_Multi("floor_posts_multi");

        $this->_getThreadsInput();
        foreach ($this->_arrMultiService as $strServiceKey => $arrServiceInfo) {
            foreach ($arrServiceInfo as $strMethod => $arrServiceParams) {
                $arrInput = array(
                    'serviceName' => $strServiceKey,
                    'method'      => $strMethod,
                    'ie'          => 'utf-8',
                    'input'       => $arrServiceParams,
                );
                $this->_objMultiCall->register($strMethod, new Tieba_Service($strServiceKey), $arrInput);
            }
        }

        $this->_objMultiCall->call();

        // 获取结果
        $res = $this->_getThreadsResp();
        return $res;
    }

    private function _getThreadsInput()
    {
        // 获取楼中楼信息
        $this->_arrMultiService['post']['getCommentSrchByThreadId'] = array(
            'thread_id'   => $this->_intTid,
            'post_id'     => $this->_intPid,
            'comment_id'  => 0,
            'res_num'     => 30,
            'see_author'  => 0,
            'has_comment' => 1,
            'has_mask'    => 1,
            'has_ext'     => 1,
            'uids'        => array(
                $this->_intUserId,
            ),
        );

        // 获取贴子信息
        $this->_arrMultiService['post']['mgetThread'] = array(
            'thread_ids'      => array(
                0 => $this->_intTid,
            ),
            'need_abstract'   => 0,
            'forum_id'        => 0,
            'need_photo_pic'  => 1,
            'need_user_data'  => 0,
            'icon_size'       => 0,
            'need_forum_name' => 1, //是否获取吧名
            'call_from'       => 'client_frs' //上游模块名
        );

        if ($this->_intUserId > 0) {
            // 获取楼层点赞信息
            $this->_arrMultiService['agree']['getAgreeByUserIdAndPostIds'] = array(
                'user_id'   => $this->_intUserId,
                'thread_id' => $this->_intTid,
                'post_ids'  => array($this->_intPid),
            );

            // 获取贴子点赞信息
            $this->_arrMultiService['agree']['getAgreeByUserIdAndThreadId'] = array(
                'user_id'   => $this->_intUserId,
                'thread_id' => $this->_intTid,
            );
        }
    }

    private function _getThreadsResp()
    {
        $arrAllResults   = $this->_objMultiCall->results;
        $arrCommentsInfo = $arrAllResults['getCommentSrchByThreadId'];
        if (false === $arrCommentsInfo || Tieba_Errcode::ERR_SUCCESS !== intval($arrCommentsInfo['errno'])) {
            Bingo_Log::warning('call post:getCommentSrchByThreadId fail input[' . serialize($this->_arrMultiService['post']['getCommentSrchByThreadId']) . ']' . 'out[' . serialize($arrCommentsInfo) . ']');
            $this->_error(Util_Errno::INTERNAL_ERROR, Util_Error::INTERNAL_ERROR);
            return false;
        }

        $arrThreadRes = $arrAllResults['mgetThread'];
        if (false === $arrCommentsInfo || Tieba_Errcode::ERR_SUCCESS !== intval($arrCommentsInfo['errno'])) {
            Bingo_Log::warning('call post:mgetThread fail input[' . serialize($this->_arrMultiService['post']['mgetThread']) . ']' . 'out[' . serialize($arrCommentsInfo) . ']');
            $this->_error(Util_Errno::INTERNAL_ERROR, Util_Error::INTERNAL_ERROR);
            return false;
        }

        // 判断是否是2017年贴子
        $this->_arrThreadInfo = $arrThreadRes['output']['thread_list'][$this->_intTid];
        $this->_strFirstPic   = $this->_arrThreadInfo['media'][0]['big_pic'];

        $this->_intForumId                = $this->_arrThreadInfo['forum_id'];
        $this->_arrThreads                = $arrCommentsInfo['output']['output'][0];
        $this->_arrThreads['agree_num']   = $this->_arrThreadInfo['agree_num'];
        $this->_arrThreads['create_time'] = $this->_arrThreadInfo['create_time'];

        foreach ($this->_arrThreads['post_infos'] as $key => $value) {
            if ($value['post_id'] != $this->_intPid) {
                unset($this->_arrThreads['post_infos'][$key]);
            }
        }
        $this->_arrThreads['post_infos'] = array_values($this->_arrThreads['post_infos']);
        // 获取userid 列表
        if ($this->_intUserId > 0) {
            $this->_arrUserIds[] = $this->_intUserId;
        }
        // 主楼用户id
        $this->_arrUserIds[] = $this->_arrThreads['first_post_userid'];
        foreach ($this->_arrThreads['post_infos'] as $key1 => $info) {
            $this->_arrUserIds[] = $info['user_id'];
            foreach ($info['comment_info'] as $key2 => $comments) {
                $this->_arrUserIds[] = $comments['user_id'];
            }
        }
        $this->_arrUserIds = array_unique($this->_arrUserIds);

        // 获取点赞信息
        $arrThreadAgreeInfo = $arrAllResults['getAgreeByUserIdAndThreadId'];
        if (false === $arrThreadAgreeInfo || Tieba_Errcode::ERR_SUCCESS !== intval($arrThreadAgreeInfo['errno'])) {
            Bingo_Log::warning('call post:mgetThread fail input[' . serialize($this->_arrMultiService['agree']['getAgreeByUserIdAndThreadId']) . ']' . 'out[' . serialize($arrThreadAgreeInfo) . ']');
        } else {
            $this->_arrThreadAgreeInfo = array(
                'agree_num' => isset($this->_arrThreadInfo['agree_num']) ? $this->_arrThreadInfo['agree_num'] : 0,
                'has_agree' => !empty($arrThreadAgreeInfo['data']['list']) ? 1 : 0,
            );
        }

        $arrPostAgreeInfo = $arrAllResults['getAgreeByUserIdAndPostIds'];
        if (false === $arrPostAgreeInfo || Tieba_Errcode::ERR_SUCCESS !== intval($arrPostAgreeInfo['errno'])) {
            Bingo_Log::warning('call post:mgetThread fail input[' . serialize($this->_arrMultiService['agree']['getAgreeByUserIdAndPostIds']) . ']' . 'out[' . serialize($arrPostAgreeInfo) . ']');
        }

        $this->_arrPostAgreeInfo = $arrPostAgreeInfo['data'];

        return true;
    }

    private function _log()
    {
        Tieba_Stlog::addNode('post_id_reply_num', $this->_arrThreadData['post_list'][0]['comment_num']);
        Tieba_Stlog::addNode('first_class', $this->_arrForumData['dir']['level_1_name']);
        Tieba_Stlog::addNode('fname', $this->_arrForumData['forum_name']['forum_name']);
        Tieba_Stlog::addNode('fid', $this->_arrForumData['forum_name']['forum_id']);
        Tieba_Stlog::addNode('tid', $this->_intTid);
        Tieba_Stlog::addNode('pid', $this->_intPid);
        Tieba_Stlog::addNode('pn', 1);
        return true;
    }
}

