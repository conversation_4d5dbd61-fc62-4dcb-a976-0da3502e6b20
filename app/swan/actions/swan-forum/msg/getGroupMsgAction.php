<?php
    /**
     * 获取用户消息列表 回复、@、点赞、官方账号、运营消息
     * Created by PhpStorm.
     * User: heliuyong
     * Date: 2019/2/28
     * Time: 3:54 PM
     */
class getGroupMsgAction extends Base_Action
{

    const TIEBA_OP_CHOSEN_ID = 1501754229; //贴吧精选账号

    private $_intUserId = 0;
    private $_strCuid = '';
    private $_arrGroupMid = array();
    private $_hasGroupMsg = 0;

    // 判断是否请求获取群组消息
    private $_bolNeedGroupMsg = true;

    private $_intErrno = 0;
    private $_arrOutData = array();

    /**
     * @return array
     */
    public function _getPrivateInfo() {
        $this->_objRequest->setStrategy(array('check_nonu' => false,'check_sign'=>false));
        $arrPrivateInfo = array(
            'group_mids'        => $this->_getInput('group_mids'),
            'has_group_msg'     => $this->_getInput('has_group_msg', 0),
        );
        return $arrPrivateInfo;
    }

    /**
     * @return bool
     */
    public function _checkPrivate() {
        $this->_hasGroupMsg = $this->_objRequest->getPrivateAttr('has_group_msg');
        $this->_arrGroupMid = $this->_objRequest->getPrivateAttr('group_mids');

        return true;
    }

    /**
     * [_execute description]
     * @return [type] [description]
     */
    protected function _execute()
    {
        $this->_intUserId = $this->_objRequest->getCommonAttr('user_id');
        // 获取群组
        if ($this->_hasGroupMsg) {
            if (!$this->_getGroupInfo()) {
                return false;
            }
        }
        // 查询群组消息
        if (!$this->_getMsgMulti()) {
            return false;
        }

        $this->_objResponse->setOutData($this->_arrOutData);
    }

    /**
     * 获取群组相关信息
     * @return bool
     */
    private function _getGroupInfo() {

        $objMulti = new Tieba_Multi('im_getGroupInfo_mult');

        // 获取贴吧主端的cuid
        $arrUidsInput = array(
            'serviceName' => 'msgpush',
            'method' => 'mgetCuidsByUids',
            'ie' => 'utf-8',
            'input' => array(
                'uids' => $this->_intUserId,
            ),
        );
        $objMulti->register('im_getCuids', new Tieba_Service('im'), $arrUidsInput);

        // 如果上报群组消息字段为空 重新获取用户群组
        if (empty($this->_arrGroupMid)) {
            $arrGroupInput = array(
                'serviceName' => 'im',
                'method' => 'queryGroupsByUidNew',
                'ie' => 'utf-8',
                'input' => array(
                    'user_id' => $this->_intUserId,
                    'need_pmsg' => 0,
                ),
            );
            $objMulti->register('im_getGroups', new Tieba_Service('im'), $arrGroupInput);
        }

        $objMulti->call();

        // 根据uid获取cuid
        $arrCuidsOut = $objMulti->getResult('im_getCuids');
        if (false == $arrCuidsOut || $arrCuidsOut['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal("call getGroupMsg failed.  req:". serialize($arrUidsInput) . "   res:" . serialize($arrCuidsOut));
            $this->_intErrno = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            return false;
        }
        if (!empty($arrCuidsOut['users'])) {
            $intUpdateTime = 0;
            $strCuid = '';
            foreach ($arrCuidsOut['users'] as $item) {
                if ($item['update_time'] > $intUpdateTime) {
                    $intUpdateTime = $item['update_time'];
                    $strCuid = $item['cuid'];
                }
            }
            $this->_strCuid = $strCuid;
        }

        // 获取用户群组
        if (empty($this->_arrGroupMid)) {
            $arrOut = $objMulti->getResult('im_getGroups');
            if (!empty($arrOut['groups'])) {
                foreach ($arrOut['groups'] as $groupInfo) {
                    if ($groupInfo['group_type'] == 30) { // 贴吧精选群组消息
                        $this->_arrGroupMid[] = array(
                            'group_id' => $groupInfo['group_id'],
                            'last_msg_id' => 0,
                        );
                    }
                }
            }
        }


        return true;
    }

    /**
     * 并行获取群信息 以及回复at粉丝消息
     * @return bool
     */
    private function _getMsgMulti()
    {
        $objMulti = new Tieba_Multi('im_getGroupMsg_mult');
//        $this->_bolNeedGroupMsg = $this->_hasGroupMsg && !empty($this->_strCuid) && !empty($this->_arrGroupMid);
        $this->_bolNeedGroupMsg = $this->_hasGroupMsg && !empty($this->_arrGroupMid);

        // 获取群组消息
        if ($this->_bolNeedGroupMsg) {
            $arrGroupMsgReq = array(
                'cuid' => $this->_strCuid,
                'reqs' => $this->_arrGroupMid,
            );
            $arrMultiInput = array(
                'serviceName' => 'im',
                'method' => 'getGroupMsg',
                'ie' => 'utf-8',
                'input' => $arrGroupMsgReq,
            );
            $objMulti->register('im_getGroupMsg', new Tieba_Service('im'), $arrMultiInput);
        }

        // 获取回复、@、点赞消息
        $arrCommonReq = array(
            'intUserId' => $this->_intUserId,
        );
        $arrMultiInput = array(
            'serviceName' => 'im',
            'method' => 'getCommonMsg',
            'ie' => 'utf-8',
            'input' => $arrCommonReq,
        );
        $objMulti->register('im_getCommonMsg', new Tieba_Service('im'), $arrMultiInput);


        Bingo_Timer::start('multi_msg');
        $objMulti->call();
        Bingo_Timer::end('multi_msg');

        // 获取群组消息
        if ($this->_bolNeedGroupMsg) {
            $arrGroupMsgRes = $objMulti->getResult('im_getGroupMsg');
            if (false == $arrGroupMsgRes || $arrGroupMsgRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::fatal("call getGroupMsg failed.  req:" . serialize($arrGroupMsgReq) . "   res:" . serialize($arrGroupMsgRes));
            }
        }

        // 获取提醒消息
        $arrCommonMsgRes = $objMulti->getResult('im_getCommonMsg');
        if (false == $arrCommonMsgRes || $arrCommonMsgRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal("call getCommonMsg failed. req:" . serialize($arrCommonReq) . "   res:" . serialize($arrCommonMsgRes));
        }

        $this->_arrOutData = array(
            'no'    => Tieba_Errcode::ERR_SUCCESS,
            'errno' => 'success',
            'data'  => array(
                'remind_msg' => $this->_buildRemindMsg($arrCommonMsgRes['msg_list']['msg_list']),
                'group_msg' => $this->_buildGroupMsg($arrGroupMsgRes['msg_list']),
            ),
        );

//        Bingo_Log::warning(var_export($this->_arrOutData, true));

        return true;
    }

    /**
     * 构造群组消息list
     * @param $arrGroupMsg
     * @return array
     */
    private function _buildGroupMsg($arrGroupMsg) {

        $arrGroupMsgInfo = array();
        if (empty($arrGroupMsg)) {
            return $arrGroupMsgInfo;
        }

        foreach ($arrGroupMsg as $intGroupId => $arrGroupMsgList) {
            $arrGroupInfo = array(
                'group_id' => $intGroupId,
            );
            $arrMsgList = array();
            foreach ($arrGroupMsgList as $arrMsgInfo) {
                if ($arrMsgInfo['user_id'] != self::TIEBA_OP_CHOSEN_ID) {
                    continue;
                }
                $arrContent = Bingo_String::json2array($arrMsgInfo['content']);
                $arrContentList = array();
                foreach ($arrContent as $contentInfo) {
                    $arrContentList[] = array(
                        'title' => trim($contentInfo['title']),
                        'abstract' => trim($contentInfo['text']),
                        'img' => trim($contentInfo['src']),
                        'link' => trim($contentInfo['url']),
                    );
                }
                $arrTempMsg = array(
                    'msg_id' => $arrMsgInfo['msg_id'],
                    'msg_type' => $arrMsgInfo['msg_type'],
                    'user_id' => $arrMsgInfo['user_id'],
                    'create_time' => $arrMsgInfo['create_time'],
                    'contents' => $arrContentList,
                );
                $arrMsgList[] = $arrTempMsg;
            }

            $arrGroupMsgInfo[] = array(
                'group_info' => $arrGroupInfo,
                'msg_list' => $arrMsgList,
            );
        }

        return $arrGroupMsgInfo;
    }

    /**
     * 构造提醒消息
     * @param $arrCommonMsg
     * @return array
     */
    private function _buildRemindMsg($arrCommonMsg) {

        $arrRemindMsg = array();
        if (empty($arrCommonMsg)) {
            return $arrRemindMsg;
        }

        $arrCommonMsgInfo = Bingo_String::json2array($arrCommonMsg[0]['content']);
        $arrRemindMsg = array(
            'reply_me' => intval($arrCommonMsgInfo['replyme']),
            'at_me' => intval($arrCommonMsgInfo['atme']),
            'agree_me' => intval($arrCommonMsgInfo['agree']),
        );
        return $arrRemindMsg;
    }
}

