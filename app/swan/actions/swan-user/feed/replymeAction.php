<?php
/**
 * Created by PhpStorm.
 * User: pang<PERSON><PERSON><PERSON>
 * Date: 2019/8/13
 * Time: 下午2:46
 */

class replymeAction extends Base_Action
{
    const VOICE_SHOW_TEXT = 2; // 显示语音为文本
    const POST_FACESHOP_URL_PREFIX = "http://static.tieba.baidu.com/tb/editor/images/faceshop";
    const ZAN_POSTLIST_OFFSET = 10;
    const COVER_PIC_SPEC = 'whcrop=164,164';

    private $arrReplyFeedList = array();
    private $has_more = false;
    private $arrUnreadMsg = array();
    private $arrProThread = array();
    private $arrMessage = array();
    private $arrPage = array();


    // 回复来源
    private $arrPostFrom = array(
        1 => '来自首页关注',
        2 => '来自首页推荐',
    );

    private $_rn = 10;
    private $_pn = 1;
    private $_intUserId = 0;
    private $_objMulti = null;

    /**
     * @return array
     */
    public function _getPrivateInfo()
    {
        $arrPrivateInfo = array(
            'pn'        => intval($this->_getInput('pn', 1)),
            'nick_name' => strval($this->_getInput('nick_name', '')),
        );
        return $arrPrivateInfo;
    }

    /**
     * @return bool
     */
    public function _checkPrivate()
    {
        if (!$this->_objRequest->getCommonAttr('login')) {
            Bingo_Log::warning("user not login");
            $this->_error(Util_Errno::USER_NOT_LOGIN, Util_Error::USER_NOT_LOGIN);
            return false;
        }

        $this->_pn        = $this->_objRequest->getPrivateAttr('pn');
        $this->_intUserId = $this->_objRequest->getCommonAttr('user_id');
        return true;
    }

    /**
     * [_execute description]
     * @return [type] [description]
     */
    protected function _execute()
    {
        // 处理
        $this->_process();

        // 构建返回
        $this->_buildResp();
        return;
    }

    /**
     * [_process description]
     * @return [type] [description]
     *
     */
    private function _process()
    {
        $this->_coreExecute();
        $this->_extensionExecute();
        $this->_addVForumId();
        $this->_processReplyList();
        return;
    }

    /**
     * 组装
     * @return [type] [description]
     */
    private function _buildResp()
    {
        $data   = array(
            'reply_list' => $this->arrReplyFeedList,
            'message'    => $this->arrMessage,
            'has_more'   => $this->has_more,
            'user'       => Util_Service_Wap::getTplUser($this->_objRequest),
        );
        $arrRes = array(
            'no'    => 0,
            'error' => 'success',
            'data'  => $data,
        );
        $this->_objResponse->setOutData($arrRes);
        return;
    }

    /**
     * @param void
     * @return boolean
     */
    private function _coreExecute()
    {
        //获取动态
        $this->_objMulti = new Tieba_Multi('replyme_core_multi');
        $this->_getFeed();
        //获取消息
        $this->_getMessage();
        //清消息
        $this->_clearMessage();

        Bingo_Timer::start('core_call');
        $this->_objMulti->call();
        Bingo_Timer::end('core_call');

        $this->_processClearMsg();

        $this->_processReplymeList();

        $this->_processGetMessage();

        unset($this->_objMulti);
    }

    /**
     * @param void
     * @return boolean
     */
    private function _extensionExecute()
    {
        $this->_objMulti = new Tieba_Multi('replyme_core_multi');

        if (!empty($this->arrUnreadMsg)) {
            $this->_setReadStatus();
        }

        if (!empty($this->arrReplyFeedList)) {

            Bingo_Timer::start('get_post_list_voice');
            $this->_getPostListVoice();
            Bingo_Timer::end('get_post_list_voice');

            Bingo_Timer::start('use_portrait');
            $this->_getUserPortrait();
            Bingo_Timer::end('use_portrait');
        }


        Bingo_Timer::start('extension_call');
        $this->_objMulti->call();
        Bingo_Timer::end('extension_call');


        //获取贴子类型
        Bingo_Timer::start('pre_process');
        $this->_preProcess(); //处理语音和大表情
        Bingo_Timer::end('pre_process');

        // 处理已读设置结果
        if (!empty($this->arrUnreadMsg)) {
            $this->_procReadStatus();
        }

        // 对消息进行二次处理
        Bingo_Timer::start('process_reply_feed');
        $this->_processReplyFeed();
        Bingo_Timer::end('process_reply_feed');

        unset($this->_objMulti);
    }

    /**
     * 设置已读
     * @param
     * @return bool
     */
    private function _setReadStatus()
    {
        $arrInput = array(
            'input' => $this->arrUnreadMsg,
        );
        $Request  = array(
            'serviceName' => 'post',
            'method'      => 'setReadReplyRemind',
            'input'       => $arrInput,
            'ie'          => 'utf-8',
        );
        $this->_objMulti->register('set_read_status', new Tieba_Service('post'), $Request);
    }

    /**
     * 设置已读状态结果
     * @param
     * @return
     */
    private function _procReadStatus()
    {
        $arrOutput = $this->_objMulti->getResult('set_read_status');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
            Bingo_Log::warning('call post setReadReplyRemind failed.');
        }
    }

    /**
     * 获取信息流数据
     * @return boolean 是否成功
     */
    private function _getFeed()
    {
        $intRn     = $this->_rn;
        $intOffset = ($this->_pn - 1) * $intRn;
        $arrParams = array(
            "user_id" => $this->_intUserId,
            "offset"  => intval($intOffset),
            "limit"   => intval($intRn),
            "group"   => 0,
        );

        $arrInput['serviceName'] = 'post';
        $arrInput['method']      = 'queryReplyRemind';
        $arrInput['input']       = $arrParams;
        $arrInput['ie']          = 'utf-8';

        $this->_objMulti->register('get_feed', new Tieba_Service('post'), $arrInput);
        return true;
    }

    //获取图文直播帖子信息

    /**
     * @param arrThreadIds
     * @return
     */
    private function _processTwZhiBoThread($arrThreadIds)
    {
        $arrZhiBoInfos = array();
        $input         = array(
            "thread_ids"      => $arrThreadIds,
            'forum_id'        => 0,
            "need_abstract"   => 0,
            "need_photo_pic"  => 0,
            "need_user_data"  => 0,
            "need_forum_name" => 1,
            'call_from'       => 'client_frs',
        );

        $arrOut = Molib_Tieba_Service::call('post', 'mgetThread', $input);
        if ($arrOut != false && $arrOut['errno'] == Tieba_Errcode::ERR_SUCCESS) {
            $arrThreadList = $arrOut['output']['thread_list'];
            //图文直播搬贴改变帖子类型为图文直播贴
            Molib_Util_ThreadFilter::setThreadTypesByCopyTwZhiBo($arrThreadList);
            $arrTwZhiBoThreadIds = array();
            foreach ($arrThreadList as $threadId => $threadInfo) {
                // 获取帖子类型
                $arrTypes = Tieba_Type_Thread::getTypeArray($threadInfo['thread_types']);
                if (isset($arrTypes['is_twzhibo_thread'])) { // 图文直播
                    $arrTwZhiBoThreadIds[] = $threadId;
                }
                if (isset($threadInfo['is_pro_thread'])) {
                    $this->arrProThread[$threadId] = $threadInfo['is_pro_thread'];
                }
            }
            if (count($arrTwZhiBoThreadIds) > 0) {
                foreach ($arrTwZhiBoThreadIds as $threadId) {
                    $arrZhiBoInfos[$threadId] = array(
                        "thread_id"           => $threadId,
                        "livecover_src"       => $arrThreadList[$threadId]['livecover_src'], // 封面图
                        "livecover_src_bsize" => "",
                        "title"               => $arrThreadList[$threadId]['title'],
                        "forum_name"          => $arrThreadList[$threadId]['forum_name'],
                        "forum_id"            => $arrThreadList[$threadId]['forum_id'],
                        "last_modified_time"  => $arrThreadList[$threadId]['last_modified_time'],
                        "thread_types"        => $arrThreadList[$threadId]['thread_types'],
                        "content"             => "",
                    );
                }
            }
        }
        return $arrZhiBoInfos;
    }

    /**
     * @param void
     * @return boolean
     */
    private function _processReplymeList()
    {
        $arrResponse = $this->_objMulti->getResult('get_feed');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrResponse['errno']) {
            $this->_error($arrResponse['errno'], Tieba_Error::getErrmsg($arrResponse['errno']));
            $intRn     = $this->_rn;
            $intOffset = ($this->_pn - 1) * $intRn;
            $arrParams = array(
                "user_id" => $this->_intUserId,
                "offset"  => intval($intOffset),
                "limit"   => intval($intRn),
                "group"   => 0,
            );
            Bingo_Log::fatal('Call post service queryReplyRemind func failed [input:' . serialize($arrParams) . '] output:[' . serialize($arrResponse) . ']');
            return false;
        }
        $arrResponse      = $arrResponse['data'];
        $arrReplyFeedList = array();

        $arrZhiBoInfos = array();
        // 处理图文直播信息
        $arrThreadIds = array();
        foreach ($arrResponse['replys'] as $arrReplyFeed) {
            $arrThreadIds[] = $arrReplyFeed['thread_id'];
        }
        $arrZhiBoInfos = $this->_processTwZhiBoThread($arrThreadIds);

        foreach ($arrResponse['replys'] as $arrReplyFeed) {
            $intId        = intval($arrReplyFeed['user_id']);
            $strName      = strval($arrReplyFeed['user_name']);
            $portrait     = Tieba_Ucrypt::encode($intId, $strName);
            $intQuoteId   = intval($arrReplyFeed['quote_uid']);
            $strQuoteName = strval($arrReplyFeed['quote_uname']);
            // repost系列字段，是回复楼中楼时， 楼中楼postid的相关信息
            // 此举解决，在回复楼中楼的情况下，应显示 "回复了我的评论:[被回复的楼中楼的内容]"
            if ($arrReplyFeed['repostid'] > 0) {
                $arrReplyFeed['quote_post_id'] = $arrReplyFeed['repostid'];
                $arrReplyFeed['quote_uname']   = $arrReplyFeed['repost_uname'];
                $arrReplyFeed['quote_uid']     = $arrReplyFeed['repost_uid'];
                $arrReplyFeed['quote_content'] = $arrReplyFeed['repost_content'];
                $arrReplyFeed['quote_post_no'] = $arrReplyFeed['repost_post_no'];
            }

            // v9.3上线回复列表已读未读状态
            // 2018-01-24 00:00:00前所有消息默认为已读
            if (intval($arrReplyFeed['time']) < 1516723200) {
                $arrReplyFeed['flag'] = 0;
            } else if (1 === $arrReplyFeed['flag']) {
                // 未读消息记录下来，调用setReadReplyRemind接口设置为已读
                $this->arrUnreadMsg[] = array(
                    'user_id'   => $this->_intUserId,
                    'thread_id' => intval($arrReplyFeed['thread_id']),
                    'post_id'   => intval($arrReplyFeed['post_id']),
                );
            }

            $arrReplyFeed                  = array(
                'thread_uid'    => $arrReplyFeed['thread_uid'],
                'product_id'    => $arrReplyFeed['product_id'],
                'thread_id'     => $arrReplyFeed['thread_id'],
                'post_id'       => $arrReplyFeed['post_id'],
                'replyer'       => array(
                    'id'       => $intId,
                    'name'     => $strName,
                    'portrait' => $portrait,
                ),
                'title'         => $arrReplyFeed['title'],
                'fname'         => $arrReplyFeed['forum_name'],
                'unread'        => $arrReplyFeed['flag'],
                'content'       => $arrReplyFeed['digest'],
                'quote_user'    => array(
                    'id'       => $intQuoteId,
                    'name'     => $strQuoteName,
                    'portrait' => Tieba_Ucrypt::encode($intQuoteId, $strQuoteName),
                ),
                'quote_pid'     => $arrReplyFeed['quote_post_id'],
                'quote_content' => $arrReplyFeed['quote_content'],
                'time'          => $arrReplyFeed['time'],
                'face_urls'     => $arrReplyFeed['face_urls'],
                'image_urls'    => $arrReplyFeed['image_urls'],
                'graffiti_urls' => $arrReplyFeed['graffiti_urls'],
                'video_urls'    => $arrReplyFeed['video_urls'],
                'music_urls'    => $arrReplyFeed['music_urls'],
            );
            $arrReplyFeed['is_pro_thread'] = empty($this->arrProThread[$arrReplyFeed['thread_id']]) ? 0 : $this->arrProThread[$arrReplyFeed['thread_id']];
            if (isset($arrZhiBoInfos[$arrReplyFeed['thread_id']])) {
                $arrReplyFeed['thread_type']  = Tieba_Type_Thread::TWZHIBO_THREAD;
                $arrReplyFeed['thread_types'] = $arrZhiBoInfos[$arrReplyFeed['thread_id']]['thread_types'];
                $arrReplyFeed['twzhibo_info'] = $arrZhiBoInfos[$arrReplyFeed['thread_id']];
            }
            $arrReplyFeedList[] = $arrReplyFeed;
        }

        $this->arrReplyFeedList = $arrReplyFeedList;
        $this->has_more         = $arrResponse['has_more'];
        return true;
    }


    /**
     * @param void
     * @return boolean
     */
    private function _processClearMsg()
    {
        $ret = $this->_objMulti->getResult('clear_message');
        if (false == $ret || Tieba_Errcode::ERR_SUCCESS != $ret['errno']) {
            Bingo_Log::warning('clear message_http replyme msg fail.ret:[' . serialize($ret) . ']');
        }
    }

    /**
     * @param void
     * @return boolean
     */
    private function _processGetMessage()
    {
        $strOut = $this->_objMulti->getResult('get_message');
        if (false === $strOut || empty($strOut)) {
            Bingo_Log::fatal('call messagepool error. out:[' . $strOut . ']');
            return false;
        }

        $arrMessage            = $strOut['data'];
        $arrReturn['fans']     = intval($arrMessage[1]);
        $arrReturn['evaluate'] = $arrMessage[2];
        $arrReturn['money']    = $arrMessage[3];
        $arrReturn['replyme']  = intval($arrMessage[4]);
        $arrReturn['feature']  = $arrMessage[5];
        $arrReturn['guess']    = $arrMessage[6];
        $arrReturn['anti']     = $arrMessage[8];
        $arrReturn['atme']     = intval($arrMessage[9]);
        $arrReturn['recycle']  = intval($arrMessage[10]);
        $arrReturn['zan']      = intval($arrMessage[20]);

        $arrOut = $this->_objMulti->getResult('get_query_store_num');
        if ($arrOut === false || $arrOut['errno'] != 0) {
            Bingo_Log::warning('get_query_store_num failed');
            return true;
        }

        $arrReturn['storethread'] = intval($arrOut['output']['num']);
        $this->arrMessage         = $arrReturn;
        return true;
    }

    /**
     * 获取用户信息
     * @param void
     * @return
     */
    private function _getUserPortrait()
    {
        $arrUserList = array();
        foreach ($this->arrReplyFeedList as $item) {
            $arrUserList[] = intval($item['replyer']['id']);
        }
        if (empty($arrUserList)) {
            return true;
        }
        $arrInput['serviceName']      = 'user';
        $arrInput['method']           = 'mgetUserData';
        $arrInput['input']['user_id'] = $arrUserList;
        $arrInput['ie']               = 'utf-8';
        $this->_objMulti->register('get_user_portrait', new Tieba_Service('user'), $arrInput);

        /**
         * 8.9 粉丝数据
         */
        $arrInput = array(
            'user_id'     => $this->_intUserId,
            'req_user_id' => $arrUserList,
        );
        $Request  = array(
            'serviceName' => 'user',
            'method'      => 'getUserFollowedInfo',
            'input'       => $arrInput,
            'ie'          => 'utf-8',
        );
        $this->_objMulti->register('get_is_fans', new Tieba_Service('friend'), $Request);

        return true;
    }


    /**
     * @param void
     * @return boolean
     */
    private function _getPostListVoice()
    {
        $arrPids = array();
        foreach ($this->arrReplyFeedList as $arrReplyFeed) {
            $arrPids[] = $arrReplyFeed['post_id'];
            //判断回复类型
            if ($arrReplyFeed['quote_pid'] > 0) {
                $arrPids[] = $arrReplyFeed['quote_pid'];
            }
        }
        if (empty($arrPids)) {
            return false;
        }
        $arrInput['serviceName']       = 'post';
        $arrInput['method']            = 'getPostInfo';
        $arrInput['input']['post_ids'] = $arrPids;
        $arrInput['ie']                = 'utf-8';
        $this->_objMulti->register('get_post_list_voice', new Tieba_Service('post'), $arrInput);
        return true;
    }


    /**
     * @param void
     * @return boolean
     */
    private function _preProcess()
    {
        $arrUserInfo = $this->_objMulti->getResult('get_user_portrait');
        if (null == $arrUserInfo) {//如果返回null， 说明没有进行Muti::register()操作
            return false;
        }

        $arrFriend = $this->_objMulti->getResult('get_is_friend');

        if (isset($arrFriend['data']['friend_info'])) {
            $arrFriend = $arrFriend['data']['friend_info'];
        } else {
            $arrFriend = array();
        }


        /**
         * 8.9 粉丝数据
         */
        $arrFansList = array();
        $arrRet      = $this->_objMulti->getResult('get_is_fans');
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call replymeAction::getUserFollowedInfo failed. [output = %s]", serialize($arrRet)));
        }
        if (isset($arrRet['res_user_infos'])) {
            foreach ($arrRet['res_user_infos'] as $arrVal) {
                $arrFansList[$arrVal['user_id']] = $arrVal['is_followed'];
            }
        }

        foreach ($this->arrReplyFeedList as $intKey => $arrItem) {

            $this->arrReplyFeedList[$intKey]['replyer']['portrait'] = Tieba_Ucrypt::encode($arrItem['replyer']['id'], $arrItem['replyer']['name'], intval($arrUserInfo['user_info'][$arrItem['replyer']['id']]['portrait_time']));

            $user_id = $arrItem['replyer']['id'];

            // god信息
            $arrUser = isset($arrUserInfo['user_info'][$user_id]) && is_array($arrUserInfo['user_info'][$user_id]) ?
                $arrUserInfo['user_info'][$user_id] : array();

            // Bingo_Log::warning(var_export($arrUser['god'], true));
            if (!empty($arrUser['god']) && 5 == $arrUser['god']['status']) {
                $arrGodFids                                             = explode(",", $arrUser['god']['fids']);
                $intGodFid                                              = intval($arrGodFids[0]);
                $this->arrReplyFeedList[$intKey]['replyer']['god_data'] = array(
                    'intro' => '',
                    'type'  => $arrUser['god']['type'],
                    'fid'   => $intGodFid,
                );
            }


            if (isset($arrFriend[$user_id]) && isset($arrFriend[$user_id]['is_friend'])) {
                if ($arrFriend[$user_id]['is_friend'] !== 1) {
                    $is_freind = 0;
                } else {
                    $is_freind = 1;
                }
                $this->arrReplyFeedList[$intKey]['replyer']['is_friend'] = $is_freind;
            } else {
                $this->arrReplyFeedList[$intKey]['replyer']['is_friend'] = 0;
            }

            /**
             * 8.9 粉丝数据
             */
            $this->arrReplyFeedList[$intKey]['replyer']['is_fans'] = intval($arrFansList[$user_id]);
        }

        $arrOut = $this->_objMulti->getResult('get_post_list_voice');
        if (isset($arrOut['output'])) {
            $arrPostsType = $arrOut['output'];
        }
        foreach ($arrPostsType as $arrItem) {
            foreach ($this->arrReplyFeedList as &$arrTmp) {
                if ($arrTmp['post_id'] == $arrItem['post_id']) {
                    //回复的内容
                    if ($arrItem['ptype'] == 1) {
                        $arrTmp['content'] .= '[语音]';
                    }
                    //大表情
                    $arrTmp['content'] = $arrTmp['content'] . $this->_processSmile3($arrItem['content']);

                    // 来源
                    $arrTmp['post_from'] = intval($arrItem['post_from']);
                    break;
                } elseif ($arrTmp['quote_pid'] == $arrItem['post_id']) {
                    //回复我的贴子的内容
                    if ($arrItem['ptype'] == 1) {
                        $arrTmp['quote_content'] .= '[语音]';
                    }
                    //大表情
                    $arrTmp['content'] = $arrTmp['content'] . $this->_processSmile3($arrItem['content']);
                    break;
                }
            }
        }
        return true;
    }


    /**
     * @param void
     * @return boolean
     */
    private function _processReplyFeed()
    {
        if (empty($this->arrReplyFeedList)) {
            return true;
        }

        //获取所有表情替换字符
        $arrClassicSmileTransData = Molib_Conf_Smile2TextData::$arrClassicSmileTransData;
        $arrSmile2TextTransData   = Molib_Conf_Smile2TextData::$arrSmile2TextTransData;

        $arrUserIds = array();
        foreach ($this->arrReplyFeedList as $arrReplyFeed) {
            $arrUserIds[]   = $arrReplyFeed['replyer']['id'];
            $arrUserIds[]   = $arrReplyFeed['quote_user']['id'];
            $arrReplyMeList = array();
            //过滤i贴吧的提醒
            if ($arrReplyFeed['product_id'] != 1) {
                continue;
            }
            //判断回复类型
            $arrReplyMeList['is_floor'] = 0;
            if ($arrReplyFeed['quote_pid'] > 0) {//回复的是楼层
                $arrReplyMeList['is_floor'] = 1;

                $arrReplyMeList['type'] = 1;//回复我的贴子

            } else {//回复的是主题, 此时我收到消息，必然是回复的我发的主题
                $arrReplyMeList['type'] = 2;//回复我的主题
            }


            //贴子内容处理转换
            $this->_convertPost($arrReplyMeList, $arrReplyFeed, $arrClassicSmileTransData, $arrSmile2TextTransData);

            $arrReplyMeList['thread_id']   = strval($arrReplyFeed['thread_id']);
            $arrReplyMeList['post_id']     = strval($arrReplyFeed['post_id']);
            $arrReplyMeList['time']        = $arrReplyFeed['time'];
            $arrReplyMeList['fname']       = $arrReplyFeed['fname'];
            $arrReplyMeList['quote_pid']   = $arrReplyFeed['quote_pid'];
            $arrReplyMeList['thread_type'] = isset($arrReplyFeed['thread_type']) ? $arrReplyFeed['thread_type'] : 0;
            $arrReplyMeList['unread']      = $arrReplyFeed['unread'];

            // 回复来源
            $arrReplyMeList['post_from'] = isset($this->arrPostFrom[$arrReplyFeed['post_from']]) ? $this->arrPostFrom[$arrReplyFeed['post_from']] : '';


            $arrReplyList[] = $arrReplyMeList;
        }

        $this->arrReplyFeedList = $arrReplyList;

        if (!empty($arrUserIds)) {
            $arrUserIds = array_unique($arrUserIds);
            $arrParam   = array(
                'user_id' => $arrUserIds,
            );
            $arrRet     = Tieba_Service::call('user', 'mgetUserData', $arrParam, null, null, 'post', 'php', 'utf-8');
            if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
                return;
            }
            foreach ($this->arrReplyFeedList as &$arrReplyInfo) {
                if (!empty($arrRet['user_info'][$arrReplyInfo['replyer']['id']]['user_nickname'])) {
                    $arrReplyInfo['replyer']['name_show'] = $arrRet['user_info'][$arrReplyInfo['replyer']['id']]['user_nickname'];
                }
                if (!empty($arrRet['user_info'][$arrReplyInfo['quote_user']['id']]['user_nickname'])) {
                    $arrReplyInfo['quote_user']['name_show'] = $arrRet['user_info'][$arrReplyInfo['quote_user']['id']]['user_nickname'];
                }
            }
        }

        if (empty($this->arrReplyFeedList) && $this->_arrPager['has_more']) {
            $this->arrPage['has_more'] = false;
        }
    }


    /**
     * @param void
     * @return boolean
     */
    private function _convertPost(&$arrReplyMeList, $arrReplyFeed, &$arrClassicSmileTransData, &$arrSmile2TextTransData)
    {
        $arrReplyMeList['replyer']    = Molib_Util_Format::formatUser($arrReplyFeed['replyer']);
        $arrReplyMeList['quote_user'] = Molib_Util_Format::formatUser($arrReplyFeed['quote_user']);
        $strTitle                     = $arrReplyFeed ['title'];
        $intLen                       = mb_strlen($strTitle, 'UTF-8');
        if ($intLen > 24) {
            $strTitle = mb_substr($strTitle, 0, 24, 'UTF-8') . "...";
        }
        $strTitle                 = html_entity_decode($strTitle, ENT_COMPAT, 'UTF-8');
        $arrReplyMeList ['title'] = $strTitle;

        $strContent = $arrReplyFeed ['content'];

        $boolCut = false;
        $intLen  = mb_strlen($strContent, 'UTF-8');
        if ($intLen > 80) {
            $strContent = mb_substr($strContent, 0, 80, 'UTF-8');
            $boolCut    = true;
        }

        $strContent                 = html_entity_decode($strContent, ENT_COMPAT, 'UTF-8');
        $arrReplyMeList ['content'] = $strContent;

        $strQuoteContent = $arrReplyFeed ['quote_content'];
        if ($arrReplyMeList['type'] == 1) {//回复我的帖子 ，增加富文本处理，add by llm
            $strQuoteContent = $this->_processQuoteContent($strQuoteContent, $arrClassicSmileTransData, $arrSmile2TextTransData);
        }
        $intLen = mb_strlen($strQuoteContent, 'UTF-8');
        if ($intLen > 80) {
            $strQuoteContent = mb_substr($strQuoteContent, 0, 80, 'UTF-8') . "...";
        }
        $strQuoteContent                  = html_entity_decode($strQuoteContent, ENT_COMPAT, 'UTF-8');
        $arrReplyMeList ['quote_content'] = $strQuoteContent;

        $needReplaceFaceText = true;
        if (isset ($arrReplyFeed ['face_urls'])) {
            foreach ($arrReplyFeed ['face_urls'] as $strFaceUrl) {
                if (!strncmp(self::POST_FACESHOP_URL_PREFIX, $strFaceUrl, strlen(self::POST_FACESHOP_URL_PREFIX))) {
                    //大表情，跳过
                    continue;
                }
                $len         = strrpos($strFaceUrl, '.') - strrpos($strFaceUrl, '/') - 1;
                $strFaceName = substr($strFaceUrl, strrpos($strFaceUrl, '/') + 1, $len);
                if (isset ($arrClassicSmileTransData [$strFaceName])) {
                    $strFaceName = $arrClassicSmileTransData [$strFaceName];
                }
                if (isset ($arrSmile2TextTransData [$strFaceName])) {
                    $strFaceName = $arrSmile2TextTransData [$strFaceName];
                }

                $arrReplyMeList ['content'] .= $needReplaceFaceText ? '#(' . $strFaceName . ')' : '[' . $strFaceName . ']';
            }
        }
        if (isset ($arrReplyFeed ['image_urls'])) {
            foreach ($arrReplyFeed ['image_urls'] as $strImgUrl) {
                $arrReplyMeList ['content'] .= '[图片]';
            }
        }
        if (isset($arrReplyFeed['graffiti_urls'])) {
            foreach ($arrReplyFeed ['graffiti_urls'] as $strGrafUrl) {
                $arrReplyMeList ['content'] .= '[涂鸦]';
            }
        }
        if (isset ($arrReplyFeed ['video_urls'])) {
            foreach ($arrReplyFeed ['video_urls'] as $strVideoUrl) {
                $arrReplyMeList ['content'] .= '[视频]';
            }
        }
        if (isset ($arrReplyFeed ['music_urls'])) {
            foreach ($arrReplyFeed ['music_urls'] as $strMusicUrl) {
                $arrReplyMeList ['content'] .= '[音乐]';
            }
        }
        if ($boolCut == true) {
            $arrReplyMeList ['content'] .= '...';
        }
    }

    //处理引用帖子富文本，add by llm

    /**
     * @param void
     * @return boolean
     */
    private function _processQuoteContent($strContent, $arrClassicSmileTransData, $arrSmile2TextTransData)
    {
        $parserCondition                  = new Molib_Util_RichTextParserCondition();
        $parserCondition->intNewLineCount = 1;
        $objParser                        = new Molib_Util_RichTextParser();
        $objResult                        = $objParser->process($parserCondition, $strContent);
        $arrContent                       = $objResult->arrContent;
        if (empty($arrContent)) {
            return $strContent;
        } else {
            $strProcessedContent = '';
            $needReplaceFaceText = true;
            foreach ($arrContent as $slot) {
                if ($slot['type'] == Molib_Util_RichTextParser::SLOT_TYPE_SMILE) {//表情
                    $strFaceName = '';
                    if (isset ($arrClassicSmileTransData [$slot['text']])) {
                        $strFaceName = $needReplaceFaceText ? '#(' . $arrClassicSmileTransData [$slot['text']] . ')' : '[' . $arrClassicSmileTransData [$slot['text']] . ']';
                    }
                    if (isset ($arrSmile2TextTransData [$slot['text']])) {
                        $strFaceName = $needReplaceFaceText ? '#(' . $arrSmile2TextTransData [$slot['text']] . ')' : '[' . $arrSmile2TextTransData [$slot['text']] . ']';
                    }
                    if ('' == $strFaceName) {
                        $strFaceName = "[表情]";
                    }
                    $strProcessedContent .= $strFaceName;
                } else if ($slot['type'] == Molib_Util_RichTextParser::SLOT_TYPE_IMG) {//图片
                    $strProcessedContent .= "[图片]";
                } else if ($slot['type'] == Molib_Util_RichTextParser::SLOT_TYPE_AT) {//@某人
                    $strProcessedContent .= $slot['text'];
                } else if ($slot['type'] == Molib_Util_RichTextParser::SLOT_TYPE_TEXT) {//文本
                    $strProcessedContent .= $slot['text'];
                } else if ($slot['type'] == Molib_Util_RichTextParser::SLOT_TYPE_LINK) {//链接
                    $strProcessedContent .= "[网址]";
                } else if ($slot['type'] == Molib_Util_RichTextParser::SLOT_TYPE_BDHD) {//视频
                    $strProcessedContent .= "[视频]";
                } else if ($slot['type'] == Molib_Util_RichTextParser::SLOT_TYPE_EMBED) {//flash
                    $strProcessedContent .= "[视频]";
                } else if ($slot['type'] == Molib_Util_RichTextParser::SLOT_TYPE_SMILE3) {//表情商店新表情
                    $strProcessedContent .= "[" . $slot['c'] . "]";
                } else if ($slot['type'] == Molib_Util_RichTextParser::SLOT_TYPE_GRAFFITI) {//涂鸦
                    $strProcessedContent .= "[涂鸦]";
                }
            }
            return $strProcessedContent;
        }
    }


    /**
     * @param void
     * @return boolean
     */
    private function _clearMessage()
    {
        $intUid              = $this->_intUserId;
        $arrParam['user_id'] = $intUid;
        $arrParam['type']    = Molib_Client_Define::REPLYME_MESSAGE_TYPE;
        $arrInput            = array(
            'serviceName' => 'messagepool',
            'method'      => 'clearMsg',
            'input'       => $arrParam,//实际要传入的参数
        );
        $obj                 = new Tieba_Service('messagepool');
        $this->_objMulti->register('clear_message', $obj, $arrInput);
    }


    /**
     * @param void
     * @return boolean
     */
    private function _getMessage()
    {
        $arrParam['user_id'] = $this->_intUserId;
        $arrInput            = array(
            'serviceName' => 'messagepool',
            'method'      => 'getMsg',
            'input'       => $arrParam,//实际要传入的参数
        );
        $obj                 = new Tieba_Service('messagepool');
        $this->_objMulti->register('get_message', $obj, $arrInput);


        $arrInput                     = array();
        $arrInput['serviceName']      = 'post';
        $arrInput['method']           = 'queryStoreUpdateNum';
        $arrInput['input']['user_id'] = $this->_intUserId;
        $arrInput['ie']               = 'utf-8';
        $this->_objMulti->register('get_query_store_num', new Tieba_Service('post'), $arrInput);

        return true;
    }


    /**
     * @param void
     * @return boolean
     */
    private function _processSmile3($strContent)
    {
        $parserCondition                  = new Molib_Util_RichTextParserCondition();
        $parserCondition->intNewLineCount = 1;
        $objParser                        = new Molib_Util_RichTextParser();
        $objResult                        = $objParser->process($parserCondition, $strContent);
        $arrContent                       = $objResult->arrContent;
        if (empty($arrContent)) {
            return $strContent;
        } else {
            $strProcessedContent = '';
            foreach ($arrContent as $slot) {
                if ($slot['type'] == Molib_Util_RichTextParser::SLOT_TYPE_SMILE3) {//表情商店新表情
                    $strProcessedContent .= "[" . $slot['c'] . "]";
                }
            }
            return $strProcessedContent;
        }
    }


    /**
     * @param void
     * @return boolean
     */
    private function _processReplyList()
    {
        //处理故事贴类型
        $data = $this->arrReplyFeedList;
        foreach ($data as $k => $thread) {
            $threadIds[] = $thread['thread_id'];
        }
        $threads = $this->getThreads($threadIds, 1, 1);
        foreach ($threads as $index => $item) {
            if (intval($item['original_tid']) > 0) {
                $arrOriginTids[]                    = $item['original_tid'];
                $arrTidToOrigin[$item['thread_id']] = $item['original_tid'];
            }
        }
        if (!empty($arrOriginTids)) {
            $arrThreadInfo = Molib_Util_ShareThread::getOriginThreadInfo($arrOriginTids);
        }
        foreach ($data as $key => $thread) {
            $thread_id                    = $thread['thread_id'];
            $arrTypes                     = Tieba_Type_Thread::getTypeArray($threads[$thread_id]['thread_types']);
            $arrMedia                     = self::procThreadMedia($threads[$thread_id]['media']);
            $data[$key]['thread_type']    = $threads[$thread_id]['thread_types'] >> 32;
            $data[$key]['forum_id']       = $threads[$thread_id]['forum_id'];
            $data[$key]['thread_img_url'] = '';
            if (2 == $data[$key]['type']) {
                $data[$key]['thread_img_url'] = $arrMedia['small_pic'];
            }
            if (isset($arrTypes['is_story_pic_thread'])) {
                $data[$key]['is_story'] = 1;
                $data[$key]['title']    = '[图片]';
            }
            if (isset($arrTypes['is_story_video_thread'])) {
                $data[$key]['is_story'] = 1;
                $data[$key]['title']    = '[视频]';
            }
            if (isset($arrTypes['is_rethread'])) {
                $data[$key]['is_share_thread'] = 1;
            }

            $data[$key]['origin_thread_info'] = empty($arrThreadInfo[$arrTidToOrigin[$thread_id]]) ? array() : $arrThreadInfo[$arrTidToOrigin[$thread_id]];
            if (!empty($arrTidToOrigin[$thread_id])) {
                $data[$key]['origin_thread_info']['tid'] = $arrTidToOrigin[$thread_id];
            }

            $data[$key]['thread_content'] = array(
                'title' => $thread['title'],
            );
            $imgurl                       = !empty($threads[$thread_id]['video_info']['thumbnail_url']) ? $threads[$thread_id]['video_info']['thumbnail_url'] : $threads[$thread_id]['media'][0]['big_pic'];
            if (!empty($imgurl)) {
                $data[$key]['thread_content']['img'] = $this->_getSpecImageUrl($imgurl, self::COVER_PIC_SPEC);
            }

            $data[$key]['thread_author_user'] = array(
                'id'       => $threads[$thread_id]['user_id'],
                'portrait' => Tieba_Ucrypt::encode($threads[$thread_id]['user_id'], $threads[$thread_id]['user_name']),
            );
        }
        $this->arrReplyFeedList = $data;
    }

    /**
     * 裁剪图片
     * @param $url
     * @param $strSpec
     * @return mixed|string
     */
    private function _getSpecImageUrl($url, $strSpec)
    {
        $picSign = Util_Richtext_TransCoderConvertor::getPicSignFromUrl($url);
        $str     = Util_Richtext_TransCoderConvertor::picSign2Url($picSign, $strSpec);
        return $str;
    }

    /**
     * [_addVForumId description]
     * input
     * return
     */
    private function _addVForumId()
    {
        //增加来源吧
        $data            = $this->arrReplyFeedList;
        $this->_objMulti = new Tieba_Multi('replyme_core_multi_v_forumid');
        if (true) {

            foreach ($data as $k => $thread) {
                $arrPostIdList[] = isset($thread['quote_pid']) ? $thread['quote_pid'] : $thread['post_id'];
                $threadIds[]     = $thread['thread_id'];
                if ($thread['fname'] == Molib_Conf_Reversion::$noForumThreadAdd['forum_name']) {
                    $data[$k]['hide_fname'] = 1;
                }
            }
            $threads       = $this->getThreads($threadIds);
            $arrPostIdList = array_unique($arrPostIdList);
            $arrPostIdList = array_chunk($arrPostIdList, 50);

            foreach ($arrPostIdList as $key => $arrPostIds) {
                $inputIds['post_ids'] = $arrPostIds;
                $arrInput             = array(
                    'serviceName' => 'post',
                    'method'      => 'getPostInfo',
                    'ie'          => 'utf-8',
                    'input'       => $inputIds,
                );

                $this->_objMulti->register('getPostInfo' . $key, new Tieba_Service('post'), $arrInput);
            }
            $this->_objMulti->call();
            $times = count($arrPostIdList);
            for ($i = 0; $i < $times; $i++) {
                $arrOutput = $this->_objMulti->getResult('getPostInfo' . $i);
                if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('Call post:getPostInfo failed. input=]' . serialize($arrPostIdList) . '[Output=]' . serialize($arrOutput));
                }
                foreach ($arrOutput['output'] as $thread) {
                    $vForumList[$thread['post_id']] = $thread['v_forum_id'];
                }
            }

            if (!empty($vForumList)) {
                $currentUserId = $this->_intUserId;
                foreach ($data as $k => $thread) {
                    $threadId     = $thread['thread_id'];
                    $threadUserId = isset($threads[$threadId]['user_id']) ? $threads[$threadId]['user_id'] : 0;
                    //回复我的&点赞的
                    //如果是楼主展示全吧
                    if ($currentUserId != $threadUserId) {
                        if (isset($thread['quote_pid'])) {
                            $data[$k]['v_forum_id'] = $vForumList[$thread['quote_pid']];
                        } else {
                            $data[$k]['v_forum_id'] = $vForumList[$thread['post_id']];
                        }
                    }
                }
            }
        }
        $this->arrReplyFeedList = $data;
    }

    /**
     * @param $threadIds
     * @return array
     */
    private function getThreads($threadIds, $intNeedAbstract = 0, $intNeedUserData = 0)
    {
        if (!$threadIds || !is_array($threadIds)) {
            return array();
        }
        $input = array(
            "thread_ids"      => $threadIds,
            "need_abstract"   => intval($intNeedAbstract),
            "forum_id"        => 0,
            "need_photo_pic"  => 0,
            "need_user_data"  => $intNeedUserData,
            "icon_size"       => 0,
            "need_forum_name" => 0, //是否获取吧名
            "call_from"       => "client-user-feed" //上游模块名
        );
        $res   = Tieba_Service::call('post', 'mgetThread', $input, null, null, 'post', 'php', 'utf-8');

        if (!isset($res['errno']) || $res['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call post:mgetThread failed, input=' . serialize($input) . ',res=' . serialize($res));
            return array();
        }

        $threads = isset($res['output']['thread_list']) ? $res['output']['thread_list'] : array();
        $users   = isset($res['output']['thread_user_list']) ? $res['output']['thread_user_list'] : array();

        if (!empty($users)) {
            foreach ($threads as $key => $threadInfo) {
                $userId                     = $threadInfo['user_id'];
                $threads[$key]['user_name'] = isset($users[$userId]) ? $users[$userId]['user_name'] : '';
            }
        }
        return $threads;
    }

    /**
     * @param $arrMedia
     * @return array
     */
    public static function procThreadMedia($arrMedia)
    {
        $arrMediaResult = array();
        if (isset($arrMedia) && !empty($arrMedia)) {
            $arrMediaItem = $arrMedia[0];
            $arrSpec      = array(
                'pic_spec' => 'whfpf=360,360,50;q=80;g=0',
            );
            if ('pic' == $arrMediaItem['type']) {
                $arrMediaResult['type']      = Molib_Util_RichText_Parser::SLOT_TYPE_IMG;
                $arrMediaResult['small_pic'] = Molib_Util_PicUrl::getPicUrl($arrMediaItem['big_pic'], $arrSpec);
            } else if ('flash' == $arrMediaItem['type']) {
                $arrMediaResult['type']      = Molib_Util_RichText_Parser::SLOT_TYPE_EMBED;
                $arrMediaResult['small_pic'] = Molib_Util_PicUrl::getPicUrl($arrMediaItem['vpic'], $arrSpec);
            }
        }
        return $arrMediaResult;
    }
}