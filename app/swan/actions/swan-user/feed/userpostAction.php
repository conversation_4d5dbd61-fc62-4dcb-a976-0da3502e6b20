<?php
/**
 * Created by PhpStorm.
 * User: pang<PERSON><PERSON><PERSON>
 * Date: 2019/8/13
 * Time: 下午5:34
 */

class userpostAction extends Base_Action
{
    const PRODUCT_THREAD_ATTR_KEY = 'ecom_info';
    const IS_GOOD = 1;
    const IS_TOP = 2;
    const IS_VOTE = 4;
    const IS_BAKAN = 8;
    const IS_PIC = 16;
    const IS_VIDEO = 32;
    const IS_SMARTAPP_SHARE_THREAD = 62;
    const IS_FACE = 64;
    const IS_MUSIC = 128;
    const IS_PROTAL = 256;
    const IS_NOTITLE = 512;
    const IS_HASTITLE = 1024;
    //白名单用户可全局隐藏贴子
    const WORDLIST_HIDETHREAD_USER = 'tb_wordlist_redis_hideThread_User';

    const COVER_PIC_SPEC = 'whcrop=164,164';

    const HAS_AGREE = 1; // 点过顶或踩
    const NOT_AGREE = 0; // 没点过顶或踩

    const AGREE_TYPE_AGREE = 2; // 点顶
    const AGREE_TYPE_CAI = 5;   // 点踩

    private $isFrameChange = 0;
    private $arrUserInfo = array();
    private $arrAuthorInfo = array();
    private $isHidePost = false;  //是否隐藏动态
    private $arrPostList = array();
    private $arrIconInfo = array();
    private $intNeedPhotoPic = 1;
    private $strClientVersion = '';
    //private $intClientType = -1;
    private $strUserNickName = '';

    private $int_view_card_num = 0;

    //大改版 无吧发帖 默认吧信息
    private $defaultName = 'me0407';

    //用户隐私
    private $mask_type = 1;

    // 个人中心主态帖子未审核视频假上墙
    private $bolShowUnauditedVideoPostList = false;
    private $arrUnauditedVideoPostList = array();
    private $arrShareVideoThreadList = array();
    private $_intIsThread = 0;
    private $_intUserId = 0;
    private $_intPn = 1;
    private $_intRn = 10;
    private $_intSameDay = 0;
    private $_intBeginTime = 0;
    private $_intEndTime = 0;
    private $_strCallFrom = '';
    private $_objMulti = null;

    /**
     * @return array
     */
    public function _getPrivateInfo()
    {
        $arrPrivateInfo = array(
            'pn'         => intval($this->_getInput('pn', 1)),
            'rn'         => intval($this->_getInput('rn', 10)),
            'begin_time' => intval($this->_getInput('begin_time', 0)),
            'end_time'   => intval($this->_getInput('end_time', 0)),
            'is_thread'  => intval($this->_getInput('is_thread', 1)),
        );
        return $arrPrivateInfo;
    }

    /**
     * @return bool
     */
    public function _checkPrivate()
    {
        if (!$this->_objRequest->getCommonAttr('login')) {
            Bingo_Log::warning("user not login");
            $this->_error(Util_Errno::USER_NOT_LOGIN, Util_Error::USER_NOT_LOGIN);
            return false;
        }

        $this->_intRn        = $this->_objRequest->getPrivateAttr('rn');
        $this->_intPn        = $this->_objRequest->getPrivateAttr('pn');
        $this->_intIsThread  = $this->_objRequest->getPrivateAttr('is_thread');
        $this->_intBeginTime = $this->_objRequest->getPrivateAttr('begin_time');
        $this->_intEndTime   = $this->_objRequest->getPrivateAttr('end_time');
        $this->_intUserId    = $this->_objRequest->getCommonAttr('user_id');
        $this->_strCallFrom  = $this->_objRequest->getCommonAttr('call_from');

        return true;
    }

    /**
     * [_execute description]
     * @return [type] [description]
     */
    protected function _execute()
    {
        // 执行
        $this->_process();
        // 构建返回
        $this->_buildResp();
        return true;
    }

    /**
     * @param void
     * @return boolean
     */
    protected function _process()
    {
        $this->arrUserInfo = $this->_getUserInfo();
        if (!empty($this->arrUserInfo['user_nickname'])) {
            $this->strUserNickName = $this->arrUserInfo['user_nickname'];
        }

        $this->_objMulti = new Tieba_Multi('userpost_core_multi');

        $this->isFrameChange = 1;
        // 是否需要打开一键设置卡片  (0:不需要 1: 需要)
        $intViewCard = 0;

        // 用户是否设置全吧隐藏
        $isPriveSetsPost = false;
        if (isset($this->arrUserInfo['priv_sets']['post'])
            && $this->arrUserInfo['priv_sets']['post'] == 3) {
            $isPriveSetsPost = true;
        }

        if ($this->arrUserInfo['priv_sets']['post'] == 3) {//pm说 改成以pc为主
            $this->mask_type = 3;
        }

        /**
         * 9.0 全吧和贴问题
         */
        if (true === $this->isHidePost) {
            $this->isHidePost = false;
        }

        if ($this->isHidePost == false) {
            //获取他的贴子
            $arrUserPost = $this->_getUserPost();
            if ($arrUserPost != false) {
                //贴子对应的主题贴
                $arrThreadId = array();
                //语音贴
                $arrVoiceThread = array();
                $arrVoicePost   = array();

                $strYear     = '';
                $arrShareIds = array();

                $intTempItem = 1;
                foreach ($arrUserPost as &$onePost) {

                    if (!empty($onePost['thread_id'])) {
                        // 屏蔽ip
                        $onePost['ip']  = '';
                        $arrThreadId [] = $onePost['thread_id'];

                        if (1 === intval($onePost['ptype'])) {
                            if ($onePost['is_thread']) {
                                $arrVoiceThread [] = $onePost['thread_id'];
                            } else {
                                $arrVoicePost [] = array('thread_id' => $onePost['thread_id'], 'post_id' => $onePost['post_id']);
                            }
                        }

                        /*
                         * 隐藏数据状态状态
                         *
                         * 返回数据: hide_status, 0代表旧数据，1表示不隐藏，2代表隐藏
                         * 给NA: NA 标识 is_remain  帖子是否隐藏（0：非隐藏、1：隐藏)
                         */
                        if (($this->mask_type == 3 || $this->mask_type == 2) && $onePost['hide_status'] == 0) {
                            // 隐藏态
                            $onePost['is_remain'] = 1;
                        } else {
                            $onePost['is_remain'] = 0;
                            if ($onePost['hide_status'] == 2) {
                                $onePost['is_remain'] = 1;
                            }
                        }
                        unset($onePost['hide_status']);


                        // 年份
                        $onePost['is_view_year'] = 0;
                        $strTmp                  = date("Y", $onePost['create_time']);
                        if (!empty($strYear) && $strTmp !== $strYear) {
                            $onePost['is_view_year'] = 1;
                        }
                        $strYear = $strTmp;

                        /**
                         * 判断一键设置卡片要不要出现
                         * is_view_card 参数必须为 1 且 帖子创建时间必须大于等于  指定时间
                         */
                        if ($intViewCard == 1
                            && $this->int_view_card_num == 0
                            && $isPriveSetsPost == true
                            && $onePost['create_time'] <= 1511481600) {
                            $this->int_view_card_num = $intTempItem;
                        }
                    }


                    $intTempItem++;
                }

                $intIsThread     = $this->_intIsThread;
                $intNeedAbstract = $intIsThread;

                //获取主题帖详细信息
                if (count($arrThreadId) > 0) {
                    $arrThreadInfo = $this->_getThreadInfo($arrThreadId, $intNeedAbstract);
                    if ($arrThreadInfo === false) {
                        $this->_error(Tieba_Errcode::ERR_CLIENT_CALL_POST, Tieba_Error::getErrmsg(Tieba_Errcode::ERR_CLIENT_CALL_POST));
                        Bingo_Log::fatal('Call post:mgetThread failed. input=]' . serialize($arrThreadId) . '[Output=]' . serialize($arrThreadInfo));
                        return false;
                    }
                }

                // 处理分享贴数据 - ljm
                if (count($arrThreadInfo) > 0) {
                    foreach ($arrUserPost as &$onePost) {
                        if (isset($arrThreadInfo[$onePost['thread_id']])) {
                            $arr = Tieba_Type_Thread::getTypeArray($arrThreadInfo[$onePost['thread_id']]['thread_types']);
                            if (isset($arr['is_rethread'])
                                && true == $arr['is_rethread']
                                && intval($arrThreadInfo[$onePost['thread_id']]['original_tid']) > 0) {
                                $arrShareIds[] = $arrThreadInfo[$onePost['thread_id']]['original_tid'];
                            }
                        }
                    }
                    // 获取分享数据, 合并数据到 arrUserPost
                    if (count($arrShareIds) > 0) {
                        $arrShareInfo = Molib_Util_ShareThread::getOriginThreadInfo($arrShareIds);
                        foreach ($arrUserPost as &$onePost) {
                            if (intval($arrThreadInfo[$onePost['thread_id']]['original_tid']) > 0
                                && isset($arrShareInfo[$arrThreadInfo[$onePost['thread_id']]['original_tid']])) {
                                $onePost['origin_thread_info'] = $arrShareInfo[$arrThreadInfo[$onePost['thread_id']]['original_tid']];
                                $onePost['is_share_thread']    = 1;
                            }
                        }
                    }
                }

                //获取商品贴ID
                $productThreadId = array();
                if (!empty($arrThreadInfo)) {
                    foreach ($arrThreadInfo AS $item) {
                        if (!isset($item[self::PRODUCT_THREAD_ATTR_KEY])) {
                            continue;
                        }
                        $product_id = intval($item[self::PRODUCT_THREAD_ATTR_KEY]['product_id']);
                        if ($product_id <= 0) {
                            continue;
                        }
                        $productThreadId[] = intval($item['thread_id']);
                    }
                }
                if (!empty($productThreadId)) {
                    $arrUserPost = $this->_getProductThreadInfo($productThreadId, $arrUserPost);
                }

                //获取语音贴详细信息，合并到帖子信息中
                $arrVoiceData = array();
                if ($intIsThread === 1) {
                    if (count($arrVoiceThread) > 0) {
                        $arrVoiceData = $this->_getVoiceDataByTid($arrVoiceThread);
                        if (count($arrVoiceData) > 0) {
                            foreach ($arrUserPost as &$onePost) {
                                if (isset($onePost['thread_id']) && isset($arrVoiceData[$onePost['thread_id']])) {
                                    $onePost['voice_info'] = $arrVoiceData[$onePost['thread_id']];
                                }
                            }
                        }
                    }

                } else {
                    if (count($arrVoicePost) > 0) {
                        $arrVoiceData = $this->_getVoiceDataByPid($arrVoicePost);
                        if (count($arrVoiceData) > 0) {
                            foreach ($arrUserPost as &$onePost) {
                                if (isset($onePost['post_id']) && isset($arrVoiceData[$onePost['post_id']])) {
                                    $onePost['voice_info'] = $arrVoiceData[$onePost['post_id']];
                                }
                            }
                        }
                    }
                }
                $arrThreadList = $this->_getAnchorInfo($arrThreadInfo);
                if ($arrThreadList !== false) {
                    $arrThreadInfo = $arrThreadList;
                }
                if ($intIsThread === 1) {
                    $this->arrPostList = $this->_buildThread($arrUserPost, $arrThreadInfo);
                } else {
                    $this->arrPostList = $this->_buildPost($arrUserPost, $arrThreadInfo);
                    //增加来源吧
                    if (!empty($this->arrPostList)) {
                        foreach ($this->arrPostList as $dateThread) {
                            foreach ($dateThread as $thread) {
                                $arrPostIdList['post_ids'][] = $thread['post_id'];
                            }
                        }

                        $arrOutput = Tieba_Service::call('post', 'getPostInfo', $arrPostIdList, null, null, 'post', 'php', 'utf-8');

                        if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                            $this->_error($arrOutput['errno'], Tieba_Error::getErrmsg($arrOutput['errno']));
                            Bingo_Log::fatal('Call post:getPostInfo failed. input=]' . serialize($arrPostIdList) . '[Output=]' . serialize($arrOutput));
                            return false;
                        }
                        $vForumList = array();
                        foreach ($arrOutput['output'] as $thread) {
                            $vForumList[$thread['post_id']] = $thread['v_forum_id'];
                        }
                        foreach ($this->arrPostList as &$dateThread) {
                            foreach ($dateThread as &$thread) {
                                $thread['v_forum_id'] = $vForumList[$thread['post_id']];
                            }
                        }
                    }

                }
            }
        }
    }

    /**
     * 对图文直播封面进行裁剪压缩
     * @param unknown $strLiveCoverSrc
     * @return unknown|Ambigous <string, mixed>
     */
    private function _getLiveCoverSrc($strLiveCoverSrc)
    {
        if (empty($strLiveCoverSrc)) {
            return $strLiveCoverSrc;
        }
        $arrSpec = array(
            'screen_w'   => 640,
            'screen_h'   => 960,
            'screen_dip' => 0,
            'q_type'     => 0,
        );

        $strLiveCoverSrc = Molib_Util_ImgCDN_Frs::procImgTextLivePic($strLiveCoverSrc, $arrSpec);
        return $strLiveCoverSrc;
    }

    /**
     * @param array arrThreadInfo
     * @return boolean
     */
    private function _getAnchorInfo($arrThreadInfo = array())
    {
        $arr_anchor_tids = array();
        foreach ($arrThreadInfo as $key => $value) {
            $arr = Tieba_Type_Thread::getTypeArray($value['thread_types']);
            if (isset($arr['is_anchor_thread'])) {
                $arr_anchor_tids[] = $value['thread_id'];
            }
        }
        if (empty($arr_anchor_tids)) {
            return false;
        }
        $arrInput  = array('thread_ids' => array_unique($arr_anchor_tids));
        $arrResult = Molib_Tieba_Service::call('livegroup', 'queryLiveGroupsByTids', $arrInput);
        if (false === $arrResult || $arrResult['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call im queryLiveGroupsByTids errno not zero threadids[' . serialize($arrInput) . '] output[' . serialize($arrResult) . ']');
            return false;
        }
        $thread_list = $arrThreadInfo;
        if (!empty($arrResult['groups'])) {
            foreach ($thread_list as $key => $val) {
                $thread_id = $val['thread_id'];
                if (isset($arrResult['groups'][$thread_id])) {
                    $arrTemp                                               = $arrResult['groups'][$thread_id];
                    $thread_list[$key]['anchor_info']['portrait']          = strval($arrTemp['portrait']);
                    $thread_list[$key]['anchor_info']['name']              = strval($arrTemp['name']);
                    $thread_list[$key]['anchor_info']['start_time']        = intval($arrTemp['start_time']);
                    $thread_list[$key]['anchor_info']['status']            = intval($arrTemp['status']);
                    $thread_list[$key]['anchor_info']['author_id']         = intval($arrTemp['author_id']);
                    $thread_list[$key]['anchor_info']['author_name']       = strval($arrTemp['author_name']);
                    $thread_list[$key]['anchor_info']['listeners']         = intval($arrTemp['listeners']);
                    $thread_list[$key]['anchor_info']['likers']            = intval($arrTemp['likers']);
                    $thread_list[$key]['anchor_info']['group_id']          = intval($arrTemp['group_id']);
                    $thread_list[$key]['anchor_info']['intro']             = strval($arrTemp['intro']);
                    $thread_list[$key]['anchor_info']['publisherPortrait'] = strval($arrTemp['publisherPortrait']);
                    $thread_list[$key]['anchor_info']['publisherName']     = strval($arrTemp['publisher_name']);
                    $thread_list[$key]['anchor_info']['publisherId']       = intval($arrTemp['publisher_id']);
                    $thread_list[$key]['thread_type']                      = Tieba_Type_Thread::ANCHOR_THREAD;
                }
            }
        }
        return $thread_list;
    }

    /**
     * @param void
     * @return boolean
     */
    private function _getUserInfo()
    {
        $intUserId = $this->_intUserId;
        $arrInput  = array(
            'user_id'          => array($intUserId),
            "need_follow_info" => 1,
            "need_pass_info"   => 1,
            "get_icon"         => 1
        );
        $arrOut    = Molib_Tieba_Service::call("user", "mgetUserDataEx", $arrInput);
        if (false === $arrOut || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            $this->_error($arrOut['errno'], Tieba_Error::getErrmsg($arrOut['errno']));
            Bingo_Log::warning('Call user:mgetUserDataEx failed. input=]' . serialize($arrInput) . '[Output=]' . serialize($arrOut));
            return false;
        }
        $this->_getUserIconExec($arrOut);
        return $arrOut['user_info'][$intUserId];
    }

    /**
     * @param param array
     * @return boolean
     */
    private function _getUserIconExec($arrOut)
    {
        $intUid = $this->_intUserId;
        if (!isset($arrOut['user_info'][$intUid]['new_iconinfo'])) {
            $arrOut['user_info'][$intUid]['new_iconinfo'] = "";
        }
        $this->arrIconInfo = $this->_buildIconInfo($arrOut['user_info'][$intUid]['new_iconinfo']);
        return true;
    }


    /**
     * @param new_iconinfo
     * @return boolean
     */
    private function _buildIconInfo($new_iconinfo)
    {
        $arrNewIconInfo = array();
        $nCount         = 0;
        $nCurrentTime   = time();
        if (is_array($new_iconinfo)) {
            foreach ($new_iconinfo as $value) {
                if (empty ($value) || empty ($value ['icon'])) {
                    continue;
                }
                if ($nCurrentTime > $value['end_time']) {
                    continue;
                }
                $arrItem              = array();
                $arrItem ['name']     = $value ['name'];
                $arrItem ['value']    = $value ['value'];
                $arrItem ['weight']   = $value ['weight'];
                $arrItem ['terminal'] = $value ['terminal'];
                $arrItem ['position'] = $value ['position'];
                $arrItem ['sprite']   = $value ['sprite'];
                $arrItem ['icon']     = $value ['icon'];
                $arrNewIconInfo []    = $arrItem;
                $nCount++;
                if ($nCount >= 8) {
                    break;
                }
            }
        }
        return $arrNewIconInfo;
    }

    /**
     * @param void
     * @return boolean
     */
    private function _getUserPost()
    {
        $intOffset      = ($this->_intPn - 1) * $this->_intRn;
        $intRn          = $this->_intRn;
        $intIsThread    = $this->_intIsThread;
        $intNeedContent = 1;
        $intForumId     = 0;

        $intBeginTime = $this->_intBeginTime;
        $intEndTime   = $this->_intEndTime;

        // 用户信息
        $intUserId = $this->_intUserId;
        $intOwnUid = $this->_intUserId;

        $isViewCard  = 1;
        $intMastUser = 0;
        $intAllHide  = 1;
        if ($intUserId === $intOwnUid && $isViewCard == 1) {
            $intMastUser = 1;
            $intAllHide  = 0;
        }

        if ($isViewCard == 0) {
            $intMastUser = 0;
        }

        if (intval($this->arrUserInfo['priv_sets']['post']) != 3
            && intval($this->arrUserInfo['priv_sets']['post']) != 2) {
            $intAllHide = 0;
        }

        $arrInput['input'] = array(
            'user_id'      => $intUserId, //838608334
            'offset'       => $intOffset,
            'res_num'      => $intRn + 1,
            'order_type'   => 1,      // 0按时间升序  1按时间倒序
            'delete_type'  => 0,     // 0未删除 1已删除 2全部
            'is_thread'    => $intIsThread,//是否是主题贴
            'need_content' => $intNeedContent,
            'forum_id'     => $intForumId,   //吧id
            'begin_time'   => $intBeginTime,
            'end_time'     => $intEndTime,  //结束时间
        );

        // 回复走老逻辑
        if ($intIsThread == 1) {
            $arrInput['input']['is_home_user'] = $intMastUser;   // 主客态
            $arrInput['input']['is_all_hide']  = $intAllHide;     // 是否过滤隐私贴
        }

        $arrInput['input']['is_partial_visible'] = 1;
        //用户帖子隐私设置
        if (isset($this->arrUserInfo['priv_sets'])) {
            $arrPrivSetsFromUserInfo = $this->arrUserInfo['priv_sets'];
            $this->mask_type         = $arrPrivSetsFromUserInfo['post'];
        }

        if ($this->mask_type == 4 && $isViewCard == 0) {
            $arrInput['input']['is_forbidden_forum'] = 1;
        }


        // 这个参数置为0，语音贴数据取不到，这个入参是可选参数，因此如此处理
        if (isset($intSubType) && $intSubType > 0) {
            $arrInput['input']['subtype'] = $intSubType;
        }

        $strService = 'post';
        $arrOut     = Molib_Tieba_Service::call($strService, 'queryUserPost', $arrInput);
        if (false === $arrOut || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            $this->_error($arrOut['errno'], Tieba_Error::getErrmsg($arrOut['errno']));
            Bingo_Log::fatal('Call ' . $strService . ':queryUserPost failed. input=]' . serialize($arrInput) . '[Output=]' . serialize($arrOut));
            return false;
        }
        return $arrOut['post']['post'];
    }

    /**
     * @param
     * @param
     * @return
     */
    private function _getThreadInfo($arrThreadId, $intNeedAbstract)
    {
        $arrInput = array(
            'thread_ids'      => $arrThreadId,
            'need_abstract'   => $intNeedAbstract,
            'forum_id'        => 0,
            'need_photo_pic'  => 1,
            'need_forum_name' => 1,
            'need_user_data'  => 1,
            'call_from'       => 'client_frs',
        );

        $arrOut = Molib_Tieba_Service::call('post', 'mgetThread', $arrInput);
        if (false === $arrOut) {
            Bingo_Log::warning("Fail to call service post:mgetThread. [input: " . serialize($arrInput) . "]");
            return false;
        }

        if ($arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call post/mgetThread fail. [input: " . serialize($arrInput) . "][ret: " . serialize($arrOut) . "]");
            return false;
        }
        //图文直播搬贴改变帖子类型为图文直播贴
        Molib_Util_ThreadFilter::setThreadTypesByCopyTwZhiBo($arrOut['output']['thread_list']);
        $this->arrAuthorInfo = $arrOut['output']['thread_user_list'];
        return $arrOut['output']['thread_list'];
    }

    /**
     * @brief: 获取商品贴信息合并到贴子中
     * @param
     * @return
     **/
    private function _getProductThreadInfo($arrInData, $arrUserPost)
    {
        if (empty($arrInData) || !is_array($arrInData) || empty($arrUserPost)) {
            return $arrUserPost;
        }

        $arrParams = array(
            'thread_ids' => $arrInData,
            'isowner'    => 1,
        );
        $arrRet    = Tieba_Service::call('ecommerce', 'getFrsData', $arrParams, null, null, 'post', 'php', 'utf-8');
        if ($arrRet === false || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(__FUNCTION__ . ' ecommerce.getFrsData error. [input=' . serialize($arrParams) . '][out=' . serialize($arrRet) . ']');
        }
        $productInfo = !empty($arrRet['data']) ? $arrRet['data'] : array();

        if (empty($productInfo)) {
            return $arrUserPost;
        }

        foreach ($arrUserPost AS $key => $item) {
            $thread_id = intval($item['thread_id']);

            if (!isset($productInfo[$thread_id])) {
                continue;
            }

            $tmpProductInfo = $productInfo[$thread_id];
            $img            = $tmpProductInfo['img'];
            $imgArr         = json_decode($img, true);
            $imgArr         = is_array($imgArr) ? $imgArr : array();

            $arrUserPost[$key]['is_deal']   = 1;
            $arrUserPost[$key]['deal_info'] = array(
                'title'       => $tmpProductInfo['title'],
                'desc'        => $tmpProductInfo['intro'],
                'stock'       => intval($tmpProductInfo['stock']),
                'sales'       => intval($tmpProductInfo['sales']),
                'expire_time' => intval($tmpProductInfo['expire_time']),
                'status'      => intval($tmpProductInfo['status']),
                'unit_price'  => intval($tmpProductInfo['unit_price']),
                'product_id'  => intval($tmpProductInfo['product_id']),
                'ship_fee'    => intval($tmpProductInfo['ship_fee']),
                'media'       => $imgArr,
            );
        }

        return $arrUserPost;
    }

    // 获取主题帖或者回复贴的语音数据

    /**
     * @param arrTids
     * @return boolean
     */
    private function _getVoiceDataByTid($arrTids)
    {

        $arrInput = array('tids' => $arrTids);
        $arrOut   = Molib_Tieba_Service::call('voice', 'getThreadVoiceInfosByTids', $arrInput);
        if ($arrOut == false) {
            return false;
        }
        if ($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(("call voice/getThreadVoiceInfosByTids fail. [input: " . serialize($arrInput) . "][ret: " . serialize($arrOut) . "]"));
            return false;
        }

        $arrVoiceData = array();
        foreach ($arrOut['ret']['threadVoiceList'] as $val) {
            $item                = array();
            $item['type']        = Molib_Util_RichText_Parser::SLOT_TYPE_VOICE;
            $item['during_time'] = intval($val['during_time']) * 1000;
            $item['voice_md5']   = $val['voice_md5'];

            $arrVoiceData[$val['thread_id']][] = $item;
        }

        return $arrVoiceData;
    }

    // 获取主题帖或者回复贴的语音数据

    /**
     * @param arrPids
     * @return boolean
     */
    private function _getVoiceDataByPid($arrPids)
    {

        $arrInput = array('pids' => $arrPids);
        $arrOut   = Molib_Tieba_Service::call('voice', 'getThreadVoiceInfosByPids', $arrInput);
        if ($arrOut == false) {
            return false;
        }
        if ($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(("call voice/getThreadVoiceInfosByPids fail. [input: " . serialize($arrInput) . "][ret: " . serialize($arrOut) . "]"));
            return false;
        }

        $arrVoiceData = array();
        foreach ($arrOut['ret']['postVoiceList'] as $val) {
            $item                = array();
            $item['type']        = Molib_Util_RichText_Parser::SLOT_TYPE_VOICE;
            $item['during_time'] = intval($val['during_time']) * 1000;
            $item['voice_md5']   = $val['voice_md5'];

            $arrVoiceData[$val['post_id']][] = $item;
        }

        return $arrVoiceData;
    }


    /**
     * 获取是否点赞, 输出在 dynamic_list.agree (8.9需求)
     * agree: {
     *       agree_num: "1",
     *       has_agree: "0",
     *       agree_type: "2"
     *   }
     * @param array arrThreadIds = array(
     *      5299399489 => 0,
     *      5296152030 => 0,
     *      ...
     * )
     * @return array
     */
    public function getUserAgree($arrThreadIds)
    {

        if (empty($arrThreadIds)) {
            return array();
        }
        $intUserId = $this->_intUserId;
        $arrInput  = array(
            'user_id'    => $intUserId,
            'target_ids' => array_keys($arrThreadIds),
            'type'       => "thread",
        );

        $arrRet = Tieba_Service::call('agree', 'mGetUserAgree', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call agree::mGetUserAgree failed. [output = %s]", serialize($arrRet)));
        }
        $arrAgreeInfo = !empty($arrRet['data']) ? $arrRet['data'] : array();
        return $arrAgreeInfo;
    }

    /**
     *  获取是否点踩, 输出在 dynamic_list.agree (9.8需求)
     * agree: {
     *       agree_num: "1",
     *       has_agree: "0",
     *       agree_type: "2"
     *   }
     * @param array arrThreadIds = array(
     *      5299399489 => 0,
     *      5296152030 => 0,
     *      ...
     * )
     * @return array
     */
    public function getUserDisagree($arrThreadIds)
    {
        if (empty($arrThreadIds)) {
            return array();
        }
        $intUserId = intval($this->_intUserId);
        $arrInput  = array(
            'user_id'    => $intUserId,
            'target_ids' => array_keys($arrThreadIds),
            'type'       => "threadcai",
        );

        $arrRet = Tieba_Service::call('agree', 'mGetUserAgree', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call disagree::mGetUserAgree failed. [output = %s]", serialize($arrRet)));
        }
        $arrAgreeInfo = !empty($arrRet['data']) ? $arrRet['data'] : array();
        return $arrAgreeInfo;
    }

    /**
     * @param arrUserPost
     * @param arrThreadInfo
     * @return boolean
     */
    private function _buildThread($arrUserPost, $arrThreadInfo)
    {
        $arrPostList         = array();
        $arrTwZhiBoThreadIds = array();
        $arrForumIdShared    = array();
        $arrShareTids        = array();

        if (count($arrUserPost) > $this->_intRn) {
            $nextPost = array_pop($arrUserPost);
            if (date('Y-m-d', $nextPost['create_time']) == date('Y-m-d', $arrUserPost[count($arrUserPost) - 1]['create_time'])) {
                $this->_intSameDay = 1;
            }
        }

        /**
         *  8.9 获取用户点赞数据
         */
        $arrIds = array();
        foreach ($arrUserPost as $arrTmp) {
            $arrIds[$arrTmp['thread_id']] = 0;
        }
        $arrAgreeInfo    = $this->getUserAgree($arrIds);
        $arrDisagreeInfo = $this->getUserDisagree($arrIds);

        foreach ($arrUserPost as $arrTmp) {

            $arrPost    = $arrTmp;
            $intTid     = $arrTmp['thread_id'];
            $is_twzhibo = 0;
            if (isset($arrThreadInfo[$intTid])) {
                //过滤投票、吧刊、图册贴
                $arrThreadType = $this->_parseThreadType($arrThreadInfo[$intTid]['thread_types']);
                if ($arrThreadType['isVote'] == 1 || $arrThreadType['isBakan'] == 1 || $arrThreadType['isProtal'] == 1) {
                    continue;
                }
                $arrTypes = Tieba_Type_Thread::getTypeArray($arrThreadInfo[$intTid]['thread_types']);

                if ($arrTypes['is_smartapp_share'] == 1 && $this->_strCallFrom == 'weixin') {
                    continue;
                }

                if (isset($arrTypes['is_poll'])) {
                    $arrThreadInfo[$intTid]['thread_type'] = Tieba_Type_Thread::POLL_THREAD;
                    $arrPollInfoExt                        = $arrThreadInfo[$intTid]['poll_info'];
                    $arrPollInfoExtStatic                  = $arrThreadInfo[$intTid]['poll_ext'];
                    $arrPollInfoExtStatic['total_num']     = $arrPollInfoExt['count'];
                    $arrThreadInfo[$intTid]['poll_info']   = $arrPollInfoExtStatic;
                }

                if (isset($arrTypes['is_movdieo'])) {
                    $arrThreadInfo[$intTid]['thread_type'] = Tieba_Type_Thread::MOVIDEO_THREAD;

                }

                // 如果是ALa直播或者录播贴,过滤
                if ($arrTypes['is_ala_live_video'] === true ||
                    $arrTypes['is_ala_video'] === true) {
                    continue;
                }
                if ($arrTypes['is_ala_share_thread'] == true) {
                    // 直播吧内分享贴
                    $arrShareTids[] = $intTid;
                }

                // 图文直播只有android的显示
                // 判断是否是图文直播帖子，批量获取图文直播内容
                $is_twzhibo = 0;
                $zhiBoInfo  = array();

                if (isset($arrTypes['is_twzhibo_thread'])) {//图文直播
                    $arrTwZhiBoThreadIds[] = $intTid;

                    $arrInput = array(
                        'forumId'    => $arrTmp['forum_id'],
                        'threadIds'  => array($intTid),
                        'labelCount' => 3
                    );

                    $arrInput['serviceName'] = 'livegroup';
                    $arrInput['method']      = 'getTWInfosByTids';
                    $arrInput['input']       = $arrInput;
                    $arrInput['ie']          = 'utf-8';
                    $this->_objMulti->register('livegroup_getTWInfosByTids_' . $intTid, new Tieba_Service('livegroup'), $arrInput);
                }

                $arrAbstract = array();
                if (!empty($arrThreadInfo[$intTid]['abstract'])) {
                    $arrAbstract = array(
                        array(
                            'type' => 0,
                            'text' => html_entity_decode(htmlspecialchars_decode($arrThreadInfo[$intTid]['abstract']), ENT_COMPAT, 'UTF-8'),
                        ),
                    );
                }
                $boolNeedPhotoPic = true;

                $arrPost['abstract']  = $arrAbstract;
                $arrPost['media']     = $this->_processMedia($arrThreadInfo[$intTid]['media'], $arrThreadInfo[$intTid]['raw_abstract_media'], $boolNeedPhotoPic);
                $arrPost['reply_num'] = $arrThreadInfo[$intTid]['post_num'];
                $arrPost['freq_num']  = $arrThreadInfo[$intTid]['freq_num'];
                if (!isset($arrPost['agree_num'])) {
                    $arrPost['agree_num'] = $arrThreadInfo[$intTid]['agree_num'];
                }
            } else {
                $arrPost['abstract']  = array();
                $arrPost['media']     = array();
                $arrPost['reply_num'] = 0;
            }
            $intUserId                = $this->_intUserId;
            $arrPost['user_id']       = $intUserId;
            $arrPost['name_show']     = $this->strUserNickName;
            $arrPost['user_portrait'] = Tieba_Ucrypt::encode($intUserId, $arrPost['user_name'], $this->arrUserInfo['portrait_time']);

            $arrPost['title']      = html_entity_decode($arrThreadInfo[$intTid]['title'], ENT_COMPAT, 'utf-8');
            $arrPost['forum_name'] = html_entity_decode($arrThreadInfo[$intTid]['forum_name'], ENT_COMPAT, 'utf-8');
            if (isset($arrThreadInfo[$intTid]['swan_info'])) {
                $arrPost['third_app_info'] = array(
                    'app_id'       => $arrThreadInfo[$intTid]['swan_info']['id'],
                    'app_name'     => $arrThreadInfo[$intTid]['swan_info']['name'],
                    'app_avatar'   => $arrThreadInfo[$intTid]['swan_info']['avatar'],
                    'app_pic'      => $arrThreadInfo[$intTid]['swan_info']['pic'],
                    'app_link'     => $arrThreadInfo[$intTid]['swan_info']['link'],
                    'app_abstract' => $arrThreadInfo[$intTid]['swan_info']['abstract'],
                    'app_h5_url'   => $arrThreadInfo[$intTid]['swan_info']['h5_url'],
                );
            }

            if ($this->isFrameChange && isset($arrThreadInfo[$intTid]['forum_id_shared'])) {//如果是贴吧客户端框架改版
                $arrPost['forum_id_shared'] = $arrThreadInfo[$intTid]['forum_id_shared'];//后面会unset，所以不存在增加proto的问题
                $arrForumIdShared           = array_merge($arrForumIdShared, $arrThreadInfo[$intTid]['forum_id_shared']);
            }
            $arrPost['thread_type']   = isset($arrThreadInfo[$intTid]['thread_type']) ? $arrThreadInfo[$intTid]['thread_type'] : 0;
            $arrPost['is_ntitle']     = intval($arrThreadInfo[$intTid]['is_ntitle']);
            $arrPost['anchor_info']   = isset($arrThreadInfo[$intTid]['anchor_info']) ? $arrThreadInfo[$intTid]['anchor_info'] : array();
            $arrPost['poll_info']     = isset($arrThreadInfo[$intTid]['poll_info']) ? $arrThreadInfo[$intTid]['poll_info'] : array();
            $arrPost['is_pro_thread'] = empty($arrThreadInfo[$intTid]['is_pro_thread']) ? 0 : $arrThreadInfo[$intTid]['is_pro_thread'];
            //客户端短视频
            if (isset($arrThreadInfo[$intTid]['video_info'])) {
                $arrPost['video_info'] = $arrThreadInfo[$intTid]['video_info'];

                // 视频贴必须经过这个函数处理
                Tieba_Video_Process::execute($arrPost['video_info'], '', Tieba_Video_Process::REQUEST_FROM_CLIENT);
                if (isset($arrThreadInfo[$intTid]['video_play'])) {
                    $arrPost['video_info']['play_count'] = $arrThreadInfo[$intTid]['video_play']['count'];
                }
                $arrPost['thread_type'] = Tieba_Type_Thread::MOVIDEO_THREAD;
            }
            if ($this->isFilterForum($arrPost['forum_name'])) {
                $arrPost['forum_name'] = '';
            }

            // 8.9 增加点赞数据
            $intHasAgree = empty($arrAgreeInfo[$intTid]) ? self::NOT_AGREE : self::HAS_AGREE;
            if (!$intHasAgree) {
                $intHasAgree = empty($arrDisagreeInfo[$intTid]) ? self::NOT_AGREE : self::HAS_AGREE;
            }
            //如果用户点过顶 那么 agree_type 返回2，如果用户点过踩 那么 agree_type 返回5  9.8需求
            $intAgreeType = 0;
            if ($intHasAgree) {
                $intAgreeType = empty($arrAgreeInfo[$intTid]) ? 0 : self::AGREE_TYPE_AGREE;
                if (!$intAgreeType) {
                    $intAgreeType = empty($arrDisagreeInfo[$intTid]) ? 0 : self::AGREE_TYPE_CAI;
                }
            }

            $arrPost['share_num'] = $arrThreadInfo[$intTid]['share_num'];
            $arrPost['agree']     = array(
                'agree_num'    => $arrThreadInfo[$intTid]['agree_num'],
                'disagree_num' => isset($arrThreadInfo[$intTid]['disagree_num']) ? intval($arrThreadInfo[$intTid]['disagree_num']) : 0,
                'has_agree'    => $intHasAgree,
                'agree_type'   => $intAgreeType,
            );

            //##话题解析
            $objParserStructured = new Molib_Util_RichText_ParserStructured();
            $arrTopicContent     = $objParserStructured->getStructFromTopicContent($arrPost['title']);
            if (!empty($arrTopicContent)) {
                $arrContent = array();
                foreach ($arrTopicContent as $temp) {
                    if ($temp['tag'] == "plainText") {
                        $arrContent[] = array(
                            'type' => Molib_Util_RichText_ParserStructured::SLOT_TYPE_TEXT,
                            'text' => $temp['value'],
                        );
                    }
                    if ($temp['tag'] == "a") {
                        $strHref = $temp['href'];
                        $strText = $temp['value'][0]['value'];
                        if (empty($strText)) {
                            continue;
                        }
                        $resTopic = array(
                            'type' => Molib_Util_RichText_ParserStructured::SLOT_TYPE_TOPIC,
                            'text' => htmlspecialchars_decode($strText),
                            'link' => htmlspecialchars_decode($strHref),
                        );
                        if (!empty($resTopic)) {
                            $arrContent[] = $resTopic;
                        }
                    }
                }
                if (!empty($arrContent)) {
                    $arrPost['rich_title'] = $arrContent;
                }
            }
            $arrAbstract = $arrPost['abstract'];
            if (!empty($arrAbstract[0]['text'])) {
                $arrTopicContent = $objParserStructured->getStructFromTopicContent($arrAbstract[0]['text']);
                if (!empty($arrTopicContent)) {
                    $arrContent = array();
                    foreach ($arrTopicContent as $temp) {
                        if ($temp['tag'] == "plainText") {
                            $arrContent[] = array(
                                'type' => Molib_Util_RichText_ParserStructured::SLOT_TYPE_TEXT,
                                'text' => $temp['value'],
                            );
                        }
                        if ($temp['tag'] == "a") {
                            $strHref = $temp['href'];
                            $strText = $temp['value'][0]['value'];
                            if (empty($strText)) {
                                continue;
                            }
                            $resTopic = array(
                                'type' => Molib_Util_RichText_ParserStructured::SLOT_TYPE_TOPIC,
                                'text' => htmlspecialchars_decode($strText),
                                'link' => htmlspecialchars_decode($strHref),
                            );
                            if (!empty($resTopic)) {
                                $arrContent[] = $resTopic;
                            }
                        }
                    }
                    if (!empty($arrContent)) {
                        $arrPost['rich_abstract'] = $arrContent;
                    }
                }
            }

            $arrPostList[$intTid] = $arrPost;
        }

        if (!empty($arrForumIdShared)) {
            $arrFname = $this->_getFnameByFid($arrForumIdShared);
            foreach ($arrPostList as &$arrPost) {
                if (isset($arrPost['forum_id_shared'])) {
                    $arrForumShared = array();
                    foreach ($arrPost['forum_id_shared'] as $fid) {
                        if (isset($arrFname[$fid])) {
                            if ($this->isFilterForum($arrFname[$fid])) {
                                continue;
                            }

                            $tmp              = array(
                                'forum_id'   => $fid,
                                'forum_name' => $arrFname[$fid],
                            );
                            $arrForumShared[] = $tmp;
                        }
                    }
                    if (!empty($arrForumShared)) {
                        $arrPost['multiple_forum_list'] = $arrForumShared;
                    } else {
                        if ($this->isFilterForum($arrFname[$fid])) {
                            continue;
                        }
                        $arrPost['multiple_forum_list'] = array(
                            array(
                                'forum_id'   => $arrPost['forum_id'],
                                'forum_name' => $arrPost['forum_name'],
                            )
                        );
                    }
                    unset($arrPost['forum_id_shared']);
                } else {
                    if ($this->isFilterForum($arrPost['forum_name'])) {
                        continue;
                    }
                    $arrPost['multiple_forum_list'] = array(
                        array(
                            'forum_id'   => $arrPost['forum_id'],
                            'forum_name' => $arrPost['forum_name'],
                        )
                    );
                }
            }
            unset($arrPost);
        } else {
            foreach ($arrPostList as &$arrPost) {
                if ($this->isFilterForum($arrPost['forum_name'])) {
                    continue;
                }
                $arrPost['multiple_forum_list'] = array(
                    array(
                        'forum_id'   => $arrPost['forum_id'],
                        'forum_name' => $arrPost['forum_name'],
                    )
                );
            }
        }

        if (count($arrTwZhiBoThreadIds) > 0) {
            Bingo_Timer::start('twzhibo_call');
            $this->_objMulti->call();
            Bingo_Timer::end('twzhibo_call');

            foreach ($arrTwZhiBoThreadIds as $threadId) {
                $arrThreadRet = $this->_objMulti->getResult('livegroup_getTWInfosByTids_' . $threadId);
                if (null == $arrThreadRet) {//如果返回null， 说明没有进行Muti::register()操作
                    continue;
                }
                $arrZanInfo = $this->_objMulti->getResult('get_zan_' . $threadId);
                if (null == $arrZanInfo) {//如果返回null， 说明没有进行Muti::register()操作
                    continue;
                }
                $arrZhiBoInfo         = $arrThreadRet['twzhiboInfo'][$threadId];
                $arrZhiBoInfo['user'] = array("icon_info" => $this->arrIconInfo,);
                $arrZhiBoInfo['zan']  = array("num" => $arrZanInfo['ret']['total_num']);
                //判断是否有封面
                if (isset($arrZhiBoInfo['livecover_src'])) {
                    $livecoverSrc                  = $this->_getLiveCoverSrc($arrZhiBoInfo['livecover_src']);
                    $arrZhiBoInfo['livecover_src'] = $livecoverSrc;
                }

                $arrPostList[$threadId]['thread_type']  = Tieba_Type_Thread::TWZHIBO_THREAD;
                $arrPostList[$threadId]['twzhibo_info'] = $arrZhiBoInfo;
            }
        }

        // 缩略图和大图
        $arrSpec = array(
            'screen_w'   => 640,
            'screen_h'   => 960,
            'screen_dip' => 0,
            'q_type'     => 0,
        );
        if (1 == Molib_Util_Version::compare($this->strClientVersion, '7.0.0')) {
            $boolIsAllOrigin = false;
        } else {
            $boolIsAllOrigin = true;
        }
        $boolIsCdn     = true;  // 暂时定为true
        $intCndErrTime = 0;
        // 这里会清除key值，需要注意！！
        $arrPostList = Molib_Util_ImgCDN_Frs::procNewPicUrlForWideDifference($arrPostList, $boolIsCdn, $arrSpec, Molib_Util_ImgCDN_Frs::FRS_PIC_STRATEGY_10, intval($intCndErrTime), $boolIsAllOrigin, 2, $this->intClientType, $this->strClientVersion);
        foreach ($arrPostList as $key => $arrPost) {
            foreach ($arrPost['media'] as $indexOfMedia => $arrMedia) {
                $arrPostList[$key]['media'][$indexOfMedia]['small_pic']   = $arrMedia['big_pic'];
                $arrPostList[$key]['media'][$indexOfMedia]['dynamic_pic'] = $arrMedia['big_pic'];
                $arrPostList[$key]['media'][$indexOfMedia]['big_pic']     = $arrMedia['src_pic'];
                $arrPostList[$key]['media'][$indexOfMedia]['water_pic']   = $arrMedia['origin_pic'];
            }
        }

        //先进行富文本处理
        $condition = new Molib_Util_RichText_ParserCondition();
        $parser    = new Molib_Util_RichText_Parser();
        $arrRet    = array();

        foreach ($arrPostList as &$item) {
            $objResult                                      = $parser->process($condition, $item['content']);
            $item['content']                                = $objResult->arrContent;
            $arrRet[date("Y-m-d ", $item['create_time'])][] = $item;
        }

        $arrRet = array_values($arrRet);
        return $arrRet;
    }

    /**
     * @param arrFid
     * @return
     * */
    private function _getFnameByFid($arrFid)
    {
        $arrOutput = array();
        $input     = array(
            "forum_id" => $arrFid,
        );
        $res       = Tieba_Service::call('forum', 'getFnameByFid', $input, null, null, 'post', 'php', 'utf-8');
        if (false == $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call forum getFnameByFid failed, input = [' . serialize($input) . '],output = [' . serialize($res) . ']');
            return $arrOutput;
        }
        foreach ($res['forum_name'] as $fid => $value) {
            $arrOutput[$fid] = $value['forum_name'];
        }
        return $arrOutput;
    }

    /**
     * @param arrUserPost
     * @param arrThreadInfo
     * @return
     */
    private function _buildPost($arrUserPost, $arrThreadInfo)
    {
        //先进行富文本处理
        $condition                  = new Molib_Util_RichText_ParserCondition();
        $condition->intNewLineCount = 1;
        $parser                     = new Molib_Util_RichText_Parser();
        //获取表情文件
        $arrSmile2TextTransData = Molib_Conf_Smile::$arrSmile2TextTransData;
        $arrSmileNotSupportAll  = Molib_Conf_Smile::$arrSmileNotSupport;

        if (count($arrUserPost) > $this->_intRn) {
            $nextPost = array_pop($arrUserPost);
            if (date('Y-m-d', $nextPost['create_time']) == date('Y-m-d', $arrUserPost[count($arrUserPost) - 1]['create_time'])) {
                $this->_intSameDay = 1;
            }
        }

        foreach ($arrUserPost as &$item) {
            $isReplayLz = 0;
            if (0 !== strpos($item['content'], '回复 <a href="http://tieba.baidu.com/')) {
                $isReplayLz = 1;
            }
            $objResult       = $parser->process($condition, $item['content']);
            $item['content'] = $objResult->arrContent;
            //表情展示相应的文字，其他多媒体展示文本
            $arrContent = array();
            foreach ($item['content'] as $arrTmp) {
                $arrTypes = Tieba_Type_Thread::getTypeArray($arrThreadInfo[$item['thread_id']]['thread_types']);
                if (isset($arrTypes['is_twzhibo_thread'])) {
                    $item['thread_type'] = Tieba_Type_Thread::TWZHIBO_THREAD;
                }

                if (isset($arrTypes['is_poll'])) {
                    $item['thread_type'] = Tieba_Type_Thread::POLL_THREAD;
                }

                if ($arrTmp['type'] == Molib_Util_RichText_Parser::SLOT_TYPE_SMILE) {
                    $arrTmp['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_TEXT;
                    $arrTmp['text'] = $this->_processSmile($arrTmp['text'], $arrSmile2TextTransData, $arrSmileNotSupportAll);
                } elseif ($arrTmp['type'] == Molib_Util_RichText_Parser::SLOT_TYPE_IMG) {
                    $arrTmp['src']  = $this->_getSpecImageUrl($arrTmp['src'], self::COVER_PIC_SPEC);
                    $arrTmp['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_TEXT;
                    $arrTmp['text'] = '[图片]';
                } elseif ($arrTmp['type'] == Molib_Util_RichText_Parser::SLOT_TYPE_EMBED) {
                    $arrTmp['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_TEXT;
                    $arrTmp['text'] = '[视频]';
                } elseif ($arrTmp['type'] == Molib_Util_RichText_Parser::SLOT_TYPE_MUSIC) {
                    $arrTmp['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_TEXT;
                    $arrTmp['text'] = '[音乐]';
                } elseif ($arrTmp['type'] == Molib_Util_RichText_Parser::SLOT_TYPE_SMILE3) {
                    $arrTmp['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_TEXT;
                    $arrTmp['text'] = '[' . $arrTmp['c'] . ']';
                }
                $arrContent[] = $arrTmp;
            }
            // 用于帮助fe解决一个显示问题。。。。
            if ($isReplayLz === 1 && isset($item['quote'])) {
                $postWho    = array(
                    array(
                        "text" => '回复',
                        'type' => 0,
                    ),
                    array(
                        'text' => '@' . $item['quote']['user_name'],
                        'type' => 4,
                        'un'   => $item['quote']['user_name'],
                    ),
                );
                $arrContent = array_merge($postWho, $arrContent);
            }
            $item['content']     = $arrContent;
            $item['video_image'] = $this->_getSpecImageUrl($arrThreadInfo[$item['thread_id']]['video_info']['thumbnail_url'], self::COVER_PIC_SPEC);

            if (isset($item['voice_info'])) {
                // 将语音信息插入到队列头
                array_splice($item['content'], 0, 0, $item['voice_info']);
                unset($item['voice_info']);
            }
            $item['reply_num'] = isset($arrThreadInfo[$item['thread_id']]) ? $arrThreadInfo[$item['thread_id']]['post_num'] : 0;
            $item['freq_num']  = isset($arrThreadInfo[$item['thread_id']]) ? $arrThreadInfo[$item['thread_id']]['freq_num'] : 0;

            $intUserId             = $this->_intUserId;
            $item['user_id']       = $intUserId;
            $item['name_show']     = $this->strUserNickName;
            $item['user_portrait'] = Tieba_Ucrypt::encode($intUserId, $item['user_name'], $this->arrUserInfo['portrait_time']);
            $item['post_type']     = empty($item['quote']['post_id']) ? 0 : 1;//1是楼中楼
            if (isset($arrThreadInfo[$item['thread_id']]['media'])) {
                foreach ($arrThreadInfo[$item['thread_id']]['media'] as $pic) {
                    $item['media'][] = $this->_getSpecImageUrl($pic['big_pic'], self::COVER_PIC_SPEC);
                }
            }
            $item['author'] = array(
                'user_id'       => $this->arrAuthorInfo[$arrThreadInfo[$item['thread_id']]['user_id']]['user_id'],
                'user_name'     => $this->arrAuthorInfo[$arrThreadInfo[$item['thread_id']]['user_id']]['user_name'],
                'user_nickname' => $this->arrAuthorInfo[$arrThreadInfo[$item['thread_id']]['user_id']]['user_nickname'],
            );

            // 删ip
            if (isset($item['ip'])) {
                unset($item['ip']);
            }

            if (isset($item['quote']['ip'])) {
                unset($item['quote']['ip']);
            }
        }

        //同一主题聚合
        $arrPostList = array();
        foreach ($arrUserPost as $arrTmp) {
            $intTid         = $arrTmp['thread_id'];
            $arrThreadTypes = Tieba_Type_Thread::getTypeArray($arrThreadInfo[$intTid]['thread_types']);

            // 过滤主贴是ALa直播或录播贴的Post
            if ($arrThreadTypes['is_ala_live_video'] === true ||
                $arrThreadTypes['is_ala_video'] === true) {
                continue;
            }

            // 微信小程序过滤手百小程序分享贴
            if (isset($arrThreadTypes['is_smartapp_share']) && $this->_strCallFrom == 'weixin') {
                continue;
            }

            $content = $arrTmp['content'];
            if ($content[0]['text'] == '回复 ' && isset($content[1]['un']) && $content[1]['type'] == 4) {
                if ($content[2]['type'] == 0 && $content[2]['text'][0] == ' ' && $content[2]['text'][1] == ':') {
                    $arrTmp['content'][2]['text'] = mb_substr($content[2]['text'], 2, null, "UTF-8");
                }
            }
            $arrTmp['content']                                     = array(
                array(
                    'post_content' => $arrTmp['content'],
                    'create_time'  => $arrTmp['create_time'],
                    'post_type'    => $arrTmp['post_type'],
                    'post_id'      => $arrTmp['post_id'],
                ),
            );
            $arrTmp['title']                                       = html_entity_decode($arrTmp['title'], ENT_COMPAT, 'utf-8');
            $arrPostList[date("Y-m-d ", $arrTmp['create_time'])][] = $arrTmp;
        }
        $arrOut = array_values($arrPostList);
        return $arrOut;

    }

    /**
     * 裁剪图片
     * @param $url
     * @param $strSpec
     * @return mixed|string
     */
    private function _getSpecImageUrl($url, $strSpec)
    {
        $picSign = Util_Richtext_TransCoderConvertor::getPicSignFromUrl($url);
        $str     = Util_Richtext_TransCoderConvertor::picSign2Url($picSign, $strSpec);
        return $str;
    }

    /**
     * @param arrRawMedia
     * @param arrRawAbstractMedia
     * @param boolGetMediaFromPhotos
     * @return
     */
    private function _processMedia($arrRawMedia, $arrRawAbstractMedia = array(), $boolGetMediaFromPhotos = true)
    {
        $arrMedia = array();
        // 当前已push入output中的图片key
        $arrPicsKeys = array();
        // abstract raw media 中的图片key
        $arrAbstractPicsKeys = array();
        // abstract raw media 图片item
        $arrAbstractPicsItems = array();
        if (0 === $this->intNeedPhotoPic) {
            foreach ($arrRawMedia as $arrItem) {
                if ($arrItem['type'] === 'pic') {
                    $arrMedia[] = array(
                        'type'      => Molib_Util_RichText_Parser::SLOT_TYPE_IMG,
                        'small_pic' => $arrItem['small_pic'],
                        'big_pic'   => $arrItem['big_pic'],
                        'water_pic' => $arrItem['water_pic'],
                        'size'      => isset($arrItem['size']) ? $arrItem['size'] : 0,
                        'post_id'   => $arrItem['post_id'],
                        'width'     => isset($arrItem['picInfo']['orignal']['width']) ? $arrItem['picInfo']['orignal']['width'] : 320,
                        'height'    => isset($arrItem['picInfo']['orignal']['height']) ? $arrItem['picInfo']['orignal']['height'] : 320,
                    );
                }
                if ($arrItem['type'] === 'abstract') {
                    $arrItem['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_TEXT;
                    $arrMedia[]      = $arrItem;
                }
                if ($arrItem['type'] === 'flash') {
                    $arrItem['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_EMBED;
                    if (strpos($arrItem['vsrc'], 'xiaoying.tv')) {
                        $arrItem['e_type'] = Molib_Util_RichText_Parser::SLOT_TYPE_EMBED_XIAOYING;
                    }
                    $arrMedia[] = $arrItem;

                }
                if ($arrItem['type'] === 'music') {
                    $arrItem['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_MUSIC;
                    $arrMedia[]      = $arrItem;
                }
            }
        } else if (count($arrRawAbstractMedia) > 0) {
            foreach ($arrRawAbstractMedia as $arrRawAbstractMediaItem) {
                // 获取一份摘要原图片数据供后面作photo图片过滤和补缺
                if ($arrRawAbstractMediaItem['type'] === 'pic') {
                    $strPicSign             = Molib_Util_ClientImgLogic::getPicSignFromUrl($arrRawAbstractMediaItem['big_pic']);
                    $arrSign                = Space_Urlcrypt::decodePicUrlCrypt($strPicSign);
                    $strPicId               = isset($arrSign[1]) ? $arrSign[1] : $strPicSign;
                    $arrAbstractPicsKeys[]  = $strPicId;
                    $arrAbstractPicsItems[] = $arrRawAbstractMediaItem;
                }
            }
            foreach ($arrRawMedia as $arrItem) {
                if ($arrItem['type'] === 'pic') {
                    // photo数据会包含非首楼图片,而摘则要只需要显示首楼图片
                    $strPicSign = Molib_Util_ClientImgLogic::getPicSignFromUrl($arrItem['big_pic']);
                    $arrSign    = Space_Urlcrypt::decodePicUrlCrypt($strPicSign);
                    $strPicId   = isset($arrSign[1]) ? $arrSign[1] : $strPicSign;
                    // 判断picid是否是摘要图片的picid，是的话则添加这个图片信息;
                    // 如果没有从photo取图片信息，则直接取arrRawMedia的信息
                    if (($boolGetMediaFromPhotos && in_array($strPicId, $arrAbstractPicsKeys)) || !$boolGetMediaFromPhotos) {
                        $arrMedia[] = array(
                            'type'      => Molib_Util_RichText_Parser::SLOT_TYPE_IMG,
                            'small_pic' => $arrItem['small_pic'],
                            'big_pic'   => $arrItem['big_pic'],
                            'water_pic' => $arrItem['water_pic'],
                            'size'      => isset($arrItem['size']) ? $arrItem['size'] : 0,
                            'post_id'   => $arrItem['post_id'],
                            'width'     => isset($arrItem['picInfo']['orignal']['width']) ? $arrItem['picInfo']['orignal']['width'] : 320,
                            'height'    => isset($arrItem['picInfo']['orignal']['height']) ? $arrItem['picInfo']['orignal']['height'] : 320,
                        );
                        // 目前已经添加进media的photo key
                        $arrPicsKeys[] = $strPicId;
                    }
                }
                if ($arrItem['type'] === 'abstract') {
                    $arrItem['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_TEXT;
                    $arrMedia[]      = $arrItem;
                }
                if ($arrItem['type'] === 'flash') {
                    $arrItem['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_EMBED;
                    if (strpos($arrItem['vsrc'], 'xiaoying.tv')) {
                        $arrItem['e_type'] = Molib_Util_RichText_Parser::SLOT_TYPE_EMBED_XIAOYING;
                    }
                    $arrMedia[] = $arrItem;
                }
                if ($arrItem['type'] === 'music') {
                    $arrItem['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_MUSIC;
                    $arrMedia[]      = $arrItem;
                }
            }
            // 如果从photo取的数据有缺失,用摘要图片数据补上
            $intPicNum = 0;
            foreach ($arrAbstractPicsItems as $arrAbstractPicsItem) {
                $strPicSign = Molib_Util_ClientImgLogic::getPicSignFromUrl($arrAbstractPicsItem['big_pic']);
                $arrSign    = Space_Urlcrypt::decodePicUrlCrypt($strPicSign);
                $strPicId   = isset($arrSign[1]) ? $arrSign[1] : $strPicSign;
                // 判断图片是否已经存在arrMedia中，不存在则插入
                if (!in_array($strPicId, $arrPicsKeys)) {
                    $arrMedia[] = array(
                        'type'      => Molib_Util_RichText_Parser::SLOT_TYPE_IMG,
                        'small_pic' => $arrAbstractPicsItem['small_pic'],
                        'big_pic'   => $arrAbstractPicsItem['big_pic'],
                        'water_pic' => $arrAbstractPicsItem['water_pic'],
                        'size'      => $arrAbstractPicsItem['size'],
                        'post_id'   => $arrAbstractPicsItem['post_id'],
                        'width'     => 320,
                        'height'    => 320,
                    );
                    $intPicNum++;
                }
            }
        }
        return $arrMedia;
    }

    /**
     * @param strSmileFileName
     * @param arrSmile2TextTransData
     * @param arrSmileNotSupportAll
     * @return boolean
     */
    private function _processSmile($strSmileFileName, $arrSmile2TextTransData, $arrSmileNotSupportAll)
    {
        $strSmile         = $strSmileFileName;
        $strSmileTextName = isset($arrSmileNotSupportAll[$strSmileFileName]) ? $arrSmileNotSupportAll[$strSmileFileName] : $strSmileFileName;
        if ($strSmileTextName != $strSmileFileName) {
            $strSmile = '(' . $strSmileTextName . ')';
        } else {
            $strSmileTextName = $arrSmile2TextTransData[$strSmileFileName] ? $arrSmile2TextTransData[$strSmileFileName] : $strSmileFileName;
            if ($strSmileTextName != $strSmileFileName) {
                $strSmile = '(' . $strSmileTextName . ')';
            }
        }
        return $strSmile;
    }

    /**
     * @param intThreadType
     * @return boolean
     */
    private function _parseThreadType($intThreadType)
    {

        if ($intThreadType & self::IS_GOOD) {
            $arrThreadType['isGood'] = 1;
        } else {
            $arrThreadType['isGood'] = 0;
        }

        if ($intThreadType & self::IS_TOP) {
            $arrThreadType['isTop'] = 1;
        } else {
            $arrThreadType['isTop'] = 0;
        }

        if ($intThreadType & self::IS_VOTE) {
            $arrThreadType['isVote'] = 1;
        } else {
            $arrThreadType['isVote'] = 0;
        }

        if ($intThreadType & self::IS_BAKAN) {
            $arrThreadType['isBakan'] = 1;
        } else {
            $arrThreadType['isBakan'] = 0;
        }

        if ($intThreadType & self::IS_PIC) {
            $arrThreadType['isPic'] = 1;
        } else {
            $arrThreadType['isPic'] = 0;
        }

        if ($intThreadType & self::IS_VIDEO) {
            $arrThreadType['isVideo'] = 1;
        } else {
            $arrThreadType['isVideo'] = 0;
        }

        if ($intThreadType & self::IS_FACE) {
            $arrThreadType['isFace'] = 1;
        } else {
            $arrThreadType['isFace'] = 0;
        }

        if ($intThreadType & self::IS_MUSIC) {
            $arrThreadType['isMusic'] = 1;
        } else {
            $arrThreadType['isMusic'] = 0;
        }

        if ($intThreadType & self::IS_PROTAL) {
            $arrThreadType['isProtal'] = 1;
        } else {
            $arrThreadType['isProtal'] = 0;
        }

        if ($intThreadType & self::IS_NOTITLE) {
            $arrThreadType['isNotitle'] = 1;
        } else {
            $arrThreadType['isNotitle'] = 0;
        }

        if ($intThreadType & self::IS_HASTITLE) {
            $arrThreadType['isHastitle'] = 1;
        } else {
            $arrThreadType['isHastitle'] = 0;
        }


        if ($intThreadType & self::IS_SMARTAPP_SHARE_THREAD) {
            $arrThreadType['isSmartapp'] = 1;
        } else {
            $arrThreadType['isSmartapp'] = 0;
        }

        return $arrThreadType;
    }

    /**
     * @param void
     * @return boolean
     */
    private function _buildResp()
    {
        //此处需要增加判断，如果是走长连接，才进行此转换

        $arrResponse = array();

        $arrResponse['post_list'] = $this->arrPostList;
        $arrResponse['hide_post'] = $this->isHidePost == true ? 1 : 0;
        $arrResponse['mask_type'] = $this->mask_type;

        if ($arrResponse['mask_type'] == 4) {
            $arrResponse['mask_type'] = 1;
        }
        if ($arrResponse['mask_type'] == 2) {
            $arrResponse['mask_type'] = 3;
        }
        // 前面插入用户待审核视频假帖子
        if ($this->bolShowUnauditedVideoPostList && ($this->arrUnauditedVideoPostList || $this->arrShareVideoThreadList)) {
            $arrUnAuditPostList       = Molib_Service_VideoService::mergeUnauditedVideoPostList($this->arrUnauditedVideoPostList, $this->arrShareVideoThreadList);
            $arrResponse['post_list'] = array_merge($arrUnAuditPostList, $arrResponse['post_list']);
        }

        //白名单用户客态可全局隐藏贴子
        $intUserId = $this->_intUserId;
        $intOwnUid = $this->_intUserId;
        if ($intUserId != $intOwnUid) {
            $arrWordList = $this->getHideThreadUser();
            if (false !== $arrWordList && !empty($arrWordList)) {
                if (in_array($intUserId, $arrWordList)) {
                    $arrResponse['post_list'] = array();
                }
            }
        }

        // 一键设置卡片位置
        $arrResponse['view_card_num'] = $this->int_view_card_num;
        $arrResponse['same_day']      = $this->_intSameDay;

        //贴子回收站红点
        $arrMsg                               = $this->_getMsg();
        $arrResponse['reddot_deleted_thread'] = intval($arrMsg[Molib_Client_Define::DELETE_THREAD_MESSAGE_TYPE]) > 0 ? 1 : 0;

        $arrRes = array(
            'no'    => 0,
            'error' => 'success',
            'data'  => $arrResponse,
        );

        $this->_objResponse->setOutData($arrRes);
        return true;
    }

    /**
     * [getUserProps 读取词表，获取可全局隐藏贴子不可见的白名单用户]
     * <AUTHOR> <[<email address>]>
     * @param  [type] $user_id [description]
     * @return [type]          [description]
     */
    public function getHideThreadUser()
    {
        $handleWordServer = Wordserver_Wordlist::factory();
        $strKey           = 'uid';
        $arrKeys          = array($strKey);
        $strItemInfo      = $handleWordServer->getValueByKeys($arrKeys, self::WORDLIST_HIDETHREAD_USER);
        if (!isset($strItemInfo[$strKey]) && $strItemInfo[$strKey] == '') {
            Bingo_Log::warning('get tb_wordlist_redis_hideThread_User wordlist error');
            return false;
        }
        $strUser = $strItemInfo[$strKey];
        $arrTmp  = explode(',', $strUser);
        return $arrTmp;
    }

    /**
     * 无吧发帖过滤默认吧
     * @param $forumId
     * @param $forumName
     * @return bool
     */
    private function isFilterForum($forumName)
    {
        if (!$forumName) {
            return true;
        }
        return $forumName === $this->defaultName;
    }

    /**
     * getMsg
     * @param user_id
     * @return
     */
    private function _getMsg()
    {
        $arrParam['user_id'] = $this->_intUserId;
        $arrOut              = Tieba_Service::call('messagepool', 'getMsg', $arrParam);
        if (false === $arrOut || empty($arrOut)) {
            Bingo_Log::warning('call messagepool error. out:[' . serialize($arrOut) . ']');
            return false;
        }
        return $arrOut['data'];
    }
}