<?php
    /**
     * @copyright Copyright (c) www.baidu.com
     * <AUTHOR> <EMAIL>
     * @date 2011-3-15
     * @version
     */
    class Util_Richtext_RichTextResult {

        public $strResultText = '';       //处理后的文章
        public $intTextSegNum = 1;        //文本段个数
        public $intTextSegNo = 1;         //当前文本段序号
        public $intEmbedCount = 0;        //视频个数
        public $intSmileCount = 0;        //表情个数
        public $intImageCount = 0;        //图片个数
        public $bolHasMore = false;
        public $arrImageUrl = array();
        public $strVideoUrl = '';
        public $arrLinkUrl = array();
    }

    class Util_Richtext_TransCoderConvertor
    {
        public static $TRANSCODER_IMG_BASE_URL = 'http://gss3.bdstatic.com/84oSdTum2Q5BphGlnYG';
        public static $URL = "/timg?wapp&amp;quality=%d&amp;size=%s&amp;cut_x=%d&amp;cut_w=%d&amp;cut_y=%d&amp;cut_h=%d&amp;sec=%d&amp;di=%s&amp;wh_rate=null&amp;src=%s";
        public static $notEscapeURl = "/timg?mowebapp&quality=%d&size=%s&cut_x=%d&cut_w=%d&cut_y=%d&cut_h=%d&sec=%d&di=%s&wh_rate=null&src=%s";

        public static $URL_Light = "/timg?wapp&amp;quality=%d&amp;size=%s&amp;sec=%d&amp;di=%s&amp;wh_rate=null&amp;src=%s";
        public static $notEscapeURl_Light = "/timg?mowebapp&quality=%d&size=%s&sec=%d&di=%s&wh_rate=null&src=%s";

        /**
         * @param $baseURL
         */
        public static function setImgBaseURL($baseURL)
        {
            self::$TRANSCODER_IMG_BASE_URL = $baseURL;
        }

        /**
         * @param $strUrl
         * @param $intPageType
         * @return string
         */
        public static function getGateUrl($strUrl,$intPageType){
            $intTm = Bingo_Timer::getNowTime();
            $strMd5 = self::signURL($strUrl,$intTm);
            $strTpl = "http://gate.baidu.com/tc?t=wapp&amp;ssid=0&amp;from=0&amp;bd_page_type=%d&amp;uid=&amp;pu=&amp;sec=%d&amp;di=%s&amp;src=%s";
            return sprintf($strTpl,$intPageType,$intTm,$strMd5,rawurlencode($strUrl));
        }
        /**
         * 获取压缩图片地址
         * @modified by xiashanshan 20171023
         * 目前看除头像外的其他图片访问都会迁移到hiphotos服务
         * @param string $baseUrl 压缩图片url的规则
         * @param string $imgUrl  源图片url
         * @param int    $width   压缩后图片的最大宽度
         * @param int    $height  压缩后图片的最大高度
         * @param int    $quality 压缩后图片的质量
         * @param int    $cutX    裁切图片的X坐标
         * @param int    $cutW    裁切图片的宽度
         * @param int    $cutY    裁切图片的Y坐标
         * @param int    $cutH    裁切图片的高度
         * @return
         */
        public static function getImageUrl($url, $width, $height = 0, $quality =45, $notEscape=0, $cutX = 0, $cutW = 0, $cutY = 0,  $cutH = 0)
        {
            // 正文内容图片
            if (strpos($url, 'imgsrc.baidu.com') || strpos($url, 'imgsa.baidu.com')) {
                // hiphotos图片服务的在线处理尺寸大小限制为980
                $width = $width > 980 ? 980 : $width;
                $str = self::getHiphotosImageUrl($url, $width, $height, $quality, $notEscape);
                return $str;
            }
            $tm = 1369815402;//Bingo_Timer::getNowTime();
            $md5 = self::signURL($url, $tm);
            $height = intval($height);
            $width = intval($width);
            if ($height == 0){
                $size = 'w'.$width;
            }
            else{
                $size = 'b'.$width.'_'.$height;
            }
            if(0 == $notEscape){
                $str = sprintf(self::$TRANSCODER_IMG_BASE_URL . self::$URL, $quality, $size, $cutX, $cutW, $cutY, $cutH, $tm, $md5, rawurlencode($url));
            }
            else{//frsabs frs页摘要
                $str = sprintf(self::$TRANSCODER_IMG_BASE_URL . self::$notEscapeURl, $quality, $size, $cutX, $cutW, $cutY, $cutH, $tm, $md5, rawurlencode($url));
            }
            return $str;
        }

        /**
         * @param  url
         * @param  width
         * @param  height
         * @param  quality
         * @return [type]
         */
        public static function getHiphotosImageUrl($url, $width, $height, $quality, $notEscape = 0) {
            $picSign = self::getPicSignFromUrl($url);
            //url spec gif
            $picSpecGif = 'g=0';

            //url spec size
            $picSpecWH = 'w=' . $width;

            // quality
            $picSpecQuality = 'q='. $quality;

            //url params
            $picSpec = $picSpecWH . ';' . $picSpecQuality . ';' . $picSpecGif;

            $str = self::picSign2Url($picSign, $picSpec);

            if(0 == $notEscape) {
                $str .= '?&amp;src=' . urlencode($url);
            } else {
                $str .= '?&src=' . urlencode($url);
            }
            return $str;
        }

        /**
         * @param $url
         * @param int $screenWidth
         * @param int $quality
         * @param int $notEscape
         * @return null|string|string[]
         */
        public static function getLightappImageUrl($url, $screenWidth=720, $quality=100, $notEscape=0)
        {
            $screenWidth = intval($screenWidth);
            //url sign
            $picSign = self::getPicSignFromUrl($url);
            //url spec gif
            $picSpecGif = 'g=' . 0;
            //url spec size
            if( $screenWidth <= 480 ){
                //$picSpecWH = 'whfpf=' . 101 . ',' . 101 . ',' . 40;
                $picSpecWH = 'whfpf=' . 106 . ',' . 106 . ',' . 40;
            } elseif( $screenWidth <= 720 ){
                $picSpecWH = 'whfpf=' . 210 . ',' . 210 . ',' . 40;
            } else {
                //$picSpecWH = 'whfpf=' . 306 . ',' . 306 . ',' . 40;
                $picSpecWH = 'whfpf=' . 315 . ',' . 315 . ',' . 40;
            }
            $picSpecQuality = 'q=100';
            //url params
            $picSpec = $picSpecWH . ';' . $picSpecQuality . ';' . $picSpecGif;

            $str = self::picSign2Url($picSign, $picSpec);

            return $str;
        }

        /**
         * frs imgcdn get sign form "http://imgsrc.baidu.com/forum/pic/item/c91373f082025aafd815170ffaedab64024f1aca.jpg
         * @param $strUrl string
         * @return $picSign string
         */
        public static function getPicSignFromUrl($strUrl){
            $arrTmp = explode('/', $strUrl);
            $intLength = count($arrTmp);
            $strPicName = $arrTmp[$intLength - 1]; // c91373f082025aafd815170ffaedab64024f1aca.jpg
            $arrTmp2 = array_filter(explode('.', $strPicName));
            $picSign = '';
            if (isset($arrTmp2[0])) {
                $picSign = $arrTmp2[0];
            }
            return $picSign;
        }

        /**
         * @param $picSign string
         * @return $picSign string
         */
        public static function picSign2Url($picSign, $picSpec) {
            $arrOutput = Space_Urlcrypt::decodePicUrlCrypt($picSign);
            if($arrOutput === false || !isset($arrOutput[0]) ||
                ($intFid && intval($arrOutput[0]) != $intFid)){
                Bingo_Log::warning("decode pic id error! input[".$strId."]");
                return '';
            }
            $foreignKey = $arrOutput[0];
            $picId = $arrOutput[1];
            $obj = new Bd_Pic_UrlCrypt();
            $arrIput = array(
                "pic_id" => $picId,
                'foreign_key' => $foreignKey,
                "product_name" => "forum",
                "pic_spec" => $picSpec,//图片处理特征，abpic表示小图，其他参加文档或wiki
            );
            $arrReqs[] = $arrIput;
            $ret = $obj->BatPid2Url($arrReqs);
            $strUrl = '';
            if (isset($ret['resps'][0])) {
                $strUrl = $ret['resps'][0];
                //域名统一 *.hiphotos.baidu.com 统一转换成c.hiphotos.baidu.com
                $strUrl = preg_replace('/http:\/\/[a-z].hiphotos/','http://c.hiphotos',$strUrl);
            }
            return $strUrl;
        }


        /**
         * @param $url
         * @param $tm
         * @param string $key
         * @return string
         */
        private static function signURL($url, $tm, $key = 'wisetimgkey_noexpire_3f60e7362b8c23871c7564327a31d9d7')
        {
            // 加密 'wisetimgkey' + 时间 + url
            $md5 = md5($key . $tm . $url);

            return $md5;
        }

    }

    class Util_StructedPb {

        const SLOT_TYPE_TEXT = 0;
        const SLOT_TYPE_LINK = 1;
        const SLOT_TYPE_SMILE = 2;
        const SLOT_TYPE_IMG = 3;
        const SLOT_TYPE_AT = 4;
        const SLOT_TYPE_BR = 7;
        const SLOT_TYPE_EMBED = 5;
        const SLOT_TYPE_MUSIC = 6;
        const SLOT_TYPE_BDHD = 8;
        const SLOT_TYPE_PHONE = 9;
        const SLOT_TYPE_VOICE = 10;
        const SLOT_TYPE_SMILE3 = 11; //表情商店的新表情
        const SLOT_TYPE_EMBED_XIAOYING = 12;//xiaoying
        const SLOT_TYPE_BUTTONLINK = 13;//button链接
        const SLOT_TYPE_PICLINK = 14;//图片链接
        const SLOT_TYPE_EMBED_MOVIDEO = 15;//客户端短视频
        const SLOT_TYPE_GRAFFITI = 16; // 涂鸦类型
        const SLOT_TYPE_TOPIC = 18; // 热议##
        const SLOT_TYPE_ALA = 19; //ALA 用在了pb/ext/Ala.php
        const SLOT_TYPE_MEME = 20; // 野表情
        const SLOT_TYPE_SMARTAPP = 21; // 小程序卡片

        const REG_HREF_IS_AT = '|/i/sys/jump\?un=(.*)|i';
        const REG_PHONE_NUM_SPLIT = '/((?<!\d)((\+[\d]{2}[\s]?)?1[358][0-9]{9,9})(?!\d))|(?<!\d)(1[358]\d-\d{4}-\d{4}(?!\d))|((?<!\d)((0\d{2,3}[- ]?)?[2-9]\d{6,7})(?!\d))|((?<!\d)([48]00[0-9]{7,7})(?!\d))/iU';
        const REG_IMG_NEWSMILE_PACKET_NAME = '/.*?(?=_)/';
        const REG_IMG_NEWSMILE_SMALL = '/.*(?=\.)/';
        const REG_IMG_NEWSMILE_BIG = '/.*(?=_h\.)/';
        const REG_IMG_NEWSMILE_SUFFIX = '/(?<=\.).*/';
        const CHECK_SPAM_URL = 'http://tieba.baidu.com/mo/q/checkurl?url=';
        const CLIENT_TYPE_IPHONE = 1;
        public static $arrTypeMap = array(
            'abstract' => self::SLOT_TYPE_TEXT,
            'link' => self::SLOT_TYPE_LINK,
            'smile' => self::SLOT_TYPE_SMILE,
            'flash' => self::SLOT_TYPE_EMBED,
            'music' => self::SLOT_TYPE_MUSIC,
            'pic' => self::SLOT_TYPE_IMG,
            'voice' => self::SLOT_TYPE_VOICE,
        );
        private $_arrBdhdLink = array('bdhd://', 'ed2k://');

        //设置clientType
        private static $_intClientType = null;
        //设置clientVersion
        private static $_intClientVersion = null;
        private static $_fid = 0;

        /**
         * 处理条件
         * @var Molib_Util_RichTextParserCondition
         */
        private $objCondition;

        /**
         * @param Mo_Util_Richtext_RichTextProcessorCondition $objCondition
         * @param $arrText
         * @param bool $_boolEmoji
         * @param bool $boolIsAllOrigin
         * @return Mo_Util_Richtext_RichTextResult
         */
        public function process(Util_RichTextProcessorCondition $objCondition,$arrText,$boolIsAllOrigin=false, $_boolEmoji=true){
            $this->objCondition = $objCondition;
            $arrSlotSet = array();
            //这个值是处理<br>的时候计算最多允许的空行数的
            $intCurrentBrCount = 0;
            foreach ($arrText as $arrTmp){
                $arrEmojiCodes = Molib_Conf_Emoji::$arrEmojiCodes;
                if ( $_boolEmoji && $arrTmp['tag'] == "plainText"){
                    $arrTextSlot = preg_split('/(\[.*\])/iU', $arrTmp['value'], -1, PREG_SPLIT_NO_EMPTY|PREG_SPLIT_DELIM_CAPTURE);
                    $arrTmp['value']  = '';
                    foreach ($arrTextSlot as $strTmp){
                        if($strTmp{0} == '['){
                            if(isset($arrEmojiCodes[$strTmp])){
                                $arrTmp['value'] .= $arrEmojiCodes[$strTmp];
                            }
                            else{
                                $arrTmp['value'] .= $strTmp;
                            }
                        }
                        else{
                            $arrTmp['value'] .= $strTmp;
                        }
                    }
                }

                $arrSlot = null;

                if ($arrTmp['tag'] != "br"){
                    $intCurrentBrCount = 0;
                }
                if ($arrTmp['tag'] == "img"){
                    $arrSlot = $this->_processImg($arrTmp, $boolIsAllOrigin);
                }elseif ($arrTmp['tag'] == "a"){
                    $arrSlot = $this->_processLink($arrTmp);
                }elseif ($arrTmp['tag'] == "embed"){
                    $arrSlot = $this->_processEmbed($arrTmp);
                }elseif ($arrTmp['tag'] == "br"){
                    if (!$this->objCondition->bolDisplayNewLine){
                        continue;
                    }
                    $arrSlot = array(
                        'type' => self::SLOT_TYPE_BR,
                        'text'  => $this->objCondition->strNewLineReplace,
                    );
                    if ($objCondition->intNewLineCount > 0){
                        $intCurrentBrCount +=1;
                        if ($intCurrentBrCount > $objCondition->intNewLineCount ){
                            continue;
                        }
                    }
                }
                //处理出错
                if ($arrSlot === false){
                    continue;
                }
                if ($arrTmp['tag'] == "plainText"){
                    $strTmp = $arrTmp['value'];
                    $strTmp = html_entity_decode($strTmp,ENT_COMPAT,'UTF-8');
                    if ($strTmp == "\n"){
                        continue;
                    }
                    if ($intCurrentBrCount > 0){
                        //两个连续<br/>之间的全空格文本需要忽略

                        $strTrimTmp = trim($strTmp);
                        if (!strlen($strTrimTmp)){
                            continue;
                        }
                        //BR计数清0
                        $intCurrentBrCount = 0;
                    }
                    if ($this->objCondition->bolBlankTrim){
                        $strTmp = preg_replace('/[ ]+/i', ' ', $strTmp);
                    }


                    $arrSlot = array(
                        'type' => self::SLOT_TYPE_TEXT,
                        'text' => $strTmp,
                    );
                    //处理百度影音
                    $arrOldSlot = $arrSlot;
                    $hasBdhd = false;
                    if ($objCondition->bolParseBdhd) {
                        foreach ($this->_arrBdhdLink as $strBdhdLink) {
                            if (false !== $intBdhdPos = strpos($strTmp, $strBdhdLink)) {
                                $arrSlot['text'] = substr($strTmp, 0, $intBdhdPos);
                                $arrOldSlot = $arrSlot;
                                $hasBdhd = true;
                                $arrSlot = array(
                                    'type' => self::SLOT_TYPE_BDHD,
                                    'link' => substr($strTmp, $intBdhdPos),
                                );
                                break;
                            }
                        }
                    }
                    //process the phone number  add by wyx
                    $strTmp = $arrOldSlot['text'];
                    if ( $objCondition->bolParsePhone &&
                        0 !== ($intMatchNum = preg_match_all(self::REG_PHONE_NUM_SPLIT, $strTmp, $arrAllMatchs, PREG_SET_ORDER|PREG_OFFSET_CAPTURE)) )
                    {
                        $intLastPos = 0;
                        foreach($arrAllMatchs as $arrOneMatch){
                            $strPhoneNum = $arrOneMatch[0][0];
                            $intOffset = $arrOneMatch[0][1];
                            $intLen = $intOffset - $intLastPos;
                            $arrTmpSlot = array(
                                'type' => self::SLOT_TYPE_TEXT,
                                'text' => substr($strTmp, $intLastPos, $intLen)
                            );
                            $intPhoneType = 2;
                            if($strPhoneNum{0} == '+' || $strPhoneNum{0} == '1'){
                                $intPhoneType = 1; //mobile phone
                            }

                            $arrSlotSet[] = $arrTmpSlot;
                            $arrTmpSlot = array(
                                'type' => self::SLOT_TYPE_PHONE,
                                'text' => $strPhoneNum,
                                'phonetype' => $intPhoneType
                            );

                            $arrSlotSet[] = $arrTmpSlot;
                            $intLastPos = $intOffset + strlen($strPhoneNum);
                        }
                        $arrSlot['type'] = self::SLOT_TYPE_TEXT;
                        $arrSlot['text'] = substr($strTmp, $intLastPos);
                    }//not have phone number
                    else if($hasBdhd){
                        $arrSlotSet[] = $arrOldSlot;
                    }
                }
                $arrSlotSet[] = $arrSlot;
            }

            $arrContent = array();
            $objShowResult = new Util_Richtext_RichTextResult();
            $bolIsText = false;
            $arrTextSlot = array(
                'type' => self::SLOT_TYPE_TEXT,
                'text' => '',
            );
            foreach ($arrSlotSet as $arrSlot){
                $intType = $arrSlot['type'];
                if ($intType == self::SLOT_TYPE_TEXT || $intType==self::SLOT_TYPE_BR){
                    $arrTextSlot['text'] .= $arrSlot['text'];
                }else{
                    if ($intType == self::SLOT_TYPE_AT){
                        $objShowResult->intAtCount++;
                        if ($this->objCondition->intAtDisplayCount != -1
                            && $objShowResult->intAtCount>$this->objCondition->intAtDisplayCount){
                            continue;
                        }
                    }
                    if ($intType == self::SLOT_TYPE_IMG){
                        $objShowResult->intImageCount++;
                        if ($this->objCondition->intImageDisplayCount != -1
                            && $objShowResult->intImageCount>$this->objCondition->intImageDisplayCount){
                            continue;
                        }
                    }
                    if ($intType == self::SLOT_TYPE_SMILE){
                        $objShowResult->intSmileCount++;
                        if ($this->objCondition->intSmileDisplayCount != -1
                            && $objShowResult->intSmileCount>$this->objCondition->intSmileDisplayCount){
                            continue;
                        }
                    }
                    if ($intType == self::SLOT_TYPE_EMBED){
                        $objShowResult->intEmbedCount++;
                    }
                    if ($intType == self::SLOT_TYPE_MUSIC){
                        $objShowResult->intMusicCount++;
                    }
                    if (strlen($arrTextSlot['text'])){
                        $arrContent[] = $arrTextSlot;
                        $arrTextSlot = array(
                            'type' => self::SLOT_TYPE_TEXT,
                            'text' => '',
                        );
                    }
                    $arrContent[] = $arrSlot;
                }
            }

            if (strlen($arrTextSlot['text'])){
                $arrContent[] = $arrTextSlot;
            }
            $arrContentOld = $arrContent;
            $arrContent = array();
            $bolLastIsImg = false;
            //图片后面的多个连续\n都
            foreach ($arrContentOld as $arrSlot){
                if ($arrSlot['type'] == self::SLOT_TYPE_TEXT){
                    if ($bolLastIsImg){
                        if (preg_match("/^\n*/",$arrSlot['text'])){
                            $arrSlot['text'] = ltrim($arrSlot['text']);
                        }
                    }else{

                    }
                }
                $arrContent[] = $arrSlot;
                if ($arrSlot['type'] == self::SLOT_TYPE_IMG){
                    $bolLastIsImg = true;
                }else{
                    $bolLastIsImg = false;
                }
            }
            // add by wangbo
            //文本后，图片前的\n都去掉
            /**
             * Parse.php 和  ../RichTextParse.php 也有一样逻辑 （看文件开头注释）
             * by chenjinya
             **/
            $arrContentOld = $arrContent;
            $arrContent = array();
            $arrSize = count($arrContentOld);
            for ($i= 0;$i< $arrSize; $i++){
                $arrSlot = $arrContentOld[$i];
                $arrNextSlot = $i < $arrSize - 1 ? $arrContentOld[$i + 1] : null;
                if ($arrSlot['type'] == self::SLOT_TYPE_TEXT)
                {
                    if ($arrNextSlot == null || ($arrNextSlot && $arrNextSlot['type'] == self::SLOT_TYPE_IMG))
                    {
                        if (preg_match("/\n*$/",$arrSlot['text'])){
                            $arrSlot['text'] = rtrim($arrSlot['text']);
                        }
                    }
                    //add by fengzhen,##话题解析
                    $ifNewContent = false;
                    $arrTopicContent = $this->getStructFromTopicContent($arrSlot['text']);
                    if(!empty($arrTopicContent)){
                        foreach($arrTopicContent as $temp){
                            if($temp['tag'] == "plainText"){
                                $arrContent[] = array(
                                    'type' => self::SLOT_TYPE_TEXT,
                                    'text' => $temp['value'],
                                );
                                $ifNewContent = true;
                            }
                            if($temp['tag'] == "a"){
                                $resTopic = $this->_processLink($temp,true);
                                if(!empty($resTopic)){
                                    $arrContent[] = $resTopic;
                                    $ifNewContent = true;
                                }
                            }
                        }
                        if(!$ifNewContent){
                            $arrContent[] = $arrSlot;
                        }
                        continue;
                    }
                    $arrContent[] = $arrSlot;
                    continue;
                    //end,add by fengzhen,##话题解析
                }
                $arrContent[] = $arrSlot;

            }
            // end of add by wangbo
            $objShowResult->arrContent = $arrContent;
            return $objShowResult;
        }

        /**
         * @param $intClientType
         */
        public function setClientType($intClientType){
            self::$_intClientType = $intClientType;
        }

        /**
         * @param $intClientVersion
         */
        public function setClientVersion($intClientVersion){
            self::$_intClientVersion = $intClientVersion;
        }
        /**
         * 设置吧id
         * <AUTHOR>
         * @param field_type bare_field_name
         * @return return_type
         */
        public function setFid($intFid){
            self::$_fid = $intFid;
        }

        /**
         * 解析一段文本中的##话题  为 a标签
         * @param $string
         * @param int $length
         * @param int $maxNum
         * @return array
         */
        public function getStructFromTopicContent($string,$length = 30, $maxNum = 10){

            $pattern_replace = '/#\s+#/';
            $patterns = '/#([^#]+)#/';
            $arrHitWord = array();
            $prefix = '<a href="http://tieba.baidu.com/mo/q/hotMessage?topic_id=0&fid='.self::$_fid.'&topic_name=';
            $tmp_content = preg_replace($pattern_replace, '##', $string);
            $arrReplaceWord = array();
            if (preg_match_all($patterns, $tmp_content, $result) > 0) {
                $num = 0;
                foreach($result[0] as $match){
                    if(mb_strlen($match, 'UTF8') > $length){
                        continue;
                    }
                    if(strpos($match,"\n") !== false){
                        continue;
                    }
                    if(!$this->_checkIfMeanStr($match)){
                        continue;
                    }
                    $arrHitWord[] = $match;
                    $matchUrl = htmlspecialchars(trim($match,'#'));
                    /**
                     * 春节表情包，活动推广，Begin
                     * 2016.12.05
                     */
                    $activityTopicKeyword = '春节表情包';
                    if($matchUrl == $activityTopicKeyword) {
                        $activityPageUrl = 'https://tieba.baidu.com/n/spring-festival-expression/home?tieba_hybrid_enabled=1';
                        $arrReplaceWord[] = '<a href="' . $activityPageUrl . '">' . htmlspecialchars($match) . '</a>';
                    }else {
                        $arrReplaceWord[] = $prefix.$matchUrl. '">' . htmlspecialchars($match). '</a>';
                    }
                    /**
                     * End
                     */
                    //$arrReplaceWord[] = $prefix.$matchUrl. '">' . htmlspecialchars($match). '</a>';
                    $num ++;
                    if($num >= $maxNum){
                        break;
                    }
                }
                if(empty($arrHitWord) || empty($arrReplaceWord)){
                    return array();
                }
                $string = str_replace($arrHitWord, $arrReplaceWord, $string);
                $conf = array(
                    'a' => 1,
                    'br' => 1,
                    'embed' => 1,
                    'html' => 1,
                    'body' => 1,
                    'head' => 1,
                    'img' => 1,
                );
                $string = preg_replace('/\0/', '', $string);
                $content_dom = rich_text_parser($string,$conf);
                if(is_array($content_dom[0]['value'][1]['value']) && !empty($content_dom[0]['value'][1]['value'])){
                    return $content_dom[0]['value'][1]['value'];
                }
            }
            return array();
        }

        /**
         * @param $str
         * @return bool
         */
        private function _checkIfMeanStr($str){
            if (preg_match("/[\x7f-\xff]/", $str)){
                return true;
            }
            if(preg_match('/[0-9]/', $str)){
                return true;
            }
            if(preg_match('/[a-zA-Z]/', $str)){
                return true;
            }
            return false;
        }

        /**
         * @param $value
         * @return array|bool
         */
        private function _processEmbed($value){

            // xiaoying add by mahong
            if(strpos($value['vsrc'], 'xiaoying.tv')){
                return array(
                    'type' => self::SLOT_TYPE_EMBED,
                    'e_type' => self::SLOT_TYPE_EMBED_XIAOYING,
                    'width' => $value['width'],
                    'height' => $value['height'],
                    'bsize' => "{$value['width']},{$value['height']}",
                    'pic_width' => $value['pic_width'],
                    'pic_height' => $value['pic_height'],
                    'during_time' => $value['duration'],
                    'text' => $value['vsrc'],
                    'link' => $value['vsrc'],
                    'src' => $value['vpic'],
                );
            }

            //客户端短视频
            if($value['e_type'] === 'movideo' || strpos($value['vsrc'], 'tieba.baidu.com')  ){
                return array(
                    'type' => self::SLOT_TYPE_EMBED,
                    'e_type' => self::SLOT_TYPE_EMBED_MOVIDEO,
                    'width' => $value['width'],
                    'height' => $value['height'],
                    'bsize' => "{$value['width']},{$value['height']}",
                    'during_time' => $value['duration'],
                    'origin_size' => $value['size'],
                    'text' => $value['vsrc'],
                    'link' => $value['vhsrc'],
                    'src' => $value['vpic'],
                );
            }

            if (!empty($value['vsrc'])){
                $strUrl = $value['vsrc'];
            }elseif (!empty($value['src'])){
                $strUrl = $value['src'];
            }else {
                return false;
            }

            $intType = self::SLOT_TYPE_EMBED;
            //音乐贴
            if (strpos($strUrl,'box.baidu.com')){
                //音乐的不处理
                return false;
            }
            return array(
                'type' => $intType,
                'text' => $strUrl,
            );
        }

        /**
         * @param $strImgUrl
         * @param $strSmileName
         * @param $strWidth
         * @param $strHeight
         * @return array
         */
        private function _processSmile3($strImgUrl, $strSmileName, $strWidth, $strHeight) {

            $intPos = strpos($strImgUrl, '/tb/editor/images');
            $str = substr($strImgUrl, $intPos);
            $arrTmp = explode('/', $str);
            $strStaticFileName = $arrTmp[6];
            $clientWidth = $strWidth;
            $clientWidthBig = 640;
            //客户端宽度小于720(ios 640)给小图，大小固定为160。
            do {
                if ($clientWidth < $clientWidthBig) {
                    $strWidth = 160;
                    $strHeight = 160;
                } else {
                    $strWidth = 240;
                    $strHeight = 240;
                    $arrMatch = array();
                    $ret = preg_match(self::REG_IMG_NEWSMILE_BIG, $strStaticFileName, $arrMatch);
                    if ($ret !== 0) {
                        //已经是大的了，兼容老测试数据。
                        break;
                    }

                    $arrMatch = array();
                    $ret = preg_match(self::REG_IMG_NEWSMILE_SMALL, $strStaticFileName, $arrMatch);
                    if ($ret !== 0) {
                        $strStaticFileNameSmall = $arrMatch[0];
                    } else {
                        Bingo_Log::warning("process smile3 : invalid static file name : [$strStaticFileName], smile will be show as static picture");
                        return array (
                            'type' => self::SLOT_TYPE_IMG,
                            'src' => $strImgUrl,
                            'bsize' => $strWidth . ',' . $strHeight,
                        );
                    }

                    $arrMatch = array();
                    $ret = preg_match(self::REG_IMG_NEWSMILE_SUFFIX, $strStaticFileName, $arrMatch);
                    if ($ret !== 0) {
                        $strStaticFileNameSuffix = $arrMatch[0];
                    } else {
                        Bingo_Log::warning("process smile3 : invalid static file name : [$strStaticFileName], smile will be show as static picture");
                        return array (
                            'type' => self::SLOT_TYPE_IMG,
                            'src' => $strImgUrl,
                            'bsize' => $strWidth . ',' . $strHeight,
                        );
                    }

                    $strStaticFileNameNew = $strStaticFileNameSmall . '_h.' . $strStaticFileNameSuffix;
                    $strImgUrl = str_replace($strStaticFileName, $strStaticFileNameNew, $strImgUrl);
                    $strStaticFileName = $strStaticFileNameNew;
                }
            } while (false);

            $strDynamicFileName = $strStaticFileName;
            $strDynamicFileName{0} = 'd';
            $len = strlen($strDynamicFileName);
            $strDynamicFileName{$len - 1} = 'f';
            $strDynamicFileName{$len - 2} = 'i';
            $strDynamicFileName{$len - 3} = 'g';

            $strImgUrlDynamic = str_replace($strStaticFileName, $strDynamicFileName, $strImgUrl);


            $arrMatch = array();
            $ret = preg_match(self::REG_IMG_NEWSMILE_PACKET_NAME, $strSmileName, $arrMatch);
            if ($ret !== 0) {
                $packetName = $arrMatch[0];
            } else {
                $packetName = "未知表情包";
            }

            $iconFile = "panel.png";

            $iconUrl = str_replace($strStaticFileName, $iconFile, $strImgUrl);

            return array (
                'type' => self::SLOT_TYPE_SMILE3,
                'c' => $strSmileName,
                'static' => $strImgUrl,
                'dynamic' => $strImgUrlDynamic,
                'height' => $strHeight,
                'width' => $strWidth,
                'icon' => $iconUrl,
                'packet_name' => $packetName,
            );

        }

        /**
         * @param $value
         * @param $boolIsAllOrigin
         * @return array
         */
        private function _processImg($value, $boolIsAllOrigin){
            $strImgUrl = $value['src'];

            $strZhangbaiImoUrl = 'http://imo.baidu.com/static/images/emotion/';//Conf::get('zhangbai_smile_host_imo','');
            $strZhangbaiTUrl = 'http://t.baidu.com/static/images/emotion/';//Conf::get('zhangbai_smile_host_t','');
            $strClientUrl = 'http://static.tieba.baidu.com/tb/editor/images/';//Conf::get('tieba_smile_host','');
            $strCdnClientUrl = 'http://tb2.bdstatic.com/tb/editor/images/';
            // added by xiashanshan, 表情适配
            $strClientProxyUrl = 'https://gsp0.baidu.com/5aAHeD3nKhI2p27j8IqW0jdnxx1xbK/tb/editor/images/'; // 等同于static.tieba.baidu.com/tb/editor/images/
            $strCdnHttpsClientUrl = 'https://tb2.bdstatic.com/tb/editor/images/'; // 适配客户端迁移https表情

            $intClientPos = strpos($strImgUrl, $strClientUrl);
            $strClientProxyPos = strpos($strImgUrl, $strClientProxyUrl);
            $intCdnClientPos = strpos($strImgUrl, $strCdnClientUrl);
            $intCdnHttpsClientPos = strpos($strImgUrl, $strCdnHttpsClientUrl);
            $intZhangbaiImoUrl = strpos($strImgUrl, $strZhangbaiImoUrl);
            $intZhangbaiTUrl = strpos($strImgUrl,$strZhangbaiTUrl);
            if ($intClientPos!== false || $intCdnClientPos!== false
                || $strClientProxyPos !== false || $intCdnHttpsClientPos !== false){
                //先判断是否为表情商店的新表情
                if (!empty($value['text'])) {
                    $strSmileName = $value['text'];
                } else {
                    $strSmileName = "新表情";//为空的话客户端要挂。
                }
                if (!empty($value['width'])) {
                    $strSmileWidth = $value['width'];
                } else {
                    $strSmileWidth = "200";
                }
                if (!empty($value['height'])) {
                    $strSmileHeight = $value['height'];
                } else {
                    $strSmileHeight = "200";
                }
                if (!empty($value['class'])) {
                    if ($value['class'] == "BDE_Smiley3") {
                        //走新表情策略
                        return $this -> _processSmile3($strImgUrl, $strSmileName, $strSmileWidth, $strSmileHeight);
                    }
                }
                $intPos = strpos($strImgUrl, '/tb/editor/images');
                $str = substr($strImgUrl,$intPos);
                $arrTmp = explode('/',$str);
                //不支持手机发的表情
                if ($arrTmp[4] == 'wise'){
                    return false;
                }
                //神来一句表情处理为图片
                if ($arrTmp[4] == 'qw_cat_small') {
                    return $this->_processSlotTypeImg($strImgUrl, $value);
                }

                $strFileName = $arrTmp[5];
                $strFileName = substr($strFileName,0,strpos($strFileName,'.'));
                //把经典表情替换为原来的表情
                $arrJdSmileData = Molib_Conf_JdSmileData::$arrJdSmileData;
                if ( isset($arrJdSmileData[$strFileName]) ){
                    $strFileName = $arrJdSmileData[$strFileName];
                }

                if ($arrTmp[4] == 'face'){//修复image_emoticon表情在客户端显示错乱问题
                    $strFileName = preg_replace('/^i_f(?!0)|^i_f01|^i_f0(?!1)/i', 'image_emoticon', $strFileName);
                }

                return array(
                    'type' => self::SLOT_TYPE_SMILE,
                    //@TODO 修改为表情列表
                    'text' => strval($strFileName),
                    'src' => $strImgUrl,
                );

            }else if ($intZhangbaiImoUrl!==false||$intZhangbaiTUrl!==false){//掌百表情
                $intPos = strpos($strImgUrl, '/static/images/emotion');
                $str = substr($strImgUrl,$intPos);
                $arrTmp = explode("/", $str);
                $strFileName = $arrTmp[4];
                $arrZhangbaiToClient = Molib_Conf_ZhangbaiSmileData::$arrZhangbaiToClient;
                if (isset($arrZhangbaiToClient[$strFileName])){
                    $strFileName = $arrZhangbaiToClient[$strFileName];
                    if(strpos($strFileName,'.')===false){
                        return array(
                            'type' => self::SLOT_TYPE_TEXT,
                            'text' => "(".strval($strFileName).")",
                        );
                    }
                    $strFileName = substr($strFileName,0,strpos($strFileName,'.'));
                }
                //把经典表情替换为原来的表情
                $arrJdSmileData = Molib_Conf_JdSmileData::$arrJdSmileData;
                if ( isset($arrJdSmileData[$strFileName]) ){
                    $strFileName = $arrJdSmileData[$strFileName];
                }
                return array(
                    'type' => self::SLOT_TYPE_SMILE,
                    //@TODO 修改为表情列表
                    'text' => strval($strFileName),
                    'src' => $strImgUrl,
                );

            }
            else if (isset($value['class']) && $value['class'] == "BDE_Graffiti") {
                //走新表情策略
                if($this->objCondition->bolGraffitiToImg){
                    //涂鸦以图片形式展示在pb上
                    return $this->_processSlotTypeImg($strImgUrl, $value, $boolIsAllOrigin);
                }else{
                    return $this->_processGraffiti($strImgUrl, $value);
                }
            }
            else if (isset($value['class']) && $value['class'] == "BDE_Meme") {
                //野表情
                return $this->_processMeme($value);
            }
            else {
                return $this->_processSlotTypeImg($strImgUrl, $value, $boolIsAllOrigin);
            }

        }

        /**
         * @param $value
         * @param bool $isTopic
         * @return array|bool
         */
        private function _processLink($value,$isTopic = false){
            $strHref = $value['href'];
            $strText = $value['value'][0]['value'];
            $intUid = isset($value['portrait']) ? Tieba_Ucrypt::decode($value['portrait']) : 0;
            if (empty($strText)){
                return false;
            }
            $bolIsAt = false;
            $strUn = '';
            $ret = preg_match(self::REG_HREF_IS_AT,$strHref,$arrMatch);
            if ($ret != 0){
                $bolIsAt = true;
                $strUn = $arrMatch[1];
                $strUn = rawurldecode($strUn);
                if( false == Bingo_Encode_Uconv::isUtf8($strUn)){
                    $strUn = Bingo_Encode::convert($strUn,Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
                }
            }

            if ($bolIsAt){
                if('@' == $strText[0]){
                    return array(
                        'type' => self::SLOT_TYPE_AT,
                        'text'  => $strText,
                        'un' => substr($strText,1),
                        'uid' => $intUid,
                    );
                }else{
                    return array(
                        'type' => self::SLOT_TYPE_AT,
                        'text'  => $strUn,
                        'un' => $strUn,
                        'uid' => $intUid,
                    );
                }
            }
            if($isTopic){//add by fengzhen,##话题解析
                return array(
                    'type' => self::SLOT_TYPE_TOPIC,
                    'text'  => htmlspecialchars_decode($strText),
                    'link' => htmlspecialchars_decode($strHref),
                );
            }
            $tmp = html_entity_decode($strText,ENT_COMPAT,'UTF-8');
            if ( $this->objCondition->intLinkTextLen != -1
                && strlen($strText) > $this->objCondition->intLinkTextLen){

                $tmp = mb_substr($tmp,0,$this->objCondition->intLinkTextLen,Bingo_Encode::ENCODE_UTF8);
                $tmp = $tmp.$this->objCondition->strLinkMoreText;
            }
            $strText = $tmp;

            $strHref = html_entity_decode($strHref,ENT_COMPAT,'UTF-8');

            if ($this->objCondition->bolCheckSpamUrl) {
                $strBaiduPattern = "/^http[s]?:\/\/[^\/\\\\]*(tieba\.baidu\.com\/)/";

                if (!preg_match($strBaiduPattern, strtolower($strHref))) {
                    //加个参数 判断是客户端传的
                    $strCheckUrlRefer =Molib_Util_CheckUrl::checkUrlRefer($strHref);
                    if(self::CLIENT_TYPE_IPHONE === self::$_intClientType){
                        $strHref = self::CHECK_SPAM_URL . urlencode($strHref).'&meta=1';
                    }else{
                        $strHref = self::CHECK_SPAM_URL . urlencode($strHref);
                    }

                    $strHref= $strHref.'&'.$strCheckUrlRefer;

                }
            }

            return array(
                'type' => self::SLOT_TYPE_LINK,
                'link' => $strHref,
                'text'  => $strText,
            );

        }

        /**
         * 处理涂鸦内容
         * @param  [type] $strImgUrl [description]
         * @param  [type] $value     [description]
         * @return [type]            [description]
         */
        private function _processGraffiti($strImgUrl, $value) {
            if (!empty($value['width'])) {
                $width = $value['width'];
                if($width <= 0){
                    $width = 1;
                }
            } else {
                $width = 1;
            }
            if (!empty($value['height'])) {
                $height = $value['height'];
                if($height <= 0){
                    $height = 1;
                }
            } else {
                $height = 1;
            }
            $arrOutput = array(
                'type' => self::SLOT_TYPE_GRAFFITI,
                'src' => $strImgUrl,
                'bsize' => $width.','.$height,
                'graffiti_info' => array(
                    'url' => $strImgUrl,
                    // todo get gid
                    'gid' => '123456',
                ),
            );
            return $arrOutput;
        }

        /**
         * [_processSlotTypeImg description]
         * @param  [type] $strImgUrl       [description]
         * @param  [type] $value           [description]
         * @param  [type] $boolIsAllOrigin [description]
         * @return [type]                  [description]
         */
        private function _processSlotTypeImg($strImgUrl, $value, $boolIsAllOrigin=false) {
            if (!empty($value['width'])) {
                $width = $value['width'];
                if($width <= 0){
                    $width = 1;
                }
            } else {
                $width = 1;
            }
            if (!empty($value['height'])) {
                $height = $value['height'];
                if($height <= 0){
                    $height = 1;
                }
            } else {
                $height = 1;
            }
            $strSmallPicUrl = Util_Richtext_TransCoderConvertor::getImageUrl($strImgUrl,$this->objCondition->intSmallImgWidth,$this->objCondition->intSmallImgHigth,$this->objCondition->intSmallImgQulity);
            $strBigPicUrl = Util_Richtext_TransCoderConvertor::getImageUrl($strImgUrl,$this->objCondition->intBigImgWidth,$this->objCondition->intBigImgHigth,$this->objCondition->intBigImgQulity);
            $arrOutput = array(
                'type' => self::SLOT_TYPE_IMG,
                'src' => $strImgUrl,
                'small_pic' => $strSmallPicUrl,
                'big_pic' => $strBigPicUrl,
                'bsize' => $width.','.$height,
            );
            // debug size todo delete
            $arrOutput['size'] = $value['size'];
            // add origin pic base on pic size
            if (!empty($value['size']) && intval($value['size']) > 512000) {
                $arrOutput['origin_src'] = $value['src'];
                $arrOutput['origin_size'] = $value['size'];
            } else {
                // 7.0以后 强行假装有大图
                if ($boolIsAllOrigin) {
                    $arrOutput['origin_src'] = $value['src'];
                    if (!empty($value['size'])) {
                        $arrOutput['origin_size'] = $value['size'];
                    }
                }
            }

            return $arrOutput;
        }

        /**
         * @param $tmpSlot
         * @return array|void
         */
        private function _processText($tmpSlot){
            $strTmp = $tmpSlot['value'];
            $strTmp = html_entity_decode($strTmp,ENT_COMPAT,'UTF-8');
            if (!strlen(trim($strTmp))){
                return;
            }
            if ($this->objCondition->bolBlankTrim){
                $strTmp = preg_replace('/[ ]+/i', ' ', $strTmp);
            }
            $arrSlotText = array(
                'type' => self::SLOT_TYPE_TEXT,
                'text' => $strTmp,
            );
            return $arrSlotText;
        }

        /**
         * @param $tmpSlot
         * @param $arrSlotSet
         * @return array|bool
         */
        private function _processSpan($tmpSlot,&$arrSlotSet){
            foreach ($tmpSlot['value'] as $tmp){
                if ($tmp['tag'] == "plainText"){
                    $arrSlotText = $this->_processText($tmp);
                    if (!empty($arrSlotText)){
                        $arrSlotSet[] = $arrSlotText;
                    }
                }elseif ($tmp['tag'] == "a"){
                    $arrSlot = $this->_processLink($tmp);
                }
            }
            return $arrSlot;
        }

        /**
         * @param $intPicId
         * @param string $type
         * @return mixed
         */
        private  function _pid2Url($intPicId, $type = 'pic' ){
            $arrParams[] = [
                'pic_id' => $intPicId,
                'product_name' => 'forum',
                'foreign_key' => 0,
                'pic_spec' => $type,
                'domain' => 'imgsrc.baidu.com',
            ];

            $arrPicUrls = Bd_Pic::pid2Url($arrParams, false);
            return $arrPicUrls['resps'][0];
        }
        /**
         * @param $value
         * @return array
         */
        private function _processMeme($value)
        {
            $intPicId = $value['pid'];
            $arrItems = explode('_', $intPicId);
            if(count($arrItems) > 1) {
                $intPckId = intval($arrItems[0]);
                $intPicId = intval($arrItems[1]);
            } else {
                $intPckId = 0;
                $intPicId = intval($arrItems[0]);
            }

            return array(
                'type' => self::SLOT_TYPE_MEME,
                'src' => $value['src'],
                'bsize' => "{$value['width']},{$value['height']}",
                'meme_info' => array(
                    'pck_id' => $intPckId,
                    'pic_id' => $intPicId,
                    'width' => intval($value['width']),
                    'height' => intval($value['height']),
                    'pic_url' => $this->_pid2Url($intPicId),
                    'thumbnail' => $this->_pid2Url($intPicId, 'abpic'),
                    'detail_link' => "http://tieba.baidu.com/n/interact/emoticon/$intPckId/$intPicId?frompb=1",
                ),
            );
        }
    }

