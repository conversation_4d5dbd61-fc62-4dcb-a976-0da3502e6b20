<?php
    /**
     * Created by PhpStorm.
     * User: he<PERSON><PERSON><PERSON>
     * Date: 2019/3/12
     * Time: 4:06 PM
     */
    class Util_RichTextProcessorCondition
    {
        public $intPageType = 1;                //页面类型，即bd_page_type
        public $intTextSegLen = 0;             //文本段大小限制,0表示不分段
        public $intTextSegNo = 1;              //待显示文本段序号，默认是1
        public $bolTextDisplayOther = false;   //余下全文模式
        public $strTextHasMore = '';

        public $strImageReplaceText = null;	// 图片替换后的文本
        public $intImageDisplayCount = -1;		// 图片显示个数
        public $bolImageBeforeInsertBr = false;            //图片前插入BR，表情除外
        public $bolShowLoadingTip = false;         //展示图片loading标记
        public $bolShowEmbedUrl   = false;         //展示视频链接

        public $intSmileDisplayCount = -1;// 表情图显示个数
        public $strSmilePicUrlPre = '';

        public $intAtDisplayCount    = -1;    // @链接显示个数

        public $intLinkTextLen = -1;	// 链接标题文本个数
        public $strLinkMoreText = '...';	// 链接字数超限的替代文本

        public $strVedioReplaceText = '*视频无法显示*';		// 视频替代文本

        public $bolBlankTrim = true;				// 多余空格替换成一个

        public $bolDisplayNewLine = false;          //是否显示换行

        public $intNewLineCount = 0;	// 允许最多连续的新行数,0表示不限制,

        public $strNewLineReplace = '<br/>'; //换行符替换成的字符

        public $intSmallImgWidth = 96;
        public $intBigImgWidth = 400;

        public $intSmallImgHigth = 2000;
        public $intBigImgHigth = 2000;

        public $intSmallImgQulity = 45;
        public $intBigImgQulity = 80;

        public $bolProcSummary = false;  //是否处理summary
        public $isLazyLoad = false;   // 是否使用lazyload模式加载图片，目前只有智能版pb页使用
        public $isQversion = false;   // Q版特殊处理，pb默认不显示图片使用abstract_mode 控制图片。

        public $bolParseBdhd = false; //是否支持百度影音

        public $bolProcPhoneNum=false; // 是否处理电话号码，电话号码点击出现提示框，发短信或者打电话的提示。

        public $bolGraffitiToImg = false;   //如果有涂鸦的富文本内容，是否已图片形式显示

    }