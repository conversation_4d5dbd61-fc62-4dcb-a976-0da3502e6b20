<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Tools.php
 * <AUTHOR>
 * @date 2019/03/03 06:29:29
 * @brief 
 *  
 **/
class Util_Tools{

    /**
     * @param
     * @return
     */
    public static function  ProcessBcode($strPostAttr){

        $arrOut = array();
        if ( strlen(trim($strPostAttr)) > 0 ){
            $arrBubRes = explode(",", trim($strPostAttr) );
            $intDefaultBubbleId = intval($arrBubRes[0]);
            $arrBubFormat = unserialize($arrBubRes[1]); 
            $arrOut = array(
                'bcode'   => $intDefaultBubbleId,
                'bformat' => trim($arrBubFormat['client']),
            );
        }     
        return $arrOut;
    }
    
    /**
     * getMillisecond
     * @return milli second
     */
    public static  function getMillisecond()
    {
    	list($t1, $t2) = explode(' ', microtime());
    	return (float)sprintf('%.0f',(floatval($t1)+floatval($t2))*1000);
    }


    /**
     * 判断用户IP是4还是6
     * @param $UserIp
     * @return bool
     */
    public static function IsIpV6($UserIp) {
        if (Bingo_Http_Ip::getIpVersion($UserIp) == Bingo_Http_Ip::IP_V6) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 调用service处判断IP是4或6，并添加对应参数
     * @param $arrParams
     * @param $UserIp
     * @param string $strKey
     * @return mixed
     */
    public static function addParamUserIpV4OrV6($arrParams, $UserIp, $strKey = 'user_ip') {

        $strNewKey = $strKey . '6';
        if (self::IsIpV6($UserIp)) {
            $arrParams[$strNewKey] = $UserIp;
            unset($arrParams[$strKey]);
        } else {
            $arrParams[$strKey] = $UserIp;
            unset($arrParams[$strNewKey]);
        }
        return $arrParams;
    }

    /**
     * anti特殊处理，功能和上边一样
     * @param $arrParams
     * @param $UserIp
     * @return mixed
     */
    public static function addAntiParamUserIpV4OrV6($arrParams, $UserIp) {

        if (self::IsIpV6($UserIp)) {
            $arrParams['ipv6'] = $UserIp;
            unset($arrParams['ip']);
        } else {
            $arrParams['ip'] = $UserIp;
            unset($arrParams['ipv6']);
        }
        return $arrParams;
    }

    /**
     * @param $str
     * @param $strTrim
     * @return bool|string
     */
    public static function ltrimStr( $str, $strTrim ) {
        $intLen = strlen( $strTrim );
        while ( substr( $str, 0, $intLen ) == $strTrim ) {
            $str = substr( $str, $intLen );
        }
        return $str;
    }

}





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
