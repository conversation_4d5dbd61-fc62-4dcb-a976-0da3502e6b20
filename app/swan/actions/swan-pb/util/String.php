<?php
    /**
     * Created by PhpStorm.
     * User: heliuyong
     * Date: 2019/3/15
     * Time: 12:01 PM
     */
    class Util_String
    {

        /**
         * 分割字符串方法
         * @param $str
         * @param $start
         * @param $len
         * @param string $charset
         * @return string
         */
        public static function mb_substr($str, $start, $len, $charset = 'utf8') {

            if ($charset == 'utf8' || $charset == 'utf-8') {
                $mb_step = 3;
            } else {
                $mb_step = 2;
            }

            $str_len = strlen($str);
            $offset = 0;
            for ($i = 0; $i < $start; $i++)
            {
                if ($offset >= $str_len) {
                    break;
                }
                $char = ord($str[$offset]);
                if($char <= 0x7F) {
                    $offset++;
                } else {
                    $offset += $mb_step;
                }
            }
            $mb_len = 0;
            for ($i = 0; $i < $len; $i++)
            {
                if ($offset+$mb_len >= $str_len) {
                    break;
                }

                $char = ord($str[$offset+$mb_len]);
                if($char <= 0x7F) {
                    $mb_len++;
                } else {
                    $mb_len += $mb_step;
                }
            }
            return substr($str, $offset, $mb_len);
        }

        /**
         * 二元分词算法
         * @param $str
         * @return array
         */
        public static function splitString($str)
        {
            $cstr = array();
            $search = array(",", "/", "\\", ".", ";", ":", "\"", "!", "~", "`", "^", "(", ")", "?", "-", "\t", "\n", "'", "<", ">", "\r", "\r\n", "{1}quot;", "&", "%", "#", "@", "+", "=", "{", "}", "[", "]", "：", "）", "（", "．", "。", "，", "！", "；", "“", "”", "‘", "’", "［", "］", "、", "—", "　", "《", "》", "－", "…", "【", "】",);

            $str = str_replace($search, " ", $str);
            preg_match_all("/[a-zA-Z]+/", $str, $estr);
            preg_match_all("/[0-9]+/", $str, $nstr);

            $str = preg_replace("/[0-9a-zA-Z]+/", " ", $str);
            $str = preg_replace("/\s{2,}/", " ", $str);

            $str = explode(" ", trim($str));

            foreach ($str as $s) {
                $l = strlen($s);

                $bf = null;
                for ($i= 0; $i< $l; $i=$i+3) {
                    $ns1 = $s{$i}.$s{$i+1}.$s{$i+2};
                    if (isset($s{$i+3})) {
                        $ns2 = $s{$i+3}.$s{$i+4}.$s{$i+5};
                        if (preg_match("/[\x80-\xff]{3}/",$ns2)) {
                            $cstr[] = $ns1.$ns2;
                        }
                    } else if ($i == 0) {
                        $cstr[] = $ns1;
                    }
                }
            }

            $estr = isset($estr[0]) ? $estr[0] : array();
            $nstr = isset($nstr[0]) ? $nstr[0] : array();

            $arrStr = array_merge($nstr, $estr, $cstr);
            if (count($arrStr) == 0) {
                return array();
            }

            $arrRet = array();
            foreach ($arrStr as $key => $value) {
                if ($key % 2 == 0) {
                    $arrRet[] = $value;
                }
                if (count($arrRet) == 3) {
                    break;
                }
            }
            return $arrRet;
        }
    }