<?php
/***************************************************************************
 *
 * Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file Post.php
 * <AUTHOR> @date 2019/03/03 11:20:59
 * @brief
 * @update
 **/

class Core_Page_Post
{
    const VOICE_SHOW_NONE   = 0;
    const VOICE_SHOW_BUTTON = 1;
    const VOICE_SHOW_TEXT   = 2;
    const VOICE_SHOW_LINK   = 3;
    const MOVIDEO_VERSION = '7.0.0'; //视频贴开始版本
    const MOVIDEO_SPECIAL_VERSION = '7.2.0'; //超级视频贴开始版本

    const THREAD_TYPE_SHARE = 6;

    //【运营活动需求】屏蔽滑稽表情2天,时间:2017年2月8日12点———2017年2月10日12点, RD:chenzude PM:liling10
    const SHIELD_FUNNY_FACE_START_TIME = 1486526400;  //2017-02-08 12:00:00
//    const SHIELD_FUNNY_FACE_START_TIME  = 0;  //2017-02-08 12:00:00 for test
    const SHIELD_FUNNY_FACE_END_TIME = 1486699200;  //2017-02-10 12:00:00
    const FUNNY_FACE_EMOTION_KEY = "image_emoticon25";

    private static $_arrPostList  = array();
    private static $_arrSubList   = array();
    private static $_arrAddList   = array();
    private static $_arrPostInfo  = array();
    private static $_arrUserInfo  = array();
    private static $_arrVoice     = array();
    private static $_arrVoiceInfo = array();
    private static $_arrForumInfo = array();

    // whether use cdn for img
    private static $_intIsImgCdn      = 0;
    // data for cdn
    private static $_intScreenWidth   = 0;
    private static $_intScreenHeight  = 0;
    private static $_intScreenDip     = 0;
    private static $_intQType         = 0;
    private static $_intPicStrategyType = 0;
    private static $_strSubappType    = '';
    // voice style
    private static $_intVoiceStyle    = 0;
    private static $_arrVoiceText     = array();
    private static $_boolAddPostList  = array();
    // request parameter
    private static $_intPbRn          = 0;
    private static $_intPn            = 0;
    private static $_intRn            = 0;
    private static $_intValidPostNum  = 0;
    private static $_intReplyNum      = 0;
    private static $_intListNum       = 0;
    private static $_intStartNo       = 0;
    private static $_intTotalPage     = 0;
    private static $_intLast          = 0;
    private static $_intMark          = 0;
    private static $_intBack          = 0;
    private static $_intPid           = 0;
    private static $_intReverse       = 0;
    private static $_intWithFloor     = 0;
    private static $_intFloorRn       = 2;
    private static $_intAddRn       = 10;
    // strategy
    private static $_boolNewSmile     = false;
    private static $_boolCheckSpamUrl = false;
    private static $_boolParseBdhd    = false;
    private static $_boolParsePhone   = false;
    private static $_boolEmoji        = false;
    private static $_hasMoVideo       = false; //客户端短视频
    private static $_intCdnErrTime		  = 0;
    private static $_intThreadType		  = 0;
    private static $_bolGraffitiToImg = false;
    // bubble
    private static $_intClientType    = 1;
    private static $_intBcode         = 0;
    private static $_strBimgFormat    = "";
    private static $_iqiyiDownloadAnd = 'http://mbdapp.iqiyi.com/j/ap/qiyi.197.apk';
    private static $_iqiyiDownloadIos = 'https://itunes.apple.com/cn/app/ai-qi-yi-shi-pin-zui-xin-dian/id393765873?mt=8';
    private static $_iqiyiUrl = 'iqiyi.com';
    private static $_checkSpamUrl = 'http://tieba.baidu.com/mo/q/checkurl?url=';
    private static $_strClientVersion    = 0;
    private static $strConf;
    private static $_intUid = 0;
    private static $_intShowSquared = 0; //是否展示九宫格

    // 网搜视频加防盗链
    private static $_arrThread = null;

    /** 
     * @param 
     * @return 
     */
    public static function build($arrCoreData)
    {
        self::_init($arrCoreData);
        self::_buildPost();

        // 网搜防盗链
        self::_buildVideoUrl();
        $arrPostData = self::_getPostList();
        return $arrPostData;
    }
    /** 
     * @param 
     * @return 
     */
    private static function _init($arrCoreData)
    {
        self::$_arrPostInfo  = $arrCoreData['post'];
        self::$_arrVoiceInfo = $arrCoreData['voice']['ret']['postVoiceList'];

        $objRequest        = $arrCoreData['request'];
        $arrStrategy       = $arrCoreData['strategy'];
        $arrArrangedParams = $arrCoreData['arranged_params'];

        self::$_arrUserInfo      = $arrCoreData['user']['user_info'];
        self::$_arrForumInfo     = $arrCoreData['forum'];

        self::$_boolParseBdhd    = $arrStrategy['pb_parse_bdhd'];
        self::$_boolParsePhone   = $arrStrategy['pb_parse_phone'];
        self::$_boolCheckSpamUrl = $arrStrategy['pb_check_spam_url'];
        self::$_boolNewSmile     = $arrStrategy['pb_new_smile'];
        self::$_intIsImgCdn      = $arrStrategy['pb_img_cdn'];
        self::$_boolAddPostList  = $arrStrategy['pb_add_post_list'];
        self::$_intPicStrategyType = $arrStrategy['pb_pic_strategy_type'];
        self::$_boolEmoji        = $arrStrategy['pb_emoji'];
        self::$_intVoiceStyle    = $arrStrategy['pb_voice_style'];
        self::$_bolGraffitiToImg = $arrStrategy['pb_graffiti_to_img'];
        self::$_intScreenWidth   = $objRequest->getPrivateAttr('scr_w');
        self::$_intScreenHeight  = $objRequest->getPrivateAttr('scr_h');
        self::$_intScreenDip     = $objRequest->getPrivateAttr('scr_dip');
        self::$_intQType         = $objRequest->getPrivateAttr('q_type');
        self::$_intFloorRn       = $objRequest->getPrivateAttr('floor_rn');
        self::$_intWithFloor     = $objRequest->getPrivateAttr('with_floor');
        self::$_intThreadType    = $objRequest->getPrivateAttr('thread_type');
        self::$_intReverse       = $arrCoreData['arranged_params']['r'];
        self::$_intPn            = $arrCoreData['arranged_params']['pn'];
        self::$_intPid           = $arrCoreData['arranged_params']['pid'];
        self::$_intPbRn          = $arrArrangedParams['pb_rn'];
        self::$_intPn            = $arrArrangedParams['pn'];
        self::$_intRn            = $arrArrangedParams['rn'];
        self::$_intLast          = $arrArrangedParams['last'];
        self::$_intMark          = $arrArrangedParams['mark'];
        self::$_intBack          = $arrArrangedParams['back'];
        self::$_intPid           = $arrArrangedParams['pid'];
        self::$_intValidPostNum  = $arrCoreData['post']['valid_post_num'];
        self::$_intReplyNum      = $arrCoreData['post']['total_post_num'];
        self::$_intStartNo       = $arrCoreData['post']['start_no'];
        self::$_intCdnErrTime    = $arrCoreData['user']['user_info'][$objRequest->getCommonAttr('user_id')]['cdn_error'];
        self::$_strSubappType    = $objRequest->getCommonAttr('subapp_type');

        //bubble code add by zhaochuanyong 2014-03-18
        self::$_intClientType    = $objRequest->getCommonAttr('client_type');
        self::$_strClientVersion = $objRequest->getCommonAttr('client_version');
        self::$_intUid           = $objRequest->getCommonAttr('user_id');
        self::$strConf          = $objRequest->getStrategy('pb_floor_top');
       //大于等于6.6 楼中楼返回5条
        if(Molib_Util_VersionMatch::checkClient(self::$strConf, self::$_intClientType, self::$_strClientVersion))
        {
        	self::$_intFloorRn = 10;
        }
        
        if (isset($arrCoreData['thread']) && 0 == $arrCoreData['thread']['errno']) {
            
            $threads = array_values($arrCoreData['thread']['output']['thread_list']);
            if(count($threads) > 0 && isset($threads[0]['video_info'])) {
                self::$_arrThread = $threads[0];
            }
        }
        //是否需要展示九宫格
        self::$_intShowSquared = Molib_Util_RichText_SquaredOrder::showSquared(self::$_intClientType, self::$_strClientVersion);
    }
    /** 
     * @param 
     * @return 
     */
    private static function _buildVideoUrl() {
        if (null == self::$_arrThread) {
            return;
        }

        $arrThreadType = Tieba_Type_Thread::getTypeArray(self::$_arrThread['thread_types']);
        if (!isset($arrThreadType['is_movideo']) || !$arrThreadType['is_movideo']) {
            return;
        }
        Tieba_Video_Process::execute(self::$_arrThread['video_info'], '', Tieba_Video_Process::REQUEST_FROM_SWAN);

        if (!isset(self::$_arrPostList[0]['content']) || !is_array(self::$_arrPostList[0]['content'])) {
            return;
        }
        foreach (self::$_arrPostList[0]['content'] as &$content) {
            if (isset($content['e_type']) && $content['e_type'] == Molib_Util_RichText_Parser::SLOT_TYPE_EMBED_MOVIDEO) {
                $content['link'] = self::$_arrThread['video_info']['video_url'];
                $content['src'] = self::$_arrThread['video_info']['thumbnail_url'];
            }
        }
    }
    /** 
     * @param 
     * @return 
     */
    private static function _buildPost()
    {
        $arrList = self::$_arrPostInfo['post_infos'];

        if ( !is_array($arrList) ){
            return array();
        }
        if ( self::$_intVoiceStyle === self::VOICE_SHOW_LINK ){
            self::$_arrVoiceText = array(
                'type' => Molib_Util_RichText_Parser::SLOT_TYPE_LINK,
                'text' => '[语音]来自新版客户端语音功能',
                'link' => 'http://c.tieba.baidu.com/c/s/download/wap?src=client',
            );
        }
        else if ( self::$_intVoiceStyle === self::VOICE_SHOW_TEXT ){
            self::$_arrVoiceText = array(
                'type' => Molib_Util_RichText_Parser::SLOT_TYPE_TEXT,
                'text' => '[语音]',
            );
        }
        else if ( self::$_intVoiceStyle === self::VOICE_SHOW_BUTTON ){
            self::_getVoiceList(self::$_arrVoiceInfo);
        }

        // fetch post and sub post
        foreach ( $arrList as $arrPost ){
            self::_fetchPost($arrPost);
        }
        // get add post
        if( isset(self::$_arrPostInfo['add_first_floor']['comment_num']) && self::$_arrPostInfo['add_first_floor']['comment_num'] > 0 && is_array(self::$_arrPostInfo['add_first_floor']['comment_info']) ){
            self::_fetchAddPost(self::$_arrPostInfo['add_first_floor']['comment_num'], self::$_arrPostInfo['add_first_floor']['comment_info']);
        }

        // process post content
        self::_processPost();

        // merge post and sub post
        if ( self::$_intWithFloor === 1 ){
            foreach ( self::$_arrPostList as $mixKey => $arrPost ){
                if ( isset(self::$_arrSubList[$arrPost['post_id']]) ){
                    self::$_arrPostList[$mixKey]['sub_post_list'] = self::$_arrSubList[$arrPost['post_id']]['sub_post_list'];
                }
                else{
                    self::$_arrPostList[$mixKey]['sub_post_list'] = array();
                }
            }
        }
        // merge addpost and post
        if (self::$_boolAddPostList && self::$_arrAddList) {
            foreach (self::$_arrPostList as $strKey => $arrItem) {
                if (1 == $arrItem['post_no']) {
                    self::$_arrPostList[$strKey]['add_post_list'] = self::$_arrAddList;
                    self::$_arrPostList[$strKey]['add_post_number'] = self::$_arrPostInfo['add_first_floor']['comment_num'];
                }
            }
        } else {
            foreach (self::$_arrPostList as $strKey => $arrItem) {
                if (1 == $arrItem['post_no']) {
                    foreach (self::$_arrAddList['add_post_list'] as $arrItem) {
                        self::$_arrPostList[$strKey]['content'] = array_merge(self::$_arrPostList[$strKey]['content'],
                        array(array('type' => 0, 'text' => "\n")));
                        self::$_arrPostList[$strKey]['content'] = array_merge(self::$_arrPostList[$strKey]['content'], $arrItem['content']);
                    }
                }
            }
        }
        if ( self::$_intReverse === 1 ){
            self::$_arrPostList = array_reverse(self::$_arrPostList);
        }

        //判断帖子是否加点赞信息
        if(self::$_intThreadType ==Tieba_Type_Thread::TWZHIBO_THREAD){
        	foreach ( self::$_arrPostList as $Key => $arrPost ){

        		$arrZanInfo =$arrPost['zan'];
        		foreach($arrZanInfo['user_id_list'] as  $key => $intUserIdTmp){
        			if(isset(self::$_arrUserInfo [$intUserIdTmp]) &&self::$_arrUserInfo [$intUserIdTmp]['user_id'] > 0){
        				$arrUserInfoTmp =self::$_arrUserInfo [$intUserIdTmp];
        				$arrUserInfoTmp = Util_User::getUserInfo($arrUserInfoTmp);
        				$arrUserInfoTmp= Molib_Util_Array::fetchArray(
        						$arrUserInfoTmp,
        						array('id', 'name', 'name_show','portrait', 'type', 'is_like','level_id','level_name', 'is_interestman','iconinfo')
        				);

        				$arrZanInfo['liker_list'][$key] = $arrUserInfoTmp;
        			}
        		}
        	}
        }
        //调用爱奇艺获取视频播放调起地址
        foreach (self::$_arrPostList as &$arrItem) {
        	if (1 == $arrItem['post_no']) {
        		foreach ($arrItem['content'] as &$content){
        			$isNativeApp = 0;
        			$arrNativeApp = array();
        			$jump = array();
        			if (Molib_Client_Define::CLIENT_TYPE_IPHONE == self::$_intClientType){
        				if ($content['type'] == 1 && strstr($content['link'], self::$_iqiyiUrl) != false){//链接
        					$url = str_replace(self::$_checkSpamUrl,"",$content['link']);
        					//调用i奇艺
        					$jump = self::_processNativeApp($url);
        				}elseif ($content['type'] == 5 && strstr($content['text'], self::$_iqiyiUrl) != false){//视频
        					//调用i奇艺
        					$jump = self::_processNativeApp($content['text']);
        				}
        				if (!empty($jump)){
        					$isNativeApp = 1;
        					$arrNativeApp = array(
        							//Android调起i奇艺
        							'jump_and' => $jump['Android'],
        							//ios调起i奇艺
        							'jump_ios' => $jump['Ios'],
        							//Android下载i奇艺地址
        							'download_and' => self::$_iqiyiDownloadAnd,
        							//ios下载i奇艺地址
        							'download_ios' => self::$_iqiyiDownloadIos,
        					);
        				}
        			}

        			$content['is_native_app'] = $isNativeApp;
        			$content['native_app'] = $arrNativeApp;
        		}
        		break;
        	}
        }

    }
    /** 
     * @param 
     * @return 
     */
    private static function _getPostList()
    {
        if ( self::$_intPn && intval(self::$_intPid) > 1 ){
            foreach ( self::$_arrPostList as $mixKey => $arrPost ){
                if ( $arrPost['post_id'] === self::$_intPid ){
                    unset(self::$_arrPostList[$mixKey]);
                }
            }
        }

        if (!empty(self::$_arrPostList )) {
            foreach (self::$_arrPostList  as $key => &$arrPost) {
                $arrPostTmp = Molib_Util_RichText_SquaredOrder::processContentOrder($arrPost['content'], 'post', $arrPost['post_no'], self::$_intShowSquared);
                $arrPost['content'] = $arrPostTmp['content'];
                $arrPost['show_squared'] = self::$_intShowSquared ? $arrPostTmp['show_squared'] : 0;//使用开关控制一下，觉得这个九宫格不靠谱，有可能会下掉
            }
            unset($arrPost);
        }

        return self::$_arrPostList;
    }
    /** 
     * @param 
     * @return 
     */
    private static function _getVoiceList($arrVoiceInfo)
    {
        foreach ( $arrVoiceInfo as $arrEach ){
            self::$_arrVoice[$arrEach['post_id']]['type']        = 10;
            self::$_arrVoice[$arrEach['post_id']]['during_time'] = $arrEach['during_time'] * 1000;
            self::$_arrVoice[$arrEach['post_id']]['voice_md5']   = $arrEach['voice_md5'];
        }
    }
    /** 
     * @param 
     * @return 
     */
    private static function _fetchPost($arrPost)
    {
        // fill author info
        $intUid = isset($arrPost['user_id']) ? intval($arrPost['user_id']) : 0;
        // anonymous user name
        if ( $intUid === 0 || Tieba_Util::isAnonymousUserName($arrPost['username']) ){
            $arrPost['author']['user_id']   = 0;
            $arrPost['author']['user_name'] = $arrPost['author']['name'] = $arrPost['author']['name_show'] = Util_User::maskIP($arrPost['username']);
            $arrPost['author']['portrait']  = Tieba_Ucrypt::encode(0, $arrPost['username']);
            $arrPost['author']['gender']    = 0;
            $arrPost['author']['level_id']  = '';
            $arrPost['author']['is_bawu']   = 0;
            $arrPost['author']['bawu_type']  = '';
        }
        else{
            $arrPost['author'] = Util_User::getUserInfo(self::$_arrUserInfo[$intUid]);

            // 添加头像挂件
            $arrPost['author']['pendant'] = Molib_Util_Props::getPendant(self::$_arrUserInfo[$intUid],self::$_intClientType,Molib_Util_Props::PENDANT_LOCATION_PB);
            $arrPost['author']['seal_prefix'] = Molib_Util_Props::getSealPrefix(self::$_arrUserInfo[$intUid]);
        }
        // add to post list
        self::$_arrPostList[] = $arrPost;

        // get sub post
        if( isset($arrPost['comment_num']) && $arrPost['comment_num'] > 0 && is_array($arrPost['comment_info']) ){
            self::_fetchSubPost($arrPost['post_id'], $arrPost['comment_num'], $arrPost['comment_info']);
        }
    }
    /** 
     * @param 
     * @return 
     */
    private static function _fetchSubPost($intPid, $intCommentNum, $arrCommentInfo)
    {
        $intFloorNum = count($arrCommentInfo);
        $arrSub = array();
        self::$_arrSubList[$intPid]['sub_post_list'] = array();
        foreach ( $arrCommentInfo as $arrComment ){
            $arrSub = $arrComment;
            $intCUid = $arrComment['user_id'];
            $arrSub['author'] = Util_User::getUserInfo(self::$_arrUserInfo[$intCUid]);
            self::$_arrSubList[$intPid]['sub_post_list'][] = $arrSub;
        }
    }
    /** 
     * @param 
     * @return 
     */
    private static function _fetchAddPost($intCommentNum, $arrCommentInfo)
    {
        $intFloorNum = count($arrCommentInfo);
        self::$_arrAddList['add_post_list'] = array();
        self::$_arrAddList['comment_num'] = $intCommentNum;
        foreach ( $arrCommentInfo as $arrComment ){
            self::$_arrAddList['add_post_list'][] = $arrComment;
        }
    }
    /** 
     * @param 
     * @return 
     */
    private static function _processPost()
    {
        $arrEmojiCodes = Molib_Conf_Emoji::$arrEmojiCodes;

        $objCondition = new Molib_Util_RichText_ParserCondition();
        $objCondition->intNewLineCount = 1;
        if ( self::$_boolParseBdhd === true ){
            $objCondition->bolParseBdhd = true;
        }
        if ( self::$_boolParsePhone === true ){
            $objCondition->bolParsePhone = true;
        }
        if ( self::$_boolCheckSpamUrl === true ){
            $objCondition->bolCheckSpamUrl = true;
        }
        //涂鸦在低与7.3.0以下版本或ios端是否显示成图片
        if(true === self::$_bolGraffitiToImg){
            $objCondition->bolGraffitiToImg = true;
        }
        $objParser = new Molib_Util_RichText_Parser();
        //pb结构化后，解析content方式
        // 对content进行内容处理,包括参数的过滤和调整
        $objParserStructured = new Molib_Util_RichText_ParserStructured(); 
        $objParserStructured->setClientType(self::$_intClientType);
        $objParserStructured->setClientVersion(self::$_strClientVersion);
        $objParserStructured->setFid(self::$_arrForumInfo['forum_name']['forum_id']);

        if (Molib_Util_Version::compare(self::$_strClientVersion, '7.0.0') == 1) {
            $boolIsAllOrigin = false;
        } else {
            $boolIsAllOrigin = true;
        }

        $arrNewPostList = array();
        foreach ( self::$_arrPostList as $arrPost ){
            $arrContent = $arrPost['content'];
            $arrPost['title'] = html_entity_decode($arrPost['title'], ENT_COMPAT, 'UTF-8');

            // rich text process
            $objResult = $objParserStructured->process($objCondition, $arrContent, self::$_boolEmoji, self::$_intScreenWidth, self::$_intScreenHeight, $boolIsAllOrigin,true);
            // voice post
            if ( intval($arrPost['ptype']) === 1 ){
                if ( self::$_intVoiceStyle === self::VOICE_SHOW_LINK || self::$_intVoiceStyle === self::VOICE_SHOW_TEXT ){
                    $objResult->arrContent = array_merge(
                        array( 0 => self::$_arrVoiceText ),
                        $objResult->arrContent
                    );
                }
                else if ( self::$_intVoiceStyle === self::VOICE_SHOW_BUTTON ){
                    if ( isset(self::$_arrVoice[$arrPost['post_id']]) ){
                        $objResult->arrContent = array_merge(
                            array( 0 => self::$_arrVoice[$arrPost['post_id']] ),
                            $objResult->arrContent
                        );
                    }
                }
                $arrPost['is_voice'] = 1;
            }
            else{
                $arrPost['is_voice'] = 0;
            }
            
            //7.2.0以后非主题视频不展示视频，版本判断在processContent里面，因为这个方法同时提供给其他接口使用，保持功能完整性
            if ($arrPost['post_no'] == 1) {
                $arrPost['content'] = self::_processContent($objResult->arrContent);
            } else {
                $arrPost['content'] = self::_processContent($objResult->arrContent, self::$_strClientVersion, self::$_boolNewSmile, false);
            }

            if ( isset(self::$_arrSubList[$arrPost['id']]) ){
                $intSubPostNum = intval(self::$_arrSubList[$arrPost['id']]['total_num']);
            }
            else{
                $intSubPostNum = 0;
            }
            $arrPost['sub_post_number'] = $intSubPostNum;
            if (isset($arrPost['post_attr']) && intval($arrPost['is_bub']) > 0 ){
                $strBcode = trim($arrPost['post_attr']);
                $arrBcode = Util_Tools::ProcessBcode($strBcode);
                $arrPost['bimg_url'] = Util_Tools::autoMatchBubbleImg(self::$_intClientType,self::$_intScreenWidth,intval($arrBcode['bcode']) );
                $arrPost['ios_bimg_format'] = trim($arrBcode['bformat']);
                $arrPost['is_bub'] = intval($arrPost['is_bub']);
                unset($arrPost['post_attr']);
            }else{
                $arrPost['bimg_url'] = "";
                $arrPost['ios_bimg_format'] = "";
                $arrPost['is_bub'] = 0;
            }

            //add by wanglianshan
            if(isset($arrPost['tailInfo'])){
            	$arrPost['has_signature'] = 1;
            	$arrPost['signature']['signature_id'] = $arrPost['tailInfo']['tailId'];
            	$arrPost['signature']['fontKeyName'] = $arrPost['tailInfo']['fontKeyName'];
            	$arrPost['signature']['fontColor'] = $arrPost['tailInfo']['fontColor'];
            	$tailContent = $arrPost['tailInfo']['tailContent'];
            	$richcontent = $objParser->process($objCondition, $tailContent);
            	$arrPost['signature']['content'] = self::_filterTailContent($richcontent->arrContent);
            	unset($arrPost['tailInfo']);
            }else{
            	$arrPost['has_signature']=0;
            }

          if(isset($arrPost['openid']) && ($arrPost['openid'] ==  "tbclient_baobao")){

              $iconLink = "http://www.myhug.cn/web/share?f=tieba&d=";
              $arrTail = array(
                 'tail_type' => 1,
                 'icon_url' =>
                 'http://tb1.bdstatic.com/tb/cms/tbmall/font/client/icon_footer_baobao2x.png',
                 'icon_link' => $iconLink,
                 'content' => '来自抱抱配图',
                );

                 $arrPost['tail_info'] = $arrTail;
             }
            $arrNewPostList[] = $arrPost;
        }
        self::$_arrPostList = $arrNewPostList;
        if ( self::$_intWithFloor === 1 ){
            $objCondition->bolParsePhone = false;
            foreach ( self::$_arrSubList as $mixFloorKey => $arrSubFloor ){
                $intNum = 0;
                $arrSubFloorTmp = array();
                if(!is_array($arrSubFloor['sub_post_list'])){
                    continue;
                }
                foreach ($arrSubFloor['sub_post_list'] as $arrSubPost) {
                    //如果设置了楼中楼返回条数，超过x条就不要了
                    $intNum++;
                    if ( self::$_intFloorRn > 0 && $intNum > self::$_intFloorRn ){
                        break;
                    }
                    $arrContent = $arrSubPost['content'];
                    $objResult = $objParserStructured->process($objCondition, $arrContent, self::$_boolEmoji, self::$_intScreenWidth, self::$_intScreenHeight,false,true);
                    // voice post
                    if ( intval($arrSubPost['ptype']) === 1 ){
                        $arrSubPost['is_voice'] = 1;
                        if ( self::$_intVoiceStyle === self::VOICE_SHOW_LINK || self::$_intVoiceStyle === self::VOICE_SHOW_TEXT ){
                            $objResult->arrContent = array_merge(
                                array( 0 => self::$_arrVoiceText ),
                                $objResult->arrContent
                            );
                        }
                        else if ( self::$_intVoiceStyle === self::VOICE_SHOW_BUTTON ){
                            if ( isset(self::$_arrVoice[$arrSubPost['comment_id']]) ){
                                self::$_arrVoice[$arrSubPost['comment_id']]['is_sub'] = 1;
                                $objResult->arrContent = array_merge(
                                    $objResult->arrContent,
                                    array( 0 => self::$_arrVoice[$arrSubPost['comment_id']] )
                                );
                            }
                        }
                    }
                    else{
                        $arrSubPost['is_voice'] = 0;
                    }

                    $arrSubPost['content'] = self::_processContent($objResult->arrContent);
                    // PB文案内容顺序 文字>其他>语音(相当于只把语音提出来放到最后，其他的都按原顺序不变) by pm wangchunjie
                    $arrPostTmp = Molib_Util_RichText_SquaredOrder::processContentOrder($arrSubPost['content'], 'floor');
                    $arrSubPost['content'] = $arrPostTmp['content'];
                    //特殊处理下礼物贴
                    $intIsGiftPost =$arrSubPost['is_giftpost'];
                    if($intIsGiftPost == 1){
                    	$arrContent =$arrSubPost['content'];
                    	if(!empty($arrContent)){
                    		foreach ($arrContent as $key => $arrVlaue){
                    			if($arrVlaue['type'] == 1){
                    				$arrSubPost['content'][$key]['type'] = 3;
                    				//引文客户端通过src 取图片
                    				$arrSubPost['content'][$key]['src'] =$arrVlaue['text'];
                    			}
                    		}
                    	}
                    }
                    $arrSubPost['title'] = isset($arrSubPost['title']) ? $arrSubPost['title'] : '';
                    $arrSubPost['floor'] = isset($arrSubPost['floor']) ? $arrSubPost['floor'] : 0;
                    $arrSubPost['id']    = strval($arrSubPost['id']);
                    $arrSubFloorTmp[] = $arrSubPost;
                }
                $arrSubFloor['sub_post_list'] = $arrSubFloorTmp;
                self::$_arrSubList[$mixFloorKey] = $arrSubFloor;
            }
        }
            $objCondition->bolParsePhone = false;
            foreach ( self::$_arrAddList['add_post_list'] as $mixFloorKey => $arrAddPost){
                $intNum = 0;
                if(!is_array($arrAddPost)){
                    continue;
                }
                $arrAddFloorTmp = array();
                //如果设置了楼中楼返回条数，超过x条就不要了
                $intNum++;
                if ( self::$_intAddRn > 0 && $intNum > self::$_intAddRn ){
                    break;
                }
                $arrContent = $arrAddPost['content'];
                $objResult = $objParserStructured->process($objCondition, $arrContent, self::$_boolEmoji, self::$_intScreenWidth, self::$_intScreenHeight);
                // voice post
                if ( intval($arrAddPost['ptype']) === 1 ){
                    $arrAddPost['is_voice'] = 1;
                    if ( self::$_intVoiceStyle === self::VOICE_SHOW_LINK || self::$_intVoiceStyle === self::VOICE_SHOW_TEXT ){
                        $objResult->arrContent = array_merge(
                                array( 0 => self::$_arrVoiceText ),
                                $objResult->arrContent
                                );
                    }
                    else if ( self::$_intVoiceStyle === self::VOICE_SHOW_BUTTON ){
                        if ( isset(self::$_arrVoice[$arrAddPost['comment_id']]) ){
                            self::$_arrVoice[$arrAddPost['comment_id']]['is_sub'] = 1;
                            $objResult->arrContent = array_merge(
                                    array( 0 => self::$_arrVoice[$arrAddPost['comment_id']]),
                                    $objResult->arrContent
                                    );
                        }
                    }
                }
                else{
                    $arrAddPost['is_voice'] = 0;
                }

                $arrAddPost['content'] = self::_processContent($objResult->arrContent);
                // PB文案内容顺序 文字>其他>语音(相当于只把语音提出来放到最后，其他的都按原顺序不变) by pm wangchunjie
                $arrAddPostTmp = Molib_Util_RichText_SquaredOrder::processContentOrder($arrAddPost['content'], 'floor');
                $arrAddPost['content'] = $arrAddPostTmp['content'];
                $arrAddPost['title'] = isset($arrAddPost['title']) ? $arrAddPost['title'] : '';
                $arrAddPost['floor'] = isset($arrAddPost['floor']) ? $arrAddPost['floor'] : 0;
                $arrAddPost['id']    = strval($arrAddPost['id']);
                $arrAddFloorTmp[] = $arrAddPost;
                self::$_arrAddList['add_post_list'][$mixFloorKey] = $arrAddPost;
            }
    }
    
    /**
     * @param {Array} $arrContent 内容
     * @param {String} $strClientVersion 版本
     * @param {String} $boolNewSmile 是否新表情 
     * @param {Bool} $boolShowVideo 是否展示视频 
     * @return {Array} newArrContent
     * @brief 改成public是因为 floor楼层也要用(勉强改造，有些self数据不同步，但是不影响体验） by chenjinya
     **/
    public static function _processContent($arrContent, $strClientVersion = "", $boolNewSmile = "", $boolShowVideo = true  )
    {
        if("" === $strClientVersion){
            $strClientVersion = self::$_strClientVersion;
        }
        if("" === $boolNewSmile){
            $boolNewSmile = self::$_boolNewSmile;
        }
        $arrSmile2TextTransData = Molib_Conf_Smile::$arrSmile2TextTransData;
        $arrSmileNotSupport     = Molib_Conf_Smile::$arrSmileNotSupport;

        $arrNewContent = array();
        $arrTextSlot = array(
            'type' => Molib_Util_RichText_Parser::SLOT_TYPE_TEXT,
            'text' => ''
        );

        $arrList = array();
        $arrMoVideo = array();
        $indexVideoKey = 0; //记录7.2.0之前回复楼层有视频的key，为了加一个“查看”的连接
        $arrOldFloorVideoSolt = array();// 兼容老版本有视频的楼层回复 
        foreach ( $arrContent as $key => $arrSlot ){
            // 合并text content
            if ( $arrSlot['type'] == Molib_Util_RichText_Parser::SLOT_TYPE_TEXT ){
                /**
                 * 普通视频前面文字的\n仍然保留，但是客户端新视频(7.0.0及以后版本thread_type MOVIDEO_THREAD, SLOT_TYPE_EMBED_MOVIDEO) 前面文字去掉 \n
                 *
                 * post list每个不同类型content中间都有个<br/>，在前面替换为\n，输出后合并text后，\n会合并到文本里面，
                 * 图片与文字之间没有\n，是因为在 molib/Util/RichText/ParseStructured.php 里处理了，我为啥没放到一起处理呢？
                 * 开始放到一起了，但是发现需要版本判断，但是版本判断放到molib里总是不好，所以我就移出来了。
                 *
                 * 这段代码很恶心，整个content list的逻辑就很恶心
                 *
                 * by chenjinya 20151103
                 * */
                if( Molib_Util_Version::compare(self::MOVIDEO_VERSION, $strClientVersion) >= 0){
                    if(isset($arrContent[$key + 1]) && (Molib_Util_RichText_Parser::SLOT_TYPE_EMBED_MOVIDEO === intval($arrContent[$key + 1]['e_type']))){
                        $intNPos = strpos($arrSlot['text'], "\n");
                        //没用替换的原因是只对最后结尾的\n做处理
                        if($intNPos === strlen($arrSlot['text']) - 1 ) {
                            $arrSlot['text'] = substr($arrSlot['text'],0,$intNPos);
                        }
                    }
                }
                $arrTextSlot['text'] .= $arrSlot['text'];


            }
            else{
                if ( strlen($arrTextSlot['text']) ){
                    $arrNewContent[]     = $arrTextSlot;
                    $arrTextSlot['text'] = '';
                }
                // 不支持部分表情
                if ( $arrSlot['type'] == Molib_Util_RichText_Parser::SLOT_TYPE_SMILE ){
                    $strSmileFileName = $arrSlot['text'];
                    $strSmileTextName = isset($arrSmileNotSupport[$strSmileFileName]) ? $arrSmileNotSupport[$strSmileFileName] : $strSmileFileName;
                    $arrSlot['c'] = $arrSmile2TextTransData[$strSmileFileName] ? $arrSmile2TextTransData[$strSmileFileName] : '';
                    if (isset($arrSmileNotSupport[$strSmileFileName])) {
                        $arrSlot['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_TEXT;
                        $arrSlot['text'] = '(' . $strSmileTextName . ')';
                    }

                    //【运营活动需求】屏蔽滑稽表情2天,时间:2017年2月8日12点———2017年2月10日12点, RD:chenzude PM:liling10
                    $intNowTime = time();
                    if (($intNowTime > self::SHIELD_FUNNY_FACE_START_TIME) && ($intNowTime <= self::SHIELD_FUNNY_FACE_END_TIME) && (trim($arrSlot['text']) == self::FUNNY_FACE_EMOTION_KEY)) {
                        $arrSlot['type'] = 18;
                        $strSmileTextName = "滑稽go die";
                        $arrSlot['text'] = '#' . $strSmileTextName . '#';
                        $arrSlot['link'] = 'http://tieba.baidu.com/mo/q/hotMessage?topic_id=0&topic_name=滑稽go die';
                        unset($arrSlot['c']);
                    }
                }
                // 生成图片 cdn url
                else if ( $arrSlot['type'] == Molib_Util_RichText_Parser::SLOT_TYPE_IMG || Molib_Util_RichText_Parser::SLOT_TYPE_GRAFFITI == $arrSlot['type'] ){
                    if ( self::$_intIsImgCdn === 1 ){
                        $arrSpec = array(
                            'screen_w'   => self::$_intScreenWidth,
                            'screen_h'   => self::$_intScreenHeight,
                            'screen_dip' => self::$_intScreenDip,
                            'q_type'     => self::$_intQType,
                            'subapp_type'=> self::$_strSubappType,
                        );
                        $arrSlot = Molib_Util_ImgCDN_Pb::procNewPicUrl($arrSlot, true, $arrSpec, self::$_intPicStrategyType);
                        $intCurrentTime = time();
                        if(($intCurrentTime-self::$_intCdnErrTime) <= Molib_Conf_CdnStrategy::$intCdnUserTime){
                        	if(isset($arrSlot['cdn_src'])){
                        		$arrSlot['cdn_src'] = 'http://c.tieba.baidu.com/c/p/img?src='.$arrSlot['cdn_src'];
                        	}
                        	if(isset($arrSlot['big_cdn_src'])){
                        		$arrSlot['big_cdn_src'] = 'http://c.tieba.baidu.com/c/p/img?src='.$arrSlot['big_cdn_src'];
                        	}
                        }
                    }
                }
                // 老客户端表情商店中的表情显示为静态图
                if ( $boolNewSmile === false ){
                    if ($arrSlot['type'] == Molib_Util_RichText_Parser::SLOT_TYPE_SMILE3){
                        $arrSlot['type']  = Molib_Util_RichText_Parser::SLOT_TYPE_IMG;
                        $arrSlot['src']   = $arrSlot['static'];
                        $arrSlot['bsize'] = $arrSlot['width'] . ',' . $arrSlot['height'];
                        unset($arrSlot['width']);
                        unset($arrSlot['height']);
                        unset($arrSlot['static']);
                        unset($arrSlot['dynamic']);
                        unset($arrSlot['group_id']);
                        unset($arrSlot['c']);
                    }
                }

                /** 
                 * 7.0.0 之前用的小影，7.0.0之后用的自有视频 删除文字标示（视频来自：哪哪哪）
                 * 绝对的历史沉重感
                 * type 0 视频来自 type 1 一个链接
                 * 视频来自：tieba.baidu.com
                 * 
                 * 即所有客户端录制的视频贴，都不需要
                 */
                if(isset($arrSlot['e_type']) && 
                    (Molib_Util_RichText_Parser::SLOT_TYPE_EMBED_XIAOYING === intval($arrSlot['e_type'])
                  || Molib_Util_RichText_Parser::SLOT_TYPE_EMBED_MOVIDEO  === intval($arrSlot['e_type'])
                        )) {
                            //客户端短视频，删掉多余的Content信息 7.0.0
                            //xiaoying add by mahong, update by jinya
                    self::$_hasMoVideo = true;
                    if (intval($arrContent[$key + 1]['type']) === Molib_Util_RichText_Parser::SLOT_TYPE_TEXT 
                        && strpos($arrContent[$key + 1]['text'], '视频来自')) {

                        $arrList[] = $key + 1;//删除 视频来自
                    }
                    if(intval($arrContent[$key + 2]['type']) === Molib_Util_RichText_Parser::SLOT_TYPE_LINK 
                        && (!strpos($arrContent[$key + 2]['text'],"xiaoying") 
                         || !strpos($arrContent[$key + 2]['text'],"tieba"))){

                        $arrList[] = $key + 2;//删除 视频来自的地方
                    }
                    
                    if( Molib_Util_Version::compare(self::MOVIDEO_SPECIAL_VERSION, $strClientVersion) >= 0){
                        //7.2.0 及其以后的客户端短视频
                        if(Molib_Util_RichText_Parser::SLOT_TYPE_EMBED_MOVIDEO === intval($arrSlot['e_type'])) {
                            /**
                             * 需要展示效果为
                             * 主题：只有视频
                             * 回复楼层：不展示视频，视频替换为  我发布了一个视频 [查看]（跳转链接）
                             * by jinya
                             *
                             * */
                            if ($boolShowVideo !== true) {
                                //回复楼层不展示视频，兼容老版本 7.2.0 之前的版本
                                //删除此content，在后面会重新添加
                                $arrList[] = $key;
                    
                                $arrOldFloorVideoSolt = array(
                                    'type' => Molib_Util_RichText_Parser::SLOT_TYPE_LINK,
                                    'text' => '查看',
                                    'link' => $arrSlot['text'],
                                );
                    
                            } else {
                                //视频贴主题只展示视频，就不用再继续循环了
                                $arrMoVideo[] = $arrSlot;
                                break;
                            }
                    
                        }
                         
                    }
                }
                
                
                $arrNewContent[$key] = $arrSlot;
            }
        }


        if ( strlen($arrTextSlot['text']) ){
            $arrNewContent[] = $arrTextSlot;
        }

        //客户端短视频，删除多余信息
        if((self::$_hasMoVideo) && !empty($arrList)){
            foreach($arrList as $num){
                unset($arrNewContent[$num]);
            }
        }
        
        //视频贴只展示视频,也是为了兼容旧版本
        if(!empty($arrMoVideo)) {
            $arrNewContent = $arrMoVideo;
        } else {
            $arrNewContent = array_values($arrNewContent);
        }
        
        //将老版本的视频贴加入到回复楼层
        if(!empty($arrOldFloorVideoSolt)) {
            $strVideoText = "我发布了一个视频贴";
            $arrLastContent = end($arrNewContent);
            if(!empty($arrLastContent) && $arrLastContent['type'] === Molib_Util_RichText_Parser::SLOT_TYPE_TEXT){
                $strVideoText = "\n" . $strVideoText;// 需要加个换行
            }
            
            
            $arrVideoContent = array( 
                array(
                    'type' => Molib_Util_RichText_Parser::SLOT_TYPE_TEXT,
                    'text' => $strVideoText,
                ),
                $arrOldFloorVideoSolt,
                
            );
            $arrNewContent = array_merge($arrNewContent,$arrVideoContent);// 将视频content 加入content list
        }
        
        return $arrNewContent;
    }
    /** 
     * @param 
     * @return 
     */
    private static function _filterTailContent($arrContent)
    {
    	$arrSmile2TextTransData = Molib_Conf_Smile::$arrSmile2TextTransData;
    	$arrNewContent = array();
    	$arrTextSlot = array(
    			'type' => Molib_Util_RichText_Parser::SLOT_TYPE_TEXT,
    			'text' => ''
    	);
    	foreach ( $arrContent as $arrSlot ){
    	// 合并text content
            if ( $arrSlot['type'] == Molib_Util_RichText_Parser::SLOT_TYPE_TEXT ){
                $arrTextSlot['text'] .= $arrSlot['text'];
            }
            else{
            	if ( strlen($arrTextSlot['text']) ){
            		$arrNewContent[]     = $arrTextSlot;
            		$arrTextSlot['text'] = '';
            	}
            	// 不支持部分表情
            	if ( $arrSlot['type'] == Molib_Util_RichText_Parser::SLOT_TYPE_SMILE ){
            		$strSmileFileName = $arrSlot['text'];
            		$strSmileTextName = isset($arrSmileNotSupport[$strSmileFileName]) ? $arrSmileNotSupport[$strSmileFileName] : $strSmileFileName;
            		$arrSlot['c'] = $arrSmile2TextTransData[$strSmileFileName] ? $arrSmile2TextTransData[$strSmileFileName] : '';
            		if (isset($arrSmileNotSupport[$strSmileFileName])) {
            			$arrSlot['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_TEXT;
            			$arrSlot['text'] = '(' . $strSmileTextName . ')';
            		}

                    //【运营活动需求】屏蔽滑稽表情2天,时间:2017年2月8日12点———2017年2月10日12点, RD:chenzude PM:liling10
                    $intNowTime = time();
                    if (($intNowTime > self::SHIELD_FUNNY_FACE_START_TIME) && ($intNowTime <= self::SHIELD_FUNNY_FACE_END_TIME) && (trim($arrSlot['text']) == self::FUNNY_FACE_EMOTION_KEY)) {
                        $arrSlot['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_TEXT;
                        $strSmileTextName = "滑稽go die";
                        $arrSlot['text'] = '#' . $strSmileTextName . '#';
                        unset($arrSlot['c']);
                    }
            	}
            	else{
            		$arrSlot['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_TEXT;
            	}
            	$arrNewContent[] = $arrSlot;
            }
    	}
    	if ( strlen($arrTextSlot['text']) ){
    		$arrNewContent[] = $arrTextSlot;
    	}

    	return $arrNewContent;
    }
    /** 
     * @param 
     * @return 
     */
    private static function _processNativeApp($link){
    	$url = 'http://expand.video.iqiyi.com/api/app?apiKey=03606c91e150496688b0c62dfcbbd4ae&playurl='.$link;
    	$httpproxy = Orp_FetchUrl::getInstance();
    	$res = $httpproxy->get($url);
    	$arrRet = (array)Bingo_String::json2array($res);
    	if (!empty($arrRet['Android']) && !empty($arrRet['Ios'])){
    		return $arrRet;
    	}else {
    		return array();
    	}
    }

}



/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
