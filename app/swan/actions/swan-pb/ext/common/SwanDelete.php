<?php
    /**
     * Created by PhpStorm.
     * User: he<PERSON><PERSON><PERSON>
     * Date: 2019/3/15
     * Time: 11:43 AM
     * @brief 删除帖同步
     */
class Ext_Common_SwanDelete implements Core_ExtBase {

    private static $_objCoreData;
    private static $_bolIsValid  = false;
    private static $_intThreadId = 0;
    private static $_kz = 0;
    private static $_intOpenSource = 0;//来源

    /**
     * @desc:
     * @param:
     * @return:
     */
    public static function init($arrCoreData, $arrSingleData) {
        self::$_objCoreData = $arrCoreData;
        self::$_intThreadId = self::$_objCoreData['post']['thread_id'];
        self::$_kz = self::$_objCoreData['request']->getPrivateAttr('kz');
        //请求的主题ID
        self::$_kz      = intval( self::$_kz );
        self::$_intThreadId     = intval( self::$_intThreadId );
        self::$_intOpenSource   = intval(self::$_objCoreData['request']->getPrivateAttr('open_source', ''));
        if ( self::$_intThreadId == 0 && self::$_kz > 0 && ((self::$_intOpenSource >= 2000 && self::$_intOpenSource <= 2999) || (self::$_intOpenSource >= 1091001510012000 && self::$_intOpenSource <= 1091001510019000)|| self::$_intOpenSource == 10910015 || self::$_intOpenSource == 1091001510087000)){
            self::$_bolIsValid = true;
        }
    }

    /**
     * @param $arrCoreData
     * @return null
     */
    public static function getWLConfig($arrCoreData)
    {
        return null;
    }

    /**
     * @desc:
     * @param:
     * @return:
     */
    public static function isMultiCall() {
        return self::$_bolIsValid;
    }

    /**
     * @desc:
     * @param:
     * @return:
     */
    public static function getInputs() {
        if (!self::$_bolIsValid) {
            return null;
        }
        $arrInput = array(
            'thread_id' => self::$_kz,
        );
        $arrMultiInput['common:insertPushTaskDb'] = array (
            'serviceName' => 'common',
            'method'      => 'insertPushTaskDb',
            'input'       => $arrInput,
        );

        return $arrMultiInput;
    }

    /**
     * @desc:
     * @param:
     * @param
     * @return:
     */
    public static function execute( $arrMultiOutput, $arrOutData ) {
        if (self::$_bolIsValid) {
            $arrOutput = $arrMultiOutput ['common:insertPushTaskDb'];
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
                Molib_Util_Log::serviceWarning('common', 'insertPushTaskDb', null, $arrOutput);
            }
        }
        return $arrOutData;
    }
}