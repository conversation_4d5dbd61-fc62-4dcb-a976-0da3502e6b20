<?php
    /**
     * Created by PhpStorm.
     * User: he<PERSON><PERSON><PERSON>
     * Date: 2019/4/23
     * Time: 7:10 PM
     */
    class Ext_Common_ThreadReverse implements Core_ExtBase {
        private static $_objCoreData;

        /**
         * @param $arrCoreData
         * @return null
         */
        public static function getWLConfig($arrCoreData) {
            return null;
        }

        /**
         * @return null
         */
        public static function getInputs() {
            return null;
        }

        /**
         * @param $arrCoreData
         * @param $arrOutData
         */
        public static function init($arrCoreData, $arrOutData) {
            self::$_objCoreData = $arrCoreData;
        }

        /**
         * @param $arrMultiOutput
         * @param $arrOutData
         * @return mixed
         */
        public static function execute($arrMultiOutput, $arrOutData) {
            if (self::$_objCoreData['request']->getPrivateAttr('r') == 1) {
                $arrOutData['post_list'] = array_reverse($arrOutData['post_list']);
            }
            return $arrOutData;
        }

        /**
         * @return bool
         */
        public static function isMultiCall() {
            return true;
        }
    }
