<?php
    /**
     * Created by Ph<PERSON>Stor<PERSON>.
     * User: he<PERSON><PERSON><PERSON>
     * Date: 2019/3/26
     * Time: 4:17 PM
     */
class Ext_Common_ThreadBawu implements Core_ExtBase {
    private static $_objCoreData;
    private static $_bolIsValid  = true;
    private static $_intForumId  = 0;

    /**
     * @param $objCoreData
     * @param $arrSingleData
     */
    public static function init( $objCoreData, $arrSingleData ) {
        self::$_objCoreData = $objCoreData;
        self::$_intForumId  = self::$_objCoreData['forum']['forum_name']['forum_id'];

    }

    /**
     * @param $arrCoreData
     * @return null
     */
    public static function getWLConfig($arrCoreData)
    {
        return null;
    }

    /**
     * @return null
     */
    public static function isMultiCall()
    {
        return null;
    }

    /**
     * @return bool
     */
    public static function isValid() {
        return self::$_bolIsValid;
    }
    /**
     * @return array
     */
    public static function asynEnter() {
        return array(
            'reg_core_data' => array('perm'),
        );
    }

    /**
     * @return mixed
     */
    public static function getInputs() {
        return null;
    }

    /**
     * @param $arrMultiOutput
     * @param $arrOutData
     * @return mixed
     */
    public static function execute( $arrMultiOutput, $arrOutData ) {
        $arrPerm = self::$_objCoreData['perm']['output']['perm'];
        if ($arrPerm['can_type2_audit_post'] && $arrPerm['can_add_manager_team']) {
            $arrOutData['bawu_admintype'] = 2;
        } else if ($arrPerm['can_type1_audit_post'] && $arrPerm['can_member_top']) {
            $arrOutData['bawu_admintype'] = 1;
        } else if ($arrPerm['can_type3_audit_post']) {
            $arrOutData['bawu_admintype'] = 3;
        } else {
            $arrOutData['bawu_admintype'] = 0;
        }

        return $arrOutData;
    }
}