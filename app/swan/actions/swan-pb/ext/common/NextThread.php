<?php
    class Ext_Common_NextThread implements Core_ExtBase{
        private static $_objCoreData;
        private static $_isValid = false;
        private static $_intThreadid = 0;
        private static $_intForumId = 0;
        /**
         * @param
         * @return
         */
        public static function init($arrCoreData, $arrOutData){
            self::$_objCoreData = $arrCoreData;
            self::$_intThreadid = Bingo_Http_Request::get('nexttid');
            self::$_intForumId = $arrCoreData['thread']['output']['thread_list'][$arrCoreData['request']->getPrivateAttr('kz')]['forum_id'];
            if (self::$_intForumId <= 0 && empty(self::$_intThreadid)) {
                self::$_isValid = false;
            } else {
                self::$_isValid = true;
            }
            return true;
        }
        /**
         * @param
         * @return
         */
        public static function getWLConfig($arrBaseData) {
            return array();
        }
        /**
         * @param
         * @return
         */
        public static function asynEnter() {
            return array(
                'reg_core_data' => array('thread'),
                'reg_output_data' => array('thread'),
            );
        }
        /**
         * @param
         * @return
         */
        public static function isMultiCall() {
            return self::$_isValid;
        }
        /**
         * @param
         * @return
         */
        public static function getInputs() {
            if (!self::$_isValid) {
                return array();
            }
            if (empty(self::$_intThreadid)) {
                $arrMultiInput['post:getFrs'] = array(
                    'serviceName' => 'post',
                    'method' => 'getFrs',
                    'input' => array(
                        "need_abstract" => 1,
                        "forum_id"      => self::$_intForumId,
                        "need_photo_pic" => 1,
                        "need_user_data" => 0,
                        "offset"    => 0,
                        "res_num"   => 5,
                        "forum_name" => self::$_objCoreData['thread']['output']['thread_list'][self::$_objCoreData['request']->getPrivateAttr('kz')]['forum_name'],
                        "need_forum_name" => 1, //是否获取吧名
                        "call_from" => "client_frs" //上游模块名
                    ),
                );
            } else {
                $arrMultiInput['post:mgetThread'] = array(
                    'serviceName' => 'post',
                    'method' => 'mgetThread',
                    'input' => array(
                        "thread_ids" => array(
                            self::$_intThreadid
                        ),
                        "need_abstract" => 1,
                        "forum_id" => 0,
                        "need_photo_pic" => 0,
                        "need_user_data" => 1,
                        "icon_size" => 1,
                        "need_forum_name" => 0, //是否获取吧名
                        "call_from" => "client_frs" //上游模块名
                    ),
                );
                $arrMultiInput['post:getThreadMaskInfo'] = array(
                    'serviceName' => 'post',
                    'method' => 'getThreadMaskInfo',
                    'input' => array(
                        'forum_id'  => 0,
                        'thread_ids' => array(
                            self::$_intThreadid
                        )
                    ),
                );
            }
            return $arrMultiInput;
        }

        /**
         * @param $arrMultiOutput
         * @param $arrOutData
         * @return mixed
         */
        public static function execute($arrMultiOutput, $arrOutData){
            if (!self::$_isValid) {
                return $arrOutData;
            }
            if (empty(self::$_intThreadid)) {
                $arrOutput = $arrMultiOutput ['post:getFrs'];

                foreach ($arrOutput['output']['thread_list'] as $key => $value) {
                    if ($value['is_deleted'] == 1 || $value['top_types'] == 1 || self::$_objCoreData['request']->getPrivateAttr('kz') == $key) {
                        continue;
                    }
                    self::$_intThreadid = $key;
                    break;
                }
            } else {
                $arrOutput = $arrMultiOutput ['post:mgetThread'];
                $arrMaskInfo = $arrMultiOutput['post:getThreadMaskInfo'];
            }
            $arrOutData['next_thread'] = array();
            if(false === $arrOutput || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning('call post::mgetThread failed. '.serialize($arrOutput));
            } else {
                $thread = $arrOutput['output']['thread_list'][self::$_intThreadid];
                $user   = $arrOutput['output']['thread_user_list'][$thread['user_id']];
                if (count($thread['media']) > 0) {
                    foreach ($thread['media'] as $media) {
                        if (!empty($media['small_pic'])) {
                            $img = $media['small_pic'];
                            break;
                        }
                        if (!empty($media['vpic'])) {
                            $img = $media['vpic'];
                            break;
                        }
                    }
                }
                if (!empty($arrMaskInfo) && ($arrMaskInfo['output']['thread_info'][0]['is_deleted'] == 1
                    || $arrMaskInfo['output']['thread_info'][0]['is_exist'] != 1
                    || $arrMaskInfo['output']['thread_info'][0]['is_partial_visible'] == 1 )) {
                    return $arrOutData;
                }
                $arrRes = array(
                    'portrait'  => Tieba_Ucrypt::encode($user['user_id'], $user['user_name'], $user['portrait_time']),
                    'title'     => $thread['title'],
                    'abstract'  => $thread['abstract'],
                    // 'content'   => '',
                    'create_time' => $thread['create_time'],
                    'user_name'   => $user['user_name'],
                    'user_nickname' => $user['user_nickname'],
                    'name_show' => empty($user['user_nickname']) ? $user['user_name'] : $user['user_nickname'],
                    'tid'       => self::$_intThreadid,
                    'thread_id' => self::$_intThreadid,
                    'img'       => empty($img) ? '' : $img,
                );
                $arrOutData['next_thread'] = $arrRes;
            }
            return $arrOutData;
        }
    }