<?php
    /**
     * Created by PhpStorm.
     * User: he<PERSON><PERSON><PERSON>
     * Date: 2019/3/14
     * Time: 3:19 PM
     * Desc: 搜索导流跳入pb时解析refer中eqid为word
     */
class Ext_Common_Eqid2Wd implements Core_ExtBase {

    private static $_objCoreData;

    /**
     * @param $objCoreData
     * @param $arrSingleData
     */
    public static function init($objCoreData,$arrSingleData){
        self::$_objCoreData = $objCoreData;
    }

    /**
     * @param $arrCoreData
     * @return null
     */
    public static function getWLConfig($arrCoreData)
    {
        return null;
    }

    /**
     * @return bool
     */
    public static function isMultiCall()
    {
        return true;
    }

    /**
     * @return array
     */
    public static function getInputs(){
        $strRefer = Bingo_Http_Request::getServer('HTTP_REFERER');
        if (empty($strRefer)) {
            return;
        }

        $psQuery = self::_getPsWd($strRefer);
        if (false !== $psQuery && '' !== $psQuery) {
            Tieba_Stlog::addNode('eqid2wd', $psQuery);
            return;
        }

        $psEqid = self::_getPsEqid($strRefer);
        Tieba_Stlog::addNode('ps_eqid', $psEqid);
        if (false === $psEqid || '' === $psEqid) {
            return;
        }
        $arrMultiInput = array();
        if ('' !== $psEqid) {

            $arrInput = array(
                'eqid'  => $psEqid,
            );
            $arrMultiInput['tbrs:eqid2Wd'] = array(
                'serviceName' => 'tbrs',
                'method' => 'eqid2Wd',
                'input' => $arrInput,
            );
        }

        return $arrMultiInput;
    }

    /**
     * @param $arrMultiOutput
     * @param
     * @return mixed
     */
    public static function execute($arrMultiOutput, $arrOutData){
        $arrOutput = $arrMultiOutput['tbrs:eqid2Wd'];
        $strPsQuery = isset($arrOutput['data']['ps_query']) ? $arrOutput['data']['ps_query'] : '';
        $strFname = self::$_objCoreData['forum']['forum_name']['forum_name'];
        $objSource = self::$_objCoreData['request']->getPrivateAttr('obj_source');
        $strTips = '';
        if (strlen($strFname) > 0) {
            if ($objSource == 'searchres') { // 搜索结果页
                $strTips = '进入' . $strFname . '吧，了解更多【' . $strPsQuery . '】相关内容';
            } else {
                $strTips = '进入' . $strFname . '吧，了解更多相关内容';
            }
        }
        if (!empty($strPsQuery)) {
            Tieba_Stlog::addNode('eqid2wd', $strPsQuery);
        }
        $arrOutData['strTips'] = $strTips;
        return $arrOutData;
    }

    /**
     * 解析refer中的eqid
     * @param $refer
     * @return bool|string
     */
    private static function _getPsEqid($refer) {
        if ('' === $refer) {
            Bingo_Log::warning("input refer is null");
            return false;
        }
//        $refer = 'https://m.baidu.com/from=844b/bd_page_type=1/ssid=818c6c697579656c697566656e67b515/uid=0/pu=sz%401320_2001%2Cta%40iphone_1_8.0_3_600%2Cusm%403/baiduid=CAE6B77DA3C29B049B962DCFF554011A/w=0_10_%E5%A4%A7%E4%B8%BB%E5%AE%B0/t=iphone/l=1/tc?ref=www_iphone&lid=10202399916886494647&order=1&fm=altb&tj=Wc_1_0_10_title&sec=9322&di=da585ab696428b03&bdenc=1&tch=*********.0.0&nsrc=IlPT2AEptyoA_yixCFOxXnANedT62v3IEhuYNy5K1De8mVjte4viZQRAZHKgVnCFZpLRgTDLoxkYwnHR0HEo8_ZBu_-lpWwq7XiRc3nqhaG2CBYLqM26O0LDVCFsl0qck4Ztg2UJBwFi&eqid=8d9634ab767e90001000000356962330&wd=';

        $refer = urldecode($refer);
        $preg = '/^https?:\/\/m\.baidu\.com.+(eqid)\s*=\s*([^&]+)/';
        $out = array();
        $pregRet = preg_match($preg, $refer, $out);
        if ($pregRet > 0) {
            $strPsEqid = isset($out[2])?$out[2]:'';
        } else {
            $strPsEqid = '';
        }

        return $strPsEqid;
    }

    /**
     * 解析refer中的wd
     * @param $refer
     * @return bool|string
     */
    private static function _getPsWd($refer)
    {
        if ('' === $refer) {
            Bingo_Log::warning("input refer is null");
            return false;
        }

//        $refer = 'https://m.baidu.com/from=844b/bd_page_type=1/ssid=818c6c697579656c697566656e67b515/uid=0/pu=sz%401320_2001%2Cta%40iphone_1_8.0_3_600%2Cusm%403/baiduid=CAE6B77DA3C29B049B962DCFF554011A/w=0_10_%E5%A4%A7%E4%B8%BB%E5%AE%B0/t=iphone/l=1/tc?ref=www_iphone&lid=10202399916886494647&order=1&fm=altb&tj=Wc_1_0_10_title&sec=9322&di=da585ab696428b03&bdenc=1&tch=*********.0.0&nsrc=IlPT2AEptyoA_yixCFOxXnANedT62v3IEhuYNy5K1De8mVjte4viZQRAZHKgVnCFZpLRgTDLoxkYwnHR0HEo8_ZBu_-lpWwq7XiRc3nqhaG2CBYLqM26O0LDVCFsl0qck4Ztg2UJBwFi&eqid=8d9634ab767e90001000000356962330&wd=';

        $refer = urldecode($refer);
        $preg = '/^https?:\/\/m\.baidu\.com.+(wd|word)\s*=\s*([^&]+)/i';
        $out = array();
        $pregRet = preg_match($preg, $refer, $out);
        $strPsQuery = '';

        if ($pregRet > 0) {
            $query = isset($out[2])?$out[2]:'';
            $isReferIeUtf8 = stripos($refer, 'ie=utf-8');

            if (false !== $isReferIeUtf8){
                $strPsQuery = $query;
            }
            else {
                $strPsQuery = Bingo_Encode::convert($query, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
            }
        }

        return $strPsQuery;
    }
}