<?php
class Util_Redis{
    const SERVICE_NAME = "Util_Redis";

    protected static $_redis = array();
    protected static $_redisQueryCounter = 0;

    private static function _getRedis($strRedisName){

        if(self::$_redis[$strRedisName]){
            return self::$_redis[$strRedisName];
        }
        Bingo_Timer::start('redisinit_'.$strRedisName);

        $objRedis = new Bingo_Cache_Redis($strRedisName);

        Bingo_Timer::end('redisinit'.$strRedisName);
        if(!$objRedis->isEnable() ){
            Bingo_Log::warning("init redis fail.");
            return false;
        }
        self::$_redis[$strRedisName] = $objRedis;
        return self::$_redis[$strRedisName];
    }

    public static function redisQuery($strRedisName,$strFunc,array $arrParams,$intRetryTimes = 0){

        $intRetryTimes = intval($intRetryTimes);
        $mixRedis = self::_getRedis($strRedisName);
        if(!$mixRedis){
            return false ;
        }

        self::$_redisQueryCounter++;
        $strTimerKey = "redisquery_".$strRedisName."_".$strFunc."_".self::$_redisQueryCounter;

        do {
            Bingo_Timer::start($strTimerKey);
            $arrRet = $mixRedis->$strFunc($arrParams);
            Bingo_Timer::end($strTimerKey);

            Bingo_Log::debug("$strTimerKey ret=".var_export($arrRet,true));

            if($arrRet['err_no'] !== 0 ){
                Bingo_Log::warning("call redis query error.[redisname=$strRedisName][func=$strFunc][input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
            }else{
                break;
            }
        }while($intRetryTimes-- > 0);

        return $arrRet;
    }
}