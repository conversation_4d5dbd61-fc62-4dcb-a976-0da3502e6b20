<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-03-13 16:23:26
 * @version
 */
class addPicAction extends Difang_Action_Base {
	const MAX_PIC_SIZE = 5368709120;
	private $intGroupId = 0;
	private $strContent = '';
	private $intRid = 0;
    private $intUid = 0;
    private $strUname = '';
	private $intPeerUid = 0;
	private $strPeerUname = '';
	protected $bolCheckUserLogin = true; 
	protected $bolNeedUserLogin = true; 
	protected $bolNeedCheckSign = false;
    public function _input() {
		$this->intType = intval(Bingo_Http_Request::get('type',2));
		$this->intGroupId = intval(Bingo_Http_Request::get('gid',0));
		$this->intSize = intval(Bingo_Http_Request::get('size',0));
		$this->intRid = intval(Bingo_Http_Request::get('rid',0));
		$this->intPeerUid = intval(Bingo_Http_Request::get('peer_uid', 0));
		$this->strPeerUname = strval(Bingo_Http_Request::get('peer_uname', ''));
		$group_id = $this->intGroupId;
		
		//TODO :about login
		if ( $group_id == 0){
			Bingo_Log::warning('Invid param');
			$this->_error(Tieba_Errcode::ERR_PARAM_ERROR);
			return false;
		}
		
		if ($this->intSize > self::MAX_PIC_SIZE){
			$this->_error(Tieba_Errcode::ERR_DIFANG_MSG_TOO_LARGE);
			return false;
		}
		
		//查询群组信息，如果是私聊则跳过这个过程
		if ($this->intPeerUid == 0) {
			$arrParam = array(
					"group_ids" => $group_id,
			);
			$arrGroupInfo = Tieba_Service::call("difang","queryGroupsByGids",$arrParam);
			if($arrGroupInfo['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				Bingo_Log::warning("call another service error, output:".serialize($arrGroupInfo));
				return self::_errRet($arrGroupInfo['errno']);
			}
			Bingo_Log::debug("call queryGroupsByGids ,output:".print_r($arrGroupInfo,true));
		}
		
		//群组类型：0：普通群组，1：世界群组，2：预设群组，3：私聊群组
		//普通群组和预设群组需要校验用户是否加入
		if ( $arrGroupInfo['groups'][0]['group_class'] !== 1 && $this->intPeerUid == 0){
			if(!CapiRequest::$intUid ){
				Bingo_Log::warning('user not login');
				$this->_error(Tieba_Errcode::ERR_USER_NOT_LOGIN);
				return false;
			}
			
			$arrParam = array(
					"user_id" => CapiRequest::$intUid,
					"group_ids" => $group_id,
			);
			$arrUserGroupInfo = Tieba_Service::call("difang","mgetByUidGids",$arrParam);
			if($arrUserGroupInfo['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				Bingo_Log::warning("call another service error, output:".serialize($arrUserGroupInfo));
				return self::_errRet($arrUserGroupInfo['errno']);
			}
			Bingo_Log::debug("call mgetByUidGids ,output:".print_r($arrUserGroupInfo,true));
			
			if( (!isset($arrUserGroupInfo['group_list'][$group_id]['is_in']) ||
			$arrUserGroupInfo['group_list'][$group_id]['is_in'] !==1) && 
			$this->intPeerUid == 0){
				Bingo_Log::warning('user is not a member of group[user_id='.CapiRequest::$intUid.',group_id='.$group_id.']');
				$this->_error(Tieba_Errcode::ERR_DIFANG_MSG_NOT_MEMBER);
				return false;
			}
		
		}
		
        if (empty($_FILES['content'])){
            $intErrno = 4;//content is empty
        }else{
            $intErrno = intval($_FILES['content']['error']);            
        }        
        if ($intErrno > 0){
            Bingo_Log::warning("pic upload error,php errno={$intErrno}");
			$this->_error(Tieba_Errcode::ERR_DIFANG_MSG_PIC_FAIL);
            return false;	
        }  
        if (isset($_FILES['content']['tmp_name'])){
			$this ->strContent = file_get_contents($_FILES['content']['tmp_name']);
        }else{
            Bingo_Log::warning("temp file not found");
			$this->_error(Tieba_Errcode::ERR_DIFANG_MSG_PIC_FAIL);
            return false;	
        }

		return true;
    }
    
    public function _process() {
    	
    	//频率控制
    	$arrParam = array(
    			'req' => array(
    					'rulegroup' => array('app'),
    					'app' => 'difang',
    					'cmd' => 'sendmsg',
    					'uid' => CapiRequest::$intUid,
    					'gid' => $this->intGroupId,
    					'need_replace' => 0,
    					'anti_command_no' => 50007,
    			),
    	);
    	$arrAntiRet = Tieba_Service::call("anti","antiLogicQuery",$arrParam);
    	if(isset($arrAntiRet['errno']) && $arrAntiRet['errno'] !== 0){
    		Bingo_Log::debug('anti about frequency not pass,input:'.serialize($arrAntiRet));
    		$this->_error(Tieba_Errcode::ERR_DIFANG_MSG_HIGH_FREQUENCE);
    		return true;
    	}
    	
        $arrImageRet = Image::uploadImage($this->strContent);
		if ($arrImageRet['no'] !== 0){
			$this->_error(Tieba_Errcode::ERR_DIFANG_MSG_PIC_FAIL);
		    return false;
		}

		$arrDBInput = array (
			'pid' => $arrImageRet['data']['pic_id_encode'],
			'height' => $arrImageRet['data']['height'],
			'width' =>$arrImageRet['data']['width'],
			);
		$ret = Tieba_Service::call('difang', 'storePic', $arrDBInput);
        if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']){
            Bingo_Log::warning('call service storePic failed,errno[' . $ret['errno'] . '],errmsg[' . $ret['errmsg'] . ']');
			$this->_error(Tieba_Errcode::ERR_DIFANG_MSG_PIC_FAIL);
			return false;
        }
		$arrInput = array (
			'group_id' => $this->intGroupId,
			'type' => 2,
			'size' => $this->intSize,
			'rid'   => $this->intRid,
			'author_id' => CapiRequest::$intUid,
			'author_name' => CapiRequest::$strUname,
			'content' => $arrImageRet['data']['pic_id_encode'],
			'height' => $arrImageRet['data']['height'],
			'width' =>$arrImageRet['data']['width'],
			);

        if ($this->intPeerUid != 0 && $this->strPeerUname != '') {
            $arrInput['user_id1'] = CapiRequest::$intUid;
            $arrInput['user_name1'] = CapiRequest::$strUname;
            $arrInput['user_id2'] = $this->intPeerUid;
            $arrInput['user_name2'] = $this->strPeerUname;
            $ret = Tieba_Service::call('difang', 'addLetter', $arrInput);
        } else {
			$ret = Tieba_Service::call('difang', 'addPost', $arrInput);
		}
        if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']){
            Bingo_Log::warning('call service addPost failed,errno[' . $ret['errno'] . '],errmsg[' . $ret['errmsg'] . ']');
			//$this->_error(Tieba_Errcode::ERR_DIFANG_MSG_PIC_FAIL);
			//return false;
            $this->_error($ret['errno']);
            return false;
        }
 
		$ret['height'] = $arrImageRet['data']['height'];
		$ret['width'] = $arrImageRet['data']['width'];
		$ret['pid'] = $arrImageRet['data']['pic_id_encode'];
		$this->arrData = $ret;

		return true;
    }
	public function _build() {
		return true;
	}
}
