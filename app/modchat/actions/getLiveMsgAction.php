<?php
/***************************************************************************
 *
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * $Id$
 *
 **************************************************************************/

/**
 * @file getMsgAction.php
 * <AUTHOR>
 * @date 2013/07/09
 * @version $Revision$
 * @brief
 **/
class getLiveMsgAction extends Difang_Action_Base {
    protected $bolNeedUserLogin = true;
    private $intGid = 0;
    private $intMid = 0;
    private $intAround = 0;
    private $intType = 0;        //类型:0全部 1文字 2图片 3语音
    
    private $_arrUser = array();
    private $_arrRoles = array();

    public function _input() {
        $this->intGid = intval(Bingo_Http_Request::get('gid', 0));
        $this->intFromMid = intval(Bingo_Http_Request::get('from_mid', 0));
        $this->intToMid = intval(Bingo_Http_Request::get('to_mid', 0));
        $this->intAround = intval(Bingo_Http_Request::get('around', 0));
        $this->intType = intval(Bingo_Http_Request::get('type', 0));

        return true;
    }

    public function _process() {
        //获取消息列表
        $arrInput = array (
            'gid' => $this->intGid,
            'from_mid' => $this->intFromMid,
            'to_mid' => $this->intToMid,
            'around' => $this->intAround,
            'type' => $this->intType,
        );
        $arrRet = Tieba_Service::call('difang', 'getLiveMsg', $arrInput);
        if($arrRet==false || $arrRet['errno']!=0) {
            $this->_error($arrRet['errno']);
            return false;
        }
        $this->arrData = $arrRet;

        return true;
    }

    public function _build() {
        return true;
    }
}
