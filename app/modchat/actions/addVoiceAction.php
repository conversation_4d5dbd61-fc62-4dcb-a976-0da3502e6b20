<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-03-13 16:23:26
 * @version
 */
class addVoiceAction extends Difang_Action_Base {
	const MAX_VOICE_SIZE = 3145728;
	private $intForumId = 0;
	private $intGroupId = 0;
	private $strContent = '';
	private $intType = 0;
	private $intDuration = 0;
	private $intRid = 0;
	private $intSize = 0;
	private $strVid = 0;
	private $intUid = 0;
	private $strUname = '';
	private $intPeerUid = 0;
	private $strPeerUname = '';
	protected $bolCheckUserLogin = true;
	protected $bolNeedUserLogin = true;
	protected $bolNeedCheckSign = false;
    public function _input() {
		$this->intType = intval(Bingo_Http_Request::get('type',3));
		$this->intGroupId = intval(Bingo_Http_Request::get('gid',0));
		$this->intDuration = intval(Bingo_Http_Request::get('duration',0));
		$this->intSize = intval(Bingo_Http_Request::get('size',0));
		$this->intRid = intval(Bingo_Http_Request::get('rid',0));
		$this->intPeerUid = intval(Bingo_Http_Request::get('peer_uid', 0));
		$this->strPeerUname = strval(Bingo_Http_Request::get('peer_uname', ''));
		$group_id = $this->intGroupId;
		
		if ($this->intSize > self::MAX_VOICE_SIZE){
			Bingo_Log::warning('message too large, size:'.$this->intSize.' '.self::MAX_VOICE_SIZE);
			$this->_error(Tieba_Errcode::ERR_DIFANG_MSG_TOO_LARGE);
			return false;
		}
		//TODO :about login
		if ( $group_id == 0 || $this->intDuration == 0 ){
			Bingo_Log::warning('Invid param:gid:'.$this->intGroupId.',duration:'.$this->intDuration);
			return false;
		}
		
		//查询群组信息，如果是私聊则跳过这个过程
		if ($this->intPeerUid == 0) { 
			$arrParam = array(
					"group_ids" => $group_id,
			);
			$arrGroupInfo = Tieba_Service::call("difang","queryGroupsByGids",$arrParam);
			if($arrGroupInfo['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				Bingo_Log::warning("call another service error, output:".serialize($arrGroupInfo));
				return self::_errRet($arrGroupInfo['errno']);
			}
			Bingo_Log::debug("call queryGroupsByGids ,output:".print_r($arrGroupInfo,true));	
		}
		
		//群组类型：0：普通群组，1：世界群组，2：预设群组，3：私聊群组
		//普通群组和预设群组需要校验用户是否加入
		if ( $arrGroupInfo['groups'][0]['group_class'] !== 1 && $this->intPeerUid == 0){
			if(!CapiRequest::$intUid ){
				Bingo_Log::warning('user not login');
				$this->_error(Tieba_Errcode::ERR_USER_NOT_LOGIN);
				return false;
			}
			
			$arrParam = array(
					"user_id" => CapiRequest::$intUid,
					"group_ids" => $group_id,
			);
			$arrUserGroupInfo = Tieba_Service::call("difang","mgetByUidGids",$arrParam);
			if($arrUserGroupInfo['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				Bingo_Log::warning("call another service error, output:".serialize($arrUserGroupInfo));
				return self::_errRet($arrUserGroupInfo['errno']);
			}
			Bingo_Log::debug("call mgetByUidGids ,output:".print_r($arrUserGroupInfo,true));
			
			if( (!isset($arrUserGroupInfo['group_list'][$group_id]['is_in']) ||
			$arrUserGroupInfo['group_list'][$group_id]['is_in'] !==1) &&
			$this->intPeerUid == 0){
				Bingo_Log::warning('user is not a member of group[user_id='.CapiRequest::$intUid.',group_id='.$group_id.']');
				$this->_error(Tieba_Errcode::ERR_DIFANG_MSG_NOT_MEMBER);
				return false;
			}
		
		}
		
        if (empty($_FILES['content'])){
            $intErrno = 4;//content is empty
        }else{
            $intErrno = intval($_FILES['content']['error']);            
        }        
        if ($intErrno > 0){
            Bingo_Log::warning("voice upload error,php errno={$intErrno}");
            return false;	
        }  
        if (isset($_FILES['content']['tmp_name'])){
			$this ->strContent = file_get_contents($_FILES['content']['tmp_name']);
        }else{
            Bingo_Log::warning("temp file not found");
        }

		return true;
    }
    
    public function _process() {
    	
    	//频率控制
    	$arrParam = array(
    			'req' => array(
    					'rulegroup' => array('app'),
    					'app' => 'difang',
    					'cmd' => 'sendmsg',
    					'uid' => CapiRequest::$intUid,
    					'gid' => $this->intGroupId,
    					'need_replace' => 0,
    					'anti_command_no' => 50007,
    			),
    	);
    	$arrAntiRet = Tieba_Service::call("anti","antiLogicQuery",$arrParam);
    	if(isset($arrAntiRet['errno']) && $arrAntiRet['errno'] !== 0){
    		Bingo_Log::debug('anti about frequency not pass,input:'.serialize($arrAntiRet));
    		$this->_error(Tieba_Errcode::ERR_DIFANG_MSG_HIGH_FREQUENCE);
    		return true;
    	}
    	
		$this->strVid = md5($this->strContent);
		$arrInput = array (
			'vid' => $this->strVid,
			'content' => $this->strContent,
			'duration' => $this->intDuration,
			);
		$ret = Tieba_Service::call('difang', 'storeVoice', $arrInput);
        if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']){
            Bingo_Log::warning('call service storeVoice::commit failed,errno[' . $ret['errno'] . '],errmsg[' . $ret['errmsg'] . ']');
        }

		$arrInput = array (
			'group_id' => $this->intGroupId,
			'type' => 3,
			'rid'   => $this->intRid,
			'size' => $this->intSize,
			'author_id' => CapiRequest::$intUid,
			'author_name' => CapiRequest::$strUname,
			'content' => $this->strVid,
			'duration' => $this->intDuration,
			);
        if ($this->intPeerUid != 0 && $this->strPeerUname != '') {
            $arrInput['user_id1'] = CapiRequest::$intUid;
            $arrInput['user_name1'] = CapiRequest::$strUname;
            $arrInput['user_id2'] = $this->intPeerUid;
            $arrInput['user_name2'] = $this->strPeerUname;
            $ret = Tieba_Service::call('difang', 'addLetter', $arrInput);
        } else {
			$ret = Tieba_Service::call('difang', 'addPost', $arrInput);
		}
        if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']){
            Bingo_Log::warning('call service addPost::commit failed,errno[' . $ret['errno'] . '],errmsg[' . $ret['errmsg'] . ']');
            $this->_error($ret['errno']);
            return false;
        }
 
		$this->arrData = $ret;
		$this->arrData['vid'] = $this->strVid;

		return true;
    }
	public function _build() {
		return true;
	}
}
