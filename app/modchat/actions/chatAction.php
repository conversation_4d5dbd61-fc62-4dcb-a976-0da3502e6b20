<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-03-19 16:23:26
 * @version
 */
class chatAction extends Difang_Action_Base {

    const CMD_ONLINE = 10;
    const CMD_OFFLINE = 11;
    const CMD_COMEIN = 12;
    const CMD_LEAVE = 13;

    const SECURE_KEY = 'difangba%#2013&*lcs(@';

    public $intCmd = 0;
    public $intGroupId = 0;
    public $strMySecure = '';
    public $strLcsSecure = '';
    public $intLcsConnTime = 0;
    public $strLcsIP = '';
    public $intLcsPort = 0;
    public $intLcsFd = 0;
    public $intLcsSign = 0;
    public $intLcsWorkerId = 0;
    public $intMessageId = 0;
    public $strBDUSS = '';
    public $intErrno = 0;

    protected $intPeerUid = 0;        //私聊对方uid
    protected $strPeerUname = '';    //私聊对方uname
    protected $bolDialog = false;    //标记是否私聊
    
    protected $bolNeedCheckSign = 0;
    protected $bolIsLcs = true;

    protected function _load() {
        if (false == $this->_myinput()){
            Bingo_Log::warning("input error");
            header(404);
        }
        // 登陆用
        $this->_user_login();
        return parent::_load();
    }

    protected function _myinput() {
        $this->strLcsIP = strval(Bingo_Http_Request::get('lcs_ip', ''));
        $this->intLcsPort = intval(Bingo_Http_Request::get('lcs_port', 0));
        $this->intLcsFd = intval(Bingo_Http_Request::get('lcs_fd', 0));
        $this->intLcsSign = strval(Bingo_Http_Request::get('lcs_sign', 0));
        $this->intLcsWorkerId = intval(Bingo_Http_Request::get('lcs_worker_id', 0));
        $this->strLcsSecure = strval(Bingo_Http_Request::get('lcs_secure', ''));
        $this->intLcsConnTime = intval(Bingo_Http_Request::get('lcs_conn_time', 0));
        $strClientReq = strval(Bingo_Http_Request::get('data', 0));

        // check 生成算法
        $strTmp = self::SECURE_KEY . ":" . $this->intLcsSign;
        $this->strMySecure = sha1($strTmp);
        Bingo_Log::debug("lcs secure : " . $this->strLcsSecure . ", my secure sha1($strTmp): " . $this->strMySecure);

            if (empty($strClientReq)){
            Bingo_Log::warning("data input empty");
            return false;
        }

        $arrReq = Bingo_String::json2array($strClientReq);
        $this->intCmd = $arrReq['h']['cmd'];
        $this->intGroupId = $arrReq['d']['gid'];
        $this->intMessageId = $arrReq['d']['mid'];
        $this->strBDUSS = strval($arrReq['d']['bduss']);
        
        $this->intPeerUid = intval($arrReq['d']['peer_uid']);
        $this->strPeerUname = strval($arrReq['d']['peer_uname']);
        $this->bolDialog = $this->intPeerUid==0 ? false : true;

        return true;
    }

    protected function _process() {
        switch($this->intCmd){

            case chatAction::CMD_ONLINE:
                $this->_process_online();
                break;
            case chatAction::CMD_OFFLINE:
                $this->_process_offline();
                break;
            case chatAction::CMD_COMEIN:
                $this->_process_comein();
                break;
            case chatAction::CMD_LEAVE:
                $this->_process_leave();
                break;
            default :
                header(404);
                Bingo_Log::warning('unknown cmd :'.$this->intCmd);
        }
        return true;
    }

    protected function _process_comein() {
    //    $this->_user_login();
        if (empty($this->strLcsIP) || empty($this->intLcsPort) || empty($this->intLcsFd) ||
                empty($this->intLcsSign) ){
            Bingo_Log::warning("important input empty");
            return false;
        }
        $arrReq = array(
            'user_id'    => CapiRequest::$intUid,
            'user_name' => CapiRequest::$strUname,
            'lcs_ip'     => $this->strLcsIP,
            'lcs_port'     => $this->intLcsPort,
            'lcs_fd'     => $this->intLcsFd,
            'lcs_sign'    => $this->intLcsSign,
            'lcs_worker_id'    => $this->intLcsWorkerId,
            'group_id'    => $this->intGroupId,
            'is_dialog' => $this->bolDialog,
        );

        $ret = Tieba_Service::call('difang', 'addUser', $arrReq);
        Bingo_Log::debug("request register @".serialize ($arrReq) . ", ret @".serialize($ret));
        if($ret === false) {
            Warning::callRpcReturnFalse('difang', 'addUser');
            $this->intErrno = CapiErrno::INTERNAL_ERROR;
        }
        if (0 !== intval($ret['errno'])){
            Warning::callRpcReturnFalse('difang', 'addUser');
            $this->intErrno = $ret['errno'];
        }
        return true;
    }

    protected function _process_online() {
        //$this->_user_login();
        $arrReq = array(
            'user_name' => CapiRequest::$strUname,
            'user_id' => CapiRequest::$intUid,
            'lcs_ip' => $this->strLcsIP,
            'lcs_port' => $this->intLcsPort,
            'lcs_sign' => $this->intLcsSign,
            'lcs_fd' => $this->intLcsFd,
        );

        $ret = Tieba_Service::call('difang', 'userLogin', $arrReq);
        Bingo_Log::debug("request register @".serialize ($arrReq) . ", ret @".serialize($ret));
        if($ret === false) {
            Warning::callRpcReturnFalse('difang', 'userLogin');
            $this->intErrno = CapiErrno::INTERNAL_ERROR;
        }
        if (0 !== intval($ret['errno'])){
            Warning::callRpcReturnFalse('difang', 'addUser');
            $this->intErrno = $ret['errno'];
        }
        return true;
    }

    protected function _process_offline() {
        if (!$this->_check_secure()){
            Bingo_Log::warning("check secure incorrect");
            return false;
        }
        $arrReq = array(
            //'user_id' => CapiRequest::$intUid,
            //'user_name' => CapiRequest::$strUname,
            'lcs_sign' => $this->intLcsSign,
        );

        $ret = Tieba_Service::call('difang', 'userLogout', $arrReq);
        Bingo_Log::debug("request register @".serialize ($arrReq) . ", ret @".serialize($ret));
        if($ret === false) {
            Warning::callRpcReturnFalse('difang', 'userLogout');
            $this->intErrno = CapiErrno::INTERNAL_ERROR;
        }
        return true;
    }

    protected function _process_leave() {
        if($this->bolDialog) {
            return $this->_process_leave_dialog();
        } else {
            return $this->_process_leave_group();
        }
    }
        
    /*
    *离开普通群
    */
    protected function _process_leave_group() {
        //$this->_user_login();
        if (empty($this->intLcsSign)){
            Bingo_Log::warning("sign input empty ");
            return false;
        }
        $arrReq = array(
            'user_id'    => CapiRequest::$intUid,
            'user_name' => CapiRequest::$strUname,
            'lcs_sign'    => $this->intLcsSign,
            'group_id'    => $this->intGroupId,
            'is_dialog' => $this->bolDialog,
        );

        $ret = Tieba_Service::call('difang', 'delUser', $arrReq);
        Bingo_Log::debug("request register @".serialize ($arrReq) . ", ret @".serialize($ret));
        if($ret === false) {
            Warning::callRpcReturnFalse('difang', 'delUser');
            $this->intErrno = CapiErrno::INTERNAL_ERROR;
        }

        if (0 === CapiRequest::$intUid){
            Bingo_Log::debug("user not login");
            return true;
        }

        if (empty($this->intMessageId)) {
            return true;
        }
        $arrReqGroup = array(
            'user_id'    => CapiRequest::$intUid,
            'group_id'    => $this->intGroupId,
            'message_id' => $this->intMessageId,
        );

        $ret = Tieba_Service::call('difang', 'setReadMsgId', $arrReqGroup);
        Bingo_Log::debug("request setReadMsgId @".serialize ($arrReqGroup).", @".serialize($ret));
        if($ret === false) {
            Warning::callRpcReturnFalse('difang', 'setReadMsgId');
            $this->intErrno = CapiErrno::INTERNAL_ERROR;
        }
        return true;
    }

    /*
    *离开私聊群
    *私聊群用户必然已经加入，所以不需要在register中del
    */
    protected function _process_leave_dialog() {
        //未登录用户没有私聊群，直接返回
        if(CapiRequest::$bolLogin != true) {
            return true;
        }

        if($this->intGroupId<1 || $this->intMessageId<1) {
            Bingo_Log::warning("group_id or message_id is empty");
            return false;
        }

        $arrReq = array(
            'user_id' => CapiRequest::$intUid,
            'group_id' => $this->intGroupId,
            'message_id' => $this->intMessageId,
        );
        $ret = Tieba_Service::call('difang', 'setDialogMsgId', $arrReq);
        if($ret===false || $ret['errno']!=0) {
            Warning::callRpcReturnFalse('difang', 'setDialogMsgId');
            $this->intErrno = CapiErrno::INTERNAL_ERROR;
            return false;
        }

        return true;
    }


    protected function _build() {
        $arrHeader = array();
        $arrHeader['v'] = 1;
        $arrHeader['rep'] = $this->intCmd;
        $arrData = array();
        $arrData['errno'] = $this->intErrno;
        $arrData['group'] = $this->intGroupId;

        $arrData['uid'] = CapiRequest::$intUid;
        $this->arrData['h'] = $arrHeader;
        $this->arrData['d'] = $arrData;
        return true;
    }

    protected function _user_login() {
        //兼容线上各种bduss形式, 跟Base.php中的处理保持一致
        $strBSP = $this->strBDUSS;
        $arrBDUSS = explode('|', $strBSP);
        if(! ($arrBDUSS == false || !isset($arrBDUSS[0]) || $arrBDUSS[0] == '')) {
            $_COOKIE['BDUSS'] = $arrBDUSS[0];
        } else {
            $_COOKIE['BDUSS'] = $strBSP;
        }
        UserService::processUserLogin();
    }

    protected function _check_secure() {
        if ($this->strMySecure != $this->strLcsSecure){
            //Bingo_Log::warning("post : " . )
            return false;
        }
        return true;
    }
}
