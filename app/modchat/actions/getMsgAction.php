<?php
/***************************************************************************
 *
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * $Id$
 *
 **************************************************************************/

/**
 * @file getMsgAction.php
 * <AUTHOR>
 * @date 2013/04/03 10:15:25
 * @version $Revision$
 * @brief
 *
 **/
class getMsgAction extends Difang_Action_Base {
    private $intGid = 0;
    private $intMid = 0;
    private $intAround = 0;
    private $intBack = 0;
    private $intPeerUid = 0;
    private $strPeerUname = '';
    private $bolDialog = false;
    protected $bolNeedUserLogin = true;
    
    private $_arrOlUser = array();
    private $_arrMember = array();
    private $_arrUser = array();
    private $_arrForumRoles = array();
    private $_arrGroupRoles = array();

    private $_arrLiveData = null;            //直播群数据，group_class=5为直播群
    
    public function _input(){
        $this->intGid = intval(Bingo_Http_Request::get('gid',0));
        $this->intFromMid = intval(Bingo_Http_Request::get('from_mid',0));
        $this->intToMid = intval(Bingo_Http_Request::get('to_mid',0));
        $this->intAround = intval(Bingo_Http_Request::get('around', 0));
        $this->intBack = intval(Bingo_Http_Request::get('back', 0));
        $this->intPeerUid = intval(Bingo_Http_Request::get('peer_uid', 0));
        $this->strPeerUname = strval(Bingo_Http_Request::get('peer_uname', ''));
        $this->bolDialog = $this->intPeerUid == 0 ? false : true;

        return true;
    }

    public function _process() {
        //获取在线用户列表
        $arrRequest = array(
            'group_id' => $this->intGid,
            'keys' => 'user_id',
        );
        $ret = Tieba_Service::call('difang', 'queryUserList', $arrRequest);
        if($ret==false || $ret['errno']!=0 || !is_array($ret['users'])) {
            Bingo_Log::warning('call difang queryUserList fail ret[' . serialize($ret) . ']');
        } else if(!empty($ret['users'])) {
            //在线用户user_id可能重复
            foreach($ret['users'] as $user) {
                $this->_arrOlUser[$user['user_id']] = intval($user['user_id']);
            }
        }
        
        //如果在线用户少于30个，则取组成员列表
        if(count($this->_arrOlUser) < 30) {
            $arrRequest = array(
                'group_id' => $this->intGid,
            );
            $ret = Tieba_Service::call('difang', 'getUidsByGid', $arrRequest);
            if($ret==false || $ret['errno']!=0) {
                Bingo_Log::warning('call difang getUidsByGid fail ret[' . serialize($ret) . ']');
            } else if(!empty($ret['ids'])) {
                //对用户去重
                foreach($ret['ids'] as $uid) {
                    if(!isset($this->_arrOlUser[$uid])) {
                        $this->_arrMember[$uid] = $uid;
                    }
                }
                $this->_arrMember = array_slice($this->_arrMember, 0, 30-count($this->_arrOlUser), true);
            }
        }

        //获取消息列表
        $arrInput = array (
            'gid' => $this->intGid,
            'from_mid' => $this->intFromMid,
            'to_mid' => $this->intToMid,
            'around' => $this->intAround,
            'back' =>$this->intBack,
            'uid' => CapiRequest::$intUid,
            'bolDialog' => $this->bolDialog,
        );
        $arrRet = Tieba_Service::call('difang', 'getMsg', $arrInput);
        if($arrRet==false || $arrRet['errno']!=0) {
            $this->_error($arrRet['errno']);
            return false;
        }
        $this->arrData = $arrRet;

        $intGroupClass = $this->arrData['group']['group_class'];
        //如果是直播群则取直播数据
        if($intGroupClass === 5) {
            $this->_getLiveData($this->arrData['group']['author']['id'], $this->arrData['group']['author']['name']);
        }
        
        foreach($arrRet['msg_list'] as $msg) {
            $this->_arrUser[$msg['author']['id']] = $msg['author']['id'];
        }
        //非私聊时获取用户角色
        if(!$this->bolDialog && !empty($this->_arrUser)) {
            //获取组员吧角色
            $arrRequest = array(
                'forum_id' => $arrRet['forum']['id'],
                'user_ids' => $this->_arrUser,
            );
            $ret = Tieba_Service::call('difang', 'mgetForumUsersRole', $arrRequest);
            if($ret===false || $ret['errno']!==0) {
                Bingo_Log::warning('call difang mgetForumUsersRole fail ret[' . serialize($ret) . ']');
            } else if(!empty($ret['roles'])) {
                $this->_arrForumRoles = $ret['roles'];
            }
            
            //获取组员群角色
            $arrRequest = array(
                'group_id' => $this->intGid,
                'user_ids' => $this->_arrUser,
            );
            $ret = Tieba_Service::call('difang', 'mgetGroupUsersRole', $arrRequest);
            if($ret===false || $ret['errno']!==0) {
                Bingo_Log::warning('call difang mgetGroupUsersRole fail ret[' . serialize($ret) . ']');
            } else if(!empty($ret['roles'])) {
                $this->_arrGroupRoles = $ret['roles'];
            }
        }
        return true;
    }

    private function _getLiveData($intAnchorId, $strAnchorName) {
        //获取最新一条直播消息数据
        $this->_arrLiveData['msg'] = array(
            'type' => 1,
            'content' => json_encode(array(
                array(
                    'type' => 0,
                    'text' => '我还没有开始直播哦，请先群里给我留言吧~',
                ),
            )),
            'author' => array(
                'id' => $intAnchorId,
                'name' => $strAnchorName,
                'portrait' => Tieba_Ucrypt::encode($intAnchorId, $strAnchorName),
            ),
        );
        $this->_arrLiveData['msg_count'] = 0;
        $this->_arrLiveData['img_count'] = 0;
        $this->_arrLiveData['voice_count'] = 0;
        $this->_arrLiveData['text_count'] = 0;
        $arrRequest = array(
            'gid' => $this->intGid,
        );
        $ret = Tieba_Service::call('difang', 'getLiveData', $arrRequest);
        if($ret===false || $ret['errno']!==0) {
            Bingo_Log::warning('call difang getLiveMsg fail ret[' . serialize($ret) . ']');
        } else {
            if(!empty($ret['msg'])) {
                $this->_arrLiveData['msg'] = $ret['msg'];
            }
            $this->_arrLiveData['msg_count'] = intval($ret['msg_count']);
            $this->_arrLiveData['text_count'] = intval($ret['text_count']);
            $this->_arrLiveData['img_count'] = intval($ret['img_count']);
            $this->_arrLiveData['voice_count'] = intval($ret['voice_count']);
        }

        //获取献花数
        $this->_arrLiveData['flower_count'] = 0;
        $arrRequest = array(
            'user_id' => $intAnchorId,
        );
        $ret = Tieba_Service::call('difang', 'getRewardCount', $arrRequest);
        if($ret===false || $ret['errno']!==0) {
            Bingo_Log::warning('call difang getRewardCount fail ret[' . serialize($ret) . ']');
        } else {
            $this->_arrLiveData['flower_count'] = intval($ret['flower_count']);
        }


        if($intAnchorId === CapiRequest::$intUid) {
            $this->_arrLiveData['is_anchor'] = 1;
        } else {
            $this->_arrLiveData['is_anchor'] = 0;
        }
        if(isset($this->_arrOlUser[$intAnchorId])) {
            $this->_arrLiveData['is_online'] = 1;
        } else {
            $this->_arrLiveData['is_online'] = 0;
        }
    }
    
    private function _process_user($arrUserIds, $isOnline=0) {
        if(empty($arrUserIds)) {
            return array();
        }
        
        $arrUserIds = array_merge($arrUserIds);            //重组key，否则请求user失败
        $ret = Tieba_Service::call('user', 'mgetUserData', array(
            'user_id' => $arrUserIds,
        ));
        if($ret==false || $ret['errno']!=0 || !is_array($ret['user_info'])) {
            Bingo_Log::warning('call user getUnameByUids fail ret[' . serialize($ret) . ']');
            return array();
        }
        
        $arrUsers = array();
        foreach($ret['user_info'] as $arrUser) {
            $intUid = intval($arrUser['user_id']);
            $strUname = strval($arrUser['user_name']);
            $strUname = Bingo_Encode::convert($strUname, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
            if($intUid > 0) {
                $arrUsers[] = array(
                    'id' => $intUid,
                    'name' => $strUname,
                    'sex' => intval($arrUser['user_sex']),
                    'portrait' => Tieba_Ucrypt::encode($intUid, $strUname),
                    'is_online' => $isOnline,
               );
            }
        }

        return $arrUsers;
    }

    public function _build() {
        //获取在线用户信息
        $this->_arrOlUser = array_slice($this->_arrOlUser, 0, 30, true);    //取最多30个
        $this->_arrOlUser = $this->_process_user($this->_arrOlUser, 1);
        $this->_arrMember = $this->_process_user($this->_arrMember, 0);
        $arrUsers = array_merge($this->_arrOlUser, $this->_arrMember);
        #妹子优先排到前面
        /*$intCount = count($arrUsers);
        if($intCount > 10) {
            for($i=0,$j=10; $i<5&&$j<$intCount;) {
                if($arrUsers[$i]['sex'] != 1) {
                    $i++;
                    continue;
                }
                if($arrUsers[$j]['sex'] == 1) {
                    $j++;
                    continue;
                }
                $tmp = $arrUsers[$i];
                $arrUsers[$i] = $arrUsers[$j];
                $arrUsers[$j] = $tmp;
                $i++;
                $j++;
            }
        }*/
        $this->arrData['user_list'] = $arrUsers;

        if(!$this->bolDialog) {
            $this->_arrUser = $this->_process_user($this->_arrUser, 0);
            $arrSex = array();
            foreach($this->_arrUser as $user) {
                $arrSex[$user['id']] = $user['sex'];
            }
            foreach($this->arrData['msg_list'] as &$msg) {
                $uid = $msg['author']['id'];
                if(isset($this->_arrForumRoles[$uid])) {
                    $msg['author']['permission'] = $this->_arrForumRoles[$uid];
                }
                if(isset($this->_arrGroupRoles[$uid])) {
                    if(isset($msg['author']['permission'])) {
                        $msg['author']['permission'] = array_merge($msg['author']['permission'], $this->_arrGroupRoles[$uid]);
                    } else {
                        $msg['author']['permission'] = $this->_arrGroupRoles[$uid];
                    }
                }
                if(isset($arrSex[$uid])) {
                    $msg['author']['sex'] = $arrSex[$uid];
                } else {
                    $msg['author']['sex'] = 2;
                }
            }
        }

        $this->arrData['live'] = $this->_arrLiveData;
        return true;
    }
}
