<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/
/**
 * @file getPicAction.php
 * <AUTHOR>
 * @date 2013/04/09 21:57:31
 * @version $Revision$ 
 * @brief 
 *  
 **/

class getPicAction extends Difang_Action_Base{
    protected $bolCheckMethodPost = false;
    protected $bolNeedUserLogin = false; //是否需要进行用户登录
    protected $bolCheckUserLogin = false;  //是否要检查用户是否登录
    protected $bolNeedCheckSign = false;  //是否需要检查加密串
    protected $bolDisplay = false; 
    protected $bolIsPv = 0; //是否是pv    

    public function _input() {
        return true;
    }
    protected function _process() {
        $intMode = Conf::get('img_proxy_type', 1);
        $strSrc = strval(Bingo_Http_Request::get('src', ''));
        $strPid = strval(Bingo_Http_Request::get('pid', ''));
        if(!empty($strPid)) {
            $strPrefix = Conf::get('pic_host', 'http://imgsrc.baidu.com/forum/pic/item/');
            #$strPrefix = Conf::get('pic_host', 'http://db-testing-rdtest00.vm.baidu.com:8090/tieba/pic/item/');
            $strSrc = $strPrefix . $strPid . '.jpg';
        }
        
        //no_profix=1指的是只穿了一个百度域的图片id
        $intSize = intval(Bingo_Http_Request::get('size',0)); 
        $intWidth = intval(Bingo_Http_Request::get('width',0));
        $intHeight = intval(Bingo_Http_Request::get('height',600)); 
        $intType = intval(Bingo_Http_Request::get('imgtype',1));
        $intQulity = intval(Bingo_Http_Request::get('qulity',0)); 
        $intGifSpecial = intval(Bingo_Http_Request::get('first_gif',0));
        if($intQulity < 10 || $intQulity > 100){
            $intQulity = intval(Conf::get('img_qulity',50));
        }
        $intRotate = Bingo_Http_Request::get('rotate',0);
        if ($intSize == 0){
            $intSize = 128;
        }   
        if ($intWidth == 0){
            $intWidth = $intSize; //为了兼容以前的接口
        }
        if (empty($strSrc)){
            $this->_error(CapiErrno::INTERNAL_ERROR,CapiError::INTERNAL_ERROR);
            return false;
        }                
        
        //质量80    
        //这里参数   $intGifSpecial在最后，是因为这是后加的参数，getTimg函数需要兼容其他调用
        if ($intMode == 1){
            $strOut = Image::getTimg($strSrc, $intWidth, $intHeight, $intQulity, $intRotate, $intType, -1,$intGifSpecial);
            Tieba_Stlog::addNode('size',strlen($strOut)); 
            if($strOut !== false){
                echo $strOut;
                exit;
            }else{
                $this->_error(CapiErrno::INTERNAL_ERROR,CapiError::INTERNAL_ERROR);      
                Bingo_Log::warning('imgAction get image error');
                return false;
            }
        }else{
            header('location:'.$strHost.$strTimgUrl);
            exit;
        }      
        return true;
    }
    public function _build() {
        return true;
    }
}


