<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2016/3/14
 * Time: 23:10
 */
require_once("common_functions.php");

class DumpTask extends OffLineTask {

    const PAGE_NUM = 3000;
    const PAGE_SIZE = 30;

    /**
     * @param $argc
     * @param $argv
     */
    public function run($argc, $argv)
    {
        if($argc < 3) {
            echoLine('php ' . basename(__FILE__) . ' activity_id dir_name');
            return;
        }

        $intActivityId = intval($argv[1]);
        if($intActivityId <= 0) {
            echoLine("activity_id '$intActivityId' invalid!");
            return;
        }
        $strDumpPath = $argv[2];
        if(!is_dir($strDumpPath)) {
            echoLine("path '$strDumpPath' not exists!");
            return;
        }

        for($intPageNo = 1; $intPageNo <= self::PAGE_NUM; ++$intPageNo) {
            $arrRet = $this->getNovelList($intActivityId, $intPageNo, self::PAGE_SIZE);
            if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
                echoLine("get novel list fail, pn = $intPageNo");
                return;
            }

            if(empty($arrRet['data']['list'])) {
                break;
            }

            for($k = 0; $k < count($arrRet['data']['list']); ++$k) {
                echo('['.(($intPageNo-1)*self::PAGE_SIZE+$k+1).'/'.$arrRet['data']['total_count'].']');
                echoLine($this->dumpNovel($strDumpPath, $arrRet['data']['list'][$k]) ? '[OK]' : '[FAIL]');
            }
        }
    }

    /**
     * @param $strDumpPath
     * @param $arrNovel
     * @return bool
     */
    private function dumpNovel($strDumpPath, $arrNovel)
    {
        $intNovelId = intval($arrNovel['novel_id']);
        $intThreadId = intval($arrNovel['thread_id']);

        $strDumpFile = $strDumpPath . "/$intNovelId.txt";

        echo($arrNovel['novel_name']);

        file_put_contents($strDumpFile, "-------------------- 作者信息 --------------------\n");
        file_put_contents($strDumpFile, "账号：{$arrNovel['author_name']}\n", FILE_APPEND);
        file_put_contents($strDumpFile, "姓名：{$arrNovel['author_info']['real_name']}\n", FILE_APPEND);
        file_put_contents($strDumpFile, "电话：{$arrNovel['author_info']['tel']}\n", FILE_APPEND);
        file_put_contents($strDumpFile, "QQ号：{$arrNovel['author_info']['qq']}\n", FILE_APPEND);
        file_put_contents($strDumpFile, "邮箱：{$arrNovel['author_info']['email']}\n\n", FILE_APPEND);

        file_put_contents($strDumpFile, "-------------------- 作品信息 --------------------\n", FILE_APPEND);
        file_put_contents($strDumpFile, "名称：{$arrNovel['novel_name']}\n", FILE_APPEND);
        file_put_contents($strDumpFile, "分类：{$arrNovel['category_name']}\n", FILE_APPEND);
        file_put_contents($strDumpFile, "介绍：{$arrNovel['intro']}\n", FILE_APPEND);
        file_put_contents($strDumpFile, "帖子：http://tieba".".baidu.com/p/$intThreadId?see_lz=1\n\n", FILE_APPEND);

        file_put_contents($strDumpFile, "-------------------- 作品内容 --------------------\n", FILE_APPEND);

        for($intPageNo = 1; $intPageNo <= self::PAGE_NUM; ++$intPageNo) {
            $arrRet  = $this->getPostList($intThreadId, $intPageNo, self::PAGE_SIZE);
            if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
                echoLine("get post list fail, pn = $intPageNo");
                return false;
            }

            if(empty($arrRet['data']['list'])) {
                break;
            }

            foreach($arrRet['data']['list'] as $strContent) {
                file_put_contents($strDumpFile, "$strContent\n", FILE_APPEND);
                echo('.');
            }
            usleep(100000);
        }

        return true;
    }

    /**
     * @param $intActivityId
     * @param $intPageNo
     * @param $intCount
     * @return array
     */
    private function getNovelList($intActivityId, $intPageNo, $intCount)
    {
        $arrParams = array(
            'activity_id' => $intActivityId,
            'status' => Service_Model_YcNovel::STATUS_AUDITED,
            'page_no' => $intPageNo,
            'page_size' => $intCount,
            'get_author_info' => 1,
        );

        return $this->callService('novel', 'getYuanchuangNovelList', $arrParams);
    }

    /**
     * @param $intThreadId
     * @param $intPageNo
     * @param $intCount
     * @return array
     */
    private function getPostList($intThreadId, $intPageNo, $intCount)
    {
        $arrParams = array(
            "thread_id" => $intThreadId,
            "offset" => ($intPageNo - 1) * $intCount,
            "res_num" => $intCount,
            "see_author" => 1,
            "has_comment" => 0,
            "has_mask" => 0,
            "has_ext" => 0,
            "need_set_pv" => 0,
            "structured_content" => 1,
        );

        $arrRet = $this->callService('post', 'getPostsByThreadId', $arrParams);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            return $arrRet;
        }

        $arrOutput = array(
            'total_count' => intval($arrRet['output']['output'][0]['total_post_num']),
            'count' => 0,
            'list' => array(),
        );
        if($arrOutput['total_count'] > ($intPageNo - 1) * $intCount
            && isset($arrRet['output']['output'][0]['post_infos'])) {

            $arrOutput['count'] = intval($arrRet['output']['output'][0]['list_num']);

            foreach($arrRet['output']['output'][0]['post_infos'] as $arrPostInfo) {
                $strContent = '';
                foreach($arrPostInfo['content'] as $arrContent) {
                    if('plainText' == $arrContent['tag']) {
                        $strContent .= $arrContent['value'] . "\n";
                    }
                }
                $arrOutput['list'][] = $strContent;
            }
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }
}

echoLine('begin dump ...');
Bingo_Timer::start('total');
$objTask = new DumpTask();
$objTask->run($argc, $argv);
Bingo_Timer::end('total');
echoLine('done! cost='.Bingo_Timer::calculate('total'));
