<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2015/7/15
 * Time: 19:17
 */
class getRandomNovelForumAction extends Util_Base {
    public function _init(){ //避免tbs校验失败后302
        $this->_tbs_check = 0;
    }

    public function _execute(){
        try {
            $arrInput = array();

            $res = $this->localCall("getRandomNovelForum", $arrInput);

            $this->_jsonRet($res['errno'], $res['errmsg'], $res['data']);
            return;

        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            $this->_jsonRet($e->getCode(), $e->getMessage() ,$e->getData());
        }
    }
}
