<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2015/10/23
 * Time: 14:33
 */
class Service_Libs_Wangju {

    /**
     * @biref 获得网剧吧视频列表
     * @param $forum_id
     * @param $page_no
     * @param $page_size
     * @return array
     */
    public static function getWangjuVideoList($forum_id, $page_no, $page_size)
    {
        $arrParam = array(
            'forum_id' => $forum_id,
            'page_no' => $page_no,
            'page_size' => $page_size,
        );

        Bingo_Timer::start(__METHOD__);
        $arrRet = Tieba_Service::call('novel', 'getWangjuVideoList', $arrParam, null, null, 'post', 'php', 'utf-8');
        Bingo_Timer::end(__METHOD__);

        if(false === $arrRet) {
            Bingo_Log::warning('call service getWangjuVideoList fail!');
            return Util_Function::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        if(Tieba_Errcode::ERR_SUCCESS  != $arrRet['errno']) {
            Bingo_Log::warning('call getWangjuVideoList fail, param='.serialize($arrParam).',ret='.serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['data']);
    }


}