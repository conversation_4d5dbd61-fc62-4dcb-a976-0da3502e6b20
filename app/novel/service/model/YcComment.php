<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2016/2/4
 * Time: 16:02
 */
class Service_Model_YcComment {

    const USER_TYPE = 0;
    const GUEST_TYPE = 1;

    const REDIS_KEY_PREFIX_READER_COMMENT = 'Yuanchuang_Reader_Comment_';
    const REDIS_KEY_PREFIX_NOVEL_COMMENT_LIST = 'Yuanchuang_Novel_Comment_List_';
    const REDIS_KEY_EXPIRE = 300;

    /**
     * @param $intType
     * @param $intNovelId
     * @param $intActivityId
     * @param $intThreadId
     * @param $intPostId
     * @param $intUserId
     * @param $strUserName
     * @param $intScore
     * @param $strComment
     * @return array
     */
    public static function addComment($intType, $intNovelId, $intActivityId, $intThreadId, $intPostId,
                                      $intUserId, $strUserName, $intScore, $strComment)
    {
        $now = time();

        $arrInput = array(
            'field' => array(
                'comment_type' => $intType,
                'novel_id' => $intNovelId,
                'activity_id' => $intActivityId,
                'thread_id' => $intThreadId,
                'post_id' => $intPostId,
                'reader_id' => $intUserId,
                'reader_name' => $strUserName,
                'score' => $intScore,
                'comment' => $strComment,
                'create_time' => $now,
                'update_time' => $now,
                'status' => 1,
            ),
        );

        $arrRet = Dl_Db_YcComment::insert($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComment::insert fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, array(
            'comment_id' => $arrRet['data'],
        ));
    }

    /**
     * @param $intId
     * @param bool $bolForUpdate
     * @return array
     */
    public static function getComment($intId, $bolForUpdate = false)
    {
        $arrInput = array(
            'field' => Dl_Db_YcComment::$_arrAllFields,
            'cond' => array(
                'comment_id' => $intId,
                'status' => 1,
            ),
        );

        if($bolForUpdate) {
            $arrInput['append'] = 'for update';
        }

        $arrRet = Dl_Db_YcComment::select($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComment::select fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        $arrComment = empty($arrRet['data']) ? array() : $arrRet['data'][0];

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrComment);
    }

    /**
     * @param $intType
     * @param $intReaderId
     * @param $intNovelId
     * @param bool $bolForUpdate
     * @return array
     */
    public static function getReaderComment($intType, $intReaderId, $intNovelId, $bolForUpdate = false)
    {
        if(!$bolForUpdate) {
            $arrRet = self::getReaderCommentFromBuffer($intType, $intReaderId, $intNovelId);
            if(false != $arrRet) {
                return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
            }
        }

        $arrInput = array(
            'field' => Dl_Db_YcComment::$_arrAllFields,
            'cond' => array(
                'comment_type' => $intType,
                'reader_id' => $intReaderId,
                'novel_id' => $intNovelId,
                'status' => 1,
            ),
        );

        if($bolForUpdate) {
            $arrInput['append'] = 'for update';
        }

        $arrRet = Dl_Db_YcComment::select($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComment::select fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        $arrComment = empty($arrRet['data']) ? array() : $arrRet['data'][0];
        if(!$bolForUpdate) {
            self::setReaderCommentToBuffer($intType, $intReaderId, $intNovelId, $arrComment);
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrComment);
    }

    /**
     * @param $intType
     * @param $intReaderId
     * @param $arrNovelIds
     * @return array
     */
    public static function mgetReaderComment($intType, $intReaderId, $arrNovelIds)
    {
        if(empty($arrNovelIds)) {
            return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, array());
        }

        $arrComments = self::mgetReaderCommentFromBuffer($intType, $intReaderId, $arrNovelIds);

        $arrMoreNovelIds = array();
        foreach($arrNovelIds as $intNovelId) {
            if(!isset($arrComments[intval($intNovelId)])) {
                $arrMoreNovelIds[] = $intNovelId;
            }
        }

        if(empty($arrMoreNovelIds)) {
            return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrComments);
        }

        $arrInput = array(
            'field' => Dl_Db_YcComment::$_arrAllFields,
            'cond' => array(
                'comment_type' => $intType,
                'reader_id' => $intReaderId,
                'novel_id' => array(
                    'opt' => 'in',
                    'val' => '(' . implode(',', $arrMoreNovelIds) . ')',
                    'quotes' => 0,
                ),
                'status' => 1,
            ),
        );

        $arrRet = Dl_Db_YcComment::select($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComment::select fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        if(!empty($arrRet['data'])) {
            self::msetReaderCommentToBuffer($intType, $intReaderId, $arrRet['data']);

            foreach($arrRet['data'] as $arrComment) {
                $arrComments[intval($arrComment['novel_id'])] = $arrComment;
            }
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrComments);
    }

    /**
     * @param $intType
     * @param $intNovelId
     * @param $intPageNo
     * @param $intCount
     * @return array
     */
    public static function getCommentListByNovel($intType, $intNovelId, $intPageNo, $intCount)
    {
        $arrRet = self::getNovelCommentListFromBuffer($intType, $intNovelId, $intPageNo, $intCount);
        if(false != $arrRet) {
            return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
        }

        $arrInput = array(
            'field' => array(
                'count(1) as total' ,
            ),
            'cond' => array(
                'status' => 1,
                'novel_id' => $intNovelId,
                'comment_type' => $intType,
            ),
        );

        $arrRet = Dl_Db_YcComment::select($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComment::select fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        $intTotal = $arrRet['data'][0]['total'];
        $intOffset = ($intPageNo-1)*$intCount;

        $arrInput = array(
            'field' => Dl_Db_YcComment::$_arrAllFields,
            'cond' => array(
                'status' => 1,
                'novel_id' => $intNovelId,
                'comment_type' => $intType,
            ),
            'append' => "order by update_time desc limit $intOffset,$intCount",
        );

        $arrRet = Dl_Db_YcComment::select($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComment::select fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        $arrOutput = array(
            'total_count' => $intTotal,
            'count' => count($arrRet['data']),
            'list' => $arrRet['data'],
        );

        self::setNovelCommentListToBuffer($intType, $intNovelId, $intPageNo, $intCount, $arrOutput);

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    /**
     * @param $intType
     * @param $intActivityId
     * @param $intPageNo
     * @param $intCount
     * @return array
     */
    public static function getCommentListByActivity($intType, $intActivityId, $intPageNo, $intCount)
    {
        $arrInput = array(
            'field' => array(
                'count(1) as total' ,
            ),
            'cond' => array(
                'status' => 1,
                'activity_id' => $intActivityId,
                'comment_type' => $intType,
            ),
        );

        $arrRet = Dl_Db_YcComment::select($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComment::select fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        $intTotal = $arrRet['data'][0]['total'];
        $intOffset = ($intPageNo-1)*$intCount;

        $arrInput = array(
            'field' => Dl_Db_YcComment::$_arrAllFields,
            'cond' => array(
                'status' => 1,
                'activity_id' => $intActivityId,
                'comment_type' => $intType,
            ),
            'append' => "order by update_time desc limit $intOffset,$intCount",
        );

        $arrRet = Dl_Db_YcComment::select($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComment::select fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        $arrOutput = array(
            'total_count' => $intTotal,
            'count' => count($arrRet['data']),
            'list' => $arrRet['data'],
        );

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    /**
     * @param $intType
     * @param $intReaderId
     * @param $intPageNo
     * @param $intCount
     * @param int $intActivityId
     * @return array
     */
    public static function getCommentListByReader($intType, $intReaderId, $intPageNo, $intCount, $intActivityId = 0)
    {
        $arrInput = array(
            'field' => array(
                'count(1) as total' ,
            ),
            'cond' => array(
                'status' => 1,
                'reader_id' => $intReaderId,
                'comment_type' => $intType,
            ),
        );
        if($intActivityId > 0) {
            $arrInput['cond']['activity_id'] = $intActivityId;
        }

        $arrRet = Dl_Db_YcComment::select($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComment::select fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        $intTotal = $arrRet['data'][0]['total'];
        $intOffset = ($intPageNo-1)*$intCount;

        $arrInput = array(
            'field' => Dl_Db_YcComment::$_arrAllFields,
            'cond' => array(
                'status' => 1,
                'reader_id' => $intReaderId,
                'comment_type' => $intType,
            ),
            'append' => "limit $intOffset,$intCount",
        );
        if($intActivityId > 0) {
            $arrInput['cond']['activity_id'] = $intActivityId;
        }

        $arrRet = Dl_Db_YcComment::select($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComment::select fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        $arrOutput = array(
            'total_count' => $intTotal,
            'count' => count($arrRet['data']),
            'list' => $arrRet['data'],
        );

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    /**
     * @param $intId
     * @param $intType
     * @param $intReaderId
     * @param $intNovelId
     * @param $strComment
     * @return array
     */
    public static function updateComment($intId, $intType, $intReaderId, $intNovelId, $strComment)
    {
        $now = time();

        $arrInput = array(
            'field' => array(
                'comment' => $strComment,
                'update_time' => $now,
            ),
            'cond' => array(
                'comment_id' => $intId,
            ),
        );

        $arrRet = Dl_Db_YcComment::update($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComment::update fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        self::delReaderCommentFromBuffer($intType, $intReaderId, $intNovelId);
        self::delNovelCommentListFromBuffer($intType, $intNovelId);

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, array(
            'comment_id' => $intId,
        ));
    }

    /**
     * @param $intId
     * @param $intType
     * @param $intReaderId
     * @param $intNovelId
     * @return array
     */
    public static function delComment($intId, $intType, $intReaderId, $intNovelId)
    {
        $now = time();

        $arrInput = array(
            'field' => array(
                'status' => 0,
                'update_time' => $now,
            ),
            'cond' => array(
                'comment_id' => $intId,
            ),
        );

        $arrRet = Dl_Db_YcComment::update($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComment::update fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        self::delReaderCommentFromBuffer($intType, $intReaderId, $intNovelId);
        self::delNovelCommentListFromBuffer($intType, $intNovelId);

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $intType
     * @param $intReaderId
     * @param $intNovelId
     * @param $arrComment
     * @return bool
     */
    protected static function setReaderCommentToBuffer($intType, $intReaderId, $intNovelId, $arrComment)
    {
        $key = self::REDIS_KEY_PREFIX_READER_COMMENT . $intType . '_' . $intReaderId;

        $arrParam = array(
            'key' => $key,
            'field' => $intNovelId,
            'value' => $arrComment,
            'expire' => self::REDIS_KEY_EXPIRE,
        );

        return Service_Libs_Redis::hsetToRedis($arrParam);
    }

    /**
     * @param $intType
     * @param $intReaderId
     * @param $intNovelId
     * @return bool|mixed|null
     */
    protected static function getReaderCommentFromBuffer($intType, $intReaderId, $intNovelId)
    {
        $key = self::REDIS_KEY_PREFIX_READER_COMMENT . $intType . '_' . $intReaderId;

        $arrParam = array(
            'key' => $key,
            'field' => $intNovelId,
        );

        $arrRet = Service_Libs_Redis::hgetFromRedis($arrParam);
        if(false === $arrRet || null === $arrRet) {
            return false;
        }

        return $arrRet;
    }

    /**
     * @param $intType
     * @param $intReaderId
     * @param $arrComments
     * @return bool
     */
    private static function msetReaderCommentToBuffer($intType, $intReaderId, $arrComments)
    {
        $key = self::REDIS_KEY_PREFIX_READER_COMMENT . $intType . '_' . $intReaderId;

        $arrParam = array(
            'key' => $key,
            'fields' => array(),
            'expire' => self::REDIS_KEY_EXPIRE,
        );

        foreach($arrComments as $arrComment) {
            $arrParam['fields'][$arrComment['novel_id']] = $arrComment;
        }

        return Service_Libs_Redis::hmsetToRedis($arrParam);
    }

    /**
     * @param $intType
     * @param $intReaderId
     * @param $arrNovelIds
     * @return array|mixed|null
     */
    private static function mgetReaderCommentFromBuffer($intType, $intReaderId, $arrNovelIds)
    {
        $key = self::REDIS_KEY_PREFIX_READER_COMMENT . $intType . '_' . $intReaderId;

        $arrParam = array(
            'key' => $key,
            'fields' => $arrNovelIds,
        );

        $arrRet = Service_Libs_Redis::hmgetFromRedis($arrParam);
        if(false === $arrRet) {
            return array();
        }

        return $arrRet;
    }

    /**
     * @param $intType
     * @param $intReaderId
     * @param $intNovelId
     */
    protected static function delReaderCommentFromBuffer($intType, $intReaderId, $intNovelId)
    {
        $key = self::REDIS_KEY_PREFIX_READER_COMMENT . $intType . '_' . $intReaderId;

        $arrParam = array(
            'key' => $key,
            'field' => $intNovelId,
        );

        Service_Libs_Redis::hclearFromRedis($arrParam);
    }

    /**
     * @param $intType
     * @param $intNovelId
     * @param $intPageNo
     * @param $intCount
     * @param array $arrList
     * @return bool
     */
    protected static function setNovelCommentListToBuffer($intType, $intNovelId, $intPageNo, $intCount, array $arrList)
    {
        $arrInput = array(
            'key' => self::REDIS_KEY_PREFIX_NOVEL_COMMENT_LIST . "{$intType}_{$intNovelId}",
            'field' => "{$intPageNo}_{$intCount}",
            'value' => $arrList,
            'expire' => self::REDIS_KEY_EXPIRE,
        );

        return Service_Libs_Redis::hsetToRedis($arrInput);
    }

    /**
     * @param $intType
     * @param $intNovelId
     * @param $intPageNo
     * @param $intCount
     * @return array
     */
    protected static function getNovelCommentListFromBuffer($intType, $intNovelId, $intPageNo, $intCount)
    {
        $arrInput = array(
            'key' => self::REDIS_KEY_PREFIX_NOVEL_COMMENT_LIST . "{$intType}_{$intNovelId}",
            'field' => "{$intPageNo}_{$intCount}",
        );

        $arrRet = Service_Libs_Redis::hgetFromRedis($arrInput);
        if($arrRet === false || $arrRet === null) {
            return false;
        }

        return $arrRet;
    }

    /**
     * @param $intType
     * @param $intNovelId
     * @return bool
     */
    protected static function delNovelCommentListFromBuffer($intType, $intNovelId)
    {
        $arrInput = array(
            'key' => self::REDIS_KEY_PREFIX_NOVEL_COMMENT_LIST . "{$intType}_{$intNovelId}",
        );

        return Service_Libs_Redis::clearFromRedis($arrInput);
    }
}
