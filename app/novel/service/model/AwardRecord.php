<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2015/8/20
 * Time: 14:22
 */
class Service_Model_AwardRecord {

    const ORDER_EXPIRE_TIME = 1800;

    const ORDER_TYPE_DRAW_AWARD = 0;

    const ORDER_STATUS_INIT = 0;
    const ORDER_STATUS_FINISHED = 1;
    const ORDER_STATUS_CANCELED = 2;
    const ORDER_STATUS_DELIVERED = 3;

    const KEY_NOVEL_AWARD_RECORD = 'Novel_AwardRecord_';
    const CACHE_EXPIRE_TIME = 120;

    const KEY_LATEST_AWARD_RECORD_LIST = 'LatestAwardRecordList_';
    const LATEST_RECORD_LEN = 10;

    /**
     * @param $order_id
     * @param $type
     * @param $forum_id
     * @param $thread_id
     * @param $reader_id
     * @param $props_id
     * @param $props_name
     * @param $props_num
     * @param $tdou_cost
     * @param $tdou_left
     * @param $ext1
     * @param $ext2
     * @return array
     */
    public static function addAwardRecord($order_id, $type, $forum_id, $thread_id, $reader_id,
                                          $props_id, $props_name, $props_num, $tdou_cost, $tdou_left,
                                          $ext1 = null, $ext2 = null)
    {
        $timestamp = time();

        $arrInput = array(
            'field' => array(
                'order_id' => $order_id,
                'type' => $type,
                'forum_id' => $forum_id,
                'thread_id' => $thread_id,
                'reader_id' => $reader_id,
                'props_id' => $props_id,
                'props_name' => $props_name,
                'props_num' => $props_num,
                'tdou_cost' => $tdou_cost,
                'tdou_left' => $tdou_left,
                'create_time' => $timestamp,
                'update_time' => $timestamp,
                'end_time' => $timestamp + self::ORDER_EXPIRE_TIME,
                'status' => self::ORDER_STATUS_INIT,
            ),
        );

        if(!is_null($ext1)) {
            $arrInput['field']['ext1'] = intval($ext1);
        }

        if(!is_null($ext2)) {
            $arrInput['field']['ext2'] = strval($ext1);
        }

        $arrRet = Dl_Db_AwardRecord::insert($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_AwardRecord::insert fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        self::addRecordToLatestList($forum_id, $thread_id, array(
            'reader_id' => $reader_id,
            'props_id' => $props_id,
            'props_num' => $props_num,
            'timestamp' => $timestamp,
        ));

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);

    }

    /**
     * @param $order_id
     * @param bool $for_update
     * @return array
     */
    public static function getAwardRecord($order_id,$for_update = false)
    {
        $key = self::KEY_NOVEL_AWARD_RECORD . $order_id;
        if(!$for_update) {
            $strRet = Service_Libs_Cache::get($key);
            if ($strRet != null && ($arrRecord = unserialize($strRet)) != false) {
                return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRecord);
            }
        }

        $arrInput = array(
            'field' => Dl_Db_AwardRecord::$_arrAllFields,
            'cond' => array(
                'order_id' => $order_id,
            ),
        );

        if($for_update) {
            $arrInput['append'] = 'for update';
        }

        $arrRet = Dl_Db_AwardRecord::select($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_AwardRecord::select fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        if(!$for_update) {
            Service_Libs_Cache::add($key, serialize($arrRet['data']), self::CACHE_EXPIRE_TIME);
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['data']);
    }

    /**
     * @param $order_id
     * @param $status
     * @return array
     */
    public static function setAwardRecordStatus($order_id, $status)
    {
        $timestamp = time();

        $arrInput = array(
            'field' => array(
                'status' => $status,
                'update_time'=> $timestamp,
            ),
            'cond' => array(
                'order_id' => $order_id,
            ),
        );

        $arrRet = Dl_Db_AwardRecord::update($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_AwardRecord::update fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        $key = self::KEY_NOVEL_AWARD_RECORD . $order_id;
        Service_Libs_Cache::del($key);

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * @param $forum_id
     * @param $thread_id
     * @param $arrRecord
     * @return array
     */
    public static function addRecordToLatestList($forum_id, $thread_id, $arrRecord)
    {
        $redis = Service_Libs_Redis::_getRedis();
        if(is_null($redis)) {
            return Util_Function::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $key = Service_Libs_Redis::COMMON_KEY_PREFIX . self::KEY_LATEST_AWARD_RECORD_LIST . $forum_id . '_' . $thread_id;

        $arrParam = array(
            'key' => $key,
            'value' => serialize($arrRecord),
        );

        $ret = $redis->RPUSH($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('RPUSH fail, param='.serialize($arrParam).',ret='.serialize($ret));
            return Util_Function::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $len = intval($ret['ret'][$key]);
        if($len <= self::LATEST_RECORD_LEN) {
            return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        $arrParam = array(
            'key' => $key,
            'start' => 1,
            'stop' => -1,
        );

        $ret = $redis->LTRIM($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('LTRIM fail, param='.serialize($arrParam).',ret='.serialize($ret));
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $forum_id
     * @param $thread_id
     * @return array
     */
    public static function getLatestRecordList($forum_id, $thread_id)
    {
        $redis = Service_Libs_Redis::_getRedis();
        if(is_null($redis)) {
            return Util_Function::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $key = Service_Libs_Redis::COMMON_KEY_PREFIX . self::KEY_LATEST_AWARD_RECORD_LIST . $forum_id . '_' . $thread_id;

        $arrParam = array(
            'key' => $key,
            'start' => 0,
            'stop' => -1,
        );

        $ret = $redis->LRANGE($arrParam);
        if ($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('LRANGE fail, params=' . serialize($arrParam) . ',ret=' . serialize($ret));
            return Util_Function::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $arrRecords = array();
        if(!is_null($ret['ret'][$key])) {
            foreach ($ret['ret'][$key] as $arrRecord) {
                $arrRecords[] = unserialize($arrRecord);
            }
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRecords);
    }
}

