<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2016/2/4
 * Time: 17:14
 */
class Service_Model_YcComplaint
{
    const STATUS_DELETED = 0;
    const STATUS_UNAUDITED = 1;
    const STATUS_AUDITED = 2;

    /**
     * @param $intNovelId
     * @param $intActivityId
     * @param $intThreadId
     * @param $intUserId
     * @param $strUserName
     * @param $strComplaint
     * @return array
     */
    public static function addComplaint($intNovelId, $intActivityId, $intThreadId, $intUserId, $strUserName, $strComplaint)
    {
        $now = time();

        $arrInput = array(
            'field' => array(
                'novel_id' => $intNovelId,
                'activity_id' => $intActivityId,
                'thread_id' => $intThreadId,
                'reader_id' => $intUserId,
                'reader_name' => $strUserName,
                'complaint' => $strComplaint,
                'create_time' => $now,
                'update_time' => $now,
                'status' => self::STATUS_UNAUDITED,
            ),
        );

        $arrRet = Dl_Db_YcComplaint::insert($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComplaint::insert fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, array(
            'complaint_id' => $arrRet['data'],
        ));
    }

    /**
     * @param $intId
     * @param bool $bolForUpdate
     * @return array
     */
    public static function getComplaint($intId, $bolForUpdate = false)
    {
        $arrInput = array(
            'field' => Dl_Db_YcComplaint::$_arrAllFields,
            'cond' => array(
                'complaint_id' => $intId,
            ),
        );

        if($bolForUpdate) {
            $arrInput['append'] = 'for update';
        }

        $arrRet = Dl_Db_YcComplaint::select($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComplaint::select fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        $arrComplaint = empty($arrRet['data']) ? array() : $arrRet['data'][0];

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrComplaint);
    }

    /**
     * @param $intNovelId
     * @param $intReaderId
     * @return array
     */
    public static function getComplaintByNovelReader($intNovelId, $intReaderId)
    {
        $arrInput = array(
            'field' => Dl_Db_YcComplaint::$_arrAllFields,
            'cond' => array(
                'novel_id' => $intNovelId,
                'reader_id' => $intReaderId,
                'status' => array(
                    'opt' => '>',
                    'val' => 0,
                    'quotes' => 0,
                ),
            ),
        );

        $arrRet = Dl_Db_YcComplaint::select($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComplaint::select fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        $arrComplaint = empty($arrRet['data']) ? array() : $arrRet['data'][0];

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrComplaint);
    }

    /**
     * @param $intActivityId
     * @param $intStatus
     * @param $intPageNo
     * @param $intCount
     * @return array
     */
    public static function getComplaintListByActivity($intActivityId, $intStatus, $intPageNo, $intCount)
    {
        $arrInput = array(
            'field' => array(
                'count(1) as total' ,
            ),
            'cond' => array(
                'status' => $intStatus,
                'activity_id' => $intActivityId,
            ),
        );

        $arrRet = Dl_Db_YcComplaint::select($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComplaint::select fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        $intTotal = $arrRet['data'][0]['total'];
        $intOffset = ($intPageNo-1)*$intCount;

        $arrInput = array(
            'field' => Dl_Db_YcComplaint::$_arrAllFields,
            'cond' => array(
                'status' => $intStatus,
                'activity_id' => $intActivityId,
            ),
            'append' => "limit $intOffset,$intCount",
        );

        $arrRet = Dl_Db_YcComplaint::select($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComplaint::select fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        $arrOutput = array(
            'total_count' => $intTotal,
            'count' => count($arrRet['data']),
            'list' => $arrRet['data'],
        );

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    /**
     * @param $intNovelId
     * @param $intStatus
     * @param $intPageNo
     * @param $intCount
     * @return array
     */
    public static function getComplaintListByNovel($intNovelId, $intStatus, $intPageNo, $intCount)
    {
        $arrInput = array(
            'field' => array(
                'count(1) as total' ,
            ),
            'cond' => array(
                'status' => $intStatus,
                'novel_id' => $intNovelId,
            ),
        );

        $arrRet = Dl_Db_YcComplaint::select($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComplaint::select fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        $intTotal = $arrRet['data'][0]['total'];
        $intOffset = ($intPageNo-1)*$intCount;

        $arrInput = array(
            'field' => Dl_Db_YcComplaint::$_arrAllFields,
            'cond' => array(
                'status' => $intStatus,
                'novel_id' => $intNovelId,
            ),
            'append' => "limit $intOffset,$intCount",
        );

        $arrRet = Dl_Db_YcComplaint::select($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComplaint::select fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        $arrOutput = array(
            'total_count' => $intTotal,
            'count' => count($arrRet['data']),
            'list' => $arrRet['data'],
        );

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    /**
     * @param $intId
     * @param $intStatus
     * @return array
     */
    public static function updateComplaintStatus($intId, $intStatus)
    {
        $now = time();

        $arrInput = array(
            'field' => array(
                'status' => $intStatus,
                'update_time' => $now,
            ),
            'cond' => array(
                'complaint_id' => $intId,
            ),
        );

        $arrRet = Dl_Db_YcComplaint::update($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call Dl_Db_YcComplaint::update fail, arrInput='.serialize($arrInput)
                . ',arrRet=' . serialize($arrRet));
            return Util_Function::errRet($arrRet['errno']);
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
    }
}
