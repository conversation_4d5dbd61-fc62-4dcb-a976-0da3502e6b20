<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2017/4/13
 * Time: 16:07
 */

class getIntervalOfScriptAction extends Libs_Client_BaseAction {

    /**
     * @brief _getPrivateInfo
     * @return array
     */
    public function _getPrivateInfo()
    {
        $this->_objRequest->addStrategy('check_sign', false);
        return array(
            'check_login' => false,
            'ispv'        => 0,
        );
    }

    /**
     * @brief _checkPrivate
     * @return bool
     */
    public function _checkPrivate()
    {
        return true;
    }

    /**
     * @brief _execute
     */
    public function _execute(){
        $arrParam = array();
        $arrRet = Tieba_Service::call('salamatch', 'getScriptExecutionStamp', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call salamatch getRemainTime fail. [output = %s]", serialize($arrRet)));
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return;
        }
        $arrOutput = array(
            'match_script'  => time() - intval($arrRet['data']['match_execution_time']),
            'signal_script' => time() - intval($arrRet['data']['signal_execution_time']),
        );

        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrOutput);
    }
}
