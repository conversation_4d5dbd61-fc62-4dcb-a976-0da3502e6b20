<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2017/3/13
 * Time: 13:53
 */
class turnAction extends Libs_Client_BaseAction {

    /**
     * @brief _getPrivateInfo
     * @return array
     */
    public function _getPrivateInfo()
    {
        $this->_objRequest->addStrategy('check_sign', false);

        $arrPrivateInfo['ispv'] = 1;
        $arrPrivateInfo['check_login'] = false;
        $arrPrivateInfo['need_login'] = true;
        return $arrPrivateInfo;
    }

    /**
     * @brief _checkPrivate
     * @return bool
     */
    public function _checkPrivate()
    {
        return true;
    }

    /**
     * @brief _execute
     */
    public function _execute(){

        if(!$this->_objRequest->getCommonAttr('login', false)) {
            $client_type = $this->_objRequest->getCommonAttr('client_type', '');
            $cuid = $this->_objRequest->getCommonAttr('cuid', '');
            Bingo_Log::warning("not login, client_type=$client_type, cuid=$cuid");
            $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN);
            return;
        }

        $intUserId = $this->_objRequest->getCommonAttr('user_id', 0);

        $arrParams = [
            'user_id' => $intUserId,
        ];
        $arrRet = Libs_Rpc::callService('sala', 'getTurnConfig', $arrParams, 'post', 'php', 'utf-8', 'local');

        $this->_jsonRet($arrRet['errno'], $arrRet['errmsg'], $arrRet['data']);
    }
}
