<?php
/**
 * Desc: connect fail
 * User: <EMAIL>
 * Date: 16/03/2017
 * Time: 17:50
 */

class connectFailAction extends Libs_Im_Base {

    // 定义参数
    protected $intFrom    = 0;
    protected $intTo      = 0;
    protected $strType    = "";
    protected $strContent = "";
    protected $strSala    = "";

    protected $arrTypes = array(
        'signal' => true,
        'common' => true,
    );

    /**
     * @brief  execute
     * @return boolean
     */
    public function execute()
    {
        // 获取参数
        $this->_getInput();

        // 校验参数
        if ($this->intFrom <= 0) {
            Bingo_Log::warning(sprintf("param error, from param is not illegal. [from = %d]", $this->intFrom));
            $this->_retOutput(array(), Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        if (empty($this->strContent) || !isset($this->arrTypes[$this->strType])) {
            Bingo_Log::warning(sprintf("param error, type is not illegal. [type = %s]", $this->strType));
            $this->_retOutput(array(), Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        $arrContent = json_decode($this->strContent, true);
        if (empty($arrContent) || !isset($arrContent['initiator']) || !isset($arrContent['receiver']) || !isset($arrContent['match_time'])) {
            Bingo_Log::warning(sprintf("param error, content is not illegal. [type = %s]", $this->strContent));
            $this->_retOutput(array(), Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }

        $arrParam = array(
            'initiator'  => intval($arrContent['initiator']),
            'receiver'   => intval($arrContent['receiver']),
            'match_time' => intval($arrContent['match_time']),
        );
        $arrRet = Tieba_Service::call('salamatch', 'uploadConnectFail', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning("upload connect fail failed! input : " . serialize($arrParam) . "output : " . serialize($arrRet));
            $this->_retOutput(array(), Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return false;
        }

        $arrResData = array(
            'pushInfo' 	=> array(
            ),
        );
        $this->_retOutput($arrResData, $arrRet['errno']);
        return true;
    }

    /**
     * @brief 获取输入
     */
    protected function _getInput()
    {
        $this->intFrom    = intval($this->arrBody['from']);
        $this->intTo      = intval($this->arrBody['to']);
        $this->strType    = strval($this->arrBody['type']);
        $this->strContent = strval($this->arrBody['content']);
        $this->strSala    = strval($this->arrBody['sala']);
    }
}
