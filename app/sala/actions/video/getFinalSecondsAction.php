<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2017/4/13
 * Time: 10:09
 */

class getFinalSecondsAction extends Libs_Client_BaseAction {

    const WORD_LIST_NAME_SALA_MATCH = 'tb_wordlist_redis_Sala_Match_White_List';

    /**
     * @brief _getPrivateInfo
     * @return array
     */
    public function _getPrivateInfo()
    {
        $this->_objRequest->addStrategy('check_sign', false);
        return array(
            'check_login' => true,
            'need_login'  => true,
            'ispv'        => 0,
        );
    }

    /**
     * @brief _checkPrivate
     * @return bool
     */
    public function _checkPrivate()
    {
        return true;
    }

    /**
     * @brief _execute
     */
    public function _execute(){

        $handleWordServer = Wordserver_Wordlist::factory();
        $intUserId = intval($this->_objRequest->getCommonAttr('user_id'));

        // 词表里取白名单用户
        $arrKeys = array(
            strval($intUserId)
        );
        $arrItemInfo = $handleWordServer->getValueByKeys($arrKeys, self::WORD_LIST_NAME_SALA_MATCH);
        if (isset($arrItemInfo[strval($intUserId)])) {
            $arrRet['data'] = array(
                'time_left' => 0,
                'title'     => "每晚20:00-23:00开始聊天",
                'new_title' =>  "每天11:00-14:00和20:00-23:00\n可以视频聊天~",
            );
        }
        else { // 不命中词表继续查
            $arrParam = array();
            $arrRet = Tieba_Service::call('salamatch', 'getRemainTime', $arrParam, null, null, 'post', 'php', 'utf-8');
            if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
                Bingo_Log::warning(sprintf("call salamatch getRemainTime fail. [output = %s]", serialize($arrRet)));
                $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                return;
            }
        }
        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrRet['data']);
    }
}
