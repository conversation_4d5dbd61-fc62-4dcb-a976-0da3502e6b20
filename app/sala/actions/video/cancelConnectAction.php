<?php
/**
 * Created by PhpStorm.
 * User: echo
 * Date: 16/03/2017
 * Time: 17:48
 */

class cancelConnectAction extends Libs_Im_Base {
    // 定义参数
    protected $intFrom    = 0;
    protected $intTo      = 0;
    protected $strType    = "";
    protected $strContent = "";
    protected $strSala    = "";

    protected $arrTypes = array(
        'signal' => true,
        'common' => true,
    );

    /**
     * @brief  execute
     * @return boolean
     */
    public function execute()
    {
        // 获取参数
        $this->_getInput();
        $this->intFrom = intval($this->_arrUserInfo['user_id']);
        // 校验参数
        if ($this->intFrom <= 0 || $this->intTo <= 0) {
            Bingo_Log::warning(sprintf("param error, from param is not illegal. [from = %d]", $this->intFrom));
            $this->_retOutput(array(), Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        if (!empty($this->strType) && $this->strType !== 'signal') {
            Bingo_Log::warning('param error. type is not illegal.');
            $this->_retOutput(array(), Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }

        $arrParam = array(
            'initiator'  => intval($this->intFrom),
            'receiver'   => intval($this->intTo),
            'type'       => $this->strType,
        );
        $arrRet = Tieba_Service::call('salamatch', 'cancelConnect', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning("cancel connect failed! input : " . serialize($arrParam) . "output : " . serialize($arrRet));
            $this->_retOutput(array(), Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return false;
        }

        $arrResData = array(
            'pushInfo' 	=> array(
            ),
        );
        $this->_retOutput($arrResData, $arrRet['errno']);
        return true;
    }

    /**
     * @brief 获取输入
     */
    protected function _getInput()
    {
        $this->intFrom    = intval($this->arrBody['from']);
        $this->intTo      = intval($this->arrBody['to']);
        $this->strType    = strval($this->arrBody['type']);
        $this->strContent = strval($this->arrBody['content']);
        $this->strSala    = strval($this->arrBody['sala']);
    }
}