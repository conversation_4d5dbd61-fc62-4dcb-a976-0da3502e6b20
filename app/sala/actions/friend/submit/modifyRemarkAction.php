<?php
/**
 * Created by PhpStorm.
 * User: ding<PERSON>hu
 * Date: 17/3/19
 * Time: 下午1:22
 */

class modifyRemarkAction extends Libs_Client_BaseAction {

    protected $_arrOutput = [];

    protected $_arrServices = [];
    protected $_arrServiceResults = [];
    
    private static $_errMsg = array(
        '2140005' => '好友数已达最大限制',
        '2360002' => '对方已是您的好友',
        '3160009' => '对方不是好友',
    );

    /**
     * @brief _getPrivateInfo
     * @return array
     */
    public function _getPrivateInfo()
    {
        $this->_objRequest->addStrategy('check_sign', false);
        $arrPrivateInfo['ispv'] = 1;
        $arrPrivateInfo['check_login'] = true;
        $arrPrivateInfo['need_login'] = true;
        $arrPrivateInfo['friend_uid'] = $this->_getInput('friend_uid', 0);
        $arrPrivateInfo['remark'] = $this->_getInput('remark', '');
        return $arrPrivateInfo;

    }


    /**
     * @brief _checkPrivate
     * @return bool
     */
    public function _checkPrivate()
    {
        return true;
    }


    /**
     * @brief _execute
     * @return json
     */
    public function _execute(){
        $user_id = intval($this->_objRequest->getCommonAttr('user_id'));
        $friend_uid = intval($this->_objRequest->getPrivateAttr('friend_uid'));
        $remark = strval($this->_objRequest->getPrivateAttr('remark'));

        if( $user_id <= 0 || $friend_uid <= 0 ){
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return;
        }

        $arrInput = [
            'user_id' => $user_id,
            'friend_uid' => $friend_uid,
            'remark' => $remark,
        ];

        $arrRet = Libs_Rpc::callService('salaim', 'modifyRemark', $arrInput, 'post', 'php', 'utf-8');
        $arrRet['errmsg'] = isset(self::$_errMsg[$arrRet['errno']]) ? self::$_errMsg[$arrRet['errno']] : $arrRet['errmsg'];
        
        return $this->_jsonRet($arrRet['errno'], $arrRet['errmsg']);
    }
    
}