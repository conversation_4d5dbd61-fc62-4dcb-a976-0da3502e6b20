<?php
/**
 * Created by PhpStorm.
 * User: ding<PERSON>hu
 * Date: 17/3/19
 * Time: 下午1:22
 */

class delFriendAction extends Libs_Client_BaseAction {

    protected $_arrOutput = [];

    protected $_arrServices = [];
    protected $_arrServiceResults = [];

    /**
     * @brief _getPrivateInfo
     * @return array
     */
    public function _getPrivateInfo()
    {
        $this->_objRequest->addStrategy('check_sign', false);
        $arrPrivateInfo['ispv'] = 1;
        $arrPrivateInfo['check_login'] = true;
        $arrPrivateInfo['need_login'] = true;
        $arrPrivateInfo['friend_uid'] = $this->_getInput('friend_uid', 0);
        return $arrPrivateInfo;

    }


    /**
     * @brief _checkPrivate
     * @return bool
     */
    public function _checkPrivate()
    {
        return true;
    }


    /**
     * @brief _execute
     * @return json
     */
    public function _execute(){
        $user_id = intval($this->_objRequest->getCommonAttr('user_id'));
        $friend_uid = intval($this->_objRequest->getPrivateAttr('friend_uid'));

        if( $user_id <= 0 || $friend_uid <= 0 ){
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return;
        }

        $arrInput = [
            'user_id' => $user_id,
            'friend_uid' => $friend_uid,
        ];

        $arrRet = Libs_Rpc::callService('salaim', 'delFriend', $arrInput, 'post', 'php', 'utf-8');

        return $this->_jsonRet($arrRet['errno'], $arrRet['errmsg']);
    }
    
}