<?php
/**
 * Created by PhpStorm.
 * User: ding<PERSON>hu
 * Date: 17/3/19
 * Time: 下午1:22
 */

class getMsgListAction extends Libs_Client_BaseAction {

    protected $_arrOutput = [];

    protected $_arrServices = [];
    protected $_arrServiceResults = [];

    /**
     * @brief _getPrivateInfo
     * @return array
     */
    public function _getPrivateInfo()
    {
        $this->_objRequest->addStrategy('check_sign', false);
        $arrPrivateInfo['ispv'] = 1;
        $arrPrivateInfo['check_login'] = true;
        $arrPrivateInfo['need_login'] = true;
        $arrPrivateInfo['friend_uid'] = $this->_getInput('friend_uid', 0);
        $arrPrivateInfo['msg_id'] = $this->_getInput('msg_id', 0);
        $arrPrivateInfo['rn'] = $this->_getInput('rn', 30);
        return $arrPrivateInfo;

    }


    /**
     * @brief _checkPrivate
     * @return bool
     */
    public function _checkPrivate()
    {
        return true;
    }


    /**
     * @brief _execute
     * @return json
     */
    public function _execute(){
        $user_id = intval($this->_objRequest->getCommonAttr('user_id'));
        $friend_uid = intval($this->_objRequest->getPrivateAttr('friend_uid'));
        $msg_id = intval($this->_objRequest->getPrivateAttr('msg_id'));
        $rn = intval($this->_objRequest->getPrivateAttr('rn'));

        if( $user_id <= 0 || $friend_uid <= 0 || $rn <= 0 || $msg_id < 0 ){
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return;
        }

        $arrInput = [
            'user_id' => $user_id,
            'friend_uid' => $friend_uid,
            'msg_id' => $msg_id,
            'rn' => $rn,
        ];

        $arrRet = Libs_Rpc::callService('salaim', 'getMsgList', $arrInput, 'post', 'php', 'utf-8');
        $data = isset($arrRet['data']) ? $arrRet['data'] : array();

        return $this->_jsonRet($arrRet['errno'], $arrRet['errmsg'], $data);
    }
    
}