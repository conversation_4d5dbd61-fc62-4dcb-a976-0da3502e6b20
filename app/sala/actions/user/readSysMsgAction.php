<?php
/**
 * Created by PhpStorm.
 * User: ligengyong
 * Date: 2017/3/28
 * Time: 14:27
 */


class readSysMsgAction extends Libs_Client_BaseAction {

    protected $_arrOutput = [];
    protected $_arrServices = [];
    protected $_arrServiceResults = [];

    /**
     * @brief _getPrivateInfo
     * @return array
     */
    public function _getPrivateInfo()
    {
        $this->_objRequest->addStrategy('check_sign', false);
        $arrPrivateInfo['ispv'] = 1;
        $arrPrivateInfo['check_login'] = true;
        $arrPrivateInfo['need_login'] = true;
        $arrPrivateInfo['sala_id'] = $this->_getInput('sala_id', 0);
        return $arrPrivateInfo;
    }


    /**
     * @brief _checkPrivate
     * @return bool
     */
    public function _checkPrivate()
    {
        return true;
    }

    /**
     * @brief _execute
     * @return json
     */
    public function _execute(){
        $sala_id = intval($this->_objRequest->getPrivateAttr('sala_id'));
        $user_id = intval($this->_objRequest->getCommonAttr('user_id'));
        if($user_id <= 0 ){
            return $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrInput = [
            'user_id' => $sala_id,
        ];
        return $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS);
    }
}
