<?php
/**
 * Created by PhpStorm.
 * User: wangjunsheng
 * Date: 17/3/19
 * Time: 下午1:22
 */

class settingAction extends Libs_Client_BaseAction {

    protected $_arrOutput = [];

    protected $_arrServices = [];
    protected $_arrServiceResults = [];

    /**
     * @brief _getPrivateInfo
     * @return array
     */
    public function _getPrivateInfo()
    {
        $this->_objRequest->addStrategy('check_sign', false);
        $arrPrivateInfo['ispv'] = 1;
        $arrPrivateInfo['check_login'] = true;
        $arrPrivateInfo['need_login'] = true;
        $arrPrivateInfo['notice_setting'] = $this->_getInput('notice_setting', '');
        return $arrPrivateInfo;

    }


    /**
     * @brief _checkPrivate
     * @return bool
     */
    public function _checkPrivate()
    {
        return true;
    }


    /**
     * @brief _execute
     * @return json
     */
    public function _execute(){

        $notice_setting = strval($this->_objRequest->getPrivateAttr('notice_setting'));
        $user_id = intval($this->_objRequest->getCommonAttr('user_id'));

        if($user_id <= 0){
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return;
        }

        $arrInput = [
            'user_id' => $user_id,
            'settings' => [
                'notice_setting' => $notice_setting,
            ],
        ];

        $arrRet = Libs_Rpc::callService('ntuser', 'salaUserSetting', $arrInput, 'post', 'php', 'utf-8');

        return $this->_jsonRet($arrRet['errno'], $arrRet['errmsg'],$arrRet['data']);
    }


}
