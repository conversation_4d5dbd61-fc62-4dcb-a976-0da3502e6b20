<?php
/**
 * Created by PhpStorm.
 * User: wangjunsheng
 * Date: 17/3/16
 * Time: 下午4:00
 */

class updateAction extends Libs_Client_BaseAction {

    protected $_arrOutput = [];

    protected $_arrServices = [];
    protected $_arrServiceResults = [];

    /**
     * @brief _getPrivateInfo
     * @return array
     */
    public function _getPrivateInfo()
    {
        $this->_objRequest->addStrategy('check_sign', false);
        $arrPrivateInfo['ispv'] = 1;
        $arrPrivateInfo['check_login'] = true;
        $arrPrivateInfo['need_login'] = true;
        $arrPrivateInfo['avatar'] = $this->_getInput('avatar', '');
        $arrPrivateInfo['birth'] = $this->_getInput('birth', '');
        $arrPrivateInfo['gender'] = $this->_getInput('gender', '');
        $arrPrivateInfo['nickname'] = $this->_getInput('nickname', '');



        return $arrPrivateInfo;

    }


    /**
     * @brief _checkPrivate
     * @return bool
     */
    public function _checkPrivate()
    {
        return true;
    }


    /**
     * @brief _execute
     * @return json
     */
    public function _execute(){

        $birth = intval($this->_objRequest->getPrivateAttr('birth'));
        $avatar = strval($this->_objRequest->getPrivateAttr('avatar'));
        $gender = strval($this->_objRequest->getPrivateAttr('gender'));
        $nickname = strval($this->_objRequest->getPrivateAttr('nickname'));
        $user_id = intval($this->_objRequest->getCommonAttr('user_id'));

        if($user_id <= 0 || (empty($birth) && empty($avatar) && empty($gender) && empty($nickname))){
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return;
        }

        if (!empty($nickname) && !$this->antiCheck('user_name',$nickname)){
            Bingo_Log::warning("antiCheck error");
            return $this->_jsonRet( Tieba_Errcode::ERR_UEG_CHECK_FAILED, '对不起，您提交的内容不符合相关法律法规！' );
        }

        $arrInput = [
            'user_id' => $user_id,
        ];

        if('' != $gender ){
            $arrInput['gender'] = intval($gender);
        }

        if(!empty($birth)){
            $arrInput['birth'] = $birth;
        }

        if(!empty($avatar)){
            $arrInput['avatar'] = $avatar;
        }

        if(!empty($nickname)){
            $arrInput['nickname'] = $nickname;
        }



        $arrRet = Libs_Rpc::callService('ntuser', 'updateUser', $arrInput, 'post', 'php', 'utf-8');

        return $this->_jsonRet($arrRet['errno'], $arrRet['errmsg'],$arrRet['data']);
    }


    /**
     * @param $param
     * @param $strParamValue
     * @return bool
     */
    protected function antiCheck($param,$strParamValue){
        if(!empty($strParamValue)){
            $req = array(
                'req' => array(
                    'check_type' => $param, // 'user_introduce','topic','answer','answer_to',
                    'content' => $strParamValue, // 检查内容
                ),
            );
            $arrRet = Tieba_Service::call('anti', 'antiWefanConfilterCheck', $req);

            if( false === $arrRet ) {
                Bingo_Log::warning('service anti.antiWefanConfilterCheck fail. [input='.serialize($req).'][out='.serialize($arrRet).']');
                return false;
            }

            if( Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                Bingo_Log::warning('service anti.antiWefanConfilterCheck error. [input='.serialize($req).'][out='.serialize($arrRet).']');
                return false;
            }

        }
        return true;
    }


}
