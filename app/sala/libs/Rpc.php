<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2016/7/19
 * Time: 18:26
 */
class Libs_Rpc {

    const DEFAULT_FETCH_ACCESS_TIMEOUT = 5000;
    const DEFAULT_FETCH_CONNECT_TIMECOUT = 10000;
    const DEFAULT_FETCH_DATA_MAX_LEN = 10000000;

    /**
     * @param $strServiceName
     * @param $strMethod
     * @param $arrInput
     * @param string $strHttpMethod
     * @param string $strFormat
     * @param string $strIe
     * @param string $strMode
     * @return array
     */
    public static function callService($strServiceName, $strMethod, $arrInput,
                                       $strHttpMethod = 'post', $strFormat = 'php',
                                       $strIe = 'utf-8', $strMode = 'remote')
    {
        $arrRet = Tieba_Service::call($strServiceName, $strMethod, $arrInput,
            null, null, $strHttpMethod, $strFormat, $strIe, $strMode);
        if(false == $arrRet) {
            Bingo_Log::warning("call service[$strServiceName::$strMethod] fail");
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return $arrRet;
    }

    /**
     * @param $strInstance
     * @param array $arrReqs
     * @return array
     */
    public static function mcallServices($strInstance, array $arrReqs)
    {
        $objRalMulti = new Tieba_Multi($strInstance);
        foreach($arrReqs as $k => $arrParams) {
            $arrMultiInput = array(
                'serviceName' => isset($arrParams['serviceName'])?$arrParams['serviceName']:'',
                'method' => isset($arrParams['method'])?$arrParams['method']:'',
                'input' => isset($arrParams['input'])?$arrParams['input']:array(),
                'httpMethod' => isset($arrParams['httpMethod'])?$arrParams['httpMethod']:'post',
                'ie' => isset($arrParams['ie'])?$arrParams['ie']:'utf-8',
            );
            $objRalMulti->register($k, new Tieba_Service($arrMultiInput['serviceName']), $arrMultiInput);
        }

        $objRalMulti->call();
        $arrOutput = array();
        foreach($arrReqs as $k => $arrParams) {
            $arrRet = $objRalMulti->getResult($k);
            if(false == $arrRet) {
                Bingo_Log::warning("call service[$k:{$arrParams['serviceName']}::{$arrParams['method']}] fail");
                $arrOutput[$k] = self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            } else {
                $arrOutput[$k] = $arrRet;
            }
        }
        return $arrOutput;
    }

    /**
     * @param $url
     * @param string $method
     * @param array $params
     * @param array $header
     * @param array $cookie
     * @param array $options
     * @return bool
     */
    public static function fetchUrl($url, $method = 'get', $params = array(),
                                    $header = array(), $cookie = array(), $options = array())
    {
        if(empty($options)) {
            $options = array(
                'timeout' => self::DEFAULT_FETCH_ACCESS_TIMEOUT,
                'conn_timeout' => self::DEFAULT_FETCH_CONNECT_TIMECOUT,
                'max_response_size'=> self::DEFAULT_FETCH_DATA_MAX_LEN,
            );
        }

        $httpproxy = Orp_FetchUrl::getInstance($options);
        Bingo_Timer::start("fetchurl");
        if('get' == $method) {
            $ret = $httpproxy->get($url, $header, $cookie);
        } else {
            $ret = $httpproxy->post($url, $params, $header, $cookie);
        }
        Bingo_Timer::end("fetchurl");

        if(false == $ret) {
            $errno = $httpproxy->errno();
            $errmsg = $httpproxy->errmsg();
            Bingo_Log::warning("fetchurl fail, errno=$errno, errmsg=$errmsg");
            return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $ret);
    }

    /**
     * @param $errno
     * @param $data
     * @param $errmsg
     * @return array
     */
    private static function _errRet($errno, $data = null, $errmsg = null)
    {
        $arrRet = array(
            'errno' => $errno,
            'errmsg' => is_null($errmsg) ? Tieba_Error::getErrmsg($errno) : $errmsg,
        );
        if(!is_null($data)) {
            $arrRet['data'] = $data;
        }
        return $arrRet;
    }
}
