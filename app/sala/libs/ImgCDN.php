<?php

class Libs_ImgCDN {

    /**
     * @param $strUrl
     * @return string
     */
    public static function getPicSignFromUrl($strUrl){
        $arrTmp = explode('/', $strUrl); // http://imgsrc.baidu.com/forum/pic/item/c91373f082025aafd815170ffaedab64024f1aca.jpg
        $intLength = count($arrTmp);
        $strPicName = $arrTmp[$intLength - 1]; // c91373f082025aafd815170ffaedab64024f1aca.jpg
        $arrTmp2 = array_filter(explode('.', $strPicName));
        $picSign = '';
        if (isset($arrTmp2[0])) {
            $picSign = $arrTmp2[0];
        }
        return $picSign;
    }

    /**
     * @param $picSign
     * @param $picSpec
     * @return mixed|string
     */
    public static function picSign2Url($picSign, $picSpec) {
        $arrOutput = Space_Urlcrypt::decodePicUrlCrypt($picSign);
        if($arrOutput === false || !isset($arrOutput[0])){
            Bingo_Log::warning("decode pic id error! input[".$picSign."]");
            return '';
        }
        list($foreignKey,$picId) = $arrOutput;
        $obj = new Bd_Pic_UrlCrypt();
        $arrIput = array(
            //"domain" => 'cq01-testing-arch-iknow02.vm.baidu.com:8082',
            "pic_id" => $picId,
            'foreign_key' => $foreignKey,
            "product_name" => "forum",
            "pic_spec" => $picSpec,//图片处理特征，abpic表示小图，其他参加文档或wiki
        );
        $arrReqs[] = $arrIput;
        $ret = $obj->BatPid2Url($arrReqs);
        //print_r($ret);
        $strUrl = '';
        if (isset($ret['resps'][0])) {
            $strUrl = $ret['resps'][0];
            //域名统一 *.hiphotos.baidu.com 统一转换成c.hiphotos.baidu.com
            $strUrl = preg_replace('/http:\/\/[a-z].hiphotos/','http://c.hiphotos',$strUrl);
        }

        return $strUrl;
    }

    /**
     * @param $arrSpec
     * @return string
     */
    public static function getBigPicStrategy( $arrSpec )
    {
        $intScreenW = $arrSpec['screen_w'];
//        $intScreenH = $arrSpec['screen_h'];
        $intOriginalPicW = $arrSpec['pic_w'];
        $intOriginalPicH = $arrSpec['pic_h'];
        $type = $arrSpec['type'];

        $intOriginal = [
            'w' => $intOriginalPicW,
            'h' => $intOriginalPicH,
        ];
        // 一些认知
        // screen_w < screen_h
        if($intOriginalPicW < $intOriginalPicH){
            $short = 'w';
        }
        else {
            $short = 'h';
        }

        if($intOriginal[$short] >= $intScreenW) {
            // 最短边比屏幕宽长,按屏幕宽裁剪
            $spec = 'scrop=' . $intScreenW;
        }
        else {
            // 先将最短边放到到屏幕宽,然后再从中间截取屏幕宽
            if($short=='w') {
                $long = (int)(($intScreenW/$intOriginalPicW * $intOriginalPicH) / 2 - $intScreenW/2);
                $crop =  [0,$long];
            }
            else {
                $long = (int)(($intScreenW/$intOriginalPicH * $intOriginalPicW) / 2 - $intScreenW/2);
                $crop =  [$long,0];
            }
            if ($type == 'gif') {
                $spec = sprintf('g=0;e%s=%d;crop=%d,%d,%d,%d',strtoupper($short),$intScreenW,$crop[0],$crop[1],$intScreenW,$intScreenW);
            }
            else {
                $spec = sprintf('e%s=%d;crop=%d,%d,%d,%d',strtoupper($short),$intScreenW,$crop[0],$crop[1],$intScreenW,$intScreenW);
            }
        }
        return $spec;
    }

}