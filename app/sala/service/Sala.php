<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2016/7/17
 * Time: 16:38
 */
class Service_Sala {

    public static $service_ie = 'utf-8';
    private static $boolReloaded = false;

    /**
     * getIE
     * @return string
     */
    public static function getIE() {
        return self::$service_ie;
    }

    /**
     * @param $strMethod
     * @param $arrInput
     * @return array
     */
    public static function call($strMethod, $arrInput)
    {
        $methods_to_subservices = self::_getMethodsArr();
        if(!array_key_exists($strMethod, $methods_to_subservices)){
            if(self::$boolReloaded){
                Bingo_Log::warning("methods call not found in service.[$strMethod]");
                return self::_errRet(Tieba_Errcode::ERR_METHOD_NOT_FOUND);
            }else{
                $methods_to_subservices = self::_reloadMethodsArr();
                if(!array_key_exists($strMethod, $methods_to_subservices)){
                    Bingo_Log::warning("methods call not found in service.[$strMethod]");
                    return self::_errRet(Tieba_Errcode::ERR_METHOD_NOT_FOUND);
                }
            }
        }

        $strSubService = $methods_to_subservices[$strMethod];
        $strClassFile = dirname(__FILE__)."/".strtolower($strSubService)."/$strSubService.php";
        $strClassName = "Service_$strSubService"."_$strSubService";

        if(!file_exists($strClassFile)){
            Bingo_Log::warning("file call not found in service.[$strClassFile]");
            return self::_errRet(Tieba_Errcode::ERR_FILE_NOT_FOUND);
        }
        try {
            require_once ($strClassFile);

            if(method_exists($strClassName, 'preCall')){
                call_user_func_array(array($strClassName, 'preCall'),array($arrInput));
            }
            $arrRet = call_user_func_array(array($strClassName, $strMethod),array($arrInput));
            if(method_exists($strClassName, 'postCall')){
                call_user_func_array(array($strClassName, 'postCall'),array($arrInput));
            }

            if(!$arrRet) {
                Bingo_Log::warning("call user func failed. [$strClassName::$strMethod] .");
                if(!self::$boolReloaded){
                    self::_reloadMethodsArr();
                }
                return self::_errRet(Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL);
            }
            return $arrRet;

        }catch (Libs_Exception $e){
            Bingo_Log::warning(self::_formatExceptions($e));
            return self::_errRet($e->getCode(), $e->getMessage());
        }
    }

    /**
     * _getMethodsArr
     * @return array
     */
    private static function _getMethodsArr()
    {
        $strCacheFile = self::_getCacheFile();
        if(file_exists($strCacheFile)){
            include $strCacheFile;
            if(isset($methods_to_subservices)) {
                return $methods_to_subservices;
            }
        }

        return self::_reloadMethodsArr();
    }

    /**
     * _reloadMethodsArr
     * @return array
     */
    private static function _reloadMethodsArr()
    {
        Bingo_Timer::start('reloadMethodsArr');

        $strPath = dirname(__FILE__);
        $arrDirs = scandir($strPath);
        $methods_to_subservices = array();

        foreach($arrDirs as $strDir) {
            if('.' == $strDir || '..' == $strDir) {
                continue;
            }

            $strSubPath = "$strPath/$strDir";
            if(!is_dir($strSubPath)) {
                continue;
            }

            $strSubService = ucfirst($strDir);
            $strClassFile = "$strSubPath/$strSubService.php";
            if(!file_exists($strClassFile)) {
                continue;
            }
            require_once $strClassFile;

            $strClassName = "Service_${strSubService}_${strSubService}";
            if(!class_exists($strClassName) || !is_subclass_of($strClassName, 'Libs_Service_Base')) {
                continue;
            }

            $objRef = new ReflectionClass($strClassName);
            $arrMethods = $objRef->getMethods(ReflectionMethod::IS_PUBLIC);
            foreach ($arrMethods as $objMethod){
                if($objMethod->name != 'preCall' && $objMethod->name != 'postCall') {
                    $methods_to_subservices[$objMethod->name] = $strSubService;
                }
            }
        }

        $strTmp = var_export($methods_to_subservices, true);
        $strTmp = "<?php\n\$methods_to_subservices = \n$strTmp;\n?>";
        $strCacheFile = self::_getCacheFile();
        $strTmpFile = "$strCacheFile.bak".rand();
        file_put_contents($strTmpFile,$strTmp);
        if (file_exists($strTmpFile)) {
            rename($strTmpFile, $strCacheFile);
        }
        if (file_exists ($strTmpFile)) {
            unlink($strTmpFile);
        }

        self::$boolReloaded = true;

        Bingo_Timer::end('reloadMethodsArr');
        Bingo_Log::pushNotice("INNER_COST_reloadMethodsArr", Bingo_Timer::calculate('reloadMethodsArr'));

        return $methods_to_subservices;
    }

    /**
     * _getCacheFile
     * @return string
     */
    private static function _getCacheFile()
    {
        return dirname(__FILE__) . '/methods.php';
    }

    /**
     * @param $errno
     * @param $errmsg
     * @param $data
     * @return array
     */
    private static function _errRet($errno, $errmsg = '', $data = null)
    {
        $arrRet = array(
            'errno' => $errno,
            'errmsg' => $errmsg ? :Tieba_Error::getErrmsg($errno),
        );
        if(!is_null($data)){
            $arrRet['data'] = $data;
        }
        return $arrRet;
    }

    /**
     * @param Libs_Exception $e
     * @return string
     */
    private static function _formatExceptions(Libs_Exception $e)
    {
        $strFormat = "[EXCEPTION] code '%u' with message '%s' in '%s' on line '%u'\n\n";
        $strFormat .= "Stack Trace:\n%s\n\n";
        $strFormat .= "Exception Data:\n%s\n";

        $code = $e->getCode();
        $message = $e->getMessage();
        $file = $e->getFile();
        $line = $e->getLine();
        $trace = $e->getTraceAsString();
        $data = var_export($e->getData(), true);

        $error = sprintf($strFormat, $code, $message, $file, $line, $trace, $data);
        return $error;
    }
}
