<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2017/3/14
 * Time: 22:13
 */
class Service_Test_Test extends Libs_Service_Base {

    /**
     * @param $arrInput
     * @return array
     */
    public static function testDummy($arrInput)
    {
        return self::success($arrInput);
    }

    /**
     * @param $arrInput
     * @return array
     * @throws Libs_Exception
     */
    public static function testRedis($arrInput)
    {
        $strFunction = self::getStrParam('f', $arrInput);
        if(empty($strFunction)) {
            throw new Libs_Exception('need param [f]', Tieba_Errcode::ERR_PARAM_ERROR, $arrInput);
        }

        $objRedis = Libs_Redis::getInstance();
        if(!method_exists($objRedis, $strFunction)) {
            throw new Libs_Exception("redis method[$strFunction] not exist", Tieba_Errcode::ERR_METHOD_NOT_FOUND);
        }

        $ret = call_user_func_array([$objRedis, $strFunction], [$arrInput]);

        return self::success($ret);
    }

}
