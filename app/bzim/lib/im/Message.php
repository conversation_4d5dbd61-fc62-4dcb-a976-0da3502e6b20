<?php
/***************************************************************************
 *
 * Copyright (c) 2010 Baidu.com, Inc. All Rights Reserved
 * $Id$
 *
 **************************************************************************/

class Lib_Im_Message {

    /**
     * @param  nill
     * @return  nill
     * */
    private static function buildApnsMsg($msg,
        $ext,
        $badge = 1 ,
        $sound = 'default'
    ){
        return '{"aps":{"alert":"'.$msg.'","badge":'.$badge.',"sound":"'.$sound.'"},"ext":'.json_encode(Lib_Im_Util::change_key_style($ext)).'}';
    }

    /**
     * @param  nill
     * @return  nill
     * */
    public static function buildAckMsg($intCmd,
        $arrMsgTag,
        $arrDevice,
        $data=array(),
        $intErrno=Tieba_Errcode::ERR_SUCCESS
    ){
        $arrOutput = array();
        $arrOutput['error']  = array(
            'errno'  => strval($intErrno),
            'errmsg' => strval(Tieba_Error::getErrmsg($intErrno)),
            'usermsg'=> Bingo_Encode::convert(strval(Tieba_Error::getUserMsg($intErrno)), Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK),
        );
        $arrOutput['data'] = $data;
        return $arrOutput;
    }

}
