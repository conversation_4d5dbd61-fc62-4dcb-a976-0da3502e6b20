<?php

class Lib_ServiceBase {

    protected static $_arrConf = [];

    /**
     * preCall
     * @param $arrInput
     */
    public static function preCall($arrInput)
    {
        Bingo_Timer::start($arrInput['method']);
        self::$_arrConf = Bd_Conf::getAppConf('app/designer/service_designer');
    }

    /**
     * postCall
     * @param $arrInput
     */
    public static function postCall($arrInput)
    {
        Bingo_Timer::end($arrInput['method']);
        Bingo_Log::pushNotice("INNER_COST_{$arrInput['method']}", Bingo_Timer::calculate($arrInput['method']));
    }

    public static function buildAppend($arrInput){
        $intNeedHasMore = (int)$arrInput['need_has_more'];
        $strAppend = '';
        $strGroupBy = '';
        $strOrderBy = '';
        if(isset($arrInput['rn'])){
            $intSize = (int)$arrInput['rn'];
            if(isset($arrInput['pn'])){
                $intLimit = ((int)$arrInput['pn'] - 1) * $intSize;
            }else{
                $intLimit = 0;
            }
            $strAppend = sprintf(" limit %d, %d ", $intLimit, $intSize + $intNeedHasMore);
        }

        if(isset($arrInput['orderby'])){
            $strOrderBy = ' order by ' . $arrInput['orderby']['field'] . ' ' . $arrInput['orderby']['sort'] . ' ';
        }
        if(isset($arrInput['groupby'])){
            $strGroupBy = ' group by ' . $arrInput['groupby'] . ' ';
        }
        $strAppend = $strGroupBy . $strOrderBy . $strAppend;
        return $strAppend;
    }
}
