<?php
/**
 * date: 2017-12-14 14:29:32
 * author : dongliang04
 **/
class Lib_Crypt {

    const APP_ID_CRYPT_KEY = 'PQl6wk3I';

    /**
     * @param $intAppId
     * @param $intAppType
     * @param $strAppId
     * @return bool
     */
    public static function encodeAppId($intAppId, $intAppType, &$strAppId)
    {
        if($intAppId <= 0 ||1 != $intAppType) {
            return false;
        }

        $strAppId = fcrypt_id_2hstr(self::APP_ID_CRYPT_KEY, intval($intAppId), intval($intAppType));
        return true;
    }

    /**
     * @param $strAppId
     * @param $intAppId
     * @param $intAppType
     * @return mixed
     */
    public static function decodeAppId($strAppId, &$intAppId = null, &$intAppType = null)
    {
        list($intAppId, $intAppType) = fcrypt_hstr_2id(self::APP_ID_CRYPT_KEY, strval($strAppId));
        Bingo_Log::warning($intAppId . $intAppType);
        if($intAppId <= 0 || 1 != $intAppType) {
            return false;
        }

        return true;
    }
}
