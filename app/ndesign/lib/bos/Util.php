<?php
require_once __DIR__.'/vendor/autoload.php';
require_once __DIR__.'/src/BaiduBce/Services/Bos/BosClient.php';
require_once __DIR__.'/src/BaiduBce/Services/Bos/BosOptions.php';
require_once __DIR__.'/src/BaiduBce/Services/Bos/CannedAcl.php';

use BaiduBce\Services\Bos\BosClient;
use BaiduBce\Services\Bos\BosOptions;
use BaiduBce\Services\Bos\CannedAcl;
use BaiduBce\Auth\SignOptions;

class Lib_Bos_Util {

    const PRODUCT = 'tieba-ndesign';

    const ACL_PRIVATE = CannedAcl::ACL_PRIVATE;
    const ACL_PUBLIC_READ = CannedAcl::ACL_PUBLIC_READ;

    protected static $_bos_client = null;
    protected static $_config = array(
        'credentials' => array(
            //'ak' => '04dc122196684680ad95c2d0413232d9',
            //'sk' => 'dc05c7a8bab54749a66d739b3540be35',
            'ak' => '0fbefe974cd24b5cb4d83acad06f1df8',
            'sk' => 'd2232740b2ad41bea8b7547f967378ed',
        ),
        //'endpoint' => '10.107.37.35:8080',
        //'endpoint' => '10.208.7.222:8080',
        'endpoint' => 'http://su.bcebos.com',
    );

    /**
     * @brief : Check _bos_client null or not.
     * @return bool
     */
    private static function _init()
    {
        if(!self::$_bos_client) {
            self::$_bos_client = new BosClient(self::$_config);
        }

        if (!self::$_bos_client){
            return false;
        }

        return true;
    }

    /**
     * @param string $strAcl
     * @return mixed
     */
    public static function createBucket($strAcl = self::ACL_PUBLIC_READ)
    {
        if(!self::_init()){
            Bingo_Log::warning("Init fail.");
            return false;
        }

        try{
            if(!self::$_bos_client->doesBucketExist(self::PRODUCT)) {
                self::$_bos_client->createBucket(self::PRODUCT);
            }

            self::$_bos_client->setBucketCannedAcl(self::PRODUCT, $strAcl);
            return true;
        }catch(Exception $e){
            Bingo_Log::warning("Exception: ". $e->getMessage());
            return false;
        }

    }

    /**
     * @param $strFileName
     * @param $strData
     * @param $arrMetaData
     * @return bool
     */
    public static function saveObjectFromString($strFileName, $strData, &$arrMetaData = null)
    {
        if(!self::_init()){
            Bingo_Log::warning("Init fail.");
            return false;
        }

        try{
            Bingo_Timer::start('BOS_putObjectFromString');
            $objResponse = self::$_bos_client->putObjectFromString(self::PRODUCT, $strFileName, $strData);
            Bingo_Timer::end('BOS_putObjectFromString');

            $arrMetaData = array(
                'content_type' => $objResponse->metadata['contentType'],
                'content_length' => $objResponse->metadata['contentLength'],
                'content_md5' => $objResponse->metadata['contentMd5'],
            );
            return true;
        }catch (Exception $e){
            Bingo_Log::warning("Exception: ". $e->getMessage());
            return false;
        }
    }

    /**
     * @param $strFileName
     * @param $intExpireTime
     * @param $strUrl
     * @return bool
     */
    public static function generateUrl($strFileName, $intExpireTime, &$strUrl)
    {
        if(!self::_init()){
            Bingo_Log::warning("Init fail.");
            return false;
        }

        try{
            $arrOptions = array();
            if($intExpireTime > 0) {
                $arrOptions[BosOptions::SIGN_OPTIONS][SignOptions::EXPIRATION_IN_SECONDS] = intval($intExpireTime);
            }
            Bingo_Timer::start('BOS_generatePreSignedUrl');
            $strUrl = self::$_bos_client->generatePreSignedUrl(self::PRODUCT, $strFileName, $arrOptions);
            Bingo_Timer::end('BOS_generatePreSignedUrl');
            return true;
        }catch(Exception $e){
            Bingo_Log::warning("Exception: ". $e->getMessage());
            return false;
        }

    }

    /**
     * @param $strFileName
     * @param $strData
     * @return bool
     */
    public static function getObjectAsString($strFileName, &$strData)
    {
        if(!self::_init()){
            Bingo_Log::warning("Init fail.");
            return false;
        }

        try{
            Bingo_Timer::start('BOS_getObjectAsString');
            $strData = self::$_bos_client->getObjectAsString(self::PRODUCT, $strFileName);
            Bingo_Timer::end('BOS_getObjectAsString');

            return true;
        }catch(Exception $e){
            Bingo_Log::warning("Exception: ". $e->getMessage());
            return false;
        }
    }

    /**
     * @param $strFileName
     * @param $hOStream
     * @param $arrMetaData
     * @return bool
     */
    public static function getObjectToStream($strFileName, $hOStream, &$arrMetaData = null)
    {
        if(!self::_init()){
            Bingo_Log::warning("Init fail.");
            return false;
        }

        try{
            Bingo_Timer::start('BOS_getObject');
            $objResponse = self::$_bos_client->getObject(self::PRODUCT, $strFileName, $hOStream);
            Bingo_Timer::end('BOS_getObject');

            $arrMetaData = array(
                'content_type' => $objResponse->metadata['contentType'],
                'content_length' => $objResponse->metadata['contentLength'],
                'content_md5' => $objResponse->metadata['contentMd5'],
            );
            return true;
        }catch (Exception $e){
            Bingo_Log::warning("Exception: ". $e->getMessage());
            return false;
        }
    }

    /**
     * @param $strFileName
     * @return bool
     */
    public static function delObject($strFileName)
    {
        if(!self::_init()){
            Bingo_Log::warning("Init fail.");
            return false;
        }

        try{
            Bingo_Timer::start('BOS_deleteObject');
            self::$_bos_client->deleteObject(self::PRODUCT, $strFileName);
            Bingo_Timer::end('BOS_deleteObject');

            return true;
        }catch(Exception $e){
            Bingo_Log::warning("Exception: ". $e->getMessage());
            return false;
        }
    }

    /**
     * @param $strFromFileName
     * @param $strToFileName
     * @return bool
     */
    public static function copyObject($strFromFileName, $strToFileName)
    {
        if(!self::_init()){
            Bingo_Log::warning("Init fail.");
            return false;
        }

        try{
            Bingo_Timer::start('BOS_copyObject');
            self::$_bos_client->copyObject(self::PRODUCT, $strFromFileName, self::PRODUCT, $strToFileName);
            Bingo_Timer::end('BOS_copyObject');

            return true;
        }catch(Exception $e){
            Bingo_Log::warning("Exception: ". $e->getMessage());
            return false;
        }
    }
}
