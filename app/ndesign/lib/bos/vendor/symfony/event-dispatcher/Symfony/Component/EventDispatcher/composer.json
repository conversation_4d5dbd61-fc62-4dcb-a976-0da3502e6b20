{"name": "symfony/event-dispatcher", "type": "library", "description": "Symfony EventDispatcher Component", "keywords": [], "homepage": "http://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "require": {"php": ">=5.3.3"}, "require-dev": {"symfony/dependency-injection": "~2.6", "symfony/expression-language": "~2.6", "symfony/config": "~2.0,>=2.0.5", "symfony/stopwatch": "~2.3", "psr/log": "~1.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "autoload": {"psr-0": {"Symfony\\Component\\EventDispatcher\\": ""}}, "target-dir": "Symfony/Component/EventDispatcher", "minimum-stability": "dev", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}}