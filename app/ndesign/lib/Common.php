<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2016/10/11
 * Time: 17:21
 */
class Lib_Common {

    /**
     * @param $strAppId
     * @param $strVersion
     * @param $strContentMd5
     * @return string
     */
    public static function getContentFileName($intAppId, $strVersion, $strContentMd5)
    {
        return "app/{$intAppId}/content/v{$strVersion}_{$strContentMd5}.txt";
    }

    /**
     * @param $strAppName
     * @return int
     */
    public static function checkAppName($strAppName)
    {
        return preg_match('/^[0-9a-zA-Z\x{4e00}-\x{9fa5}._() ]{1,64}$/u', $strAppName);
    }

    /**
     * @param $strContent
     * @return bool
     */
    public static function checkContent($strContent)
    {
        //return '' == $strContent || (strlen($strContent) < 2000000 && !is_null(json_decode($strContent, 1)));
        return strlen($strContent) < 2000000;
    }

    /**
     * @param $strSettings
     * @return bool
     */
    public static function checkSettings($strSettings)
    {
        //return '' == $strSettings || (strlen($strSettings) < 2000 && !is_null(json_decode($strSettings, 1)));
        return strlen($strSettings) < 2000;
    }

    /**
     * @param $strContentMd5
     * @return int
     */
    public static function checkContentMd5($strContentMd5)
    {
        return preg_match('/^(|[0-9a-fA-F]{32})$/', $strContentMd5);
    }

    /**
     * @param $arrUserData
     * @return string
     */
    public static function getPortrait($arrUserData)
    {
        $strUserName = Bingo_Encode::convert($arrUserData['user_name'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
        $strPortrait = Tieba_Ucrypt::encode($arrUserData['user_id'], $strUserName);
        if(isset($arrUserData['portrait_time']) && $arrUserData['portrait_time'] > 0) {
            $strPortrait .= '?t=' . $arrUserData['portrait_time'];
        }
        return $strPortrait;
    }

    /**
     * @param $arrUserData
     * @return string
     */
    public static function getNameShow($arrUserData)
    {
        switch(true) {
            case !empty($arrUserData['profession_manager_nick_name']):
                return strval($arrUserData['profession_manager_nick_name']);
            case !empty($arrUserData['user_nickname']):
                return strval($arrUserData['user_nickname']);
            default:
                return $arrUserData['user_name'];
        }
    }

    /**
     * @param $strIp
     * @return string
     */
    public static function maskIP($strIp)
    {
        $intPos  = strrpos($strIp, '.');
        $strHalf = substr($strIp, 0, $intPos);
        return $strHalf.'.*';
    }
}

