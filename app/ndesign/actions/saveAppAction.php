<?php
/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file saveAppAction.php
 * <AUTHOR>
 * @date 2017/11/14 10:25:35
 * @brief 
 *  
 **/


class saveAppAction extends Lib_ActionBase{

    protected function _execute(){
        try{
            $this->_userPostCheck();
            $intUserId = $this->_arrUserInfo['user_id'];
            $strAppId = strval($this->_get('id', 0));
            $strAppName = strval($this->_get('name', ''));
            $strAppCover = strval($this->_get('cover', ''));
            $strAppDesc = strval($this->_get('desc', ''));
            $strAppContent = strval($this->_get('content', ''));
            $strSetting = strval($this->_get('setting', ''));
            $strExtraInfo = strval($this->_get('extraInfo', ''));
            $adminPrivate = intval($this->_get('admin_private', -1));

            if(!Lib_Crypt::decodeAppId($strAppId, $intAppId)) {
                throw new Util_Exception("invalid app id: $strAppId", Tieba_Errcode::ERR_PARAM_ERROR);
            }

            //先把app查出来
            $arrServiceInput = array(
                'user_id' => $intUserId,
                'app_id' => $intAppId,
            );



            //content and extraInfo 存bos
            if('' != $strAppContent){
                if(!Lib_Common::checkContent($strAppContent)){
                    throw new Util_Exception("invalid content, app_id=$strAppId, content="
                        . mb_substr($strAppContent, 0, 100, 'utf-8'), Tieba_Errcode::ERR_PARAM_ERROR);
                }
                $intContentSize = strlen($strAppContent);
                $strContentMd5 = md5($strAppContent);

                $strFileName = Lib_Common::getContentFileName($intAppId, 'draft', $strContentMd5);
                if(!Lib_Bos_Util::saveObjectFromString($strFileName, $strAppContent)){
                    throw new Util_Exception("save content fail. app_id = $intAppId", Tieba_Errcode::ERR_RPC_CALL_FAIL);
                }
            } else {
                $intContentSize = 0;
                $strContentMd5 = '';
            }

            if('' != $strExtraInfo){
                if(!Lib_Common::checkContent($strExtraInfo)){
                    throw new Util_Exception("invalid content, app_id=$strAppId, content="
                        . mb_substr($strExtraInfo, 0, 100, 'utf-8'), Tieba_Errcode::ERR_PARAM_ERROR);
                }
                $intExtraInfoSize = strlen($strExtraInfo);
                $strExtraInfoMd5 = md5($strExtraInfo);


                $strFileName = Lib_Common::getContentFileName($intAppId, 'draft', $strExtraInfoMd5);
                if(!Lib_Bos_Util::saveObjectFromString($strFileName, $strExtraInfo)){
                    throw new Util_Exception("save extra_info fail. app_id = $strAppId", Tieba_Errcode::ERR_RPC_CALL_FAIL);
                }
            } else {
                $intExtraInfoSize = 0;
                $strExtraInfoMd5 = '';
            }



            //保存下来的md5和size入库
            $arrServiceInput = array(
                'user_id' => $intUserId,
                //'name' => $strAppName,
                'app_id' => $intAppId,
            );
            $arrGenServiceInput = array(
                'name' => array('value' => $strAppName, 'default' => ''),
                'cover' => array('value' => $strAppCover, 'default' => ''),
                'description' => array('value' => $strAppDesc, 'default' => ''),
                'draft_size' => array('value' => $intContentSize, 'default' => 0),
                'draft_md5' => array('value' => $strContentMd5, 'default' => ''),
                'setting' => array('value' => $strSetting, 'default' => ''),
                'extra_size' => array('value' => $intExtraInfoSize, 'default' => 0),
                'extra_md5' => array('value' => $strExtraInfoMd5, 'default' => ''),
                'admin_private' => array('value' => $adminPrivate, 'default' => -1),
            );
            if(!self::_genServiceInput($arrGenServiceInput, $arrServiceInput)){
                throw new Util_Exception("gen service input fail", Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            $strService = 'ndesign';
            $strMethod = 'saveApp';
            $arrServiceOutput = Util_Rpc::callService($strService, $strMethod,  $arrServiceInput);
            if(!Util_Ret::checkServiceOutput($arrServiceInput, $arrServiceOutput, $strMethod)){
                throw new Util_Exception($arrServiceOutput['errmsg'], $arrServiceOutput['errno']);
            }
            $this->_succRet($arrServiceOutput['data']);
        }catch (Util_Exception $e){
            Bingo_Log::warning('errno: ' . $e->getCode() . ', errmsg: ' . $e->getMessage());
            $this->_jsonRet($e->getCode());
        }
    }

    private static function _genServiceInput($arrInput, &$arrOutput){
        foreach($arrInput as $key => $value){
            if($arrInput[$key]['value'] != $arrInput[$key]['default']){
                $arrOutput[$key] = $arrInput[$key]['value'];
            }
        }
        return true;
    }
}



/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
