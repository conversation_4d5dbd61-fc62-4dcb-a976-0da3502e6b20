<?php
/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file activeVersionAction.php
 * <AUTHOR>
 * @date 2017/11/14 10:26:28
 * @brief 
 *  
 **/

class activeVersionAction extends Lib_ActionBase {

    protected function _execute()
    {
        try {
            $this->_userPostCheck();

            $intUserId = $this->_arrUserInfo['user_id'];
            $strAppId = Bingo_Http_Request::get('id', '');
            $strVersion = trim(strval(Bingo_Http_Request::get('version', '')));

            if(!Lib_Crypt::decodeAppId($strAppId, $intAppId)) {
                throw new Util_Exception("invalid app id: $strAppId", Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $arrServiceInput = array(
                'user_id' => $intUserId,
                'app_id' => $intAppId,
                'version' => $strVersion,
            );
            $arrServiceOutput = Util_Rpc::callService(MODULE, 'activeVersion', $arrServiceInput);
            if(Tieba_Errcode::ERR_SUCCESS != $arrServiceOutput['errno']) {
                throw new Util_Exception($arrServiceOutput['errmsg'], $arrServiceOutput['errno']);
            }

            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS);

        } catch (Util_Exception $e) {

            Bingo_Log::warning('errno: ' . $e->getCode() . ', errmsg: ' . $e->getMessage());
            $this->_jsonRet($e->getCode());
        }

    }

}



/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
