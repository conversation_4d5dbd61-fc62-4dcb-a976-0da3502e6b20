<?php
/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file createAction.php
 * <AUTHOR>
 * @date 2017/12/29 10:25:21
 * @brief 
 *  
 **/

class createAction extends Lib_ActionBase{
    /**
     * @param $arrInput
     * @return array
     */
    protected function _execute(){
        try{
            $this->_userPostCheck();
            $array1 = array();
            $array2 = array(1 => "data");
            $result = array_merge($array1, $array2);
            // echo json_encode($array1);
            // exit;
            $intUserId = intval($this->_arrUserInfo['user_id']);
            $strName = trim(strval($this->_get('name','')));
            $strIntro = trim(strval($this->_get('intro','')));
            $strCover = trim(strval($this->_get('cover','')));
            $strDomain = trim(strval($this->_get('domain','')));
            $intAcl = $this->_get('access_level', Lib_Define::GROUP_ACL_PRIVATE);

            $arrServiceInput = array(
                'user_id' => $intUserId,
                'domain' => $strDomain,
                'name' => $strName,
                'intro' => $strIntro,
                'cover' => $strCover,
                'access_level' => $intAcl,
            );
            $strMethod = 'createGroup';
            $arrServiceOutput = Util_Rpc::callService(MODULE, $strMethod, $arrServiceInput);
            Bingo_Log::warning(print_r($arrServiceOutput, 1));
            if(!Util_Ret::checkServiceOutput($arrServiceInput, $arrServiceOutput, $strMethod)){
                throw new Util_Exception($arrServiceOutput['errmsg'], $arrServiceOutput['errno']);
            }
            $intGroupId = $arrServiceOutput['data']['id'];
            $intAppType = 1;
            Lib_Crypt::encodeAppId($intGroupId, $intAppType, $strAppId);
            $arrRet = array(
                'id' => $strAppId,
                'domain'=> $strDomain
            );
            $this->_succRet($arrRet);
        }catch (Util_Exception $e){
            Bingo_Log::warning('errno: ' . $e->getCode() . ', errmsg: ' . $e->getMessage());
            $this->_jsonRet($e->getCode(), $e->getMessage());
        }
    }
}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
