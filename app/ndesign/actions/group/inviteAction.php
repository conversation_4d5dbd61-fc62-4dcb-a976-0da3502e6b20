<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file inviteAction.php
 *
 * <AUTHOR>
 * @date 2017/12/29 10:25:21
 * @brief
 *
 **/
class inviteAction extends Lib_ActionBase
{

    /**
     * @param $arrInput
     *
     * @return array
     */
    protected function _execute()
    {
        try {
            $this->_userLoginCheck();
            $intUserId = $this->_arrUserInfo['user_id'];
            $inviteCode = Bingo_Http_Request::get('invite_code', '');
            $strMethod = 'inviteJoinGroup';
            $arrServiceInput = array(
                'user_id' => intval($intUserId),
                'invite_code' => $inviteCode,
            );

            $arrServiceOutput = Util_Rpc::callService(MODULE, $strMethod, $arrServiceInput);
            // if(!Util_Ret::checkServiceOutput($arrServiceInput, $arrServiceOutput, $strMethod)){
            //     throw new Util_Exception($arrServiceOutput['errmsg'], $arrServiceOutput['errno']);
            // }
            
            if(Tieba_Errcode::ERR_SUCCESS != $arrServiceOutput['errno']) {
                throw new Util_Exception($arrServiceOutput['errmsg'], $arrServiceOutput['errno']);
            }

            $this->_succRet($arrServiceOutput['data']);
        } catch (Util_Exception $e) {
            Bingo_Log::warning('errno: '.$e->getCode().', errmsg: '.$e->getMessage());
            $this->_jsonRet($e->getCode(), $e->getMessage());
        }
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
