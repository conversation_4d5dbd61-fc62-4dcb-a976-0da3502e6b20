<?php
/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file publishVersionAction.php
 * <AUTHOR>
 * @date 2017/11/14 10:26:06
 * @brief 
 *  
 **/


class publishVersionAction extends Lib_ActionBase {

    protected function _execute()
    {
        try {
            $this->_userPostCheck();

            $intUserId = $this->_arrUserInfo['user_id'];
            $strAppId = Bingo_Http_Request::get('id', '');
            $strVersion = trim(strval(Bingo_Http_Request::get('version', '')));
            $strDescription = trim(strval(Bingo_Http_Request::get('description', '')));
            if(!Lib_Crypt::decodeAppId($strAppId, $intAppId)) {
                throw new Util_Exception("invalid app id: $strAppId", Tieba_Errcode::ERR_PARAM_ERROR);
            }

            //发布准备
            $arrParams = array(
                'user_id' => $intUserId,
                'app_id' => $intAppId,
                'version' => $strVersion,
                'prepare' => 1,
            );
            $arrRet = Util_Rpc::callService(MODULE, 'publishVersion', $arrParams);
            if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
                throw new Util_Exception($arrRet['errmsg'], $arrRet['errno']);
            }
            if(1 == $arrRet['data']['is_repeated']) {
                $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS);
                return;
            }

            //转存草稿
            $strFromFileName = Lib_Common::getContentFileName($intAppId, 'draft', $arrRet['data']['content_md5']);
            $strToFileName = Lib_Common::getContentFileName($intAppId, $strVersion, $arrRet['data']['content_md5']);
            if(!Lib_Bos_Util::copyObject($strFromFileName, $strToFileName)) {
                throw new Util_Exception("copy content fail, app_id=$intAppId", Tieba_Errcode::ERR_RPC_CALL_FAIL);
            }
            //确认发布
            $arrParams = array(
                'user_id' => $intUserId,
                'app_id' => $intAppId,
                'version' => $strVersion,
                'description' => $strDescription,
            );
            $arrRet = Util_Rpc::callService(MODULE, 'publishVersion', $arrParams);
            if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
                throw new Util_Exception($arrRet['errmsg'], $arrRet['errno']);
            }

            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS);

        } catch (Util_Exception $e) {

            Bingo_Log::warning('errno: ' . $e->getCode() . ', errmsg: ' . $e->getMessage());
            $this->_jsonRet($e->getCode(), $e->getMessage());
        }

    }



}



/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
