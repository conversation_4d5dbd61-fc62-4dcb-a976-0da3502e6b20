<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file getAppListAction.php
 *
 * <AUTHOR>
 * @date 2017/11/14 10:25:55
 * @brief
 *
 **/
class getAppListAction extends Lib_ActionBase
{
    const PAGE_SIZE = 10;
    const PAGE_NUM = 1;

    protected function _execute()
    {
        try {
            $this->_userLoginCheck();
            $intUserId = $this->_arrUserInfo['user_id'];
            $intAppAcl = $this->_get('access_level', Lib_Define::APP_ACL_ABLE);
            $intPn = intval($this->_get('pn', self::PAGE_NUM));
            $intRn = intval($this->_get('rn', self::PAGE_SIZE));
            $strGroupId = Bingo_Http_Request::get('group_id', '');

            if (!Lib_Crypt::decodeAppId($strGroupId, $intGroupId)) {
                throw new Util_Exception("invalid group id: $strGroupId", Tieba_Errcode::ERR_PARAM_ERROR);
            }

            if ($intPn <= 0 || $intRn < 0 || $intRn > 100) {
                throw new Util_Exception("param error, pn=$intPn, rn=$intRn", Tieba_Errcode::ERR_PARAM_ERROR);
            }

            $arrMultiInput['getAppCount'] = array(
                'serviceName' => 'ndesign',
                'method' => 'getAppCount',
                'input' => array(
                    'user_id' => $intUserId,
                    'group_id' => $intGroupId,
                ),
            );

            $arrMultiInput['getAppList'] = array(
                'serviceName' => 'ndesign',
                'method' => 'getAppList',
                'input' => array(
                    'user_id' => $intUserId,
                    'group_id' => $intGroupId,
                    'pn' => $intPn,
                    'rn' => $intRn,
                ),
            );

            $arrRet = Util_Rpc::mcallServices(MODULE, $arrMultiInput);
            foreach ($arrRet as $key => $value) {
                if (Tieba_Errcode::ERR_SUCCESS != $value['errno']) {
                    throw new Util_Exception($value['errmsg'], $value['errno']);
                }
            }

            $arrApps = $arrRet['getAppList']['data'];
            $intCount = $arrRet['getAppCount']['data'];

            $arrList = array();
            foreach ($arrApps as $value) {
                $intType = 1;
                if (!Lib_Crypt::encodeAppId($value['app_id'], $intType, $strAppId)) {
                    throw new Util_Exception("invalid app id: $strAppId", Tieba_Errcode::ERR_PARAM_ERROR);
                }
                $arrList[] = array(
                    'id' => $strAppId,
                    'name' => $value['name'],
                    'desc' => $value['description'],
                    'user_id' => $value['user_id'],
                    'admin_private' => $value['admin_private'],
                    'cover' => $value['cover'],
                    'utime' => $value['update_time']
                );
            }

            //获得用户信息
            //$arrUsers = $this->_getAppUsers($arrApps['list']);
            $total_pn = intval(($intCount / $intPn));
            $arrOutput = array(
                'page' => array(
                    'total_pn' => $total_pn,
                    'current_pn' => $intPn,
                ),
                'list' => $arrList,
                //'users' => $arrUsers,
            );

            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrOutput);
        } catch (Util_Exception $e) {
            Bingo_Log::warning('errno: '.$e->getCode().', errmsg: '.$e->getMessage());
            $this->_jsonRet($e->getCode());
        }
    }

    /**
     * @param $arrAppList
     *
     * @return array
     *
     * @throws Util_Exception
     */
    private function _getAppUsers($arrAppList)
    {
        if (empty($arrAppList)) {
            return [];
        }
        $arrUserIds = array_unique(array_column($arrAppList, 'user_id'));

        $arrReqs['mget_user_data_ex'] = [
            'serviceName' => 'user',
            'method' => 'mgetUserDataEx',
            'input' => [
                'user_id' => $arrUserIds,
                'need_follow_info' => 0,
                'need_pass_info' => 1,
                'get_icon' => 1,
            ],
            'httpMethod' => 'get',
        ];

        $arrRets = Util_Rpc::mcallServices('mget_login_user_info', $arrReqs);

        $arrRet = $arrRets['mget_user_data_ex'];
        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            throw new Util_Exception('get user data ex fail', $arrRet['errno']);
        }
        $arrUserDataExs = $arrRet['user_info'];

        $arrUsers = [];
        foreach ($arrUserIds as $intUserId) {
            if (!isset($arrUserDataExs[$intUserId])) {
                $arrUsers[$intUserId] = [];
                continue;
            }
            //$arrUserInfo = $arrUserInfos[$intUserId];
            $arrUserDataEx = $arrUserDataExs[$intUserId];

            $arrUsers[$intUserId] = [
                'user_id' => $intUserId,
                'user_name' => $arrUserDataEx['user_name'],
                'user_sex' => intval($arrUserDataEx['user_sex']),
                'portrait' => Lib_Common::getPortrait($arrUserDataEx),
                'name_show' => Lib_Common::getNameShow($arrUserDataEx),
                //'is_admin' => intval($arrUserInfo['is_manager']),
                //'can_create' => $arrUserInfo['block_mask'] & Libs_Define::USER_BLOCK_MASK_CREATE ? 0 : 1,
                //'can_publish' => $arrUserInfo['block_mask'] & Libs_Define::USER_BLOCK_MASK_PUBLISH ? 0 : 1,
            ];
        }

        return $arrUsers;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
