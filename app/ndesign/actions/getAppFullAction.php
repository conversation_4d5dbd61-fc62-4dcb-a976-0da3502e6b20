<?php
/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file getAppFullAction.php
 * <AUTHOR>
 * @date 2017/11/14 10:25:47
 * @brief 
 *  
 **/

class getAppFullAction extends Lib_ActionBase{
    private $_arrInput = array();
    private $_arrGetData = array();
    protected function _execute(){
        try{
            $this->_userLoginCheck();
            $intUserId = $this->_arrUserInfo['user_id'];
            $strAppId = $this->_get('id', '');
            $strVersion = $this->_get('version', '');

            if(!Lib_Crypt::decodeAppId($strAppId, $intAppId)) {
                throw new Util_Exception("invalid app id: $strAppId", Tieba_Errcode::ERR_PARAM_ERROR);
            }
            if($strVersion != ''){
                $arrMultiInput['getVersion'] = array(
                    'serviceName' => 'ndesign',
                    'method' => 'getVersion',
                    'input' => array(
                        //'user_id' => $intUserId,
                        'app_id' => $intAppId,
                        'version' => $strVersion,
                    ),
                );
            }
            $arrMultiInput['getApp'] = array(
                'serviceName' => 'ndesign',
                'method' => 'getApp',
                'input' => array(
                    'user_id' => $intUserId,
                    'app_id' => $intAppId,
                ),
            );
            $arrServiceOutput = Util_Rpc::mcallServices(MODULE, $arrMultiInput);
            Bingo_Log::warning(print_r($arrServiceOutput,1).'uiiuiui');
            foreach($arrServiceOutput as $key => $value){
                $this->_arrGetData[$key] = $value;
                if($value['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                    throw new Util_Exception($value['errmsg'], $value['errno']);
                }
            }

            if($strVersion == ''){
                $arrInfo = $this->_getDraftInfo();
            }else{
                $arrInfo = $this->_getVersionInfo();
            }

            if(empty($arrInfo['content']['file_name'])){
                $strContent = '';
            }else{
                if(!Lib_Bos_Util::getObjectAsString($arrInfo['content']['file_name'], $strContent)){
                    throw new Util_Exception("load content fail, app_id=$intAppId, version=$strVersion",Tieba_Errcode::ERR_RPC_CALL_FAIL);
                }
            }
            if(empty($arrInfo['extraInfo']['file_name'])){
                $strExtraInfo = '';
            }else{
                if(!Lib_Bos_Util::getObjectAsString($arrInfo['extraInfo']['file_name'], $strExtraInfo)){
                    throw new Util_Exception("load extraInfo fail, app_id=$intAppId, version=$strVersion",Tieba_Errcode::ERR_RPC_CALL_FAIL);
                }
            }

            $arrAppInfo = $this->_arrGetData['getApp']['data'];
            $arrOutput = array(
                'id' => $strAppId,
                'name' => $arrAppInfo['name'],
                'desc' => $arrAppInfo['description'],
                'utime' => $arrAppInfo['update_time'],
                'content' => $strContent,
                'setting' => $arrAppInfo['setting'],
                'active_ver' => $arrAppInfo['active_ver'],
                'latest_ver' => $arrAppInfo['latest_ver'],
                'extraInfo' => $strExtraInfo,
            );


        } catch (Util_Exception $e){
            Bingo_Log::warning('errno: ' . $e->getCode() . ', errmsg: ' . $e->getMessage());
            return $this->_jsonRet($e->getCode());
        }

        return $this->_succRet($arrOutput);
    }

    private function _getDraftInfo(){
        $arrApp = $this->_arrGetData['getApp']['data'];
        if(empty($arrApp)){
            throw new Util_Exception("no this app", Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intAppId = $arrApp['app_id'];
        if(empty($arrApp) || empty($arrApp['draft_md5'])){
            $strFileName = '';
        }else{
            $strFileName = Lib_Common::getContentFileName($intAppId, 'draft', $arrApp['draft_md5']);
        }

        if(empty($arrApp['extra_md5'])){
            $strExtraName = '';
        }else{
            $strExtraName = Lib_Common::getContentFileName($intAppId, 'draft', $arrApp['extra_md5']);
        }

        return array(
            'content' => array(
                'file_name' => $strFileName,
            ),
            'extraInfo' => array(
                'file_name' => $strExtraName,
            ),
        );
    }
    private function _getVersionInfo(){
        $arrAppVersion = $this->_arrGetData['getAppVersion']['data'][0];
        $arrApp = $this->_arrGetData['getApp']['data'];
        if(empty($arrAppVersion) || empty($arrAppVersion['content_md5'])){
            $strFileName = '';
        }else{
            $strFileName = Lib_Common::getContentFileName($intAppId, $arrAppVersion['version'], $arrAppVersion['content_md5']);
        }

        if(empty($arrApp['extra_md5'])){
            $strExtraName = '';
        }else{
            $strExtraName = Lib_Common::getContentFileName($intAppId, 'draft', $arrApp['extra_md5']);
        }

        return array(
            'content' => array(
                'file_name' => $strFileName,
            ),
            'extraInfo' => array(
                'file_name' => $strExtraName,
            ),
        );
    }

}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
