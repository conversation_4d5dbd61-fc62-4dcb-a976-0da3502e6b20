<?php
/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file getAppRenderAction.php
 * <AUTHOR>
 * @date 2017/11/14 10:26:51
 * @brief 
 *  
 **/

class getAppRenderAction extends Lib_ActionBase{
    protected function _execute()
    {
        try {
            $intUserId = $this->_arrUserInfo['user_id'];
            $strAppId = Bingo_Http_Request::get('id', '');
            $strVersion = Bingo_Http_Request::get('version', '');
            
            if(!Lib_Crypt::decodeAppId($strAppId, $intAppId)) {
                throw new Util_Exception("invalid app id: $strAppId", Tieba_Errcode::ERR_PARAM_ERROR);
            }

            Bingo_Timer::start('UI_getVersion');
            $arrApp = $this->_getAppActiveVer($intUserId, $intAppId);
            if($strVersion == 'draf'){
                $strFileName = $arrApp['file_name'];
            }else{
                $strVersion = $strVersion == '' ? $arrApp['active_ver'] : $strVersion;
                Bingo_Log::warning($strVersion);
                $arrVersion = $this->_getVersionInfo($intAppId, $strVersion);
                $strFileName = $arrVersion['file_name'];
            }


            if('' == $strVersion){
                throw new Util_Exception("there is no active ver in this app", Tieba_Errcode::ERR_PARAM_ERROR);
            }

            Bingo_Timer::end('UI_getVersion');

            Bingo_Timer::start('UI_getObjectAsString');
            if(empty($strFileName)) {
                $strContent = '';
            } else {
                if(!Lib_Bos_Util::getObjectAsString($strFileName, $strContent)) {
                    throw new Util_Exception("load content fail, app_id=$strAppId, version=$strVersion",
                        Tieba_Errcode::ERR_RPC_CALL_FAIL);
                }
            }
            Bingo_Timer::end('UI_getObjectAsString');

            $arrOutput = array(
                'name' => $arrApp['app_name'],
                'setting' => $strVersion == 'draf' ? $arrApp['setting'] : $arrVersion['setting'],
                'content' => $strContent,
                'desc' => $strVersion == 'draf' ? $arrApp['description'] : $arrVersion['description'],
            );
            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $arrOutput);

        } catch (Util_Exception $e) {

            Bingo_Log::warning('errno: ' . $e->getCode() . ', errmsg: ' . $e->getMessage());
            $this->_jsonRet($e->getCode());
        }

    }
    /**
     * @param $intOpUid
     * @param $strAppId
     * @return string
     * @throws Util_Exception
     */
    private function _getAppActiveVer($intUserId, $intAppId)
    {
        $arrParams = array(
            //'user_id' => $intUserId,
            'app_id' => $intAppId,
        );
        $arrRet = Util_Rpc::callService(MODULE, 'getApp', $arrParams, 'get');
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            throw new Util_Exception($arrRet['errmsg'], $arrRet['errno']);
        }
        $arrApp = $arrRet['data'];
        if(empty($arrApp) || empty($arrApp['draft_md5'])) {
            $strFileName =  '';
        } else {
            $strFileName = Lib_Common::getContentFileName($intAppId, 'draft', $arrApp['draft_md5']);
        }

        return array(
            'app_name' => strval($arrApp['name']),
            'active_ver' => strval($arrApp['active_ver']),
            'setting' => strval($arrApp['setting']),
            'file_name' => $strFileName,
            'description' => strval($arrApp['description']),
        );
    }

    /**
     * @param $intOpUid
     * @param $strAppId
     * @param $strVersion
     * @return string
     * @throws Util_Exception
     */
    private function _getVersionInfo($intAppId, $strVersion)
    {
        $arrParams = array(
            'app_id' => $intAppId,
            'version' => $strVersion,
        );
        $arrRet = Util_Rpc::callService(MODULE, 'getVersion', $arrParams, 'get');
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            throw new Util_Exception($arrRet['errmsg'], $arrRet['errno']);
        }
        $arrVersion = $arrRet['data'][0];

        if(empty($arrVersion) || empty($arrVersion['content_md5'])) {
            $strFileName =  '';
        } else {
            $strFileName = Lib_Common::getContentFileName($intAppId, $arrVersion['version'], $arrVersion['content_md5']);
        }

        return array(
            'setting' => !empty($arrVersion) ? $arrVersion['setting'] : '',
            'file_name' => $strFileName,
        );
    }  
}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
