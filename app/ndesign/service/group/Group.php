<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file Group.php
 *
 * <AUTHOR>
 * @date 2017/11/16 19:43:04
 * @brief
 *
 **/
class Service_Group_Group extends Lib_ServiceBase
{
    /**
     * @param array $arrInput
     * @return array [id]
     */
    public static function createGroup($arrInput)
    {
        $arrNeedCheck = array(
            'user_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
            'name' => array('type' => 'string', 'default' => '', 'opt' => Util_Param::OPT_EQ),
        );

        if (!Util_Param::checkInput($arrInput, $arrNeedCheck) ||
        !in_array($arrInput['access_level'], array(Lib_Define::GROUP_ACL_PRIVATE, Lib_Define::GROUP_ACL_PUBLIC)) ||
        !preg_match("/^[a-zA-Z0-9_\-]{3,}$/", $arrInput['domain'])) {
            Bingo_Log::warning('error param.');

            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $conds = array();
        Util_Param::getString($conds, $arrInput, 'domain');

        $arrServiceInput = array(
            'cond' => $conds,
        );

        $arrServiceOutput = Dl_Group_Group::select($arrServiceInput);

        if (!Util_Ret::checkDlOutput($arrServiceInput, $arrServiceOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrSelectResult = $arrServiceOutput['data'][0];
        // $intAppId = $arrApp['app_id'];
        // 判断是否存在domain
        if (!empty($arrSelectResult) && Lib_Define::APP_ACL_DISABLE != $arrSelectResult['access_level']) {
            $errora = '已存在domain';
            Bingo_Log::warning('已存在domain');

            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, $errora);
        }
        // return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, '已存在domain');

        // 存储group
        $arrDlInput = array(
            'field' => array(
                //'app_type' => $intAppType,
                'name' => strval($arrInput['name']),
                'intro' => strval($arrInput['intro']),
                'domain' => strval($arrInput['domain']),
                'cover' => strval($arrInput['cover']),
                'access_level' => intval($arrInput['access_level']),
                'create_time' => time(),
                'update_time' => time(),
            ),
        );

        try {
            Dl_Group_Group::_startTrans();

            if (!empty($arrSelectResult)) {
                $arrDlInput['cond']['id'] = $arrSelectResult['id'];
                $arrDlOutput = Dl_Group_Group::update($arrDlInput);
            } else {
                $arrDlOutput = Dl_Group_Group::insert($arrDlInput);
            }

            if (!Util_Ret::checkDlOutput($arrDlInput, $arrDlOutput)) {
                return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }

            $groupId = !empty($arrSelectResult) ? $arrSelectResult['id'] : $arrDlOutput['data'];

            $arrDlGroupUser = array(
                'group_id' => intval($groupId),
                'user_id' => intval($arrInput['user_id']),
                'role' => Lib_Define::GROUP_USER_ROLD_AUTHOR,
                );

            $arrDlGroupUserOutput = Dl_Group_User::addGroupUser($arrDlGroupUser);
            Bingo_Log::warning(print_r($arrDlGroupUserOutput, 1));
            if (Tieba_Errcode::ERR_SUCCESS != $arrDlGroupUserOutput['errno']) {
                Bingo_Log::warning('addGroupUser fail, input='.serialize($arrDlGroupUser).',ret='.serialize($arrDlGroupUserOutput));
                Dl_Group_Group::_rollbackTrans();

                return $arrDlGroupUserOutput;
            }

            Dl_Group_Group::_commitTrans();

            $arrOutput = array(
                'id' => intval($groupId),
            );

            return Util_Ret::succRet($arrOutput);
        } catch (Util_Exception $e) {
            Bingo_Log::warning('.......');

            Bingo_Log::warning('errno: '.$e->getCode().', errmsg: '.$e->getMessage());
            Dl_Group_Group::_rollbackTrans();

            return Util_Ret::errRet($e->getCode(), $e->getMessage());
        }
    }

    /**
     * @param array $arrInput
     * @return array groupinfo
     */
    public static function getGroupInfo($arrInput)
    {
        // $arrInput['user_id'] = 123333312;
        // $arrInput['domain'] = '1514854070domain';
        $arrNeedCheck = array(
            'user_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
        );

        if (!Util_Param::checkInput($arrInput, $arrNeedCheck) ||
        !preg_match("/^[a-zA-Z0-9_\-]{3,}$/", $arrInput['domain'])) {
            Bingo_Log::warning('error param.');

            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $conds = array();
        Util_Param::getString($conds, $arrInput, 'domain');

        $arrServiceInput = array(
            'cond' => $conds,
        );

        $arrServiceOutput = Dl_Group_Group::select($arrServiceInput);

        if (!Util_Ret::checkDlOutput($arrServiceInput, $arrServiceOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrGroupSelectResult = $arrServiceOutput['data'][0];
        $accessGroupLevel = $arrGroupSelectResult['access_level'];
        if (empty($arrGroupSelectResult) || (Lib_Define::GROUP_ACL_DISABLE == $accessGroupLevel)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DIFANG_USER_GROUP_NOT_EXISTS);
        }

        // 组用户 权限检查
        $arrDlInput = array(
            'cond' => array(
                'access_level' => Lib_Define::ACL_ABLE,
                'group_id' => intval($arrGroupSelectResult['id']),
                'user_id' => $arrInput['user_id'],
            ),
        );
        $arrDlOutput = Dl_Group_User::select($arrDlInput);

        if (!Util_Ret::checkDlOutput($arrDlInput, $arrDlOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        if (Lib_Define::GROUP_ACL_PRIVATE == $accessGroupLevel) {
            if (empty($arrDlOutput['data'])) {
                return Util_Ret::errRet(Tieba_Errcode::ERR_DIFANG_USER_NO_RIGHTS, '没有权限');
            }
        }
        if (empty($arrDlOutput['data'])) {
            $arrGroupSelectResult['own'] = 0;
        } else {
            $arrGroupSelectResult['own'] = 1;
        }

        return Util_Ret::succRet($arrGroupSelectResult);
    }

    /**
     * @param array $arrInput
     * @return array 
     */
    public static function getGroupList($arrInput)
    {
        // todo联表查询

        $arrNeedCheck = array(
            'user_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
        );
        if (!Util_Param::checkInput($arrInput, $arrNeedCheck) ||
            !in_array($arrInput['own'], array(0, 1))) {
            Bingo_Log::warning('error param.');

            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrInput['orderby'] = array(
            'field' => 'id',
            'sort' => 'DESC',
        );
        $strAppend = self::buildAppend($arrInput);
        if ('' == $strAppend) {
            Bingo_Log::warning('sql spell fail.');

            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'append' => $strAppend,
        );

        // 拥有的组列表
        if (1 == $arrInput['own']) {
            Util_Param::getInteger($arrConds, $arrInput, 'user_id');
            $arrDlInput['cond']['access_level'] = Lib_Define::ACL_ABLE;
            Util_Param::getInteger($arrDlInput['cond'], $arrInput, 'user_id');
            $arrDlOutput = Dl_Group_User::select($arrDlInput);
            if (!Util_Ret::checkDlOutput($arrDlInput, $arrDlOutput)) {
                return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            $arrGroupUserDlData = $arrDlOutput['data'];
            if (empty($arrGroupUserDlData)) {
                return Util_Ret::succRet([]);
            }

            $arrDlInput['cond'] = array();
            Util_Param::getInteger($arrDlInput['cond'], array(
                'access_level' => array(Lib_Define::GROUP_ACL_PRIVATE, Lib_Define::GROUP_ACL_PUBLIC),
            ), 'access_level');
            
            $arrGroupId = array();
            foreach ($arrGroupUserDlData as $value) {
                $arrGroupId[] = $value['group_id'];
            }
            Util_Param::getInteger($arrDlInput['cond'], array('id' => $arrGroupId), 'id');


            $arrDlOutput = Dl_Group_Group::select($arrDlInput);
            if (!Util_Ret::checkDlOutput($arrDlInput, $arrDlOutput)) {
                return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }

            return Util_Ret::succRet($arrDlOutput['data']);
        }

        $arrDlInput['cond']['access_level'] = Lib_Define::GROUP_ACL_PUBLIC;
        $arrDlOutput = Dl_Group_Group::select($arrDlInput);

        if (!Util_Ret::checkDlOutput($arrDlInput, $arrDlOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return Util_Ret::succRet($arrDlOutput['data']);
    }

    /**
     * @param array $arrInput
     * @return array [num]
     */
    public static function getGroupCount($arrInput)
    {
        $arrNeedCheck = array(
            'user_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
        );
        if (!Util_Param::checkInput($arrInput, $arrNeedCheck) ||
            !in_array($arrInput['own'], array(0, 1))) {
            Bingo_Log::warning('error param.');

            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 私有查询
        if (1 == $arrInput['own']) {
            Util_Param::getInteger($arrConds, $arrInput, 'user_id');
            if (empty($arrConds)) {
                Bingo_Log::warning('sql spell fail.');

                return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $arrConds['access_level'] = Lib_Define::ACL_ABLE;
            $arrDlInput = array(
                'cond' => $arrConds,
                'field' => array(
                    'count(1) as num',
                ),
            );
            $arrDlOutput = Dl_Group_User::select($arrDlInput);
            if (!Util_Ret::checkDlOutput($arrDlInput, $arrDlOutput)) {
                return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }

            return Util_Ret::succRet($arrDlOutput['data'][0]['num']);
        }

        $arrDlInput = array(
            'cond' => array(
                'access_level' => Lib_Define::GROUP_ACL_PUBLIC,
            ),
            'field' => array(
                'count(1) as num',
            ),
        );

        $arrDlOutput = Dl_Group_Group::select($arrDlInput);
        if (!Util_Ret::checkDlOutput($arrDlInput, $arrDlOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return Util_Ret::succRet($arrDlOutput['data'][0]['num']);
    }

    /**
     * @param array $arrInput
     * @return array 
     */
    public static function handleGroup($arrInput)
    {
        $arrNeedCheck = array(
            'user_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
            'group_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
        );

        if (!Util_Param::checkInput($arrInput, $arrNeedCheck) ||
        !in_array($arrInput['action'], array('in', 'out', 'delete'))) {
            Bingo_Log::warning('error param.');

            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        switch ($arrInput['action']) {
            case 'in':

       
                $result = self::_joinGroup($arrInput);;
                break;
            case 'out':
                $result = Dl_Group_User::delGroupUser(
                    array(
                    'group_id' => intval($arrInput['group_id']),
                    'user_id' => intval($arrInput['user_id']),
                    )
                );
                break;
            case 'delete':
                $result = self::_delGroup($arrInput);
                break;
        }

        return $result;
    }

    /**
     * getInviteCode
     *
     * @param [type] $arrInput [group_id, user_id]
     * @return void
     */
    public static function getInviteCode($arrInput){

        $arrNeedCheck = array(
            'user_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
            'group_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
        );

        if (!Util_Param::checkInput($arrInput, $arrNeedCheck)) {
            Bingo_Log::warning('error param.');
            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $userCacheCode = $arrInput['user_id'].$arrInput['group_id'].'inviteGroup';
        $inviteCode = Dl_Group_Group::getCacheObj()->get(array('key'=>$userCacheCode));
        if( $inviteCode && Dl_Group_Group::getCacheObj()->get(array('key'=>$inviteCode)) ){
            return Util_Ret::succRet(array('invite_code'=>  $inviteCode )); 
        }


        // === 查询群组 ===
        $groupRes = self::_getGroup(array(
            'id' => $arrInput['group_id'],
        ));

        if( Tieba_Errcode::ERR_SUCCESS != $groupRes['errno'] ){
            return $groupRes;
        }
        // === end 查询群组 ===

        $arrDlInput = array(
            'cond'=>array(
                'group_id'=> $groupRes['data']['id'],
                'user_id'=> $arrInput['user_id'],
                'access_level'=> Lib_Define::ACL_ABLE
                )
            );
        
        $arrDlOutput = Dl_Group_User::select($arrDlInput);
        
        if (!Util_Ret::checkDlOutput($arrDlInput, $arrDlOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrDlData = $arrDlOutput['data'][0];
        if (empty($arrDlData) || ($arrDlData['role'] != Lib_Define::GROUP_USER_ROLD_ADMIN && $arrDlData['role'] != Lib_Define::GROUP_USER_ROLD_AUTHOR) ) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_IM_NO_PERMISSION, '没有权限');
        }
        
        $cacheData = Bingo_String::array2json(array(
            'user_id'=>$arrInput['user_id'],
            'group_info' => $groupRes['data'],
            'time' => time()
        ));
        
        $inviteCode = md5($cacheData);
        Bingo_Log::warning($inviteCode.'.....');

        $inviteCodeRes = Dl_Group_Group::getCacheObj()->add(array(
            'key'=>$inviteCode, 
            'value'=>$cacheData,
            // 'expire'=> 20
        ));
        $userCacheCodeRes = Dl_Group_Group::getCacheObj()->add(array(
            'key'=>$userCacheCode, 
            'value'=>$inviteCode,
            // 'expire'=> 20
        ));

        if( !$inviteCodeRes || !$userCacheCodeRes ){
            return Util_Ret::errRet(); 
        }
        
        
        return Util_Ret::succRet(array('invite_code'=>  $inviteCode )); 
    }

    /**
     * inviteJoinGroup
     *
     * @param [type] $arrInput [invite_code, user_id ]
     * @return void
     */
    public static function inviteJoinGroup($arrInput){

        $arrNeedCheck = array(
            'user_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
            'invite_code' => array('type' => 'string', 'default' => '', 'opt' => Util_Param::OPT_EQ),
        );

        if (!Util_Param::checkInput($arrInput, $arrNeedCheck)) {
            Bingo_Log::warning('error param.');
            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $inviteCode = $arrInput['invite_code'];
        $cacheObj = Dl_Group_Group::getCacheObj();

        $inviteInfo = $cacheObj->get(array(
            'key'=> $inviteCode
        ));
        
        $arrInviteInfo = Bingo_String::json2array($inviteInfo);
        if( empty($arrInviteInfo) ){
            return Util_Ret::errRet(Tieba_Errcode::VERIFICATION_VCODE_EXPIRED); 
        }

        // $cacheObj->del(array('key'=>$inviteCode));

        $joinRes = self::_joinGroup(array(
            'group_id'=> $arrInviteInfo['group_info']['id'],
            'user_id'=> $arrInput['user_id']
        ), true);



        return $joinRes;
    }

    /**
     * joinGroup
     *
     * @param [type] $arrInput  [group_id,user_id]
     * @return array [domain]
     */
    private static function _joinGroup($arrInput, $isInvite = false)
    {
        // === 查询群组 ===
        $arrDlInput = array(
            'cond' => array(
                // 'access_level' => Lib_Define::GROUP_ACL_PUBLIC,
                'id' => $arrInput['group_id'],
            ),
        );
        $arrDlOutput = Dl_Group_Group::select($arrDlInput);
        if (!Util_Ret::checkDlOutput($arrDlInput, $arrDlOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrDlResult = $arrDlOutput['data'][0];
        if (empty($arrDlResult) || Lib_Define::GROUP_ACL_DISABLE == $arrDlResult['access_level']) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DIFANG_USER_GROUP_NOT_EXISTS, '群组不存在');
        }
        // === end 查询群组 ===

        // 非邀请，
        if (!$isInvite && Lib_Define::GROUP_ACL_PUBLIC != $arrDlResult['access_level']) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_IM_NO_PERMISSION, '没有权限');
        }

        $arrServiceUserRes =  Dl_Group_User::addGroupUser(
            array(
            'group_id' => intval($arrInput['group_id']),
            'user_id' => intval($arrInput['user_id']),
            'role' => Lib_Define::GROUP_USER_ROLD_NOMAL,
            )
        );

        if(Tieba_Errcode::ERR_SUCCESS != $arrServiceUserRes['errno']) {
           return $arrServiceUserRes;
        }

        return Util_Ret::succRet(array(
            'domain'=> $arrDlResult['domain']
        ));

    }

    /**
     * @param array $arrInput
     * @return array 
     */
    private static function _delGroup($arrInput)
    {
        $conds = array();
        Util_Param::getInteger($conds, $arrInput, 'group_id');
        Util_Param::getInteger($conds, $arrInput, 'user_id');

        $arrServiceInput = array(
            'cond' => $conds,
        );

        $arrSelectGroupUserInput = array(
            'cond' => array(
                'group_id' => intval($arrInput['group_id']),
                'user_id' => intval($arrInput['user_id']),
                'access_level' => Lib_Define::ACL_ABLE,
            ),
        );
        $arrSelectGroupUserOutput = Dl_Group_User::select($arrSelectGroupUserInput);
        if (!Util_Ret::checkDlOutput($arrSelectGroupUserInput, $arrSelectGroupUserOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $selectGroupUserResult = $arrSelectGroupUserOutput['data'][0];

        if (empty($selectGroupUserResult)) {
            // todo 不是成员
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, '未有权限');
        }
        if (Lib_Define::GROUP_USER_ROLD_ADMIN != $selectGroupUserResult['role'] && Lib_Define::GROUP_USER_ROLD_AUTHOR != $selectGroupUserResult['role']) {
            // todo 未有管理权限
            Bingo_Log::warning($selectGroupUserResult['role'].'121212');

            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, '未有权限1');
        }

        $conds = array(
            'id' => intval($arrInput['group_id']),
        );
        $arrDlInput = array(
            'field' => array(
                'access_level' => Lib_Define::GROUP_ACL_DISABLE,
                'update_time' => time(),
            ),
            'cond' => $conds,
        );
        $arrDlOutput = Dl_Group_Group::update($arrDlInput);

        if (!Util_Ret::checkDlOutput($arrDlInput, $arrDlOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $conds = array();
        Util_Param::getInteger($conds, $arrInput, 'group_id');
        $arrdelGroupUserDelOutput = Dl_Group_User::update(array(
            'field' => array(
                'access_level' => Lib_Define::ACL_DISABLE,
                'update_time' => time(),
            ),
            'cond' => array(
                'group_id' => intval($arrInput['group_id']),
            ),
        ));
        if (Tieba_Errcode::ERR_SUCCESS != $arrGroupUserDelResult['errno']) {
            // todo错误回滚
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return Util_Ret::succRet();
    }

    /**
     * _getGroup
     *
     * @param [type] $conds 
     * @return void
     */
    private static function _getGroup($conds)
    {
        $arrDlInput = array(
            'cond' => $conds,
        );
        $arrDlOutput = Dl_Group_Group::select($arrDlInput);
        if (!Util_Ret::checkDlOutput($arrDlInput, $arrDlOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrDlResult = $arrDlOutput['data'][0];
        if (empty($arrDlResult) || Lib_Define::GROUP_ACL_DISABLE == $arrDlResult['access_level']) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DIFANG_USER_GROUP_NOT_EXISTS, '群组不存在');
        }

        return Util_Ret::succRet($arrDlResult);
    }

    /**
     * @param array $arrInput
     * @return array 
     */
    public static function getGroupUserList($arrInput)
    {
        $arrNeedCheck = array(
            'user_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
            'group_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
        );
        if (!Util_Param::checkInput($arrInput, $arrNeedCheck)) {
            Bingo_Log::warning('error param.');

            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrInput['orderby'] = array(
            'field' => 'id',
            'sort' => 'ASC',
        );
        $strAppend = self::buildAppend($arrInput);
        if ('' == $strAppend) {
            Bingo_Log::warning('sql spell fail.');

            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'append' => $strAppend,
        );

        $arrDlInput['cond'] = array(
            'access_level' => Lib_Define::ACL_ABLE,
            'group_id' => $arrInput['group_id'],
        );
        $arrDlOutput = Dl_Group_User::select($arrDlInput);
        if (!Util_Ret::checkDlOutput($arrDlInput, $arrDlOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        if (empty($arrDlOutput['data'])) {
            return Util_Ret::succRet([]);
        }

        // ====== 获取用户信息server
        $userServiceInput = array(
            'user_id' => array_column($arrDlOutput['data'], 'user_id'),
            'get_icon' => 1,
            'need_follow_info' => 1,
            'need_pass_info' => 1,
        );
        $userServiceOuts = Util_Rpc::callService('user', 'mgetUserData', $userServiceInput);
        // === end === 获取用户信息server
        Bingo_Log::warning(print_r($userServiceOuts,1).'userServiceOuts123213');
        $arrCountInput = array(
            'cond' => $arrDlInput['cond'],
            'field' => array(
                'count(1) as num',
            ),
        );

        $arrCountOutput = Dl_Group_User::select($arrCountInput);
        if (!Util_Ret::checkDlOutput($arrCountInput, $arrCountOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrRet = array(
            'count' => $arrCountOutput['data'][0]['num'],
            'list' => [],
        );

        foreach ($arrDlOutput['data'] as $value) {
            $data = array_merge(
                $userServiceOuts['user_info'][$value['user_id']],
                 $value
            );
            $data['portrait'] = Tieba_Ucrypt::encode ($data['user_id'],$data['user_name']);
            $arrRet['list'][] = $data;
        }
        // 获取用户信息

        return Util_Ret::succRet($arrRet);
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
