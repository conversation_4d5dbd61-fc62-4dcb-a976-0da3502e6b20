<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file App.php
 *
 * <AUTHOR>
 * @date 2017/11/16 19:43:04
 * @brief
 *
 **/
class Service_App_App extends Lib_ServiceBase
{
    /**
     * @param array $arrInput
     * @return array 
     */
    public static function createApp($arrInput)
    {
        $arrNeedCheck = array(
            'user_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
            'group_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
            'name' => array('type' => 'string', 'default' => '', 'opt' => Util_Param::OPT_EQ),
        );
        if (!Util_Param::checkInput($arrInput, $arrNeedCheck)) {
            Bingo_Log::warning('error param.');

            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        /**  =========== 群组权限检查 start =========== */
        $arrDlInput = array(
            'cond' => array(
                'group_id' => $arrInput['group_id'],
                'user_id' => $arrInput['user_id'],
                )
        );
        $arrDlOutput = Dl_Group_User::select($arrDlInput);
        
        if (!Util_Ret::checkDlOutput($arrDlInput, $arrDlOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        
        if( empty($arrDlOutput['data'][0]) ){
            return Util_Ret::errRet(Tieba_Errcode::ERR_DIFANG_MSG_NOT_MEMBER,'请先加入群组');
        }
        $arrDlInput = array(
            'cond' => array(
                'id' => $arrInput['group_id']
                )
        );
        $arrDlOutput = Dl_Group_Group::select($arrDlInput);
        if (!Util_Ret::checkDlOutput($arrDlInput, $arrDlOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrDlData = $arrDlOutput['data'][0];
        if( empty($arrDlData) || $arrDlData['access_level'] == Lib_Define::GROUP_ACL_DISABLE){
            return Util_Ret::errRet(Tieba_Errcode::ERR_DIFANG_USER_GROUP_NOT_EXISTS,'群组不存在');
        }

        /**  =========== 群组权限检查 end =========== */
        
        $conds = array();
        Util_Param::getInteger($conds, $arrInput, 'user_id');
        Util_Param::getInteger($conds, $arrInput, 'group_id');
        Util_Param::getString($conds, $arrInput, 'name');

        $arrServiceInput = array(
            'cond' => $conds,
        );
        $arrServiceOutput = Dl_App_App::select($arrServiceInput);
        if (!Util_Ret::checkDlOutput($arrServiceInput, $arrServiceOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrApp = $arrServiceOutput['data'][0];
        $intAppId = $arrApp['app_id'];

        if (!empty($arrApp) && Lib_Define::APP_ACL_DISABLE != $arrApp['access_level']) {
            //Util_Crypt::encodeAppId($arrApp['app_id'], $arrApp['app_type'], $strAppId);
            $arrOutput = array(
                'app_id' => $intAppId,
            );

            return Util_Ret::succRet($arrOutput);
        }
        $arrDlInput = array(
            'field' => array(
                //'app_type' => $intAppType,
                'name' => strval($arrInput['name']),
                'description' => strval($arrInput['desc']),
                'user_id' => intval($arrInput['user_id']),
                'group_id' => intval($arrInput['group_id']),
                'create_time'=> time(),
                'update_time'=> time(),
                'access_level' => Lib_Define::APP_ACL_ABLE,
            ),
        );
        if (!empty($arrApp)) {
            $arrDlInput['cond']['app_id'] = $arrApp['app_id'];
            $arrDlOutput = Dl_App_App::update($arrDlInput);
        } else {
            $arrDlOutput = Dl_App_App::insert($arrDlInput);
        }
        if (!Util_Ret::checkDlOutput($arrDlInput, $arrDlOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        //Bingo_Log::warning(print_r(0));
        $intAppId = !empty($arrApp) ? $arrApp['app_id'] : $arrDlOutput['data'];

        //Lib_Crypt::encodeAppId($intAppId, $intAppType, $strAppId);
        $arrOutput = array(
            'app_id' => intval($intAppId),
        );

        return Util_Ret::succRet($arrOutput);
    }

    /**
     * @param array $arrInput
     * @return array 
     */
    public static function getApp($arrInput)
    {
        Util_Param::getString($arrConds, $arrInput, 'app_id');
        // Util_Param::getInteger($arrConds, $arrInput, 'user_id');

        if (empty($arrConds)) {
            Bingo_Log::warning('param error and input . '.serialize($arrInput));

            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        $appCheckData = self::_getAppContent($arrInput['app_id'], $arrInput['user_id']);

        return $appCheckData;
    }

    /**
     * @param array $arrInput
     * @return array 
     */
    public static function saveApp($arrInput)
    {
        Util_Param::getInteger($arrConds, $arrInput, 'app_id');
        // Util_Param::getInteger($arrConds, $arrInput, 'user_id');

        Util_Param::getString($arrFields, $arrInput, 'name');
        Util_Param::getString($arrFields, $arrInput, 'description');
        Util_Param::getString($arrFields, $arrInput, 'draft_md5');
        Util_Param::getInteger($arrFields, $arrInput, 'draft_size');
        Util_Param::getString($arrFields, $arrInput, 'setting');
        Util_Param::getString($arrFields, $arrInput, 'cover');
        Util_Param::getString($arrFields, $arrInput, 'extra_md5');
        Util_Param::getInteger($arrFields, $arrInput, 'extra_size');

        Bingo_Log::warning(print_r($arrFields, 1));
        if (empty($arrFields) || empty($arrConds)) {
            Bingo_Log::warning('sql get data fail');

            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 权限检查
        $checkPowerAppData = self::_checkAdminPower($arrInput['app_id'], $arrInput['user_id']);
        if($checkPowerAppData['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            return $checkPowerAppData;
        }

        $arrFields['update_time'] = time();
        if( $checkPowerAppData['data']['user_id'] == $arrInput['user_id'] && isset($arrInput['admin_private']) ){
            if ( (int)$arrInput['admin_private'] === 0){
                $arrFields['admin_private'] = Lib_Define::APP_ADMIN_PUBLIC;
            }elseif( (int)$arrInput['admin_private'] ===1 ){
                $arrFields['admin_private'] = Lib_Define::APP_ADMIN_PRIVATE;
            }
        }
        $arrDlInput = array(
            'field' => $arrFields,
            'cond' => $arrConds,
        );
        $arrDlOutput = Dl_App_App::update($arrDlInput);
        if (!Util_Ret::checkDlOutput($arrDlOutput, $arrDlOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return Util_Ret::succRet();
    }

    /**
     * @param array $arrInput
     * @return array 
     */
    public static function getAppList($arrInput)
    {
        $arrNeedCheck = array(
            'user_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
            'group_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
            //'app_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
        );
        if (!Util_Param::checkInput($arrInput, $arrNeedCheck)) {
            Bingo_Log::warning('error param.');

            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrInput['orderby'] = array(
            'field'=>'update_time',
            'sort'=>'DESC'
        );
        $strAppend = self::buildAppend($arrInput);
        // Util_Param::getInteger($arrConds, $arrInput, 'user_id');
        Util_Param::getInteger($arrConds, $arrInput, 'group_id');

        if (empty($arrConds) || '' == $strAppend) {
            Bingo_Log::warning('sql spell fail.');

            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrDlInput = array(
            'cond' => $arrConds,
            'append' => $strAppend,
        );
        $arrDlInput['cond']['access_level'] = Lib_Define::APP_ACL_ABLE;
        $arrDlOutput = Dl_App_App::select($arrDlInput);
        if (!Util_Ret::checkDlOutput($arrDlInput, $arrDlOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return Util_Ret::succRet($arrDlOutput['data']);
    }

    /**
     * @param array $arrInput
     * @return array 
     */
    public static function getAppCount($arrInput)
    {
        $arrNeedCheck = array(
            // 'user_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
            'group_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
        );
        if (!Util_Param::checkInput($arrInput, $arrNeedCheck)) {
            Bingo_Log::warning('error param.');

            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        Util_Param::getInteger($arrConds, $arrInput, 'user_id');
        Util_Param::getInteger($arrConds, $arrInput, 'group_id');
        if (empty($arrConds)) {
            Bingo_Log::warning('sql spell fail.');

            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrDlInput = array(
            'cond' => $arrConds,
            'field' => array(
                'count(1) as num',
            ),
        );
        $arrDlInput['cond']['access_level'] = Lib_Define::APP_ACL_ABLE;
        $arrDlOutput = Dl_App_App::select($arrDlInput);
        if (!Util_Ret::checkDlOutput($arrDlInput, $arrDlOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return Util_Ret::succRet($arrDlOutput['data'][0]['num']);
    }

    /**
     * @param array $arrInput
     * @return array 
     */
    public static function delApp($arrInput)
    {
        Util_Param::getInteger($conds, $arrInput, 'app_id');
        Util_Param::getInteger($conds, $arrInput, 'user_id');

        if (empty($conds)) {
            Bingo_Log::warning('sql spell fail');

            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 权限检查
        $checkPowerAppData = self::_checkAdminPower($arrInput['app_id'], $arrInput['user_id']);
        if($checkPowerAppData['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            return $checkPowerAppData;
        }

        $arrDlInput = array(
            'field' => array(
                'access_level' => Lib_Define::APP_ACL_DISABLE,
                'update_time' => time(),
            ),
            'cond' => $conds,
        );
        $arrDlOutput = Dl_App_App::update($arrDlInput);
        if (!Util_Ret::checkDlOutput($arrDlOutput, $arrDlOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return Util_Ret::succRet();
    }

    /**
     * @param array $arrInput
     * @return array 
     */
    public static function changeAppGroup($arrInput)
    {
        $arrNeedCheck = array(
            'user_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
            // 'group_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
            're_group_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
            // 're_user_id' => array('type' => 'int', 'default' => 0, 'opt' => Util_Param::OPT_LEQ),
        );
        Bingo_Log::warning(print_r($arrInput,1).'dsfds');
        if (!Util_Param::checkInput($arrInput, $arrNeedCheck)) {
            Bingo_Log::warning('error param.');

            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrDlInput = array(
            'field' => array(
                'update_time' => time(),
                'group_id' => $arrInput['re_group_id'],
                'user_id'=> $arrInput['re_user_id'] ? $arrInput['re_user_id'] : $arrInput['user_id']
            ),
            'cond' => array(
                'user_id'=> $arrInput['user_id'],
                'group_id'=> $arrInput['group_id']
            ),
        );

        if( $arrInput['app_id'] > 0 ){
            $arrDlInput['cond']['app_id'] = $arrInput['app_id'];
        }

        $arrDlOutput = Dl_App_App::update($arrDlInput);
        if (!Util_Ret::checkDlOutput($arrDlOutput, $arrDlOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return Util_Ret::succRet();
    }

    /**
     * 获取app内容
     * @param string $appId
     * @param string $userId [不传则不验证用户权限]
     * @return $appData
     */
    private static function _getAppContent($appId, $userId){

        $arrDlInput = array(
            'cond' => array('app_id'=>$appId)
        );

        $arrDlOutput = Dl_App_App::select($arrDlInput);
        if (!Util_Ret::checkDlOutput($arrDlOutput, $arrDlOutput)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $appData = $arrDlOutput['data'][0];
        if( empty($appData) ){
            return Util_Ret::errRet(Tieba_Errcode::ERROR_MIS_DATA_NOT_EXSITS);
        }

        if( !isset($userId) ){
            return Util_Ret::succRet($appData);
        }
        $arrDlInput = array(
            'cond'=> array(
                'group_id'=> $appData['group_id'],
                'user_id' => $userId,
                'access_level' => Lib_Define::ACL_ABLE
            )
        );
        $arrDlOutput = Dl_Group_User::select($arrDlInput);
        if (!Util_Ret::checkDlOutput($arrDlInput, $arrDlOutput )) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        if( empty($arrDlOutput['data'][0]) ){
            return Util_Ret::errRet(Tieba_Errcode::ERR_DIFANG_MSG_NOT_MEMBER, '未加入此群组');
        }
        return Util_Ret::succRet($appData);
    }

    /**
     * 检查用户对app 的访问以及管理权限
     * @param string $appId
     * @param string $userId [不传则不验证用户权限]
     * @return $appData
     */
    private static function _checkAdminPower($appId, $userId){
        $appData = self::_getAppContent($appId, $userId);
        if($appData['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            return $appData;
        }
        if( $appData['data']['admin_private'] == Lib_Define::APP_ADMIN_PRIVATE && $appData['data']['user_id'] !== $userId  ){
            return Util_Ret::errRet(Tieba_Errcode::ERR_IM_NO_PERMISSION, '没有权限');
        }
        return $appData;
    }

}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
