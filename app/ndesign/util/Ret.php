<?php
define('debug', 0);
class Util_Ret{
    public static function errRet($errno, $errmsg = '', $data = array()){
        if($errmsg == ''){
            $errmsg = Tieba_Error::getErrmsg($errno);
        }
        return array(
            'errno' => $errno,
            'errmsg' => $errmsg,
            'data' => $data,
        );
    }

    public static function succRet($data = array()){
        $errno = Tieba_Errcode::ERR_SUCCESS;
        $errmsg = Tieba_Error::getErrmsg($errno);
        return array(
            'errno' => $errno,
            'errmsg' => $errmsg,
            'data' => $data,
        );
    }

    public static function checkServiceOutput($arrInput, $arrOutput, $strMethod=''){
        if(debug){
            Bingo_Log::warning($strMethod . '---start----------------');
            Bingo_Log::warning(print_r($arrInput, 1));
            Bingo_Log::warning(print_r($arrOutput, 1));
            Bingo_Log::warning($strMethod . '----end-----------------');
        }
        $arrTrace =debug_backtrace();
        $strFile = $arrTrace[0]['file'];
        $strFunction = $strMethod == '' ? $arrTrace[0]['function'] : $strMethod;
        $strRow = $arrTrace[0]['line'];
        if($arrOutput === false){
            $strLog = sprintf("file:{$strFile} function:{$strFunction} row: {$strRow} call service fail. input{%s}", serialize($arrInput));
            Bingo_Log::fatal($strLog);
            return false;
        }else if($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            $strLog = sprintf("file:{$strFile} function:{$strFunction} row:{$strRow} call service fail. input:{%s} output:{%s}", serialize($arrInput), serialize($arrOutput));
            Bingo_Log::warning($strLog);
            return false;
        }
        return true;
    }

    public static function checkDlOutput($arrInput, $arrOutput){
        if(debug){
            Bingo_Log::warning($strMethod . '---start----------------');
            Bingo_Log::warning(print_r($arrInput, 1));
            Bingo_Log::warning(print_r($arrOutput, 1));
            Bingo_Log::warning($strMethod . '----end-----------------');
        }
        $arrTrace =debug_backtrace();
        $strFile = $arrTrace[0]['file'];
        $strFunction = $strMethod == '' ? $arrTrace[0]['function'] : $strMethod;
        $strRow = $arrTrace[0]['line'];
        if($arrOutput === false){
            $strLog = sprintf("file:{$strFile} function:{$strFunction} row: {$strRow} call service fail. input{%s}", serialize($arrInput));
            Bingo_Log::fatal($strLog);
            return false;
        }else if($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            $strLog = sprintf("file:{$strFile} function:{$strFunction} row:{$strRow} call service fail. input:{%s} output:{%s}", serialize($arrInput), serialize($arrOutput));
            Bingo_Log::warning($strLog);
            return false;
        }
        return true;        
    }
}
?>