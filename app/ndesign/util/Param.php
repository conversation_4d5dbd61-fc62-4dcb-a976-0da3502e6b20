<?php


class Util_Param {
    CONST OPT_LEQ = '<=';
    CONST OPT_LE = '<';
    const OPT_IS_ARRAY = 'is_array';
    const OPT_EQ = '==';
    /**
     * @param  [type] $strSql [description]
     * @return [type]         [description]
     */
    public static function checkInput($arrInput, $arrCheckInput){
        foreach($arrCheckInput as $key => $value){
            if(!isset($arrInput[$key])){
                return false;
            }
            if($value['type'] == 'int' && $value['opt'] == self::OPT_LEQ){
                if($arrInput[$key] <= $value['default']){
                    return false;
                }
            }
            if($value['type'] == 'string' && $value['opt'] == self::OPT_EQ){
                if($arrInput[$key] == $value['default']){
                    return false;
                }
            }
            if($value['type'] == 'array' && $value['opt'] == self::OPT_IS_ARRAY){
                if(!is_array($arrInput[$key])){
                    return false;
                }
            }
        }
        return true;
    }
    /**
     * @param  [type] $strSql [description]
     * @return [type]         [description]
     */
    public static function changeIndexArrayToRelArray($arrInput, $key){
        foreach($arrInput as $item){
            $arrOutput[$item[$key]] = $item;
        }
        return $arrOutput;
    }
    
    /**
     * [getInt description]
     * @param  [type] &$conds   [description]
     * @param  [type] $condKey  [description]
     * @param  [type] $arrInput [description]
     * @param  [type] $key      [description]
     * @return [type]           [description]
     */
    public static function getInteger(&$conds, $arrInput, $key) {
        if(isset($arrInput[$key])) {
            if(!is_array($arrInput[$key])){
                $conds [$key] = (int)$arrInput[$key];
            }else{
                //$conds [$key] = sprintf("%s IN(%s)", $key, implode(',',$arrInput[$key]));
                $conds[$key] = array(
                    'quotes' => 0,
                    'opt' => 'in',
                    'val' => $arrInput[$key],
                );
            }
        }
    }
    /**
     * [getString description]
     * @param  [type] &$conds   [description]
     * @param  [type] $condKey  [description]
     * @param  [type] $arrInput [description]
     * @param  [type] $key      [description]
     * @return [type]           [description]
     */
    public static function getString(&$conds, $arrInput, $key) {
        if(isset($arrInput[$key])) {
            $conds [$key] = trim($arrInput[$key]);
        }
    }

    /**
     * [getStringLike description]
     * @param  [type] &$conds   [description]
     * @param  [type] $condKey  [description]
     * @param  [type] $arrInput [description]
     * @param  [type] $key      [description]
     * @return [type]           [description]
     */
    public static function getStringLike(&$conds, $arrInput, $key) {
        if(isset($arrInput[$key])) {
            $conds[$key] = array(
                'quotes' => 1,
                'opt' => 'like',
                'val' => trim($arrInput[$key]),
            );
            //$conds [$key . ' LIKE '] = '%' . trim($arrInput[$key]) . '%';
        }
    }
    /**
     * [getArray description]
     * @param  [type] &$conds   [description]
     * @param  [type] $condKey  [description]
     * @param  [type] $arrInput [description]
     * @param  [type] $key      [description]
     * @param  [type] $standard [description]
     * @return [type]           [description]
     */
    public static function getArray(&$conds, $arrInput, $key, $standard) {
        if(isset($arrInput[$key])) {
            if(!is_array($arrInput[$key])) {
                $arrInput[$key] = array($arrInput[$key]);
            }
            $arrItem = array();
            foreach($arrInput[$key] as $input) {
                if(isset($standard[$input])) {
                    $arrItem []= $input;
                }
            }
            if(!empty($arrItem)) {
                $conds[] = sprintf('%s IN (%s)', $key, implode(',',$arrItem));
            }
        }
    }
    /**
     * [getIds description]
     * @param  [type] &$conds   [description]
     * @param  [type] $condKey  [description]
     * @param  [type] $arrInput [description]
     * @param  [type] $key      [description]
     * @return [type]           [description]
     */
    public static function getIds(&$conds, $arrInput, $key) {
        if(isset($arrInput[$key])) {
            if(!is_array($arrInput[$key])) {
                $conds[$condKey . ' ='] = $arrInput[$key];
                return $conds;
            } else {
                $conds[] = sprintf('%s IN (%s)', $key, implode(',',$arrInput[$key]));
                return $conds;
            }
        }
        return false;
    }
    /**
     * [getRange description]
     * @param  [type] &$conds   [description]
     * @param  [type] $condKey  [description]
     * @param  [type] $arrInput [description]
     * @param  [type] $key      [description]
     * @return [type]           [description]
     */
    public static function getRange(&$conds,$arrInput, $key) {
        if(isset($arrInput[$key])){
            if(!is_array($arrInput[$key])){
                $conds[] = $key . '=' . $arrInput[$key];
            }else{
                if(isset($arrInput[$key]['max'])){
                    $conds[] = $key . '<' . $arrInput[$key]['max'];
                }
                if(isset($arrInput[$key]['min'])){
                    $conds[] = $key . '>' . $arrInput[$key]['min'];
                }
                if(isset($arrInput[$key]['maxe'])){
                    $conds[] = $key . '<=' . $arrInput[$key]['maxe'];
                }
                if(isset($arrInput[$key]['mine'])){
                    $conds[] = $key . '>=' . $arrInput[$key]['mine'];
                }
            }
        }
    }
    /**
     * [getRange description]
     * @param  [type] &$conds   [description]
     * @param  [type] $arrInput [description]
     * @param  [type] $key      [description]
     * @return [type]           [description]
     */
    public static function getTimeRange(&$conds,$arrInput, $key, &$times) {
        
        if(isset($arrInput[$key.'_to'])){
            $conds[] = $key . '<=' . $arrInput[$key.'_to'];
            if(isset($times)){
                $times[] = $arrInput[$key.'_to'];
            }
        }
        if(isset($arrInput[$key.'_from'])){
            $conds[] = $key . '>=' . $arrInput[$key.'_from'];
            if(isset($times)){
                $times[] = $arrInput[$key.'_from'];
            }
        }
                
    }
}
