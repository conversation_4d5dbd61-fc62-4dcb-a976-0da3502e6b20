<?php
/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Exception.php
 * <AUTHOR>
 * @date 2017/11/16 19:37:09
 * @brief 
 *  
 **/

class Util_Exception extends Exception{
    private $_arrData = array();

    public function __construct($strMsg = '', $intCode = 0, $arrData = array()){
        $this->_arrData = $arrData;
        parent::__construct($strMsg, $intCode);
    }

    public function getData(){
        return $this->_arrData;
    }
}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
