<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2016/7/19
 * Time: 13:41
 */
class Util_Redis {

    private static $_instances = null;
    private $_redis_name = null;
    private $_redis = null;

    /**
     * @param $strRedisName
     * @return Util_Redis
     */
    public static function getInstance($strRedisName)
    {
        if(!isset(self::$_instances[$strRedisName])) {
            self::$_instances[$strRedisName] = new self($strRedisName);
        }
        return self::$_instances[$strRedisName];
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function set($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['value'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $value = $arrInput['value'];
        $setnx = isset($arrInput['setnx'])?intval($arrInput['setnx']):0;
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;
        $serial = isset($arrInput['serial']) ? intval($arrInput['serial']) : 1;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => $key,
            'value' => $serial ? serialize($value) : $value,
        );
        if($setnx){
            $ret = $objRedis->SETNX($arrParams);
        }else{
            $ret = $objRedis->SET($arrParams);
        }
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[SET] error.[".serialize($ret)."]");
            return false;
        }

        if($expire > 0){
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return mixed|null
     */
    public function get($arrInput)
    {
        if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $serial = isset($arrInput['serial']) ? intval($arrInput['serial']) : 1;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array(
            'key' => $key,
        );
        $ret = $objRedis->GET($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[GET] error.[".serialize($ret)."]");
            return false;
        }
        $data = null;
        if(!is_null($ret['ret'][$key])){
            $data = $serial ? unserialize($ret['ret'][$key]) : $ret['ret'][$key];
        }
        return $data;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function setEx($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['value'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $value = $arrInput['value'];
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;
        $serial = isset($arrInput['serial']) ? intval($arrInput['serial']) : 1;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => $key,
            'value' => $serial ? serialize($value) : $value,
        );
        if($expire > 0){
            $arrParams['seconds'] = intval($expire);
            $ret = $objRedis->SETEX($arrParams);
        }else{
            $ret = $objRedis->SET($arrParams);
        }
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[SET] error.[".serialize($ret)."]");
            return false;
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function setKeyExpire($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['expire'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => $key,
        );
        if($expire > 0){
            $arrParams['seconds'] = intval($expire);
            $ret = $objRedis->EXPIRE($arrParams);
        } else {
            $ret = $objRedis->PERSIST($arrParams);
        }
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[EXPIRE] error.[".serialize($ret)."]");
            return false;
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function clearKey($arrInput)
    {
        if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => strval($key),
        );
        $ret = $objRedis->DEL($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[DEL] error.[".serialize($ret)."]");
            return false;
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function mclearKeys($arrInput)
    {
        if(!isset($arrInput['keys']) || !is_array($arrInput['keys'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $keys = $arrInput['keys'];
        if(empty($keys)) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array();
        foreach($keys as $key) {
            $arrParams['reqs'][] = array(
                'key' => strval($key),
            );
        }
        $ret = $objRedis->DEL($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[DEL] error.[".serialize($ret)."]");
            return false;
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function setToHash($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['field'])
            || !isset($arrInput['value'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $field = $arrInput['field'];
        $value = $arrInput['value'];
        $setnx = isset($arrInput['setnx'])?intval($arrInput['setnx']):0;
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;
        $serial = isset($arrInput['serial']) ? intval($arrInput['serial']) : 1;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => $key,
            'field' => $field,
            'value' => $serial ? serialize($value) : $value,
        );
        if($setnx) {
            $ret = $objRedis->HSET($arrParams);
        } else {
            $ret = $objRedis->HSETNX($arrParams);
        }
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HSET] error.[".serialize($ret)."]");
            return false;
        }

        if($expire > 0){
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return mixed|null
     */
    public function getFromHash($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['field'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $field = $arrInput['field'];
        $serial = isset($arrInput['serial']) ? intval($arrInput['serial']) : 1;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array(
            'key' => $key,
            'field' => $field,
        );
        $ret = $objRedis->HGET($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HGET] error.[".serialize($ret)."]");
            return false;
        }
        $data = null;
        if(!is_null($ret['ret'][$key])){
            $data = $serial ? unserialize($ret['ret'][$key]) : $ret['ret'][$key];
        }
        return $data;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function clearFromHash($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['field'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $field = $arrInput['field'];

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array(
            'key' => strval($key),
            'field' => strval($field),
        );
        $ret = $objRedis->HDEL($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HDEL] error.[".serialize($ret)."]");
            return false;
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function msetToHash($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['fvals'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $fvals = $arrInput['fvals'];
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;
        $serial = isset($arrInput['serial']) ? intval($arrInput['serial']) : 1;

        $arrFieldValues = array();
        foreach($fvals as $field=>$val) {
            $arrFieldValues[] = array(
                'field' => $field,
                'value' => $serial ? serialize($val) : $val,
            );
        }
        if(empty($arrFieldValues)) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array(
            'key' => $key,
            'fields' => $arrFieldValues,
        );
        $ret = $objRedis->HMSET($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HMSET] error.[".serialize($ret)."]");
            return false;
        }

        if($expire> 0) {
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $objRedis->EXPIRE($arrParams);
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function msetToHashByKeys($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        if(empty($arrInput['reqs'])) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrKeyReqs = array();
        foreach($arrInput['reqs'] as $req) {
            if(!isset($req['key']) || !isset($req['field']) || !isset($req['value'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $expire = isset($req['expire']) ? intval($req['expire']) : 0;
            $serial = isset($req['serial']) ? intval($req['serial']) : 1;

            $arrKeyReqs[$req['key']]['fields'][] = array(
                'field' => $req['field'],
                'value' => $serial ? serialize($req['value']) : $req['value'],
            );
            if(isset($arrKeyReqs[$req['key']]['expire'])
                && $arrKeyReqs[$req['key']]['expire'] != $expire) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrKeyReqs[$req['key']]['expire'] = $expire;
        }

        $arrSetParams = array();
        $arrExpireParams = array();
        foreach($arrKeyReqs as $key => $arrReq) {
            $arrSetParams['reqs'][] = array(
                'key' => $key,
                'fields' => $arrReq['fields'],
            );
            if($arrReq['expire'] > 0) {
                $arrExpireParams['reqs'][] = array(
                    'key' => $key,
                    'seconds' => $arrReq['expire'],
                );
            }
        }

        $ret = $objRedis->HMSET($arrSetParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HMSET] error.[".serialize($ret)."]");
            return false;
        }

        if(!empty($arrExpireParams)) {
            $objRedis->EXPIRE($arrExpireParams);
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return mixed|null
     */
    public function mgetFromHash($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['fields'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $fields = $arrInput['fields'];
        if(empty($fields)) {
            return array();
        }
        $serial = isset($arrInput['serial']) ? intval($arrInput['serial']) : 1;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array(
            'key' => $key,
            'field' => $fields,
        );
        $ret = $objRedis->HMGET($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HMGET] error.[".serialize($ret)."]");
            return false;
        }
        $data = array();
        if(!is_null($ret['ret'][$key])){
            foreach($ret['ret'][$key] as $k => $v) {
                if(!is_null($v) && (!$serial || ($v = unserialize($v)) !== false)) {
                    $data[$fields[$k]] = $v;
                }
            }
        }
        return $data;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function mclearFromHash($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['fields'])
            || !is_array($arrInput['fields']) || empty($arrInput['fields'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $fields = $arrInput['fields'];

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        foreach($fields as $field) {
            $arrParams['reqs'][] = array(
                'key' => strval($key),
                'field' => strval($field),
            );
        }
        $ret = $objRedis->HDEL($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HDEL] error.[".serialize($ret)."]");
            return false;
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function incrHashField($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['field'])
            || !isset($arrInput['step'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        $key = $arrInput['key'];
        $field = $arrInput['field'];
        $step = $arrInput['step'];
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array(
            'key' => $key,
            'field' => $field,
            'step' => $step,
        );
        $ret = $objRedis->HINCRBY($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HINCRBY] error.[".serialize($ret)."]");
            return false;
        }

        if($expire > 0){
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function mincrHashFields($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['fields'])
            || !isset($arrInput['step'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $fields = $arrInput['fields'];
        if(empty($fields)) {
            return true;
        }
        $step = $arrInput['step'];
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        foreach($fields as $field) {
            $arrParams['reqs'][] = array(
                'key' => $key,
                'field' => $field,
                'step' => $step,
            );
        }
        $ret = $objRedis->HINCRBY($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HINCRBY] error.[".serialize($ret)."]");
            return false;
        }

        if($expire > 0){
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function mincrHashFieldsByKeys($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        if(empty($arrInput['reqs'])) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        $arrKeyExpires = array();
        foreach($arrInput['reqs'] as $req) {
            if(!isset($req['key']) || !isset($req['field']) || !isset($req['step'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrParams['reqs'][] = array(
                'key' => $req['key'],
                'field' => $req['field'],
                'step' => $req['step'],
            );
            $expire = isset($req['expire']) ? intval($req['expire']) : 0;
            if(isset($arrKeyExpires[$req['key']]) && $arrKeyExpires[$req['key']] != $expire) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrKeyExpires[$req['key']] = $expire;
        }

        $ret = $objRedis->HINCRBY($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HINCRBY] error.[".serialize($ret)."]");
            return false;
        }

        $arrParams = array();
        foreach($arrKeyExpires as $key => $expire) {
            if($expire > 0) {
                $arrParams['reqs'][] = array(
                    'key' => $key,
                    'seconds' => $expire,
                );
            }
        }
        if(!empty($arrParams)) {
            $objRedis->EXPIRE($arrParams);
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return array|null
     */
    public function mgetAllFromHash($arrInput)
    {
        if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $serial = isset($arrInput['serial']) ? intval($arrInput['serial']) : 1;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array(
            'key' => $key,
        );
        $ret = $objRedis->HGETALL($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HGETALL] error.[".serialize($ret)."]");
            return false;
        }
        $data = null;
        if(!is_null($ret['ret'][$key])){
            foreach($ret['ret'][$key] as $item) {
                $data[$item['field']] = $serial ? unserialize($item['value']) : $item['value'];
            }
        }
        return $data;
    }

    /**
     * @param $arrInput
     * @return array|null
     */
    public function mgetAllFromHashByKeys($arrInput)
    {
        if(!isset($arrInput['keys']) || !is_array($arrInput['keys'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $keys = $arrInput['keys'];
        if(empty($keys)) {
            return array();
        }
        $serial = isset($arrInput['serial']) ? intval($arrInput['serial']) : 1;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        foreach($keys as $key) {
            $arrParams['reqs'][] = array(
                'key' => strval($key),
            );
        }
        $ret = $objRedis->HGETALL($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[HGETALL] error.[".serialize($ret)."]");
            return false;
        }
        $data = array();
        foreach($keys as $key) {
            if(is_null($ret['ret'][$key])) {
                continue;
            }
            foreach($ret['ret'][$key] as $item) {
                $data[$key][$item['field']] = $serial ? unserialize($item['value']) : $item['value'];
            }
        }
        return $data;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function updateRankMember($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['member'])
            || !isset($arrInput['score'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParam = array(
            'key' => $arrInput['key'],
            'member' => strval($arrInput['member']),
            'score' => $arrInput['score'],
        );

        $ret = $objRedis->ZADD($arrParam);
        if(!$ret || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZADD fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        if(isset($arrInput['expire'])) {
            $arrParam = array(
                'key' => $arrInput['key'],
                'seconds' => intval($arrInput['expire']),
            );
            $objRedis->EXPIRE($arrParam);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function mupdateRankMembers($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['mscores']) || !is_array($arrInput['mscores'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['mscores'])) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        foreach($arrInput['mscores'] as $member => $score) {
            $arrParams['reqs'][] = array(
                'key' => $arrInput['key'],
                'member' => strval($member),
                'score' => $score,
            );
        }

        $ret = $objRedis->ZADD($arrParams);
        if(!$ret || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZADD fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        if(isset($arrInput['expire'])) {
            $arrParams = array(
                'key' => $arrInput['key'],
                'seconds' => intval($arrInput['expire']),
            );
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function mupdateRankMembersByKeys($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['reqs'])) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        $arrKeyExpires = array();
        foreach($arrInput['reqs'] as $req) {
            if(!isset($req['key']) || !isset($req['member']) || !isset($req['score'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrParams['reqs'][] = array(
                'key' => $req['key'],
                'member' => strval($req['member']),
                'score' => $req['score'],
            );
            $expire = isset($req['expire']) ? intval($req['expire']) : 0;
            if(isset($arrKeyExpires[$req['key']]) && $arrKeyExpires[$req['key']] != $expire) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrKeyExpires[$req['key']] = $expire;
        }

        $ret = $objRedis->ZADD($arrParams);
        if(!$ret || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZADD fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        $arrParams = array();
        foreach($arrKeyExpires as $key => $expire) {
            if($expire > 0) {
                $arrParams['reqs'][] = array(
                    'key' => $key,
                    'seconds' => $expire,
                );
            }
        }
        if(!empty($arrParams)) {
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function incrRankMember($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['member'])
            || !isset($arrInput['step'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array(
            'key' => $arrInput['key'],
            'member' => strval($arrInput['member']),
            'step' => $arrInput['step'],
        );

        $ret = $objRedis->ZINCRBY($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZINCRBY fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        if(isset($arrInput['expire'])) {
            $arrParams = array(
                'key' => $arrInput['key'],
                'seconds' => intval($arrInput['expire']),
            );
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function mincrRankMembers($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['msteps']) || !is_array($arrInput['msteps'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['msteps'])) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        foreach($arrInput['msteps'] as $member => $step) {
            $arrParams['reqs'][] = array(
                'key' => $arrInput['key'],
                'member' => strval($member),
                'step' => $step,
            );
        }

        $ret = $objRedis->ZINCRBY($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZINCRBY fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        if(isset($arrInput['expire'])) {
            $arrParams = array(
                'key' => $arrInput['key'],
                'seconds' => intval($arrInput['expire']),
            );
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function mincrRankMembersByKeys($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['reqs'])) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        $arrKeyExpires = array();
        foreach($arrInput['reqs'] as $req) {
            if(!isset($req['key']) || !isset($req['member']) || !isset($req['step'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrParams['reqs'][] = array(
                'key' => $req['key'],
                'member' => strval($req['member']),
                'step' => $req['step'],
            );
            $expire = isset($req['expire']) ? intval($req['expire']) : 0;
            if(isset($arrKeyExpires[$req['key']]) && $arrKeyExpires[$req['key']] != $expire) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrKeyExpires[$req['key']] = $expire;
        }

        $ret = $objRedis->ZINCRBY($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZINCRBY fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        $arrParams = array();
        foreach($arrKeyExpires as $key => $expire) {
            if($expire > 0) {
                $arrParams['reqs'][] = array(
                    'key' => $key,
                    'seconds' => $expire,
                );
            }
        }
        if(!empty($arrParams)) {
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function remRankMember($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['member'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array(
            'key' => $arrInput['key'],
            'member' => strval($arrInput['member']),
        );

        $ret = $objRedis->ZREM($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZREM fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function mremRankMembers($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['members'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['members'])) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        foreach($arrInput['members'] as $member) {
            $arrParams['reqs'][] = array(
                'key' => $arrInput['key'],
                'member' => strval($member),
            );
        }

        $ret = $objRedis->ZREM($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZREM fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function mremRankMembersByKeys($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['reqs'])) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        foreach($arrInput['reqs'] as $req) {
            if(!isset($req['key']) || !isset($req['member'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrParams['reqs'][] = array(
                'key' => $req['key'],
                'member' => strval($req['member']),
            );
        }

        $ret = $objRedis->ZREM($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZREM fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function getRankCount($arrInput)
    {
        if(!isset($arrInput['key'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $strKey = $arrInput['key'];
        $arrParams = array(
            'key' => $strKey,
        );
        $ret = $objRedis->ZCARD($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZCARD fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        return isset($ret['ret'][$strKey]) ? intval($ret['ret'][$strKey]) : 0;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function mgetRankCounts($arrInput)
    {
        if(!isset($arrInput['keys'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['keys'])) {
            return array();
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        foreach($arrInput['keys'] as $strKey) {
            $arrParams['reqs'][] = array(
                'key' => $strKey,
            );
        }

        $ret = $objRedis->ZCARD($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZCARD fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        $arrOutput = array();
        foreach($arrInput['keys'] as $strKey) {
            $arrOutput[$strKey] = isset($ret['ret'][$strKey]) ? intval($ret['ret'][$strKey]) : 0;
        }

        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function getMemberRanking($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['member'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $strKey = $arrInput['key'];
        $arrParams = array(
            'key' => $strKey,
            'member' => strval($arrInput['member']),
        );

        $ret = $objRedis->ZREVRANK($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZREVRANK fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        return isset($ret['ret'][$strKey]) ? intval($ret['ret'][$strKey]) : null;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function mgetMemberRankings($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['members'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['members'])) {
            return array();
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $strKey = $arrInput['key'];
        $arrMembers = $arrInput['members'];
        $arrParams = array();
        foreach($arrMembers as $member) {
            $arrParams['reqs'][] = array(
                'key' => $strKey,
                'member' => strval($member),
            );
        }

        $ret = $objRedis->ZREVRANK($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZREVRANK fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        if(count($arrMembers) > 1) {
            $arrOutput = array();
            foreach($arrMembers as $member) {
                $arrOutput[$member] = isset($ret['ret'][$strKey][$member]) ?
                    intval($ret['ret'][$strKey][$member]) : null;
            }
        } else {
            $member = $arrMembers[0];
            $arrOutput[$member] = isset($ret['ret'][$strKey]) ? intval($ret['ret'][$strKey]) : null;
        }

        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function mgetMemberRankingsByKeys($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['reqs'])) {
            return array();
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        $bolMultiMember = false;
        foreach($arrInput['reqs'] as $req) {
            if(!isset($req['key']) || !isset($req['member'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrParams['reqs'][] = array(
                'key' => $req['key'],
                'member' => strval($req['member']),
            );
            if(!$bolMultiMember) {
                if(!isset($arrMemberMark[$req['key']])) {
                    $arrMemberMark[$req['key']] = 1;
                } else {
                    $bolMultiMember = true;
                }
            }
        }

        $ret = $objRedis->ZREVRANK($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZREVRANK fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        $arrOutput = array();
        foreach($arrInput['reqs'] as $req) {
            if($bolMultiMember) {
                $arrOutput[$req['key']][$req['member']] =
                    isset($ret['ret'][$req['key']][$req['member']]) ?
                        intval($ret['ret'][$req['key']][$req['member']]) : null;
            } else {
                $arrOutput[$req['key']][$req['member']] =
                    isset($ret['ret'][$req['key']]) ? intval($ret['ret'][$req['key']]) : null;
            }
        }
        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function getRankMemberScore($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['member'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $strKey = $arrInput['key'];
        $arrParam = array(
            'key' => $strKey,
            'member' => strval($arrInput['member']),
        );

        $ret = $objRedis->ZSCORE($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZSCORE fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        return isset($ret['ret'][$strKey]) ? $ret['ret'][$strKey] : null;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function mgetRankMemberScores($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['members'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['members'])) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $strKey = $arrInput['key'];
        $arrMembers = $arrInput['members'];
        $arrParams = array(
            'reqs' => array(),
        );
        foreach($arrMembers as $member) {
            $arrParams['reqs'][] = array(
                'key' => $strKey,
                'member' => strval($member),
            );
        }

        $ret = $objRedis->ZSCORE($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZSCORE fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        if(count($arrMembers) > 1) {
            $arrOutput = array();
            foreach($arrMembers as $member) {
                $arrOutput[$member] = isset($ret['ret'][$strKey][$member]) ? $ret['ret'][$strKey][$member] : null;
            }
        } else {
            $member = $arrMembers[0];
            $arrOutput[$member] = isset($ret['ret'][$strKey]) ? $ret['ret'][$strKey] : null;
        }

        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function mgetRankMemberScoresByKeys($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['reqs'])) {
            return array();
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParams = array();
        $bolMultiMember = false;
        foreach($arrInput['reqs'] as $req) {
            if(!isset($req['key']) || !isset($req['member'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrParams['reqs'][] = array(
                'key' => $req['key'],
                'member' => strval($req['member']),
            );
            if(!$bolMultiMember) {
                if(!isset($arrMemberMark[$req['key']])) {
                    $arrMemberMark[$req['key']] = 1;
                } else {
                    $bolMultiMember = true;
                }
            }
        }

        $ret = $objRedis->ZSCORE($arrParams);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZSCORE fail, params='.serialize($arrParams).',ret='.serialize($ret));
            return false;
        }

        $arrOutput = array();
        foreach($arrInput['reqs'] as $req) {
            if($bolMultiMember) {
                $arrOutput[$req['key']][$req['member']] =
                    isset($ret['ret'][$req['key']][$req['member']]) ? $ret['ret'][$req['key']][$req['member']] : null;
            } else {
                $arrOutput[$req['key']][$req['member']] =
                    isset($ret['ret'][$req['key']]) ? $ret['ret'][$req['key']] : null;
            }
        }
        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function getRankList($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['offset']) || !isset($arrInput['count']) ) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $strKey = $arrInput['key'];
        $intSortType = isset($arrInput['sort']) ? intval($arrInput['sort']) : 0;
        if($intSortType > 0) {
            $mixMax = isset($arrInput['max']) ? $arrInput['max'] : '+inf';
            $mixMin = isset($arrInput['min']) ? $arrInput['min'] : '-inf';
        } else {
            $mixMax = isset($arrInput['max']) ? $arrInput['max'] : '-inf';
            $mixMin = isset($arrInput['min']) ? $arrInput['min'] : '+inf';
        }
        $intOffset = intval($arrInput['offset']);
        $intCount = intval($arrInput['count']);
        $arrParams = array(
            'key' => $strKey,
            'max' => $mixMax,
            'min' => $mixMin,
            'offset' => $intOffset,
            'count' => $intCount,
        );

        if($intSortType > 0) {
            $ret = $objRedis->ZRANGEBYSCOREWITHSCORES($arrParams);
            if($ret === false || $ret['err_no'] != 0) {
                Bingo_Log::warning('ZRANGEBYSCOREWITHSCORES fail, params='.serialize($arrParams).',ret='
                    .serialize($ret));
                return false;
            }
        } else {
            $ret = $objRedis->ZREVRANGEBYSCOREWITHSCORES($arrParams);
            if($ret === false || $ret['err_no'] != 0) {
                Bingo_Log::warning('ZREVRANGEBYSCOREWITHSCORES fail, params='.serialize($arrParams).',ret='
                    .serialize($ret));
                return false;
            }
        }

        return isset($ret['ret'][$strKey]) ? $ret['ret'][$strKey] : array();
    }

    /**
     * @param $arrInput
     * @return bool|int
     */
    public function getRankCountByCond($arrInput)
    {
        if (!isset($arrInput['key']) || (!isset($arrInput['max']) && !isset($arrInput['min']))) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return false;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $strKey = $arrInput['key'];
        $mixMax = isset($arrInput['max']) ? $arrInput['max'] : '+inf';
        $mixMin = isset($arrInput['min']) ? $arrInput['min'] : '-inf';
        $arrParams = array(
            'key' => $strKey,
            'max' => $mixMax,
            'min' => $mixMin,
        );

        $ret = $objRedis->ZCOUNT($arrParams);
        if ($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZCOUNT fail, params=' . serialize($arrParams) . ',ret=' . serialize($ret));
            return false;
        }

        return isset($ret['ret'][$strKey]) ? intval($ret['ret'][$strKey]) : 0;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function addMemberToSet($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['member'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $member = $arrInput['member'];
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => $key,
            'member' => array($member),
        );
        $ret = $objRedis->SADD($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[SADD] error.[".serialize($ret)."]");
            return false;
        }

        if($expire > 0){
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function remMemberFromSet($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['member'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $member = $arrInput['member'];

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => $key,
            'member' => array($member),
        );
        $ret = $objRedis->SREM($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[SREM] error.[".serialize($ret)."]");
            return false;
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function addMembersToSet($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['members']) || !is_array($arrInput['members'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $members = $arrInput['members'];
        if(empty($members)) {
            return true;
        }
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => $key,
            'member' => $members,
        );
        $ret = $objRedis->SADD($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[SADD] error.[".serialize($ret)."]");
            return false;
        }

        if($expire > 0){
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $objRedis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function remMembersFromSet($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['members']) || !is_array($arrInput['members'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $members = $arrInput['members'];
        if(empty($members)) {
            return true;
        }

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => $key,
            'member' => $members,
        );
        $ret = $objRedis->SREM($arrParams);
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[SREM] error.[".serialize($ret)."]");
            return false;
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function getMembersFromSet($arrInput)
    {
        if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $count = isset($arrInput['count'])? intval($arrInput['count']) : 0;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }
        $arrParams = array(
            'key' => $key,
        );
        if($count > 0) {
            $arrParams['count'] = $count;
            //目前redis版本的该命令支持原生协议，不支持mcpack+nshead(20160720)
            $ret = $objRedis->SRANDMEMBER($arrParams);
        } else {
            $ret = $objRedis->SMEMBERS($arrParams);
        }
        if($ret===false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[SMEMBERS] error.[".serialize($ret)."]");
            return false;
        }

        return isset($ret['ret'][$key]) ? $ret['ret'][$key] : null;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function pushToList($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['value'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $value = $arrInput['value'];
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;
        $serial = isset($arrInput['serial']) ? intval($arrInput['serial']) : 1;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParam = array(
            'key' => $key,
            'value' => $serial ? serialize($value) : $value,
        );

        $ret = $objRedis->RPUSH($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('RPUSH fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        if($expire > 0){
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $objRedis->EXPIRE($arrParams);
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function popFromList($arrInput)
    {
        if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $serial = isset($arrInput['serial']) ? intval($arrInput['serial']) : 1;

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParam = array(
            'key' => $key,
        );

        $ret = $objRedis->LPOP($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('LPOP fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        $data = null;
        if(!is_null($ret['ret'][$key])){
            $data = $serial ? unserialize($ret['ret'][$key]) : $ret['ret'][$key];
        }
        return $data;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public function getListLen($arrInput)
    {
        if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];

        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $arrParam = array(
            'key' => $key,
        );

        $ret = $objRedis->LLEN($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('LLEN fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        return !is_null($ret['ret'][$key]) ? intval($ret['ret'][$key]) : 0;
    }

    /**
     * __construct
     * @param $strRedisName
     */
    protected function __construct($strRedisName)
    {
        $this->_redis_name = $strRedisName;
    }

    /**
     * @param $strMethod
     * @param $arrInput
     * @return bool
     */
    public function __call($strMethod, $arrInput)
    {
        if(is_null($objRedis = $this->_getRedis())) {
            return false;
        }

        $ret = $objRedis->$strMethod($arrInput[0]);
        if($ret === false || $ret['err_no'] !== 0){
            Bingo_Log::warning("call redis[$strMethod] error.[".serialize($ret)."]");
        }

        return $ret;
    }

    /**
     * _getRedis
     * @return Bingo_Cache_Redis|null
     */
    private function _getRedis()
    {
        if(!is_null($this->_redis)) {
            $objRedis = $this->_redis;
        } else {
            $objRedis = new Bingo_Cache_Redis($this->_redis_name);
            $this->_redis = $objRedis;
        }
        if(!$objRedis || !$objRedis->isEnable()){
            Bingo_Log::warning("init redis{$this->_redis_name} fail.");
            return null;
        }
        return $objRedis;
    }

}
