<?php

define('DB_DEBUG', 1);

class  Util_Db {
    const CHARSET = 'utf8';

    protected static $_mysql_operator_white_list = array('+=','-=','|=','&=','>','<','>=','<=','<>','in','like');

    protected static $_instances = null;

    protected $_db_name = null;
    protected $_db_table = null;
    protected $_db    = null;

    /**
     * @param $db_table
     * @param $db_name
     * @return Util_Db
     */
    public static function getInstance($db_name, $db_table)
    {
        if(!isset(self::$_instances[$db_name][$db_table])) {
            self::$_instances[$db_name][$db_table] = new self($db_name, $db_table);
        }

        return self::$_instances[$db_name][$db_table];
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function insert($arrInput)
    {
        if(!isset($arrInput['field']) || !is_array($arrInput['field'])){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return $this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if(is_null($db = $this->_getObjDB())) {
            return $this->_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $fields = $arrInput['field'];
        $strOnDup = null;
        if(isset($arrInput['onDup'])&&is_array($arrInput['onDup'])){
            $strOnDup = $this->_assembleFields($arrInput['onDup']);
        }
        $strOptions = null;
        if(isset($arrInput['options'])&&is_string($arrInput['options'])){
            $strOptions = mysql_real_escape_string(trim($arrInput['options']));
        }

        $ret = $db->insert($this->_db_table, $fields, $strOptions, $strOnDup);
        if(DB_DEBUG) {
            Bingo_Log::warning('sql:'.$db->getLastSQL());
        }
        if($ret===false){
            Bingo_Log::warning('[output:'.serialize($ret).'error:'.$db->error().'sql:'.$db->getLastSQL().']');
            return $this->_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array('mysql_errno'=>$db->errno()));
        }
        $insertId = $db->getInsertID();

        return $this->_errRet(Tieba_Errcode::ERR_SUCCESS, $insertId);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function multiInsert($arrInput)
    {
        if(!isset($arrInput['field']) || !is_array($arrInput['field'])
            || !isset($arrInput['values']) || !is_array($arrInput['values'])) {
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return $this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $fields = $arrInput['field'];
        $values = $arrInput['values'];
        if(empty($fields) || empty($values)) {
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return $this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        foreach($values as $list) {
            if(count($fields) != count($list)) {
                Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
                return $this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
        }

        if(is_null($db = $this->_getObjDB())) {
            return $this->_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $strOnDup = null;
        if(isset($arrInput['onDup'])&&is_array($arrInput['onDup'])){
            $strOnDup = $this->_assembleFields($arrInput['onDup']);
        }
        $strOptions = null;
        if(isset($arrInput['options'])&&is_string($arrInput['options'])){
            $strOptions = mysql_real_escape_string(trim($arrInput['options']));
        }

        //BD_DB库函数multiInsert在values中存在','时有BUG!!!
        //$ret = $db->multiInsert($this->_db_table, $fields, $values, $strOptions, $strOnDup);
        $sql = $this->_getMultiInsertSql($this->_db_table, $fields, $values, $strOptions, $strOnDup);
        $ret = $db->query($sql);
        if(DB_DEBUG) {
            Bingo_Log::warning('sql:'.$db->getLastSQL());
        }
        if($ret===false){
            Bingo_Log::warning('[output:'.serialize($ret).'error:'.$db->error().'sql:'.$db->getLastSQL().']');
            return $this->_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array('mysql_errno'=>$db->errno()));
        }
        $affectedRows = $db->getAffectedRows();

        return $this->_errRet(Tieba_Errcode::ERR_SUCCESS, $affectedRows);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function select($arrInput)
    {
        if(!isset($arrInput['field']) || !is_array($arrInput['field'])){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            //$arrInput['field'] = self::$_arrFields;
            return $this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if(is_null($db = $this->_getObjDB())) {
            return $this->_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $arrFields = $arrInput['field'];
        $str_cond = null;
        if(isset($arrInput['cond'])&&is_array($arrInput['cond'])){
            $str_cond = $this->_assembleConds($arrInput['cond']);
        } else if(isset($arrInput['cond']) && is_string($arrInput['cond'])) {
            $str_cond = trim($arrInput['cond']);
        } else if(isset($arrInput['conds'])&&is_array($arrInput['conds'])){
            $str_cond = $this->_assembleMultiConds($arrInput['conds']);
        }
        $str_append = null;
        if(isset($arrInput['append'])){
            $str_append = $arrInput['append'];
        }

        $ret = $db->select($this->_db_table, $arrFields, $str_cond, null, $str_append);
        if(DB_DEBUG) {
            Bingo_Log::warning('sql:'.$db->getLastSQL());
        }
        if($ret===false){
            Bingo_Log::warning('[output:'.serialize($ret).'error:'.$db->error().'sql:'.$db->getLastSQL().']');
            return $this->_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array('mysql_errno'=>$db->errno()));
        }

        return $this->_errRet(Tieba_Errcode::ERR_SUCCESS,$ret);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function update($arrInput)
    {
        if(!isset($arrInput['field']) || !is_array($arrInput['field'])){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return $this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if(is_null($db = $this->_getObjDB())) {
            return $this->_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $str_field = $this->_assembleFields($arrInput['field']);
        $str_cond = null;
        if(isset($arrInput['cond'])&&is_array($arrInput['cond'])){
            $str_cond = $this->_assembleConds($arrInput['cond']);
        } else if(isset($arrInput['cond']) && is_string($arrInput['cond'])) {
            $str_cond = trim($arrInput['cond']);
        }

        $ret = $db->update($this->_db_table, $str_field, $str_cond);
        if(DB_DEBUG) {
            Bingo_Log::warning('sql:'.$db->getLastSQL());
        }
        if($ret===false){
            Bingo_Log::warning('[output:'.serialize($ret).'error:'.$db->error().'sql:'.$db->getLastSQL().']');
            return $this->_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array('mysql_errno'=>$db->errno()));
        }
        $affectedRows = $db->getAffectedRows();

        return $this->_errRet(Tieba_Errcode::ERR_SUCCESS,$affectedRows);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function delete($arrInput)
    {
        if(is_null($db = $this->_getObjDB())) {
            return $this->_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $str_cond = null;
        if(isset($arrInput['cond'])&&is_array($arrInput['cond'])){
            $str_cond= $this->_assembleConds($arrInput['cond']);
        } else if(isset($arrInput['cond']) && is_string($arrInput['cond'])) {
            $str_cond = trim($arrInput['cond']);
        }

        $str_append = null;
        if(isset($arrInput['append'])){
            $str_append = $arrInput['append'];
        }

        $ret = $db->delete($this->_db_table, $str_cond, null, $str_append);
        if(DB_DEBUG) {
            Bingo_Log::warning('sql:'.$db->getLastSQL());
        }
        if($ret===false){
            Bingo_Log::warning('[output:'.serialize($ret).'error:'.$db->error().'sql:'.$db->getLastSQL().']');
            return $this->_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array('mysql_errno'=>$db->errno()));
        }

        return $this->_errRet(Tieba_Errcode::ERR_SUCCESS,$ret);
    }

    /**
     * __construct
     * @param $db_name
     * @param $db_table
     */
    protected function __construct($db_name, $db_table)
    {
        $this->_db_name = $db_name;
        $this->_db_table = $db_table;
    }

    /**
     * @param $dbFunc
     * @param $arrInput
     * @return mixed
     */
    public function __call($dbFunc, $arrInput)
    {
        if(is_null($db = $this->_getObjDB())) {
            return $this->_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        if (empty($arrInput)) {
            return $db->$dbFunc();
        }
        else {
            return $db->$dbFunc($arrInput[0]);
        }
    }

    /**
     * @param $table
     * @param $fields
     * @param $values
     * @param $strOptions
     * @param $strOnDup
     * @return string
     */
    protected function _getMultiInsertSql($table, $fields, $values, $strOptions, $strOnDup)
    {
        $sql = 'INSERT';

        if(!empty($strOptions)) {
            $sql .= " $strOptions";
        }

        $sql .= " INTO $table(" . implode(',', $fields) . ") VALUES";
        foreach($values as $list) {
            $sql .= '(';
            foreach($list as $item) {
                if(is_int($item)) {
                    $sql .= "$item,";
                } else {
                    $sql .= "'".strval($item)."',";
                }
            }
            $sql = rtrim($sql, ',') . "),";
        }
        $sql = rtrim($sql, ',');

        if(!empty($strOnDup)) {
            $sql .= " ON DUPLICATE KEY UPDATE $strOnDup";
        }

        return $sql;
    }

    /**
     * @param $arrConds
     * @return string
     */
    protected function _assembleConds($arrConds)
    {
        $str_cond = null;
        foreach($arrConds as $key=>$value){
            if(!is_null($str_cond)){
                $str_cond .= ' AND ';
            }
            $str_cond .= $this->_combineKeyValue($key, $value);
        }
        return $str_cond;
    }

    /**
     * @param $arrMultiConds
     * @return string
     */
    protected function _assembleMultiConds($arrMultiConds)
    {
        $str_cond = null;
        foreach($arrMultiConds as $arrConds){
            if(!is_null($str_cond)){
                $str_cond .= ' OR ';
            }
            $str_cond .= $this->_assembleConds($arrConds);
        }
        return $str_cond;
    }

    /**
     * @param $arrFields
     * @return string
     */
    protected function _assembleFields($arrFields)
    {
        $str_field = '';
        foreach($arrFields as $key=>$value){
            if($str_field !== ''){
                $str_field .= ', ';
            }
            $str_field .= $this->_combineKeyValue($key, $value);
        }
        return $str_field;
    }

    /**
     * @param $key
     * @param $value
     * @return string
     */
    protected function _combineKeyValue($key, $value)
    {
        $key = mysql_real_escape_string(trim($key));
        $str_combine = '';
        if (is_int($value)){
            $str_combine = "$key=$value";
        } else if (is_string($value)){
            $value = mysql_real_escape_string(trim($value));
            $str_combine = "$key='$value'";
        } else if (is_array($value)){
            $val = $value['val'];
            $opt = in_array(strval($value['opt']),self::$_mysql_operator_white_list) ? strval($value['opt']) : '=';
            $quotes = isset($value['quotes']) ? intval($value['quotes']) : 1;
            if(is_int($val)) {
                $str_combine = "$key $opt $val";
            } else if(is_string($val)){
                $val = mysql_real_escape_string(trim($val));
                $str_combine = "$key $opt ".($quotes ? "'$val'" : "$val");
            }else if(is_array($val) && !empty($val) && 'in' == $value['opt']){
                if(is_int($val[0])) {
                    $str_combine = "$key in (";
                    foreach($val as $v) {
                        $str_combine .= intval($v) . ',';
                    }
                    $str_combine = rtrim($str_combine, ',') . ')';
                } else if(is_string($val[0])){
                    $str_combine = "$key in (";
                    foreach($val as $v) {
                        $str_combine .= "'".mysql_real_escape_string(trim(strval($v)))."',";
                    }
                    $str_combine = rtrim($str_combine, ',') . ')';
                }
            }
        }
        return $str_combine;
    }

    /**
     * _getObjDB
     * @return Bd_DB|null
     */
    protected function _getObjDB()
    {
        if(!is_null($this->_db)) {
            $objTbMysql = $this->_db;
        } else {
            $objTbMysql = Tieba_Mysql::getDB($this->_db_name);
            $this->_db = $objTbMysql;
        }
        if($objTbMysql && $objTbMysql->isConnected()) {
            $objTbMysql->charset(self::CHARSET);
            return $objTbMysql;
        } else {
            Bingo_Log::warning("db {$this->_db_name}connect fail.");
            return null;
        }
    }

    /**
     * @param $errno
     * @param $data
     * @return array
     */
    protected function _errRet($errno, $data = null)
    {
        $errmsg = Tieba_Error::getErrmsg($errno);
        return Util_Ret::errRet($errno, $errmsg, $data);
    }
}
