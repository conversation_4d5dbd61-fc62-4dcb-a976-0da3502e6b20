<?php
class Lib_SendSmsp {
    private $mesTo = "";
    private $message = "";
    
    /**
     * @brief 初始化
     * @param null
     * @return null
     */
    public function setTo($phoneArray) {
        for($i = 0; $i < count ( $phoneArray ); $i ++) {
            if ($this->_checkPhone ( $phoneArray [$i] ) == false) {
                return false;
            }
        }
        // --���кϷ���phone����������
        $this->mesTo = $phoneArray;
        return true;
    }
    
    /**
     * @brief 初始化
     * @param null
     * @return null
     */
    protected function _checkPhone($phone) {
        return ereg ( "^1[3-8][0-9]{9}$", $phone );
    }
    
    /**
     * @brief 初始化
     * @param null
     * @return null
     */
    public function setMes($mes) {
        if (strlen ( trim ( $mes ) ) > 0) {
            $this->message = $mes;
            return true;
        }
        return false;
    }
    
    /**
     * @brief 初始化
     * @param null
     * @return null
     */
    public function send() {
        if (empty ( $this->mesTo ) || $this->message == "") {
            return - 1;
        }

        foreach ( $this->mesTo as $phone ) {
            $str = '"' . $phone. '@' . $this->message . '"';
            $cmd = "/bin/gsmsend-script " . $str;
            //$cmd = "/bin/gsmsend -s emp01.baidu.com:15002 ".$str;
            exec ( $cmd, $arr, $res );
        }
        return $res;
    }
}
