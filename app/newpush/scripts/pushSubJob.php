<?php
/**
 * @Author: he<PERSON><PERSON>
 * @Created Time: 2015-11-09 14:52:00
 * @File Name: PushSubJob.php
 * @Description: 推送子任务
 *
 */

define ('SCRIPT_NAME', basename(__FILE__, ".php"));
define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../' );
define ('LOG_PATH',  ROOT_PATH . "log/app/newpush");

set_include_path(get_include_path() . PATH_SEPARATOR . ROOT_PATH . '/app/newpush/scripts/');

/**
 * @param className
 */
function __autoload($strClassName) {
    require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');

try {
    Bingo_Log::init(array(
        LOG => array(
            'file'  => LOG_PATH . "/" . SCRIPT_NAME . ".log",
            'level' => 0xFF,
        ),
    ), LOG);

    Newpush_Scripts_PushSubJob::process();
} catch (Exception $e) {
    Bingo_Log::warning('Bingo_Log init fail!');
    exit(0);
}

class Newpush_Scripts_PushSubJob {

    const RETRY_COUNT = 3;

    /**
     * @brief 推送子任务入口
     * @param null
     * @return null
     */
    public static function process() {

        Bingo_Timer::start('getSubJobByQueue');
        ral_set_idc('nj');
        $arrRes = Tieba_Service::call('newpush', 'getSubJobByQueue', array(), null, null, 'post', 'php', 'utf-8');
        Bingo_Timer::end('getSubJobByQueue');
        if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']) {
            Bingo_Log::warning(SCRIPT_NAME . ' fail to getSubJobByQueue! res: ' . serialize($arrRes));
            Bingo_Log::notice(Bingo_Timer::toString());
            exit;
        }

        Bingo_Timer::start('pushMsgCenter');
        if (isset($arrRes['output']) && is_array($arrRes['output'])) {
            ral_set_idc('jx');
            foreach ($arrRes['output'] as $subJobId) {
                $arrParams = array(
                    'subJobId' => $subJobId,
                    '_partition_key' => $subJobId,
                );
                $res = self::commitNmq('newpush', 'pushMsgCenter', $arrParams);
                if ($subJobId % 1000 == 212) {
                    Bingo_Log::warning(SCRIPT_NAME . " commitNmq! newpush:pushMsgCenter, params: " .  serialize($arrParams) . ", res: " . serialize($res));
                }
            }
        }
        Bingo_Timer::end('pushMsgCenter');
        Bingo_Log::notice(Bingo_Timer::toString());
    }

    /**
     * @brief: 带重试功能的nmq提交
     * @param:
     *      $topic     : topic
     *      $cmd       : cmd
     *      $arrParams : 参数
     *      $retryCount: 重试次数
     * @return: null
     */
    private static function commitNmq($topic, $cmd, $arrParams, $retryCount=3) {
        if ($retryCount > 0) {
            $arrRes = Tieba_Commit::commit($topic, $cmd, $arrParams);
            if ($arrRes === false || 0 != $arrRes['errno']) {
                Bingo_Log::warning(SCRIPT_NAME . " fail to commitNmq $retryCount! topic: $topic, cmd: $cmd, params: " .  serialize($arrParams) );
                usleep(5000);
                self::commitNmq($topic, $cmd, $arrParams, $retryCount-1);
            } else {
                return $arrRes;
            }
        } else {
            return false;
        }
    }

	/**
	 * @brief
	 *      带重试功能的Tieba_Service::call
	 * @param $strService
	 * @param $strMethod
	 * @param $arrInput
	 * @param int $retryCount
	 * @return bool
	 * @internal param $ strService: service*      strService: service
	 *      strMethod : method
	 *      arrInput  : 请求参数
	 *      retryCount: 重试次数
	 */
    private static function serviceCall($strService, $strMethod, $arrInput, $retryCount=self::RETRY_COUNT) {
        if ($retryCount > 0) {
            $arrRes = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
            if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']) {
                Bingo_Log::warning(SCRIPT_NAME . " fail to $strMethod $retryCount! req: " .  serialize($arrInput) . ' res: ' . serialize($arrRes));
                sleep(1);
                self::serviceCall($strService, $strMethod, $arrInput, $retryCount - 1);
            } else {
                return $arrRes;
            }
        } else {
            return false;
        }
    }
}
