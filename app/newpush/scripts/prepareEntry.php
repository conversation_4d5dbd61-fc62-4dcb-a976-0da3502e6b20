<?php
/**
 * @Author: he<PERSON><PERSON>
 * @Created Time: 2015-12-14 17:51:45
 * @File Name: PrepareEntry.php
 * @Description: 准备推送数据入口脚本
 *
 */

define ('SCRIPT_NAME', basename(__FILE__, ".php"));
define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../' );
define ('LOG_PATH',  ROOT_PATH . "log/app/newpush");

Newpush_Scripts_PrepareEntry::process();

class Newpush_Scripts_PrepareEntry {

    /**
     * @brief 初始化
     * @param null
     * @return null
     */
    private static function init() {

        if (!is_dir(LOG_PATH)) {
            $strCmd = "mkdir -p " . LOG_PATH;
            system($strCmd);
        }
    }

    /**
     * @brief 准备推送数据入口
     * @param null
     * @return null
     */
    public static function process() {

        self::init();

        while (true) {
            $date = getdate();
            if ($date['minutes'] >= 59) {
                break;
            }

            $logPath = LOG_PATH . "/" . SCRIPT_NAME . ".log." . date("YmdH");

            // 线上php启动加载时间太长
            $cmd = "curl -i \"http://nj.service.tieba.baidu.com/service/newpush?method=preparePushData&format=json\" --connect-timeout 1 >> $logPath 2>&1 &";
            system($cmd);
            sleep(1);
        }
    }
}
