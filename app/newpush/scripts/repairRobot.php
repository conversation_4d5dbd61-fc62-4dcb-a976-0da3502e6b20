<?php
/**
 * @Author: he<PERSON><PERSON>
 * @Created Time: 2016-01-28 17:53:04
 * @File Name: repairRobot.php
 * @Description: 修复机器人
 *
 */

define ('SCRIPT_NAME', basename(__FILE__, ".php"));
define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../' );
define ('LOG_PATH',  ROOT_PATH . "log/app/newpush");

set_include_path(get_include_path() . PATH_SEPARATOR . ROOT_PATH . 'app/newpush/scripts/');

/**
 * @param className
 */
function __autoload($strClassName) {
    require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');

try {

    Bingo_Log::init(array(
        LOG => array(
            'file'  => LOG_PATH . "/" . SCRIPT_NAME . ".log",
            'level' => 0xFF,
        ),
    ), LOG);

    Newpush_Scripts_RepairRobot::process();
} catch (Exception $e) {
    Bingo_Log::warning('Bingo_Log init fail!');
    exit(-1);
}

class Newpush_Scripts_RepairRobot {

    const REDIS_PID = 'newpush';  //Redis 实例名称
    const RETRY_COUNT = 3;
    const KEY_LAST_JOB_ID = 'repair_last_jobid';

    /**
     * @brief
     *      入口函数
     * @return bool
     */
    public static function process() {

        $arrReq = array(
            'pn' => 1,
            'rn' => 1,
            'is_robot' => 1,
        );
        $arrRes = self::serviceCall('newpush', 'getStartPushJob', $arrReq);
        if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']) {
            Bingo_Log::warning('fail to getStartPushJob! req: ' . serialize($arrReq) .  ', res: ' . serialize($arrRes));
            exit(-1);
        }

        if (isset($arrRes['data'][0]['job_id'])) {
            $jobId = $arrRes['data'][0]['job_id'];

            $arrReq = array(
                'key' => self::KEY_LAST_JOB_ID,
                'value' => $jobId,
            );
            $arrRes = self::redisGetSet($arrReq);
            if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['err_no']) {
                Bingo_Log::warning('fail to getset redis LAST_JOB_ID! req: ' . serialize($arrReq) .  ', res: ' . serialize($arrRes));
                exit(-1);
            }

            $lastJobId = $arrRes['ret'][$arrReq['key']];
            
            if ($jobId == $lastJobId) {

                // 清理任务
                $arrReq = array(
                    'job_id' => $jobId,
                    'status' => 11,
                );
                $arrRes = self::serviceCall('newpush', 'rawUpdateJob', $arrReq);
                if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']) {
                    Bingo_Log::warning('fail to rawUpdateJob! req: ' . serialize($arrReq) .  ', res: ' . serialize($arrRes));
                }
            }
        }
    }

    /**
     * @brief 初始化redis
     * @param $retryCount 重试次数
     * @return redis实例
     */
    private static function initRedis($retryCount=self::RETRY_COUNT) {

        if ($retryCount > 0) {

            $redis = new Bingo_Cache_Redis(self::REDIS_PID);
            if (!$redis || !$redis ->isEnable()){
                Bingo_Log::warning(SCRIPT_NAME . " init Redis fail $retryCount .");
                usleep(10000);
                self::initRedis($retryCount - 1);
            } else {
                return $redis;
            }
        } else {
            return null;
        }
    }

    /**
     * @brief
     *      带重试功能的redis get操作
     * @param
     *      arrInput  : 请求参数
     *      retryCount: 重试次数
     * @param int $retryCount
     * @return bool
     */
    private static function redisGetSet($arrInput, $retryCount=self::RETRY_COUNT) {
        $redis = self::initRedis();
        if (!is_null($redis) && $retryCount > 0) {
            $arrRes = $redis->GETSET($arrInput);
            if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['error_no']) {
                Bingo_Log::warning(SCRIPT_NAME . " fail to redis getset $retryCount! req: " .  serialize($arrInput) . ' res: ' . serialize($arrRes));
                usleep(10000);
                self::redisGetSet($arrInput, $retryCount - 1);
            } else {
                return $arrRes;
            }
        } else {
            return false;
        }
    }

    /**
     * @brief
     *      带重试功能的Tieba_Service::call
     * @param $strService
     * @param $strMethod
     * @param $arrInput
     * @param int $retryCount
     * @return bool
     * @internal param $ strService: service*      strService: service
     *      strMethod : method
     *      arrInput  : 请求参数
     *      retryCount: 重试次数
     */
    private static function serviceCall($strService, $strMethod, $arrInput, $retryCount=self::RETRY_COUNT) {
        if ($retryCount > 0) {
            $arrRes = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
            if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']) {
                Bingo_Log::warning(SCRIPT_NAME . " fail to $strMethod $retryCount! req: " .  serialize($arrInput) . ' res: ' . serialize($arrRes));
                usleep(10000);
                self::serviceCall($strService, $strMethod, $arrInput, $retryCount - 1);
            } else {
                return $arrRes;
            }
        } else {
            return false;
        }
    }
}
