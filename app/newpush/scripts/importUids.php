<?php
/**
 * @Author: heqingming
 * @Created Time: 2015-11-05 19:07:17
 * @File Name: ImportUids.php
 * @Description: 准备推送数据
 *
 */

define ('SCRIPT_NAME', basename(__FILE__, ".php"));
define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../' );
define ('DATA_PATH', ROOT_PATH . "data/app/newpush/broadcast");
define ('LOG_PATH',  ROOT_PATH . "log/app/newpush");

set_include_path(get_include_path() . PATH_SEPARATOR . ROOT_PATH . 'app/newpush/scripts/');

/**
 * @param className
 */
function __autoload($strClassName) {
    require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');

try {

    if (!is_dir(DATA_PATH)) {
        $strCmd = "mkdir -p " . DATA_PATH;
        system($strCmd);
    }

    @ini_set('memory_limit', '1024M');
    Bingo_Log::init(array(
        LOG => array(
            'file'  => LOG_PATH . "/" . SCRIPT_NAME . ".log",
            'level' => 0xFF,
        ),
    ), LOG);

    Newpush_Scripts_ImportUids::process();
} catch (Exception $e) {
    Bingo_Log::warning('Bingo_Log init fail!');
    exit(0);
}

class Newpush_Scripts_ImportUids {

    const JOB_STATUS_NEW               = 1;  //新建：新建任务暂未提交，可再次编辑内容
    const JOB_STATUS_RJECTED           = 2;  //审核拒绝：初审终审拒绝均更新为该状态
    const JOB_STATUS_WAIT_READY       = 3;  //等待用户数据准备中：新建者编辑完成后提交任务进入该状态，如果需要审核则等待审核，否则在到发布时间时进入到发布状态
    const JOB_STATUS_READY               = 4;  //数据已准备好状态
    const JOB_STATUS_WAIT_FIRST_AUDIT = 5;  //等待初审中
    const JOB_STATUS_WAIT_FINAL_AUDIT = 6;  //初审通过,等待终审
    const JOB_STATUS_WAIT_PUBLIC       = 7;  //终审通过，待发布状态
    const JOB_STATUS_PUBLICED           = 8;  //已发布：在到达发布时间时转入该状态
    const JOB_STATUS_PUSH_START       = 9;  //推送开始：到达推送时间时转入该状态
    const JOB_STATUS_PUSH_END           = 10; //推送结束：任务完成
    const JOB_STATUS_COMPLEMENTLY       = 11; //任务结束，在所有子任务完成时更新至该状态代表彻底结束

    const TAG_STATUS_NEW     = 0;
    const TAG_STATUS_INIT    = 1;

    const FOLLOW_TYPE_ALL                 = 0; //获取全部关注信息
    const FOLLOW_TYPE_OFFICIAL            = 1; //官方吧
    const FOLLOW_TYPE_FORUM_BROADCAST     = 2; //吧广播
    const FOLLOW_TYPE_OFFICIAL_ACCOUNT    = 3; //贴吧官方账号
    const FOLLOW_TYPE_PUBLIC_ACCOUNT      = 4; //公众账号
    const FOLLOW_TYPE_SUBSCRIBE_ACCOUNT   = 5; //订阅帐号
    const FOLLOW_TYPE_LIVE_ACCOUNT        = 6; //直播主播帐号
    const FOLLOW_TYPE_INDIVIDUATION_FULL  = 7; //个性化推送全量
    const FOLLOW_TYPE_INDIVIDUATION_GROUP = 8; //个性化推送组播
    const FOLLOW_TYPE_BAZHUTASK = 9; //吧主专版任务推送

    const REDIS_NAME     = 'newpush';
    const FETCH_COUNT    = 1;
    const RETRY_COUNT    = 3;

    const REDIS_PER_TIME = 10;
    const FILE_PER_TIME  = 100000;
    const GRADE_PER_NUM  = 1000;
    const BUCKET_NUM     = 1024;
    const MAX_FORUM_MEMBER = 300000;//吧会员数，判断是直接访问grade备库还是使用数据组文件数据的分界线
    const FORUM_GRADE_DATABASE = "DB_grade_new_read";//grade备库ral配置

    const REDIS_SUBUID_SUFFIX_KEY = '_subjob';//存放用户id的单个resid后缀 
    const  REDIS_SADD_PRE_NUM= 1000;//redis 在zadd的时候，每次插入的数量限制,当前redis的包大小限制是64K
    
    const ORP_SMALLAPP_TOKEN = "v-u3J8de49oY3U4mwaQFeoM-JnQ99MkO";
    const REDIS_WORDLIST_NAME_BAZHU_WHITE = 'tb_wordlist_redis_bazhu_whitelist';
    const BAZHU_WHITE_CONTROLLOR_KEY = 'controller';
    private static $_FOLLOW_TYPE_FILE_NAME_SUFFIX_ARRAY = array(
        self::FOLLOW_TYPE_ALL                   => 'all',
        self::FOLLOW_TYPE_OFFICIAL              => 'offical',
        self::FOLLOW_TYPE_FORUM_BROADCAST       => 'forum_broadcast',
        self::FOLLOW_TYPE_OFFICIAL_ACCOUNT      => 'offical_account',
        self::FOLLOW_TYPE_PUBLIC_ACCOUNT        => 'public_account',
        self::FOLLOW_TYPE_SUBSCRIBE_ACCOUNT     => 'subscribe_account',
        self::FOLLOW_TYPE_LIVE_ACCOUNT          => 'live_account',
        self::FOLLOW_TYPE_INDIVIDUATION_FULL    => 'individuation_full',
        self::FOLLOW_TYPE_INDIVIDUATION_GROUP   => 'individuation_group',
        self::FOLLOW_TYPE_BAZHUTASK             => 'bazhutask',
    );
    private static $_redis = null;
    private static $_strUidFile = 'wget ftp://tieba00:<EMAIL>://home/<USER>/tbdc/data/tieba_rpt_bakan_recommend_user_file/tieba_rpt_bakan_recommend_user_file.';

    private static $_whiteList = array(
        *********,  // 如履薄bing
        **********, // GreyAnts lirubing
        **********, // 程书坤
    );

    /**
     * @brief
     *      获取grade db实例
     * @param int $retryCount
     * @return Bd_DB db实例
     * db实例
     * @internal param $ retryCount 重试次数*      retryCount 重试次数
     */
    private static function getGradeDb($retryCount=3) {
        $db = new Bd_DB();
        if (is_null($db)) {
            getGradeDb($retryCount-1);
        } else {
            Bingo_Timer::start("dbinit_Grade");
            $ret = $db->ralConnect(self::FORUM_GRADE_DATABASE);
            Bingo_Timer::end("dbinit_Grade");
            if (!$ret) {
                sleep(1);
                getGradeDb($retryCount-1);
            } else {
                return $db;
            }
        }
    }

    /**
     * @brief
     *      入口函数
     * @return bool
     * @internal param $
     */
    public static function process() {

        $arrReq = array(
            'status' => self::JOB_STATUS_WAIT_READY,
            'pn' => 1,
            'rn'  => 1,
        );

        ral_set_idc('nj');
        $arrRes = self::serviceCall('newpush', 'getJobAndTagByQuery', $arrReq);

        /*
        $arrRes = array(
            'errno' => Tieba_Errcode::ERR_SUCCESS,
            'errmsg' => 'success',
            'data' => array(
                array(
                    'job_id' => 6000,
                    'file_path' => '',
                    'follow_id' => 296192,
                    'follow_type' => 2,
                    'tag_info' => array(
                        'tag_id' => 60000,
                        'tag_status' => 0,
                        'group_id' => 60000,
                        'group_type' => 1,
                    ),
                ),
            ),
        );
         */

        if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']) {
            Bingo_Log::warning('fail to getJobInfo! req: ' . serialize($arrReq) .
                ', res: ' . serialize($arrRes));
            exit(0);
        } else if (!isset($arrRes['data']) || count($arrRes['data']) <= 0) {
            Bingo_Log::warning('no more new job! req: ' . serialize($arrReq) .
                ', res: ' . serialize($arrRes));
            exit(0);
        }

        $jobInfo = $arrRes['data'][0];
        $jobId   = intval($jobInfo['job_id']);
        $tagId   = intval($jobInfo['tag_info']['tag_id']);
        if (!empty($jobInfo['filepath'])) {

            self::$_redis = new Bingo_Cache_Redis(self::REDIS_NAME);
            if (is_null(self::$_redis)) {
                Bingo_Log::warning(SCRIPT_NAME . ' init redis fail.');
                exit(0);
            }

            $filePath = $jobInfo['filepath'];
            $followType = $jobInfo['follow_type'];
            $begTime = microtime(true);
            $boolRes = self::fetchUidsFromFile($jobId, $tagId, $filePath, $followType);
            $endTime = microtime(true);
            Bingo_Log::pushNotice('file_cost', round($endTime - $begTime, 3));
            if ($boolRes !== true) {
                Bingo_Log::warning('fetchUidsFromFile fail, jobInfo = ' . serialize($jobInfo));
                exit(0);
            }
        } else {

            $followId   = intval($jobInfo['follow_id']);
            $followType = intval($jobInfo['follow_type']);

            if ($jobInfo['tag_info']['tag_status'] == self::TAG_STATUS_NEW) {
                $groupId = intval($jobInfo['tag_info']['group_id']);
                $groupType = intval($jobInfo['tag_info']['group_type']);
                $begTime = microtime(true);
                self::fetchUidsByFollowIdFollowType($followId, $followType, $tagId, $groupId, $groupType);
                $endTime = microtime(true);
                Bingo_Log::pushNotice('follow_cost', round($endTime - $begTime, 3));

                // update taginfo status
                $arrReq = array(
                    'tag_id' => $tagId,
                    'status' => self::TAG_STATUS_INIT,
                );
                self::serviceCall('newpush', 'updateTag', $arrReq);
            }
        }

        $arrReq = array(
            'tag_id' => $tagId,
        );
        self::serviceCall('newpush', 'syncJobStatus', $arrReq);

        Bingo_Log::notice('run success.');
        return true;
    }

    /**
     * @brief
     *      从文件获取uid列表并存储
     * @param $jobId
     * @param $tagId
     * @param $file
     * @internal param $ jobId: job id*      jobId: job id
     *      tagId: tag id
     *      file : 文件路径
     * @return ture or false
     */
    private static function fetchUidsFromFile($jobId, $tagId, $file, $followType = 2) {
        $business = self::$_FOLLOW_TYPE_FILE_NAME_SUFFIX_ARRAY[$followType];
        $filePath = DATA_PATH . '/' . $business . '_job_' . $jobId;
        $objOrpStorage = new Orp_Storage(self::ORP_SMALLAPP_TOKEN, "tieba");
        if (($fileOrp = $objOrpStorage->get($file)) == false) {
            Bingo_Log::warning("get $file from orp fail, errmsg = " . $objOrpStorage->getErrmsg());
            return false;
        }
        $tmpFile = $fileOrp->getTmpFilePath();
        system("cp $tmpFile $filePath", $errno);
        if (0 !== $errno) {
            Bingo_Log::warning("exec fail: [cp $tmpFile $filePath]");
            return false;
        }

        if ($file = fopen($filePath, 'r')) {

            $arrUids = array();
            //加入白名单，可能会和后面的uid重复，但是在加入redis的时候，重复的key不会会添加失败
            $handleWordServer = Wordserver_Wordlist::factory();
            $arrRes = $handleWordServer->getValueByKeys(array(self::BAZHU_WHITE_CONTROLLOR_KEY), self::REDIS_WORDLIST_NAME_BAZHU_WHITE);
            if (!$arrRes || $arrRes[self::BAZHU_WHITE_CONTROLLOR_KEY] == null) {
                Bingo_Log::warning('bazhu: get white list from wordlist fail. key = ' . self::BAZHU_WHITE_CONTROLLOR_KEY . '; res = ' . serialize($arrRes));
            } else {
                if ($arrRes[self::BAZHU_WHITE_CONTROLLOR_KEY] == 1) {
                    $arrMisInput = array(
                        'table'     => self::REDIS_WORDLIST_NAME_BAZHU_WHITE,
                        'start'     => 0,
                        'stop'      => 99, //白名单最多100个，太多的话，redis可能会失败
                    );
                    $arrWhiteUid = $handleWordServer->getTableContents($arrMisInput);
                    if (!$arrWhiteUid || !isset($arrWhiteUid['err_no']) || $arrWhiteUid['err_no'] != 0) {
                        Bingo_Log::warning('get white uids from redis-mis fail , input = ' . serialize($arrMisInput) . ', output = ' . serialize($arrWhiteUid));
                    } else {
                        $arrWhiteUid = $arrWhiteUid['ret'];
                        foreach ($arrWhiteUid as $key => $value) {
                            if (intval($key) > 0){
                                $arrUids[] = intval($key);
                            }
                        }
                    }
                } 
            }

            while (!feof($file)) {
                $strUid = trim(stream_get_line($file, 1024, "\n"));
                if (empty($strUid)) {
                    continue;
                }
                $arrUids[] = intval($strUid);

                /*
                $uid = fgets($file);
                $uid = preg_replace('/[\\r\\n ]+/', '', $uid);
                if ($uid != '') {
                    $arrUids[] = $uid;
                } */

                if (count($arrUids) == self::FILE_PER_TIME) {
                    $boolRes = self::storeUids($jobId, $tagId, $arrUids);
                    if ($boolRes !== true) {
                        Bingo_Log::warning('storeUids fail, jobId = ' . $jobId . ',tagId = ' . $tagId);
                        return false;
                    }
                    $arrUids = array();
                }
            }

            if (count($arrUids) > 0) {
                $boolRes = self::storeUids($jobId, $tagId, $arrUids);
                if ($boolRes !== true) {
                    Bingo_Log::warning('storeUids fail, jobId = ' . $jobId . ',tagId = ' . $tagId);
                    return false;
                }
            }

            fclose($file);
        }

        unlink($filePath);
        return true;
    }

    /**
     * @brief
     *      存储从文件获取来的uid列表
     * @param $jobId
     * @param $tagId
     * @param $arrUids
     * @internal param $ jobId  : job id*      jobId  : job id
     *      tagId  : tag id
     *      arrUids: 将要存储的uid列表
     * @return null
     */
    private static function storeUids($jobId, $tagId, $arrUids) {
        $arrUids = array_unique($arrUids);
        $arrSubJobIds = array();
        $baseSubJobId = $jobId * self::BUCKET_NUM;
        foreach ($arrUids as $uid) {
            $arrSubJob = array(
                'score'     => $uid,
                'member'    => $uid,
            );
            $arrSubJobIds[$baseSubJobId + $uid % self::BUCKET_NUM][] = $arrSubJob;
        }

        $arrReq = array();
        foreach ($arrSubJobIds as $uidSuffix => $arrSubJob) {
            $arrReq = array(
                'key'     => $uidSuffix . self::REDIS_SUBUID_SUFFIX_KEY,
            );
            $intMemberNum = count($arrSubJob);
            for ($i = 0; $i < $intMemberNum; $i += self::REDIS_SADD_PRE_NUM) {
                $tmpMember = array_slice($arrSubJob, $i, self::REDIS_SADD_PRE_NUM);
                $arrReq['members'] = $tmpMember;
                $boolRes = self::zadd($arrReq, 3);
                if ($boolRes !== true) {
                    Bingo_Log::warning('redis zadd fail');
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * @brief
     *      通过followId和followType获取uid列表并存储
     * @param $followId
     * @param $followType
     * @param $tagId
     * @param $groupId
     * @param $groupType
     * @internal param $ followId  : follow id*      followId  : follow id
     *      followType: follow type
     *      tagId     : tag id
     *      groupId   : group id
     *      groupType : group type
     * @return null
     */
    private static function fetchUidsByFollowIdFollowType($followId, $followType, $tagId, $groupId, $groupType) {
        switch ($followType) {
            case self::FOLLOW_TYPE_FORUM_BROADCAST:
            case self::FOLLOW_TYPE_OFFICIAL:
            case self::FOLLOW_TYPE_SUBSCRIBE_ACCOUNT:
                self::fetchUidsByForumId($followId, $tagId, $groupId, $groupType);
                break;
            case self::FOLLOW_TYPE_LIVE_ACCOUNT:
                self::fetchUidsByFriendId($followId, $tagId, $groupId, $groupType);
                break;
            default:
                break;
        }
    }

    /**
     * @brief
     *      通过吧ID获取该吧所有用户列表并存储
     * @param $forumId
     * @param $tagId
     * @param $groupId
     * @param $groupType
     * @return bool
     * @internal param $ forumId  : 吧ID*      forumId  : 吧ID
     *      tagId    : tag id
     *      groupId  : group id
     *      groupType: group type
     */
    private static function fetchUidsByForumId($forumId, $tagId, $groupId, $groupType) {
        $arrUids = array();
        $arrReq = array(
            'forum_id' => array(
                0 => $forumId,
            ),
        );
        $arrRes = self::serviceCall('forum', 'mgetBtxInfoEx', $arrReq);
        if (!$arrRes) {
            return false;
        }
        $memberCount = intval($arrRes['output'][$forumId]['statistics']['member_count']);
        if ($memberCount > self::MAX_FORUM_MEMBER) {
            $arrRes = self::getUidsFromOrpByFid($forumId, $tagId, $groupId, $groupType);
            if ($arrRes === false) {
                Bingo_Log::warning("getUidsFromOrpByFid fail, forumId: [$forumId], tagId: [$tagId], groupId: [$groupId], groupType: [$groupType]");
                $arrRes = self::getUidsFromDbByFid($forumId, $tagId, $groupId, $groupType);
                if ($arrRes === false) {
                    Bingo_Log::warning("getUidsFromDbByFid fail, forumId: [$forumId], tagId: [$tagId], groupId: [$groupId], groupType: [$groupType]");
                }
            }
        } else {
            $arrRes = self::getUidsFromDbByFid($forumId, $tagId, $groupId, $groupType);
            if ($arrRes === false) {
                Bingo_Log::warning("getUidsFromDbByFid fail, forumId: [$forumId], tagId: [$tagId], groupId: [$groupId], groupType: [$groupType]");
            }
        }
    }

    /**
     * @brief 根据吧id从orp文件来获取吧客户端关注用户
     * @param $forumId
     * @param $tagId
     * @param $groupId
     * @param $groupType
     * @return bool : arrUids 从orp文件中获取到的uid列表
     * @internal param $ :
     *      forumId
     *      tagId
     *      groupId
     *      groupType
     */
    private static function getUidsFromOrpByFid($forumId, $tagId, $groupId, $groupType) {

        $filePath = '';
        $date = date('Ymd', strtotime("-1 sunday", time()));
        $strSourceUrl = self::$_strUidFile . $date;
        $filePath = DATA_PATH . "/uids_$date";
        if (!file_exists($filePath) || (filesize($filePath) == 0)) {
            unlink($filePath);
            $strCmd = "wget $strSourceUrl -O $filePath";
            system($strCmd, $errno);
            if ($errno != 0) {
                Bingo_Log::warning("exec fail $i: [$strCmd]");
                unlink($filePath);
                return false;
            }
        }

        // 删除之前的文件
        $strCmd = "find " . DATA_PATH . " -type f | grep 'uids_' | grep -v 'uids_$date' | xargs rm -rf"; 
        if (false === system($strCmd)) {
            Bingo_Log::warning("exec fail: [$strCmd]");
        }

        $forumFile = DATA_PATH . "/forum_$forumId";
        if (false === unlink($forumFile)) {
            Bingo_Log::warning("unlink fail: [$forumFile]");
            return false;
        }

        $strCmd = "grep -E \"^" . $forumId . "[^0-9]+\" $filePath | awk '{if($1 != 0){print $2 > \"$forumFile\" }}'";
        if (false === system($strCmd)) {
            Bingo_Log::warning("exec fail: [$strCmd]");
            return false;
        }

        $objOpenFile = fopen($forumFile, 'r');
        if (!$objOpenFile) {
            return false;
        }

        $arrUids = array();
        $arrTmpUids = array();
        while (!feof($objOpenFile)) {
            $strUid = trim(stream_get_line($objOpenFile, 1024, "\n"));
            if (empty($strUid)) {
                continue;
            }
            $arrTmpUids[] = intval($strUid);

            if (count($arrTmpUids) % 100000 == 0) {
                $arrTmpUids = array_unique($arrTmpUids);
                foreach ($arrTmpUids as $item) {
                    $arrUids[$item % self::BUCKET_NUM][] = $item;
                }
                self::storeUidsInDb($forumId, self::FOLLOW_TYPE_FORUM_BROADCAST, $tagId, $groupId, $groupType, $arrUids);
                $arrUids = array();
                $arrTmpUids = array();
            }
        }
        $arrTmpUids = array_unique($arrTmpUids);
        foreach ($arrTmpUids as $item) {
            $arrUids[$item % self::BUCKET_NUM][] = $item;
        }
        self::storeUidsInDb($forumId, self::FOLLOW_TYPE_FORUM_BROADCAST, $tagId, $groupId, $groupType, $arrUids);
        return true;
    }

    /**
     * @brief 根据吧id从数据库备库来获取吧客户端关注用户
     * @param $forumId
     * @param $tagId
     * @param $groupId
     * @param $groupType
     * @return bool : arrUids 从DB中获取到的uid列表
     * @internal param $ :
     *      forumId
     *      tagId
     *      groupId
     *      groupType
     */
    private static function getUidsFromDbByFid($forumId, $tagId, $groupId, $groupType) {
        $db = self::getGradeDb();
        if (is_null($db)) {
            return false;
        }

        /* 不再需要这段逻辑
        $strSql = "SELECT COUNT(user_id) AS total_num FROM forum_member WHERE forum_id = $forumId and flag = 1;";
        $arrRes = array();
        for ($i = 1; $i <= 3; $i++) {
            $arrRes = $db->query($strSql);
            if (!$arrRes || empty($arrRes)) {
                Bingo_Log::warning("query mysql fail $i: $strSql");
                if ($i == 3) {
                    return false;
                }
                usleep(100000);
            }
        }
        $intTotalNum = intval($arrRes[0]['total_num']); */

        $userId = 0;
        $arrRes = array();
        $arrUids = array();
        $arrTmpUids = array();
        $limit = self::GRADE_PER_NUM;
        do {
            $strSql = "SELECT user_id FROM forum_member WHERE forum_id = $forumId and flag = 1 and user_id > $userId order by user_id limit $limit;";
            $arrRes = $db->query($strSql);
            $userId = $arrRes[count($arrRes) - 1]['user_id'];
            $arrTmpUids = array_merge($arrTmpUids, $arrRes);

            if (count($arrTmpUids) > 100000) {
                $arrTmpUids0 = array_slice($arrTmpUids, 0, 100000);
                foreach ($arrTmpUids0 as $item) {
                    $uid = $item['user_id'];
                    $arrUids[$uid % self::BUCKET_NUM][] = $uid;
                }
                self::storeUidsInDb($forumId, self::FOLLOW_TYPE_FORUM_BROADCAST, $tagId, $groupId, $groupType, $arrUids);
                $arrUids = array();
                $arrTmpUids1 = array_slice($arrTmpUids, 100000, count($arrTmpUids) - 100000);
                unset($arrTmpUids);
                $arrTmpUids = $arrTmpUids1;
            }
        } while (!empty($arrRes));

        if (count($arrTmpUids) > 0) {
            foreach ($arrTmpUids as $item) {
                $uid = $item['user_id'];
                $arrUids[$uid % self::BUCKET_NUM][] = $uid;
            }
            self::storeUidsInDb($forumId, self::FOLLOW_TYPE_FORUM_BROADCAST, $tagId, $groupId, $groupType, $arrUids);
        }

        return true;
    }

    /**
     * @brief
     *      通过好友关注关系获取uid列表并存储
     * @param $uid
     * @param $tagId
     * @param $groupId
     * @param $groupType
     * @internal param $ uid: 用户ID*      uid: 用户id
     * @return array
     */
    private static function fetchUidsByFriendId($uid, $tagId, $groupId, $groupType) {

        $arrTmpUids = array();
        $retryCount = 0;
        $totalCount = 500;
        $currCount = 0;
        $offset = 0;
        while ($offset < $totalCount && $currCount < 10) {
            $arrReq = array(
                'user_id' => $uid,
                'type'    => 1,
                'offset'  => $offset,
                'limit'   => 500,
            );
            $arrRes = Tieba_Service::call('user', 'getFollowAndFollowedByUid', $arrReq, null, null, 'post', 'php', 'utf-8');
            if (!$arrRes) {
                $holdSec = 10;
                if (++$retryCount > 2) {
                    $holdSec = 30;
                }
                Bingo_Log::warning("getFollowAndFollowedByUid fail, sleep $holdSec secs. req: " . serialize($arrReq));
                sleep($holdSec);
                continue;
            }
            if ($arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('getFollowAndFollowedByUid fail, sleep 1 secs. req: ' . serialize($arrReq) . ', res: ' . serialize($arrRes));
                $retryCount++;
                sleep(1);
                continue;
            }
            if (!isset($arrRes['fans']['total_count']) || !isset($arrRes['fans']['user_infos'])) {
                Bingo_Log::warning('getFollowAndFollowedByUid fail, sleep 1 secs. req: ' . serialize($arrReq) . ', res: ' . serialize($arrRes));
                $retryCount++;
                sleep(1);
                continue;
            }

            $retryCount = 0;
            $totalCount = intval($arrRes['fans']['total_count']);
            $offset += count($arrRes['fans']['user_infos']);
            foreach ($arrRes['fans']['user_infos'] as $item) {
                $arrTmpUids[] = $item['user_id'];
            }
            $currCount++;
        }

        $currCount = 0;
        $offset = 0;
        $totalCount = 100;
        $retryCount = 0;

        /*
        $arrReq = array(
            'user_id' => $uid,
        );
        $arrRes = self::serviceCall('friend', 'getAllFriendCount', $arrReq);
        if (isset($arrRes['errno']) && $arrRes['errno'] == Tieba_Errcode::ERR_SUCCESS) {
            $totalCount = intval($arrRes['data']['friend_count']);
        } */

        while ($offset < $totalCount && $currCount < 50) {
            $arrReq = array(
                'user_id' => $uid,
                'offset'  => $offset,
                'limit'   => 100,
            );
            $arrRes = Tieba_Service::call('friend', 'getFriendList', $arrReq, null, null, 'post', 'php', 'utf-8');
            if (!$arrRes) {
                $holdSec = 10;
                if (++$retryCount > 2) {
                    $holdSec = 30;
                }
                Bingo_Log::warning("getFriendList fail, sleep $holdSec secs. req: " . serialize($arrReq));
                sleep($holdSec);
                continue;
            }
            if ($arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('getFriend fail, sleep 1 secs. req: ' . serialize($arrReq) . ', res: ' . serialize($arrRes));
                $retryCount++;
                sleep(1);
                continue;
            }
            if (!isset($arrRes['fans']['total_count']) || !isset($arrRes['fans']['user_infos'])) {
                Bingo_Log::warning('getFriend fail, sleep 1 secs. req: ' . serialize($arrReq) . ', res: ' . serialize($arrRes));
                $retryCount++;
                sleep(1);
                continue;
            }

            $retryCount = 0;
            $totalCount = intval($arrRes['fans']['total_count']);
            $offset += count($arrRes['data']['friend_info']);
            foreach ($arrRes['data']['friend_info'] as $item) {
                $arrTmpUids[] = $item['user_id'];
            }
            $currCount++;
        }
        $arrTmpUids = array_unique($arrTmpUids);
        $arrUids = array();

        // 好友关系的存储，有点怪异，数量不大
        foreach ($arrTmpUids as $item) {
            $arrUids[$item % self::BUCKET_NUM][] = $item;
        }
        
        self::storeUidsInDb($uid, self::FOLLOW_TYPE_LIVE_ACCOUNT, $tagId, $groupId, $groupType, $arrUids);
    }

    /**
     * @brief
     *      将uid存储入db
     * @param $followId
     * @param $followType
     * @param $tagId
     * @param $groupId
     * @param $groupType
     * @param $arrUids
     * @internal param $ followId  : follow id*      followId  : follow id
     *      followType: follow type
     *      groupId   : group id
     *      groupType : group type
     *      arrUids   : 将要存储的uid列表
     * @return null
     */
    private static function storeUidsInDb($followId, $followType, $tagId, $groupId, $groupType, $arrUids) {
        $arrReq = array();
        $objRalMulti = new Tieba_Multi('storeUids');
        foreach ($arrUids as $key => $uidGroup) {
            $arrInput = array(
                'serviceName' => 'newpush',
                'method'      => 'createTagUsers',
                'input'       => array(
                    'tag_key' => $tagId * self::BUCKET_NUM + $key,
                    'group_id' => $groupId,
                    'group_type' => $groupType,
                    'status' => 0,
                    'uids' => $uidGroup,
                ),
            );
            $ralKey = "storeuids_$key";
            $arrReq[$ralKey] = $arrInput;
            $objRalMulti->register($ralKey, new Tieba_Service('newpush'), $arrInput);
        }

        $arrMultiRes = $objRalMulti->call(); 
        foreach ($arrMultiRes as $key => $arrServiceRes) {
            unset($arrReq[$key]);
        }

        // handle failure requests
        if (count($arrReq) > 0) {
            usleep(100000);
            $objRalMulti = new Tieba_Multi('storeUids');
            foreach ($arrReq as $key => $failReq) {
                $objRalMulti->register($key, new Tieba_Service('newpush'), $failReq);
            }
            $objRalMulti->call();
        }

        // add white list
        //self::addWhiteList($tagId, $groupId, $groupType);
    }

    /**
     * @brief
     *      将uid存储入db
     * @param $followId
     * @param $followType
     * @param $tagId
     * @param $groupId
     * @param $groupType
     * @param $arrUids
     * @internal param $ followId  : follow id*      followId  : follow id
     *      followType: follow type
     *      groupId   : group id
     *      groupType : group type
     *      arrUids   : 将要存储的uid列表并存储
     * @return null
     */
    private static function storeUidsInDbOld($followId, $followType, $tagId, $groupId, $groupType, $arrUids) {
        $num = 0;
        $arrReq = array();
        $arrFails = array();
        $objRalMulti = new Tieba_Multi('storeUids');
        foreach ($arrUids as $key1 => $uidGroup) {
            if (++$num % 10 == 0) {
                $arrMultiRes = $objRalMulti->call();
                $objRalMulti = new Tieba_Multi('storeUids');
                foreach ($arrMultiRes as $key2 => $arrServiceRes) {
                    if (Tieba_Errcode::ERR_SUCCESS != $arrServiceRes['errno']) {
                        $arrFails[$key1] = $arrReq[$key2];
                    }
                }
            }
            $arrInput = array(
                'serviceName' => 'newpush',
                'method'      => 'createTagUsers',
                'input'       => array(
                    'tag_key' => $tagId * self::BUCKET_NUM + $key1,
                    'group_id' => $groupId,
                    'group_type' => $groupType,
                    'status' => 0,
                    'uids' => $uidGroup,
                ),
            );
            $ralKey = "storeuids_$num";
            $arrReq[$ralKey] = $arrInput;
            $objRalMulti->register($ralKey, new Tieba_Service('newpush'), $arrInput);
        }
        if ($num % 10 != 0) {
            $arrMultiRes = $objRalMulti->call(); 
            foreach ($arrMultiRes as $key => $arrServiceRes) {
                if (Tieba_Errcode::ERR_SUCCESS != $arrServiceRes['errno']) {
                    $arrFails[$key] = $arrReq[$key];
                }
            }
        }

        // handle failure requests
        usleep(100000);
        $num = 0;
        $objRalMulti = new Tieba_Multi('storeUids');
        foreach ($arrFails as $key => $failReq) {
            if (++$num % 10 == 0) {
                $arrMultiRes = $objRalMulti->call();
                $objRalMulti = new Tieba_Multi('storeUids');
            }
            $arrInput = $arrFails[$key]; 
            $objRalMulti->register($key, new Tieba_Service('newpush'), $arrInput);
        }
        if ($num % 10 != 0) {
            $arrMultiRes = $objRalMulti->call();
        }

        // add white list
        //self::addWhiteList($tagId, $groupId, $groupType);
    }

    /**
     * @brief
     *      带重试功能的zadd操作
     * @param
     *      arrInput  : 请求参数
     *      retryCount: 重试次数
     * @param int $retryCount
     * @return true or false
     */
    private static function zadd($arrInput, $retryCount=self::RETRY_COUNT) {
        if ($retryCount > 0) {
            $arrRes = self::$_redis->ZADD($arrInput);
            if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['err_no']) {
                Bingo_Log::warning(SCRIPT_NAME . " fail to zadd $retryCount! req: " .  serialize($arrInput) . ' res: ' . serialize($arrRes));
                usleep(10000);
                return self::zadd($arrInput, $retryCount - 1);
            }
            return true;
        }
        return false;
    }

    /**
     * @brief
     *      带重试功能的Tieba_Service::call
     * @param $strService
     * @param $strMethod
     * @param $arrInput
     * @param int $retryCount
     * @return bool
     * @internal param $ strService: service*      strService: service
     *      strMethod : method
     *      arrInput  : 请求参数
     *      retryCount: 重试次数
     */
    private static function serviceCall($strService, $strMethod, $arrInput, $retryCount=self::RETRY_COUNT) {
        if ($retryCount > 0) {
            $arrRes = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
            if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']) {
                Bingo_Log::warning(SCRIPT_NAME . " fail to $strMethod $retryCount! req: " .  serialize($arrInput) . ' res: ' . serialize($arrRes));
                sleep(1);
                self::serviceCall($strService, $strMethod, $arrInput, $retryCount - 1);
            } else {
                return $arrRes;
            }
        } else {
            return false;
        }
    }

    /**
     * 将白名单用户添加到用户表中
     * 单个单个添，防止某白名单用户已在库中，导致其他用户失败
     * @param: tagId, groupId, groupType
     * @return: null
     */
    private static function addWhiteList($tagId, $groupId, $groupType) {
        $objRalMulti = new Tieba_Multi('storeUids');
        foreach (self::$_whiteList as $uid) {
            $arrInput = array(
                'serviceName' => 'newpush',
                'method'      => 'createTagUsers',
                'input'       => array(
                    'tag_key' => $tagId * self::BUCKET_NUM + $uid % self::BUCKET_NUM,
                    'group_id' => $groupId,
                    'group_type' => $groupType,
                    'status' => 0,
                    'uids' => array($uid),
                ),
            );
            $objRalMulti->register("storeuids_$uid", new Tieba_Service('newpush'), $arrInput);
        }
        $objRalMulti->call();
    }
}
