<?php
/**
 * @Author: he<PERSON><PERSON>
 * @Created Time: 2015-12-14 18:06:38
 * @File Name: PushEntry.php
 * @Description: 推送子任务入口
 *
 */

define ('SCRIPT_NAME', basename(__FILE__, ".php"));
define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../' );
define ('LOG_PATH',  ROOT_PATH . "log/app/newpush");

Newpush_Scripts_PushEntry::process();

class Newpush_Scripts_PushEntry {

    /**
     * @brief 初始化
     * @param null
     * @return null
     */
    private static function init() {

        if (!is_dir(LOG_PATH)) {
            $strCmd = "mkdir -p " . LOG_PATH;
            system($strCmd);
        }
    }

    /**
     * @brief 推送子任务入口
     * @param null
     * @return null
     */
    public static function process() {

        self::init();
        while (true) {
            $date = getdate();
            if ($date['minutes'] >= 59) {
                break;
            }

            $logPath = LOG_PATH . "/" . SCRIPT_NAME . ".log." . date("YmdH");

            // 线上php启动加载时间太长
            $cmd = "curl -i \"http://nj.service.tieba.baidu.com/service/newpush?method=pushSubJob&format=json\" --connect-timeout 1 >> $logPath 2>&1 &";
            system($cmd);
            sleep(1);
        }
    }
}
