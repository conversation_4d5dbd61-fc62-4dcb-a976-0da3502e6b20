<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-05-14 16:16:32
 * @comment json接口
 * @version
 */
class likeTagAction extends Util_Base {

    public function execute(){
        try {
            //参数获取





            //数据输出，初始化为默认值，自行修改
            $retData=array();
            $retData['arrOutput'] = array();

            // 默认成功返回值
            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,Tieba_Error::getUserMsg(Tieba_Errcode::ERR_SUCCESS),$retData);
        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), '未知错误');    
        }
    }
}
