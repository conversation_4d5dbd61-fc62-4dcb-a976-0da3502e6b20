<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015:05:14 16:16:45
 * @version
 * @structs & methods(copied from idl.)
*/

define("MODULE", "Strategy_dl");


class Dl_Strategy_Strategy extends Lib_Util_Service {

    const MODULE_LOG_NAME = '[new push strategy] ';

    const SERVICE_NAME = "Dl_Strategy_Strategy";

    const DATABASE_NAME = "forum_newpush";

    const CHAR_SET_UTF8 = 'utf8';
    
    const REDIS_NAME = 'newpush';

    const REDIS_MAX_NUM = 500;

    const REDIS_DEFAULT_NUM = 300;

    // *********** 吧主相关数据库信息 add by liukaining ,begin

    const DATABASE_NAME_BAZHU = "forum_bztask";
    const TABLE_NAME_BZPUSHUSER = "bzpushuser";
    const TABLE_NAME_TASK_LIST = "task_list";
    const TABLE_NAME_TASK_LIMIT_FID = "limit_fid";
    const TABLE_NAME_TASK_LIMIT_DIR = "limit_fdir";
    const CAN_PUSH_YES = 1;


    /**
     * 初始化DB实例
     * @return 返回数据库实例
     */
    protected static function _getBazhuDB(){

        $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME_BAZHU);
        $objTbMysql->charset(self::CHAR_SET_UTF8);
        if($objTbMysql && $objTbMysql->isConnected()) {
            return $objTbMysql;
        } else {
            Lib_Util_Log::warning("db connect fail.".self::DATABASE_NAME);
            return null;
        }
    }
    


    /**
     * 获取吧主推送用户数据
     * <EMAIL>
     * @param
     * @return
     *
     * */
    public static function getBzpushuser($arrInput) {
        $db = self::_getBazhuDB();
        if (!$db) {
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $intLimit = Lib_Util_Conf::FOLLOW_TYPE_NUMBER;
        $lastUid = $arrInput['last_uid'];
        $intTagKey = $arrInput['tag_key'];
        $boolCanPush = self::CAN_PUSH_YES;
        $strSQL = '';
        if ($arrInput['allow_all'] == 1){
            $strSQL = "SELECT distinct user_id from bzpush_user where tag_key = $intTagKey and user_id > $lastUid order by user_id asc limit $intLimit " ;
        } else {
            if(count($arrInput['role_limit']) > 0){
                $strRoleLimit = implode(",", $arrInput['role_limit']);
                $strRoleSql = "role_id in ($strRoleLimit) ";
            } else {
                $strRoleSql = '';
            }
            if(count($arrInput['dir_level1_limit']) > 0){
                $strDir1Limit = implode(",",$arrInput['dir_level1_limit']);
                $strDir1Sql = " (forum_level1_dir in ($strDir1Limit)) ";
            } else {
                $strDir1Sql = '';
            }
            if(count($arrInput['dir_level2_limit']) > 0){
                $strDir2Limit = implode(",",$arrInput['dir_level2_limit']);
                $strDir2Sql = " (forum_level2_dir in ($strDir2Limit)) ";
            } else {
                $strDir2Sql = '';
            }
            if(count($arrInput['forumid_limit']) > 0){
                $strForumLimit = implode(",",$arrInput['forumid_limit']);
                $strFidSql = " or (forum_id in ($strForumLimit)) ";
            } else {
                $strFidSql = '';
            }

            $strLimitCond = $strRoleSql;
            if (($strDir1Sql != '') && ($strDir2Sql == '')  ){
                $strLimitCond = $strRoleSql." and "."(".$strDir1Sql.") ";
            } else if (($strDir1Sql == '') && ($strDir2Sql != '') ){
                $strLimitCond = $strRoleSql." and "."(".$strDir2Sql.") ";
            } else if (($strDir1Sql != '') && ($strDir2Sql != '')){
                $strLimitCond = $strRoleSql." and "."(".$strDir1Sql." or ".$strDir2Sql.") ";
            } else {
                $strLimitCond = $strRoleSql;
            }


            $minGrade = $arrInput['min_grade'];
            $maxGrade = $arrInput['max_grade'];
            $strSQL = "SELECT distinct user_id from bzpush_user where tag_key = $intTagKey and user_id > $lastUid "." and ((grade >= $minGrade and grade <= $maxGrade and ".$strLimitCond." ) ".$strFidSql." ) order by user_id asc limit $intLimit" ;

        }
        Bingo_Log::warning("bzpush_SQL:".serialize($strSQL));
        $arrOutput = $db->query($strSQL);

        unset($arrInput);
        if(false === $arrOutput) {
            Lib_Util_Log::warning("bz_pushuser select fail! [output:" . serialize($arrOutput) . "error:" . $db->error() . "sql:" . $db->getLastSQL() . "]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }


        return self::_successRet($arrOutput);

    }


    /**
     * @param $arrInput
     * @return array
     */
    public static function getTaskDetailInfo($arrInput){
        $db = self::_getBazhuDB();
        if (!$db) {
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $intTaskId = intval($arrInput['task_id']);
        $strSqlTaskList = "select canmaster,canslaver,caneditor,minlevel,maxlevel,allowall from task_list where taskid = $intTaskId";
        $arrOutput = $db->query($strSqlTaskList);
        if(false === $arrOutput) {
            Lib_Util_Log::warning("task_list select fail! [output:" . serialize($arrOutput) . "error:" . $db->error() . "sql:" . $db->getLastSQL() . "]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_successRet($arrOutput);


    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getTaskLimitId($arrInput){
        $db = self::_getBazhuDB();
        if (!$db) {
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $intTaskId = intval($arrInput['task_id']);
        $strSqlLimitFid = "select forumid from limit_fid where taskid = $intTaskId";
        $arrOutput = $db->query($strSqlLimitFid);
        if(false === $arrOutput) {
            Lib_Util_Log::warning("limit_fid select fail! [output:" . serialize($arrOutput) . "error:" . $db->error() . "sql:" . $db->getLastSQL() . "]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_successRet($arrOutput);


    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getTaskLimitDir($arrInput){
        $db = self::_getBazhuDB();
        if (!$db) {
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $intTaskId = intval($arrInput['task_id']);
        $strSqlLimitFdir = "select level1name,level2name from limit_fdir where taskid = $intTaskId";
        $arrOutput = $db->query($strSqlLimitFdir);
        if(false === $arrOutput) {
            Lib_Util_Log::warning("limit_fdir select fail! [output:" . serialize($arrOutput) . "error:" . $db->error() . "sql:" . $db->getLastSQL() . "]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_successRet($arrOutput);


    }


    // ******************** end ********

    /**
     * 初始化DB实例
     * @return 返回数据库实例
     */
    protected static function _getDB(){

        $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME);
        $objTbMysql->charset(self::CHAR_SET_UTF8);
        if($objTbMysql && $objTbMysql->isConnected()) {
            return $objTbMysql;
        } else {
            Lib_Util_Log::warning("db connect fail.".self::DATABASE_NAME);
            return null;
        }
    }

    /**
     * 初始化Redis实例
     * @return 返回Redis实例
     */
    protected static function _getRedis(){
        $objRedis = new Bingo_Cache_Redis(self::REDIS_NAME);

        if(is_null($objRedis)) {
            Lib_Util_Log::warning("init redis fail" . self::REDIS_NAME);
            return null;
        }
        return $objRedis;
    }
    
    /**
     * @param
     * @return
     *
     * */
    public static function getUidFromRedisBySub($arrInput) {
        if (!isset($arrInput['key']) || empty($arrInput['key']) 
            || !isset($arrInput['start'])
            || !isset($arrInput['stop']) ) {
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
        $redis = self::_getRedis();
        if (!$redis) {
            return self::_errRet(Tieba_Errcode::ERR_BUSI_CONN_REDIS_FAIL);
        }

        $strKey = strval($arrInput['key']);
        $intStart = intval($arrInput['start']);
        $intStop = intval($arrInput['stop']);
        if ($intStop - $intStart >= self::REDIS_MAX_NUM){
            $intStop = $intStart + self::REDIS_DEFAULT_NUM;
        }
        $arrInput = array(
            'key'   => $strKey,
            'start' => $intStart,
            'stop'  => $intStop,
        );
        $arrOutput = $redis->ZRANGE($arrInput);
        if (!$arrOutput || !isset($arrOutput['err_no']) || $arrOutput['err_no'] != 0) {
            Lib_Util_Log::warning("ZRANGEWITHSCORES fail , input = " . serialize($arrInput) . ", output = " . serialize($arrOutput));
            return self::_errRet($arrOutput['err_no']);
        }

        return self::_successRet($arrOutput['ret']);

    }


    /**
     * 根据tarKey获取用户列表和弱关系群组信息
     * @param $arrInput 入参必须携带tarKey和Start
     * @return array 返回用户列表
     */
    public static function getUidByTagKey($arrInput) {
        $db = self::_getDB();
        if (!$db) {
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $res = $db->select('newpush_tag_user', $arrInput['arrFields'], $arrInput['conditions']);
        unset($arrInput);
        if(false === $res) {
            Lib_Util_Log::warning("newpush_tag_user select fail! [output:" . serialize($res) . "error:" . $db->error() . "sql:" . $db->getLastSQL() . "]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_successRet($res);
    }

    /**
     * 根据followId和followType获取强关系的群组信息
     * @param $arrInput 入参必须携带followId和followType
     * @return array 返回群组信息
     */
    public static function getGroupInfoByFollow($arrInput) {
        $db = self::_getDB();
        if (!$db) {
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $res = $db->select('newpush_tag_info', $arrInput['arrFields'], $arrInput['conditions']);
        unset($arrInput);
        if(false === $res) {
            Lib_Util_Log::warning("getGroupInfoByFollow select fail! [output:" . serialize($res) . "error:" . $db->error() . "sql:" . $db->getLastSQL() . "]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_successRet($res);
    }

}
