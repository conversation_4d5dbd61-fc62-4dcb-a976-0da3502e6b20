[insertMsginfo]
type : update
output_key : results
use_transaction : 0
[.@command]
sql : insert newpush_msginfo set tag_id= {tag_id:n}, tag_name = {tag_name:s}, msg_type = {msg_type:n}, user_id = {user_id:n},content ={content:s},mid={mid:n},job_id= {jobid:n},sid= {sid:n},createtime= {createtime:n}
abort_on_fail : 1

[insertMsglist]
type : update
output_key : results
use_transaction : 0
[.@command]
sql : insert newpush_group_msglist set fromid= {gid:n}, sid={sid:n},mid={mid:n},type={type:n},createtime={createtime:n}
abort_on_fail : 1

[insertUserMsglist]
type : update
output_key : results
use_transaction : 0
[.@command]
sql : insert newpush_user_msglist set fromid= {fromid:n}, toid= {toid:n}, sid={sid:n},mid={mid:n},flag={flag:n},type={type:n},createtime={createtime:n}
abort_on_fail : 1


[getMsglistByToid]
type : query
output_key : results
[.@command]
sql : select fromid,toid,sid,mid,flag,type,createtime from newpush_user_msglist where toid in ({toids:r}) and fromid not in ({toids:r}) order by createtime desc limit {offset:n}, {count:n}

[getMsglistByToidAndTime]
type : query
output_key : results
[.@command]
sql : select fromid,toid,sid,mid,flag,type,createtime from newpush_user_msglist where toid in ({toids:r}) and fromid not in ({toids:r}) and createtime >= {startTime:n} and createtime < {endTime:n} order by createtime desc limit {offset:n}, {count:n}


[getUserMsglistBySid]
type : query
output_key : results
[.@command]
sql : select fromid,toid,sid,mid,flag,type,createtime from newpush_user_msglist where toid in ({toids:r}) and sid > {startsid:n} and sid <= {stopsid:n}

#删除消息
[deleteMsginfo]
type : update
output_key : results
use_transaction : 0
rollback_on_abort : 1
[.@command]
sql : delete from newpush_msginfo where createtime < {createtime:n}
abort_on_fail : 1

#删除消息拉链
[deleteJobMsg]
type : update
output_key : results
use_transaction : 0
rollback_on_abort : 1
[.@command]
sql : delete from newpush_group_msglist where fromid  in ({gids:r}) and createtime < {createtime:n}
abort_on_fail : 1


[getMsginfoByMid]
type : query
output_key : results
[.@command]
sql : select tag_id,tag_name,msg_type,user_id,content,mid,sid,job_id,createtime from newpush_msginfo where mid in ({mids:r})

[getMsginfoByJobid]
type : query
output_key : results
[.@command]
sql : select tag_id,tag_name,msg_type,user_id,content,mid,sid,job_id,createtime from newpush_msginfo where job_id in ({jobids:r}) order by createtime desc limit 1;

[getMsginfoByTime]
type : query
output_key : results
[.@command]
sql : select tag_id,tag_name,msg_type,user_id,content,mid,sid,job_id,createtime from newpush_msginfo where createtime > {starttime:n} and createtime < {endtime:n} limit 10;

[getMsglistByGidSid]
type : query
output_key : results
[.@command]
sql : select fromid,sid,mid,type,createtime from newpush_group_msglist where fromid= {gid:n} and sid >= {startsid:n} and sid <= {stopsid:n}

[getMsglistByGidMid]
type : query
output_key : results
[.@command]
sql : select fromid,sid,mid,type,createtime from newpush_group_msglist where fromid = {gid:n} and mid in ({mids:r})

[getMsglistByGidCreatetime]
type : query
output_key : results
[.@command]
sql : select fromid,sid,mid,type,createtime from newpush_group_msglist where fromid = {gid:n} and createtime > {starttime:n} and createtime < {endtime:n} limit {count:n}

[setUserMaskStat]
type : update
output_key : results
[.@command]
sql : insert into newpush_maskstatus set uid={userId:n},status={isMask:n},type_bitmap={type:s},update_time={update_time:n} on duplicate key update status={isMask:n},type_bitmap={type:s},update_time={update_time:n}

[getUserMaskStat]
type : query
output_key : results
[.@command]
sql : select uid,status,type_bitmap,update_time from newpush_maskstatus where uid = {userId:n}

[getLastSidByGid]
type : query
output_key : results
[.@command]
sql : select max(sid) from newpush_group_msglist where gid = {gid:n}

[getLastSidByUid]
type : query
output_key : results
[.@command]
sql : select max(sid) from newpush_user_msglist where toid = {toid:n}
