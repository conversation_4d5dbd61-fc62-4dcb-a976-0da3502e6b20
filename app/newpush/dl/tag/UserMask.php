<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015:11:06 18:17:37
 * @version
 * @structs & methods(copied from idl.)
 */


define("MODULE", "Dl_Tag_UserMask");

class Dl_Tag_UserMask
{

    const SERVICE_NAME = "Dl_Tag_UserMask";

    protected static $_db = null;
    protected static $_conf = null;
    protected static $_use_split_db = false;
    protected static $_tag_table_name = "newpush_maskstatus";
    protected static $_table_fields = "uid, type_bitmap";
    protected static $_table_atoi_fields = array('uid', 'type_bitmap');

    private static $_db_ral_service_name = Lib_Util_Conf::DB_RAL_SERVICE_NAME;

    /**
     * @brief get mysql obj.
     * @return: obj of Bd_DB, or null if connect fail.
     **/
    private static function _getDB()
    {
        if (self::$_db) {
            return self::$_db;
        }

        self::$_db = new Bd_DB();
        if (self::$_db == null) {
            Bingo_Log::warning("new bd_db fail.");
            return null;
        }

        Bingo_Timer::start('dbinit');
        $ret = self::$_db->ralConnect(self::$_db_ral_service_name);
        Bingo_Timer::end('dbinit');
        if (!$ret) {
            Bingo_Log::warning("bd db ral connect fail. db ral name: [" . self::$_db_ral_service_name . "]");
            self::$_db = null;
            return null;
        }

        //set db connection to utf8
        self::$_db->charset("utf8");
    
        return self::$_db;
    }

    /**
     * @brief init
     * @return: true if success. false if fail.
     **/
    private static function _init()
    {
        return true;
    }


    /**
     * @brief error return
     * @param
     *    int32_t errno
     * @return
     *    array ret
     **/
    private static function _errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @brief
     * @param:
     * @return: $arrOutput
     **/
    public static function insertUserMask($arrInput)
    {

        if (!isset($arrInput['uid'])
            || !isset($arrInput['type_bitmap'])
            || !isset($arrInput['update_time'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $uid = intval($arrInput['uid']);
        $status = 0;
        $type_bitmap = intval($arrInput['type_bitmap']);
        $update_time = intval($arrInput['update_time']);

        $db = self::_getDB();
        if (empty($db)) {
            Bingo_Log::warning('get db error.');
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $insertSql = "insert into " . self::$_tag_table_name . "(uid, status, type_bitmap, update_time)
                        values({$uid}, {$status}, {$type_bitmap}, {$update_time})";

        $res = $db->query($insertSql);
        if (false === $res) {
            Bingo_Log::warning('db query failed. output[res=' . serialize($res) . ' error=' . $db->error() . ' sql=' . $db->getLastSQL() . ']');
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }

    /**
     * @brief
     * @param:
     * @return: $arrOutput
     **/
    public static function updateUserMask($arrInput)
    {

        if (!isset($arrInput['uid'])
            || !isset($arrInput['type_bitmap'])
            || !isset($arrInput['update_time'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $uid = intval($arrInput['uid']);
        $type_bitmap = intval($arrInput['type_bitmap']);
        $update_time = intval($arrInput['update_time']);

        $tag_id = intval($arrInput['tag_id']);

        $fields = array(
            'type_bitmap' => $type_bitmap,
            'update_time' => $update_time,
        );

        $conds = "uid={$uid}";

        $db = self::_getDB();
        if (empty($db)) {
            Bingo_Log::warning('get db error.');
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $res = $db->update(self::$_tag_table_name, $fields, $conds);
        if (false === $res) {
            Bingo_Log::warning('db query failed. output[res=' . serialize($res) . ' error=' . $db->error() . ' sql=' . $db->getLastSQL() . ']');
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }

    /**
     * @brief
     * @param:
     *    uint64_t tag_id
     * @return: $arrOutput
     *    push_tag_info data
     **/
    public static function getUserMask($arrInput)
    {

        if (!isset($arrInput['uid'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $uid = intval($arrInput['uid']);

        $db = self::_getDB();
        if (empty($db)) {
            Bingo_Log::warning('get db error.');
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $conds = " uid={$uid}";
        $res = $db->select(self::$_tag_table_name, self::$_table_fields, $conds);
        if (false === $res) {
            Bingo_Log::warning('db query failed. output[res=' . serialize($res) . ' error=' . $db->error() . ' sql=' . $db->getLastSQL() . ']');
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        if (empty($res)) {
            $data = array();
        } else {
            $data = $res[0];
            $data = self::_atoi($data, self::$_table_atoi_fields);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );
        return $arrOutput;
    }

    /**
     * @brief
     * @param:
     *    string tag_name
     * @return: $arrOutput
     *    push_tag_info data
     **/
    public static function getUserMasksByUids($arrInput)
    {
        if (!isset($arrInput['uids'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $uids = $arrInput['uids'];

        $db = self::_getDB();
        if (empty($db)) {
            Bingo_Log::warning('get db error.');
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $conds = ' uid in (';
        $isFirst = 1;
        foreach($uids as $uid) {
            if ($isFirst == 1) {
                $conds = $conds . $uid;
                $isFirst = 0;
            } else {
                $conds = $conds . ', ' . $uid;
            }
        }
        $conds = $conds . ')';
        $res = $db->select(self::$_tag_table_name, self::$_table_fields, $conds);
        if (false === $res) {
            Bingo_Log::warning('db query failed. output[res=' . serialize($res) . ' error=' . $db->error() . ' sql=' . $db->getLastSQL() . ']');
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $data = array();
        if (!empty($res)) {
            $data = $res;
            $data = self::_atoi_array($data, self::$_table_atoi_fields);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );
        return $arrOutput;
    }

    /**
     * @brief atoi
     * @param
     *   array input
     *   array kesy
     * @return
     *   array ret
     **/
    private static function _atoi_array($inputArray, $keys) {
        $arraySize = count($inputArray, 0);
        for ($index = 0; $index < $arraySize; $index++) {
            $input = $inputArray[$index];
            $inputArray[$index] = self::_atoi($input, $keys);
        }

        return $inputArray;
    }

    /**
     * @brief atoi
     * @param
     *   array input
     *   array keys
     * @return
     *   array ret
     **/
    private static function _atoi($input, $keys)
    {
        foreach ($keys as $k) {
            if (isset($input[$k])) {
                $input[$k] = intval($input[$k]);
            }
        }
        return $input;
    }
}
