[initJobR<PERSON>ord]
type : update
[.@command]
sql : insert into newpush_pushrecord (job_id, tag_id, follow_type, follow_id, starttime, endtime) values ({job_id:n}, {tag_id:n}, {follow_type:n}, {follow_id:n}, {starttime:n}, {endtime:n});

[updateJobRecord]
type : update
[.@command]
sql : update newpush_pushrecord set push_fail={push_fail:n},push_user_ios={push_user_ios:n},push_suc_ios={push_suc_ios:n},get_msg_ios={get_msg_ios:n},client_list_ios={client_list_ios:n},content_uv_ios={content_uv_ios:n},content_pv_ios={content_pv_ios:n},click_uv_ios={click_uv_ios:n},click_pv_ios={click_pv_ios:n},push_user_android={push_user_android:n},push_suc_android={push_suc_android:n},get_msg_android={get_msg_android:n},client_list_android={client_list_android:n},content_uv_android={content_uv_android:n},content_pv_android={content_pv_android:n},click_uv_android={click_uv_android:n},click_pv_android={click_pv_android:n},data_info={data_info:s} where job_id={job_id:n};

[getPushRecordsWithUid]
type : query
[.@command]
sql : select follow_id, starttime, push_fail, push_user_ios, push_suc_ios, get_msg_ios, client_list_ios, content_uv_ios, content_pv_ios, click_uv_ios, click_pv_ios, push_user_android, push_suc_android, get_msg_android, client_list_android, content_uv_android, content_pv_android, click_uv_android, click_pv_android from newpush_pushrecord where follow_id={user_id:n} and follow_type={follow_type:n} and endtime>{starttime:n} and endtime<={endtime:n} order by job_id desc limit {offset:n},{rn:n};

[getPushRecords]
type : query
[.@command]
sql : select  follow_id, starttime,push_fail, push_user_ios, push_suc_ios, get_msg_ios, client_list_ios, content_uv_ios, content_pv_ios, click_uv_ios, click_pv_ios, push_user_android, push_suc_android, get_msg_android, client_list_android, content_uv_android, content_pv_android, click_uv_android, click_pv_android from newpush_pushrecord where follow_type={follow_type:n} and endtime>{starttime:n} and endtime<={endtime:n} order by job_id desc limit {offset:n},{rn:n};

[getPushUserId]
type : query
[.@command]
sql : select distinct(follow_id) as user_id from newpush_pushrecord where follow_type={follow_type:n} and endtime>{starttime:n} and endtime<={endtime:n};

[getTotalRecordsWithUid]
type : query
[.@command]
sql : select sum(push_fail), sum(push_user_ios), sum(push_suc_ios), sum(get_msg_ios), sum(client_list_ios), sum(content_uv_ios), sum(content_pv_ios), sum(click_uv_ios), sum(click_pv_ios), sum(push_user_android), sum(push_suc_android), sum(get_msg_android), sum(client_list_android), sum(content_uv_android), sum(content_pv_android), sum(click_uv_android), sum(click_pv_android) from newpush_pushrecord where follow_id={user_id:n} and follow_type={follow_type:n} and endtime>{starttime:n} and endtime<={endtime:n};

[getTotalRecords]
type : query
[.@command]
sql : select sum(push_fail), sum(push_user_ios), sum(push_suc_ios), sum(get_msg_ios), sum(client_list_ios), sum(content_uv_ios), sum(content_pv_ios), sum(click_uv_ios), sum(click_pv_ios), sum(push_user_android), sum(push_suc_android), sum(get_msg_android), sum(client_list_android), sum(content_uv_android), sum(content_pv_android), sum(click_uv_android), sum(click_pv_android) from newpush_pushrecord where follow_type={follow_type:n} and endtime>{starttime:n} and endtime<={endtime:n};

[getTotalRecordCountWithUid]
type : query
[.@command]
sql : select count(job_id) from newpush_pushrecord where follow_id={user_id:n} and follow_type={follow_type:n} and endtime>{starttime:n} and endtime<={endtime:n};

[getTotalRecordCount]
type : query
[.@command]
sql : select count(job_id) from newpush_pushrecord where follow_type={follow_type:n} and endtime>{starttime:n} and endtime<={endtime:n};

[getJobidByTime]
type : query
[.@command]
sql : select job_id from newpush_pushrecord where starttime>{starttime:n} and starttime<={endtime:n};
