<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file Pushrecord.php
 * <AUTHOR>
 * @date 2015年11月10日
 * @brief 
 *
 **/
 
define("MODULE","pushrecord_dl");

class Dl_Pushrecord_Pushrecord {
    // db
    const MODULE_NAME       = 'newpush';
    const SERVICE_NAME      = "Dl_Pushrecord_Pushrecord";
    const DATABASE_NAME     = "DB_forum_mobusiness";
    const DB_CHARSET        = 'utf8';
    
    // redis
    const PUSHRECORD_REDIS_NAME = "pushcounter";
    const PUSH_RECORD_EXPIRE_TIME = 604800;
    const PUSH_RECORD_KEY_PREFIX_HM = 'push_record_hm';
    const PUSH_RECORD_KEY_PREFIX_IOS = 'push_record_ios';
    const PUSH_RECORD_KEY_PREFIX_ANDROID = 'push_record_android';
    const RECORD_TYPE_LIST = 1;

    protected static $_conf = null;
    protected static $_db = null;
    protected static $_use_split_db = false;
    protected static $_redis = null;
    // pushcounter set_type
    protected static $_arrMsgCountType = array(
        'PUSH_CLIENT_LIST'     => 1,
        'PUSH_CLIENT_CONTENT'  => 2,
        'PUSH_CLIENT_CLICK'    => 3,
        'PUSH_SERVER_GETMSG'   => 4,
        'PUSH_BAZHU_ACK'       => 101,
    );
    
    /**
     * @brief get mysql obj.
     * @return
     * @return : obj of Bd_DB, or null if connect fail.
     */
    private static function _getDB() {
        if (self::$_db) {
            return self::$_db;
        }
        
        self::$_db = new Bd_DB();
        if (self::$_db == null) {
            Bingo_Log::warning("new bd_db fail.");
            return null;
        }

        if(self::$_use_split_db) {
            $splitDBConfPath = ROOT_PATH . '/conf/db/';
            $splitDBConfFile = self::DATABASE_NAME . '.conf';
            Bingo_Timer::start('dbinit');
            $ret = self::$_db->enableSplitDB(self::DATABASE_NAME, $splitDBConfPath, $splitDBConfFile);
            Bingo_Timer::end('dbinit');
            if(!$ret) {
                Bingo_Log::warning('enable splitdb fail.');
                self::$_db = null;
                return null;
            }
            return self::$_db;
        } else {
            Bingo_Timer::start('dbinit');
            $ret = self::$_db->ralConnect(self::DATABASE_NAME);
            Bingo_Timer::end('dbinit');
            if(!$ret) {
                Bingo_Log::warning('bd db ral connect fail.');
                self::$_db = null;
                return null;
           }
           return self::$_db;
        }
    
        return null;
    }
    
    /**
     * @brief init
     * @param
     * @return : true if success. false if fail.
     */
    private static function _init() {
        if (self::_getDB() == null) {
            Bingo_Log::warning("init db fail.");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        if (self::$_conf == null) {
            $dlConfFile = '/app/' . self::MODULE_NAME . '/'. strtolower(self::SERVICE_NAME);
            self::$_conf = Bd_Conf::getConf($dlConfFile);
            if (self::$_conf == false) {
                Bingo_Log::warning ("init get conf fail.");
                return false;
            }
        }
        return true;
    }
    
    /**
     * @param $arrInput
     * @return
     */
    private static function _errRet($errno) {
        return array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg ($errno),
        );
    }
    
    /**
     * @param $arrInput
     * @return
     */
    public static function preCall($arrInput) {
        // pre-call hook
    }
    
    /**
     * @param $arrInput
     * @return
     */
    public static function postCall($arrInput) {
        // post-call hook
    }
    
    /**
     * @brief 消息推送实时统计获取redis
     * @param null
     * @return redis
     */
    private static function _getRedis() {
        if (self::$_redis) {
            return self::$_redis;
        }
        Bingo_Timer::start('redisinit');
        self::$_redis = new Bingo_Cache_Redis(self::PUSHRECORD_REDIS_NAME);
        Bingo_Timer::end('redisinit');
    
        if (false == self::$_redis || null == self::$_redis) {
            Lib_Util_Log::warning("Service_Pushrecord pushcounter redis fail:".serialize(Bd_Ral_Rpc::get_error()));
            return null;
        }
        return self::$_redis;
    }
    
    /**
     * 初始化key,设置expire,失败后业务上保证一次重试
     * @param $arrInput
     * @return 
     */
    public static function initTaskJob($arrInput) {
        if (!isset($arrInput['jobid'])) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        $redis = self::_getRedis();
        if ( $redis == null) {
            $redis = self::_getRedis();
            if ( $redis == null) {
                return self::_errRet(Tieba_Errcode::ERR_REDIS_CONN_FAIL);
            }
        }
        
        $jobid = intval($arrInput['jobid']);
        $timestamp = time() + self::PUSH_RECORD_EXPIRE_TIME;
        $input = array(
            'key' => self::_getRedisKey(self::PUSH_RECORD_KEY_PREFIX_HM, $jobid),
            'field' => 'jobid',
            'value'  => $jobid,
        );
        $ret = $redis->HSET($input);
        if ($ret == false || $ret['err_no'] != 0) {
            $ret = $redis->HSET($input);
            if ($ret== false || $ret['err_no'] != 0) {
                Lib_Util_Log::warning("Dl_Pushrecord initTask fail! hset failed!". serialize($input));
                return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
            }
        }
        
        $input = array(
            'reqs' => array (
                array (
                    'key' => self::_getRedisKey(self::PUSH_RECORD_KEY_PREFIX_HM, $jobid),
                    'timestamp' => $timestamp,
                ),
            ),
        );
        $arrInit = array();

        foreach (self::$_arrMsgCountType as $type) {
            $input['reqs'][] = array(
                'key' => self::_getRedisKey(self::PUSH_RECORD_KEY_PREFIX_IOS . "_" . $type, $jobid),
                'timestamp' => $timestamp,
            );
            $input['reqs'][] = array(
                'key' => self::_getRedisKey(self::PUSH_RECORD_KEY_PREFIX_ANDROID . "_" . $type, $jobid),
                'timestamp' => $timestamp,
            );
            
            $arrInit['reqs'][] = array(
                'key' => self::_getRedisKey(self::PUSH_RECORD_KEY_PREFIX_IOS . "_" . $type, $jobid),
                'member' => array(0),
            );
            $arrInit['reqs'][] = array(
                'key' => self::_getRedisKey(self::PUSH_RECORD_KEY_PREFIX_ANDROID . "_" . $type, $jobid),
                'member' => array(0),
            );
        }
        
        $ret = $redis->SADD($arrInit);
        if ($ret == false || $ret['err_no'] != 0) {
            $ret = $redis->SADD($arrInit);
            if ($ret== false || $ret['err_no'] != 0) {
                Lib_Util_Log::warning("Dl_Pushrecord initTask fail! sadd failed!". serialize($input));
                return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
            }
        }
        
        $ret = $redis->EXPIREAT($input);
        if ($ret == false || $ret['err_no'] != 0) {
            $ret = $redis->EXPIRE($input);
            if ($ret== false || $ret['err_no'] != 0) {
                Lib_Util_Log::warning("Dl_Pushrecord initTask fail! set expire failed!". serialize($input));
                return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
            }
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }
    
    /**
     * 添加统计记录
     * @param $arrInput
     * @return 
     */
    public static function addCountRecords($arrInput) {

        if (!isset($arrInput['jobid'])) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        $redis = self::_getRedis();
        if ( $redis == null) {
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CONN_FAIL);
        }
        // 添加记录，存放hashmap
        $jobKey = self::_getRedisKey(self::PUSH_RECORD_KEY_PREFIX_HM, $arrInput['jobid']);
        $input = array();
        foreach ($arrInput['records'] as $field => $step) {
            $input['reqs'][] = array(
                'key' => $jobKey,
                'field' => $field,
                'step'  => $step,
            );
        }
        $localIdc = Bd_Idc::getCurrentIdcRoomOrDefault('nj');
        ral_set_idc('jx');
        $ret = $redis->HINCRBY($input);
        ral_set_idc($localIdc);
        if ($ret == false || $ret['err_no'] != 0) {
            Lib_Util_Log::warning("Service_Pushrecord service fail! hincrby failed!".serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }

    /**
     * 添加拉消息记录
     * @param $arrInput
     * @return
     */
    public static function setRecords($arrInput) {
        $redis = self::_getRedis();
        if ( $redis == null) {
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CONN_FAIL);
        }
        $input = array();
        $count = array();
        foreach($arrInput as $recordType => $records) {
            if (!isset($records['userid']) || !isset($records['type']) || count($records['jobids']) <= 0 || !in_array($recordType, self::$_arrMsgCountType)) {
                continue;
            }
            $userId = $records['userid'];
            $prefixKey = null;

            if ($records['type'] == 'ios') {
                $prefixKey = self::PUSH_RECORD_KEY_PREFIX_IOS . "_" . intval($recordType);
            } else if ($records['type'] == 'android') {
                $prefixKey = self::PUSH_RECORD_KEY_PREFIX_ANDROID . "_" . intval($recordType);
            } else {
                continue;
            }

            if ($prefixKey == null) {
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }

            foreach ($records['jobids'] as $jobid) {
                $input['reqs'][] = array(
                    'key' => self::_getRedisKey($prefixKey, $jobid),
                    'member' => array($userId),
                );
                if ($recordType != self::RECORD_TYPE_LIST ) { // type =1 列表页
                    $jobKey = self::_getRedisKey(self::PUSH_RECORD_KEY_PREFIX_HM, $jobid);
                    $count['reqs'][] = array(
                        'key' => $jobKey,
                        'field' => $prefixKey,
                        'step'  => 1,
                    );
                }
            }
        }

        $localIdc = Bd_Idc::getCurrentIdcRoomOrDefault('nj');
        ral_set_idc('jx');
        // uv
        $ret = $redis->SADD($input); // set
        if ($ret == false || $ret['err_no'] != 0) {
            Lib_Util_Log::warning("Service_Pushrecord service fail! SADD failed! input [".serialize($arrInput). "] output [" . serialize($ret) ."]");
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        // pv
        if (count($count) > 0) {
            $redis->HINCRBY($count);
        }
        ral_set_idc($localIdc);
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }

    /**
     * 获取Redis数据
     * @param
     * @return
     */
    public static function getJobRecordsByJobids($arrInput) {
        if (!isset($arrInput['jobids'])) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        $redis = self::_getRedis();
        if ( $redis == null) {
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CONN_FAIL);
        }

        $input = array();
        foreach ($arrInput['jobids'] as $jobid) {
            if (intval($jobid) <= 0) {
                continue;
            }
            $jobKey = self::_getRedisKey(self::PUSH_RECORD_KEY_PREFIX_HM, $jobid);
            $input['reqs'][] = array (
                'key' => $jobKey,
            );
        }

        $ret = $redis->HGETALL($input);
        if (!$ret || $ret['err_no'] != 0) {
            Lib_Util_Log::warning("pushrecord_get_redis_failed ret:".serialize($ret)." req: ".serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'output' => $ret['ret'],
        );
        return $arrOutput;
    }

    /**
     * 获取set的元素数量
     * @param $arrInput
     * @return array
     */
    public static function getGetMsgUserInfoByJobids($arrInput) {
        if (!isset($arrInput['jobids'])) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $redis = self::_getRedis();
        if ( $redis == null) {
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CONN_FAIL);
        }

        $input = array();
        foreach ($arrInput['jobids'] as $jobid) {
            if (intval($jobid) <= 0) {
                continue;
            }
            foreach (self::$_arrMsgCountType as $type) {
                //拉消息ios用户数量
                $key = self::_getRedisKey(self::PUSH_RECORD_KEY_PREFIX_IOS . "_". $type, $jobid);
                $input['reqs'][] = array(
                    'key' => $key,
                );

                $key = self::_getRedisKey(self::PUSH_RECORD_KEY_PREFIX_ANDROID . "_". $type, $jobid);
                $input['reqs'][] = array(
                    'key' => $key,
                );
            }
        }
        $ret = $redis->SCARD($input);
        if (!$ret || $ret['err_no'] != 0) {
            Lib_Util_Log::warning("pushCount_get_redis_failed ret:".serialize($ret)." req: ".serialize($input));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'output' => $ret['ret'],
        );
        return $arrOutput;
    }

    /**
     * 获取对应jobid的ttl
     * @param $arrInput
     * @return
     */
    public static function getTTL($arrInput) {
        if (!isset($arrInput['jobids'])) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        $redis = self::_getRedis();
        if ( $redis == null) {
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CONN_FAIL);
        }
        
        $input = array();
        foreach ($arrInput['jobids'] as $jobid) {
            if (intval($jobid) <= 0) {
                continue;
            }
            $jobKey = self::_getRedisKey(self::PUSH_RECORD_KEY_PREFIX_HM, $jobid);
            $input['reqs'][] = array (
                'key' => $jobKey,
            );
        }
        $ret = array();
        if (count($input) > 0) {
            $ret = $redis->TTL($input);
            if (!$ret || $ret['err_no'] != 0) {
                Lib_Util_Log::warning("pushCount_get_redis_ttl_failed ret:".serialize($ret)." req: ".serialize($input));
            }
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'output' => $ret['ret'],
        );
        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return
     */
    public static function query($arrInput) {
        if (!isset($arrInput['function'])) {
            Lib_Util_Log::warning('input params invalid: function is empty.[' . serialize($arrInput). ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $function = $arrInput['function'];

        $ret = self::_init();
        if ($ret != true ) {
            return !$ret;
        }
        Bingo_Timer::start('dbquery');
        $odb = new Lib_Util_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        Bingo_Timer::end('dbquery');
        if ($odb == null) {
            Lib_Util_Log::warning('new lib_db fail.');
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $arrOut = $odb->query($function, $arrInput);
        return $arrOut;
    }

    /**
     * @param $prefix, $jobid
     * @return string
     */
    private static function _getRedisKey($prefix, $jobid) {
        return $prefix . "_" . $jobid;
    }

}
