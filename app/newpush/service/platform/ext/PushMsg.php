<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015:11:06 18:15:05
 * @version
 * @structs & methods(copied from idl.)
 */
class Service_Platform_Ext_PushMsg
{
    /**
     * @brief
     * @param: $errno
     * @return array : $ret
     */
    private static function _errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @bbrief
     * @param $errno
     * @param $errmsg
     * @return array : $ret
     * @internal param $ :
     *    uint32_t $errno
     *    string   $errmsg
     */
    private static function _errRetSelf($errno, $errmsg)
    {
        return array(
            'errno' => $errno,
            'errmsg' => $errmsg,
        );
    }

    /**
     * @brief
     * @param $jobData
     * @param $tagData
     * @return array : $arrOutput
     * @internal param $ : $arrJob
     */
    public static function sendNewJobMsg($jobData, $tagData) {
        $followType = intval($jobData['follow_type']);

        switch ($followType) {
            case Lib_Util_Conf::FOLLOW_TYPE_FORUM_BROADCAST:
            case Lib_Util_Conf::FOLLOW_TYPE_OFFICIAL:
            case Lib_Util_Conf::FOLLOW_TYPE_SUBSCRIBE_ACCOUNT:
                return self::_broadcastSendNewJobMsg($jobData, $tagData);
            case Lib_Util_Conf::FOLLOW_TYPE_BAZHUTASK:
            case Lib_Util_Conf::FOLLOW_TYPE_BAZHUNOTICE:
                return self::_bazhutaskSendNewJobMsg($jobData, $tagData);
            default:
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

    }

    /**
     * @brief 
     * @param
     * @return
     * */
    private static function _broadcastSendNewJobMsg($jobData, $tagData) {
        $followId = intval($jobData['follow_id']);
        $followType = intval($jobData['follow_type']);
        $arrForumInfos = self::getForumInfo($followId);
        if (!isset($arrForumInfos[$followId])) {
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrForumInfo = $arrForumInfos[$followId];
        $followName = $arrForumInfo['forum_name'];
        $userType = Lib_Util_Conf::$MAP_FOLLOWTYPE_USERTYPE[$followType];
        $portrait = $arrForumInfo['portrait'];
        $arrContent = Bingo_String::json2array($jobData['content'], 'utf-8');

        $extension = array(
            'userInfo' => array(        
                'userId' => $followId,
                'userName' => $followName,
                'userType' => $userType,
                'portrait' => $portrait,
            ),
            'toUserInfo' => array(
                'userId' => 0,
                'userName' => '',
                'userType' => 0,
                'portrait' => '',
            ),
            'isFriend' => 1,
        );
        if (isset($arrContent['userinfo'])) {
            $extension['userInfo'] = $arrContent['userinfo'];
        }

        //数据库中存储的是已经urlencode的数据，所以此处不需要再次进行encode
        $arrSendContent = $arrContent['info_list'];
        $intMsgType = intval($arrContent['msgType']);

        $strContent = '';
        if ($intMsgType == 1) {
            //单图文消息先进行urldecode
            $arrSendContent = Lib_Util_Format::urldecodeArray($arrSendContent);
            $strContent = $arrSendContent;
        } else {
            $strContent = json_encode($arrSendContent);
        }

        $arrMsgInfo = array(
            'msgType' => $arrContent['msgType'],
            'userId' => $followId,
            'content' => $strContent,
            'userInfo' => $extension['userInfo'],
            'toUserInfo' => $extension['toUserInfo'],
            'link' => $arrContent['link'],
            'taskId' => $jobData['job_id'],
        );
        
        if (isset($arrContent['version_rule'])) {
            $arrMsgInfo['version_rule'] = $arrContent['version_rule'];
        }
        
        $arrParams = array(
            'reliable' => 1,    //是否执行可靠推送，默认为1
            'jobid' => $jobData['job_id'],    //任务ID
            'tag_id' => $jobData['tag_id'],
            'tag_name' => $tagData['tag_name'],
            'pushInfo' => $arrMsgInfo,
            'followType' => $jobData['follow_type'],
        );

       return Service_Msgcenter_Msgcenter::storeMsg($arrParams);
    } 

    /**
     * @brief 
     * @param
     * @return
     * */
    private static function _bazhutaskSendNewJobMsg($jobData, $tagData){
        $followId = intval($jobData['follow_id']);
        $followType = intval($jobData['follow_type']);
        /*
        $arrForumInfos = self::getForumInfo($followId);
        if (!isset($arrForumInfos[$followId])) {
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrForumInfo = $arrForumInfos[$followId];
        $followName = $arrForumInfo['forum_name'];
        $userType = Lib_Util_Conf::$MAP_FOLLOWTYPE_USERTYPE[$followType];
        $portrait = $arrForumInfo['portrait'];
        */
        $arrContent = Bingo_String::json2array($jobData['content'], 'utf-8');

        $extension = array(
            'userInfo' => array(        
                'userId' => $followId,
                'userName' => $followName,
        //        'userType' => $userType,
        //        'portrait' => $portrait,
            ),
            'toUserInfo' => array(
                'userId' => 0,
                'userName' => '',
                'userType' => 0,
                'portrait' => '',
            ),
        //  'isFriend' => 1,
            'isFriend' => 0,
        );

        //数据库中存储的是已经urlencode的数据，所以此处不需要再次进行encode
        $arrSendContent = $arrContent['info_list'];

        //此处不进行urldecode， 直接发给对方
        $strContent = json_encode($arrSendContent);
        $arrMsgInfo = array(
            'msgType' => $arrContent['msgType'],
            'userId' => $followId,
            'content' => $strContent,
            'userInfo' => $extension['userInfo'],
            'toUserInfo' => $extension['toUserInfo'],
            'link' => $arrContent['link'],
            'taskId' => $jobData['job_id'],
        );
        
        $arrParams = array(
            'reliable' => 1,    //是否执行可靠推送，默认为1
            'jobid' => $jobData['job_id'],    //任务ID
            'tag_id' => $jobData['tag_id'],
            'tag_name' => $tagData['tag_name'],
            'push_info' => $arrMsgInfo,
            'follow_type' => $jobData['follow_type'],
        );

       //return Service_Msgcenter_Msgcenter::storeMsg($arrParams);
       return Service_Msgcenter_Msgcenter::storeBZMsg($arrParams);
    }

    /**
     * @brief
     * @param: $arrJob
     * @return: $arrOutput
     */
    public static function initJobRecord($jobData, $tagData) {
        $arrParams = array(
            'job_id' => $jobData['job_id'],    //任务ID
            'tag_id' => $jobData['tag_id'],
            'tag_name' => $tagData['tag_name'],
            'follow_id' => $jobData['follow_id'],
            'follow_type' => $jobData['follow_type'],
            'starttime' => $jobData['push_starttime'],
            'endtime' => $jobData['push_endtime'],
        );

        $ret = Tieba_Service::call('newpush', 'initJobRecord', $arrParams, null, null, 'post', 'php', 'utf-8');
        if($ret === false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
            //如果调用错误，打印两条错误日志，触发告警
            Bingo_Log::warning("call initJobRecord failed");
            Bingo_Log::warning("call initJobRecord failed");
            return Tieba_Service::call('newpush', 'initJobRecord', $arrParams, null, null, 'post', 'php', 'utf-8');
        }

        return $ret;
    }

    /**
     * @brief
     * @param: $arrJob
     * @return array : $arrOutput
     */
    private static function getForumInfo($forumId) {
        $arrInput = array(
            'forum_id' => array(
                0 => $forumId,
            ),
        );

        $forumInfos = array();

        $res = Tieba_Service::call('forum', 'mgetBtxInfo', $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($res['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($res['output'])){
            Bingo_Log::warning('get mgetForumAttr failed ' . serialize($res) . " follow_id=" . $forumId);
            return $forumInfos;
        }

        if (!isset($res['output'][$forumId])) {
            Bingo_Log::warning('get mgetForumAttr failed no forum value ' . serialize($res) . " follow_id=" . $forumId);
        }

        foreach ($res['output'] as $fid => $tmpInfo) {
            $forumInfo = array();
            $forumPortrait = '';
            if (isset($tmpInfo['attrs']['card_p1']['style_name'])) {
                $style = $tmpInfo['attrs']['card_p1']['style_name'];
                $arrStyle = json_decode($style, true);
                $forumPortrait = $arrStyle['avatar'];
            }
            $forumName = '';
            if (isset($tmpInfo['forum_name']['forum_name'])) {
                $forumName = $tmpInfo['forum_name']['forum_name'];
            }

            $forumInfo['forum_id'] = $fid;
            $forumInfo['forum_name'] = $forumName;
            $forumInfo['portrait'] = $forumPortrait;
            $forumInfos[$fid] = $forumInfo;

            if (empty($forumPortrait)) {
                Bingo_Log::warning('get mgetForumAttr failed empty portrait' . serialize($res) . " follow_id=" . $forumId);
            }
        }

        return $forumInfos;
    }
}
