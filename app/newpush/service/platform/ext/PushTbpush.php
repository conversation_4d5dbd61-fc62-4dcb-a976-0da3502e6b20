<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2017:03:22 18:15:05
 * @version
 * @structs & methods(copied from idl.)
 */
define ('SCRIPT_NAME', basename(__FILE__, ".php"));
define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../' );
define ('DATA_PATH', ROOT_PATH . "data/app/newpush/broadcast");
define ('LOG_PATH',  ROOT_PATH . "log/app/newpush");
class Service_Platform_Ext_PushTbpush
{

    const TAG_STATUS_NEW     = 0;
    const TAG_STATUS_INIT    = 1;
    const MAX_FORUM_MEMBER = 300000;//吧会员数，判断是直接访问grade备库还是使用数据组文件数据的分界线
    const BUCKET_NUM     = 1024;
    const GRADE_PER_NUM  = 1000;
    const FORUM_GRADE_DATABASE = "DB_grade_new_read";//grade备库ral配置
    const RETRY_COUNT    = 3;

    private static $_strUidFile = 'wget ftp://tieba00:<EMAIL>://home/<USER>/tbdc/data/tieba_rpt_bakan_recommend_user_file/tieba_rpt_bakan_recommend_user_file.';

    /**
     * @brief errRet
     * @param
     *   uint32_t errno
     * @return
     *    array ret
     **/
    private static function _errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @bbrief errRetSelf
     * @param
     *   uint32_t errno
     *   string errmsg
     * @return
     *   array ret
     **/
    private static function _errRetSelf($errno, $errmsg)
    {
        return array(
            'errno' => $errno,
            'errmsg' => $errmsg,
        );
    }

    /**
     * @param $arrInput
     * @return array|mixed
     */
    public static function processTbpushUids($arrInput)
    {
        // 这个是 NMQ 的接受者,需要做参数校验

//        if (isset($arrInput['from_nmq']) && $arrInput['from_nmq']) {
//            $data = Tieba_Service::getArrayParams($arrInput, 'data');
//            if (!$data || !is_array($data)) {
//                Lib_Util_Log::warning(self::MODULE_LOG_NAME . "cmd from nmq is not a array in[". serialize($arrInput) . "]");
//                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
//            }
//            $arrInput = $data;
//        }
        if (!isset ($arrInput['format'])) {
            $arrInput ['format'] = 'mcpack';
        }
        $strFormat = strtolower($arrInput ['format']);
        if ($strFormat !== 'mcpack' && $strFormat !== 'json') {
            Bingo_Log::warning("input params wrong format:$strFormat.");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!isset ($arrInput ['data'])) {
            Bingo_Log::warning("input params no data.");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $php_input_data = file_get_contents("php://input");
        $strCmd = substr($php_input_data, 5);
        if (empty($strCmd)) {
            Bingo_Log::warning("input params no data.");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrCmd = array();
        if ($strFormat === 'mcpack') {
            $mcpack = mc_pack_text2pack($strCmd);
            $arrCmd = mc_pack_pack2array($mcpack);
        } else {
            $arrCmd = Bingo_String::json2array($strCmd);
        }
        if (!isset ($arrCmd ['command_no'])) {
            Bingo_Log::warning("no command no found.$strCmd");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intCmd = intval($arrCmd ['command_no']);
        Bingo_Log::pushNotice('input', Bingo_String::array2json($arrCmd));
        $intTransId = 0;
        if (isset ($arrCmd ['trans_id'])) {
            $intTransId = intval($arrCmd ['trans_id']);
        }


        $intTagKey  = $arrCmd['tag_key'];
        $intForumId = $arrCmd['forum_id'];
        $intMsgid   = $arrCmd['msgid'];
        $intUidType = $arrCmd['uid_type'];

        // step 1 : 通过 tag id ,tag key ,forum_id ,取到DDBS 里面的数目

        $intTotal = 0;
        $arrInputDl['arrFields'] = array(
            'count(uid) as cnt',
        );

        $arrInputDl['conditions'] = "`tag_key`=" . $intTagKey . " and `status`=0 ";
        $resDl = Dl_Strategy_Strategy::getUidByTagKey($arrInputDl);
        if (isset($resDl['errno']) && $resDl['errno'] == 0) {
            // 处理用户ID的数组
            $intTotal = $resDl['output'][0]['cnt'];
        }

        // num 是最大处理 UID 的值,现在是 300

        // step 2 : 判断数目,是否小于设定的[ num ]临界值,

        // step 2.1 : 如果小于[ num ],直接取出来,回调 tbpush::pushUidsToQueue

        if($intTotal <= Lib_Util_Conf::MAX_UIDS_TBPUSH_COUNT){
            $arrUids = array();
            $arrInputUid['arrFields'] = array(
                'uid',
            );

            $arrInputUid['conditions'] = "`tag_key`=" . $intTagKey . " and `status`=0 ";
            $resUid = Dl_Strategy_Strategy::getUidByTagKey($arrInputUid);
            if (isset($resUid['errno']) && $resUid['errno'] == 0) {
                // 处理用户ID的数组
                $count = count($resUid['output']);
                for ($i = 0; $i< $count; $i++) {
                    $arrUids[] = $resUid['output'][$i]['uid'];
                }
            }

            if(count($arrUids) > 0){
                $arrPushInput = array(
                    'uids'      => $arrUids,
                    'msgid'     => $intMsgid,
                    'uid_type'  => $intUidType,
                );

                $arrPushOut = Tieba_Service::call("tbpush", 'pushUidsToQueue', $arrPushInput, null, null, 'post', 'php', 'utf8');
                if($arrPushOut === false || $arrPushOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
                    Bingo_Log::warning('newpush::getTagsByFollowId error.arrInput:[' . serialize($arrPushInput) . '] res ['.serialize($arrPushOut).']');
                    return self::_errRet($arrPushOut['errno']);
                }
            }


        }
        // step 2.2.1 : 如果大于[ num ],则根据 num 分段,做好 limit n,m

        else{
            $intIndexDevide = floor($intTotal / Lib_Util_Conf::MAX_UIDS_TBPUSH_COUNT );
            $arrIndex = array();
            for ($i=0; $i<$intIndexDevide; $i++){
                $arrIndex[] = Lib_Util_Conf::MAX_UIDS_TBPUSH_COUNT * $i;
            }
            // step 2.2.2 : 循环调用上面分段的数组,取 DB 数据,回调 tbpush::pushUidsToQueue
            foreach ($arrIndex as $indexV){

                $intPn = $indexV;
                $intRn = Lib_Util_Conf::MAX_UIDS_TBPUSH_COUNT;

                $arrInputUid['arrFields'] = array(
                    'uid',
                );

                $arrInputUid['conditions'] = "`tag_key`=" . $intTagKey . " and `status`=0 order by `uid` asc limit  ".$intPn.",".$intRn;
                $resUid = Dl_Strategy_Strategy::getUidByTagKey($arrInputUid);
                if (isset($resUid['errno']) && $resUid['errno'] == 0) {
                    // 处理用户ID的数组
                    $count = count($resUid['output']);
                    for ($i = 0; $i< $count; $i++) {
                        $arrUids[] = $resUid['output'][$i]['uid'];
                    }
                }
                if(count($arrUids) > 0){
                    $arrPushInput = array(
                        'uids'      => $arrUids,
                        'msgid'     => $intMsgid,
                        'uid_type'  => $intUidType,
                    );

                    $arrPushOut = Tieba_Service::call("tbpush", 'pushUidsToQueue', $arrPushInput, null, null, 'post', 'php', 'utf8');
                    if($arrPushOut === false || $arrPushOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
                        Bingo_Log::warning('newpush::getTagsByFollowId error.arrInput:[' . serialize($arrPushInput) . '] res ['.serialize($arrPushOut).']');
                        return self::_errRet($arrPushOut['errno']);
                    }
                }
            }

        }

       //  结束

        $arrData = array();
        $errno = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $arrData,
        );
    }


    /**
     * @param $arrInput
     * @return array
     *
     */
    public static function processTbpushFirstUids($arrInput){

        if($arrInput['follow_id'] <= 0 || $arrInput['follow_type'] < 0 ||$arrInput['msgid'] < 0 ||$arrInput['uid_type'] < 0 ){
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $intFollowId    = $arrInput['follow_id'];
        $intFollowType  = $arrInput['follow_type'];
        $intMsgid       = $arrInput['msgid'];
        $intUidType     = $arrInput['uid_type'];

        // step 1 : create tag by follow_id

        $inputFname = array(
            "forum_id" => array(
                0 => $intFollowId //吧id
            )
        );
        $outputFname   = Tieba_Service::call('forum', 'getFnameByFid', $inputFname, null, null, 'post', 'php', 'utf-8');

        if($outputFname === false || $outputFname['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('forum::getFnameByFid error.arrInput:[' . serialize($inputFname) . '] res ['.serialize($outputFname).']');
            return self::_errRet($outputFname['errno']);
        }

        $strFollowName = $outputFname['forum_name'][$intFollowId]['forum_name'];

        $arrTagInput = array(
            'follow_name'   => $strFollowName,
            'follow_id'     => $intFollowId,
            'follow_type'   => $intFollowType,
        );

        $arrTagOut = self::_getTagInfo($arrTagInput);
        if($arrTagOut === false || $arrTagOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('self::getTagInfo error.arrInput:[' . serialize($arrTagInput) . '] res ['.serialize($arrTagOut).']');
            return self::_errRet($arrTagOut['errno']);
        }

        $arrTagInfo = $arrTagOut['data'];

        $intTagId =  $arrTagInfo['tag_id'];

        if(intval($intTagId) <=0){
            Bingo_Log::warning("tag_id invalid. [" . serialize($arrTagInfo) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if ($arrTagInfo['status'] == self::TAG_STATUS_NEW) {
            $groupId = intval($arrTagInfo['group_id']);
            $groupType = intval($arrTagInfo['group_type']);
            $begTime = microtime(true);
            $fetchRet = self::fetchUidsByFollowIdFollowType($intFollowId, $intFollowType, $intTagId, $groupId, $groupType);
            $endTime = microtime(true);
            Bingo_Log::pushNotice('follow_cost', round($endTime - $begTime, 3));
            if(!$fetchRet){
                Bingo_Log::warning('fetchUidsByFollowIdFollowType. res ['.serialize($fetchRet).']');
                return self::_errRet($fetchRet['errno']);
            }
            $arrReq = array(
                'tag_id' => $intTagId,
                'status' => self::TAG_STATUS_INIT,
            );
            self::serviceCall('newpush', 'updateTag', $arrReq);
        }

        $baseTagKey = $intTagId * self::BUCKET_NUM;

        for ($i = 0; $i < self::BUCKET_NUM; $i++) {
            $arrParams = array(
                'tag_key'   => $baseTagKey + $i,
                'forum_id'  => $intFollowId,
                'msgid'     => $intMsgid,
                'uid_type'  => $intUidType,
            );
            // store in NMQ
            self::commitNmq('newpush', 'getuids', $arrParams);
//            $arrNewpushOut = Tieba_Service::call("newpush", 'processTbpushUids', $arrParams, null, null, 'post', 'php', 'utf8');
        }

        // step 4 : nmq ,call processTbpushUids



        $arrData = array();
        $errno = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $arrData,
        );
    }

    /**
     * @brief: 带重试功能的nmq提交
     * @param:
     *      $topic     : topic
     *      $cmd       : cmd
     *      $arrParams : 参数
     *      $retryCount: 重试次数
     * @return: null
     */
    private static function commitNmq($topic, $cmd, $arrParams, $retryCount=1) {
        if ($retryCount > 0) {
            $arrRes = Tieba_Commit::commit($topic, $cmd, $arrParams);
            if ($arrRes === false || 0 != $arrRes['errno']) {
                Bingo_Log::warning("fail to commitNmq $retryCount! topic: $topic, cmd: $cmd, params: " .  serialize($arrParams) );
                usleep(5000);
                self::commitNmq($topic, $cmd, $arrParams, $retryCount-1);
            } else {
                return $arrRes;
            }
        } else {
            return false;
        }
    }
    /**
     * @param arrInput
     * @return tagId
     * */
    private static function _getTagInfo($arrInput){
        $arrDataTag = array();
        //根据follow_id和follow_type查询tag
        $arrQueryDbInput = array(
            'follow_id' => $arrInput['follow_id'],
            'follow_type' => $arrInput['follow_type'],
        );

        $ret = Dl_Tag_Tag::getTagByFollowIdAndType($arrQueryDbInput);
        if (!$ret || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            return $ret;
        }

        $arrData = $ret['data'];
        if (empty($arrData)) {
            //新建对应follow_type的tag
            $followId = intval($arrInput['follow_id']);
            $followType = intval($arrInput['follow_type']);
            $followName = $arrInput['follow_name'];

            $arrDbInput = array(
                'tag_name' => $followName,
                'tag_desc' => $followName,
                'manager_id' => 0,
                'manager_role' => 0,
                'group_id' => $followId,
                'group_name' => $followName,
                'group_type' => 30,
                'follow_id' => $followId,
                'follow_type' => $followType,
            );

            $ret = Service_Platform_Platform::createTag($arrDbInput);
            if (false === $ret || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning ('call service createTag failed.');
                return self::_errRet (Tieba_Errcode::ERR_DL_CALL_FAIL);
            }

            //根据follow_id和follow_type查询tag，如果失败则插入失败
            $ret = Dl_Tag_Tag::getTagByFollowIdAndType($arrQueryDbInput);
            if (!$ret || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                return $ret;
            }

            $arrDataTag = $ret['data'];
            if (empty($arrDataTag)) {
                Bingo_Log::warning ('create tag info failed. not exist tag for follow id:' . $arrInput['follow_id']);
                return self::_errRetSelf (Tieba_Errcode::ERR_DL_CALL_FAIL, "create tag info failed. not exist tag of follow id:" . $arrInput['follow_id']);
            }
        }
        $arrDataTag = $ret['data'];
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $arrDataTag,
        );

        return $arrOutput;
    }


    /**
     * @brief
     *      通过followId和followType获取uid列表并存储
     * @param $followId
     * @param $followType
     * @param $tagId
     * @param $groupId
     * @param $groupType
     * @internal param $ followId  : follow id*      followId  : follow id
     *      followType: follow type
     *      tagId     : tag id
     *      groupId   : group id
     *      groupType : group type
     * @return null
     */
    private static function fetchUidsByFollowIdFollowType($followId, $followType, $tagId, $groupId, $groupType) {
        switch ($followType) {
            case Lib_Util_Conf::FOLLOW_TYPE_FORUM_BROADCAST:
            case Lib_Util_Conf::FOLLOW_TYPE_OFFICIAL:
            case Lib_Util_Conf::FOLLOW_TYPE_SUBSCRIBE_ACCOUNT:
                self::fetchUidsByForumId($followId, $tagId, $groupId, $groupType);
                break;
            default:
                break;
        }
    }

    /**
     * @brief
     *      通过吧ID获取该吧所有用户列表并存储
     * @param $forumId
     * @param $tagId
     * @param $groupId
     * @param $groupType
     * @return bool
     * @internal param $ forumId  : 吧ID*      forumId  : 吧ID
     *      tagId    : tag id
     *      groupId  : group id
     *      groupType: group type
     */
    private static function fetchUidsByForumId($forumId, $tagId, $groupId, $groupType) {
        $arrUids = array();
        $arrReq = array(
            'forum_id' => array(
                0 => $forumId,
            ),
        );
        $arrRes = self::serviceCall('forum', 'mgetBtxInfoEx', $arrReq);
        if (!$arrRes) {
            return false;
        }
        $memberCount = intval($arrRes['output'][$forumId]['statistics']['member_count']);
        if ($memberCount > self::MAX_FORUM_MEMBER) {
            $arrRes = self::getUidsFromOrpByFid($forumId, $tagId, $groupId, $groupType);
            if ($arrRes === false) {
                Bingo_Log::warning("getUidsFromOrpByFid fail, forumId: [$forumId], tagId: [$tagId], groupId: [$groupId], groupType: [$groupType]");
                $arrRes = self::getUidsFromDbByFid($forumId, $tagId, $groupId, $groupType);
                if ($arrRes === false) {
                    Bingo_Log::warning("getUidsFromDbByFid fail, forumId: [$forumId], tagId: [$tagId], groupId: [$groupId], groupType: [$groupType]");
                }
            }
        } else {
            $arrRes = self::getUidsFromDbByFid($forumId, $tagId, $groupId, $groupType);
            if ($arrRes === false) {
                Bingo_Log::warning("getUidsFromDbByFid fail, forumId: [$forumId], tagId: [$tagId], groupId: [$groupId], groupType: [$groupType]");
            }
        }
    }


    /**
     * @brief 根据吧id从orp文件来获取吧客户端关注用户
     * @param $forumId
     * @param $tagId
     * @param $groupId
     * @param $groupType
     * @return bool : arrUids 从orp文件中获取到的uid列表
     * @internal param $ :
     *      forumId
     *      tagId
     *      groupId
     *      groupType
     */
    private static function getUidsFromOrpByFid($forumId, $tagId, $groupId, $groupType) {

        $filePath = '';
        $date = date('Ymd', strtotime("-1 sunday", time()));
        $strSourceUrl = self::$_strUidFile . $date;
        $filePath = DATA_PATH . "/uids_$date";
        if (!file_exists($filePath) || (filesize($filePath) == 0)) {
            unlink($filePath);
            $strCmd = "wget $strSourceUrl -O $filePath";
            system($strCmd, $errno);
            if ($errno != 0) {
                Bingo_Log::warning("exec fail : [$strCmd]");
                unlink($filePath);
                return false;
            }
        }

        // 删除之前的文件
        $strCmd = "find " . DATA_PATH . " -type f | grep 'uids_' | grep -v 'uids_$date' | xargs rm -rf";
        if (false === system($strCmd)) {
            Bingo_Log::warning("exec fail: [$strCmd]");
        }

        $forumFile = DATA_PATH . "/forum_$forumId";
        if (false === unlink($forumFile)) {
            Bingo_Log::warning("unlink fail: [$forumFile]");
            return false;
        }

        $strCmd = "grep -E \"^" . $forumId . "[^0-9]+\" $filePath | awk '{if($1 != 0){print $2 > \"$forumFile\" }}'";
        if (false === system($strCmd)) {
            Bingo_Log::warning("exec fail: [$strCmd]");
            return false;
        }

        $objOpenFile = fopen($forumFile, 'r');
        if (!$objOpenFile) {
            return false;
        }

        $arrUids = array();
        $arrTmpUids = array();
        while (!feof($objOpenFile)) {
            $strUid = trim(stream_get_line($objOpenFile, 1024, "\n"));
            if (empty($strUid)) {
                continue;
            }
            $arrTmpUids[] = intval($strUid);

            if (count($arrTmpUids) % 100000 == 0) {
                $arrTmpUids = array_unique($arrTmpUids);
                foreach ($arrTmpUids as $item) {
                    $arrUids[$item % self::BUCKET_NUM][] = $item;
                }
                self::storeUidsInDb($forumId, Lib_Util_Conf::FOLLOW_TYPE_FORUM_BROADCAST, $tagId, $groupId, $groupType, $arrUids);
                $arrUids = array();
                $arrTmpUids = array();
            }
        }
        $arrTmpUids = array_unique($arrTmpUids);
        foreach ($arrTmpUids as $item) {
            $arrUids[$item % self::BUCKET_NUM][] = $item;
        }
        self::storeUidsInDb($forumId, Lib_Util_Conf::FOLLOW_TYPE_FORUM_BROADCAST, $tagId, $groupId, $groupType, $arrUids);
        return true;
    }


    /**
     * @brief 根据吧id从数据库备库来获取吧客户端关注用户
     * @param $forumId
     * @param $tagId
     * @param $groupId
     * @param $groupType
     * @return bool : arrUids 从DB中获取到的uid列表
     * @internal param $ :
     *      forumId
     *      tagId
     *      groupId
     *      groupType
     */
    private static function getUidsFromDbByFid($forumId, $tagId, $groupId, $groupType) {
        $db = self::getGradeDb();
        if (is_null($db)) {
            return false;
        }

        $userId = 0;
        $arrRes = array();
        $arrUids = array();
        $arrTmpUids = array();
        $limit = self::GRADE_PER_NUM;
        do {
            $strSql = "SELECT user_id FROM forum_member WHERE forum_id = $forumId and flag = 1 and user_id > $userId order by user_id limit $limit;";
            $arrRes = $db->query($strSql);
            $userId = $arrRes[count($arrRes) - 1]['user_id'];
            $arrTmpUids = array_merge($arrTmpUids, $arrRes);

            if (count($arrTmpUids) > 100000) {
                $arrTmpUids0 = array_slice($arrTmpUids, 0, 100000);
                foreach ($arrTmpUids0 as $item) {
                    $uid = $item['user_id'];
                    $arrUids[$uid % self::BUCKET_NUM][] = $uid;
                }
                self::storeUidsInDb($forumId, Lib_Util_Conf::FOLLOW_TYPE_FORUM_BROADCAST, $tagId, $groupId, $groupType, $arrUids);
                $arrUids = array();
                $arrTmpUids1 = array_slice($arrTmpUids, 100000, count($arrTmpUids) - 100000);
                unset($arrTmpUids);
                $arrTmpUids = $arrTmpUids1;
            }
        } while (!empty($arrRes));

        if (count($arrTmpUids) > 0) {
            foreach ($arrTmpUids as $item) {
                $uid = $item['user_id'];
                $arrUids[$uid % self::BUCKET_NUM][] = $uid;
            }
            self::storeUidsInDb($forumId, Lib_Util_Conf::FOLLOW_TYPE_FORUM_BROADCAST, $tagId, $groupId, $groupType, $arrUids);
        }

        return true;
    }

    /**
     * @brief
     *      将uid存储入db
     * @param $followId
     * @param $followType
     * @param $tagId
     * @param $groupId
     * @param $groupType
     * @param $arrUids
     * @internal param $ followId  : follow id*      followId  : follow id
     *      followType: follow type
     *      groupId   : group id
     *      groupType : group type
     *      arrUids   : 将要存储的uid列表
     * @return null
     */
    private static function storeUidsInDb($followId, $followType, $tagId, $groupId, $groupType, $arrUids) {
        $arrReq = array();
        $objRalMulti = new Tieba_Multi('storeUids');
        foreach ($arrUids as $key => $uidGroup) {
            $arrInput = array(
                'serviceName' => 'newpush',
                'method'      => 'createTagUsers',
                'input'       => array(
                    'tag_key' => $tagId * self::BUCKET_NUM + $key,
                    'group_id' => $groupId,
                    'group_type' => $groupType,
                    'status' => 0,
                    'uids' => $uidGroup,
                ),
            );
            $ralKey = "storeuids_$key";
            $arrReq[$ralKey] = $arrInput;
            $objRalMulti->register($ralKey, new Tieba_Service('newpush'), $arrInput);
        }

        $arrMultiRes = $objRalMulti->call();
        foreach ($arrMultiRes as $key => $arrServiceRes) {
            unset($arrReq[$key]);
        }

        // handle failure requests
        if (count($arrReq) > 0) {
            usleep(100000);
            $objRalMulti = new Tieba_Multi('storeUids');
            foreach ($arrReq as $key => $failReq) {
                $objRalMulti->register($key, new Tieba_Service('newpush'), $failReq);
            }
            $objRalMulti->call();
        }

    }

    /**
     * @brief
     *      获取grade db实例
     * @param int $retryCount
     * @return Bd_DB db实例
     * db实例
     * @internal param $ retryCount 重试次数*      retryCount 重试次数
     */
    private static function getGradeDb($retryCount=3) {
        $db = new Bd_DB();
        if (is_null($db)) {
            getGradeDb($retryCount-1);
        } else {
            Bingo_Timer::start("dbinit_Grade");
            $ret = $db->ralConnect(self::FORUM_GRADE_DATABASE);
            Bingo_Timer::end("dbinit_Grade");
            if (!$ret) {
                sleep(1);
                getGradeDb($retryCount-1);
            } else {
                return $db;
            }
        }
    }

    /**
     * @brief
     *      带重试功能的Tieba_Service::call
     * @param $strService
     * @param $strMethod
     * @param $arrInput
     * @param int $retryCount
     * @return bool
     * @internal param $ strService: service*      strService: service
     *      strMethod : method
     *      arrInput  : 请求参数
     *      retryCount: 重试次数
     */
    private static function serviceCall($strService, $strMethod, $arrInput, $retryCount=self::RETRY_COUNT) {
        if ($retryCount > 0) {
            $arrRes = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
            if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']) {
                Bingo_Log::warning(SCRIPT_NAME . " fail to $strMethod $retryCount! req: " .  serialize($arrInput) . ' res: ' . serialize($arrRes));
                sleep(1);
                self::serviceCall($strService, $strMethod, $arrInput, $retryCount - 1);
            } else {
                return $arrRes;
            }
        } else {
            return false;
        }
    }

}
