<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015:11:06 18:15:05
 * @version
 * @structs & methods(copied from idl.)
 */

class Service_Platform_Ext_PushSubjob
{
    /**
     *
     */
    const REDIS_KEY_PREFIX_PUSHJOB = "pushjob:";

    /**
     * @brief
     * @param: $errno
     * @return array : $ret
     */
    private static function _errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @bbrief
     * @param $errno
     * @param $errmsg
     * @return array : $ret
     * @internal param $ :
     *    uint32_t $errno
     *    string   $errmsg
     */
    private static function _errRetSelf($errno, $errmsg)
    {
        return array(
            'errno' => $errno,
            'errmsg' => $errmsg,
        );
    }

    /**
     * @brief: 在子任务完成推送之后调用该接口来同步任务状态，当全部子任务全部推送完成时更新任务状态
     * @param: $arrInput
     * @return array : $arrOutput
     */
    public static function updateJobStatusBySubjob($arrInput) {
        if (!isset($arrInput['subJobId'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $subjobId = intval($arrInput['subJobId']);
        $jobId = intval($subjobId / Lib_Util_Conf::BUCKET_NUM);
        $subjobId = intval($subjobId % Lib_Util_Conf::BUCKET_NUM);

        //此处不能使用该getJobById, 存在缓存
        //$res = Service_Platform_Ext_PushJob::getJobById(array('job_id' => $jobId));
        $res = Dl_Job_Job::getJobById(array('job_id' => $jobId));
        if (false === $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ('call DL geetJobById failed.');
            return self::_errRet (Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $jobData = $res['data'];
        if (empty($jobData)) {
            Bingo_Log::warning('Call DL_Job::getJobById empty result:' . $jobId);
            return self::_errRetSelf(Tieba_Errcode::ERR_DL_CALL_FAIL, 'not exist job with id:' . $jobId);
        }

        if ($jobData['status'] != Service_Platform_Ext_PushJob::STATUS_PUSH_START) {
            Bingo_Log::warning('Call updateJobStatusBySubjob service job status could not update subjob status ' . $jobId);
            return self::_errRetSelf(Tieba_Errcode::ERROR_FAILED_AUDIT, 'job status could not update subjob status ' . $jobId);
        }

        $res = self::putSubjob(array(
            'job_id' => $jobId,
            'subjob_id' => $subjobId,
        ));

        if (false === $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ('call Redis putSubjob failed.');
            return self::_errRet (Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $subjobCount = intval($res['data']['count']);
        if ($subjobCount == 1) {
            //添加第一个子任务时，设置超时
            $arrExpireReqs = array();
            $arrExpireReqs[] = array(                               
                'key' => self::REDIS_KEY_PREFIX_PUSHJOB . $jobId,
                'seconds' => 86400,
            );

            //执行批量设置操作
            $arrExpireParams = array(
                'reqs' => $arrExpireReqs,
            );

            //只设置，不返回结果
            self::batchSetRedisExpire($arrExpireParams);
        }

        //如果所有的子任务的状态都已更新，则更新该任务的状态为结束
        if ($subjobCount >= Lib_Util_Conf::BUCKET_NUM) {
            $jobData['status'] = Service_Platform_Ext_PushJob::STATUS_COMPLEMENTLY;

            $redisRes = self::clearSubjob(array('job_id' => $jobId));
            $res = Dl_Job_Job::updateJob($jobData);

            if (false == $redisRes || $redisRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning ('call DL clearSubjob failed.');
                return self::_errRet (Tieba_Errcode::ERR_REDIS_CALL_FAIL);
            }
            if (false === $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning ('call DL updateJob failed.');
                return self::_errRet (Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
            
            //记录任务结束时间
            Bingo_Log::warning("endPushJob job_id=" . $jobData['job_id'] . " fid=" . $jobData['follow_id'] . " time=" . time());
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }

    /**
     * @brief
     * @param
     *    uint64_t job_id
     * @return array uint32_t count
     * uint32_t count
     */
    public static function getSubjobs($arrInput){
        if(!isset($arrInput['job_id'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $jobId = $arrInput['job_id'];
        $key = self::REDIS_KEY_PREFIX_PUSHJOB . $jobId;

        $redis = Lib_Util_Redis::initCache();
        if (false === $redis || is_null($redis)) {
            Bingo_Log::warning("get redis failed");
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        $arrParams = array(
            'key' => $key,
        );
        $arrRet = $redis->HGETALL($arrParams);
        if (!$arrRet || $arrRet['err_no'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $data = $arrRet['ret'][$key];
        $arrSubjobs = array();
        $intIndex = 0;
        foreach($data as $arrSubjob) {
            $arrSubjobs[$intIndex++] = $arrSubjob['field'];
        }

        $arrRet = $redis->HLEN($arrParams);
        if (!$arrRet || $arrRet['err_no'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $count = $arrRet['ret'][$key];

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => array(
                'count' => $count,
                'subjobs' => $arrSubjobs,    
            ),
        );
        return $arrOutput;
    }

    /**
     * @brief
     * @param
     *    uint64_t job_id
     *    uint64_t subjob_id
     * @return array uint32_t count
     * uint32_t count
     */
    public static function putSubjob($arrInput){
        if(!isset($arrInput['job_id']) 
            || !isset($arrInput['subjob_id'])){

            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $jobId = $arrInput['job_id'];
        $subjobId = $arrInput['subjob_id'];

        $key = self::REDIS_KEY_PREFIX_PUSHJOB . $jobId;
        $field = "$subjobId";
        $value = '1';

        $redis = Lib_Util_Redis::initCache();
        if (false === $redis || is_null($redis)) {
            Bingo_Log::warning("get redis failed");
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        $arrParams = array(
            'key' => $key,
            'field' => $field,
            'value' => $value,
        );
        $arrRet = $redis->HSET($arrParams);
        if (!$arrRet || $arrRet['err_no'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $arrRet = $redis->HLEN(array('key' => $key));
        if (!$arrRet || $arrRet['err_no'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $count = $arrRet['ret'][$key];
        $data = array('count' => $count);

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );
        return $arrOutput;
    }

    /**
     * @brief
     * @param
     *   uint64_t job_id
     * @return array uint32_t errno
     * uint32_t errno
     */
    public static function clearSubjob($arrInput){
        if(!isset($arrInput['job_id'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $jobId = $arrInput['job_id'];
        $key = self::REDIS_KEY_PREFIX_PUSHJOB . $jobId;

        $redis = Lib_Util_Redis::initCache();
        if (false === $redis || is_null($redis)) {
            Bingo_Log::warning("get redis failed");
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        
        $arrParams = array(
            'key' => strval($key),
        );

        $arrRet = $redis->DEL($arrParams);
        if (!$arrRet || $arrRet['err_no'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }

    /**
     * @brief:
     * @param:
     * @return array
     */
    public static function batchSetRedisExpire($arrInput) {
        if (!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $redis = Lib_Util_Redis::initCache();
        if (false === $redis || is_null($redis)) {
            Bingo_Log::warning("get redis failed");
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $arrParams = array(
            'reqs' => $arrInput['reqs'],
        );

        $arrRet = $redis->EXPIRE($arrParams);
        if($arrRet === false || $arrRet['err_no'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("call redis error");
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }

}
