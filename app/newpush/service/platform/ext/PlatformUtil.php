<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015:11:06 18:15:05
 * @version
 * @structs & methods(copied from idl.)
 */


/**
 * Class Service_Platform_Ext_PlatformUtil
 */
class Service_Platform_Ext_PlatformUtil
{

    private static $_whiteList = array(
        893543783,  // christopher621 lvning
        1085494236, // mengqingshare liukaining
        22626212,   // 路过的小明
        417185216,  // 如履薄bing
        1488040016, // GreyAnts lirubing
        1214739882, // 火山0611 hanlingzhi
        1498368150, // 程书坤
        211305909,  // wdd669920jsw
    );

    /**
     * @brief
     * @param: $errno
     * @return array : $ret
     */
    private static function _errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @bbrief
     * @param $errno
     * @param $errmsg
     * @return array : $ret
     * @internal param $ :
     *    uint32_t $errno
     *    string   $errmsg
     */
    private static function _errRetSelf($errno, $errmsg)
    {
        return array(
            'errno' => $errno,
            'errmsg' => $errmsg,
        );
    }

    /**
     * @brief
     * @param:
     * @return array : $arrOutput
     */
    public static function modifyFollowName($arrInput) {
        
        $ret = Service_Platform_Ext_PushTag::getAllTags($arrInput);
        if($ret === false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("call service modifyFollowName failed");
            return $ret;
        }

        $arrTags = $ret['data'];
        if (empty($arrTags)) {
            Bingo_Log::warning("call service modifyFollowName empty tag data");
            return $ret;
        }

        $intIndex = 0;
        $fids = array();

        foreach($arrTags as $arrTag) {
            $fids[$intIndex++] = $arrTag['follow_id'];
        }

        $params = array(
            'forum_id' => $fids,    
        );

        $ret = Tieba_Service::call('forum', 'getFnameByFid', $params, null, null, 'post', 'php', 'utf-8');
        if($ret === false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("call service modifyFollowName getFnameByFid failed");
            return $ret;
        }

        $arrNames = $ret['forum_name'];
        foreach($arrTags as $arrTag) {
            if (isset($arrNames[$arrTag['follow_id']]) && isset($arrNames[$arrTag['follow_id']]['forum_name'])) {
                $arrName = $arrNames[$arrTag['follow_id']]['forum_name'];
                if (!empty($arrName)) {
                    //$arrTag['tag_name'] = $arrName;
                    $arrTag['tag_desc'] = $arrName;
                    //$arrTag['group_name'] = $arrName;

                    $ret = Service_Platform_Ext_PushTag::updateTag($arrTag);
                    if($ret === false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
                        Bingo_Log::warning("call service modifyFollowName updateTag failed. tag_id=" . $arrTag['tag_id']);
                    }
                }
            } else {
                Bingo_Log::warning("call service modifyFollowName getFnameByFid failed. fid=" . $arrTag['follow_id']);
            }
        }

        return Service_Platform_Ext_PushTag::getAllTags($arrInput);
    }

    /**
     * @brief
     * @param:
     * @return array : $arrOutput
     */
    public static function deleteUnusedJob($arrInput) {
        if (!isset($arrInput['start_index']) || !isset($arrInput['end_index'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }    

        $startIndex = $arrInput['start_index'];
        $endIndex = $arrInput['end_index'];

        $index = 0;
        for ($index = $startIndex; $index <= $endIndex; $index++) {
            $ret = Service_Platform_Ext_PushJob::rawDeleteJob(array('job_id' => $index));
            if ($ret === false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("call service deleteUnusedJob rawDeleteJob failed.");
            }
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief
     * @param: $arrInput
     * @return array : $arrOutput
     */
    public static function modifyJobContent($arrInput)
    {
        if (!isset($arrInput['job_ids'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }    

        $arrJobIds = $arrInput['job_ids'];
        foreach ($arrJobIds as $jobId) {
            $ret = Service_Platform_Ext_PushJob::getJobById(array('job_id' => $jobId));
            $arrJob = $ret['data'];
            if (!empty($arrJob)) {
                $arrJob['content'] = preg_replace('/[\r\n]/', '\n', $arrJob['content']);

                $arrContent = Bingo_String::json2array($arrJob['content'], 'utf-8');
                if ($arrContent === false) {
                    Bingo_Log::warning("modifyJobContent failed. jobId=" . $arrJob['job_id']);
                    continue;
                }

                $arrInfoList = array();
                foreach($arrContent['info_list'] as $arrInfo) {
                    $arrInfoList[] = Lib_Util_Format::urlencodeArray($arrInfo);
                }

                $arrContent['info_list'] = $arrInfoList;
                $strContent = json_encode($arrContent);

                $arrJob['content'] = $strContent;
                Service_Platform_Ext_PushJob::rawUpdateJob($arrJob);
            }
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @brief
     * @param: $arrInput
     * @return array : $arrOutput
     */
    public static function testAuditAndStartPushJob($arrInput)
    {
        if (!isset($arrInput['job_id'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }    

        $jobId = intval($arrInput['job_id']);
        $ret = Service_Platform_Ext_PushJob::getJobById(array('job_id' => $jobId));

        $arrJob = $ret['data'];
        if (empty($arrJob)) {
            Bingo_Log::warning("input params invalid. job not existed!");
            return self::_errRetSelf(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'not exist job with id:' . $jobId);
        }

        if ($arrJob['status'] != Service_Platform_Ext_PushJob::STATUS_WAIT_FIRST_AUDIT) {
            Bingo_Log::warning("input params invalid. job status not correct!");
            return self::_errRetSelf(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'job status can not audit:' . $jobId);
        }

        //备份follow id, updateJob时设置回去
        $oldTagId = $arrJob['tag_id'];
        $oldFollowId = $arrJob['follow_id'];

        //强制将任务的吧id修改为test123吧
        $arrJob['follow_id'] = 2488131;
        $arrJob['status'] = Service_Platform_Ext_PushJob::STATUS_PUBLICED;

        //将job的推送时间调整为当前时间
        $arrJob['push_starttime'] = time();
        $arrJob['push_endtime'] = time() + 86400;
        
        $jobId = intval($arrJob['job_id']);

        //判断任务状态，如果不是已开始推送，则设置为开始推送状态
        if ($arrJob['status'] != Service_Platform_Ext_PushJob::STATUS_PUSH_START) {
            //先取任务对应的tag，如果不存在则将任务设置为非法任务
            //$ret = Service_Platform_Ext_PushTag::getTagsByFollowType(array('follow_type' => Lib_Util_Conf::FOLLOW_TYPE_FORUM_BROADCAST));
            //$tagRet = Dl_Tag_Tag::getTagById(array('tag_id' => $arrJob['tag_id']));
            $tagRet = Dl_Tag_Tag::getTagByFollowIdAndType(array('follow_id' => 2488131, 'follow_type' => Lib_Util_Conf::FOLLOW_TYPE_FORUM_BROADCAST));
            if (false === $tagRet || $tagRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning ('call Service getTagsById failed.');
                return self::_errRet (Tieba_Errcode::ERR_DL_CALL_FAIL);
            }

            $arrTag = $tagRet['data'];
            if (empty($arrTag)) {
                //如果没有找到job对应的tag，则将该job标记为非法状态,并返回错误
                $jobData['status'] = Service_Platform_Ext_PushJob::STATUS_UNLEGAL;
                Dl_Job_Job::updateJob($arrJob);
    
                //本次返回错误给客户
                Bingo_Log::warning ('call Service getStartPushJob failed. not exist tag for job id ' . $arrTag['job_id']);
                return self::_errRet (Tieba_Errcode::ERR_DL_CALL_FAIL);
            }

            //强制把job的tag_id换成test123吧对应的tag_id
            $arrJob['tag_id'] = $arrTag['tag_id'];

            $msgRet = Service_Platform_Ext_PushMsg::sendNewJobMsg($arrJob, $arrTag);
            if (false === $msgRet || $msgRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                return $msgRet;
            }

            $arrJob['tag_id'] = $oldTagId;
            $arrJob['follow_id'] = $oldFollowId;
            $arrJob['status'] = Service_Platform_Ext_PushJob::STATUS_PUSH_START;
            $jobRet = Dl_Job_Job::updateJob($arrJob);
            if (false === $jobRet || $jobRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning ('call Service updateJob failed.');
                return self::_errRet (Tieba_Errcode::ERR_DL_CALL_FAIL);
            }

            //记录任务开始时间
            Bingo_Log::warning("startPushJob job_id=" . $arrJob['job_id'] . " fid=" . $arrJob['follow_id'] . " time=" . time());

            //为了数据统计
            Service_Platform_Ext_PushMsg::initJobRecord($arrJob, $arrTag);
        }

        return $ret;
    }

    /**
     * 将白名单用户添加到用户表中
     * 单个单个添，防止某白名单用户已在库中，导致其他用户失败
     * @param: $arrInput
     * @return: 
     */
    public static function loadWhiteList($arrInput) {
        $arrRes = Service_Platform_Ext_PushTag::getAllTags(array());
        if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes ['errno']) {
            Bingo_Log::warning('call loadWhiteList failed. req: ' . serialize($arrInput) . ', res: ' .serialize($arrRes));
            return $arrRes;
        }

        foreach ($arrRes['data'] as $tag) {
            if ($tag['status'] == 1) {
                self::addWhiteList($tag['tag_id'], $tag['group_id'], $tag['group_type']);
            }
        }

        return self::_errRet (Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 将白名单用户添加到用户表中
     * 单个单个添，防止某白名单用户已在库中，导致其他用户失败
     * @param: tagId, groupId, groupType
     * @return: null
     */
    private static function addWhiteList($tagId, $groupId, $groupType) {
        $objRalMulti = new Tieba_Multi('storeUids');
        foreach (self::$_whiteList as $uid) {
            $arrInput = array(
                'serviceName' => 'newpush',
                'method'      => 'createTagUsers',
                'input'       => array(
                    'tag_key' => $tagId * Lib_Util_Conf::BUCKET_NUM + $uid % Lib_Util_Conf::BUCKET_NUM,
                    'group_id' => $groupId,
                    'group_type' => $groupType,
                    'status' => 0,
                    'uids' => array($uid),
                ),
            );
            $objRalMulti->register("storeuids_$uid", new Tieba_Service('newpush'), $arrInput);
        }
        $objRalMulti->call();
    }

    /**
     * 将uid对应的未关注的吧删除
     * @param: uid
     * @return: array
     */
    public static function removeWhiteListByUid($arrInput) {
        if (!isset($arrInput['uid'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $uid = intval($arrInput['uid']);
        $serviceInput = array(
            "user_id" => $uid,
            "check_forum" => 1,
            "page_no" => 1,
            "page_size" => 300,
        );

        $ret = Tieba_Service::call('perm', 'getLikeForumListIndex', $serviceInput, null, null, 'post', 'php', 'utf-8');
        if ($ret === false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ('call Service getLikeForumListIndex failed');
            return self::_errRet (Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrLikeForumList = $ret['output']['member_list'];

        $ret = Service_Platform_Ext_PushTag::getAllTags(array());
        if($ret === false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("call service modifyFollowName failed");
            return $ret;
        }

        $arrTags = $ret['data'];
        if (empty($arrTags)) {
            Bingo_Log::warning("call service modifyFollowName empty tag data");
            return $ret;
        }

        foreach ($arrTags as $arrTag) {
            $tagId = $arrTag['tag_id'];
            $followId = $arrTag['follow_id'];

            if (!isset($arrLikeForumList[$followId])) {
                //如果该用户未关注该吧，则将该用户从吧用户列表中移除
                $tag_key = intval($tagId * Lib_Util_Conf::BUCKET_NUM + $uid % Lib_Util_Conf::BUCKET_NUM);
                $arrParams = array(
                    'tag_key' => $tag_key,
                    'uid' => $uid,
                );

                $ret = Service_Platform_Ext_PushUser::removeTagUser($arrParams);
            }
        }

        return self::_errRet (Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param: $arrInput
     * @return: 
     */
    public static function queryCacheInfo($arrInput) {
        if (!isset($arrInput['key']) || !isset($arrInput['type'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $objCache = Lib_Util_Cache::getInstance();
        if (!$objCache) {
            Bingo_Log::warning('call service queryCache failed. Lib_Util_Cache::getInstance is null');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $cacheKey = $arrInput['key'];
        $cacheType = $arrInput['type'];

        //将吧id列表添加到cache key中
        $ret = $objCache->get($cacheKey, $cacheType);
        if ($ret === false) {
            Bingo_Log::warning('call service queryCache failed. Lib_Util_Cache::add failed.');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $ret,
        );
    }

    /**
     * @param: $arrInput
     * @return: 
     */
    public static function removeCacheInfo($arrInput) {
        if (!isset($arrInput['key']) || !isset($arrInput['type'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $objCache = Lib_Util_Cache::getInstance();
        if (!$objCache) {
            Bingo_Log::warning('call service remvoveTagCache failed. Lib_Util_Cache::getInstance is null');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $cacheKey = $arrInput['key'];
        $cacheType = $arrInput['type'];

        //将吧id列表添加到cache key中
        $ret = $objCache->remove($cacheKey, $cacheType);
        if ($ret === false) {
            Bingo_Log::warning('call service removeTagCache failed. Lib_Util_Cache::add failed.');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

}
