<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015:11:06 18:15:05
 * @version
 * @structs & methods(copied from idl.)
 */

class Service_Platform_Ext_PushOrigJob
{
    const STATUS_UNLEGAL = 0;
    const STATUS_NEW = 1;                //新建：新建任务暂未提交，可再次编辑内容
    const STATUS_RJECTED = 2;            //审核拒绝：初审终审拒绝均更新为该状态
    const STATUS_WAIT_READY = 3;         //等待用户数据准备中：新建者编辑完成后提交任务进入该状态，如果需要审核则等待审核，否则在到发布时间时进入到发布状态
    const STATUS_READY = 4;              //数据已准备好状态
    const STATUS_WAIT_FIRST_AUDIT = 5;   //等待初审中
    const STATUS_WAIT_FINAL_AUDIT = 6;   //初审通过,等待终审
    const STATUS_WAIT_PUBLIC = 7;        //终审通过，待发布状态
    const STATUS_PUBLICED = 8;           //已发布：在到达发布时间时转入该状态
    const STATUS_PUSH_START = 9;         //推送开始：到达推送时间时转入该状态
    const STATUS_PUSH_END = 10;          //推送结束：任务完成
    const STATUS_COMPLEMENTLY = 11;      //任务结束，在所有子任务完成时更新至该状态代表彻底结束
    
    const DEFAULT_FOLLOW_TYPE = Lib_Util_Conf::FOLLOW_TYPE_FORUM_BROADCAST; //默认服务类型
    /**
     * @brief errRet
     * @param
     *   uint32_t errno
     * @return
     *    array ret
     **/
    private static function _errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @bbrief errRetSelf
     * @param
     *   uint32_t errno
     *   string errmsg
     * @return
     *   array ret
     **/
    private static function _errRetSelf($errno, $errmsg)
    {
        return array(
            'errno' => $errno,
            'errmsg' => $errmsg,
        );
    }

    /**
     * @brief: 吧广播和吧主专版注册消息接口
     * @param: $arrInput
     * @return: $arrOutput
     **/
    public static function submitNewJob($arrInput)
    {
        //如果未指定follow_type，则默认是吧广播，此处目前对接吧广播和吧主专版
        if (!isset($arrInput['follow_type']) || empty($arrInput['follow_type'])) {
            $arrInput['follow_type'] = self::DEFAULT_FOLLOW_TYPE;  
        }
        
        return self::createNewJob($arrInput);
    }

    /**
     * @brief
     * @param:
     *    uint64_t job_id
     *    uint64_t follow_id
     *    uint64_t follow_type
     *    string   follow_name
     *    string   content
     *    uint64_t creator_id
     *    uint32_t creator_role
     *    string   creator_name
     *    uint64_t push_starttime
     *    uint64_t push_endtime
     * @return: $arrOutput
     **/
    private static function createNewJob($arrInput)
    {
        //该推送系统job_id手动生成，如果采用原平台的job_id，可能会与现系统发生冲突
        if (!isset($arrInput['job_id'])
            || !isset($arrInput['msg_type'])
            || !isset($arrInput['follow_id'])
            || !isset($arrInput['follow_type'])
            || !isset($arrInput['follow_name'])
            || !isset($arrInput['content'])
            || !isset($arrInput['push_starttime'])
            || !isset($arrInput['push_endtime'])) {

            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        Bingo_Log::notice("createNewJob input:" . serialize($arrInput));

        $jobId = intval($arrInput['job_id']);
        $intFollowType = intval($arrInput['follow_type']);  

        switch ($intFollowType) {
            case Lib_Util_Conf::FOLLOW_TYPE_FORUM_BROADCAST:
            case Lib_Util_Conf::FOLLOW_TYPE_OFFICIAL:
            case Lib_Util_Conf::FOLLOW_TYPE_SUBSCRIBE_ACCOUNT:
                $arrTagInfo = self::_getBroadcastTag($arrInput);
                if (!$arrTagInfo || !isset($arrTagInfo['errno']) || $arrTagInfo['errno'] != Tieba_Errcode::ERR_SUCCESS){
                    return $arrTagInfo;
                }
                $tagData = $arrTagInfo['data'];
                break;
            case Lib_Util_Conf::FOLLOW_TYPE_BAZHUTASK:
            case Lib_Util_Conf::FOLLOW_TYPE_BAZHUNOTICE:
                $arrTagInfo = self::_getBazhuTaskTag($arrInput);
                if (!$arrTagInfo || !isset($arrTagInfo['errno']) || $arrTagInfo['errno'] != Tieba_Errcode::ERR_SUCCESS){
                    return $arrTagInfo;
                }
                $tagData = $arrTagInfo['data'];
                break;
            default:
                Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $pushStartTime = intval($arrInput['push_starttime']);
        $pushEndTime = intval($arrInput['push_endtime']);
        if ($pushStartTime < 1) {
            $pushStartTime = time();
            $pushEndTime = $pushStartTime + 86400;
        }
        if ($pushEndTime < 1) {
            $pushEndTime = $pushStartTime + 86400;
        }

        //处理推送内容，添加msg_type
        $arrSendContent = array();
        $arrInfoList = array();

        $intMsgType = intval($arrInput['msg_type']);
        $arrSendContent['msgType'] = $intMsgType;
        $arrSendContent['link'] = '';

        //此处有单文本以及图文内容  1: 单文本
        if ($intMsgType == 1) {
            $arrInfoList = Lib_Util_Format::urlencodeArray($arrInput['content']);
        } else {
            foreach($arrInput['content'] as $arrInfo) {
                $arrInfoList[] = Lib_Util_Format::urlencodeArray($arrInfo);
            }
        }

        $boolEmptyInfo = false;
        //检测是否内容解析成功
        if (empty($arrInfoList)) {
            $boolEmptyInfo = true;
        }
       
        $arrSendContent['info_list'] = $arrInfoList;
        // 用户展示样式，由业务方决定
        if (isset($arrInput['userinfo']) && is_array($arrInput['userinfo'])) {
            $arrSendContent['userinfo'] = $arrInput['userinfo'];
        }
        // 用户能否收到消息过滤版本，由业务方决定
        if (isset($arrInput['version_rule'])) {
            $arrSendContent['version_rule'] = $arrInput['version_rule'];
        }

        //此处入库时不进行urldecode, 在出库后先执行json_decode,然后再对字段进行urldecode
        $strContent = json_encode($arrSendContent);

        $arrDbInput = array(
            'job_id' => $jobId,
            'tag_id' => intval($tagData['tag_id']),
            'follow_id' => intval($arrInput['follow_id']),
            'follow_type' => $intFollowType, 

            'follow_name' => $arrInput['follow_name'],
            'priority' => isset($arrInput['priority']) ? intval($arrInput['priority']) : 0,
            'content' => $strContent,

            'filepath' => (isset($arrInput['filepath']) && !empty($arrInput['filepath'])) ? $arrInput['filepath'] : '',
            'md5value' =>(isset($arrInput['md5value']) && !empty($arrInput['md5value'])) ? $arrInput['md5value'] : '' ,

            'creator_id' => 0,
            'creator_role' => 0,
            'creator_name' => (isset($arrInput['creator_name']) && empty($arrInput['creator_name'])) ? $arrInput['creator_name'] : '',
            'publishtime' => 0,
            'push_starttime' => $pushStartTime,
            'push_endtime' => $pushEndTime,
        );

        //任务的默认状态是READY，等待用户数据准备
        $arrDbInput['status'] = self::STATUS_WAIT_READY;

        //如果用户数据已准备好，直接将任务状态设置为已准备好
        //吧主专版: STATUS_WAIT_READY
        //吧广播: STATUS_WAIT_READY -> STATUS_WAIT_FIRST_AUDIT
        //官方吧: STATUS_WAIT_READY -> STATUS_PUBLICED 
        if (isset($tagData['status']) && $tagData['status'] == Service_Platform_Ext_PushTag::TAG_STATUS_INITED) {
            switch ($intFollowType) {
                case Lib_Util_Conf::FOLLOW_TYPE_FORUM_BROADCAST:
                    $arrDbInput['status'] = self::STATUS_WAIT_FIRST_AUDIT;
                    break;
                case Lib_Util_Conf::FOLLOW_TYPE_OFFICIAL:
                case Lib_Util_Conf::FOLLOW_TYPE_SUBSCRIBE_ACCOUNT:
                    $arrDbInput['status'] = self::STATUS_PUBLICED;
                    break;
                case Lib_Util_Conf::FOLLOW_TYPE_BAZHUTASK:
                    break;
                default:
                    Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
                    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
        }

        if ($intFollowType == Lib_Util_Conf::FOLLOW_TYPE_BAZHUTASK || $intFollowType == Lib_Util_Conf::FOLLOW_TYPE_BAZHUNOTICE){
            $arrDbInput['status'] = self::STATUS_PUBLICED;
        }
        //如果推送的info内容为空，直接将任务的状态设置为结束状态
        if ($boolEmptyInfo) {
            $arrDbInput['status'] = self::STATUS_COMPLEMENTLY;
        }

        $arrDbInput['createtime'] = time();

        $res = Dl_Job_Job::insertJob($arrDbInput);
        if (!$res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ('call DL insertJob failed.');
            return self::_errRet (Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrOutput = array(
            'errno' => Tieba_Errcode::ERR_SUCCESS,
            'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
            'job_id' => $jobId,
        );
        return $arrOutput;
    }


    /**
     * @brief: 当前的平台使用该接口审核任务，将任务转为终审状态, 批量操作，任何一个任务检查失败则报错
     * @param: $arrInput
     * @return: $arrOutput
     **/
    public static function batchAuditNewJob($arrInput)
    {
        if (!isset($arrInput['job_ids'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //如果传输了push_starttime，则设置任务推送时间
        $intPushStartTime = time();
        $intPushEndTime = time() + 86400;
        if (isset($arrInput['push_starttime'])) {
            $intPushStartTime = intval($arrInput['push_starttime']);
            $intPushEndTime = $intPushStartTime + 86400;
        }
        //批量审核任务，要判断任务状态是否用户数据已准备好？
        $res = Dl_Job_Job::getJobsByIds(array('job_ids' => $arrInput['job_ids']));
        if (false === $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ('call DL getJobsByIds failed.');
            return self::_errRet (Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        //如果提交审核的某一个任务ID不存在？
        //如果任务列表中有一个任务的状态不对？
        //目前处理是任一条件不满足，全部失败
        $arrJobs = $res['data'];
        if (empty($arrJobs)) {
            Bingo_Log::warning("not exist jobs with ids " . serialize($arrInput['job_ids']));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAILED);
        }

        $arrHashJobs = array();
        foreach($arrJobs as $arrJob) {
            $arrHashJobs[$arrJob['job_id']] = $arrJob;
        }

        foreach($arrInput['job_ids'] as $jobId) {
            //判断传入的job_ids是否都存在
            if (!isset($arrHashJobs[$jobId])) {
                Bingo_Log::warning("not exist jobs with id " . $jobId);
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
            //判断任务状态是否为已准备好数据或者已审核完成
            $jobData = $arrHashJobs[$jobId];
            if ($jobData['status'] != self::STATUS_READY
                && $jobData['status'] != self::STATUS_WAIT_FIRST_AUDIT
                && $jobData['status'] != self::STATUS_WAIT_FINAL_AUDIT) {

                Bingo_Log::warning("job status could not audit " . $jobId);
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
        }

        foreach($arrJobs as $arrJob) {
            $arrJob['status'] = self::STATUS_PUBLICED;
            if ($intPushStartTime > 0) {
                $arrJob['push_starttime'] = $intPushStartTime;
                $arrJob['push_endtime'] = $intPushEndTime;
            }
            Dl_Job_Job::updateJob($arrJob);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );
        return $arrOutput;
    }

    /**
     * @brief: 当前的平台使用该接口查询任务是否可以进行审核，批量查询
     * @param: $arrInput
     * @return: $arrOutput
     **/
    public static function queryNewJobsByIds($arrInput)
    {
        if (!isset($arrInput['job_ids'])){
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $res = Dl_Job_Job::getJobsByIds(array('job_ids' => $arrInput['job_ids']));
        if (false === $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ('call DL getJobsByIds failed.');
            return self::_errRet (Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        //如果ID对应的任务不存在，不返回该任务
        $arrJobs = $res['data'];
        if (empty($arrJobs)) {
            Bingo_Log::warning("not exist jobs with ids " . serialize($arrInput['job_ids']));
            return self::_errRet (Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrNewJobs = array();

        //对返回任务的状态进行调整，0：数据未准备好，1：数据已准备好，可审核  2：已审核通过
        foreach($arrJobs as $arrJob) {
            if ($arrJob['status'] < self::STATUS_READY) {
                //0：表示用户数据还未准备好，暂时不可审核
                $arrJob['status'] = 0;
            } elseif ($arrJob['status'] < self::STATUS_WAIT_PUBLIC)  {
                //1: 表示用户数据已准备好，可以进行审核
                $arrJob['status'] = 1;
            } elseif ($arrJob['status'] == self::STATUS_PUSH_START) {
                //任务已经开始推送
                $arrJob['status'] = 3;
            } elseif ($arrJob['status'] > self::STATUS_PUSH_START) {
                //任务已经推送完成
                $arrJob['status'] = 4;
            } else {
                //
                $arrJob['status'] = 2;
            }
            $arrNewJobs[$arrJob['job_id']] = $arrJob;
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $arrNewJobs,
        );
        return $arrOutput;
    }

    /**
     * @param arrInput
     * @return tagId
     * */
    private static function _getBroadcastTag($arrInput){
        //根据follow_id和follow_type查询tag
        $arrQueryDbInput = array(
            'follow_id' => $arrInput['follow_id'],
            'follow_type' => $arrInput['follow_type'],
        ); 
        
        $ret = Dl_Tag_Tag::getTagByFollowIdAndType($arrQueryDbInput);
        if (!$ret || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            return $ret;
        }

        $arrData = $ret['data'];
        if (empty($arrData)) {
            //新建对应follow_type的tag
            $followId = intval($arrInput['follow_id']);
            $followType = intval($arrInput['follow_type']);
            $followName = $arrInput['follow_name'];

            $arrDbInput = array(
                'tag_name' => $followName,
                'tag_desc' => $followName,
                'manager_id' => 0,
                'manager_role' => 0, 
                'group_id' => $followId,
                'group_name' => $followName,
                'group_type' => 30,
                'follow_id' => $followId,
                'follow_type' => $followType,
            );

            $ret = Service_Platform_Platform::createTag($arrDbInput);
            if (false === $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning ('call service createTag failed.');
                return self::_errRet (Tieba_Errcode::ERR_DL_CALL_FAIL);
            }

            //根据follow_id和follow_type查询tag，如果失败则插入失败
            $ret = Dl_Tag_Tag::getTagByFollowIdAndType($arrQueryDbInput);
            if (!$ret || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                return $ret;
            }

            $arrData = $ret['data'];
            if (empty($arrData)) {
                Bingo_Log::warning ('submitNewJob failed. not exist tag for follow id:' . $arrInput['follow_id']);
                return self::_errRetSelf (Tieba_Errcode::ERR_DL_CALL_FAIL, "submitNewJob failed. not exist tag of follow id:" . $arrInput['follow_id']);
            }
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $arrData,
        );

        return $arrOutput;
    }

    /**
     * @param nil
     * @return  array
     **/
    private static function _getBazhuTaskTag($arrInput) {
        $arrData = array();
        $arrData['tag_id'] = Lib_Util_Conf::BAZHUTASK_TAG_ID;
        //$arrData['status'] = self::STATUS_WAIT_PUBLIC;
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $arrData,
        );
        return $arrOutput;
    } 
}
