<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015:11:06 18:15:05
 * @version
 * @structs & methods(copied from idl.)
 */

class Service_Platform_Ext_TagCache
{

	/**
	 * @brief errRet
	 * @param
	 *   uint32_t errno
	 * @return
	 *    array ret
	 **/
    private static function _errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

	/**
	 * @brief
	 * @param: $arrInput
	 * @return array : $arrOutput
	 */
    public static function queryTagCache($follow_id, $follow_type)
	{
		$objCache = Lib_Util_Cache::getInstance();
		if (!$objCache) {
		    Bingo_Log::warning('call service queryTagCache failed. Lib_Util_Cache::getInstance is null');
		    return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}

		$cacheType = Lib_Util_Conf::CACHEKEY_TAG_FOLLOW_ID_TYPE;
		$cacheKey = $follow_id . ":" . $follow_type;

		//将吧id列表添加到cache key中
		$ret = $objCache->get($cacheKey, $cacheType);
		if ($ret === false) {
		    Bingo_Log::warning('call service queryTagCache failed. Lib_Util_Cache::add failed.');
		    return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}

		$errno = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno' => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
			'data' => unserialize($ret),
        );
    }

	/**
	 * @brief
	 * @param: $arrInput
	 * @return array : $arrOutput
	 */
    public static function addTagCache($arrTag)
	{
		$objCache = Lib_Util_Cache::getInstance();
		if (!$objCache) {
		    Bingo_Log::warning('call service addTagCache failed. Lib_Util_Cache::getInstance is null');
		    return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}

		$cacheType = Lib_Util_Conf::CACHEKEY_TAG_FOLLOW_ID_TYPE;
		$cacheKey = $arrTag['follow_id'] . ":" . $arrTag['follow_type'];

		//将吧id列表添加到cache key中
		$ret = $objCache->add($cacheKey, serialize($arrTag), 600, $cacheType);
		if ($ret === false) {
		    Bingo_Log::warning('call service addTagCache failed. Lib_Util_Cache::add failed.');
		    return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}

		$errno = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno' => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

	/**
	 * @brief
	 * @param: $arrInput
	 * @return array : $arrOutput
	 */
    public static function removeTagCache($follow_id, $follow_type)
	{
		$objCache = Lib_Util_Cache::getInstance();
		if (!$objCache) {
		    Bingo_Log::warning('call service remvoveTagCache failed. Lib_Util_Cache::getInstance is null');
		    return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}

		$cacheType = Lib_Util_Conf::CACHEKEY_TAG_FOLLOW_ID_TYPE;
		$cacheKey = $follow_id . ":" . $follow_type;

		//将吧id列表添加到cache key中
		$ret = $objCache->remove($cacheKey, $cacheType);
		if ($ret === false) {
		    Bingo_Log::warning('call service removeTagCache failed. Lib_Util_Cache::add failed.');
		    return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}

		$errno = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno' => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }
}
