<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015:11:06 18:15:05
 * @version
 * @structs & methods(copied from idl.)
 */

class Service_Platform_Ext_PushTag
{

    const TAG_STATUS_NEW = 0; //tag用户信息未初始化
    const TAG_STATUS_INITED = 1; //tag用户信息已初始化

    /**
     * @brief errRet
     * @param
     *   uint32_t errno
     * @return
     *    array ret
     **/
    private static function _errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @bbrief errRetSelf
     * @param
     *   uint32_t errno
     *   string errmsg
     * @return
     *   array ret
     **/
    private static function _errRetSelf($errno, $errmsg)
    {
        return array(
            'errno' => $errno,
            'errmsg' => $errmsg,
        );
    }

    /**
     * @brief
     * @param:
     *    uint64_t tag_id
     *    string tag_name
     *    string tag_desc
     *    uint64_t manager_id
     *    uint32_t maneger_role
     *    uint64_t group_id
     *    string group_name
     *    uint32_t group_type
     *    string follow_id
     *    uint32_t follow_type
     * @return: $arrOutput
     **/
    public static function createTag($arrInput)
    {

        if (!isset($arrInput['tag_id'])
            || !isset($arrInput['tag_name'])
            || !isset($arrInput['tag_desc'])
            || !isset($arrInput['manager_id'])
            || !isset($arrInput['manager_role'])
            || !isset($arrInput['group_id'])
            || !isset($arrInput['group_name'])
            || !isset($arrInput['group_type'])
            || !isset($arrInput['follow_id'])
            || !isset($arrInput['follow_type'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $tagId = $arrInput['tag_id'];
        $arrDbInput = array(
            'tag_id' => $tagId,
            'tag_name' => $arrInput['tag_name'],
            'tag_desc' => $arrInput['tag_desc'],
            'manager_id' => $arrInput['manager_id'],
            'manager_role' => $arrInput['manager_role'],
            'group_id' => intval($arrInput['group_id']),
            'group_name' => $arrInput['group_name'],
            'group_type' => intval($arrInput['group_type']),
            'follow_id' => $arrInput['follow_id'],
            'follow_type' => intval($arrInput['follow_type']),
        );

        $arrDbInput['status'] = self::TAG_STATUS_NEW;
        $arrDbInput['createtime'] = time();

        $res = Dl_Tag_Tag::insertTag($arrDbInput);
        if (false === $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ('call DL insertTag failed.');
            return self::_errRet (Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $res = Dl_Tag_Tag::getTagById(array('tag_id' => $tagId));
        if (false === $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ('call DL getTagById failed.');
            return self::_errRet (Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $data = $res['data'];
        if (empty($data)) {
            Bingo_Log::warning('call DL insertTag failed.');
            return self::_errRetSelf(Tieba_Errcode::ERR_DL_CALL_FAIL, " create tag failed with tag id:" . $tagId);
        }

        //$data = array('tag_id' => $tagId);
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );
        return $arrOutput;
    }

    /**
     * @brief
     * @param:
     *    uint64_t tag_id
     *    string tag_name
     *    string tag_desc
     *    uint64_t manager_id
     *    uint32_t maneger_role
     *    uint64_t group_id
     *    string group_name
     *    uint32_t group_type
     *    string follow_id
     *    uint32_t follow_type
     *    uint32_t status
     * @return: $arrOutput
     **/
    public static function updateTag($arrInput)
    {

        if (!isset($arrInput['tag_id'])
            || (!isset($arrInput['tag_name'])
                && !isset($arrInput['tag_desc'])
                && !isset($arrInput['manager_id'])
                && !isset($arrInput['manager_role'])
                && !isset($arrInput['group_id'])
                && !isset($arrInput['group_name'])
                && !isset($arrInput['group_type'])
                && !isset($arrInput['follow_id'])
                && !isset($arrInput['follow_type'])
                && !isset($arrInput['status']))) {

            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $tagRes = Dl_Tag_Tag::getTagById(array('tag_id' => $arrInput['tag_id']));
        if (false === $tagRes || $tagRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ('call DL getTagById failed.');
            return self::_errRet (Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $tagData = $tagRes['data'];
        if (empty($tagData)) {
            Bingo_Log::warning('Call DL_Job::getTagById empty result:' . $arrInput['tag_id']);
            return self::_errRetSelf(Tieba_Errcode::ERR_DL_CALL_FAIL, 'not exist tag with id:' . $arrInput['tag_id']);
        }

        if (isset($arrInput['tag_name'])) {
            $tagData['tag_name'] = $arrInput['tag_name'];
        }
        if (isset($arrInput['tag_desc'])) {
            $tagData['tag_desc'] = $arrInput['tag_desc'];
        }
        if (isset($arrInput['manager_id'])) {
            $tagData['manager_id'] = $arrInput['manager_id'];
        }
        if (isset($arrInput['manager_role'])) {
            $tagData['manager_role'] = $arrInput['manager_role'];
        }
        if (isset($arrInput['group_id'])) {
            $tagData['group_id'] = $arrInput['group_id'];
        }
        if (isset($arrInput['group_name'])) {
            $tagData['group_name'] = $arrInput['group_name'];
        }
        if (isset($arrInput['group_type'])) {
            $tagData['group_type'] = $arrInput['group_type'];
        }
        if (isset($arrInput['follow_id'])) {
            $tagData['follow_id'] = $arrInput['follow_id'];
        }
        if (isset($arrInput['follow_type'])) {
            $tagData['follow_type'] = $arrInput['follow_type'];
        }
        if (isset($arrInput['status'])) {
            $tagData['status'] = $arrInput['status'];
        }

        $res = Dl_Tag_Tag::updateTag($tagData);
        if (false === $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ('call DL updateTag failed.');
            return self::_errRet (Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }

    /**
     * @brief
     * @param:
     *    uint64_t tag_id
     * @return: $arrOutput
     *    push_tag_info data
     **/
    public static function getTagById($arrInput)
    {

        if (!isset($arrInput['tag_id'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $res = Dl_Tag_Tag::getTagById(array('tag_id' => $arrInput['tag_id']));
        if (false === $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call DL getTagById failed.');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $data = $res['data'];

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );
        return $arrOutput;
    }

    /**
     * @brief
     * @param 
     *   array tag_ids
     * @return: $arrOutput
     **/
    public static function getTagsByIds($arrInput) {
        if (!isset($arrInput['tag_ids'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);    
        }

        return Dl_Tag_Tag::getTagsByIds(array("tag_ids" => $arrInput['tag_ids']));
    }

    /**
     * @brief
     * @param:
     *    string tag_name
     * @return: $arrOutput
     *    push_tag_info data
     **/
    public static function getTagByName($arrInput)
    {

        if (!isset($arrInput['tag_name'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $res = Dl_Tag_Tag::getTagByName(array('tag_name' => $arrInput['tag_name']));
        if (false === $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call DL getTagByName failed.');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $data = $res['data'];

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );

        return $arrOutput;
    }

    /**
     * @brief
     * @param:
     *    uint32_t follow_type
     * @return: $arrOutput
     *    push_tag_info data[]
     **/
    public static function getTagsByFollowType($arrInput)
    {

        if (!isset($arrInput['follow_type'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrDbInput = array(
            'follow_type' => intval($arrInput['follow_type']),
        );

        $res = Dl_Tag_Tag::getTagsByFollowType($arrDbInput);
        if (false === $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call DL getTagsByFollowType failed.');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $data = $res['data'];

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );
        return $arrOutput;
    }

    /**
     * @brief
     * @param:
     *    uint32_t follow_type
     * @return: $arrOutput
     *    push_tag_info data[]
     **/
    public static function getTagsByFollowTypes($arrInput)
    {

        if (!isset($arrInput['follow_types'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $res = Dl_Tag_Tag::getTagsByFollowTypes($arrInput);
        if (false === $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call DL getTagsByFollowTypes failed.');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $data = $res['data'];

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );
        return $arrOutput;
    }

    /**
     * @brief
     * @param:
     *    uint32_t follow_type
     * @return: $arrOutput
     *    push_tag_info data[]
     **/
    public static function getTagFollowIdsByFollowTypes($arrInput)
    {

        if (!isset($arrInput['follow_types'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $res = Dl_Tag_Tag::getTagFollowIdsByFollowTypes($arrInput);
        if (false === $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call DL getTagsByFollowTypes failed.');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $data = $res['data'];

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );
        return $arrOutput;
    }
    /**
     * @brief
     * @param:
     *    string follow_id
     *    uint32_t follow_type
     * @return array : $arrOutput
     *    push_tag_info data[]
     */
    public static function getTagByFollowIdAndType($arrInput)
    {

        if (!isset($arrInput['follow_id']) || !isset($arrInput['follow_type'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrDbInput = array(
            'follow_id' => $arrInput['follow_id'],
            'follow_type' => intval($arrInput['follow_type']),
        );

        $res = Dl_Tag_Tag::getTagByFollowIdAndType($arrDbInput);
        if (false === $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call DL getTagByFollowIdAndType failed.');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $data = $res['data'];

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );
        return $arrOutput;
    }

    /**
     * @brief
     * @param:
     *    string follow_id
     * @return: $arrOutput
     *    push_tag_info data[]
     **/
    public static function getTagsByFollowId($arrInput)
    {

        if (!isset($arrInput['follow_id'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrDbInput = array(
            'follow_id' => $arrInput['follow_id'],
        );

        $res = Dl_Tag_Tag::getTagsByFollowId($arrDbInput);
        if (false === $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call DL getTagByFollowIdAndType failed.');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $data = $res['data'];

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );
        return $arrOutput;
    }

    /**
     * @brief
     * @param:
     *    string follow_id
     *    array follow_types
     * @return: $arrOutput
     *    push_tag_info data[]
     **/
    public static function getTagsByFollowIdAndTypes($arrInput)
    {

        if (!isset($arrInput['follow_id']) || !isset($arrInput['follow_types'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrDbInput = array(
            'follow_id' => $arrInput['follow_id'],
            'follow_types' => $arrInput['follow_types'],
        );

        $res = Dl_Tag_Tag::getTagsByFollowIdAndTypes($arrDbInput);
        if (false === $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call DL getTagByFollowIdAndType failed.');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $data = $res['data'];

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );
        return $arrOutput;
    }

    /**
     * @brief
     * @param:
     *    string follow_id
     *    uint32_t follow_type
     * @return array : $arrOutput
     *    push_tag_info data[]
     */
    public static function getAllTags($arrInput)
    {
        $res = Dl_Tag_Tag::getAllTags(array());
        if (false === $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call DL getAllTags failed.');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $data = $res['data'];

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );
        return $arrOutput;
    }
}
