<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015:11:06 18:15:05
 * @version
 * @structs & methods(copied from idl.)
 */

class Service_Platform_Cache_TagCache
{

    /**
     * @brief errRet
     * @param
     *   uint32_t errno
     * @return
     *    array ret
     **/
    private static function _errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @brief
     * @param: $arrInput
     * @return array : $arrOutput
     */
    public static function queryTagCache($follow_id, $follow_type)
    {
        $objCache = Lib_Util_Cache::getInstance();
        if (!$objCache) {
            Bingo_Log::warning('call service queryTagCache failed. Lib_Util_Cache::getInstance is null');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $cacheType = Lib_Util_Conf::CACHEKEY_TAG_FOLLOW_ID_TYPE;
        $cacheKey = $follow_id . ":" . $follow_type;

        //将吧id列表添加到cache key中
        $ret = $objCache->get($cacheKey, $cacheType);
        if ($ret === false) {
            Bingo_Log::warning('call service queryTagCache failed. Lib_Util_Cache::add failed.');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => unserialize($ret),
        );
    }

    /**
     * @brief
     * @param: $arrInput
     * @return array : $arrOutput
     */
    public static function addTagCache($arrTag)
    {
        $objCache = Lib_Util_Cache::getInstance();
        if (!$objCache) {
            Bingo_Log::warning('call service addTagCache failed. Lib_Util_Cache::getInstance is null');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $cacheType = Lib_Util_Conf::CACHEKEY_TAG_FOLLOW_ID_TYPE;
        $cacheKey = $arrTag['follow_id'] . ":" . $arrTag['follow_type'];

        //将吧id列表添加到cache key中
        $ret = $objCache->add($cacheKey, serialize($arrTag), 600, $cacheType);
        if ($ret === false) {
            Bingo_Log::warning('call service addTagCache failed. Lib_Util_Cache::add failed.');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @brief
     * @param: $arrInput
     * @return array : $arrOutput
     */
    public static function removeTagCache($follow_id, $follow_type)
    {
        $objCache = Lib_Util_Cache::getInstance();
        if (!$objCache) {
            Bingo_Log::warning('call service remvoveTagCache failed. Lib_Util_Cache::getInstance is null');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $cacheType = Lib_Util_Conf::CACHEKEY_TAG_FOLLOW_ID_TYPE;
        $cacheKey = $follow_id . ":" . $follow_type;

        //将吧id列表添加到cache key中
        $ret = $objCache->remove($cacheKey, $cacheType);
        if ($ret === false) {
            Bingo_Log::warning('call service removeTagCache failed. Lib_Util_Cache::add failed.');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @brief
     * @param: $arrInput
     * @return array : $arrOutput
     */
    public static function removeTagCacheKeys($arrInput)
    {
        $objCache = Lib_Util_Cache::getInstance();
        if (!$objCache) {
            Bingo_Log::warning('call service removeTagCacheKeys failed. Lib_Util_Cache::getInstance is null');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $cacheKey = Lib_Util_Conf::CACHEKEY_NEWPUSH_ENABLEFIDS;
        $cacheType = $cacheKey;

        //添加之前先移除，但忽略返回结果
        $ret = $objCache->remove($cacheKey, $cacheType);
        if ($ret === false) {
            Bingo_Log::warning('call service removeTagCacheKeys failed. Lib_Util_Cache::remove failed.');
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @brief
     * @param: $arrInput
     * @return array : $arrOutput
     */
    public static function loadTagCacheKeys($arrInput)
    {
        //$ret = Service_Platform_Ext_PushTag::getAllTags($arrInput);
        $arrDbInput = array(
            'follow_types' => array(
                Lib_Util_Conf::FOLLOW_TYPE_FORUM_BROADCAST,
                Lib_Util_Conf::FOLLOW_TYPE_OFFICIAL,
                Lib_Util_Conf::FOLLOW_TYPE_SUBSCRIBE_ACCOUNT,
            ),
        );

        $ret = Service_Platform_Ext_PushTag::getTagFollowIdsByFollowTypes($arrDbInput);
        if ($ret === false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call service loadTagCacheKeys for getAllTags failed");
            return $ret;
        }

        $arrTags = $ret['data'];
        if (empty($arrTags)) {
            Bingo_Log::warning("call service loadTagCacheKeys empty tag data");
            return $ret;
        }

        $fids = array();
        foreach($arrTags as $arrTag) {
            //此处只缓存已经初始化好的tag即可
            if ($arrTag['status'] == Dl_Tag_Tag::TAG_STATUS_INITED) {
                $fids[] = $arrTag['follow_id'];
            }
        }

        $fids = array_unique($fids);
        
        //此处不做缓存，由ui层进行缓存
        //$objCache = Lib_Util_Cache::getInstance();
        //if (!$objCache) {
        //    Bingo_Log::warning('call service loadTagCacheKeys failed. Lib_Util_Cache::getInstance is null');
        //    return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        //}

        //$cacheKey = Lib_Util_Conf::CACHEKEY_NEWPUSH_ENABLEFIDS;
        //$cacheType = $cacheKey;

        //将吧id列表添加到cache key中
        //$ret = $objCache->add($cacheKey, serialize($fids), 86400, $cacheType);
        //if ($ret === false) {
        //   Bingo_Log::warning('call service loadTagCacheKeys failed. Lib_Util_Cache::add failed.');
        //   return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        //}
        //

        $errno = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $fids,
        );
    }

    /**
     * @brief
     * @param: $arrInput
     * @return array : $arrOutput
     */
    public static function showTagCacheKeys($arrInput)
    {
        $objCache = Lib_Util_Cache::getInstance();
        if (!$objCache) {
            Bingo_Log::warning('call service showTagCacheKeys failed. Lib_Util_Cache::getInstance is null');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $cacheKey = Lib_Util_Conf::CACHEKEY_NEWPUSH_ENABLEFIDS;
        $cacheType = $cacheKey;

        $ret = $objCache->get($cacheKey, $cacheType);
        if ($ret === false) {
            Bingo_Log::warning('call service showTagCacheKeys failed. Lib_Util_Cache::add failed.');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => unserialize($ret),
        );
    }
}
