<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2016-01-08 16:16:41
 * @version 1.0.0
 */

class Service_Strategy_Ext_WeakBazhuTask extends Service_Strategy_Ext_Base {

    const REDIS_SUBUID_SUFFIX_KEY = '_subjob';//存放用户id的单个resid后缀

    // 测试开关
    // 测试版为1
//    const BZPUSH_SWITCH_CONTROL = 1;
    // 正式版为2
    const BZPUSH_SWITCH_CONTROL= 2;

    private static  $arrBawuRole = array(
        'manager'=>1,
        'assist'=>2,
        'videoadmin'=>3,
        'picadmin'=>4,
        'publicadmin'=>5,
        'publication_editor'=>6,
        'daquan'=>7,
        'daquan_editor'=>8,
        'disk_editor'=>9,
        'top_setter'=>16,
        'post_deleter'=>17,
        'vip'=>18,
        'vip2'=>19,
        'voiceadmin'=>20,
        'fourth_manager'=>21,
        'place_operator'=>22,
        'place_editor'=>23,
        'vertical_operator'=>24,
        'profession_manager'=>25,
        'broadcast_admin'=>26,
        'place_category_editor'=>27,
        'entertainment_manager'=>28,
    );

    /**
     * 弱关系吧主任务推送模式的获取用户ID列表
     * @param $arrInput subJobId
     * @return true 操作成功 反之 false
     */
    public static function getUidArray($arrInput) {
        $intSubJobId = intval($arrInput['subJobId']);
        $strKey = "$intSubJobId" . self::REDIS_SUBUID_SUFFIX_KEY;
        $intStart = intval(self::$_current) * Lib_Util_Conf::FOLLOW_TYPE_NUMBER;
        $intStop = intval(self::$_current) * Lib_Util_Conf::FOLLOW_TYPE_NUMBER + Lib_Util_Conf::FOLLOW_TYPE_NUMBER;


        $intTagKey = $arrInput['tagKey'] % Lib_Util_Conf::BUCKET_NUM;
        Bingo_Timer::start('time_getUidFromBzpushDB');

        // get taskid

        $intTaskId = intval($arrInput['follow_name']);

        $arrTaskInput = array(
            'task_id' => $intTaskId,
        );
        $arrTaskOut = Dl_Strategy_Strategy::getTaskDetailInfo($arrTaskInput);
        if (!$arrTaskOut || !isset($arrTaskOut['errno']) || $arrTaskOut['errno'] != 0){
            Lib_Util_Log::warning("getTaskDetailInfo fail ,input= " . serialize($arrTaskInput) . ", outpu = " . serialize($arrTaskOut));
            return false;
        }

        $arrTaskInfo = $arrTaskOut['output'][0];
        // 等级限制
        $intMinGrade = $arrTaskInfo['minlevel'];
        $intMaxGrade = $arrTaskInfo['maxlevel'];
        $boolAllowAll = $arrTaskInfo['allowall'];

        $arrRoleLimit = array();
        $arrForumidLimit = array();
        $arrDirLevel2Limit = array();
        $arrDirLevel1Limit = array();

        // 吧务角色限制
        if(!$boolAllowAll){
            if($arrTaskInfo['canmaster']){
                $arrRoleLimit[] = self::$arrBawuRole['manager'];
                $arrRoleLimit[] = self::$arrBawuRole['profession_manager'];
                $arrRoleLimit[] = self::$arrBawuRole['fourth_manager'];
            }

            if($arrTaskInfo['canslaver']){
                $arrRoleLimit[] = self::$arrBawuRole['assist'];
            }

            if($arrTaskInfo['caneditor']){
                $arrRoleLimit[] = self::$arrBawuRole['videoadmin'];
                $arrRoleLimit[] = self::$arrBawuRole['picadmin'];
                $arrRoleLimit[] = self::$arrBawuRole['publicadmin'];
                $arrRoleLimit[] = self::$arrBawuRole['publication_editor'];
                $arrRoleLimit[] = self::$arrBawuRole['broadcast_admin'];
                $arrRoleLimit[] = self::$arrBawuRole['voiceadmin'];

            }

            // 吧ID 限制
            $arrForumidOut = Dl_Strategy_Strategy::getTaskLimitId($arrTaskInput);
            if (!$arrForumidOut || !isset($arrForumidOut['errno']) || $arrForumidOut['errno'] != 0){
                Lib_Util_Log::warning("getTaskLimitId fail ,input= " . serialize($arrTaskInput) . ", outpu = " . serialize($arrForumidOut));
                return false;
            }


            foreach ($arrForumidOut['output'] as $value){
                $arrForumidLimit[] = $value['forumid'];
            }

            // 吧目录限制
            $arrFdirOut = Dl_Strategy_Strategy::getTaskLimitDir($arrTaskInput);
            if (!$arrFdirOut || !isset($arrFdirOut['errno']) || $arrFdirOut['errno'] != 0){
                Lib_Util_Log::warning("getTaskLimitId fail ,input= " . serialize($arrTaskInput) . ", outpu = " . serialize($arrFdirOut));
                return false;
            }


            foreach ($arrFdirOut['output'] as $value){
                if($value['level1name'] != ''){
                    $strTem1 = "'".$value['level1name']."'";
                    $arrDirLevel1Limit[] = $strTem1;
                }
                $strTem2 = "'".$value['level2name']."'";
                $arrDirLevel2Limit[] = $strTem2;
            }

        }

        $arrBzpushuserInput = array(
            'last_uid'  => self::$_current,
            'tag_key'   => $intTagKey,
            'allow_all' => $boolAllowAll,
            'role_limit'    => $arrRoleLimit,
            'forumid_limit' => $arrForumidLimit,
            'dir_level2_limit'     => $arrDirLevel2Limit,
            'dir_level1_limit'     => $arrDirLevel1Limit,
            'min_grade'     => $intMinGrade,
            'max_grade'     => $intMaxGrade,
        );

        $res = Dl_Strategy_Strategy::getBzpushuser($arrBzpushuserInput);
        Bingo_Timer::end('time_getBzpushuser');
        if($res['errno'] == 0) {
            // 处理用户ID的数组
            $arrUid = $res['output'];
            Bingo_Log::warning("bzpush_task_uid:".serialize($arrUid));
            foreach ($arrUid as $value){
                self::$_uid_array[] = $value['user_id'];
            }
            $count = count(self::$_uid_array);

            // 如果获取的用户数量小于一次拉取的用户数量，标识subJob已经为最后一次，反之最后的用户ID作为当前subJob的进度
            if($count < Lib_Util_Conf::FOLLOW_TYPE_NUMBER) {
                self::$_current = -1;
            }else{
                self::$_current = $arrUid[$count-1];
            }
            unset($res);
            return true;
        } else {
            Bingo_Log::warning("DB query failed,output:".serialize($res));
            return false;
        }
    }

    /**
     * @param $arrInput 无意义
     * @return true 操作成功 反之 false
     */
    public static function getInfoByUidArray($arrInput) {
        if (count(self::$_uid_array) <= 0){
            return true;
        }
        Bingo_Timer::start('time_mgetCidsByUids');
        $arrInput = array(
            'user_ids'     => self::$_uid_array,
        );
        $arrOutput = Tieba_Service::call('impusher', 'mgetCidsByUids', $arrInput);
        Bingo_Log::warning("bzpush_task_get_CUID:".serialize($arrOutput['data']));
        Bingo_Timer::end('time_mgetCidsByUids');
        if (isset($arrOutput['errno']) && $arrOutput['errno'] == Tieba_Errcode::ERR_SUCCESS && isset($arrOutput['data'])) {
            self::_filterClientyType($arrOutput['data']);             
        } else {
            Lib_Util_Log::warning("call inpusher mgetCidsByUids  fail , input = " . serialize($arrInput) . ", output = " . serialize($arrOutput));
            return false;
        }
        return true;
    }
    
    /**
     * @param
     * @return
     * */
    protected static function _filterClientyType($arrInput) {
        self::$_uid_array = array();
        self::$_filter_array = array();
        
        Bingo_Timer::start('time_filterClientType');
        foreach ($arrInput as $intUserId => $arrDeviceList) {
            $count = count($arrDeviceList);
            if ($count > 10) {
                $arrUpdateTime = array();
                foreach ($arrDeviceList as $arrDevice) {
                    $arrUpdateTime[] = $arrDevice['update_time'];

                }
                array_multisort($arrUpdateTime, $arrDeviceList);
                $arrDeviceList = array_slice($arrDeviceList, $count - 10);
                $count = 10;
                Lib_Util_Log::warning("filterDeviceByVersion device count too more,user_id=$intUserId" . serialize($arrDeviceList));
            }
            foreach ($arrDeviceList as $arrDevice) {
                if ($arrDevice['client_type'] == 101) {
                    self::$_user_num ++;
                    self::$_ios_device_num ++;
                    self::$_uid_array[$intUserId]['cuid'][] = $arrDevice['cuid'];
                } else if ($arrDevice['client_type'] == 102) {
                    self::$_user_num ++;
                    self::$_android_device_num ++;
                    self::$_uid_array[$intUserId]['cuid'][] = $arrDevice['cuid'];
                }
            }
        }
        Bingo_Timer::end('time_filterClientType');
    }


    /**
     * @param
     * @return
     * */
    public static function storeUserMsgList($arrInput) {
        Bingo_Log::warning("bzpush_task_store_message:".serialize(self::$_uid_array));
        if (self::BZPUSH_SWITCH_CONTROL == 1){
            self::$_uid_array = array();
        }

        $arrUid = array_keys(self::$_uid_array);
        if (count($arrUid) <= 0) {
            return true;
        }
        Bingo_Timer::start('time_storeBZUserMsgList');
        $arrReq = array(
            'jobid'         => floor($arrInput['subJobId'] / Lib_Util_Conf::BUCKET_NUM),
            'follow_type'   => $arrInput['followType'],
            'from_uid'      => $arrInput['followId'],
            'to_uids'       => array_keys(self::$_uid_array),
            'mid'           => $arrInput['mid'],
        );

        $arrRes = Service_Msgcenter_Msgcenter::storeBZUserMsgList($arrReq);
        Bingo_Timer::end('time_storeBZUserMsgList');
        if (!$arrRes || !isset($arrRes['errno']) || $arrRes['errno'] != 0){
            Lib_Util_Log::warning("call  Service_Msgcenter_Msgcenter::storeBZUserMsgList  fail ,input= " . serialize($arrReq) . ", outpu = " . serialize($arrRes));
            return false;
        }
        $intFollowType = intval($arrInput['followType']);
        $arrSid = $arrRes['data'];
        foreach(self::$_uid_array as $intUid => $arrCuid) {
            if (isset($arrSid[$intUid])) {
                self::$_uid_array[$intUid]['sid'] = $arrSid[$intUid];
                self::$_uid_array[$intUid]['follow_type'] = $intFollowType;
            } 
        }
        return true;
    }
}
