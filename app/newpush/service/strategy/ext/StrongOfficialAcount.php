<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file StrongOfficialAcount.php
 * <AUTHOR>
 * @date 2016年4月9日
 * @brief 
 *
 **/
class Service_Strategy_Ext_StrongOfficialAcount extends Service_Strategy_Ext_Base {

    /**
     * 强关联推送模式的获取用户ID列表
     * @param $arrInput 必须携带tarKey和Start
     * @return true 操作成功 反之 false
     */
    public static function getUidArray($arrInput) {
        $arrInput['arrFields'] = array(
            'uid',
        );
        $arrInput['conditions'] = "`tag_key`=" . $arrInput['tagKey'] . " and `uid`>" . self::$_current . " and `status`=0 order by `uid` asc limit " . Lib_Util_Conf::FOLLOW_TYPE_NUMBER;
        $res = Dl_Strategy_Strategy::getUidByTagKey($arrInput);
        if (isset($res['errno']) && $res['errno'] == 0) {
            // 处理用户ID的数组
            $count = count($res['output']);
            for ($i = 0; $i< $count; $i++) {
                self::$_uid_array[] = $res['output'][$i]['uid'];
            }
            // 如果获取的用户数量小于一次拉取的用户数量，标识subJob已经为最后一次，反之最后的用户ID作为当前subJob的进度
            if ($count < Lib_Util_Conf::FOLLOW_TYPE_NUMBER) {
                self::$_current = -1;
            } else {
                self::$_current = $res['output'][Lib_Util_Conf::FOLLOW_TYPE_NUMBER -1]['uid'];
            }
            self::filterUserByMask($arrInput);
            unset($res);
            return true;
        } else {
            return false;
        }
    }

    /**
     * 强关联推送模式 获取群组信息和设备信息
     * @param $arrInput 必须携带followId和followType
     * @return true 操作成功 反之 false
     */
    public static function getInfoByUidArray($arrInput) {
        // 获取群组信息
        $arrInput['arrFields'] = array(
            'group_id',
            'group_name',
            'group_type',
        );
        $arrInput['conditions'] = "`tag_id`=" . intval($arrInput['tagKey'] / Lib_Util_Conf::BUCKET_NUM);
        $res = Dl_Strategy_Strategy::getGroupInfoByFollow($arrInput);
        if(isset($res['errno']) && $res['errno'] == 0 && isset($res['output'][0])) {
            $group_info = $res['output'][0];
            unset($res);
        } else {
            return false;
        }
        // 将group信息格式化
        $group_info['gid'] = $group_info['group_id'];
        unset($group_info['group_id']);
        $group_info['gname'] = $group_info['group_name'];
        unset($group_info['group_name']);
        $group_info['gtype'] = $group_info['group_type'];
        unset($group_info['group_type']);
        $group_info['follow_id'] =  $arrInput['followId'];
        $input['user_ids'] = self::$_uid_array;
        if (!empty($input['user_ids'])) {
            // 获取设备信息
            $devices = Lib_Util_Service::call('impusher', 'mgetCidsByUids', $input, 'remote');
            if (isset($devices['errno']) && $devices['errno'] == 0 && isset($devices['data'])) {
                if(in_array($arrInput['followId'],Lib_Util_Conf::$STRESS_TEST_MOCK_FORUM)){
                    Lib_Util_Log::debug("call impush mgetCidsByUids success " . serialize($devices).' --req-- '.serialize($input));
                }
                self::filterDeviceByVersion($devices['data'], $group_info);
            }else {
                Lib_Util_Log::warning("call impusher::mgetCidsByUids fail!" . serialize($devices).' --req-- '.serialize($input));
                return false;
            }
        }
        return true;
    }

    /**
     * 过滤设备和拼装返回数据使用
     * @param $deviceArray
     * @param array $group_info
     */
    public static function filterDeviceByVersion($deviceArray, $group_info = array()) {
        self::$_uid_array = array();
        self::$_filter_array = array();

        // 遍历取到的所有设备，进行策略过滤
        $expiredTime = time() - 3 * 87600;
        foreach($deviceArray as $user_id => $device_info_array) {

            //过滤设备类型，只推送客户端设备以及最近三天内登录过的设备
            $new_device_info_array = array();
            foreach ($device_info_array as $device_info) {
                if ($device_info['client_type'] == 1 || $device_info['client_type'] == 2) {
                    if ($device_info['update_time'] > $expiredTime) {
                        $new_device_info_array[] = $device_info;
                    } 
                }
            }
            $device_info_array = $new_device_info_array;

            $count = count($device_info_array);

            //防止单个uid对应的设备数量过多
            if ($count > 2) {
                //先根据update_time进行排序，然后取最后10条数据
                $arrUpdateTime = array();
                foreach ($device_info_array as $arrDevice) {
                    $arrUpdateTime[] = $arrDevice['update_time'];
                }

                //根据update_time排序device_info_array
                array_multisort($arrUpdateTime, $device_info_array);

                $device_info_array = array_slice($device_info_array, $count - 2);
                $count = 2;
                Lib_Util_Log::warning("filterDeviceByVersion device count too more,user_id=$user_id" . serialize($device_info_array));
            }

            $invaildUidFlag = true;
            for($i = 0; $i < $count; $i++) {
                $device_info = $device_info_array[$i];
                // 判断设备类型及其版本限制
                if ($device_info['client_type'] == 1) {
                    // 判断IOS是否指定的版本之上
                    $invaildUidFlag = false;
                    $compare = Lib_Util_Version::compare($device_info['version'], Lib_Util_Conf::FOLLOW_TYPE_VERSION_IOS_BASE);
                    $compare1 = Lib_Util_Version::compare($device_info['version'], Lib_Util_Conf::FOLLOW_TYPE_VERSION_TIAOMAN_IOS);
                    if ($compare == -1 && $compare1 >= 0) { // version >= 7.4.0
                        self::$_filter_array[$device_info['user_id']]['cuid'][] = $device_info['cuid'];
                    } /* else {
                    self::$_uid_array[$device_info['user_id']]['cuid'][] = $device_info['cuid'];
                    }
                    */
                } else if ($device_info['client_type'] == 2) {
                    // 安卓不推。
                    /*
                    // 判断ANDROID是否指定的版本之上
                    $invaildUidFlag = false;
                    $compare = Lib_Util_Version::compare($device_info['version'], Lib_Util_Conf::FOLLOW_TYPE_VERSION_ANDROID_BASE);
                    if ($compare == -1) {
                    self::$_filter_array[$device_info['user_id']]['cuid'][] = $device_info['cuid'];
                    } else {
                    self::$_uid_array[$device_info['user_id']]['cuid'][] = $device_info['cuid'];
                    }
                    */
                } else {
                    self::$_invaild_device_num++;
                }
            }
            if ($invaildUidFlag) {
                self::$_invaild_uid_num++;
            }
        }
        // 如果用户存在一个可以直推的设备，其他不能直推的设备也不走老推送模式。同时如果group_info有值，将群组信息加入到每个用户当中，适用于强关系推送。
        foreach ( self::$_uid_array as $user_id => $user_info ) {
            // unset(self::$_filter_array[$user_id]);
            if ($group_info) {
                // 强关系群组信息获取方式
                self::$_uid_array[$user_id]['group'] = $group_info;
            } else {
                // 弱关系群组信息获取方式（包括广播和组播）
                if (isset(self::$_group_array[$user_id])) {
                    self::$_uid_array[$user_id]['group'] = self::$_group_array[$user_id];
                } else {
                    unset(self::$_uid_array[$user_id]);
                    continue;
                }
            }
        }
        // 对走老推送的用户也添加群组信息
        foreach ( self::$_filter_array as $user_id => $user_info ) {
            if ($group_info) {
                // 强关系群组信息获取方式
                self::$_filter_array[$user_id]['group'] = $group_info;
            } else {
                // 弱关系群组信息获取方式（包括广播和组播）
                if (isset(self::$_group_array[$user_id])) {
                    self::$_filter_array[$user_id]['group'] = self::$_group_array[$user_id];
                } else {
                    unset(self::$_filter_array[$user_id]);
                    continue;
                }
            }
        }

        // 统计数据的逻辑
        foreach ( self::$_uid_array as $user_id => $user_info ) {
            self::$_user_num++;
            foreach ( $user_info['cuid'] as $cuid ) {
                if (strpos($cuid, "|") === false) {
                    self::$_ios_device_num++;
                } else {
                    self::$_android_device_num++;
                }
            }
        }
        foreach ( self::$_filter_array as $user_id => $user_info ) {
            self::$_old_user_num++;
            foreach ( $user_info['cuid'] as $cuid ) {
                if (strpos($cuid, "|") === false) {
                    self::$_old_ios_device_num++;
                } else {
                    self::$_old_android_device_num++;
                }
            }
        }
    }
}
