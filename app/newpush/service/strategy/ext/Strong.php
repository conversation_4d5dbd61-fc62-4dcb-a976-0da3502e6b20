<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-11-09 16:16:41
 * @version 1.0.0
 */

class Service_Strategy_Ext_Strong extends Service_Strategy_Ext_Base {

    /**
     * 强关联推送模式的获取用户ID列表
     * @param $arrInput 必须携带tarKey和Start
     * @return true 操作成功 反之 false
     */
    public static function getUidArray($arrInput) {
        $arrInput['arrFields'] = array(
            'uid',
        );
        $arrInput['conditions'] = "`tag_key`=" . $arrInput['tagKey'] . " and `uid`>" . self::$_current . " and `status`=0 order by `uid` asc limit " . Lib_Util_Conf::FOLLOW_TYPE_NUMBER;
        $res = Dl_Strategy_Strategy::getUidByTagKey($arrInput);
        if (isset($res['errno']) && $res['errno'] == 0) {
            // 处理用户ID的数组
            $count = count($res['output']);
            for ($i = 0; $i< $count; $i++) {
                self::$_uid_array[] = $res['output'][$i]['uid'];
            }
            // 如果获取的用户数量小于一次拉取的用户数量，标识subJob已经为最后一次，反之最后的用户ID作为当前subJob的进度
            if ($count < Lib_Util_Conf::FOLLOW_TYPE_NUMBER) {
                self::$_current = -1;
            } else {
                self::$_current = $res['output'][Lib_Util_Conf::FOLLOW_TYPE_NUMBER -1]['uid'];
            }
            self::filterUserByMask($arrInput);
            unset($res);
            return true;
        } else {
            return false;
        }
    }

    /**
     * 强关联推送模式 获取群组信息和设备信息
     * @param $arrInput 必须携带followId和followType
     * @return true 操作成功 反之 false
     */
    public static function getInfoByUidArray($arrInput) {
        // 获取群组信息
        $arrInput['arrFields'] = array(
            'group_id',
            'group_name',
            'group_type',
        );
        $arrInput['conditions'] = "`tag_id`=" . intval($arrInput['tagKey'] / Lib_Util_Conf::BUCKET_NUM);
        $res = Dl_Strategy_Strategy::getGroupInfoByFollow($arrInput);
        if(isset($res['errno']) && $res['errno'] == 0 && isset($res['output'][0])) {
            $group_info = $res['output'][0];
            unset($res);
        } else {
            return false;
        }
        // 将group信息格式化
        $group_info['gid'] = $group_info['group_id'];
        unset($group_info['group_id']);
        $group_info['gname'] = $group_info['group_name'];
        unset($group_info['group_name']);
        $group_info['gtype'] = $group_info['group_type'];
        unset($group_info['group_type']);
        $group_info['follow_id'] =  $arrInput['followId'];
        $input['user_ids'] = self::$_uid_array;
        if (!empty($input['user_ids'])) {
            // 获取设备信息
//            $devices = Lib_Util_Service::innerCall('impusher', 'mgetCidsByUids',$input);
            $devices = Tieba_Service::call('impusher', 'mgetCidsByUids', $input, null, null, 'post', 'php', 'utf-8');

//            Bingo_Log::warning("#####liukaining_mget :".serialize($devices)."#########");
            //-------加入测试数据--------
            /*
            $devices['data']['123'][] = array(
                'user_id' => 123,
                'cuid' => 'rr5C5058FA63FA5CFBCEEF4855FE0B8AD3|000000000000000|com.baidu.tieba6.9.9',
                'version' => '6.9.9',
                'client_type' => 2,
                'from_id' => 'tieba',
                'update_time' => 1446646365,
            );

            $devices['data']['124'][] = array(
                'user_id' => 124,
                'cuid' => 'eeC5058FA63FA5CFBCEEF4855FE0B8AD3|000000000000000|com.baidu.tieba7.0.0',
                'version' => '6.9.9',
                'client_type' => 20,
                'from_id' => 'tieba',
                'update_time' => 1446646365,
            );

            $devices['data']['125'][] = array(
                'user_id' => 125,
                'cuid' => 'aa5C5058FA63FA5CFBCEEF4855FE0B8AD3|000000000000000|com.baidu.tieba7.0.0',
                'version' => '6.9.9',
                'client_type' => 2,
                'from_id' => 'tieba',
                'update_time' => 1446646365,
            );

            $devices['data']['125'][] = array(
                'user_id' => 125,
                'cuid' => 'bb5C5058FA63FA5CFBCEEF4855FE0B8AD3|000000000000000|com.baidu.tieba7.0.0',
                'version' => '7.0.0',
                'client_type' => 1,
                'from_id' => 'tieba',
                'update_time' => 1446646365,
            );*/
            //-------------------------
            if (isset($devices['errno']) && $devices['errno'] == 0 && isset($devices['data'])) {
                if(in_array($arrInput['followId'],Lib_Util_Conf::$STRESS_TEST_MOCK_FORUM)){
                    Lib_Util_Log::debug("call impush mgetCidsByUids success " . serialize($devices).' --req-- '.serialize($input));
                }
                self::filterDeviceByVersion($devices['data'], $group_info);
            }else {
                Lib_Util_Log::warning("call impusher::mgetCidsByUids fail!" . serialize($devices).' --req-- '.serialize($input));
                return false;
            }
        }
        return true;
    }
}
