<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-11-09 16:16:41
 * @version 1.0.0
 */

class Service_Strategy_Ext_WeakGroup extends Service_Strategy_Ext_Base {

    /**
     * 检查subJob是否已经完成，并进行进度赋值
     * @param $subJobId
     * @param $tagKey
     * @return bool|true
     */
    public static function isSubJobStart($subJobId, $tagKey) {
        // 弱关系组播不需要检查任务进度
        return true;
    }

    /**
     * 设置subJob当前的进度
     * @param $subJobId
     * @param $tagKey
     * @param $current
     * @return bool
     */
    public static function setSubJobTag($subJobId, $tagKey, $current) {
        // 弱关系组播不需要检查任务进度
        return true;
    }

    /**
     * 弱关联组播推送模式的获取用户ID列表
     * @param $arrInput 必须携带tarKey
     * @return true 操作成功 反之 false
     */
    public static function getUidArray($arrInput) {
        // 从redis中获取的用户ID
        self::$_redis = Lib_Util_Redis::initCache();
        $input = array(
            'key' => $arrInput['tagKey'],
            'count' => Lib_Util_Conf::FOLLOW_TYPE_NUMBER,
        );
        $result = self::$_redis->SRANDMEMBER($input);
        if($result['err_no'] === 0 && isset($result['ret'][$arrInput['tagKey']])) {
            self::$_uid_array = $result['ret'][$arrInput['tagKey']];
        }else{
            Lib_Util_Log::warning("getUidArray redis SRANDMEMBER failed input: " . serialize($input) . ".result : " . serialize($result));
            return false;
        }
        // 获取用户群组信息
        $arrInput['arrFields'] = array(
            'Uid',
            'PushGroupInfo',
        );
        $id_condition = implode(",", self::$_uid_array );
        $arrInput['conditions'] = "`TagKey`='" . $arrInput['tagKey'] . "' and `Uid` in(" . $id_condition . ")";
        $res = Dl_Strategy_Strategy::getUidByTarKey($arrInput);
        if($res['errno'] == 0) {
            // 处理用户ID的数组
            $count = count($res['output']);
            for($i=0; $i< $count; $i++) {
                $uid = $res['output'][$i]['Uid'];
                $group = explode(":", $res['output'][$i]['PushGroupInfo']);
                // 处理每个UID的群组信息
                if(is_array($group) && count($group) == 3) {
                    self::$_group_array[$uid] = array(
                        'gid' => $group[0],
                        'gname' => $group[1],
                        'gtype' => $group[2],
                    );
                }else {
                    Lib_Util_Log::warning("uid " . $uid . " group info Incorrect! ". serialize($res['output'][$i]));
                    continue;
                }
            }
        }else {
            return false;
        }
        // 删除获取到的uid从redis中
        $input = array(
            'key' => $arrInput['tagKey'],
            'member' => self::$_uid_array,
        );
        $result = self::$_redis->SREM($input);
        if($result['err_no'] === 0 && isset($result['ret'][$arrInput['tagKey']])) {
            $delete_num = $result['ret'][$arrInput['tagKey']];
            if($delete_num != count(self::$_uid_array)) {
                Lib_Util_Log::warning("delete uid number no equal ! get:" . count(self::$_uid_array) . " delete:" . $delete_num);
            }
        }else{
            Lib_Util_Log::warning("getUidArray redis SREM failed input: " . serialize($input) . ".result : " . serialize($result));
            return false;
        }
        return true;
    }

    /**
     * 弱关系组播推送模式 获取群组信息和设备信息
     * @param $arrInput 无意义了
     * @return true 操作成功 反之 false
     */
    public static function getInfoByUidArray($arrInput) {
        // 获取设备信息
        $devices = Lib_Util_Service::innerCall('msgpush', 'mgetCuidsByUids',self::$_uid_array);
        if ($devices['errno'] == 0 && isset($devices['users'])) {
            self::filterDeviceByVersion($devices['users']);
        }else {
            Lib_Util_Log::warning("call msgpush::mgetCuidsByUids fail!" . serialize($devices));
            return false;
        }
        return true;
    }

}