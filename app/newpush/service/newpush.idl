//@description: 发送push消息
//@author: <EMAIL>
struct out_info
{
uint64_t thread_id[]; //贴子id，[]表示数组
string forum_name = optional(); //吧名，optional表示可选
};
service newpush
{
/**
* @brief : 示例接口（强烈建议将service test中的test改为模块名）
* @param [in] user_id : uint32_t : 用户id（输入之一）
* @param [in] offset : uint32_t : 偏移量（输入之一）
* @param [out] data[] : out_info : 输出信息，是一个结构体数组
**/
void pushMsg(uint32_t user_id, uint32_t offset, out out_info data[]);
/**
* @brief : 示例接口（强烈建议将service test中的test改为模块名）
* @param [in] user_id : uint32_t : 用户id（输入之一）
* @param [in] offset : uint32_t : 偏移量（输入之一）
* @param [out] data[] : out_info : 输出信息，是一个结构体数组
**/
void getMsg(uint32_t user_id, uint32_t offset, out out_info data[]);
};


