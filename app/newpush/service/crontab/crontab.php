<?php
/**
 * @Author: heqingming
 * @Created Time: 2015-11-05 21:44:00
 * @File Name: PreparePushData.php
 * @Description: 准备推送数据
 *
 */

define ('MODULE', 'Crontab_service');

class Service_Crontab_Crontab {

	const JOB_STATUS_NEW 			  = 1;  //新建：新建任务暂未提交，可再次编辑内容
	const JOB_STATUS_RJECTED 		  = 2;  //审核拒绝：初审终审拒绝均更新为该状态
	const JOB_STATUS_WAIT_READY 	  = 3;  //等待用户数据准备中：新建者编辑完成后提交任务进入该状态，如果需要审核则等待审核，否则在到发布时间时进入到发布状态
	const JOB_STATUS_READY 			  = 4;  //数据已准备好状态
	const JOB_STATUS_WAIT_FIRST_AUDIT = 5;  //等待初审中
	const JOB_STATUS_WAIT_FINAL_AUDIT = 6;  //初审通过,等待终审
	const JOB_STATUS_WAIT_PUBLIC 	  = 7;  //终审通过，待发布状态
	const JOB_STATUS_PUBLICED 		  = 8;  //已发布：在到达发布时间时转入该状态
	const JOB_STATUS_PUSH_START 	  = 9;  //推送开始：到达推送时间时转入该状态
	const JOB_STATUS_PUSH_END 		  = 10; //推送结束：任务完成
	const JOB_STATUS_COMPLEMENTLY 	  = 11; //任务结束，在所有子任务完成时更新至该状态代表彻底结束

    const SUB_JOB_COUNT = 1000;
    const RETRY_COUNT = 3;

    /**
     * @brief 准备推送数据
     * @param null
     * @return null
     */
    public static function preparePushData($arrInput) {

        $arrReq = array(
			'pn' => 1,
            'rn'  => 1,
        );
        
        ral_set_idc('nj');
        $arrRes = Lib_Util_Service::innerCall('newpush', 'getStartPushJob', $arrReq);
        if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']) {
            Bingo_Log::warning('fail to getStartPushJob! req: ' . serialize($arrReq) . ', res: ' . serialize($arrRes));
            exit;
        }

        if (isset($arrRes['data']) && is_array($arrRes['data'])) {

            ral_set_idc('jx');
            $arrJobList = $arrRes['data'];
            foreach ($arrJobList as $jobInfo) {
                $tagId      = $jobInfo['tag_id'];
                $jobId      = $jobInfo['job_id'];
                $followId   = $jobInfo['follow_id'];
                $followType = $jobInfo['follow_type'];
                $baseTagKey = $tagId * self::SUB_JOB_COUNT;
                $baseJobId  = $jobId * self::SUB_JOB_COUNT;

                for ($i = 0; $i < self::SUB_JOB_COUNT; $i++) {
                    $arrParams = array(
                        'subJobId' => $baseJobId + $i,
                        'tagKey' => $baseTagKey + $i,
                        'followId' => $followType,
                        'followType' => $followType,
                        '_partition_key' => $i,
                    );
                    // store in NMQ
                    $res = self::commitNmq('newpush', 'callStrategy', $arrParams);
                    if ($i == 0) {
                        Bingo_Log::warning("commitNmq! newpush:callStrategy, params: " .  serialize($arrParams) . ", res: " . serialize($res));
                    }
                }
            }
        }
    }

    /**
     * @brief 推送子任务
     * @param null
     * @return null
     */
    public static function pushSubJob($arrInput) {

        Bingo_Timer::start('getSubJobByQueue');
        ral_set_idc('nj');
        $arrRes = Lib_Util_Service::innerCall('newpush', 'getSubJobByQueue', array());
        Bingo_Timer::end('getSubJobByQueue');
        if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']) {
            Bingo_Log::warning('fail to getSubJobByQueue! res: ' . serialize($arrRes));
            Bingo_Log::notice(Bingo_Timer::toString());
            exit;
        }

        Bingo_Timer::start('pushMsgCenter');
        if (isset($arrRes['output']) && is_array($arrRes['output'])) {
            ral_set_idc('jx');
            foreach ($arrRes['output'] as $subJobId) {
                $arrParams = array(
                    'subJobId' => $subJobId,
                    '_partition_key' => $subJobId,
                );
                $res = self::commitNmq('newpush', 'pushMsgCenter', $arrParams);
                if ($subJobId % 1000 == 212) {
                    Bingo_Log::warning("commitNmq! newpush:pushMsgCenter, params: " .  serialize($arrParams) . ", res: " . serialize($res));
                }
            }
        }
        Bingo_Timer::end('pushMsgCenter');
        Bingo_Log::notice(Bingo_Timer::toString());
    }

    /**
     * @brief: 带重试功能的nmq提交
     * @param:
     *      $topic     : topic
     *      $cmd       : cmd
     *      $arrParams : 参数
     *      $retryCount: 重试次数
     * @return: null
     */
    private static function commitNmq($topic, $cmd, $arrParams, $retryCount=3) {
        if ($retryCount > 0) {
            $arrRes = Tieba_Commit::commit($topic, $cmd, $arrParams);
            if ($arrRes === false || 0 != $arrRes['errno']) {
                Bingo_Log::warning("fail to commitNmq $retryCount! topic: $topic, cmd: $cmd, params: " .  serialize($arrParams) );
                usleep(5000);
                self::commitNmq($topic, $cmd, $arrParams, $retryCount-1);
            } else {
                return $arrRes;
            }
        } else {
            return false;
        }
    }
}
