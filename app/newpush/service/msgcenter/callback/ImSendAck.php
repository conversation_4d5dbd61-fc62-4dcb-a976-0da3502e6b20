<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015/09/17 14:58:22
 * @version
 */


// 在nmq之后，通过回调推送responce数据包
class Service_Msgcenter_Callback_ImSendAck {
     //客户端老版本兼容
    const PROTO_VERSION_OLD = '1.0.0';
    private static function _buildAckPack($cmd, $recordId, $errno, $response, $arrData) {
        $arrPack = array();
        $pversion = "";
        if (is_array($arrData)) {
            $pversion = strval($arrData['pversion']);
        }

        if (empty($pversion) || $pversion < self::PROTO_VERSION_OLD) {
            // 新版本不需要这些字段, 兼容老版本客户端
            $arrPack['is_ack']  = "1";
            $arrPack['cmd']     = strval($cmd);
            $arrPack['msg_tag'] = array(
                'sequence' => strval($arrData['seq_id']),
            );

            $arrPack['error']  = array(
                'errno'  => strval($errno),
                'errmsg' => strval(Tieba_Error::getErrmsg($errno)),
                'usermsg'=> Bingo_Encode::convert(strval(Tieba_Error::getUserMsg($errno)), Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK),
            );
            $arrPack['data'] = array(
                'msg_id'    => strval($response['msg_id']),
                'group_id'  => strval($response['qid']),
                'record_id' => strval($recordId),  //兼容im之前版本
            );
        } else {
            $arrPack['error']  = array(
                'errorno'  => intval($errno),
                'errmsg' => strval(Tieba_Error::getErrmsg($errno)),
                'usermsg'=> Bingo_Encode::convert(strval(Tieba_Error::getUserMsg($errno)), Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK),
            );
            $arrPack['data'] = array(
                'msg_id'    => intval($response['msg_id']),
                'group_id'  => intval($response['qid']),
                'record_id' => intval($recordId),
            );
            if (isset($arrData['toUid'])) {  //私聊ack需要返回toUid
                $arrPack['data']['toUid'] = $arrData['toUid'];
                $arrPack['data']['toUserType'] = $arrData['toUserType'];
            }
        }


        //$arrPack['time'] = strval(Bingo_Timer::getNowTime());
        //$arrPack['logid'] = strval(Bingo_Log::getLogId());
        return $arrPack;
    }


    public static function execute($arrInput, $arrOutput) {
        if (!isset($arrInput['app_id']) || !isset($arrInput['cuid']) || !isset($arrInput['cmd']) || !isset($arrInput['seq_id'])) {
            Bingo_Log::warning("Service_Msgcenter_Callback_ImSendAck input error. [".serialize($arrInput)."]".serialize($arrOutput));
            return false;
        }

        $appId     = intval($arrInput['app_id']);
        $cuid      = strval($arrInput['cuid']);
        $cmd       = intval($arrInput['cmd']);
        $data      = strval($arrInput['data']);
        $arrData   = @json_decode($data,true);
        $result    = $arrOutput['result'];
        $errno     = $arrOutput['errno'];
        $response = $result;
        if (empty($cuid)) {
             Bingo_Log::debug("cuid is empty. needn't to push ack!...........");
            // 不需要反推ack
            return true;
        }
        //Bingo_Log::debug("Service_Msgcenter_Callback_ImSendAck data. [".serialize($arrInput)."]");
        $seqId      = strval($arrData['seq_id']);   //协议层的seqid,用于协议去重和ack判断
        $recordId   = intval($arrInput['seq_id']);  //参数名定错了,其实是recordid
        $arrPack  = self::_buildAckPack($cmd, $recordId, $errno, $response, $arrData);
        $cuidInfo = array(
            'cuid' => $cuid,
            'mask' => 0,
            'mask_apns' => 1,
        );

        $arrReq = array(
            'app_id' => $appId,
            'cuids'  => array($cuidInfo),
            'cmd'    => $cmd,
            'seq_id' => $seqId,
            'content'    => Lib_Util_Format::changeKeyStyle($arrPack),
            'abstract'   => '',
            //'login_flag' => 0,
            //'push_type'  => 0,
            //'fast_mode'  => 0,
        );
        // TODO 联调
        $res = Tieba_Service::call("msgpush", "pushMsg", $arrReq,NULL,NULL,'post','php','utf-8');
        //Bingo_Log::debug("TTTTTTTTTTTT Service_Msgcenter_Callback_ImSendAck call msgpush::pushMsg!. [".serialize($arrData)."]" . serialize($arrReq));
        if (isset($res['errno']) &&  $res['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("Service_Msgcenter_Callback_ImSendAck call msgpush::pushMsg fail!. [".serialize($res)."]" . serialize($arrReq));
            return false;
        }
        if ($arrInput['_commit_time']) {
            Bingo_Log::pushNotice('optime', $arrInput['_commit_time'].'.000');
        }

        Bingo_Log::debug("Service_Msgcenter_Callback_ImSendAck . [".serialize($arrInput)."]=========".serialize($res));

        return true;
    }

}









