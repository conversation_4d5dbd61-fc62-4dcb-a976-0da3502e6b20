# 日志级别
# 1：打印FATAL
# 2：打印FATAL和WARNING
# 4：打印FATAL、WARNING、NOTICE（线上程序正常运行时的配置）
# 8：打印FATAL、WARNING、NOTICE、TRACE（线上程序异常时使用该配置）
# 16：打印FATAL、WARNING、NOTICE、TRACE、DEBUG（测试环境配置）
log_level: 4

#im回调 可拆分产品线部署 
[app_callback_1]
[.before_send]

[.after_pre_store]
@cmd_202001: ImSendAck
@cmd_205001: ImSendAck

[.after_store]
@cmd_202001: ImSendNotify
@cmd_202007: ImSendNotify
@cmd_205001: ImSendNotify
@cmd_207001: ImSendNotify
@cmd_208001: ImSendNotify
@cmd_301001: ImSendNotify
@cmd_205006: ImSendNotify