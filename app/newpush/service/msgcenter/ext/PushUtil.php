<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015:05:14 16:16:41
 * @version
 * @structs & methods(copied from idl.)
 */

class Service_Msgcenter_Ext_PushUtil
{
    const PREFIX_JOB_EXIST = 'job_exist:';
    const PROTO_VERSION_OLD = '1.0.0';
    const NEWPUSH_MULTI_KEY = 'NEWPUSH_MULTI_KEY';

    const RECORD_KEY_SERVICE_CALL_FAILED = 'service_fail'; //数据统计Key 服务调用失败
    const RECORD_KEY_APNS_SUC = 'apns_suc';//数据统计Key APNS推送成功
    const RECORD_KEY_APNS_FAILED = 'apns_failed';//数据统计Key APNS推送成功
    const RECORD_KEY_AND_LCS_SUC = 'lcs_and_suc';//数据统计Key LCS ios设备推送成功
    const RECORD_KEY_IOS_LCS_SUC = 'lcs_ios_suc';//数据统计Key LCS android 设备推送成功
    const RECORD_KEY_TOTAL_LCS_SUC = 'lcs_total_suc';//数据统计Key LCS ios设备推送成功
    const RECORD_KEY_LCS_OFFLINE = 'lcs_offline';//数据统计Key LCS android 设备推送成功
    const RECORD_KEY_PUSH_FAILED = 'push_failed';//数据统计Key 推送失败
    const RECORD_KEY_GETMSG_SUC = 'get_msg_suc'; //数据统计Key 拉消息成功
    const RECORD_KEY_GETMSG_FAILED = 'get_msg_failed';//数据统计Key 拉消息失败
    const RECORD_KEY_GETMSG_SERV_ERR = 'get_msg_serverr';//数据统计Key 拉消息服务失败次数

    private static $objCache = null;
    private static $_objRalMulti = null;

    /**
     * @param $errno
     * @return array
     */
    public static function _errRet($errno, $data = null)
    {
        $ret = array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );

        if (!is_null($data)) {
            $ret['data'] = $data;
        }

        return $ret;
    }

    /**
     * 获取cache实例
     * @param null
     * @return Lib_Util_Cache|null
     */
    public static function _getCache()
    {
        if (self::$objCache) {
            return self::$objCache;
        }

        Bingo_Timer::start('cacheinit');
        self::$objCache = Lib_Util_Cache::getInstance();
        Bingo_Timer::end('cacheinit');

        if (!self::$objCache) {
            Lib_Util_Log::warning("init cache fail.");
            self::$objCache = null;
            return null;
        }
        return self::$objCache;
    }

    /**
     * 生成消息Id
     * @return int
     * @internal param
     */
    public static function getMidViaNewIdAlloc()
    {
        $arrReq = array(
            'name' => 'message_id',
            'random' => Lib_Util_Conf::IDALLOC_RANDOMID,
            'id_count' => 1,
        );
        $arrRes = Lib_Util_Idalloc::getNewId($arrReq);
        if ($arrRes['errno'] !== 0) {
            Lib_Util_Log::warning('ControlCenter::preSendMsg newLogic_0 Idalloc failed. ' . serialize($arrRes));
            return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
        }
        $arrMsgIds = $arrRes['ids'];

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'msg_ids' => $arrMsgIds,
        );
        return $arrOutput;
    }

    /**
     * @param $arrUserlists
     * @return array
     */
    public static function fetchCuidsFromUserInfo($arrUserlists)
    {
        if (!is_array($arrUserlists) || count($arrUserlists) == 0) {
            Bingo_Log::warning('input params invalid. [' . serialize($arrUserlists) . ']');
            return false;
        }

        $arrTOCuids = array();
        foreach ($arrUserlists as $uid => $cuidsInfo) {
            if (isset($cuidsInfo['cuid']) && is_array($cuidsInfo['cuid']) && count($cuidsInfo['cuid']) > 0) {
                $arrTOCuids = array_merge($arrTOCuids, $cuidsInfo['cuid']);
            }
        }

        return $arrTOCuids;
    }

    /**
     * @param $arrUserlists
     * @return array
     */
    public static function fetchCuidsForIMPuser($arrUserlists)
    {
        if (!is_array($arrUserlists) || count($arrUserlists) == 0) {
            Bingo_Log::warning('input params invalid. [' . serialize($arrUserlists) . ']');
            return false;
        }

        $arrTOCuids = array();
        foreach ($arrUserlists as $uid => $cuidsInfo) {
            $index = 0;
            if (isset($cuidsInfo['cuid']) && is_array($cuidsInfo['cuid']) && count($cuidsInfo['cuid']) > 0) {
                foreach ($cuidsInfo['cuid'] as $key => $value) {
                    $index++;
                    $tempKey = $uid . '_' . $index;
                    $arrTOCuids[$tempKey] = array(
                        'cuid' => $value,
                    );
                }
            }
        }
        
        return $arrTOCuids;
    }

    /**
 * @param $msg
 * @param $ext
 * @param int $badge
 * @param string $sound
 * @return string
 */
    public static function buildApnsMsg($msg, $ext, $badge = 1, $sound = 'default')
    {
        if (empty($msg)) {
            $msg = "您有一条新的推送";
        }
        $apnsMsg = self::getApnsContent($msg);
        $strExt = json_encode(Lib_Util_Util::change_key_style($ext));
        if ($strExt == null || $strExt == 'null') {
            $strExt = "{}";
            Lib_Util_Log::warning("apnsMsg send ext null");
        }
        return '{"aps":{"alert":"' . $apnsMsg . '","badge":' . $badge . ',"sound":"' . $sound . '"},"ext":' . $strExt . '}';
    }

    /**
     * @param $msg
     * @param $ext
     * @param int $badge
     * @param string $sound
     * @return string
     */
    public static function buildApnsBzMsg($msg, $ext, $badge = 1, $sound = 'default')
    {
        if (empty($msg)) {
            $msg = "您有一条新的推送";
        }
//        $apnsMsg = self::getApnsContent($msg);

        $strContent = $msg['content'];
        $arrTemCon = json_decode($strContent,true);
        $apnsMsg = Lib_Util_Format::urldecodeArray($arrTemCon);

        // 唯一支持的 TYPE
        $intAllowType = 3;
        $strExt = json_encode(Lib_Util_Util::change_key_style($ext));
        if ($strExt == null || $strExt == 'null') {
            $strExt = "{}";
            Lib_Util_Log::warning("apnsMsg send ext null");
        }
        $arrBazhu = array(
            'type' => $intAllowType,
            'data_id' => $msg['taskId'],
            'sid' => $msg['sid'],
        );

        $arrOutContent = array(
            "aps" => array(
                "alert" => array(
                    "title" => $apnsMsg[0]['title'],
                    "body" => $apnsMsg[0]['desc'],
                ),
                "badge" => $badge,
                "sound" => $sound,
            ),
            "ext" => $strExt,
            "bazhu" => $arrBazhu,

        );

        $strOutContent = json_encode($arrOutContent);
        return $strOutContent;
    }

    /**
     * @param $arrInput
     * @return string
     */
    private static function getApnsContent($arrInput)
    {
        $prefix = '';
        $suffix = '';
        $apnsContent = '你有一条新消息';
        $userId = '';
        $userType = isset($arrInput['userInfo']['userType']) ? intval($arrInput['userInfo']['userType']) : 3;
        $userName = isset($arrInput['userInfo']['userName']) ? strval($arrInput['userInfo']['userName']) : '大贴';
        if ($userType == Lib_Util_Conf::USERTYPE_BROADCAST_FORUM || Lib_Util_Conf::USERTYPE_OFFICIAL_FORUM == $userType) {
            $arrOpflag = unserialize($arrInput['op_flag']);
            $forumName = strval($arrOpflag['user_name']);
            $forumid = $arrOpflag['user_id'];
            if (strlen($forumName) <= 0) {
                $forumName = $userName;
            }
            $prefix = $forumName . "吧";
        }

        $msgType = intval($arrInput['msgType']);
        $content = $arrInput['content'];
        switch ($msgType) {
            case Lib_Util_Conf::MSG_TYPE_MESSAGE: {
                $suffix = self::msubstr($content, Lib_Util_Conf::MAX_APNS_CONTENT_LEN);//substr($content, 0, 60);
                break;
            }
            case Lib_Util_Conf::MSG_TYPE_PICTURE:
            case Lib_Util_Conf::MSG_TYPE_MALL_SMILE: {
                $suffix = "[图片]";
                break;
            }
            case Lib_Util_Conf::MSG_TYPE_VOICE: {
                $suffix = "[语音]";
                break;
            }
            case Lib_Util_Conf::MSG_TYPE_PIC_TEXT: {
                $arrContent = json_decode($content, true);
                if (isset($arrContent[0]['title']) || isset($arrContent[0]['text'])) {
                    if (!isset($arrContent[0]['text']) || empty($arrContent[0]['text'])) {
                        $suffix = self::msubstr($arrContent[0]['title'], Lib_Util_Conf::MAX_APNS_CONTENT_LEN);//substr($arrContent[0]['title'], 0, 60);
                    } else if (!isset($arrContent[0]['title']) || empty($arrContent[0]['title'])) {
                        $suffix = self::msubstr($arrContent[0]['text'], Lib_Util_Conf::MAX_APNS_CONTENT_LEN);
                    } else {
                        $string = $arrContent[0]['title'] . '-' . $arrContent[0]['text'];
                        $suffix = self::msubstr($string, Lib_Util_Conf::MAX_APNS_CONTENT_LEN);
                    }
                } else {
                    return $apnsContent;
                }
                break;
            }
            default: {
                Lib_Util_Log::warning(" msg type is not right. [" . serialize($arrInput) . "]");
                return $apnsContent;
            }
        }
        if (empty($prefix) && empty($suffix)) {
            return $apnsContent;
        }
        $apnsContent = $prefix . ":" . $suffix;
        return $apnsContent;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function buildEnableMsg($arrInput)
    {
        if (!isset($arrInput['content']) || !isset($arrInput['mid'])) {
            Bingo_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $qid = intval($arrInput['gid']);
        $coreMsg = unserialize($arrInput['content']);
        //封装消息内容，构成拉消息及推送消息时，需要的消息格式
        // 由于客户端统计要求，在content中增加msg_src字段
        $tempContent = json_decode($coreMsg['content'], true);
        $finalContent = null;
        if(null != $tempContent){
            if($tempContent['reply'] == 1) {
                $content = $tempContent['content'];
                $finalContent = $content;
            }else{
                foreach ($tempContent as $index => &$content) {
                    $content = Lib_Util_Format::urldecodeArray($content);
                    $content['msg_src'] = '2_' . $coreMsg['taskId'];
                }
                $finalContent = json_encode($tempContent);
            }
        }else{
            $finalContent = $coreMsg['content'];
        }

        $opFlagArray = array(
            'user_type' => $coreMsg['userInfo']['userType'],
            'user_id' => $coreMsg['userInfo']['userId'],
            'user_name' => $coreMsg['userInfo']['userName'],
            'portrait' => $coreMsg['userInfo']['portrait'],
        );
        $opFlag = serialize($opFlagArray);
        $arrRequireMsg = array(
            'qid' => $qid,
            'msgId' => intval($arrInput['mid']),
            'msgType' => intval($arrInput['msg_type']),
            'userId' => intval($arrInput['user_id']),
            'content' => strval($finalContent),
            'userInfo' => $coreMsg['userInfo'],
            'toUserInfo' => $coreMsg['toUserInfo'],
            'taskId' => $coreMsg['taskId'],
            'link' => $coreMsg['link'],
            'sid' => $arrInput['sid'],
            'duration' => intval($arrInput['duration']),
            'createTime' => intval($arrInput['createtime']),
            'status' => intval($arrInput['status']),
            'seq_id' => intval($arrInput['seq_id']),
            'msg_tag' => strval($arrInput['msg_tag']),
            'op_flag' => strval($opFlag),
        );
        /*
        if ($opFlagArray['user_type'] == Lib_Util_Conf::FOLLOW_TYPE_SUBSCRIBE_ACCOUNT && !empty($opFlagArray['user_id'])) {
            $arrRequireMsg['userId'] = $opFlagArray['user_id'];
        }
        */
        if (isset($coreMsg['versionRule'])) {
            $arrRequireMsg['versionRule'] = $coreMsg['versionRule'];
        }
        return $arrRequireMsg;
    }

    /**
     * @param $arrInput
     * @return array
     * @internal param $uid
     * @internal param $objCaller
     */
    public static function clearSubJobQueue($arrInput)
    {
        $redis = Lib_Util_Redis::initCache();
        if (!$redis || is_null($redis)) {
            Bingo_Log::warning('fail to Lib_Util_Redis::initCache');
        }
        $input = array(
            'reqs' => array(
                array(
                    'key' => 'high_push_list',
                ),
                array(
                    'key' => 'low_push_list',
                ),
            ),
        );
        $result = $redis->DEL($input);
        if (!$result || $result['err_no'] != 0) {
            Bingo_Log::warning('fail to DEL, input: ' . serialize($input) . ', result: ' . serialize($result));
        }
        $arrRes = array(
            'errno' => 0,
            'errmsg' => 'success',
        );
        return $arrRes;
    }

    /**
     * @param $cmd
     * @param $recordId
     * @param $errno
     * @param $mid
     * @param $qid
     * @param $arrData
     * @return array
     */
    public static function _buildAckPack($cmd, $recordId, $errno, $mid, $qid, $arrData)
    {
        $arrPack = array();
        $pversion = "";
        if (is_array($arrData)) {
            $pversion = strval($arrData['pversion']);
        }

        if (empty($pversion) || $pversion < self::PROTO_VERSION_OLD) {
            // 新版本不需要这些字段, 兼容老版本客户端
            $arrPack['is_ack'] = "1";
            $arrPack['cmd'] = strval($cmd);
            $arrPack['msg_tag'] = array(
                'sequence' => strval($arrData['seq_id']),
            );

            $arrPack['error'] = array(
                'errno' => strval($errno),
                'errmsg' => strval(Tieba_Error::getErrmsg($errno)),
                'usermsg' => Bingo_Encode::convert(strval(Tieba_Error::getUserMsg($errno)), Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK),
            );
            $arrPack['data'] = array(
                'msg_id' => strval($mid),
                'group_id' => strval($qid),
                'record_id' => strval($recordId),  //兼容im之前版本
            );
        } else {
            $arrPack['error'] = array(
                'errorno' => intval($errno),
                'errmsg' => strval(Tieba_Error::getErrmsg($errno)),
                'usermsg' => Bingo_Encode::convert(strval(Tieba_Error::getUserMsg($errno)), Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK),
            );
            $arrPack['data'] = array(
                'msg_id' => intval($mid),
                'group_id' => intval($qid),
                'record_id' => intval($recordId),
            );
            if (isset($arrData['toUid'])) {  //私聊ack需要返回toUid
                $arrPack['data']['toUid'] = $arrData['toUid'];
                $arrPack['data']['toUserType'] = $arrData['toUserType'];
            }
        }

        return $arrPack;
    }

    /**
     * 将输入的推送消息内容，转换为DB中的存储形态
     * @arrInput:
     *      array content
     *
     * @param $arrInput
     * @return string :
     *
     * @internal param $arrContent
     */
    public static function buildDbStoreMsg($arrInput)
    {
        //DB中存储的必须元素
        $arrRequireFields = array(
            'userInfo' => $arrInput['userInfo'],
            'content' => $arrInput['content'],
            'taskId' => $arrInput['jobid'],
            'link' => $arrInput['link'],
        );
        if (isset($arrInput['toUserInfo'])) {
            $arrRequireFields['toUserInfo'] = $arrInput['toUserInfo'];
        }

        if (isset($arrInput['versionRule'])) {
            $arrRequireFields['versionRule'] = $arrInput['versionRule'];
        }
        
        return serialize($arrRequireFields);
    }

    /**
     * @param $job_id
     * @return bool
     */
    public static function isNeedStore($jobId)
    {
        $redis = Lib_Util_Redis::initCache();
        $ret = true;
        $arrInput = array(
            'key' => self::PREFIX_JOB_EXIST . $jobId,
        );
        $arrRes = $redis->EXISTS($arrInput);
        if (isset($arrRes['err_no']) && $arrRes['err_no'] == Tieba_Errcode::ERR_SUCCESS) {
            if (isset($arrRes['ret'][$arrInput['key']]) && $arrRes['ret'][$arrInput['key']] == 1) {
                $ret = false;
            }
        } else {
            Bingo_Log::warning('msgcenter::isNeedStore EXISTS error. input: ' . serialize($arrInput) . ', res: ' . serialize($arrRes));
        }

        return $ret;
    }

    /**
     * @param $job_id
     * @return bool
     */
    public static function setNeedStore($jobId)
    {
        $redis = Lib_Util_Redis::initCache();
        $arrInput = array(
            'key' => self::PREFIX_JOB_EXIST . $jobId,
            'value' => 1,
            'seconds' => 600,
        );
        for ($i = 0; $i < 3; $i++) {
            $arrRes = $redis->SETEX($arrInput);
            if (isset($arrRes['err_no']) && $arrRes['err_no'] == Tieba_Errcode::ERR_SUCCESS) {
                break;
            } else {
                Bingo_Log::warning("msgcenter::setNeedStore SETEX error $i. input: " . serialize($arrInput) . ", res: " . serialize($arrRes));
            }
        }
    }

    /**
     * @param $arrFollowTypes
     * @param $uid
     * @return array|bool
     */
    public static function getUserFollowIdsByType($arrFollowTypes, $uid)
    {
        self::$_objRalMulti = new Molib_Tieba_Multi(self::NEWPUSH_MULTI_KEY);
        if (self::$_objRalMulti === null) {
            Bingo_Log::warning('initializing ext ral_multi failure');
            return false;
        }
        $objCaller = null;
        foreach ($arrFollowTypes as $followType) {
            if ($followType == 0) {
                // 获取全部follow列表
                if (1 === Lib_Util_Conf::$ENABLE_PREM_QUERYTYPES[Lib_Util_Conf::LIKE_FORUM_QUERY_KEY]) {
                    self::registForumLikeReq($uid, $objCaller);
                }
                if (1 === Lib_Util_Conf::$ENABLE_PREM_QUERYTYPES[Lib_Util_Conf::LIKE_USER_QUERY_KEY]) {
                    self::registUserLikeReq($uid, $objCaller);
                }
                break;
            } elseif (in_array($followType, Lib_Util_Conf::$FORUM_PUSH_FOLLOWTYPE)) {
                // 通过吧关注信息获取推送消息列表
                if (1 === Lib_Util_Conf::$ENABLE_PREM_QUERYTYPES[Lib_Util_Conf::LIKE_FORUM_QUERY_KEY]) {
                    self::registForumLikeReq($uid, $objCaller);
                }
                continue;
            } elseif (in_array($followType, Lib_Util_Conf::$USER_PUSH_FOLLOWTYPE)) {
                // 通过用户关注信息获取推送消息列表
                if (1 === Lib_Util_Conf::$ENABLE_PREM_QUERYTYPES[Lib_Util_Conf::LIKE_USER_QUERY_KEY]) {
                    self::registUserLikeReq($uid, $objCaller);
                }
                continue;
            }
        }

        Bingo_Timer::start('ext_call_multi');
        self::$_objRalMulti->call();
        Bingo_Timer::end('ext_call_multi');

        // get output
        $arrMultiOutput = self::$_objRalMulti->getAllResult();
        $fids = array();
        $starUids = array();
        if ($arrMultiOutput['newpush:getUserFollowedStar'] ['errno'] == Tieba_Errcode::ERR_SUCCESS && count($arrMultiOutput['newpush:getUserFollowedStar']['output']['member_list']) > 0) {
            $fids = $arrMultiOutput['newpush:getUserFollowedStar']['output']['member_list'];
        }

        $finalData = array(
            'fids' => $fids,
            'starUids' => $starUids,
        );
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $finalData,
        );
        return $arrOutput;
    }

    /**
     * @param $uid
     * @param $objCaller
     */
    private static function registForumLikeReq($uid, $objCaller)
    {
        $followForumKey = 'newpush:getUserFollowedStar';
        $getLikeUserInput = array(
            'user_id' => $uid,
            'page_no' => 1,
            'page_size' => 100,
            'check_forum' => 0,
        );
        $arrStarReq = array(
            'serviceName' => 'perm',
            'method' => 'getLikeForumList',
            'input' => $getLikeUserInput,
        );
        self::$_objRalMulti->register($followForumKey, $arrStarReq, $objCaller);
    }

    /**
     * @param $uid
     * @param $objCaller
     */
    private static function registUserLikeReq($uid, $objCaller)
    {

    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function initGroupSid($arrInput)
    {
        // input params check;
        if (!isset($arrInput['gid']) || !isset($arrInput['sid'])) {
            Bingo_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //input params.
        $gid = intval($arrInput['gid']);
        $sid = intval($arrInput['sid']);

        //output params.
        $data = false;
        $arrReq = array(
            'sid' => $sid,
            'gid' => $gid,
        );
        $arrRes = Dl_Newpush_Newpush::setGroupSid($arrReq);
        $data = $arrRes;

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $data);
    }

    /**
     * @brief
     * @param $arrInput
     * @return array : $arrOutput
     */
    public static function processRecordResult($arrRes, $recordCountResult)
    {
        if (empty($recordCountResult)) {
            $recordCountResult['lcs_and_suc_count'] = 0;
            $recordCountResult['lcs_ios_suc_count'] = 0;
            $recordCountResult['lcs_total_suc_count'] = 0;
            $recordCountResult['lcs_total_offline_count'] = 0;
            $recordCountResult['anps_suc_count'] = 0;
            $recordCountResult['anps_fail_count'] = 0;
            $recordCountResult['fail_count'] = 0;
        }

        foreach ($arrRes['res'] as $cuid => $ret) {
            if ($ret) {
                $recordCountResult['lcs_total_suc_count'] ++;
                continue;
            } else {
                $recordCountResult['lcs_total_offline_count'] ++;
                continue;
            }
        }

        foreach ($arrRes['stat'] as $channel => $ret) {
            if ($channel == 'apns') {
                foreach ($ret as $flag => $count) {
                    if ($flag == 'succeed') {
                        $recordCountResult['anps_suc_count'] += $count;
                        continue;
                    } elseif ($flag == 'failed') {
                        $recordCountResult['anps_fail_count'] += $count;
                        continue;
                    }
                }
            } elseif ($channel == 'lcs') {
                foreach ($ret as $flag => $count) {
                    if ($flag == '1') {
                        $recordCountResult['lcs_ios_suc_count'] += $count;
                        continue;
                    } elseif ($flag == '2') {
                        $recordCountResult['lcs_and_suc_count'] += $count;
                        continue;
                    }
                }
            }

            if ($ret) {
//                $lcs_total_suc_count++;
                $recordCountResult['lcs_total_suc_count'] ++;
                continue;
            } else {
                $recordCountResult['lcs_total_offline_count'] ++;
                continue;
            }
        }

        return $recordCountResult;
    }

    /**
     * @param
     * @return
     * */
    public static function arrUrldecode($arrInput) {
        foreach ($arrInput as &$value) {
            $value = urldecode($value);
        }
        unset($value);
        return $arrInput;
    }

    /**
     * 按长度截取字符串
     * @param unknown $str ：待截取的字符串
     * @param unknown $length ：截取的长度
     * @param int|number $start ：开始的位置，默认为0
     * @param string $charset ：字符集
     * @param bool|string $suffix ：是否加后缀
     * @return string ：返回的字符串
     */
    public static function msubstr($str, $length, $start = 0, $charset = "utf-8", $suffix = true)
    {
        if (function_exists("mb_substr")) {
            $slice = mb_substr($str, $start, $length, $charset);
        } elseif (function_exists('iconv_substr')) {
            $slice = iconv_substr($str, $start, $length, $charset);
        } else {
            $re['utf-8'] = "/[\x01-\x7f]|[\xc2-\xdf][\x80-\xbf]|[\xe0-\xef][\x80-\xbf]{2}|[\xf0-\xff][\x80-\xbf]{3}/";
            $re['gb2312'] = "/[\x01-\x7f]|[\xb0-\xf7][\xa0-\xfe]/";
            $re['gbk'] = "/[\x01-\x7f]|[\x81-\xfe][\x40-\xfe]/";
            $re['big5'] = "/[\x01-\x7f]|[\x81-\xfe]([\x40-\x7e]|\xa1-\xfe])/";
            preg_match_all($re[$charset], $str, $match);
            $slice = join("", array_slice($match[0], $start, $length));
        }

        return ($suffix && (mb_strlen($str, $charset) > $length)) ? $slice . '...' : $slice;
    }
}
