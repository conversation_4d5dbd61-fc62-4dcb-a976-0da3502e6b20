<?php
/***************************************************************************
 *
 * Copyright (c) 2010 Baidu.com, Inc. All Rights Reserved
 * $Id$
 *
 **************************************************************************/

class Lib_Util_DB {
    protected $_db = null;
    protected $_conf = null;
    protected $_tpl = null;
    protected static $_arrType = array('query', 'update');

    const CHAR_SET_GBK = 'gbk';
    const CHAR_SET_UTF8 = 'utf8';
    const CONF_COMMAND_TYPE = 'type';
    const CONF_COMMAND_NODE = 'command';
    const CONF_COMMAND_SQL = 'sql';
    const CONF_COMMAND_OUTKEY = 'output_key';
    const DEFAULT_OUTKEY = 'results';

    /**
     * Lib_Util_DB constructor.
     * @param $db
     * @param $conf
     * @param string $charset
     */
    public function __construct($db, $conf, $charset=self::CHAR_SET_UTF8)
    {
        $this->_db = $db;
        $this->_conf = $conf;
        $this->_tpl = new Bd_DB_SQLTemplate($db);

        //define('BINGO_ENCODE_LANG', $charset);
        $this->_db->charset($charset);
    }

    /**
     * @param $strName
     * @return bool
     */
    private function _getConf($strName)
    {
        if (!is_array($this->_conf) || !isset($this->_conf[$strName])) {
            Lib_Util_Log::warning("query function [$strName] not found.");
            return false;
        }

        $arrConf = $this->_conf[$strName];
        $strType = strval($arrConf[self::CONF_COMMAND_TYPE]);
        if (!in_array($strType, self::$_arrType)) {
            Lib_Util_Log::warning("query command type[$strType] for [$strName] invalid.");
            return false;
        }

        if (!isset($arrConf[self::CONF_COMMAND_NODE])) {
            Lib_Util_Log::warning("query command for [$strName] not found.");
            return false;
        }

        return $arrConf;
    }

    /**
     * @param $errno
     * @param array $results
     * @param string $strKey
     * @return array
     */
    private function _getOutArray($errno=Tieba_Errcode::ERR_SUCCESS, $results=array(), $strKey=self::DEFAULT_OUTKEY)
    {
        $arrOut = array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
        if ($errno == Tieba_Errcode::ERR_SUCCESS) {
            $arrOut[$strKey] = $results;
        }
        return $arrOut;
    }

    /**
     * @param $strName
     * @param $arrParams
     * @return array
     */
    public function query($strName, $arrParams)
    {
        if($this->_db == null) {
            Lib_Util_Log::warning('db connection is null');
            return self::_getOutArray(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $arrConf = self::_getConf($strName);
        if ($arrConf === false) {
            Lib_Util_Log::warning("get config for [$strName] failed.");
            return self::_getOutArray(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $strMethod = "_{$arrConf[self::CONF_COMMAND_TYPE]}";
        $arrOut = $this->$strMethod($strName, $arrConf, $arrParams);

        return $arrOut;
    }


    /**
     * @param $strName
     * @param $arrConf
     * @param $arrParams
     * @return array
     */
    private function _query($strName, $arrConf, $arrParams)
    {
        $arrResult = array();
        foreach ($arrConf[self::CONF_COMMAND_NODE] as $value) {
            if (empty($value) || empty($value[self::CONF_COMMAND_SQL])) {
                Lib_Util_Log::warning("has empty command for [$strName], ignore");
                continue;
            }
            $strSql = $value[self::CONF_COMMAND_SQL];
            $arrTmp = array();
            $ret = $this->_queryDB($strSql, $arrParams, $arrTmp);
            if ($ret === false) {
                return self::_getOutArray(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            $arrResult[] = (array)$arrTmp;
        }

        $strOutKey = self::DEFAULT_OUTKEY;
        if (is_string($arrConf[self::CONF_COMMAND_OUTKEY]) && !empty($arrConf[self::CONF_COMMAND_OUTKEY])) {
            $strOutKey = $arrConf[self::CONF_COMMAND_OUTKEY];
        }
        return self::_getOutArray(Tieba_Errcode::ERR_SUCCESS, $arrResult, $strOutKey);
    }


    /**
     * @param $strName
     * @param $arrConf
     * @param $arrParams
     * @return array
     */
    private function _update($strName, $arrConf, $arrParams)
    {
        //事务
        $use_transation = 0;
        if (isset($arrConf['use_transaction']) && $arrConf['use_transaction'] != 0) {
            $use_transation = 1;
            $this->startTransaction();
        }

        $intLastRet = false;
        $arrResult = array();
        foreach ($arrConf[self::CONF_COMMAND_NODE] as $value) {
            if (empty($value) || empty($value[self::CONF_COMMAND_SQL])) {
                Lib_Util_Log::warning("has empty command for [$strName], ignore.");
                continue;
            }

            if (isset($value['ignore_if_last_ret']) &&
                    $intLastRet!==false &&
                    $intLastRet==$value['ignore_if_last_ret']) {
                $arrResult[] = 0;
                $intLastRet = false; // 确保相邻的command的ignore_if_last_ret语义(与初始化保持一致)
                continue;
            }

            $arrTmp = array();
            $strSql = $value[self::CONF_COMMAND_SQL];
            $ret = $this->_queryDB($strSql, $arrParams, $arrTmp);
            if ($ret===false ) {
                if($use_transation){
                    $this->rollback();
                }
                return self::_getOutArray(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            $arrResult[] = $ret;
            $intLastRet = $ret;
        }
        if ($use_transation) {
            $this->commit();
        }

        $strOutKey = self::DEFAULT_OUTKEY;
        if (is_string($arrConf[self::CONF_COMMAND_OUTKEY]) && !empty($arrConf[self::CONF_COMMAND_OUTKEY])) {
            $strOutKey = $arrConf[self::CONF_COMMAND_OUTKEY];
        }
        return self::_getOutArray(Tieba_Errcode::ERR_SUCCESS, $arrResult, $strOutKey);
    }

    /**
     * 执行sql
     * @param string $sqlTpl
     * @param array $arrParams
     * @param array $outResult
     * @return string|string|unknown
     */
    private function _queryDB($sqlTpl, $arrParams, &$outResult) {
        //拼sql
        $this->_tpl->prepare($sqlTpl);
        $strSql = $this->_tpl->bindParam($arrParams, null, true);
        if ($strSql === null || $strSql === false) {
            Lib_Util_Log::warning("build sql fail [$sqlTpl]");
            return false;
        }

        //执行sql
        $result = $this->_db->query($strSql);
        /*Lib_Util_Log::debug('exec sql [ ' . $strSql . ' ]'
                            . '[errno:' . $this->_db->errno() . ']'
                            . '[error:' . $this->_db->error() . ']'
                            . '[rows:' . $this->_db->getAffectedRows() . ']'
                            . '[charset:' . $this->get_charset() . ']'
                            . '[cur sql time(us):' . $this->_db->getLastCost() . ']'
                            . '[all sql time(us):' . $this->_db->getTotalCost() . ']');*/

        if ($result === false) {
            Lib_Util_Log::warning('exec sql fail [' . $this->_db->errno() . '] [' .  $this->_db->error() . '] [' . $strSql . ']');
            return false;
        }
        $outResult = $result;
        return $this->_db->getAffectedRows();
    }

    /**
     * @return mixed
     */
    public function set_gbk() {
        define('BINGO_ENCODE_LANG', self::CHAR_SET_GBK);
        return $this->_db->charset(self::CHAR_SET_GBK);
    }

    /**
     * @return mixed
     */
    public function set_utf8() {
        define('BINGO_ENCODE_LANG', self::CHAR_SET_UTF8);
        return $this->_db->charset(self::CHAR_SET_UTF8);
    }

    /**
     * @return mixed
     */
    public function get_charset() {
        return $this->_db->__get('db')->character_set_name();
    }

    /**
     * @return mixed
     */
    public function startTransaction() {
        return $this->_db->startTransaction();
    }

    /**
     * @return mixed
     */
    public function commit() {
        return $this->_db->commit();
    }

    /**
     * @return mixed
     */
    public function rollback() {
        return $this->_db->rollback();
    }
}
