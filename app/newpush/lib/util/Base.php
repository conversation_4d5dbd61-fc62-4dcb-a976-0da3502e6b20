<?php
/**
 * ui action的基类
 **/
abstract class Lib_Util_Base extends Bingo_Action_Abstract {

    // 错误号
    /**
     * @var int
     */
    protected $_intErrno = 0;

    // 错误信息
    /**
     * @var string
     */
    protected $_strError = 'success';
    /**
     * @var array
     */
    protected $_arrUserInfo = array();

    /**
     * @return bool
     * @throws Lib_Util_Exception
     */
    public function init(){
    
        //获取路由名，并打到ui日志里
        $strRouter = Bingo_Http_Request::getStrHttpRouter();
        Bingo_Log::pushNotice("urlkey", $strRouter);
        
        //获取当前用户信息，并打到ui日志里
        $this->_getUserInfo();
        foreach($this->_arrUserInfo as $strKey => $mixVal) {
            Bingo_Log::pushNotice($strKey, $mixVal);
        }

        //默认对POST请求做tbs校验，如不需要自行去掉此模块
        if(Bingo_Http_Request::isPost()){
            $strTbs = strval(Bingo_Http_Request::get('tbs',''));
            if ( false === $this->_tbsCheck($strTbs) ) {
                throw new Lib_Util_Exception("tbs check error!",Tieba_Errcode::ERR_INVALID_SIGN);
            }
        }
        return true;
    }

    /**
     * @param $strTbs
     * @return bool
     */
    protected function _tbsCheck($strTbs) {
        if(!Tieba_Tbs::check($strTbs, true)){
             return false;
        }
        return true;
    }

    /**
     * @return array
     */
    protected function _getUserInfo() {
        if (!empty($this->_arrUserInfo)) {
            return $this->_arrUserInfo;
        }

        $bolLogin   = (boolean)Tieba_Session_Socket::isLogin();
        $intUserId  = intval(Tieba_Session_Socket::getLoginUid());
        $strUserName    = strval(Tieba_Session_Socket::getLoginUname());
        $bolNoUname     = (boolean)Tieba_Session_Socket::getNo_un();

        $arrUserSInfo   = array(
            'is_login'  => $bolLogin,
            'user_id'   => $intUserId,
            'user_name' => $strUserName,
            'is_noname' => $bolNoUname,
        );
        $this->_arrUserInfo = $arrUserSInfo;
        return $this->_arrUserInfo;
    }


    /**
     * @param $errno
     * @param $errmsg
     * @param array $arrExtData
     */
    protected function _jsonRet($errno, $errmsg, array $arrExtData=array()){
        $arrRet = array(
            'no'=>intval($errno),
            'error'=>strval($errmsg),
            'data'=>$arrExtData,
        );
        foreach($arrRet as $k=>$v){
            Bingo_Page::assign($k,$v);
        }
        Bingo_Page::setOnlyDataType("json");
        Bingo_Http_Response::contextType('application/json');
    }

    /**
     * @param $errno
     * @param $errmsg
     * @param array $arrExtData
     */
    protected function _serialRet($errno, $errmsg, array $arrExtData=array()){
        $arrRet = array(
            'no'=>intval($errno),
            'error'=>strval($errmsg),
            'data'=>$arrExtData,
        );
        foreach($arrRet as $k=>$v){
            Bingo_Page::assign($k,$v);
        }
        Bingo_Page::setOnlyDataType("serial");
        Bingo_Http_Response::contextType('application/json');
    }

}
