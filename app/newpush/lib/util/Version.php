<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file Version.php
 * <AUTHOR>
 * @date 2015年11月10日
 * @brief 
 *
 **/


class Lib_Util_Version {

    /**
     * 版本比较，返回是否满足对应版本区间
     * @param $arrConf
     * @param $intClientType
     * @param $strClientVersion
     * @return boolean 
     */
    static public function check($arrConf, $intClientType, $strClientVersion) {
        $bolReturn = false;
        if ( isset($arrConf[$intClientType]) ) {
            $bolReturn = (self::compare( $arrConf[$intClientType]['min_version'], $strClientVersion) < 0);
            
            if ($bolReturn && $arrConf[$intClientType]['max_version'] != 'INFINITY') {
                $bolReturn = (self::compare($arrConf[$intClientType]['max_version'], $strClientVersion) >= 0);
            }
        }
        return $bolReturn;
    }

    /**
     * 对比两个版本
     * @param $strVer1
     * @param $strVer2
     * @return int
     */
    static public function compare($strVer1, $strVer2) {
        // ($strVer1 > $strVer2) return 1  ( > 0);
        // ($strVer1 < $strVer2) return -1 ( < 0);
        // ($strVer1 == $strVer2) return 0 ( ==0);
        $arrVer1 = explode ( '.', $strVer1 );
        $arrVer2 = explode ( '.', $strVer2 );
        $intCount = 4;//count ( $arrVer1 );
        for($intI = 0; $intI < $intCount; $intI ++) {
            if (intval($arrVer1 [$intI]) > intval($arrVer2 [$intI])) {
                return 1;
            } else if (intval($arrVer1 [$intI]) < intval($arrVer2 [$intI])) {
                return -1;
            }
        }
        return 0;
    }
}
 
