<?php

/**
 * Created by PhpStorm.
 * User: wangdongdong04
 * Date: 2015/11/13
 * Time: 16:51
 */
class Lib_Util_DDBSBase
{
    private static $_db = null;
    private static $_odb = null;
    private static $_conf = null;
    private static $_use_split_db = false;
    private static $splitDBConfFile = 'db_dl_newpush_newpushdb.conf';
    private static $db_ral_service_name = Lib_Util_Conf::DB_RAL_SERVICE_NAME;
    private static $di_conf_path = Lib_Util_Conf::DI_CONF_PATH;

    /**
     * @return bool
     */
    public function startTransaction() {
        return self::$_odb && self::$_odb->startTransaction();
    }

    /**
     * @return bool
     */
    public function commit() {
        return self::$_odb && self::$_odb->commit();
    }

    /**
     * @return bool
     */
    public function rollback() {
        return self::$_odb && self::$_odb->rollback();
    }

    /**
     * 设置数据库访问方式：
     * @param:
     *  $arrInput(
     *      'enable_split' => 1, // 开启分表
     *      'split_conf' => 1, // 分表配置文件地址
     *      'ral_service' => 1, // ral 服务名称
     *      'di_conf' => 1, // di 文件地址
     *
     * );
     * @reutrn：
     *      void
     *
     */
    public static function setDDBSConf($arrSplitFileds){
        self::$_use_split_db = boolval($arrSplitFileds['enable_split']);
        self::$splitDBConfFile = strval($arrSplitFileds['split_conf']);
        if(isset($arrSplitFileds['ral_service'])){
            self::$db_ral_service_name = strval($arrSplitFileds['ral_service']);
        }
        if(isset($arrSplitFileds['di_conf'])){
            self::$di_conf_path = strval($arrSplitFileds['di_conf']);
        }

        return;
    }

    /**
     * @brief get mysql obj.
     * @return Bd_DB|null : obj of Bd_DB, or null if connect fail.
     */
    private static function _getDB(){
        if(self::$_db){
            return self::$_db ;
        }
        self::$_db = new Bd_DB();
        if(self::$_db == null){
            Lib_Util_Log::warning("new bd_db fail.");
            return null;
        }
        if(self::$_use_split_db){
            $splitDBConfPath = ROOT_PATH."/conf/db/";
            $splitDBConfFile = self::$splitDBConfFile;
            $ret = self::$_db -> enableSplitDB(self::$db_ral_service_name, $splitDBConfPath, $splitDBConfFile);
            if(!$ret){
                Lib_Util_Log::warning("enable splitdb fail.");
                self::$_db = null;
                return null;
            }
            return self::$_db;
        }else{
            Bingo_Timer::start('dbinit');
            $ret = self::$_db->ralConnect(self::$db_ral_service_name);
            Bingo_Timer::end('dbinit');
            if(!$ret){
                Lib_Util_Log::warning("bd db ral connect fail.");
                self::$_db = null;
                return null;
            }
            return self::$_db;

        }
        return null;
    }


    /**
     * @return bool|Lib_Util_DB|null
     */
    public static  function init(){
        if(self::$_odb === null ){
            if(self::$_db == null) {
                self::$_db = self::_getDB();
                if(self::$_db == null) {
                    Lib_Util_Log::warning("init get db fail.");
                    return false;
                }
            }

            if(self::$_conf == null){
                self::$_conf = Bd_Conf::getConf(self::$di_conf_path);
                if(self::$_conf == false){
                    Lib_Util_Log::warning("init get conf fail.");
                    return false;
                }
            }

            self::$_odb = new Lib_Util_DB(self::$_db, self::$_conf);
            if(self::$_odb == null) {
                Lib_Util_Log::warning("new bd_db2 fail.");
                return false;
            }
        }

        return self::$_odb;
    }


    /**
     * @param $arrInput
     * @return mixed
     */
    public static function query($arrInput){
        if(!isset($arrInput['function'])){
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $function = $arrInput['function'];
        if(!self::init()){
            Lib_Util_Log::warning('load config fail by query.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        //$arrInput = self::_encryptContent($arrInput);
        $arrOut = self::$_odb->query($arrInput['function'], $arrInput);
        Lib_Util_Log::trace("DB call. [".serialize($arrInput)."]".serialize($arrOut));

        //$arrOut = self::_decryptContent($arrInput, $arrOut);


        return $arrOut;
    }

    /**
     * @param $arrInput
     * @param $arrOut
     * @return mixed
     */
    private function _decryptContent($arrInput, $arrOut) {
        //对im_message_info的query操作中的content进行base64decode+解压缩
        if (in_array(strval($arrInput['method']), array('query'))) {
            if (isset($arrOut['results'][0])) {
                Bingo_Timer::start('decrypt_content');
                foreach ($arrOut['results'][0] as $strKey => $arrItem) {
                    $arrAttr = self::_getMessageAttr($arrItem['attr']);
                    if (strlen($arrItem['content']) && $arrAttr[self::ENCRYPT_SWITCH_KEY]) {
                        $arrOut['results'][0][$strKey]['content'] = self::_decryptData($arrItem['content']);
                        Lib_Util_Log::warning("decrypt_content!!!,uid={$arrInput['user_id']}");
                    }
                }
                Bingo_Timer::end('decrypt_content');
            }
        }
        return $arrOut;
    }

    /**
     * @param $strDescryptData
     * @param $intUserId
     * @return bool|string
     */
    private function _encryptData($strDescryptData, $intUserId) {
        $strEncryptData = base64_encode(gzdeflate($strDescryptData));
        if (in_array(intval($intUserId), array(213813936, 15016193, 984962145, 180859996))) {
            return $strEncryptData;
        }
        if (!self::ENCRYPT_ONLINE) {
            return false;
        }
        return $strEncryptData;
    }

    /**
     * @param $arrInput
     * @return mixed
     */
    private function _encryptContent($arrInput) {
        //对im_message_info的update操作中content进行压缩+base64encode
        if (in_array(strval($arrInput['method']), array('update')) && strlen($arrInput['content'])) {
            Bingo_Timer::start('encrypt_data');
            $strEncryptData = self::_encryptData($arrInput['content'], $arrInput['user_id']);
            Bingo_Timer::end('encrypt_data');
            $arrInput['attr'][self::ENCRYPT_SWITCH_KEY] = self::ENCRYPT_SWITCH_OFF;
            if (false !== $strEncryptData) {
                Lib_Util_Log::warning("encrypt_content!!!,uid={$arrInput['user_id']}");
                $arrInput['content'] = $strEncryptData;
                $arrInput['attr'][self::ENCRYPT_SWITCH_KEY] = self::ENCRYPT_SWITCH_ON;
                $arrInput['attr'] = serialize($arrInput['attr']);
            }
        }
        return $arrInput;
    }

    /**
     * @param $strEncryptData
     * @return string
     */
    private function _decryptData($strEncryptData) {
        $strDescryptData = gzinflate(base64_decode($strEncryptData));
        return $strDescryptData;
    }

    /**
     * @param $strAttr
     * @return array|mixed
     */
    private function _getMessageAttr($strAttr) {
        if ($strAttr) {
            $arrAttr = unserialize($strAttr);
        } else {
            $arrAttr = array();
        }
        return $arrAttr;
    }
}
