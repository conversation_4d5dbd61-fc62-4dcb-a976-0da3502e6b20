<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * @brief 统一封装im添加cuid等日志字段的逻辑
 * <AUTHOR>
 * @date 2015-09-17
 * @version 
 */
class Lib_Util_Log {
    const LOG_FORMAT_OLD   = 0;    //0: key[value] 
    const LOG_FORMAT_NEW   = 1;    //1: key=value
    protected static $_strLogPrefix = "";   //需要添加的log头
    protected static $_arrLogPrefix = NULL; //log的key=>value形式
    protected static $_intFormat = 1;    

    public static function setModeName($mode_name)
    {
        Bingo_Log::setModeName($mode_name);
    }

    public static function init($arrConfig=array(), $strDefaultModule='',$ompConfig=false)
    {
        self::$_strLogPrefix = "";
        Bingo_Log::init($arrConfig, $strDefaultModule, $ompConfig);
    }

    public static function pushNotice($strKey, $strValue)
    {
        Bingo_Log::pushNotice($strKey, $strValue);
    }

    public static function buildNotice($strOtherLog='', $strModule='', $strFile='', $intLine=0, $intTraceLevel=0)
    {
        Bingo_Log::buildNotice($strOtherLog, $strModule, $strFile, $intLine, $intTraceLevel);
    }

    public static function getNoticeNods()
    {
        Bingo_Log::getNoticeNods();
    }

    public static function getNoticeNodes()
    {
        Bingo_Log::getNoticeNodes();
    }

    public static function getLogId()
    {
        Bingo_Log::getLogId();
    }

    public static function addModule($strModule, $objLog) 
    {
        Bingo_Log::addModule($strModule, $objLog);
    }

    public static function getModule($strModule='')
    {
        Bingo_Log::getModule($strModule);
    }

    public static function setFormat($intFormat = self::LOG_FORMAT_OLD)
    {
        self::$_intFormat = intval($intFormat);
    }

    public static function addStrLogPrefix($strParam) 
    {
        if (empty($strParam) || !is_string($strParam)) {
            return false;
        }
        self::$_strLogPrefix .= $strParam . ' ';
        self::$_arrLogPrefix = NULL;
    }

    // 添加全局log参数，对notice warning trace debug都有效
    public static function addLogPrefix($arrParam)
    {
        if (empty($arrParam) || !is_array($arrParam)) {
            return false;
        }

        if (self::$_intFormat == self::LOG_FORMAT_OLD) {
            foreach($arrParam as $key => $value) {
                self::$_strLogPrefix .= $key . '[' . strval($value) . ']' . ' ';
            }
        } else {
            foreach($arrParam as $key => $value) {
                self::$_strLogPrefix .= $key . '=' . urlencode($value) . ' ';
            }
        }
        
        if(self::$_arrLogPrefix === NULL ){
            self::$_arrLogPrefix = array( $key => $value);
        }else{
            self::$_arrLogPrefix[$key] = $value;
        }
        return true;
    }
    
    //从strPrefix中获取log的key =>value数组
    public static function getArrLogPrefix(){
        if(self::$_arrLogPrefix !== NULL ){
            return self::$_arrLogPrefix;
        }
        
        self::$_arrLogPrefix = array();
        if(self::$_strLogPrefix == '' ){    
            return self::$_arrLogPrefix;
        }
        
        if(self::$_intFormat == self::LOG_FORMAT_OLD){
            $pattern = '/([^\[]+)\[([^\]]+)\]/';
            $machNum = preg_match_all($pattern, self::$_strLogPrefix , $matches );
            if( $machNum != false && $machNum > 0 ){
                for ($index = 0; $index < $machNum; $index++) {
                    self::$_arrLogPrefix[trim($matches[1][$index])] = trim($matches[2][$index]);
                }
            }
        }else{
            $arrLogParam = split(' ',self::$_strLogPrefix);
            foreach($arrLogParam as $strValue){
                list($key,$value) = split('=',$strValue);
                if($key != false){
                    self::$_arrLogPrefix[trim($key)] = urldecode(trim($value));
                }
            }
        }
        return self::$_arrLogPrefix;
    }

    // 获取log
    public static function getLogPrefix()
    {
        return self::$_strLogPrefix;
    }

    protected static function _getFileAndLine($intLevel=0)
    {
        $arrTrace = debug_backtrace();
        $intDepth = 1 + $intLevel;
        $intTraceDepth = count($arrTrace);
        if ($intDepth > $intTraceDepth)
            $intDepth = $intTraceDepth;
        $arrRet = $arrTrace[$intDepth];
        if (isset($arrRet['file'])) $arrRet['file'] = basename($arrRet['file']);
        return $arrRet;
    }


    public static function fatal($strLog, $strModule='', $strFile='', $intLine=0, $intTraceLevel=0)
    {
        $arrRet = self::_getFileAndLine($intTraceLevel);
        return Bingo_Log::fatal(self::$_strLogPrefix . $strLog, $strModule, $arrRet['file'], $arrRet['line'], $intTraceLevel);
    }

    public static function warning($strLog, $strModule='', $strFile='', $intLine=0, $intTraceLevel=0)
    {
        $arrRet = self::_getFileAndLine($intTraceLevel);
        return Bingo_Log::warning(self::$_strLogPrefix . $strLog, $strModule, $arrRet['file'], $arrRet['line'], $intTraceLevel);
    }
    
    public static function notice($strLog, $strModule='', $strFile='', $intLine=0, $intTraceLevel=0)
    {
        $arrRet = self::_getFileAndLine($intTraceLevel);
        return Bingo_Log::notice(self::$_strLogPrefix . $strLog, $strModule, $arrRet['file'], $arrRet['line'], $intTraceLevel);
    }
    
    public static function trace($strLog, $strModule='', $strFile='', $intLine=0, $intTraceLevel=0)
    {
        $arrRet = self::_getFileAndLine($intTraceLevel);
        return Bingo_Log::trace(self::$_strLogPrefix . $strLog, $strModule, $arrRet['file'], $arrRet['line'], $intTraceLevel);
    }
    
    public static function debug($strLog, $strModule='', $strFile='', $intLine=0, $intTraceLevel=0)
    {
        $arrRet = self::_getFileAndLine($intTraceLevel);
        return Bingo_Log::debug(self::$_strLogPrefix . $strLog, $strModule, $arrRet['file'], $arrRet['line'], $intTraceLevel);
    }
}
