<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015/9/10 10:26:44
 * @version 1.0
 * @structs & methods(copied from idl.)
*/
class Lib_Util_Util{
    
    protected static $idc_place_map = array(
        'jx' => 'bj',
        'tc' => 'bj',
        'nj' => 'nj',
    );

    //方便多个不同的DL使用同一个连接
    protected static $_db = null;

    /**
     * @brief get mysql obj.
     * @return: obj of Bd_DB, or null if connect fail.
     **/
    public static function _getCommonDB()
    {

        if (self::$_db) {
            return self::$_db;
        }

        self::$_db = new Bd_DB();
        if (self::$_db == null) {
            Bingo_Log::warning("new bd_db fail.");
            return null;
        }

        Bingo_Timer::start('dbinit');
        $ret = self::$_db->ralConnect(Lib_Util_Conf::DB_RAL_SERVICE_NAME);
        Bingo_Timer::end('dbinit');

        if (!$ret) {
            Bingo_Log::warning("bd db ral connect fail. db ral name: [" . self::$_db_ral_service_name . "]");
            self::$_db = null;
            return null;
        }

        //set db connection to utf8
        self::$_db->charset("utf8");
        return self::$_db;
    }

    /**
     * @brief 将key修改为驼峰，并且将value全部转换为字符串类型
     * @param array $arrVals
     * @return array array $arrVals
     * array $arrVals
     * @internal param $ array $arrVals*       array $arrVals
     */
    public static function change_key_style(array &$arrVals) {
        foreach( $arrVals as $key => $item ) {
            $key2 = preg_replace("/([\w\d]{1,1})_([\w\d]{1,1})/ie","'$1'.strtoupper('$2')",$key);
            if ( is_array($item) ) {
                $item = Lib_Util_Util::change_key_style($arrVals[$key]); 
            }else if( !is_string($item) ){
                $item = (string)$item;
            }
            
            if ( $key2 != $key) {
                $arrVals[$key2] = $item;
                unset( $arrVals[$key] );
            }
        }
        
        return $arrVals;
    }
    
    /**
     *  根据arr1中的顺序，按照对arr2中的key进行排序(cache获取接口后，顺序往往不是按照key顺序，可以通过此函数进行排序)
     *  支持arr2中有多个值对应key相同；结果：将先按照key顺序排列，key相同则按照原元素先后顺序
     *  在im项目中应用场景：
     *      1）获取推荐加入的群组id（有序）
     *      2）根据群组id查询群组信息，此时数据库返回的群组信息与传入的群组id顺序不一致
     *      3）调用该函数将群组信息按照传入的群组id进行排序
     */
    /**
     * @param $arr1
     * @param $keyname
     * @param $arr2
     * @return array
     */
    public static function sortByArrOrder($arr1,$keyname,$arr2){
        $tmpArr = array();
        foreach($arr2 as $value){
            if(isset($value[$keyname])){
                $tmpArr[$value[$keyname]][] = $value;
            }
        }
       
        $retArr = array();
        foreach($arr1 as $key){
            if(isset($tmpArr[$key])){
                $retArr= array_merge($retArr, $tmpArr[$key]);
            }
        }
        return $retArr;
    }
    
    /**
     * @brief 判断是否为合法的id，用来请求参数的判断。要求为整形获取可转换为字符串的整形且大于0。
     */
    /**
     * @param $uintId
     * @return bool
     */
    public static function isInvalidId($uintId) {
        if (is_numeric($uintId) && ($uintId > 0)){
            return false;
        }else{
            return true;
        }
        
    }

    /**
     * @param $idc
     * @return mixed
     */
    public static function getIdcRoomPlace($idc) {
        $idcRoomPlace = self::$idc_place_map[$idc];
        return $idcRoomPlace;
    }

    /**
     * @param $localIdc
     * @param $romoteIdc
     * @return int
     */
    public static function isInSamePlaceIdcRoom($localIdc, $romoteIdc) {
        $locaIdcRoomPlace = self::$idc_place_map[$localIdc];
        $remoteIdcRoomPlace = self::$idc_place_map[$romoteIdc];

        //本地机房nj，lcs机房未知情况，不做推送
        if ($locaIdcRoomPlace === 'nj' && $remoteIdcRoomPlace === NULL) {
            return 0;
        }

        if ($locaIdcRoomPlace === NULL || $remoteIdcRoomPlace === NULL) {
            return -1;
        }
        
        if ($locaIdcRoomPlace == $remoteIdcRoomPlace) {
            return 1;
        }
        else {
            return 0;
        }
    }

    /**
     * @return int
     */
    public static function getUniqeId(){

        $usec = $sec = 0;
        list($usec, $sec) = explode(" ", microtime());
        return intval(intval($sec).intval( (float)$usec * 1000000 ). rand()%1000); 
    
    }

}
