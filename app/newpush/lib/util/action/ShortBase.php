<?php

/**
 * Class Lib_Util_Action_ShortBase
 */
class Lib_Util_Action_ShortBase extends Bingo_Action
{
    //模版
    /**
     * @var bool
     */
    protected $_alt = false;//数据格式
    /**
     * @var bool
     */
    protected $_strTemplete = false;//模版变量名
    /**
     * @var array
     */
    protected $arrData = array();

    /**
     * @var int
     */
    protected $_errno = 0;

    /**
     * @var null
     */
    protected $_conf = null;

    /**
     *
    */
    protected $_jsonData = array(
        'error_code' => 0,
        'error_msg' => 'sucess',
    );

    /**
     * @param $key
     * @param $default
     * @return mixed
     */
    public function getInput($key, $default)
    {
        return Bingo_Http_Request::getNoXssSafe($key, $default);
    }

    /**
     * @param $intErrno
     * @param null $strError
     * @return bool
     */
    protected function _error($intErrno, $strError = null)
    {
        $this->_errno = $intErrno;
        return true;
    }


    /**
     *
     */
    public function execute()
    {
        //初始化数据
        $this->_load();
        //通用检查
        $ret = $this->_check();
        //处理输入
        if ($ret === true) {
            $ret = $this->_input();
        }

        //主逻辑处理
        if ($ret === true) {
            $ret = $this->_process();
        }
        //构建输出
        $this->_build();
        $this->_display();
        $this->_finish();

    }

    /**
     * @return bool
     */
    protected function _load()
    {
        return true;
    }

    /**
     * @return bool
     */
    protected function _input()
    {
        return true;
    }

    /**
     * @return bool
     */
    protected function _process()
    {
        return true;
    }

    /**
     * @return bool
     */
    public function init()
    {
        //获取返回数据格式类型
        $this->_alt = strtolower((trim(Bingo_Http_Request::getNoXssSafe('alt', 'json'))));
        Tieba_Stlog::setFileName('feye-stat');
        return true;
    }

    /**
     * @return bool
     */
    protected function _check()
    {
        return true;
    }

    /**
     *
     */
    public function _build()
    {
        $this->buildPage();
    }

    /**
     *
     */
    protected function _finish()
    {
    }

    /**
     *
     */
    protected function buildPage()
    {
        if ($this->_alt == 'json' || $this->_strTemplete === false) {  //json 类型
            if ($this->_errno != 0) {
                $this->_jsonData['error_code'] = $this->_errno;
                $this->_jsonData['error_msg'] = 'failed!';
            }
            if ($this->arrData != false) {
                $this->_jsonData = array_merge($this->_jsonData, $this->arrData);
            }
            echo(Bingo_String::array2json($this->_jsonData));
        } else if ($this->_strTemplete !== false) {
            Bingo_Page::assign('err_no', $this->_errno);
            if ($this->_errno != 0) {
                Bingo_Page::assign('err_text', 'failed!');
            } else {
                Bingo_Page::assign('err_text', 'sucess!');
            }
            //初始化view
            Bingo_Page::init(array(
                'baseDir' => MODULE_VIEW_PATH,
                'outputType' => '.',
                'isXssSafe' => true,
            ));
            Bingo_Page::setTpl($this->_strTemplete);
            Bingo_Page::setXssSafe(true);
            Bingo_Page::assign($this->arrData);
            Bingo_Page::buildPage();
        } else {
            //...nothing todo
        }
    }

    /**
     *
     */
    protected function _display()
    {
        if (empty($_SERVER['HTTP_USER_AGENT'])) {
            Tieba_Stlog::addNode('agent', 'none');
        }
        Tieba_Stlog::addNode('email', Tieba_Session_Socket::getEmail());
        Tieba_Stlog::addNode('mobilephone', Bingo_Http_Request::getNoXssSafe('tag_id', -1));
        Tieba_Stlog::addNode('no_un', intval(Tieba_Session_Socket::getNo_un()));
        Tieba_Stlog::addNode('uid', Tieba_Session_Socket::getLoginUid());
        Tieba_Stlog::addNode('un', Tieba_Session_Socket::getLoginUname());
        Tieba_Stlog::addNode('uip', Bingo_Http_Ip::getUserClientIp());

        Tieba_Stlog::addNode('url', $_SERVER['PATH_INFO']);
        Tieba_Stlog::addNode('mid', defined("MODULE_NAME") ? MODULE_NAME : 'im');
        Tieba_Stlog::addNode('ref', isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '');
        Tieba_Stlog::addNode('errno', $this->_errno);
        Tieba_Stlog::addNode('ErrMsg', Tieba_Error::getErrmsg($this->_errno));
        Tieba_Stlog::addNode('urlkey', Bingo_Controller_Front::getInstance()->getDispatchRouter());
    }
}
