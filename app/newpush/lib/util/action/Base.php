<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-11-08
 * @version 1.0
 */
abstract class Lib_Util_Action_Base extends Bingo_Action_Abstract
{

    const CLIENT_TYPE_IPHONE = 1;
    const CLIENT_TYPE_ANDROID = 2;
    const CLIENT_TYPE_IPAD = 5;

    protected $strFormat = "";
    protected $intUserId = 0;
    //protected $strUserName = "";
    protected $strCuid = "";
    protected $intSequenceId = 0;
    protected $strLcsIp = "";
    protected $intLcsPort = 0;
    protected $intLcsFd = 0;
    protected $strDevice = "";
    protected $arrDevice = "";
    protected $intLastLoginTime = 0;
    protected $intCmd = 0;
    protected $arrMsgTag = array();
    protected $errno = 0;
    protected $errmsg = "";
    protected $usermsg = "";
    protected $arrError = array();
    protected $needResponse = true;         //是否直接响应客户端（推送消息的ACK，需要由send接口响应）
    protected $needCheckDuplicate = false;  //是否需要检查客户端请求去重, 默认不检查去重
    protected $strProtocolVersion = 10;     //协议版本 1.0
    private $_bolCacheResponce = false;   //是否需要缓存本次交互结果

    protected $objCache = null;

    //lcs请求需要特殊的打包格式
    protected $bolIsLcs = false;
    protected $bolIsPv = 1;                   //是否是pv

    protected $arrHead = array();            //输入
    protected $arrBody = array();            //输入
    protected $arrData = array();             //输出
    protected $intConntime = 0;
    protected $project = '';
    protected $intOldLcsFd = 0;
    protected $strOldLcsIp = '';
    protected $intOldConntime = 0;
    protected $strOpMode = '';

    //不toast给用户错误提示, 客户端内部处理
    protected $arrHideErrno = array(
        Tieba_Errcode::ERR_USER_NOT_ONLINE,
        Tieba_Errcode::ERR_DL_CALL_FAIL,
        Tieba_Errcode::ERR_CALL_SERVICE_FAIL,
    );

    /**
     * execute
     *
     * @param null
     *
     * @return true|false
     */
    public function execute()
    {
        //初始化数据
        if ($this->_load()) {
            $ret = true;
            //检查提交去重
            $bolDuplicate = $this->_check_duplicate();
            if (!$bolDuplicate) {
                //通用检查
                $ret = $this->_check();
                //处理输入
                if ($ret === true) {
                    $ret = $this->_input();
                }

                //主逻辑处理
                if ($ret === true) {
                    $ret = $this->_process();
                }
            }
            //构建输出
            if ($ret === true) {
                $this->_build();
            }
        }

        //输出数据
        if ($this->bolIsLcs) {
            $this->_lcs_display();
        } else {
            $this->_display();
        }
        $this->_finish();
        return true;
    }

    /**
     * _input
     *
     * @param null
     *
     * @return true|false
     */
    protected function _input()
    {
        return true;
    }

    /**
     * _get_cache
     *
     * @param null
     *
     * @return true|false
     */
    protected function _get_cache()
    {
        if ($this->objCache) {
            return $this->objCache;
        }

        Bingo_Timer::start('cacheinit');
        $this->objCache = Lib_Util_Cache::getInstance();
        Bingo_Timer::end('cacheinit');

        if (!$this->objCache) {
            Lib_Util_Log::warning("init cache fail.");
            $this->objCache = null;
            return null;
        }
        return $this->objCache;
    }

    //唯一标识一个请求, 用于客户端重试去重, 注意对于发消息，是在nmq后更新cache
    /**
     * _get_cache
     *
     * @param null
     *
     * @return true|false
     */
    protected function _get_request_key()
    {
        $key = $this->strCuid . '_' . $this->intCmd . '_' . $this->intSequenceId;
        return $key;
    }

    /**
     * _get_cache
     *
     * @param null
     *
     * @return true|false
     */
    abstract protected function _process();

    /**
     * _get_cache
     *
     * @param null
     *
     * @return true|false
     */
    protected function _build()
    {
        return true;
    }

    /**
     * _get_cache
     *
     * @param null
     *
     * @return true|false
     */
    public function init()
    {
        return true;
    }

    /**
     * _get_cache
     *
     * @param null
     *
     * @return true|false
     */
    protected function _finish()
    {
        static $arrClientTypes = array(1 => 'apple', 'android', 'wphone', 'win8', 'ipad');

        Tieba_Stlog::addNode('uid', $this->intUserId);
        //Tieba_Stlog::addNode('un',         $this->strUserName);

        Tieba_Stlog::addNode('mid', "api");
        Tieba_Stlog::addNode('errno', $this->errno);
        Tieba_Stlog::addNode('urlkey', $this->intCmd);
        Tieba_Stlog::addNode('optime', time());
        Tieba_Stlog::addNode('pro', 'client');
        Tieba_Stlog::addNode('uipstr', Bd_Ip::getUserIp());
        Tieba_Stlog::addNode('uip', ip2long(Bd_Ip::getUserIp()));
        Tieba_Stlog::addNode('logid', Bingo_Log::getLogId());

        Tieba_Stlog::addNode('agent', isset($this->arrDevice['agent']) ? $this->arrDevice['agent'] : '');
        Tieba_Stlog::addNode('phone_imei', isset($this->arrDevice['_phone_imei']) ? $this->arrDevice['_phone_imei'] : '');
        Tieba_Stlog::addNode('_phone_imei', isset($this->arrDevice['_phone_imei']) ? $this->arrDevice['_phone_imei'] : '');
        Tieba_Stlog::addNode('from', isset($this->arrDevice['from']) ? $this->arrDevice['from'] : '');
        Tieba_Stlog::addNode('_client_type', isset($this->arrDevice['_client_type']) ? $this->arrDevice['_client_type'] : '');
        Tieba_Stlog::addNode('client_type_str', isset($arrClientTypes[$this->arrDevice['_client_type']]) ? $arrClientTypes[$this->arrDevice['_client_type']] : '');
        Tieba_Stlog::addNode('_client_version', isset($this->arrDevice['_client_version']) ? $this->arrDevice['_client_version'] : '');
        Tieba_Stlog::addNode('os_version', isset($this->arrDevice['_os_version']) ? $this->arrDevice['_os_version'] : '');
        Tieba_Stlog::addNode('client_id', isset($this->arrDevice['_client_id']) ? $this->arrDevice['_client_id'] : '');
        list($width, $height) = split(',', $this->arrDevice['_phone_screen']);
        Tieba_Stlog::addNode('width', intval($width));
        Tieba_Stlog::addNode('height', intval($height));
        Tieba_Stlog::addNode('net_type', isset($this->arrDevice['net_type']) ? $this->arrDevice['net_type'] : '');
        Tieba_Stlog::addNode('model', isset($this->arrDevice['model']) ? $this->arrDevice['model'] : '');
        Tieba_Stlog::addNode('is_mobile', 0);
        Tieba_Stlog::addNode('is_new_user', 0);
        //client 新增上报字段
        //品牌：上报字段brand，取值示例：iphone,samsung,htc等
        Tieba_Stlog::addNode('brand', isset($this->arrDevice['brand']) ? $this->arrDevice['brand'] : '');
        //品牌型号：上报字段brand_type，取值示例：iphone5,iphone5c,ipad mini2等
        Tieba_Stlog::addNode('brand_type', isset($this->arrDevice['brand_type']) ? $this->arrDevice['brand_type'] : '');

        //cuid
        $strCuidTmp = !empty($this->arrDevice['cuid']) ? $this->arrDevice['cuid'] : $this->strCuid;
        $intPos = strpos($strCuidTmp, '|com.baidu.tieba');
        if ($intPos !== false) {
            $strCuidTmp = mb_substr($strCuidTmp, 0, $intPos, 'utf-8');
        }
        Tieba_Stlog::addNode('cuid', $strCuidTmp);
        //bduid
        $bduid = '';
        if (isset($this->arrDevice['_phone_imei']) && $this->arrDevice['_phone_imei'] != '') {
            $bduid = strtoupper(md5($this->arrDevice['_phone_imei']));
        } else if (isset($this->arrDevice['_client_id']) && $this->arrDevice['_client_id'] != '') {
            $bduid = strtoupper(md5($this->arrDevice['_client_id']));
        }
        Tieba_Stlog::addNode('bduid', trim($bduid, '"'));
        //subapp
        $strSubApp = strval(Bingo_Http_Request::getNoXssSafe('subapp_type', ''));
        if (empty($strSubApp)) {
            $strSubApp = strval(Bingo_Http_Request::getNoXssSafe('app_id', ''));
            if (empty($strSubApp)) {
                if (Molib_Util_Version::compare($this->arrDevice['_client_version'] , '4.2.7')==0) {
                    $strSubApp = 'mini';
                } else if (!empty($this->arrDevice['subapp_type'])) {//简版subapp_type
                    $strSubApp = $this->arrDevice['subapp_type'];
                } else {
                    $strSubApp = 'tieba';
                }
            }
        }
        Tieba_Stlog::addNode('subapp_type', $strSubApp);
        if ($this->arrDevice['net']) {
            Lib_Util_Log::pushNotice('net', $this->arrDevice['net']);
        }
    }

    /**
     * _get_cache
     *
     * @param null
     *
     * @return true|false
     */
    protected function _check()
    {
        return true;
    }

    // check 客户端提交，根据sequence进行去重
    /**
     * _get_cache
     *
     * @param null
     *
     * @return true|false
     */
    protected function _check_duplicate()
    {
        if ($this->needCheckDuplicate) {
            Tieba_Stlog::addNode('check_dup', 1);  //表示该ui请求需要提交去重

            if ($this->needResponse === true) {
                $this->_bolCacheResponce = true;    //缓存本次返回数据
            }
            $cache = $this->_get_cache();
            $cacheKey = $this->_get_request_key();
            $cacheValue = $cache->get($cacheKey, Lib_Util_Conf::CACHE_RESPONSE_VALUE_PREFIX);
            if (!empty($cacheValue) && isset($cacheValue['error']) &&
                isset($cacheValue['data'])
            ) {
                $error = $cacheValue['error'];
                $data = $cacheValue['data'];
                if (!is_array($error) || !is_array($data)) {
                    Lib_Util_Log::warning('get cache data error ret[' . serialize($cacheValue) . ']');
                    return false;
                }
                $this->errno = intval($error['errno']);
                $this->arrData = $data;
                Tieba_Stlog::addNode('is_dup', 1);  //表示该ui请求命中去重
                Lib_Util_Log::pushNotice('is_dup_omp', 1);  //表示该ui请求命中去重
                $this->_bolCacheResponce = false;    //重复提交无需更新cache
                return true;
            } else {
                Tieba_Stlog::addNode('is_dup', 0);
                Lib_Util_Log::pushNotice('is_dup_omp', 0);
                return false;
            }
        }
        Tieba_Stlog::addNode('check_dup', 0);
        return false;
    }

    /**
     * _get_cache
     *
     * @param null
     *
     * @return true|false
     */
    protected function _load()
    {
        Tieba_Stlog::addNode('ispv', 0);
        // 获取lcs数据
        $this->strFormat = strtolower(strval(Bingo_Http_Request::getNoXssSafe('format', '')));
        $this->strProtocolVersion = intval(Bingo_Http_Request::getNoXssSafe('protocol_version', 10)); //默认1.0版本
        $this->strLcsIp = strval(Bingo_Http_Request::getNoXssSafe('lcs_ip', ""));
        $this->intLcsPort = intval(Bingo_Http_Request::get('lcs_port', 0));
        $this->intLcsFd = intval(Bingo_Http_Request::get('lcs_fd', 0));
        $this->intConntime = intval(Bingo_Http_Request::get('lcs_conn_time', 0));
        $this->strOpMode = strval(Bingo_Http_Request::getNoXssSafe('op_mode', ""));
        $this->strLcsIdc = strval(Bingo_Http_Request::getNoXssSafe('lcs_idc', ""));

        // 获取请求的head及body数据
        $arrInput = Lib_Util_Pack::pack2array($this->strFormat);
        $this->arrHead = $arrInput['head'];
        $this->arrMsgTag = $this->arrHead['msg_tag'];
        $this->arrBody = $arrInput['body'];

        Tieba_Stlog::addNode('prot_version', $this->strProtocolVersion);

        // protocol 2.0协议中 cmd、seq_id、cuid、app_id会通过GET的形式传递给后端
        if ($this->strProtocolVersion < 20) {
            $this->intSequenceId = intval($this->arrMsgTag['sequence']);
            $this->strCuid = str_replace("\0", '', strval($this->arrHead['cuid']));
            $this->intCmd = intval($this->arrHead['cmd']);
            $this->needCheckDuplicate = false; //之前版本协议不支持去重
        } else {
            $this->intSequenceId = intval(Bingo_Http_Request::get('seq_id', 0));
            $this->strCuid = str_replace("\0", '', strval(Bingo_Http_Request::getNoXssSafe('cuid', '')));
            $this->intCmd = intval(Bingo_Http_Request::get('cmd', 0));
            $this->arrMsgTag['sequence'] = $this->intSequenceId;  //协议修改对ui层透明
        }
        Tieba_Stlog::addNode('seq_id', $this->intSequenceId);
        Bd_Log::addNotice('urlkey', $this->intCmd);//显示打Bd_Log

        // 不需要密钥
        if (is_array($this->arrBody) && isset($this->arrBody['secretKey'])) {
            unset($this->arrBody['secretKey']);
        }

        // 初始化log信息, 统一添加cuid user_id user_name cmd日志信息
        Lib_Util_Log::setFormat(Lib_Util_Log::LOG_FORMAT_OLD);
        $arrLogParam = array(
            'cuid' => $this->strCuid,
            'cmd' => $this->intCmd,
        );
        Lib_Util_Log::addLogPrefix($arrLogParam);

        $arrGetClientInfoReq = array(
            'cuid' => $this->strCuid,
        );
        // 获取cuid对应的uid等信息
        $ret = Lib_Util_Service::call('msgpush', 'getClientInfo', $arrGetClientInfoReq);
        if ($ret == false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($ret['clientinfo'])) {
            Lib_Util_Log::warning('Lib_Service_Register call msgpush::getClientInfo fail.' . 'input[' . serialize($arrGetClientInfoReq) . '] ret[' . serialize($ret) . ']');
            return false;
        }
        $client_info = $ret['clientinfo'];
        $ret = $client_info;
        if ($ret === false && ($this->intCmd != 1001 && $this->intCmd != 1006 && $this->intCmd != 1007)) {
            // register数据异常直接返回未上线错误，客户端逻辑会处理重新上线
            // 如果继续运行让ui返回未登陆错误, 会导致用户跳转登陆页
            Lib_Util_Log::warning('client request cuid error, cannot get client info! cuid[' . $this->strCuid . ']');
            $this->_error(Tieba_Errcode::ERR_USER_NOT_ONLINE);
            return false;
        }
        //显示打Bd_Log
        Bd_Log::addNotice('_client_type', isset($this->arrDevice['_client_type']) ? $this->arrDevice['_client_type'] : '');
        Bd_Log::addNotice('_client_version', isset($this->arrDevice['_client_version']) ? $this->arrDevice['_client_version'] : '');


        $arrLogParam = array(
            'user_id' => $this->intUserId,
            //'user_name' => $this->strUserName,
        );
        Lib_Util_Log::addLogPrefix($arrLogParam);

        // 添加到head
        $this->arrHead['user_id'] = $this->intUserId;
        //$this->arrHead['user_name'] = $this->strUserName;
        $this->arrHead['device'] = $this->strDevice;
        $this->arrHead['in_time'] = $this->intLastLoginTime;
        $this->arrHead['lcs_ip'] = $this->strLcsIp;
        $this->arrHead['lcs_port'] = $this->intLcsPort;
        $this->arrHead['lcs_fd'] = $this->intLcsFd;

        //Lib_Util_Log::notice('----BASE-INFO-----. head:['.serialize($this->arrHead). '] body:[' . serialize($this->arrBody).']');
        Lib_Util_Log::debug('----BASE-INFO-----. head:[' . serialize($this->arrHead) . ']');
        return true;
    }

    /**
     * _get_cache
     *
     * @param null
     *
     * @return true|false
     */
    private static function buildAckMsg($intCmd, $arrMsgTag, $arrDevice, $data = array(), $intErrno = Tieba_Errcode::ERR_SUCCESS)
    {
        $arrOutput = array();
        $arrOutput['error'] = array(
            'errno' => strval($intErrno),
            'errmsg' => strval(Tieba_Error::getErrmsg($intErrno)),
            'usermsg' => Bingo_Encode::convert(strval(Tieba_Error::getUserMsg($intErrno)), Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK),
        );
        $arrOutput['data'] = $data;
        //高版本精简字段
        if (empty($arrDevice['pversion']) || Molib_Util_Version::compare($arrDevice['pversion'] , Lib_Util_Conf::PROTO_VERSION_OLD)>0) {
            $arrOutput['is_ack'] = "1";
            $arrOutput['cmd'] = strval($intCmd);
            $arrOutput['msg_tag'] = $arrMsgTag;
            $arrOutput['time'] = strval(Bingo_Timer::getNowTime());
            $arrOutput['logid'] = strval(Lib_Util_Log::getLogId());
        }
        return $arrOutput;
    }

    /**
     * @brief 将key修改为驼峰，并且将value全部转换为字符串类型
     * @param array $arrVals
     * @return array array $arrVals
     * array $arrVals
     * @internal param $ array $arrVals*       array $arrVals
     */
    private static function change_key_style(array &$arrVals)
    {
        foreach ($arrVals as $key => $item) {
            //$key2 = preg_replace("/([\w\d]{1,1})_([\w\d]{1,1})/ie","'$1'.strtoupper('$2')",$key);
            //去正则
            $arrKey = explode("_", $key);
            $n = count($arrKey);
            if ($n == 1) {
                $key2 = $key;
            } else {
                for ($i = 0; $i < $n; $i++) {
                    if ($i == 0) {
                        continue;
                    }
                    $arrKey[$i] = ucfirst($arrKey[$i]);
                }
                $key2 = implode("", $arrKey);
            }

            if (is_array($item)) {
                $item = self::change_key_style($arrVals[$key]);
            } else if (!is_string($item)) {
                $item = (string)$item;
            }

            if ($key2 != $key) {
                $arrVals[$key2] = $item;
                unset($arrVals[$key]);
            }
        }

        return $arrVals;
    }

    //输出数据
    /**
     * _get_cache
     *
     * @param null
     *
     * @return true|false
     */
    protected function _display()
    {
        Bingo_Timer::start('build_page');
        $arrOutput = array();
        $intErrno = intval($this->errno);
        // 协议约定data必须返回array
        if (!is_array($this->arrData)) {
            $this->arrData = array($this->arrData);
        }
        $arrOutput = self::buildAckMsg($this->intCmd, $this->arrMsgTag, $this->arrDevice, $this->arrData, $intErrno);
        if (!isset($this->arrError['errno'])) {
            $this->arrError['errno'] = strval($this->errno);
            if ($this->errmsg == "") {
                $this->arrError['errmsg'] = strval(Tieba_Error::getErrmsg($intErrno));
            }
            if ($this->usermsg == "") {
                $strUserMsg = strval(Tieba_Error::getUserMsg($intErrno));
                $this->arrError['usermsg'] = Bingo_Encode::convert($strUserMsg, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
            }
        }
        // 这里和客户端约定，只要server不返回usermsg,就不toast给用户错误提示
        if (in_array($intErrno, $this->arrHideErrno)) {
            $this->arrError['usermsg'] = "";
        }
        if (!empty($this->arrDevice['pversion']) && Molib_Util_Version::compare($this->arrDevice['pversion'] , Lib_Util_Conf::PROTO_VERSION_OLD)<=0) {
            $this->arrError['errorno'] = $this->arrError['errno'];
            unset($this->arrError['errno']);
            unset($this->arrError['errmsg']);
        }
        $arrOutput['error'] = $this->arrError;
        $arrOutput = self::change_key_style($arrOutput);
        if ($this->_bolCacheResponce) {
            $cache = $this->_get_cache();
            $cacheKey = $this->_get_request_key();
            $ret = $cache->add($cacheKey, $arrOutput,
                Lib_Util_Conf::CACHE_RESPONSE_VALUE_TIME,
                Lib_Util_Conf::CACHE_RESPONSE_VALUE_PREFIX);
            if ($ret === false) {
                Lib_Util_Log::warning("cache responce failed");
            }
        }
        // 命中去重直接返回
        if ($this->needResponse === true) {
            Header("X_BD_MSG_CMD: " . strval($this->intCmd));
            Header("Content-Type: application/octet-stream");
            Header("Need-Transfer: true");
            $ret = Lib_Util_Pack::array2pack($arrOutput, $this->strFormat);
            Tieba_Stlog::addNode('size', strlen($ret));
            echo $ret;
        } else {
            Header("Need-Transfer: false");
            Tieba_Stlog::addNode('size', 0);
        }
        Bingo_Timer::end('build_page');
    }

    /**
     * _get_cache
     *
     * @param null
     *
     * @return true|false
     */
    protected function _lcs_display()
    {
        Bingo_Timer::start('build_page');
        $strOut = Bingo_String::array2json($this->arrData, Bingo_Encode::ENCODE_UTF8);
        echo $strOut;
        Bingo_Timer::end('build_page');
    }

    //出错函数
    /**
     * _get_cache
     *
     * @param null
     *
     * @return true|false
     */
    protected function _error($intErrno, $strError = null, $strUserMsg = null, $arrErrorData = array())
    {
        $this->errno = intval($intErrno);
        $this->arrError['errno'] = strval($intErrno);
        $intErrno = intval($intErrno);
        if ($strError == "") {
            $this->arrError['errmsg'] = strval(Tieba_Error::getErrmsg($intErrno));
        }
        if ($strUserMsg === null) {
            $strUserMsg = strval(Tieba_Error::getUserMsg($intErrno));
            $this->arrError['usermsg'] = Bingo_Encode::convert($strUserMsg, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
        }
        if (!empty($arrErrorData) && is_array($arrErrorData)) {
            $this->arrError['info'] = $arrErrorData;
        }

        $strFile = '';
        $intLine = 0;
        $arrTrace = debug_backtrace();
        $intDepth = 0;
        $intTraceDepth = count($arrTrace);
        if ($intDepth > $intTraceDepth) {
            $intDepth = $intTraceDepth;
        }
        $arrRet = $arrTrace[$intDepth];
        if (isset($arrRet['file'])) {
            $arrRet['file'] = basename($arrRet['file']);
        }
        $strFile = $arrRet['file'];
        $intLine = $arrRet['line'];
        $strHead = serialize($this->arrHead);
        $strBody = serialize($this->arrBody);

        Lib_Util_Log::warning("[uierror][errno:$intErrno][errmsg:$strError][head:$strHead][body:$strBody]", '', $strFile, $intLine);
    }

    /**
     * _get_cache
     *
     * @param null
     *
     * @return true|false
     */
    public function getInput($key, $default)
    {
        if (is_array($this->arrBody) && isset($this->arrBody[$key])) {
            return $this->arrBody[$key];
        } else {
            return $default;
        }
    }

}
