<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015:09:04 21:27:51
 * @version 1.0
 * @structs & methods(copied from idl.)
 */
class Lib_Util_Idalloc
{

    /**
     * @var null
     */
    protected static $_idalloc = null;

    /**
     * @brief init
     * @return bool : true if success. false if fail.
     */
    private static function _init()
    {
        return true;
    }

    /**
     * @param $errno
     * @return array
     */
    private static function _errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @param $arrInput
     */
    public static function preCall($arrInput)
    {
        // pre-call hook
    }

    /**
     * @param $arrInput
     */
    public static function postCall($arrInput)
    {
        // post-call hook
    }

    /**
     * @brief 获取name对应的当前id
     * @arrInput:
     *     string name
     * @param $arrInput
     * @return array : $arrOutput
     *     uint64_t id
     */
    public static function getCurrentId($arrInput)
    {
        if (!isset($arrInput['name'])) {
            Lib_Util_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $strName = strval($arrInput['name']);
        if (empty($strName)) {
            Lib_Util_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (!self::_init()) {
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $intId = 0;
        $ret = self::$_idalloc->get(array(
            'name' => $strName,
            'method' => 'default',
        ));
        if (!$ret || $ret['error'] !== 0) {
            return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
        }
        $intId = intval($ret['id']);

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'id' => $intId,
        );
        return $arrOutput;
    }


    /**
     * @brief 分配name对应的新id
     * @arrInput:
     *     string name
     * @param $arrInput
     * @return array : $arrOutput
     *     uint64_t id
     */
    public static function getNewId($arrInput)
    {
        if (!isset($arrInput['name'])) {
            Lib_Util_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $strName = strval($arrInput['name']);
        if (empty($strName)) {
            Lib_Util_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $random = isset($arrInput['random']) ? intval($arrInput['random']) : 1;
        $idCount = isset($arrInput['id_count']) ? intval($arrInput['id_count']) : 1;

        $ret = Molib_Util_ImIdAlloc::getNewId($random, $strName, ($idCount * 2)); //当前是ID分配器是使用0,1
        $arrIds = array();
        if (!$ret || !isset($ret['id']) || $ret['error_no'] !== 0) {
            Lib_Util_Log::warning('Molib_Util_ImIdAlloc getNewId rpc call fail.strName:[' . $strName . '] ret:[' . serialize($ret) . ']');
            return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
        }

        for ($index = 0; $index < $idCount; $index++) {
            $arrIds[] = intval($ret['id']) - $index * 2;
        }
        $arrIds = array_reverse($arrIds);
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'ids' => $arrIds,
        );
        return $arrOutput;
    }


    /**
     * @param string $strIDType
     * @param int $intStep
     * @return array|bool
     */
    public static function genNewIdViaRalCall($strIDType = Lib_Util_Conf::IDALLOC_TYPE_JOBID, $intStep = 1)
    {
//        $error = Tieba_Errcode::ERR_SUCCESS;
//        $intId = explode(" ",microtime());
//        $arrOutput = array(
//            'errno' => $error,
//            'errmsg' => Tieba_Error::getErrmsg($error),
//            'id' => $intId[1],
//        );
//        return $arrOutput;
//

        if ($strIDType !== Lib_Util_Conf::IDALLOC_TYPE_JOBID && $strIDType !== Lib_Util_Conf::IDALLOC_TYPE_TAGID) {
            Lib_Util_Log::warning("input params invalid, idtype unsupported. [" . serialize($strIDType) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        Bingo_Timer::start('atom');
        $ret = Tieba_Idalloc::alloc($strIDType,$intStep);
        Bingo_Timer::end('atom');

        Bingo_Log::notice("[newpush] ".$strIDType." :" .json_encode($ret));

        if (!isset($ret[0]) || $ret[0] == 0) {
            Bingo_Log::warning("call Tieba_Idalloc failed. output: ". Bingo_String::array2json($ret));
            return false;
        }
        $intId = intval($ret[0]);
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'id' => $intId,
        );
        return $arrOutput;
    }

    /**
     * @param $serverName
     * @param $arrInput
     * @return bool
     */
    private static function ralCall($serverName, $arrInput)
    {
        if (empty($serverName) || empty($arrInput)) {
            return false;
        }
        $strTimer = 'tbapi_call_' . $serverName;
        Bingo_Timer::start($strTimer);
        $out = ral($serverName, 'query', $arrInput, array('rtimeout' => 500));
        Bingo_Timer::end($strTimer);
        if (isset($out['error_no']) && (intval($out['error_no']) == 0)) {
            return $out;
        } else {
            Bingo_Log::pushErrtag("rpc_call_" . $serverName . "_query_fail");
            Bingo_Log::error("query fail! tbapi call clubcm error!server[" . $serverName . "] method[query] [input:" . serialize($arrInput) . "] [output:" . serialize($out) . "]");
            return false;
        }
        return false;
    }
}
