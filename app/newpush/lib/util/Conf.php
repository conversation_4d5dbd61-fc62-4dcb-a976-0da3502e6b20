<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-05-14 16:16:24
 * @version
 */
class Lib_Util_Conf {
    const REDIS_INSTANCE_NAME = 'newpush';      // Redis 实例名称
    const REDIS_MSGBODY_STORE_SUFFIX = "_msg";  // Redis 消息体存储后缀
    const REDIS_MSGSID_STORE_SUFFIX = "_sid";   // Redis sid 存储后缀
    const REDIS_MSGLIST_STORE_SUFFIX = "_mid";  // Redis 消息拉链存储后缀
    const REDIS_MSGLIST_MAX_LENGTH = 50;        // Redis 消息拉链存储后缀
    const REDIS_MSGLIST_BUFF_LENGTH = 10;       // 消息队列截断buff最小长度
    const REDIS_MSGBODY_EXPIRETIME = 86400;     // 消息过期时间
    
    const PUSH_NMQ_TOPIC = 'msgpush';           // PUSH NMQ Topic
    const PUSH_NMQ_CMD = 'pushTOLCSPusher';     // PUSH NMQ  cmd
    
    const MSGGET_BATCH_SETTING = 0;             // Redis 批量获取消息配置：
    const MSGQUEUE_COMPATIBILITY = 0;           // 新老版本兼容配置项：
    
    const DB_RAL_SERVICE_NAME = 'DB_forum_newpush';
    const DI_CONF_PATH = '/app/newpush/di_newpush_newpush';
    const CACHE_SERVICE_NAME = "forum_newpush"; // ral的cache服务配置名
    
    const CACHE_DUPMSG_EXPIRETIME = 7200;       // 消息去重验证cache失效时间
    const CACHE_MSGINFO_EXPIRETIME = 86400;     // 消息去重验证cache失效时间
    const CACHE_USERMASK_EXPIRETIME = 604800;     // user_mask验证cache失效时间
    
    const JOB_MSG_DUP_CHECK = 'job_msg';        // 消息去重key
    const MSGINFO_QUEYR = 'msginfo';            // 消息内容cache查询key
    
    const CACHEKEY_JOBID_JOB = 'jobid_job';                             // Jobid做key来查询job
    const CACHEKEY_TAG_FOLLOW_ID_TYPE = 'follow_id_type_tag';           // 根据follow_id 和 follow_type查询tag
    const CACHEKEY_USER_ID_FOLLOW_ID = 'user_info';                     // 根据user_id 和 follow_id 查询对应的tag_user信息
    const CACHEKEY_JOBID_MSGINFO = 'jobid_msg';                         // Jobid做key来查询
    const CACHEKEY_MID_MSGINFO = 'mid_msg';                             // Mid做Key来查询
    const CACHEKEY_FORUM_FROMID_LASTMID = 'forum_fromid_lastmid';       // 根据 forum FromId存储lastMid
    const CACHEKEY_USER_FROMID_LASTMID = 'user_fromid_lastmid';         // 根据 forum FromId存储lastMid
    const CACHEKEY_CREATETIME_MSGINFO = 'ctime_msg';                    // creattime做Key来查询
    const CACHEKEY_NEWPUSH_ENABLEFIDS = 'newpush_enablefids';           // newpush 开启吧推送列表
    const CACHEKEY_NEWPUSH_FORUMMSGLIST = 'newpush_fmsglist';           // newpush 存储消息拉链记录
    const CACHEKEY_NEWPUSH_OFFICIALFORUMMSGLIST = 'newpush_ofmsglist';  // newpush 官方吧回复消息存储消息拉链记录
    const CACHEKEY_MASK_USER_STATUS = "newpush_usermask";               // newpush 用户屏蔽状态
   
    const MAX_CACHE_QUERY_ITEM = 10000;                 // cache查询中最大的multi get量；
    const MAX_MIDS_GET_BY_SIDRANGE = 20;                // SID 获取 MID的最大个数
    
    const CACHE_RESPONSE_VALUE_PREFIX = '_ui_dupcheck'; // 客户端请求去重 cache后缀
    const CACHE_RESPONSE_VALUE_TIME = 86400;            // 缓存一天
    
    const PROTO_VERSION_OLD = '1.0.0';                  // proto 版本
    const BASE_FOLLOWID = 2000000000;                   // 给客户端推送时的基础Gid，最总效果为baseFollowId+uid(followId)
    const SWITCH_IMPUSHER = 1;                          // 使用IMPusher 开关
    const COUNT_JOBIDQUERYBYFOLLOWID = 1;               // 获取消息时的数量
    const COUNT_GETMSGBYFOLLOWID = 20;                  // 获取消息时的数量
    
    const IMPUSHER_APPID = 'newpush';                   // IMPuser中配置的appId
    const LIKE_FORUM_QUERY_KEY = 'likeForum';           // 吧关注信息查询key
    const LIKE_USER_QUERY_KEY = 'likeUser';             // 明星关注信息查询key
    const NORMALUSER_QUERYKEY = 'normalUser';           // 官方吧用户回复信息查询key
    const FORUMPEER_QUERYKEY = 'forumPeer';             // 官方吧回复用户信息查询key
    const BAZHUTASK_USER_QUERY_KEY = 'bazhuTaskUser';   // 吧主专版任务推送key
    const DEFAULT_USER_QUERY_KEY = 'defaultUser';       // default推送key
    
    const USERTYPE_OFFICIAL_FORUM = 1;
    const USERTYPE_BROADCAST_FORUM = 3;
    const USERTYPE_OFFICIAL_ACCOUNT = 4;
    const USERTYPE_OFFICIAL_ACCOUNT_NEW = 5;
    const USERTYPE_NORMAL_USER_ACCOUNT = 0;
    
    const SWITCH_QUERYMSGINFO_ONPUSH = 1;       // 推送时查询消息内容开关
    
    const IDALLOC_RANDOMID = 0;                 // IDAlloc 分配ID集群
    const SERVER_CLUBCM_IDALLOC = 'clubcm';     // CLUBCM 分配IDAlloc Service 名
    const IDALLOC_TYPE_JOBID = 'newpush_jobid'; // JOBID分配名称
    const IDALLOC_TYPE_TAGID = 'newpush_tagid'; // TAGID分配名称
    
    // FollowType的设置
    const FOLLOW_TYPE_ALL = 0;                  // 获取全部关注信息
    const FOLLOW_TYPE_OFFICIAL = 1;             // 官方吧
    const FOLLOW_TYPE_FORUM_BROADCAST = 2;      // 吧广播
    const FOLLOW_TYPE_OFFICIAL_ACCOUNT = 3;     // 贴吧官方账号
    const FOLLOW_TYPE_PUBLIC_ACCOUNT = 4;       // 公众账号
    const FOLLOW_TYPE_SUBSCRIBE_ACCOUNT = 5;    // 订阅帐号
    const FOLLOW_TYPE_LIVE_ACCOUNT = 6;         // 直播主播帐号
    const FOLLOW_TYPE_INDIVIDUATION_FULL = 7;   // 个性化推送全量
    const FOLLOW_TYPE_INDIVIDUATION_GROUP = 8;  // 个性化推送组播
    const FOLLOW_TYPE_BAZHUTASK = 9;            // 吧主专版任务推送
    const FOLLOW_TYPE_BAZHUNOTICE = 10;            // 吧主专版公告推送
    
    // MsgType
    const MSG_TYPE_MESSAGE = 1;
    const MSG_TYPE_PICTURE = 2;
    const MSG_TYPE_VOICE = 3;
    const MSG_TYPE_MALL_SMILE = 4;              // 表情商店大表情
    const MSG_TYPE_INVITE = 5;
    const MSG_TYPE_ACTIVITY = 6;                // 群活动
    const MSG_TYPE_PIC_TEXT = 7;                // 图文消息
    const MSG_TYPE_LIVE = 8;                    // 直播群消息
    const MSG_TYPE_SPECIAL = 9;                 // 群分享消息。后续此模式的消息可复用此类型，在content中作区分
    const MSG_TYPE_COMMON = 10;                 // 回复at粉丝消息
    const MSG_TYPE_SYSTEM = 11;
    const MSG_TYPE_LIVE_SYSTEM = 12;            // 伪直播群系统消息
    const MSG_TYPE_NOTIFY_LOG = 15;             // 日志系统-系统消息
    const MSG_TYPE_ONLINE_DEBUG = 16;           // 在线联调消息
    const MSG_TYPE_PMSG_ROBOT_FRIEND = 21;      // 私聊机器人推荐好友
    const MSG_USER_TYPE_PMSG_HAVE_READ = 22;
    const MSG_TYPE_PMSG_STRANGER_CARD = 23;     // aio 陌生人聊天

    public static $MSG_TYPES = array(
        self::MSG_TYPE_MESSAGE,
        self::MSG_TYPE_PICTURE,
        self::MSG_TYPE_VOICE,
        self::MSG_TYPE_MALL_SMILE,
        self::MSG_TYPE_INVITE,
        self::MSG_TYPE_SYSTEM,
        self::MSG_TYPE_ACTIVITY,
        self::MSG_TYPE_COMMON,
        self::MSG_TYPE_PIC_TEXT,
        self::MSG_TYPE_LIVE,
        self::MSG_TYPE_SPECIAL,
        self::MSG_TYPE_LIVE_SYSTEM,
        self::MSG_USER_TYPE_PMSG_HAVE_READ,
        self::MSG_TYPE_PMSG_STRANGER_CARD,
    );

    const APNS_MSG_TP = 13;             //推送apns使用
    const MSG_OPMSG_APNS_SUBTP = 2;     // 推送使用使用的apns subtp
    const MAX_APNS_CONTENT_LEN = 25;    //最大的apns推送content长度，包括...

    public static $FORUM_PUSH_FOLLOWTYPE = array(
        self::FOLLOW_TYPE_OFFICIAL,
        self::FOLLOW_TYPE_FORUM_BROADCAST,
        self::FOLLOW_TYPE_SUBSCRIBE_ACCOUNT,
    );

    public static $USER_PUSH_FOLLOWTYPE = array(
        self::FOLLOW_TYPE_LIVE_ACCOUNT,
    );

    // FollowType强关系类型推送模式列表
    public static $FOLLOW_TYPE_STRONG_ARRAY = array(
        self::FOLLOW_TYPE_FORUM_BROADCAST,
        self::FOLLOW_TYPE_OFFICIAL,
        self::FOLLOW_TYPE_OFFICIAL_ACCOUNT,
        self::FOLLOW_TYPE_PUBLIC_ACCOUNT,
        self::FOLLOW_TYPE_LIVE_ACCOUNT,
    );

    //用户屏蔽bitmap
    public static $USER_MASK_BITMAP = array(
        self::FOLLOW_TYPE_FORUM_BROADCAST => 0x1,
        self::FOLLOW_TYPE_OFFICIAL => 0x2,
        self::FOLLOW_TYPE_SUBSCRIBE_ACCOUNT  => 0x4,
    );

    // FollowType弱关系类型组播推送模式列表
    public static $FOLLOW_TYPE_WEAK_GROUP_ARRAY = array(
        self::FOLLOW_TYPE_INDIVIDUATION_GROUP,
    );

    // FollowType弱关系类型广播推送模式列表
    public static $FOLLOW_TYPE_WEAK_BROADCAST_ARRAY = array(
        self::FOLLOW_TYPE_INDIVIDUATION_FULL,
    );

    // FollowType弱关系类型吧主任务推送模式列表
    public static $FOLLOW_TYPE_WEAK_BAZHUTASK_ARRAY = array(
        self::FOLLOW_TYPE_BAZHUTASK,
    );

    // FollowType弱关系类型吧主公告推送模式列表
    public static $FOLLOW_TYPE_WEAK_BAZHUNOTICE_ARRAY = array(
        self::FOLLOW_TYPE_BAZHUNOTICE,
    );

    // 条漫强关系，自定义策略推送模式
    public static $FOLLOW_TYPE_STRONG_OFFICIALCOUNT_ARRAY = array(
        self::FOLLOW_TYPE_SUBSCRIBE_ACCOUNT,
    );
    
    public static $ENABLE_FOLLOWTYPES = array(
        self::FOLLOW_TYPE_FORUM_BROADCAST,
        self::FOLLOW_TYPE_OFFICIAL,
    );

    public static $ENABLE_PREM_QUERYTYPES = array(
        self::LIKE_FORUM_QUERY_KEY => 1,
        self::NORMALUSER_QUERYKEY => 1,
        self::FORUMPEER_QUERYKEY => 1,
        self::LIKE_USER_QUERY_KEY => 0,

    );

    public static $MAP_USERTYPE_FOLLOWTYPE = array(
        self::USERTYPE_OFFICIAL_FORUM => self::FOLLOW_TYPE_OFFICIAL,
        self::USERTYPE_BROADCAST_FORUM => self::FOLLOW_TYPE_FORUM_BROADCAST,
        self::USERTYPE_OFFICIAL_ACCOUNT => self::FOLLOW_TYPE_OFFICIAL_ACCOUNT,
        self::USERTYPE_OFFICIAL_ACCOUNT_NEW => self::FOLLOW_TYPE_SUBSCRIBE_ACCOUNT,
    );

    public static $MAP_FOLLOWTYPE_USERTYPE = array(
        self::FOLLOW_TYPE_OFFICIAL => self::USERTYPE_OFFICIAL_FORUM,
        self::FOLLOW_TYPE_FORUM_BROADCAST => self::USERTYPE_BROADCAST_FORUM,
        self::FOLLOW_TYPE_OFFICIAL_ACCOUNT => self::USERTYPE_OFFICIAL_ACCOUNT,
        self::FOLLOW_TYPE_SUBSCRIBE_ACCOUNT => self::USERTYPE_OFFICIAL_ACCOUNT_NEW,
    );

    const FOLLOW_TYPE_VERSION_IOS_BASE = "7.5.0";       // FollowTypeIOS设备推送版本下限
    const FOLLOW_TYPE_VERSION_TIAOMAN_IOS = "7.4.0";    // 条漫支持版本
    const FOLLOW_TYPE_VERSION_ANDROID_BASE = "7.1.0";   // FollowType ANDROID推送版本下限
//    const FOLLOW_TYPE_NUMBER = 300;                     // FollowType推送的人数
    const FOLLOW_TYPE_NUMBER = 50;                     // FollowType推送的人数
    const FOLLOW_TYPE_NUMBER_MINI = 100;                // 对于response太大的请求，拆小
    
    const USERINFO_COUNT_PER_TIME = 100;                // 每次HMSET的用户数
    const NEW_PUSH_LIST_NUMBER = 1000;                  // 推送队列的长度
    const NEW_PUSH_SMALL_SWITCH = 1;                    // NewPush流量控制 1为全量 2为开启一半 3为开启三分之一，以此类推
    const OLD_PUSH_SMALL_SWITCH = 1;                    // 老Push流量控制 1为全量 2为开启一半 3为开启三分之一，以此类推
    
    const BAZHUTASK_TAG_ID = 10000000000001;            //吧主专版任务推送的固定TAG_id
    const BAZHUTASK_JOB_ID = 'newpush_bazhu_jobid';
    const BAZHUTASK_FOLLOW_ID = 10000000000001;         //吧主专版任务推送的固定follow_id
    
    const STRESS_TEST_SWITCH = true;    // 压测开关
    const BUCKET_NUM = 1024;            // bucket strategy
    const ENABLE_MASK_BROADCAST = 1;
    
    const USER_ATTENTION_NORMAL = 0;    // 用户关注吧且未屏蔽吧广播status=0
    const USER_ATTENTION_CANCELED = 1;  // 用户取消关注吧status=1
    const USER_ATTENTIONED_MASKED = 2;  // 用户关注吧但屏蔽吧广播status=2

    //prepare过程中，在getStartPushJob 的时候，当前只支持  吧广播(2) 和 吧主专版的任务(9) 两个follow的使用
    //如果增加其他业务，需要在这个地方增加对应的follow type
    const STR_ALLOW_PREPARE_FOLLOW_TYPE = "1,2,5,9,10";
    
    public static $ENABLE_MASK_TYPE = array(
        self::ENABLE_MASK_BROADCAST,
    );
    
    const USERMSGLIST_FLAG_OFFICIALFROUM = 1;   // userMsgList flag 类型：1-fromId为官方吧
    const USERMSGLIST_FLAG_NORMALUSER = 0;      // userMsgList flag 类型：0-fromId为普通用户
    const USERMSGLIST_TYPE_U_OF = 1;            // userMsgList type 类型：普通用户发给官方吧
    const USERMSGLIST_TYPE_OF_U = 2;            // userMsgList type 类型：官方吧回复普通用户
    const USERMSGLIST_TYPE_OF_MULITU = 3;       // userMsgList type 类型：官方吧个性化推送
    const USERMSGLIST_TYPE_U_OA = 4;            // userMsgList type 类型：普通用户发给官方账号
    const USERMSGLIST_TYPE_OA_U = 5;            // userMsgList type 类型：官方账号回复普通用户

    CONST MAX_UIDS_TBPUSH_COUNT = 300; // tbpush 最大的UID 数目
    // debug测试用户列表
    public static $arr_debug_user_list = array(
        '22626212' => 1,
        '1807825414' => 1,
        '417185216' => 1,        
    );

    // debug测试吧列表
    public static $arr_debug_follow_list = array(
        '13863725' => 1,
    );

    // 压测 mock 吧数据
    public static $STRESS_TEST_MOCK_FORUM = array(
        2488131,
    );
    
    private static $_conf;

    /**
     * @param
     * @return null
     */
    public static function getUiConf() {
        if (self::$_conf) {
            return self::$_conf;
        }
        self::$_conf = Bd_Conf::getConf('/app/newpush/ui_newpush');
        if (false === self::$_conf) {
            Bingo_Log::warning("read conf fail");
            return null;
        }
        return self::$_conf;
    }
}
