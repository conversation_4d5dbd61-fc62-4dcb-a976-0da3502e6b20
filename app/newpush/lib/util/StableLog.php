<?php

/**
 * Class Lib_Util_StableLog
 */
class Lib_Util_StableLog {
    protected static $_buildStableLog = false; //需要打印稳定性日志
    protected static $_stableLogPath = '';
    protected static $_objInstance = null;
    protected static $_arrStableNotice = array();

    /**
     * @return Lib_Util_StableLog|null
     */
    public static function getInstance()
    {
        if (self::$_objInstance) {
            return self::$_objInstance;
        }
        return self::$_objInstance = new self();
    }

    /**
     * Lib_Util_StableLog constructor.
     */
    protected function __construct()
    {
    }

    /**
     * @param $strKey
     * @param $strValue
     */
    public function pushStableNotice($strKey, $strValue)
    {
        self::$_buildStableLog = true;
        self::$_arrStableNotice[$strKey] = $strValue;
        //Bingo_Log::pushNotice($strKey, $strValue);
    }

    /**
     * @param string $strOtherLog
     * @param string $strModule
     * @param string $strFile
     * @param int $intLine
     * @param int $intTraceLevel
     */
    public function buildStableNotice($strOtherLog='', $strModule='', $strFile='', $intLine=0, $intTraceLevel=0)
    {
        if (self::$_buildStableLog) {
            $arrNodes = Bingo_Log::getNoticeNodes();
            $arrNodes = array_merge($arrNodes, self::$_arrStableNotice);
            self::_bd_log($arrNodes);
        }
    }

    /**
     * @param $arrArgsStd
     */
    private function _bd_log($arrArgsStd)
    {
        $argsStd = '';
        foreach ($arrArgsStd as $strKey => $strValue) {
            $argsStd .= "$strKey=$strValue ";
        }
        $argsStd = trim($argsStd);
        $urlkey = intval(Bingo_Http_Request::get('cmd',''));
        $log = '' . NOTICE . ': ' . strftime('%m-%d %H:%M:%S') . ' ' . MODULE . ' * ' . posix_getpid() . ' [logid=' . Bd_Log::genLogID() . ' filename=' . Bd_Log::$current_instance->current_file . ' lineno=' . Bd_Log::$current_instance->current_line . ' errno=' . Bd_Log::$current_instance->current_err_no . ' ' . $argsStd . ' errmsg=' . rawurlencode(Bd_Log::$current_instance->current_err_msg) . " product=tieba subsys=mo_client_im_ui module=". MODULE ." urlkey=".$urlkey." ]" . "\n";
        $strLogPath = ROOT_PATH . "/log/imstable/imstable.log.new.".date('YmdH');
        @mkdir(dirname($strLogPath));
        file_put_contents($strLogPath, $log, FILE_APPEND);
    }

    /**
     *
     */
    public function __destruct()
    {
        self::$_objInstance->buildStableNotice();
    }
}
