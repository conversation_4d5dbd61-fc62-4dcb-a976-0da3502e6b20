<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-05-14 16:16:24
 * @version
 */

class Lib_Util_Redis {

    private static $_cache;

    const CACHE_PID = Lib_Util_Conf::REDIS_INSTANCE_NAME;   // cache的pid
    const SWITCH_OF_CACHE = true;       // cache配置总开关，方便测试
    const PREFIX_ALL_KEY = 'newpush_';  // 所有cache的key的前缀，修改前缀即可失效现有所有cache

    //初始化cache
    /**
     * @return Bingo_Cache_Redis|null
     */
    public static function initCache(){
        if(false === self::SWITCH_OF_CACHE) {
            return null;
        }
        if(self::$_cache){
            return self::$_cache ;
        }
        
        Bingo_Timer::start('redis_init');
        self::$_cache = new Bingo_Cache_Redis(self::CACHE_PID);
        Bingo_Timer::end('redis_init');

        if(!self::$_cache || !self::$_cache->isEnable()){
            Bingo_Log::warning("init Redis cache fail.");
            self::$_cache = null;
            return null;
        }
        return self::$_cache;
    }
    //获取单个cache
    /**
     * @param $strKey
     * @return null
     */
    public static function getCache($strKey){
        if(false === self::SWITCH_OF_CACHE) {
            return null;
        }
        //add your code
        
    }
    //批量获取cache
    /**
     * @param $arrKey
     * @return null
     */
    public static function mgetCache($arrKey){
        if(false === self::SWITCH_OF_CACHE) {
            return null;
        }
        //add your code
    }    
    //删除cache
    /**
     * @param $strKey
     * @return bool
     */
    public static function removeCache($strKey){
        if(false === self::SWITCH_OF_CACHE) {
            return true;
        }
        //add your code
    }
    //设置cache
    /**
     * @param $strKey
     * @param $mixValue
     * @param $intLifeTime
     * @return bool
     */
    public static function addCache($strKey, $mixValue, $intLifeTime){
        if(false === self::SWITCH_OF_CACHE) {
            return true;
        }
        //add your code
    }
}
