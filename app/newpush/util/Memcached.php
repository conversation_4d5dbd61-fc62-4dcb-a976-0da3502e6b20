<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-05-14 16:16:24
 * @version
 */

class Util_Memcached {

    private static $_cache;
        
    //cache的pid
    const CACHE_PID = '';
    
    //cache配置总开关，方便测试
    const SWITCH_OF_CACHE = true;
    
    //所有cache的key的前缀，修改前缀即可失效现有所有cache
    const PREFIX_ALL_KEY = 'newpush_';

    //初始化cache
    public static function initCache(){
        if(false === self::SWITCH_OF_CACHE) {
            return null;
        }
        if(self::$_cache){
            return self::$_cache ;
        }
        
        Bingo_Timer::start('memcached_init');
        self::$_cache = new Bingo_Cache_Memcached(self::CACHE_PID);
        Bingo_Timer::end('memcached_init');

        if(!self::$_cache || !self::$_cache->isEnable()){
            Bingo_Log::warning("init cache fail.");
            self::$_cache = null;
            return null;
        }
        return self::$_cache;
    }
    //获取单个cache
    public static function getCache($strKey){
        if(false === self::SWITCH_OF_CACHE) {
            return null;
        }
        if(!self::$_cache){
            self::initCache();
            if(!self::$_cache->isEnable()) {
                return null;
            }
        }
        $strKey = self::PREFIX_ALL_KEY.$strKey;
        Bingo_Timer::start('memcached_get');
        $mixRes = self::$_cache->get($strKey);
        Bingo_Timer::end('memcached_get');
        return $mixRes;
    }
    //批量获取cache
    public static function mgetCache($arrKey){
        if(false === self::SWITCH_OF_CACHE) {
            return null;
        }
        if(!self::$_cache){
            self::initCache();
            if(!self::$_cache->isEnable()) {
                return null;
            }
        }
        foreach ($arrKey as &$strKey) {
            $strKey = self::PREFIX_ALL_KEY.$strKey;
        }
        Bingo_Timer::start('memcached_mget');
        $mixRes = self::$_cache->multipleGet($arrKey);
        Bingo_Timer::end('memcached_mget');
        return $mixRes;
    }    
    //删除cache
    public static function removeCache($strKey){
        if(false === self::SWITCH_OF_CACHE) {
            return true;
        }
        if(!self::$_cache){
            self::initCache();
            if(!self::$_cache->isEnable()) {
                return false;
            }
        }

        $strKey = self::PREFIX_ALL_KEY.$strKey;
        Bingo_Timer::start('memcached_del');
        $mixRes = self::$_cache->remove(strval($strKey));
        Bingo_Timer::end('memcached_del');
        if($mixRes === CACHE_OK){
            return true;
        }
        else{  
            Bingo_Log::warning('remove cache err no : '.$mixRes." key[$strKey]");
            return false;
        }
    }
    //设置cache
    public static function addCache($strKey, $mixValue, $intLifeTime){
        if(false === self::SWITCH_OF_CACHE) {
            return true;
        }

        if(!self::$_cache){
            self::initCache();
            if(!self::$_cache->isEnable()) {
                return false;
            }
        }
        $strKey = self::PREFIX_ALL_KEY.$strKey;
        $intLifeTime = intval($intLifeTime);
        Bingo_Timer::start('memcached_add');
        $mixRes = self::$_cache->add(strval($strKey), $mixValue, $intLifeTime);
        Bingo_Timer::end('memcached_add');
        if($mixRes === CACHE_OK){
            return true;
        }else{  
            Bingo_Log::warning('add cache err no : '.$mixRes." key[$strKey] val[".
                serialize($mixValue)."] time[$intLifeTime]");
            return false;
        }
    }
}
