<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2017/4/20
 * Time: 18:39
 */
require_once '../stat_routine.php';

/**
 * Class connection_establish_cost
 * 连接建立平均耗时
 */
class connection_establish_cost extends StatRoutine {

    private $_intTotal = 0;
    private $_intTotalCost = 0;

    /**
     * @param $context
     * @param $record
     * @return mixed
     */
    public function process($context, $record) {
        if(!$record['call_succ']) {
            return;
        }
        $this->_intTotal += 1;
        $this->_intTotalCost += $record['call_conn_time'];
    }

    /**
     * @param $context
     * @return mixed
     */
    public function conclude($context) {
        if(!$this->_intTotal) {
            return 0;
        }
        return (int)($this->_intTotalCost / $this->_intTotal);
    }

}
