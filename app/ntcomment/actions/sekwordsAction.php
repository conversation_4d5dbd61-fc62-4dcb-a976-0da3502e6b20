<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/


/**
 * @file getCommentListAction.php
 * <AUTHOR>
 * @date 2016-07-24
 * @brief 异步拉评论
 *  
 **/
class sekwordsAction extends Libs_Client_BaseAction {
	const TOPIC_SE_INDEX = 1;
	const TAG_SE_INDEX = 2;
	const USER_SE_INDEX = 3;
	
	private $_selectReq = array();
	private $_selectRes = array();
	private $_type = self::TOPIC_SE_INDEX;
    private static $_se_app = array(
    	1 => 'topic',
    	2 => 'tag',
    	3 => 'user',
    );
    
    private static $_se_index_word = array(
    	1 => 'topic_title',
    	2 => 'tag_name',
    	3 => 'user_nickname',
    );
    /**
     * @brief
     * @param
     * @return
     */
    public function _getPrivateInfo()
    {
        $arrPrivateInfo['ispv'] = 1;
        //获取贴吧2.0user信息
        $arrPrivateInfo['need_nt_userinfo'] = false;

        $arrPrivateInfo['check_login'] = false;

        $arrPrivateInfo['need_login'] = false;
        $this->_objRequest->addStrategy('check_sign', false);
        $arrPrivateInfo['type'] = $this->_getInput('type', 0);
        $arrPrivateInfo['kw'] = $this->_getInput('kw', 0);
        $arrPrivateInfo['pn'] = $this->_getInput('pn', 1);
        $arrPrivateInfo['rn'] = $this->_getInput('rn', 10);
        return $arrPrivateInfo;
    }
    /**
     * @brief
     * @param
     * @return
     */
    public function _checkPrivate()
    {
        return true;
    }
    public function _execute(){
        try{
            //参数获取
            $user_id  = intval($this->_objRequest->getCommonAttr('user_id'));
            $kw = strval($this->_objRequest->getPrivateAttr('kw'));
            $rn = intval($this->_objRequest->getPrivateAttr('rn'));
            $pn = intval($this->_objRequest->getPrivateAttr('pn'));
            
            $this->_type = intval($this->_objRequest->getPrivateAttr('type'));
            $this->_buildParams();
            
            //UEG
            
            //OTHER策略
            
            $this->_select();
            
            $this->_buildOutput();
            $has_more = true;
            $all_num = isset($this->_selectRes['all_num']) ? intval($this->_selectRes['all_num']) : 0; 
            if ($all_num <= $pn*$rn) {
            	$has_more = false;
            }
            $data = array(
            	'list' => isset($this->_selectRes['rows']) ? $this->_selectRes['rows'] : array(),
            	'page' => array(
            		'has_more'   => $has_more,
            		'current_pn' => $pn,
            	),
            );
                        
            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_SUCCESS), data);
            
        } catch(Libs_Exception $e) {
            $errno = $e->getCode();
            $errmsg = $e->getMessage();
            Bingo_Log::warning('errno='.$e->getCode().' msg='.$e->getMessage());

            $this->_jsonRet($errno, $errmsg);
        }
    }
    
	/**
     * @param $arrList
     * @param $arrUsers
     * @return
     * @throws Libs_Exception
     */
    private function _getUserDetailInfos($arrList)
    {
        if(empty($arrList)) {
            return array();
        }

        $arrUserIds = array_unique(array_column($arrList, 'user_id'));
        $arrParams = array(
            'user_ids' => $arrUserIds,
        );
        $arrRet = Tieba_Service::call('ntuser', 'getUserInfos', $arrParams, 'get');
        if($arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call ntuser::getUserInfos fail, input=" . serialize($arrParams) . ',ret='.serialize($arrRet));
            throw new Libs_Exception('user service error', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return $arrRet['data']['user_infos'];
    }
    /**
     * @param 
     * @return
     * @throws Libs_Exception
     */
    private function _buildParams( $kw, $pn, $rn) {
    	$this->_selectReq = array(
    		self::$_se_index_word[$this->_type] => strval($kw),
    		'page_no'  => intval($pn)-1,
    		'list_num' => intval($rn),
    	);
    }
    /**
     * @param 
     * @return
     * @throws Libs_Exception
     */
    private function _select() {
    	
    	$se = Bd_RalRpc::create('Se', array('pid'=>'wefans', 'app'=> self::$_se_app[$this->_type]));
		if (!empty($this->_selectReq)) {
			$res = $se->select($this->_selectReq);
			if (null == $res || $res['err_no'] != Tieba_Errcode::ERR_SUCCESS) {
			    Bingo_Log::warning("insert error,app:[tag],input:" . serialize($this->_selectReq) . " error_info: " . serialize($res) . "---" .  $se->get_error());
			    return false;
			}
			$this->_selectRes = $res;
		}
		return true;
    }
    /**
     * @param 
     * @return
     * @throws Libs_Exception
     */
	private function _buildOutput() {
    	if ($this->_type == self::TAG_SE_INDEX) {
    		//Tag ,拉回复数，关注数
    		$this->_buildTagOutput();
    		
    	} elseif ($this->_type == self::USER_SE_INDEX) {
    		//拉用户粉丝数
    		$this->_buildUserOutput();
    	} else {
    		//拉回复数，关注数
    		$this->_buildTopicOutput();
    	}
    }
    /**
     * @param 
     * @return
     * @throws Libs_Exception
     */
    private function _getTagFollowNum() {
    	$arrRet = array();
    	$arrTagIds = array_unique(array_column($this->_selectRes, 'tag_id'));
    	if (!empty($arrTagIds)) {
	    	$arrRet = Tieba_Service::call('nttag', 'mgetTagsAttrs', $arrTagIds, 'get');
	        if($arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
	            Bingo_Log::warning("call mgetTagsAttrs fail, input=" . serialize($arrTagIds) . ',ret='.serialize($arrRet));
	            return false;
	        }
    		
    	}
    	return $arrRet;
    }
    /**
     * @param 
     * @return
     * @throws Libs_Exception
     */
    private function _getTopicFollowNum() {
    	$arrRet = array();
    	$arrTopicIds = array_unique(array_column($this->_selectRes, 'topic_id'));
    	if (!empty($arrTopicIds)) {
    		$arrRet = Tieba_Service::call('nttopic', 'mgetTopicInfo', $arrTopicIds, 'get');
	        if($arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
	            Bingo_Log::warning("call mgetTopicInfo fail, input=" . serialize($arrTopicIds) . ',ret='.serialize($arrRet));
	            return false;
	        }
    	}
    	return $arrRet;
    }
    /**
     * @param 
     * @return
     * @throws Libs_Exception
     */
	private function _getUserFollowNum() {
    	$arrRet = array();
    	$arrUserIds = array_unique(array_column($this->_selectRes, 'user_id'));
    	if (!empty($arrUserIds)) {
    		$arrRet = Tieba_Service::call('ntuser', 'mgetUserExtAttr', $arrUserIds, 'get');
	        if($arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
	            Bingo_Log::warning("call mgetUserExtAttr fail, input=" . serialize($arrUserIds) . ',ret='.serialize($arrRet));
	            return false;
	        }
    	}
    	return $arrRet;
    }
    /**
     * @param 
     * @return
     * @throws Libs_Exception
     */
    private function _buildTopicOutput() {
    	$arrRes = $this->_getTagFollowNum();
    	$topics_follow_info = isset($arrRes['data']) ?  $arrRes['data'] : array();
    	if (!empty($topics_follow_info) && isset($this->_selectRes['rows'])) {
    			
    		foreach ($this->_selectRes['rows'] as $key => $value) {
    			$topic_id = intval($value['topic_id']);
    			if ($topic_id <= 0) {
    				continue;
    			}
   				if (isset($topics_follow_info[$topic_id])) {
    				$this->_selectRes['rows'][$key]['topic_reply_num']  = intval($topics_follow_info[$topic_id]['reply_num']);
    				$this->_selectRes['rows'][$key]['topic_follow_num'] = intval($topics_follow_info[$topic_id]['follow_num']);
    			}
    		}
    	}
    		
    	return true;
    }
    /**
     * @param 
     * @return
     * @throws Libs_Exception
     */
    private function _buildTagOutput() {
    	$arrRes = $this->_getTagFollowNum();
    	$tags_follow_info = isset($arrRes['data']['tag']) ?  $arrRes['data']['tag'] : array();
    	if (!empty($tags_follow_info) && isset($this->_selectRes['rows'])) {
    		
    		foreach ($this->_selectRes['rows'] as $key => $value) {
    			$tag_id = intval($value['tag_id']);
    			if ($tag_id <= 0) {
    				continue;
    			}
    			if (isset($tags_follow_info[$tag_id])) {
    				$this->_selectRes['rows'][$key]['tag_reply_num']  = $tags_follow_info[$tag_id]['reply_num'];
    				$this->_selectRes['rows'][$key]['tag_topic_num']  = $tags_follow_info[$tag_id]['topic_num'];
    				$this->_selectRes['rows'][$key]['tag_follow_num'] = $tags_follow_info[$tag_id]['follow_num'];
    			}
    		}
    	}
    	return true;
    }
    
	/**
     * @param 
     * @return
     * @throws Libs_Exception
     */
    private function _buildUserOutput() {
    	$arrRes = $this->_getTagFollowNum();
    	$users_follow_info = isset($arrRes['data']['tag']) ?  $arrRes['data']['tag'] : array();
    	if (!empty($users_follow_info) && isset($this->_selectRes['rows'])) {
    		
    		foreach ($this->_selectRes['rows'] as $key => $value) {
    			$user_id = intval($value['user_id']);
    			if ($user_id <= 0) {
    				continue;
    			}
    			if (isset($users_follow_info[$user_id])) {
    				$this->_selectRes['rows'][$key]['user_fans']  = $users_follow_info[$user_id]['fans_count'];
    			}
    		}
    	}
    	return true;
    }
}
