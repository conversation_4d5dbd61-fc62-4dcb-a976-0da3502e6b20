<?php
/***************************************************************************
 *
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file   Model.php
 * <AUTHOR>
 * @date   2016/04/05 14:15:16
 * @brief  db访问接口
 *
 **/


define('DB_DEBUG', 0);

// 需要兼容原来的接口嘛
class  Dl_Db_Model{
    const DATABASE_NAME = "forum_tbvip";
    private static $_mysql_operator_white_list = array("+=","-=","|=","&=",">","<",">=","<=","in","like");

    protected $error = null;
    protected $errno = null;
    protected $data  = null;
    protected $db    = null;
    protected $table = null;
    protected $db_name = null;

    // 这些静态逻辑也可以分离出来，单独的一个类
    //abstract function getModel($db_name = NULL,$db_table = NULL) ;

    /**
     * Dl_Db_Model constructor.
     * @param null $model
     * @param null $dbname
     */
    public function __construct($model=null,$dbname=null) {
        $this->table = $model;
        $this->db_name = is_null($dbname) ? Dl_Db_Model::DATABASE_NAME : $dbname;

        $this->getObjDB($this->db_name,$charset='utf8');



    }

    /**
     * @brief  插入数据前的回调方法
     * @param  $arrInput
     * @return bool
     */
    protected function _before_insert($arrInput) {
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    protected function _after_insert($arrInput) {
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    protected function _before_select($arrInput) {
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    protected function _after_select($arrInput) {
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    protected function _before_update($arrInput) {
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    protected function _after_update($arrInput) {
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    protected function _before_delete($arrInput) {
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    protected function _after_delete($arrInput) {
        return true;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function baseInsert($arrInput) {
        if ( false === $this->_before_insert($arrInput)) {
            return $this->errRet();
        }

        if(!isset($arrInput['field']) || !is_array($arrInput['field'])){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (isset($arrInput['table']))  {
            $this->table = $arrInput['table'];
        }

        if(isset($arrInput['db'])){
            $db = $arrInput['db'];
        }else{
            $db = $this->getObjDB();
        }
        if(is_null($db)){
            return $this->errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $fields = $arrInput['field'];
        $strOnDup = null;
        if(isset($arrInput['onDup'])&&is_array($arrInput['onDup'])){
            foreach($arrInput['onDup'] as $key=>$value){
                if($strOnDup !== null){
                    $strOnDup .= ",";
                }
                $key = mysql_escape_string(trim($key));
                if (is_int($value)){
                    $strOnDup .= "$key=$value ";
                }
                else if (is_string($value)){
                    $value = mysql_escape_string(trim($value));
                    $strOnDup .= "$key='$value' ";
                }else if (is_array($value)){
                    $val = $value['val'];
                    $opt = in_array(strval($value['opt']),self::$_mysql_operator_white_list) ? strval($value['opt']) : '=';
                    $quotes = isset($value['quotes']) ?  0 : 1;

                    if(is_int($val)){
                        $strOnDup .= "$key $opt $val ";
                    }else{
                        $val = mysql_escape_string(trim($val));
                        if ($quotes === 0){
                            $strOnDup .= "$key $opt $val ";
                        }else{
                            $strOnDup .= "$key $opt '$val' ";
                        }
                    }

                }
            }
        }
        $ret = $db->insert($this->table, $fields,null,$strOnDup);
        if(DB_DEBUG) {
            Bingo_Log::warning('sql:'.$db->getLastSQL());
        }
        if($ret <= 0){
            Bingo_Log::warning("[output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");

            return $this->errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array('mysql_errno'=>$db->errno()));
        }
        $insertId = $db->getInsertID();
        //$this->errRet(Tieba_Errcode::ERR_SUCCESS,$insertId);
        $this->errno = Tieba_Errcode::ERR_SUCCESS;
        $this->data[] = $insertId;
        $arrInput['insert_id'] = $insertId;
        $arrInput['table'] = $this->table ;
        $this->_after_insert($arrInput);

        return $this->errRet(Tieba_Errcode::ERR_SUCCESS, $insertId);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function baseSelect($arrInput) {

        if ( false === $this->_before_select($arrInput)) {
            return $this->errRet();
        }

        if( !isset($arrInput['field']) || !is_array($arrInput['field'])){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (isset($arrInput['table']))  {
            $this->table = $arrInput['table'];
        }

        $arrFields = $arrInput['field'];
        $str_cond = null;
        if(isset($arrInput['cond'])&&is_array($arrInput['cond'])){
            foreach($arrInput['cond'] as $key=>$value){
                if($str_cond !== null){
                    $str_cond .= " and ";
                }

                $key = mysql_escape_string(trim($key));
                if (is_int($value)){
                    $str_cond .= "$key=$value ";
                }
                else if (is_string($value)){
                    $value = mysql_escape_string(trim($value));
                    $str_cond .= "$key='$value' ";
                }else if (is_array($value)){
                    $val = $value['val'];
                    $opt = in_array(strval($value['opt']),self::$_mysql_operator_white_list) ? strval($value['opt']) : '=';
                    $quotes = isset($value['quotes']) ?  0 : 1;

                    if(is_int($val)){
                        $str_cond .= "$key $opt $val ";
                    }else{
                        $val = mysql_escape_string(trim($val));
                        if ($quotes === 0){
                            $str_cond .= "$key $opt $val ";
                        }else{
                            $str_cond .= "$key $opt '$val' ";
                        }
                    }

                }

            }
        }
        if(isset($arrInput['cond'])&&is_string($arrInput['cond'])) {
            $str_cond = $arrInput['cond'];
        }
        $str_append = null;
        if(isset($arrInput['append'])){
            $str_append = $arrInput['append'];
        }

        if(isset($arrInput['db'])){
            $db = $arrInput['db'];
        }else{
            $db = $this->getObjDB();
        }
        if(is_null($db)){
            return $this->errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $ret = $db->select($this->table, $arrFields, $str_cond, null, $str_append);
        //Bingo_Log::warning('sql:'.$db->getLastSQL());
        if(DB_DEBUG) {
            Bingo_Log::warning('sql:'.$db->getLastSQL());
        }
        if($ret===false){
            Bingo_Log::warning("[output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return $this->errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array('mysql_errno'=>$db->errno()));
        }
        $this->errno = Tieba_Errcode::ERR_SUCCESS;

        $this->_after_select($arrInput);
        return $this->errRet(Tieba_Errcode::ERR_SUCCESS,$ret);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function baseUpdate($arrInput) {
        if ( false === $this->_before_update($arrInput)) {
            return $this->errRet();
        }
        if(!isset($arrInput['field']) || !is_array($arrInput['field'])){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (isset($arrInput['table']))  {
            $this->table = $arrInput['table'];
        }
        $str_field = null;
        foreach($arrInput['field'] as $key=>$value){
            if($str_field !== null){
                $str_field .= ",";
            }
            $key = mysql_escape_string(trim($key));
            if (is_int($value)){
                $str_field .= "$key=$value ";
            }
            else if (is_string($value)){
                $value = mysql_escape_string(trim($value));
                $str_field .= "$key='$value' ";
            }else if (is_array($value)){
                $val = $value['val'];
                $opt = in_array(strval($value['opt']),self::$_mysql_operator_white_list) ? strval($value['opt']) : '=';
                $quotes = isset($value['quotes']) ?  0 : 1; //默认有引号

                if(is_int($val)){
                    $str_field .= "$key $opt $val ";
                }else{
                    $val = mysql_escape_string(trim($val));
                    if ($quotes === 0){
                        $str_field .= "$key $opt $val ";
                    }else{
                        $str_field .= "$key $opt '$val' ";
                    }
                }

            }
        }
        $str_cond = null;
        if(isset($arrInput['cond'])&&is_array($arrInput['cond'])){
            foreach($arrInput['cond'] as $key=>$value){
                if($str_cond !== null){
                    $str_cond .= " and ";
                }
                $key = mysql_escape_string(trim($key));
                if (is_int($value)){
                    $str_cond .= "$key=$value ";
                }
                else if (is_string($value)){
                    $value = mysql_escape_string(trim($value));
                    $str_cond .= "$key='$value' ";
                }else if (is_array($value)){
                    $val = $value['val'];
                    $opt = in_array(strval($value['opt']),self::$_mysql_operator_white_list) ? strval($value['opt']) : '=';
                    $quotes = isset($value['quotes']) ?  0 : 1;

                    if(is_int($val)){
                        $str_cond .= "$key $opt $val ";
                    }else{
                        $val = mysql_escape_string(trim($val));
                        if ($quotes === 0){
                            $str_cond .= "$key $opt $val ";
                        }else{
                            $str_cond .= "$key $opt '$val' ";
                        }
                    }

                }
            }
        }

        $str_append = null;
        if(isset($arrInput['append'])){
            $str_append = $arrInput['append'];
        }

        if(isset($arrInput['db'])){
            $db = $arrInput['db'];
        }else{
            $db = $this->getObjDB();
        }
        if(is_null($db)){
            return $this->errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $ret = $db->update($this->table, $str_field, $str_cond);
        if(DB_DEBUG) {
            Bingo_Log::warning('sql:'.$db->getLastSQL());
        }
        if($ret===false){
            Bingo_Log::warning("[output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return $this->errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array('mysql_errno'=>$db->errno()));
        }
        $affectedRows = $db->getAffectedRows();

        $this->errno = Tieba_Errcode::ERR_SUCCESS;
        $this->data[] = $affectedRows;

        $this->_after_update($arrInput);

        return $this->errRet(Tieba_Errcode::ERR_SUCCESS,$affectedRows);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function baseDelete($arrInput) {

        if ( false === $this->_before_delete($arrInput)) {
            return $this->errRet();
        }

        if (isset($arrInput['table']))  {
            $this->table = $arrInput['table'];
        }

        $str_cond = null;
        if(isset($arrInput['cond'])&&is_array($arrInput['cond'])){
            foreach($arrInput['cond'] as $key=>$value){
                if($str_cond !== null){
                    $str_cond .= " and ";
                }

                $key = mysql_escape_string(trim($key));
                if (is_int($value)){
                    $str_cond .= "$key=$value ";
                }
                else if (is_string($value)){
                    $value = mysql_escape_string(trim($value));
                    $str_cond .= "$key='$value' ";
                }else if (is_array($value)){
                    $val = $value['val'];
                    $opt = in_array(strval($value['opt']),self::$_mysql_operator_white_list) ? strval($value['opt']) : '=';
                    $quotes = isset($value['quotes']) ?  0 : 1;

                    if(is_int($val)){
                        $str_cond .= "$key $opt $val ";
                    }else{
                        $val = mysql_escape_string(trim($val));
                        if ($quotes === 0){
                            $str_cond .= "$key $opt $val ";
                        }else{
                            $str_cond .= "$key $opt '$val' ";
                        }
                    }

                }

            }
        }

        $str_append = null;
        if(isset($arrInput['append'])){
            $str_append = $arrInput['append'];
        }

        if(isset($arrInput['db'])){
            $db = $arrInput['db'];
        }else{
            $db = $this->getObjDB();
        }
        if(is_null($db)){
            return $this->errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $ret = $db->delete($this->table, $str_cond, null, $str_append);
        if(DB_DEBUG) {
            Bingo_Log::warning('sql:'.$db->getLastSQL());
        }
        if($ret===false){
            Bingo_Log::warning("[output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return $this->errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array('mysql_errno'=>$db->errno()));
        }
        $this->errno = Tieba_Errcode::ERR_SUCCESS;

        $this->_after_delete($arrInput);

        return $this->errRet(Tieba_Errcode::ERR_SUCCESS,$ret);
    }

    /**
     * @param null $errno
     * @param string $data
     * @param null $error
     * @return array
     */
    protected function errRet($errno=null, $data = "", $error=null){
        if (!is_null($errno)) {
            $this->errno = $errno;
        }

        if ( is_null($error) ) {
            if ( is_null($this->error)) {
                $this->error = Tieba_Error::getErrmsg($this->errno);
            }
        }
        else {
            $this->error = $error;
        }

        $arrRet = array(
            'errno' => $this->errno,
            'errmsg' => $this->error,
        );
        if($data !== ""){
            $arrRet['data'] = $data;
        }
        Bingo_Log::pushNotice("errno",$this->errno);
        return $arrRet;
    }

    /**
     * @param null $dbname
     * @param string $charset
     * @return null
     */
    public function getObjDB($dbname=null,$charset='utf8'){
        if(is_null($dbname)){
            //$dbname = self::DATABASE_NAME;
            $dbname = $this->db_name;
        }
        $objTbMysql = Tieba_Mysql::getDB($dbname);
        $this->db = $objTbMysql;
        if($objTbMysql && $objTbMysql->isConnected()) {
            $objTbMysql->charset($charset);
            return $objTbMysql;
        } else {
            Bingo_Log::warning("db connect fail.");
            return null;
        }
    }

    /**
     * @param $str
     * @return string
     */
    private static function addQuote($str){
        return "'$str'";
    }

    /**
     * @return null
     */
    public function getMysqlObj(){
        return $this->db;
    }

    /**
     * @param $dbFunc
     * @param $arrInput
     * @return mixed
     */
    public function __call($dbFunc, $arrInput){
        //Bingo_Log::warning("$dbFunc, ".serialize($arrInput));
        if (empty($arrInput)) {
            return $this->db->$dbFunc();
        }
        else {
            return $this->db->$dbFunc($arrInput[0]);
        }
    }
}
