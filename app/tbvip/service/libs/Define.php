<?php
/**
 * @file Define.php
 * <AUTHOR>
 * @date 2016/04/05 16:40:16
 * @brief const defines
 *
 **/
class Service_Libs_Define {
    const TBVIP_USER_NAME  = 'tb_vip';
    // status of user
    const TBVIP_USER_STAT_INIT          = 0; //未审核
    const TBVIP_USER_STAT_SUCC          = 1; //审核通过 
    const TBVIP_USER_STAT_REFUSE        = 2; //审核未通过
    const TBVIP_USER_STAT_OFFLINE       = 3; //撤销资格
    // type of user
    const TBVIP_USER_TYPE_ORANGE_V  = 1;  //橙V
    const TBVIP_USER_TYPE_BLACK_V   = 2;  //黑V
    //property of user
    const TBVIP_USER_PROPERT_NORMAL = 0;  //普通
    const TBVIP_USER_PROPERT_WHITE  = 1;  //白名单
    const TBVIP_USER_PROPERT_BLACK  = 2;  //黑名单
    // status of category
    const TBVIP_CATEGORY_ONLINE   = 1;
    const TBVIP_CATEGORY_OFFLINE  = 0;

    // status of admin
    const TBVIP_ADMIN_ONLINE  = 1;
    const TBVIP_ADMIN_OFFLINE = 0;
    // status of literary
    const TBVIP_LITERARY_STAT_INIT          = 0; //未审核
    const TBVIP_LITERARY_STAT_SUCC          = 1; //审核通过 
    const TBVIP_LITERARY_STAT_REFUSE        = 2; //审核未通过
    const TBVIP_LITERARY_STAT_OFFLINE       = 3; //撤销资格
    //type of admin
    const TBVIP_ADMIN_NORMAL  = 1; //普通管理员
    const TBVIP_ADMIN_SUPER   = 9; //超级管理员
    const TBVIP_ADMIN_ALL_PRIV= 'all';
    //超级管理员
    const TBVIP_ADMIN_SUPER_UID   = 123223; //初始化超级管理员

    //大V类型
    const LITERARY_CATEGORY   = 2; //初始化超级管理员

    public static $activity_url   = 'http://tieba.baidu.com/mo/q/tbvip/index';
    //大V icon
    protected static $arrIcon = array(
        self::TBVIP_USER_TYPE_ORANGE_V => 'http://tb1.bdstatic.com/tb/cms/ngmis/file_1460603513234.png',
        self::TBVIP_USER_TYPE_BLACK_V  => 'http://tb1.bdstatic.com/tb/cms/ngmis/file_1460603404361.png',
    );
    /**
    * @brief 获取v图标
    * @param type
    * @return str
    **/
    public static function getIconUrl($intType) {
        return self::$arrIcon[$intType];
    }
}