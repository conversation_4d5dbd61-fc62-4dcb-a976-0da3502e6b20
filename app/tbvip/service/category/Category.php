<?php
/**
 * @file   Category.php
 * <AUTHOR>
 * @date   2016/04/05 19:32:16
 * @brief  大V基本信息Service
 *
 **/

class Service_Category_Category extends Service_Libs_Base {
    /**
     * @breif 大V分类表
     * 0 : 线下
     * 1 : 线上
     */
    /**
    * @brief 添加大V分类条目 只有超级管理员才可以添加
    * @param array
    * @return array
    **/
    public static function addCategory($arrInput) {
        $arrCheckParam = array('name','followed','thread_num','remark','operator','op_uid');
        if (!Service_Libs_Util::checkAllSet($arrInput, $arrCheckParam)) {
            Bingo_Log::warning('param error:'.serialize($arrInput)); 
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intOpUid= intval($arrInput['op_uid']);
        $strName = $arrInput['name'];
        $intFollowed = intval($arrInput['followed']);
        $intThreadNum= intval($arrInput['thread_num']);

        if ($intFollowed <=0 || $intThreadNum<=0 || $intOpUid<=0 || empty($strName)) {
            Bingo_Log::warning('param error:'.serialize($arrInput)); 
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        //操作用户权限判断 必须为超级管理员
        $strPriv  = Service_Libs_User::getPrivilegeByUid($intOpUid);
        if ($strPriv != Service_Libs_Define::TBVIP_ADMIN_ALL_PRIV) {
            Bingo_Log::warning('no privilege:'.serialize($arrInput)); 
            return self::_errRet(Tieba_Errcode::ERR_NZYQ_NOT_MANAGER);
        }
        $arrParam = array(
            'name' => $strName,
        );
        $arrRet = self::getCategoryByCond($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] || $arrRet===false) {
            Bingo_Log::warning(sprintf("Call getCategoryByCond::select failed.[input=%s] [output=%s]",
                serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        //已存在同名分类
        if (!empty($arrRet['data'])) {
            Bingo_Log::warning($strName . ' already exists');
            return self::_errRet(Tieba_Errcode::ERR_POST_FRS_CLASS_EXIST);
        }
        $arrFields = array(
            'name'      => $strName,
            'followed'  => $intFollowed,
            'thread_num'=> $intThreadNum,
            'in_order'  => isset($arrInput['in_order']) ? intval($arrInput['in_order']) : 0,
            'remark'      => $arrInput['remark'],
            'create_time' => time(),
            'update_time' => time(),
            'operator'    => $arrInput['operator'],
        );
        $arrParam = array(
            'field' => $arrFields,
        );
        $arrRet = Dl_Db_Category::insert($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Call Dl_Db_Category::insert failed.[input=%s] [output=%s]", serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['data']);
    }
    /**
    * @brief 编辑大V分类条目 超级管理员
    * @param array
    * @return array
    **/
    public static function editCategory($arrInput) {
        $intId = intval($arrInput['id']);
        $intOpUid  = intval($arrInput['op_uid']);
        if ($intId<=0 || $intOpUid<=0 || empty($arrInput['operator'])){
            Bingo_Log::warning(sprintf("Param error. [input=%s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        //操作用户权限判断 必须为超级管理员
        $strPriv  = Service_Libs_User::getPrivilegeByUid($intOpUid);
        if ($strPriv != Service_Libs_Define::TBVIP_ADMIN_ALL_PRIV) {
            Bingo_Log::warning('no privilege:'.serialize($arrInput)); 
            return self::_errRet(Tieba_Errcode::ERR_NZYQ_NOT_MANAGER);
        }
        $arrParam = array(
            'cond'  => array(
                'id' => $intId,
            ),
            'field' => array(),
        );
        $arrField = Dl_Db_Category::filterFields($arrInput);
        unset($arrField['id']);
        if (empty($arrField)) {
            Bingo_Log::warning(sprintf("filter field error. [input=%s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrField['update_time'] = time();
        $arrParam['field'] = $arrField;
        $arrRet = Dl_Db_Category::update($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Call Dl_Db_Category::update failed.[input=%s] [output=%s]",
                serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['data']);
    }
    /**
     * @brief  获取分类基本信息
     * @param  $arrInput
     * @return array
     */
    public static function getCategoryById($arrInput) {
        $intId = intval($arrInput['id']);
        if ($intId <= 0) {
            Bingo_Log::warning(sprintf("Param error. [input=%s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrParam = array(
            'cond'  => array(
                'id' => $intId,
            ),
        );
        if (isset($arrInput['status'])) {
            $arrParam['cond']['status'] = intval($arrInput);
        }
        $arrRet = Dl_Db_Category::select($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Call Dl_Db_User::select failed.[input=%s] [output=%s]",
                serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['data'][0]);
    }
    /**
     * @brief  获取分类基本信息
     * @param  $arrInput
     * @return array
     */
    public static function getCategoryByIds($arrInput) {
        $arrId = $arrInput['id'];
        if (!is_array($arrId) || empty($arrId)) {
            Bingo_Log::warning(sprintf("Param error. [input=%s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $strCond  = " id in (" . implode(",", $arrId) . ")";
        if (isset($arrInput['status'])) {
            $strCond .= " and status=".intval($arrInput['status']);
        }

        $arrParam = array(
            'cond'  => $strCond,
        );
        
        $arrRet = Dl_Db_Category::select($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Call Dl_Db_Category::select failed.[input=%s] [output=%s]",
                serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['data']);
    }

    /**
     * @brief  批量category_ids获取大V基本信息
     * @param  $arrInput
     * @return array
     */
    public static function mgetCategory($arrInput) {
        $arrCategory = $arrInput['ids'];
        if (!is_array($arrCategory) || empty($arrCategory)) {
            Bingo_Log::warning(sprintf("Param error. [input=%s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $strCond  = " id in (" . implode(",", $arrCategory) . ")";
        if (isset($arrInput['order'])) {
            $strCond .= " and order by ".$arrInput['order'];
        }

        $arrParam = array(
            'cond'  => $strCond,
        );
        $arrRet = Dl_Db_Category::select($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Call Dl_Db_Category::select failed.[input=%s] [output=%s]",
                serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['data']);
    }

    /**
     * @brief  获取所有大V分类列表不包括分页
     * @param  null
     * @return array
     */
    public static function getCategoryListAll($arrParms) {
        $arrParam['append'] = "ORDER BY in_order DESC";
        if(isset($arrParms["status"])){
            $arrParam ['cond'] = "status=".$arrParms["status"];
        }
        $arrRet = Dl_Db_Category::select($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Call Dl_Db_Category::select failed.[input=%s] [output=%s]",
                Bingo_String::array2json($arrParam), Bingo_String::array2json($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrList = $arrRet['data'];
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrList);
    }

    /**
     * @brief 获取category id
     * @param void
     * @return array
     **/
    public static function getCategoryIDWithoutEmpty(){
        $arrParam['append'] = "group by category";
        $arrParam['field'] = array(
            "category",
        );

        $arrParam['cond'] = array(
            "status" => 1,
        );

        $arrRet = Dl_Db_User::select($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Call Dl_Db_User::select failed.[input=%s] [output=%s]",
                Bingo_String::array2json($arrParam), Bingo_String::array2json($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        if(empty($arrRet["data"]) || !is_array($arrRet["data"])){
            return  self::_errRet(Tieba_Errcode::ERR_SUCCESS, array());
        }
        return  self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet["data"]);
    }
    /**
     * @brief  获取所有大V分类列表不包括分页
     * @param  null
     * @return array
     */
    public static function getCategoryListWithoutEmpty() {
        $arrParam['append'] = "group by category";
        $arrParam['field'] = array(
            "category",
        );
        $arrParam['cond'] = array(
            "status" => 1,
        );
        $arrRet = Dl_Db_User::select($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Call Dl_Db_User::select failed.[input=%s] [output=%s]",
                Bingo_String::array2json($arrParam), Bingo_String::array2json($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        if(empty($arrRet["data"]) || !is_array($arrRet["data"])){
            return  self::_errRet(Tieba_Errcode::ERR_SUCCESS, array());
        }
        $arrIDs = array();
        foreach($arrRet["data"] as $value){
            $arrIDs[] = intval($value["category"]);
        }

        $arrParam2["cond"] = " id in (" . implode(",", $arrIDs) . ")";
        $arrParam2['append'] = "ORDER BY in_order DESC ";

        $arrRet = Dl_Db_Category::select($arrParam2);
        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Call Dl_Db_Category::select failed.[input=%s] [output=%s]",
                Bingo_String::array2json($arrParam), Bingo_String::array2json($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrList = $arrRet['data'];
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrList);
    }
    
    /**
     * @brief  获取大V分类列表
     * @param  $arrInput
     * @return array
     */
    public static function getCategoryList($arrInput) {
        $intPn = intval($arrInput['pn']);
        $intRn = intval($arrInput['rn']);
        $intPn = $intPn <= 0 ? 1 : $intPn;
        $intRn = $intRn <= 0 ? 20 : $intRn;
        $intOffset = ($intPn - 1) * $intRn;
        
        $arrParam  = array();
        if (isset($arrInput['status'])) {
            $arrParam['cond']['status'] = intval($arrInput);
        }
        $arrParam['append'] = "ORDER BY in_order DESC limit $intOffset, $intRn";

        $arrRet = Dl_Db_Category::select($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Call Dl_Db_Category::select failed.[input=%s] [output=%s]",
                serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        
        $arrList = $arrRet['data'];
        $arrRet  = Dl_Db_Category::getTotalCount($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Query category count failed. [input=%s] [output=%s]",
                serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $intCount = $arrRet['data'];
        $arrOutput = array(
            'list' => $arrList,
            'page' => array(
                'current_pn'  => $intPn,
                'total_count' => $intCount,
                'total_pn'    => ceil($intCount / $intRn),
            ),
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    /**
     * @brief  判断用户是否达到该分类申请条件
     * @param  $arrInput
     * @return array
     */
    public static function checkCategory($arrInput) {
        $arrOutput  = array(
            'result' => 0,
        );
        $intUserId  = $arrInput['user_id'];
        $intCategory= $arrInput['category'];
        if ($intUserId <=0 || $intCategory <=0) {
            Bingo_Log::warning(sprintf("Param error. [input=%s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrParam = array(
            'id' => $intCategory,
            'status' => Service_Libs_Define::TBVIP_CATEGORY_ONLINE,
        );
        $arrRet   = self::getCategoryById($arrParam);
        if ($arrRet===false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("getCategoryById failed");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrData = $arrRet['data'];
        if (empty($arrData)) {
            Bingo_Log::warning('category not exist[input]' . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $intThreadNum = intval($arrData['thread_num']);
        $intFollowed  = intval($arrData['followed']);
        //判断粉丝数
        $arrFollowed  = Service_Libs_User::getFollowAndFollowedByUid($intUserId);
        if (empty($arrFollowed)) {
            Bingo_Log::warning('getFollowAndFollowedByUid error:'.$intUserId);
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
        } 
        if ($intFollowed > intval($arrFollowed['fans']['total_count'])) {
            Bingo_Log::warning('fans is not enough:'.$intUserId);
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
        }
        //判断发回贴数
        $arrParam = array(
            'user_id' => $intUserId,
            'offset'  => 0,
            'res_num'    => $intThreadNum+1,
            'delete_type'=>0,
            'order_type' => 1,
            'begin_time' => strtotime('-30 days'),
            'end_time'   => time(),
        );
        $arrPost   = Service_Libs_User::queryUserPost($arrParam);
        if (!is_array($arrPost)) {
            Bingo_Log::warning('queryUserPost error:[input]'.serialize($arrParam).'[output]'.serialize($arrPost));
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
        }
        if ($intThreadNum > count($arrPost)) {
            Bingo_Log::warning('thread_num is not enough:'.$intUserId);
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
        }
        $arrOutput['result'] = 1;
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }
    /**
     * @brief  获取分类基本信息
     * @param  $arrInput
     * @return array
     */
    public static function getCategoryByCond($arrInput) {
        $arrCond = Dl_Db_Category::filterFields($arrInput);
        if (empty($arrCond)) {
            Bingo_Log::warning(sprintf("Param error. [input=%s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrParam = array(
            'cond'  => $arrCond,
        );
        $arrRet = Dl_Db_Category::select($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Call Dl_Db_Category::select failed.[input=%s] [output=%s]",
                serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['data']);
    }
}