<?php
/**
 * @file   Literary.php
 * <AUTHOR>
 * @date   2016/05/27 16:04:16
 * @brief  贴吧文学认证作者作品Service
 *
 **/
class Service_Literary_Literary extends Service_Libs_Base {


    const LITERARY_MSG_TITLE = "贴吧文学作品审核通知";

    /**
     * @brief  根据user_id查询作品
     * @param  $arrInput
     * @return array
     */
    public static function getLiteraryByUser($arrInput) {
        $intPn = intval($arrInput['pn']);
        $intRn = intval($arrInput['rn']);
        $intPn = $intPn <= 0 ? 1 : $intPn;
        $intRn = $intRn <= 0 ? 20 : $intRn;
        $intOffset = ($intPn - 1) * $intRn;
        $intUserId = intval($arrInput['user_id']);
        if ($intUserId <= 0) {
            Bingo_Log::warning(sprintf("Param error. [input=%s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrParam = array(
            'cond'  => array(
                'user_id' => $intUserId,
            ),
        );
        if (isset($arrInput['status'])) {
            $arrParam['cond']['status'] = intval($arrInput['status']);
        }
        $arrParam['append'] = "ORDER BY create_time DESC limit $intOffset, $intRn";
        $arrRet = Dl_Db_Literary::select($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Call Dl_Db_Literary::select failed.[input=%s] [output=%s]", serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrList = $arrRet['data'];
        $arrRet  = Dl_Db_Literary::getTotalCount($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Query Dl_Db_Literary count failed. [input=%s] [output=%s]",
                serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $intCount = $arrRet['data'];
        $arrOutput = array(
            'list' => $arrList,
            'page' => array(
                'current_pn'  => $intPn,
                'total_count' => $intCount,
                'total_pn'    => ceil($intCount / $intRn),
            ),
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    /**
    * @brief 添加作品
    * @param array
    * @return array
    **/
    public static function addLiterary($arrInput) {
        $arrCheckParam = array('user_id','user_name','name','forum_name','forum_id','img_url');
        if (!Service_Libs_Util::checkAllSet($arrInput, $arrCheckParam)) {
            Bingo_Log::warning('param error:'.serialize($arrInput)); 
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUserId  = intval($arrInput['user_id']);
        $strUserName= $arrInput['user_name'];
        $strName    = $arrInput['name'];
        $intForumId = intval($arrInput['forum_id']);
        $strForumName = $arrInput['forum_name'];
        $strImgUrl    = $arrInput['img_url'];

        if ($intUserId<=0 || empty($strName) || empty($strUserName) || empty($strForumName) || empty($strImgUrl)) {
            Bingo_Log::warning('param error:'.serialize($arrInput)); 
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        //判断该用户改作品是否已提交
        $arrParam = array(
            'user_id' => $intUserId,
            'forum_id'=> $intForumId,
            'name'    => $strName,
        );

        $arrLiterary = self::getLiteraryByCond($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrLiterary['errno'] || $arrLiterary===false) {
            Bingo_Log::warning(sprintf("Call getLiteraryByCond::select failed.[input=%s] [output=%s]", serialize($arrParam), serialize($arrLiterary)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        //已存在作品
        if (!empty($arrLiterary['data']['list'])) {
            $intStatus = $arrLiterary['data']['list'][0]['status'];
            //正在审核或已通过审核
            if ($intStatus == Service_Libs_Define::TBVIP_LITERARY_STAT_SUCC || $intStatus == Service_Libs_Define::TBVIP_LITERARY_STAT_INIT) {
                //重复申请
                Bingo_Log::warning('repeat apply[input]'.serialize($arrInput));
                return self::_errRet(Tieba_Errcode::ERR_MA_REPEAT_APPLY);
            //审核未通过，更新作品信息
            } else if ($intStatus == Service_Libs_Define::TBVIP_LITERARY_STAT_REFUSE) {
                $arrInput['id'] = $arrLiterary['data']['list'][0]['id'];
                $arrInput['status'] = Service_Libs_Define::TBVIP_LITERARY_STAT_INIT;
                return self::updateLiterary($arrInput);
            } 
            Bingo_Log::warning('未知作品状态'.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrFields = array(
            'name'     => $strName,
            'intro'    => isset($arrInput['intro']) ? $arrInput['intro'] : '',
            'user_id'  => $intUserId,
            'user_name'=> $strUserName,
            'forum_name' => $strForumName,
            'forum_id'   => $intForumId,
            'img_url'    => $strImgUrl,
            'status'     => Service_Libs_Define::TBVIP_LITERARY_STAT_INIT,
            'create_time'=> time(),
            'update_time'=> time(),
        );
        $arrParam = array(
            'field' => $arrFields,
        );
        $arrRet = Dl_Db_Literary::insert($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Call Dl_Db_Rank::insert failed.[input=%s] [output=%s]", serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['data']);
    }

    /**
     * @brief  根据条件查询作品
     * @param  $arrInput
     * @return array
     */
    public static function getLiteraryByCond($arrInput) {
        $intPn = intval($arrInput['pn']);
        $intRn = intval($arrInput['rn']);
        $intPn = $intPn <= 0 ? 1 : $intPn;
        $intRn = $intRn <= 0 ? 20 : $intRn;
        $intOffset = ($intPn - 1) * $intRn;
        $arrCond = Dl_Db_Literary::filterFields($arrInput);
        if (!is_array($arrCond) || empty($arrCond)) {
            Bingo_Log::warning(sprintf("Param error. [input=%s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrParam = array(
            'cond'  => $arrCond,
        );
        $arrParam['append'] = "ORDER BY create_time DESC limit $intOffset, $intRn";
        $arrRet = Dl_Db_Literary::select($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Call Dl_Db_Literary::select failed.[input=%s] [output=%s]", serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrList = $arrRet['data'];
        $arrRet  = Dl_Db_Literary::getTotalCount($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Query Dl_Db_Literary count failed. [input=%s] [output=%s]",
                serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $intCount = $arrRet['data'];
        $arrOutput = array(
            'list' => $arrList,
            'page' => array(
                'current_pn'  => $intPn,
                'total_count' => $intCount,
                'total_pn'    => ceil($intCount / $intRn),
            ),
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    /**
    * @brief 更新作品信息 对外开放
    * @param array
    * @return array
    **/
    public static function updateLiterary($arrInput) {
        $intId = intval($arrInput['id']);
        if ($intId <= 0) {
            Bingo_Log::warning(sprintf("param is error. [input=%s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrParam = array(
            'cond'  => array(
                'id' => $intId,
            ),
            'field' => array(),
        );
        $arrField = Dl_Db_Literary::filterFields($arrInput);
        unset($arrField['id']);
        unset($arrField['user_id']);
        if (empty($arrField)) {
            Bingo_Log::warning(sprintf("filter field error. [input=%s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrField['update_time'] = time();
        $arrParam['field'] = $arrField;

        $arrRet = Dl_Db_Literary::update($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Call Dl_Db_Literary::update failed.[input=%s] [output=%s]",serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['data']);
    }

    /**
    * @brief 编辑作品信息 对MIS开放
    * 有各种状态判断
    * @param array
    * @return array
    **/
    public static function editLiterary($arrInput) {
        $intId = intval($arrInput['id']);
        if ($intId <= 0) {
            Bingo_Log::warning(sprintf("param is error. [input=%s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //审核通过时检查，吧ID是否已经绑定；审核拒绝时跳过认证
        if(intval($arrInput['status']) == Service_Libs_Define::TBVIP_LITERARY_STAT_SUCC ){
            //检查吧ID是否重复
            $forum_id = intval($arrInput['forum_id']);
            if ($forum_id <= 0) {
                Bingo_Log::warning(sprintf("param is error. [input=%s]", serialize($arrInput)));
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $arrParam = array(
                'cond'  => array(
                    'forum_id' => $forum_id,
                    'status' => Service_Libs_Define::TBVIP_LITERARY_STAT_SUCC,
                ),
                'field' => array(
                    'id',
                ),
            );

            $ret = Dl_Db_Literary::select($arrParam);
            if ($ret == false) {
                Bingo_Log::warning(sprintf("Call Dl_Db_Literary::select failed.[input=%s] [output=%s]",serialize($arrParam), serialize($ret)));
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }

            if(!empty($ret['data'])){
                Bingo_Log::warning(sprintf("Call Dl_Db_Literary::select failed.[input=%s] [output=%s]",serialize($arrParam), serialize($ret)));
                return self::_errRet(Tieba_Errcode::FORUM_HAS_EXISTS);
            }
        }

        $arrParam = array(
            'cond'  => array(
                'id' => $intId,
            ),
            'field' => array(),
        );
        $arrField = Dl_Db_Literary::filterFields($arrInput);
        unset($arrField['user_id']);
        unset($arrField['name']);
        unset($arrField['forum_id']);
        unset($arrField['forum_name']);
        if (empty($arrField)) {
            Bingo_Log::warning(sprintf("filter field error. [input=%s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrField['update_time'] = time();
        $arrParam['field'] = $arrField;

        $arrRet = Dl_Db_Literary::update($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Call Dl_Db_Literary::update failed.[input=%s] [output=%s]",serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        //状态判断是否发通知
        if(isset($arrInput['status'])){
            $intStatus = intval($arrInput['status']);
            if ($intStatus == Service_Libs_Define::TBVIP_LITERARY_STAT_SUCC) {
                //审核通过 
                $res = self::openForumPerm($arrInput);//开通吧务权限

                if($res == false || $res['errno'] != Tieba_Errcode::ERR_SUCCESS){
                    Bingo_Log::warning(sprintf("Call openForumPerm failed.[input=%s] [output=%s]",serialize($arrInput), serialize($res)));
                    return $res;
                }
            } 
        }

        if ($arrInput['notice'] == 1 ) {
            //发消息
            self::noticeUserOnPc($arrInput);
        }


        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['data']);
    }


    /**
    * @brief 开通作者吧务权限
    * @param array
    * @return array
    **/
    public static function openForumPerm($arrInput){
        //todo 待补充

        if(!isset($arrInput['id'])){
            Bingo_Log::warning(sprintf("param is error. [input=%s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrField = array(
            'id',
            'name',
            'intro',
            'user_id',
            'user_name',
            'forum_name',
            'forum_id',
            'status',
            'img_url',
            'reason',
            'create_time',
            'update_time',
            'operator',
        );

        $arrParam = array(
            'cond'  => array(
                'id' => intval($arrInput['id']),
            ),
            'field' => $arrField,
        );

        $ret = Dl_Db_Literary::select($arrParam);
        if($ret == false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(sprintf("Call Dl_Db_Literary::select failed.[input=%s] [output=%s]",serialize($arrParam), serialize($ret)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $Literary = $ret['data'][0];
        $user_id = $Literary['user_id'];
        
        $input = array(
            "forum_id" => $Literary['forum_id'], //吧id
            "forum_name" => $Literary['forum_name'], //吧名
            "user_id" => $Literary['user_id'], //用户id
            "user_name" => $Literary['user_name'], //用户名
            "op_user_id" => 1, //操作者的用户id
            "op_user_name" => $Literary['operator'],
            "role_name" => "certification_author", //角色名
        );
        $res   = Tieba_Service::call('perm', 'setUserRole', $input, null, null, 'post', 'php', 'utf-8');

        return $res;
    }




    /**
    *  文学作品发PC私信
    * @param array
    * @return array
    **/
    public static function noticeUserOnPc($arrInput){
        if(!isset($arrInput['id'])){
            Bingo_Log::warning(sprintf("param is error. [input=%s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrField = array(
            'id',
            'name',
            'intro',
            'user_id',
            'user_name',
            'forum_name',
            'forum_id',
            'status',
            'img_url',
            'reason',
            'create_time',
            'update_time',
            'operator',
        );

        $arrParam = array(
            'cond'  => array(
                'id' => intval($arrInput['id']),
            ),
            'field' => $arrField,
        );

        $ret = Dl_Db_Literary::select($arrParam);
        if($ret == false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(sprintf("Call Dl_Db_Literary::select failed.[input=%s] [output=%s]",serialize($arrParam), serialize($ret)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $Literary = $ret['data'][0];
        $intStatus = $Literary['status'];
       
        if ($intStatus == Service_Libs_Define::TBVIP_LITERARY_STAT_SUCC) {
            //发消息
            $strContent = "作品认证信息：恭喜您，您的作品《".$Literary['name']."》已经通过我们审核，您将享有作品对应吧的专属权限";
            $title= self::LITERARY_MSG_TITLE;
        } else if ($intStatus == Service_Libs_Define::TBVIP_LITERARY_STAT_REFUSE) {
            //审核失败 todo
            $strContent = '失败信息：抱歉，您的作品由于'.$Literary['reason'].'原因，未通过审核，请继续努力';
            $title = self::LITERARY_MSG_TITLE;
        }
        if(!empty($title) && !empty($strContent)){
            Service_Libs_Util::sendNotice($Literary['user_id'], $title, $strContent);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $ret['data']['0']);

    }

    /**
    * @brief 物理删除作品信息
    * @param array
    * @return array
    **/
    public static function delLiterary($arrInput) {
        $intId = intval($arrInput['id']);
        if ($intId <= 0) {
            Bingo_Log::warning(sprintf("Param error. [input=%s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrParam = array(
            'cond'=> array(
                'id' => $intId,
            ),
        );
        $arrRet = Dl_Db_Literary::deleteRecord($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Call Dl_Db_Literary::deleteRecord failed.[input=%s] [output=%s]", serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['data']);
    }
}
