<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014:03:18 18:43:45
 * @version
 * @structs & methods(copied from idl.)
 */

define ( "MODULE", "Perfmon_service" );
class Service_Data_Data {
	
	const SERVICE_NAME = "Service_Data_Data";
	protected static $_conf = null;
	
	public static $appList = array ('tbmall-203.orp.all', //'app.orp.all',
	//'apptest.orp.tc',
	'appui-81.orp.all', //'arkon2matrix-194.orp.all',
	//'arkontomatrix3-193.orp.all',
	'bakan-13.orp.all', 'bawu-43.orp.all', 'bb-162.orp.all', 'choujiang-230.orp.all', 'comforum-47.orp.all', 'complaint-54.orp.all', 'cuseraudit-44.orp.all', 'dal-52.orp.all', 'dal-forumrela-73.orp.all', 'dal-lbs-72.orp.all', 'dal-suishoufa-88.orp.all', 'data-click-184.orp.all', 'deploy.orp.all', 'feedback-14.orp.all', 'fortune-232.orp.all', 'friend-95.orp.all', 'hxdtest.orp.all', 'indexui-84.orp.all', 'internal-shopping-40.orp.all', 'jingyan-3.orp.all', 'message-83.orp.all', 'mis-28.orp.all', 'mkt-213.orp.all', 'mo-client-frs-109.orp.all', 'mo-client-im-176.orp.all', 'mo-client-im-ui-177.orp.all', 'mo-client-img-218.orp.all', 'mo-client-newlog-228.orp.all', 'mo-client-other-164.orp.all', 'mo-client-pb-135.orp.all', 'mo-client-pre-233.orp.all', 'mo-modata-108.orp.all', 'mo-padweb-91.orp.all', 'mo-wowap-frs-96.orp.all', 'mo-wowap-other-147.orp.all', 'mo-wowap-pb-142.orp.all', 'mo-yuyin-156.orp.all', 'msm-221.orp.all', 'mtn-9.orp.all', 'nmq-tieba-bundle.ksarch.all', 'or-116.orp.all', 'orptes3-60.orp.all', 'orptest-4.orp.all', 'orptest2-45.orp.all', 'otp-15.orp.all', 'pc-actstage-171.orp.all', 'pc-anti-120.orp.all', 'pc-billboard-153.orp.all', 'pc-billboard-ui-152.orp.all', 'pc-cui-144.orp.all', 'pc-encourage-134.orp.all', 'pc-encourageui-133.orp.all', 'pc-faces-219.orp.all', 'pc-forum-118.orp.all', 'pc-frsui-122.orp.all', 'pc-fstar-145.orp.all', 'pc-game-148.orp.all', 'pc-gconforum-175.orp.all', 'pc-home-157.orp.all', 'pc-itieba-136.orp.all', 'pc-managerapply-160.orp.all', 'pc-nsign-220.orp.all', 'pc-nzhibo-231.orp.all', 'pc-nzyq-113.orp.all', 'pc-nzyq-ui-114.orp.all', 'pc-pay-181.orp.all', 'pc-pbui-90.orp.all', 'pc-perm-117.orp.all', 'pc-pop-106.orp.all', 'pc-post-121.orp.all', 'pc-push-105.orp.all', 'pc-reply-112.orp.all', 'pc-restserver-183.orp.all', 'pc-sapi-189.orp.all', 'pc-searchis-141.orp.all', 'pc-sign-103.orp.all', 'pc-suggestion-93.orp.all', 'pc-user-119.orp.all', 'pc-userrank-146.orp.all', 'pc-voice-154.orp.all', 'pc-zhibo-107.orp.all', 'photo-53.orp.all', 'platform-234.orp.all', 'pletter-75.orp.all', 'pmc-71.orp.all', 'pubdata-34.orp.all', 'reeze-190.orp.all', 'relay-58.orp.all', 'searchui-85.orp.all', 'smallapp-48.orp.all', 'starbantu-55.orp.all', 'superboy-125.orp.all', 'superboypc-126.orp.all', 'tbmallmsg-204.orp.all', 'tbscore-202.orp.all', //'tenyear-196.orp.all',
	//'testApp1114-195.orp.all',
	//'testApp222-197.orp.all',
	//'testB-215.orp.all',
	//'testdead.orp.all',
	//'testHideHostapp-178.orp.all',
	//'ttttt-159.orp.all',
	'ueg-antiserver-102.orp.all', 'ueg-imgaudit-168.orp.all', 'ueg-urlfeature-179.orp.all', 'ueg-usercredit-186.orp.all', 'ueg-userstat-99.orp.all', 'uegmis-205.orp.all', 'unihandle-89.orp.all', 'upm-27.orp.all', 'WiseHongbao-224.orp.all', 'youliao-70.orp.all' );
	public static $appMap = array(
			"smallapp"=>"ui_smallapp",
			"dal"=>"ui_dal",
			"photo"=>"photo",
			"relay"=>"relay",
			"pletter"=>"pletter",
			"appui"=>"ui_appui",
			"message"=>"message",
			"indexui"=>"ui_indexui",
			"searchui"=>"ui_searchui",
			"pc_pbui"=>"ui_pc_pbui",
			"mo_padweb"=>"ui_mo_padweb",
			"mo_wowap_frs"=>"ui_mo_wowap_frs",
			"ueg_userstat"=>"userstate",
			"ueg_antiserver"=>"antiserver",
			"pc_pop"=>"ui_pc_pop",
			"pc_zhibo"=>"ui_pc_zhibo",
			"mo_modata"=>"modata",
			"mo_client_frs"=>"ui_mo_client_frs",
			"pc_perm"=>"perm",
			"pc_forum"=>"forum",
			"pc_user"=>"user",
			"pc_anti"=>"anti",
			"pc_post"=>"post",
			"pc_frsui"=>"ui_pc_frsui",
			"mo_client_pb"=>"ui_mo_client_pb",
			"pc_itieba"=>"ui_pc_itieba",
			"mo_wowap_pb"=>"ui_mo_wowap_pb",
			"pc_cui"=>"ui_pc_cui",
			"mo_wowap_other"=>"ui_mo_wowap_other",
			"pc_game"=>"ui_pc_game",
			"pc_home"=>"ui_pc_home",
			"mo_client_other"=>"ui_mo_client_other",
			"pc_actstage"=>"ui_pc_actstage",
			"mo_client_im"=>"ui_mo_client_im_ui",
			"mo_client_im_ui"=>"ui_mo_client_im_ui",
			"pc_restserver"=>"ui_pc_restserver",
			"tbmall"=>"ui_tbmall",
			"tbmallmsg"=>"tbmallmsg",
			"mo_client_img"=>"ui_mo_client_img",
			"mo_client_newlog"=>"ui_mo_client_newlog",
			"mo_client_pre"=>"spread",
			"bawu"=>"bawu",
			"comforum"=>"comforum",
			"fstar"=>"fstar",
			"gconforum"=>"gconforum",
			"mis"=>"mis",
			"voice"=>"voice",		
	);
	
	
	private static $_mykeys=array('COST','QPS',);
	private static $_rooms=array('tc','jx','nj');
	
	
	/**
	 * @brief init
	 * @return: true if success. false if fail.

	 **/
	private static function _init() {
		
		//add init code here. init will be called at every public function beginning.
		//not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
		//init should be recalled for many times.
		

		if (self::$_conf == null) {
			self::$_conf = Bd_Conf::getConf ( "/app/perfmon/service_test_test" );
			if (self::$_conf == false) {
				Bingo_Log::warning ( "init get conf fail." );
				return false;
			}
		
		}
		return true;
	}
	
	private static function _errRet($errno) {
		return array ('errno' => $errno, 'errmsg' => Tieba_Error::getErrmsg ( $errno ) );
	}
	
	public static function preCall($arrInput) {
		// pre-call hook
	}
	
	public static function postCall($arrInput) {
		// post-call hook
	}
	/**
	 * @brief
	 * @arrInput:
	 * uint32_t user_id
	 * uint32_t offset
	 * @return: $arrOutput
	 * out_info data[]
	 **/
	public static function api($arrInput) {
		
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if (! isset ( $arrInput ['user_id'] ) || ! isset ( $arrInput ['offset'] )) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		//input params.
		$user_id = intval ( $arrInput ['user_id'] );
		$offset = intval ( $arrInput ['offset'] );
		
		if (! self::_init ()) {
			return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
		}
		//output params.
		$data = false;
		
		//your code here......
		

		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array ('errno' => $error, 'errmsg' => Tieba_Error::getErrmsg ( $error ), 'data' => $data );
		return $arrOutput;
	}
	
	public static function testCall() {
		var_dump ( DL_Noah_Noah::query ( self::$appList, array ('qps_cnt_sum', 'cpu_used_sum' ) ) );
		exit ();
	}
	
	private static function __getInterval($begin, $end) {
		
		$interval = 10;
		$seconds = strtotime ( $end ) - strtotime ( $begin );
		//控制interval
		if ($seconds <= 3600) {
			$interval = 10;
		} else if ($seconds <= 3600 * 3) {
			$interval = 60;
		} else if ($seconds <= 86400) {
			$interval = 600;
		} else {
			$interval = 3600;
		}
		return $interval;
	}
	
	public static function resCost($arrInput) {
		
		$appName = $arrInput ['appname'];
		$begin = $arrInput ['begin'];
		$end = $arrInput ['end'];
		$interval = $arrInput ['interval'];
		
		//qps,cpu,cost
		$items = array ('qps_cnt_sum', 'cpu_used_sum', 'log_access_log_nginx_upstreamtime_avg_avg' );
		
		if (empty ( $appName ))
			return false;
		
		if (empty ( $begin )) {
			$res = DL_Noah_Noah::query ( $appName, $items );
		} else {
			if (empty ( $interval ))
				$interval = self::__getInterval ( $begin, $end );
			$res = DL_Noah_Noah::query ( $appName, $items, $begin, $end, $interval );
		}
		$appNodes = DL_Noah_Noah::getContainersWithApp ( $appName );
		
		$arrRest = $res;
		$arrRest [$appName] ['container_num'] = count ( $appNodes );
		
		$arrSource = array ();
		foreach ( $arrRest [$appName] ['qps_cnt_sum'] ['data'] as $key => $val ) {
			$arrSource ['data'] [$key] = round ( $arrRest [$appName] ['cpu_used_sum'] ['data'] [$key] / $val, 3 );
		}
		$arrRest [$appName] ['cpu_used_per_qps'] = $arrSource;
		
		return $arrRest;
	}
	
	public static function resAllCost($arrInput) {
		
		$appName = $arrInput ['appname'];
		$begin = $arrInput ['begin'];
		$end = $arrInput ['end'];
		$interval = $arrInput ['interval'];
		$items = $arrInput ['items'];
		
		//qps,cpu,cost
		if (empty ( $items )) {
			$items = array ('qps_cnt_sum', 'cpu_used_sum', 'log_access_log_nginx_upstreamtime_avg_avg' );
		}
		
		if (empty ( $appName ) || ! is_array ( $appName )) {
			return false;
		}
		
		if (empty ( $begin )) {
			$res = DL_Noah_Noah::query ( $appName, $items );
		} else {
			if (empty ( $interval ))
				$interval = self::__getInterval ( $begin, $end );
			$res = DL_Noah_Noah::query ( $appName, $items, $begin, $end, $interval );
		}
		
		foreach ( $appName as $app ) {
			$res [$app] ['container_num'] = count ( DL_Noah_Noah::getContainersWithApp ( $app ) );
			$arrSource = array ();
			foreach ( $res [$app] ['qps_cnt_sum'] ['data'] as $key => $val ) {
				$arrSource ['data'] [$key] = round ( $res [$app] ['cpu_used_sum'] ['data'] [$key] / $val, 3 );
			}
            #你懂的，没有数据
			//$res [$app] ['cpu_used_per_qps'] = $arrSource;
            $res [$app] ['cpu_used_per_qps'] = $res[$app]['cpu_used_sum'];
		}
		
		return $res;
	}
	/*
     * 获取app的版本历史的性能数据
     * $arrInput = array(
     *                    'appname' => 'post' //app名称,
     *                    'begin' => 11111 //开始时间,
     *                    'end' => 11111 //结束时间,
     *                    'container' => $container, //可选 
     *                    'idc'=>$idc //可选
     *                  )
     */
	public static function getEveryAppInfo($arrInput){
		if(!isset($arrInput['appname']) || !isset($arrInput['begin']) || !isset($arrInput['end'])){
			return false;
		}
		$objMulti = new Tieba_Multi('drsMulti');
		$date1 = date("Ymd",$arrInput['begin']);
		$date2 = date("Ymd",$arrInput['end']);
		if($date1 == $date2){
			$interval = 10;
		}else{
			$interval = 600;
		}
		if($arrInput['idc'] === ''){//avg
			if($interval === 10){
				foreach ($arrInput['appname'] as $app){
					foreach (self::$_rooms as $room){
						$objMulti->register(
								$app.$room,
								new Tieba_Service('drsquery'),
								array(
										'method'=>'getValues',
										'input'=>array(
												'service'=>$app,
												'tag'=>$room,
												'starttime'=>$arrInput['begin'],
												'endtime'=>$arrInput['end'],
										)
								)
						);
					}
				}
			}else if($interval === 600){
				foreach ($arrInput['appname'] as $app){
					foreach (self::$_rooms as $room){
						$objMulti->register(
								$app.$room,
								new Tieba_Service('drsquery'),
								array(
										'method'=>'getMergeValue',
										'input'=>array(
												'service'=>$app,
												'tag'=>$room,
												'starttime'=>$arrInput['begin'],
												'endtime'=>$arrInput['end'],
										)
								)
						);
					}
				}
			}	
		}else{
			if($interval === 10){
				foreach ($arrInput['appname'] as $app){
					$objMulti->register(
							$app,
							new Tieba_Service('drsquery'),
							array(
									'method'=>'getValues',
									'input'=>array(
											'service'=>$app,
											'tag'=>$arrInput['idc'],
											'starttime'=>$arrInput['begin'],
											'endtime'=>$arrInput['end'],
									)
							)
					);
				}	
			}else if($interval === 600){
				foreach ($arrInput['appname'] as $app){
					$objMulti->register(
							$app,
							new Tieba_Service('drsquery'),
							array(
									'method'=>'getMergeValue',
									'input'=>array(
											'service'=>$app,
											'tag'=>$arrInput['idc'],
											'starttime'=>$arrInput['begin'],
											'endtime'=>$arrInput['end'],
									)
							)
					);
				}
			}
		}
		$ret = $objMulti->call();
		if($arrInput['idc'] === ''){
			if($interval === 600){
				foreach ($arrInput['appname'] as $app){
					foreach (self::$_rooms as $room){
						foreach ($ret[$app.$room]['res'] as $timestamp=>$values){
							foreach (self::$_mykeys as $key=>$v){
								$tmp[$app.$room][$v]['data'][]= $values[$v];
							}
						}
						
					}
				}
			}else{
				foreach ($arrInput['appname'] as $app){
					foreach (self::$_rooms as $room){
						$lasttime = key($ret[$app.$room]['res']);//$begin=key($re['res']);
						foreach ($ret[$app.$room]['res'] as $timestamp=>$values){
							$gap=(($timestamp-$lasttime)/10)-1;
							$lasttime=$timestamp;
							if($gap>0){
								for($i=0;$i<$gap;$i++){
									foreach(self::$_mykeys as $key=>$v){
										$tmp[$app.$room][$v]['data'][]=0;
									}
								}
							}
							foreach (self::$_mykeys as $key=>$v){
								$tmp[$app.$room][$v]['data'][]= $values[$v];
							}
						}
					}
				}
			}
			foreach ($arrInput['appname'] as $app){// calculate avg
				foreach ($tmp[$app.'tc']['QPS']['data'] as $key=>$value){
					$sum = 0;
					$sum = $tmp[$app.'jx']['QPS']['data'][$key] + $tmp[$app.'nj']['QPS']['data'][$key];
					$sum += $value;
					if($sum != 0){
						$sum /= 3;
					}
					$data[$app]['QPS']['data'][]=$sum;
				}
				foreach ($tmp[$app.'tc']['COST']['data'] as $key=>$value){
					$sum = 0;
					$sum = $tmp[$app.'jx']['COST']['data'][$key] + $tmp[$app.'nj']['COST']['data'][$key];
					$sum += $value;
					if($sum != 0){
						$sum /= 3;
					}
					$data[$app]['COST']['data'][] = $sum;
				}
			}
		}else{
			if($interval === 600){
				foreach ($arrInput['appname'] as $app){
					foreach ($ret[$app]['res'] as $timestamp=>$values){
						foreach (self::$_mykeys as $key=>$v){
							$data[$app][$v]['data'][]=$values[$v];//$data
						}
					}
				}
			}else{
				foreach($arrInput['appname'] as $app){
					$lasttime = key($ret[$app]['res']);//$begin=key($re['res']);
					foreach ($ret[$app]['res'] as $timestamp=>$values){
						$gap=(($timestamp-$lasttime)/10)-1;
						$lasttime=$timestamp;
						if($gap>0){
							for($i=0;$i<$gap;$i++){
								foreach(self::$_mykeys as $key=>$v){
									$data[$app][$v]['data'][]=0;
								}
							}
						}
						foreach(self::$_mykeys as $key=>$v){
							$data[$app][$v]['data'][]=$values[$v];
						}
					}
				}
			}
		}	
		foreach ($arrInput['appname'] as $app){
			$arrOutput[$app]['begin']=$arrInput['begin'];
			$arrOutput[$app]['end']=$arrInput['end'];
			$arrOutput[$app]['interval']=10;
			$arrOutput[$app]['tags'][] = array(false,false);
			$arrOutput[$app]['qps_data'] = $data[$app]['QPS']['data'];//array(1,2,3);//$ret['QPS']['data'];
			$arrOutput[$app]['cost_data'] = $data[$app]['COST']['data'];//array(3,2,1);
		}	
		return $arrOutput;
	}
	
	
	
	
	public static function getAppVersionPerfData($arrInput) {
		//参数判断
		if(!isset($arrInput['appname']) || !isset($arrInput['begin']) || !isset($arrInput['end'])){
		  return false;
		}
		$appname = $arrInput ['appname'];
		$begin = $arrInput ['begin'];
		$end = $arrInput ['end'];
	    $arrOutput = array ();
	    $arrOutput['begin'] = $begin;
		$arrOutput['end'] = $end;
		$arrOutput['interval'] = self::__getInterval( $begin, $end );
		
		
		//获取版本历史
// 		$versionList = self::getORPVersion ( $arrInput ['appname'], $arrInput ['begin'], $arrInput ['end']);
// 		foreach ( $versionList as $versionid => $version ) {
// 			$arrOutput['tags'][] = array($version['time'], $version['name']);
// 		}
// 	   $appList = Service_App_App::getAppList();
// 	   $longName = "";
//        if(!isset($appList[$arrInput['appname']])){
//        	    $longName = Service_App_App::getServiceLongApp($arrInput['appname']);
//        	    if(!$longName){
//                return false;
//        	    }
//        }else {
//          $longName = $appList[$arrInput['appname']];
//        }

		$arrOutput['tags'][] = array(false,false);
		
		
//        $item1 = 'qps_cnt_sum';
        //$item2 = 'cpu_used_sum';
//        $item2 = 'log_access_log_nginx_upstreamtime_avg_avg';
//        if(isset($arrInput['idc']) && !empty($arrInput['idc'])){
//            $item1 = 'idc_'.$arrInput['idc'].".".$item1;
//            $item2 = 'idc_'.$arrInput['idc'].".".$item2;
//        }
//		$ret = DL_Noah_Noah::query ($longName, array ($item1, $item2 ) , date("YmdHis",$begin), date("YmdHis",$end), $arrOutput['interval']);
		$input['service'] = self::$appMap[$arrInput['appname']];
		$input['tag'] = $arrInput['idc'];
		$input['starttime'] = $begin;
		$input['endtime'] = $end;
		//if($arrOutput['interval'] == 10){
		//	$ret = Tieba_Service::call('drsquery','getValues',$input,NULL,NULL,'post','json');
		//}
		if('' === trim($input['tag'])){//default value is average of three room(tc,jx,nj) 
			$input['tag'] = 'tc';
			$retTc = self::getDrsPerformanceData($input);
			$input['tag'] = 'jx';
			$retJx = self::getDrsPerformanceData($input);
			$input['tag'] = 'nj';
			$retNj = self::getDrsPerformanceData($input);
			foreach ($retTc['QPS']['data'] as $key => $value){
				$sum = $value + $retJx['QPS']['data'][$key] + $retNj['QPS']['data'][$key];	
				if($sum != 0){
					$avg = $sum / 3;
				}else{
					$avg = $sum;
				}
				$ret['QPS']['data'][$key] = $avg;
			}
			foreach ($retTc['COST']['data'] as $key => $value){
				$sum = $value + $retJx['COST']['data'][$key] + $retNj['COST']['data'][$key];
				if($sum != 0){
					$avg = $sum / 3;
				}else{
					$avg = $sum;
				}
				$ret['COST']['data'][$key] = $avg;
			}
		}else{
			$ret = self::getDrsPerformanceData($input);
		}
		$arrOutput['qps_data'] = $ret['QPS']['data'];
		$arrOutput['cost_data'] = $ret['COST']['data'];
		return $arrOutput;
	}

	
	
	
	public static function getDrsPerformanceData($arrInput){
		$begin = $arrInput['starttime'];
		$end = $arrInput['endtime'];
		$date1 = date("Ymd",$begin);
		$date2 = date("Ymd",$end);
		if($date1 == $date2){
			$interval = 10;
		}else{
			$interval = 600;
		}
		if($interval == 10){
			$re = Tieba_Service::call('drsquery','getValues',$arrInput,NULL,NULL,'post','json');
		}else{
			$re = Tieba_Service::call('drsquery','getMergeValue',$arrInput,NULL,NULL,'post','json');
		}
		if(null == $ret || false == $ret){
			Bingo_Log::warning("call service drsquery failed");
		}
	    
		if(!empty($re['res'])){
			$begin=key($re['res']);
	        $endv=end($re['res']);
			$end=key($re['res']);
			$baseval=array(
			  'data'=>array(),
			  'interval'=>$interval,
			  'begin'=>$begin,
			  'end'=>$end);
			foreach (self::$_mykeys as $key=>$v){
				$arrRet[$v]=$baseval;
			}
			if(600==$interval){
				foreach ($re['res'] as $timestamp =>$values){
					foreach(self::$_mykeys as $key=>$v){
						$arrRet[$v]['data'][]=$values[$v];
					}
				}
			}else{
				$lasttime=$begin;
				foreach ($re['res'] as $timestamp =>$values){
					$gap=(($timestamp-$lasttime)/10)-1;
					$lasttime=$timestamp;
					if($gap>0){
						for($i=0;$i<$gap;$i++){
							foreach(self::$_mykeys as $key=>$v){
								$arrRet[$v]['data'][]=0;
							}
						}
					}
					foreach(self::$_mykeys as $key=>$v){
						$arrRet[$v]['data'][]=$values[$v];
					}
				}
			}
		}
		return $arrRet;
	}

	public static function getAllInfo($arrInput){
		$arrOutput = array();
		if(!isset($arrInput['appname']) || '' === trim($arrInput['appname']) || !isset($arrInput['begin']) || !isset($arrInput['end'])){
			return false;
		}
		$relyApps = DL_Omp_Rt::getCalleeWithApp($arrInput['appname']);
		if(!isset($relyApps)){
			return false;
		}
		sort($relyApps);
		foreach ($relyApps as $app){
			$app = self::_map($app);
			$drsInput['appname'] = $app;
			$drsInput['idc'] = $arrInput['idc'];
			$drsInput['begin'] = $arrInput['begin'];
			$drsInput['end'] = $arrInput['end'];
			$retPerfmonData = self::getDrsPerformanceDataTest($drsInput);
			$arrOutput['relyApp'][] = $app;
			$arrOutput[$app] = $retPerfmonData;
		}
		return $arrOutput;
	}
	
	
	
	/*
	 * 获取App依赖的模块，并根据耗时计算角度值
	 * $arrInput => array('appname' => 'post', 'time'=>111, 'container'=>xxx, 'idc'=>tc)
	 */
	public static function getRelyAppsCostBySingleTime($arrInput){
	    $arrOutput = array();
	    if(!isset($arrInput['appname']) || '' === trim($arrInput['appname']) || !isset($arrInput['time'])){
	        return false;
	    }
// 	    $relyApps = self::getRelyAppsByApp($arrInput['appname']);
		if(!isset($arrInput['relyAppName'])){
			$relyApps = DL_Omp_Rt::getCalleeWithApp($arrInput['appname']);
		}else{
			$relyApps = $arrInput['relyAppName'];
		}
	    //按照模块名称排序
	    sort($relyApps);
	    
	    
	    foreach ($relyApps as $app){
	    	$app = self::_map($app);
	    	$drsIput['appname'] = $app;
	    	$drsIput['idc'] = $arrInput['idc'];
	    	$drsIput['begin'] = $arrInput['time']-100;
	    	$drsIput['end'] = $arrInput['time'];
	    	$retPerfmonData = self::getDrsPerformanceDataTest($drsIput);
	    	$cost = end($retPerfmonData['COST']['data']);
	    	$arrOutput['data'][] = array($app,$cost);
	    }
	    	    
	    //获取耗时
// 	    $ret = DL_Omp_Rt::getCostWithAppPairs($arrInput['appname'], $relyApps, $arrInput['time']-100, $arrInput['time']);
// 	    foreach($relyApps as $appName ){
// 	    	$cost = end($ret[$appName]['cost']);
// 	    	$appName = self::_map($appName);
// 	    	$arrOutput['data'][] = array($appName,$cost);
// 	    }
	    
	    return $arrOutput;
	}
	/*
	 * 获取所有app
	 * 
	 */
	public static function getApps($arrInput){
	    $arrOutput = array();
        $res = Service_App_App::getAppList();
        foreach($res as $key => $value){
            $arrOutput[] = array( 'appname' => $key, 'apptext'=> "");
        }
	    return $arrOutput;
	}
	
	/*
	 * 获取App的历史耗时数据
     * $arrInput = array(
     *                    'appname' => 'post' //app名
     *                    'relynames' => array('foo')  //依赖app,
     *                    'begin' => 11111 //开始时间,
     *                    'end' => 11111 //结束时间,
     *                  )
     * $arrOutput = array(
     *                     'interval' => 10
     *                     'begin'=>111
     *                     'end=>'222
     *                     'data' => array(
     *                               'cost_time' => array(10,10,20,...)
     *                            )
     *                   )
     */
	public static function getCostHistoryWithAppPairs($arrInput) {
	  //参数判断
	  if(!isset($arrInput['appname']) || !isset($arrInput['relynames'])|| !isset($arrInput['begin']) || !isset($arrInput['end'])){
		  return false;
	  }	  
	  $appName = $arrInput ['appname'];
	  $relyNames = $arrInput['relynames'];
	  $begin = $arrInput ['begin'];
	  $end = $arrInput ['end'];
	  if(!is_array($relyNames))
	     $relyNames = array($relyNames);
	  $ret = DL_Omp_Rt::getCostWithAppPairs($appName, $relyNames, $begin, $end);
	  $arrOutput = array();
	  foreach ($relyNames as $key){
	     $arrOutput['tags'][] = self::_map($key);
	     $arrOutput[self::_map($key)] = $ret[$key]['cost'];
	   }
	   $arrOutput['interval'] = $ret['interval'];
	   $arrOutput['begin'] = $ret['interval'];
	   $arrOutput['end'] = $ret['interval'];
	   return $arrOutput;
	}
	
	public static function getCostHistoryWithAppPairsTest($arrInput) {
		//参数判断
		if(!isset($arrInput['appname']) || !isset($arrInput['relynames'])|| !isset($arrInput['begin']) || !isset($arrInput['end'])){
			return false;
		}
		$appName = $arrInput ['appname'];
		$relyNames = $arrInput['relynames'];
		$begin = $arrInput ['begin'];
		$end = $arrInput ['end'];
		if(!is_array($relyNames))
			$relyNames = array($relyNames);
// 		$ret = DL_Omp_Rt::getCostWithAppPairs($appName, $relyNames, $begin, $end);
		//prepare for drs input
		$arrOutput = array();
		foreach ($relyNames as $app){
			$drsIput['appname'] = $app;
			$drsIput['idc'] = $arrInput['idc'];
			$drsIput['begin'] = $begin;
			$drsIput['end'] = $end;
			$retPerfmonData = self::getDrsPerformanceDataTest($drsIput);
			$arrOutput['tag'][] = $app;
			$arrOutput[$app] = $retPerfmonData['COST']['data'];
		}
		
		$date1 = date("Ymd",$begin);
		$date2 = date("Ymd",$end);
		if($date1 == $date2){
			$arrOutput['interval'] = 10;
		}else{
			$arrOutput['interval'] = 600;
		}
		$arrOutput['begin'] = $begin;
		$arrOutput['end'] = $end;
		
// 		$arrOutput = array();
// 		foreach ($relyNames as $key){
// 			$arrOutput['tags'][] = self::_map($key);
// 			$arrOutput[self::_map($key)] = $ret[$key]['cost'];
// 		}
// 		$arrOutput['interval'] = $ret['interval'];
// 		$arrOutput['begin'] = $ret['interval'];
// 		$arrOutput['end'] = $ret['interval'];
// 		return $arrOutput;
	}
	
	
	public static function getDrsPerformanceDataTest($arrInput){
		$input['service'] = self::$appMap[$arrInput['appname']];
		$input['tag'] = $arrInput['idc'];
		$input['starttime'] = $arrInput['begin'];
		$input['endtime'] = $arrInput['end'];
		//if($arrOutput['interval'] == 10){
		//	$ret = Tieba_Service::call('drsquery','getValues',$input,NULL,NULL,'post','json');
		//}
		if('' === trim($input['tag'])){//default value is average of three room(tc,jx,nj)
			$input['tag'] = 'tc';
			$retTc = self::getDrsPerformanceData($input);
			$input['tag'] = 'jx';
			$retJx = self::getDrsPerformanceData($input);
			$input['tag'] = 'nj';
			$retNj = self::getDrsPerformanceData($input);
			foreach ($retTc['QPS']['data'] as $key => $value){
				$sum = $value + $retJx['QPS']['data'][$key] + $retNj['QPS']['data'][$key];
				if($sum != 0){
					$avg = $sum / 3;
				}else{
					$avg = $sum;
				}
				$ret['QPS']['data'][$key] = $avg;
			}
			foreach ($retTc['COST']['data'] as $key => $value){
				$sum = $value + $retJx['COST']['data'][$key] + $retNj['COST']['data'][$key];
				if($sum != 0){
					$avg = $sum / 3;
				}else{
					$avg = $sum;
				}
				$ret['COST']['data'][$key] = $avg;
			}
		}else{
			$ret = self::getDrsPerformanceData($input);
		}
// 		$arrOutput['qps_data'] = $ret['QPS']['data'];
// 		$arrOutput['cost_data'] = $ret['COST']['data'];
		return $ret;
	}
	
	
	
    /*
     * 获取指定appname的Info
     */
    public static function getAppInfoByAppName($arrInput){
    	if(!isset($arrInput['appname'])){
    		return false;
    	}
       //DL调用
       $arrOut = array();
// 	   $arrOut['nodes'] = Service_App_App::getAppNodes($arrInput['appname']);
       $arrOut['nodes'] = false;
       $arrOut['idc'] = array('tc','jx','nj');
       $arrOut['text'] = '';
       return $arrOut;
    }
    /*
     * 自定义tab获取数据
     */
    public static function getCustMonData($arrInput){
    	if(!isset($arrInput['key']) || !isset($arrInput['begin']) || !isset($arrInput['end'])){
    		return false;
    	}
       $key = $arrInput['key'];
       $begin =  $arrInput['begin'];
       $end =  $arrInput['end'];
       $arrOut = array();
	   $interval = self::__getInterval ($begin,$end); 
	   //DL调用
	   
	   //
	   $arrOut['begin'] = $arrInput['begin'];
	   $arrOut['end'] = $arrInput['end'];
	   $arrOut['interval'] = $interval;
	   while($begin <= $end){
	   	   $arrOut['data'][] = rand(10, 100);
	       $begin += $interval;
	   }
       return $arrOut;
    }	
	/*
	 * 获取app的依赖模块
	 */
	private static function getRelyAppsByApp($appname) {
	    //DL层调用
	    $res = DL_Omp_Rt::getCalleeWithApp($appname);
	    return $res;
	}
	
	/*
     * 获取app的历史版本
     */
	private static function getORPVersion($appname, $begin, $end) {
		$res = array();
		$interval = self::__getInterval ( $begin, $end );
		$ret = DL_Orp_Rt::getProductMenus($begin, $end, $appname);
		foreach ($ret as $value){
		   if(isset($value['result']) && $value['result'] && isset($value['data'])){
		      $res[] = array('name'=>$value['data']['menuName'].'|'.$value['data']['userName'], 'time'=>$value['data']['createTime']);
		   }
		}
		//DL调用
		return $res;
	}
	
	private static function _map($input){
		$pos = strrpos($input,'_');
		if($pos === false){
			return $input;
		}
		$output = substr($input,$pos+1,strlen($input)-$pos-1);
		return $output;
	}
	
}
