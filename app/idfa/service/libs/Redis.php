<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Redis.php
 * <AUTHOR>
 * @date 2015/11/10 10:18:34
 * @brief 
 *  
 **/


class Service_Libs_Redis {
    protected static $_redis = array();

    /**
     * @brief get redis obj.
     * @param: redis的name
     * @return: obj of Bingo_Cache_Redis, or null if connect fail.
     */
    public static function _getRedis($name = null) {
        if (is_null($name)) {
            $name = Service_Libs_Define::REDIS_NAME;
        }
        if (isset(self::$_redis[$name])) {
            return self::$_redis[$name];
        }

        Bingo_Timer::start('redis_init');
        $redis = new Bingo_Cache_Redis($name);
        Bingo_Timer::end('redis_init');
        if (!$redis || !$redis->isEnable()) {
            Bingo_Log::warning("init redis fail.");
            return null;
        }
        self::$_redis[$name] = $redis;
        return self::$_redis[$name];
    }

    /**
     * @brief set.
     * @param: arrInput.
     * @return: result.
     */
    public static function set($arrInput) {
        if (!isset($arrInput['key']) || !isset($arrInput['value'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            $arrOutput = array(
                'errno' => Tieba_Errcode::ERR_PARAM_ERROR,
                'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR) ,
                'data' => null,
            );
            return $arrOutput;
        }

        $redis = self::_getRedis();
        $arrParams = array(
            'key' => $arrInput['key'],
            'value' => $arrInput['value'],
        );

        return $redis->SET($arrParams);
    }

    /**
     * @brief setex.
     * @param: arrInput.
     * @return: result.
     */
    public static function setex($arrInput) {
        if (!isset($arrInput['key']) || !isset($arrInput['value'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            $arrOutput = array(
                'errno' => Tieba_Errcode::ERR_PARAM_ERROR,
                'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR) ,
                'data' => null,
            );
            return $arrOutput;
        }

        $redis = self::_getRedis();
        $arrParams = array(
            'key' => $arrInput['key'],
            'value' => $arrInput['value'],
            'seconds' => $arrInput['seconds'],
        );

        return $redis->SETEX($arrParams);
    }

    /**
     * @brief expire.
     * @param: arrInput.
     * @return: result.
     */
    public static function setExpire($arrInput) {
        if (!isset($arrInput['key']) || !isset($arrInput['expire'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            $arrOutput = array(
                'errno' => Tieba_Errcode::ERR_PARAM_ERROR,
                'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR) ,
                'data' => null,
            );
            return $arrOutput;
        }

        $redis = self::_getRedis();
        $input = array(
            'key' => $arrInput['key'],
            'seconds' => $arrInput['expire'],
        );
        return $redis->EXPIRE($input);
    }

    /**
     * @brief get.
     * @param: arrInput.
     * @return: result.
     */
    public static function get($arrInput) {
        if (!isset($arrInput['key'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            $arrOutput = array(
                'errno' => Tieba_Errcode::ERR_PARAM_ERROR,
                'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR) ,
                'data' => null,
            );
            return $arrOutput;
        }

        $redis = self::_getRedis();
        $arrParams = array(
            'key' => $arrInput['key'],
        );

        return $redis->GET($arrParams);
    }

    /**
     * @brief ttl.
     * @param: arrInput.
     * @return: result.
     */
    public static function ttl($arrInput) {
        if (!isset($arrInput['key'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            $arrOutput = array(
                'errno' => Tieba_Errcode::ERR_PARAM_ERROR,
                'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR) ,
                'data' => null,
            );
            return $arrOutput;
        }

        $redis = self::_getRedis();
        $arrParams = array(
            'key' => $arrInput['key'],
        );

        return $redis->TTL($arrParams);
    }

    /**
     * @brief del.
     * @param: arrInput.
     * @return: result.
     */
    public static function del($arrInput) {
        if (!isset($arrInput['key'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            $arrOutput = array(
                'errno' => Tieba_Errcode::ERR_PARAM_ERROR,
                'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR) ,
                'data' => null,
            );
            return $arrOutput;
        }

        $redis = self::_getRedis();
        $arrParams = array(
            'key' => $arrInput['key'],
        );
        return $redis->DEL($arrParams);
    }

}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
