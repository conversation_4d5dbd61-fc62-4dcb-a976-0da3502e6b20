<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013/08/07
 * @version 1.0
 */
class addSysThreadAction extends Difang_Action_Base {
    protected $bolIsPv = 0;                   //是否是pv
    protected $bolNeedUserLogin = true;       //是否需要进行用户登录
    protected $bolCheckUserLogin = true;      //是否要检查用户是否登录
    protected $bolNeedCheckSign = true;       //是否需要检查加密串
    protected $bolNeedCheckNoUn = false;      //是否判断无username
    private   $arrReq  = array();
    private   $arrRes  = array();
    private   $intGroupId  = 0;

    protected function _input() {
        //新提交ui那边需要的新增字段
        $this->arrReq['user_id'] = 875984991;            //使用有聊哥公用白名单账号,不会命中反作弊
        $this->arrReq['user_name'] = '贴吧有聊哥';
        $this->arrReq['user_ip'] = Bingo_Http_Ip::ip2long(CapiRequest::$strIp);
        $this->arrReq['open_id'] = Difang_Conf::get('commit_open_id');
        //$_COOKIE['BDUSS'] = '9UUUR2ZWZqaDZaYzM1ZFIxR3VySjZWaUtTRmxsejhHSzFiSjZZY2lkTzFvQ2xTQVFBQUFBJCQAAAAAAAAAAAEAAABfeDY0zPmwydPQwcS45wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALUTAlK1EwJSTE';

        $this->arrReq['lbs'] = Bingo_Http_Request::get('lbs', '');
        $intThreadType = intval(Bingo_Http_Request::get('thread_type', 0));
        $this->arrReq['thread_type'] = $intThreadType;
        $this->arrReq['title'] = Bingo_Http_Request::get('title', '');
        $this->arrReq['kw'] = Bingo_Http_Request::get('kw', 'test');
        $this->arrReq['fid'] = intval(Bingo_Http_Request::get('fid', 35));
        $this->arrReq['product_private_key'] = Difang_Conf::get('product_private_key', 'youliao');
        $this->arrReq['content'] = Bingo_Http_Request::get('content', '');
        $this->arrReq['anonymous'] = 0;
        $this->arrReq['tbs'] = Tieba_Tbs::gene(true);
        $this->arrReq['vcode'] = trim(Bingo_Http_Request::get('vcode', ''));
        $this->arrReq['vcode_md5'] =  Bingo_Http_Request::get('vcode_md5', '');
        $this->arrReq['vcode_tag'] = intval(Bingo_Http_Request::get('vcode_tag', 20));
        $this->arrReq['vcode_sign'] = strval(Bingo_Http_Request::get('vcode_sign', ''));
        $this->intGroupId = intval(Bingo_Http_Request::get('gid', 0));

        //设置Open信息，Difang_Action_Base里已经设置过了，这里是为了将_open_id由tbclient改成youliao
        $strOpenId = Difang_Conf::get('commit_open_id');
        $strUip = CapiRequest::$strIp;
        $strKey = Tbapi_Core_Server::genOpenSecureStr($strOpenId, $strUip, Difang_Conf::get('commit_secure_key'));
        $arrDataEx = array(
            'phone_type' => '',
        );
        if(isset(CapiDef::$arrClientTypes[CapiRequest::$intClientType])) {
            $arrDataEx['phone_type'] = CapiDef::$arrClientTypes[CapiRequest::$intClientType];
        };
        $objCache = Bingo_Cache::factory('source', array(
            'dir' => CSAPI_SERVER_DATA_PATH.'/cache/whiteip',
        ));
        $objIpDict = new IpDict(array(
            'fileName' => CSAPI_SERVER_DATA_PATH . '/forum/whiteip.lst',
            'autoRefresh' => true,
            'cache' => $objCache,
        ));
        $objIpDict->load();
        $bolIsMobileGate = $objIpDict->isInIpDict(CapiRequest::$strIp);
        Bingo_Timer::end('ipcheck');
        if($bolIsMobileGate) {
            CapiRequest::$bolIsMobileGate = true;
        }
        if(CapiRequest::$bolIsMobileGate) {
            $arrDataEx['_data_client_type'] = 2;      //表示是移动网关
            Tieba_Stlog::addNode('is_mobile', 1);
        } else {
            $arrDataEx['_data_client_type'] = 1;      //表示不是移动网关
            Tieba_Stlog::addNode('is_mobile', 0);
        }
        Tbapi_Core_Server::setOpenInfo($strOpenId, $strUip, $strKey, $arrDataEx);

        return true;
    }

    protected function _process() {
        //Bingo_Log::debug("modsys feedbackAction input : ".var_export($this->arrReq,true));
        $arrOut = Tbapi_Core_Server::mocall('post','add_thread',$this->arrReq);
        //Bingo_Log::debug("modsys feedbackAction ui ".var_export($arrOut,true));
        if($arrOut===false || $arrOut['error']['errno']==Tieba_Errcode::ERR_MO_POST_ADD_THREAD_FAIL) {
            $this->_error(CapiErrno::INTERNAL_ERROR, CapiError::INTERNAL_ERROR);
            return false;
        } elseif ($arrOut['error']['errno'] == Tieba_Errcode::ERR_MO_PARAM_INVALID) {
            $this->_error(CapiErrno::PARAM_NOT_ENOUGH, CapiError::PARAM_NOT_ENOUGH);
            return false;
        } elseif ($arrOut['error']['errno'] == Tieba_Errcode::ERR_MO_POST_SIGN_FAIL) {
            $this->_error(CapiErrno::INVALID_SIGN, CapiError::INVALID_SIGN);
            return false;
        } elseif ($arrOut['error']['errno'] !== 0) {
            $this->_error(intval($arrOut['error']['errno']));
            return false;
        }

        $intThreadId = intval($arrOut['data']['thread_id']);
        //发群组分享帖时更新群组分享帖id
        if($intThreadId>0 && $this->intGroupId>0) {
            $arrRequest = array(
                'group_id' => $this->intGroupId,
                'thread_id' => $intThreadId,
                'forum_id' => $this->arrReq['fid'],
                'user_id' => CapiRequest::$intUid,
            );
            $ret = Tieba_Service::call('difang', 'updateGroup', $arrRequest);
            if($ret===false || $ret['errno']!==0) {
                $this->_error(intval($ret['errno']));
                return false;
            }
        }
        
        $this->arrData = array(
            'errno' => 0,
            'errmsg' => '',
            'forum_id' => intval($arrOut['data']['forum_id']),
            'forum_name' => strval($arrOut['data']['forum_name']),
            'thread_id' => $intThreadId,
            'post_id' => intval($arrOut['data']['post_id']),
        );
        return true;
    }
}
