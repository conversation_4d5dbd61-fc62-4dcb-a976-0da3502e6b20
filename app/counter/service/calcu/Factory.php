<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/
/**
 * @file Impl.php
 * <AUTHOR>
 * @date 2016/1/18
 * @brief
 **/
 
class Service_Calcu_Factory
{
    /**
     * @param  string $strCalculatorType counter type
     * @return string the class name of Calculator
     */
    public static function getCalculatorByType($strCalculatorType) 
    {
        return Util_Def::$arrCalculatorMap[$strCalculatorType];
    }

    /**
     * create calculator class obj from clacu type
     * @param  string $strCalculatorType
     * @return obj
     */
    public static function getCalculatorObjByType($strCalculatorType)
    {
        if (!isset(Util_Def::$arrCalculatorMap[$strCalculatorType]))
        {
            return null;
        }
        $strClassPrefix = "Service_Calcu_";
        $strSuffix = ucfirst(Util_Def::$arrCalculatorMap[$strCalculatorType]);
        $strClassName = $strClassPrefix . $strSuffix;
        $objCalculator = new $strClassName();
        $bolReady = $objCalculator->init();
        if (!$bolReady)
        {
            return null;
        }
        return $objCalculator;
    }
}