<?php
/**
 *
 * <AUTHOR>
 * @brife  redis相关操作
 *
 */
class Libs_Redis {

    const DEFAULT_REDIS_EXPIRE = 604800;
    protected $_redisName = '';
    protected $_redis = null;
    /**
     * Libs_Redis constructor.
     * @param $redisName
     * @return
     */
    public function __construct($redisName){
        $this->_redisName = $redisName;
    }
    /**
     * @param
     * @return
     */
    private function getRedis() {
        if ($this->_redis) {
            return $this->_redis;
        }
        Bingo_Timer::start('redis_init');
        $this->_redis = new Bingo_Cache_Redis($this->_redisName);
        Bingo_Timer::end('redis_init');
        if (!$this->_redis || !$this->_redis->isEnable()) {
            Bingo_Log::warning("init fdynamic redis fail.");
            $this->_redis = null;
            return null;
        }
        return $this->_redis;
    }

    /**
     *
     * @param  string $key
     * @return string value , reture false if failed.
     */
    public function getKv($key){
        $redis = $this->getRedis();
        if(is_null($redis)){
            return false;
        }
        $redisInput = array(
            'key'  => $key,
        );
        Bingo_Timer::start(__FUNCTION__);
        $redisOut = $redis -> GET($redisInput);
        Bingo_Timer::end(__FUNCTION__);
        if($redisOut['err_no'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(__FUNCTION__ . " call redis failed ,input:". serialize($redisInput) . ", output:" . serialize($redisOut));
            return false;
        }
        return $redisOut['ret'][$key];
    }


    /**
     * 批量获取Redis kv
     * @param
     * @return array ,return false if failed.
     */
    public function mgetKv($arrKeys) {
        if (!is_array($arrKeys)){
            Bingo_Log::warning("array key is invalid");
            return false;
        }
        foreach ($arrKeys as $key) {
            $key = strval($key);
            $redisInput['reqs'][] = array(
                'key' => $key,
            );
        }
        if (count($redisInput) <= 0) {
            Bingo_Log::warning("no need to del");
            return false;
        }

        $redis = $this->getRedis();
        if (!$redis) {
            Bingo_Log::warning('load redis failed');
            return false;
        }

        Bingo_Timer::start('redis_GET');
        $redisOutput = $redis->GET($redisInput);
        Bingo_Timer::end('redis_GET');

        if($redisOutput['err_no'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(__FUNCTION__ . " call redis failed ,input:". serialize($redisInput) . ", output:" . serialize($redisOutput));
            return false;
        }

        return $redisOutput['ret'];
    }

    /**
     * 批量删除Redis的kv
     * @param
     * @return
     */
    public function mdelKv($arrKeys) {
        if (!is_array($arrKeys)){
            Bingo_Log::warning("array key is invalid");
            return false;
        }
        foreach ($arrKeys as $key) {
            $key = strval($key);
            $redisInput['reqs'][] = array(
                'key' => $key,
            );
        }
        if (count($redisInput) <= 0) {
            Bingo_Log::warning("no need to del");
            return false;
        }
        $redis = $this->getRedis();
        if (!$redis) {
            Bingo_Log::warning('load redis failed');
            return false;
        }

        Bingo_Timer::start('redis_DEL');
        $redisOutput = $redis -> DEL($redisInput);
        Bingo_Timer::end('redis_DEL');

        if($redisOutput['err_no'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(__FUNCTION__ . " call redis failed ,input:". serialize($redisInput) . ", output:" . serialize($redisOutput));
            return false;
        }

        return true;
    }


    /**
     * 设置reids数据
     * @param
     * @return
     */
    public function setKv($arrInput) {
        if (!isset($arrInput['key']) || !isset($arrInput['value'])){
            Bingo_Log::warning("arrInput is invalid");
            return false;
        }
        $seconds = self::DEFAULT_REDIS_EXPIRE;
        if(isset($arrInput['seconds'])){
            $seconds = intval($arrInput['seconds']);
        }
        $redisInput = array(
            'key'     => strval($arrInput['key']),
            'value'   => strval($arrInput['value']),
            'seconds' => $seconds,
        );

        $redis = $this->getRedis();
        if (!$redis) {
            Bingo_Log::warning('load redis failed');
            return false;
        }

        Bingo_Timer::start('redis_SETEX');
        $redisOutput = $redis->SETEX($redisInput);
        Bingo_Timer::end('redis_SETEX');

        if($redisOutput['err_no'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(__FUNCTION__ . " call redis failed ,input:". serialize($redisInput) . ", output:" . serialize($redisOutput));
            return false;
        }
        return true;
    }

    /**
     * 批量设置redis数据
     * @param
     * @return
     */
    public function msetKv($arrInput) {
        if (!is_array($arrInput['kv'])){
            Bingo_Log::warning("array kv is invalid");
            return false;
        }
        $redisInput = array();
        foreach ($arrInput['kv'] as $kv) {
            $key     = strval($kv['key']);
            $value   = strval($kv['value']);
            $seconds = self::DEFAULT_REDIS_EXPIRE;
            if(isset($kv['seconds'])){
                $seconds = intval($kv['seconds']);
            }
            $redisInput['reqs'][] = array(
                'key'     => $key,
                'value'   => $value,
                'seconds' => $seconds,
            );
        }

        if (count($redisInput) <= 0) {
            Bingo_Log::warning("no need to add");
            return false;
        }

        $redis = $this->getRedis();
        if (!$redis) {
            Bingo_Log::warning('load redis failed');
            return false;
        }

        Bingo_Timer::start('redis_SETEX');
        $redisOutput = $redis->SETEX($redisInput);
        Bingo_Timer::end('redis_SETEX');

        if($redisOutput['err_no'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(__FUNCTION__ . " call redis failed ,input:". serialize($redisInput) . ", output:" . serialize($redisOutput));
            return false;
        }
        return true;
    }

    /**
     * 设置reids数据
     * @param
     * @return
     */
    public function hmSetKv($arrInput) {
        if (!isset($arrInput['key']) || !isset($arrInput['fields'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        $seconds = self::DEFAULT_REDIS_EXPIRE;
        if(isset($arrInput['seconds'])){
            $seconds = intval($arrInput['seconds']);
        }
        $redisInput = array(
            'key'     => $arrInput['key'],
            'fields'  => $arrInput['fields'],
            'seconds' => $seconds,
        );

        $redis = $this->getRedis();
        if (!$redis) {
            Bingo_Log::warning('load redis failed');
            return false;
        }

        Bingo_Timer::start('redis_SETEX');
        $redisOutput = $redis->HMSET($redisInput);
        Bingo_Timer::end('redis_SETEX');

        if($redisOutput['err_no'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(__FUNCTION__ . " call redis failed ,input:". serialize($redisInput) . ", output:" . serialize($redisOutput));
            return false;
        }
        return true;
    }

    /**
     * 获取reids数据
     * @param
     * @return
     */
    public function hmGetKv($arrInput) {
        if (!isset($arrInput['key']) || !isset($arrInput['fields'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        $redisInput = array(
            'key'   => $arrInput['key'],
            'field' => $arrInput['fields'],
        );

        $redis = $this->getRedis();
        if (!$redis) {
            Bingo_Log::warning('load redis failed');
            return false;
        }

        Bingo_Timer::start('redis_SETEX');
        $redisOutput = $redis->HMGET($redisInput);
        Bingo_Timer::end('redis_SETEX');

        if($redisOutput['err_no'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(__FUNCTION__ . " call redis failed ,input:". serialize($redisInput) . ", output:" . serialize($redisOutput));
            return false;
        }

        return $redisOutput['ret'];
    }

    /**
     * 获取reids数据
     * @param
     * @return
     */
    public function hgetAllKv($arrInput) {
        if (!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        $redisInput = array(
            'key' => $arrInput['key'],
        );

        $redis = $this->getRedis();
        if (!$redis) {
            Bingo_Log::warning('load redis failed');
            return false;
        }

        Bingo_Timer::start('redis_SETEX');
        $redisOutput = $redis->HGETALL($redisInput);
        Bingo_Timer::end('redis_SETEX');

        if($redisOutput['err_no'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(__FUNCTION__ . " call redis failed ,input:". serialize($redisInput) . ", output:" . serialize($redisOutput));
            return false;
        }

        return $redisOutput['ret'];
    }

    /**
     * 设置reids数据
     * @param
     * @return
     */
    public function hdelKv($arrInput) {
        if (!isset($arrInput['key']) || !isset($arrInput['fields'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        $redisInput = array(
            'key'     => $arrInput['key'],
            'field'  => $arrInput['fields'],
        );

        $redis = $this->getRedis();
        if (!$redis) {
            Bingo_Log::warning('load redis failed');
            return false;
        }

        Bingo_Timer::start('redis_SETEX');
        $redisOutput = $redis->HDEL($redisInput);
        Bingo_Timer::end('redis_SETEX');
        if($redisOutput['err_no'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(__FUNCTION__ . " call redis failed ,input:". serialize($redisInput) . ", output:" . serialize($redisOutput));
            return false;
        }
        return true;
    }
}


