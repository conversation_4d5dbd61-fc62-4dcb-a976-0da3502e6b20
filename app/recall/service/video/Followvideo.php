<?php

/**
 *
 * 关注用户的动态feed召回-视频
 * <AUTHOR>
 * @date 2019-09-11
 *
 */
class Service_Video_Followvideo {

    const USER_FOLLOW_SOURCE_ID = 110080;
    /**
     * 正确的返回
     * @param $ret
     * @return array
     */
    protected static function succRet($ret) {
        $errno = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $ret,
        );
    }

    /**
     * 出错返回
     * @param unknown $errno
     * @return multitype:unknown string
     */
    protected static function errRet($errno) {
        if ($errno === Tieba_Errcode::ERR_SUCCESS) {
            $errno = Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL;
        }
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * 获取用户关注feedlist
     * @param $arrInput
     * @return
     */
    public static function getUserFollowsVideoFeed($arrInput) {
        if (!isset($arrInput['user_id']) || !isset($arrInput['res_num'])) {
            Bingo_Log::warning(__FUNCTION__ . " params error, input:" . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrThread = array();
        //未登录用户直接返回
        $realUid = intval($arrInput['real_uid']);
        if ($realUid == 0) {
             return self::succRet($arrThread);
        }

        $objMultiCall = new Tieba_Multi(__FUNCTION__);
        $arrInput = array(
            'user_id' => intval($arrInput['user_id']),
            'feed_id' => 0,
            'flip' => 0,
            'limit' => 100,
            'bjh_thread_type' => 2,
        );
        $arrMultiInput = array(
            'serviceName' => 'post',
            'method'      => 'getFeedNewsList',
            'ie'          => 'utf-8',
            'input'       => $arrInput,
        );
        //获取百家号文章内容
        $objMultiCall->register('getFeedList-Type-2', new Tieba_Service('post'), $arrMultiInput);
        //获取百家号图文动态内容
        $arrMultiInput['input']['bjh_thread_type'] = 4;
        $objMultiCall->register('getFeedList-Type-4', new Tieba_Service('post'), $arrMultiInput);

        Bingo_Timer::start(__FUNCTION__);
        $objMultiCall->call();
        Bingo_Timer::end(__FUNCTION__);

        $arrBadId = isset($arrInput['bad_ids']) ? array_flip(explode(",", $arrInput['bad_ids'])) : array();

        $ret = $objMultiCall->getResult('getFeedList-Type-2');
        if ($ret['errno'] === Tieba_Errcode::ERR_SUCCESS) {
            foreach($ret['data']['feed_list'] as $row){
                if(!isset($row['feed_info']['thread_id'])){
                    Bingo_Log::warning(__FUNCTION__ . " data is invalid, output:" . serialize($ret));
                    continue;
                }
                $tid = intval($row['feed_info']['thread_id']);
                if (isset($arrBadId[$tid])) {
                    continue;
                }
                $arrThread[] = array(
                    'tid' => $tid,
                    'fid' => 0,
                    'source' => self::USER_FOLLOW_SOURCE_ID,
                    'weight' => 0,
                );
            }
        } else {
            Bingo_Log::warning(__FUNCTION__ . " call service fail! getFeedList-Type-2, output:" . serialize($ret));
        }

        $ret = $objMultiCall->getResult('getFeedList-Type-4');
        if ($ret['errno'] === Tieba_Errcode::ERR_SUCCESS) {
            foreach($ret['data']['feed_list'] as $row){
                if(!isset($row['feed_info']['thread_id'])){
                    Bingo_Log::warning(__FUNCTION__ . " data is invalid, output:" . serialize($ret));
                    continue;
                }
                $tid = intval($row['feed_info']['thread_id']);
                if (isset($arrBadId[$tid])) {
                    continue;
                }
                $arrThread[] = array(
                    'tid' => $tid,
                    'fid' => 0,
                    'source' => self::USER_FOLLOW_SOURCE_ID,
                    'weight' => 0,
                );
            }
        } else {
            Bingo_Log::warning(__FUNCTION__ . " call service fail! getFeedList-Type-4, output:" . serialize($ret));
        }
        $arrThread = Libs_Recall::makeScoreForVideo($arrInput, $arrThread);
        return self::succRet($arrThread);
    }

}