<?php

/**
 * @file: Siamesenet.php
 * @author: song<PERSON><PERSON><PERSON>@baidu.com
 * @datetime: 18/10/24 上午10:18
 * @brief: 孪生神经网络视频协同
 */
class Service_Video_Siamesenet
{
    const REDIS_NAME_USER_HISTORY = 'recomuserhis';
    const REDIS_PREFIX_USER_HISTORY_VIDEO = 'user_his_video_';
    const REDIS_PREFIX_USER_HISTORY_FRS = 'user_his_frs_';

    const REDIS_BDRP_YUELAOU2_NAME = 'yuelaou2';
    const REDIS_YUELAOU2_PREFIX = 'siamesenet_video_cf_';
    const SOURCE_NUM = 114129;//视频孪生神经网络首页浏览历史协同
    const FRS_SOURCE_NUM = 114130;//视频孪生神经网络FRS浏览历史协同

    /**
     * 正确的返回
     * @param $ret
     * @return array
     */
    protected static function succRet($ret)
    {
        $errno = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $ret,
        );
    }

    /**
     * 出错返回
     * @param $errno
     * @return array
     */
    protected static function errRet($errno)
    {
        if ($errno === Tieba_Errcode::ERR_SUCCESS) {
            $errno = Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL;
        }
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * 根据首页的点击历史查的
     * @param $arrInput
     * @return array
     */
    public static function videoSiamesenetRecall($arrInput)
    {
        if (!isset($arrInput['res_num']) || !isset($arrInput['user_id'])) {
            Bingo_Log::warning(__FUNCTION__ . " params error, input:" . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $resNum = intval($arrInput['res_num']);
        $uid = intval($arrInput['user_id']);
        if ($resNum <= 0 || $uid <= 0) {
            Bingo_Log::warning(__FUNCTION__ . " params error, input:" . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $objRedisHis = new Libs_Redis(self::REDIS_NAME_USER_HISTORY);
        $redisKeyHis = self::REDIS_PREFIX_USER_HISTORY_VIDEO . $uid;
        $redisValHis = $objRedisHis->getKv($redisKeyHis);
        if ($redisValHis === false) {
            Bingo_Log::warning(__FUNCTION__ . " call redis fail. [key = $redisKeyHis]");
            return self::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $arrThread = array();
        if (empty($redisKeyHis)) {
            return self::succRet($arrThread);
        }
        $unCompressValue = gzuncompress($redisValHis);
        if (empty($unCompressValue)) {
            return self::succRet($arrThread);
        }
        $arrHis = json_decode($unCompressValue, true);
        if ($arrHis === false) {
            return self::succRet($arrThread);
        }
        $arrHis = array_reverse($arrHis);
        $arrTopHis = array_slice($arrHis, 0, 20);
        $arrCallbackKeys = array();
        foreach ($arrTopHis as $topHis) {
            $arrItem = explode(",", $topHis);
            $tid = $arrItem[0];
            if (intval($tid) > 0) {
                $arrCallbackKeys[] = self::REDIS_YUELAOU2_PREFIX . $tid;
            }
        }
        $objRedis = new Libs_Redisbd(self::REDIS_BDRP_YUELAOU2_NAME);
        $redisValCallback = $objRedis->mgetKv($arrCallbackKeys);
        if ($redisValCallback === false) {
            Bingo_Log::warning(__FUNCTION__ . " call redis fail. key =" . serialize($arrCallbackKeys));
            return self::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        $arrThread = array();
        if (empty($redisValCallback)) {
            return self::succRet($arrThread);
        }

        $arrBadId = isset($arrInput['bad_ids']) ? array_flip(explode(",", $arrInput['bad_ids'])) : array();
        foreach ($redisValCallback as $callbackValue) {
            if (empty($callbackValue)) {
                continue;
            }
            $arrCallbackValue = explode(";", $callbackValue);
            if (empty($arrCallbackValue)) {
                continue;
            }
            $c = 0;
            foreach ($arrCallbackValue as $strItem) {
                $arrItem = explode(":", $strItem);
                $tid = intval($arrItem[0]);
                if (isset($arrBadId[$tid])) {
                    continue;
                }
                $feature = strval($arrItem[1]);
                $newItem = array(
                    'tid' => $tid,
                    'source' => self::SOURCE_NUM,
                    'feature' => $feature,
                );
                $arrThread[$tid] = $newItem;
                $c++;
                if ($c >= 20) {
                    break;
                }
            }
        }
        if (!empty($arrThread)) {
            $arrThread = array_values($arrThread);
        }
        $arrThread = Libs_Recall::makeScoreForVideo($arrInput, $arrThread);
        return self::succRet($arrThread);
    }

    /**
     * 根据 FRS 的点击历史查的
     * @param $arrInput
     * @return array
     */
    public static function videoSiamesenetFrsRecall($arrInput)
    {
        if (!isset($arrInput['res_num']) || !isset($arrInput['user_id'])) {
            Bingo_Log::warning(__FUNCTION__ . " params error, input:" . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $resNum = intval($arrInput['res_num']);
        $uid = intval($arrInput['user_id']);
        if ($resNum <= 0 || $uid <= 0) {
            Bingo_Log::warning(__FUNCTION__ . " params error, input:" . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $objRedisHis = new Libs_Redis(self::REDIS_NAME_USER_HISTORY);
        $redisKeyHis = self::REDIS_PREFIX_USER_HISTORY_FRS . $uid;
        $redisValHis = $objRedisHis->getKv($redisKeyHis);
        if ($redisValHis === false) {
            Bingo_Log::warning(__FUNCTION__ . " call redis fail. [key = $redisKeyHis]");
            return self::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $arrThread = array();
        if (empty($redisKeyHis)) {
            return self::succRet($arrThread);
        }
        $unCompressValue = gzuncompress($redisValHis);
        if (empty($unCompressValue)) {
            return self::succRet($arrThread);
        }
        $arrHis = json_decode($unCompressValue, true);
        if ($arrHis === false) {
            return self::succRet($arrThread);
        }
        $arrHis = array_reverse($arrHis);
        $arrTopHis = array_slice($arrHis, 0, 50);
        $arrCallbackKeys = array();
        foreach ($arrTopHis as $topHis) {
            $arrItem = explode(",", $topHis);
            $tid = $arrItem[0];
            if (intval($tid) > 0) {
                $arrCallbackKeys[] = self::REDIS_YUELAOU2_PREFIX . $tid;
            }
        }
        $arrCallbackKeys = array_unique($arrCallbackKeys);
        $objRedis = new Libs_Redisbd(self::REDIS_BDRP_YUELAOU2_NAME);
        $redisValCallback = $objRedis->mgetKv($arrCallbackKeys);
        if ($redisValCallback === false) {
            Bingo_Log::warning(__FUNCTION__ . " call redis fail. key =" . serialize($arrCallbackKeys));
            return self::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        $arrThread = array();
        if (empty($redisValCallback)) {
            return self::succRet($arrThread);
        }
        $arrBadId = isset($arrInput['bad_ids']) ? array_flip(explode(",", $arrInput['bad_ids'])) : array();
        foreach ($redisValCallback as $callbackValue) {
            if (empty($callbackValue)) {
                continue;
            }
            $arrCallbackValue = explode(";", $callbackValue);
            if (empty($arrCallbackValue)) {
                continue;
            }
            $c = 0;
            foreach ($arrCallbackValue as $strItem) {
                $arrItem = explode(":", $strItem);
                $tid = intval($arrItem[0]);
                if (isset($arrBadId[$tid])) {
                    continue;
                }
                $feature = strval($arrItem[1]);
                $newItem = array(
                    'tid' => $tid,
                    'source' => self::SOURCE_NUM,
                    'feature' => $feature,
                );
                $arrThread[$tid] = $newItem;
                $c++;
                if ($c >= 20) {
                    break;
                }
            }
        }
        if (!empty($arrThread)) {
            $arrThread = array_values($arrThread);
        }
        return self::succRet($arrThread);
    }

}