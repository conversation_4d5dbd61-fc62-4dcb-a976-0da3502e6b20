<?php
/**
 * 根据你ums特征检索, 13013，video_ums_search
 * User: wangtongyun
 */

class Service_Video_Searchums {

    /**
     * 正确的返回
     * @param $ret
     * @return array
     */
    protected static function succRet($ret) {
        $errno = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data'   => $ret,
        );
    }

    /**
     * 出错返回
     * @param unknown $errno
     * @return multitype:unknown string
     */
    protected static function errRet($errno){
        if($errno === Tieba_Errcode::ERR_SUCCESS){
            $errno = Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL;
        }
        return array(
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @param $arrInput
     * @return
     */
    public static function videoUmsSearch($arrInput){
        if (!isset($arrInput['res_num']) || !isset($arrInput['user_id'])) {
            Bingo_Log::warning(__FUNCTION__ . " params error, input:" . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $recallType = Util_Const::RECALL_TYPE_VIDEO;
        $method = Util_Const::METHOD_VIDEO_UMS_SEARCH;
        $recallInput = Libs_Recall::makeParam($arrInput, $recallType, $method);

        if ($recallInput === false) {
            Bingo_Log::warning(__FUNCTION__ . " make recall params error");
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $res = ral(Util_Const::RECALL_SERVICE_VIDEO, 'post', $recallInput, rand());
        if (!$res || $res['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal(__FUNCTION__ . " call recall service fail, message:" . $res['message']);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $dataList = !empty($res['data']) ? $res['data'] : array();
        $randNum = rand(1, 1000);
        if ($randNum == 5) {
            Bingo_Log::notice(__FUNCTION__ . " FinalReturn: " . serialize($dataList));
        }
        return self::succRet($dataList);
    }
}