<?php
/**
 * ===========================================
 * @desc: 
 * @author: fengzhen
 * @date: 2018-6-28
 * ===========================================
 * @version 1.0.0
 * @copyright Copyright (c) www.baidu.com
 */
class Service_Video_Midpagevideomcn {

    const REDISDB_NAME = 'recom_callback';
    const REDIS_PREFIX = 'midpage_mcn_tid_';
    const SOURCE = 131018;

    const REDIS_BDRP_YUELAOU2 = 'yuelaou2';

    /**
     * 正确的返回
     * @param $ret
     * @return array
     */
    protected static function succRet($ret) {
        $errno = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data'   => $ret,
        );
    }

    /**
     * 出错返回
     * @param unknown $errno
     * @return multitype:unknown string
     */
    protected static function errRet($errno){
        if($errno === Tieba_Errcode::ERR_SUCCESS){
            $errno = Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL;
        }
        return array(
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @param $arrInput
     * @return
     */
    public static function videoMicrVideoMcnRecall($arrInput){
        if (!isset($arrInput['res_num']) || empty($arrInput['tid'])) {
            Bingo_Log::warning(__FUNCTION__ . " params error, input:" . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $resNum = intval($arrInput['res_num']);
        $tid = intval($arrInput['tid']);
        if($resNum <= 0 || $tid <= 0){
            Bingo_Log::warning(__FUNCTION__ . " params error, input:" . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $key = self::REDIS_PREFIX . $tid;
        //$objRedis = new Libs_Redis(self::REDISDB_NAME);
        $objRedis = new Libs_Redisbd(self::REDIS_BDRP_YUELAOU2);

        $redisVal = $objRedis->getKv($key);
        if($redisVal === false){
            Bingo_Log::warning ( __FUNCTION__ . " call redis fail. [key = $key]" );
            return self::errRet ( Tieba_Errcode::ERR_REDIS_CALL_FAIL );
        }
        $arrThread = array();
        if(strlen($redisVal) == 0){
            Bingo_Log::warning ( __FUNCTION__ . " redis value is empty. [key = $key]" );
            return self::succRet($arrThread);
        }
        $arrTids = explode(";", $redisVal);
        if(count($arrTids) > 0){
            $arrBadId = isset($arrInput['bad_ids'])? array_flip(explode(",", $arrInput['bad_ids'])): array();
            foreach ($arrTids as $strTid){
                $arrTmp = explode(  ':', $strTid);
                if(count($arrTmp) != 2){
                    continue;
                }
                $tid = intval($arrTmp[0]);
                $weight = floatval($arrTmp[1]);
                if(isset($arrBadId[$tid])){
                    continue;
                }
                $arrThread[] = array(
                    'tid'    => $tid,
                    'source' => self::SOURCE,
                    'weight' => $weight,
                );
                if(count($arrThread) >= $resNum){
                    break;
                }
            }
        }
        return self::succRet($arrThread);
    }
}