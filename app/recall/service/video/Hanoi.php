<?php
/**
 * 汉诺塔视频在线召回
 * User: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date: 2017/10/12
 * Time: 下午1:46
 */

class Service_Video_Hanoi {
    /**
     * 走新版小流量的tag配置
     */
    private static $_new_service_tag = 'tag_23_offline'; //改个不存在的值来下掉小流量


    /**
     * @brief 各个特征的分桶规则配置、和线下召回保持一致
     */
    private static $_bucketConf = array (
        'user&real&kw' => '0.301,0.398,0.477,0.555,0.602,0.655,0.699,0.754,0.832,1.0',
        'user&interest&fircate' => '0.102,0.169,0.213,0.277,0.407,0.585,0.801,0.965,1.0,1.0',
        'user&interest&prefer_forum' => '0.01,0.01,0.01,0.01,0.012,0.051,0.186,0.441,0.962,1.0',
        'user&interest&kw' => '0.13,0.2,0.222,0.3,0.382,0.5,0.6,0.79,1.0,1.0',
        'user&interest&seccate' => '0.079,0.147,0.191,0.223,0.338,0.471,0.674,0.88,0.995,1.0',
        'user&interest&cls' => '0.113,0.13,0.15,0.176,0.208,0.25,0.311,0.4,0.556,1.0',
        'user&real&prefer_forum' => '0.261,0.3,0.3,0.301,0.301,0.477,0.602,0.701,0.903,1.0',
        'user&interest&topic' => '0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.872',
        'user&interest&sdir' => '0.005,0.008,0.014,0.021,0.033,0.055,0.093,0.163,0.296,1.0',
        'user&interest&predict_forum' => '0.0411,0.0611,0.0744,0.0855,0.0962,0.1076,0.1214,0.1416,0.1777,1.0',
        'user&interest&tag' => '0.102,0.191,0.245,0.416,0.585,0.752,0.907,0.986,1.0,1.0',
        'user&real&cls' => '0.02,0.03,0.043,0.059,0.08,0.107,0.143,0.189,0.282,1.0',
    );

    /**
     * 正确的返回
     * @param $ret
     * @return array
     */
    protected static function succRet($ret) {
        $errno = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data'   => $ret,
        );
    }

    /**
     * 出错返回
     * @param unknown $errno
     * @return multitype:unknown string
     */
    protected static function errRet($errno){
        if($errno === Tieba_Errcode::ERR_SUCCESS){
            $errno = Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL;
        }
        return array(
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @brief 获取召回数据
     * @param
     * @return
     */
    public static function videoHanoiRecall($arrInput) {
        if (!isset($arrInput['res_num']) || !isset($arrInput['user_id'])) {
            Bingo_Log::warning(__FUNCTION__ . " params error, input:" . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $resNum = intval($arrInput['res_num']);
        $uid = intval($arrInput['user_id']);
        if($resNum <= 0 || $uid <= 0){
            Bingo_Log::warning(__FUNCTION__ . " params error, input:" . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //Test 造参数
//        $input=array('user_id' => $arrInput['user_id'], 'features' => array('tieba_user_base'));
//        $res = Tieba_Service::call('yuelaou2', 'getUserFeature', $input, NULL, NULL, 'post', 'php', 'utf-8');
//        $arrInput['user_info'] = $res['data']['tieba_user_base']['user_info'];
//        $arrInput['user_info_real'] = $res['data']['tieba_user_base']['user_info_real'];
//        unset($arrInput['bad_ids']);
//        $arrInput['bad_ids'] = '5250393988,5341205323,5357350724';
//        $arrInput['abtest_tag'] = 'normal';
        //test end

        $userBase = !empty($arrInput['user_info']) ? $arrInput['user_info'] : array();
        $userReal = !empty($arrInput['user_info_real']) ? $arrInput['user_info_real'] : array();
        $badIds   = !empty($arrInput['bad_ids']) ? explode(',', $arrInput['bad_ids']) : array();
        if (empty($userBase) && empty($userReal)) {
            Bingo_Log::warning(__FUNCTION__ . " user feature not found, input:" . serialize($arrInput));
            return self::succRet(array());
        }

        if (!empty($userBase)) {
            $userBase = Bingo_Encode::convert($userBase, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
            $userBase = json_decode($userBase, true);
        }
        if (!empty($userReal)) {
            $userReal = Bingo_Encode::convert($userReal, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
            $userReal = json_decode($userReal, true);
        }
        if ($userBase === false || $userReal === false || $userBase === null || $userReal === null) {
            Bingo_Log::warning(__FUNCTION__ . " json_decode return false, input:" . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!empty($arrInput['abtest_tag']) && $arrInput['abtest_tag'] == self::$_new_service_tag) {
            $videoUserFeature = Libs_Hanoi::makeParams($uid, $userBase, $userReal);
        } else {
            $videoUserFeature = self::makeFeature($uid, $userBase, $userReal);
        }
        $featureLen = count($videoUserFeature, 1);
        if ($featureLen > 800) {
            $logCont = serialize($videoUserFeature);
            Bingo_Log::warning('hanoi_params_too_long: ' . __FUNCTION__ . ' ' . $logCont);
            return self::succRet(array());
        }

        if (!empty($badIds) && is_array($badIds)) {
            foreach($badIds as $k => $bId) {
                $badIds[$k] = intval($bId);
            }
        }
        $ralInput = array(
            'feature'      => $videoUserFeature,
            'thread_count' => $resNum,
            'visited_tids' => $badIds,
        );
        if (!empty($arrInput['abtest_tag']) && $arrInput['abtest_tag'] == self::$_new_service_tag) {
            $ralInput['exp_flag'] = '/cpu/tieba/multiview/user';
        } else {
            $ralInput['exp_flag'] = '/cpu/tieba/video/user';
        }

        //上新的cmd小流量、18-3-14
        /*if (!empty($arrInput['abtest_tag']) && $arrInput['abtest_tag'] == 'tag_20') {
            $ralInput['exp_flag'] = '/cpu/tieba/video/user';
        }*/

        $strParam = json_encode($ralInput, JSON_UNESCAPED_UNICODE);
        if (!empty($arrInput['abtest_tag']) && $arrInput['abtest_tag'] == self::$_new_service_tag) {

        } else {
            $strParam = Bingo_Encode::convert($strParam, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
        }

        Bingo_Timer::start('get_hanoi_video');
        $serverName = 'TiebaTriggerService_new';
        if (!empty($arrInput['abtest_tag']) && $arrInput['abtest_tag'] == self::$_new_service_tag) {
            ral_set_pathinfo('TiebaTriggerService/mvVideoAnnQuery');
        } else {
            ral_set_pathinfo('TiebaTriggerService/videoAnnQuery');
        }
        $data = ral($serverName, 'post', $strParam, rand());
        Bingo_Log::notice(__FUNCTION__ . "ral_output: " . $data);
        $data = !empty($data) ? json_decode($data, true) : false;
        if ($data['code'] !== 200) {
            Bingo_Log::warning(__FUNCTION__ . " service return error code, server name ['TiebaTriggerService'] . input=" . serialize($ralInput) . ',========output : ' . serialize($data));
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        Bingo_Timer::end('get_hanoi_video');

        $arrThread = array();
//        $finalSource = 6684;
//        if (!empty($arrInput['abtest_tag']) && $arrInput['abtest_tag'] == self::$_new_service_tag) {
//            $finalSource = 6689;
//        }
        //热帖过滤
        $redisObj = new Libs_Redis('yuelaou');
        $redisVal = $redisObj->getKv('hot_thread_video_rtdnn_suppress50');
        $redisVal = !empty($redisVal) ? explode(';', $redisVal) : array();
        $redisVal = !empty($redisVal) ? array_flip(array_slice($redisVal, 0, 50)) : array();

        if (!empty($data['items']) && is_array($data['items'])) {
            foreach($data['items'] as $item) {
                //distance 是一个值域在 0-2 的距离值。转换为权重
                $weight = 2 - round($item['distance'], 4);
                $tid = intval($item['item_id']);
                if (isset($redisVal[$tid])) {
                    continue;
                }
                $arrThread[] = array(
                    'tid'    => $tid,
                    'source' => 6684,
                    'weight' => floatval($weight),
                );
            }
        }

		$arrThread = Libs_Recall::makeScoreForVideo($arrInput, $arrThread);
        Bingo_Log::notice(__FUNCTION__ . 'callback_data: ' . serialize($arrThread));
        return self::succRet($arrThread);
    }

    /**
     * @brief 处理特征
     * @param $uid
     * @param $userBase 用户历史特征
     * @param $userReal 用户实时特征
     * @return
     */
    private static function makeFeature($uid, $userBase, $userReal) {
        $videoUserFeature = array(
            'user_base_uid' => strval($uid),
        );
        //这些特征如果存在就直接赋值、否则不传该参数
        $strKey = array('xf', 'lifestage', 'job', 'trade', 'edulevel', 'age', 'gender', 'level', 'status');
        foreach($strKey as $sk) {
            if (isset($userBase[$sk])) {
                $videoUserFeature['user_base_' . $sk] = strval($userBase[$sk]);
            }
        }
        //user_interest_prefer_forum
        $preferList = !empty($userBase['fondlist']) ? $userBase['fondlist'] : array();
        if (!empty($preferList) && is_array($preferList)) {
            $element = array();
            foreach($preferList as $pref) {
                $fid = $pref[0];
                $weight = floatval($pref[1]);
                $index  = self::getBucketId('user&interest&prefer_forum', $weight);
                $element[] = $fid . '_' . $index;
            }
            if (count($element) > 100) {
                $element = array_slice($element, 0, 100);
            }
            $videoUserFeature['user_interest_prefer_forum'] = $element;
        }
        //user_interest_predict_forum
        $forumRec = !empty($userBase['forum_rec']) ? $userBase['forum_rec'] : array();
        if (!empty($forumRec) && is_array($forumRec)) {
            $element = array();
            foreach($forumRec as $fr) {
                $val = $fr[0];
                $weight = $fr[1];
                $index = self::getBucketId('user&interest&predict_forum', $weight);
                $element[] = $val . '_' . $index;
            }
            if (count($element) > 50) {
                $element = array_slice($element, 0, 50);
            }
            $videoUserFeature['user_interest_predict_forum'] = $element;
        }
        //user_interest_sdir
        $cateConsume = !empty($userBase['cate_consume']) ? $userBase['cate_consume'] : array();
        if (!empty($cateConsume) && is_array($cateConsume)) {
            $element = array();
            foreach ($cateConsume as $key => $catWeight) {
                $catArr = explode(':', $key);
                if (count($catArr) != 2 || empty($catArr[0]) || empty($catArr[1])) {
                    continue;
                }
                $index = self::getBucketId('user&interest&sdir', $catWeight);
                $element[] = $catArr[1] . '_' . $index;
            }
            if (count($element) > 50) {
                $element = array_slice($element, 0, 50);
            }
            $videoUserFeature['user_interest_sdir'] = $element;
        }
        //user_interest_kw
        $tagListv1 = !empty($userBase['taglistv2']) ? $userBase['taglistv2'] : array();
        if (!empty($tagListv1) && is_array($tagListv1)) {
            $element = array();
            foreach($tagListv1 as $tg) {
                $val = $tg[0];
                $weight = $tg[1];
                $index = self::getBucketId('user&interest&kw', $weight);
                $element[] = $val . '_' . $index;
            }
            if (count($element) > 100) {
                $element = array_slice($element, 0, 100);
            }
            $videoUserFeature['user_interest_kw'] = $element;
        }
        //user_interest_cls
        $cateList = !empty($userBase['catelist']) ? $userBase['catelist'] : array();
        if (!empty($cateList) && is_array($cateList)) {
            $element = array();
            foreach($cateList as $ct) {
                $val = $ct[0];
                $weight = $ct[1];
                $index = self::getBucketId('user&interest&cls', $weight);
                $element[] = $val . '_' . $index;
            }
            if (count($element) > 50) {
                $element = array_slice($element, 0, 50);
            }
            $videoUserFeature['user_interest_cls'] = $element;
        }
        //user_interest_fircate
        $firCate = !empty($userBase['firCate']) ? $userBase['firCate'] : array();
        if (!empty($firCate) && is_array($firCate)) {
            $element = array();
            foreach($firCate as $key => $fc) {
                $val = $key;
                $weight = isset($fc['tcnt']) ? $fc['tcnt'] / 100 : 0;
                $index = self::getBucketId('user&interest&fircate', $weight);
                $element[] = $val . '_' . $index;
            }
            if (count($element) > 50) {
                $element = array_slice($element, 0, 50);
            }
            $videoUserFeature['user_interest_fircate'] = $element;
        }
        //user_interest_seccate
        $secCate = !empty($userBase['secCate']) ? $userBase['secCate'] : array();
        if (!empty($secCate) && is_array($secCate)) {
            $element = array();
            foreach($secCate as $key => $sc) {
                $val = $key;
                $weight = isset($sc['tcnt']) ? $sc['tcnt'] / 100 : 0;
                $index = self::getBucketId('user&interest&seccate', $weight);
                $element[] = $val . '_' . $index;
            }
            if (count($element) > 50) {
                $element = array_slice($element, 0, 50);
            }
            $videoUserFeature['user_interest_seccate'] = $element;
        }
        //user_interest_tag
        $vTag = !empty($userBase['vTag']) ? $userBase['vTag'] : array();
        if (!empty($vTag) && is_array($vTag)) {
            $element = array();
            foreach($vTag as $key => $vt) {
                $val = $key;
                $weight = isset($vt['tcnt']) ? $vt['tcnt'] / 100 : 0;
                $index = self::getBucketId('user&interest&tag', $weight);
                $element[] = $val . '_' . $index;
            }
            if (count($element) > 50) {
                $element = array_slice($element, 0, 50);
            }
            $videoUserFeature['user_interest_tag'] = $element;
        }
        //user_real_prefer_forum
        $realForumList = !empty($userReal['forumlist']) ? $userReal['forumlist'] : array();
        if (count($realForumList) > 50) {
            $realForumList = self::getTopWeight($realForumList, 50);
        }
        if (!empty($realForumList) && is_array($realForumList)) {
            $element = array();
            if (count($realForumList) > 50) {
                $realForumList = array_slice($realForumList, 0, 50);
            }
            foreach($realForumList as $rf) {
                $val = $rf[0];
                $weight = $rf[1];
                $index = self::getBucketId('user&real&prefer_forum', $weight);
                $element[] = $val . '_' . $index;
            }
            if (count($element) > 50) {
                $element = array_slice($element, 0, 50);
            }
            $videoUserFeature['user_real_prefer_forum'] = $element;
        }
        //user_real_kw
        $realTagList = !empty($userReal['taglist']) ? $userReal['taglist'] : array();
        if (count($realTagList) > 50) {
            $realTagList = self::getTopWeight($realTagList, 50);
        }
        if (!empty($realTagList) && is_array($realTagList)) {
            $element = array();
            if (count($realTagList) > 50) {
                $realTagList = array_slice($realTagList, 0, 50);
            }
            foreach($realTagList as $rt) {
                $val = $rt[0];
                $weight = $rt[1];
                $index = self::getBucketId('user&real&kw', $weight);
                $element[] = $val . '_' . $index;
            }
            if (count($element) > 50) {
                $element = array_slice($element, 0, 50);
            }
            $videoUserFeature['user_real_kw'] = $element;
        }
        //user_real_cls
        $realCateList = !empty($userReal['catelist']) ? $userReal['catelist'] : array();
        if (!empty($realCateList) && is_array($realCateList)) {
            $element = array();
            foreach($realCateList as $rc) {
                $val = $rc[0];
                $weight = $rc[1];
                $index = self::getBucketId('user&real&cls', $weight);
                $element[] = $val . '_' . $index;
            }
            if (count($element) > 50) {
                $element = array_slice($element, 0, 50);
            }
            $videoUserFeature['user_real_cls'] = $element;
        }

        return $videoUserFeature;
    }

    /**
     * @brief 获取该特征在对应分桶中的index
     * @param $prefix 特征key
     * @param $val 特征值
     * @return int
     */
    private static function getBucketId($prefix, $val) {
        $val = floatval($val);
        $index = 0;
        if (!isset(self::$_bucketConf[$prefix])) {
            return false;
        }
        $bucketList = explode(',', self::$_bucketConf[$prefix]);
        foreach($bucketList as $v) {
            if ($val <= floatval($v)) {
                return $index;
            }
            $index++;
        }
        return $index;
    }

    /**
     * @brief 获取权重高的前几个数组元素
     * @param $data, [1] 保存权重
     * @param $cn 获取多少条
     * @return array
     */
    private static function getTopWeight($data, $cn) {
        if (empty($data)) {
            return array();
        }
        $cn = !empty($cn) ? $cn : 50;

        $sort = array(
            'direction' => 'SORT_DESC',
            'field' => '1',
        );

        $arrSort = array();
        foreach($data as $uniqid => $row) {
            foreach($row as $k => $v) {
                $arrSort[$k][$uniqid] = $v;
            }
        }

        array_multisort($arrSort[$sort['field']], constant($sort['direction']), $data);
        return array_slice($data, 0, $cn);
    }
}
