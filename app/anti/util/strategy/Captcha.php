<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 14-8-1:PM4:47
 * @version 1.0
 */

class Util_Strategy_Captcha
{
    const APPID = 1;
    const TPL = 'tb';

    private static $appCapConfig = array(
        'frs' => array(
            'expire_time' => 86400,
            'name' => "frs_auth_mobile",
        ),
    );

    public static function checkIfVerifyPhoneCap($userId, $baiduId, $ip, $app)
    {
        $expireTime = self::getAppCapExpireTime($app);
        $authName = self::getAppCapAuthName($app);
        if (!$expireTime || !$authName) {
            throw new Exception(__FUNCTION__ . ' call Util_Strategy_Captcha::checkIfVerifyPhoneCap error, [maybe req app is not exist] ' . serialize($app), Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $widget = new Passport_Pauth_AuthWidget(self::APPID, self::TPL);
        $authed = $widget->setExpire($expireTime)->setNames($authName)->query($userId, $baiduId, $ip);
        return $widget->isAuthed();
    }

    public static function getPhoneCapToken($userId, $baiduId, $ip, $app)
    {
        $authName = self::getAppCapAuthName($app);
        if (!$authName) {
            throw new Exception(__FUNCTION__ . ' call Util_Strategy_Captcha::getPhoneCapToken error, [maybe req app is not exist] ' . serialize($app), Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $widget = new Passport_Pauth_AuthWidget(self::APPID, self::TPL);
        return $widget->getToken($userId, $baiduId, $authName, $ip, array(Passport_Pauth_AuthWidget::FIELD_MOBILE), Passport_Pauth_AuthWidget::SELECT_ANY);
    }

    public static function getAppCapExpireTime($app)
    {
        if (!isset(self::$appCapConfig[$app])) {
            return false;
        }
        return self::$appCapConfig[$app]['expire_time'];
    }

    public static function getAppCapAuthName($app)
    {
        if (!isset(self::$appCapConfig[$app])) {
            return false;
        }
        return self::$appCapConfig[$app]['name'];
    }
} 