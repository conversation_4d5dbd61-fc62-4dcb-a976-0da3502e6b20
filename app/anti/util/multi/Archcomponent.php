<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file ActsctrlMulti.php
 * <AUTHOR>
 * @date 2013/12/09 17:53:36
 * @brief 
 *  
 **/


class Util_Multi_Archcomponent implements Tieba_Multi_Caller{
    public function transRalMultiReq($input) {
        if (!isset($input['strMethod'])) {
            $input['strMethod'] = 'query';
        }
        if (!isset($input['intBalanceKey'])){
            $input['intBalanceKey'] = self::_genBalanceKey();
        }

        return array(
            'service'=> $input['strService'],
            'method' => $input['strMethod'],
            'input'  => $input['input'],
            'extra'  => $input['intBalanceKey'],
            'header' => $input['header'],
        );

    }


    public function getRalMultiRes($ralRes) {
        return $ralRes;
    }

    private static function _genBalanceKey() {
        $nowTime = gettimeofday();
        srand($nowTime['usec']);
        return mt_rand();
    }
}






/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
