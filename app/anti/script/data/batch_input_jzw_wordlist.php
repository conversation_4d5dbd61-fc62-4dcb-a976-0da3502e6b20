<?php
//usage:
//在测试环境
//修改  ral.conf  , 将 "service_mis" 改为线上配置 
//执行命令 ~/orp001/php/bin/php batch_input_wordlist.php
ini_set("memory_limit", "2G");


//批量导入数据

require_once(dirname(__FILE__).'/../../script_init.php'); //引入脚本初始化文件
init_script_env('wordlist');                         //设置脚本所属模块

$arrDbConf = array(
				   'host'          => '************:6057',
				   'user'          => 'rd',
				   'passwd'        => 'MhxzKhl',
				  );

$strDbName = 'mis';

//connect mysql
$mysqlLink = mysql_connect($arrDbConf['host'], $arrDbConf['user'], $arrDbConf['passwd']) or
die("Could not connect: " . mysql_error());

mysql_select_db($strDbName);


$strTableName = 'fds_diggrave';
$value = 1;
$strRemark = '从资源区mis导入';
$intExpire = 0;


$strSql = 'select * from mis_wordlist_jzw limit 1';
$result = mysql_query($strSql,$mysqlLink);

//批量增加新key
//$i = 0;
while ($row = mysql_fetch_array($result, MYSQL_ASSOC)) {

	Bingo_Log::getModule()->flush();
	if(!is_numeric($row['word'])){
		continue;
	}
	$arrInput = array();
	$arrInput['table_name'] 	= $strTableName;
	$arrInput['create_user'] 	= $row['modifier'];//iconv('GBK','UTF-8//IGNORE',$row['modifier']);
	$arrInput['create_time'] 	= $row['modifytime'];
	$arrInput['value'] = $value;
	$arrInput['remark'] = iconv('GBK','UTF-8//IGNORE',$row['memo']);
	$arrInput['expire'] = intval($row['endtime'])===0 ? intval($row['endtime']) : (intval($row['endtime'])-intval($row['modifytime']))/86400;
	$arrInput['key'] = $row['word'];
	$arrInputPara = array();
	$arrInputPara['input'] = $arrInput;
	$arrRet =  Tieba_Service::call("mis","addItem",$arrInputPara);
	if($arrRet === false){
		$arrRet =  Tieba_Service::call("mis","addItem",$arrInputPara);
	}
	var_dump($arrRet,__FILE__,__LINE__);
	
	if(!is_null($row['deleter']) || (intval($row['endtime']) < time() && intval($row['endtime']) > 0)){
		$arrInput = array();
		$arrInput['table_name'] 	= $strTableName;
		$arrInput['create_user'] 	= $row['modifier'];//iconv('GBK','UTF-8//IGNORE',$row['modifier']);
		$arrInput['create_time'] 	= $row['modifytime'];
		$arrInput['value'] = $value;
		$arrInput['remark'] = iconv('GBK','UTF-8//IGNORE',$row['memo']);
		$arrInput['expire'] = intval($row['endtime'])===0 ? intval($row['endtime']) : (intval($row['endtime'])-intval($row['modifytime']))/86400;
		$arrInput['key'] = $row['word'];
		$arrInput['delete_user'] 	= is_null($row['deleter']) ? '': $row['deleter'];//iconv('GBK','UTF-8//IGNORE',$row['deleter']);
		$arrInput['delete_time'] 	= is_null($row['deletetime']) ? $row['endtime']: $row['deletetime'];
		$arrInputPara['input'] = $arrInput;
		$arrRet =  Tieba_Service::call("mis","delItem",$arrInputPara);
		if($arrRet === false){
			$arrRet =  Tieba_Service::call("mis","delItem",$arrInputPara);
		}
	}
	var_dump($arrRet,__FILE__,__LINE__);

	if(isset($arrRet['errno']) && Tieba_Errcode::ERR_SUCCESS == $arrRet['errno']){
		Bingo_Log::debug("add success. [key:".$strFname."]");
	}else{
		$fp = fopen("log/jzw_failed.txt",'a+');
		$ddd=sprintf("id:%s\tword:%s\t\r\n",$row['id'],$row['word']);
		fwrite($fp, $ddd, strlen($ddd));
		fclose($fp);
		Bingo_Log::warning("add fail:  [key:".$strFname."] [input:".serialize($arrInput)."] [arr_ret:".serialize($arrRet)."]");

	}

	usleep(5);
}



////如果是删除旧key，增加新key的情况
//while ($row = mysql_fetch_array($result, MYSQL_ASSOC)) {
//
//	Bingo_Log::getModule()->flush();
//
//	//$i++;
//	//if($i >= 30){
//	//	break;
//	//}
//	$strFname = $row['name'];
//
//	$arrInput = array();
//	$arrInput['table_name'] 	= $strTableName;
//	$arrInput['create_user'] 	= 'script01';
//	$arrInput['create_time'] 	= time();
//	$arrInput['delete_user'] 	= 'script01';
//	$arrInput['delete_time'] 	= time();
//	$arrInput['value'] = $value;
//	$arrInput['remark'] = $strRemark;
//	$arrInput['expire'] = $intExpire;
//	$arrInput['key'] = $strFname;
//	$arrInputPara = array();
//	$arrInputPara['input'] = $arrInput;
//	$arrRet =  Tieba_Service::call("mis","delItem",$arrInputPara);
//	var_dump($arrRet,__FILE__,__LINE__);
//
//	if(isset($arrRet['errno']) && Tieba_Errcode::ERR_SUCCESS == $arrRet['errno']){
//		Bingo_Log::debug("del success. [key:".$strFname."]");
//		$arrRet =  Tieba_Service::call("mis","addItem",$arrInputPara);
//		var_dump($arrInput,$arrRet,__FILE__,__LINE__);
//
//		if(isset($arrRet['errno']) && Tieba_Errcode::ERR_SUCCESS == $arrRet['errno']){
//			Bingo_Log::debug("add success. [key:".$strFname."]");
//
//		}else{
//			Bingo_Log::warning("add fail:  [key:".$strFname."] [input:".serialize($arrInput)."] [arr_ret:".serialize($arrRet)."]");
//
//		}
//
//	}else{
//		Bingo_Log::warning("del fail:  [key:".$strFname."] [input:".serialize($arrInput)."] [arr_ret:".serialize($arrRet)."]");
//
//	}
//
//	usleep(5);
//	//exit();
//}

