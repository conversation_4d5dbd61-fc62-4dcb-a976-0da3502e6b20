<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file antiTbmaskQuery.php
 * <AUTHOR>
 * @date 2013/12/03 16:19:17
 * @brief 
 *  
 **/


$req=array(
    'req'=>array(
        'mask_list'=>array('anti_browse'),
        'strMethod'=>'tbmask_query',
        'id_list'=>array(990814920,562560872,0,123123),
        ),
    );
$res = Tieba_Service::call('anti','antiTbmaskQuery',$req);
var_dump($res);





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
