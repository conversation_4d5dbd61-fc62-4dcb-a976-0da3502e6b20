<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file antiFnameFilter.php
 * <AUTHOR>
 * @date 2013/12/03 12:56:56
 * @brief 
 *  
 **/

$req=array(
        'req'=>array(
            'fname'=>iconv("UTF-8","GBK//IGNORE",'07ʽ����'),
        ),
    );

$res = Tieba_Service::call('anti','antiFnameFilter',$req);
var_dump($res);
print_r('ssss'.iconv("GBK","UTF-8//IGNORE",$res['res']['str_reason']));


/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
