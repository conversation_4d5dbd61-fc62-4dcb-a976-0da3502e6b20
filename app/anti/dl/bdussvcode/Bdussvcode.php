<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015:06:25 12:00:27
 */
class Dl_Bdussvcode_Bdussvcode {
	const REDIS_NAME="felib";
	const SERVICE_NAME = "Service_Bdussvcode";
	const EXPIRE_TIME = 604800; //3600*24*7
	protected static $_cache = null;
	
	/**
	 * @brief init
	 *
	 * @return : true if success. false if fail.
	 *
	 *
	 */
	private static function _init() {
		self::$_expire=intval(self::EXPIRE_TIME);
		return true;
	}

	/**
	 * @brief get error return
	 * @param 
	 * errno
	 * @return : true if success. false if fail.
	 *
	 *
	 */
	private static function _errRet($errno) {
		return array (
			'errno' => $errno,
			'errmsg' => Tieba_Error::getErrmsg ( $errno ), 
		);
	}
	
	/**
	 * @brief get cache obj.
	 * @return: obj of Bingo_Cache_Memcached, or null if connect fail.
	
	**/	
	private static function _getCache() {
		if (self::$_cache) {
			return self::$_cache;
		}
		Bingo_Timer::start ( 'cacheinit' );
		self::$_cache = new Bingo_Cache_Redis(self::REDIS_NAME);
		Bingo_Timer::end ( 'cacheinit' );
		
		if (! self::$_cache || ! self::$_cache->isEnable ()) {
			Bingo_Log::warning ( "get cache failed:in _getCache,init cache fail." );
			self::$_cache = null;
			return null;
		}
		return self::$_cache;
	}
	
	/**
	 * @brief set bduss vcode
	 * @param 
	 * key
	 * value
	 * expire
	 * @return : true if success. false if fail.
	 *
	 *
	 */
	public static function setBdussVcode($key,$value,$expire=0){
		if(!self::$_cache){
			$initRes=self::_getCache();
			if(!$initRes){
				Bingo_Log::warning ( "init redis fail." );
				return false;
			}
		}
		if($expire<=0){
			$expire=self::$_expire;
		}
		$arrSetParams = array('key'=>$key,'value'=>intval($value));
		$arrRet = self::$_cache->SET($arrSetParams);
		if($arrRet===false || $arrRet['err_no'] !== 0){
			Bingo_Log::pushNotice("setbdussvcode:$key",-1);
			Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
			return false;
		}else if($expire > 0){
			Bingo_Log::pushNotice("setbdussvcode:$key",$value);
			$arrExpireParams = array(
				'key' => $key,
				'seconds' => intval($expire),
			);
			$arrRet = self::$_cache->EXPIRE($arrExpireParams);
			if($arrRet===false || $arrRet['err_no'] !== 0){
				return false;
			}
		}
		return true;
	}

	/**
	 * @brief get bduss vcode
	 * @param 
	 * errno
	 * @return : value if success. false if fail.
	 *
	 *
	 */
	public static function getBdussVcode($key){
		if(!self::$_cache){
			$initRes=self::_getCache();
			if(!$initRes){
				Bingo_Log::warning ( "init redis fail." );
				return false;
			}
		}
		$arrGetParams = array(
			'key' => $key,
		);
		$arrRet = self::$_cache->GET($arrGetParams);
		if($arrRet===false || $arrRet['err_no'] !== 0){
			Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
			return false;
		}
		if(!is_null($arrRet['ret'][$key])){
			Bingo_Log::pushNotice("getbdussvcode:$key",1);
			$data = intval($arrRet['ret'][$key]);
			return $data;
		}else{
			Bingo_Log::pushNotice("hitRedis_key:$key",0);
			return false;
		}
	}

	/**
	 * @brief remove redis
	 * @param 
	 * key
	 * @return : true if success.
	 *
	 *
	 */
	public static function removeBdussVcode($key){
		if(!self::$_cache){
			$initRes=self::_getCache();
			if(!$initRes){
				Bingo_Log::warning ( "init redis fail." );
				return false;
			}
		}
		$arrDelParams = array(
			'key' => $key,
		);
		$arrRet = self::$_cache->DEL($arrDelParams);
		if($arrRet===false || $arrRet['err_no'] !== 0){
			Bingo_Log::pushNotice("clearBdussVcode:$key",-1);
			Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
			return false;
		}else{
			Bingo_Log::pushNotice("clearBdussVcode:$key",1);
			return true;
		}
	}

	/**
	 * @brief increase key's value
	 * @param 
	 * key
	 * expire
	 * @return : true if no error occure
	 *
	 *
	 */
	public static function increaseBdussVcode($key,$expire=0){
		if(!self::$_cache){
			$initRes=self::_getCache();
			if(!$initRes){
				Bingo_Log::warning ( "init redis fail." );
				return false;
			}
		}
		if($expire<=0){
			$expire=self::$_expire;
		}
		$arrIncrParams = array('key'=>$key,);
		$arrRet = self::$_cache->INCR($arrIncrParams);
		if($arrRet===false || $arrRet['err_no'] !== 0){
			Bingo_Log::warning("incr call redis error.[".serialize($arrRet)."]");
			return false;
		}else if($expire > 0){
			//Bingo_Log::pushNotice("incrbdussvcode:$key",$arrRet[]);
			$arrExpireParams = array(
				'key' => $key,
				'seconds' => intval($expire),
			);
			$arrRet = self::$_cache->EXPIRE($arrExpireParams);
			if($arrRet===false || $arrRet['err_no'] !== 0){
				return false;
			}
		}
		return true;
	}

	/**
	 * @brief key is exsists or not
	 * @param 
	 * key
	 * @return : ture if exsists
	 *
	 *
	 */
	public static function keyIsExists($key){
		if(!self::$_cache){
			$initRes=self::_getCache();
			if(!$initRes){
				Bingo_Log::warning ( "init redis fail." );
				return false;
			}
		}
		$arrParams = array('key' => $key);
		$arrRet = self::$_cache->EXISTS($arrParams);
		if($arrRet===false || $arrRet['err_no'] !== 0){
			return false;
		}
		return true;
	}

	/**
	 * @brief genkey
	 * @param 
	 * userid
	 * cookiebduss
	 * @return : 
	 * redis key 
	 *
	 */

	public static function genKey($arrInput) {
		if (!isset($arrInput['user_id']) || !isset($arrInput['cookiebduss'])){
			Bingo_Log::warning("input params invalid:in genKey,input is[" . serialize($arrInput) . "]");
			return null;
		}
		return 'vcode_' . $arrInput['user_id'] . '_'  . $arrInput['cookiebduss'];
	}
}
