<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2012:12:26 15:35:03
 * @version 
 * @structs & methods(copied from idl.)
 * 	struct req_actsctrl{
 * 		string rulegroup[];
 * 		uint32_t user_id = optional();
 * 		uint32_t ip = optional();
 * 		string forum_name = optional();
 * 		uint32_t forum_id = optional();
 * 	};
 * 	
 * 	struct res_actsctrl{
 * 		uint32_t error;
 * 		uint32_t need_vcode = optional();
 * 	};
 * 	
 * 	service actsctrl{
 * 		void actsctrlQuery(req_actsctrl req, out res_actsctrl res);
 * 		void actsctrlSubmit(req_actsctrl req, out res_actsctrl res);
 * 	};
 * 	
**/
class Dl_Mismonitor_Mismonitor{

const SERVER_NAME = 'mismonitor';
const RAL_SERVICE_NAME = 'mismonitor';
const GET_METHOD  = 'get';
const POST_METHOD = 'post';
const GET_REQUEST_URL  = '/monitorpf/api/insert';
const GET_POSt_URL  = '/monitorpf/api/addthread';

private static function _call($arrInput,$strService,$intBalanceKey){
    $strTimer = 'ral_call_'.self::SERVER_NAME.'_'.$strService;
    Bingo_Timer::start($strTimer);
    Bingo_Log::debug("ral call [input".serialize($arrInput)."]");
    $out = Tieba_Ral::call($strService,'query',$arrInput,$intBalanceKey);
    Bingo_Timer::end($strTimer);
    if (!$out || !is_array($out) || empty($out)){
        Bingo_Log::warning("ral call error[service:{$strService}] [input".serialize($arrInput)."]");
        return false;
    }
    return $out;
}
/**
 * @brief
 * @params: $arrInput:
 * 	req_actsctrl req
 * @return: $arrOutput:
 * 	res_actsctrl res
**/
public static function mismonitorSubmit($arrInput){

	$arrParam = array(
		'method'   => self::POST_METHOD,
		'post_var' => array(
			'data' => $arrInput,
		),
	);

	$_objRpc = new Bd_Rpc_Camel(self::RAL_SERVICE_NAME);
	$_objRpc->setMethod(self::genGetUrl(self::GET_REQUEST_URL,$arrParam['post_var']));
	$retStr = $_objRpc->call($arrParam);

	if ($retStr !== false){
		$jsonArr = Bingo_String::json2array($retStr);
		return $jsonArr;
	}
	Bingo_Log::warning('call service failed:mis_monitor,input is['.serialize($arrParam).'].');
	return false;
}

public static function mismonitorAddThread($arrInput){

	$arrParam = array(
		'method'   => self::POST_METHOD,
		'post_var' => array(
			'tid' => $arrInput,
			'type' => 0,
		),
	);

	$_objRpc = new Bd_Rpc_Camel(self::RAL_SERVICE_NAME);
	$_objRpc->setMethod(self::genGetUrl(self::GET_POSt_URL,$arrParam['post_var']));
	$retStr = $_objRpc->call($arrParam);

	if ($retStr !== false){
		$jsonArr = Bingo_String::json2array($retStr);
		return $jsonArr;
	}
	Bingo_Log::warning('call service failed:mis_monitor,input is['.serialize($arrParam).'].');
	return false;
}
private static function genGetUrl($url,$data){

	if(empty($data)) return $url;
	if(strpos($url,'?')){
		return $url.'&'.self::arrayToQueryString($data);
	}else{
		return $url.'?'.self::arrayToQueryString($data);
	}
}

private static function arrayToQueryString($arr){

	if (empty($arr)){
		return '';
	}
	if (!is_array($arr)){
		return '';
	}
	$str = '';
	foreach ($arr as $k => $v){
		if(is_array($v)){
			$str .= "$k=".rawurlencode(Bingo_String::array2json($v)).'&';
		}else{
			$str .= "$k=".rawurlencode($v).'&';
		}
	}
	return rtrim($str,'&');
}
}
