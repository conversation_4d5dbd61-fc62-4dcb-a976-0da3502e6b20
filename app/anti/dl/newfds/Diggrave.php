<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Diggrave.php
 * <AUTHOR>
 * @date 2014/02/18 12:06:37
 * @brief 
 *  
 **/

class Dl_Newfds_Diggrave extends Dl_Newfds_Fdsbase{
	const FDS_QUERY_ANTI_DIG_GRAVE = 19990;
	const FDS_QUERY_ANTI_DIG_GRAVE_THREAD = 19994;
	
	public function __construct(){
		$this->_service_type = "fds_diggrave";
		$this->_word_list_name = 'fds_diggrave';
		
		parent::__construct();
	}

	public function check($req){
		$forum_id = 0;
		$grade = 0;
		$perm_flag = 0;
		$is_vote = 0;
		$is_good = 0;
		$thread_id = 0;
		$opgroup = '';
		$create_time = self::INT32_MAX;
		
		$flag = 0;
		if(!isset($req['fid']) || intval($req['fid']) <= 0){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}else{
			$forum_id = intval($req['fid']);
		}
		if(isset($req['opgroup']) && isset(self::$_opgroup_map[$req['opgroup']])){
			$opgroup = $req['opgroup'];
		}
		
		$res = $this->_zrangebyscore($forum_id, time());
		if($res === false || !is_array($res) || count($res) <=0 ){
			return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
		}	
		if(intval($req['command_no']) === self::FDS_QUERY_ANTI_DIG_GRAVE){
			foreach ($res as $anti_dig_grave_info){
				if(empty($opgroup) || ($opgroup === $anti_dig_grave_info['opgroup'])){
					$flag = 1;
					break;
				}
			}
		}else{
			if(isset($req['grade']) && intval($req['grade']) > 0){
				$grade = intval($req['grade']);
				if(isset($req['perm_flag']) && intval($req['perm_flag']) > 0){
					$perm_flag = intval($req['perm_flag']);
				}
			}
			if(isset($req['thread_type_vote']) && intval($req['thread_type_vote']) > 0){
				$is_vote = intval($req['thread_type_vote']);
			}
			
			if(isset($req['thread_type_good']) && intval($req['thread_type_good']) > 0){
				$is_good = intval($req['thread_type_good']);
			}
			
			foreach ($res as $anti_dig_grave_info){
				$end_time = intval($anti_dig_grave_info['end_time']);
				if($end_time < time()){
					continue;
				}
				if(!empty($opgroup) && ($opgroup !== $anti_dig_grave_info['opgroup'])){
					continue;
				}

				$date_threshold = intval($anti_dig_grave_info['thread_threshold']);
				$grade_threshold = intval($anti_dig_grave_info['grade']);
				
				if(self::_check_member($grade, $perm_flag, $grade_threshold) === true){
					Bingo_Log::notice("the user skip for grade[%d], perm_flag[%d], grade_threshold[%d]", $grade, $perm_flag, $grade_threshold);
					continue;
				}
				
				if(!isset($req['forum_id']) || intval($req['forum_id']) <= 0){
					return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
				}else{
					$forum_id = intval($req['forum_id']);
				}
				
				if(($is_good > 0) || ($is_vote > 0)){
					$flag = 1;
					break;
				}
				
				if($create_time === self::INT32_MAX){
					if(!isset($req['thread_id']) || intval($req['thread_id']) <= 0){
						return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
					}else{
						$thread_id = intval($req['thread_id']);
					}
					$post_req = array(
							'thread_id' => $thread_id,
							'offset' => 0,
							'res_num' => 1,
							'see_author' => 0,
							'has_comment' => 0,
							'has_mask' => 0,
					);
					$post_res = Tieba_Service::call('post', 'getPostsByThreadId', $post_req);
					if($post_res === false){
						Bingo_Log::warning('call service failed:post,in check');
						$flag = 0;
					}elseif($post_res['errno'] !== Tieba_Errcode::ERR_SUCCESS){
						Bingo_Log::warning("call service failed:post,in check. input: ". serialize($post_req). "  err_no:[".$post_res['err_no']."] err_msg:[".$post_res['err_msg']."]");
						$flag = 0;
					}else{
						if(isset($post_res['output']['output'][0]['post_infos'][0]['now_time']) && (intval($post_res['output']['output'][0]['post_infos'][0]['now_time']) > 0)){
							$create_time = intval($post_res['output']['output'][0]['post_infos'][0]['now_time']);
							if($create_time < $date_threshold){
								$flag = 1;
							}else{
								$flag = 0;
							}
						}else{
							$flag = 0;
						}
					}
				}else{
					if($create_time < $date_threshold){
						$flag = 1;
					}else{
						$flag = 0;
					}
				}
				
				if($flag > 0){
					break;
				}
			}	
		}
		
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
				'forum_dig_grave' => array(array('anti_dig_grave' => $flag)),
		);
		return $arrOutput;
	}
	
	public function perm($req){
		if(!isset($req['fid']) || intval($req['fid']) <= 0){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$res = 0;
		$forum_id = intval($req['fid']);
		$wordlistRes = $this->_callWordList($forum_id);
		if($wordlistRes !==false && !is_null($wordlistRes)){
			$res = 1;
		}elseif($this->_cansetDefault($forum_id) === true){
			$res = 1;
		}
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
				'dig_grave_shenshou_manage_module' => array(array('has_config' => $res)),
		);
		return $arrOutput;
	}
	
	public function set($req){
		Bingo_Log::notice('in set,input is['.serialize($req).']');
		$forum_id = 0;
		$opgroup = self::$_opgroup_map['bawu'];
		$begin_time = time();
		$end_time = time();
		$op_uid = 0;
		$op_uname = '';
		$op_is_pm = 0;
		$grade = 3;
		$thread_threshold = 10000000;
		$call_from = '';
		$remarks = '';
		
		$db=self::_getDB();
		if(null===$db){
			Bingo_Log::warning("get db failed:in set");
			return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		
		if(!isset($req['fid']) || intval($req['fid']) <= 0){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}else{
			$forum_id = intval($req['fid']);
		}
		
		if(isset($req['opgroup']) && !isset(self::$_opgroup_map[$req['opgroup']])){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}elseif(isset($req['opgroup']) && isset(self::$_opgroup_map[$req['opgroup']])){
			$opgroup = self::$_opgroup_map[$req['opgroup']];
		}else{
			$req['opgroup'] = 'bawu';
		}
		
		if(!isset($req['begin_time']) || intval($req['begin_time']) <= 0){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}else{
			$begin_time = intval($req['begin_time']);
		}
		
		if(!isset($req['end_time']) || intval($req['end_time']) <= 0){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}else{
			$end_time = intval($req['end_time']);
		}
		
		if(!isset($req['op_uid']) || intval($req['op_uid']) <= 0){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}else{
			$op_uid = intval($req['op_uid']);
		}
		
		if(isset($req['op_uname'])){
			$op_uname = $db->escapeString($req['op_uname']);
		}
		
		if(isset($req['op_is_pm']) && intval($req['op_is_pm']) > 0){
			$op_is_pm = intval($req['op_is_pm']);
		}
		
		if(!isset($req['grade']) || intval($req['grade']) <= 0){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}else{
			$grade = intval($req['grade']);
		}
		
		if(isset($req['thread_threshold']) && !is_numeric($req['thread_threshold'])){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}elseif(isset($req['thread_threshold']) && is_numeric($req['thread_threshold'])){
			$thread_threshold = intval($req['thread_threshold']);
		}
		
		if(isset($req['call_from'])){
			$call_from = $db->escapeString($req['call_from']);
		}
		
		if(isset($req['remarks'])){
			$remarks = $db->escapeString($req['remarks']);
		}
		
		if($opgroup == self::$_opgroup_map['bawu'] && isset(self::$_conf[$this->_service_type]['use_quota']) && self::$_conf[$this->_service_type]['use_quota']==1){//如果是吧务检查配额是不是够
			$quota_res = $this->getQuotaLeft($req);
			if($quota_res['errno'] === Tieba_Errcode::ERR_SUCCESS && $quota_res['quota_seconds_left'] <=0){
				Bingo_Log::warning("quota is used up!");
				return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
			}
		}
		
		$res = $this->_zrangebyscore($forum_id, time());
		if($res !== false && is_array($res) || count($res) > 0 ){
			foreach ($res as $anti_dig_grave_info){
				if(intval($anti_dig_grave_info['end_time']) < time()){
					continue;
				}
				if($anti_dig_grave_info['opgroup'] === $req['opgroup']){
					Bingo_Log::warning("anti_diggrave with same opgroup of this forum has been set.");
					return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
				}
			}
		}
		
		$table_index = $forum_id % self::TABLE_NUM;
		$sql = sprintf("INSERT INTO fds_log_%s(`forum_id`,`service_type`,`opgroup`,`begin_time`,`end_time`,`op_uid`,`op_uname`,`op_is_pm`,`grade`,`thread_threshold`,`call_from`,`remarks`) values(%s,%s,%s,%s,%s,%s,'%s',%s,%s,%s,'%s','%s');",
				$table_index,$forum_id,self::$_service_type_map[$this->_service_type],$opgroup,$begin_time,$end_time,$op_uid,$op_uname,$op_is_pm,$grade,$thread_threshold,$call_from,$remarks);
		$res = $db->query($sql);
		if($res ===false){
			Bingo_Log::warning('insert db failed:in addLogDB,errno is['.$db->errno().']['.$db->error().'],sqlStr is['.$sql.']');
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		if($end_time > time()){
			$last_insert_id = $db->getInsertID();;
			$req['fds_log_id'] = $last_insert_id;
			
			$res = $this->_zadd($req);
			if($res === false){
				return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
			}else{
				return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
			}
		}else{
			return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
		}
	}
	
	public function cancel($req){
		Bingo_Log::notice('in cancel,input is['.serialize($req).']');
		$forum_id = 0;
		$opgroup = '';
		$now_time = time();
		$op_uid = 0;
		$op_uname = '';
		$fds_log_id = 0;
		
		$db=self::_getDB();
		if(null===$db){
			Bingo_Log::warning("get db failed:in set");
			return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		
		if(!isset($req['fid']) || intval($req['fid']) <= 0){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}else{
			$forum_id = intval($req['fid']);
		}
		
		if(!isset($req['now_time']) || intval($req['now_time']) <= 0){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}else{
			$now_time = intval($req['now_time']);
		}
		
		if(isset($req['opgroup']) && isset(self::$_opgroup_map[$req['opgroup']])){
			$opgroup = $req['opgroup'];
		}
		
		if(isset($req['op_uid']) && is_numeric($req['op_uid'])){
			$op_uid = intval($req['op_uid']);
		}
		
		if(isset($req['op_uname'])){
			$op_uname = $db->escapeString($req['op_uname']);
		}
		
		if(isset($req['fds_log_id']) && is_numeric($req['fds_log_id'])){
			$fds_log_id = intval($req['fds_log_id']);
		}
		
		$res = $this->_zrangebyscore($forum_id, time());
		if($res === false || !is_array($res) || count($res) <=0 ){
			return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
		}
		$op_target = NULL;
		foreach($res as $diggrave_detail){
			$end_time = intval($diggrave_detail['end_time']);
			if($end_time < time()){
				continue;
			}
			if(($fds_log_id > 0) && ($fds_log_id === intval($diggrave_detail['fds_log_id']))){
				$op_target = $diggrave_detail;
				break;
			}else{
				if(!empty($opgroup) && ($opgroup !== $diggrave_detail['opgroup'])){
					continue;
				}
				$op_target = $diggrave_detail;
				break;
			}
		}
		
		if(!is_null($op_target)){
			$fds_log_id = intval($op_target['fds_log_id']);
			$detail_member = $op_target[self::SERIALIZED_INFO_KEY];
			$res = $this->_zrem($forum_id, $detail_member);
			if($res === false){
				return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
			}	
		}
		if($fds_log_id > 0){
			$table_index = $forum_id % self::TABLE_NUM;
			$sql = sprintf("UPDATE fds_log_%s SET end_time=%s, unset_op_uid=%s, unset_op_uname='%s' WHERE id=%s AND end_time > %s;",
					$table_index, $now_time, $op_uid, $op_uname, $fds_log_id, $now_time);
			$res = $db->query($sql);
			if($res ===false){
				Bingo_Log::warning('update db failed:in cancel,errno is['.$db->errno().']['.$db->error().'],sqlStr is['.$sql.']');
				return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
			}
		}
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	}
	
	public function getBawuLogCount($req){
		if(!isset($req['fid']) || intval($req['fid']) <= 0){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$forum_id = intval($req['fid']);
		$db=self::_getDB();
		if(null===$db){
			Bingo_Log::warning("get db failed:in getBawuLogCount");
			return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$table_index = $forum_id % self::TABLE_NUM;
		$sql = sprintf("SELECT COUNT(1) AS total FROM fds_log_%s WHERE forum_id=%s AND service_type=%s AND opgroup=2",
						$table_index ,$forum_id,self::$_service_type_map[$this->_service_type]);
		$res = $db->query($sql);
		if($res ===false){
			Bingo_Log::warning('query db failed:in getBawuLogCount,errno is['.$db->errno().']['.$db->error().'],sqlStr is['.$sql.']');
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
				'dig_grave_shenshou_manage_module' => array(array('total' => intval($res[0]['total']))),
		);
		return $arrOutput;
	}
	
	public function getBawuLog($req){
		if(!isset($req['fid']) || intval($req['fid']) <= 0){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		if(!isset($req['offset']) || intval($req['offset']) < 0){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		if(!isset($req['num']) || intval($req['num']) <= 0){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$db=self::_getDB();
		if(null===$db){
			Bingo_Log::warning("get db failed:in getBawuLog");
			return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$forum_id = intval($req['fid']);
		$offset = intval($req['offset']);
		$num = intval($req['num']);
		$table_index = $forum_id % self::TABLE_NUM;
		$sql =sprintf("SELECT * from fds_log_%s WHERE forum_id=%s AND service_type=%s AND opgroup=2 order by begin_time desc limit %s,%s" ,
				$table_index, $forum_id,self::$_service_type_map[$this->_service_type],$offset,$num);
		$res = $db->query($sql);
		if($res ===false){
			Bingo_Log::warning('query db failed:in getBawuLog,errno is['.$db->errno().']['.$db->error().'],sqlStr is['.$sql.']');
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
				'dig_grave_shenshou_list' => $res,
		);
		return $arrOutput;
	}
	
	protected function _reqExtractor($req){
		$necessary_keys=array(
				'fid'=> 0,
				'opgroup' => 'bawu',
				'begin_time' => time(),
				'end_time' => time(),
				'op_uid' => 0,
				'thread_threshold' => 0,
				'grade' => 0,
				'fds_log_id' => 0,
		);
		$res = array();
		foreach ($necessary_keys as $key=>$val){
			if(array_key_exists($key,$req)){
				$res[$key] = $req[$key];
			}else{
				$res[$key] = $val;
			}
		}
		return $res;
	}
	
	protected function _cansetDefault($forum_id){
		return false;
	}
}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
