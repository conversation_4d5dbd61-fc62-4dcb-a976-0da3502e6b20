<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file QQfilter.php
 * <AUTHOR>
 * @date 2013/08/21 16:34:19
 * @brief 
 *  
 **/

class Dl_QQfilter_QQfilter{
    const SERVICE_NAME = "Dl_QQfilter_QQfilter";
    protected static $_conf = null;



    /**
     * @brief init
     * @return: true if success. false if fail.

    **/		
    private static function _init(){
        
        //add init code here. init will be called at every public function beginning.
        //not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
        //init should be recalled for many times.
        
        /*if(self::$_conf == null){	
            self::$_conf = Bd_Conf::getConf("/app/anti/dl_panfilename_panfilename");
            if(self::$_conf == false){
                Bingo_Log::warning("init get conf fail.");
                return false;
            }
            
        }*/
        return true; 
    }


    private static function _errRet($errno){
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    public static function qqQuery($arrInput){
        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['req'])){	
            Bingo_Log::warning("input params invalid:in qqQuery,input is[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
            
        //input params.
        $req = $arrInput['req'];

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.
        $res = array();
        //your code here......

        foreach ($req as $uin){
            $sign=md5($uin.'_0_baidu&qq');
            $url="/query2.php?cmd=2&uin=".$uin."&ip=0&sign=".$sign;
            $arrParam = array(
                'method'   => 'post',
                'post_var' => array(
                    'data' => array(
                        'cmd'=>2,
                        'uin'=>intval($uin),
                        'ip'=>'0',
                        'sign'=>$sign,
                    ),
                ),
            );
            $_objRpc = new Bd_Rpc_Camel('query_blackqq');
            $_objRpc->setMethod($url);
            $datas = $_objRpc->call($arrParam);
            if(intval($datas['errcode']) == 0){
                $res[]=array(
                    'qq'=>$uin,
                    'is_black'=>$datas['is_black'],
                    );
            }
        } 

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'res' => $res,
        );
        return $arrOutput;
    }

}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
