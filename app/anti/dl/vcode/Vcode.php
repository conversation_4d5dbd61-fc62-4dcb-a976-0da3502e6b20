<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013:07:15 00:26:27
 * @version 
 * @structs & methods(copied from idl.)
  struct req_vcode{
  uint32_t user_id;
  string date;
  };
  struct req_update{
  uint32_t user_id;
  uint32_t req_time;
  uint32_t forum_id;
  uint32_t is_valid;
  };
  struct res_vcode{
  uint32_t error;
  string errmsg;
  };

  service vcode{
  void vcodeQuery(req_vcode req, out res_vcode res);
  void updateVcodeData( req_update req, out res_vcode res);
  void checkStrategy(req_vcode req,out res_vcode res);
  };
 */
class Dl_Vcode_Vcode {

    const SERVICE_NAME = "Service_Vcode";

    protected static $_conf = null;
    protected static $_db = null;
    protected static $_use_split_db = false;

    const DB_RAL_SERVICE_NAME = "db_service_vcode";

    protected static $_cache = null;

    const REDIS_UNAME = "forum";
    const REDIS_TK = "forum";
    const REDIS_RAL_NAME = "Redis_forum_ueg";
//const REDIS_UNAME="forum";
//const REDIS_TK="forum";
//const REDIS_RAL_NAME="Redis_forum_captcha";????????
    const STYPE_VCODE = "vcode";
    const STYPE_BLOCKIP = "blockip";
    const STYPE_BLOCKID = "blockid";
//???????????
    const OPTYPE_SYSTEM = "system";
    const OPTYPE_BAWU = "bawu";
    const OPTYPE_ADMIN = "admin";
//????????
    const ACT_SET = 0;
    const ACT_UNSET = 1;
    const SET_CODE_CMD = 55003;
    const UNSET_CODE_CMD = 55004;

    public static $INPUT_RIGHT = 1;
    public static $INPUT_ERROR = 0;
    public static $STRATEGYNAME = 'vcode_unset_strategy';
    public static $ALL_FORUM_ID = 0;
    public static $CURRENT_FORUM_ID = -1;
    
    /**
     * @brief get mysql obj.
     * @return: obj of Bd_DB, or null if connect fail.

     * */
    private static function _getDB() {
        if (self::$_db) {
            return self::$_db;
        }
        self::$_db = new Bd_DB();
        if (self::$_db == null) {
            Bingo_Log::warning("get db failed");
            return null;
        }
        if (self::$_use_split_db) {
            $splitDBConfPath = ROOT_PATH . "/conf/db/";
            $splitDBConfFile = "db_dl_anti_vcode.conf";
            $r = self::$_db->enableSplitDB(self::DB_RAL_SERVICE_NAME, $splitDBConfPath, $splitDBConfFile);
            if (!$r) {
                Bingo_Log::warning("get splitdb failed");
                self::$_db = null;
                return null;
            }
            return self::$_db;
        } else {
            Bingo_Timer::start('dbinit');
            $r = self::$_db->ralConnect(self::DB_RAL_SERVICE_NAME);
            Bingo_Timer::end('dbinit');
            if (!$r) {
                Bingo_Log::warning("bd db ral connect fail.");
                self::$_db = null;
                return null;
            }
            return self::$_db;
        }
        return null;
    }

    /**
     * @brief get cache obj.
     * @return: obj of Bingo_Cache_Memcached, or null if connect fail.

     * */
    private static function _getCache() {
        if (self::$_cache) {
            return self::$_cache;
        }
        Bingo_Timer::start('cacheinit');

        //$cacheConf = Bd_Conf::getConf('cache/cache_dl_anti_vcode');
        //if (!$cacheConf) {
        //    Bingo_Log::warning("get cache config fail.[cache/cache_service_userstate]");
        //    return null;
        //}
        //self::$_cache = new Bingo_Cache_Memcached(array(
        //    'conf' => $cacheConf,
        //));
        self::$_cache = new Bingo_Cache_Memcached('');

        Bingo_Timer::end('cacheinit');

        if (!self::$_cache || !self::$_cache->isEnable()) {
            Bingo_Log::warning("init cache fail.");
            self::$_cache = null;
            return null;
        }
        return self::$_cache;
    }

    /**
     * @brief init
     * @return: true if success. false if fail.

     * */
    private static function _init() {

        //add init code here. init will be called at every public function beginning.
        //not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
        //init should be recalled for many times.

        if (self::$_conf == null) {
            self::$_conf = Bd_Conf::getConf("/app/anti/dl_vcode_vcode");
            if (self::$_conf == false) {
                Bingo_Log::warning("init get conf fail.");
                return false;
            }
        }
        return true;
    }

    private static function checkRedisRal(){
        $service = ral_get_service(self::REDIS_RAL_NAME, RAL_CURR_SERVER);
        Bingo_Log::notice(self::REDIS_RAL_NAME.', '.serialize($service));
    }

    private static function _errRet($errno) {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @brief
     * @arrInput
     * uint32_t user_id
     * string date //2013-07-15
     * @return $arrOutput
     * true or false
     * key vcode_user_id
     */
    public static function updateVcodeData($arrInput) {
        if (!isset($arrInput['user_id']) || !isset($arrInput['req_time']) || !isset($arrInput['forum_id']) || !isset($arrInput['is_valid'])) {
            Bingo_Log::warning("input params invalid:in updateVcodeData,input is[" . json_encode($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if ($arrInput['is_valid'] != self::$INPUT_RIGHT && $arrInput['is_valid'] != self::$INPUT_ERROR) {
            Bingo_Log::warning('updaetVcodeData is_valid arg fail');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!is_numeric($arrInput['req_time']) || !is_numeric($arrInput['user_id'])) {
            Bingo_Log::warning('updaetVcodeData req_time arg fail');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intReqTime = (int) $arrInput['req_time'];
        $strDate = date('Y-m-d', $intReqTime);
        $user_id = (int) $arrInput['user_id'];
        $forum_id = (int) $arrInput['forum_id'];
        $is_valid = $arrInput['is_valid'];
       
        $key=self::genKey(array('user_id' => $user_id, 'date' => $strDate, 'is_valid' => $is_valid));
          /*$value=self::getRedisAValue($key);
          if($value==false)
          {
          	 return false;
          }
          if(!isset($value[$key]))
          {
          		self::setRedisAValue($key,1,time()+86400);
          }
          else
          {
          		self::setRedisAValue($key,$value[$key]+1,time()+86400);
          }*/
        self::incrRedisAValue($key,time()+86400);
        return true;
    }

    /*
     * ���ý����֤�븽��Ĳ���
     * vcode_type 0Ϊ�Ź��� 1Ϊ����
     */

    public static function addUnsetStrategy($arrInput) {
        if (!isset($arrInput['vcode_type']) || !isset($arrInput['days']) || !isset($arrInput['times']) || !isset($arrInput['forum_id']) || !isset($arrInput['right_rate'])) {
            Bingo_Log::warning('input params invalid:in addUnsetStrategy,input is[' . serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrResult = self::lpush(self::$STRATEGYNAME, self::genStrategyValue(array(
                            'vcode_type' => $arrInput['vcode_type'],
                            'days' => $arrInput['days'],
                            'times' => $arrInput['times'],
                            'forum_id' => $arrInput['forum_id'],
                            'right_rate' => $arrInput['right_rate'],
        )));
        if ($arrResult === false) {
            Bingo_Log::warning('add UnsetStrategy fail input is ' . serialize($arrInput));
            return false;
        }
        return true;
    }

    public static function getUnsetStrategy($arrInput) {
        return self::lrange(self::$STRATEGYNAME);
    }

    public static function delUnsetStrategy($arrInput) {
        return self::del(self::$STRATEGYNAME);
    }



    public static function checkStrategy($arrInput) {
        if (!isset($arrInput['user_id']) || !isset($arrInput['forum_id'])) {
            Bingo_Log::warning('params miss input is ' . serialize($arrInput));
            return false;
        }
        $user_id = (int) $arrInput['user_id'];
        $forum_id = (int) $arrInput['forum_id'];
        //���н⸽������ж�
            $intRightTimes = 0;
            $intErrorTimes = 0;
            $intTotalTimes = 0;

                $strDate = date('Y-m-d', time());
                $hkeys=array();
                $hkey = self::genKey(array('user_id' => $user_id, 'date' => $strDate, 'is_valid' => self::$INPUT_RIGHT));
                $hkeyError = self::genKey(array('user_id' => $user_id, 'date' => $strDate, 'is_valid' => self::$INPUT_ERROR));
                $hkeys[] = $hkey;
                $hkeys[] = $hkeyError;
                $arrVcodeRecodes = self::getRedisAValues($hkeys);
                if(!isset($arrVcodeRecodes[$hkey])){
                	return false;
                }
                if ($arrVcodeRecodes === false || !isset($arrVcodeRecodes[$hkeyError])) {
                	//Bingo_Log::warning('get vcode err num fail input is ' . serialize($arrInput));
                	if($arrVcodeRecodes[$hkey]<10)
                	{
                		return false;
                	}
                }
                if(!isset($arrVcodeRecodes[$hkeyError])){
                	$arrVcodeRecodes[$hkeyError] = 0;
                }
                
                $intRightTimes = $intRightTimes + ($arrVcodeRecodes[$hkey]);
                $intErrorTimes = $intErrorTimes + ($arrVcodeRecodes[$hkeyError]);
                $intTotalTimes = $intTotalTimes + $intRightTimes + $intErrorTimes;

             $intRealRightTimes = intval($intRightTimes);
            $intRealErrorTimes = intval($intErrorTimes); 
            $intRealTotalTimes = $intRealRightTimes + $intRealErrorTimes;
            $bolCanUnset = false;
            $rate=$intRealRightTimes / $intRealTotalTimes;
            if ($intRealTotalTimes >= 10 && $rate >= 0.8)
            {
                $bolCanUnset = true;
            }
            //�����֤�븽��
                if ($bolCanUnset) {
                    $arrUnsetInput = array();
                    $arrUnsetInput['command_no'] = self::UNSET_CODE_CMD;
                    $arrUnsetInput['op_uname'] = iconv("UTF-8", "GB2312//IGNORE","验证码兜底");
                    $arrUnsetInput['op_uid'] = 12345;
                    $arrUnsetInput['call_from'] = 'anti_vcode';
                    $arrUnsetInput['op_ip'] = 0;
                    $arrUnsetInput['service_type'] = self::STYPE_VCODE;
                    $arrUnsetInput['opgroup'] = self::OPTYPE_SYSTEM;
                    $arrUnsetInput['key'] = $user_id;
                    $arrUnsetInput['user_id'] = $user_id;
                    $arrUnsetInput['forum_id'] = self::$ALL_FORUM_ID;
                    $arrUnsetRes = Tieba_Service::call('unihandle', 'handleCmd', array('req' => $arrUnsetInput));
                    if( false == $arrUnsetRes )
                    {
                    	Bingo_Log::warning('call service failed:unihandle,in checkStrategy');
                    }
                    if (!isset($arrUnsetRes['errno']) || $arrUnsetRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                        Bingo_Log::warning('unset userstate faile,result is[' . serialize($arrUnsetRes).']');
                        return false;
                    }
               			Bingo_Log::warning('vcode unset userstate(), userid:'.$user_id." rate:".$rate);
               			$delKeys = array();
               			$delKeys[] = $hkey;
               			$delKeys[] = $hkeyError;
               			self::dels($delKeys);
                }

        return true;
    }

    public static function getVcodeRecord($arrInput) {
        if (!isset($arrInput['user_id']) || !isset($arrInput['req_time'])) {
            Bingo_Log::warning('input params invalid:in getVcodeRecord,input is[' . serialize($arrInput).']');
            return false;
        }
        $user_id = (int) $arrInput['user_id'];
        $req_time = (int) $arrInput['req_time'];
        $hkey = self::genKey(array('user_id' => $user_id, 'date' => date('Y-m-d', $req_time), 'is_valid' => self::$INPUT_RIGHT));

        $arrVcodeRecodes = self::getVcodeRecord($hkey);
        if ($arrVcodeRecodes === false || !isset($arrVcodeRecodes[$hkey]) || !is_array($arrVcodeRecodes[$hkey])) {
            Bingo_Log::warning('get vcode recode fail input is ' . serialize($arrInput));
            return false;
        }
        $hkeyError = self::genKey(array('user_id' => $user_id, 'date' => date('Y-m-d', $req_time), 'is_valid' => self::$INPUT_ERROR));
        $arrVcodeRecodesError = self::getVcodeRecord($hkeyError);
        if ($arrVcodeRecodesError === false || !isset($arrVcodeRecodesError[$hkeyError]) || !is_array($arrVcodeRecodesError[$hkeyError])) {
            Bingo_Log::warning('get vcode recode fail input is ' . serialize($arrInput));
            return false;
        }
        return array('right' => $arrVcodeRecodes, 'error' => $arrVcodeRecodesError);
    }

    public static function clearVcodeRecord($arrInput){
        if (!isset($arrInput['user_id'])) {
            Bingo_Log::warning('params miss input is ' . serialize($arrInput));
            return false;
        }
        $user_id=$arrInput['user_id'];
        $req_time=time();
        $hkeys = array();
        $hkeys[] = self::genKey(array('user_id' => $user_id, 'date' => date('Y-m-d',$req_time), 'is_valid' => self::$INPUT_RIGHT));
        $hkeys[] = self::genKey(array('user_id' => $user_id, 'date' => date('Y-m-d',$req_time), 'is_valid' => self::$INPUT_ERROR));
        self::dels($hkeys);
        return true;
    }
    
    private static function genKey($arrInput) {
        return 'vcode_' . $arrInput['user_id'] . '_'  . '_' . $arrInput['is_valid'];
    }

    private static function genField($arrInput) {
        return $arrInput['req_time'];
    }

    private static function genValue($arrInput) {
        return $arrInput['forum_id'];
    }

    private static function genStrategyValue($arrInput) {
        return $arrInput['vcode_type'] . '_' . $arrInput['days'] . '_' . $arrInput['times'] . '_' . $arrInput['forum_id'] . '_' . $arrInput['right_rate']; 
    }

    private static function lpush($strStrategyName, $strValue) {
        self::checkRedisRal();
        $arrData = array('key' => $strStrategyName, 'value' => array($strValue));
        $input = array();
        $input['reqs'][] = $arrData;
        $input['uname'] = self::REDIS_UNAME;
        $input['tk'] = self::REDIS_TK;
        $input['method'] = 'LPUSH';
        $result = Tieba_Ral::call(self::REDIS_RAL_NAME, null, $input, rand());
        return self::checkRedisResult(__FUNCTION__, $result);
    }

    private static function lrange($strStrategyName) {
        self::checkRedisRal();
        $arrData = array('key' => $strStrategyName, 'start' => 0, 'stop' => -1);
        $input = array();
        $input['reqs'][] = $arrData;
        $input['uname'] = self::REDIS_UNAME;
        $input['tk'] = self::REDIS_TK;
        $input['method'] = 'LRANGE';
        $result = Tieba_Ral::call(self::REDIS_RAL_NAME, null, $input, rand());
        return self::checkRedisResult(__FUNCTION__, $result);
    }

    private static function del($key) {
        self::checkRedisRal();
        $arrData = array('key' => $key);
        $input = array();
        $input['reqs'][] = $arrData;
        $input['uname'] = self::REDIS_UNAME;
        $input['tk'] = self::REDIS_TK;
        $input['method'] = 'DEL';
        $result = Tieba_Ral::call(self::REDIS_RAL_NAME, null, $input, rand());
        return self::checkRedisResult(__FUNCTION__, $result);
    }
    
    private static function dels($keys) {
        self::checkRedisRal();
    	$input = array();
    	foreach ($keys as $key){
    		$input['reqs'][] = array('key' => $key);
    	}
    	$input['uname'] = self::REDIS_UNAME;
    	$input['tk'] = self::REDIS_TK;
    	$input['method'] = 'DEL';
    	$result = Tieba_Ral::call(self::REDIS_RAL_NAME, null, $input, rand());
    	return self::checkRedisResult(__FUNCTION__, $result);
    }

    private static function hset($hash_key, $field, $value) {
        $arrData = array('key' => $hash_key, 'field' => $field, 'value' => $field . ' ' . $value);
        $input = array();
        $input['reqs'][] = $arrData;
        $input['uname'] = self::REDIS_UNAME;
        $input['tk'] = self::REDIS_TK;
        $input['method'] = 'HSET';
        $result = Tieba_Ral::call(self::REDIS_RAL_NAME, null, $input, rand());
        return self::checkRedisResult(__FUNCTION__, $result);
    }

    private static function hvals($hash_key) {
        $arrKey = array('key' => $hash_key);
        $input = array();
        $input['reqs'][] = $arrKey;
        $input['uname'] = self::REDIS_UNAME;
        $input['tk'] = self::REDIS_TK;
        $input['method'] = 'HVALS';
        $result = Tieba_Ral::call(self::REDIS_RAL_NAME, null, $input, rand());
        return self::checkRedisResult(__FUNCTION__, $result);
    }

    //���redis�ķ���ֵ
    private static function checkRedisResult($function, $result) {
        if (null == $result || false == $result) {
            Bingo_Log::warning("call redis failed:function is[" . $function . "],input is[" . serialize($result) . "]");
            return false;
        }
        if (isset($result['err_no']) && 0 == $result['err_no']) {
            return $result['ret'];
        } else {
            Bingo_Log::warning("redis err_no not equal 0. function:" . $function . " errinfo[" . serialize($result) . "]");
            return false;
        }
    }
    
//��redisȡstringvalue
private static function getRedisAValue($key){
	$input = array();
    $kv= array(
                               'key' =>$key,
                          );
     $input['reqs'][]=$kv;
     $input['uname'] = self::REDIS_UNAME;
     $input['tk'] = self::REDIS_TK;
     $input['method'] = 'GET';
     $result = Tieba_Ral::call(self::REDIS_RAL_NAME,null,$input,rand());	
     //Bingo_Log::warning("getRedisAValue(): ".var_export($input,true));
	 return self::checkRedisResult("getRedisAValue",$result);

}

private static function getRedisAValues($keys){
    self::checkRedisRal();
	$input = array();
	foreach($keys as $key){
		$kv= array(
				'key' =>$key,
		);
		$input['reqs'][]=$kv;
	}
	$input['uname'] = self::REDIS_UNAME;
	$input['tk'] = self::REDIS_TK;
	$input['method'] = 'GET';
	$result = Tieba_Ral::call(self::REDIS_RAL_NAME,null,$input,rand());
	//Bingo_Log::warning("getRedisAValue(): ".var_export($input,true));
	return self::checkRedisResult("getRedisAValue",$result);
}

private static function incrRedisAValue($key,$expiretime=0){
    self::checkRedisRal();
	$input = array();
	$kv=array( 'key'=>$key);
	$input['reqs'][]=$kv;
	$input['uname'] = self::REDIS_UNAME;
	$input['tk'] = self::REDIS_TK;
	$input['method'] = 'INCR';
	$result = Tieba_Ral::call(self::REDIS_RAL_NAME,null,$input,rand());
	if(0==$expiretime)  
	{
		return self::checkRedisResult("setRedisAValue1",$result);
	}
	else  //��Ҫ���ó�ʱʱ��
	{
		self::checkRedisResult("setRedisAValue2",$result);
		if(false!==$result)
		{
			$vv=array(
					'key'=>$key,
					'timestamp'=>$expiretime,
			);
			unset($input);
			$input['reqs'][]=$vv;
			$input['method'] = 'EXPIREAT';
			$input['uname'] = self::REDIS_UNAME;
			$input['tk'] = self::REDIS_TK;
			$re = Tieba_Ral::call(self::REDIS_RAL_NAME,null,$input,rand());
			return self::checkRedisResult("setRedisAValue3",$re);
		}
	}
}

//��redis����stringvalue, $expiretime=0�Ͳ��賬ʱ
private static function setRedisAValue($key,$value,$expiretime=0)
{
	 //Bingo_Log::warning("setRedisAValue():".var_export($key,true)."   value".var_export($value,true));
	$input = array();
	$kv=array( 'key'=>$key, 'value'=>$value, );
     $input['reqs'][]=$kv;
     $input['uname'] = self::REDIS_UNAME;
     $input['tk'] = self::REDIS_TK;
     $input['method'] = 'SET';
     $result = Tieba_Ral::call(self::REDIS_RAL_NAME,null,$input,rand());	
     if(0==$expiretime)  //���賬ʱ
     {
         return self::checkRedisResult("setRedisAValue1",$result);
     }
     else  //��Ҫ���ó�ʱʱ��
     {
     	 self::checkRedisResult("setRedisAValue2",$result);
     	 if(false!==$result)
     	 {
				$vv=array(
                        'key'=>$key,
                        'timestamp'=>$expiretime,
                        );
                unset($input);
     			$input['reqs'][]=$vv;
     			$input['method'] = 'EXPIREAT';
     			$input['uname'] = self::REDIS_UNAME;
                $input['tk'] = self::REDIS_TK;
     			$re = Tieba_Ral::call(self::REDIS_RAL_NAME,null,$input,rand());	
     			return self::checkRedisResult("setRedisAValue3",$re);
     	 }
     }
}

    /**
     * passport 验证码校验
     * @param $arrInput
     * @return $arrOutput
     */
    public static function passPortCheckCaptcha($arrInput) {
        // 入参校验
        if (empty($arrInput) || empty($arrInput['tk']) || empty($arrInput['ds']) || empty($arrInput['scence'])
            || empty($arrInput['client_type']) || empty($arrInput['appname'])){
            Bingo_Log::warning("input params invalid:in passPortCheckCaptcha,input is[".json_encode($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 用户申请服务的标识 appKey
        $appid = 'ab5854a98de565dbb99eebf4bbc83f25';
        // 分给用户的私钥 appSecret
        $appsecret = 'f77e7277e8e508039d4feea238d16d67';
        // 用户将要传递的数据
        $data =  array(
            'cmd' => 'get',
            'token'=> $arrInput['tk'],     // check时从前端获取
            'ds' => $arrInput['ds'],       // check时从前端获取
            'scence'=>$arrInput['scence'], // 使用场景
            'other'=>json_encode(
                array(
                    'client_type'=> $arrInput['client_type'], // 客户端类型
                )
             ),
        );
        // 请求参数
        $querystring = array(
            'timestamp' => time(),
            'appkey' => $appid,
            'data' => self::aes_encode(json_encode($data), $appsecret),
        );
        ksort($querystring);
        $str = http_build_query($querystring);
        $signature = md5($str . $appsecret);
        // 增加签名
        $querystring['signature'] = $signature;
        $headers = array(
            'pathinfo' => '/pm/check',
        );
        $passService = 'passport_captcha';
        $strTimer = 'ral_call_' . $passService;
        Bingo_Timer::start($strTimer);
        $retjson = Tieba_Ral::call($passService, 'post', $querystring, null, $headers);
        Bingo_Timer::end($strTimer);
        $ret = json_decode(self::aes_decode($retjson, $appsecret), true);
        if (empty($ret) || $ret['code'] != 0 || !isset($ret['ret']['is_cap'])) {
            Bingo_Log::warning("call passanti err :in passPortCheckCaptcha,input is[".json_encode($ret)."]");
            return self::_errRet(Tieba_Errcode::ERR_MO_COMMIT_VCODE_CHECK_ERROR);
        }
        $is_captcha = $ret['ret']['is_cap'];
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'is_captcha' => $is_captcha,

        );
        return $arrOutput;
    }

    /**
     * 用配置中的密钥，aes解密
     * @param $str   加密前
     * @param $key   密钥
     * @return string 加密后
     */
    private static function aes_encode($str,$key) {
        if (function_exists('openssl_decrypt')) {
            $paddedstr = $str;
            $method = 'AES-256-ECB';
            //aes block size is fixed 16.
            if (strlen($str) % 16 != 0) {
                $paddedstr = str_pad($str, strlen($str) + 16 - strlen($str) % 16, "\0");
            }
            $ciphertext = openssl_encrypt($paddedstr, $method, $key, $options=OPENSSL_RAW_DATA | OPENSSL_ZERO_PADDING);
            return base64_encode($ciphertext);
        } else {
            $size = mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128,MCRYPT_MODE_ECB);
            $iv = mcrypt_create_iv($size,MCRYPT_RAND);
            $dd= mcrypt_encrypt(MCRYPT_RIJNDAEL_128, $key, $str,MCRYPT_MODE_ECB, $iv);
            return base64_encode($dd);
        }
    }

    /**
     * 用配置中的密钥，aes解密
     * @param $str   反解前
     * @param $key   密钥
     * @return string 反解后
     */
    public static function aes_decode($str,$key) {
        if (function_exists('openssl_decrypt')) {
            $method = "AES-256-ECB";
            $cipherText = base64_decode($str);
            $ret = openssl_decrypt($cipherText, $method, $key, $options=OPENSSL_RAW_DATA | OPENSSL_ZERO_PADDING);
            return rtrim(rtrim($ret, chr(0)), chr(7));
        } else {
            $str=str_replace(" ", "+", $str);
            $str=base64_decode($str);
            $size = mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128,MCRYPT_MODE_ECB);
            $iv = mcrypt_create_iv($size ,MCRYPT_RAND);
            return rtrim(mcrypt_decrypt(MCRYPT_RIJNDAEL_128, $key, $str,MCRYPT_MODE_ECB, $iv),"\0");
        }
    }
}
