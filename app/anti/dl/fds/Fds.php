<?php

class Dl_Fds_Fds{

const SERVER_NAME = 'fds';
const RAL_SERVICE_NAME = 'fds';

private static function _call($arrInput, $strMethod){
    $strTimer = 'ral_call_'.self::SERVER_NAME.'_'.$strMethod;
    Bingo_Timer::start($strTimer);
    Bingo_Log::debug("ral call [method:{$strMethod}] [input".serialize($arrInput)."]"); 
    $arrOutput = Tieba_Ral::call(self::RAL_SERVICE_NAME, 'post', $arrInput, rand());
    Bingo_Timer::end($strTimer);
    if (!$arrOutput || !is_array($arrOutput)){
        Bingo_Log::warning("ral call error[method:{$strMethod}] [input".serialize($arrInput)."]");
        return false;
    }
    return $arrOutput;
}
/**
 * @brief
 * @params: $arrInput:
 * 	req_fds req
 * @return: $arrOutput:
 * 	res_fds res
**/
public static function fds($arrInput){
	$arrOut = self::_call($arrInput, 'fds');
	return $arrOut;
}
}
