<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013:01:28 15:28:24
 * @version 
 * @structs & methods(copied from idl.)
*/

//*************************************************************************************
// IDL CONTENT BEGIN: 
//*************************************************************************************
// 	
// 	
// 	struct txtItem
// 	{
//		string text;
//		string property;
//		string ie;
// 	};
// 	
// 	struct output
// 	{
// 	    int32_t errNo;
// 	    float array score;
// 	    int32_t resNum;
// 	};
// 	
// 	
// 	
// 	
//*************************************************************************************
// IDL CONTENT END. 
//*************************************************************************************

class Dl_Trate_Trate{

const SERVICE_NAME = "Dl_Trate_Trate";
const PID = "forum";
const TRATE_REPLY = "front";
const TRATE_FOLLOW = "reply_following";
const TRATE_LOUZHONGLOU = "louzhonglou_hot";
protected static $_conf = null;
	
/**
 * @brief init
 * @return: true if success. false if fail.

**/		
private static function _init(){
	
	//add init code here. init will be called at every public function beginning.
	//not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
	//init should be recalled for many times.
	
	if(self::$_conf == null){	
		self::$_conf = Bd_Conf::getConf("/app/anti/dl_trate_trate");
		if(self::$_conf == false){
			Bingo_Log::warning("init get conf fail.");
			return false;
		}
		
	}
	return true; 
}

/**
 * @brief get array params from $arrInput.
 * @return: array converted from $arrInput[strParamName] or from raw post field..
**/	
private static function  _getArrayParams($arrInput, $strParamName){
    
    if($strParamName == false){ 
        Bingo_Log::warning("invalid param name");   
        return false;
    }
    $param = $arrInput[$strParamName];
	if(is_array($param)){
		return $param;
	}
	$format = strtolower($arrInput['format']);
    if($param == false){ 
    	if($format === 'mcpackraw'){
    		$param = file_get_contents("php://input");
    		$arrOut = mc_pack_pack2array($param);
    		
    	}else {
    		return  false; 		
    	}
    	
    	
    }else{
    	
	    if($format === 'mcpack'){
	        $arrOut = mc_pack_text2pack($param);
	        $arrOut = mc_pack_pack2array($arrOut);
	                
	    }else if($format === 'json'){
	        $arrOut = Bingo_String::json2array($param);  
	    }else{
	        $arrOut = unserialize($param);
	    }  	
    	
    	
    }

    return $arrOut;

}

private static function _errRet($errno){
    return array(
        'errno' => $errno,
        'errmsg' => Tieba_Error::getErrmsg($errno),
    );
}

private static function _genReq($content,$type) {

	$arrContent = array(
		'service_name' => 'trate',
		'id' => 111,
		'method' => 'predict',
		'params' => array(
				'input' => array(
						'pid'=>'forum',
						'type'=>$type,
						'txtItem'=> $content,
					),
			
			),	
		
		);

	$req = array(
			'header' => array(
					'connection'=>false,
				),
			'content' => array($arrContent),
		);

	return $req;
}

/**
 * @brief
 * @arrInput:
 * 	trate_req req
 * @return: $arrOutput
 * 	trate_res res
**/
public static function reply_calc($content,$title){
		
	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$res = false;
	$arrInput = array(
		array(
			'text'=>$title,
			'property'=>'title',
			'ie'=>'gbk',
		),
		array(
			'text'=>$content,
			'property'=>'content',
			'ie'=>'gbk',
			),	

		);
	$req = self::_genReq($arrInput,self::TRATE_REPLY);

	$strTimer = 'ral_call_'.self::SERVICE_NAME;
	Bingo_Timer::start($strTimer);
	$res = Tieba_Ral::call('trate','',$req,rand());
	Bingo_Timer::end($strTimer);
	if(!$res) {
		Bingo_Log::warning("call service failed:trate");
		return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
	}

	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'res' => $res,
	);
	return $arrOutput;
}

public static function louzhonglou_calc($content,$title){

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$res = false;
	$arrInput = array(
		array(
			'text'=>'',
			'property'=>'title',
			'ie'=>'gbk',
		),
		array(
			'text'=>$content,
			'property'=>'content',
			'ie'=>'gbk',
			),	

		);
	$req = self::_genReq($arrInput,self::TRATE_LOUZHONGLOU);

	$strTimer = 'ral_call_'.self::SERVICE_NAME;
	Bingo_Timer::start($strTimer);
	$res = Tieba_Ral::call('trate','',$req,rand());
	Bingo_Timer::end($strTimer);
	if(!$res) {
		Bingo_Log::warning("talk with trate fail.");
		return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
	}

	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'res' => $res,
	);
	return $arrOutput;
}

public static function follow_calc($content,$titile){
		
	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$res = false;
	$arrInput = array(
		array(
			'text'=>'',
			'property'=>'title',
			'ie'=>'gbk',
		),
		array(
			'text'=>$content,
			'property'=>'content',
			'ie'=>'gbk',
			),	

		);

	$req = self::_genReq($arrInput,self::TRATE_FOLLOW);

	$strTimer = 'ral_call_'.self::SERVICE_NAME;
	Bingo_Timer::start($strTimer);
	$res = Tieba_Ral::call('trate','',$req,rand());
	Bingo_Timer::end($strTimer);
	if(!$res) {
		Bingo_Log::warning("call service failed:trate");
		return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
	}

	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'res' => $res,
	);
	return $arrOutput;
}
}
