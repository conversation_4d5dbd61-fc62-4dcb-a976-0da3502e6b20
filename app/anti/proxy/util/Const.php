<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 14-8-26:PM4:24
 * @version 1.0
 */

class Proxy_Util_Const
{

    const ERROR_PREVIOUS = ' [Anti Proxy Exception] ';

    const ERROR_INPUT_NEED_REQUIRED_FIELD = 1;

    const ERROR_INPUT_SERVICE_NOT_EXIST = 2;

    const ERROR_INPUT_OPTIONAL_NOT_EXIST = 3;

    const ERROR_INSTANCE_NOT_EXIST = 4;

    public static $codes = array(
        self::ERROR_INPUT_NEED_REQUIRED_FIELD => 'Input requires fields.',
        self::ERROR_INPUT_SERVICE_NOT_EXIST => 'Service is not exist.',
        self::ERROR_INPUT_OPTIONAL_NOT_EXIST => 'Declared optional field is not exist.',
        self::ERROR_INSTANCE_NOT_EXIST => 'Instance is not exist, please check the class [Proxy_Impl_{$service}Resource] if exists.',
    );

    public static function getMsg($code)
    {
        $msg = '';
        if (isset(self::$codes[$code])) {
            $msg = self::$codes[$code];
        }
        return $msg;
    }
} 