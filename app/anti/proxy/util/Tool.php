<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 14-9-17:PM12:34
 * @version 1.0
 */

class Proxy_Util_Tool
{
    public static function assign2Des(&$des, $source, $desField, $sourceFiled, $default)
    {
        if (isset($source[$sourceFiled])) {
            $des[$desField] = $source[$sourceFiled];
            settype($des[$desField], gettype($default));
        } else {
            $des[$desField] = $default;
        }
    }

    public static function assign2Self(&$self, $desField, $sourceFiled)
    {
        if (isset($self[$sourceFiled])) {
            $self[$desField] = $self[$sourceFiled];
        }
    }

    public static function assign2SelfDefault(&$self, $desField, $default)
    {
        if (!isset($self[$desField])) {
            $self[$desField] = $default;
        }
    }

    /**
     * @desc 根据参数决定内部运行逻辑
     * @param unknown $group
     * @param unknown $cmd_no
     * @param unknown $func
     * @return string
     */
    public static function getLogicGroup($group, $cmd_no, $func)
    {
        if (Util_Const::FUNC_ANTISERVER == $func) {
            return Util_Const::GROUP_LOGIC_NAF_DFT;
        }
    	elseif( Util_Const::FUNC_TB_CONFILTER == $func ){
    		return Util_Const::GROUP_LOGIC_TB_CONFILTER_DFT ;
    	}
    }
}
