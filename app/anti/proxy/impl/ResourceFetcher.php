<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 14-8-28:PM5:49
 * @version 1.0
 */

class Proxy_Impl_ResourceFetcher
{

    public function __construct()
    {
    }

    /**
     * 获取资源前初始化.
     * @param Proxy_Entity_AgentReq $req
     * @param Proxy_Entity_AgentRes $res
     */
    protected function initialize(Proxy_Entity_AgentReq $req, Proxy_Entity_AgentRes $res)
    {

    }

    /**
     * 获取资源.
     * @param Proxy_Entity_AgentReq $req
     * @param Proxy_Entity_AgentRes $res
     * @throws Exception
     */
    public function fetch(Proxy_Entity_AgentReq $req, Proxy_Entity_AgentRes $res)
    {
        $class = sprintf('Proxy_Impl_%sResource', $req->getResourceReq()->getService());
        if (class_exists($class)) {
            $instance = new $class();
            try {
                $this->initialize($req, $res);
                $resourceRes = new Proxy_Entity_ResourceRes();
                $instance->getResource($req->getResourceReq(), $resourceRes);
                $res->setResourceRes($resourceRes);
                $this->handleFetchResult($req, $res);
            } catch (Exception $e) {
                throw $e;
            }
        } else {
            throw new Exception(Proxy_Util_Const::getMsg(Proxy_Util_Const::ERROR_INSTANCE_NOT_EXIST),
                Proxy_Util_Const::ERROR_INSTANCE_NOT_EXIST, Proxy_Util_Const::ERROR_PREVIOUS);
        }
    }

    /**
     * 规范化获取的资源.
     * @param Proxy_Entity_AgentReq $req
     * @param Proxy_Entity_AgentRes $res
     */
    protected function handleFetchResult(Proxy_Entity_AgentReq $req, Proxy_Entity_AgentRes $res)
    {

    }
} 