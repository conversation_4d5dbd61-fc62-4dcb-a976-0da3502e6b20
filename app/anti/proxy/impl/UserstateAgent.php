<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 14-12-30:PM3:54
 * @version 1.0
 */

class Proxy_Impl_UserstateAgent extends Proxy_Interface_AbstractAgent
{
    private $req, $res, $userstateFetcher;

    public function __construct($req, $group)
    {
        $this->req = new Proxy_Entity_AgentReq($req, $group);
        $this->res = new Proxy_Entity_AgentRes();
        $this->userstateFetcher = new Proxy_Impl_UserstateResourceFetcher();
    }

    /**
     * 初始化.
     * @return mixed
     */
    protected function init()
    {
        $group = $this->req->getGroup();
        switch ($group) {
            case 'antiUserBlockQuery':
                Proxy_Util_Tool::assign2SelfDefault($this->req->getReq(), 'forum_id', 0);
                Proxy_Util_Tool::assign2SelfDefault($this->req->getReq(), 'ip', 0);
                break;
            case 'antiGetStateMsg':
                $this->req->setReqKey('ip', 0);
                $this->req->setReqKey('transfer', 0);
                break;
        }
    }

    /**
     * 参数校验.
     * @return mixed
     */
    protected function check()
    {
        $group = $this->req->getGroup();
        $agentReq = $this->req->getReq();
        switch ($group) {
            case 'antiUserBlockQuery':
                if (!$this->req->hasReqKey('user_id')) {
                    Bingo_Log::warning('input params invalid:in antiUserBlockQuery, parse params failed. input is[' . serialize($agentReq) . '].');
                    throw new Exception(Tieba_Error::getErrmsg(Tieba_Errcode::ERR_ANTI_WRONG_PARAM), Tieba_Errcode::ERR_ANTI_WRONG_PARAM, Proxy_Util_Const::ERROR_PREVIOUS);
                }
                break;
            case 'antiUserBlockListQuery':
                if (!$this->req->hasReqKey('command_no') || !$this->req->hasReqKey('uid') || !$this->req->hasReqKey('uip')) {
                    Bingo_Log::warning('input params invalid:in antiUserBlockListQuery,input is[' . serialize($agentReq) . '].');
                    throw new Exception(Tieba_Error::getErrmsg(Tieba_Errcode::ERR_ANTI_WRONG_PARAM), Tieba_Errcode::ERR_ANTI_WRONG_PARAM, Proxy_Util_Const::ERROR_PREVIOUS);
                }
                break;
            case 'antiGetStateMsg':
                if (!$this->req->hasReqKey('user_id') || intval($agentReq['user_id']) <= 0) {
                    Bingo_Log::warning("input params invalid:in antiGetStateMsg,(no user_id),input is[" . serialize($agentReq) . "]");
                    throw new Exception(Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR), Tieba_Errcode::ERR_PARAM_ERROR, Proxy_Util_Const::ERROR_PREVIOUS);
                }
                if (!$this->req->hasReqKey('forum_id') || intval($agentReq['forum_id']) < 0) {
                    Bingo_Log::warning("input params invalid:in antiGetStateMsg,(no forum_id),input is[" . serialize($agentReq) . "]");
                    throw new Exception(Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR), Tieba_Errcode::ERR_PARAM_ERROR, Proxy_Util_Const::ERROR_PREVIOUS);
                }
                break;
            case 'getAllExceptionState':
                if (!$this->req->hasReqKey('user_id') || intval($agentReq['user_id']) <= 0) {
                    Bingo_Log::warning("input params invalid:in getAllExceptionState,(no user_id),input is[" . serialize($agentReq) . "]");
                    throw new Exception(Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR), Tieba_Errcode::ERR_PARAM_ERROR, Proxy_Util_Const::ERROR_PREVIOUS);
                }
                if (!$this->req->hasReqKey('opgroup') || empty($agentReq['opgroup'])) {
                    Bingo_Log::warning("input params invalid:in getAllExceptionState,(no opgroup),input is[" . serialize($agentReq) . "]");
                    throw new Exception(Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR), Tieba_Errcode::ERR_PARAM_ERROR, Proxy_Util_Const::ERROR_PREVIOUS);
                }
                if (array_search($this->req->getReqKey('opgroup'), Proxy_Impl_UserStateResource::$OPGROUPS) === false) {
                    Bingo_Log::warning("input params invalid:in getAllExceptionState,(no opgroup),input is[" . serialize($agentReq) . "]");
                    throw new Exception(Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR), Tieba_Errcode::ERR_PARAM_ERROR, Proxy_Util_Const::ERROR_PREVIOUS);
                }
                break;
        }
    }

    /**
     * 依存服务状态校验.
     * @return mixed
     */
    protected function filter()
    {
    }

    /**
     * 处理逻辑.
     * @return mixed
     */
    protected function manufacture()
    {
        $group = $this->req->getGroup();
        switch ($group) {
            case 'antiUserBlockQuery':
                $this->antiUserBlockQuery();
                break;
            case 'antiUserBlockListQuery':
                $this->antiUserBlockListQuery();
                break;
            case 'antiGetStateMsg':
                $this->antiGetStateMsg();
                break;
            case 'antiGetBlockReason':
                $this->antiGetBlockReason();
                break;
            case 'getAllExceptionState':
                $this->getAllExceptionState();
                break;
        }
    }

    /**
     * 结果处理.
     * @return mixed
     */
    protected function dealResult()
    {
        $arrOutput = array(
            'errno' => $this->res->getError(),
            'errmsg' => Tieba_Error::getErrmsg($this->res->getError()),
            'res' => $this->res->getRes(),
        );
        switch ($this->req->getGroup()) { // Special result, not to comply with a standard
            case 'antiUserBlockQuery':
                $arrOutput["is_block"] = $this->res->getResKey("is_block");
                break;
            case 'antiGetBlockReason':
                $arrOutput = $this->res->getRes();
                break;
            case 'getAllExceptionState':
                Naf_Util::iconvRecursive($arrOutput['res'], 'utf8', 'gbk//ignore');
                break;
        }
        return $arrOutput;
    }

    private function antiUserBlockQuery()
    {
        Bingo_Timer::start('userstate');
        $this->buildResourceReq($this->req, 'UserState', 'getUserBlockInfo', $this->req->getReq());
        $this->userstateFetcher->fetch($this->req, $this->res);
        Bingo_Timer::end('userstate');
    }

    private function antiUserBlockListQuery()
    {
        Bingo_Timer::start('userstate');
        $this->buildResourceReq($this->req, 'UserState', 'getUserBlockList', $this->req->getReq());
        $this->userstateFetcher->fetch($this->req, $this->res);
        Bingo_Timer::end('userstate');
    }

    private function antiGetStateMsg()
    {
        Bingo_Timer::start('userstate');
        $this->buildResourceReq($this->req, 'UserState', 'getUserBlockInfo', $this->req->getReq());
        $this->userstateFetcher->fetch($this->req, $this->res);
        Bingo_Timer::end('userstate');
    }

    private function antiGetBlockReason()
    {
        Bingo_Timer::start('userstate');
        $this->buildResourceReq($this->req, 'UserState', 'getBlockReason', $this->req->getReq());
        $this->userstateFetcher->fetch($this->req, $this->res);
        Bingo_Timer::end('userstate');
    }

    private function getAllExceptionState()
    {
        Bingo_Timer::start('userstate');
        $this->buildResourceReq($this->req, 'UserState', 'getAllStateInfos', array());
        $this->userstateFetcher->fetch($this->req, $this->res);
        Bingo_Timer::end('userstate');
        Bingo_Timer::start('tbmask');
        $this->buildResourceReq($this->req, 'Tbmask', 'tbmaskQuery', array());
        $this->userstateFetcher->fetch($this->req, $this->res);
        Bingo_Timer::end('tbmask');
    }
}