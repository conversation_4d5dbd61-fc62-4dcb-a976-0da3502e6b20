<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 14-12-29:PM6:02
 * @version 1.0
 */

class Proxy_Impl_ActsctrlAgent extends Proxy_Interface_AbstractAgent
{

    private $req, $res, $actsctrlFetcher, $conf;

    public function __construct($req, $group)
    {
        $this->req = new Proxy_Entity_AgentReq($req, $group);
        $this->res = new Proxy_Entity_AgentRes();
        $this->actsctrlFetcher = new Proxy_Impl_ActsctrlResourceFetcher();
    }

    protected function init()
    {
    }

    protected function check()
    {
        $group = $this->req->getGroup();
        $agentReq = $this->req->getReq();
        /**
         * @TODO 针对ipv6做个兼容，传了但是传的是空，就注销掉
         */
        if (isset($agentReq['ipv6']) && empty($agentReq['ipv6'])){
            $this->req->delReqKey('ipv6');
        }
        if (isset($agentReq['ip']) && empty($agentReq['ip'])){
            $this->req->delReqKey('ip');
        }
        switch ($group) {
            case 'antiActsctrlQuery':
                if (!$this->req->hasReqKey('rulegroup') || empty($agentReq['rulegroup'])) {
                    Bingo_Log::warning('input params invalid:in antiActsctrlQuery,input is[' . serialize($agentReq) . '].');
                    throw new Exception(Tieba_Error::getErrmsg(Tieba_Errcode::ERR_ANTI_WRONG_PARAM), Tieba_Errcode::ERR_ANTI_WRONG_PARAM, Proxy_Util_Const::ERROR_PREVIOUS);
                }
                break;
            case 'antiActsctrlMultiQuery':
                if (!$this->req->hasReqKey('reqs') || !is_array($agentReq['reqs']) || (count($agentReq['reqs']) <= 0) || (count($agentReq['reqs']) > Util_Const::MULTI_MAX_COUNT)) {
                    Bingo_Log::warning('input params invalid:in antiActsctrlMultiQuery,input is[' . serialize($agentReq) . '].');
                    throw new Exception(Tieba_Error::getErrmsg(Tieba_Errcode::ERR_ANTI_WRONG_PARAM), Tieba_Errcode::ERR_ANTI_WRONG_PARAM, Proxy_Util_Const::ERROR_PREVIOUS);
                }
                break;
            case 'antiActsctrlSubmit':
                if (!$this->req->hasReqKey('rulegroup') || empty($agentReq['rulegroup'])) {
                    Bingo_Log::warning('input params invalid:in antiActsctrlSubmit,input is[' . serialize($agentReq) . '].');
                    throw new Exception(Tieba_Error::getErrmsg(Tieba_Errcode::ERR_ANTI_WRONG_PARAM), Tieba_Errcode::ERR_ANTI_WRONG_PARAM, Proxy_Util_Const::ERROR_PREVIOUS);
                }
                break;
            case 'antiActsctrlMultiSubmit':
                if (!$this->req->hasReqKey('reqs') || !is_array($agentReq['reqs']) || (count($agentReq['reqs']) <= 0) || (count($agentReq['reqs']) > Util_Const::MULTI_MAX_COUNT)) {
                    Bingo_Log::warning('input params invalid:in antiActsctrlMultiSubmit,input is[' . serialize($agentReq) . '].');
                    throw new Exception(Tieba_Error::getErrmsg(Tieba_Errcode::ERR_ANTI_WRONG_PARAM), Tieba_Errcode::ERR_ANTI_WRONG_PARAM, Proxy_Util_Const::ERROR_PREVIOUS);
                }
                break;
            case 'antiNafActsctrlQuery':
            case 'antiNafActsctrlSubmit':
                if (!$this->req->hasReqKey('rulegroup') || empty($agentReq['rulegroup'])) {
                    Bingo_Log::warning('input params invalid [rulegroup]:in '.$group.',input is[' . serialize($agentReq) . '].');
                    throw new Exception(Tieba_Error::getErrmsg(Tieba_Errcode::ERR_ANTI_WRONG_PARAM), Tieba_Errcode::ERR_ANTI_WRONG_PARAM, Proxy_Util_Const::ERROR_PREVIOUS);
                }
                if (!$this->req->hasReqKey('command_no') || empty($agentReq['command_no'])) {
                    Bingo_Log::warning('input params invalid [command_no]:in '.$group.',input is[' . serialize($agentReq) . '].');
                    throw new Exception(Tieba_Error::getErrmsg(Tieba_Errcode::ERR_ANTI_WRONG_PARAM), Tieba_Errcode::ERR_ANTI_WRONG_PARAM, Proxy_Util_Const::ERROR_PREVIOUS);
                }
                if (!$this->req->hasReqKey('app_key') || empty($agentReq['app_key'])) {
                    Bingo_Log::warning('input params invalid [app_key]:in '.$group.',input is[' . serialize($agentReq) . '].');
                    throw new Exception(Tieba_Error::getErrmsg(Tieba_Errcode::ERR_ANTI_WRONG_PARAM), Tieba_Errcode::ERR_ANTI_WRONG_PARAM, Proxy_Util_Const::ERROR_PREVIOUS);
                }
                break;
        }
    }

    protected function filter()
    {
    }

    protected function manufacture()
    {
        $group = $this->req->getGroup();
        switch ($group) {
            case 'antiActsctrlQuery':
                $this->antiActsctrlQuery();
                break;
            case 'antiActsctrlMultiQuery':
                $this->antiActsctrlMultiQuery();
                break;
            case 'antiActsctrlSubmit':
                $this->antiActsctrlSubmit();
                break;
            case 'antiActsctrlMultiSubmit':
                $this->antiActsctrlMultiSubmit();
                break;
            case 'antiNafActsctrlQuery':
                $this->antiNafActsctrlQuery();
                break;
            case 'antiNafActsctrlSubmit':
                $this->antiNafActsctrlSubmit();
                break;
        }
    }

    protected function dealResult()
    {
        $arrOutput = array(
            'errno' => $this->res->getError(),
            'errmsg' => Tieba_Error::getErrmsg($this->res->getError()),
            'res' => $this->res->getRes(),
        );
        return $arrOutput;
    }

    /**
     * @desc 先调用事前naf，在调用粒度控制
     * @throws Exception
     */
    private function antiNafActsctrlQuery()
    {
        $arrInput = $this->req->getReq();
        $logid = Bd_Log::genLogID();
        $req = array(
            'req' => array(
                'config' => array(
                    'group' => 'anti',                   // 必填，业务方产品线标识，线上调用请联系ueg rd 分配，测试可直接用
                    'command_no' => intval($arrInput['command_no']),  // 必填，业务方具体业务标识，线上请联系ueg rd 分配，测试可直接用
                    'log_key' => $logid,                 // 必填，单个请求的业务唯一标识，由产品线赋值，无特殊需求可用 LOG_ID 填充，测试可直接用
                ),
                'func' => array(
                    'newAntiServer',                    // 必填，调用的服务，常量
                ),
                'eachParam' => array(
                    'newAntiServer' => array(
                        '_config' => array(
                            '_returnRecall' => 1,       // 必填，是否返回判断结果，常量
                            '_test' => false,           // 可选，测试可直接用，测试环境请置 _test = true , 上线请置 _test = false ;或者不填
                        ),
                        /*
                        'req' => array(
                            // 最简单测试case
                            'app_key' => $arrInput['app_key'],  // 可选，测试可直接用，utf8编码
                            'user_id' => isset($arrInput['user_id']) ? $arrInput['user_id'] : 0,    // 厂内pass用户id
                            'user_name' => isset($arrInput['user_name']) ? $arrInput['user_name'] : '',
                            'thread_id' => isset($arrInput['thread_id']) ? $arrInput['thread_id'] : 0,
                            'post_id' => isset($arrInput['post_id']) ? $arrInput['post_id'] : 0,
                            'ip' => isset($arrInput['ip']) ? $arrInput['ip'] : 0,
                            'cuid' => isset($arrInput['cuid']) ? $arrInput['cuid'] : 0,
                            'is_thread' => isset($arrInput['is_thread']) ? $arrInput['is_thread'] : 0,
                        ),*/
                    ),
                ),
            ),
        );

        foreach ($arrInput as $key=>$value){
            if (in_array($key, array('rulegroup', 'app', 'cmd'))){
                continue;
            }
            $req['req']['eachParam']['newAntiServer']['req'][$key] = $value;
        }

        $naf_res = Tieba_Service::call('anti', 'antiSpam', $req, null, null, 'post', 'php', 'utf-8');
        Bingo_Log::notice('anti antiSpam. req='.serialize($req).', naf_res='.serialize($naf_res));
        // 命中了事前策略
        if (isset ( $naf_res ['res'] ['func'] ['newAntiServer'] ['res'] ['recall'] ['self'] ['antiservice'] )) {
            $dealer = $naf_res ['res'] ['func'] ['newAntiServer'] ['res'] ['recall'] ['self'] ['antiservice'];
            $this->conf = Bd_Conf::getConf("/app/anti/service_anti");
            $dealer_map = $this->conf ['Naf_Dealer'];
            if (isset ( $dealer_map [$dealer ['dealername']] )) {
                //if ((intval ( $dealer ['enable'] ) > 0)){
                    $arrInput ['dealer_type'] = intval ( $dealer_map [$dealer ['dealername']] );
                //}
            }
        }
        Bingo_Log::notice('Actsctrl actsctrlQuery. req='.serialize($arrInput));
        Bingo_Timer::start('antiNafActsctrlQuery');
        $this->buildResourceReq($this->req, 'Actsctrl', 'actsctrlQuery', $arrInput);
        $this->actsctrlFetcher->fetch($this->req, $this->res);
        Bingo_Timer::end('antiNafActsctrlQuery');
    }

    private function antiNafActsctrlSubmit()
    {
        Bingo_Timer::start('antiNafActsctrlSubmit');
        $this->buildResourceReq($this->req, 'Actsctrl', 'actsctrlSubmit', $this->req->getReq());
        $this->actsctrlFetcher->fetch($this->req, $this->res);
        Bingo_Timer::end('antiNafActsctrlSubmit');
    }

    private function antiActsctrlQuery()
    {
        Bingo_Timer::start('actsctrl_query');
        $this->buildResourceReq($this->req, 'Actsctrl', 'actsctrlQuery', $this->req->getReq());
        $this->actsctrlFetcher->fetch($this->req, $this->res);
        Bingo_Timer::end('actsctrl_query');
    }

    private function  antiActsctrlMultiQuery()
    {
        Bingo_Timer::start('actsctrl_multi_query');
        $actsctrlRrc = new Proxy_Impl_ActsctrlResource();
        $actsctrlReq = new Proxy_Entity_ResourceReq();
        $actsctrlRes = new Proxy_Entity_ResourceRes();
        $actsctrlReq->setInput($this->req->getReq())->setService('new_actsctrl_query_pandora')->setMethod('query');
        $actsctrlRrc->multiActsctrl($actsctrlReq, $actsctrlRes);
        $this->res->setRes($actsctrlRes->getRes());
        Bingo_Timer::end('actsctrl_multi_query');
    }

    private function antiActsctrlSubmit()
    {
        Bingo_Timer::start('actsctrl_submit');
        $getReq = $this->req->getReq();
        if ($getReq['app'] == 'antiAttack'){
            $this->req->setReqKey('rulegroup', array('antiAttack'));
        }
        $this->buildResourceReq($this->req, 'Actsctrl', 'actsctrlSubmit', $this->req->getReq());
        $this->actsctrlFetcher->fetch($this->req, $this->res);
        Bingo_Timer::end('actsctrl_submit');
    }

    private function antiActsctrlMultiSubmit()
    {
        Bingo_Timer::start('actsctrl_multi_submit');
        $actsctrlRrc = new Proxy_Impl_ActsctrlResource();
        $actsctrlReq = new Proxy_Entity_ResourceReq();
        $actsctrlRes = new Proxy_Entity_ResourceRes();
        $actsctrlReq->setInput($this->req->getReq())->setService('new_actsctrl_submit_pandora')->setMethod('query');
        $actsctrlRrc->multiActsctrl($actsctrlReq, $actsctrlRes);
        $this->res->setRes($actsctrlRes->getRes());
        Bingo_Timer::end('actsctrl_multi_submit');
    }
}