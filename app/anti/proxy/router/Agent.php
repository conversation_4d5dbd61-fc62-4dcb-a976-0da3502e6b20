<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 14-9-26:PM4:40
 * @version 1.0
 */

class Proxy_Router_Agent
{

    const GET_VCODE = 'GET_VCODE';
    const ANTISPAM = 'ANTISPAM' ;
    const TB_CONFILTER = 'TB_CONFILTER';

    public static function getAgent($router, $group, $req)
    {
        switch ($router) {
            case self::GET_VCODE:
                return new Proxy_Impl_GetVcodeAgent($req, $group);
            case self::ANTISPAM :
            	return new Proxy_Impl_AntispamAgent($req, $group);
            case self::TB_CONFILTER :
            	return new Proxy_Impl_ConfilterAgent($req, $group);
        }

        return null;
    }
} 