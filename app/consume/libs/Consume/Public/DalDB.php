<?php
class Consume_Public_DalDB{
    
    const DUPLICATE_ERROR_NO = 1062;
    
    protected   $objDb = null;     
    protected   $objTemplate = null;
    
    protected   $strSeverName   = "";
    protected   $intUseSplitDb  = "";
    protected   $strDbConfDir   = "";
    protected   $strDbConfName  = "";
    protected   $transaction    = false;
    
    private    static $_instance = null ;
    
    private function __construct(){}
    
    public function __clone(){
        trigger_error('Clone is not allow!',E_USER_ERROR);
    }
    
    public static function getInstance(){
        if( self::$_instance === null || self::$_instance === false ){
            self::$_instance = new self;
        }
        return self::$_instance;
    }
    
    public  function init($strSeverName, 
                          $intUseSplitDb = false, 
                          $strDbConfDir = false, 
                          $strDbConfName = false){
        $this->strSeverName  = $strSeverName; 
        $this->intUseSplitDb = $intUseSplitDb;
        $this->strDbConfDir  = $strDbConfDir;
        $this->strDbConfName = $strDbConfName;
        $this->objDb         = null;
        $this->objTemplate   = null;
    }
        
    public  function connect(){
        if( $this->objDb !== null ){
            return true;
        }
        $this->objDb = new Bd_DB();
        if($this->objDb == null){
            Bingo_Log::warning("new bd_db fail.");
            return false;
        }
        if($this->intUseSplitDb){
            $splitDBConfPath = $this->strDbConfDir; //ROOT_PATH."/conf/db/";
            $splitDBConfFile = $this->strDbConfName; //"db_dl_post_post.conf";
            $r = $this->objDb->enableSplitDB($this->strSeverName, 
                                             $this->splitDBConfPath, 
                                             $this->splitDBConfFile);
            if(!$r){
                Bingo_Log::warning("enable splitdb fail.");
                $this->objDb = null;
                return false;
            }
        }else{
            Bingo_Timer::start('dbinit');
            $r = $this->objDb->ralConnect($this->strSeverName);
            Bingo_Timer::end('dbinit');
            if(!$r){
                Bingo_Log::warning("bd db ral connect fail.");
                $this->objDb = null;
                return false;
           }
        }
        
        $this->objTemplate = new Bd_Db_SQLTemplate($this->objDb);
        if($this->objTemplate == false ){
            Bingo_Log::warning("bd db Bd_Db_SQLTemplate fail.");
            $this->objDb = null;
            return false;
		}
		$this->objDb->query('set names gbk');
        return true;
    }
        
    public  function startTransaction(){
        if($this->objDb){
            $this->transaction = true;
            return $this->objDb->startTransaction();      
        }
        return false;
    }
    
    public  function commit(){
        if($this->transaction && $this->objDb){
            return $this->objDb->commit();
        }
        return false;
    }
        
    public  function rollBack(){
        if($this->transaction  && $this->objDb){
            $this->objDb->rollback();    
        }
        return false;
    }
    
    public function getLastErrorNo(){
        if($this->objDb){
            return $this->objDb->errno();
        }
        return 0;
    }
    
    public  function buildSQL($sqlTpl, $inputParam){
        if( $this->objDb === null && $this->connect() === false)   {
            Bingo_Log::warning('db init fale in execute!');
            return false;
        }
        if(empty($inputParam) || $inputParam == false ){
            return $sqlTpl;
		}
		$this->objDb->charset('utf8');
		mb_convert_variables('utf-8', 'gbk',  $inputParam);
		mb_convert_variables('utf-8', 'gbk', $sqlTpl);
		$this->objTemplate->prepare($sqlTpl);
		
		$strSql = $this->objTemplate->bindParam($inputParam, NULL, true);
		mb_convert_variables('gbk', 'utf-8', $strSql);
		$this->objDb->charset('gbk');
        return $strSql;
    }
    
    public  function execute($sqlTpl, $inputParam){
        $arrOutput = array();
        $strSql = $this->buildSQL($sqlTpl, $inputParam);
        if($strSql === null || $strSql === false){
           Bingo_Log::warning("build sql fail�� sql��[$sqlTpl] input[".serialize($inputParam)."]");
           return false;                
        }
        
        $intStartTime = gettimeofday();
        // ִ�����
        $result = $this->objDb->query($strSql);          
        if ($result === false) {
            Bingo_Log::warning('exec sql fail! error['.$this->objDb->errno().']['.$this->objDb->error().'] input['.$strSql.']');
            return false;
        }
        $intEndTime = gettimeofday();
        $intUseTimes = ceil(Bingo_Timer::getUtime($intStartTime,$intEndTime)/1000);
                
        $strTimeLog = "Timer[$intUseTimes]" ;
        Bingo_Log::debug('exec sql [ ' . $strSql . ' ]'.$strTimeLog);
        
        $arrOutput = array();
        if(!is_array($result)){
            $arrOutput['row'] = $this->objDb->getAffectedRows();    
        }else{
            $arrOutput = $result;
        }
        
        return $arrOutput;         
    }
    
    public function escapeString($strWord) {
        if (false === $this->connect()) {
            return "";
        }
        return mysql_real_escape_string($strWord);
    }
}
