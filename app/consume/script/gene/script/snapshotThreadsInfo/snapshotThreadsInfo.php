<?php
/**
 * ��ʱ��ȡ�����������ӿ�����Ϣ
 * <EMAIL>
 * 2013-03-31 21:52:06
 **/

define ('SCRIPTNAME','snapshotThreadsInfo');
define ('BASEPATH',dirname(__FILE__));
define ('CONFPATH',BASEPATH."/conf");
define ('DATAPATH',BASEPATH."/data");
define ('LOGPATH',BASEPATH."/log");

#ȫ�������ļ�
$globalConf = array(
    'forumnamefile'       => CONFPATH.'/fname.txt',
    'outfilename'         => DATAPATH.'/0/snapshotThreadsInfo.txt',
    'verisonfilename'     => DATAPATH.'/snapshotReady.txt',
    'phplockfile'         => DATAPATH.'/snapshotThreadsInfo.lock',
    'lastdofilename'      => DATAPATH.'/lastdotime.tmp',
    
    'threadlistnum'       => 100,
    'donesleeptime'       => 0,
    'bachsleeptime'       => 0,
    'newthreadlasttime'   => 0,
    'threadcreatetimemin' => 0,
    'threadholdlevel'     => array(),
    'threadholddefaultlevel'  => 12,
    'threadholdfids'      => array(),  
);
//ȫ������
$globalData = array(
    'changeversion'       => 0,
    'Hfnamefile'          => null,
    'Houtfile'            => null,
    'Hversionfile'        => null,
    'Hlastdofile'         => null,
    'Hlockfile'           => null,
    'lasttime'            => 0,
    'predonetime'         => 0,
    'ignolthredholdtime'  => 0,
    'now'                 => time(),
);

define ('LOG', 'log');
Bingo_Log::init(array(
    LOG => array(
        'file'  => LOGPATH ."/". SCRIPTNAME. ".log",
        'level' => 0xF,
    ),
), LOG);

//��ʼ������
init();

//��ʼ����
$fname = "";
$batchOutput = "";
@ini_set('memory_limit','512M');//512M
$fnames = getForumNames();
foreach($fnames as $fname){
    $fname = trim($fname);
    if($fname == false ){
		   continue;
    }
    
    $batchOutput ="";
    usleep($globalConf['bachsleeptime']);
    
    Bingo_Log::notice('begin do with:'.$fname);
	$frsInput = array(
	            'word' => $fname,
	            'page_num' => 0,
	            'res_num' => 100,
	            'ip' => 0,
	            'post_category' =>0,
	);
	$frsOut=Tbapi_Core_Server::apicall('frsfrs','Frsfrs',$frsInput);
    if(!isset($frsOut['thread_list'])){
       Bingo_log::warning($fname.' get thread list faile!');
       continue;
    }
    unset($frsInput);
    
    //�����������ӵĲ���
    if($globalData['lasttime'] > 0 ){
       $count = 0 ;
       foreach($frsOut['thread_list'] as $threadInfo){
          if( isset($threadInfo['thread_types']) &&    //�ö���ֱ������
              (($threadInfo['thread_types'] & 0x2) != 0) == 1 
            ){
             continue;
          }
          if( isset($threadInfo['last_modified_time'])&&
              $threadInfo['last_modified_time'] < $globalData['lasttime']
            ){
              break;
          }
          $count++;
       }
       
       if($count < count($frsOut['thread_list']) ){
          $frsOut['thread_list'] = array_slice($frsOut['thread_list'],0,$count);
       }
    }
    
    //���ӻظ�������
    $frsOut['thread_list'] = array_filter($frsOut['thread_list'],'threadFilter');

    $postInput = array();
    $gradeInput= array();
    foreach($frsOut['thread_list'] as $threadInfo){
       $postInput[] = array(
                               'thread_id'  => $threadInfo['thread_id'],
                               'post_id'    => 0,
                               'page_num'   => 0,
                               'res_num'    => 1,
                               'see_author' => 0,
                               'reverse'    => 0,
                            );
       $gradeInput[] = array(
                               'user_id'  => $threadInfo['user_id'],
                               'forum_id' => $threadInfo['forum_id'],
                           );
    }
    $threadFirstPostInfos = getBatchFirstPostInfo($postInput);
    $userGradeInfos = getBatchUserGradeInfo($gradeInput);
    unset($postInput);
    unset($gradeInput);

    foreach($frsOut['thread_list'] as $threadInfo){
         if(!isset($threadFirstPostInfos[$threadInfo['thread_id']])) {
            continue;
         }

         $firstPostInfo = $threadFirstPostInfos[$threadInfo['thread_id']];
         if( $globalData['ignolthredholdtime'] > 0 &&
             $firstPostInfo['now_time'] > $globalData['ignolthredholdtime'] ){ //�շ�������ֱ�Ӻ���
            Bingo_Log::trace('thread is too new ,ignol!'.serialize($threadInfo));
            continue;
         }
         $batchOutput .= formatPrint(array(
                               $threadInfo['thread_id'],
                               $threadInfo['forum_name'], 
                               $threadInfo['forum_id'], 
                               $threadInfo['user_id'],
                               isset($userGradeInfos[$threadInfo['user_id']])?$userGradeInfos[$threadInfo['user_id']]['level_id']:0,
                               $firstPostInfo['now_time'],
                               $threadInfo['last_modified_time'],
                               $threadInfo['post_num'],
                               $threadInfo['freq_num'],
                               $threadInfo['title'],
                               $firstPostInfo['content'],
                             )
                          )."\n";
    }
    
    if($batchOutput != false ){
        if( fputs($globalData['Houtfile'],$batchOutput) == false ){
            Bingo_log::warning('write out file faile!');
            done();
        }
    }
    
    Bingo_Log::notice('done do with:'.$fname.'['.count($frsOut['thread_list']).']');
    unset($frsOut);
    unset($threadFirstPostInfos);
    unset($userGradeInfos);    
    unset($batchOutput);
    
    Bingo_Log::getModule()->flush();
}

$globalData['Hversionfile'] = fopen($globalConf['verisonfilename'],'w');
if( $globalData['Hversionfile'] == false || 
    fputs($globalData['Hversionfile'],$globalConf['changeversion']."\n") == false ){
    Bingo_log::warning('version file write faile!');
}

$globalData['Hlastdofile'] = fopen($globalConf['lastdofilename'],'w');
if( $globalData['Hlastdofile'] == false || 
    fputs($globalData['Hlastdofile'],(time()-60)."\n") == false ){
    Bingo_log::warning('lastdo file write faile!');
}
Bingo_log::notice('time use:['.(time()-$globalData['now']).'s ]');
//����������Դ
done();
sleep($globalConf['donesleeptime']);

////////////////////////////////funtion list//////////////////////////////////////////
function __autoload($strClassName) 
{
    require_once str_replace('_', '/', $strClassName) . '.php'; 
}
spl_autoload_register('__autoload');

function threadFilter($threadInfo){
    global $globalConf;
    global $globalData;
    
    //�ظ�������
    $threadholdlevel        =  $globalConf['threadholdlevel'];
    $threadholddefaultlevel =  $globalConf['threadholddefaultlevel'];
    $threadholdfids         =  $globalConf['threadholdfids'];
    $threadHoldPostNum = isset($threadholdfids[$threadInfo['forum_id']]) ? $threadholdfids[$threadInfo['forum_id']]:$threadholddefaultlevel;
    if(isset($threadInfo['post_num']) && $threadInfo['post_num'] < $threadHoldPostNum ){
       Bingo_Log::trace('post num too less ,ignol!'.serialize($threadInfo));
       return false;
    }
    return true;
}

function init(){
    global $globalConf;
    global $globalData;
               
    //�˴��ж��Լ��Ľű��Ƿ��Ѿ������� todo
    $php_lock_fp = @fopen($globalConf['phplockfile'], "w+");
    if($php_lock_fp === false){
        Bingo_Log::debug('Couldn\'t open the lock file !\n');
        done();
    }
    if (flock($php_lock_fp, LOCK_EX + LOCK_NB)) {
	    register_shutdown_function('php_release_lock');
	    fwrite($php_lock_fp, getmypid());
    } else {
	    @fclose($php_lock_fp);
	    Bingo_Log::debug("Couldn't lock the file !\n");
	    done();
    }
    $globalData['Hlockfile'] = $php_lock_fp ;
    
    //�˴���ʼ�������ļ� todo
    $confArray = parse_ini_file(CONFPATH.'/snapshotThreadsInfo.ini');
    if(isset($confArray['verisonfilename'])) {
        $globalConf['verisonfilename'] = DATAPATH.'/'.trim($confArray['verisonfilename']);
    }
    if(isset($confArray['forumnamefile'])) {
        $globalConf['forumnamefile'] = CONFPATH.'/'.trim($confArray['forumnamefile']);
    }
    //��ȡ��ǰ���л��İ汾
    $globalConf['changeversion'] = intval(array_shift(file($globalConf['verisonfilename'])))+1;
    
    if(isset($confArray['outfilename'])) {
        $globalConf['outfilename'] = DATAPATH.'/'.($globalConf['changeversion']%2).'/'.trim($confArray['outfilename']);
    }
    
    if(isset($confArray['threadlistnum'])) {
        $globalConf['threadlistnum'] = intval($confArray['threadlistnum']);
    }
    if(isset($confArray['newthreadlasttime'])) {
        $globalConf['newthreadlasttime'] = intval($confArray['newthreadlasttime']);
    }
    if(isset($confArray['donesleeptime'])) {
        $globalConf['donesleeptime'] = intval($confArray['donesleeptime']);
    }
    if(isset($confArray['bachsleeptime'])) {
        $globalConf['bachsleeptime'] = intval($confArray['bachsleeptime']);
    }
    if(isset($confArray['threadcreatetimemin'])) {
        $globalConf['threadcreatetimemin'] = intval($confArray['threadcreatetimemin']);
        $globalData['ignolthredholdtime'] = time() - $globalConf['threadcreatetimemin'];
    }
    if(isset($confArray['threadholdlevel'])) {
        $globalConf['threadholdlevel'] = array_filter(split(',',$confArray['threadholdlevel']));
        $globalConf['threadholddefaultlevel'] = array_pop($globalConf['threadholdlevel']);
    }
    
    foreach ($globalConf['threadholdlevel'] as $key => $level){
        $name  = "threadholdfids_".$key;
        if(isset($confArray[$name])) {
            $fids = array_filter(split(',',$confArray[$name]));
            foreach($fids as $fid){
                $globalConf['threadholdfids'][$fid] = $level;
            }
        }
    }
    
    //��ȡ�����ļ����
    $globalData['Hfnamefile']   = fopen($globalConf['forumnamefile'],'r');
    $globalData['Houtfile']     = fopen($globalConf['outfilename'],'w');
    if($globalData['Hfnamefile'] == false || 
       $globalData['Houtfile'] == false){
       done();
    }
    
    if($globalConf['newthreadlasttime'] > 0 ){
        //��ȡ�ϴδ����ʱ��
       $globalData['predonetime'] = intval(array_shift(file($globalConf['lastdofilename'])));
       if($globalData['predonetime'] > 0 ){
           $globalData['lasttime'] = $globalData['predonetime'] ;
       }else{
           $globalData['lasttime'] = time()- $globalConf['newthreadlasttime'];
       }
    }
    print_r($globalConf);
    print_r($globalData);
    return ;
}

function done()
{
    global $globalConf;
    global $globalData;
    if($globalData['Hfnamefile'] != false ){
        fclose($globalData['Hfnamefile']);
    }else{
        Bingo_log::warning('fname file ['.$globalConf['forumnamefile'].'] is not exist!');
    }
    if($globalData['Houtfile'] != false ){
        fclose($globalData['Houtfile']);
    }else{
        Bingo_log::warning('out file ['.$globalConf['outfilename'].'] open faile!');
    }
    
    if($globalData['Hversionfile'] != false ){
        fclose($globalData['Hversionfile']);
    }else{
        Bingo_log::warning('version file ['.$globalConf['verisonfilename'].'] open faile!');
    }
    
    if($globalData['Hlastdofile'] != false ){
        fclose($globalData['Hlastdofile']);
    }else{
        Bingo_log::warning('last do  file ['.$globalConf['Hlastdofile'].'] open faile!');
    }
    
    exit;
}

function formatPrint($inparam=array(),$split="\t"){
    $resString = "";
    foreach($inparam as $param){
          $string = (string)$param;
          $string = str_replace(array($split,"\n")," ",$string);
          $resString = $resString.$split.$string;
          unset($string);
    }
    $tmpresString = substr($resString,1);
    unset($resString);
    return $tmpresString;
}

function getBatchUserGradeInfo($input){
    $MAX_REQ_NUM = 50;
    $output = array();
    while(count($input) > 0 ){
       $timeInput = array_splice($input,0,$MAX_REQ_NUM);
       $res = Rpc_Grade::getGradeInfos($timeInput);
       if($res === false ){
            Bingo_log::warning('get grade info faile!'.serialize($timeInput).serialize($res));
            continue;
       }
       foreach($res as $gradeInfo){
           $output[$gradeInfo['user_id']] = $gradeInfo;
       }
       unset($res);
       unset($timeInput);
    }
    return $output;
}

function getBatchFirstPostInfo($input){
    $MAX_REQ_NUM = 50;
    $output = array();
    while(count($input) > 0 ){
       $timeInput = array_splice($input,0,$MAX_REQ_NUM); 
       $tidArr = array();
       foreach ($timeInput as $timeVal) {
           $tidArr[] = $timeVal['thread_id'];
       }       
       $arrInput = array('thread_ids' => $tidArr);
       $allPostInfo = Tieba_Service::call('post', 'getBatchFirstPostByThreadId', $arrInput);
       $res = $allPostInfo['output'];
       if($res === false ){
            Bingo_log::warning('get post info faile!'.serialize($timeInput).serialize($res));
            continue;
       }
       foreach($res as $postInfos){
           if(!isset($postInfos['post_infos'][0])) {
               Bingo_Log::warning('post info data has no data'.serialize($postInfos));
               continue;
           }
           $firstPost = $postInfos['post_infos'][0];
           if($firstPost['post_no'] != 1 ){
               Bingo_log::debug('first post has been delete!'.serialize($postInfos));
               continue;
           }
           $output[$firstPost['thread_id']] = $firstPost;
       }
       unset($allPostInfo);
       unset($res);
       unset($timeInput);
    }
    return $output;
}

function php_release_lock()
{
    global $globalConf;
    global $globalData;
	$php_lock_fp = $globalData['Hlockfile'];
	if(is_resource($php_lock_fp)){
		flock($php_lock_fp, LOCK_UN);  //�ͷ�����
		@fclose($php_lock_fp);
		Bingo_Log::debug("Release lock!\n");
	}
	@unlink(PHP_LOCK_FILE);
}

function getForumNames(){
    $key = 'fname';
    $arrOptions = array(
                       'lifeTime' => 60,
                       'dir' => './conf/',
                   );
    $cache = Bingo_Cache::factory('file',$arrOptions);
    
    $cacheData = $cache->get($key);
    if($cacheData != false ){
        return $cacheData;
    }
    
    //�����ݿ���ȡ����
    $objDb = new Bd_DB();
    $r = $objDb->ralConnect('db_forum_consume');
    if(!$r){
        Bingo_Log::warning("bd db ral connect fail.");
        $this->objDb = null;
        return false;
    }
    $output= $objDb->query('select distinct forum_id from gene_forum_tag_relation;');
    $fids = array();
    foreach($output as $valus){
       $fids[] = $valus['forum_id'];
    }
    $fnames = getBatchFnameByid($fids);
    $cache->set($key,$fnames,60);
    return $fnames;
}

function getBatchFnameByid($input){
    $MAX_REQ_NUM = 50;
    $output = array();
    while(count($input) > 0 ){
       $timeInput = array_splice($input,0,$MAX_REQ_NUM); 
       
       $tmpInput = array('forum_id' => $timeInput );
       $tmpOnput = Tieba_Service::call('forum', 'getFnameByFid', $tmpInput);
       if($tmpOnput['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($tmpOnput['forum_name'])){
            Bingo_Log::warning('get getFnameByFid faile!'.serialize($tmpOnput));
            continue;
       }
       foreach($tmpOnput['forum_name'] as $forumNameInfo){
           if($forumNameInfo['exist'] == 1){
               $output[] = $forumNameInfo['forum_name'];
            }
       }
       unset($res);
       unset($timeInput);
    }
    return $output;
}
