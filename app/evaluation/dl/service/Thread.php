<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [Thread.php description]
 * <AUTHOR>
 * @DateTime 17/5/11 09:37
 * @brief
 */

class Dl_Service_Thread{

    /**
     * [getTopPidByTid description]
     * <AUTHOR>
     * @param
     * @return
     */
    public static function getTopPidByTid($tid){
        $arrInput = array(
            "thread_id" => $tid , //帖子id
            "offset" => 0,
            "res_num" => 1
        );
        $arrOutput   = Tieba_Service::call('post', 'getPostList', $arrInput, null, null, 'post', 'php', 'utf-8');

        if($arrOutput != false && $arrOutput['errno'] == Tieba_Errcode::ERR_SUCCESS){
            return $arrOutput['output'][0]['post_ids'][0];
        }else{
            Bingo_Log::warning('ERR[call service failed] IN['.__FUNCTION__.'] CALL[post.getPostList] REQ['.serialize($arrInput).'] RES['.serialize($arrOutput).']');
            return false;
        }
    }

    public static function getShareOriginalThread($tid)
    {
        $arrInput = array(
            "thread_ids" => array($tid)
        );
        echo json_encode($arrInput);
        $arrOutput = Tieba_Service::call('post', 'mgetThreadStaticInfo', $arrInput, null, null, 'post', 'php', 'utf-8');
        echo json_encode($arrOutput).'\n';

        if ($arrOutput != false && $arrOutput['errno'] == Tieba_Errcode::ERR_SUCCESS) {
            if (isset($arrOutput['output'][$tid]['original_tid'])) {
                return $arrOutput['output'][$tid]['original_tid'];
            } else {
                return false;
            }
        } else {
            Bingo_Log::warning('ERR[call service failed] IN[' . __FUNCTION__ . '] CALL[post.getPostList] REQ[' . serialize($arrInput) . '] RES[' . serialize($arrOutput) . ']');
            return false;
        }
    }
}