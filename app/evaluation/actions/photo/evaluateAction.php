<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [evaluateAction.php description]
 * <AUTHOR>
 * @DateTime 17/5/9 20:46
 * @brief
 */


class evaluateAction extends Lib_BaseAction{

    protected $_boolCheckPerm = false;

    protected function run(){
        $taskId = Bingo_Http_Request::getNoXssSafe("task_id", 0);
        $typeId = Bingo_Http_Request::getNoXssSafe("type_id", -1);
        $groupId = Bingo_Http_Request::getNoXssSafe("group_id", -1);
        $isDeleted = Bingo_Http_Request::getNoXssSafe("is_deleted", -1);
        $isFlod = Bingo_Http_Request::getNoXssSafe("is_flod", 0);
        $isMainStat = Bingo_Http_Request::getNoXssSafe("is_mainStat", 0);

        $this->_assign("taskId", $taskId);
        $this->_assign("typeId", $typeId);
        $this->_assign("groupId", $groupId);
        $this->_assign("isDeleted", $isDeleted);
        $this->_assign("isFlod", $isFlod);
        $this->_assign("isMainStat", $isMainStat);
        $this->_displayTpl("post/evaluate.php");
    }
}