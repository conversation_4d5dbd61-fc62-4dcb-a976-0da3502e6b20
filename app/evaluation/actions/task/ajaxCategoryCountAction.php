<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [ajaxCategoryCountAction.php description]
 * <AUTHOR>
 * @DateTime 17/5/12 22:36
 * @brief
 */

class AjaxCategoryCountAction extends Lib_BaseAction{
    protected $_boolCheckPerm = false;

    protected function run()
    {
        $taskId = Bingo_Http_Request::getNoXssSafe("task_id", 0);

        if(empty($taskId)){
            $this->_retjson(Tieba_Errcode::ERR_INPUT_PARAM);
        }


        try{
            $taskObj = Task_Factory::getInstance($taskId);

            $categoryCount = $taskObj->getCategoryCount();
            
            $this->_retjson(Tieba_Errcode::ERR_SUCCESS, array(
                "categoryCount" => $categoryCount,
            ));

        }catch (Exception $e){
            $this->_retjson(Tieba_Errcode::ERR_UNKOWN);
        }
        // TODO: Implement run() method.
    }
}