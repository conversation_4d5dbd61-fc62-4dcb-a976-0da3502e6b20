<?php
/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file active/Active.php
 * <AUTHOR>
 * @date 2017/11/18 15:02:02
 * @brief 
 *  
 **/

class Service_Oprecord_Record{
	const STATUS_SUCCESS = 0;
	const STATUS_FAILED = 1;

	/**
		*
		*	获取商品列表
		* 	@param
		* 	@option
		* 	@return
		*
	 */
	public static function getListByStatus($arrInput){
		//校验参数
		$arrCond	= array();
		isset($arrInput['status']) && $arrCond['status'] = intval($arrInput['status']);
		!empty($arrInput['user_name']) && $arrCond['user_name'] = $arrInput['user_name'];
		!empty($arrInput['record_type']) && $arrCond['record_type'] = intval($arrInput['record_type']);

		if (isset($arrInput['record_key1'])) {
			if (is_array($arrInput['record_key1'])) {
				$arrCond['record_key1'] = array(
					'opt' => 'in',
					'val'   => '('.implode(",", $arrInput['record_key1']).')',
                	'quotes'    => 0,
				);
			} else {
				$arrCond['record_key1'] = intval($arrInput['record_key1']);
			}
		}

		if (isset($arrInput['time_start'])) {
			$arrCond['xx'] = array(
				'key' => 'create_time',
				'opt' => '>=',
				'val' => $arrInput['time_start'],
			);
		}

		if (isset($arrInput['time_end'])) {
			$arrCond['xxx'] = array(
				'key' => 'create_time',
				'opt' => '<=',
				'val' => $arrInput['time_end'],
			);
		}

		$hasCount = false;
		if (isset($arrInput['pn']) && isset($arrInput['rn'])) {
			$limit	= intval($arrInput['rn']);
			$offset	= (intval($arrInput['pn']) - 1) * $limit;
			$strAppend	= " order by record_id desc limit $offset,$limit";
			$hasCount = true;
		} else {
			$strAppend	= " order by record_id desc";
		}
		$arrParam	= array(
			'cond'	=> $arrCond,
		);

		!empty($strAppend) && $arrParam['append']	= $strAppend;

		//构造返回值参数
		$arrRet = Dl_Db_Record::select($arrParam);
		if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
			Bingo_Log::warning(sprintf("select ad fail. [input = %s] [ouput = %s]",
				serialize($arrParam), serialize($arrRet)));
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}

		//返回所有值
		$arrData = array();

		if ($hasCount) {
			$arrData['rows']	= $arrRet['data'];
			$arrRet	= Dl_Db_Record::getTotalCount($arrParam);
			if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
				Bingo_Log::warning(sprintf("get total count fail. [input = %s] [ouput = %s]",
					serialize($arrParam), serialize($arrRet)));
				return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
			}
			$props_count = $arrRet['data'];

			$arrData['count'] = $props_count;
			if($props_count > ($offset + count($arrData['rows']))){
				$arrData['has_more']	= 1;
			}
			else{
				$arrData['has_more']	= 0;
			}
		} else {
			$arrData = $arrRet['data'];
		}

		//返回结果
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$arrData);
	}


	/**
	 *
	 *	添加商品信息
	 * 	@param
	 * 	@option
	 * 	@return
	 *
	 */
	public static function addInfo($arrInput){

		//参数校验
		$arrCheckParam  = array(
			'record_type',
		);
		$arrRet =  Util_Function::checkParam($arrInput, $arrCheckParam, true);
		if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
			Bingo_Log::warning("call checkParam int failed.input:[".serialize($arrInput)."].output:[".serialize($arrRet)."].");
			return $arrRet;
		}
		
		//参数校验
		$arrCheckParam  = array(
			'record_input',
			'user_name',
		);
		$arrRet =  Util_Function::checkParam($arrInput,$arrCheckParam);
		if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
			Bingo_Log::warning("call checkParam failed.input:[".serialize($arrParam)."].output:[".serialize($arrRet)."].");
			return $arrRet;
		}

		$arrField	= array(
			'record_type'	=> intval($arrInput['record_type']),
			'record_input'	=> $arrInput['record_input'],
			'user_id'	=> intval($arrInput['user_id']),
			'user_name'	=> $arrInput['user_name'],
			'user_type'	=> intval($arrInput['user_type']),
			'status'	=> intval($arrInput['status']),
			'create_time'	=> time(),
			'record_key1' => strval($arrInput['record_key1']),
		);

		//构造数据库参数
		$arrParam = array(
			'field' => $arrField,
		);

		// insert operations
		$arrRet = Dl_Db_Record::insert($arrParam);
		if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
			Bingo_Log::warning(sprintf("insert new record fail. [input = %s] [ouput = %s]",
				serialize($arrParam), serialize($arrRet)));
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}

		//返回结果
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
	}
}
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
