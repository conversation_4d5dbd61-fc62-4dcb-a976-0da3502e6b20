<?php
/**
 * @file sendGiftInPostAction
 * <AUTHOR>
 * @date 2013/06/19
 * @brief 
 **/

class sendGiftInPostAction extends Util_Base {
	const SCENE_ID = 2000138;
	const AUTH_KEY = 'e4fdc26da31ae483';
    public function _execute() {
    	try {
    		if ($this->_arrUserInfo['is_login'] !== true ){
                //未登陆
                throw new Exception("user need login!",Tieba_Errcode::ERR_USER_NOT_LOGIN);
            }
			$user_id = $this->_arrUserInfo['user_id'];
			$benefit_username = Bingo_Http_Request::getPostNoXssSafe('user_name', '');
			if (empty($benefit_username)) {
				Bingo_Log::warning("input params error");
				throw new Exception("input params error!",Tieba_Errcode::ERR_PARAM_ERROR);
			}
			$input = array(
			    "user_name" => array( //用户id
			        0 => $benefit_username //用户id
			    )
			);
			$res = Tieba_Service::call('user', 'getUidByUnames', $input, NULL, NULL, 'post', 'php', 'utf-8');
    		if( $res === false || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
				Bingo_Log::warning("call service user getUidByUnames error. [input=".serialize($input).'][output='.serialize($res).']');
				throw new Exception("service call fail!",Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
			}
			$benefit_userid = $res['output']['uids'][0]['user_id'];
			$gift_id = Bingo_Http_Request::getPostNoXssSafe('gift_id', 0);
			$num = Bingo_Http_Request::getPostNoXssSafe('num', 1);
			$arrParams = array(
				'pay_userid'  => $user_id,
				'pay_username' => $this->_arrUserInfo['user_name'],
				'benefit_userid' => $benefit_userid,
				'benefit_username' => $benefit_username,
				'scene_id' => self::SCENE_ID,		//pending
				'gift_id' => $gift_id,
				'auth_key' => self::AUTH_KEY,		//pending
				'num' => $num,
			);
	    	$arrRet = Tieba_Service::call('present', 'placeOrder', $arrParams, NULL, NULL, 'POST', 'php', 'utf-8');
			if( $arrRet === false) {
				Bingo_Log::warning("call service present placeOrder error. [input=".serialize($arrParams).'][output='.serialize($arrRet).']');
				throw new Exception("service call fail!",Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
			}
    		if( $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
				Bingo_Log::warning("call service present placeOrder error. [input=".serialize($arrParams).'][output='.serialize($arrRet).']');
				return $this->_jsonRet($arrRet['errno'], Tieba_Error::getUserMsg($arrRet['errno']), $arrRet['data']);
			}
			if (isset($arrRet['data']['goods_image'])) {
				$arrRet['data']['goods_image'] = "http://imgsrc.baidu.com/forum/abpic/item/bd510fb30f2442a76e6ec26fd443ad4bd0130293.jpg";
				$arrRet['data']['goods_pic'] = "http://imgsrc.baidu.com/forum/abpic/item/bd510fb30f2442a76e6ec26fd443ad4bd0130293.jpg";
			}
			$arrRet['data'] = Bingo_Encode::convert($arrRet['data'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);Bingo_Log::warning(serialize($arrRet['data']));
			$this->_jsonRet($arrRet['errno'], Tieba_Error::getUserMsg($arrRet['errno']), $arrRet['data']);
    	} catch(Exception $e){
            Bingo_Log::warning( "no=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), Tieba_Error::getUserMsg($e->getCode()));	
        }
		
    }
}


?>
