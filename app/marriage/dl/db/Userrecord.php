<?php
/**
 * User: <EMAIL>
 * Date: 16/4/16
 * Time: 下午12:22
 */

class Dl_Db_Userrecord extends Dl_Db_Model {

    const DB_TABLE = 'user_record';
    const DB_NAME  = '';
    private static $_arrDB;
    private static $_instance;

    private static $_mysql_operator_white_list = array("+=","-=","|=","&=",">","<",">=","<=","in","like");

	private static $arrField = array(
        "id",
        "order_id",
        "user_id",
        "props_id",
        "props_name",
        "scores",
        "price",
        "num",
        "type",
        "call_from",
        "account_type",
        "status",
        "create_time",
        "update_time",
        "address",
	);

    /**
     * @brief  get the database instance
     * @param  null
     * @return object [database instance]
     */
    public static function getModel(){
        $db_name = self::DB_NAME;
        $db_table= self::DB_TABLE;
        $arrKey  = $db_name.$db_table;
        if(isset(self::$_arrDB[$arrKey])){
            return self::$_arrDB[$arrKey];
        }else{
            $class_name = __CLASS__;
            self::$_arrDB[$arrKey] = new $class_name($db_table,$db_name);
            return self::$_arrDB[$arrKey];
        }
    }

    /**
     * @brief  select operation
     * @param  array
     * @return mixed
     */
    public static function select($arrInput){
        if (!$arrInput['field']) {
            $arrInput['field'] = self::$arrField;
        }
        self::$_instance = self::getModel();
        $arrRet = self::$_instance->baseSelect($arrInput);
		foreach($arrRet['data'] as &$arrDbRet){
			if(!empty($arrDbRet['address'])){
				$arrDbRet['address']	= unserialize($arrDbRet['address']);
			} else {
                $arrDbRet['address'] = array();
            }
		}
		return $arrRet;
    }

    /**
     * @brief  update operation
     * @param  array
     * @return mixed
     */
    public static function update($arrInput){
        self::$_instance = self::getModel();
		if(isset($arrInput['field']['api_list']) && is_array($arrInput['field']['api_list'])){
			$arrInput['field']['api_list']	= serialize($arrInput['field']['api_list']);
		}
        $ret = self::$_instance->baseUpdate($arrInput);
        return $ret;
    }

    /**
     * @brief  insert operation
     * @param  array
     * @return mixed
     */
    public static function insert($arrInput){
        self::$_instance = self::getModel();
		if(isset($arrInput['field']['api_list']) && is_array($arrInput['field']['api_list'])){
			$arrInput['field']['api_list']	= serialize($arrInput['field']['api_list']);
		}
        $arrRet	= self::$_instance->baseInsert($arrInput);
		return $arrRet;
    }


    /**
     * @brief  delete operation
     * @param  array
     * @return mixed
     */
    public static function deleteRecord($arrInput){
        self::$_instance = self::getModel();
        return self::$_instance->baseDelete($arrInput);
    }

    /**
     * @brief  get the total count of the select condition
     * @param  $arrInput
     * @return array
     */
    public static function getTotalCount($arrInput) {
        $arrParam = array(
            'field' => array('COUNT(1)'),
            'cond'  => $arrInput['cond'],
        );
        isset($arrInput['append'])  && $arrParam['append'] = $arrInput['append'];
        self::$_instance = self::getModel();
        $arrRet = self::$_instance->baseSelect($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning("get count failed. [input = %s] [output =%s]",
                serialize($arrParam), serialize($arrRet));
            return self::$_instance->errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, 0);
        }
        return self::$_instance->errRet(Tieba_Errcode::ERR_SUCCESS,
            $arrRet['data'][0]['COUNT(1)']);
    }

    /**
     * @brief  customize query
     * @param  query[string]
     * @return array
     */
    public static function uQuery($strFields, $strConds = null, $strAppend = null) {
        $query = "SELECT $strFields FROM " . self::DB_TABLE;
        if ($strConds) {
            $query = "$query WHERE $strConds ";
        }
        if ($strAppend) {
            $query = "$query $strAppend";
        }
        self::$_instance = self::getModel();
        return self::$_instance->query($query);
    }

    /**
     * @brief  最大章节号
     * @param  $arrInput
     * @return array
     */
    public static function getMaxSceneId($arrInput) {
        $arrParam = array(
            'field' => array('MAX(id)'),
            'cond'  => $arrInput['cond'],
        );
        self::$_instance = self::getModel();
        $arrRet = self::$_instance->baseSelect($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("get count failed. [errno = %s]", serialize($arrRet)));
            return self::$_instance->errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, 0);
        }
        return self::$_instance->errRet(Tieba_Errcode::ERR_SUCCESS, intval($arrRet['data'][0]['MAX(id)']) + 1000001);
    }
    /**
        *
        *   query
        *   @param
        *   @option
        *   @return
        *
     */
    public static function _query($strSql,$db = null){
        self::$_instance = self::getModel();
        return self::$_instance->query($strSql,$db);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function baseSelect($arrInput) {

        if ( false === $this->_before_select($arrInput)) {
            return $this->errRet();
        }

        if( !isset($arrInput['field']) || !is_array($arrInput['field'])){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (isset($arrInput['table']))  {
            $this->table = $arrInput['table'];
        }

        $arrFields = $arrInput['field'];
        $str_cond = null;
        if(isset($arrInput['cond'])&&is_array($arrInput['cond'])){
            foreach($arrInput['cond'] as $key=>$value){
                if($str_cond !== null){
                    $str_cond .= " and ";
                }

                $key = mysql_escape_string(trim($key));
                if (is_int($value)){
                    $str_cond .= "$key=$value ";
                }
                else if (is_string($value)){
                    $value = mysql_escape_string(trim($value));
                    $str_cond .= "$key='$value' ";
                }else if (is_array($value)){
                    $val = $value['val'];
                    if (isset($value['key'])) {
                        $key = mysql_escape_string(trim($value['key']));
                    }
                    $opt = in_array(strval($value['opt']),self::$_mysql_operator_white_list) ? strval($value['opt']) : '=';
                    $quotes = isset($value['quotes']) ?  0 : 1;

                    if(is_int($val)){
                        $str_cond .= "$key $opt $val ";
                    }else{
                        $val = mysql_escape_string(trim($val));
                        if ($quotes === 0){
                            $str_cond .= "$key $opt $val ";
                        }else{
                            $str_cond .= "$key $opt '$val' ";
                        }
                    }

                }

            }
        }
        if(isset($arrInput['cond'])&&is_string($arrInput['cond'])) {
            $str_cond = $arrInput['cond'];
        }
        $str_append = null;
        if(isset($arrInput['append'])){
            $str_append = $arrInput['append'];
        }

        if(isset($arrInput['db'])){
            $db = $arrInput['db'];
        }else{
            $db = $this->getObjDB();
        }
        if(is_null($db)){
            return $this->errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $ret = $db->select($this->table, $arrFields, $str_cond, null, $str_append);
        //Bingo_Log::warning('sql:'.$db->getLastSQL());

        if(DB_DEBUG) {
            Bingo_Log::warning('sql:'.$db->getLastSQL());
        }
        if($ret===false){
            Bingo_Log::warning("[output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return $this->errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array('mysql_errno'=>$db->errno()));
        }
        $this->errno = Tieba_Errcode::ERR_SUCCESS;

        $this->_after_select($arrInput);
        return $this->errRet(Tieba_Errcode::ERR_SUCCESS,$ret);
    }

}
