<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Superboy.php
 * <AUTHOR>
 * @date 2013/09/06 16:28:11
 * @brief 
 *  
 **/

class Ext_Common_Superboy {

    const CUPID_ID_SUPERBOY = 276;
    const CUPID_ID_SUPERBOY_PLAYER = 269;
    const CUPID_ID_SUPERBOY_TV = 271;
    const CUPID_ID_VOICE_PLAYER = 272;
    const CUPID_ID_VOICE_TV = 273;

    private static $_intForumType = 0;
    private static $_intJoinType = 0;

    public static function getMultiInput($objCoreData) {
        $arrRet = array();
        if($objCoreData->checkCupid(self::CUPID_ID_SUPERBOY)) {

            $arrForumBase = $objCoreData->getForum('base');

            $arrInput = array(
                'type' => 'superboy',
                'forum_id' => $arrForumBase['forum_id'],
            );
            $arrOut = Tbapi_Midlib_Midl_Cupid::query($arrInput);

            $bolGetSuperboyData = true;
            if(isset($arrOut[self::CUPID_ID_SUPERBOY_PLAYER])) {
                self::$_intForumType = 1;
                self::$_intJoinType = 2;
            } elseif (isset($arrOut[self::CUPID_ID_SUPERBOY_TV])) {
                self::$_intForumType = 2;
                self::$_intJoinType = 2;
            } elseif (isset($arrOut[self::CUPID_ID_VOICE_PLAYER])) {
                self::$_intForumType = 1;
                self::$_intJoinType = 3;
            } elseif (isset($arrOut[self::CUPID_ID_VOICE_TV])) {
                self::$_intForumType = 2;
                self::$_intJoinType = 3;
            } else {
                $bolGetSuperboyData = false;
            }
        } else {
            $bolGetSuperboyData = false;
        }

        if($bolGetSuperboyData !== true) {
            return false;
        }
        $arrServiceInput = array(
            'forum_id'      => $arrForumBase['forum_id'],
            "forum_type"    => self::$_intForumType //1���˰� 2 ��Ŀ��
        );

        $arrMultiInput['call'][] = array(
            'service_name'  => 'superboy',
            'method'        => 'getMixInfoByForumId',
            'input'         => $arrServiceInput,
        );

        return $arrMultiInput;
    }

    public static function execute($objCoreData, $arrMultiOut) {

        $arrRet = array();
        $arrData = array();

        $arrMultiOut = $arrMultiOut[0];
        if(Tieba_Errcode::ERR_SUCCESS !== $arrMultiOut['errno']) {
            return $arrRet;
        }
        $arrMultiOut["ret"]["pk_info"] = array_values($arrMultiOut["ret"]["pk_info"]);
        $arrData["superboy_player_rank_info"] = $arrMultiOut["ret"]["player_rank_info"];
        $arrData["superboy_pk_info"] = $arrMultiOut["ret"]["pk_info"];


        $arrServiceInput = array(
            'join_type' => $_intJoinType  //join_type: 1 �ܰ�2���У�3������  4 ������һ�𷵻�
        );
        $arrOut = Tieba_Service::call('superboy','getRankinglist',$arrServiceInput, NULL, NULL, 'post', 'php', 'utf-8');
        if(Tieba_Errcode::ERR_SUCCESS !== $arrOut['errno']) {
            Bingo_Log::warning('call service thread::getList failed,errno[' . $arrOut['errno'] . '],errmsg[' . $arrOut['errmsg'] . ']');
        }

        $arrData["superboy_rank_list"] = $arrOut["ret"][0]["players"];
        foreach($arrData["superboy_rank_list"] as $intKey => $arrValue) {
            if($arrData["superboy_player_rank_info"]["player_info"] && $arrValue["player_id"] == $arrData["superboy_player_rank_info"]["player_info"]["player_id"]) {
                $arrData["superboy_player_rank_info"]["player_rank"] = $intKey+1;

            }
            if($arrValue["vote_number"] > 9999999) {
                $arrData["superboy_rank_list"][$intKey]["vote_number"] = ceil($arrValue["vote_number"]/10000)."��";

            }

        }
        $arrData["superboy_forum_type"] = self::$_intForumType; //1 ���˰� 2 ��Ŀ�� 
        $arrData["superboy_join_type"] = self::$_intJoinType;
        $arrRet['superboy']['tpl_var'] = $arrData;

        return $arrRet; 
    }

    public static function getCacheKey($objCoreData) {
        $arrForumBase = $objCoreData->getForum('base');
        $strKey = $arrForumBase['forum_id'];
        return $strKey;
    }    
}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
