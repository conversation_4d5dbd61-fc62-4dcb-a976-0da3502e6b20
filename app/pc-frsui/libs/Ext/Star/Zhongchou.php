<?php
/**
 * @file Zhongchou.php
 * <AUTHOR>
 * @date 2014/08/19 11:24:18
 * @brief
 *
 **/

class Ext_Star_Zhongchou implements Util_CommonBase 
{
	const MODULE_NAME_FRSSTAR    = 'starfrs_p1';
	const START_TIME             = 1408434448;                     // ���ʼʱ��
	const SECOND_PER_STATION     = 1800;                           // ÿ��Сʱ��һ����
	const TABLE_NAME             = 'tb_wordlist_redis_zhongchou';  // ���ôʱ�
	const STATION_NUM            = 3;                              // ����վ�Ƹ���
	static protected  $_conf     = NULL;                           // �����ļ�
	static protected  $_intForumId   = 0;
	static protected  $_strForumName = '';
	static protected  $_intYingyuanId = 0;
	static protected  $_intStartTime  = 0;
	static protected  $_intSwitchOn   = false;
	static protected  $_arrForumNames = array();
	const YINGYUAN_THREAD_ID = 103902; 

	public static function execute($objCoreData) {
		$arrForumBase = $objCoreData->getForum('base');
		$arrUserBase = $objCoreData->getUser('base');
		$arrForumDir = $objCoreData->getForum('dir');
		$arrStyles   = $objCoreData->getForum('style');

		// �������ǰɣ�ֱ�ӷ���
		if (!isset($arrStyles[self::MODULE_NAME_FRSSTAR])){
			return array();
		}

		self::$_intForumId = $arrForumBase['forum_id'];
		self::$_strForumName = $arrForumBase['forum_name'];

		// ��ȡ�����ļ�
		$arrRet = self::_getConfParam();
		if (false === $arrRet){
			return array();
		}else{
			self::$_intStartTime = $arrRet['start_time'];
			self::$_intYingyuanId = $arrRet['forum_id'];
			self::$_arrForumNames = $arrRet['forum_names'];
			self::$_intSwitchOn   = intval($arrRet['switch']);
		}
		if (!(self::$_intForumId == self::$_intYingyuanId || in_array(self::$_strForumName,self::$_arrForumNames))) {
			return array();
		}	
		if (!self::$_intSwitchOn) {
			return array();
		}

		// ��ȡ��Ҫ������
		//$arrYingyuanInfo = self::_getYingyuanInfo(self::YINGYUAN_THREAD_ID);
		$arrYingyuanInfo = self::_getYingyuanInfo(self::$_intYingyuanId);
		$arrStationInfo  = self::_getStationInfo();
		$bolIsYingyuanForum = self::_isYingyuan();

		// ��װ����
		$ret = self::_build($arrYingyuanInfo,$arrStationInfo,$bolIsYingyuanForum);

		$arrOut['yingyuan'] = array('tpl_var'  => $ret);
		return $arrOut;
	}

	/*
	* @desc ��װ����
	* @NOTE: ӦԮ�ɺͷ�ӦԮ��չʾ�����ݲ�һ��
	*/
	static public function _build($arrYingyuanInfo,$arrStation,$bolYingyuan){
		$arrOut = array();
		if ($bolYingyuan){
			$arrOut = $arrYingyuanInfo;
			$arrOut['station_info'] = $arrStation;
			$arrOut['ext_info']     = array(
											'with_donate'   => true,
											);
		}else {
			$arrOut['bus_info'] = array(
										'bus_num'   =>  $arrYingyuanInfo['bus_info']['bus_num'],
										);

			$intForumId = self::_getFidByFname($arrStation['cur_station']);
			$arrOut['switch_info'] = array(
										'show_bus'   => $intForumId == self::$_intForumId,
										);
		}

		return $arrOut;
	}

	/*
	* @desc ��ȡ������Ϣ�����翪ʼʱ�䡢Ѳ���б�
	*/
	protected static function _getConfParam(){
		$objWordServer = Wordserver_Wordlist::factory();
		$arrParam      = array('start_time','forum_names','forum_id','switch');
		$arrRet        = $objWordServer->getValueByKeys($arrParam,self::TABLE_NAME);
		if (NULL === $arrRet){
			Bingo_Log::warning('call Wordserver_Wordlist query key error '.$objWordServer->get_error().' input '.serialize($arrParam));
			return false;
		}

		//$arrRet['forum_names'] = Bingo_Encode::convert($arrRet['forum_names'],Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
		$arrRet['forum_names'] = explode(',',$arrRet['forum_names']);
		$arrRet['start_time']  = intval($arrRet['start_time']);
		$arrRet['switch']      = isset($arrRet['switch']) ? intval($arrRet['switch']) : false;
		return $arrRet;
	}

	/*
	*
	*/
	protected static function _getFidByFname($strFname){
		if (empty($strFname)){
			Bingo_Log::warning('forum_name error '.$strFname);
			return  0;
		}
		$arrInput = array(
                        'query_words'   => array($strFname,),
                		);
		Bingo_Timer::start("service_forum_getFidByFname");
		$arrOut = Tieba_Service::call('forum', 'getFidByFname', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
		Bingo_Timer::end("service_forum_getFidByFname");
        if (Tieba_Errcode::ERR_SUCCESS !== $arrOut['errno']) {
        	Bingo_Log::warning('call service forum::getFidByFname failed,errno[' . $arrOut['errno'] . '],errmsg[' . $arrOut['errmsg'] . ']');
            return false;
        }
		$intFid = intval($arrOut['forum_id'][0]['forum_id']);
		return $intFid;
	}

	 
	/**
	 * ע��: ������cache
	 * @param unknown_type $objCoreData
	 * @return unknown
	 */
	public static function getCacheKey($objCoreData) {
		return false;     // for testing
		$arrForumBase = $objCoreData->getForum('base');
        $strKey = $arrForumBase['forum_id'];
        return $strKey;
	}

	/*
	* @desc �жϵ�ǰ���Ƿ���ӦԮ�ɣ��������Žܰɣ�
	*/
	static protected function _isYingyuan(){
		return (self::$_intForumId == self::$_intYingyuanId );
		//return (self::$_intForumId == 35);
	}

	/*
	* @desc ��ȡ��ӦԮ������
	*/
	static protected function _getYingyuanInfo($forum_id){
		$arrInput = array(
						'forum_id'  => $forum_id,
						'come_from' => 'frs',
						);
		$arrRet = Tieba_Service::call('fstar','getYingyuanInfo',$arrInput, NULL, NULL, 'post', 'php', 'utf-8');
		if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
			Bingo_Log::warning('call fstar::getYingyuanInfo fails with input '.serialize($arrInput).' output '.serialize($arrRet));
			return array();
		}

		return $arrRet['data'];
	}

	/*
	* @desc �ж�Ѳ�ε����ĸ���
	*/
	static protected function  _getStationInfo(){
		// ��ȡ�����ļ�
		/*if (NULL == self::$_conf){
			self::$_conf = Bd_Conf::getConf("/app/fstar/zhongchou");
			if (false === self::$_conf){
				Bingo_Log::warning('init get conf fail');
				return false;
			}
		}
		$arrForum    = self::$_conf['forum_names'];
		*/
		$intCnt      = count(self::$_arrForumNames);	

		$intCurTime = time();
		$intInterval = $intCurTime - self::$_intStartTime;
		$intIndex    = floor($intInterval/self::SECOND_PER_STATION);
		$intIndex    = $intIndex % $intCnt;
		$startIndex  = ($intIndex + $intCnt - 1)%$intCnt;
		$endIndex    = ($intIndex + $intCnt + 1)%$intCnt;
		/*$startIndex  = max(0,$intIndex-1);
		if ($startIndex >= $intCnt){
			$startIndex = $intCnt - 1;
		}
		$endIndex    = min($intIndex+1,$intCnt - 1);
		*/
		for($i=0;$i<self::STATION_NUM;$i++){
			$arrStationInfo[] = self::$_arrForumNames[($i+$startIndex)%$intCnt];
		}
		//$arrStationInfo = array_slice(self::$_arrForumNames,$startIndex,$endIndex-$startIndex+1);
		$arrCurStation  = self::$_arrForumNames[$intIndex];

		if ($intIndex == 0){
			$arrStationInfo[0] = NULL;
		}else if ($intIndex == $intCnt - 1){
			$arrStationInfo[self::STATION_NUM-1] = NULL;
		}

		$arrOut = array(
						'stations'  => $arrStationInfo,
						'cur_station'  => $arrCurStation,
						);

		return $arrOut;
	}
}
