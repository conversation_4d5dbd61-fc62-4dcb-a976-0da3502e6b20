<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2016/6/19
 * Time: 14:40
 */
set_time_limit(0);
ini_set('memory_limit', '1024M');
define('BASE_PATH', dirname(__FILE__) . '/../..');
define('ROOT_PATH', BASE_PATH . '/../..');
/**
 * @param $strClassName
 * @return bool
 */
function __autoload($strClassName)
{
    require_once str_replace('_', '/', $strClassName) .'.php';
    return true;
}
spl_autoload_register('__autoload');

Tieba_Init::init('tbread');


$objWordAna = new Libs_Ext_WordAnalyzer();

$objWordAna->SetSource('李政道是个科学家');

$objWordAna->StartAnalysis();

echo $objWordAna->GetFinallyResult(), PHP_EOL;
