<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2015/5/14
 * Time: 2:32
 */
class Util_Conv {

    /**
     * @param $un
     * @return string
     */
    private static function _unicode_to_utf8($un)
    {
        if($un < 128) {
            return chr($un);
        }

        $utf8 = '';
        for($n = 5; $n > 0; --$n) {
            if($un >= 1<<($n*5+1)) {
                for($i = 0; $i < $n; $i++, $un >>= 6) {
                    $utf8 = chr(($un & 0x3F) | 0x80) . $utf8;
                }
                $utf8 = chr(($un & (1 << (6-$n)) -1) | ((0xFFFF << (7-$n)) & 0xFFFF)) . $utf8;
                break;
            }
        }

        return $utf8;
    }

    /**
     * @param $matches
     * @return string
     */
    private static function _match_converter_base16($matches)
    {
        return self::_unicode_to_utf8(base_convert($matches[1], 16, 10));
    }

    /**
     * @param $matches
     * @return string
     */
    private static function _match_converter($matches)
    {
        return self::_unicode_to_utf8($matches[1]);
    }

    /**
     * @param $content
     * @return mixed
     */
    public static function unicode_to_utf8($content)
    {
        $content = preg_replace_callback('/&#x([0-9a-fA-F]{2,8});/', array('self', '_match_converter_base16'), $content);

        $content = preg_replace_callback('/&#([0-9]{3,10});/',  array('self', '_match_converter'), $content);

        return  $content;
    }
}
