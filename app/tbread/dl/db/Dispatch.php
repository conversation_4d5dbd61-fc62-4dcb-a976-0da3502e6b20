<?php
/**
 * User: pangz<PERSON><PERSON>
 * Date: 16/4/16
 * Time: ����12:22
 */

class Dl_Db_Dispatch extends Dl_Db_Model {

    const DB_TABLE = 'dispatch_info';	//Ԫ����Ŀ����
    const DB_NAME  = 'forum_tbread';	//�û����Ѹ������ݿ�
    private static $_arrDB;
    private static $_instance;

    private static $arrField = array(
		'id',
		'thread_id',
		'forum_id',
		'LO_name',
		'LT_name',
		'create_time',
		'update_time',
    );

    /**
     * @brief  get the database instance
     * @param  null
     * @return object [database instance]
     */
    public static function getModel(){
        $db_name = self::DB_NAME;
        $db_table= self::DB_TABLE;
        $arrKey  = $db_name.$db_table;
        if(isset(self::$_arrDB[$arrKey])){
            return self::$_arrDB[$arrKey];
        }else{
            $class_name = __CLASS__;
            self::$_arrDB[$arrKey] = new $class_name($db_table,$db_name);
            return self::$_arrDB[$arrKey];
        }
    }

    /**
     * @brief  select operation
     * @param  array
     * @return mixed
     */
    public static function select($arrInput){
        if (!$arrInput['field']) {
            $arrInput['field'] = self::$arrField;
        }
        self::$_instance = self::getModel();
        $arrRet = self::$_instance->baseSelect($arrInput);
		foreach($arrRet['data'] as &$arrDbRet){
			isset($arrDbRet['forum_id']) && strlen($arrDbRet['forum_id']) > 0 && $arrDbRet['forum_id']	= unserialize($arrDbRet['forum_id']);
			isset($arrDbRet['LO_name']) && strlen($arrDbRet['LO_name']) > 0 && $arrDbRet['LO_name']	= unserialize($arrDbRet['LO_name']);
			isset($arrDbRet['LT_name']) && strlen($arrDbRet['LT_name']) > 0 && $arrDbRet['LT_name']	= unserialize($arrDbRet['LT_name']);
		}
		return $arrRet;
    }

    /**
     * @brief  update operation
     * @param  array
     * @return mixed
     */
    public static function update($arrInput){
        self::$_instance = self::getModel();
		isset($arrInput['field']['forum_id']) && is_array($arrInput['field']['forum_id']) && $arrInput['field']['forum_id']	= serialize($arrInput['field']['forum_id']);
		isset($arrInput['field']['LO_name']) && is_array($arrInput['field']['LO_name']) && $arrInput['field']['LO_name']	= serialize($arrInput['field']['LO_name']);
		isset($arrInput['field']['LT_name']) && is_array($arrInput['field']['LT_name']) && $arrInput['field']['LT_name']	= serialize($arrInput['field']['LT_name']);
        $ret = self::$_instance->baseUpdate($arrInput);
        return $ret;
    }

    /**
     * @brief  insert operation
     * @param  array
     * @return mixed
     */
    public static function insert($arrInput){
        self::$_instance = self::getModel();
		isset($arrInput['field']['forum_id']) && is_array($arrInput['field']['forum_id']) && $arrInput['field']['forum_id']	= serialize($arrInput['field']['forum_id']);
		isset($arrInput['field']['LO_name']) && is_array($arrInput['field']['LO_name']) && $arrInput['field']['LO_name']	= serialize($arrInput['field']['LO_name']);
		isset($arrInput['field']['LT_name']) && is_array($arrInput['field']['LT_name']) && $arrInput['field']['LT_name']	= serialize($arrInput['field']['LT_name']);
        $arrRet	= self::$_instance->baseInsert($arrInput);
		return $arrRet;
    }


    /**
     * @brief  delete operation
     * @param  array
     * @return mixed
     */
    public static function deleteRecord($arrInput){
        self::$_instance = self::getModel();
        return self::$_instance->baseDelete($arrInput);
    }

    /**
     * @brief  get the total count of the select condition
     * @param  $arrInput
     * @return array
     */
    public static function getTotalCount($arrInput) {
        $arrParam = array(
            'field' => array('COUNT(id)'),
            'cond'  => $arrInput['cond'],
			'append'	=> $arrInput['append'],
        );
        self::$_instance = self::getModel();
        $arrRet = self::$_instance->baseSelect($arrParam);

        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning("get count failed. [input = %s] [output =%s]",
                serialize($arrParam), serialize($arrRet));
            return self::$_instance->errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, 0);
        }
        return $arrRet;
    }

    /**
     * @brief  customize query
     * @param  query[string]
     * @return array
     */
    public static function uQyery($strSql) {
        self::$_instance = self::getModel();
        return self::$_instance->query($strSql);
    }

}
