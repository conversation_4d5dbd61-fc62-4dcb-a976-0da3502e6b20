<?php
/*
 * Copyright (c) 2014 Baidu, Inc.
 * All rights reserved.
 *
 * <AUTHOR>
 * @date      2014/07/11
 * @abstract  schedule methods
 *            
 */

class Service_Schedule
{
    const DB_NAME = 'forum_tradecenter';

    const DEFAULT_INTERVAL = 86400;
    const MAX_CYCLE_MONTH = 6;

    const STATUS_READY    = 0;
    const STATUS_OCCUPIED = 1;

    const STATUS_INTENDED = 2;
    const STATUS_USED     = 3;
    // 虚拟状态，数据库不存在，只在service中有
    const STATUS_PASSED   = 4;
    const STATUS_BUFFERED = 5;

    private static $_objDB = null;

    private static function _init()
    {
        if (is_object(self::$_objDB))
        {
            return true;
        }
        self::$_objDB = new Service_Lib_DB(self::DB_NAME);
        self::$_objDB->init();
        if (!is_object(self::$_objDB))
        {
            Bingo_Log::warning('fail to init db object');
            return false;
        }

        return true;
    }

    public static function setClassSchedule($arrInput)
    {
        if (!self::_init())
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        
        $arrLogInput     = $arrInput;

        $intClassId      = intval($arrInput['class_id']);
        $intCycle        = intval($arrInput['cycle']);
        $dblPrice        = doubleval($arrInput['price']);
        $intCurrencyType = intval($arrInput['currency_type']);
        $intUuapId       = intval($arrInput['op_uuap_id']);
        $strUuapName     = self::$_objDB->escape(trim($arrInput['op_uuap_name']));
        $intOpTime       = time();

        self::$_objDB->autoCommit(false);
        self::$_objDB->startTransaction();

        $arrParam = array(
            'class_id'      => $intClassId,
            'cycle'         => $intCycle,
            'price'         => $dblPrice,
            'currency_type' => $intCurrencyType,
            'op_uuap_id'    => $intUuapId,
            'op_uuap_name'  => $strUuapName,
            'op_time'       => $intOpTime,
        );
        
        $strSql = "REPLACE INTO schedule_class(class_id,cycle,price,currency_type,op_uuap_id,op_uuap_name,op_time) 
                   VALUES({class_id:n},{cycle:n},{price:n},{currency_type:n},{op_uuap_id:n},{op_uuap_name:s},{op_time:n})";
        $arrRet = self::$_objDB->query($strSql, $arrParam);
        if ($arrRet === false)
        {
            self::$_objDB->getErrInfo();
            self::$_objDB->rollback();
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        self::$_objDB->commit();
        self::$_objDB->autoCommit(true);
        Service_ScheduleLog::addClassScheduleLog($arrLogInput);
        return Service_Lib_Utility::buildReturn();
    }

    public static function getClassSchedule($arrInput)
    {
        if (!self::_init())
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $intClassId = intval($arrInput['class_id']);

        $strSql   = "SELECT schedule_id,class_id,cycle,price,currency_type FROM schedule_class WHERE class_id={class_id:n} ORDER BY schedule_id DESC LIMIT 1";
        $arrParam = array(
            'class_id' => $intClassId,
        );
        $arrRet = self::$_objDB->query($strSql, $arrParam);
        if ($arrRet === false)
        {
            self::$_objDB->getErrInfo();
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrRet[0]);
    }
    
    public static function getAllClassSchedule($arrInput)
    {
        if (!self::_init())
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $strSql = "SELECT schedule_id,class_id,cycle,price,currency_type, op_time FROM schedule_class ORDER BY schedule_id DESC";
        $arrRet = self::$_objDB->query($strSql, array());
        if ($arrRet === false)
        {
            self::$_objDB->getErrInfo();
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }
    
    public static function getClassScheduleByFid($arrInput)
    {
        if (!self::_init())
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $intForumID = intval($arrInput['forum_id']);
        
        $arrParam = array(
            'forum_id' => $intForumID,
        );
        //get class id
        $arrRet = Service_Provider::getProviderByFid($arrParam);
        if ($arrRet === false)
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        if (!isset($arrRet['output']['class_id']))
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS, array());
        }
        //get schedule info
        $arrParam = array(
            'class_id' => intval($arrRet['output']['class_id']),
        );
        $arrRet = self::getClassSchedule($arrParam);
        return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrRet['output']);
    }

    public static function delClassSchedule ($arrInput) {
        if (!self::_init())
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        
        $intClassID = isset($arrInput['class_id'])?intval($arrInput['class_id']):-1;
        if ($intClassID < 0){
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        $strSql   = "DELETE FROM schedule_class WHERE class_id={class_id:n}";
        $arrParam = array(
            'class_id' => $intClassID,
        );
        $arrRet = self::$_objDB->query($strSql, $arrParam);
        if ($arrRet === false)
        {
            self::$_objDB->getErrInfo();
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        
        //add log
        $arrInput['op_type'] = 2;//del
        self::$_objDB->autoCommit(true);
        Service_ScheduleLog::addClassScheduleLog($arrInput);
        
        return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS);        
    }

    public static function mgetForumSchedule($arrInput)
    {
        $arrForumId = Tieba_Service::getArrayParams($arrInput, 'forum_id');
        $arrData    = array();
        foreach ($arrForumId as $intForumId)
        {
            $arrInput  = array(
                'forum_id' => intval($intForumId),
            );
            $arrOutput = self::getForumSchedule($arrInput);
            if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS)
            {
                //return $arrOutput;
                Bingo_Log::warning("get forum schedule of [$intForumId] failed with errno [{$arrOutput['errno']}]");
            }
            $arrData[intval($intForumId)] = $arrOutput['output'];
        }

        return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrData);
    }

    public static function getForumSchedule($arrInput)
    {
        if (!self::_init())
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $intForumId = intval($arrInput['forum_id']);

        // check params
        if ($intForumId <= 0)
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // get forum class id
        $arrInput = array(
            'forum_id' => $intForumId,
        );
        $arrOutput = Service_Provider::getProviderByFid($arrInput);
        if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS)
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_KLOSE_GET_PROVIDER_INFO_FAILED);
        }
        if (!isset($arrOutput['output']['class_id']))
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS, array());
        }
        $intClassId = intval($arrOutput['output']['class_id']);

        // get class schedule
        $arrInput  = array(
            'class_id' => $intClassId,
        );
        $arrOutput = self::getClassSchedule($arrInput);
        if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS)
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_KLOSE_GET_CLASS_SCHEDULE_FAILED);
        }
        $arrClassSchedule = $arrOutput['output'];
        if ($arrClassSchedule === null || empty($arrClassSchedule))
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_KLOSE_NO_CLASS_SCHEDULE);
        }

        $dblDefaultPrice        = doubleval($arrClassSchedule['price']);
        $intDefaultCurrencyType = intval($arrClassSchedule['currency_type']);
        $intCycle               = intval($arrClassSchedule['cycle']);
        if ($intCycle <= 0 && $intCycle > self::MAX_CYCLE_MONTH)
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_KLOSE_ILLEGAL_CYCLE);
        }

        // get buffer time from system param
        $arrInput = array(
            'param_name' => 'minimum_schedule_interval',
        );
        $arrOutput = Service_Param::getSystemParamValueByName($arrInput);
        if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS || !is_numeric($arrOutput['output']))
        {
            Bingo_Log::warning('get system param failed');
            Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_KLOSE_GET_SYSTEM_PARAM_FAILED);
        }
        $intBufferTime     = intval($arrOutput['output']) * 3600;
        $intCurTime        = time();
        $intCycleStartTime = Service_Lib_Time::getMonthFirstTime($intCurTime);
        $intCurDayTime     = Service_Lib_Time::getZeroHour($intCurTime);
        $intAvailableTime  = Service_Lib_Time::getZeroHour($intCurTime) + $intBufferTime;
        $intCycleEndTime   = Service_Lib_Time::getOpenCycleEndTime($intCurTime, $intCycle);

        // get forum schedule
        $strSql = "SELECT schedule_id,forum_id,start_time,end_time,price,currency_type,status FROM schedule_forum 
                   WHERE forum_id={forum_id:n} AND start_time<={end_time:n} AND end_time>={start_time:n}";
        $arrParam = array(
            'forum_id'   => $intForumId,
            'start_time' => $intCycleStartTime,
            'end_time'   => $intCycleEndTime,
        );
        $arrRet = self::$_objDB->query($strSql, $arrParam);
        if ($arrRet === false)
        {
            self::$_objDB->getErrInfo();
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrForumSchedule = $arrRet;

        /*
         * 构造TimeSection输入
         * 第一个节点是本月到六个月内的时间
         * 之后插入schedule_forum表中从缓冲期结束后的所有节点
         * 接着是本月第一天到本日时间
         * 最后是本日时间到缓冲期结束时间(可用时间)
         */
        $arrRawData = array();
        $arrRawData[] = array(
            'start_time'    => $intCycleStartTime,
            'end_time'      => $intCycleEndTime,
            'status'        => self::STATUS_READY,
            'price'         => $dblDefaultPrice,
            'currency_type' => $intDefaultCurrencyType,
        );

        $objTimeSection = new Service_Lib_TimeSection($arrRawData);
        if (!$objTimeSection->init())
        {
            Bingo_Log::warning('init TimeSection error');
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_KLOSE_INIT_TIMESECTION_FAILED);
        }

        if (is_array($arrForumSchedule) && count($arrForumSchedule) > 0)
        {
            foreach ($arrForumSchedule as $arrNode)
            {
                if ($arrNode['end_time'] < $intCycleStartTime || $arrNode['start_time'] > $intCycleEndTime)
                {
                    continue;
                }
                if ($arrNode['start_time'] < $intCycleStartTime)
                {
                    $arrNode['start_time'] = $intCycleStartTime;
                }
                if ($arrNode['end_time'] > $intCycleEndTime)
                {
                    $arrNode['end_time'] = $intCycleEndTime;
                }
                $objTimeSection->setTimeSection(
                    intval($arrNode['start_time']), 
                    intval($arrNode['end_time']), 
                    intval($arrNode['status']), 
                    doubleval($arrNode['price']), 
                    intval($arrNode['currency_type'])
                );
            }
        }

        $objTimeSection->setTimeSection($intCycleStartTime, $intCurDayTime, self::STATUS_PASSED, $dblDefaultPrice, $intDefaultCurrencyType);
        $objTimeSection->setTimeSection($intCurDayTime, $intAvailableTime, self::STATUS_BUFFERED, $dblDefaultPrice, $intDefaultCurrencyType);

        $arrOutput = $objTimeSection->output();

        return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    public static function setForumSchedule($arrInput)
    {
        if (!self::_init())
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $arrLogInput     = $arrInput;
        $intForumId      = intval($arrInput['forum_id']);
        $intStartTime    = intval($arrInput['start_time']);
        $intEndTime      = intval($arrInput['end_time']);
        $dblPrice        = doubleval($arrInput['price']);
        $intCurrencyType = intval($arrInput['currency_type']);
        $intStatus       = intval($arrInput['status']);
        $intUuapId       = intval($arrInput['op_uuap_id']);
        $strUuapName     = self::$_objDB->escape(trim($arrInput['op_uuap_name']));
        $intOpTime       = time();

        // check params
        if ($intForumId <= 0 || ($dblPrice < 0 && $intStatus !==1 ))
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // start transaction
        self::$_objDB->autoCommit(false);
        self::$_objDB->startTransaction();

        // get forum class id
        $arrInput = array(
            'forum_id' => $intForumId,
        );
        $arrOutput = Service_Provider::getProviderByFid($arrInput);        
        if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS)
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_KLOSE_GET_PROVIDER_INFO_FAILED);
        }
        if (!isset($arrOutput['output']['class_id']))
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS, array());
        }
        $intClassId = intval($arrOutput['output']['class_id']);

        // get class schedule
        $arrInput  = array(
            'class_id' => $intClassId,
        );
        $arrOutput = self::getClassSchedule($arrInput);
        if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS)
        {
            Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_KLOSE_GET_CLASS_SCHEDULE_FAILED);
        }
        $arrClassSchedule = $arrOutput['output'];

        if ($arrClassSchedule === null || empty($arrClassSchedule))
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_KLOSE_NO_CLASS_SCHEDULE);
        }

        $dblDefaultPrice        = doubleval($arrClassSchedule['price']);
        $intDefaultCurrencyType = intval($arrClassSchedule['currency_type']);
        $intCycle               = intval($arrClassSchedule['cycle']);
        if ($intCycle <= 0 && $intCycle > self::MAX_CYCLE_MONTH)
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_KLOSE_ILLEGAL_CYCLE);
        }

        // get buffer time from system param
        $arrInput = array(
            'param_name' => 'minimum_schedule_interval',
        );
        $arrOutput = Service_Param::getSystemParamValueByName($arrInput);
        if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS || !is_numeric($arrOutput['output']))
        {
            Bingo_Log::warning('get system param failed');
            Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_KLOSE_GET_SYSTEM_PARAM_FAILED);
        }
        $intBufferTime     = intval($arrOutput['output']) * 3600;
        $intCurTime        = time();
        $intCycleStartTime = Service_Lib_Time::getMonthFirstTime($intCurTime);
        $intCurDayTime     = Service_Lib_Time::getZeroHour($intCurTime);
        $intAvailableTime  = Service_Lib_Time::getZeroHour($intCurTime) + $intBufferTime;
        $intCycleEndTime   = Service_Lib_Time::getOpenCycleEndTime($intCurTime, $intCycle);

        if ($intEndTime < $intCurDayTime || $intEndTime > $intCycleEndTime || $intStartTime > $intCycleEndTime || $intStartTime < $intCycleStartTime)
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // get forum schedule
        $strSql = "SELECT schedule_id,forum_id,start_time,end_time,price,currency_type,status FROM schedule_forum 
                   WHERE forum_id={forum_id:n} AND start_time<={end_time:n} AND end_time>={start_time:n} FOR UPDATE";
        $arrParam = array(
            'forum_id'   => $intForumId,
            'end_time'   => $intCycleEndTime,
            'start_time' => $intCycleStartTime,
        );
        $arrRet = self::$_objDB->query($strSql, $arrParam);
        if ($arrRet === false)
        {
            self::$_objDB->getErrInfo();
            self::$_objDB->rollback();
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrForumSchedule = $arrRet;

        /*
         * 构造TimeSection输入
         * 第一个节点是本月到六个月内的时间
         * 之后插入schedule_forum表中从缓冲期结束后的所有节点
         * 接着是本月第一天到本日时间
         * 最后是本日时间到缓冲期结束时间(可用时间)
         */
        $arrRawData = array();
        $arrRawData[] = array(
            'start_time'    => $intCycleStartTime,
            'end_time'      => $intCycleEndTime,
            'status'        => self::STATUS_READY,
            'price'         => $dblDefaultPrice,
            'currency_type' => $intDefaultCurrencyType,
        );

        $objTimeSection = new Service_Lib_TimeSection($arrRawData);
        if (!$objTimeSection->init())
        {
            Bingo_Log::warning('init TimeSection error');
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_KLOSE_INIT_TIMESECTION_FAILED);
        }

        if (is_array($arrForumSchedule) && count($arrForumSchedule) > 0)
        {
            foreach ($arrForumSchedule as $arrNode)
            {
                if ($arrNode['end_time'] < $intCycleStartTime || $arrNode['start_time'] > $intCycleEndTime)
                {
                    continue;
                }
                if ($arrNode['start_time'] < $intCycleStartTime)
                {
                    $arrNode['start_time'] = $intCycleStartTime;
                }
                if ($arrNode['end_time'] > $intCycleEndTime)
                {
                    $arrNode['end_time'] = $intCycleEndTime;
                }
                $objTimeSection->setTimeSection(
                    intval($arrNode['start_time']), 
                    intval($arrNode['end_time']), 
                    intval($arrNode['status']), 
                    doubleval($arrNode['price']), 
                    intval($arrNode['currency_type'])
                );
            }
        }

        $objTimeSection->setTimeSection($intCycleStartTime, $intCurDayTime, self::STATUS_PASSED, $dblDefaultPrice, $intDefaultCurrencyType);
        $objTimeSection->setTimeSection($intCurDayTime, $intAvailableTime, self::STATUS_BUFFERED, $dblDefaultPrice, $intDefaultCurrencyType);

        // 先检测是否能插入TimeSection中
        $boolStatusOK = $objTimeSection->checkSectionStatus($intStartTime, $intEndTime, self::STATUS_READY);
        if (!$boolStatusOK)
        {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_KLOSE_SCHEDULE_IS_OCCUPIED);
        }
        
        $objTimeSection->setTimeSection($intStartTime, $intEndTime, $intStatus, $dblPrice, $intCurrencyType);
        
        // 删除原有值
        $strSql = "DELETE FROM schedule_forum WHERE forum_id={forum_id:n} AND start_time<={end_time:n} AND end_time>={start_time:n}";
        $arrParam = array(
            'forum_id'   => $intForumId,
            'end_time'   => $intCycleEndTime,
            'start_time' => $intCycleStartTime,
        );
        $arrRet = self::$_objDB->query($strSql, $arrParam);
        if ($arrRet === false)
        {
            self::$_objDB->getErrInfo();
            self::$_objDB->rollback();
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        // 插入新值
        $arrSchedule = $objTimeSection->output();
        // 过滤生成的无用节点和同默认值相等的节点
        foreach ($arrSchedule as $key => $arrNode)
        {
            if ($arrNode['status'] === self::STATUS_BUFFERED || $arrNode['status'] === self::STATUS_PASSED)
            {
                unset($arrSchedule[$key]);
            }
            if ($arrNode['status'] === self::STATUS_READY && $arrNode['price'] === $dblDefaultPrice && $arrNode['currency_type'] === $intDefaultCurrencyType)
            {
                unset($arrSchedule[$key]);
            }
        }
        
        if (count($arrSchedule) > 0)
        {
            $arrData = array();
            $strData = '';
            foreach ($arrSchedule as $arrNode)
            {
                $arrData[] = "({$intForumId},{$arrNode['start_time']},{$arrNode['end_time']},{$arrNode['price']},{$arrNode['currency_type']},{$arrNode['status']},{$intUuapId},'{$strUuapName}',{$intCurTime})";
            }
            $strData = implode(',', $arrData);
            $strSql = "INSERT INTO schedule_forum(forum_id,start_time,end_time,price,currency_type,status,op_uuap_id,op_uuap_name,op_time) VALUES{$strData}";

            $arrRet = self::$_objDB->query($strSql, array());
            if ($arrRet === false)
            {
                self::$_objDB->getErrInfo();
                self::$_objDB->rollback();
                return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
        }

        self::$_objDB->commit();
        
        //add log
        self::$_objDB->autoCommit(true);
        Service_ScheduleLog::addForumScheduleLog($arrLogInput);
        
        return Service_Lib_Utility::buildReturn();
    }

    public static function getForumProductSchedule($arrInput)
    {
        $intForumId   = intval($arrInput['forum_id']);
        $intStartTime = intval($arrInput['start_time']);
        $intEndTime   = intval($arrInput['end_time']);
        $arrForumSchedule = self::getForumSchedule($arrInput);
        if ($arrForumSchedule['errno'] !== Tieba_Errcode::ERR_SUCCESS)
        {
            return $arrForumSchedule;
        }

        $arrRawData = $arrForumSchedule['output'];
        $objTimeSection = new Service_Lib_TimeSection($arrRawData);
        if (!$objTimeSection->init()) {
            Bingo_Log::warning('init TimeSection error');
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_KLOSE_INIT_TIMESECTION_FAILED);
        }
        // check forum product schedule
        $strSql = "SELECT forum_id,start_time,end_time,product_status FROM product WHERE forum_id={forum_id:n} AND start_time<={end_time:n} AND end_time>={start_time:n} AND product_status<>0";
        $arrParam = array(
            'forum_id'   => $intForumId,
            'start_time' => $intStartTime,
            'end_time'   => $intEndTime,
        );
        $arrRet = self::$_objDB->query($strSql, $arrParam);
        if ($arrRet === false) {
            Bingo_Log::warning('get product schedule failed');
            self::$_objDB->getErrInfo();
            self::$_objDB->rollback();
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        foreach ($arrRet as $arrNode) {
            $intNodeStart    = intval($arrNode['start_time']);
            $intNodeEnd      = intval($arrNode['end_time']);
            $intNodeStatus   = intval($arrNode['product_status']);
            $arrPriceSection = $objTimeSection->getTimeSection($intNodeStart, $intNodeEnd);
            foreach ($arrPriceSection as $arrSection)
            {
                $intSecStart    = intval($arrSection['start_time']);
                $intSecEnd      = intval($arrSection['end_time']);
                $dblSecPrice    = doubleval($arrSection['price']);
                $intSecCurrType = intval($arrSection['currency_type']);

                $objTimeSection->setTimeSection($intSecStart, $intSecEnd, $intNodeStatus, $dblSecPrice, $intSecCurrType);
            }
        }
        
        return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS, $objTimeSection->output());
    }

    public static function getSchedulePrice($arrInput)
    {
        $intForumId   = intval($arrInput['forum_id']);
        $intStartTime = intval($arrInput['start_time']);
        $intEndTime   = intval($arrInput['end_time']);

        $arrInput = array(
            'forum_id' => $intForumId,
        );
        $arrOutput = self::getForumSchedule($arrInput);
        if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS)
        {
            return Service_Lib_Utility::buildReturn($arrOutput['errno']);
        }
        $arrRawData = $arrOutput['output'];
        $objTimeSection = new Service_Lib_TimeSection($arrRawData);
        if (!$objTimeSection->init())
        {
            Bingo_Log::warning('init TimeSection error');
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_KLOSE_INIT_TIMESECTION_FAILED);
        }

        $dblPrice = $objTimeSection->calculatePrice($intStartTime, $intEndTime, self::DEFAULT_INTERVAL);

        return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS, $dblPrice);
    }

    public static function mgetSchedulePrice($arrInput)
    {
        $arrSchedule = Tieba_Service::getArrayParams($arrInput, 'schedule_list');
        $dblTotal = 0;
        $dblDiscount = 0;
        $arrSchPrice = array();
        foreach ($arrSchedule as $arrItem)
        {
            $arrPrice = self::getSchedulePrice($arrItem);
            if ($arrPrice['errno'] !== Tieba_Errcode::ERR_SUCCESS)
            {
                return $arrPrice;
            }
            $dblPrice = $arrPrice['output'];
            $arrSchPrice[] = $dblPrice;
            $dblTotal += $dblPrice;
        }

        $dblDiscount = Service_Lib_Utility::calculateDiscount($dblTotal);
        $arrRet = array(
            'schedule_price' => $arrSchPrice,
            'total_price' => $dblTotal,
            'discount' => $dblDiscount,
        );

        return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }
}
