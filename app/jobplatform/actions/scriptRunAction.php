<?php
//set_time_limit(0);
//ini_set("memory_limit", "1024M");
	
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-08-26 09:10:03
 * @comment ��Ʒ����ӿ�
 * @version
 */
class scriptRunAction extends Util_Base {	

	public function execute()
	{
		//set_time_limit(0);
	    //ini_set("memory_limit", "1024M");
	    //$strDir = dirname(__FILE__);
        define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../' );
	    
		try {
			//�����ȡ			
			$task_id = intval(Bingo_Http_Request::get('task_id',''));			
            if($task_id  < 0){
                Bingo_Log::warning("param error!! task_id[". $task_id . "]");
                $errno = Tieba_Errcode::ERR_PARAM_ERROR;
                $arrRet = array(
                    'errno' => $errno,
                    'errmsg' => Tieba_Error::getErrmsg($errno)
                );
                echo json_encode($arrRet);
                return;
            }
            $arrRet = array(
                'errno' => 0, 
                'errmsg' => Tieba_Error::getErrmsg(0)
            );
            echo json_encode($arrRet);

            //return first
            fastcgi_finish_request();
            while(ob_get_level()){
                    ob_end_clean();
            }
            header('Connection: close');
            ignore_user_abort();
            ob_start();
            echo('Connection Closed');
            $size = ob_get_length();
            header("Content-Length: $size");
            ob_end_flush();
            flush();

            //run script
            //test code need to comment
    //        if($task_id != 1){
    //            return;
     //       }

            $magicNum = 'TiebaPlatform';
            $cmd = ROOT_PATH . '/hhvm/bin/hhvm ' . ROOT_PATH. '/app/jobplatform/script/callTask.php ' .$magicNum . ' ' . $task_id .' >/dev/null 2>&1 &';
            //$cmd = ROOT_PATH . '/php/bin/php ' . ROOT_PATH. '/app/jobplatform/script/callTask.php ' .$magicNum . ' ' . $task_id .' >'.ROOT_PATH.'/app/jobplatform/script/callTask.log 2>&1 &';
            //$cmd = ROOT_PATH . '/php/bin/php ' . ROOT_PATH. '/app/jobplatform/script/callTask.php ' .$magicNum . ' ' . $task_id .' >/dev/null 2>&1 &';
            //Bingo_Log::warning( "==========================call task start : task_id :".$task_id . ', cmd: ' .$cmd);
            $output = array();
            $shellRet = -1; 
            exec($cmd, $output, $shellRet);
            if($shellRet != 0){
                Bingo_Log::warning( "==========================call task failed: task_id :".$task_id . ', cmd: ' .$cmd);
            }
            return;
		}
		catch(Util_Exception $e)
		{
			Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
			$this->_jsonStatusRet($e->getMessage());
		}
	}
}
?>

