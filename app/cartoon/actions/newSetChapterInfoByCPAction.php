<?php
/**
 * @file   setBookInfoByCPAction.php
 * <AUTHOR>
 * @date   2016/04/14 19:11:16
 * @brief  修改校验规则及数据
 **/

class newSetChapterInfoByCPAction extends Bingo_Action_Abstract {

    private static $arrParams = array();
    private static $arrChapterInfo = null;
    private static $arrDbBook   = array();
    private static $intCpId     = 0;
    private static $strSign     = '';
    private static $strTimeStamp= '';
    private static $strData     = '';
    /**
     * @brief  check params
     * @return bool
     */
    private function _checkParams() {
        self::$intCpId = Bingo_Http_Request::getNoXssSafe('cp_id', 0);
        self::$strSign = Bingo_Http_Request::getNoXssSafe('sign', '');
        self::$strTimeStamp = Bingo_Http_Request::getNoXssSafe('timeStamp', '');
        self::$strData      = Bingo_Http_Request::getRaw('data', '');

        if (self::$intCpId <=0 || empty(self::$strSign) || empty(self::$strTimeStamp) || empty(self::$strData)) {
            Bingo_Log::warning("Param error. mis data, cp_id timeStamp or sign");
            return false;
        }
        
        self::$arrChapterInfo = json_decode(urldecode(self::$strData), true);

        if (empty(self::$arrChapterInfo)) {
            Bingo_Log::warning("param error. data is empty");
            return false;
        }

        // max book amount each time
        if (count(self::$arrChapterInfo) > Util_Define::MAX_PUSH_AMOUNT) {
            Bingo_Log::warning("Param error. The amount has exceeded the max amount");
            return false;
        }

        $arrField = array(
            'cartoon_id',
            'inner_chapter_num',
            'chapter_name',
            'zip_url',
        );
        foreach (self::$arrChapterInfo as $_chapter) {
            foreach ($arrField as $_field) {
                if (empty($_chapter[$_field])) {
                    Bingo_Log::warning(sprintf("param error. the key [%s] is empty", $_field));
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * @brief  check the identity of the CP
     * @return bool
     */
    private function _checkCPIdentity() {
        $strKey = Util_Define::getKeyByCpId(self::$intCpId);
        if (strlen($strKey) > 10) {
            $strMySign = md5(strval(self::$intCpId) . '_' . $strKey . '_' .  strval(self::$strTimeStamp) . '_' . strval(self::$strData));
        }
        if ($strMySign == self::$strSign) {
            return true;
        }
        else {
            return false;
        }
    }

    /**
     * @return bool
     */
    public function execute() {
        // check params
        if (!$this->_checkParams()) {
            echo json_encode($this->_errorRet(Util_Define::ERROR_PARAM_ERROR), JSON_UNESCAPED_UNICODE);
            return false;
        }

        // check cp identity
        if (!$this->_checkCPIdentity()) {
            echo json_encode($this->_errorRet(Util_Define::ERROR_INVALID_CP), JSON_UNESCAPED_UNICODE);
            return true;
        }

        $arrOutput = array();

        foreach (self::$arrChapterInfo as $chapter) {
            $intCartoonId       = intval($chapter['cartoon_id']);
            $intInnerChapterNum = intval($chapter['inner_chapter_num']);
            $arrData = array(
                'cartoon_id'        => $intCartoonId,
                'inner_chapter_num' => $intInnerChapterNum,
            );
            if (isset($chapter['callback'])) {
                $arrData['callback'] = $chapter['callback'];
            }
            self::$arrDbBook = array();
            // check if the book is valid
            $intStatus = $this->_getBookStatus($intCartoonId);
            if (Util_Define::ERROR_INTERNAL_ERROR == $intStatus){
                $arrOutput[] = $this->_errorRet(Util_Define::ERROR_INTERNAL_ERROR, $arrData);
                continue;
            }

            if (Util_Define::ERROR_BOOK_NOT_EXIST == $intStatus){
                $arrOutput[] = array(
                    'errno'  => Util_Define::ERROR_STATUS_BOOK_NOT_EXIST,
                    'errmsg' => Util_Define::getErrorMsgForBookStatus(Util_Define::ERROR_STATUS_BOOK_NOT_EXIST),
                    'data'   => $arrData,
                );
                continue;
            }
            //判断cp_id
            $intCpId = intval(self::$arrDbBook['cp_id']);
            //没有权限修改
            if ($intCpId != 0 && $intCpId != self::$intCpId) {
                $arrOutput[] = $this->_errorRet(Util_Define::ERROR_CHAPTER_DENY, $arrData);
                continue;
            }

            $arrChapterStatus = $this->_getChapterStatus($intCartoonId, $intInnerChapterNum);

            // get chapter status failed
            if (Util_Define::ERROR_INTERNAL_ERROR == $arrChapterStatus['errno']) {
                $arrOutput[] = $this->_errorRet(Util_Define::ERROR_INTERNAL_ERROR, $arrData);
                continue;
            }
            // if the chapter doesn't not exist, create a new chapter
            if (Util_Define::ERROR_CHAPTER_NOT_EXIST == $arrChapterStatus['errno']) {
                $arrInput = array(
                    'cartoon_id'        => $chapter['cartoon_id'],
                    'inner_chapter_num' => $chapter['inner_chapter_num'],
                    'chapter_name'      => $chapter['chapter_name'],
                    'zip_url'           => $chapter['zip_url'],
                    'status'            => Util_Define::CARTOON_CHAPTER_INIT,
                );

                $arrRet = Tieba_Service::call('cartoon', 'addCartoonChapter', $arrInput, null, null, 'post', 'php', 'utf-8');

                if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
                    Bingo_Log::warning(sprintf("Call addCartoonChapter failed [input=%s] [output=%s]",
                        serialize($arrInput), serialize($arrRet)));;
                    $arrOutput[] = $this->_errorRet(Util_Define::ERROR_INTERNAL_ERROR, $arrData);
                    continue;
                }
                $arrOutput[] = $this->_errorRet(Util_Define::ERROR_SUCCESS, $arrData);
                continue;
            }
            //if there is a valid chatper on line, cp can not modify
            if (Util_Define::ERROR_CHAPTER_IS_VALID == $arrChapterStatus['errno']) {
                $arrOutput[] = $this->_errorRet(Util_Define::ERROR_CHAPTER_IS_VALID, $arrData);
                continue;
            }
            //if there is a init or invalid chapter, update it
            if (Util_Define::ERROR_CHAPTER_IS_INVALID == $arrChapterStatus['errno']) {
                $arrInput = array(
                    'id'                => $arrChapterStatus['id'],
                    'cartoon_id'        => $chapter['cartoon_id'],
                    'inner_chapter_num' => $chapter['inner_chapter_num'],
                    'chapter_name'      => $chapter['chapter_name'],
                    'zip_url'           => $chapter['zip_url'],
                    'status'            => Util_Define::CARTOON_CHAPTER_INIT,
                );

                $arrRet = Tieba_Service::call('cartoon', 'addCartoonChapter', $arrInput, null, null, 'post', 'php', 'utf-8');

                if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
                    Bingo_Log::warning(sprintf("Call addCartoonChapter failed [input=%s] [output=%s]",
                        serialize($arrInput), serialize($arrRet)));;
                    $arrOutput[] = $this->_errorRet(Util_Define::ERROR_INTERNAL_ERROR, $arrData);
                    continue;
                }
                $arrOutput[] = $this->_errorRet(Util_Define::ERROR_SUCCESS, $arrData);
                continue;
            }
        }
        echo json_encode($arrOutput, JSON_UNESCAPED_UNICODE);
        return true;
    }


    /**
     * @breif  return the information to cp
     * @param  $intErrno
     * @param  $arrData
     * @return array
     */
    private function _errorRet($intErrno, $arrData = null) {
        $arrOutput = array(
            'errno'  => $intErrno,
            'errmsg' => Util_Define::getErrorMsgForChapter($intErrno),
        );
        if ($arrData !== null) {
            $arrOutput['data'] = $arrData;
        }
        return $arrOutput;
    }

    /**
     * @brief  获取作品状态
     * @param  $intCartoonId
     * @return int
     */
    private function _getBookStatus($intCartoonId) {
        $arrInput = array(
            'cartoon_id' => $intCartoonId,
        );

        $arrRet = Tieba_Service::call('cartoon', 'getCartoonBookByCartoonId', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Call getChaptersByCartoonIdAndInnerNum failed [input=%s] [output=%s]",
                serialize($arrInput), serialize($arrRet)));
            return Util_Define::ERROR_INTERNAL_ERROR;
        }
        if (empty($arrRet['data'])) {
            return Util_Define::ERROR_BOOK_NOT_EXIST;
        }
        self::$arrDbBook = $arrRet['data'][0];
        return Util_Define::ERROR_BOOK_EXIST;
    }


    /**
     * @brief  to get the status of the chapter
     * @param  $intCartoonId  cartoon_id
     * @param  $intInnerChapterNum inner chapter number
     * @return array
     */
    private function _getChapterStatus($intCartoonId, $intInnerChapterNum) {
        $arrInput = array(
            'cartoon_id'        => $intCartoonId,
            'inner_chapter_num' => $intInnerChapterNum,
        );
        // get the book status by name
        $arrRet = Tieba_Service::call('cartoon', getChaptersByCartoonIdAndInnerNum, $arrInput, null, null, 'post', 'php', 'utf-8');

        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("Call getChaptersByCartoonIdAndInnerNum failed [input=%s] [output=%s]",
                serialize($arrInput), serialize($arrRet)));
            return array(
                'errno' => Util_Define::ERROR_INTERNAL_ERROR,
            );
        }

        // to check if the book is in our database
        if (empty($arrRet['data'])) {
            return array(
                'errno'=> Util_Define::ERROR_CHAPTER_NOT_EXIST,
            );
        }

        // to check if there are chapters online or offline
        if (Util_Define::CARTOON_CHAPTER_ONLINE == $arrRet['data'][0]['status'] ||
            Util_Define::CARTOON_CHAPTER_OFFLINE ==$arrRet['data'][0]['status'] ||
            Util_Define::CARTOON_CHAPTER_UPLOAD == $arrRet['data'][0]['status']) {
            return array(
                'errno' => Util_Define::ERROR_CHAPTER_IS_VALID,
            );
        }

        // other case
        return array(
            'errno' => Util_Define::ERROR_CHAPTER_IS_INVALID,
            'id' => $arrRet['data'][0]['id'],
        );
    }
}