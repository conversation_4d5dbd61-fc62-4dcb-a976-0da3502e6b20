<?php

class Transcoder {

    private $_arrThreadInfo;
    private $_isNewCae = false;
    private $_arrAuditInfo;

    private $_strMode = 'cae3';

    public function __construct($strMode) {
        $this->_strMode = $strMode;
    }

    public function transcode($arrTask) {
        $intThreadId = $arrTask['thread_id'];
        if ($arrTask['is_newcae'] == 1) {
            $this->_isNewCae = true;
        } else {
            $this->_isNewCae = false;
        }

        if (!$this->_getThreadInfo($intThreadId)) {
            return Util::ERR_TRANSCODE_FAIL;
        }
        if (!$this->_isThreadValid()) {
            return Util::ERR_TRANSCODE_FAIL;
        }
        $intErrno = $this->_reqTranscode();
        Util::warning('transcode result. [mode: '.$this->_strMode.'] [errno: '.$intErrno.']');
        return $intErrno;
    }

    private function _isThreadValid() {
        if (!isset($this->_arrThreadInfo['video_info'])) {
            Util::warning($this->_arrThreadInfo['thread_id'].' has no video info.');
            return false;
        }
        return true;
    }

    private function _getThreadInfo($intThreadId) {
        $arrInput = array(
            'thread_ids' => array($intThreadId),
            'need_abstract' => 0,
            'forum_id' => 0,
            'need_photo_pic' => 0,
            'need_user_data' => 0,
            'icon_size' => 0,
            'need_forum_name' => 0,
            'call_from' => 'client_frs',
        );
        $arrOutput = Util::callService('post', 'mgetThread', $arrInput, 'utf-8');
        if (false === $arrOutput) {
            Util::warning('get thread info failed.');
            return false;
        }
        $this->_arrThreadInfo = $arrOutput['output']['thread_list'][$intThreadId];
        return true;
    }

    private function _getThatDayMonday($thatDayStamp) {
        $MonDayDateStamp = strtotime("Monday");
        $skipWeek = ceil(($MonDayDateStamp - $thatDayStamp) / 86400 / 7);
        $thatMonday = $MonDayDateStamp - $skipWeek * 7 * 86400;
        return date("Ymd", $thatMonday);
    }

    private function _reqTranscode() {
        $intErrno = 0;
        switch($this->_strMode) {
        case 'crf':
            $intErrno = $this->_reqCrfTranscode();
            break;
        case 'cae3':
            $intErrno = $this->_reqCaeTranscode();
            break;
        default:
            $intErrno = Util::ERR_INVALID_MODE;
            Util::warning('invalid mode. [mode: '.$this->_strMode.']');
            break;
        }
        return $intErrno;
    }

    private function _reqCaeTranscode() {
        if (isset($this->_arrThreadInfo['video_info']['video_desc_cae3'])) {
            Util::notice($this->_arrThreadInfo['thread_id'].' has already transcoded for cae3.');

            $arrCaeInfo = $this->_arrThreadInfo['video_info']['video_desc_cae3'][0];

            if (($arrCaeInfo['from'] == 1 && $this->_isNewCae == 1) || ($arrCaeInfo['from'] == 2 && $this->_isNewCae == 0)) {
                // 到这个阶段只有一种情况
                // 当前cae3是由新/老脚本触发的，然后被老/新脚本再次触发
                $arrServiceInput = array(
                    'thread_id'    => $this->_arrThreadInfo['thread_id'],
                    'video_size'   => $arrCaeInfo['video_size'],
                    'video_width'  => $arrCaeInfo['video_width'],
                    'video_height' => $arrCaeInfo['video_height'],
                    'video_id'     => $arrCaeInfo['video_id'],
                    'video_url'    => $arrCaeInfo['video_url'],
                    'from_cae3'    => 1,
                );

                if ($this->_isNewCae == true) {
                    $arrServiceInput['is_newcae'] = 1;
                }

                $arrServiceOutput = Tieba_Service::call('video', 'caeFieldWriteBack', $arrServiceInput, null, null, 'post', 'php', 'utf-8');
                if ($arrServiceOutput === false || $arrServiceOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Util::warning('reqCaeTranscode Tieba_Service::call caeFieldWriteBack failed! failed input: ' . json_encode($arrInput));
                }
            }

            return Util::ERR_ALREADY_TRANSCODED;
        }
        $arrVideoInfo = $this->_arrThreadInfo['video_info'];
        $strUrl = $arrVideoInfo['video_url'];

        $intIndex = 99999;
        foreach ($arrVideoInfo['video_desc'] as $arrDesc) {
            if ($arrDesc['video_id'] < $intIndex) {
                $strUrl = $arrDesc['video_url'];
                $intIndex = $arrDesc['video_id'];
            }
        }
        
        // 解析url，复制文件到bj的bos，然后开始转码等回调
        $arrUrl = explode('/', $strUrl);
        $arrBosInput = array(
            'idc'       =>  'bj',
            'source'    =>  array(
                'bucket'    =>  $arrUrl[3],
                'object'    =>  $arrUrl[4],
            ),
            'target'    =>  array(
                'bucket'    =>  'tieba-smallvideo-transcode-cae-tmp',
                'object'    =>  $arrUrl[4],
            ),
        );
        $arrResult = Lib_Bos::copyObject($arrBosInput);
        if (!$arrResult) {
            Util::warning('copy object failed.');
            return Util::ERR_COPY_OBJECT_FAIL;
        }

        $intResolution = min($arrVideoInfo['video_width'], $arrVideoInfo['video_height']);
        /*
        if ($intResolution >= 1080) {
            $intResolution = 1080;
        } else */
        if ($intResolution >= 720) {
            $intResolution = 720;
        } else if ($intResolution >= 480) {
            $intResolution = 480;
        } else {
            $intResolution = 360;
        }
        $intIsVertical = ($arrVideoInfo['video_width'] > $arrVideoInfo['video_height']) ? 0 : 1;

        $strFileName = substr($arrUrl[4], 0, strlen($arrUrl[4]) - 4) . '_cae3.mp4';
        $arrInput    = array(
            'idc'     => 'bj',
            'pipline' => 'cae3_pipline',
            'source'  => $arrUrl[4],
            'target'  => $strFileName,
            'preset'  => Util::$arrCae3Preset[$intResolution][$intIsVertical],
        );
        $arrResult = Lib_Mct::createSimpleJob($arrInput);

        if (isset($arrResult['jobId'])) {
            $strJobId = $arrResult['jobId'];
            $arrInput = array(
                'key'     => Util::REDIS_KEY_CAE3_JOBID_PREFIX . $strJobId,
                'value'   => json_encode(array(
                    'thread_id' => $this->_arrThreadInfo['thread_id'],
                    'is_newcae' => $this->_isNewCae,
                )),
                'seconds' => 3600,
            );
            $ret = Util::callRedis('SETEX', $arrInput, 3);
        }

        return Util::ERR_SUCCESS;
    }

    private function _reqCrfTranscode() {
        $bolAlreadyTranscode = false;
        $bolNeedTransAgain = false;
        foreach ($this->_arrThreadInfo['video_info']['video_desc_crf'] as $arrCrfDesc) {
            $bolNeedTransAgain = true;
            if (strpos($arrCrfDesc['video_url'], 'tieba-smallvideo-transcode-crf') !== false) {
                $bolAlreadyTranscode = true;
                break;
            }
        }
        if ($bolAlreadyTranscode) {
            Util::notice($this->_arrThreadInfo['thread_id'].' has already transcoded for crf.');
            return Util::ERR_ALREADY_TRANSCODED;
        }
        $arrVideoInfo = $this->_arrThreadInfo['video_info'];
        $strUrl = $arrVideoInfo['video_url'];
        $intIndex = 99999;
        $arrNaniVideo = array(12, 13, 207, 210);
        foreach ($arrVideoInfo['video_desc'] as $arrDesc) {
            if (0 == $arrDesc['video_id'] && in_array($arrVideoInfo['video_type'], $arrNaniVideo)) {
                continue;
            }
            if ($arrDesc['video_id'] < $intIndex) {
                $strUrl = $arrDesc['video_url'];
                $intIndex = $arrDesc['video_id'];
            }
        }

        $arrInput = array(
            'cond' => array(
                'thread_id' => $this->_arrThreadInfo['thread_id'],
                'start_time' => $this->_arrThreadInfo['create_time'] - 86400,
                'end_time' => $this->_arrThreadInfo['create_time'] + 86400
            ),
            'weekkey'   => $this->_getThatDayMonday($this->_arrThreadInfo['create_time']),
        );
        $arrVideoOutput = Util::callService('video', 'getVideoFromAudit', $arrInput);
        if (!$arrVideoOutput) {
            Util::warning($this->_arrThreadInfo['thread_id'].' failed!');
            return Util::ERR_TRANSCODE_FAIL;
        }

        $arrExtData = array(
            'weekkey' => $arrVideoOutput['data'][0]['weekkey'],
            'video_audit_id' => $arrVideoOutput['data'][0]['id'],
            'thread_id' => $this->_arrThreadInfo['thread_id'],
            'preset_id' =>  5,
            'levels' => 1,
            'rename' => 1,
        );
        if ($bolNeedTransAgain) {
            $arrExtData['delogo'][] = array(
                'x' =>  0,
                'y' =>  0,
                'w' =>  1,
                'h' =>  1,
            );
        }
        $arrParams = array(
            'video_url'     => $strUrl,
            'video_preset'  => 5,
            'ext_data'      => json_encode($arrExtData),
        );
        $arrOutput = Tbapi_Core_Midl_Http::httpcall('service_video_transcode', '/VideoService/Videotrans/', $arrParams, 'post');
        if (!$arrOutput || $arrOutput['err_no'] != 0) {
            Util::warning('call service_video_transcode failed. [input: '.serialize($arrParams).' ] [output: '.serialize($arrOutput).' ]');
            return Util::ERR_TRANSCODE_FAIL;
        }

        Util::notice($this->_arrThreadInfo['thread_id'].' done!');
        return Util::ERR_SUCCESS;
    }

}
