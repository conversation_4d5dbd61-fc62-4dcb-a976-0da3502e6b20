<?php
/**
 * Copyright 2019 Baidu Inc. All Rights Reserved.
 */

/**
 * modification history
 * --------------------
 * 2019/7/16, by <PERSON><PERSON><PERSON><PERSON><PERSON>, edited.
 */

/**
 * DESCRIPTION
 * This file taking thread_id from rank and sponsor transcoding tasks.
 */

require_once __DIR__ . '/util.php';
require_once __DIR__ . '/transcoder.php';

class Task {

    private $_strMode = 'cae3';

    public function __construct($strMode) {
        $this->_strMode = $strMode;
    }

    public function run($intRunTime = 600, $intDelay = 10) {
        $objTranscoder = new Transcoder($this->_strMode);
        $objRank = new Rank();
        $intStopTime = time() + $intRunTime;
        while (time() < $intStopTime) {
            $arrTask = $objRank->pop();
            if (0 === $arrTask['thread_id']) {
                Util::warning('tid list is empty or calling rank failed.');
                sleep($intDelay);
                continue;
            }

            $arrRet = $objTranscoder->transcode($arrTask);
            if (Util::ERR_SUCCESS === $arrRet) {
                sleep($intDelay);
            }
        }
    }

}
