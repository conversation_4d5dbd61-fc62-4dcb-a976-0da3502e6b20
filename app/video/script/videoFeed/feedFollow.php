<?php
/**
 * Created by PhpStorm.
 * User: wangyalong02
 * Date: 2018/5/30
 * Time: 下午5:42
 */


require_once "BaseScript.php";

class feedFollow
{

    public static $_arrFollowFeedList = array();

    /**
     * [ getTid description]
     * @param  array  description]
     * @return boolean
     * */
    public static function getFollowFeedList(){

        $arrInput = array(
            'pn' => 1,
            'rn' => 50,
            'status' => 0,
        );

        $arrRet = Tieba_Service::call('video', 'getNaniFollowByStatus', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning('select video::getNaniFollowByStatus fail, input=' . json_encode($arrInput) . ', ret=' . json_encode($arrRet));
            echo 'get Tid failed' . "\n";
            return false;
        }

        self::$_arrFollowFeedList = $arrRet['data'];
        return true;
    }

    /**
     * 执行关注,
     * @param $arrInput
     * @return array | boolean
     */
    public static function processFollow($arrInput)
    {

        $intPassId = $arrInput['article_uid'];
        $arrDlInput = array(
            'pass_id' => array($intPassId),
        );
        $arrOutput = Tieba_Service::call('video', 'getPassNani', $arrDlInput, null, null, 'post', 'php', 'utf-8');
        if (!$arrOutput || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call service video:getPassNani failed, input:" . serialize($arrDlInput) . "output: " . serialize($arrOutput));
            echo 'follow failed1' . "\n";
            return false;
        }
        if(empty($arrOutput['data']['list'])){
            echo "not pass_id $intPassId";
            return false;
        }
        $intNaniUid = $arrOutput['data']['list']['0']['nani_id'];

        $arrServiceInput = array(
            'user_id' => $arrInput['uid'],
            'followed_user_ids' => array(
                $intNaniUid,
            ),
            'need_notice' => 0, //消息提醒
            'in_live'     => 0,
        );
        $arrOutput = Tieba_Service::call('user', 'setUserFollower', $arrServiceInput, null, null, 'post', 'php', 'utf-8');
        if (!$arrOutput || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call service user:setUserFollower failed, input:" . serialize($arrServiceInput) . "output: " . serialize($arrOutput));
            echo 'follow failed1' . "\n";
            return false;
        }else{

            $arrInputPush = array(
                'user_id' => $intNaniUid,
                'op_uid'  => $arrInput['uid'],
            );

            self::pushNaniFollowMsg($arrInputPush);

        }
        return true;
    }

    /**
     * execute
     * @param
     * @return boolean
     */

    public static function execute()
    {

        if (!self::getFollowFeedList()) {
            echo "get Tid failed";
            return false;
        }

        if (empty(self::$_arrFollowFeedList)){
            echo "feedList is empty\n";
            return true;
        }

        foreach (self::$_arrFollowFeedList as $item) {

            $arrRes = self::processFollow($item);
            $arrEndInput = array(
                'status' => 1,
                'id'      => $item['id'],
            );

            if(false === $arrRes){
                $arrEndInput['status'] = 2; // 关注失败
            }

            $arrRet = Tieba_Service::call('video', 'updateNaniFollowByStatus', $arrEndInput, null, null, 'post', 'php', 'utf-8');

            if(false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
                echo "call video::updateNaniFollowByStatus fail!!!  ";
                Bingo_Log::warning('call video::updateNaniFollowByStatus input = '.serialize($arrEndInput).' output = '.serialize($arrRet));
            }
        }
        echo "exec success\n";
    }
    /**
     * @param $arrInput
     * @return bool
     */
    private static  function pushNaniFollowMsg($arrInput) {
        $intUserId = $arrInput['user_id'];
        $intOpUid  = $arrInput['op_uid'];
        if ($intUserId <= 0 || $intOpUid <= 0) {
            Bingo_Log::warning("uid error!");

            return false;
        }

        $arrPushInput = array(
            'user_id'         => $intUserId,
            'op_uid'          => $intOpUid,
            'msg_type'        => 3,
            // 粉丝消息
            'op_time'         => time(),
            'msg_content'     => "关注了你",
          //  'close_nani_push' => 1,
        );

        $arrPushOut = Tieba_Service::call('video', 'pushHudongMsg', $arrPushInput, null, null, 'post', 'php', 'utf-8');

        if (false == $arrPushOut || $arrPushOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call video:pushHudongMsg false. input[' . serialize($arrPushInput) . '] output[' . serialize($arrPushOut) . ']');

            return false;
        }

        return true;
    }
}
feedFollow::execute();
