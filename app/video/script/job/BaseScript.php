<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Base.php
 * <AUTHOR>
 * @date 2017/04/23 16:00
 * @brief 
 *  
 **/
ini_set ( "memory_limit", "2G" );
define ( 'ROOT_PATH', dirname ( __FILE__ ) . "/../../../../" );
define ( 'HOME_PHP_PATH', realpath ( ROOT_PATH . '/php/bin/php' ) );
define ( 'IS_ORP_RUNTIME', true );

Tieba_Init::init ( MODULE_NAME );

Bingo_Log::init(array(
    LOG => array(
        'file'  => dirname ( __FILE__ ) . '/../../../../log/app/video/'.NAME_SCRIPT.'.log',
        'level' => 0x04 | 0x02,
    ),
), LOG);
//echo dirname ( __FILE__ ) . '/../../../../log/app/video/'.NAME_SCRIPT.'.log';
if (! defined ( 'REQUEST_ID' )) {
    define ( 'REQUEST_ID', Bingo_Log::getLogId () );
}


if (function_exists ( 'camel_set_logid' )) {
    camel_set_logid ( REQUEST_ID );
}


class BaseScript{
    const DATABASE_NAME = "forum_video_audit";
    const RETRY_TIMES = 3;
    private static $_arrConf = null;
    private static $_db = null;
    private static $_charset = "utf8";
    protected $_intStep = 200;
    protected $_idfield = 'id';
    protected $_intExpireTime = 3600;

   
    /**
     * [_queryDBHard description]
     * @param  [type] $strSql [description]
     * @return [type]         [description]
     */
    protected static function _queryDBHard($strSql){
        $intSleep = 1;
        $ret = self::_queryDB($strSql);
        while(false === $ret && $intSleep < self::RETRY_TIMES){
            sleep($intSleep);
            $ret = self::_queryDB($strSql);
            //var_dump($ret);var_dump($strSql);
            $intSleep += 1;
        }
        if($intSleep >= self::RETRY_TIMES){
            Bingo_Log::warning('sql failed after trying 3 times:'.$strSql);
            echo 'sql failed after trying 3 times:' . $strSql;
            return false;
        }
        return $ret;
    }
    
    /**
     * [_queryDB description]
     * @param  [type] $strSql [description]
     * @return [type]         [description]
     */
    protected static function _queryDB($strSql){
        $ret = self::_getDB();
        if(!$ret){
            Bingo_Log::warning('db connect failed!');
            echo "db connect failed";
            //self::$_arrRecord = array();
            return false;
        }

        Bingo_Timer::start(__FUNCTION__);
        $ret = self::$_db -> query($strSql);
        //var_dump($ret);
        Bingo_Timer::end(__FUNCTION__);

        if(false === $ret){
            Bingo_Log::warning('db query failed:'.$strSql);
            echo 'db query failed:'.$strSql;
            return false;
        }
        return $ret;
    }
    /**
     * [_getConf description]
     * @param  [type] $strConfName [description]
     * @return [type]              [description]
     */
    protected static function _getConf($strConfName){
        if (self::$_arrConf) {
            return self::$_arrConf;
        }

        self::$_arrConf = Bd_Conf::getConf("app/managerapply/$strConfName");
        if (false === self::$_arrConf) {
            Bingo_Log::warning("getConfig file error!");
            return null;
        }

        return self::$_arrConf;
    }
    /**
     * [_getDB description]
     * @param
     * @return [type] [description]
     */
    protected static function _getDB(){
        if(self::$_db) {
            return self::$_db;
        }
        self::$_db = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if(self::$_db == null || !self::$_db->isConnected()) {
            Bingo_Log::warning('db connect fail.');
            return null;
        }
        return self::$_db;
        /*if (self::$_db) {
            return self::$_db;
        }
        self::$_db = new Bd_DB ();
        if (self::$_db == null) {
            Bingo_Log::warning(" get db failed : new bd_db fail.");
            return null;
        }
        Bingo_Timer::start('dbinit');
        $r = self::$_db->ralConnect(self::DB_RAL_SERVICE_NAME);
        Bingo_Timer::end('dbinit');
        if (!$r) {
            Bingo_Log::warning(" get db failed : bd db ral connect fail.");
            self::$_db = null;
            return null;
        }
        self::$_db->charset(self::$_charset);
        return self::$_db;*/
    }
    /**
     * [_getFileLines description]
     * @param  [type] $filename [description]
     * @return [type]           [description]
     */
    protected static function _getFileLines($filename){
        $strCmd = "wc -l $filename";
        $out = '';
        exec($strCmd, $out);
        $arr = explode(" ", $out[0]);
        return $arr[0];
    }
    /**
     * [_errRet description]
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    protected static function _errRet($errno){
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }


    /**
     * [_succRet description]
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    protected static function _succRet($data){
        return array(
            'errno' => Tieba_Errcode::ERR_SUCCESS,
            'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
            'data' => $data,
        );
    }

}


/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
