<?php
//这个脚本用来跑编辑后台的编辑统计

class EditLogStat{

    const PAGE_SIZE = 10000;

    private static $_arrTemplateData = array(
        'edit_total_num' => 0,                    //编辑总量
        'first_edit_total_num' => 0,              //初编编辑总量
        'first_edit_reject_num' => 0,             //初编拒绝总量
        'first_edit_tb_num' => 0,                 //初编贴吧通过量
        'first_edit_tb_per' => 0,                 //初编贴吧通过率
        'first_edit_bjh_num' => 0,                //初编手百通过量
        'first_edit_bjh_per' => 0,                //初编手百通过率
        'first_edit_total_succ_per' => 0,         //初编总通过率
        'sec_edit_total_num' => 0,                //复编总量
        'sec_edit_reject_num' => 0,               //复编拒绝量
        'sec_edit_tb_num' => 0,                   //复编贴吧通过量
        'sec_edit_tb_per' => 0,                   //复编贴吧通过率
        'sec_edit_bjh_num' => 0,                  //复编手百通过量
        'sec_edit_bjh_per' => 0,                  //复编手百通过率
        'sec_edit_total_succ_per' => 0,           //复编总通过率

        'edited_total_num' => 0,                  //被复编总量
        'edited_reject_num' => 0,                 //拒绝复编准确量:复编结果与初编结果均为拒绝
        'edited_tb_num' => 0,                     //贴吧通过被复编准确量:复编结果与初编结果一致为通过贴吧的量
        'edited_bjh_num' => 0,                    //手百通过被复编准确量:复编结果与初编结果一致为通过手百的量
        'edited_right_num' => 0,                  //被复编准确量:复编提交和初编提交后，编辑状态及外部分发类型保持一致的量
        'edited_right_per' => 0,                  //被复编准确率
        'edited_total_right_num' => 0,            //总被复编准确量:统计时需要去掉质量标注、分类、打分、结果不准确的内容
        'edited_total_right_per' => 0,            //总被复编准确率
    );

    private static $_arrQualityInfo = array(
        'video_quality',
        'other_quality',
        'cover_quality',
        'title_quality',
        'fir_category',
        'video_score',
    );

    //查询的时间点
    private $_intStartTime;
    private $_intEndTime;
    private $_intUserId;
    private $_arrOriData;
    private $_arrHandledData;

    private $_arrThreadData;

    //是否需要被复编信息
    private $_intNeedEdited;
    /**
     * [_queryDBHard description]
     * @param  [type] $strSql [description]
     * @return [type]         [description]
     */
    private static function echo_memory_usage(){
        $arrTrace =debug_backtrace();
        $strFile = $arrTrace[1]['file'];
        $strRow = $arrTrace[1]['line'];
        $strMemory = (!function_exists('memory_get_usage')) ? '0' : round(memory_get_usage() / 1024/ 1024, 2) . 'MB';
        echo "file:{$strFile} row: {$strRow} current memory : $strMemory", PHP_EOL;
        return ;
    }
    /**
     * [_queryDBHard description]
     * @param  [type] $strSql [description]
     * @return [type]         [description]
     */
    public function execute(){
        if(!self::_init()){
            echo "init fail. input:[" . serialize($arrInput) . "]", PHP_EOL;
            return false;
        }
        $this->_getInput();
        self::echo_memory_usage();
        $this->_handleData();
        self::echo_memory_usage();
        $this->_buildOutput();
        self::echo_memory_usage();

        return true;
    }
    /**
     * [_queryDBHard description]
     * @param  [type] $strSql [description]
     * @return [type]         [description]
     */
    private function _init(){

        $this->_intUserId = intval($arrInput['user_id']);

        $this->_intNeedEdited = isset($arrInput['need_edited']) ? $arrInput['need_edited'] : 0;
        $this->_arrOriData = array();
        return true;
    }
    /**
     * [_queryDBHard description]
     * @param  [type] $strSql [description]
     * @return [type]         [description]
     */
    private function _buildOutput(){
        //Bingo_Log::warning(print_r($this->_arrOriData, 1));
        //Bingo_Log::warning(print_r($this->_arrThreadData, 1));
        //Bingo_Log::warning(print_r($this->_arrHandledData, 1));
        echo "op_uid\t";
        foreach(self::$_arrTemplateData as $key => $value){
            echo "$key\t";
        }
        foreach($this->_arrHandledData as $key => $value){
            echo PHP_EOL;
            echo "$key\t";
            foreach($value as $k => $v){
                echo "$v\t";
            }
        }
    }
    //这里用来计算乱七八糟数据
    /**
     * [_queryDBHard description]
     * @param  [type] $strSql [description]
     * @return [type]         [description]
     */
    private function _handleData(){
        $this->_arrThreadData = array();
        if(count($this->_arrOriData) == 0){
            $this->_arrHandledData = array();
            return;
        }
        foreach($this->_arrOriData as $key => $value){
            $intThreadId = $value['thread_id'];
            if(!isset($this->_arrThreadData[$intThreadId])){
                $this->_arrThreadData[$intThreadId] = array();
            }

            $this->_arrHandledData[$value['op_uid']] = self::$_arrTemplateData;
            if($value['ori_op_uid'] != 0){
                $this->_arrHandledData[$value['ori_op_uid']] = self::$_arrTemplateData;
            }
            $arrThread = &$this->_arrThreadData[$intThreadId];
            //初编 bol_cal_first是用来检测被复编情况的
            if($value['edit_status'] == 4){
                $arrThread['bol_cal_first'] = 1;
                $arrThread['f_user_id'] = $value['op_uid'];
                $arrThread['f_dispatch_status'] = $value['dispatch_status'];
                $arrThread['f_edit_result'] = $value['edit_result'];
                $arrThread['f_quality_info'] = $value['quality_info'];
            }else if($value['edit_status'] == 5 && ($value['op_uid'] != $value['ori_op_uid'])){
                $arrThread['bol_cal_first'] = intval($arrThread['f_user_id']) > 0 ? 1 : 0;
                $arrThread['f_user_id'] = $value['ori_op_uid'];
                $arrThread['f_dispatch_status'] = isset($arrThread['f_dispatch_status']) ? $arrThread['f_dispatch_status'] : $value['ori_dispatch_status'];
                $arrThread['f_edit_result'] = isset($arrThread['f_edit_result']) ? $arrThread['f_edit_result'] : $value['ori_edit_result'];
                $arrThread['f_quality_info'] = isset($arrThread['f_quality_info']) ? $arrThread['f_quality_info'] : $value['ori_quality_info'];
                $arrThread['s_user_id'] = $value['op_uid'];
                $arrThread['s_edit_result'] = $value['edit_result'];
                $arrThread['s_dispatch_status'] = $value['dispatch_status'];
                $arrThread['s_quality_info'] = $value['quality_info'];
            }else if($value['edit_status'] == 5 && ($value['op_uid'] == $value['ori_op_uid'])){
                $arrThread['bol_cal_first'] = intval($arrThread['f_user_id']) > 0 ? 1 : 0;
                $arrThread['f_user_id'] = $value['op_uid'];
                $arrThread['f_dispatch_status'] = $value['dispatch_status'];
                $arrThread['f_edit_result'] = $value['edit_result'];
                $arrThread['f_quality_info'] = $value['quality_info'];
                $arrThread['s_user_id'] = 0;
                $arrThread['s_edit_result'] = 0;
                $arrThread['s_dispatch_status'] = 0;
                $arrThread['s_quality_info'] = array();
            }
        }

        self::echo_memory_usage();
        // Bingo_Log::warning("thread count: " . count($this->_arrThreadData));
        $this->_arrOriData = array();
        self::echo_memory_usage();

        //计算初编复编等各种量
        foreach($this->_arrThreadData as $key => $value){
            //计算初编的量
            if(intval($value['f_user_id']) != 0 && $value['bol_cal_first'] == 1){
                $arrUser = &$this->_arrHandledData[$value['f_user_id']];
                $arrUser['edit_total_num']++;
                $arrUser['first_edit_total_num']++;
                if($value['f_edit_result'] == 1 && $value['f_dispatch_status'] == 1){         //初编贴吧通过
                    $arrUser['first_edit_tb_num']++;
                }else if($value['f_edit_result'] == 1 && $value['f_dispatch_status'] == 2){   //初编百家号通过
                    $arrUser['first_edit_bjh_num']++;
                }else if($value['f_edit_result'] == 2){      //初编拒绝
                    $arrUser['first_edit_reject_num']++;
                }else{       //不知道什么鬼情况

                }
            }
            //计算复编的量
            if(intval($value['s_user_id']) > 0){
                $arrUser = &$this->_arrHandledData[$value['s_user_id']];
                $arrUser['edit_total_num']++;
                $arrUser['sec_edit_total_num']++;

                if($value['s_edit_result'] == 1 && $value['s_dispatch_status'] == 1){         //复编贴吧通过
                    $arrUser['sec_edit_tb_num']++;
                }else if($value['s_edit_result'] == 1 && $value['s_dispatch_status'] == 2){   //复编百家号通过
                    $arrUser['sec_edit_bjh_num']++;
                }else if($value['s_edit_result'] == 2){      //复编拒绝
                    $arrUser['sec_edit_reject_num']++;
                }else{       //不知道什么鬼情况

                }

            }
            //计算被复编的量
            if(intval($value['f_user_id']) > 0 && intval($value['s_user_id']) > 0){
                $bolQualityFlag = 0;
                $arrUser = &$this->_arrHandledData[$value['f_user_id']];
                $arrUser['edited_total_num']++;
                if(self::_checkQualityInfo($value['f_quality_info'], $value['s_quality_info'])){
                    $bolQualityFlag = 1;
                }              
                if($value['f_edit_result'] == 1 && $value['s_edit_result'] == 1){
                    if($value['f_dispatch_status'] == 1 && $value['s_dispatch_status'] == 1){     //被复编通过贴吧量
                        $arrUser['edited_tb_num']++;
                        $arrUser['edited_right_num']++;
                        if($bolQualityFlag){
                            $arrUser['edited_total_right_num']++;
                        }
                    }else if($value['f_dispatch_status'] == 2 && $value['s_dispatch_status'] == 2){   //被复编通过百家号量
                        $arrUser['edited_bjh_num']++;
                        $arrUser['edited_right_num']++;
                        if($bolQualityFlag){
                            $arrUser['edited_total_right_num']++;
                        }
                    }else{    //通过的方式不一致

                    }
                }else if($value['f_edit_result'] == $value['s_edit_result'] = 2){     //复编拒绝量
                    if($bolQualityFlag){
                        $arrUser['edited_total_right_num']++;
                    }
                    $arrUser['edited_reject_num']++;
                    $arrUser['edited_right_num']++;
                }else{   //初编与复编不一致

                }
            }
        }

        self::echo_memory_usage();
        $this->_arrThreadData = array();
        self::echo_memory_usage();

        //计算初编复编等各种率
        foreach($this->_arrHandledData as $key => &$value){
            $value['first_edit_tb_per'] = sprintf("%.2f", (($value['first_edit_tb_num'] / $value['first_edit_total_num']) * 100));
            $value['first_edit_bjh_per'] = sprintf("%.2f", (($value['first_edit_bjh_num'] / $value['first_edit_total_num']) * 100));
            $value['first_edit_total_succ_per'] = sprintf("%.2f", (($value['first_edit_bjh_num'] +$value['first_edit_tb_num']) / $value['first_edit_total_num']) * 100);

            $value['sec_edit_tb_per'] = sprintf("%.2f", (($value['sec_edit_tb_num'] / $value['sec_edit_total_num']) * 100));
            $value['sec_edit_bjh_per'] = sprintf("%.2f", (($value['sec_edit_bjh_num'] / $value['sec_edit_total_num']) * 100));
            $value['sec_edit_total_succ_per'] = sprintf("%.2f", (($value['sec_edit_bjh_num']+$value['sec_edit_tb_num']) / $value['sec_edit_total_num']) * 100);
            $value['edited_right_per'] = sprintf("%.2f", ($value['edited_right_num'] / $value['edited_total_num'] * 100));

            $value['edited_total_right_per'] = sprintf("%.2f", ($value['edited_total_right_num'] / $value['edited_total_num'] * 100));
        }
    }
    /**
     * [_checkQualityInfo description]
     * @param  [type] $strSql [description]
     * @return [type]         [description]
     */
    private function _checkQualityInfo($strOri, $strFinal){
        if($strOri == $strFinal){
            return true;
        }
        if($strOri == '' || $strFinal == ''){
            return false;
        }
        $arrOri = unserialize(stripslashes($strOri));
        $arrFinal = unserialize(stripslashes($strFinal));
        foreach(self::$_arrQualityInfo as $value){
            $ori = $arrOri[$value];
            $final = $arrFinal[$value];
            //评分某一个为1 且不相等
            if($ori != $final){
                if($value == 'video_score'){
                    if($ori == 1 || $final == 1){
                        return false;
                    }
                }
                //一级目录不同
                else if($value == 'fir_category'){
                    return false;
                }
                else if($value == 'video_quality'){
                    if($ori == '低俗' || $ori == '内容不适' || $final == '低俗' || $final == '内容不适'){
                        return false;
                    }
                }
                else if($value == 'title_quality'){
                    if($ori == '标题低质' || $final == '标题低质'){
                        return false;
                    }
                }
            } 
        }
        return true;
    }
    /**
     * 这个函数用来从库中获取所有的edit_log日志
     * @param  [type] $strSql [description]
     * @return [type]         [description]
     */
    private function _getInput(){
        $this->_getAllUserInputFromFile();
        return ;
    }

    /**
     * 这个函数用来从库中获取所有的edit_log日志
     * @param  [type] $strSql [description]
     * @return [type]         [description]
     */
    private function _getAllUserInputFromFile(){
        $objFile = 'edit.txt';
        if(file_exists($objFile)){
            $strOriData = file_get_contents($objFile);
        }

        $arrOriData = explode(PHP_EOL, $strOriData);
        array_pop($arrOriData);
        self::echo_memory_usage();
        foreach($arrOriData as $value){
            $arrOriValue = explode("\t",$value);
            $arrKeyValue = array(
                'thread_id' => isset($arrOriValue[0])? $arrOriValue[0] : '',
                'op_uid'    => isset($arrOriValue[1])? $arrOriValue[1] : '',
                'edit_status' => isset($arrOriValue[2])? $arrOriValue[2] : '',
                'edit_result' => isset($arrOriValue[3])? $arrOriValue[3] : '',
                'dispatch_status' => isset($arrOriValue[4])? $arrOriValue[4] : '',
                'quality_info' => isset($arrOriValue[5])? $arrOriValue[5] : '',
                'ori_op_uid' => isset($arrOriValue[6])? $arrOriValue[6] : '',
                'ori_dispatch_status' => isset($arrOriValue[7])? $arrOriValue[7] : '',
                'ori_edit_result' => isset($arrOriValue[8])? $arrOriValue[8] : '',
                'ori_quality_info' => isset($arrOriValue[9])? $arrOriValue[9] : '',
            );
            $this->_arrOriData[] = $arrKeyValue;
        }
        return true;

    }
}


$objDatas = new EditLogStat();
$objDatas->execute();
echo 'memory usage: ' . memory_get_usage() . ' bytes', PHP_EOL;
echo 'memory peak usage: ' . memory_get_peak_usage() . ' bytes', PHP_EOL;

