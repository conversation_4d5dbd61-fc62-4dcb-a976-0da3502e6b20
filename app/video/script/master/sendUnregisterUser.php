<?php
/**
 * Created by PhpStorm.
 * User: liu<PERSON>i
 * Date: 18/8/29
 * Time: 上午11:35
 * @brief  师徒系统 H5徒弟的流失召回
 */

ini_set ( "memory_limit", "2G"  );
define ( 'ROOT_PATH', dirname ( __FILE__  ) . "/../../.."  );
define ( 'HOME_PHP_PATH', realpath ( ROOT_PATH . '/php/bin/php'  )  );
define ( 'IS_ORP_RUNTIME', true  );
define ( 'MODULE_NAME','video');
define('NAME_SCRIPT', 'master_suser');

Tieba_Init::init ( MODULE_NAME );

Bingo_Log::init(array(
    LOG => array(
        'file'  => dirname ( __FILE__ ) . '/../../../log/video/'.NAME_SCRIPT.'.log',
        'level' => 0x04 | 0x02,
    ),
), LOG);

if (! defined ( 'REQUEST_ID'  )) {
    define ( 'REQUEST_ID', Bingo_Log::getLogId ()  );
}

if (function_exists ( 'camel_set_logid'  )) {
    camel_set_logid ( REQUEST_ID  );
}



class Script_SendUnregisterUser{

    const DB_RAL_SERVICE_NAME = "forum_movideo";


    private static $strMsg = '【伙拍小视频】你的账户已经成功入账%s元,超时金额会过期的哦!点击链接马上下载伙拍小视频登录提现:http://dwz.cn/wa4YvITX';
    /**
     * @param array
     * @return  true false
     */
    public function process(){
        $intEndTime =  strtotime(date('Y-m-d', time())) + 20*60*60;
        //当天晚上8点执行脚本，查昨天晚8点 - 今天晚8点的符合条件的人
        $intStartTime =  strtotime(date('Y-m-d', time())) - 4*60*60;

        $arrServiceInput = array(
            'start_time' => $intStartTime,
            'end_time'   =>  $intEndTime,
        );
        $arrServiceOutput = Tieba_Service::call('video', 'getUnregisterRepacketUserCnt', $arrServiceInput, null, null, 'post', 'php', 'utf-8');
        if($arrServiceOutput === false || $arrServiceOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            $strLog = sprintf("call video:getUnregisterRepacketUserCnt fail. input:[%s], output:[%s]", json_encode($arrServiceInput), json_encode($arrServiceOutput));
            Bingo_Log::warning($strLog);
            echo $strLog;
            return false;
        }

        $intTotal = (int)$arrServiceOutput['data']['cnt'];
        $intPn = 0;
        $intRn = 50;
        $arrAllUnregisterUser = array();
        while($intPn < $intTotal){
            $arrServiceInput = array(
                'pn'  => $intPn,
                'rn'  => $intRn,
                'start_time' => $intStartTime,
                'end_time'   =>  $intEndTime,
            );
            $arrServiceOutput = Tieba_Service::call('video', 'getUnregisterRepacketUser', $arrServiceInput, null, null, 'post', 'php', 'utf-8');
            if($arrServiceOutput === false || $arrServiceOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                $strLog = sprintf("call video:getUnregisterRepacketUser fail. input:[%s], output:[%s]", json_encode($arrServiceInput), json_encode($arrServiceOutput));
                Bingo_Log::warning($strLog);
                echo $strLog;
            }
            $arrAllUnregisterUser = array_merge($arrAllUnregisterUser , $arrServiceOutput['data']);
            $intPn += $intRn;
            usleep(300000);
        }

        foreach($arrAllUnregisterUser as $arrOneMsg){

            $intInviteMoney = (int)$arrOneMsg['money']/100;//分转化为元
            $intPhoneNum   = (int)$arrOneMsg['mobile'];
            $strCompleteMsg = sprintf(self::$strMsg , strval($intInviteMoney));
            $bolSmsRet = Sms_Msg::sendMsg(array($intPhoneNum) , $strCompleteMsg);
            if(!$bolSmsRet){
                $strLog = "call service SmsRet::sendMsg error! input=$intPhoneNum";
                Bingo_Log::warning($strLog);
                echo $strLog;
            }
            usleep(300000);
        }

        return true;
    }

}

$obj = new Script_SendUnregisterUser();
$ret = $obj->process();
if($ret){
    exit(0);
}
exit(1);
?>

