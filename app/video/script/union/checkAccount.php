<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2018/6/21
 * Time: 10:12
 */
define('NAME_SCRIPT', 'checkAccount');
require_once '../BaseScript.php';

Bingo_Timer::start('total');
$intCode = checkAccount::run();
Bingo_Timer::end('total');
echo "done! cost: " . Bingo_Timer::calculate('total');
exit($intCode);

/**
 * 功能说明：
 * 用于实时对账（本月）和历史对账（上月以及以前没有确认的账单）
 * 实时对账检查会员和公会账单的一致性，存在不一致的账单则告警
 * 历史对账刷新会员和公会的账单使其一致，期间有操作失败则告警
 * 参数说明：
 * type: 1-实时对账，2-历史对账
 * union_pn: 最多多少页Union列表, 默认1000（一页1000个）
 * member_pn: 最多多少页member列表, 默认1000（一页1000个）
 * try_times: call服务最大尝试次数，默认3次
 */
class checkAccount {

    const PAGE_SIZE = 1000;

    protected static $_intType = 0;
    protected static $_intTaskStartTime = 0;

    protected static $_intUnionPageNum = 1000;
    protected static $_intMemberPageNum = 1000;

    protected static $_intUnionCheckCount = 0;
    protected static $_intUnionSuccessCount = 0;
    protected static $_arrMisMatchMemberAccounts = [];
    protected static $_arrMisMatchUnionAccounts = [];

    protected static $_intServiceTryTimes = 3;

    public static function run()
    {
        if (!self::_init()) {
            return -1;
        }

        for ($i = 1; $i <= self::$_intUnionPageNum; ++$i) {
            if (!self::_getUnionList($i, $arrUnionList)) {
                Bingo_Log::warning("get union list fail, page: $i");
                continue;
            }

            foreach ($arrUnionList as $arrUnion) {
                if (!self::_getUnionAccounts($arrUnion['union_id'], $arrUnionAccounts)) {
                    Bingo_Log::warning("get union accounts fail, union_id: {$arrUnion['union_id']}, type: " . self::$_intType);
                    continue;
                }

                foreach ($arrUnionAccounts as $arrUnionAccount) {
                    if ($arrUnionAccount['check_version'] == Lib_Define_Union::GRADE_VERSION) {
                        continue;
                    }
                    ++ self::$_intUnionCheckCount;

                    if (!self::_lockUnionAccount($arrUnionAccount['union_id'], $arrUnionAccount['cmonth'], $intLocked)) {
                        Bingo_Log::warning("lock union account fail, union_id: {$arrUnionAccount['union_id']}"
                            . ", month: {$arrUnionAccount['cmonth']}, type: " . self::$_intType);
                        continue;
                    }
                    if (!$intLocked) {
                        Bingo_Log::warning("unlocked, bypass union account check, union_id: {$arrUnionAccount['union_id']}"
                            . ", month: {$arrUnionAccount['cmonth']}");
                        ++ self::$_intUnionSuccessCount;
                        continue;
                    }

                    $bolCheckFail = false;
                    for ($j = 1; $j <= self::$_intMemberPageNum; ++$j) {
                        if (!self::_getMemberAccountList($arrUnionAccount['union_id'], $arrUnionAccount['cmonth'],
                            $j, $arrMemberAccountList))
                        {
                            Bingo_Log::warning("get member account list fail, union_id: {$arrUnionAccount['union_id']}"
                                . ", month: {$arrUnionAccount['cmonth']}, page: $j");
                            $bolCheckFail = true;
                            continue;
                        }

                        foreach ($arrMemberAccountList as $arrMemberAccount) {
                            if ($arrMemberAccount['grade_version'] == Lib_Define_Union::GRADE_VERSION
                                && $arrMemberAccount['status'] == Lib_Define_Union::UNION_MEMBER_ACCOUNT_STATUS_CHECKED) {
                                continue;
                            }

                            if (!self::_checkMemberAccount($arrMemberAccount, $arrCheckResult)) {
                                Bingo_Log::warning("check member account fail, member_id: {$arrMemberAccount['member_id']}"
                                    . ", month: {$arrMemberAccount['cmonth']}");
                                $bolCheckFail = true;
                            }
                            if (self::$_intType == 1) {
                                $arrCalcAccount = $arrCheckResult['calc_account'];
                                $arrRecAccount = $arrCheckResult['rec_account'];
                                if ($arrCalcAccount['grade_version'] == $arrRecAccount['grade_version']
                                    && !self::_compareMemberAccount($arrCalcAccount, $arrRecAccount)) {
                                    self::$_arrMisMatchMemberAccounts[] = $arrMemberAccount;
                                }
                            }
                            usleep(10000);
                        }

                        if (count($arrMemberAccountList) < self::PAGE_SIZE) {
                            break;
                        }
                    }
                    if ($bolCheckFail) {
                        Bingo_Log::warning("member account check fail before union account check, type: " . self::$_intType
                            ."union_id: {$arrUnionAccount['union_id']}, month: {$arrUnionAccount['cmonth']}");
                        continue;
                    }

                    if (!self::_checkUnionAccount($arrUnionAccount, $arrCheckResult)) {
                        Bingo_Log::warning("union account check fail, type: " . self::$_intType
                            ."union_id: {$arrUnionAccount['union_id']}, month: {$arrUnionAccount['cmonth']}");
                        continue;
                    }
                    if (self::$_intType == 1) {
                        $arrCalcAccount = $arrCheckResult['calc_account'];
                        $arrRecAccount = $arrCheckResult['rec_account'];
                        if (!self::_compareUnionAccount($arrCalcAccount, $arrRecAccount)) {
                            self::$_arrMisMatchUnionAccounts[] = $arrUnionAccount;
                        }
                    } else if (!$arrCheckResult['updated']) {
                        Bingo_Log::warning("member account has changed during check, "
                            ."union_id: {$arrUnionAccount['union_id']}, month: {$arrUnionAccount['cmonth']}");
                    }
                    ++ self::$_intUnionSuccessCount;
                }
            }

            if (count($arrUnionList) < self::PAGE_SIZE) {
                break;
            }
        }

        echo 'union_check_count: ',  self::$_intUnionCheckCount, 'union_succ_count: ',  self::$_intUnionSuccessCount, PHP_EOL;
        Bingo_Log::notice('union_check_count: '.self::$_intUnionCheckCount.'union_succ_count: '.self::$_intUnionSuccessCount);
        if (self::$_intType == 1) {
            $arrMismatchUnionIds = array_unique(array_merge(array_column(self::$_arrMisMatchMemberAccounts, 'union_id'),
                array_column(self::$_arrMisMatchUnionAccounts, 'union_id')));
            echo 'member_account_mismatch: ',  count(self::$_arrMisMatchMemberAccounts),
                'union_account_mismatch: ',  count(self::$_arrMisMatchUnionAccounts),
                'mismatch_union_count: ', count($arrMismatchUnionIds), PHP_EOL;
            Bingo_Log::warning("mismatch found during account check, union_ids: " . implode(', ', $arrMismatchUnionIds));
            return !empty($arrMismatchUnionIds) ? -1 : 0;
        }
        return self::$_intUnionSuccessCount < self::$_intUnionCheckCount ? -1 : 0;
    }

    /**
     * @param $arrCalcMemberAccount
     * @param $arrRecMemberAccount
     * @return bool
     */
    protected static function _compareMemberAccount($arrCalcMemberAccount, $arrRecMemberAccount)
    {
        unset($arrCalcMemberAccount['status']);
        unset($arrRecMemberAccount['status']);
        return $arrCalcMemberAccount == $arrRecMemberAccount;
    }

    /**
     * @param $arrCalcUnionAccount
     * @param $arrRecUnionAccount
     * @return bool
     */
    protected static function _compareUnionAccount($arrCalcUnionAccount, $arrRecUnionAccount)
    {
        return $arrCalcUnionAccount['balance_cny'] == $arrRecUnionAccount['balance_cny'];
    }

    /**
     * @param $intUnionId
     * @param $intMonth
     * @param $intLocked
     * @return bool
     */
    protected static function _lockUnionAccount($intUnionId, $intMonth, &$intLocked)
    {
        if (self::$_intType == 1) {
            $intLocked = 1;
            return true;
        }

        $arrParams = [
            'union_id' => $intUnionId,
            'cmonth' => $intMonth,
        ];
        if (!($arrRet = self::_callService('video', 'lockUnionAccountWeakly', $arrParams))) {
            return false;
        }
        $intLocked = $arrRet['data']['locked'];
        return true;
    }

    /**
     * @param $arrMemberAccount
     * @param $arrCheckResult
     * @return bool
     */
    protected static function _checkMemberAccount($arrMemberAccount, &$arrCheckResult)
    {
        $arrParams = [
            'member_id' => $arrMemberAccount['member_id'],
            'cmonth' => $arrMemberAccount['cmonth'],
            'update' => self::$_intType == 1 ? 0 : 1,
        ];
        if (!($arrRet = self::_callService('video', 'checkMemberAccount', $arrParams))) {
            return false;
        }
        $arrCheckResult = $arrRet['data'];
        return true;
    }

    /**
     * @param $arrUnionAccount
     * @param $arrCheckResult
     * @return bool
     */
    protected static function _checkUnionAccount($arrUnionAccount, &$arrCheckResult)
    {
        $arrParams = [
            'union_id' => $arrUnionAccount['union_id'],
            'cmonth' => $arrUnionAccount['cmonth'],
            'update' => self::$_intType == 1 ? 0 : 1,
        ];
        if (!($arrRet = self::_callService('video', 'checkUnionAccount', $arrParams))) {
            return false;
        }
        $arrCheckResult = $arrRet['data'];
        return true;
    }

    /**
     * @param $intPn
     * @param $arrUnionList
     * @return bool
     */
    protected static function _getUnionList($intPn, &$arrUnionList)
    {
        $arrParams = [
            'status' => Lib_Define_Union::UNION_AUDIT_STATUS_ENTER,
            'pn' => $intPn,
            'rn' => self::PAGE_SIZE,
        ];
        if (!($arrRet = self::_callService('video', 'getUnionList', $arrParams))) {
            return false;
        }
        $arrUnionList = $arrRet['data']['rows'];
        return true;
    }

    /**
     * @param $intUnionId
     * @param $arrUnionAccounts
     * @return bool
     */
    protected static function _getUnionAccounts($intUnionId, &$arrUnionAccounts)
    {
        $intMonth = (int)date('Ym', self::$_intTaskStartTime);
        $arrParams = [
            'union_id' => $intUnionId,
            'cmonth' => self::$_intType == 1 ? $intMonth : 'BETWEEN '
                . Lib_Define_Union::ACCOUNT_START_MONTH . ' AND ' . ($intMonth - 1),
            'status' => '<= ' . Lib_Define_Union::UNION_ACCOUNT_STATUS_SUBMIT,
            'pn' => 1,
            'rn' => 1000,
            'sync_bank' => 0,
        ];
        if (!($arrRet = self::_callService('video', 'getUnionAccountList', $arrParams))) {
            return false;
        }
        $arrUnionAccounts = $arrRet['data']['list'];
        return true;
    }

    /**
     * @param $intUnionId
     * @param $intMonth
     * @param $intPn
     * @param $arrMemberAccountList
     * @return bool
     */
    protected static function _getMemberAccountList($intUnionId, $intMonth, $intPn, &$arrMemberAccountList)
    {
        $arrParams = [
            'union_id' => $intUnionId,
            'cmonth' => $intMonth,
            'pn' => $intPn,
            'rn' => self::PAGE_SIZE,
            'mask' => 0,
        ];
        if (!($arrRet = self::_callService('video', 'getMemberAccountList', $arrParams))) {
            return false;
        }
        $arrMemberAccountList = $arrRet['data']['list'];
        return true;
    }

    protected static function _init()
    {
        self::$_intTaskStartTime = time();

        // get opts
        $arrOpts = getopt('', [
            'type::',
            'union_pn::',
            'member_pn::',
            'try_times::',
        ]);

        if (!isset($arrOpts['type']) || $arrOpts['type'] === false) {
            echo "type required", PHP_EOL;
            return false;
        }
        self::$_intType = (int)$arrOpts['type'];

        if (isset($arrOpts['union_pn']) && $arrOpts['union_pn'] > 0) {
            self::$_intUnionPageNum = $arrOpts['union_pn'];
        }

        if (isset($arrOpts['member_pn']) && $arrOpts['member_pn'] > 0) {
            self::$_intMemberPageNum = $arrOpts['member_pn'];
        }

        if (isset($arrOpts['try_times']) && $arrOpts['try_times'] > 0) {
            self::$_intServiceTryTimes = $arrOpts['try_times'];
        }

        return true;
    }

    /**
     * @param $strService
     * @param $strMethod
     * @param $arrParams
     * @return bool|mixed
     */
    protected static function _callService($strService, $strMethod, $arrParams)
    {
        $intTryTimes = self::$_intServiceTryTimes;
        while (true) {
            $arrRet = Tieba_Service::call($strService, $strMethod, $arrParams, null, null, 'post', 'php', 'utf-8');
            if ($arrRet && $arrRet['errno'] == Tieba_Errcode::ERR_SUCCESS) {
                return $arrRet;
            }
            if (--$intTryTimes <= 0) {
                Bingo_Log::warning("fail to call service $strService::$strMethod, input: " . json_encode($arrParams)
                    . ', ret: ' . json_encode($arrRet));
                break;
            }
            sleep(1);
        }
        return false;
    }

}
