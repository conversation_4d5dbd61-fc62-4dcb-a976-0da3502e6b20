<?php
/**
 * <AUTHOR>
 * @date 20170626
 */
define("MODULE","Service_Game_Feed");
class Service_Game_Feed {
	const SERVICE_NAME = "Service_Game_Feed";
    const ALL_USER = 8;
	
    /**
     * @param [type] $arrInput [description]
     * @return
     */
    public static function addThreadFeed($arrInput){
        if($arrInput['thread_id']<=0){
            Bingo_Log::pushNotice('error','param:thread_id=0');
            return true;
        }
        $arrVFids = $arrInput['v_forum_ids'];
        if(isset($arrInput['v_forum_info'])){
            foreach($arrInput['v_forum_info'] as $intFid => $result){
                if(Tieba_Errcode::ERR_SUCCESS != $result['errno']){
                    $arrVFids = array_diff($arrVFids, array($intFid));
                }
            }
        }
        if(empty($arrVFids)){
            Bingo_Log::pushNotice('error','param:empty_arrVFids');
            return true;
        }
        $intUid = (int)$arrInput['user_id'];
        $intTid = (int)$arrInput['thread_id'];
        $intCreateTime = (int)$arrInput['create_time'];
        $strTitle = $arrInput['title'];
        //$strTitle = Bingo_Encode::convert($strTitle, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);

        $intOpUid = 0;
        $strOpUname = 'nmq_add';
        
        $arrInput = array(
            'forum_id' => $arrVFids,
        );
        $arrOutput = Service_Game_Game::getGameIdByFid($arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call Service_Game_Forum::getGameIdByFid failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return false;
        }
        $arrMap = array();
        $arrFids = array();
        $arrFGids = array();
        foreach($arrOutput['data'] as $intFid => $strGameId){
            if($strGameId){
                $arrFids []= $intFid;
                $arrFGids []= (int)$strGameId;
                $arrMap[$intFid] = (int)$strGameId;
            }
        }

        if(empty($arrMap)){
            Bingo_Log::pushNotice('error','no_white_forum');
            return true;
        }

        $arrInput = array(
            'user_id' => array(
                $intUid,
                self::ALL_USER,
            ),
        );
        $arrOutput = Service_Game_User::getGameIdByUid($arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call Service_Game_Forum::getGameIdByFid failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return false;
        }
        $arrUGidsSingle = isset($arrOutput['data']['map'][$intUid]) ? $arrOutput['data']['map'][$intUid] : array() ;
        $arrUGidsAll = isset($arrOutput['data']['map'][self::ALL_USER]) ? $arrOutput['data']['map'][self::ALL_USER] : array() ;
        $arrUGids = array_merge($arrUGidsSingle, $arrUGidsAll);
        $arrUGids = array_unique($arrUGids);
        $arrGids = array_intersect($arrUGids, $arrFGids);
        Bingo_Log::warning(sprintf("single:[%s] all[%s] u[%s] f[%s] g[%s]", join(',', $arrUGidsSingle), join(',', $arrUGidsAll), join(',', $arrUGids),join(',', $arrFGids),join(',', $arrGids)));

        $arrInsert = array();
        foreach($arrGids as $intGameId){
            $arrInsert[] = array(
                'game_id'    => $intGameId,
                'thread_id'  => $intTid,
                'user_id'    => $intUid,
                'title'      => $strTitle,
                'create_time' => $intCreateTime,
                'play_count' => 0,
                'status'     => Util_Const::GAME_VIDEO_STATUS_ONLINE,
                'op_uid'     => $intOpUid,
                'op_uname'   => $strOpUname,
                'op_time'    => time(),
            );

        }
        if(empty($arrInsert)){
            Bingo_Log::warning('empty');
            return true;
        }

        $arrOutput = Dl_Game_Game::replace($arrInsert);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call Service_Game_Game::select failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return false;
        }
        return true;
        
    }
    /**
     * [modifyGameUser description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function delThreadFeed($arrInput){
        if($arrInput['thread_id']<=0){
            Bingo_Log::pushNotice('error','param:thread_id=0');
            return true;
        }

        $intTid = (int)$arrInput['thread_id'];
        $arrInput = array(
            'thread_id' => $intTid,
            //'id' => $arrIds,
            'status' => Util_Const::GAME_VIDEO_STATUS_OFFLINE,
            'op_uid' => 0,
            'op_uname' => 'nmq_del',
            'op_time' => time(),
        );
        return self::updateFeed($arrInput);
    }
    /**
     * [recThreadFeed description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function recThreadFeed($arrInput){
        if($arrInput['thread_id']<=0){
            Bingo_Log::pushNotice('error','param:thread_id=0');
            return true;
        }

        $intTid = (int)$arrInput['thread_id'];
        $arrInput = array(
            'thread_id' => $intTid,
            //'id' => $arrIds,
            'status' => Util_Const::GAME_VIDEO_STATUS_ONLINE,
            'op_uid' => 0,
            'op_uname' => 'nmq_rec',
            'op_time' => time(),
        );
        return self::updateFeed($arrInput);
    }
    /**
     * [updateFeed description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    private static function updateFeed($arrInput){
        $intTid = (int)$arrInput['thread_id'];
        $arrInput = array(
            'thread_id' => $intTid,
        );
        $arrOutput = Dl_Game_Game::select($arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call Service_Game_Game::select failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return false;
        }
        if(empty($arrOutput['data'])){
            return true;
        }
        $arrIds = array();
        foreach($arrOutput['data'] as $record){
            $arrIds[]= $record['id'];
        }
        $arrInput = array(
            //'thread_id' => $intTid,
            'id' => $arrIds,
            'status' => Util_Const::GAME_VIDEO_STATUS_OFFLINE,
            'op_uid' => 0,
            'op_uname' => 'nmq_del',
            'op_time' => time(),
        );
        $arrOutput = Dl_Game_Game::update($arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call Service_Game_Game::select failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return false;
        }
        return true;
    }
    
    /**
     * [_succRet description]
     * @param  [type] $ret [description]
     * @return [type]      [description]
     */
    protected static function _succRet($ret = null){
        $error = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data'   => $ret,
        );
    }
    /**
     * [_errRet description]
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    protected static function _errRet($errno){
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }
}
