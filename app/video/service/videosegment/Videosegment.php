<?php
/***************************************************************************
 * 
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Videosegment.php
 * <AUTHOR>
 * @date 2018/08/31 17:43:47
 * @brief 视频封面相关处理
 *  
 **/

define('MODULE', 'Video_service');
class Service_Videosegment_Videosegment {

    // redis请求队列key
    const REDIS_VIDEO_SEGMENT_REQUEST_KEY   = 'video_segment_request';
    // redis接收队列key
    const REDIS_VIDEO_SEGMENT_RESPOND_KEY   = 'video_segment_respond';

    // 视频最小播放时长限制（单位:s）
    const VIDEO_DURATION_MINIMUM_LIMIT      = 30;

    // 视频动态封面相关接口 ------ add by hanpeiyan ------ start
    /**
     * 接发帖nmq获取视频贴
     * @param
     * @return
     */
    public static function getVideoThreadByNmq($arrInput) {
        // 获取参数并解析
        $arrParams = Tieba_Service::getArrayParams($arrInput, 'data');
        if ($arrParams === false || empty($arrParams)) {
            Bingo_Log::warning("getArrayParams error.input[" . serialize($arrInput) . "] output[" . serialize($arrParams) . "]");
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_MCPACK_ERR);
        }
        if (!isset($arrParams['_cmd']) || !isset($arrParams['thread_id']) || $arrParams['thread_id'] <= 0) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrParams) . "]");
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 过滤非视频贴
        if ($arrParams['thread_type'] != Tieba_Type_Thread::MOVIDEO_THREAD) {
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_SUCCESS);
        }

        // 整理入参信息
        $input = array(
            'thread_id' => intval($arrParams['thread_id']),
        );
        $output = self::dynamicCoverSend($input);
        if ($output['errno'] == Tieba_Errcode::ERR_LIVEPOST_NETWORK_UNSTABLE) {
            Bingo_Log::warning("call Threadinfo failed, need retry. input[" . serialize($input) . "], output[" . serialize($output) . "]");
            header('status: 507');
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_LIVEPOST_NETWORK_UNSTABLE);
        }
        if ($output['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call video:dynamicCoverSend fail, input:[' . serialize($input) . '],output:[' . serialize($output) . ']');
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return Lib_Audit::arrRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 整理视频数据添加请求队列
     * @param
     * @return
     */
    public static function dynamicCoverSend($arrInput) {
        if (empty($arrInput['thread_id'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrOutput = self::getVideoInfoByThreadId($arrInput);
        if ($arrOutput['errno'] == Tieba_Errcode::ERR_LIVEPOST_NETWORK_UNSTABLE) {
            Bingo_Log::warning('call video:getVideoInfoByThreadId fail, input:[' . serialize($arrInput) . '],output:[' . serialize($arrOutput) . ']');
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_LIVEPOST_NETWORK_UNSTABLE);
        }
        if ($arrOutput['errno'] == Tieba_Errcode::ERR_SUCCESS && empty($arrOutput['data'])) {
            // 被过滤的帖子走这个逻辑里
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_SUCCESS);
        }
        if ($arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call video:getVideoInfoByThreadId fail, input:[' . serialize($arrInput) . '],output:[' . serialize($arrOutput) . ']');
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrOutput = $arrOutput['data'];
        Bingo_Log::notice('video::dynamicCoverSend send input:[' . serialize($arrInput) . ']');

        // 写请求redis队列
        $arrData = array(
            'tid'       => intval($arrInput['thread_id']),
            'title'     => strval($arrOutput['title']),
            'url'       => strval($arrOutput['video_url']),
            'width'     => intval($arrOutput['video_width']),
            'height'    => intval($arrOutput['video_height']),
        );
        $input = array(
            'key'   => self::REDIS_VIDEO_SEGMENT_REQUEST_KEY,
            'value' => json_encode($arrData),
        );
        $output = Dl_Videosegment_Videosegment::rpush($input);
        if ($output == false || $output['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call redis rpush fail, input:[' . serialize($input) . '],output:[' . serialize($output) . ']');
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        // 获取redis长度并打日志,监控
        self::getRedisListLen(self::REDIS_VIDEO_SEGMENT_REQUEST_KEY);

        return Lib_Audit::arrRet(Tieba_Errcode::ERR_SUCCESS, $output);
    }

    /**
     * 接收IDL处理后的视频数据
     * IDL视频处理文档地址:http://hetu.baidu.com/api/platform/api/show?apiId=945
     * @param
     * @return
     */
    public static function dynamicCoverReceive() {
        // 获取参数并解析
        $strdata        = file_get_contents("php://input");
        $arrData        = json_decode($strdata, true);
        $arrSelection   = json_decode($arrData['feature_result_list'][0]['value'], true);
        $selection      = $arrSelection['selection'];
        $requestId      = $arrData['passthrough_field'];

        Bingo_Log::notice('video::dynamicCoverReceive receive input:[' . serialize($arrData) . ']');
        Bingo_Log::notice('video::dynamicCoverReceive receive request_id:[' . $requestId . '], selection:[' . serialize($selection) . ']');
        if (empty($requestId) || empty($selection)) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrData) . "]");
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (empty($selection['section']) || empty($selection['start_time']) || empty($selection['end_time'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrData) . "]");
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 写接收redis队列
        $arrInput = array(
            'request_id' => $requestId,
            'selection'  => array(
                'url'   => $selection['section'],
                'start' => $selection['start_time'],
                'end'   => $selection['end_time'],
            ),
        );
        $input = array(
            'key'   => self::REDIS_VIDEO_SEGMENT_RESPOND_KEY,
            'value' => json_encode($arrInput),
        );
        Bingo_Log::warning('video::dynamicCoverReceive receive add_redis:[' . serialize($input) . ']');
        $output = Dl_Videosegment_Videosegment::rpush($input);
        if ($output == false || $output['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call redis rpush fail, input:[' . serialize($input) . '],output:[' . serialize($output) . ']');
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        // 获取redis长度并打日志,监控
        self::getRedisListLen(self::REDIS_VIDEO_SEGMENT_RESPOND_KEY);

        return Lib_Audit::arrRet(Tieba_Errcode::ERR_SUCCESS, $output);
    }

    /**
     * 获取请求/接收队列list
     * @param
     * @return
     */
    public static function getRedisList($arrInput) {
        if (empty($arrInput['key'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $intStart   = isset($arrInput['start']) ? intval($arrInput['start']) : 0;
        $intStop    = isset($arrInput['stop']) ? intval($arrInput['stop']) : -1;
        $input = array(
            'key'   => $arrInput['key'],
            'start' => $intStart,
            'stop'  => $intStop,
        );
        $output = Dl_Videosegment_Videosegment::lrange($input);
        if ($output == false || $output['err_no'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call redis rpush fail, input:[' . serialize($input) . '],output:[' . serialize($output) . ']');
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrList = $output['ret'][$arrInput['key']];

        $input = array(
            'key' => $arrInput['key'],
        );
        $output = Dl_Videosegment_Videosegment::llen($input);
        if ($output == false || $output['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call redis llen fail, input:[' . serialize($input) . '],output:[' . serialize($output) . ']');
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrCount = intval($output['ret'][$arrInput['key']]);

        $arrRet = array(
            'list'  => $arrList,
            'count' => $arrCount,
        );

        return Lib_Audit::arrRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }

    /**
     * 删除key
     * @param
     * @return
     */
    public static function delRedisKey($arrInput) {
        if (empty($arrInput['key'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $input = array(
            'key' => $arrInput['key'],
        );
        $output = Dl_Videosegment_Videosegment::del($input);
        if ($output == false || $output['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call redis del fail, input:[' . serialize($input) . '],output:[' . serialize($output) . ']');
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return Lib_Audit::arrRet(Tieba_Errcode::ERR_SUCCESS, $output);
    }

    /**
     * 根据帖子id获取视频信息
     * @param
     * @return
     */
    private static function getVideoInfoByThreadId($arrInput) {
        if (empty($arrInput['thread_id'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $input = array(
            'thread_ids' => array(
                0 => intval($arrInput['thread_id']),
            ),
            'need_abstract'     => 0,
            'forum_id'          => 0,
            'need_photo_pic'    => 0,
            'need_user_data'    => 0,
            'icon_size'         => 0,
            'need_forum_name'   => 0,
            'call_from'         => 'client_frs',
        );
        $output = Tieba_Service::call('post', 'mgetThread', $input, null, null, 'post', 'php', 'utf-8');
        if ($output == false || $output['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call post:mgetThread fail, input:[' . serialize($input) . '],output:[' . serialize($output) . ']');
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrThreadInfo  = $output['output']['thread_list'][intval($arrInput['thread_id'])];

        // 这里兼容发帖后写mola延迟,导致立刻获取帖子信息为空,所以这里做下兼容,并返回该错误号表示需要重试（重试下即可）
        if (empty($arrThreadInfo)) {
            Bingo_Log::warning('call post:mgetThread and threadinfo delay, input:[' . serialize($input) . '],output:[' . serialize($output) . ']');
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_LIVEPOST_NETWORK_UNSTABLE);
        }

        // 过滤短视频（过滤小于30s的视频）
        if (intval($arrThreadInfo['video_info']['video_duration']) < self::VIDEO_DURATION_MINIMUM_LIMIT) {
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_SUCCESS);
        }
        // 过滤已经走过动态封面获取逻辑的数据（去重）
        if (isset($arrThreadInfo['video_segment'])) {
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_SUCCESS);
        }

        // 取清晰度最高的视频信息（video_id越小,清晰度越高）
        $arrVideoInfo   = array(
            'title'         => $arrThreadInfo['title'],
            'video_url'     => $arrThreadInfo['video_info']['video_url'],
            'video_width'   => $arrThreadInfo['video_info']['video_width'],
            'video_height'  => $arrThreadInfo['video_info']['video_height'],
            'video_duration'=> $arrThreadInfo['video_info']['video_duration'],
        );
        if (!empty($arrThreadInfo['video_info']['video_desc'])) {
            $intMinVideoId = PHP_INT_MAX;
            foreach ($arrThreadInfo['video_info']['video_desc'] as $key => $value) {
                if ($intMinVideoId > intval($value['video_id'])) {
                    $arrVideoInfo['video_url']      = $value['video_url'];
                    $arrVideoInfo['video_width']    = $value['video_width'];
                    $arrVideoInfo['video_height']   = $value['video_height'];

                    $intMinVideoId  = intval($value['video_id']);
                }
            }
        }

        return Lib_Audit::arrRet(Tieba_Errcode::ERR_SUCCESS, $arrVideoInfo);
    }

    /**
     * 获取redis list长度
     * @param
     * @return
     */
    private static function getRedisListLen($strKey) {
        // 获取长度并打印到日志中,做监控
        $input = array(
            'key' => $strKey,
        );
        $output = Dl_Videosegment_Videosegment::llen($input);
        if ($output == false || $output['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call redis llen fail, input:[' . serialize($input) . '],output:[' . serialize($output) . ']');
            return false;
        }

        $intRedisLen = isset($output['ret'][$input['key']]) ? intval($output['ret'][$input['key']]) : 0;
        Bingo_Log::notice('video_segment key[' . $input['key'] . '],llen[' . $intRedisLen . ']');

        return true;
    }
    // 视频动态封面相关接口 ------ add by hanpeiyan ------ end
}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
