<?php


/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015:10:14 10:48:19
 * @version
 * @structs & methods(copied from idl.)
 */
define("MODULE", "Video_service");
//require_once("../app/video/dl/video/Video.php"); // for orp normal.online
//require_once ("../dl/video/Video.php"); //for untest.php

class Service_Video_Video
{
	const SERVICE_NAME = "Service_Video_Video";
	public static $conf = array("product" => "tieba-smallvideo",);
	protected static $_conf = null;
	
	const DB_NAME = 'forum_xiaoying';
	public static $strVideoAuditTable = "video_audit";
	
	private static $_objDB = null;

    const GREAT_VIDEO_BAIJIAHAO_TABLE_NAME = 'great_video_baijiahao';
    const VIDEO_EDIT_TABLE_PREFIX = 'video_edit_';
    const NANI_VIDEO_THREAD_REDIS_KEY = 'nani_video_thread_redis_queue_es';
    const NO_NEED_TO_EDIT_TEMPORARILY_IN_REDIS_KEY = 'not_need_to_edit_temporarily_in_'; //为了节约成本暂时限制每天编辑的video的数量
    const NO_NEED_TO_EDIT_TEMPORARILY_REDIS_KEY    = 'not_need_to_edit_temporarily_'; //为了节约成本暂时限制每天编辑的video的数量

    private static $_arrSortFieldMap = array(
    	0 => 'thread_id',
    	1 => 'thread_create_time',
    	2 => 'thread_create_time',
    );
    private static $_arrSortTypeMap =  array(
    	0 => SORT_DESC,
    	1 => SORT_DESC,
    	2 => SORT_ASC,
    );

    private static $_arrSqlSortTypeMap =  array(
    	0 => 'DESC',
    	1 => 'DESC',
    	2 => 'ASC',
    );

	/**
	 * @brief
	 *
	 * @param {Array} $arrInput
	 *
	 * @return array
	 */
	private static function _getDB()
	{
		if(self::$_objDB){
			return self::$_objDB;
		}
		Bingo_Timer::start('db_init');
		self::$_objDB = Tieba_Mysql::getDB(self::DB_NAME);
		Bingo_Timer::end('db_init');
		if(self::$_objDB && self::$_objDB->isConnected()){
			//self::$_objDB->query("set names charset utf8");
			return self::$_objDB;
		}else{
			Bingo_Log::warning('fail to connect db');
			
			return null;
		}
	}
	
	/**
	 * @brief
	 *
	 * @param {Array} $arrInput
	 *
	 * @return array
	 */
	private static function _queryDB($strSql, $strTimer = 'db_query')
	{
		$objDB = self::_getDB();
		if(!$objDB){
			Bingo_Log::warning('fail to get db');
			
			return false;
		}
		Bingo_Timer::start($strTimer);
		$arrRet = $objDB->query($strSql);
		Bingo_Timer::end($strTimer);
		if($arrRet === false){
			Bingo_Log::warning("execute sql error [$strSql]");
		}
		
		return $arrRet;
	}
	
	/**
	 * @brief init
	 * @return: true if success. false if fail.
	 *
	 * @param {Array} $arrInput
	 *
	 * @return array
	 */
	private static function _init()
	{
		//add init code here. init will be called at every public function beginning.
		//not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
		//init should be recalled for many times.
		if(self::$_conf == null){
			self::$_conf = Bd_Conf::getConf("/app/video/service_video_video");
			if(self::$_conf == false){
				Bingo_Log::warning("init get conf fail.");
				
				return false;
			}
		}
		
		return true;
	}
	
	/**
	 * [_errRet description]
	 *
	 * @param  [type] $errno [description]
	 * @param  string $data [description]
	 *
	 * @return [type]        [description]
	 */
	private static function _errRet($errno, $data = '')
	{
		return array('errno' => $errno,
		             'errmsg' => Tieba_Error::getErrmsg($errno),
		             'data' => $data,);
	}
	
	/**
	 * @brief
	 *
	 * @param {Array} $arrInput
	 *
	 * @return array
	 */
	public static function preCall($arrInput)
	{
		// pre-call hook
	}
	
	/**
	 * @brief
	 *
	 * @param {Array} $arrInput
	 *
	 * @return array
	 */
	public static function postCall($arrInput)
	{
		// post-call hook
	}
	
	/**
	 * @brief 备份数据
	 * @arrInput:
	 *     $arrNeedData = array(
	 * "user_id",
	 * "user_name",
	 * "forum_id",
	 * "forum_name",
	 * "thread_id",
	 * "post_id",
	 * "title",
	 * "video_md5",
	 * "video_url",
	 * "video_duration",
	 * "thumbnail_url",
	 * )
	 * @brief
	 * 支持多吧发贴，存储字段 is_multi_forum v_forum_ids
	 * @param {Array} $arrInput
	 *
	 * @return: $arrOutput
	 *    out_info data[]
	 **/
	public static function saveVideoInfo($arrInput)
	{
		$arrNeedData = array("user_id",
		                     "user_name",
		                     "forum_id",
		                     "forum_name",
		                     "thread_id",
		                     "post_id",
		                     "title",
		                     "video_md5",
		                     "video_url",
		                     "video_duration",
		                     "thumbnail_url",
		                     "is_thread",);
		$arrVideoInfo = array();
		foreach($arrNeedData as $value){
			if(!isset($arrInput[$value])){
				$strLog = "param error, input[ ".serialize($arrInput)."";
				Bingo_Log::warning($strLog);
				
				return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
			}
			$arrVideoInfo[$value] = $arrInput[$value];
		}
		if(empty($arrVideoInfo["video_from"])){
			$arrVideoInfo["video_from"] = "client";
		}
		//支持多吧发贴 start 
		if (isset($arrInput['is_multi_forum'])) {
			if ($arrInput['is_multi_forum'] == 1 && !empty($arrInput['is_multi_forum'])) {
				//多吧发贴时，forum_id为兼容老逻辑，保存多吧IDlist的第一个fourmid  ，mod by zbo
				$arrInput['forum_id'] = intval($arrInput['is_multi_forum'][0]);
			}
			$arrVideoInfo['is_multi_forum'] = intval($arrInput['is_multi_forum']);
		} else {
			$arrVideoInfo['is_multi_forum'] = 0;
		}
		if (isset($arrInput['v_forum_ids'])) {
			$arrVideoInfo['v_forum_ids'] = json_encode($arrInput['v_forum_ids']);
		} else {
			$arrVideoInfo['v_forum_ids'] = "";
		}
		//支持多吧发贴 end
		$arrVideoInfo['create_time'] = Bingo_Timer::getNowTime();
		$arrSaveData = array("field" => $arrVideoInfo,);
		$strTableName = "video_info_".$arrInput['user_id'] % 256;
		$arrRes = Dl_Video_Video::saveVideoInfo($arrSaveData, $strTableName);
		
		return $arrRes;
	}
	
	/**
	 * @brief 修改数据
	 * @arrInput:
	 *     $arrNeedData = array(
	 * "user_id",
	 * "is_thread",
	 * "thread_id",
	 * "post_id",
	 * );
	 *
	 * @param {Array} $arrInput
	 *
	 * @return: $arrOutput
	 *    out_info data[]
	 **/
	public static function updateVideoInfo($arrInput)
	{
		$arrNeedData = array("user_id",
		                     "thread_id",
		                     "post_id",
		                     "is_thread",);
		$arrVideoInfo = array();
		foreach($arrNeedData as $value){
			if(!isset($arrInput[$value])){
				$strLog = "param error, input[ ".serialize($arrInput)."";
				Bingo_Log::warning($strLog);
				
				return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
			}
			$arrVideoInfo[$value] = $arrInput[$value];
		}
		if($arrVideoInfo['is_thread'] == 1){
			$arrSaveData = array("field" => array("status" => 1),
			                     "cond" => array("thread_id" => $arrVideoInfo['thread_id'],
			                                     "is_thread" => $arrVideoInfo['is_thread']));
		}else{
			$arrSaveData = array("field" => array("status" => 1),
			                     "cond" => array("thread_id" => $arrVideoInfo['thread_id'],
			                                     "post_id" => $arrVideoInfo['post_id']));
		}
		$strTableName = "video_info_".intval($arrVideoInfo['user_id']) % 256;
		$arrRes = Dl_Video_Video::updateVideoInfo($arrSaveData, $strTableName);
		
		return $arrRes;
	}
	
	/**
	 * @brief 删除视频
	 *
	 * @param {Array} $arrInput
	 *
	 * @return array
	 */
	public static function delMoVideo($arrInput)
	{
        /*
		$arrInput['product'] = self::$conf['product'];
		$arrServiceInput = array("product" => self::$conf['product'],
		                         "url" => $arrInput["url"],
		                         "file_name" => $arrInput["file_name"],);
        */
        if (!isset($arrInput['url'])) {
            Bingo_Log::warning('param error, input: '.json_encode($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!isset($arrInput['product']) || !isset($arrInput['file_name'])) {
            if (!preg_match('#^(https?://)?(tb-video\.bdstatic\.com|gss3\.baidu\.com/6LZ0ej3k1Qd3ote6lo7D0j9wehsv)/([^/]+)/([^\?]+)#', $arrInput['url'], $arrMatches)) {
                Bingo_Log::warning('param error, input: '.json_encode($arrInput));
                return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $arrInput['product'] = $arrMatches[3];
            $arrInput['file_name'] = $arrMatches[4];
        }

		$arrRet = self::mcpackCall('smallvideo', 'delFile', $arrInput);
		if($arrRet === false || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
			Bingo_Log::warning('call smallvideo delFile fail.[input]'.serialize($arrInput)."[output]".serialize($arrRet));
			
			return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}

		return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['data']);
	}

	/**
	 * @brief 分片上传(已经迁移到UI层 20151202-jinya)
	 *
	 * @param {Array} $arrInput
	 *
	 * @return array
	 */
	public static function uploadMultiPart($arrOriginInput)
	{
		$arrInput = array('key' => $arrOriginInput['key'],
		                  'data_num' => $arrOriginInput['data_num'],
		                  'all_num' => $arrOriginInput['all_num'],
		                  'data_len' => $arrOriginInput['data_len'],
		                  'all_len' => $arrOriginInput['all_len'],
		                  'data' => $arrOriginInput['data'],);
		$arrInput['product'] = self::$conf['product'];
		ral_set_idc("nj");
		$arrOutput = self::mcpackCall('smallvideo', 'uploadMultiPart', $arrInput);
		$arrInput['data'] = "(bytes)";
		if(false === $arrOutput || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::fatal("call smallvideo uploadMultiPart fail; [input]".serialize($arrInput).";[output]".serialize($arrOutput));
			
			return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}
		
		return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput['data']);
	}
	
	/**
	 * @brief 生成url(已经迁移到UI层 20151202-jinya)
	 * 此方法暂时不会调用, add by zbo 20170525
	 * @param {Array} $arrInput
	 *
	 * @return array
	 */
	public static function generateUrl($arrOriginInput)
	{
		$arrInput = array('key' => $arrOriginInput['key'],
		                  'all_num' => $arrOriginInput['all_num'],);
		$arrInput['product'] = self::$conf['product'];
		ral_set_idc("nj");
		$arrOutput = self::mcpackCall('smallvideo', 'generateUrl', $arrInput);
		if(false === $arrOutput || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::fatal("call smallvideo generateUrl fail; input[".serialize($arrInput).";[output]".serialize($arrOutput));
			
			//透传出去errno，判断是否需要重新上传分片
			return self::errRet($arrOutput['errno']);
		}
		$strUrl = $arrOutput['data'];
		
		return self::errRet(Tieba_Errcode::ERR_SUCCESS, $strUrl);
	}
	
	/**
	 * @brief 发帖到视频吧(废弃 20151202-jinya)
	 * 此方法暂时不会调用， add by zbo 20170525
	 * @param $arrRequest 发帖参数
	 *
	 * @return array
	 */
	public static function addThread($arrRequest)
	{
		//发帖
		$arrRequest['forum_id'] = 1252235;
		$arrRequest['forum_name'] = "视频";
		$arrRequest['product_private_key'] = "special_pro"; //这个参数很牛逼，并发可用
		$arrParams = array('req' => $arrRequest,);
		$arrRet = Tieba_Service::call('post', 'addThread', $arrParams, null, null, 'post', 'php', 'utf-8');
		if($arrRet === false){
			Bingo_Log::warning('call post service fail');
			
			return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}
		if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
			Bingo_Log::warning('call service post::addThread fail, input='.serialize($arrParams).',ret='.serialize($arrRet));
			
			return self::errRet($arrRet['errno']);
		}
		$arrThreadInfo = $arrRet['res'];
		
		return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrThreadInfo);
	}
	
	/**
	 * @brief 获取小影信息
	 *
	 * @param $arrInput ['date'] 日期为单位 20150819
	 *
	 * @return array
	 */
	public static function getXiaoyingByDate($arrInput)
	{
		$strDatestamp = $arrInput['date'];
		Bingo_Timer::start('db_init');
		$_objDB = Tieba_Mysql::getDB("forum_xiaoying");
		Bingo_Timer::end('db_init');
		if(!$_objDB){
			Bingo_Log::warning('fail to connect db');
			
			return self::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		if(empty($strDatestamp)){
			$strDatestamp = strtotime(date("Ymd", time()));
		}
		$strSqlWhere = '';
		$strSqlWhere .= " where `create_time` >= ".$strDatestamp." and `create_time` <= ".($strDatestamp + 86400);
		$firstMonday = self::getThatDayMonday($strDatestamp);
		$strTableName = self::$strVideoAuditTable."_".date("Ymd", $firstMonday);
		$strSql = 'select id as video_audit_id, play_count, forum_id,forum_name, title, abstract, xiaoying_url, user_id, user_name, thread_id, post_id,status, create_time, weekkey from '.$strTableName;
		$strSql .= $strSqlWhere;
		$arrOutputList = $arrRet = $_objDB->query($strSql);
		//$arrOutputList = Bingo_Encode::convert($arrOutputList,Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
		if(false === $arrOutputList){
			Bingo_Log::warning("sql  [$strSql] fail .".serialize($arrOutputList));
			
			return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}
		
		return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutputList);
	}
	
	/**
	 * @brief 根据日期获取当前表名
	 *
	 * @param {int} $thatDayStamp 存储数据
	 *
	 * @return: timestamp.
	 **/
	public static function getThatDayMonday($thatDayStamp)
	{
		$MonDayDateStamp = strtotime("Monday");
		$skipWeek = ceil(($MonDayDateStamp - $thatDayStamp) / 86400 / 7);
		$thatMonday = $MonDayDateStamp - $skipWeek * 7 * 86400;
		
		return $thatMonday;
	}
	
	/**
	 * @brief mcpack方式调用Service
	 *
	 * @param {Array} 沿用tieba_service::call顺序
	 *
	 * @return: service output.
	 **/
	public static function mcpackCall($strServiceName, $strMethodName, $arrInput)
	{
		$header = array('pathinfo' => '/service/'.$strServiceName.'?method='.$strMethodName.'&format=mcpackraw',
		                'Host' => "service.tieba.baidu.com",);
		$arrOutput = ral('service_'.$strServiceName, $strMethodName, $arrInput, array(), $header);
		
		return $arrOutput;
	}
	/**
	 * @brief
	 *
	 * @param {Array} $arrInput
	 *
	 * @return array
	 */
	public static function errRet($errno, $data = '', $errmsg = '')
	{
		if($errmsg === ''){
			$errmsg = Tieba_Error::getErrmsg($errno);
		}
		$arrRet = array('errno' => $errno,
		                'errmsg' => $errmsg,);
		if($data !== ''){
			$arrRet['data'] = $data;
		}
		Bingo_Log::pushNotice("errno", $errno);
		
		return $arrRet;
	}
	
	/**
	 * 视频审核
	 *
	 * @param $arrParamInput 推送信息
	 * Array(
	 *  "user_id" //被推送人的uid
	 *  "type" //推送类型
	 *  "title" //贴子标题
	 * )
	 * http://service.tieba.baidu.com/service/im?method=addPlatForumPMsg&group_id=0&msg_type=1&user_id=1035957097&user_type=4&to_user_id=897802130&to_user_type=0&content=Jinya+GG+memeda&duration=0&record_id=-1&cuid=%27%27&version=%27%27&device=%27%27&data={%22client_version%22:%22%22,%22seq_id%22:0}&format=json
	 *
	 * @return array
	 */
	public static function passOrNotPushMsg($arrParamInput)
	{
		$strMsgType = $arrParamInput['type'];
		$strThreadTitle = $arrParamInput['title'];
		$intUserId = $arrParamInput['to_user_id'];
		switch($strMsgType){
			case 'pass':
				$strPushMsg = "您的视频贴《{$strThreadTitle}》已经通过审核";
				$intEventId = '';
				break;
			case 'fail':
				$strPushMsg = "您的视频贴《{$strThreadTitle}》您的视频贴未通过审核";
				$intEventId = '';
				break;
			case 'revert':
				$strPushMsg = "您的视频贴《{$strThreadTitle}》您的视频贴已经恢复";
				$intEventId = '';
				break;
		}
		$arrParam = array('group_id' => 0,  //必填，填0
		                  'msg_type' => 1,  //1普通文本  2图片  3语音 10图文消息
		                  'user_id' => 1035957097,  //消息发送方id, 1035957097 贴吧团队
		                  'user_type' => 4,  //消息发送方类型，0普通用户，1平台化官方吧用户类型，3订阅用户类型，4贴吧官方账号
		                  'to_user_id' => $intUserId, //消息接收方uid
		                  'to_user_type' => 0, //消息接收方类型，同上
		                  'content' => $strPushMsg, //消息内容
		                  'duration' => 0,  //语音消息时长，必填字段，不是语音消息填0
		                  'record_id' => -1, //必填，填-1
		                  'cuid' => '',     //必填，填空
		                  'version' => '',  //必填，可以为空
		                  'data' => json_encode(array('client_version' => '', 'seq_id' => 0)),
		                  'device' => '', //设备信息，可以为空
		);
		$arrOut = Tieba_Service::call('im', 'addPlatForumPMsg', $arrParam, null, null, 'post', 'php', 'utf-8');
		$intErrNo = (isset($arrOut['errno'])) ? $arrOut['errno'] : -1;
		if(empty($arrOut) || Tieba_Errcode::ERR_SUCCESS != $intErrNo){
			Bingo_Log::warning('call service im-addPlatForumPMsg  fail, [input]'.serialize($arrParam).'[output]'.serialize($arrOut));
		}
		
		return self::errRet(Tieba_Errcode::ERR_SUCCESS);
	}
	
	/**
	 * @desc
	 *
	 * @param
	 *     uid
	 *     pn
	 *     rn
	 *
	 * @return
	 * <AUTHOR>
	 */
	public static function getPersonalVideo($arrInput)
	{
		if(!isset($arrInput['uid']) || $arrInput['uid'] <= 0){
			return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$intPn = 0;
		$intRn = 3;
		$intUid = intval($arrInput['uid']);
		if(isset($arrInput['pn'])){
			$intPn = intval($arrInput['pn']);
		}
		if(isset($arrInput['rn'])){
			$intRn = intval($arrInput['rn']);
		}
		$arrCond = array('user_id' => $intUid,
		                 'status' => 0,
		                 'is_thread' => 1,);
		$strTableName = "video_info_".$intUid % 256;
		$arrDlInput = array('field' => array('thread_id',),
		                    'table' => $strTableName,
		                    'cond' => $arrCond,
		                    'append' => " order by create_time desc limit ".$intPn.",".$intRn,);
		$arrOutputRet = Dl_Video_Video::getPersonalVideo($arrDlInput);
		if(!$arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');
			
			return self::errRet($arrOutputRet['errno']);
		}
		$arrTids = array();
		foreach($arrOutputRet['data'] as $videoinfo){
			if(isset($videoinfo['thread_id'])){
				$arrTids[] = $videoinfo['thread_id'];
			}
		}
		
		return array('errno' => Tieba_Errcode::ERR_SUCCESS,
		             'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
		             'tids' => array_values($arrTids),);
	}
	
	
	
	/**
	 *
	 * 这个函数现在在空跑，所以先返回空，等到需要的时候再改
	 * 改的时候需要加索引：
	 * alter table video_audit_20171218 add KEY `idx_ast_opt`(audit_status,op_time);
	 * 
	 * @desc 根据时间获取视屏贴的Tid信息,支持查询一天内的数据
	 * 如果查询同一天数据，请把start_time 和end_time传相同值(零点零分零秒)
	 * 如果start_time==end_time 则取start_time开始之后一天的数据
	 *
	 * @param
	 *     start_time
	 *     end_time
     *     micro_video = 1 获取短视频
     *     check_duration = 1 返回时长大于0的
     *     check_duration = 0 返回时长等于0的
     *
     * 
	 *
	 * @return
	 * <AUTHOR>
	 */
	public static function getVideoThreadTidByDate($arrInput)
	{
		if(!isset($arrInput['start_time']) || empty($arrInput['start_time']) || !isset($arrInput['end_time']) || empty($arrInput['end_time'])){
			return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		return self::errRet(Tieba_Errcode::ERR_SUCCESS, array());
		$start_time = intval($arrInput['start_time']);
		$end_time = intval($arrInput['end_time']);
        $micro_video = intval($arrInput['micro_video']);
        $check_duration = intval($arrInput['check_duration']);
        $strCallFrom = strval($arrInput['call_from']);
        $bolFromRecommend = false;
        if ($strCallFrom === 'client_recommend') {
            $bolFromRecommend = true;
        }
        
        $arrTids = array();
        $arrVideoTids = array();
        $microVideoArr = array();
        
		if($end_time - $start_time >= 86400){
			return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		if($start_time == $end_time){
			$end_time = $end_time + 86400;
		}
		$title = '我发表了一篇视频贴，大伙来看看吧~';
		//$title = is_utf8($title)? $title:Bingo_Encode::convert($title,Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
		//分表查询
		$skipWeek = floor(($end_time - $start_time) / 86400 / 7) + 1;
		$firstMonday = self::getThatDayMonday($start_time);
		$arrWeek = array();
		for($weekIndex = 0; $weekIndex <= $skipWeek; $weekIndex++){
			if($firstMonday + $weekIndex * 7 * 86400 > $end_time){
				break;
			}
			$arrWeek[] = date("Ymd", $firstMonday + $weekIndex * 7 * 86400);
		}
		
		foreach($arrWeek as $key => $value){
			$strTableName = "video_audit_".$value;
			$arrDlInput = array('function' => 'getVideoThreadTidByDate',
			                    'table_name' => $strTableName,
			                    //'title' => $title,
			                    'start_time' => $start_time,
			                    'end_time' => $end_time,
			                    '_dbname' => 'forum_video_audit',);
			$arrOutputRet = Dl_Active_Active::execSql($arrDlInput);
			if(!$arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');
				
				return self::_errRet($arrOutputRet['errno']);
			}
			foreach($arrOutputRet['results'][0] as $videoinfo){
				if(isset($videoinfo['thread_id']) && !empty($videoinfo['thread_id']) && $videoinfo['thread_id'] > 0 && $videoinfo['title'] != $title){
				    // 给推荐过滤切帧失败的帖子
				    if ($bolFromRecommend && intval($videoinfo['frame_status']) === Service_Video_Frame::FRAME_FAIL) {
				        continue;
                    }

                    // need_audit_flag =  1
                    if(isset($arrInput['need_audit_flag']) && 1 == $arrInput['need_audit_flag']){
                        $arrTids[] = array(
                            'audit_flag' => $videoinfo['audit_flag'],  //0&&1:通过;2:低俗;3:内容不适;4:低俗||内容不适
                            'thread_id' => $videoinfo['thread_id']
                        );
                    } else {
                        $arrTids[] = $videoinfo['thread_id'];
                    }

                    $microVideoArr[] = $videoinfo;
				}
			}
		}
		$arrDlInput = array('function' => 'getBigSouVideoThreadTidByDate',
		                    'table_name' => 'video_info_spider2',
		                    'start_time' => $start_time,
		                    'end_time' => $end_time,
		                    '_dbname' => 'forum_movideo',);
		$arrOutputRet = Dl_Active_ActiveVideo::execSql($arrDlInput);
		if(!$arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');
			
			return self::_errRet($arrOutputRet['errno']);
		}
		foreach($arrOutputRet['results'][0] as $videoinfo){
			if(isset($videoinfo['thread_id']) && !empty($videoinfo['thread_id'])){
				$arrTids[] = $videoinfo['thread_id'];
			}
		}
        
        if( $micro_video ){
            $arrTids = self::_getMicroVideo($microVideoArr, $micro_video, $check_duration);
        }
        
		return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrTids);
	}
	
	/**
	 * @desc 根据时间获取视屏贴的Tid信息,支持查询一天内的数据
	 * 如果查询同一天数据，请把start_time 和end_time传相同值(零点零分零秒)
	 * 如果start_time==end_time 则取start_time开始之后一天的数据
	 *
	 * @param
	 *     start_time
	 *     end_time
     *     micro_video = 1 获取短视频
     *     check_duration = 1 返回时长大于0的
     *     check_duration = 0 返回时长等于0的
     *     
	 *
	 * @return
	 * <AUTHOR>
	 */
	public static function getVideoThreadTidForEditByDate($arrInput)
	{
		if(!isset($arrInput['start_time']) || empty($arrInput['start_time']) || !isset($arrInput['end_time']) || empty($arrInput['end_time'])){
			return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$start_time = intval($arrInput['start_time']) ;
		$end_time = intval($arrInput['end_time']);
        $strCallFrom = strval($arrInput['call_from']);
        /*
        $bolFromRecommend = false;
        if ($strCallFrom === 'client_recommend') {
            $bolFromRecommend = true;
        }
        */
        
        $arrTids = array();
        $arrVideoTids = array();
        //$microVideoArr = array();
        
		if($end_time - $start_time >= 86400){
			return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		if($start_time == $end_time){
			$end_time = $end_time + 86400;
		}
		//$title = '我发表了一篇视频贴，大伙来看看吧~';
		//$title = is_utf8($title)? $title:Bingo_Encode::convert($title,Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
		//分表查询
		$skipWeek = floor(($end_time - $start_time) / 86400 / 7) + 1;
		$firstMonday = self::getThatDayMonday($start_time);
		$arrWeek = array();
		for($weekIndex = 0; $weekIndex <= $skipWeek; $weekIndex++){
			if($firstMonday + $weekIndex * 7 * 86400 > $end_time){
				break;
			}
			$arrWeek[] = date("Ymd", $firstMonday + $weekIndex * 7 * 86400);
		}
        
        $arrPriorOneWeek = array( date('Ymd',(strtotime(min($arrWeek))-7*86400)));//时间前推7天，多查询一张表
        $arrWeek = array_merge($arrPriorOneWeek,$arrWeek);
		foreach($arrWeek as $key => $value){
			$strTableName = "video_audit_".$value;
			$arrDlInput = array('function' => 'getVideoThreadTidForEditByDate',
			                    'table_name' => $strTableName,
			                    'start_time' => $start_time,
			                    'end_time' => $end_time,
			                    '_dbname' => 'forum_video_audit',);
			$arrOutputRet = Dl_Active_Active::execSql($arrDlInput);
            Bingo_Log::notice('call getVideoThreadTidForEditByDate, res_count='.count($arrOutputRet['results'][0]).' req='.serialize($arrDlInput ));
			if(!$arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');
				
				return self::_errRet($arrOutputRet['errno']);
			}
			foreach($arrOutputRet['results'][0] as $videoinfo){
				if(isset($videoinfo['thread_id']) && !empty($videoinfo['thread_id'])){

                    $arrTids[] = $videoinfo['thread_id'];
				}
			}
		}
        
		return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrTids);
	}

    /**
     * @desc 根据时间获取视频贴的Tid信息,支持查询一天内的数据
     * 如果查询同一天数据，请把start_time 和end_time传相同值(零点零分零秒)
     * 如果start_time==end_time 则取start_time开始之后一天的数据
     * @param $arrInput
     * @return array
     */
	public static function getWorksVideoThreadForEditByDate($arrInput) {
        if(!isset($arrInput['start_time']) || empty($arrInput['start_time']) || !isset($arrInput['end_time']) || empty($arrInput['end_time'])){
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $start_time = intval($arrInput['start_time']);
        $end_time = intval($arrInput['end_time']);
        $arrWorksInfos = array();
        if($end_time - $start_time >= 86400){
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if($start_time == $end_time){
            $end_time = $end_time + 86400;
        }
        //分表查询
        $skipWeek = floor(($end_time - $start_time) / 86400 / 7) + 1;
        $firstMonday = self::getThatDayMonday($start_time);
        $arrWeek = array();
        for($weekIndex = 0; $weekIndex <= $skipWeek; $weekIndex++){
            if($firstMonday + $weekIndex * 7 * 86400 > $end_time){
                break;
            }
            $arrWeek[] = date("Ymd", $firstMonday + $weekIndex * 7 * 86400);
        }

        
        $arrPriorOneWeek = array( date('Ymd',(strtotime(min($arrWeek))-7*86400)));//时间前推7天，多查询一张表
        $arrWeek = array_merge($arrPriorOneWeek,$arrWeek);
        foreach($arrWeek as $key => $value){
            $strTableName = "video_audit_".$value;
            $arrDlInput = array('function' => 'getWorksVideoThreadForEditByDate',
                'table_name' => $strTableName,
                'start_time' => $start_time,
                'end_time' => $end_time,
                '_dbname' => 'forum_video_audit',);
            $arrOutputRet = Dl_Active_Active::execSql($arrDlInput);
            Bingo_Log::notice('call getWorksVideoThreadForEditByDate, res_count='.count($arrOutputRet['results'][0]).' req='.serialize($arrDlInput ));
            if(!$arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');

                return self::_errRet($arrOutputRet['errno']);
            }
            foreach($arrOutputRet['results'][0] as $videoinfo){
                if( !empty($videoinfo)){
                    $arrWorksInfos[] = $videoinfo;
                }
            }
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrWorksInfos);
    }
	
	/**
	 * 根据tid取到一级和二级标签,为sep提供
	 *
	 * @param  [int] $tid [description]
	 *
	 * @return [type]      [description]
	 */
	public static function getLabelByTid($arrInput)
	{
		$tid = $arrInput['thread_id'];
		if(empty($tid) || intval($tid) <= 0){
			return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$input = array("thread_ids" => array(0 => $tid,),
		               "need_abstract" => 0,
		               "forum_id" => 0,
		               "need_photo_pic" => 0,
		               "need_user_data" => 0,
		               "icon_size" => 0,
		               "need_forum_name" => 0, //是否获取吧名
		               "call_from" => "client_frs", //上游模块名
		);
		$res = Tieba_Service::call('post', 'mgetThread', $input, null, null, 'post', 'php', 'utf-8');
		if(empty($res) || Tieba_Errcode::ERR_SUCCESS != $res['errno']){
			Bingo_Log::warning('getLabelByTid call post_mgetThread failed,input['.serialize($input).'],out['.serialize($res).']');
			
			return self::errRet(Tieba_Errcode::ERR_POST_CT_FAILED_CALL_SERVICE);
		}
		$videoType = 0;
		$videoMd5 = 0;
		if(isset($res['output']['thread_list'][$tid]) && isset($res['output']['thread_list'][$tid]['video_info'])){
			$videoType = $res['output']['thread_list'][$tid]['video_info']['video_type'];
			$videoMd5 = $res['output']['thread_list'][$tid]['video_info']['video_md5'];
		}
		$data = array($tid => array('first_label' => '',
		                            'second_label' => '',),);
		// 网搜视频或者快手才有label
		if(Molib_Util_Video::TEMP_VIDEO_FROM_SERACH !== $videoType && Molib_Util_Video::TEMP_VIDEO_FROM_UGC !== $videoType && Molib_Util_Video::TEMP_VIDEO_FROM_KUAISHOU !== $videoType){
			return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $data);
		}
		$arrDlInput = array('function' => 'getLabelByTid',
		                    'video_md5' => $videoMd5,);
		$arrOutputRet = Dl_Video_Sql::execSql($arrDlInput);
		if(!$arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');
			
			return self::_errRet($arrOutputRet['errno']);
		}
		// 数据库中没有这个帖子
		if(empty($arrOutputRet['results'][0])){
			return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $data);
		}
		$videoInfo = $arrOutputRet['results'][0][0];
		// 取得一级标签
		$intVideoLabel = $videoInfo['video_label'];
		// 二级标签
		$strVideoTags = $videoInfo['tags'];
		if(isset($strVideoTags) && strval($strVideoTags) != ''){
			$data[$tid]['second_label'] = $strVideoTags;
		}
		$input = array('label_id' => $intVideoLabel);
		// 一级标签根据id获得string
		$res = Tieba_Service::call('video', 'getLabelNameById', $input, null, null, 'post', 'php', 'utf-8');
		if(empty($res) || Tieba_Errcode::ERR_SUCCESS != $res['errno']){
			Bingo_Log::warning('getLabelByTid call video_getLabelNameById failed,input['.serialize($input).'],out['.serialize($res).']');
			
			return self::errRet(Tieba_Errcode::ERR_SUCCESS, $data);
		}
		$data[$tid]['first_label'] = $res['data']['name'];
		
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $data);
	}
	
	/**
	 * 根据
	 *
	 * @param  [type] $arrInput [description]
	 *
	 * @return [type]           [description]
	 */
	public static function getLabelNameById($arrInput)
	{
		if(!isset($arrInput['label_id'])){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$labelId = intval($arrInput['label_id']);
		if($labelId <= 0){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrDlInput = array('function' => 'getLabelNameById',
		                    'label_id' => $labelId,);
		$arrOutputRet = Dl_Video_Sql::execSql($arrDlInput, 'gbk');
		if(!$arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');
			
			return self::_errRet($arrOutputRet['errno']);
		}
		if(empty($arrOutputRet['results'][0])){
			return self::_errRet(Tieba_Errcode::ERR_MODATA_EMPTY_DATA);
		}
		
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutputRet['results'][0][0]);
	}
	
	/**
	 * @desc 获取视屏贴信息
	 *
	 * @param
	 *     $arrTids
	 *
	 * @return
	 * <AUTHOR>
	 */
	public static function getVideoThreadInfoByTids($arrInput)
	{
		if(!isset($arrInput['thread_ids']) || empty($arrInput['thread_ids'])){
			return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrTids = Tieba_Service::getArrayParams($arrInput, 'thread_ids');
		$arrInput = array('thread_ids' => $arrTids,
		                  'need_abstract' => 0,
		                  'forum_id' => 0,
		                  'need_photo_pic' => 0,
		                  'need_user_data' => 0,
		                  'call_from' => 'client_frs',);
		$arrOutput = Tieba_Service::call('post', 'mgetThread', $arrInput, null, null, 'post', 'php', 'utf-8');
		if($arrOutput === false || empty ($arrOutput) || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']){
			Bingo_Log::warning('call post:mgetThread failed.[sevice_name:post][method:mgetThread] [input:'.serialize($arrInput).'] [output:'.serialize($arrOutput).']');
			
			return self::errRet($arrOutput['errno']);
		}
		//图文直播搬贴改变帖子类型为图文直播贴
		$arrThreadList = $arrOutput ['output'] ['thread_list'];
		foreach($arrThreadList as $key => $threadInfo){
			if(empty($threadInfo) || $threadInfo['is_deleted'] == 1){
				continue;
			}
			$arrThreadTypes = Tieba_Type_Thread::getTypeArray($threadInfo['thread_types']);
			$arrOutput ['output'] ['thread_list'][$key]['thread_type'] = $arrThreadTypes;
		}
		
		return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput ['output']);
	}
	
	/**
	 * @desc 获取视频上传信息
	 *
	 * @param
	 *
	 * @return
	 */
	public static function uploadVideoStatus($arrInput)
	{
		if(!isset($arrInput['video_md5']) || empty($arrInput['video_md5']) || !isset($arrInput['product']) || empty($arrInput['product']) /*|| !isset($arrInput['chunk_sum']) ||empty($arrInput['chunk_sum'])
    			|| !isset($arrInput['chunk_size']) ||empty($arrInput['chunk_size'])
    			|| !isset($arrInput['video_size']) ||empty($arrInput['video_size'])*/ || !isset($arrInput['video_len']) || empty($arrInput['video_len']) || !isset($arrInput['video_type']) || empty($arrInput['video_type'])
			//|| !isset($arrInput['is_merge']) ||empty($arrInput['is_merge'])
		){
			return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		Bingo_Log::warning("service======uploadVideoStatus");
		$fileName = $arrInput['video_len'].'_'.$arrInput['video_md5'].'.'.$arrInput['video_type'];
		$arrParamInput = array('product' => $arrInput['product'],
		                       'file_name' => $fileName,);
		Bingo_Log::warning("service======isObjectExist==arrParamInput==".var_export($arrParamInput, true));
		$arrOutput = self::mcpackCall('smallvideo', 'isObjectExist', $arrParamInput);
		Bingo_Log::warning("service======isObjectExist==arrOutput==".var_export($arrOutput, true));
		if(false === $arrOutput){
			Bingo_Log::warning("actions_video_uploadBlock call smallvideo isObjectExist fail; [input]".serialize($arrInput).";[output]".serialize($arrOutput));
			
			return self::errRet(Tieba_Errcode::ERR_MO_POST_FILE_UPLOAD_DATA_ERROR);
		}
		
		return self::errRet($arrOutput['errno'], $arrOutput['data']);
	}
	
	/**
	 * @desc IDL封面替换 add by zbo
	 *
	 * @param
	 *
	 * @return
	 */
	public static function idlCallBack($arrInput)
	{
		//call back 这块有些问题 需要打日志来监控下 by dongliang04
		Bingo_Log::warning("IDL Callback: " . json_encode($arrInput));
		//Bingo_Log::pushNotice("IDL CallBack" . serialize($arrInput));
		if(!isset($arrInput['request_id']) || empty($arrInput['request_id'])
            || !isset($arrInput['thumbnail']) || empty($arrInput['thumbnail'])){
			Bingo_Log::warning("IDL err param ".serialize($arrInput));
			return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrInfo = json_decode($arrInput['request_id'], true);
		$intThreadId = $arrInfo['thread_id'];
		$intUserId = intval($arrInfo['user_id']);
		if($intUserId == 831268735){
			Bingo_Log::warning("IDL CallBack".serialize($arrInput));
		}
        $thumbnail_url = $arrInput['thumbnail'];
        if($thumbnail_url == false){
            Bingo_Log::warning('IDL upload pic fail '.serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        //获取帖子信息
        if (!Lib_Post::getThreadInfo($intThreadId, $arrThreadInfo) || !$arrThreadInfo) {
            Bingo_Log::warning('get thread info fail, thread_id=' . $intThreadId);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $intPostId = $arrThreadInfo['first_post_id'];
		$createTime = $arrThreadInfo['create_time'];
        $ext_attr = $arrThreadInfo['video_info'];
        $old_thumbnail_url = $ext_attr['thumbnail_url'];

        $videoType = $arrThreadInfo['video_info']['video_type'];
        if ($videoType == Molib_Util_Video::TEMP_VIDEO_WORKS_FROM_PC) {//新流程都得替换封面
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }

		//发NMQ请求UEG图片服务
		//update video_info表
		$arrSaveData = array("field" => array("thumbnail_url" => strval($thumbnail_url),),
		                     "cond" => array("thread_id" => $intThreadId,
		                                     "post_id" => $intPostId,));
		$strTableName = "video_info_".intval($intUserId) % 256;
		$arrRet = Dl_Video_Video::updateVideoInfo($arrSaveData, $strTableName);
		if($arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning("IDL update video_info fail . input: ".serialize($arrSaveData).", output: ".serialize($arrRet));
		}

		//update audit表
        $intErrorNo = Tieba_Errcode::ERR_SUCCESS;
        $bolSetExtAttr = true;
        do {
            if (!Lib_Audit::startTransaction()) {
                Bingo_Log::warning('call Lib_Audit::startTransaction fail, input:['.json_encode($arrInput).']');
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
            // 查询
            $arrParams = array(
                'weekkey' => empty($arrInfo['weekkey']) ? Lib_Audit::getThatDayMonday($createTime) : $arrInfo['weekkey'],
                'cond' => array('thread_id' => $intThreadId,),
                'for_update' => true,
            );
            $arrRet = Lib_Audit::selectVideoInfo($arrParams);
            if ($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("call Lib_Audit::selectVideoInfo error. [input]:" . serialize($arrParams) . " [output]:" . serialize($arrRet));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
            if (empty($arrRet['data'])) {
                Bingo_Log::warning("can not find the video in audit db. [input]:" . serialize($arrParams) . " [output]:" . serialize($arrRet));
                $intErrorNo = Tieba_Errcode::ERR_NO_RECORD;
                break;
            }
            $arrAuditInfo = $arrRet['data'][0];
            $createTime = $arrAuditInfo['create_time'];
            $arrExtParam = json_decode($arrAuditInfo['ext_param'], true);

            // 如果审核平台中替换过封面，不更新video_info属性，仅修改备份的原始封面信息，否则更新video_info属性
            if (isset($arrExtParam['original_thumbnail_url']) && !empty($arrAuditInfo['video_cover'])) {
                $arrExtParam['original_thumbnail_url'] = $thumbnail_url;
                $arrExtParam['original_thumbnail_width'] = intval($arrInput['width']);
                $arrExtParam['original_thumbnail_height'] = intval($arrInput['height']);
                $bolSetExtAttr = false;
                $thumbnail_url = $arrAuditInfo['video_cover'];

            // 如果是其他类型的封面请求落后于Delogo封面请求到达，仅修改备份的原始封面信息，否则更新video_info属性
            } else if (isset($arrExtParam['delogo_info']['original_thumbnail_url']) && $arrInfo['type'] !== 'delogo'
                && !empty($arrAuditInfo['video_cover'])) {
                $arrExtParam['delogo_info']['original_thumbnail_url'] = $thumbnail_url;
                $arrExtParam['delogo_info']['original_thumbnail_width'] = intval($arrInput['width']);
                $arrExtParam['delogo_info']['original_thumbnail_height'] = intval($arrInput['height']);
                $bolSetExtAttr = false;
                $thumbnail_url = $arrAuditInfo['video_cover'];

            // 更新封面属性
            } else {
                foreach ($arrExtParam['ext_attr'] as $value) {
                    if ($value['key'] == 'video_info' && $value['value']) {
                        $ext_attr = is_string($value['value']) ? unserialize($value['value']) : $value['value'];
                        break;
                    }
                }

                $ext_attr['thumbnail_url'] = $thumbnail_url;
                if (isset($arrInput['width'])) {
                    $ext_attr['thumbnail_width'] = intval($arrInput['width']);
                }
                if (isset($arrInput['height'])) {
                    $ext_attr['thumbnail_height'] = intval($arrInput['height']);
                }

                foreach ($arrExtParam['ext_attr'] as &$value) {
                    if ($value['key'] == 'video_info') {
                        $value['value'] = $ext_attr;
                        break;
                    }
                }
            }

            $arrExtParam['is_idl_thumbnail_url'] = 1;
            //更新
            $arrParams = array(
                'create_time' => $createTime,
                'field' => array(
                    'video_cover' => strval($thumbnail_url),
                    'ext_param' => mysql_escape_string(json_encode($arrExtParam)),
                ),
                'cond' => array("thread_id" => $intThreadId,),
            );
            $arrRet = Lib_Audit::updateVideoInfo($arrParams);
            if ($arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("IDL update audit fail . input: " . serialize($arrParams) . ", output: " . serialize($arrRet));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
            if (!Lib_Audit::commit()) {
                Bingo_Log::warning('call Lib_Audit::commit fail, input:['.json_encode($arrInput).']');
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
        } while(0);

        if (Tieba_Errcode::ERR_SUCCESS != $intErrorNo) {
            if (!Lib_Audit::rollback()) {
                Bingo_Log::warning('call Lib_Audit::rollback fail, input:['.json_encode($arrInput).']');
            }
            return Lib_Audit::arrRet($intErrorNo);
        }

        //set贴子属性
        if ($bolSetExtAttr && !empty($ext_attr)) {
            $arrRet = Lib_Post::setExtAttr($intThreadId, $intPostId, $ext_attr);
            if($arrRet === false){
                Bingo_Log::warning('IDL set ext attr fail response:'.json_encode($arrRet));
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }
        Bingo_Log::warning("set IDL thumbnail success ! thread_id:".$intThreadId." old thumbnail_url: ".$old_thumbnail_url." new thumbnail_url: ".$thumbnail_url);

        // 删除请求记录
        if (!Tieba_Video_IDL::removeThumbnailReqRecord($intThreadId)) {
            Bingo_Log::warning('remove thumbnail request record fail, thread_id=' . $intThreadId);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
	}

	/**
	 * @desc IDL封面替换
	 *
	 * @param
	 *
	 * @return
	 */
	public static function picAddNormal($strPicBase64, $width, $height)
	{
		$picContent = base64_decode($strPicBase64);
		$arrReq = array("cmd_no" => 500,         //命令号
		                "product_token" => "forum",     //产品线名称
		                "idc_token" => "cn",        //机房名称，国内使用 cn
		                "pic_data" => $picContent, //图片内容
		);
		$ret = Bd_Pic::picCommit($arrReq);
		Bingo_Log::warning("picCommit result , the ret is ".serialize($ret));
		if($ret === false || $ret['err_no'] !== Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning("picCommit fail , the ret is ".serialize($ret));
			
			return false;
		}
		$p = array('pic_id' => $ret['pic_id'], //步骤1获取到的pic_id
		           'product_name' => "forum",//产品线名称，申请权限1得到的name
		           'pic_spec' => "eWH=$width,$height",//在线处理参数（详细说明请参考官方文档）
		);
		$params[] = $p;
		//$boolUseRal = false;
		$ret = Bd_Pic::pid2Url($params, false);
		Bingo_Log::warning("pid2Url result , the ret is ".serialize($ret));
		
		return $ret['resps'][0];
	}
	
	/**
	 * @desc IDL封面替换
	 *
	 * @param
	 *
	 * @return
	 */
	public static function picAddTest($arrInput)
	{
		$fileName = "./../../script/1.jpg";
		$strPicBase64 = base64_encode(file_get_contents($fileName));
		$picContent = base64_decode($strPicBase64);
		$arrReq = array("cmd_no" => 500,         //命令号
		                "product_token" => "forum",     //产品线名称
		                "idc_token" => "cn",        //机房名称，国内使用 cn
		                "pic_data" => $picContent, //图片内容
		);
		$ret = Bd_Pic::picCommit(picAddNormal());
		
		return $ret;
	}

    /**
     * @param $arrInput
     * @return array
     */
    public static function addGreatVideoBaijiaohao($arrInput) {
        if(empty($arrInput['app_name']) || empty($arrInput['video_url']) || empty($arrInput['cover_url'])){
            Bingo_Log::warning("error params. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrDlInput = $arrInput;
        $arrDlInput['function'] = 'addGreatVideoBaijiaohao';
        $arrDlInput['table_name'] = self::GREAT_VIDEO_BAIJIAHAO_TABLE_NAME;
        $arrDlInput['add_time'] = time();

        $arrOutputRet = Dl_Video_Sql::execSql($arrDlInput);
        if(false === $arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');
            return self::_errRet(isset($arrOutputRet['errno']) ? $arrOutputRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return array
     * 优质视频机筛和策略数据导入数据库service
     */
    public static function addGreatVideoToMis($arrInput) {
        if(empty($arrInput['thread_id'])) {
            Bingo_Log::warning("error params. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        Bingo_Log::warning(sprintf("call addGreatVideoToMis input[%s]", json_encode($arrInput)));

        $intThreadId = intval($arrInput['thread_id']);
        $strRejectField = strval($arrInput['reject_field']);
        $intTmp = intval($arrInput['video_from']);
        $intVideoSearchType = intval($arrInput['search_type']);
        $intRawSearchType = intval($arrInput['search_type']);
        $intIsVideoGood = intval($arrInput['is_video_good']);

        // isRecommendCallback
        $booIsRecommendCallback = intval($arrInput['is_recommend']);

        $intCallbackType = !empty($arrInput['callback_type']) ? intval($arrInput['callback_type']) : 0;


        $arrThreadInput = array(
            "thread_ids" => array(
                $intThreadId,
            ),
            "need_abstract" => 0,
            "forum_id" => 0,
            "need_photo_pic" => 0,
            "need_user_data" => 1,
            "icon_size" => 0,
            "need_forum_name" => 1,  //是否获取吧名
            "call_from" => "client_frs", //上游模块名,这里只能用client_frs
        );
        $arrThreadOutput = Tieba_Service::call('post', 'mgetThread', $arrThreadInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrThreadOutput || Tieba_Errcode::ERR_SUCCESS != $arrThreadOutput['errno']) {
            $arrThreadOutput = Tieba_Service::call('post', 'mgetThread', $arrThreadInput, null, null, 'post', 'php', 'utf-8');
            if (false === $arrThreadOutput || Tieba_Errcode::ERR_SUCCESS != $arrThreadOutput['errno']) {
                Bingo_Log::warning('call post/mgetThread err! [' . serialize(compact('arrThreadInput', 'arrThreadOutput')) . ']');
                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }

        $arrData = $arrThreadOutput['output']['thread_list'][$intThreadId];

        $strVideoUrl = strval($arrData['video_info']['video_url']);
        $strCoverUrl = strval($arrData['video_info']['thumbnail_url']);
        $strVideoMd5 = strval($arrData['video_info']['video_md5']);
        $strVideoType = strval($arrData['video_info']['video_type']);
        if($strVideoType >= 100 && $strVideoType <= 500){
        	if(empty($strVideoMd5)){
        		$strVideoMd5 = md5($strVideoUrl);
        	}
        }
        if(empty($strVideoMd5)) {
            Bingo_Log::warning("empty video_md5");
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        $arrTableName = self::getNeedCheckTableName();

        foreach ($arrTableName as $strTableName) {
            if(1 == $intTmp) {   //机筛,,最近六个月有相同MD5,thread_id,
                $arrDlInput['function'] = 'selectGreatVideoFromMis';
                $arrDlInput['table_name'] = $strTableName;
                $arrDlInput['video_md5'] = $strVideoMd5;
                $arrDlInput['thread_id'] = $intThreadId;
                $arrOutputRet = Dl_Video_Sql::execSql($arrDlInput);
                if(false === $arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                    Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');
                    return self::_errRet(isset($arrOutputRet['errno']) ? $arrOutputRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
                if(!empty($arrOutputRet['results'][0])) {
                    foreach ($arrOutputRet['results'][0] as $value) {
                        $intTmp = intval($value['thread_id']);
                        if($intTmp == $intThreadId) {
                            Bingo_Log::warning("repeat thread_id");
                            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
                        }
                    }
                    Bingo_Log::warning("video_md5=$strVideoMd5 or thread_id=$intThreadId,  has existed in mis");
                    $strRejectField = "机筛MD5重复";
                    $arrParam = array(
                        'thread_id'   => $intThreadId,
                        'video_md5'   => $strVideoMd5,
                    );
                    $arrRet = Service_Edit_Edit::addMd5RepeatVideoInfo($arrParam);
                    //return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
                }
            }
            else if ( 2 == $intTmp ) {
                $arrDlInput['function'] = 'selectGreatVideoFromMisStrategy';
                $arrDlInput['table_name'] = $strTableName;
                $arrDlInput['video_md5'] = $strVideoMd5;
                $arrDlInput['thread_id'] = $intThreadId;
                $arrOutputRet = Dl_Video_Sql::execSql($arrDlInput);
                if(false === $arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                    Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');
                    return self::_errRet(isset($arrOutputRet['errno']) ? $arrOutputRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }

                if(!empty($arrOutputRet['results'][0])) {
                    Bingo_Log::warning("video_md5=$strVideoMd5 or thread_id=$intThreadId,  has existed in mis");
                    return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
                }
            }
        }

        $intEditResult = Lib_Define_Edit::EDIT_RESULT_INIT;
        $intEditStatus = Lib_Define_Edit::EDIT_STATUS_READY_TO_AUTO_EDIT; // 待自动编辑状态
        $bolNeedOpTime = false;
        if(!empty($strRejectField) && $intRawSearchType != 41 && $intRawSearchType != 43 && $intRawSearchType != 44) {
            $intEditResult = Lib_Define_Edit::EDIT_RESULT_REJECT;
            $intEditStatus = Lib_Define_Edit::EDIT_STATUS_NO_NEED_TO_EDIT;
            $bolNeedOpTime = true;
        }

        //为了节省预算，有些视频入编辑库，但是不编辑，edit_status=7,特殊状态
        $arrRet = self::isNotNeedToEdit($arrData);
        if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet["errno"]){
            Bingo_Log::warning('call isNotNeedToEdit error! the output is '. serialize($arrRet) . 'the input is '. serialize($arrData));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        if (1 == $arrRet['data']) {
            $intEditStatus = Lib_Define_Edit::EDIT_STATUS_NO_NEED_TO_EDIT_TEMPORARILY; //视频资源入编辑库但是不在编辑平台展示,之后会将此部分视频进行回溯
        }

        $intUserId = intval($arrData['user_id']);
        $arrServiceInput = array(
            'user_id' => $intUserId,
        );
        $strServiceName = "user";
        $strServiceMethod = "mgetUserDataEx";
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,"post",null,"utf-8");
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            Bingo_Log::warning("call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrUserData = $arrOutput['user_info'][$intUserId];
        $bolIsPGC = (!empty($arrData['video_channel_info']) || !empty($arrUserData['god']) || $strVideoType == 4);

        if(1 == $intTmp) {   //机筛数据
            if($bolIsPGC) {
                $intVideoFrom = 10;  //pgc机筛
            }
            else {
                $intVideoFrom = 40; //ugc机筛
            }
        }
        else {
            if($bolIsPGC){
                $intVideoFrom = 20;  //pgc策略
            }
            else {
                $intVideoFrom = 30; //ugc策略
            }
        }

        // is recommend callback

        if($booIsRecommendCallback == 1){
            $intVideoFrom = Lib_Define_Edit::VIDEO_FROM_RECOMMEND;
        }

        $intForumId = intval($arrData['forum_id']);
        //支持无吧贴
        if($intForumId != 0){
	        $arrServiceInput = array(
	            'forum_id' => $intForumId,
	        );
	        $strServiceName = "forum";
	        $strServiceMethod = "getForumDir";
	        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,"post",null,"utf-8");
	        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
	            Bingo_Log::warning("call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
	            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
	        }
            $arrForumDir = $arrOutput['output'];
	        $strLevel1Name = $arrForumDir['level_1_name'];
	        $strLevel2Name = $arrForumDir['level_2_name'];   	
        }else{
        	//无吧贴1 2级目录直接为空
        	$strLevel1Name = '';
        	$strLevel2Name = '';
        }
        /*
         * 不在根据吧映射一级分类
        if(isset(Util_Tag::$arrForumDir2ToMap[$strLevel2Name])) {
            $strSecDir = Util_Tag::$arrForumDir2ToMap[$strLevel2Name];
        }
        else {
            $strSecDir = '广告';
        }
        $strFirstDir = Util_Tag::$arrSecDirMapFirstDir[$strSecDir];
         */
        $strFirstDir = '未分类';



        $arrServiceInput = array(
            "thread_id" => $intThreadId, //帖子id
            "offset" => 0,
            "res_num" => 10,
            "see_author" => 1,
            "has_comment" => 0,
            "has_mask" => 1,
            "has_ext" => 1,
            "need_set_pv" => 0,
            "structured_content" => 0,
        );
        $strServiceName = "post";
        $strServiceMethod = "getPostsByThreadId";
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,"post",null,"utf-8");
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            Bingo_Log::warning("call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrPostInfo = $arrOutput['output']['output'][0]['post_infos'];
        $strDesc = strval($arrPostInfo[1]['content']);   //帖子作者的第二楼
        
        //过滤主端故事视频贴
        if( intval($arrOutput['output']['output'][0]['thread_type']) == Tieba_Type_Thread::STORY_VIDEO_THREAD ){
            Bingo_Log::pushNotice('isStory', 1);
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        //Bingo_Log::warning("desc===============>" . var_export($strDesc, true));  // for test

        $intThumbnailHeight = intval($arrData['video_info']['thumbnail_height']);
        $intThumbnailWidth = intval($arrData['video_info']['thumbnail_width']);

        $intPortraitCoverWidth = 0;
        $intPortraitCoverHeight = 0;
        $strPortraitCoverUrl = "";
        if($intThumbnailHeight > $intThumbnailWidth){
        	
            Bingo_Log::warning("need portrait.");
            $intPortraitCoverHeight = $intThumbnailHeight;
            $intPortraitCoverWidth = $intThumbnailWidth;
            $strPortraitCoverUrl = $strCoverUrl;
        	// $arrOutput = getimagesize($strCoverUrl);
        	// if(!empty($arrOutput) && $arrOutput[0] < $arrOutput[1]){
        	// 	$strPortraitCoverUrl = $strCoverUrl;
	        // 	$intPortraitCoverWidth = $arrOutput[0];
	        // 	$intPortraitCoverHeight = $arrOutput[1];
        	// }
        }
        if($intThumbnailWidth <= 2048) {   //切图有宽高尺寸限制 http://man.baidu.com/ksarch/common/pic/#图片在线处理_说明
            if($intThumbnailWidth <= 980) {
                $intThumbnailNeedWidth = $intThumbnailWidth;
            }
            else {
                $arrOkSize = array(980, 1000,1024,1044,1050,1080,1100,1140,1152,1200,1280,1300,1360,1366,1400,1440,1458,1500,1600,1680,1856,1920,2026,2048);
                foreach ($arrOkSize as $key => $intSize) {
                    if(intval($intSize) <= $intThumbnailWidth) {
                        $intThumbnailNeedWidth = $intSize;
                    }
                }
            }
            $intThumbnailNeedHeight = ceil(($intThumbnailNeedWidth / 16.0) * 9);
            $strSwitchCoverUrl = Util_Pic::extUrlToInnerUrl($strCoverUrl, "awhcrop=$intThumbnailNeedWidth,$intThumbnailNeedHeight");
        }



        if(!empty($strSwitchCoverUrl)) {
            Bingo_Log::warning("use switch cover url");
            $strCoverUrl = $strSwitchCoverUrl;
            $intThumbnailHeight = $intThumbnailNeedHeight;
            $intThumbnailWidth = $intThumbnailNeedWidth;
        }


        $strInsertTableName = self::VIDEO_EDIT_TABLE_PREFIX . date("Ym", intval($arrData['create_time']));

        // audit_flag

        $intAuditFlag = self::getAuditFlagByThreadId($intThreadId);

        if (empty($arrData['title'])) {
            Bingo_Log::warning(sprintf("title is empty! tid[%s] info[%s]", $intThreadId, json_encode($arrData)));
        }

        $arrDlInput = array(
            'function' => 'addGreatVideoToMis',
            'table_name' => $strInsertTableName,
            'forum_id' => intval($arrData['forum_id']),
            'forum_name' => strval($arrData['forum_name']),
            'user_id'  => intval($arrData['user_id']),
            'thread_id' => $intThreadId,
            'title' => strval($arrData['title']),
            'create_time' => time(),
            'video_duration' => intval($arrData['video_info']['video_duration']),
            'video_length' => (intval($arrData['video_info']['video_length']) > 0  ? intval($arrData['video_info']['video_length']) : intval($arrData['video_info']['video_size'])),
            'video_type' => intval($arrData['video_info']['video_type']),
            'video_url' => $strVideoUrl,
            'video_md5' => $strVideoMd5,
            'video_cover' => $strCoverUrl,
            'portrait_video_cover' => $strPortraitCoverUrl,
            'video_from' => $intVideoFrom,
            'thread_create_time' => intval($arrData['create_time']),
            'edit_status' => $intEditStatus,  //未编辑
            'edit_result'  => $intEditResult,
            'dispatch_status'  => 0,
            'bjh_user_name'  => '',
            'priority'  => 0,
            'ext2'  => 0,
            'ext3'  => '',
            'reject_field' => $strRejectField,
            'level_1_name' => $strLevel1Name,
            'level_2_name' => $strLevel2Name,
            'fir_category' => $strFirstDir,
            'sec_category' => $strSecDir,
            'portrait_cover_height' => $intPortraitCoverHeight,
            'portrait_cover_width' => $intPortraitCoverWidth,
            'thumbnail_width' => $intThumbnailWidth,
            'thumbnail_height' => $intThumbnailHeight,
            'description'  => $strDesc,
            'tag' => strval($arrInput['tag']),
            'video_site_id' => 0,
            'video_site_name' => '',
            'search_type' => $intVideoSearchType,
            'callback_type' => $intCallbackType,
            'audit_flag' => $intAuditFlag,
            'is_video_good' => $intIsVideoGood,
            'ext_info'  => '',
        );

        if ($bolNeedOpTime) {
            $arrDlInput['op_time'] = time();
            $arrDlInput['op_uname'] = 'machinefilter';
            $arrDlInput['function'] = 'addGreatVideoToMisWithOp';
        }

        $categ =array(
            "对口型", 
            "趣玩", 
            "恶搞", 
            "牛人集锦", 
            "逗比", 
            "整蛊");
        $intRandNum = rand(0,5);
        if( $arrDlInput['video_type'] == 206){
            $arrDlInput['fir_category'] = '搞笑趣味'; 
            $arrDlInput['edit_status'] = 6; 
            $arrDlInput['edit_result'] = 1; 
            $arrDlInput['sec_category'] = $categ[$intRandNum];
        }

        //added by ligengyong//
        do{
            //有可能
            if($intThreadId < 1 ) {
                Bingo_Log::warning('thread id empty .'.$intThreadId);
                break;
            }
            $input = array(
                'tids' => array(
                    $intThreadId,
                ),
            );
            $arrOutput = Tieba_Service::call('videocp','mgetTidInfo',$input,null,null,"post",null,"utf-8");
            if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                Bingo_Log::warning("call videocp mgetTidInfo fail. input:[".serialize($input)."]; output:[".serialize($arrOutput)."]");
                break;
            }
            $data = $arrOutput['data'][$intThreadId];
            if(empty($data)){
                Bingo_Log::warning("call videocp mgetTidInfo is not from xiaoying. input:[".serialize($input)."]; output:[".serialize($arrOutput)."]");
                break;
            }
            Bingo_Log::pushNotice('xiaoying_uid', $arrData['user_id']);
            $arrDlInput['video_site_id'] = intval($data['video_site_id']);
            $arrDlInput['video_site_name'] = trim($data['video_from']);
            
            if($data['video_from'] == '小影'){
                $arrDlInput['priority'] = 100; //100
                $arrDlInput['edit_status'] = 2; //站点细分 10.小影
                $arrDlInput['edit_result'] = 0; //站点细分 10.小影
                if(!empty($data['title'])){
                    $arrDlInput['ext3'] = $data['title'];
                }
                if(!empty($data['tags'])){
                    $arrDlInput['tag'] = $data['tags'];
                }
                $uid = intval($arrData['user_id']);
                if(!empty(Util_Tag::$mapFromTB2BJHofXiaoYing[$uid])){
                    $arrDlInput['bjh_user_name'] = Util_Tag::$mapFromTB2BJHofXiaoYing[$uid];
                }
                if(!empty(Util_Tag::$mapFromTB2BJHofXiaoYing1Category[$uid]) &&
                    !empty(Util_Tag::$mapFromTB2BJHofXiaoYing2Category[$uid])){
                    $arrDlInput['fir_category'] = Util_Tag::$mapFromTB2BJHofXiaoYing1Category[$uid];
                    $arrDlInput['sec_category'] = Util_Tag::$mapFromTB2BJHofXiaoYing2Category[$uid];
                }
            } else if($data['video_from'] == 'msspFeeds'){
                if(!empty($data['tags'])){
                    $arrDlInput['tag'] = $data['tags'];
                }
                if(isset(Util_Tag::$strFirstMsspFeeds[$data['sub_category']])){
                    $data['sub_category'] = Util_Tag::$strFirstMsspFeeds[$data['sub_category']];
                }
                if (isset(Util_Tag::$arrSecDirMapFirstDir[$data['sub_category']])){
                    $arrDlInput['fir_category'] = Util_Tag::$arrSecDirMapFirstDir[$data['sub_category']];   
                    $arrDlInput['sec_category'] = $data['sub_category'];
                }
            } else if ($data['video_from'] == 'videomatelib') {
                if(!empty($data['tags'])){
                    $arrDlInput['tag'] = $data['tags'];
                }
                if (isset(Util_Tag::$arrVideoMateLibFirDirMap[$data['top_category']])) {
                    $arrDlInput['fir_category'] = Util_Tag::$arrVideoMateLibFirDirMap[$data['top_category']];
                    $arrDlInput['sec_category'] = strval(Util_Tag::$arrVideoMateLibSecDirMap[$data['top_category']][$data['sub_category']]);
                }
            }
        }
        while(0);

        //end added by ligengyong //

        //create priority
        $arrPriInput = array(
        	'pass' => empty($strRejectField) ? 1 : 0,
        	'from' => $intTmp, 
        	'user_id' => intval($arrData['user_id']),
        	'type' => $intVideoSearchType,
            'video_type' => intval($arrData['video_info']['video_type']),
        );
        //这里不进行白名单的查找,所以把user_id传过去
		$arrPriOut = Lib_Define_Edit::getPriority($arrPriInput);
		$arrDlInput['priority'] = $arrPriOut['priority'];
		$arrDlInput['search_type'] = $arrPriOut['search_type'];


        if($intAuditFlag == 3 || $intAuditFlag == 2 ){
            $arrDlInput['priority'] = 70;
        }
        //end create priority

        // change search_type here!
        $intFinallySearchType = Lib_Edit::getInsertSearchTypeWithBiases(intval($arrDlInput['search_type']), intval($intThreadId));
        $arrDlInput['search_type'] = $intFinallySearchType !== null ? $intFinallySearchType : $arrDlInput['search_type'];

        $arrOutputRet = Dl_Video_Sql::execSql($arrDlInput);
        Bingo_Log::warning(sprintf("call Dl_Video_Sql::execute. input[%s] output[%s]", json_encode($arrDlInput), json_encode($arrOutputRet)));
        if(false === $arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');
            return self::_errRet(isset($arrOutputRet['errno']) ? $arrOutputRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return array
     * 优质视频机筛和策略数据导入数据库service
     */
    public static function addWorksVideoToMis($arrInput) {

        // 从审核表中获取预分配tid
        $predistributedThreadId = $arrInput['thread_id'];
        $intVideoSearchType = intval($arrInput['search_type']);
        $intIsVideoGood = intval($arrInput['is_video_good']);
        $intCallbackType = !empty($arrInput['callback_type']) ? intval($arrInput['callback_type']) : 0;
        $strVideoUrl = strval($arrInput['video_url']);
        $strCoverUrl = strval($arrInput['video_cover']);
        $strVideoMd5 = strval($arrInput['video_md5']);
        $strVideoType = strval($arrInput['video_type']);
        $strRejectField = strval($arrInput['reject_field']);
        $strRejectReason = isset($arrInput['reject_reason'])? strval($arrInput['reject_reason']) : '';
        $strDeleteReason = strval($arrInput['delete_reason']);
        $intAuditStatus  = intval($arrInput['audit_status']);
        
        // 获取video 信息和作品信息
        $extParam = $arrInput['ext_param'];
        $extParamArr = json_decode($extParam, 1);
        $videoInfo = array();
        $worksInfo = array();
        foreach ($extParamArr['ext_attr'] as $attr) {
            if ($attr['key'] == 'video_info') {
                $videoInfo = $attr['value'];
            }

            if ($attr['key'] == 'works_info') {
                $worksInfo = $attr['value'];
            }
        }
        if (empty($worksInfo) || !isset($worksInfo['is_works']) || 1 != intval($worksInfo['is_works'])) {
            Bingo_Log::warning("this is not video works thread, input=" . json_encode($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $tagList = $worksInfo['tag_list'];

        if(empty($strVideoMd5)) {
            Bingo_Log::warning("empty video_md5");
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        // 初始化编辑状态
        $intEditResult = Lib_Define_Edit::EDIT_RESULT_INIT;
        $intEditStatus = Lib_Define_Edit::EDIT_STATUS_READY; // 待编辑
        if(!empty($strRejectField)) {
            $intEditResult = Lib_Define_Edit::EDIT_RESULT_REJECT;
            $intEditStatus = Lib_Define_Edit::EDIT_STATUS_NO_NEED_TO_EDIT;
        }

        $intForumId = intval($arrInput['forum_id']);
        //支持无吧贴
        if($intForumId != 0){
            $arrServiceInput = array(
                'forum_id' => $intForumId,
            );
            $strServiceName = "forum";
            $strServiceMethod = "getForumDir";
            $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null,null, "post", null, "utf-8");
            if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                Bingo_Log::warning("call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $arrForumDir = $arrOutput['output'];
            $strLevel1Name = $arrForumDir['level_1_name'];
            $strLevel2Name = $arrForumDir['level_2_name'];
        }else{
            //无吧贴1 2级目录直接为空
            $strLevel1Name = '';
            $strLevel2Name = '';
        }

        $intThumbnailHeight = intval($videoInfo['thumbnail_height']);
        $intThumbnailWidth = intval($videoInfo['thumbnail_width']);
        $intPortraitCoverWidth = 0;
        $intPortraitCoverHeight = 0;
        $strPortraitCoverUrl = "";
        if($intThumbnailHeight > $intThumbnailWidth){
            Bingo_Log::warning("need portrait.");
            $intPortraitCoverHeight = $intThumbnailHeight;
            $intPortraitCoverWidth = $intThumbnailWidth;
            $strPortraitCoverUrl = $strCoverUrl;
        }
        if($intThumbnailWidth <= 2048) {   //切图有宽高尺寸限制 http://man.baidu.com/ksarch/common/pic/#图片在线处理_说明
            if($intThumbnailWidth <= 980) {
                $intThumbnailNeedWidth = $intThumbnailWidth;
            }
            else {
                $arrOkSize = array(980, 1000,1024,1044,1050,1080,1100,1140,1152,1200,1280,1300,1360,1366,1400,1440,1458,1500,1600,1680,1856,1920,2026,2048);
                foreach ($arrOkSize as $key => $intSize) {
                    if(intval($intSize) <= $intThumbnailWidth) {
                        $intThumbnailNeedWidth = $intSize;
                    }
                }
            }
            $intThumbnailNeedHeight = ceil(($intThumbnailNeedWidth / 16.0) * 9);
            $strSwitchCoverUrl = Util_Pic::extUrlToInnerUrl($strCoverUrl, "awhcrop=$intThumbnailNeedWidth,$intThumbnailNeedHeight");
        }

        if(!empty($strSwitchCoverUrl)) {
            Bingo_Log::warning("use switch cover url");
            $strCoverUrl = $strSwitchCoverUrl;
            $intThumbnailHeight = $intThumbnailNeedHeight;
            $intThumbnailWidth = $intThumbnailNeedWidth;
        }

        $strInsertTableName = self::VIDEO_EDIT_TABLE_PREFIX . date("Ym", intval($arrInput['create_time']));

        // audit_flag
        $intAuditFlag = $arrInput['audit_flag'];
        if (empty($arrInput['title'])) {
            Bingo_Log::warning(sprintf("title is empty! tid[%s] info[%s]", $predistributedThreadId, json_encode($arrInput)));
        }

        $arrDlInput = array(
            'function' => 'addGreatVideoToMis',
            'table_name' => $strInsertTableName,
            'forum_id' => intval($intForumId),
            'forum_name' => strval($arrInput['forum_name']),
            'user_id'  => intval($arrInput['user_id']),
            'thread_id' => $predistributedThreadId,
            'title' => strval($arrInput['title']),
            'create_time' => time(),
            'video_duration' => intval($videoInfo['video_duration']),
            'video_length' => (intval($videoInfo['video_length']) > 0  ? intval($videoInfo['video_length']) : intval($videoInfo['video_size'])),
            'video_type' => intval($videoInfo['video_type']),
            'video_url' => $strVideoUrl,
            'video_md5' => $strVideoMd5,
            'video_cover' => $strCoverUrl,
            'portrait_video_cover' => $strPortraitCoverUrl,
            'video_from' => 0,
            'thread_create_time' => intval($arrInput['create_time']),
            'edit_status' => $intEditStatus,  //未编辑
            'edit_result'  => $intEditResult,
            'dispatch_status'  => 0,
            'bjh_user_name'  => '',
            'priority'  => 0,
            'ext2'  => 0,
            'ext3'  => '',
            'reject_field' => $strRejectField,
            'level_1_name' => $strLevel1Name,
            'level_2_name' => $strLevel2Name,
            'fir_category' => isset($tagList['first']) ? $tagList['first'] : '',
            'sec_category' => isset($tagList['second']) ? $tagList['second'] : '',
            'portrait_cover_height' => $intPortraitCoverHeight,
            'portrait_cover_width' => $intPortraitCoverWidth,
            'thumbnail_width' => $intThumbnailWidth,
            'thumbnail_height' => $intThumbnailHeight,
            'description'  => '',
            'tag' => isset($tagList['tag']) ? $tagList['tag'] : '',
            'video_site_id' => 0,
            'video_site_name' => '',
            'search_type' => $intVideoSearchType,
            'callback_type' => $intCallbackType,
            'audit_flag' => $intAuditFlag,
            'is_video_good' => $intIsVideoGood,
            'ext_info'  => json_encode(array("audit_del_msg" => $strDeleteReason)),
        );

        if($intAuditStatus == Util_Const::WORKS_SHIELD_WORDS_PASS){ //作品命中屏蔽词，记录命中了哪些屏蔽词
            $arrDlInput['function']      = 'addGreatVideoEditToMis';
            $arrDlInput['reject_reason'] = $strRejectReason;
            Bingo_Log::notice('call addGreatVideoEditToMis , input='. serialize($arrDlInput));
        }
        
        //获取作品优先级
        $arrPriInput = array(
            'user_id' => intval($arrInput['user_id']),
        );
        $arrPriOut = Lib_Define_Edit::getWorksPriority($arrPriInput);
        $arrDlInput['priority'] = $arrPriOut['priority'];

        $arrOutputRet = Dl_Video_Sql::execSql($arrDlInput);
        if(false === $arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');
            return self::_errRet(isset($arrOutputRet['errno']) ? $arrOutputRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * @param $intThreadId
     * @return int
     */
    public static function getAuditFlagByThreadId($intThreadId){

        $intAuditFlag = 0;
        if($intThreadId <= 0){
            Bingo_Log::warning("thread_id is 0 !");
            return $intAuditFlag;
        }

        $arrVideoInput['thread_id'] = $intThreadId;
        $arrVideoOut = Service_Video_Video::getVideoAuditInfoByThreadId($arrVideoInput);
        if(false === $arrVideoOut || Tieba_Errcode::ERR_SUCCESS != $arrVideoOut["errno"]) {
            $strLog = "call Service_Video_Video::getVideoAuditInfoByThreadId fail. input:[".serialize($arrVideoInput)."]; output:[".serialize($arrVideoOut)."]";
            Bingo_Log::warning($strLog);
            return $intAuditFlag;
        }

        $arrVideoInfo = $arrVideoOut['data']['audit_info'];
        $intAuditFlag = $arrVideoInfo['audit_flag'] == 0 ? 0 : $arrVideoInfo['audit_flag'];
        return $intAuditFlag;
    }

    /**
     * @return array
     */
    private static function getNeedCheckTableName() {
        $arrCheckTable = array();
        $strNowYearMonth = date("Ym");
        $tempDate = $strNowYearMonth;
        $intNum = 0;
        while ($intNum <= 5) {  //最近六个月
            $arrCheckTable[] = self::VIDEO_EDIT_TABLE_PREFIX. $tempDate;

            if (substr($tempDate, -2) == '01') {
                $tempYear = substr($tempDate, 0, 4);
                $tempDate = strval(intval($tempYear) - 1) . "12";
            } else {
                $tempDate = strval(intval($tempDate) - 1);
            }

            $intNum = $intNum + 1;
        }
        return $arrCheckTable;
    }


    /**
     * @param $arrTime
     * @return array
     */
    private static function getDataFromTableName($arrTime) {
        sort($arrTime);
        $intLeftTime = $arrTime[0];
        $intRightTime = end($arrTime);
        
        //统一时间获取方法
        $arrParams = array(
            'start_time' => $intLeftTime,
            'end_time' => $intRightTime,
        );
        return Service_Dispatch_VideoEdit::getTableNameByEndTime($arrParams);
    }

    /**
     * @param $arrTime
     * @return array
     */
    private static function getDataFromTableNameForRec($arrTime) {
        sort($arrTime);
        $intLeftTime = $arrTime[0];
        $intRightTime = end($arrTime);

        //统一时间获取方法
        $arrParams = array(
            'start_time' => $intLeftTime,
            'end_time' => $intRightTime,
        );
        return Service_Dispatch_VideoEdit::getTableNameByEndTimeForRec($arrParams);
    }

    /**
     * 编辑后台 查询时关于分页的信息 
     * 主要是针对原来的逻辑做了一次优化，精简了代码
     * optimized the original implementation， simply the codes
     * @param  [type] &$conds   [description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    private static function getVideoEditPageInfo(&$conds, &$order, $arrInput){
    	$arrPageFlag = $arrInput['page_flag'];
    	$intIsPageUp = $arrInput['is_page_up'];
    	$intSortType = (int)$arrInput['thread_create_time_sort_type'];
    	$intPn = $arrInput['pn'];

    	$arrFieldMap = self::$_arrSortFieldMap;

    	$arrOrderMap = self::$_arrSqlSortTypeMap;
    	$arrOperMap = array(
    		0 => array(
    			1 => '>',
    			0 => '<',
    		),
    		1 => array(
    			1 => '>=',
    			0 => '<=',
    		), 
    		2 => array(
    			1 => '<=',
    			0 => '>=',
    		), 
    	);
    	$field = $arrFieldMap[$intSortType];
    	$order = $arrOrderMap[$intSortType];
    	$oper  = $arrOperMap[$intSortType][$intIsPageUp];
    	$order = " $field  $order ";
    	if($intPn > 1){
    		$conds[] = " $field $oper ". intval($arrPageFlag[$field]);
    	}
    }
    /**
     * [generateVideoEditCond description]
     * generate the $conds and $order of ` WHERE $conds ORDER BY $order `
     * but without `LIMIT $limit , $range`
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function genVideoEditCond($arrInput){
    	//$intPs = $arrInput['ps'];
    	$conds = array();
    	$times = array();
    	$order = ' ';
    	self::getVideoEditPageInfo($conds, $order, $arrInput);
    	
        Util_Param::getTimeRange($conds, $arrInput, 'thread_create_time',$times);
        //Util_Param::getArray($conds, $arrInput, 'page_flag');
        Util_Param::getInteger($conds, $arrInput, 'user_id');
        Util_Param::getString($conds, $arrInput, 'bjh_user_name');
        Util_Param::getInteger($conds, $arrInput, 'edit_status');
        Util_Param::getInteger($conds, $arrInput, 'edit_result');
        Util_Param::getInteger($conds, $arrInput, 'dispatch_status');
        Util_Param::getArray($conds, $arrInput, 'video_from');
        Util_Param::getString($conds, $arrInput, 'fir_category');
        Util_Param::getString($conds, $arrInput, 'sec_category');
        Util_Param::getString($conds, $arrInput, 'video_quality');
        Util_Param::getInteger($conds, $arrInput, 'is_video_square');
        Util_Param::getTimeRange($conds, $arrInput, 'video_duration');
        Util_Param::getStringLike($conds, $arrInput, 'tag');
        Util_Param::getStr($conds, $arrInput, 'level_1_name');
        Util_Param::getStr($conds, $arrInput, 'level_2_name');
        Util_Param::getStr($conds, $arrInput, 'op_uname');
        Util_Param::getTimeRange($conds, $arrInput, 'video_score');
        Util_Param::getTimeRange($conds, $arrInput, 'edit_finish_time', $times);
        Util_Param::getTimeRange($conds, $arrInput, 'create_time', $times);
        Util_Param::getStr($conds, $arrInput, 'reject_field');
        Util_Param::getStr($conds, $arrInput, 'reject_reason');
        Util_Param::getTimeRange($conds, $arrInput, 'pre_sync_time', $times);
        Util_Param::getInteger($conds, $arrInput, 'thread_id');
        Util_Param::getStr($conds, $arrInput, 'title');
        Util_Param::getInteger($conds, $arrInput, 'video_site_id');
        Util_Param::getInteger($conds, $arrInput, 'video_type');
        if(empty($conds)){
            $strConds = ' 1=1 ';
        }
        $strConds = join(' AND ', $conds);
        $arrTableName = array();
        if(!empty($times)){
        	$arrTableName = self::getDataFromTableName($times);
        }
        $arrRet =  array(
        	'conds'  => $strConds,
        	'tables' => $arrTableName,
        	'order'  => $order,
        	'times'   => $times,
        );
        return $arrRet;
    }
    /**
     * [getVideoEditList description]
     * new version of selectVideoFromMis
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getVideoEditList($arrInput) {
        $intPn       = intval($arrInput['pn']);
    	$intPs       = (int)$arrInput['ps'];
        $intNeedCnt  = isset($arrInput['need_total_count']) ? intval($arrInput['need_total_count']) : 0;
        $intNeedData = isset($arrInput['need_data']) ? intval($arrInput['need_data']) : 0;
        $intNeedHide = isset($arrInput['need_hide']) ? intval($arrInput['need_hide']) : 0;
        $intSortType = (int)$arrInput['thread_create_time_sort_type'];
    	
    	// get condition of the search
    	// get order of the search
    	$arrPrefix    = Service_Video_Video::genVideoEditCond($arrInput);
    	$strConds     = $arrPrefix['conds'];
    	$arrTableName = $arrPrefix['tables'];
    	$strOrder     = $arrPrefix['order'];
    	$arrTime      = $arrPrefix['times'];
    	if(empty($arrTableName)) {
            Bingo_Log::warning("have no table(table name empty). arrTime [" . serialize($arrTime) . "]");
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        Bingo_Log::warning("sql arrTableName. [" . serialize($arrTableName) . "]");

        // start: get multi-call input
        $arrRetData = array();
        $objMultiX = new Tieba_Multi('getVideoEditList_multicall');
        if(1 == $intNeedCnt) {
            $objMultiParams['video']['selectVideoFromMisAllTotalCount'] = array(
                'tables'    => $arrTableName,
                'condition' => $strConds,
            );
        }
        if(1 == $intNeedData) {
            $objMultiParams['video']['selectAllVideoFromMisData'] = array(
                'tables'    => $arrTableName,
                'condition' => $strConds,
                'order_by'  => $strOrder,
                'limit'     => $intPs,
            );
        }

        foreach ($objMultiParams as $serviceName => $serviceCalls) {
            foreach ($serviceCalls as $method => $serviceCall) {
                $arrInput = array(
                    "serviceName" => $serviceName,
                    "method" => $method,
                    'ie' => 'utf-8',
                    "input" => $serviceCall,
                );
                $objMultiX->register($method, new Tieba_Service($serviceName), $arrInput);
            }
        }
        // end: get multi-call input
        Bingo_Timer::start('getVideoEditList_multi');
        $arrMultiOutput = $objMultiX->call();
        Bingo_Timer::end('getVideoEditList_multi');

        // start: treat multi-call output
        if(1 == $intNeedCnt) {
            $arrOutput = $arrMultiOutput['selectVideoFromMisAllTotalCount'];
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $arrServiceInput = $objMultiParams['video']['selectVideoFromMisTotalCount'];
                $strLog = "call video/selectVideoFromMisTotalCount fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::fatal($strLog);
                return self::_errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
            }
            //Bingo_Log::warning("+++>" . var_export($arrOutput, true)); // for test
            $arrRetData['page']['count'] = ($intPn - 1) * $intPs + intval($arrOutput['data']['total_count']);
            $arrRetData['page']['pn'] = $intPn;
            $arrRetData['page']['ps'] = $intPs;
            $arrRetData['hasMore'] = (intval($arrOutput['data']['total_count']) > ($intPn * $intPs)) ? 1 : 0;
        }

        if(1 == $intNeedData) {
            $arrOutput = $arrMultiOutput['selectAllVideoFromMisData'];
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $arrServiceInput = $objMultiParams['video']['selectAllVideoFromMisData'];
                $strLog = "call video/selectAllVideoFromMisData fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::fatal($strLog);
                return self::_errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
            }
            $arrList = $arrOutput['data'];
            $strSortField = self::$_arrSortFieldMap[$intSortType];
            $strSortOrder = self::$_arrSortTypeMap[$intSortType];
            $arrSort = array();
            foreach ($arrList as $key => $value) {
                $arrSort[$key] = intval($value[$strSortField]);
            }
            array_multisort($arrSort, $strSortOrder, $arrList);
            $arrRetData['rows'] = array_slice($arrList, 0, $intPs);
        }
        // end: treat multi-call output


        Bingo_Timer::start("selectVideoFromMisData_mgetUserData");
        if( !empty($arrRetData['rows']) ){
            $arrUids     = array();
            $arrUInfo    = array();
            $arrTids     = array();
            $arrMaskInfo = array();
            foreach($arrRetData['rows'] as $item){
                $intUid = intval($item['user_id']);
                $arrUids[] = $intUid;
                $intTid = intval($item['thread_id']);
                $arrTids []= $intTid;
            }
            if( !empty($arrUids) ){
            	$arrUids = array_unique($arrUids);
                $arrParams = array(
                    'user_id' => $arrUids,
                );
                $arrRet = Tieba_Service::call('user', 'mgetUserData', $arrParams, null, null, 'post', 'php', 'utf-8');
                if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                    Bingo_Log::warning(__FUNCTION__.' service user.mgetUserData error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                }else{
                    $arrUInfo = $arrRet['user_info'];
                }
            }
            if(!empty($arrTids) && $intNeedHide){
            	$arrTids = array_unique($arrTids);
            	$arrMaskInfo = Util_Post::getMaskInfo($arrTids);
            }
            foreach($arrRetData['rows'] as &$item ){
                $intUid = intval($item['user_id']);
                $intTid = intval($item['thread_id']);
                $item['user_name'] = strval($arrUInfo[$intUid]['user_name']);
                $god = isset($arrUInfo[$intUid]['god']) ? $arrUInfo[$intUid]['god'] : array();
                if( !empty($god) ){
                    $item['is_god'] = 1;
                    $item['user_nickname'] = strval($arrUInfo[$intUid]['user_nickname']);
                }else{
                    $item['is_god'] = 0;
                    $item['user_nickname'] = '';
                }
                if($intNeedHide){
                	$item['is_hide'] = (int)$arrMaskInfo[$intTid]['is_hide'];
                }
            }
        }
        Bingo_Timer::end("selectVideoFromMisData_mgetUserData");

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }

    /**
     * @param $arrInput
     * @return array
     * 视频编辑后台搜索功能service
     */
    public static function selectVideoFromMis($arrInput) {   //UI做参数合格检查,这里不做参数检查

        $intPn                     = intval($arrInput['pn']);
        $intPs                     = intval($arrInput['ps']);
        $intOffset = ($intPn - 1) * $intPs;
        $intOpUid                  = intval($arrInput['op_uid']);
        $intEditFinishTimeFrom     = intval($arrInput['thread_create_time_from']);
        $intEditFinishTimeTo       = intval($arrInput['thread_create_time_to']);
        $intEditFinishTimeSortType = intval($arrInput['thread_create_time_sort_type']);/////////////
        $intUserId                 = intval($arrInput['user_id']);
        $strBjhUserName            = strval($arrInput['bjh_user_name']);
        $intEditStatus             = intval($arrInput['edit_status']);
        $intEditResult             = intval($arrInput['edit_result']);
        $intDispatchStatus         = intval($arrInput['dispatch_status']);
        $arrVideoFrom              = $arrInput['video_from'];
        $strFirCategory            = strval($arrInput['fir_category']);
        $strSecCategory            = strval($arrInput['sec_category']);
        $intVideoQuality           = strval($arrInput['video_quality']);
        $intIsVideoSquare          = intval($arrInput['is_video_square']);
        $intVideoDurationFrom      = intval($arrInput['video_duration_from']);
        $intVideoDurationTo        = intval($arrInput['video_duration_to']);
        $strTag                    = strval($arrInput['tag']);
        $strLevel1Name             = strval($arrInput['level_1_name']);
        $strLevel2Name             = strval($arrInput['level_2_name']);
        $strOpUName                = strval($arrInput['op_uname']);
        $intVideoScoreFrom         = intval($arrInput['video_score_from']);
        $intVideoScoreTo           = intval($arrInput['video_score_to']);
        $intOpTimeFrom             = intval($arrInput['edit_finish_time_from']);
        $intOpTimeTo               = intval($arrInput['edit_finish_time_to']);
        $strRejectFiled            = strval($arrInput['reject_field']);
        $strRejectReason           = strval($arrInput['reject_reason']);
        $intPreSyncTimeFrom        = intval($arrInput['pre_sync_time_from']);
        $intPreSyncTimeTo          = intval($arrInput['pre_sync_time_to']);
        $intIsPageUp               = intval($arrInput['is_page_up']);
        $create_time_from          = intval($arrInput['create_time_from']);    //内容流入编辑后台起始时间
        $create_time_to            = intval($arrInput['create_time_to']);    //内容流入编辑后台终止时间
        $tbTitle                   = urldecode(trim($arrInput['tbTitle']));   //贴吧标题
        $intThreadId               = intval($arrInput['thread_id']);      //贴子ID
        $intVideoSiteId            = intval($arrInput['video_site_id']);        //来源站点ID
        $intVideoType              = strval($arrInput['video_type']);
        $intSearchType             = intval($arrInput['search_type']);     //为了筛出微视频 新增加了search_type=9
        $intIsVideoGood            = intval($arrInput['is_video_good']);    //是否符合微视频精选
        $intClassId                = intval($arrInput['class_id']);    //社区类型（1： 游戏社区）
        $intSubClassId             = intval($arrInput['sub_class_id']);    //社区子类型
        $strKeyWord = strval($arrInput['keyword']);//帖子标题关键字

        $intNeedTotalCount = isset($arrInput['need_total_count']) ? intval($arrInput['need_total_count']) : 0;
        $intNeedData = isset($arrInput['need_data']) ? intval($arrInput['need_data']) : 0;

        $strSqlCondition = '';
        $strSqlOrderBy = 'thread_id desc';
        $strSqlForceIndex = "";
        $arrTime = array();

        if(empty($intEditFinishTimeSortType)) {
            $strSqlOrderBy = 'thread_create_time desc';
        }
        else {
            if(1 == $intEditFinishTimeSortType) {  //倒序
                $strSqlOrderBy = 'thread_create_time desc';
            }
            else if (2 == $intEditFinishTimeSortType) {   //升序
                $strSqlOrderBy = 'thread_create_time asc';
            }
        }

        if(!empty($intEditFinishTimeFrom) && !empty($intEditFinishTimeTo)) {
            $arrTime[] = $intEditFinishTimeFrom;
            $arrTime[] = $intEditFinishTimeTo;
            $strSqlCondition = $strSqlCondition . ' thread_create_time >= ' . $intEditFinishTimeFrom . " and thread_create_time <= " . $intEditFinishTimeTo . " and ";
        }
        if(!empty($intUserId)) {
            $strSqlCondition = $strSqlCondition . ' user_id = ' . $intUserId . " and ";
        }
        if(!empty($strBjhUserName)) {
            $strBjhUserName = mysql_escape_string($strBjhUserName);
            $strSqlCondition = $strSqlCondition . " bjh_user_name = '$strBjhUserName' " . ' and ';
        }
        if(!empty($intEditStatus)) {
            $strSqlCondition = $strSqlCondition . ' edit_status = ' . $intEditStatus . " and ";
            if($intEditStatus == Lib_Define_Edit::EDIT_STATUS_NO_NEED_TO_EDIT){
                //amis上选中6代表机筛拒绝
                //这里的问题是，机筛拒绝只是edit_status=6中一部分，其余edit_status=6暂不知来源
                //因此过滤条件为，edit_status=6 并且 reject_field != '' （有机筛拒绝理由）
                $strSqlCondition = $strSqlCondition . " reject_field != '' and ";
            }
        }
        if(!empty($intEditResult)) {
            $strSqlCondition = $strSqlCondition . ' edit_result = ' . $intEditResult . " and ";
        }
        if(!empty($intDispatchStatus)) {
            $strSqlCondition = $strSqlCondition . ' dispatch_status = ' . $intDispatchStatus . " and ";
        }
        if(!empty($arrVideoFrom)) {
            $strTemp = '(' . implode(',' , $arrVideoFrom) . ')';
            $strSqlCondition = $strSqlCondition . ' video_from in ' . $strTemp . " and ";
        }
        if(!empty($strFirCategory)) {
            $strFirCategory = mysql_escape_string($strFirCategory);
            $strSqlCondition = $strSqlCondition . " fir_category = '$strFirCategory' " . " and ";
        }
        if(!empty($strSecCategory)) {
            $strSecCategory = mysql_escape_string($strSecCategory);
            $strSqlCondition = $strSqlCondition . " sec_category = '$strSecCategory' " . " and ";
        }
        if(!empty($intVideoQuality)) {
            $strSqlCondition = $strSqlCondition . " video_quality = '$intVideoQuality' " . " and ";
        }
        if(!empty($intIsVideoSquare)) {
            $strSqlCondition = $strSqlCondition . ' is_video_square = ' . $intIsVideoSquare . " and ";
        }
        //符合微视频
        if($intSearchType == Lib_Define_Edit::SEARCH_TYPE_MINI_VIDEO){
            $arrSearchTypeInput = array(
                'amis_user' => $strOpUName,
                'search_type' => array(
                    $intSearchType,
                ),
            );
            $arrSearchTypeOutput = Lib_Edit::getSearchTypeWithBiases($arrSearchTypeInput);
            $strSearchTypeQuery = implode(',' , $arrSearchTypeOutput['search_type']);
            $strSqlCondition = $strSqlCondition . " search_type in ($strSearchTypeQuery) " . ' and ';
        }
        //符合微视频精选
        if(!empty($intIsVideoGood)){
            $strSqlCondition = $strSqlCondition . ' is_video_good = ' . $intIsVideoGood . ' and ';
        }
        //社区视频类型
        if(!empty($intClassId)){
            $strSqlCondition = $strSqlCondition . ' class_id = ' . $intClassId . ' and ';
        }
        //社区视频子类型
        if(!empty($intSubClassId)){
            $strSqlCondition = $strSqlCondition . ' sub_class_id = ' . $intSubClassId . ' and ';
        }
        if(!empty($intVideoDurationFrom)) {
            $strSqlCondition = $strSqlCondition . ' video_duration >= ' . $intVideoDurationFrom . " and ";
        }
        if(!empty($intVideoDurationTo)) {
            $strSqlCondition = $strSqlCondition . ' video_duration <= ' . $intVideoDurationTo . " and ";
        }
        if(!empty($strTag)) {
            $strSqlCondition = $strSqlCondition . " tag like '%$strTag%' and ";
        }
        if(!empty($strLevel1Name)) {
            $strSqlCondition = $strSqlCondition . " level_1_name = '$strLevel1Name' " . " and ";
        }
        if(!empty($strLevel2Name)) {
            $strSqlCondition = $strSqlCondition . " level_2_name = '$strLevel2Name' " . " and ";
        }
        if(!empty($intOpUid)) {
            $strSubSelectSql = "select distinct thread_id from edit_log where op_uid = $intOpUid ";
            if(!empty($intEditFinishTimeFrom) && !empty($intEditFinishTimeTo)){
                $strSubSelectSql .= " and thread_create_time >= $intEditFinishTimeFrom and thread_create_time <= $intEditFinishTimeTo ";
            }
            $strSqlCondition = $strSqlCondition . " thread_id in ($strSubSelectSql) " . " and ";
        }
        if(!empty($intVideoScoreFrom)) {
            $strSqlCondition = $strSqlCondition . ' video_score >= ' . $intVideoScoreFrom . " and ";
        }
        if(!empty($intVideoScoreTo)) {
            $strSqlCondition = $strSqlCondition . ' video_score <= ' . $intVideoScoreTo . " and ";
        }
        if(!empty($intOpTimeFrom) && !empty($intOpTimeTo)) {
            $arrTime[] = $intOpTimeFrom;
            $arrTime[] = $intOpTimeTo;
            $strSqlCondition = $strSqlCondition . ' edit_finish_time >= ' . $intOpTimeFrom . " and edit_finish_time <= " . $intOpTimeTo . " and ";
        }
        if(!empty($create_time_from) && !empty($create_time_to)){
            $arrTime[] = $create_time_from;
            $arrTime[] = $create_time_to;
            $strSqlCondition .= ' create_time >= '.$create_time_from.' and create_time <= '.$create_time_to.' and ';
        }
        if(!empty($strRejectFiled)) {
            $strRejectFiled = mysql_escape_string($strRejectFiled);
            $strSqlCondition = $strSqlCondition . " reject_field = '$strRejectFiled' " . " and ";
        }
        if(!empty($strRejectReason)) {
            $strRejectReason = mysql_escape_string($strRejectReason);
            $strSqlCondition = $strSqlCondition . " reject_reason = '$strRejectReason' " . " and ";
        }
        if(!empty($intPreSyncTimeFrom) && !empty($intPreSyncTimeTo)) {
            $arrTime[] = $intPreSyncTimeFrom;
            $arrTime[] = $intPreSyncTimeTo;
            $strSqlCondition = $strSqlCondition . ' pre_sync_time >= ' . $intPreSyncTimeFrom . " and pre_sync_time <= " . $intPreSyncTimeTo . " and ";
        }
        if(!empty($strKeyWord)){
            $strKeyWord = mysql_escape_string($strKeyWord);
            $strSqlCondition = $strSqlCondition . " title like '%" . $strKeyWord . "%'" . " and ";
            $strSqlForceIndex = " force index (thread_create_time) ";
        }
        
        if( $intThreadId > 0 ){// 只要是拿thread_id检索 以上各种arr不care  直接强制=  <EMAIL>
            $strSqlCondition = ' thread_id='.$intThreadId.' AND ';
        }
        //贴吧标题
        if( !empty($tbTitle) ){
            $tbTitle = mysql_escape_string($tbTitle);
            $strSqlCondition .= ' title="'.$tbTitle.'" AND ';
        }
        if( $intVideoSiteId > 0 ){
            $strSqlCondition .= ' video_site_id='.$intVideoSiteId.' and ';
        }
        if(!empty($intVideoType) && 0 !== intval($intVideoType)){
        	$arrVideoTypes = explode(",", $intVideoType);
        	if(count($arrVideoTypes)==1){
        		$strSqlCondition .= ' video_type='.$intVideoType.' and ';
        	}else{
        		$strSqlCondition .= ' video_type IN('.$intVideoType.') and ';
        	}
        }
        $strSqlCondition = $strSqlCondition . " 1=1 ";
        Bingo_Log::warning("sql condition = [$strSqlCondition]");
        if(empty($arrTime)) {
            Bingo_Log::warning("do not have time condition");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrTableName = self::getDataFromTableName($arrTime);

        if(empty($arrTableName)) {
            Bingo_Log::warning("have no table(table name empty). arrTime [" . serialize($arrTime) . "]");
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        Bingo_Log::warning("sql arrTableName. [" . serialize($arrTableName) . "]");

        $arrRetData = array();
        $objMultiX = new Tieba_Multi('selectVideoFromMis_multicall');

        if(1 == $intNeedTotalCount) {
            $objMultiParams['video']['selectVideoFromMisAllTotalCount'] = array(
                'tables' => $arrTableName,
                'condition' => $strSqlCondition,
            );
        }

        if(1 == $intNeedData) {
            $objMultiParams['video']['selectAllVideoFromMisData'] = array(
                'tables' => $arrTableName,
                'condition' => $strSqlCondition,
                'order_by'  => $strSqlOrderBy,
                'limit' => " $intOffset, $intPs",
                'force_index' => $strSqlForceIndex,
            );
        }

        foreach ($objMultiParams as $serviceName => $serviceCalls) {
            foreach ($serviceCalls as $method => $serviceCall) {
                $arrInput = array(
                    "serviceName" => $serviceName,
                    "method" => $method,
                    'ie' => 'utf-8',
                    "input" => $serviceCall,
                );
                $objMultiX->register("$method", new Tieba_Service($serviceName), $arrInput);
            }
        }

        Bingo_Timer::start('ala_updateLiveTb_core');
        $arrMultiOutput = $objMultiX->call();
        Bingo_Timer::end('ala_updateLiveTb_core');

        if(1 == $intNeedTotalCount) {
            $arrOutput = $arrMultiOutput['selectVideoFromMisAllTotalCount'];
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $arrServiceInput = $objMultiParams['video']['selectVideoFromMisTotalCount'];
                $strLog = "call video/selectVideoFromMisTotalCount fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::fatal($strLog);
                return self::_errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
            }
            $arrRetData['page']['count'] = intval($arrOutput['data']['total_count']);
            $arrRetData['page']['pn'] = $intPn;
            $arrRetData['page']['ps'] = $intPs;
            $arrRetData['hasMore'] = (intval($arrOutput['data']['total_count']) > ($intPn * $intPs)) ? 1 : 0;
            $arrRetData['count'] = $arrRetData['page']['count'];
        }

        if(1 == $intNeedData) {
            $arrOutput = $arrMultiOutput['selectAllVideoFromMisData'];
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $arrServiceInput = $objMultiParams['video']['selectAllVideoFromMisData'];
                $strLog = "call video/selectAllVideoFromMisData fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::fatal($strLog);
                return self::_errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
            }
            $arrList = $arrOutput['data'];

            if(empty($intEditFinishTimeSortType)) {
                $arrSort = array();
                foreach ($arrList as $key => $value) {
                    $arrSort[$key] = intval($value['thread_id']);
                }
                array_multisort($arrSort, SORT_DESC, $arrList);
            }
            else if (1 == $intEditFinishTimeSortType) {   //desc
                $arrSort = array();
                foreach ($arrList as $key => $value) {
                    $arrSort[$key] = intval($value['thread_create_time']);
                }
                array_multisort($arrSort, SORT_DESC, $arrList);
            }
            else if (2 == $intEditFinishTimeSortType) {
                $arrSort = array();
                foreach ($arrList as $key => $value) {
                    $arrSort[$key] = intval($value['thread_create_time']);
                }
                array_multisort($arrSort, SORT_ASC, $arrList);
            }


            $arrRetData['rows'] = array_slice($arrList, 0, $intPs);
        }

		// 获取用户信息和帖子信息
        Bingo_Timer::start("selectVideoFromMisData_mgetUserData");
        self::getExtInfoForVideos($arrRetData);
        Bingo_Timer::end("selectVideoFromMisData_mgetUserData");


        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);

    }



	/**
	 * @brief   为上述视频列表补充用户信息和帖子信息
	 * @param   array  $arrInput
	 * @return  null
	 */
	private static function getExtInfoForVideos(&$arrInput){
		if(empty($arrInput['rows'])){
			return;
		}

		// 获取用户id和帖子id
		$arrUserIds = array();
		$arrThreadIds = array();
		foreach($arrInput['rows'] as $row){
			$intUserId   = intval($row['user_id']);
			if($intUserId > 0){
				$arrUserIds[$intUserId] = $intUserId;
			}
			$intThreadId = intval($row['thread_id']);
			if($intThreadId > 0){
				$arrThreadIds[$intThreadId] = $intThreadId;
			}
		}
		$arrUserIds = array_values($arrUserIds);
		$arrThreadIds = array_values($arrThreadIds);

		// 并行请求(获取用户详细信息和帖子详细信息)
		$arrMultiCall = array(
			0 => array(
				'serviceName' => 'user',
				'method' => 'mgetUserData',
				'input' => array('user_id' => $arrUserIds),
				'ie' => 'utf-8'
			),
			1 => array(
				'serviceName' => 'post',
				'method' => 'mgetThread',
				'input'  => array(
					'thread_ids' 	  => $arrThreadIds,
					'need_abstract'   => 0,
					'forum_id' 		  => 0,
					'need_photo_pic'  => 0,
					'need_user_data'  => 0,
					'icon_size' 	  => 0,
					'need_forum_name' => 0,
					'call_from' 	  => 'client_frs',
				),
				'ie' => 'utf-8'
			),
			2 => array(
				'serviceName' => 'post',
				'method' => 'getThreadMaskInfo',
				'input'  => array(
					'thread_ids' 	  => $arrThreadIds,
					'forum_id'        => 0,
				),
				'ie' => 'utf-8'
			),	
		);
		
		$multiCall = new Tieba_Multi('getExtInfoForVideos');
		foreach ($arrMultiCall as $key => $value) {
			$multiCall->register($key, new Tieba_Service($value['serviceName']), $value);
		}
		$multiCall->call();
		$arrUserInfos   = $multiCall->getResult(0);
		$arrThreadInfos = $multiCall->getResult(1);
		$arrThreadMask  = $multiCall->getResult(2);
		

		// 处理用户信息
		if(!$arrUserInfos || Tieba_Errcode::ERR_SUCCESS != $arrUserInfos['errno']){
			Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . 'get user infos failed by calling user.mgetUserData service. input: [' . serialize($arrMultiCall[0]['input']) . ']; output: [' . serialize($arrUserInfos) . ']');
			$arrUserInfos = array();
		}else{
			$arrUserInfos = $arrUserInfos['user_info'];
		}

		// 处理帖子信息
		if(!$arrThreadInfos || Tieba_Errcode::ERR_SUCCESS != $arrThreadInfos['errno']){
			Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' get thread infos failed by calling post.mgetThread service. input: [' . serialize($arrMultiCall[1]['input']) . ']; output: [' . serialize($arrThreadInfos) . ']');
			$arrThreadInfos = array();
		}else{
			$arrThreadInfos = $arrThreadInfos['output']['thread_list'];
		}
		if(!$arrThreadMask || Tieba_Errcode::ERR_SUCCESS != $arrThreadMask['errno']){
			Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' get thread mask failed by calling post.getThreadMaskInfo service. input: [' . serialize($arrMultiCall[2]['input']) . ']; output: [' . serialize($arrThreadMask) . ']');
			$arrThreadInfos = array();
		}else{
			$arrMask = array();
			foreach($arrThreadMask['output']['thread_info'] as $k => $v){
				$arrMask[$v['thread_id']] = $v;
			}
			$arrThreadMask = $arrMask;
		}

        // 获取水印信息
        if (!Lib_Edit::getVideoWaterMarkTypeByThreadIds($arrThreadIds, $arrWaterMarkTypes)) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' get watermark type fail for threads');
        }

		// 处理返回结果
		foreach($arrInput['rows'] as &$row ){
			// 用户信息
			$uid = intval($row['user_id']);
			$row['user_name'] = isset($arrUserInfos[$uid]['user_name']) ? $arrUserInfos[$uid]['user_name'] : '';
			$god = isset($arrUserInfos[$uid]['god']) ? $arrUserInfos[$uid]['god'] : array();
			if($god){
				$row['is_god'] = 1;
				$row['user_nickname'] = isset($arrUserInfos[$uid]['user_nickname']) ? $arrUserInfos[$uid]['user_nickname'] : '';
			}else{
				$row['is_god'] = 0;
				$row['user_nickname'] = '';
			}

			// 帖子信息
			$intThreadId = intval($row['thread_id']);
			if(!empty($arrThreadInfos[$intThreadId]['video_info']['video_desc'][0]['video_url'])){
				$row['video_url'] = $arrThreadInfos[$intThreadId]['video_info']['video_desc'][0]['video_url'];
			}
			$row['is_hide'] = (int)$arrThreadMask[$intThreadId]['is_hide'];
            $row['video_watermark_type'] = isset($arrWaterMarkTypes[$intThreadId]) ? $arrWaterMarkTypes[$intThreadId] : 0;
		}
		unset($row);
	}


    /**
     * 查看每个表的被selectVideoFromMisTotalCounts multi调用 优化原接口
     * @param 
     * @return array
     */
    public static function selectVideoFromMisTotalCount($arrInput){
    	$arrDlInput['function'] = 'selectVideoFromMisTotalCount';
        $arrDlInput['table_name'] = $arrInput['table_name'];
        $arrDlInput['condition'] = $arrInput['condition'];
        $arrOutputRet = Dl_Video_Sql::execSql($arrDlInput);
        if(false === $arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');
            return self::_errRet(isset($arrOutputRet['errno']) ? $arrOutputRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $intCurrentTotal = intval($arrOutputRet['results'][0][0]['count']);
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, array('total_count' => $intCurrentTotal,));
    }
    /**
     * 查看每个表的被selectVideoFromMisTotalCounts multi调用 优化原接口
     * @param 
     * @return array
     */
    public static function selectVideoFromMisAllTotalCount($arrInput){
    	$arrTableName = $arrInput['tables'];
    	$strSqlCondition = $arrInput['condition'];
    	$intTotalCount = 0;

        $objMultiX = new Tieba_Multi('selectVideoFromMisTotalCounts_multi_call');
    	foreach($arrTableName as $key => $strTableName){
	        $arrDBInput = array(
	            'table_name' => $strTableName,
	            'condition' => $strSqlCondition,
	        );
            $arrMultiInput = array(
                "serviceName" => 'video',
                "method" => 'selectVideoFromMisTotalCount',
                'ie' => 'utf-8',
                "input" => $arrDBInput,
            );
            $objMultiX->register($strTableName, new Tieba_Service('video'), $arrMultiInput);
    	}
    	Bingo_Timer::start("selectVideoFromMisTotalCounts_multi_call");
    	$arrOutput = $objMultiX->call();
    	Bingo_Timer::end('selectVideoFromMisTotalCounts_multi_call');
    	foreach($arrOutput as $key => $value){
    		if(!$value || $value['errno'] != Tieba_Errcode::ERR_SUCCESS){
    			Bingo_Log::warning("selectVideoFromMisTotalCounts get count fail. input:[" . serialize($arrInput) . "]. output:[" . serialize($arrOutput) . "]");
    			return self::_errRet(isset($value['errno']) ? $value['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
    		}
    		$intTotalCount += $value['data']['total_count'];
    	}
    	return self::_errRet(Tieba_Errcode::ERR_SUCCESS, array('total_count' => $intTotalCount,));
    }
    /**
     * 查看每个表的被selectVideoFromMisTotalCounts multi调用 优化原接口
     * @param 
     * @return array
     */
    public static function selectAllVideoFromMisData($arrInput){
        $arrTableName = $arrInput['tables'];
        $strSqlCondition = $arrInput['condition'];
        $arrRetData = array();

        $objMultiX = new Tieba_Multi('selectAllVideoFromMisData_multi_call');
        foreach($arrTableName as $strTableName){
        	$arrDBInput = array(
        		'table_name' => $strTableName,
        		'condition' => $strSqlCondition,
        		'order_by' => $arrInput['order_by'],
                'limit' => $arrInput['limit'],
                'force_index' => $arrInput['force_index'],
        	);
            $arrMultiInput = array(
                "serviceName" => 'video',
                "method" => 'selectVideoFromMisData',
                'ie' => 'utf-8',
                "input" => $arrDBInput,
            );
            $objMultiX->register($strTableName, new Tieba_Service('video'), $arrMultiInput);
    	}
    	Bingo_Timer::start("selectAllVideoFromMisData_multi_call");
    	$arrOutput = $objMultiX->call();
    	Bingo_Timer::end('selectAllVideoFromMisData_multi_call');
    	foreach($arrOutput as $key => $value){
    		if(!$value  || $value['errno'] != Tieba_Errcode::ERR_SUCCESS){
    			Bingo_Log::warning("selectAllVideoFromMisData multi call fail.");
    			return self::_errRet(isset($value['errno']) ? $value['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
    		}
    		$arrRetData = array_merge($arrRetData, $value['data']);
    	}
    	return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function selectVideoFromMisData($arrInput) {
        $strTableName = $arrInput['table_name'];
        $strCondition = $arrInput['condition'];
        $arrRetData = array();

        $arrDlInput['function'] = 'selectVideoFromMisData';
        $arrDlInput['table_name'] = $strTableName;
        $arrDlInput['condition'] = $strCondition;
        $arrDlInput['order_by'] = $arrInput['order_by'];
        $arrDlInput['limit'] = $arrInput['limit'];
        $arrDlInput['force_index'] = $arrInput['force_index'];
        $arrOutputRet = Dl_Video_Sql::execSql($arrDlInput);
        if(false === $arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');
            return self::_errRet(isset($arrOutputRet['errno']) ? $arrOutputRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrRetData = array_merge($arrRetData, $arrOutputRet['results'][0]);

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }


    /**
     * @param $arrInput
     * @return array
     */
    public static function updateVideoDispatchStatus($arrInput) {
        if(empty($arrInput['dispatch_status_update']) || empty($arrInput['table_to_threads']) || !is_array($arrInput['table_to_threads'])) {
            Bingo_Log::warning("error params. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrTableToThreads = $arrInput['table_to_threads'];
        foreach ($arrTableToThreads as $strSuffixTableName => $arrThreadIds) {   //
            $arrDlInput['function'] = 'updateVideoDispatchStatus';
            $arrDlInput['table_name'] = self::VIDEO_EDIT_TABLE_PREFIX . $strSuffixTableName;

            $intDispatchStatus = intval($arrInput['dispatch_status_update']);

            $strSet = "dispatch_status=$intDispatchStatus";

            if(3 == $intDispatchStatus) {
                $strSet = $strSet . ", pre_sync_time=" . time();
            }
            else if(4 == $intDispatchStatus) {
                $strSet = $strSet . ", publish_time=" . time();
            }
            $arrDlInput['str_set'] = $strSet;
            $strCondition = '';
            $strTemp = implode(',' , $arrThreadIds);
            $strCondition = $strCondition . 'thread_id in (' . $strTemp . ') ';
            if(!empty($arrInput['dispatch_status_origin'])) {
                $strCondition = $strCondition . ' and dispatch_status = ' . intval($arrInput['dispatch_status_origin']);
            }
            $arrDlInput['condition'] = $strCondition;
            $arrOutputRet = Dl_Video_Sql::execSql($arrDlInput);
            if(false === $arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');
                return self::_errRet(isset($arrOutputRet['errno']) ? $arrOutputRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }
    
    /**
     * @brief: 判定当前视频是否是微视频
     * @param
     *      check_duration = 1 返回时长大于0的数据
     *      check_duration = 0 返回时长等于0的数据
     * @return
     **/
    private static function _getMicroVideo($data, $micro_video, $check_duration){
        if( empty($micro_video) ){
            return array();
        }
        $tids = array();
        
        foreach($data AS $item){
            $thread_id = intval($item['thread_id']);
            $duration = intval($item['duration']);
            $video_type = intval($item['video_type']);
            $ext_param = strval($item['ext_param']);
            $ext_param_arr = json_decode($ext_param, true);
            $ext_attr = $ext_param_arr['ext_attr'];
            $video_width = 0;
            $video_height = 0;
            
            if($duration > 60 || empty($ext_attr)){
                //时长大于1分钟
                continue;
            }
            foreach( $ext_attr AS $ext ){
                if( $ext['key'] == 'video_info' ){
                    $video_height = intval($ext['value']['video_height']);
                    $video_width = intval($ext['value']['video_width']);
                }
            }
            if( $video_height == 0 || $video_width == 0 ){
                continue;
            }
            if( ($video_width/$video_height) > 1 ){
                //横屏视频
                continue;
            }
            
            if( $check_duration && $duration > 0 ){
                $tids[] = $thread_id;
                continue;
            }
            
            if( $check_duration == 0 && $duration == 0 && $video_type == Molib_Util_Video::TEMP_VIDEO_UPLOAD_FROM_URL_SPIDER ){
                $tids[] = $thread_id;
                continue;
            }
        }
        
        return $tids;
    }

    /**
     * @brief 回溯脚本用临时接口
     * @param $arrInput
     * @return array
     */
    public static function updateVideoAuditTable($arrInput)
    {
        if (!isset($arrInput['weekkey']) && !isset($arrInput['create_time'])
            || !isset($arrInput['fields']) || empty($arrInput['fields'])
            || !isset($arrInput['id']) && !isset($arrInput['thread_id'])
        ) {
            Bingo_Log::warning("param error ".serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (isset($arrInput['weekkey'])) {
            $strWeekKey = mysql_escape_string(trim(strval($arrInput['weekkey'])));
        } else {
            $strWeekKey = date('Ymd', self::getThatDayMonday(intval($arrInput['create_time'])));
        }

        if (isset($arrInput['id'])) {
            $arrCond = array(
                'id' => intval($arrInput['id'])
            );
        } else {
            $arrCond = array(
                'thread_id' => intval($arrInput['thread_id'])
            );
        }

        $arrFields = self::_parseFields($arrInput['fields']);

        $arrParams = array(
            'weekkey' => $strWeekKey,
            'cond' => $arrCond,
            'field' => array_keys($arrFields),
        );

        $arrRet = Lib_Audit::selectVideoInfo($arrParams);
        if($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("call Lib_Audit::selectVideoInfo error. [input]:".serialize($arrParams)." [output]:".serialize($arrRet));
            return self::errRet($arrRet? $arrRet['errno'] : Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        if(empty($arrRet['data'])){
            Bingo_Log::warning("can not find the video in audit db. [input]:".serialize($arrParams)." [output]:".serialize($arrRet));
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_NO_RECORD);
        }
        $arrAuditInfo = $arrRet['data'][0];

        self::_updateFields($arrFields, $arrAuditInfo);

        $arrParams = array(
            'table_name' => self::$strVideoAuditTable . '_' . $strWeekKey,
            'cond' => $arrCond,
            'field' => $arrFields,
        );
        $arrRet = Lib_Audit::updateVideoInfo($arrParams);
        if($arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("call Lib_Audit::updateVideoInfo error. [input]:".serialize($arrParams)." [output]:".serialize($arrRet));
            return self::errRet($arrRet? $arrRet['errno'] : Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrFields);
    }

    /**
     * @param $arrFields
     * @return mixed
     */
    private static function _parseFields($arrFields)
    {
        foreach ($arrFields as $strField => $mixValue) {
            $arrItems = explode('.', $strField);
            unset($arrFields[$strField]);
            $strField = array_shift($arrItems);
            if (preg_match('/^@(.+)$/', $mixValue, $arrMatches) && is_numeric($arrMatches[1])) {
                $mixValue = filter_var($arrMatches[1], FILTER_VALIDATE_FLOAT) ? floatval($arrMatches[1]) : intval($arrMatches[1]);
            } else if (preg_match('/^\$((true)|(false)|(null))$/i', $mixValue, $arrMatches)) {
                $mixValue = !empty($arrMatches[2]) ? true : (!empty($arrMatches[3]) ? false : null);
            }
            $arrFields[mysql_escape_string($strField)] = array(
                'items' => $arrItems,
                'value' => $mixValue,
            );
        }
        return $arrFields;
    }

    /**
     * @param $arrFields
     * @param $arrOriginalValues
     * @return bool
     */
    private static function _updateFields(&$arrFields, $arrOriginalValues)
    {
        foreach ($arrFields as $strField => $arrValue) {
            $strOriginalValue = $arrOriginalValues[$strField];

            if(!empty($arrValue['items'])) {
                $arrOriginalValue = json_decode($strOriginalValue, true);
                if(false !== $arrOriginalValue) {
                    $strFormat = 'json';
                } else {
                    $arrOriginalValue = unserialize($strOriginalValue);
                    if (false !== $arrOriginalValue) {
                        $strFormat = 'serial';
                    } else {
                        return false;
                    }
                }
                $refValue = &$arrOriginalValue;
                foreach ($arrValue['items'] as $strItem) {
                    if(!is_array($refValue) || !array_key_exists($strItem, $refValue)) {
                        return false;
                    }
                    $refValue = &$refValue[$strItem];
                }
                $refValue = $arrValue['value'];
                unset($refValue);
                $arrValue['value'] = 'json' == $strFormat ? json_encode($arrOriginalValue) : serialize($arrOriginalValue);
            }

            $arrFields[$strField] = is_numeric($arrValue['value'])? $arrValue['value'] : mysql_escape_string(strval($arrValue['value']));
        }
        return true;
    }

    /**
     * @brief 调试接口
     * @param $arrInput
     * @return array
     */
    public static function getVideoAuditInfoByThreadId($arrInput)
    {
        if (!isset($arrInput['thread_id'])) {
            Bingo_Log::warning("param error " . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intThreadId = intval($arrInput['thread_id']);

        // get thread info
        $arrParams = array(
            'thread_ids' => array($intThreadId),
            'need_abstract' => 0,
            'forum_id' => 0,
            'need_photo_pic' => 0,
            'need_user_data' => 0,
            'icon_size' => 0,
            'need_forum_name' => 0,
            'call_from' => 'client_frs',
        );
        $arrRet = Tieba_Service::call('post', 'mgetThread', $arrParams, null, null, 'post', 'php', 'utf-8');
        if ($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call post::mgetThread error. [input]:" . serialize($arrParams) . " [output]:" . serialize($arrRet));
            return self::errRet($arrRet ? $arrRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        if (!isset($arrRet['output']['thread_list'][$intThreadId])) {
            Bingo_Log::warning("thread not exist. [input]:" . serialize($arrParams) . " [output]:" . serialize($arrRet));
            return self::errRet(Tieba_Errcode::ERR_NO_RECORD, '', 'thread not exist');
        }
        $arrThreadInfo = $arrRet['output']['thread_list'][$intThreadId];
        $arrTypes = Tieba_Type_Thread::getTypeArray($arrThreadInfo['thread_types']);
        if (!$arrTypes['is_movideo']) {
            Bingo_Log::warning('invalid thread type, tid='.$intThreadId.', types='.json_encode($arrTypes));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, '', 'not movideo thread');
        }

        //get video audit info
        $arrParams = array(
            'cond' => array(
                'thread_id' => $intThreadId,
                'start_time' => $arrThreadInfo['create_time'] - 86400 * 15,
                'end_time' => $arrThreadInfo['create_time'] + 1,
            ),
        );
        if (isset($arrInput['weekkey']) && !empty($arrInput['weekkey'])) {
            $arrParams['weekkey'] = $arrInput['weekkey'];
        } else if (isset($arrThreadInfo['video_info']['video_log_id'])) {
            $arrParams['weekkey'] = substr($arrThreadInfo['video_info']['video_log_id'], -8);
        }
        $arrRet = Lib_Audit::selectVideoInfo($arrParams);
        if ($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call Lib_Audit::selectVideoInfo error. [input]:" . serialize($arrParams) . " [output]:" . serialize($arrRet));
            return self::errRet($arrRet ? $arrRet['errno'] : Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        if (empty($arrRet['data'])) {
            Bingo_Log::warning("can not find the video in audit db. [input]:" . serialize($arrParams) . " [output]:" . serialize($arrRet));
        }
        $arrAuditInfo = empty($arrRet['data']) ? array() : $arrRet['data'][0];

        $arrOutput = array(
            'thread_info' => $arrThreadInfo,
            'audit_info' => $arrAuditInfo,
        );

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    /**
     * @param $intThreadId
     * @return $threadScore
     */
    public static function getThreadScoreByThreadId($arrInput) {
        if (!isset($arrInput['thread_id'])) {
            Bingo_Log::warning("param error " . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intThreadId    = $arrInput['thread_id'];
        $intThreadScore = 0;
        $arrGetVideoInfoInput = array(
            'thread_id' => $intThreadId,
        );

        //获取审核信息
        $arrVideoInfoOutput = Tieba_Service::call('video', 'getVideoAuditInfoByThreadId', $arrGetVideoInfoInput, null, null, 'post', 'php', 'utf-8');
        if (empty($arrVideoInfoOutput) || $arrVideoInfoOutput === false || $arrVideoInfoOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call service video::getVideoAuditInfoByThreadId return error!!!  Input: " . serialize($arrGetVideoInfoInput) . "Output: " . serialize($arrVideoInfoOutput));

            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $intCreateTime = $arrVideoInfoOutput['data']['thread_info']['create_time'];
        $arrGetEditInfoInput = array(
            'tids' => array($intThreadId),
            'thread_create_time' => $intCreateTime,
        );

        //获取编辑信息
        $arrEditInfoOutput = Tieba_Service::call('video', 'getVideoEditListByIds', $arrGetEditInfoInput, null, null, 'post', 'php', 'utf-8');
        if (empty($arrEditInfoOutput) || $arrEditInfoOutput === false || $arrEditInfoOutput['errno'] != Tieba_Errcode::ERR_SUCCESS || empty($arrEditInfoOutput['data'])) {
            Bingo_Log::warning("call service video::getVideoEditListByIds return error!!!  Input: " . serialize($arrGetEditInfoInput) . "Output:" . serialize($arrEditInfoOutput));

            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $intThreadScore = $arrEditInfoOutput['data'][0]['edit_result'] == 1 ? $arrEditInfoOutput['data'][0]['video_score'] : 0;

        $arrOutput = array(
            'thread_score' => $intThreadScore,
        );

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }
    /**
     * @brief 调试接口
     * @param $arrInput
     * @return array
     */
    public static function commitVideoOverlay($arrInput)
    {
        if (!isset($arrInput['thread_id'])) {
            Bingo_Log::warning("param error " . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intThreadId = intval($arrInput['thread_id']);
        $intReplace = isset($arrInput['replace']) ? intval($arrInput['replace']) : 0;
        $strWeekKey = isset($arrInput['weekkey']) ? $arrInput['weekkey'] : '';
        $intNoTrans = isset($arrInput['notrans']) ? intval($arrInput['notrans']) : -1;
        $intLevel = isset($arrInput['level']) ? intval($arrInput['level']) : -1;
        $intPresetId = isset($arrInput['preset_id']) ? intval($arrInput['preset_id']) : -1;
        $intLevels = isset($arrInput['levels']) ? intval($arrInput['levels']) : -1;
        $intAppend = isset($arrInput['append']) ? intval($arrInput['append']) : 1;

        $arrOverlay = array();
        if (!isset($arrInput['overlay']) || empty($arrInput['overlay'])) {
            $arrOverlay = Lib_Audit::getDefaultWatermarkSettings();
        } else {
            if (is_string($arrInput['overlay'])) {
                if (preg_match('/(\d+)(:(\d+))?(:([a-z][a-z0-9]*))?(:(\d+))?/', $arrInput['overlay'], $arrMatches)) {
                    $arrOverlay['pos'] = intval($arrMatches[1]);
                    if (!empty($arrMatches[3])) {
                        $arrOverlay['margin'] = intval($arrMatches[3]);
                    }
                    if (!empty($arrMatches[5])) {
                        $arrOverlay['name'] = $arrMatches[5];
                    }
                }
            } else if (is_array($arrInput['overlay'])) {
                if (isset($arrInput['overlay']['pos'])) {
                    $arrOverlay['pos'] = intval($arrInput['overlay']['pos']);
                    if (isset($arrInput['overlay']['margin'])) {
                        $arrOverlay['margin'] = intval($arrInput['overlay']['margin']);
                    }
                    if (isset($arrInput['overlay']['name'])) {
                        $arrOverlay['name'] = trim($arrInput['overlay']['name']);
                    }
                }
            }
        }
        if (empty($arrOverlay)) {
            Bingo_Log::warning('param error, input='.json_encode($arrInput));
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // get thread info
        $arrParams = array(
            'thread_ids' => array($intThreadId),
            'need_abstract' => 0,
            'forum_id' => 0,
            'need_photo_pic' => 0,
            'need_user_data' => 0,
            'icon_size' => 0,
            'need_forum_name' => 0,
            'call_from' => 'client_frs',
        );
        $arrRet = Tieba_Service::call('post', 'mgetThread', $arrParams, null, null, 'post', 'php', 'utf-8');
        if ($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call post::mgetThread error. [input]:" . serialize($arrParams) . " [output]:" . serialize($arrRet));
            return self::errRet($arrRet ? $arrRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        if (!isset($arrRet['output']['thread_list'][$intThreadId])) {
            Bingo_Log::warning("thread not exist. [input]:" . serialize($arrParams) . " [output]:" . serialize($arrRet));
            return self::errRet(Tieba_Errcode::ERR_THREAD_NOT_EXIST, '', 'thread not exist');
        }
        $arrThreadInfo = $arrRet['output']['thread_list'][$intThreadId];
        $arrTypes = Tieba_Type_Thread::getTypeArray($arrThreadInfo['thread_types']);
        if (!$arrTypes['is_movideo']) {
            Bingo_Log::warning('invalid thread type, tid='.$intThreadId.', types='.json_encode($arrTypes));
            return self::errRet(Tieba_Errcode::ERR_ACTION_FORBIDDEN, '', 'not movideo thread');
        }

        // get video audit info
        $arrParams = array(
            'cond' => array(
                'thread_id' => $intThreadId,
                'start_time' => $arrThreadInfo['create_time'] - 86400 * 15,
                'end_time' => $arrThreadInfo['create_time'] + 1,
            ),
        );
        if (!empty($strWeekKey)) {
            $arrParams['weekkey'] = $strWeekKey;
        } else if (isset($arrThreadInfo['video_info']['video_log_id'])) {
            $arrParams['weekkey'] = substr($arrThreadInfo['video_info']['video_log_id'], -8);
        }
        $arrRet = Lib_Audit::selectVideoInfo($arrParams);
        if ($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call Lib_Audit::selectVideoInfo error. [input]:" . serialize($arrParams) . " [output]:" . serialize($arrRet));
            return self::errRet($arrRet ? $arrRet['errno'] : Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        if (empty($arrRet['data'])) {
            Bingo_Log::warning("can not find the video in audit db. [input]:" . serialize($arrParams) . " [output]:" . serialize($arrRet));
            return self::errRet(Tieba_Errcode::ERR_NO_RECORD, '', 'no record in audit db');
        }
        $arrAuditInfo = $arrRet['data'][0];
        $strWeekKey = $arrAuditInfo['weekkey'];
        $intVideoAuditId = (int)$arrAuditInfo['video_audit_id'];
        $arrExtParam = json_decode($arrAuditInfo['ext_param'], true);

        // check if has overlaid video
        if (!$intReplace) {
            foreach ($arrExtParam['ext_attr'] as $value) {
                if($value['key'] == 'video_info') {
                    $arrExtAttr = is_string($value['value']) ? unserialize($value['value']) : $value['value'];
                    if (!isset($arrExtAttr['video_desc']) || empty($arrExtAttr['video_desc'])) {
                        break;
                    }
                    foreach ($arrExtAttr['video_desc'] as $arrItem) {
                        if ($arrItem['video_id'] == 0) {
                            Bingo_Log::warning('overlaid video exists, thread_id='.$intThreadId);
                            return Lib_Audit::arrRet(Tieba_Errcode::ERR_SUCCESS);
                        }
                    }
                    break;
                }
            }
        }

        // set overlay level
        if ($intLevel >= 0) {
            $arrOverlay['level'] = $intLevel;
        } else if (Lib_Audit::getNeedOverlayWithLowResolution($arrAuditInfo)) {
            $arrOverlay['level'] = 2;
        }

        // commit overlay request
        $arrExtData = array(
            'weekkey' => $strWeekKey,
            'video_audit_id' => $intVideoAuditId,
            'thread_id' => $intThreadId,
            'overlay' => $arrOverlay,
            'append' => $intAppend,
        );
        if ($intLevels >= 0) {
            $arrExtData['levels'] = $intLevels;
        }

        // preset id
        $intDefaultPresetId = Util_Const::TRANS_PRESET_ID_COMMON;
        if (Lib_Audit::getNeedSpecialTransForNani($arrAuditInfo)) {
            $intDefaultPresetId = Util_Const::TRANS_PRESET_ID_NANI;
        }
        $intPresetId < 0 && $intPresetId = $intDefaultPresetId;

        // notrans option
        if ($intNoTrans >= 0) {
            $arrExtData['notrans'] = $intNoTrans;
        } else if(Lib_Audit::getNeedNoTrans($arrAuditInfo)) {
            $arrExtData['notrans'] = Util_Const::NO_TRANS_TYPE_ALL;
        }

        // check if has delogo info
        if (isset($arrExtParam['delogo_info']['delogo'])) {
            $arrExtData['delogo'] = $arrExtParam['delogo_info']['delogo'];
        }

        // send request
        $arrParams = array(
            'video_url'     => $arrAuditInfo['video_url'],
            'video_preset'  => $intPresetId,
            'ext_data'      => json_encode($arrExtData),
        );
        $arrRet = Tbapi_Core_Midl_Http::httpcall(Util_Const::TRANSCODE_ARCH_SERVICE, Util_Const::TRANSCODE_ARCH_METHOD, $arrParams, 'post');
        if (!$arrRet || $arrRet['err_no'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call transcode service failed, params:['.json_encode($arrParams).'],ret:['.json_encode($arrRet).']');
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
        }

        return Lib_Audit::arrRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief 调试接口
     * @param $arrInput
     * @return array
     */
    public static function updateTransCodeTable($arrInput)
    {
        if (!isset($arrInput['video_name']) || !isset($arrInput['fields']) || empty($arrInput['fields'])) {
            Bingo_Log::warning("param error ".serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $strPrefix = isset($arrInput['prefix']) ? $arrInput['prefix'] : 'video_transcode';
        $intHashValue = self::_BKDR_Hash($arrInput['video_name']);
        $intSplit =  $strPrefix == 'video_crop' ? 50 : 100;
        $strTableName = $strPrefix . '_' . ($intHashValue % $intSplit + 1);
        $strVideoNameKey = $strPrefix == 'video_frame_info' ? 'video_id' : 'video_name';
        $intRecordId = isset($arrInput['id']) ? (int)$arrInput['id'] : -1;

        $arrFields = self::_parseFields($arrInput['fields']);

        $arrConds = array(
            "$strVideoNameKey =" => mysql_escape_string($arrInput['video_name']),
        );
        if ($intRecordId >= 0) {
            $arrConds['id ='] = $intRecordId;
        }

        $objDb = Tieba_Mysql::getDB('forum_video_audit');
        if (is_null($objDb)) {
            Bingo_Log::warning("db init fail");
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrQueryFields = array_keys($arrFields);
        if (!isset($arrFields['id'])) {
            $arrQueryFields[] = 'id';
        }
        $arrRet = $objDb->select($strTableName, $arrQueryFields, $arrConds);
        if($arrRet === false){
            Bingo_Log::warning("select error. sql:" . $objDb->getLastSQL());
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        if(empty($arrRet)){
            Bingo_Log::warning("no record. sql:". $objDb->getLastSQL());
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_NO_RECORD);
        }
        $arrTransCodeInfo = $arrRet[0];
        if ($intRecordId < 0) {
            $arrConds['id ='] = (int)$arrTransCodeInfo['id'];
        }

        self::_updateFields($arrFields, $arrTransCodeInfo);

        $arrRet = $objDb->update($strTableName, $arrFields, $arrConds);
        if($arrRet === false){
            Bingo_Log::warning("update error. sql:" . $objDb->getLastSQL());
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrFields);
    }

    /**
     * @brief 调试接口
     * @param $arrInput
     * @return array
     */
    public static function queryTransCodeTable($arrInput)
    {
        if (!isset($arrInput['video_name']) || !isset($arrInput['fields']) || empty($arrInput['fields'])) {
            Bingo_Log::warning("param error ".serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $strPrefix = isset($arrInput['prefix']) ? $arrInput['prefix'] : 'video_transcode';
        $intHashValue = self::_BKDR_Hash($arrInput['video_name']);
        $intSplit =  $strPrefix == 'video_crop' ? 50 : 100;
        $strTableName = $strPrefix . '_' . ($intHashValue % $intSplit + 1);
        $strVideoNameKey = $strPrefix == 'video_frame_info' ? 'video_id' : 'video_name';
        $intRecordId = isset($arrInput['id']) ? (int)$arrInput['id'] : -1;

        $arrFields = (array)$arrInput['fields'];

        $arrConds = array(
            "$strVideoNameKey =" => mysql_escape_string($arrInput['video_name']),
        );
        if ($intRecordId >= 0) {
            $arrConds['id ='] = $intRecordId;
        }

        $objDb = Tieba_Mysql::getDB('forum_video_audit');
        if (is_null($objDb)) {
            Bingo_Log::warning("db init fail");
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRet = $objDb->select($strTableName, $arrFields, $arrConds);
        if($arrRet === false){
            Bingo_Log::warning("select error. sql:" . $objDb->getLastSQL());
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        if(empty($arrRet)){
            Bingo_Log::warning("no record. sql:". $objDb->getLastSQL());
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_NO_RECORD);
        }
        $arrTransCodeInfo = $intRecordId >= 0 ? $arrRet[0] : $arrRet;

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrTransCodeInfo);
    }

    /**
     * @param $str
     * @return int
     */
    private static function _BKDR_Hash($str)
    {
        $seed = 1313;
        $hash = 0;
        for($i = 0; $i < strlen($str); ++$i) {
            $hash = ($hash * $seed + ord($str{$i})) & 0xFFFFFFFF;
        }
        return ($hash & 0x7FFFFFFF);
    }

    /**
     * @brief 回溯脚本用临时接口
     * @param $arrInput
     * @return array
     * @deprecated
     */
    public static function setTransCodeInfo($arrInput)
    {
        if(!isset($arrInput['thread_id']) || !isset($arrInput['video_id'])
            || !isset($arrInput['video_url']) || !isset($arrInput['video_duration'])
            || !isset($arrInput['video_width']) || !isset($arrInput['video_height'])
            || !isset($arrInput['video_size']) || !isset($arrInput['video_md5'])
        ){
            Bingo_Log::warning("param error ".serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $intThreadId = intval($arrInput['thread_id']);
        $intVideoId = intval($arrInput['video_id']);
        $strVideoUrl = trim($arrInput['video_url']);
        if (($intPos = strpos($strVideoUrl, '?')) !== false) {
            $strVideoUrl = substr($strVideoUrl, 0, $intPos);
        }
        $intVideoDuration = intval($arrInput['video_duration']);
        $intVideoWidth =intval($arrInput['video_width']);
        $intVideoHeight = intval($arrInput['video_height']);
        $intVideoSize = intval($arrInput['video_size']);
        $strVideoMd5 = trim($arrInput['video_md5']);
        $intHasAudio = isset($arrInput['has_audio']) ? intval($arrInput['has_audio']) : 1;

        // get thread info
        $arrParams = array(
            'thread_ids' => array($intThreadId),
            'need_abstract' => 0,
            'forum_id' => 0,
            'need_photo_pic' => 0,
            'need_user_data' => 0,
            'icon_size' => 0,
            'need_forum_name' => 0,
            'call_from' => 'client_frs',
        );
        $arrRet = Tieba_Service::call('post', 'mgetThread', $arrParams, null, null, 'post', 'php', 'utf-8');
        if($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("call post::mgetThread error. [input]:".serialize($arrParams)." [output]:".serialize($arrRet));
            return self::errRet($arrRet? $arrRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        if(!isset($arrRet['output']['thread_list'][$intThreadId])) {
            Bingo_Log::warning("thread not exist. [input]:".serialize($arrParams)." [output]:".serialize($arrRet));
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_NO_RECORD);
        }
        $arrThreadInfo = $arrRet['output']['thread_list'][$intThreadId];
        if(!isset($arrThreadInfo['video_info'])) {
            Bingo_Log::warning("video_info not exist. [input]:".serialize($arrParams)." [output]:".serialize($arrRet));
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrThreadExtAttr = $arrThreadInfo['video_info'];

        //get video audit info
        $strWeekKey = isset($arrInput['weekkey']) ? $arrInput['weekkey'] : date('Ymd', self::getThatDayMonday($arrThreadInfo['create_time']));
        $arrParams = array(
            'weekkey' => $strWeekKey,
            'cond' => array(
                'thread_id' => $intThreadId,
            ),
        );
        $arrRet = Lib_Audit::selectVideoInfo($arrParams);
        if($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("call Lib_Audit::selectVideoInfo error. [input]:".serialize($arrParams)." [output]:".serialize($arrRet));
            return self::errRet($arrRet? $arrRet['errno'] : Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        if(empty($arrRet['data'])){
            Bingo_Log::warning("can not find the video in audit db. [input]:".serialize($arrParams)." [output]:".serialize($arrRet));
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_NO_RECORD);
        }
        $arrAuditInfo = $arrRet['data'][0];
        $arrAuditExtParam = json_decode($arrAuditInfo['ext_param'], true);

        //set贴子属性
        $arrThreadExtAttr['video_desc'] = array(
            array(
                'video_id' => $intVideoId,
                'video_url' => $strVideoUrl,
                'video_width' => $intVideoWidth,
                'video_height' => $intVideoHeight,
            ),
        );
        $intPostId = $arrThreadInfo['first_post_id'];

        $arrRet = Lib_Post::setExtAttr($intThreadId, $intPostId, $arrThreadExtAttr);
        if($arrRet === false){
            Bingo_Log::warning('set ext attr fail response:'.json_encode($arrRet));
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        //update audit表
        foreach($arrAuditExtParam['ext_attr'] as &$arrItem) {
            if($arrItem['key'] == 'video_info') {
                $arrAuditExtAttr = &$arrItem['value'];
                if(isset($arrAuditExtAttr['thumbnail_bak'])) {
                    break;
                }
                $arrAuditExtAttr['video_desc'] = array(
                    array(
                        'video_id' => $intVideoId,
                        'video_url' => $strVideoUrl,
                        'video_width' => $intVideoWidth,
                        'video_height' => $intVideoHeight,
                    ),
                );

                $arrParams = array(
                    'table_name' => self::$strVideoAuditTable . '_' . $strWeekKey,
                    'cond' => array(
                        'id' => $arrAuditInfo['id'],
                    ),
                    'field' => array(
                        'ext_param' => mysql_escape_string(json_encode($arrAuditExtParam)),
                    ),
                );
                $arrRet = Lib_Audit::updateVideoInfo($arrParams);
                if($arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
                    Bingo_Log::warning("call Lib_Audit::updateVideoInfo error. [input]:".serialize($arrParams)." [output]:".serialize($arrRet));
                    return self::errRet($arrRet? $arrRet['errno'] : Tieba_Errcode::ERR_DB_QUERY_FAIL);
                }
                break;
            }
        }

        //update transcode表
        $arrTransCodeInfo = array(
            'file' => json_encode(array(
                array(
                    'video_name' => basename($strVideoUrl),
                    'video_id' => $intVideoId,
                    'video_width' => $intVideoWidth,
                    'video_height' => $intVideoHeight,
                    'video_size' => $intVideoSize,
                    'video_duration' => $intVideoDuration,
                    'video_url' => $strVideoUrl,
                ),
            )),
            'name' => $arrAuditInfo['video_name'],
            'video_width' => $intVideoWidth,
            'video_height' => $intVideoHeight,
            'video_size' => $intVideoSize,
            'video_duration' => $intVideoDuration,
            'video_md5' => $strVideoMd5,
            'has_audio' => $intHasAudio,
        );
        $strTransCodeInfo = http_build_query($arrTransCodeInfo);
        $arrParams = array(
            'video_name' => $arrAuditInfo['video_name'],
            'fields' => array(
                'transcode_status' => 2,
                'upload_status' => 2,
                'video_info_transcoded' => $strTransCodeInfo,
            ),
        );
        $arrRet = self::updateTransCodeTable($arrParams);
        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('update transcode table fail');
            return self::errRet($arrRet['errno']);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }
	/**
	 * @brief
	 *
	 * @param {Array} $arrInput
	 *
	 * @return array
	 */

    public static function getThreadStatusByMd5($arrInput){
        $md5 = $arrInput['video_md5'];
        $table_time = date('Ymd',self::getThatDayMonday(time()));
        $table_name = video_audit_.$table_time;
        $field = array(
            "user_id",
            "thread_id",
            "title",
            "delete_reason",
            "ext_param");
        $cond = "video_md5='".$md5."'";
        $arrInput = array(
            'function' => 'getThreadStatusByMd5',
            'table_name' => $table_name,
            'cond' => $cond);
        $arrOutput = Dl_Audit_Audit::execSql($arrInput);       
        if (!$arrOutput || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrInput).'],output:['.serialize($arrOutput).']');
            return self::_errRet($arrOutput['errno']);
        }
        if(empty($arrOutput['results'])){
            $table_name = video_audit_.date('Ymd',strtotime("-7 day $table_time"));
            $arrInput = array(
                'function' => 'getThreadStatusByMd5',
                'table_name' => $table_name,
                'cond' => $cond);
            $arrOutput = Dl_Audit_Audit::execSql($arrInput);       
            if (!$arrOutput || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call db fail, input:['.serialize($arrInput).'],output:['.serialize($arrOutput).']');
                return self::_errRet($arrOutput['errno']);
            }
        }
        $retData = array();
        $ext_param_json = json_decode($arrOutput["results"][0][0]['ext_param'],true);
        $ext_attr_json = $ext_param_json['ext_attr'];
        foreach($ext_attr_json as $ext_item){
            if ($ext_item['key'] == 'video_info'){
                $retData['thumbnail_width'] = $ext_item['value']['thumbnail_width']; 
                $retData['thumbnail_height'] = $ext_item['value']['thumbnail_height']; 
                $retData['cover'] = $ext_item['value']['thumbnail_url']; 
           }
        }
        $retData['title'] = $arrOutput["results"][0][0]['title']; 
        $retData['uid'] = $arrOutput["results"][0][0]['user_id'];
        $retData['thread_id'] = $arrOutput["results"][0][0]['thread_id']; 
        if (empty($arrOutput["results"][0][0]['delete_reason'])){
            $retData['fadeOnWarStatus'] = 1;
        }else{
            $retData['fadeOnWarStatus'] = 2;
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$retData);

    }
    /**
     * @param $arrInput
     * @return array
     */

    public static function getBliVideoStatus2Data($arrInput)
    {   
        $time = time();
        $month = date('Ym', $time);
        $table_name = 'video_url_info_'.$month;
        $field = array(
            'source_url');
        $arrCond = array(
            'status' => 2,
            'video_from' => 4);
		$arrDlInput = array('field' => $field,
		                    'table' => $table_name,
		                    'cond' => $arrCond,
                            'append' => null);
        $retData = Dl_Video_Video::getPersonalVideo($arrDlInput); 
	    return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$retData); 
    }
    

    /**
     * 获取分类及标签信息
     * @param type $arrInput
     * @return type
     */
    public static function getVideoWorksCateInfo($arrInput){
        if(empty($arrInput['first_cate_name']) && !empty($arrInput['second_cate_name'])){
            Bingo_Log::warning("input params error!!!");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if(!empty($arrInput['first_cate_name']) && empty($arrInput['second_cate_name'])){
            Bingo_Log::warning("input params error!!!");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        $arrCateList = array(
            'cate_list' => array(),
            'tag_list' => array(),
        );
        $handleWordServer = Wordserver_Wordlist::factory();   
        $strTableName     = 'tb_wordlist_redis_new_cate_tag_info';  
        //返回一二级分类
        if(empty($arrInput['first_cate_name']) && empty($arrInput['second_cate_name'])){
            $arrFirstInput = array('first_cate_info');
            $arrOutput = $handleWordServer->getValueByKeys($arrFirstInput, $strTableName); 
            if (!$arrOutput || $arrOutput['err_no'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning(sprintf("call wordlist::getValueByKeys failed! input[%s] output[%s]", serialize($arrFirstInput), serialize($arrOutput)));
                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $arrFirstCates = unserialize($arrOutput['first_cate_info']);
            if(empty($arrFirstCates)){
                Bingo_Log::warning("wordlist::getValueByKeys , first_cate_info is null ");
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrCateList);
            }
            foreach ($arrFirstCates as $firstCate){
                $arrSecInput[] =  'second_'.$firstCate.'_cate_info';
            }

            $arrSecOutput = $handleWordServer->getValueByKeys($arrSecInput, $strTableName); 
            if (!$arrSecOutput || $arrSecOutput['err_no'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning(sprintf("call wordlist::getValueByKeys failed! input[%s] output[%s]", serialize($arrSecInput), serialize($arrSecOutput)));
                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            foreach ($arrSecOutput as $k=> $secondCate){
                $arrSecondCates = unserialize($secondCate);
                if(empty($arrSecondCates)){
                    Bingo_Log::warning("wordlist::getValueByKeys , secodeCate key=".$secondCate.' value='.$secondCate);
                    continue;
                }
                $st =stripos($k,'_');
                $ed =stripos($k,'cate');
                $kk = substr($k,$st+1,$ed-$st-2);
                $arrCate[$kk] =  $arrSecondCates;
            }
            $arrCateList['cate_list'] = $arrCate;
            
        }
        
        //返回标签
        if(!empty($arrInput['first_cate_name']) && !empty($arrInput['second_cate_name'])){
            $key = 'tag_fixed_'.$arrInput['first_cate_name'].'_'.$arrInput['second_cate_name'];
            $arrTagInput = array($key);
            $arrTagOutput = $handleWordServer->getValueByKeys($arrTagInput, $strTableName); 
            if (!$arrTagOutput || $arrTagOutput['err_no'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning(sprintf("call wordlist::getValueByKeys failed! input[%s] output[%s]", serialize($arrTagInput), serialize($arrTagOutput)));
                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $arrTags = unserialize($arrTagOutput[$key]);
            if(empty($arrTags)){
                $arrTags = array();
                Bingo_Log::warning("wordlist::getValueByKeys , tag is null .tagKay=".$key);
//                return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrCateList);
            }
            
            $arrCateList['tag_list'][$arrInput['first_cate_name']][$arrInput['second_cate_name']] = $arrTags;
        }
        
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrCateList);
        
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function pushWatermarkInfoIntoList($arrInput)
    {
//        $arrWatermarkInput = array(
//            'thread_id' => $intThreadId,
//            'thread_create_time' => $intCreateTime,
//            'watermark_width' => $intWatermarkWidth,
//            'watermark_height' => $intWatermarkHeight,
//            'watermark_dot' => $arrWatermarkDot,
//        );

        // step 1 : get param
        //  参数校验放到了UI 层,此处不做校验,只取具体的 param

        $bolWater = false;
        if(!empty($arrInput['watermark_width']) ||  !empty($arrInput['watermark_height']) || !empty($arrInput['watermark_dot'])){
            $bolWater = true;
        }
        
        $bolCut = false;
        if(!empty($arrInput['edit'])){
            $bolCut = true;
        }
        
        if (false === $bolWater && false === $bolCut) {
            Bingo_Log::warning("input params error!!!");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        $intThreadId = $arrInput['thread_id'];
        $arrWaterPositionInfo = array(
            'thread_id'             => intval($intThreadId),
            'thread_create_time'    => intval($arrInput['thread_create_time']),
//            'watermark_width'       => $arrInput['watermark_width'],
//            'watermark_height'      => $arrInput['watermark_height'],
//            'watermark_dot'         => $arrInput['watermark_dot'],
            'op_uname'	            => $arrInput['op_uname'],
            'op_uid'	            => $arrInput['op_uid'],
            
        );
        if($bolWater){
            $arrWaterPositionInfo['watermark_width']  = intval($arrInput['watermark_width']);
            $arrWaterPositionInfo['watermark_height'] = intval($arrInput['watermark_height']);
            $arrWaterPositionInfo['watermark_dot']    = $arrInput['watermark_dot'];
            $arrWaterPositionInfo['img_width']        = intval($arrInput['img_width']);
            $arrWaterPositionInfo['img_height']       = intval($arrInput['img_height']);
        }
        if($bolCut){
            //增加切片信息
//            $arrWaterPositionInfo['head']  = $arrInput['head'];
//            $arrWaterPositionInfo['tail']  = $arrInput['tail'];
            $arrWaterPositionInfo['editing']  = $arrInput['edit'];
        }
        
        Bingo_Log::warning("water_position_info::".serialize($arrWaterPositionInfo));
        $strPositionInfo = json_encode($arrWaterPositionInfo);

        // step 2 : build redis input

        $strWatermarkThreadKey      = Lib_Define_Edit::WATERMARK_THREAD_PREFIX.$intThreadId;
        $strWatermarkThreadValue    = $strPositionInfo;

		/*
		$redis = Util_Redis::_getRedis(Lib_Define_Edit::WATERMARK_REDIS);
		$arrRedisInput = array(
			'key'   => $strWatermarkThreadKey,
			'start' => 0,
			'stop'  => -1,
		);
		$arrRedisOutput = $redis->LRANGE($arrRedisInput);
		Bingo_Log::warning(var_export($arrRedisOutput, true));
		exit;
		*/

        // step 3 : lpush into redis
        $redis = Util_Redis::_getRedis(Lib_Define_Edit::WATERMARK_REDIS);
        $arrRedisInput = array(
            'key'   => $strWatermarkThreadKey,
            'value' => $strWatermarkThreadValue,
        );

        $arrRedisOut= $redis->LPUSH($arrRedisInput);
        if (false === $arrRedisOut) {
            Bingo_Log::warning("call redis failed");
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $inputEXPIRE = array(
            'key' => $strWatermarkThreadKey,
            'seconds' => 86400 * 5,
        );
        $outEXPIRE = $redis->EXPIRE($inputEXPIRE);
        if (false === $outEXPIRE) {
            Bingo_Log::warning("EXPIRE error!!!");
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

		// step4：将水印位置信息存储到mysql

		// 获取数据库句柄
		$db = Tieba_Mysql::getDB('forum_movideo');
		if(!$db){
			Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . 'connect database failed');
			return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$db->charset('utf8');

		// insert
		$arrInsertParams = array(
			'thread_id' => intval($arrInput['thread_id']),
			'op_uid'    => intval($arrInput['op_uid']),
			'op_uname'  => strval($arrInput['op_uname']),
			'position'  => $strPositionInfo,
		);
		$intEffectedRows = $db->insert('position_log', $arrInsertParams);
		if(false === $intEffectedRows){
			Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' insert position log failed. input: [' . serialize($arrInsertParams) . ']; output: [' . serialize($intEffectedRows) . ']');
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}

        // return
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,array());
    }
    
    /**
     * @brief 慢启动上报
     * @param $arrInput
     * @return array
     */
    public static function reportSlowStart($arrInput)
    {
        $intThreadId = isset($arrInput['thread_id']) ? (int)$arrInput['thread_id'] : 0;
        $arrReport = isset($arrInput['report']) ? (array)$arrInput['report'] : array();
        if ($intThreadId <= 0) {
            Bingo_Log::warning('param error, input='.json_encode($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrReport['thread_id'] = $intThreadId;

        if (!Lib_Transcode::pushSlowStartReportToList($intThreadId, $arrReport)) {
            Bingo_Log::warning('fail to push slow start report to list, thread_id='.$intThreadId.', report='.json_encode($arrReport));
            return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief 获得当前慢启动上报列表
     * @param $arrInput
     * @return array
     */
    public static function getSlowStartReportList($arrInput)
    {
        $intPn = isset($arrInput['pn']) ? (int)$arrInput['pn'] : 1;
        $intRn = isset($arrInput['rn']) ? (int)$arrInput['rn'] : 0;
        if ($intPn <= 0 || $intRn < 0) {
            Bingo_Log::warning('param error, input='.json_encode($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intOffset = ($intPn - 1) * $intRn;

        if (!Lib_Transcode::getLengthOfSlowStartReportList($intLength)) {
            Bingo_Log::warning('fail to get length of slow start report list');
            return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrReportList = array();
        if ($intRn > 0 && !Lib_Transcode::getSlowStartReportList($intOffset, $intRn, $arrReportList)) {
            Bingo_Log::warning('fail to get slow start report list, offset='.$intOffset.', count='.$intRn);
            return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrOutput = array(
            'total_count' => $intLength,
            'list' => $arrReportList,
        );

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    /**
     * @param $arrInput
     * @return array
     * 视频编辑后台搜索功能service
     */
    public static function selectVideoFromMisForRec($arrInput) {   //UI做参数合格检查,这里不做参数检查

        $intPn                     = intval($arrInput['pn']);
        $intPs                     = intval($arrInput['ps']);
        $intOpUid                  = intval($arrInput['op_uid']);
        $intEditFinishTimeFrom     = intval($arrInput['thread_create_time_from']);
        $intEditFinishTimeTo       = intval($arrInput['thread_create_time_to']);
        $intEditFinishTimeSortType = intval($arrInput['thread_create_time_sort_type']);/////////////
        $arrPageFlag               = $arrInput['page_flag'];
        $intUserId                 = intval($arrInput['user_id']);
        $strBjhUserName            = strval($arrInput['bjh_user_name']);
        $intEditStatus             = intval($arrInput['edit_status']);
        $intEditResult             = intval($arrInput['edit_result']);
        $intDispatchStatus         = intval($arrInput['dispatch_status']);
        $arrVideoFrom              = $arrInput['video_from'];
        $strFirCategory            = strval($arrInput['fir_category']);
        $strSecCategory            = strval($arrInput['sec_category']);
        $intVideoQuality           = strval($arrInput['video_quality']);
        $intIsVideoSquare          = intval($arrInput['is_video_square']);
        $intVideoDurationFrom      = intval($arrInput['video_duration_from']);
        $intVideoDurationTo        = intval($arrInput['video_duration_to']);
        $strTag                    = strval($arrInput['tag']);
        $strLevel1Name             = strval($arrInput['level_1_name']);
        $strLevel2Name             = strval($arrInput['level_2_name']);
        $strOpUName                = strval($arrInput['op_uname']);
        $intVideoScoreFrom         = intval($arrInput['video_score_from']);
        $intVideoScoreTo           = intval($arrInput['video_score_to']);
        $intOpTimeFrom             = intval($arrInput['edit_finish_time_from']);
        $intOpTimeTo               = intval($arrInput['edit_finish_time_to']);
        $strRejectFiled            = strval($arrInput['reject_field']);
        $strRejectReason           = strval($arrInput['reject_reason']);
        $intPreSyncTimeFrom        = intval($arrInput['pre_sync_time_from']);
        $intPreSyncTimeTo          = intval($arrInput['pre_sync_time_to']);
        $intIsPageUp               = intval($arrInput['is_page_up']);
        $create_time_from          = intval($arrInput['create_time_from']);    //内容流入编辑后台起始时间
        $create_time_to            = intval($arrInput['create_time_to']);    //内容流入编辑后台终止时间
        $tbTitle                   = urldecode(trim($arrInput['tbTitle']));   //贴吧标题
        $intThreadId               = intval($arrInput['thread_id']);      //贴子ID
        $intVideoSiteId            = intval($arrInput['video_site_id']);        //来源站点ID
        $intVideoType              = strval($arrInput['video_type']);
        $intSearchType             = intval($arrInput['search_type']);     //为了筛出微视频 新增加了search_type=9
        $intIsVideoGood            = intval($arrInput['is_video_good']);    //是否符合微视频精选

        $intNeedTotalCount = isset($arrInput['need_total_count']) ? intval($arrInput['need_total_count']) : 0;
        $intNeedData = isset($arrInput['need_data']) ? intval($arrInput['need_data']) : 0;

        $strSqlCondition = '';
        $strSqlOrderBy = 'thread_id desc';
        $arrTime = array();

        if(empty($intEditFinishTimeSortType)) {
            if($intPn > 1) {     //不是第一页
                if(1 == $intIsPageUp) {
                    $strSqlCondition = $strSqlCondition . ' thread_id > ' . intval($arrPageFlag['thread_id']) . " and ";
                }
                else {
                    $strSqlCondition = $strSqlCondition . ' thread_id < ' . intval($arrPageFlag['thread_id']) . " and ";
                }

            }
            $strSqlOrderBy = 'thread_id desc';
        }
        else {
            if(1 == $intEditFinishTimeSortType) {  //倒序
                if($intPn > 1) {     //不是第一页
                    if(1 == $intIsPageUp) {
                        $strSqlCondition = $strSqlCondition . ' thread_create_time >= ' . intval($arrPageFlag['thread_create_time']) . " and ";
                    }
                    else {
                        $strSqlCondition = $strSqlCondition . ' thread_create_time <= ' . intval($arrPageFlag['thread_create_time']) . " and ";
                    }
                }
                else {
                    $strSqlCondition = $strSqlCondition . ' thread_create_time > 0  and ';
                }
                $strSqlOrderBy = 'thread_create_time desc';
            }
            else if (2 == $intEditFinishTimeSortType) {   //升序
                if($intPn > 1) {     //不是第一页
                    if(1 == $intIsPageUp) {
                        $strSqlCondition = $strSqlCondition . ' thread_create_time <= ' . intval($arrPageFlag['thread_create_time']) . " and ";
                    }
                    else {
                        $strSqlCondition = $strSqlCondition . ' thread_create_time >= ' . intval($arrPageFlag['thread_create_time']) . " and ";
                    }
                }
                else {
                    $strSqlCondition = $strSqlCondition . ' thread_create_time > 0  and ';
                }
                $strSqlOrderBy = 'thread_create_time asc';
            }
        }

        if(!empty($intEditFinishTimeFrom) && !empty($intEditFinishTimeTo)) {
            $arrTime[] = $intEditFinishTimeFrom;
            $arrTime[] = $intEditFinishTimeTo;
            $strSqlCondition = $strSqlCondition . ' thread_create_time >= ' . $intEditFinishTimeFrom . " and thread_create_time <= " . $intEditFinishTimeTo . " and ";
        }
        if(!empty($intUserId)) {
            $strSqlCondition = $strSqlCondition . ' user_id = ' . $intUserId . " and ";
        }
        if(!empty($strBjhUserName)) {
            $strBjhUserName = mysql_escape_string($strBjhUserName);
            $strSqlCondition = $strSqlCondition . " bjh_user_name = '$strBjhUserName' " . ' and ';
        }
        if(!empty($intEditStatus)) {
            $strSqlCondition = $strSqlCondition . ' edit_status = ' . $intEditStatus . " and ";
        }
        if(!empty($intEditResult)) {
            $strSqlCondition = $strSqlCondition . ' edit_result = ' . $intEditResult . " and ";
        }
        if(!empty($intDispatchStatus)) {
            $strSqlCondition = $strSqlCondition . ' dispatch_status = ' . $intDispatchStatus . " and ";
        }
        if(!empty($arrVideoFrom)) {
            $strTemp = '(' . implode(',' , $arrVideoFrom) . ')';
            $strSqlCondition = $strSqlCondition . ' video_from in ' . $strTemp . " and ";
        }
        if(!empty($strFirCategory)) {
            $strFirCategory = mysql_escape_string($strFirCategory);
            $strSqlCondition = $strSqlCondition . " fir_category = '$strFirCategory' " . " and ";
        }
        if(!empty($strSecCategory)) {
            $strSecCategory = mysql_escape_string($strSecCategory);
            $strSqlCondition = $strSqlCondition . " sec_category = '$strSecCategory' " . " and ";
        }
        if(!empty($intVideoQuality)) {
            $strSqlCondition = $strSqlCondition . ' video_quality = ' . $intVideoQuality . " and ";
        }
        if(!empty($intIsVideoSquare)) {
            $strSqlCondition = $strSqlCondition . ' is_video_square = ' . $intIsVideoSquare . " and ";
        }
        //符合微视频
        if($intSearchType == Lib_Define_Edit::SEARCH_TYPE_MINI_VIDEO){
            $arrSearchTypeInput = array(
                'amis_user' => $strOpUName,
                'search_type' => array(
                    $intSearchType,
                ),
            );
            $arrSearchTypeOutput = Lib_Edit::getSearchTypeWithBiases($arrSearchTypeInput);
            $strSearchTypeQuery = implode(',' , $arrSearchTypeOutput['search_type']);
            $strSqlCondition = $strSqlCondition . " search_type in ($strSearchTypeQuery) " . ' and ';
        }
        //符合微视频精选
        if(!empty($intIsVideoGood)){
            $strSqlCondition = $strSqlCondition . ' is_video_good = ' . $intIsVideoGood . ' and ';
        }
        if(!empty($intVideoDurationFrom)) {
            $strSqlCondition = $strSqlCondition . ' video_duration >= ' . $intVideoDurationFrom . " and ";
        }
        if(!empty($intVideoDurationTo)) {
            $strSqlCondition = $strSqlCondition . ' video_duration <= ' . $intVideoDurationTo . " and ";
        }
        if(!empty($strTag)) {
            $strSqlCondition = $strSqlCondition . " tag like '%$strTag%' and ";
        }
        if(!empty($strLevel1Name)) {
            $strSqlCondition = $strSqlCondition . " level_1_name = '$strLevel1Name' " . " and ";
        }
        if(!empty($strLevel2Name)) {
            $strSqlCondition = $strSqlCondition . " level_2_name = '$strLevel2Name' " . " and ";
        }
        if(!empty($intOpUid)) {
            $strSqlCondition = $strSqlCondition . " op_uid = $intOpUid  " . " and ";
        }
        if(!empty($intVideoScoreFrom)) {
            $strSqlCondition = $strSqlCondition . ' video_score >= ' . $intVideoScoreFrom . " and ";
        }
        if(!empty($intVideoScoreTo)) {
            $strSqlCondition = $strSqlCondition . ' video_score <= ' . $intVideoScoreTo . " and ";
        }
        if(!empty($intOpTimeFrom) && !empty($intOpTimeTo)) {
            $arrTime[] = $intOpTimeFrom;
            $arrTime[] = $intOpTimeTo;
            $strSqlCondition = $strSqlCondition . ' edit_finish_time >= ' . $intOpTimeFrom . " and edit_finish_time <= " . $intOpTimeTo . " and ";
        }
        if(!empty($create_time_from) && !empty($create_time_to)){
            $arrTime[] = $create_time_from;
            $arrTime[] = $create_time_to;
            $strSqlCondition .= ' create_time >= '.$create_time_from.' and create_time <= '.$create_time_to.' and ';
        }
        if(!empty($strRejectFiled)) {
            $strRejectFiled = mysql_escape_string($strRejectFiled);
            $strSqlCondition = $strSqlCondition . " reject_field = '$strRejectFiled' " . " and ";
        }
        if(!empty($strRejectReason)) {
            $strRejectReason = mysql_escape_string($strRejectReason);
            $strSqlCondition = $strSqlCondition . " reject_reason = '$strRejectReason' " . " and ";
        }
        if(!empty($intPreSyncTimeFrom) && !empty($intPreSyncTimeTo)) {
            $arrTime[] = $intPreSyncTimeFrom;
            $arrTime[] = $intPreSyncTimeTo;
            $strSqlCondition = $strSqlCondition . ' pre_sync_time >= ' . $intPreSyncTimeFrom . " and pre_sync_time <= " . $intPreSyncTimeTo . " and ";
        }

        if( $intThreadId > 0 ){
            $strSqlCondition .= ' thread_id='.$intThreadId.' AND ';
        }
        //贴吧标题
        if( !empty($tbTitle) ){
            $tbTitle = mysql_escape_string($tbTitle);
            $strSqlCondition .= ' title="'.$tbTitle.'" AND ';
        }
        if( $intVideoSiteId > 0 ){
            $strSqlCondition .= ' video_site_id='.$intVideoSiteId.' and ';
        }
        if(!empty($intVideoType) && 0 !== intval($intVideoType)){
            $arrVideoTypes = explode(",", $intVideoType);
            if(count($arrVideoTypes)==1){
                $strSqlCondition .= ' video_type='.$intVideoType.' and ';
            }else{
                $strSqlCondition .= ' video_type IN('.$intVideoType.') and ';
            }
        }
        $strSqlCondition = $strSqlCondition . " 1=1 ";
        Bingo_Log::warning("sql condition = [$strSqlCondition]");

        if(empty($arrTime)) {
            Bingo_Log::warning("do not have time condition");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrTableName = self::getDataFromTableNameForRec($arrTime);

        if(empty($arrTableName)) {
            Bingo_Log::warning("have no table(table name empty). arrTime [" . serialize($arrTime) . "]");
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        Bingo_Log::warning("sql arrTableName. [" . serialize($arrTableName) . "]");

        $arrRetData = array();
        $objMultiX = new Tieba_Multi('selectVideoFromMis_multicall');

        if(1 == $intNeedTotalCount) {
            $objMultiParams['video']['selectVideoFromMisAllTotalCount'] = array(
                'tables' => $arrTableName,
                'condition' => $strSqlCondition,
            );
        }

        if(1 == $intNeedData) {
            $objMultiParams['video']['selectAllVideoFromMisData'] = array(
                'tables' => $arrTableName,
                'condition' => $strSqlCondition,
                'order_by'  => $strSqlOrderBy,
                'limit' => $intPs,
            );
        }

        foreach ($objMultiParams as $serviceName => $serviceCalls) {
            foreach ($serviceCalls as $method => $serviceCall) {
                $arrInput = array(
                    "serviceName" => $serviceName,
                    "method" => $method,
                    'ie' => 'utf-8',
                    "input" => $serviceCall,
                );
                $objMultiX->register("$method", new Tieba_Service($serviceName), $arrInput);
            }
        }

        Bingo_Timer::start('ala_updateLiveTb_core');
        $arrMultiOutput = $objMultiX->call();
        Bingo_Timer::end('ala_updateLiveTb_core');

        if(1 == $intNeedTotalCount) {
            $arrOutput = $arrMultiOutput['selectVideoFromMisAllTotalCount'];
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $arrServiceInput = $objMultiParams['video']['selectVideoFromMisTotalCount'];
                $strLog = "call video/selectVideoFromMisTotalCount fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::fatal($strLog);
                return self::_errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
            }
            //Bingo_Log::warning("+++>" . var_export($arrOutput, true)); // for test
            $arrRetData['page']['count'] = ($intPn - 1) * $intPs + intval($arrOutput['data']['total_count']);
            $arrRetData['page']['pn'] = $intPn;
            $arrRetData['page']['ps'] = $intPs;
            $arrRetData['hasMore'] = (intval($arrOutput['data']['total_count']) > ($intPn * $intPs)) ? 1 : 0;
        }

        if(1 == $intNeedData) {
            $arrOutput = $arrMultiOutput['selectAllVideoFromMisData'];
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $arrServiceInput = $objMultiParams['video']['selectAllVideoFromMisData'];
                $strLog = "call video/selectAllVideoFromMisData fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::fatal($strLog);
                return self::_errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
            }
            $arrList = $arrOutput['data'];

            if(empty($intEditFinishTimeSortType)) {
                $arrSort = array();
                foreach ($arrList as $key => $value) {
                    $arrSort[$key] = intval($value['thread_id']);
                }
                array_multisort($arrSort, SORT_DESC, $arrList);
            }
            else if (1 == $intEditFinishTimeSortType) {   //desc
                $arrSort = array();
                foreach ($arrList as $key => $value) {
                    $arrSort[$key] = intval($value['thread_create_time']);
                }
                array_multisort($arrSort, SORT_DESC, $arrList);
            }
            else if (2 == $intEditFinishTimeSortType) {
                $arrSort = array();
                foreach ($arrList as $key => $value) {
                    $arrSort[$key] = intval($value['thread_create_time']);
                }
                array_multisort($arrSort, SORT_ASC, $arrList);
            }


            $arrRetData['rows'] = array_slice($arrList, 0, $intPs);
        }

        // 获取用户信息和帖子信息
        Bingo_Timer::start("selectVideoFromMisData_mgetUserData");
        self::getExtInfoForVideos($arrRetData);
        Bingo_Timer::end("selectVideoFromMisData_mgetUserData");


        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);

    }

    /**
     * @brief 用于回溯编辑库的临时接口
     * @param $arrInput
     * @return array
     */
    public static function getEditInfoByTids($arrInput)
    {
        $strMonth = $arrInput['month'];
        $arrTids = $arrInput['tids'];
        if (!($intTime = strtotime($strMonth)) || empty($arrTids)) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'need month(required ep.2018-05) & tids(required)');
        }

        $strTable = 'video_edit_' . date('Ym', $intTime);
        is_string($arrTids) && $arrTids = explode(',', $arrTids);
        $strTids = implode(',', array_map('intval', $arrTids));
        $strSql = "select id, forum_id, user_id, thread_id, title, description, create_time, video_duration, video_length, video_url, video_md5, video_cover, video_type, dispatch_path, dispatch_status, edit_result, edit_status, video_from, op_uid, op_time, op_uname, thread_create_time, edit_finish_time, publish_time, pre_sync_time, video_quality, cover_quality, other_quality,title_quality, video_score, is_video_square,is_video_good, fir_category, sec_category, level_1_name, level_2_name, tag, frame_status, forum_name, bjh_user_name, reject_field, reject_reason, priority, title_base64, thumbnail_width, thumbnail_height, video_site_id, video_site_name,portrait_video_cover, portrait_cover_width, portrait_cover_height, sharpness from $strTable where thread_id in ($strTids)";
        $objDb = Tieba_Mysql::getDB('forum_movideo');
        $arrRows = $objDb->query($strSql);
        if ($arrRows === false) {
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, $strSql);
        }

        $arrData['rows'] = $arrRows;
        self::getExtInfoForVideos($arrData);

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrData['rows']);
    }

    /**
     * @brief 用于回溯编辑库的临时接口
     * @param $arrInput
     * @return array
     */
    public static function updateEditScoreByTids($arrInput)
    {
        $strMonth = $arrInput['month'];
        $arrTids = $arrInput['tids'];
        if (!($intTime = strtotime($strMonth)) || empty($arrTids) || !isset($arrInput['score']) || !isset($arrInput['op_uid']) || !isset($arrInput['op_uname'])) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'need month(required ep.2018-05) & tids(required) & score(required) & op_uid(required) & op_uname(required) & quality(optional)');
        }
        $intVideoScore = (int)$arrInput['score'];
        $intOpUid = (int)$arrInput['op_uid'];
        $strOpUname = (string)$arrInput['op_uname'];

        $strTable = 'video_edit_' . date('Ym', $intTime);
        is_string($arrTids) && $arrTids = explode(',', $arrTids);
        $strTids = implode(',', array_map('intval', $arrTids));

        $intCurrentTime = time();
        $arrFields = array(
            'video_score' => $intVideoScore,
            'edit_finish_time' => $intCurrentTime,
            'op_uid' => (int)$intOpUid,
            'op_uname' => $strOpUname,
            'op_time' => $intCurrentTime,
        );
        if (isset($arrInput['video_quality'])) {
            $arrFields['video_quality'] = $arrInput['video_quality'];
        }
        $strConds = "thread_id in ($strTids)";
        $objDb = Tieba_Mysql::getDB('forum_movideo');
        $intRows = $objDb->update($strTable, $arrFields, $strConds);
        if ($intRows === false) {
            Bingo_Log::warning('update forum_movideo.'.$strTable.' fail, fields:'.json_encode($arrFields). ', conds:'
                .$strConds.', sql:'.$objDb->getLastSQL());
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $intRows);
    }

    /**
     * @brief 获取视频信息给小度在家
     * @param $arrInput
     * @return array
     */
    public static function getVideoForXiaoDuZaiJia ($arrInput) {

        $intRn  = (intval($arrInput['rn'])>0) ? intval($arrInput['rn']) : 10;
        $intTid = intval($arrInput['start_id'])>0 ? intval($arrInput['start_id']) : 5880114788; //5880114788是20180914时间之前满足需求最新的视频的tid(20180914-05:09:02).

        //先获取需要查询的编辑表
        $arrParam = array(
            "thread_ids"      => array(
                0 => $intTid
            ),
            "need_abstract"   => 0,
            "forum_id"        => 0,
            "need_photo_pic"  => 0,
            "need_user_data"  => 0,
            "icon_size"       => 0,
            "need_forum_name" => 0,
        );
        $arrRet = Tieba_Service::call('post', 'mgetThread', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call post mgetThread failed. [input = %s][output = %s]", serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        if (empty($arrRet['output']['thread_list'][$intTid])) {
            Bingo_Log::warning("get the start_id create time is empty from mgetThread!");
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $intCreateTime = intval($arrRet['output']['thread_list'][$intTid]['create_time']);
        $strCreateTime = date('Ym', $intCreateTime);
        $strTableName  = "video_edit_" . $strCreateTime;

        $db = self::getDB('forum_movideo');
        if($db === null){
            Bingo_Log::warning("DB link fail.");
            return self::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $arrField = array(
            'thread_id',
            'video_score',
        );
        $strCond = " thread_id < $intTid and video_score > 2 and video_type in (12,207,210) order by thread_create_time desc limit $intRn";
        $arrRet  = $db->select($strTableName, $arrField, $strCond, null, null);
        if($arrRet === false){
            Bingo_Log::warning("select  fail! sql: ".$db->getLastSQL().' affect_num:'.$db->getAffectedRows()." [output:".serialize($db->error())."]");
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrTidInfo = $arrRet;
        $arrTid     = array_column($arrRet, 'thread_id');

        //有可能需要查询两个表
        if (count($arrRet) < $intRn) {
            if (strcmp($strCreateTime,"201801") == 0) {
                $strCreateTime = "201712";
            }
            else if (strcmp($strCreateTime,"201701") == 0) {
                $strCreateTime = "201612";
            }
            else
            {
                $strCreateTime = date('Ym', strtotime("-1 months", strtotime(strval($strCreateTime))));
            }
            $strTableName  = "video_edit_" . $strCreateTime;
            $intRn   = $intRn - count($arrRet);
            $strCond = " thread_id < $intTid and video_score > 2 and video_type in (12,207,210) order by thread_create_time desc limit $intRn";
            $arrRet  = $db->select($strTableName, $arrField, $strCond, null, null);
            if($arrRet === false){
                Bingo_Log::warning("select  fail! sql: ".$db->getLastSQL().' affect_num:'.$db->getAffectedRows()." [output:".serialize($db->error())."]");
                return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            $arrOtherTidInfo = $arrRet;
        }

        if (!empty($arrOtherTidInfo)) {
            $arrTidInfo = array_merge($arrTidInfo, $arrOtherTidInfo);
            $arrTid     = array_merge($arrTid, (array_column($arrOtherTidInfo, 'thread_id')));
        }

        if (empty($arrTidInfo) || empty($arrTid)) {
            Bingo_Log::warning("the tid is  empty!");
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrParam = array(
            "thread_ids"      => $arrTid,
            "need_abstract"   => 0,
            "forum_id"        => 0,
            "need_photo_pic"  => 0,
            "need_user_data"  => 0,
            "icon_size"       => 0,
            "need_forum_name" => 0,
        );
        $arrRet = Tieba_Service::call('post', 'mgetThread', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call post mgetThread failed. [input = %s][output = %s]", serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        if (empty($arrRet['output']['thread_list'])) {
            Bingo_Log::warning("get thread_list via post:mgetThread empty!");
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        foreach($arrTidInfo as $item){
            $arrThreadInfo = $arrRet['output']['thread_list'][$item['thread_id']];
            if (!empty($arrThreadInfo)) {
                $arrTidData['thread_id']      = intval($arrThreadInfo['thread_id'])>0 ? intval($arrThreadInfo['thread_id']) : 0;
                $arrTidData['title']          = empty($arrThreadInfo['title']) ? '' : $arrThreadInfo['title'];
                $arrTidData['create_time']    = intval($arrThreadInfo['create_time'])>0 ? intval($arrThreadInfo['create_time']) : 0;
                $arrTidData['video_type']     = intval($arrThreadInfo['video_info']['video_type'])>0 ? intval($arrThreadInfo['video_info']['video_type']) : 0;
                $arrTidData['video_score']    = intval($item['video_score'])>0 ? intval($item['video_score']) : 0;
                $arrTidData['video_url']      = empty($arrThreadInfo['video_info']['video_desc'][0]['video_url']) ? '' : $arrThreadInfo['video_info']['video_desc'][0]['video_url'];
                $arrTidData['video_cover']    = empty($arrThreadInfo['video_info']['first_frame_cover']) ? '' : $arrThreadInfo['video_info']['first_frame_cover'];
                $arrTidData['video_duration'] = empty($arrThreadInfo['video_info']['video_duration']) ? '' : $arrThreadInfo['video_info']['video_duration'];
                $arrOutput[] = $arrTidData;
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    /**
     * @param null $dbname
     * @param string $charset
     * @return null
     */
    public static function getDB($dbname = null, $charset = 'utf8')
    {
        if(is_null($dbname)){
            $dbname = 'forum_movideo';
        }
        $objTbMysql = Tieba_Mysql::getDB($dbname);
        if($objTbMysql && $objTbMysql->isConnected()){
            $objTbMysql->charset($charset);
            return $objTbMysql;
        }else{
            Bingo_Log::warning("db connect fail.");
            return null;
        }
    }

    /**
     * @param $arrInput
     * @return array
     * @复遍重构专用
     */
    public static function addEditVideoToEsDb($arrInput) {
        if(empty($arrInput['thread_id'])) {
            Bingo_Log::warning("error params. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $intThreadId = intval($arrInput['thread_id']);
        $strRejectField = strval($arrInput['reject_field']);
        $intTmp = intval($arrInput['video_from']);
        $intVideoSearchType = intval($arrInput['search_type']);
        $intIsVideoGood = intval($arrInput['is_video_good']);

        // isRecommendCallback
        $booIsRecommendCallback = intval($arrInput['is_recommend']);

        $intCallbackType = !empty($arrInput['callback_type']) ? intval($arrInput['callback_type']) : 0;


        $arrThreadInput = array(
            "thread_ids" => array($intThreadId,),
            "need_abstract" => 0,
            "forum_id" => 0,
            "need_photo_pic" => 0,
            "need_user_data" => 1,
            "icon_size" => 0,
            "need_forum_name" => 0,  //是否获取吧名
            "call_from" => "client_frs", //上游模块名,这里只能用client_frs
        );
        $arrThreadOutput = Tieba_Service::call('post', 'mgetThread', $arrThreadInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrThreadOutput || Tieba_Errcode::ERR_SUCCESS != $arrThreadOutput['errno']) {
            $arrThreadOutput = Tieba_Service::call('post', 'mgetThread', $arrThreadInput, null, null, 'post', 'php', 'utf-8');
            if (false === $arrThreadOutput || Tieba_Errcode::ERR_SUCCESS != $arrThreadOutput['errno']) {
                Bingo_Log::warning('call post/mgetThread err! [' . serialize(compact('arrThreadInput', 'arrThreadOutput')) . ']');
                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }

        $arrData = $arrThreadOutput['output']['thread_list'][$intThreadId];

        $strVideoUrl = strval($arrData['video_info']['video_url']);
        $strCoverUrl = strval($arrData['video_info']['thumbnail_url']);
        $strVideoMd5 = strval($arrData['video_info']['video_md5']);
        $strVideoType = strval($arrData['video_info']['video_type']);
        if($strVideoType >= 100 && $strVideoType <= 500){
            if(empty($strVideoMd5)){
                $strVideoMd5 = md5($strVideoUrl);
            }
        }
        if(empty($strVideoMd5)) {
            Bingo_Log::warning("empty video_md5");
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        $arrTableName = (array)(self::VIDEO_EDIT_TABLE_PREFIX.'es');

        foreach ($arrTableName as $strTableName) {
            if(1 == $intTmp && empty($strRejectField)) {   //机筛,,最近六个月有相同MD5,thread_id,
                $arrDlInput['function'] = 'selectGreatVideoFromMis';
                $arrDlInput['table_name'] = $strTableName;
                $arrDlInput['video_md5'] = $strVideoMd5;
                $arrDlInput['thread_id'] = $intThreadId;
                $arrOutputRet = Dl_Video_Sql::execSql($arrDlInput);
                if(false === $arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                    Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');
                    return self::_errRet(isset($arrOutputRet['errno']) ? $arrOutputRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
                if(!empty($arrOutputRet['results'][0])) {
                    foreach ($arrOutputRet['results'][0] as $value) {
                        $intTmp = intval($value['thread_id']);
                        if($intTmp == $intThreadId) {
                            Bingo_Log::warning("repeat thread_id");
                            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
                        }
                    }
                    Bingo_Log::warning("video_md5=$strVideoMd5 or thread_id=$intThreadId,  has existed in mis");
                    $strRejectField = "机筛MD5重复";
                    $arrParam = array(
                        'thread_id'   => $intThreadId,
                        'video_md5'   => $strVideoMd5,
                    );
                    $arrRet = Service_Edit_Edit::addMd5RepeatVideoInfo($arrParam);
                    //return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
                }
            }
            else if ( 2 == $intTmp ) {
                $arrDlInput['function'] = 'selectGreatVideoFromMisStrategy';
                $arrDlInput['table_name'] = $strTableName;
                $arrDlInput['video_md5'] = $strVideoMd5;
                $arrDlInput['thread_id'] = $intThreadId;
                $arrOutputRet = Dl_Video_Sql::execSql($arrDlInput);
                if(false === $arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                    Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');
                    return self::_errRet(isset($arrOutputRet['errno']) ? $arrOutputRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }

                if(!empty($arrOutputRet['results'][0])) {
                    Bingo_Log::warning("video_md5=$strVideoMd5 or thread_id=$intThreadId,  has existed in mis");
                    return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
                }
            }
        }

        $intEditResult = 0 ;
        $intEditStatus = 4; //直接复遍
        if(!empty($strRejectField)) {
            $intEditResult = 2;  //拒绝
            $intEditStatus = 1;
        }
        $intUserId = intval($arrData['user_id']);
        $arrServiceInput = array(
            'user_id' => $intUserId,
        );
        $strServiceName = "user";
        $strServiceMethod = "mgetUserDataEx";
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,"post",null,"utf-8");
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            Bingo_Log::warning("call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrUserData = $arrOutput['user_info'][$intUserId];
        $bolIsPGC = (!empty($arrData['video_channel_info']) || !empty($arrUserData['god']) || $strVideoType == 4);

        if(1 == $intTmp) {   //机筛数据
            if($bolIsPGC) {
                $intVideoFrom = 10;  //pgc机筛
            }
            else {
                $intVideoFrom = 40; //ugc机筛
            }
        }
        else {
            if($bolIsPGC){
                $intVideoFrom = 20;  //pgc策略
            }
            else {
                $intVideoFrom = 30; //ugc策略
            }
        }

        // is recommend callback

        if($booIsRecommendCallback == 1){
            $intVideoFrom = Lib_Define_Edit::VIDEO_FROM_RECOMMEND;
        }

        $intForumId = intval($arrData['forum_id']);
        //支持无吧贴
        if($intForumId != 0){
            $arrServiceInput = array(
                'forum_id' => $intForumId,
            );
            $strServiceName = "forum";
            $strServiceMethod = "getForumDir";
            $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,"post",null,"utf-8");
            if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                Bingo_Log::warning("call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $arrForumDir = $arrOutput['output'];
            $strLevel1Name = $arrForumDir['level_1_name'];
            $strLevel2Name = $arrForumDir['level_2_name'];
        }else{
            //无吧贴1 2级目录直接为空
            $strLevel1Name = '';
            $strLevel2Name = '';
        }
        if(isset(Util_Tag::$arrForumDir2ToMap[$strLevel2Name])) {
            $strSecDir = Util_Tag::$arrForumDir2ToMap[$strLevel2Name];
        }
        else {
            $strSecDir = '广告';
        }
        $strFirstDir = Util_Tag::$arrSecDirMapFirstDir[$strSecDir];



        $arrServiceInput = array(
            "thread_id" => $intThreadId, //帖子id
            "offset" => 0,
            "res_num" => 10,
            "see_author" => 1,
            "has_comment" => 0,
            "has_mask" => 1,
            "has_ext" => 1,
            "need_set_pv" => 0,
            "structured_content" => 0,
        );
        $strServiceName = "post";
        $strServiceMethod = "getPostsByThreadId";
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,"post",null,"utf-8");
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            Bingo_Log::warning("call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]");
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrPostInfo = $arrOutput['output']['output'][0]['post_infos'];
        $strDesc = strval($arrPostInfo[1]['content']);   //帖子作者的第二楼

        //过滤主端故事视频贴
        if( intval($arrOutput['output']['output'][0]['thread_type']) == Tieba_Type_Thread::STORY_VIDEO_THREAD ){
            Bingo_Log::pushNotice('isStory', 1);
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        //Bingo_Log::warning("desc===============>" . var_export($strDesc, true));  // for test

        $intThumbnailHeight = intval($arrData['video_info']['thumbnail_height']);
        $intThumbnailWidth = intval($arrData['video_info']['thumbnail_width']);

        $intPortraitCoverWidth = 0;
        $intPortraitCoverHeight = 0;
        $strPortraitCoverUrl = "";
        if($intThumbnailHeight > $intThumbnailWidth){
            $arrOutput = getimagesize($strCoverUrl);
            if(!empty($arrOutput) && $arrOutput[0] < $arrOutput[1]){
                $strPortraitCoverUrl = $strCoverUrl;
                $intPortraitCoverWidth = $arrOutput[0];
                $intPortraitCoverHeight = $arrOutput[1];
            }
        }
        if($intThumbnailWidth <= 2048) {   //切图有宽高尺寸限制 http://man.baidu.com/ksarch/common/pic/#图片在线处理_说明
            if($intThumbnailWidth <= 980) {
                $intThumbnailNeedWidth = $intThumbnailWidth;
            }
            else {
                $arrOkSize = array(980, 1000,1024,1044,1050,1080,1100,1140,1152,1200,1280,1300,1360,1366,1400,1440,1458,1500,1600,1680,1856,1920,2026,2048);
                foreach ($arrOkSize as $key => $intSize) {
                    if(intval($intSize) <= $intThumbnailWidth) {
                        $intThumbnailNeedWidth = $intSize;
                    }
                }
            }
            $intThumbnailNeedHeight = ceil(($intThumbnailNeedWidth / 16.0) * 9);
            $strSwitchCoverUrl = Util_Pic::extUrlToInnerUrl($strCoverUrl, "awhcrop=$intThumbnailNeedWidth,$intThumbnailNeedHeight");
        }



        if(!empty($strSwitchCoverUrl)) {
            Bingo_Log::warning("use switch cover url");
            $strCoverUrl = $strSwitchCoverUrl;
            $intThumbnailHeight = $intThumbnailNeedHeight;
            $intThumbnailWidth = $intThumbnailNeedWidth;
        }


        $strInsertTableName = self::VIDEO_EDIT_TABLE_PREFIX . 'es';

        // audit_flag

        $intAuditFlag = self::getAuditFlagByThreadId($intThreadId);

        $arrDlInput = array(
            'function' => 'addGreatVideoToMis',
            'table_name' => $strInsertTableName,
            'forum_id' => intval($arrData['forum_id']),
            'forum_name' => strval($arrData['forum_name']),
            'user_id'  => intval($arrData['user_id']),
            'thread_id' => $intThreadId,
            'title' => strval($arrData['title']),
            'create_time' => time(),
            'video_duration' => intval($arrData['video_info']['video_duration']),
            'video_length' => (intval($arrData['video_info']['video_length']) > 0  ? intval($arrData['video_info']['video_length']) : intval($arrData['video_info']['video_size'])),
            'video_type' => intval($arrData['video_info']['video_type']),
            'video_url' => $strVideoUrl,
            'video_md5' => $strVideoMd5,
            'video_cover' => $strCoverUrl,
            'portrait_video_cover' => $strPortraitCoverUrl,
            'video_from' => $intVideoFrom,
            'thread_create_time' => intval($arrData['create_time']),
            'edit_status' => $intEditStatus,
            'edit_result'  => $intEditResult,
            'dispatch_status'  => 0,
            'bjh_user_name'  => '',
            'priority'  => 0,
            'ext2'  => 0,
            'ext3'  => '',
            'reject_field' => $strRejectField,
            'level_1_name' => $strLevel1Name,
            'level_2_name' => $strLevel2Name,
            'fir_category' => $strFirstDir,
            'sec_category' => $strSecDir,
            'portrait_cover_height' => $intPortraitCoverHeight,
            'portrait_cover_width' => $intPortraitCoverWidth,
            'thumbnail_width' => $intThumbnailWidth,
            'thumbnail_height' => $intThumbnailHeight,
            'description'  => $strDesc,
            'tag' => strval($arrInput['tag']),
            'video_site_id' => 0,
            'video_site_name' => '',
            'search_type' => $intVideoSearchType,
            'callback_type' => $intCallbackType,
            'audit_flag' => $intAuditFlag,
            'is_video_good' => $intIsVideoGood,
        );
        $categ =array(
            "对口型",
            "趣玩",
            "恶搞",
            "牛人集锦",
            "逗比",
            "整蛊");
        $intRandNum = rand(0,5);
        if( $arrDlInput['video_type'] == 206){
            $arrDlInput['fir_category'] = '搞笑趣味';
            $arrDlInput['edit_status'] = 6;
            $arrDlInput['edit_result'] = 1;
            $arrDlInput['sec_category'] = $categ[$intRandNum];
        }

        //added by ligengyong//
        do{
            //有可能
            if($intThreadId < 1 ) {
                Bingo_Log::warning('thread id empty .'.$intThreadId);
                break;
            }
            $input = array(
                'tids' => array(
                    $intThreadId,
                ),
            );
            $arrOutput = Tieba_Service::call('videocp','mgetTidInfo',$input,null,null,"post",null,"utf-8");
            if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                Bingo_Log::warning("call videocp mgetTidInfo fail. input:[".serialize($input)."]; output:[".serialize($arrOutput)."]");
                break;
            }
            $data = $arrOutput['data'][$intThreadId];
            if(empty($data)){
                Bingo_Log::warning("call videocp mgetTidInfo is not from xiaoying. input:[".serialize($input)."]; output:[".serialize($arrOutput)."]");
                break;
            }
            Bingo_Log::pushNotice('xiaoying_uid', $arrData['user_id']);
            $arrDlInput['video_site_id'] = intval($data['video_site_id']);
            $arrDlInput['video_site_name'] = trim($data['video_from']);

            if($data['video_from'] == '小影'){
                $arrDlInput['priority'] = 100; //100
                $arrDlInput['edit_status'] = 2; //站点细分 10.小影
                $arrDlInput['edit_result'] = 0; //站点细分 10.小影
                if(!empty($data['title'])){
                    $arrDlInput['ext3'] = $data['title'];
                }
                if(!empty($data['tags'])){
                    $arrDlInput['tag'] = $data['tags'];
                }
                $uid = intval($arrData['user_id']);
                if(!empty(Util_Tag::$mapFromTB2BJHofXiaoYing[$uid])){
                    $arrDlInput['bjh_user_name'] = Util_Tag::$mapFromTB2BJHofXiaoYing[$uid];
                }
                if(!empty(Util_Tag::$mapFromTB2BJHofXiaoYing1Category[$uid]) &&
                    !empty(Util_Tag::$mapFromTB2BJHofXiaoYing2Category[$uid])){
                    $arrDlInput['fir_category'] = Util_Tag::$mapFromTB2BJHofXiaoYing1Category[$uid];
                    $arrDlInput['sec_category'] = Util_Tag::$mapFromTB2BJHofXiaoYing2Category[$uid];
                }
            }
            if($data['video_from'] == 'msspFeeds'){
                if(!empty($data['tags'])){
                    $arrDlInput['tag'] = $data['tags'];
                }
                if(isset(Util_Tag::$strFirstMsspFeeds[$data['sub_category']])){
                    $data['sub_category'] = Util_Tag::$strFirstMsspFeeds[$data['sub_category']];
                }
                if (isset(Util_Tag::$arrSecDirMapFirstDir[$data['sub_category']])){
                    $arrDlInput['fir_category'] = Util_Tag::$arrSecDirMapFirstDir[$data['sub_category']];
                    $arrDlInput['sec_category'] = $data['sub_category'];
                }
            }
        }
        while(0);

        //end added by ligengyong //

        //create priority
        $arrPriInput = array(
            'pass' => empty($strRejectField) ? 1 : 0,
            'from' => $intTmp,
            'user_id' => intval($arrData['user_id']),
            'type' => $intVideoSearchType,
            'video_type' => intval($arrData['video_info']['video_type']),
        );
        //这里不进行白名单的查找,所以把user_id传过去
        $arrPriOut = Lib_Define_Edit::getPriority($arrPriInput);
        $arrDlInput['priority'] = $arrPriOut['priority'];
        $arrDlInput['search_type'] = $arrPriOut['search_type'];

        // change search_type here
        $intFinallySearchType = Lib_Edit::getInsertSearchTypeWithBiases(intval($arrDlInput['search_type']), intval($intThreadId));
        $arrDlInput['search_type'] = $intFinallySearchType !== null ? $intFinallySearchType : $arrDlInput['search_type'];

        if($intAuditFlag == 3 || $intAuditFlag == 2 ){
            $arrDlInput['priority'] = 70;
        }

        //end create priority
        if (isset($arrInput['source_flag']) && $arrInput['source_flag'] == 're_edit') {//流量来自复遍
            $arrDlInput['table_name'] = self::VIDEO_EDIT_TABLE_PREFIX.'es';
        }

        $arrOutputRet = Dl_Video_Sql::execSql($arrDlInput);
        if(false === $arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');
            return self::_errRet(isset($arrOutputRet['errno']) ? $arrOutputRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function isNotNeedToEdit($arrInput) {
        //check param
        if(empty($arrInput)) {
            Bingo_Log::warning("error params. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $intStatus = 0;//表示需要入编辑区，edit_status != 7

        // Nani 源视频不编辑
        if (Util_Const::$arrNaniSourceVTypes[$arrInput['video_info']['video_type']] == 1) {
            $intStatus = 1;
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $intStatus);
        }

        // MCN用户必须编辑
        $handleWordServer = Wordserver_Wordlist::factory();
        $strTableName = 'tb_wordlist_redis_MCN';
        $arrWordListKey = array(
            $arrInput['user_id'],
        );
        $arrRet = $handleWordServer->getValueByKeys($arrWordListKey, $strTableName);
        if (1 === intval($arrRet[$arrInput['user_id']])) {
            $intStatus = 0;
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $intStatus);
        }

        //1、筛选条件：播放时长太短的,目前限制的5秒
        if ($arrInput['video_info']['video_duration'] < 5) {
            $intStatus = 1;
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $intStatus);
        }

        //2、好看视频不再进行编辑
        //备注：2019年4月1日前，走人审，花预算
        /*
        $intTime = time();
        if ($arrInput['video_info']['video_type'] == 110 && $intTime >= 1553702400) {
            $intStatus = 1;
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $intStatus);
        }
         */

        /*
        $arrAllVideoType = array(1,12,13,207,210,211);
        //3、限定video_type非1、12、13、207、210、211的竖屏视屏资源不出现在编辑区
        if (!in_array($arrInput['video_info']['video_type'],$arrAllVideoType) && ($arrInput['video_info']['video_width'] < $arrInput['video_info']['video_height'])) {
            $intStatus = 1;
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $intStatus);
        }
         */

        //限制编辑的数量，
        //非1，12，13，207，210的不超过1.2W
        //1，12，13，207，210的不超过3.5W
        $objRedis = Util_Redis::_getRedis();
        if (empty($objRedis)) {
            Bingo_log::warning('call ala redis failed!');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'get redis失败请重试');
        }

        $strCurrDay = date("Y-m-d", time());
        $strKey = self::NO_NEED_TO_EDIT_TEMPORARILY_REDIS_KEY.$strCurrDay;

        $arrRedisInput = array(
            'key'      => $strKey,
        );
        $arrRet = $objRedis -> GET($arrRedisInput);
        if (0 != $arrRet['err_no'] || false == $arrRet) {
            Bingo_log::warning('call ala redis failed! $arrRedisInput=' . serialize($arrRedisInput));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'get redis失败请重试');
        }

        $intCount = intval($arrRet['ret'][$strKey]);
        if ($intCount > 50000) {   //非指定类型的视频每天最多编辑25000条
            $intStatus = 1;
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $intStatus);
        }

        $strKey = self::NO_NEED_TO_EDIT_TEMPORARILY_IN_REDIS_KEY.$strCurrDay;
        $arrRedisInput = array(
            'key'      => $strKey,
        );
        $arrRet = $objRedis -> GET($arrRedisInput);
        if (0 != $arrRet['err_no'] || false == $arrRet) {
            Bingo_log::warning('call ala redis failed! $arrRedisInput=' . serialize($arrRedisInput));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'get redis失败请重试');
        }
        $intCount = intval($arrRet['ret'][$strKey]);
        if ($intCount > 35000) {   //指定类型的视频每天最多编辑35000条
            $intStatus = 1;
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $intStatus);
    }

    public static function bosNmqDeleteCallback($intThreadId, $intCreateTime)
    {
        $strWeekkey    = Lib_Audit::getThatDayMonday($intCreateTime);

        // 查db获取视频信息
        $arrParams = array(
            'weekkey'    => $strWeekkey,
            'cond'       => array(
                'thread_id' => $intThreadId,
            ),
            'for_update' => false,
        );

        $arrRet = Lib_Audit::selectVideoInfo($arrParams);
        if (!$arrRet || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf("call Lib_Audit::selectVideoInfo failed! input[%s] output[%s]", serialize($arrParams), serialize($arrRet)));
            return false;
        }

        $arrAuditInfo = $arrRet['data'][0];

        // 获取转码视频列表
        $arrExtParam = json_decode($arrAuditInfo['ext_param'], true);
        // 格式有问题
        if (empty($arrExtParam) || !is_array($arrExtParam)) {
            return true;
        }

        $arrVideoInfo = array();
        foreach ($arrExtParam['ext_attr'] as $key => $value) {
            if ($value['key'] == 'video_info') {
                $arrVideoInfo = &$arrExtParam['ext_attr'][$key]['value'];
                break;
            }
        }
        // 没有视频相关的信息
        if (empty($arrVideoInfo) || empty($arrVideoInfo['video_desc'])) {
            return true;
        }

        // 存储需要删除的视频 url
        $arrVideoList = array();
        foreach ($arrVideoInfo['video_desc'] as $arrDesc) {
            $arrVideoList[] = $arrDesc['video_url'];
        }

        // 对应的video_desc写空
        unset($arrVideoInfo['video_desc']);
        $strExtParam = json_encode($arrExtParam);

        // 将数据同步回 db
        $arrParams = array(
            'field'      => array(
                'ext_param' => mysql_escape_string($strExtParam),
            ),
            'cond'       => array(
                'thread_id' => $intThreadId,
            ),
            'table_name' => Lib_Audit::VDIEO_TABLE_PRE . $strWeekkey,
        );

        $arrRet = Lib_Audit::updateVideoInfo($arrParams);
        if (!$arrRet || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf("call Lib_Audit::updateVideoInfo failed! input[%s] output[%s]", serialize($arrParams), serialize($arrRet)));
            return false;
        }

        // 更新帖子属性
        $bolIsSuc = Lib_Post::setExtAttr($arrAuditInfo['thread_id'], $arrAuditInfo['post_id'], $arrVideoInfo);
        if (!$bolIsSuc) {
            Bingo_Log::warning(sprintf("call Lib_Post::setExtAttr failed! input[%s %s %s]", $arrAuditInfo['thread_id'], $arrAuditInfo['post_id'], json_encode($arrVideoInfo)));
        } else {
            $arrFailed = Lib_Bos::deleteObject($arrVideoList, 'su');
        }

        return true;
    }

    public static function bosNmqRecoverCallback($intThreadId, $intCreateTime)
    {
        $strWeekkey    = Lib_Audit::getThatDayMonday($intCreateTime);

        // 查db获取视频信息
        $arrParams = array(
            'weekkey'    => $strWeekkey,
            'cond'       => array(
                'thread_id' => $intThreadId,
            ),
            'for_update' => false,
        );

        $arrRet = Lib_Audit::selectVideoInfo($arrParams);
        if (!$arrRet || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf("call Lib_Audit::selectVideoInfo failed! input[%s] output[%s]", serialize($arrParams), serialize($arrRet)));
            return false;
        }

        $intId = $arrRet['data'][0]['id'];

        $arrTrasInput = array(
            'video_audit_id' => $intId,
            'weekkey' => $strWeekkey,
            'delogo' => array(
                array(
                    'x' => 1,
                    'y' => 1,
                    'w' => 1,
                    'h' => 1,
                ),
            ),
            'video_url' => $arrRet['data'][0]['video_url'],
        );

        $arrTrasOutput = Tieba_Service::call('video', 'commitVideoDelogo', $arrTrasInput, null, null, 'post', 'php', 'utf-8');
        if (!$arrTrasOutput || $arrTrasOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf("call video::commitVideoDelogo failed! input[%s] output[%s]", json_encode($arrTrasInput), json_encode($arrTrasOutput)));
            return false;
        }

        return true;
    }

    public static function mcpTranscodeSign($arrInput) 
    {
        if (empty($arrInput['timestamp'])) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        Bingo_Timer::start('sign');

        $objTime = new DateTime();
        $objTime->setTimestamp($arrInput['timestamp']);
        $objTime->setTimezone(new DateTimeZone("UTC"));

        $arrHeaders = array(
            'Host' => 'media.su.baidubce.com',
            'X-Bce-Date' => $objTime->format('Y-m-d\TH:i:s\Z'),
            'Content-Type' => 'application/json',
        );
        $strAuthKey = Lib_BDCloudSign::sign('POST', '/v3/job/transcoding', $arrHeaders, $objTime, array());

		Bingo_Timer::end('sign');

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $strAuthKey);
    }

    public static function mcpMediaInfoSign($arrInput) 
    {
        if (empty($arrInput['timestamp']) || empty($arrInput['bucket']) || empty($arrInput['key'])) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        Bingo_Timer::start('sign');

        $objTime = new DateTime();
        $objTime->setTimestamp($arrInput['timestamp']);
        $objTime->setTimezone(new DateTimeZone("UTC"));

        $arrHeaders = array(
            'Host' => 'media.su.baidubce.com',
            'X-Bce-Date' => $objTime->format('Y-m-d\TH:i:s\Z'),
            'Content-Type' => 'application/json',
        );

        $arrParams = array(
            'bucket' => $arrInput['bucket'],
            'key' => $arrInput['key'],
        );
        $strAuthKey = Lib_BDCloudSign::sign('GET', '/v3/mediainfo', $arrHeaders, $objTime, $arrParams);

		Bingo_Timer::end('sign');

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $strAuthKey);
    }

    /**
     * 用来补充审核库里面的数据，当视频贴被发布时，宽高时长等数据可能没有或者不准确，用这个接口来覆盖赋予精确的数据
     */
    public static function writeBackVideoMedia($arrInput)
    {
        if (empty($arrInput['weekkey']) || empty($arrInput['audit_id'])) {
            Bingo_Log::warning(sprintf("param error. miss weekkey or audit_id"));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        Bingo_Log::warning(sprintf("writeBackVideoMedia input[%s]", json_encode($arrInput)));
        $strWeekkey = $arrInput['weekkey'];
        $intId = $arrInput['audit_id'];
        $intWidth = $arrInput['width'];
        $intHeight = $arrInput['height'];
        $intDuration = $arrInput['duration'];
        $strContainer = $arrInput['container'];
        $intSize = $arrInput['size'];

        $intErrorNo = Tieba_Errcode::ERR_SUCCESS;
        do {
            // 事务开始
            if (!Lib_Audit::startTransaction()) {
                Bingo_Log::warning('call Lib_Audit::startTransaction fail, input:['.json_encode($arrInput).']');
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            // 先查库取数据
            $arrInput = array(
                'cond' => array(
                    'id' => $intId,
                ),
                'weekkey' => $strWeekkey,
                'for_update' => true,
            );

            $arrOutput = Lib_Audit::selectVideoInfo($arrInput);

            // 查失败
            if (!$arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                Bingo_Log::warning('call Lib_Audit::selectVideoInfo fail, params:['.json_encode($arrInput).'],ret:['.json_encode($arrOutput).']');
                $intErrorNo = $arrOutput['errno'];
                break;
            }
            // 查不到
            if (empty($arrOutput['data'])) {
                Bingo_Log::warning('no this video record, params:['.json_encode($arrInput).'],ret:['.json_encode($arrOutput).']');
                $intErrorNo = Tieba_Errcode::ERR_NO_RECORD;
                break;
            }

            // 拿视频扩展信息
            $arrExtParam = json_decode($arrOutput['data'][0]['ext_param'], true);  // audit ext_param

            // 找到ext_param中的video_info字段
            foreach ($arrExtParam['ext_attr'] as $value) {
                if ($value['key'] == 'video_info') {
                    $arrVideoInfo = is_string($value['value']) ? unserialize($value['value']) : $value['value'];
                    break;
                }
            }

            $arrVideoInfo['video_duration'] = $intDuration;
            $arrVideoInfo['video_width'] = $intWidth;
            $arrVideoInfo['video_height'] = $intHeight;
            $arrVideoInfo['video_size'] = $intSize;
            $arrVideoInfo['video_format'] = $strContainer;

            // 更新 ext_attr
            foreach ($arrExtParam['ext_attr'] as &$value) {
                if ($value['key'] == 'video_info') {
                    $value['value'] = $arrVideoInfo;
                    break;
                }
            }

            // 回写转码状态信息和拓展信息
            $arrParams = array(
                'field'      => array(
                    'ext_param' => mysql_escape_string(json_encode($arrExtParam)),
                    'duration'      => $intDuration,
                    'video_height'  => $intHeight,
                    'video_width'   => $intWidth
                ),
                'cond'       => array(
                    'id' => $intId,
                ),
                'table_name' => Lib_Audit::VDIEO_TABLE_PRE . $strWeekkey,
            );

            // 将新的信息写回数据库
            Bingo_Log::warning(sprintf("prepare call Lib_Audit::updateVideoInfo input[%s]", json_encode($arrParams)));
            $arrRet = Lib_Audit::updateVideoInfo($arrParams);
            Bingo_Log::warning(sprintf("after call Lib_Audit::updateVideoInfo input[%s]", json_encode($arrRet)));
            if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
                Bingo_Log::fatal('call Lib_Audit:updateVideoInfo select db failed, params:['.json_encode($arrParams).'],ret:['.json_encode($arrRet).']');
                $intErrorNo = $arrRet['errno'];
                break;
            }
            
            // 提交事务
            if (!Lib_Audit::commit()) {
                Bingo_Log::warning('call Lib_Audit::commit fail, input:['.json_encode($arrInput).']');
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
        } while (0);

        // 更新DB失败
        if (Tieba_Errcode::ERR_SUCCESS != $intErrorNo) {
            if (!Lib_Audit::rollback()) {
                Bingo_Log::warning('call Lib_Audit::rollback fail, input:['.json_encode($arrInput).']');
            }
            return self::_errRet($intErrNo);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    public static function fmtTraceInfo($arrInput) {
        if (!isset($arrInput['trace'])) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrTraceInfo = $arrInput['trace'];

        $arrRet = array();
        foreach (Lib_TimeStreamLog::$arrFmtSort as $strKey) {
            foreach ($arrTraceInfo as $k => $v) {
                $arrKeySplit = explode(':', $k);
                $arrValueSplit = explode(';', $v);
                if (sizeof($arrKeySplit) != 3 || sizeof($arrValueSplit) != 2) {
                    Bingo_Log::warning(sprintf("table trace info fmt error. key[%s] value[%s]", $k, $v));
                    continue;
                }

                if ($arrKeySplit[2] == '0') {
                    $arrKeySplit[2] = '开始';
                } else if ($arrKeySplit[2] == '999') {
                    $arrKeySplit[2] = '结束';
                } else if ($arrKeySplit[2] > '0' && $arrKeySplit[2] < '999') {
                    $arrKeySplit[2] = '进行中';
                } else if ($arrKeySplit[2] == '-1') {
                    $arrKeySplit[2] = '异常结束';
                }

                if ($strKey == $arrKeySplit[1]) {
                    $arrRet[] = array(
                        'scene' => $arrKeySplit[1],
                        'flow' => $arrKeySplit[2],
                        'meaning' => $arrValueSplit[1],
                        'time' => $arrValueSplit[0],
                        'is_error' => $arrKeySplit[2] == '-1' ? '是' : '',
                    );
                }
            }
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }

    public static function fmtTraceFunnel($arrInput) {
        if (!isset($arrInput['trace'])) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrTraceInfo = $arrInput['trace'];

        $arrRet = array();
        foreach (Lib_TimeStreamLog::$arrFmtSort as $strKey) {
            foreach ($arrTraceInfo as $k => $v) {
                // 2021022_origin_999 被切割成数组
                $arrKeySplit = explode('_', $k);
                if (sizeof($arrKeySplit) == 4) {
                    $arrKeySplit = array(
                        $arrKeySplit[0],
                        $arrKeySplit[1] . '_' . $arrKeySplit[2],
                        $arrKeySplit[3],
                    );
                }

                if (sizeof($arrKeySplit) != 3) {
                    continue;
                }

                // trans == trans这种
                if ($strKey == $arrKeySplit[1]) {
                    foreach ($v as $cfname => $cfvalue) {
                        $arrCfNameSplit = explode(':', $cfname);
                        if (sizeof($arrCfNameSplit) != 2) {
                            continue;
                        }
                            
                        if ($arrKeySplit[2] == '0') {
                            $arrRet[$arrKeySplit[1]]['start'][$arrCfNameSplit[1]] = $cfvalue;
                        } else if ($arrKeySplit[2] == '999') {
                            $arrRet[$arrKeySplit[1]]['end'][$arrCfNameSplit[1]] = $cfvalue;
                        }
                    }
                }
            }
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }
}
