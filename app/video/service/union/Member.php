<?php
/**
 * <AUTHOR>
 * @date 2018-06-21
 * @desc 成员管理
 *
 * pm yangyang47 wongchunyik
 * mrd http://agroup.baidu.com/newapp/file/view/128490
 * 设计文档 http://agroup.baidu.com/ntrd/view/office/986662
 * 
 */
class Service_Union_Member extends Service_Union_Base{

    /**
     * [添加成员]
     * @param  [array] $arrInput  [input]
     * @return [array]  $arrOutput [output]
     */
    public static function addUnionMember($arrInput){
        $o = Util_Validator::instance(array(
            'union_id' => 'int|optional:-1|gt:0',
            'sheet_id' => 'string|n',
            'nani_id' => 'int|optional:-1|gt:0',
            'user_id' => 'int|optional:-1|gt:0',
            'category_id' => 'int|optional:-1|gt:0',
            'remark' => 'string|optional',
            'phone' => 'int|gt:0',
        ));
        if (!$o->validate($arrInput, $arrInput)) {
            Bingo_Log::warning(sprintf('params error, input=[%s], error=[%s]', serialize($arrInput), $o->getLastError()));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 事务
        $intErrorNo = Tieba_Errcode::ERR_SUCCESS;
        do {
            // start trans
            if (!Dl_Union_Db::startTrans()) {
                Bingo_Log::warning(sprintf('fail to start trans! user_id=[%s]', $arrInput['user_id']));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            // 查询锁定会员
            $arrConds = array(
                'user_id = ' => $arrInput['user_id'],
            );
            $arrDlInput = array(
                'conds' => $arrConds,
                'appends' => 'FOR UPDATE',
            );
            $arrDlOut = Dl_Union_Member::getData($arrDlInput);
            if ($arrDlOut === false) {
                Bingo_Log::warning(sprintf('call Dl_Union_Member::getData error! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            // 非空，检查状态 
            if (!empty($arrDlOut)) {
                $arrStatus = array(
                    Lib_Define_Union::UNION_MEMBER_STATUS_INIT,
                    Lib_Define_Union::UNION_MEMBER_STATUS_COMMIT,
                    Lib_Define_Union::UNION_MEMBER_STATUS_WAITING,
                    Lib_Define_Union::UNION_MEMBER_STATUS_AUDITING,
                    Lib_Define_Union::UNION_MEMBER_STATUS_AUDIT_PASS,
                    //Lib_Define_Union::UNION_MEMBER_STATUS_UNION_REJECT,
                    Lib_Define_Union::UNION_MEMBER_STATUS_ENTER,
                );
                foreach($arrDlOut as $val){
                    //是否已入驻
                    if($val['status'] == Lib_Define_Union::UNION_MEMBER_STATUS_ENTER){
                        Bingo_Log::warning(sprintf('union member is bind, union_id=[%s], user_id=[%s]', $val['union_id'], $val['user_id']));
                        $intErrorNo = Lib_Define_Union::ERR_UNION_MEMBER_HAS_BIND;
                        break;
                    }
                    //是否已发送邀请
                    if($val['union_id'] == $arrInput['union_id'] && in_array($val['status'], $arrStatus)){
                        Bingo_Log::warning(sprintf('union has invite current user. union_id=[%s], user_id=[%s]', $arrInput['union_id'], $val['user_id']));
                        $intErrorNo = Lib_Define_Union::ERR_UNION_MEMBER_IS_IN_INVITELIST;
                        break;
                    }
                }
                if($intErrorNo != Tieba_Errcode::ERR_SUCCESS){
                    break;
                }
            } 

            // 锁定公会工单信息
            $arrDlInput = array(
                'conds' => array(
                    'sheet_id =' => $arrInput['sheet_id'],
                ),
                'appends' => 'FOR UPDATE',
            );
            $arrDlOut = Dl_Union_InviteSheet::getData($arrDlInput);
            if ($arrDlOut === false) {
                Bingo_Log::warning(sprintf('call Dl_Union_InviteSheet::getData error! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
            if (empty($arrDlOut['0']) || $arrDlOut['0']['status'] != Lib_Define_Union::UNION_INVITE_SHEET_STATUS_INIT) {
                Bingo_Log::warning(sprintf('sheet status error! input=[%s], output=[%s]', serialize($arrDlInput), serialize($arrDlOut)));
                $intErrorNo = Lib_Define_Union::ERR_UNION_INVITE_SHEET_STATUS_ERR;
                break;
            }

            // insert
            $objAes = new Lib_Aes();
            $objAes->setKey(Lib_Define_Union::UNION_AES_KEY);
            $strAesPhone = $objAes->encrypt($arrInput['phone']);

            $arrDlInput = array(
                'union_id' => $arrInput['union_id'],
                'sheet_id' => $arrInput['sheet_id'],
                'nani_id' => $arrInput['nani_id'],
                'user_id' => $arrInput['user_id'],
                'category_id' => $arrInput['category_id'],
                'remark' => !empty($arrInput['remark']) ? $arrInput['remark'] : '',
                'phone_num' => $strAesPhone,
                'create_time' => time(),
                'status' => Lib_Define_Union::UNION_MEMBER_STATUS_INIT,
            );
            $intId = Dl_Union_Member::addData($arrDlInput);
            if ($intId === false) {
                Bingo_Log::warning(sprintf('call Dl_Union_Member::addData error! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            // commit
            if (!Dl_Union_Db::commitTrans()) {
                Bingo_Log::warning(sprintf('fail to commit trans! user_id=[%s]', $arrInput['user_id']));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
        } while(0);

        if ($intErrorNo !== Tieba_Errcode::ERR_SUCCESS) {
            if (!Dl_Union_Db::rollbackTrans()) {
                Bingo_Log::warning(sprintf('fail to rollback! user_id=[%s]', $arrInput['user_id']));
            }
            return self::_errRet($intErrorNo);
        }

        $arrOutput = array(
            'member_id' => '' . $intId,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    /**
     * [成员同意邀请，并提交demo及信息]
     * @param  [array] $arrInput  [input]
     * @return [array]  $arrOutput [output]
     */
    public static function agreeUnionInvite($arrInput){
        $o = Util_Validator::instance(array(
            'thread_ids' => 'array|n',
            'user_realname' => 'string|n',
            'user_idcard' => 'string|n',
            'user_photo_file' => 'string|n',
            'member_id' => 'int|gt:0',
            'user_id' => 'int|gt:0',
        ));
        if (!$o->validate($arrInput, $arrInput)) {
            Bingo_Log::warning(sprintf('params error, input=[%s], error=[%s]', serialize($arrInput), $o->getLastError()));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //事务
        $intErrorNo = Tieba_Errcode::ERR_SUCCESS;
        do {
            if (!Dl_Union_Db::startTrans()) {
                Bingo_Log::warning(sprintf('fail to start trans! member_id=[%s], user_id=[%s]', $arrInput['member_id'], $arrInput['user_id']));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            // 获取成员公会信息
            $arrConds = array(
                'member_id = ' => $arrInput['member_id'],
            );
            $arrDlInput = array(
                'conds' => $arrConds,
                'appends' => 'FOR UPDATE',
            );
            $arrDlOut = Dl_Union_Member::getData($arrDlInput);
            if ($arrDlOut === false) {
                Bingo_Log::warning(sprintf('call Dl_Union_Member::getData error! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
            if (empty($arrDlOut)) {
                Bingo_Log::warning(sprintf('member user info is empty! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_COMFORUM_USER_NOT_EXIST;
                break;
            }
            $arrMember = $arrDlOut[0];
            if ($arrInput['user_id'] != $arrMember['user_id']) {
                Bingo_Log::warning("uid not match, member_id: {$arrInput['member_id']}, expect: {$arrMember['user_id']}, got: {$arrInput['user_id']}");
                $intErrorNo = Tieba_Errcode::ERR_ACTION_FORBIDDEN;
                break;
            }
            if ($arrMember['status'] != Lib_Define_Union::UNION_MEMBER_STATUS_COMMIT
                && $arrMember['status'] != Lib_Define_Union::UNION_MEMBER_STATUS_WAITING) {
                Bingo_Log::warning("member status error, member_id: {$arrInput['member_id']}, status: {$arrMember['status']}");
                $intErrorNo = Tieba_Errcode::ERR_STATUS_ERROR;
                break;
            }
            $strSheetId = $arrMember['sheet_id'];
            $intUnionId = intval($arrMember['union_id']);
            $intNaniId = intval($arrMember['nani_id']);

            // 锁定公会工单信息 检查状态为初始态
            $arrConds = array(
                'sheet_id =' => $strSheetId,
            );
            $arrDlInput = array(
                'conds' => $arrConds,
                'appends' => 'FOR UPDATE',
            );
            $arrDlOut = Dl_Union_InviteSheet::getData($arrDlInput);
            if ($arrDlOut === false) {
                Bingo_Log::warning(sprintf('call Dl_Union_InviteSheet::getData error! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
            if (empty($arrDlOut)) {
                Bingo_Log::warning(sprintf('member user info is empty! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_NO_RECORD;
                break;
            }
            $arrSheet = $arrDlOut[0];
            if ($arrSheet['status'] != Lib_Define_Union::UNION_INVITE_SHEET_STATUS_INIT) {
                Bingo_Log::warning("sheet status error, member_id: {$arrInput['member_id']}, sheet_id: $strSheetId, status: {$arrSheet['status']}");
                $intErrorNo = Tieba_Errcode::ERR_STATUS_ERROR;
                break;
            }
            $intAgreeNum = intval($arrSheet['accept_num']);
            $intSheetDemoNum = intval($arrSheet['demo_num']);

            // 更新定成员信息 已邀请或待同意的状态可以提交
            $arrConds = array(
                'member_id =' => $arrInput['member_id'],
            );
            $objAes = new Lib_Aes();
            $objAes->setKey(Lib_Define_Union::UNION_AES_KEY);
            $strAesIDCard = $objAes->encrypt($arrInput['user_idcard']);
            $intDemoNum = count($arrInput['thread_ids']);
            $arrData = array(
                'demo_tids' => implode(',', $arrInput['thread_ids']),
                'demo_num' => $intDemoNum,
                'user_realname' => $arrInput['user_realname'],
                'user_idcard' => $strAesIDCard,
                'user_photo_file' => $arrInput['user_photo_file'],
                'status' => Lib_Define_Union::UNION_MEMBER_STATUS_AUDITING,
                'update_time' => time(),
            );
            $arrDlInput = array(
                'data' => $arrData,
                'conds' => $arrConds,
            );
            $arrDlOut = Dl_Union_Member::updateData($arrDlInput);
            if ($arrDlOut === false) {
                Bingo_Log::warning(sprintf('call Dl_Union_Member::updateData error! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            // 添加demo视频
            foreach($arrInput['thread_ids'] as $val){
                $arrDlInput = array(
                    'thread_id' => intval($val),
                    'member_id' => $arrInput['member_id'],
                    'sheet_id' => $strSheetId,
                    'union_id' => $intUnionId,
                    'nani_id' => $intNaniId,
                    'user_id' => $arrInput['user_id'],
                    'create_time' => time(),
                );
                $arrDlOut = Dl_Union_DemoVideo::addData($arrDlInput);
                if ($arrDlOut === false) {
                    Bingo_Log::warning(sprintf('call Dl_Union_DemoVideo::addData error! input=[%s]', serialize($arrDlInput)));
                    $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }
            }
            if($intErrorNo != Tieba_Errcode::ERR_SUCCESS){
                break;
            }

            // 更新公会工单状态数据
            $arrConds = array(
                'sheet_id =' => $strSheetId,
            );
            $arrData = array(
                'accept_num' => $intAgreeNum + 1,
                'demo_num' => $intSheetDemoNum + $intDemoNum,
                'update_time' => time(),
            );
            $arrDlInput = array(
                'data' => $arrData,
                'conds' => $arrConds,
            );
            $arrDlOut = Dl_Union_InviteSheet::updateData($arrDlInput);
            if ($arrDlOut === false) {
                Bingo_Log::warning(sprintf('call Dl_Union_InviteSheet::updateData error! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            if (!Dl_Union_Db::commitTrans()) {
                Bingo_Log::warning(sprintf('fail to commit trans! member_id=[%s], user_id=[%s]', $arrInput['member_id'], $arrInput['user_id']));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
        } while (0);

        if ($intErrorNo !== Tieba_Errcode::ERR_SUCCESS) {
            if (!Dl_Union_Db::rollbackTrans()) {
                Bingo_Log::warning(sprintf('fail to rollback! member_id=[%s], user_id=[%s]', $arrInput['member_id'], $arrInput['user_id']));
            }
            if ($intErrorNo != Tieba_Errcode::ERR_STATUS_ERROR) {
                return self::_errRet($intErrorNo);
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * [判断工会达人是否处于已提交或待同意状态]
     * @param  [array] $arrInput  [input]
     * @return [array]  $arrOutput [output]
     */
    public static function checkUnionMemberInvite($arrInput){
        //必填项校验
        if(empty($arrInput['user_id'])){
            Bingo_Log::warning(sprintf('params error, input[%s]', serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //判断某达人是否在被邀请状态
        $strStatus = implode(',', array(Lib_Define_Union::UNION_MEMBER_STATUS_COMMIT , Lib_Define_Union::UNION_MEMBER_STATUS_WAITING));
        $arrConds = array(
            'user_id = ' => $arrInput['user_id'],
            "status in ($strStatus)",
        );
        $intRet = (int)self::checkExist($arrConds);
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS , $intRet);
    }

    /**
     * [检查是否存在]
     * @param  [array] $arrInput  [input]
     * @return [array]  $arrOutput [output]
     */
    private static function checkExist($arrConds){
        if(empty($arrConds)){
            return false;
        }
        $arrDlInput = array(
            'conds' => $arrConds,
        );
        $intExistNum = Dl_Union_Member::getCount($arrDlInput);
        if($intExistNum > 0){
            return true;
        }
        return false;
    }

    /**
     * [成员拒绝邀请]
     * @param  [array] $arrInput  [input]
     * @return [array]  $arrOutput [output]
     */
    public static function refuseUnionInvite($arrInput){
        if(empty($arrInput['member_id']) || $arrInput['member_id'] <= 0){
            Bingo_Log::warning(sprintf('params error, input=[%s]', serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //更新条件
        $arrStatus = array(
            Lib_Define_Union::UNION_MEMBER_STATUS_COMMIT, 
            Lib_Define_Union::UNION_MEMBER_STATUS_WAITING,
        );
        $arrParams = array(
            'member_id' => intval($arrInput['member_id']),  
            'conds_status' => $arrStatus,
            'new_status' => Lib_Define_Union::UNION_MEMBER_STATUS_REJECT,
            'num_key' => 'refuse_num',
            'num_type' => 'incr',
        );
        return self::updateMemberStatusTrans($arrParams);
    }

    /**
     * [公会邀请时删除成员]
     * @param  [array] $arrInput  [input]
     * @return [array]  $arrOutput [output]
     */
    public static function unionCancelInvite($arrInput){
        if(empty($arrInput['member_id']) || $arrInput['member_id'] <= 0){
            Bingo_Log::warning(sprintf('params error, input=[%s]', serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //更新条件
        $arrStatus = array(
            Lib_Define_Union::UNION_MEMBER_STATUS_INIT, 
            Lib_Define_Union::UNION_MEMBER_STATUS_COMMIT, 
            Lib_Define_Union::UNION_MEMBER_STATUS_WAITING, 
            Lib_Define_Union::UNION_MEMBER_STATUS_AUDITING, 
            Lib_Define_Union::UNION_MEMBER_STATUS_REJECT,
        );
        $arrParams = array(
            'member_id' => intval($arrInput['member_id']),
            'conds_status' => $arrStatus,
            'new_status' => Lib_Define_Union::UNION_MEMBER_STATUS_EXPIRE,
            'num_key' => 'member_num',
            'num_type' => 'decr',
        );
        return self::updateMemberStatusTrans($arrParams);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function updateMember($arrInput)
    {
        if(!isset($arrInput['member_id']) || empty($arrInput['member_id'])){
            Bingo_Log::warning(sprintf('params error, input[%s]', serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrConds = array(
            'member_id = ' => intval($arrInput['member_id']),
        );
        $arrData = array();
        if(isset($arrInput['_status'])){
            Bingo_Log::warning("update member's status, member_id: {$arrInput['member_id']}, status: {$arrInput['_status']}");
            $arrData['status'] = $arrInput['_status'];
        }
        if(isset($arrInput['category_id']) && !empty($arrInput['category_id'])){
            $arrData['category_id'] = $arrInput['category_id'];
        }
        if(isset($arrInput['remark']) && !empty($arrInput['remark'])){
            $arrData['remark'] = $arrInput['remark'];
        }
        $arrData['update_time'] = time();
        $arrDlInput = array(
            'data' => $arrData,
            'conds' => $arrConds,
        );
        $arrDlOut = Dl_Union_Member::updateData($arrDlInput);
        if ($arrDlOut === false) {
            Bingo_Log::warning(sprintf('call Dl_Union_Member::updateData error! input=[%s]', serialize($arrDlInput)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * [获取成员列表 sheet_id维度]
     * @param  [array] $arrInput  [input]
     * @return [array]  $arrOutput [output]
     */
    public static function getSheetMemberList($arrInput){
        if(empty($arrInput['sheet_id']) || empty($arrInput['pn']) || empty($arrInput['rn'])){
            Bingo_Log::warning(sprintf('params error, input[%s]', serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $strSheetId = $arrInput['sheet_id'];
        $intPn = intval($arrInput['pn']);
        $intRn = intval($arrInput['rn']);

        $arrConds = array(
            'sheet_id = ' => $strSheetId,
        );
        if(isset($arrInput['status']) && !empty($arrInput['status']) && is_array($arrInput['status'])){
            $strStatus = implode(',', $arrInput['status']);
            $arrConds[] = "status in ($strStatus)";
        }
        if(isset($arrInput['union_id']) && !empty($arrInput['union_id'])){
            $arrConds['union_id ='] = intval($arrInput['union_id']);
        }

        $arrAppends = array();
        if(isset($arrInput['orderby']) && !empty($arrInput['orderby'])){
            $arrAppends[] = $arrInput['orderby'];
        }
        $strLimit = sprintf('limit %d, %d', ($intPn - 1) * $intRn, $intRn);
        $arrAppends[] = $strLimit;
        $arrData = self::getList($arrConds, $arrAppends);
        if ($arrData === false) {
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
    }

    /**
     * [获取成员列表 union_id维度]
     * @param  [array] $arrInput  [input]
     * @return [array]  $arrOutput [output]
     */
    public static function getUnionMemberList($arrInput){
        if(empty($arrInput['union_id']) || empty($arrInput['pn']) || empty($arrInput['rn'])){
            Bingo_Log::warning(sprintf('params error, input[%s]', serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUnionId = intval($arrInput['union_id']);
        $intPn = intval($arrInput['pn']);
        $intRn = intval($arrInput['rn']);

        $arrConds = array(
            'union_id =' => $intUnionId,
        );
        if(isset($arrInput['status']) && !empty($arrInput['status'])){
            if (is_array($arrInput['status'])) {
                $strStatus = implode(',', $arrInput['status']);
                $arrConds[] = "status in ($strStatus)";
            } else {
                $arrConds['status ='] = (int)$arrInput['status'];
            }
        }

        $strLimit = sprintf('limit %d, %d', ($intPn - 1) * $intRn, $intRn);
        $arrAppends = array(
            $strLimit,
        );
        $arrData = self::getList($arrConds, $arrAppends);
        if ($arrData === false) {
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
    }

    /**
     * [获取成员信息 按user_id]
     * @param  [array] $arrInput  [input]
     * @return [array]  $arrOutput [output]
     */
    public static function getUnionMemberByUserId($arrInput){
        if(empty($arrInput['user_id'])){
            Bingo_Log::warning(sprintf('params error, input[%s]', serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUserId = intval($arrInput['user_id']);

        $arrConds = array(
            'user_id = ' => $intUserId,
        );
        if(isset($arrInput['status']) && !empty($arrInput['status']) && is_array($arrInput['status'])){
            $strStatus = implode(',', $arrInput['status']);
            $arrConds[] = "status in ($strStatus)";
        }
        if(isset($arrInput['union_id']) && !empty($arrInput['union_id'])){
            $arrConds['union_id = '] = intval($arrInput['union_id']);
        }
        if(isset($arrInput['member_id']) && !empty($arrInput['member_id'])){
            $arrConds['member_id = '] = intval($arrInput['member_id']);
        }
        if(isset($arrInput['sheet_id']) && !empty($arrInput['sheet_id'])){
            $arrConds['sheet_id = '] = $arrInput['sheet_id'];
        }

        $arrAppends = array();
        if(isset($arrInput['appends']) && !empty($arrInput['appends']) && is_array($arrInput['appends'])){
            $arrAppends = $arrInput['appends'];
        }
        $arrData = self::getList($arrConds, $arrAppends);
        if ($arrData === false) {
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrOutput = $arrData['list'];
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    /**
     * [获取列表]
     * @param  [array] $arrInput  [input]
     * @return [array]  $arrOutput [output]
     */
    private static function getList($arrConds = array(), $arrAppends = array()){
        //total
        $arrDlInput = array(
            'conds' => $arrConds,
        );
        $arrDlOut = Dl_Union_Member::getCount($arrDlInput);
        if ($arrDlOut === false) {
            Bingo_Log::warning(sprintf('call Dl_Union_Member::getCount error! input=[%s]', serialize($arrDlInput)));
            return false;
        }
        if($arrDlOut <= 0){
            $arrOutput = array(
                'list' => array(),
                'total' => '0',
            );
            return $arrOutput;
        }
        $intTotal = $arrDlOut;

        //list
        $arrDlInput = array(
            'conds' => $arrConds,
            'appends' => $arrAppends,
        );
        $arrDlOut = Dl_Union_Member::getData($arrDlInput);
        if ($arrDlOut === false) {
            Bingo_Log::warning(sprintf('call Dl_Union_Member::getData error! input=[%s]', serialize($arrDlInput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        if(empty($arrDlOut)){
            $arrOutput = array(
                'list' => array(),
                'total' => '0',
            );
            return $arrOutput;
        }

        //format pre_grade
        foreach($arrDlOut as &$val){
            $val['pre_grade'] = Lib_Union::getGradeString($val['pre_grade']);
        }
        $arrOutput = array(
            'list' => $arrDlOut,
            'total' => $intTotal,
        );
        return $arrOutput;
    }

    /**
     * [退出公会 或 公会移除成员（已入驻）]
     * @param  [array] $arrInput  [input]
     * @return [array]  $arrOutput [output]
     */
    public static function removeUnionMember($arrInput){
        if(empty($arrInput['member_id']) || $arrInput['member_id'] <= 0){
            Bingo_Log::warning(sprintf('params error, input=[%s]', serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //更新条件
        $arrStatus = array(
            Lib_Define_Union::UNION_MEMBER_STATUS_ENTER, 
        );
        $arrParams = array(
            'member_id' => intval($arrInput['member_id']),  
            'conds_status' => $arrStatus,
            'new_status' => Lib_Define_Union::UNION_MEMBER_STATUS_QUIT,
        );
        return self::updateMemberStatusTrans($arrParams);
    }



   /**
     * [获取某个工单下待审核成员详细信息--Mis]
     * @param  [array] $arrInput  [input]
     * @return [array]  $arrOutput [output]
     */
    public static function getAuditUnionMemberInfo4Mis($arrInput){
        
        if(empty($arrInput['sheet_id']) || empty($arrInput['pn']) || empty($arrInput['rn'])){
            Bingo_Log::warning(sprintf('params error, input[%s]', serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrConds['sheet_id ='] = $arrInput['sheet_id']; 
        //更新条件
        $arrStatus = array(
            Lib_Define_Union::UNION_MEMBER_STATUS_AUDITING, 
            Lib_Define_Union::UNION_MEMBER_STATUS_AUDIT_REJECT, 
            Lib_Define_Union::UNION_MEMBER_STATUS_AUDIT_PASS, 
        );
        $strStatus = implode(',', $arrStatus);

        $arrConds[] = "status in ( $strStatus )";
        $intPn = intval($arrInput['pn']);
        $intRn = intval($arrInput['rn']);
        $strLimit = sprintf(' limit %d, %d ', ($intPn - 1) * $intRn, $intRn);
        
        if(isset($arrInput['nani_id']) && !empty($arrInput['nani_id'])){
            $arrConds['nani_id ='] = $arrInput['nani_id']; 
        }
        $arrDlInput = array(
            'fields'  => array('member_id','sheet_id','nani_id','user_id','category_id','demo_num_s3','demo_num_s4','demo_good_num','pre_grade','status'),
            'conds'   => $arrConds,
            'appends' => $strLimit,
        );
        $arrDlMemOut = Dl_Union_Member::getData($arrDlInput);
        if ($arrDlMemOut === false) {
            Bingo_Log::warning(sprintf('call Dl_Union_Member::getData error! input=[%s]', serialize($arrDlInput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        
        if(empty($arrDlMemOut)){
            $arrOutput = array(
                'rows' => array(),
                'count' => 0,
            );
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
        }

        $intDlCntOut = Dl_Union_Member::getCount($arrDlInput);
        if ($intDlCntOut === false) {
            Bingo_Log::warning(sprintf('call Dl_Union_Member::getCount error! input=[%s]', serialize($arrDlInput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrMemberId = array();
        $arrUserId   = array();
        $arrRes   = array();
        foreach ($arrDlMemOut as $value) {
            $arrMemberId[] = $value['member_id'];
            $arrRes[$value['member_id']] = $value;
            $arrUserId[] = $value['user_id']; 
        }
        $arrUid2NickName = self::_mgetNickNameByUserId($arrUserId);

        $strMemberId = implode(',', $arrMemberId);
        $arrConds = array();
        $arrConds[] = "member_id in  ( $strMemberId )";
        $arrDlInput = array(
            'fields'  => array('thread_id','member_id','video_score','update_time'),
            'conds'   => $arrConds,
        );
        $arrDlDemoOut = Dl_Union_DemoVideo::getData($arrDlInput);
        if ($arrDlDemoOut === false) {
            Bingo_Log::warning(sprintf('call Dl_Union_DemoVideo::getData error! input=[%s]', serialize($arrDlInput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        foreach ($arrDlDemoOut as $value) {
            $arrDlDemoOut[$value['member_id']][] = $value;
        }
        
        foreach ($arrRes as $member_id => &$value) {
            $maxTime = 0;
            for($i=1;$i<=3;$i++){
                $value["thread_$i"] = 'https://nani.baidu.com/n/nani/share/item/'.$arrDlDemoOut[$member_id][$i-1]['thread_id'];
                $value["score_$i"] = $arrDlDemoOut[$member_id][$i-1]['video_score'];
                if($arrDlDemoOut[$member_id][$i-1]['update_time'] >$maxTime){
                    $maxTime = $arrDlDemoOut[$member_id][$i-1]['update_time'];
                }
            }
            $value['www'] = 'https://nani.baidu.com/n/nani/person?user_id=' . $value['user_id'];
            $value['nickname'] = $arrUid2NickName[$value['user_id']];
            $value['audit_time'] = $maxTime;
            $value['category_id'] = Lib_Define_Union::$arrCategory[$value['category_id']];
            $value['pre_grade'] = Lib_Union::getGradeString($value['pre_grade']);
        }
        unset($value);  
        /***按3分,4分,精选数排序开始****/
        if(!empty($arrInput['orderby'])){
            $arrSort = array();
            foreach ($arrRes as $member_id => $value) {
                $arrSort[$member_id] = $value[$arrInput['orderby']['field']];
            }

            if($arrInput['orderby']['sort'] == 'desc'){
                arsort($arrSort);
            }else if($arrInput['orderby']['sort']  == 'asc'){
                asort($arrSort);
            }
            $arrResSort = array();
            foreach ($arrSort as $member_id => $value) {
                $arrResSort[] = $arrRes[$member_id];
            }
            $arrRes = $arrResSort;
        }else{
            $arrRes = array_values($arrRes);
        }
        /***按3分,4分,精选数排序结束****/

        $arrOutput = array(
            'rows' => $arrRes,
            'count' => $intDlCntOut,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    /**
     * @param array
     * @return bool|array
     */
    private static function _mgetNickNameByUserId($arrUids){

         $arrServiceInput = array(
            'user_id' => $arrUids,
            'need_follow_info' => 0,
            'need_pass_info' => 0,
            'get_icon' => 0
        );      
        $strServiceName = "user"; 
        $strServiceMethod = "mgetUserDataEx";
        $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
        
        if(!$arrOutput || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call service user:mgetUserDataEx failed, input:".  serialize($arrServiceInput)."output: ".  serialize($arrOutput)); 
            return false;
        }
        $arrUid2NickName = array();
        foreach ($arrOutput['user_info'] as $intUid => $arrUserInfo) {
            $strUserNickName = !empty($arrUserInfo['nani_nickname']) ? $arrUserInfo['nani_nickname'] : (!empty($arrUserInfo['user_nickname']) ? $arrUserInfo['user_nickname'] : $arrUserInfo['user_name']);
            $arrUid2NickName[$intUid] = $strUserNickName;
        }
        return $arrUid2NickName;
    }

    /**
     * [下载某个工单下待审核成员详细信息excel版数据--Mis]
     * @param  [array] $arrInput  [input]
     * @return [array]  $arrOutput [output]
     */
    public static function downloadAuditUnionMemberInfo4Mis($arrInput){
        
        if(empty($arrInput['sheet_id'])){
            Bingo_Log::warning(sprintf('params error, input[%s]', serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrConds['sheet_id ='] = $arrInput['sheet_id']; 
        //更新条件
        $arrStatus = array(
            Lib_Define_Union::UNION_MEMBER_STATUS_AUDITING, 
            Lib_Define_Union::UNION_MEMBER_STATUS_AUDIT_REJECT, 
            Lib_Define_Union::UNION_MEMBER_STATUS_AUDIT_PASS, 
        );
        $strStatus = implode(',', $arrStatus);

        $arrConds[] = "status in ( $strStatus )";
        
        if(isset($arrInput['nani_id']) && !empty($arrInput['nani_id'])){
            $arrConds['nani_id ='] = $arrInput['nani_id']; 
        }
        $arrDlInput = array(
            'fields'  => array('member_id','sheet_id','nani_id','user_id','category_id','demo_num_s3','demo_num_s4','demo_good_num','pre_grade','status'),
            'conds'   => $arrConds,
        );
        $arrDlMemOut = Dl_Union_Member::getData($arrDlInput);
        if ($arrDlMemOut === false) {
            Bingo_Log::warning(sprintf('call Dl_Union_Member::getData error! input=[%s]', serialize($arrDlInput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        
        if(empty($arrDlMemOut)){
            $arrOutput = array(
                'list' => array(),
            );
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
        }

        $intDlCntOut = Dl_Union_Member::getCount($arrDlInput);
        if ($intDlCntOut === false) {
            Bingo_Log::warning(sprintf('call Dl_Union_Member::getCount error! input=[%s]', serialize($arrDlInput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrMemberId = array();
        $arrUserId   = array();
        $arrRes   = array();
        foreach ($arrDlMemOut as $value) {
            $arrMemberId[] = $value['member_id'];
            $arrRes[$value['member_id']] = $value;
            $arrUserId[] = $value['user_id']; 
        }
        $arrUid2NickName = self::_mgetNickNameByUserId($arrUserId);

        $strMemberId = implode(',', $arrMemberId);
        $arrConds = array();
        $arrConds[] = "member_id in  ( $strMemberId )";
        $arrDlInput = array(
            'fields'  => array('thread_id','member_id','video_score','update_time'),
            'conds'   => $arrConds,
        );
        $arrDlDemoOut = Dl_Union_DemoVideo::getData($arrDlInput);
        if ($arrDlDemoOut === false) {
            Bingo_Log::warning(sprintf('call Dl_Union_DemoVideo::getData error! input=[%s]', serialize($arrDlInput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        foreach ($arrDlDemoOut as $value) {
            $arrDlDemoOut[$value['member_id']][] = $value;
        }
        
        foreach ($arrRes as $member_id => &$value) {
            $maxTime = 0;
            for($i=1;$i<=3;$i++){
                $value["thread_$i"] = 'https://tieba.baidu.com/p/'.$arrDlDemoOut[$member_id][$i-1]['thread_id'];
                $value["score_$i"] = $arrDlDemoOut[$member_id][$i-1]['video_score'];
                if($arrDlDemoOut[$member_id][$i-1]['update_time'] >$maxTime){
                    $maxTime = $arrDlDemoOut[$member_id][$i-1]['update_time'];
                }
            }
            $value['www'] = 'https://nani.baidu.com/n/nani/person?user_id=' . $value['user_id'];
            $value['nickname'] = $arrUid2NickName[$value['user_id']];
            $value['audit_time'] = $maxTime;
            $value['category_id'] = Lib_Define_Union::$arrCategory[$value['category_id']];
        }
        unset($value);  
        
        if(!empty($arrInput['orderby'])){
            $arrSort = array();
            foreach ($arrRes as $member_id => $value) {
                $arrSort[$member_id] = $value[$arrInput['orderby']['field']];
            }

            if($arrInput['orderby']['sort'] == 'desc'){
                arsort($arrSort);
            }else if($arrInput['orderby']['sort']  == 'asc'){
                asort($arrSort);
            }
            $arrResSort = array();
            foreach ($arrSort as $member_id => $value) {
                $arrResSort[] = $arrRes[$member_id];
            }
            $arrRes = $arrResSort;
        }else{
            $arrRes = array_values($arrRes);
        }

        $arrOutput = array(
            'list' => $arrRes,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }


    /**
     * 删除工会成员--Mis
     * @param $arrInput
     * @return array
     */
    public static function deleteUnionMember4Mis($arrInput)
    {
        if(empty($arrInput['member_id']) || empty($arrInput['op_name']) || empty($arrInput['op_uid'])) {
            Bingo_Log::warning(sprintf('params error, input[%s]', serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intTime = time();

        //事务
        $intErrorNo = Tieba_Errcode::ERR_SUCCESS;
        do {
            if (!Dl_Union_Db::startTrans()) {
                Bingo_Log::warning(sprintf('fail to start trans! member_id=[%s]', $arrInput['member_id']));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            // get member
            $arrDlInput = [
                'fields'  => array('union_id', 'member_id', 'status'),
                'conds' => [
                    'member_id =' => $arrInput['member_id'],
                ],
                'appends' => 'FOR UPDATE',
            ];
            $arrDlOut = Dl_Union_Member::getData($arrDlInput);
            if ($arrDlOut === false) {
                Bingo_Log::warning(sprintf('call Dl_Union_Member::getData error! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
            if(empty($arrDlOut)){
                Bingo_Log::warning("no this member, member_id: {$arrInput['member_id']}");
                $intErrorNo = Tieba_Errcode::ERR_NO_RECORD;
                break;
            }
            $arrMember = $arrDlOut[0];

            // get union
            $arrDlInput = [
                'fields'  => array('union_id','member_num'),
                'conds' => [
                    'union_id =' => $arrMember['union_id'],
                ],
                'appends' => 'FOR UPDATE',
            ];
            $arrDlOut = Dl_Union_Union::getData($arrDlInput);
            if ($arrDlOut === false) {
                Bingo_Log::warning(sprintf('call Dl_Union_Union::getData error! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
            if(empty($arrDlOut)){
                Bingo_Log::warning("no this union, union_id: {$arrMember['union_id']}");
                $intErrorNo = Tieba_Errcode::ERR_NO_RECORD;
                break;
            }
            $arrUnion = $arrDlOut[0];

            // update union
            if ($arrMember['status'] == Lib_Define_Union::UNION_MEMBER_STATUS_ENTER) {
                $arrData = array(
                    'member_num'  => max(0, $arrUnion['member_num'] - 1),
                    'op_name' => $arrInput['op_name'],
                    'op_uid'  => $arrInput['op_uid'],
                    'op_time' => $intTime,
                    'update_time' => $intTime,
                );
                $arrDlInput = array(
                    'data' => $arrData,
                    'conds' => array(
                        'union_id =' => $arrUnion['union_id'],
                    ),
                );
                $arrDlOut = Dl_Union_Union::updateData($arrDlInput);
                if ($arrDlOut === false) {
                    Bingo_Log::warning(sprintf('call Dl_Union_Union::updateData error! input=[%s]', serialize($arrDlInput)));
                    $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }
            }

            // update member status
            if ($arrMember['status'] != Lib_Define_Union::UNION_MEMBER_STATUS_EXPIRE) {
                $arrData    = array(
                    'status'  => Lib_Define_Union::UNION_MEMBER_STATUS_EXPIRE,//已失效
                    'update_time' => $intTime,
                    'op_time' => $intTime,
                    'op_name' => $arrInput['op_name'],
                    'op_uid'  => $arrInput['op_uid'],
                );
                $arrDlInput = [
                    'data' => $arrData,
                    'conds' => [
                        'member_id =' => $arrInput['member_id'],
                    ],
                ];
                $arrDlOut = Dl_Union_Member::updateData($arrDlInput);
                if ($arrDlOut === false) {
                    Bingo_Log::warning(sprintf('call Dl_Union_Member::updateData error! input=[%s]', serialize($arrDlInput)));
                    $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }
            }

            // delete member account
            $arrDlInput = array(
                'union_id' => $arrUnion['union_id'],
                'member_id' => $arrMember['member_id'],
                'cmonth' => date('Ym', $intTime),
            );
            $arrDlOut = Dl_Union_MemberAccount::deleteDataTrans($arrDlInput);
            if ($arrDlOut === false) {
                Bingo_Log::warning(sprintf('call Dl_Union_MemberAccount::deleteDataTrans error! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            if (!Dl_Union_Db::commitTrans()) {
                Bingo_Log::warning(sprintf('fail to commit trans! union_id=[%s]', $arrInput['union_id']));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
        } while (0);

        if ($intErrorNo !== Tieba_Errcode::ERR_SUCCESS) {
            if (!Dl_Union_Db::rollbackTrans()) {
                Bingo_Log::warning(sprintf('fail to rollback!  member_id=[%s]', $arrInput['member_id']));
            }
            if ($intErrorNo != Tieba_Errcode::ERR_NO_RECORD) {
                return self::_errRet($intErrorNo);
            }
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * [更新member状态及union表中的成员数，事务操作， 删除或移除成员]
     * $arrInput = array(
     *      'member_id',  
     *      'sheet_id',
     *      'conds_status' => array(), 更新的条件
     *      'new_status', 更新后的状态
     *      'num_key', invite_sheet 表中的成员数字段key
     *      'num_type', (incr | decr)
     *  );
     * @param  [array] $arrStatus  [更新的条件]
     * @return [array] $arrOutput [output]
     */
    protected static function updateMemberStatusTrans($arrInput){
        $o = Util_Validator::instance(array(
            'member_id' => 'int|gt:0',
            'user_id' => 'int|optional:0',
            'conds_status' => 'array|int|min:0',
            'new_status' => 'int|gt:0',
            'num_key' => 'string|optional:',
            'num_type' => 'string|optional:',
        ));
        if (!$o->validate($arrInput, $arrInput)) {
            Bingo_Log::warning(sprintf('params error, input=[%s], error=[%s]', serialize($arrInput), $o->getLastError()));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $intMemberId = $arrInput['member_id'];
        $intUserId = $arrInput['user_id'];
        $arrStatus = $arrInput['conds_status'];
        $intNewStatus = $arrInput['new_status'];
        $strNumKey = $arrInput['num_key'];
        $strNumType = $arrInput['num_type'];
        $intCurrentTime = time();
        $intCurrentMonth = (int)date('Ym', $intCurrentTime);

        //事务
        $intErrorNo = Tieba_Errcode::ERR_SUCCESS;
        do {
            if (!Dl_Union_Db::startTrans()) {
                Bingo_Log::warning(sprintf('fail to start trans! member_id=[%s]', $intMemberId));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            // 锁定会员状态
            $arrConds = array(
                'member_id = ' => $intMemberId,
            );
            if ($intUserId > 0) {
                $arrConds['user_id ='] = $intUserId;
            }
            $arrDlInput = array(
                'conds' => $arrConds,
                'appends' => 'FOR UPDATE',
            );
            $arrDlOut = Dl_Union_Member::getData($arrDlInput);
            if ($arrDlOut === false) {
                Bingo_Log::warning(sprintf('call Dl_Union_Member::getData error! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
            if (empty($arrDlOut)){
                Bingo_Log::warning(sprintf('member user info is empty! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_COMFORUM_USER_NOT_EXIST;
                break;
            }
            $arrMember = $arrDlOut['0'];
            $strSheetId = $arrMember['sheet_id'];
            $intUnionId = (int)$arrMember['union_id'];
            $intPreStatus = $arrMember['status'];
            if (!in_array($intPreStatus, $arrStatus)) {
                Bingo_Log::warning(sprintf('member status error! member_id: [%u], status: [%u], input: [%s], ',
                    $intMemberId, $arrMember['status'], json_encode($arrInput)));
                $intErrorNo = Tieba_Errcode::ERR_STATUS_ERROR;
                break;
            }

            //锁定公会工单信息 初始态 更新数据
            if (!empty($strNumKey)) {
                $arrConds = array(
                    'sheet_id = ' => $strSheetId,
                );
                $arrDlInput = array(
                    'conds' => $arrConds,
                    'appends' => 'FOR UPDATE',
                );
                $arrData = Dl_Union_InviteSheet::getData($arrDlInput);
                if ($arrDlOut === false) {
                    Bingo_Log::warning(sprintf('call Dl_Union_InviteSheet::getData error! input=[%s]', serialize($arrDlInput)));
                    $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }
                $arrSheet = $arrData['0'];
                $intNum = !empty($arrData['0'][$strNumKey]) ? intval($arrData['0'][$strNumKey]) : 0;

                $arrData = array();
                if($strNumType == 'incr'){
                    $arrData[$strNumKey] = $intNum + 1;
                }
                if($strNumType == 'decr'){
                    $arrData[$strNumKey] = ($intNum > 0) ? ($intNum - 1) : 0;
                }
                $arrData['update_time'] = $intCurrentTime;

                // 公会删除成员时，待审核状态 需要同时更新demo_num
                if($intPreStatus == Lib_Define_Union::UNION_MEMBER_STATUS_AUDITING && $intNewStatus == Lib_Define_Union::UNION_MEMBER_VIDEO_EXPIRE){
                    $arrData['accept_num'] = max(0, $arrSheet['accept_num'] - 1);
                    $arrData['demo_num'] = max(0, $arrSheet['demo_num'] - 3);
                }

                $arrDlInput = array(
                    'data' => $arrData,
                    'conds' => $arrConds,
                );
                $arrDlOut = Dl_Union_InviteSheet::updateData($arrDlInput);
                if ($arrDlOut === false) {
                    Bingo_Log::warning(sprintf('call Dl_Union_InviteSheet::updateData error! input=[%s]', serialize($arrDlInput)));
                    $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }
            }

            // 更新成员状态
            $arrConds = array(
                'member_id = ' => $intMemberId,
            );
            $arrData = array(
                'status' => $intNewStatus,
                'update_time' => $intCurrentTime,
            );
            $arrDlInput = array(
                'data' => $arrData,
                'conds' => $arrConds,
            );
            $arrDlOut = Dl_Union_Member::updateData($arrDlInput);
            if ($arrDlOut === false) {
                Bingo_Log::warning(sprintf('call Dl_Union_Member::updateData error! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            if ($intPreStatus == Lib_Define_Union::UNION_MEMBER_STATUS_ENTER
                && $intNewStatus != Lib_Define_Union::UNION_MEMBER_STATUS_ENTER)
            {
                // 更新公会成员计数
                $arrConds = array(
                    'union_id =' => $intUnionId,
                    'member_num >' => 0,
                );
                $arrData = array(
                    'member_num = member_num - 1',
                    'update_time' => $intCurrentTime,
                );
                $arrDlInput = array(
                    'data' => $arrData,
                    'conds' => $arrConds,
                );
                $arrDlOut = Dl_Union_Union::updateData($arrDlInput);
                if ($arrDlOut === false) {
                    Bingo_Log::warning(sprintf('call Dl_Union_Union::updateData error! input=[%s]', serialize($arrDlInput)));
                    $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }

                // 作废账单
                $arrDlInput = array(
                    'union_id' => $intUnionId,
                    'member_id' => $intMemberId,
                    'cmonth' => $intCurrentMonth,
                );
                $arrDlOut = Dl_Union_MemberAccount::deleteDataTrans($arrDlInput);
                if ($arrDlOut === false) {
                    Bingo_Log::warning(sprintf('call Dl_Union_MemberAccount::deleteDataTrans error! input=[%s]', serialize($arrDlInput)));
                    $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }
            }

            if (!Dl_Union_Db::commitTrans()) {
                Bingo_Log::warning(sprintf('fail to commit trans! member_id=[%s]', $intMemberId));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
        } while (0);

        if ($intErrorNo !== Tieba_Errcode::ERR_SUCCESS) {
            if (!Dl_Union_Db::rollbackTrans()) {
                Bingo_Log::warning(sprintf('fail to rollback! member_id=[%s]', $intMemberId));
            }
            return self::_errRet($intErrorNo);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * [获取成员数 sheet_id维度]
     * @param  [array] $arrInput  [input]
     * @return [array]  $arrOutput [output]
     */
    public static function getSheetMemberNum($arrInput){
        if(empty($arrInput['sheet_id'])){
            Bingo_Log::warning(sprintf('params error, input[%s]', serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $strSheetId = $arrInput['sheet_id'];

        $arrConds = array(
            'sheet_id = ' => $strSheetId,
        );
        if(isset($arrInput['status']) && !empty($arrInput['status']) && is_array($arrInput['status'])){
            $strStatus = implode(',', $arrInput['status']);
            $arrConds[] = "status in ($strStatus)";
        }
        if(isset($arrInput['union_id']) && !empty($arrInput['union_id'])){
            $arrConds['union_id ='] = intval($arrInput['union_id']);
        }
        $arrDlInput = array(
            'conds' => $arrConds,
        );

        $arrDlOut = Dl_Union_Member::getCount($arrDlInput);
        if ($arrDlOut === false) {
            Bingo_Log::warning(sprintf('call Dl_Union_Member::getCount error! input=[%s]', serialize($arrDlInput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrData = array(
            'total' => $arrDlOut,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
    }

    /**
     * [更新成员表信息, 提交邀请]
     * @param  [array] $arrInput  [input]
     * @return [array]  $arrOutput [output]
     */
    public static function commitInviteSheet($arrInput){
        if(empty($arrInput['sheet_id'])){
            Bingo_Log::warning(sprintf('params error, input[%s]', serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $strSheetId = $arrInput['sheet_id'];

        //事务
        $intErrorNo = Tieba_Errcode::ERR_SUCCESS;
        do {
            if (!Dl_Union_Db::startTrans()) {
                Bingo_Log::warning(sprintf('fail to start trans! sheet_id=[%s]', $strSheetId));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            //锁定公会工单信息 状态为初始态
            $arrConds = array(
                'sheet_id =' => $strSheetId,
            );
            $arrDlInput = array(
                'conds' => $arrConds,
                'appends' => 'FOR UPDATE',
            );
            $arrDlOut = Dl_Union_InviteSheet::getData($arrDlInput);
            if ($arrDlOut === false) {
                Bingo_Log::warning(sprintf('call Dl_Union_InviteSheet::getData error! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
            if (empty($arrDlOut)) {
                Bingo_Log::warning("no this sheet, sheet_id: $strSheetId");
                $intErrorNo = Tieba_Errcode::ERR_NO_RECORD;
                break;
            }
            $arrSheet = $arrDlOut[0];
            if ($arrSheet['status'] != Lib_Define_Union::UNION_INVITE_SHEET_STATUS_INIT) {
                Bingo_Log::warning("sheet status error, sheet_id: $strSheetId, status: {$arrSheet['status']}");
                $intErrorNo = Tieba_Errcode::ERR_STATUS_ERROR;
                break;
            }

            //更新成员状态为已提交邀请
            $arrConds = array(
                'sheet_id =' => $strSheetId,
                'status =' => Lib_Define_Union::UNION_MEMBER_STATUS_INIT,
            );
            if(isset($arrInput['member_id']) && !empty($arrInput['member_id'])){
                $arrConds['member_id ='] = intval($arrInput['member_id']);
            }
            $arrData = array(
                'commit_time' => time(),
                'update_time' => time(),
                'status' => Lib_Define_Union::UNION_MEMBER_STATUS_COMMIT,
            );
            $arrDlInput = array(
                'data' => $arrData,
                'conds' => $arrConds,
            );
            $intTotal = Dl_Union_Member::updateData($arrDlInput);
            if ($intTotal === false) {
                Bingo_Log::warning(sprintf('call Dl_Union_Member::updateData error! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            // 更新公会工单member_num
            if ($intTotal > 0) {
                $arrConds = array(
                    'sheet_id =' => $strSheetId,
                );
                $arrData = array(
                    'member_num' => $arrSheet['member_num'] + $intTotal,
                    'update_time' => time(),
                );
                $arrDlInput = array(
                    'data' => $arrData,
                    'conds' => $arrConds,
                );
                $arrDlOut = Dl_Union_InviteSheet::updateData($arrDlInput);
                if ($arrDlOut === false) {
                    Bingo_Log::warning(sprintf('call Dl_Union_InviteSheet::updateData error! input=[%s]', serialize($arrDlInput)));
                    $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }
            }

            if (!Dl_Union_Db::commitTrans()) {
                Bingo_Log::warning(sprintf('fail to commit trans! sheet_id=[%s]', $strSheetId));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
        } while (0);

        if ($intErrorNo !== Tieba_Errcode::ERR_SUCCESS) {
            if (!Dl_Union_Db::rollbackTrans()) {
                Bingo_Log::warning(sprintf('fail to rollback! sheet_id=[%s]', $strSheetId));
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * [更新成员表信息, 提交工单]
     * @param  [array] $arrInput  [input]
     * @return [array]  $arrOutput [output]
     */
    public static function commitSheetForAudit($arrInput){
        if(empty($arrInput['sheet_id'])){
            Bingo_Log::warning(sprintf('params error, input[%s]', serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $strSheetId = $arrInput['sheet_id'];

        //事务
        $intErrorNo = Tieba_Errcode::ERR_SUCCESS;
        do {
            if (!Dl_Union_Db::startTrans()) {
                Bingo_Log::warning(sprintf('fail to start trans! sheet_id=[%s]', $strSheetId));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            //锁定公会工单信息 检查状态为初始态
            $arrConds = array(
                'sheet_id =' => $strSheetId,
            );
            $arrDlInput = array(
                'conds' => $arrConds,
                'appends' => 'FOR UPDATE',
            );
            $arrDlOut = Dl_Union_InviteSheet::getData($arrDlInput);
            if ($arrDlOut === false) {
                Bingo_Log::warning(sprintf('call Dl_Union_InviteSheet::getData error! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
            if (empty($arrDlOut)) {
                Bingo_Log::warning("no this sheet, sheet_id: $strSheetId");
                $intErrorNo = Tieba_Errcode::ERR_NO_RECORD;
                break;
            }
            $arrSheet = $arrDlOut[0];
            if ($arrSheet['status'] != Lib_Define_Union::UNION_INVITE_SHEET_STATUS_INIT) {
                Bingo_Log::warning("sheet status error, sheet_id: $strSheetId, status: {$arrSheet['status']}");
                $intErrorNo = Tieba_Errcode::ERR_STATUS_ERROR;
                break;
            }

            // 把未处理的成员状态改成 10(已失效)
            $arrStatus = array(
                Lib_Define_Union::UNION_MEMBER_STATUS_COMMIT,
                Lib_Define_Union::UNION_MEMBER_STATUS_WAITING,
            );
            $arrConds = array(
                'sheet_id =' => $strSheetId,
                'status in (' . implode(',', $arrStatus) . ')',
            );
            $arrData = array(
                'update_time' => time(),
                'status' => Lib_Define_Union::UNION_MEMBER_STATUS_EXPIRE,
            );
            $arrDlInput = array(
                'data' => $arrData,
                'conds' => $arrConds,
            );
            $intTotal = Dl_Union_Member::updateData($arrDlInput);
            if ($intTotal === false) {
                Bingo_Log::warning(sprintf('call Dl_Union_Member::updateData error! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            // 更新工单status和member_num
            $arrConds = array(
                'sheet_id =' => $strSheetId,
            );
            $arrData = array(
                'status' => Lib_Define_Union::UNION_INVITE_SHEET_STATUS_UNAUDIT,
                'member_num' => max(0, $arrSheet['member_num'] - $intTotal),
                'commit_time' => time(),
                'update_time' => time(),
            );
            $arrDlInput = array(
                'data' => $arrData,
                'conds' => $arrConds,
            );
            $arrDlOut = Dl_Union_InviteSheet::updateData($arrDlInput);
            if ($arrDlOut === false) {
                Bingo_Log::warning(sprintf('call Dl_Union_InviteSheet::updateData error! input=[%s]', serialize($arrDlInput)));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            if (!Dl_Union_Db::commitTrans()) {
                Bingo_Log::warning(sprintf('fail to commit trans! sheet_id=[%s]', $strSheetId));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
        } while (0);

        if ($intErrorNo !== Tieba_Errcode::ERR_SUCCESS) {
            if (!Dl_Union_Db::rollbackTrans()) {
                Bingo_Log::warning(sprintf('fail to rollback! sheet_id=[%s]', $strSheetId));
            }
            if ($intErrorNo != Tieba_Errcode::ERR_STATUS_ERROR) {
                return self::_errRet($intErrorNo);
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief 审核会员用这个，不要直接用update
     * @param $arrInput
     * @return array
     */
    public static function auditMember($arrInput)
    {
        $o = Util_Validator::instance([
            'member_id' => 'int|gt:0',
            'op_type' => 'int|in:1,2', //1-通过, 2-拒绝
            'op_uid' => 'int|gt:0',
            'op_name' => 'string|n',
        ]);
        if (!$o->validate($arrInput, $arrInput)) {
            Bingo_Log::warning('[auditMember] param error, input: ' . json_encode($arrInput) . ', error: ' . $o->getLastError());
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intMemberId = $arrInput['member_id'];
        $intOpType = $arrInput['op_type'];
        $intOpUid = $arrInput['op_uid'];
        $strOpName = $arrInput['op_name'];
        $intCurrentTime = time();

        $intErrorNo = Tieba_Errcode::ERR_SUCCESS;
        $arrNewUnion = [];
        $arrNewSheet = [];
        $arrNewMember = [];
        do {
            // start trans
            if (!Dl_Union_Db::startTrans()) {
                Bingo_Log::warning("[auditMember] fail to start trans, member_id: $intMemberId");
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            // 查询锁定会员
            $arrParams = [
                'conds' => [
                    'member_id =' => $intMemberId,
                ],
                'appends' => 'FOR UPDATE',
            ];
            $arrData = Dl_Union_Member::getData($arrParams);
            if ($arrData === false) {
                Bingo_Log::warning("[auditMember] fail to get member, member_id: $intMemberId");
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
            if (empty($arrData)) {
                Bingo_Log::warning("[auditMember] no this member, member_id: $intMemberId");
                $intErrorNo = Tieba_Errcode::ERR_NO_RECORD;
                break;
            }
            $arrPreMember = $arrData[0];
            if ($arrPreMember['status'] != Lib_Define_Union::UNION_MEMBER_STATUS_AUDITING) {
                Bingo_Log::warning("[auditMember] member status error, member_id: $intMemberId, status: {$arrPreMember['status']}");
                $intErrorNo = Tieba_Errcode::ERR_STATUS_ERROR;
                break;
            }
            $intUnionId = $arrPreMember['union_id'];
            $strSheetId = $arrPreMember['sheet_id'];

            // 查询锁定公会
            $arrParams = [
                'conds' => [
                    'union_id =' => $intUnionId,
                ],
                'appends' => 'FOR UPDATE',
            ];
            $arrData = Dl_Union_Union::getData($arrParams);
            if ($arrData === false) {
                Bingo_Log::warning("[auditMember] fail to get union, union_id: $intUnionId");
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
            if (empty($arrData)) {
                Bingo_Log::warning("[auditMember] no this union, union_id: $intUnionId");
                $intErrorNo = Tieba_Errcode::ERR_NO_RECORD;
                break;
            }
            $arrPreUnion = $arrData[0];
            if ($arrPreUnion['status'] != Lib_Define_Union::UNION_AUDIT_STATUS_PASS
                && $arrPreUnion['status'] != Lib_Define_Union::UNION_AUDIT_STATUS_ENTER) {
                Bingo_Log::warning("[auditMember] union status error, union_id: $intUnionId, status: {$arrPreUnion['status']}");
                $intErrorNo = Tieba_Errcode::ERR_STATUS_ERROR;
                break;
            }

            // 查询锁定工单
            $arrParams = [
                'conds' => [
                    'sheet_id =' => $strSheetId,
                ],
                'appends' => 'FOR UPDATE',
            ];
            $arrData = Dl_Union_InviteSheet::getData($arrParams);
            if ($arrData === false) {
                Bingo_Log::warning("[auditMember] fail to get sheet, sheet_id: $strSheetId");
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
            if (empty($arrData)) {
                Bingo_Log::warning("[auditMember] no this sheet, sheet_id: $strSheetId");
                $intErrorNo = Tieba_Errcode::ERR_NO_RECORD;
                break;
            }
            $arrPreSheet = $arrData[0];
            if ($arrPreSheet['status'] != Lib_Define_Union::UNION_INVITE_SHEET_STATUS_UNAUDIT
                && $arrPreSheet['status'] != Lib_Define_Union::UNION_INVITE_SHEET_STATUS_AUDITING) {
                Bingo_Log::warning("[auditMember] sheet status error, sheet_id: $strSheetId, status: {$arrPreSheet['status']}");
                $intErrorNo = Tieba_Errcode::ERR_STATUS_ERROR;
                break;
            }

            // 获取工单新状态
            $intNewSheetAuditNum = $arrPreSheet['audit_num'] + 1;
            $intNewSheetPassNum = $intOpType == 1 ? $arrPreSheet['pass_num'] + 1 : $arrPreSheet['pass_num'];
            $intNewSheetStatus = Lib_Define_Union::UNION_INVITE_SHEET_STATUS_AUDITING;
            $intSheetMemberNumForPass = $arrPreUnion['status'] == Lib_Define_Union::UNION_AUDIT_STATUS_PASS ?
                Lib_Define_Union::FIRST_INVITE_SHEET_MEMBER_NUM_PASS : Lib_Define_Union::SECOND_INVITE_SHEET_MEMBER_NUM_PASS;
            if ($arrPreSheet['accept_num'] - $intNewSheetAuditNum < $intSheetMemberNumForPass - $intNewSheetPassNum) {
                $intNewSheetStatus = Lib_Define_Union::UNION_INVITE_SHEET_STATUS_AUDIT_REJECT;
            } else if ($intNewSheetAuditNum >= $arrPreSheet['accept_num']) {
                $intNewSheetStatus = Lib_Define_Union::UNION_INVITE_SHEET_STATUS_AUDIT_PASS;
            }

            // 获取公会新状态
            if ($arrPreUnion['status'] == Lib_Define_Union::UNION_AUDIT_STATUS_PASS
                && $intNewSheetStatus == Lib_Define_Union::UNION_INVITE_SHEET_STATUS_AUDIT_PASS) {
                $intNewUnionStatus = Lib_Define_Union::UNION_AUDIT_STATUS_ENTER;
            } else {
                $intNewUnionStatus = $arrPreUnion['status'];
            }
            if ($intNewSheetStatus == Lib_Define_Union::UNION_INVITE_SHEET_STATUS_AUDIT_PASS) {
                $intNewUnionMemberNum = $arrPreUnion['member_num'] + $intNewSheetPassNum;
            } else {
                $intNewUnionMemberNum = $arrPreUnion['member_num'];
            }

            // 获取会员新状态
            if ($intOpType == 1) {
                if ($intNewUnionStatus == Lib_Define_Union::UNION_AUDIT_STATUS_ENTER
                    && $intNewSheetStatus == Lib_Define_Union::UNION_INVITE_SHEET_STATUS_AUDIT_PASS) {
                    $intNewMemberStatus = Lib_Define_Union::UNION_MEMBER_STATUS_ENTER;
                } else {
                    $intNewMemberStatus = Lib_Define_Union::UNION_MEMBER_STATUS_AUDIT_PASS;
                }
            } else {
                $intNewMemberStatus = Lib_Define_Union::UNION_MEMBER_STATUS_AUDIT_REJECT;
            }

            // 更新公会数据
            if ($intNewUnionStatus != $arrPreUnion['status']
                || $intNewUnionMemberNum != $arrPreUnion['member_num']) {
                $arrNewUnion = [
                    'member_num' => $intNewUnionMemberNum,
                    'update_time' => $intCurrentTime,
                    'status' => $intNewUnionStatus,
                    'op_uid' => $intOpUid,
                    'op_name' => $strOpName,
                    'op_time' => $intCurrentTime,
                ];
                if ($intNewUnionStatus == Lib_Define_Union::UNION_AUDIT_STATUS_ENTER
                    && $intNewUnionStatus != $arrPreUnion['status']) {
                    $arrNewUnion['enter_time'] = $intCurrentTime;
                }

                $arrParams = [
                    'conds' => [
                        'union_id =' => $intUnionId,
                    ],
                    'data' => $arrNewUnion,
                ];
                $arrData = Dl_Union_Union::updateData($arrParams);
                if ($arrData === false) {
                    Bingo_Log::warning("[auditMember] fail to update union, union_id: $intUnionId");
                    $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }
            }
            $arrNewUnion += $arrPreUnion;

            // 更新工单数据
            $arrNewSheet = [
                'audit_num' => $intNewSheetAuditNum,
                'pass_num' => $intNewSheetPassNum,
                'status' => $intNewSheetStatus,
                'update_time' => $intCurrentTime,
                'op_uid' => $intOpUid,
                'op_name' => $strOpName,
                'op_time' => $intCurrentTime,
            ];
            $arrParams = [
                'conds' => [
                    'sheet_id =' => $strSheetId,
                ],
                'data' => $arrNewSheet,
            ];
            $arrData = Dl_Union_InviteSheet::updateData($arrParams);
            if ($arrData === false) {
                Bingo_Log::warning("[auditMember] fail to update sheet, sheet_id: $strSheetId");
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
            $arrNewSheet += $arrPreSheet;

            // 更新会员状态
            $arrNewMember = [
                'update_time' => $intCurrentTime,
                'status' => $intNewMemberStatus,
                'op_uid' => $intOpUid,
                'op_name' => $strOpName,
                'op_time' => $intCurrentTime,
            ];
            if ($intNewMemberStatus == Lib_Define_Union::UNION_MEMBER_STATUS_ENTER) {
                $arrNewMember['enter_time'] = $intCurrentTime;
            }
            $arrParams = [
                'conds' => [
                    'member_id =' => $intMemberId,
                ],
                'data' => $arrNewMember,
            ];
            $arrData = Dl_Union_Member::updateData($arrParams);
            if ($arrData === false) {
                Bingo_Log::warning("[auditMember] fail to update member, member_id: $intMemberId");
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
            $arrNewMember += $arrPreMember;

            // 更新待入驻或未审核的会员状态
            if (($intNewSheetStatus != Lib_Define_Union::UNION_INVITE_SHEET_STATUS_AUDITING && $arrPreSheet['pass_num'] > 0)
                || ($intNewSheetStatus == Lib_Define_Union::UNION_INVITE_SHEET_STATUS_AUDIT_REJECT
                    && $intNewSheetAuditNum < $arrPreSheet['accept_num'])
            ) {
                $intUnEnteredMemberStatus = $intNewSheetStatus == Lib_Define_Union::UNION_INVITE_SHEET_STATUS_AUDIT_PASS ?
                    Lib_Define_Union::UNION_MEMBER_STATUS_ENTER : Lib_Define_Union::UNION_MEMBER_STATUS_UNION_REJECT;
                $arrUnEnteredMember = [
                    'update_time' => $intCurrentTime,
                    'status' => $intUnEnteredMemberStatus,
                    'op_uid' => $intOpUid,
                    'op_name' => $strOpName,
                    'op_time' => $intCurrentTime,
                ];
                if ($intUnEnteredMemberStatus == Lib_Define_Union::UNION_MEMBER_STATUS_ENTER) {
                    $arrUnEnteredMember['enter_time'] = $intCurrentTime;
                    $arrPreStatus = [
                        Lib_Define_Union::UNION_MEMBER_STATUS_AUDIT_PASS,
                    ];
                } else {
                    $arrPreStatus = [
                        Lib_Define_Union::UNION_MEMBER_STATUS_AUDIT_PASS,
                        Lib_Define_Union::UNION_MEMBER_STATUS_AUDITING,
                    ];
                }
                $arrParams = [
                    'conds' => [
                        'sheet_id =' => $strSheetId,
                        'status in (' . implode(', ', $arrPreStatus) . ')',
                    ],
                    'data' => $arrUnEnteredMember,
                ];
                $arrData = Dl_Union_Member::updateData($arrParams);
                if ($arrData === false) {
                    Bingo_Log::warning("[auditMember] fail to update members in sheet, sheet_id: $strSheetId");
                    $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }
            }

            // 更新本月公会人数
            if ($intNewUnionMemberNum > $arrPreUnion['member_num']) {
                $intMonth = (int)date('Ym', $intCurrentTime);
                $arrParams = [
                    'data' => [
                        'member_num' => $intNewUnionMemberNum,
                        'update_time' => $intCurrentTime,
                    ],
                    'conds' => [
                        'union_id =' => $intUnionId,
                        'cmonth =' => $intMonth,
                        'member_num <' => $intNewUnionMemberNum,
                    ],
                ];
                $arrData = Dl_Union_UnionAccount::updateData($arrParams);
                if ($arrData === false) {
                    Bingo_Log::warning("update union account fail, union_id: $intUnionId, month: $intMonth");
                    $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }
            }

            // commit
            if (!Dl_Union_Db::commitTrans()) {
                Bingo_Log::warning("[auditMember] fail to commit trans, member_id: $intMemberId");
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
        } while(0);

        if ($intErrorNo !== Tieba_Errcode::ERR_SUCCESS) {
            if (!Dl_Union_Db::rollbackTrans()) {
                Bingo_Log::warning("[auditMember] rollback fail, member_id: $intMemberId");
            }
            return self::_errRet($intErrorNo);
        }
        $arrNewUnion['contact_phone'] = self::_decryptAESstring($arrNewUnion['contact_phone']);
        $arrData = [
            'union' => $arrNewUnion,
            'sheet' => $arrNewSheet,
            'member' => $arrNewMember,
        ];
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
    }
    /**
     * @brief 获取给待发送信息的达人的公会名，用户名信息
     * @param $arrInput
     * @return array
     */
    public static function getWaitSendSmgInfo()
    {
        $arrDlInput = array(
            'fields' => array(
                'phone_num',
                'union_id',
                'user_id',
                'member_id',
            ),
            'conds'  => array(
                'update_time > ' => (time() - 10800),
                'status = ' => Lib_Define_Union::UNION_MEMBER_STATUS_COMMIT,
            ),
        );
        $arrMemberOut = Dl_Union_Member::getData($arrDlInput);
        if ($arrMemberOut === false) {
            Bingo_Log::warning(sprintf('call Dl_Union_Member::getData error! input=[%s]', serialize($arrDlInput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrRes = array();
        if(!empty($arrMemberOut)){
            $arrAllNeedSendId =  array();
            $arrAllUid        =  array();
            foreach($arrMemberOut as $arrOneMsg){
                $arrAllNeedSendId[] = (int)$arrOneMsg['union_id'];
                $arrAllUid[] = (int)$arrOneMsg['user_id'];
            }
            $strAllNeedSendId =  implode(',' , $arrAllNeedSendId);

            $arrDlInput = array(
                'fields' => array(
                    'union_id',
                    'union_name',
                ),
                'conds'  => array(
                    "union_id in ($strAllNeedSendId)",
                ),
            );
            $arrUnionOut = Dl_Union_Union::getData($arrDlInput);
            if ($arrUnionOut === false) {
                Bingo_Log::warning(sprintf('call Dl_Union_Union::getData error! input=[%s]', serialize($arrDlInput)));
                return false;
            }

            $arrUserId2NickName = self::_mgetNickNameByUserId($arrAllUid);
            if($arrUserId2NickName === false){
                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            $arrUnionId2Name  = array();
            foreach ($arrUnionOut as $arrOneLine) {
                $arrUnionId2Name[$arrOneLine['union_id']] = $arrOneLine['union_name'];
            }

            foreach($arrMemberOut as $arrOneMsg){
                $intUserId   = (int)$arrOneMsg['user_id'];
                $intPhoneNum = self::_decryptAESstring($arrOneMsg['phone_num']);
                $strUnionName  = (string)$arrUnionId2Name[$arrOneMsg['union_id']]; 
                $strNickName = (string)$arrUserId2NickName[$intUserId];
                $arrRes[] = array(
                    'member_id'  => $arrOneMsg['member_id'],
                    'phone_num'  => $intPhoneNum,
                    'union_name' => $strUnionName,
                    'nick_name'  => $strNickName,
                );
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRes);
    }
    /**
     * @param $strPhoneNum
     * @return bool
     */
    private static function _decryptAESstring($strNum) {
       static $objAes = null;
        if (is_null($objAes)) {
           $objAes = new Lib_Aes();
           $objAes->setKey(Lib_Define_Union::UNION_AES_KEY);
        }
        $res = $objAes->decrypt($strNum);
        return $res;
   }

    /**
     * @param  array
     * @return bool
     */
    public static function getMemberListByIdCard($arrInput){
        if(empty($arrInput['id_card'])){
            Bingo_Log::warning(sprintf('params error, input[%s]', serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $objAes = new Lib_Aes();
        $objAes->setKey(Lib_Define_Union::UNION_AES_KEY);
        $strAesIDCard = $objAes->encrypt($arrInput['id_card']);
        $arrConds = array(
            'user_idcard =' => $strAesIDCard,
        );

        $arrDlInput = array(
            'conds' => $arrConds,
        );
        $arrDlOut = Dl_Union_Member::getData($arrDlInput);
        if ($arrDlOut === false) {
            Bingo_Log::warning(sprintf('call Dl_Union_Member::getData error! input=[%s]', serialize($arrDlInput)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        if(!empty($arrDlOut)){
            foreach($arrDlOut as &$val){
                $strIdcard = $objAes->decrypt($val['user_idcard']);
                $val['user_idcard'] = $strIdcard;
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrDlOut);
    }

    /**
     * [根据member id 获取member infos]
     * @param  [array] $arrInput  [input]
     * @return [array]  $arrOutput [output]
     */
    public static function mgetMemberInfoByMid($arrInput){

        if(empty($arrInput['member_id']) || !is_array($arrInput['member_id'])){
            Bingo_Log::warning(sprintf('params error, input[%s]', serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $strMemberId  = implode(',' , $arrInput['member_id']);
        $arrConds = array( "member_id in ( $strMemberId )" );

        $arrDlInput = array(
            'fields'  => array('member_id','user_id','category_id','enter_time','phone_num','union_id'),
            'conds'   => $arrConds,
        );
        $arrDlOut = Dl_Union_Member::getData($arrDlInput);
        if ($arrDlOut === false) {
            Bingo_Log::warning(sprintf('call Dl_Union_Member::getData error! input=[%s]', serialize($arrDlInput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrUserId   = array();
        $arrRes   = array();
        foreach ($arrDlOut as $value) {
            $arrRes[$value['member_id']] = $value;
            $arrUserId[] = $value['user_id'];
        }
        $arrUid2NickName = self::_mgetNickNameByUserId($arrUserId);

        foreach ($arrRes as $member_id => &$value) {
            $value['nick_name'] = $arrUid2NickName[$value['user_id']];
            $value['category_id'] = Lib_Define_Union::$arrCategory[$value['category_id']];
            $value['phone_num']  = self::_decryptAESstring($value['phone_num']);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRes);
    }
    /**
     * [获取成员信息 按user_id]
     * @param  [array] $arrInput  [input]
     * @return [array]  $arrOutput [output]
     */
    public static function getMemberIdByUserId($arrInput){
        if(empty($arrInput['user_id'])){
            Bingo_Log::warning(sprintf('params error, input[%s]', serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUserId = intval($arrInput['user_id']);

        $arrConds = array(
            'user_id = ' => $intUserId,
            'status = '  => Lib_Define_Union::UNION_MEMBER_STATUS_ENTER
        );
        $arrDlInput = array(
            'fields'  => array('member_id','user_id'),
            'conds'   => $arrConds,
        );
        $arrDlOut = Dl_Union_Member::getData($arrDlInput);
        if ($arrDlOut === false) {
            Bingo_Log::warning(sprintf('call Dl_Union_Member::getData error! input=[%s]', serialize($arrDlInput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrDlOut);
    }
    /**
     * [获取成员信息 按user_id]
     * @param $arrInput
     * @return array
     */
    public static function updateStatusForSendMsg($arrInput)
    {
        if(empty($arrInput['member_id']) || !is_array($arrInput['member_id'])){
            Bingo_Log::warning(sprintf('params error, input[%s]', serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $strMemberId = implode(',', $arrInput['member_id']);
        $arrConds = array(
            "member_id in ($strMemberId)",
            'status = '   => Lib_Define_Union::UNION_MEMBER_STATUS_COMMIT,
        );
        $arrData = array(
            'status' => Lib_Define_Union::UNION_MEMBER_STATUS_WAITING,
            'update_time' =>  time(),
        );

        $arrDlInput = array(
            'data' => $arrData,
            'conds' => $arrConds,
        );
        $arrDlOut = Dl_Union_Member::updateData($arrDlInput);
        if ($arrDlOut === false) {
            Bingo_Log::warning(sprintf('call Dl_Union_Member::updateData error! input=[%s]', serialize($arrDlInput)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }
}
