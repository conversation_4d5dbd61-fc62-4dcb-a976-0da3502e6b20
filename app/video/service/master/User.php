<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2018/5/16
 * Time: 11:10
 */

class Service_Master_User extends Service_Master_Base
{

    const USER_TYPE_OLD = 1;
    const USER_TYPE_ALL_NEW = 2;
    const USER_TYPE_INVITE_NEW = 3;
    const WORD_LIST_NAME = 'tb_wordlist_redis_Nani_MASTER_CONFIG';
    const KEY_SHOW_OLD_USER_RED_PACKET = "nani:old_red_packet";
    const INVITE_CODE_LEN = 8;

    const CLIENT_TYPE_IOS = 1;
    const CLIENT_TYPE_ANDROID = 2;

    /**
     * @brief  获取用户类型
     * @param  $arrInput
     * @return array
     */
    public static function getNewUserType($arrInput)
    {
        $intUserId = intval($arrInput['user_id']);
        $strCuid   = strval(trim($arrInput['cuid']));
        $strImei   = strval(trim($arrInput['imei']));
        $strTBUSS  = strval(trim($arrInput['tbuss']));

        if ($intUserId <= 0) {
            Bingo_Log::warning(sprintf("param error. [input = %s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrOutput = array();

        // 先查uid是否存在
        $arrDBInput = array(
            'cond' => array(
                'user_id' => $intUserId,
            ),
        );
        $arrRet     = Dl_Nanipush_NaniUser::select($arrDBInput);
        if ($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf("call Dl_Nanipush_NaniUser:select failed. [input: %s] [output: %s]",
                serialize($arrDBInput), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        if (!empty($arrRet['data'])) {
            $arrOutput['category'] = self::USER_TYPE_OLD;
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
        }

        // cuid不空，查uid和cuid
        if (!empty($strCuid)) {
            // 先查uid是否存在
            $arrDBInput = array(
                'cond' => array(
                    'cuid' => $strCuid,
                ),
            );
            $arrRet     = Dl_Nanipush_NaniUser::select($arrDBInput);
            if ($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning(sprintf("call Dl_Nanipush_NaniUser:select failed. [input: %s] [output: %s]",
                    serialize($arrDBInput), serialize($arrRet)));
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            if (!empty($arrRet['data'])) {
                $arrOutput['category'] = self::USER_TYPE_OLD;
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
            }
        } else if (!empty($strImei)) {
            $arrDBInput = array(
                'cond' => array(
                    'imei' => $strImei,
                ),
            );
            $arrRet     = Dl_Nanipush_NaniUser::select($arrDBInput);
            if ($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning(sprintf("call Dl_Nanipush_NaniUser:select failed. [input: %s] [output: %s]",
                    serialize($arrDBInput), serialize($arrRet)));
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            if (!empty($arrRet['data'])) {
                $arrOutput['category'] = self::USER_TYPE_OLD;
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
            }
        }

        // 查一下是否是邀请新用户
        $bolInviteNewUser = self::_checkInviteNewUser($strTBUSS, $intUserId, $strInviteCode);
        if (!$bolInviteNewUser) {
            $arrOutput['category'] = self::USER_TYPE_ALL_NEW;
        } else {
            $arrOutput['category'] = self::USER_TYPE_INVITE_NEW;
            $arrOutput['invitor']  = $strInviteCode;
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    /**
     * @param  $strInviteCode
     * @return mixed
     */
    private static function _deInviteCode($strInviteCode)
    {
        $intLen = strlen($strInviteCode);
        if (empty($strInviteCode) || self::INVITE_CODE_LEN != $intLen) {
            Bingo_Log::warning("input params invalid. input: [" . json_encode($strInviteCode) . "]");
            return 0;
        }

        $strSource    = 'KRXEAC7BMTQ1S2NWH3O6VF5ZIUJGPY9L48D';
        $intSourceLen = strlen($strSource);
        $strCode      = $strInviteCode;
        $strCode      = strtoupper($strCode);

        if (strrpos($strCode, '0') !== false) {
            $strCode = substr($strCode, strrpos($strCode, '0') + 1);
        }
        $intCodeLen = strlen($strCode);
        $strCode    = strrev($strCode);
        $intUid     = 0;

        for ($i = 0; $i < $intCodeLen; $i++) {
            $intUid += strpos($strSource, $strCode[$i]) * pow($intSourceLen, $i);
        }
        return $intUid;
    }

    /**
     * @brief  从arch那边获取手机号，判断是否为邀请新用户
     * @param  $strTBUSS
     * @param  $intUserId
     * @param  $strInviteCode
     * @return bool
     */
    private static function _checkInviteNewUser($strTBUSS, $intUserId, &$strInviteCode)
    {
        if (empty($strTBUSS)) {
            return false;
        }
        $arrParam = array(
            'TBUSS' => $strTBUSS,
        );
        $arrRet = Tieba_Service::call('tbpass', 'getUinfoByTBUSS', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] || empty($arrRet['data']['securemobil'])) {
            Bingo_Log::warning(sprintf("call tbpass getUinfoByTBUSS failed. [input = %s][output = %s]", serialize($arrParam), serialize($arrRet)));
            return false;
        }

        // 先从arch那取用户信息，主要是获取手机号
        $strEncodeMobile = $arrRet['data']['securemobil'];
        $intRegTime      = $arrRet['data']['regtime']; // 注册时间
        $strDecodeMobile = base64_decode($strEncodeMobile);  // 1先base64_decode
        $strDecodeMobile = mcrypt_decrypt(MCRYPT_RIJNDAEL_128, $intUserId, $strDecodeMobile, MCRYPT_MODE_CBC, $intRegTime);

        // 再去查下邀请新用户的库，如果有记录就表示邀请新用户，否则不是
        $arrParam = array(
            'mobile' => $strDecodeMobile,
        );
        $arrRet = Service_Master_NewReg::getNewUnRegUserByMobile($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            return false;
        }
        if (!empty($arrRet['data'])) {
            $strInviteCode = strval($arrRet['data']['invite_code']);
            return true;
        }
        else {
            return false;
        }
    }

    /**
     * @brief  检查新用户红包
     * @param  $arrInput
     * @return array
     */
    public static function checkNewUserRedPacket($arrInput)
    {
        // 词表加时间限制
        if (!self::_checkMasterDuration()) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        $intUserId = intval($arrInput['user_id']);
        $intMobile = intval($arrInput['mobile']);
        $strCuid   = strval(trim($arrInput['cuid']));
        $strImei   = strval(trim($arrInput['imei']));
        $strTBUSS  = strval(trim($arrInput['tbuss']));

        $strClientVersion = strval(trim($arrInput['client_version']));
        $intClientType    = strval(trim($arrInput['client_type']));

        if(empty($strClientVersion)){
            Bingo_Log::warning("no client_version");
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);;
        }
        // 检查用户状态
        $arrParam = array(
            'user_id' => $intUserId,
            'cuid'    => $strCuid,
            'imei'    => $strImei,
            'tbuss'   => $strTBUSS,
        );
        $arrRet   = self::getNewUserType($arrParam);
        $strInVitor = strval($arrRet['data']['invitor']);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("get new user type failed. [input = %s][output = %s]", serialize($arrParam), serialize($arrRet)));
            return self::_errRet($arrRet['errno']);
        }

        // 旧用户返回
        $intUserType = intval($arrRet['data']['category']);
        if ($intUserType === self::USER_TYPE_OLD) {
            $arrOutput['category'] = $intUserType;
            $strType = 'master_cash_old_user';
            $intAmount = self::_getConfigMoney($strType);
            // 如果老用户，判断是否需要展示红包弹窗
            if (self::_showOldUserRedPacket($intUserId) && $intAmount) {
                $arrOutput['amount'] = round($intAmount / 100, 2);
            }
            else {
                $arrOutput['amount'] = 0;
            }
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
        }

        // 如果是新用户和邀请新用户
        if ($intUserType === self::USER_TYPE_ALL_NEW || $intUserType === self::USER_TYPE_INVITE_NEW) {

            // get client version

            $strVersionCheck = self::_getVersionCheck($intClientType);

            if((Molib_Util_Version::compare($strClientVersion,$strVersionCheck) > 0)){
                Bingo_Log::warning("cheat user ! uid is $intUserId and  cuid is $strCuid ");
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
            }

            // check client_version client_type  cuid
            if($intClientType == self::CLIENT_TYPE_ANDROID ){
//                if((Molib_Util_Version::compare($strClientVersion,$strVersionCheck) > 0)){
//                    Bingo_Log::warning("cheat user ! uid is $intUserId and  cuid is $strCuid ");
//                    return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
//                }
                if(strpos($strCuid,"|com.baidu.nani") === false ){
                    Bingo_Log::warning("cheat user ! uid is $intUserId and  cuid is $strCuid ");
                    return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
                }

            }

            // sdk

            $arrSdkUid = array(
                'user_id' => $intUserId,
            );

            $arrRetSdk   = Tieba_Service::call('video', 'isRegisterByValidSDKVersion', $arrSdkUid, null, null, 'post', 'php', 'utf-8');
            if (Tieba_Errcode::ERR_SUCCESS !== $arrRetSdk['errno']) {
                Bingo_Log::warning(sprintf("call video isRegisterByValidSDKVersion failed. [input = %s][output = %s]", serialize($arrSdkUid), serialize($arrRetSdk)));
                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            $intStatus = isset($arrRetSdk['data']['status']) ? $arrRetSdk['data']['status'] : 0;
            if($intStatus != 1){
                Bingo_Log::warning("cheat user ! uid is $intUserId and  cuid is $strCuid ");
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
            }

            // 获取新用户的红包金额
            $strType = $intUserType === self::USER_TYPE_ALL_NEW ? 'master_cash_new_user' : 'master_cash_new_user_invite';
            $intAmount = self::_getConfigMoney($strType);

            // 发新用户红包
            //add by lhy 第三方登陆的无手机号的也不给钱 加版本控制 2.2.0以上的判断手机号
            if((Molib_Util_Version::compare($strClientVersion,'2.2.0') <= 0)) {
                if ($intMobile !== 0) {
                    $arrParam = array(
                        'user_id' => $intUserId,      // 签到的 uid
                        'common_id' => $strInVitor,     // 通用信息字段，写签到的日期
                        'act_type' => 'newUser',       // 固定，红包类型，sign
                        'req_type' => 'master',        // 固定，发红包的系统，master 代表 师徒系统
                        'req_id' => 2,               // 固定，师徒活动
                        'amount' => $intAmount,
                        'need_2nd_audit' => true,
                    );
                    $arrRet = Tieba_Service::call('video', 'setCash', $arrParam, null, null, 'post', 'php', 'utf-8');
                    if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
                        Bingo_Log::warning(sprintf("call video setCash failed. [input = %s][output = %s]", serialize($arrParam), serialize($arrRet)));
                        return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                    }
                    $intAmount = intval($arrRet['data']['amount']);
                }
            }else{
                $arrParam = array(
                    'user_id' => $intUserId,      // 签到的 uid
                    'common_id' => $strInVitor,     // 通用信息字段，写签到的日期
                    'act_type' => 'newUser',       // 固定，红包类型，sign
                    'req_type' => 'master',        // 固定，发红包的系统，master 代表 师徒系统
                    'req_id' => 2,               // 固定，师徒活动
                    'amount' => $intAmount,
                    'need_2nd_audit' => true,
                );
                $arrRet = Tieba_Service::call('video', 'setCash', $arrParam, null, null, 'post', 'php', 'utf-8');
                if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
                    Bingo_Log::warning(sprintf("call video setCash failed. [input = %s][output = %s]", serialize($arrParam), serialize($arrRet)));
                    return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
                $intAmount = intval($arrRet['data']['amount']);
            }


            // 自动绑定红包,校验 cuid 和imei
            if ($intUserType === self::USER_TYPE_INVITE_NEW && !empty($strInVitor)) {

                //如果是二次分享活动中的一个虚拟师傅&此人通过手机号登陆的，取出nani欠该师傅的钱
                if(!empty($intMobile) && !empty($intUserId)) {
                    $arrInput = array(
                        'mobile' => $intMobile,
                        'user_id' => $intUserId,
                    );
                    $arrRet = self::_setVirtualMasterInfo($arrInput);
                    if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
                        Bingo_Log::warning(sprintf("set virtural master info failed. [input = %s][output = %s]", serialize($arrInput), serialize($arrRet)));
                        return self::_errRet($arrRet['errno']);
                    }
                }
                //END


                $arrCommitInviteCodeInput = array(
                    'user_id'     => $intUserId,
                    'mobile'  => $intMobile,
                    'invite_code' => $strInVitor,
                    'type'        => 1,
                    'cuid'    => $strCuid,
                    'imei'    => $strImei,
                );
                $arrCommitInviteCodeOut   = Service_Master_Platform::commitInviteCodeAndGetCash($arrCommitInviteCodeInput);
                if (Tieba_Errcode::ERR_SUCCESS !== $arrCommitInviteCodeOut['errno']) {
                    Bingo_Log::warning(sprintf("call Service_Master_Platform::commitInviteCodeAndGetCash failed. [input = %s][output = %s]", serialize($arrCommitInviteCodeInput), serialize($arrCommitInviteCodeOut)));
                }
            }
        }

        // 返回数据
        $arrOutput = array(
            'category' => $intUserType,
            'amount'   => empty($intAmount) ? 0 : round($intAmount / 100, 2),
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }
    /**
     * @param $intClientType
     * @return string
     */
    private static function _setVirtualMasterInfo($arrInput){

        $intMobile = $arrInput['mobile'];
        $intUserId = $arrInput['user_id'];
        $arrInput = array(
            'inviter_mobile' => $intMobile,
        );

        $arrOutput   = Tieba_Service::call('video', 'getRecordByInviterMobile', $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($arrOutput === false || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
            Bingo_Log::warning(sprintf("call video getRecordByInviterMobile failed. [input = %s][output = %s]", serialize($arrInput), serialize($arrOutput)));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $objMultiX = new Tieba_Multi('insert_redpacket_multicall');
        $serviceName = 'video';
        $serviceNameUser = 'user';
        $methodRedpacket = 'sendOweRedpacket';
        $methodRelationship = 'createInviteRelationship';
        $methodFollower = 'setUserFollower';

        foreach ($arrOutput['data'] as $value) {

            //注册给钱service,回写redpacket record info表
            $arrInput = array(
                'user_id' => $intUserId,
                'redpacket_type' => intval($value['redpacket_type']),
                'redpacket_type_ext' => intval($value['user_id']),
                'amount' => intval($value['amount']),
            );
            $arrMultiInput = array(
                "serviceName" => $serviceName,
                "method" => $methodRedpacket,
                'ie' => 'utf-8',
                "input" => $arrInput,
            );
            $objMultiX->register($methodRedpacket . '_' . $value['id'], new Tieba_Service($serviceName), $arrMultiInput);

            if($value['redpacket_type'] == Lib_Redpacket::REDPACKET_TYPE_INVITE) {//欠费表里也可能记录着徒弟行为给虚拟师傅挣得钱
                //注册邀请关系service
                $arrInput = array(
                    'uid' => intval($value['user_id']),
                    'inviter_uid' => $intUserId,
                );
                $arrMultiInput = array(
                    "serviceName" => $serviceName,
                    "method" => $methodRelationship,
                    'ie' => 'utf-8',
                    "input" => $arrInput,
                );
                $objMultiX->register($methodRelationship . '_' . $value['id'], new Tieba_Service($serviceName), $arrMultiInput);

                //注册关注service
                $arrInput = array(
                    'user_id' => intval($value['user_id']),
                    'followed_user_ids' => array(
                        $intUserId,
                    ),
                    'need_notice' => 1,
                    //消息提醒
                    'in_live' => 0,
                );
                $arrMultiInput = array(
                    "serviceName" => $serviceNameUser,
                    "method" => $methodFollower,
                    'ie' => 'utf-8',
                    "input" => $arrInput,
                );
                $objMultiX->register($methodFollower . '_' . $value['id'], new Tieba_Service($serviceNameUser), $arrMultiInput);
            }
        }

        $objMultiX->call();
        foreach ($arrOutput['data'] as $value) {
            $arrMultiOutput = $objMultiX->getResult($methodRedpacket . '_' . $value['id']);
            if (false === $arrMultiOutput || Tieba_Errcode::ERR_SUCCESS != $arrMultiOutput["errno"]) {
                $strLog = "call $serviceName $methodRedpacket fail. id:[" . $value['id'] . "]  output:[" . serialize($arrMultiOutput) . "]";
                Bingo_Log::warning($strLog);

            }
            if($value['redpacket_type'] == Lib_Redpacket::REDPACKET_TYPE_INVITE) {//欠费表里也可能记录着徒弟行为给虚拟师傅挣得钱

                $arrMultiOutput = $objMultiX->getResult($methodRelationship . '_' . $value['id']);
                if (false === $arrMultiOutput || Tieba_Errcode::ERR_SUCCESS != $arrMultiOutput["errno"]) {
                    $strLog = "call $serviceName $methodRelationship fail. id:[" . $value['id'] . "]  output:[" . serialize($arrMultiOutput) . "]";
                    Bingo_Log::warning($strLog);

                }

                $arrMultiOutput = $objMultiX->getResult($methodFollower . '_' . $value['id']);
                if (false === $arrMultiOutput || Tieba_Errcode::ERR_SUCCESS != $arrMultiOutput["errno"]) {
                    $strLog = "call $serviceNameUser $methodFollower fail. id:[" . $value['id'] . "]  output:[" . serialize($arrMultiOutput) . "]";
                    Bingo_Log::warning($strLog);

                }
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $intClientType
     * @return string
     */
    private static function _getVersionCheck($intClientType){
        $strVersionCheck = "1.8.0";
        $strVersionCheckIos = "1.8.0";

        $objWordServer = Wordserver_Wordlist::factory();

        $arrKeys = array(
            "android_version_check",
            "ios_version_check",
        );

        $arrRet = $objWordServer->getValueByKeys($arrKeys, "tb_wordlist_redis_Nani_MASTER_CONFIG");

        if (!$arrRet) {
            Bingo_Log::warning("redis wordlist failed!!!");
            return $strVersionCheck;
        }

        if($intClientType ==  self::CLIENT_TYPE_IOS){
            if(isset($arrRet['ios_version_check'])){
                $strVersionCheck = $arrRet['ios_version_check'];
            } else {
                $strVersionCheck = $strVersionCheckIos;
            }
        }
        if($intClientType ==  self::CLIENT_TYPE_ANDROID){
            if(isset($arrRet['android_version_check'])){
                $strVersionCheck = $arrRet['android_version_check'];
            }
        }


        return $strVersionCheck;
    }


    /**
     * @brief  检查是否展示老用户红包弹窗
     * @param  $intUserId
     * @return bool
     */
    private static function _showOldUserRedPacket($intUserId) {
        // 先检查是否弹过
        $arrParam    = array(
            'key'   => self::KEY_SHOW_OLD_USER_RED_PACKET,
            'field' => $intUserId,
        );
        $intShowFlag = Util_Redis::hgetFromRedis($arrParam);
        if ($intShowFlag === false) {
            Bingo_Log::warning(sprintf("get show red packet flag failed. [input = %s]", serialize($arrParam)));
            return false;
        }

        if (empty($intShowFlag)) {
            // 没弹过就弹窗并标识
            $arrParam    = array(
                'key'   => self::KEY_SHOW_OLD_USER_RED_PACKET,
                'field' => $intUserId,
                'value' => time(),
            );
            $intShowFlag = Util_Redis::hsetToRedis($arrParam);
            if ($intShowFlag === false) {
                Bingo_Log::warning(sprintf("get show red packet flag failed. [input = %s]", serialize($arrParam)));
                return false;
            }
            return true;
        }
        else { //弹过了就返回false
            return false;
        }
    }

    /**
     * @brief  获取词表配置的签到红包金额
     * @param  $strType
     * @return integer
     */
    private static function _getConfigMoney($strType) {
        $handleWordServer = Wordserver_Wordlist::factory();
        $arrKeys = array(
            'master_cash_old_user',
            'master_cash_new_user_invite',
            'master_cash_new_user',
        );
        $arrItemInfo = $handleWordServer->getValueByKeys($arrKeys, self::WORD_LIST_NAME);

        if (!empty($arrItemInfo[$strType])) {
            return $arrItemInfo[$strType];
        }
        else {
            return 0;
        }
    }

    /**
     * @brief  check是否处于师徒活动有效期
     * @return bool
     */
    private static function _checkMasterDuration() {
        $handleWordServer = Wordserver_Wordlist::factory();
        $arrKeys = array(
            'master_start_time',
            'master_end_time',
        );
        $arrItemInfo = $handleWordServer->getValueByKeys($arrKeys, self::WORD_LIST_NAME);
        $intTime = time();
        if ($intTime < intval($arrItemInfo['master_start_time']) || $intTime > intval($arrItemInfo['master_end_time'])) {
            return false;
        }
        return true;
    }
}
