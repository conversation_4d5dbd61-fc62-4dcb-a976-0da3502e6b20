<?php


define("MODULE","Video_service");
class Service_Pgc_Pgc{
    const SERVICE_NAME = "Service_Pgc_Pgc";
    const PORTAIT_URL = 'http://tb.himg.baidu.com/sys/portrait/item/';
    protected static $_conf = null;
    

    /**
     * @brief init
     * @return: true if success. false if fail.

    **/
    private static function _init(){

    	//add init code here. init will be called at every public function beginning.
    	//not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
    	//init should be recalled for many times.

    	if(self::$_conf == null){
    		self::$_conf = Bd_Conf::getConf("/app/video/service_pgc_user");
    		if(self::$_conf == false){
    			Bingo_Log::warning("init get conf fail.");
    			return false;
    		}

    	}
    	return true;
    }


    private static function _errRet($errno, $arrData = array(), $retKey = 'data'){
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            $retKey => $arrData,
        );
    }

    public static function preCall($arrInput){
        // pre-call hook
    }

    public static function postCall($arrInput){
        // post-call hook
    }
    
    /**传可变参数
     * 该函数只校验是否存在该值，对具体的值有效性不做校验
     */
    private static function  _checkParam(/*$arrInput, x, xx, xxx,*/){
        $args = func_get_args();
        if(null == $args || empty($args) || null == $args[0] || empty($args[0]) || !is_array($args[0])){
            return false;
        }
        $arrInput = $args[0];
        $count = count($args);
        for ($i = 1; $i < $count; $i++){
            if(!isset($arrInput[$args[$i]])){
                return false;
            }
        }
        return true;
    }
    
    /**
     * 校验非空、非0
     * @param array $arrOut
     * @param string $key
     * @return mixed
     */
    private static function _checkOutput($arrOut, $key = 'errno'){
        if(null == $arrOut || empty($arrOut) || !isset($arrOut[$key]) || Tieba_Errcode::ERR_SUCCESS != $arrOut[$key]){
            return false;
        }
        return true;
    }
    
    public static function addPgcUser($arrInput){
        if(!self::_checkParam($arrInput, 'uid', 'fid', 'op_uid') 
            || intval($arrInput['uid']) <= 0 
            || intval($arrInput['fid']) <= 0
            || intval($arrInput['op_uid']) <= 0){
            Bingo_Log::warning('addPgcUser input param is invalid.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'function' => 'checkPgcUserIsExist',
            'user_id' => intval($arrInput['uid']),
        );
        $arrDlOut = Dl_Pgc_User::execSql($arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('addPgcUser call checkPgcUserIsExist db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        if(isset($arrDlOut['results']) && intval($arrDlOut['results'][0][0]['cnt']) > 0){
        	Bingo_Log::warning('addPgcUser had a pgc user,not need add. uid:['.$arrInput['uid'].'] out:['.serialize($arrDlOut).']');
            return self::_errRet(Tieba_Errcode::ERROR_MIS_FIELD_HAS_EXSITS);
        }
        $arrDlInput = array(
            'function' => 'addPgcUser',
            'user_id' => intval($arrInput['uid']),
            'forum_id' => intval($arrInput['fid']),
            'remark' => $arrInput['remark'],
            'create_time' => time(),
            'op_uid' => intval($arrInput['op_uid']),
            'op_name' => $arrInput['op_name'],
            //'op_time' => time(),
        );
        $arrDlOut = Dl_Pgc_User::execSql($arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('addPgcUser call addPgcUser db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }
    
    public static function getPgcUserList($arrInput){
        if(!self::_checkParam($arrInput, 'pn', 'ps')){
            Bingo_Log::warning('getPgcUserList input param is invalid.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $pn = intval($arrInput['pn'])>0?intval($arrInput['pn']):1;
        $ps = intval($arrInput['ps'])>0?intval($arrInput['ps']):20;
        
        $offset = ($pn-1)*$ps;
        $size = $ps;
        $strCondition = '';
        if(isset($arrInput['uid']) && intval($arrInput['uid']) > 0){
            $uid = intval($arrInput['uid']);
            $strCondition .= ('where user_id = '.$uid);
        }
        if(isset($arrInput['check_status'])){
            $checkStatus = intval($arrInput['check_status']);
            if(empty($strCondition)){
                $strCondition .= ('where check_status = '.$checkStatus);
            }else{
                $strCondition .= (' and check_status = '.$checkStatus);
            }
        }

        if (isset($arrInput['create_time']) && ($arrInput['create_time'] > 0)) {
            $intCreateTime = $arrInput['create_time'];
            $strCondition .= " where create_time > $intCreateTime ";
        }

        $arrDlInput = array(
            'function' => 'getPgcUserSum',
            'append_condition' => $strCondition,
        );
        $arrDlOut = Dl_Pgc_User::execSql($arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getPgcUserSum call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $total = intval($arrDlOut['results'][0][0]['cnt']);
        if($total <= 0){
            $arrData = array(
                'list' => array(),
                'page' => array(
                    'pn' => $pn,
                    'ps' => 0,
                    'total' => 0,
                    'has_more' =>0,
                )
            );
            Bingo_Log::warning('getPgcUserSum return data is empty, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
        }
        $arrDlInput = array(
            'function' => 'queryPgcUserList',
            'append_condition' => $strCondition,
            'offset' => $offset,
            'size' => $size,
        );
        $arrDlOut = Dl_Pgc_User::execSql($arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('queryPgcUserList call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrPgcUserList = $arrDlOut['results'][0];
        if(empty($arrPgcUserList)){
            $arrData = array(
                'list' => array(),
                'page' => array(
                    'pn' => $pn,
                    'ps' => 0,
                    'total' => 0,
                    'has_more' =>0,
                )
            );
            Bingo_Log::warning('queryPgcUserList return data is empty, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
        }
        $arrUids = array();
        $arrFids = array();
        foreach ($arrPgcUserList as $arrRow){
            $uid = intval($arrRow['user_id']);
            if($uid > 0 && !in_array($uid, $arrUids)){
                $arrUids[] = $uid;
            }
            $fid = intval($arrRow['forum_id']);
            if($fid > 0 && !in_array($fid, $arrFids)){
                $arrFids[] = $fid;
            }
            /*$opUid = intval($arrRow['op_uid']);
            if($opUid > 0 && !in_array($opUid, $arrUids)){
                $arrUids[] = $uid;
            }*/
        }
        
        $arrUserInfos = self::_getUserAttrInfo($arrUids);
        $arrFnames = self::_getFnameByFid($arrFids);
        
        foreach ($arrPgcUserList as &$arrRow){
            $uid = intval($arrRow['user_id']);
            $fid = intval($arrRow['forum_id']);
            $opUid = intval($arrRow['op_uid']);
            $arrRow['uid'] = $uid;
            $arrRow['fid'] = $fid;
            $arrRow['uname'] = $arrUserInfos[$uid]['uname'];
            $arrRow['fname'] = $arrFnames[$fid];
            //$arrRow['op_name'] = $arrUserInfos[$opUid]['uname'];
            $arrRow['bus_lic_pic_url'] = self::_getPicUrlByPicid($arrRow['business_license_pic_id']);
            $arrRow['id_card_pic_url'] = self::_getPicUrlByPicid($arrRow['id_card_pic_id']);
        }
        $count = count($arrPgcUserList);
        $arrData = array(
            'list' => $arrPgcUserList,
            'page' => array(
                'pn' => $pn,
                'ps' => $count,
                'total' => $total,
                'has_more' => ($offset+$count)<$total?1:0,
            )
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
    }
    
    public static function getPgcUserDetails($arrInput){
        if(!self::_checkParam($arrInput, 'uid') || intval($arrInput['uid']) <= 0){
            Bingo_Log::warning('getPgcUserInfo input param is invalid.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $bolNotTransform = (isset($arrInput['not_transform']) && 0 == $arrInput['not_transform']?true:false);
        $uid = intval($arrInput['uid']);
        $arrDlInput = array(
            'function' => 'queryPgcUserDetails',
            'user_id' => $uid,
        );
        $arrDlOut = Dl_Pgc_User::execSql($arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getPgcUserDetails call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrUserDetails = $arrDlOut['results'][0][0];
        if(empty($arrUserDetails) || !isset($arrUserDetails['user_id']) 
            || $uid != intval($arrUserDetails['user_id'])){
            Bingo_Log::warning('getPgcUserDetails return data is empty, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $fid = $arrUserDetails['forum_id'];
        $uid = $arrUserDetails['user_id'];
        $arrUserDetails['uid'] = $uid;
        $arrUserDetails['fid'] = $fid;
        if(!$bolNotTransform){
            $arrFnames = self::_getFnameByFid(array($fid));
            $arrUserInfo = self::_getUserAttrInfo(array($uid));
            if(!empty($arrUserInfo)){
                $arrUserDetails['uname'] = $arrUserInfo[$uid]['uname'];
                $arrUserDetails['portrait_url'] = $arrUserInfo[$uid]['portrait_url'];
                $arrUserDetails['fan_num'] = $arrUserInfo[$uid]['fan_num'];
                //$arrUserDetails['op_name'] = $arrUserInfo[$arrUserDetails['op_uid']]['uname'];
            }
            
            $arrUserDetails['fname'] = empty($arrFnames)?'':$arrFnames[$fid];
            $arrUserDetails['bus_lic_pic_url'] = self::_getPicUrlByPicid($arrUserDetails['business_license_pic_id']);
            $arrUserDetails['id_card_pic_url'] = self::_getPicUrlByPicid($arrUserDetails['id_card_pic_id']);
        }
        
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrUserDetails);
    }
    
    private static function _getPicUrlByPicid($picid, $fid = 0){
        if (0 >= $picid) {
            Bingo_Log::warning('Service_Record_Record _getPicUrlByPicid call fail.param invalid.[' . $picid . ']');
            return '';
        }
        $arrInput = array(
            array(
                'pic_id' => $picid,
                'foreign_key' => $fid,
                'pic_spec' => 'pic',
                'product_name' => 'forum',
                'domain' => 'img'.'src.baidu.com'
            )  
        );
        $outPut = Bd_Pic::pid2Url($arrInput, false);
        if ($outPut === false || empty($outPut['resps'][0])) {
            Bingo_Log::warning('_getPicUrlByPicid faile.input:[' . serialize($arrInput) . '] out:[' . serialize($outPut) . ']');
            return '';
        }
        return $outPut['resps'][0];
    }
    
    private static function _getUserInfo($arrUserInfo)
    {
        $arrResult       = $arrUserInfo;
        $intUid          = intval($arrUserInfo['user_id']);
        $strUname        = strval($arrUserInfo['user_name']);
        $strNickName     = strval($arrUserInfo['user_nickname']);
        $intPortraitTime = intval($arrUserInfo['portrait_time']);
    
        $arrResult['name']      = $strUname;
        $arrResult['id']        = $intUid;
        $arrResult['portrait']  = Tieba_Ucrypt::encode($intUid, Molib_Util_Encode::convertUTF8ToGBK($strUname), $intPortraitTime);
        $arrResult['name_show'] = empty($strNickName) ? $strUname : $strNickName;
        $arrResult['user_type'] = $arrUserInfo['user_type'] > 0 ? 2 : 1;
        $arrResult['is_verify'] = $arrUserInfo['user_type'] > 0 ? true : false;
    
        return $arrResult;
    }
    
    private static function _getUserAttrInfo($arrUids)
    {
        if (empty($arrUids) || ! is_array($arrUids)) {
            Bingo_Log::warning('call _getUserAttrInfo fail, input:[' . serialize($arrUids) . ']');
            return false;
        }
        $arrInput = array(
            'user_id' => $arrUids,
            'need_follow_info' => 1,
        );
        $arrOutput = Tieba_Service::call('user', 'mgetUserDataEx', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (! self::_checkOutput($arrOutput)) {
            Bingo_Log::warning('call user mgetUserData fail, input:[' . serialize($arrInput) . '],output:[' . serialize($arrOutput) . ']');
            return false;
        }
        $arrUserInfos = array();
        $arrRet = $arrOutput;
        foreach ($arrRet['user_info'] as $uid => $arrUserInfoTmp) {
            $arrTmp = self::_getUserInfo($arrUserInfoTmp);
            if($uid != $arrTmp['id']){
                continue;
            }
            $arrUser = array(
                'uid' => $uid,
                'uname' => $arrTmp['name'],
                'user_type' => $arrTmp['user_type'],
                'portrait' => $arrTmp['portrait'],
                'portrait_url' => self::PORTAIT_URL.$arrTmp['portrait'].'.jpg',
                'fan_num' => intval($arrUserInfoTmp['followed_count']),
            );
            $arrUserInfos[$uid] = $arrUser;
        }
        return $arrUserInfos;
    }
    
    private static function _getFnameByFid($arrFids){
        if(empty($arrFids) || !is_array($arrFids)){
            Bingo_Log::warning('call _getFnameByFid fail, input:[' . serialize($arrFids) . ']');
            return false;
        }
        $input = array(
            "forum_id" => $arrFids,
        );
        $res   = Tieba_Service::call('forum', 'getFnameByFid', $input, NULL, NULL, 'post', 'php', 'utf-8');
        if(!self::_checkOutput($res)){
            Bingo_Log::warning('call forum getFnameByFid fail, input:[' . serialize($input) . '],output:[' . serialize($res) . ']');
            return false;
        }
        $arrFnamesByFid = array();
        foreach ($res['forum_name'] as $fid => $arrFname){
            if(1 == $arrFname['exist'] && $fid == $arrFname['forum_id']){
                $arrFnamesByFid[$fid] = $arrFname['forum_name'];
            }
        }
        return $arrFnamesByFid;
    }
    
    public static function getPgcUserBaseInfo($arrInput){
        if(!self::_checkParam($arrInput, 'uid') || intval($arrInput['uid']) <= 0){
            Bingo_Log::warning('getPgcUserInfo input param is invalid.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $uid = intval($arrInput['uid']);
        $arrDlInput = array(
            'function' => 'queryPgcUserInfo',
            'user_id' => $uid,
            'select_fields' => 'user_id, check_status, check_remark',
        );
        $arrDlOut = Dl_Pgc_User::execSql($arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getPgcUserBaseInfo call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        if(!isset($arrDlOut['results'][0][0]['user_id']) || !isset($arrDlOut['results'][0][0]['check_status'])
            || $uid != intval($arrDlOut['results'][0][0]['user_id'])){
            Bingo_Log::warning('getPgcUserInfo return data is empty.uid:['.$uid.'] out:['.serialize($arrDlOut).']');
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $checkStatus = $arrDlOut['results'][0][0]['check_status'];
        $checkRemark = $arrDlOut['results'][0][0]['check_remark'];
        $arrUserInfo = self::_getUserAttrInfo(array($uid));
        if(false == $arrUserInfo || empty($arrUserInfo)){
            Bingo_Log::warning('getPgcUserInfo call _getUserAttrInfo fail.uid:['.$uid.'] out:['.serialize($arrUserInfo).']');
            //return self::_errRet(Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL);
        }
        $arrData = array(
            'uid' => $uid,
            'uname' => $arrUserInfo[$uid]['uname'],
            'portrait_url' => $arrUserInfo[$uid]['portrait_url'],
            'fan_num' => $arrUserInfo[$uid]['fan_num'],
            'check_status' => $checkStatus,
            'check_remark' => $checkRemark,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
    }
    
    public static function updatePgcUserInfo($arrInput){
        if(!self::_checkParam($arrInput, 'uid', 'type') || intval($arrInput['uid']) <= 0){
            Bingo_Log::warning('updatePgcUserInfo input param is invalid.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'function' => 'updatePgcUserInfo',
            'user_id' => intval($arrInput['uid']),
            'contacts' => $arrInput['contacts'],
            'telephone' => $arrInput['telephone'],
            'type' => $arrInput['type'],
            'org_name' => $arrInput['org_name'],
            'location' => $arrInput['location'],
            'content_trend' => $arrInput['content_trend'],
            'business_license_pic_id' => $arrInput['business_license_pic_id'],
            'id_card_pic_id' => $arrInput['id_card_pic_id'],
            'check_status' => 1,  //变成待审核状态
            'update_time' => time(),
        );
        $arrDlOut = Dl_Pgc_User::execSql($arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('updatePgcUserInfo call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }
    
    public static function modifyPgcUserInfo($arrInput){
        if(!self::_checkParam($arrInput, 'uid') || intval($arrInput['uid']) <= 0){
            Bingo_Log::warning('modifyPgcUserInfo input param is invalid.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $uid = intval($arrInput['uid']);
        $condition = ('user_id = '.$uid);
        unset($arrInput['uid']);
        $modify_fields = '';
        foreach ($arrInput as $key => $value){
            if(!in_array($key, Dl_Pgc_User::$_table_fields)){
                Bingo_Log::warning('modifyPgcUserInfo input param has invalid field, can ignore. key:['.$key.'] value:['.$value.']');
                continue;
            }
            if(is_numeric($value) && strlen($value) <= 20){
                $modify_fields .= ($key.'='.$value);
            }else if(is_string($value)){
                $modify_fields .= ($key.'=\''.$value.'\'');
            }
            $modify_fields .= ', ';
        }
        $modify_fields = rtrim($modify_fields, ', ');
        
        $arrDlInput = array(
            'function' => 'modifyPgcUserInfo',
            'modify_fields' => $modify_fields,
            'condition' => $condition,
        );
        $arrDlOut = Dl_Pgc_User::execSql($arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('modifyPgcUserInfo call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }
  
}
