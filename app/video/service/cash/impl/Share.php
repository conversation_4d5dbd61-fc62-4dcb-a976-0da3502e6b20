<?php
/**
 * <AUTHOR>
 * @date 20180411
 */

define("MODULE", "video");
class Service_Cash_Impl_Share implements  Service_Cash_Impl_Interface{
    const ACT_TYPE = 'share';
    /**
     * [check description]
     * @param  [type] $arrInput  [description]
     * @param  [type] $arrConfig [description]
     * @return [type]            [description]
     */
    public function check($arrInput, $arrConfig = array()){
        $intTid = (int)$arrInput['thread_id'];
        $intUid = (int)$arrInput['user_id'];
        $strUrl = $arrInput['share_url'];
        // $regex = "baidu.com/n/nani/campus/square";
        // $arrConfig['url_regex'] = $regex;
        //通用规则
        if($intTid > 0){ // 分享贴子
            if($arrConfig['check_delete'] || $arrConfig['share_self']){
                // check thread exists, not deleted
                $thread = Util_Post::getThread($intTid);
                if(false === $thread){
                    Bingo_Log::pushNotice('reason', 'thread_deleted_or_getFailed');
                    return Lib_Cash::_errRet(Lib_Cash::ERR_VIDEO_DELETED);
                }
                if($arrConfig['check_delete'] && 1 == $thread['is_deleted']){
                    return Lib_Cash::_errRet(Lib_Cash::ERR_VIDEO_DELETED);
                }
                if($arrConfig['share_self'] && $thread['user_id'] == $intUid){
                    return Lib_Cash::_errRet(Lib_Cash::ERR_OPERATE_SELF);
                }
            }
            if($arrConfig['check_private']){
                // check thread not private
                $arrMaskMap = Util_Post::getMaskInfo(array($intTid));
                if(false === $arrMaskMap || 1 == $arrMaskMap[$intTid]['is_hide']){
                    Bingo_Log::pushNotice('reason', 'thread_private_or_getFailed');
                    return Lib_Cash::_errRet(Lib_Cash::ERR_VIDEO_PRIVATE);
                }
            }
        }else{
            // check share url
            if(!isset($arrConfig['url_regex'])){
                Bingo_Log::warning('no url_regex in config');
                Bingo_Log::pushNotice('reason', 'no_url_regex');
                return Lib_Cash::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }else{
                $pattern = $arrConfig['url_regex'];
                $pattern = "/".str_replace("/", "\/", $pattern)."/";
                Bingo_Log::pushNotice('url_regex', $arrConfig['url_regex']);
                Bingo_Log::pushNotice('share_url', $arrInput['share_url']);
                if(!preg_match($pattern, $arrInput['share_url'], $match)){
                    return Lib_Cash::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
                }
            }
        }
        return Lib_Cash::_succRet();
        
    }
    /**
     * [set description]
     * @param [type] $arrInput  [description]
     * @return [type] $arrConfig [description]
     */
    public function set($arrInput, $arrConfig = array()){
        return Lib_Cash::_succRet();
    }
    /**
     * [param description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public function param($arrInput, $arrConfig = array()){
        if(empty($arrInput['thread_id'])&&empty($arrInput['share_url'])){
            Bingo_Log::warning("thread_id[".$arrInput['thread_id']."] share_url[".$arrInput['share_url']."]");
            return false;
        }
    }
    
}
