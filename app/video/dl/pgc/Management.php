<?php
/**
 * <AUTHOR>
 * 编辑后台 pgc用户管理
 * @date 2017-12-10
 */
class Dl_Pgc_Management {
    
    const DATABASE_NAME = 'forum_movideo';
    const TABLE_NAME = 'pgc_management';
    const CHARSET = 'utf8';
    protected static $_db = null;
    
    public static $_table_fields = array(
        'id',
        'user_id',
        'auth_status',
        'org_type',
        'org_name',
        'content_trend',
        'user_type',
        'update_time',
        'create_time',
        'status',
        'op_uid',
        'op_name',
    );

    /**
     * @param 
     * @return 
     */
    public function __construct(){
        self::$_db = $this->_getDB(self::DATABASE_NAME, self::CHARSET);
    }

    /**
     * @param $dbName
     * @return obj
     */
    private function _getDB($dbName, $charset = 'utf8'){
        if(empty($dbName)){
            Bingo_Log::fatal("db connect fail. no dbName input");
            return null;
        }
        if(self::$_db){
            return self::$_db;
        }
        $objTbMysql = Tieba_Mysql::getDB($dbName);
        if($objTbMysql && $objTbMysql->isConnected()) {
            $objTbMysql->charset($charset);
            self::$_db = $objTbMysql;
            return $objTbMysql;
        } else {
            Bingo_Log::fatal("db connect fail. db=" . $dbName);
            return null;
        }
        return self::$_db;
    }

    /**
     * addPgcUser
     * @param $arrInput
     * @return output
     */
    public function addPgcUser($arrInput){
        $ret = self::$_db->insert(self::TABLE_NAME, $arrInput);
        if($ret === false){
            $lastSql = self::$_db->getLastSQL();
            $lastErr = self::$_db->error();
            Bingo_Log::warning("execute sql error [$lastSql] [$lastErr]");
        }
        return $ret;
    }

    /**
     * getPgcUserCntByConds
     * @param $arrInput
     * @return output
     */
    public function getPgcUserCntByConds($arrInput){
        $conds = null;
        if(isset($arrInput['conds']) && !empty($arrInput['conds'])){
            $conds = $arrInput['conds'];
        }
        $ret = self::$_db->selectCount(self::TABLE_NAME, $conds);
        if($ret === false){
            $lastSql = self::$_db->getLastSQL();
            $lastErr = self::$_db->error();
            Bingo_Log::warning("execute sql error [$lastSql] [$lastErr]");
        }
        return $ret;
    }

    /**
     * getPgcUser
     * @param $arrInput
     * @return output
     */
    public function getPgcUser($arrInput){
        $conds = null;
        $appends = null;
        if(isset($arrInput['conds']) && !empty($arrInput['conds'])){
            $conds = $arrInput['conds'];
        }
        if(isset($arrInput['appends']) && !empty($arrInput['appends'])){
            $appends = $arrInput['appends'];
        }
        $ret = self::$_db->select(self::TABLE_NAME, self::$_table_fields, $conds, null, $appends);
        if($ret === false){
            $lastSql = self::$_db->getLastSQL();
            $lastErr = self::$_db->error();
            Bingo_Log::warning("execute sql error [$lastSql] [$lastErr]");
        }
        return $ret;
    }

    /**
     * updatePgcUserInfo
     * @param $arrInput
     * @return output
     */
    public function updatePgcUserInfo($arrInput){
        if(!isset($arrInput['conds']) || empty($arrInput['conds'])){
            Bingo_Log::warning('updatePgcUserInfo conds empty, input:['.serialize($arrInput).']');
            return false;
        }
        if(!isset($arrInput['data']) || empty($arrInput['data'])){
            Bingo_Log::warning('updatePgcUserInfo update data empty, input:['.serialize($arrInput).']');
            return false;
        }
        $conds = $arrInput['conds'];
        $data = $arrInput['data'];
        $updateData = array();
        foreach($data as $key => $val){
            if(!in_array($key, self::$_table_fields)){
                continue;
            }
            $updateData[$key] = $val;
        }
        if(empty($updateData)){
            Bingo_Log::warning('updatePgcUserInfo update data empty, input:['.serialize($arrInput).']');
            false;
        }
        $ret = self::$_db->update(self::TABLE_NAME, $updateData, $conds, null, $appends = null);
        if($ret === false){
            $lastSql = self::$_db->getLastSQL();
            $lastErr = self::$_db->error();
            Bingo_Log::warning("execute sql error [$lastSql] [$lastErr]");
        }
        return $ret;
    }

    /**
     * getData
     * @param $arrInput
     * @return output
     */
    public function getData($arrInput){
        $fields = !empty($arrInput['fields']) ? $arrInput['fields'] : self::$_table_fields;
        $conds = !empty($arrInput['conds']) ? $arrInput['conds'] : null;
        $appends = !empty($arrInput['appends']) ? $arrInput['appends'] : null;
        $ret = self::$_db->select(self::TABLE_NAME, $fields, $conds, null, $appends);
        if($ret === false){
            $lastSql = self::$_db->getLastSQL();
            $lastErr = self::$_db->error();
            Bingo_Log::warning("execute sql error [$lastSql] [$lastErr]");
        }
        return $ret;
    }
}
