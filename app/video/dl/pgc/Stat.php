<?php
/**
 * <AUTHOR>
 * 编辑后台 pgc用户统计
 * @date 2017-12-10
 */
class Dl_Pgc_Stat {
    
    const DATABASE_NAME = 'forum_movideo';
    const TABLE_NAME = 'pgc_stat';
    const CHARSET = 'utf8';
    protected static $_db = null;
    
    public static $_table_fields = array(
        'id',
        'user_id',
        'thread_id',
        'title',
        'popular_count',
        'play_count',
        'agree_count',
        'comment_count',
        'share_count',
        'date_time',
        'create_time',
    );

    /**
     * @param 
     * @return 
     */
    public function __construct(){
        self::$_db = $this->_getDB(self::DATABASE_NAME, self::CHARSET);
    }

    /**
     * @param $dbName
     * @return obj
     */
    private function _getDB($dbName, $charset = 'utf8'){
        if(empty($dbName)){
            Bingo_Log::fatal("db connect fail. no dbName input");
            return null;
        }
        if(self::$_db){
            return self::$_db;
        }
        $objTbMysql = Tieba_Mysql::getDB($dbName);
        if($objTbMysql && $objTbMysql->isConnected()) {
            $objTbMysql->charset($charset);
            self::$_db = $objTbMysql;
            return $objTbMysql;
        } else {
            Bingo_Log::fatal("db connect fail. db=" . $dbName);
            return null;
        }
        return self::$_db;
    }

    /**
     * getStatCntByConds
     * @param $arrInput
     * @return output
     */
    public function getStatCntByConds($arrInput){
        $conds = null;
        if(isset($arrInput['conds']) && !empty($arrInput['conds'])){
            $conds = $arrInput['conds'];
        }
        $ret = self::$_db->selectCount(self::TABLE_NAME, $conds);
        if($ret === false){
            $lastSql = self::$_db->getLastSQL();
            $lastErr = self::$_db->error();
            Bingo_Log::warning("execute sql error [$lastSql] [$lastErr]");
        }
        return $ret;
    }

    /**
     * getStat
     * @param $arrInput
     * @return output
     */
    public function getStat($arrInput){
        $conds = null;
        $appends = null;
        if(isset($arrInput['conds']) && !empty($arrInput['conds'])){
            $conds = $arrInput['conds'];
        }
        if(isset($arrInput['appends']) && !empty($arrInput['appends'])){
            $appends = $arrInput['appends'];
        }
        $ret = self::$_db->select(self::TABLE_NAME, self::$_table_fields, $conds, null, $appends);
        if($ret === false){
            $lastSql = self::$_db->getLastSQL();
            $lastErr = self::$_db->error();
            Bingo_Log::warning("execute sql error [$lastSql] [$lastErr]");
        }
        return $ret;
    }

}
