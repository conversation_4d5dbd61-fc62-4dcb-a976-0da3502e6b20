<?php
class Dl_Db_Demo extends Dl_Db_BaseModel {

    const DB_TABLE = 'demo';

    const DB_NAME  = 'forum_movideo';

    private static $_arrDB;

    private static $_instance;

    private static $arrField = array(
        'id',
        'position',
        'gid',
        'op',
        'create_time',
        'update_time',
    );

    /**
     * 获取数据库对象
     *
     * @param
     * @return db
     */
    public static function getModel(){
        $db_name  = self::DB_NAME;
        $db_table = self::DB_TABLE;
        $arrKey   = $db_name.$db_table;

        if(isset(self::$_arrDB[$arrKey])){
            return self::$_arrDB[$arrKey];
        }else{
            $class_name = __CLASS__;
            self::$_arrDB[$arrKey] = new $class_name($db_table,$db_name);
            return self::$_arrDB[$arrKey];
        }
    }

    /**
     * 查询
     *
     * @param field[array]
     * @return
     */
    public static function select($arrInput){
        if (!$arrInput['field']) {
            $arrInput['field'] = self::$arrField;
        }
        self::$_instance = self::getModel();
        return self::$_instance->baseSelect($arrInput);
    }

    /**
     * 更新
     *
     * @param
     * @return
     */
    public static function update($arrInput){
        self::$_instance = self::getModel();
        return self::$_instance->baseUpdate($arrInput);
    }

    /**
     * 插入
     *
     * @param
     * @return
     */
    public static function insert($arrInput){
        self::$_instance = self::getModel();
        return self::$_instance->baseInsert($arrInput);
    }

    /**
     * 删除
     *
     * @param
     * @return
     */
    public static function delete($arrInput) {
        self::$_instance = self::getModel();
        return self::$_instance->baseDelete($arrInput);
    }
}
