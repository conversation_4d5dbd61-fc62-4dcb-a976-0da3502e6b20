<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2015/5/5
 * Time: 13:55
 */
class Util_Post {

    /**
     * @param $forum_id
     * @param $forum_name
     * @param $user_id
     * @param $user_name
     * @param $user_ip
     * @param $title
     * @param $content
     * @param int $thread_type
     * @param null $ext_attr
     * @deprecated 不建议使用，因为201705支持多吧发帖，鉴于目前还没调用暂时没改 另外视频模块目前也不需要
     * @return array
     */
	public static function addThread($forum_id, $forum_name, $user_id, $user_name, $user_ip,
	                                 $title, $content, $thread_type = 0, $ext_attr = null)
	{
       
		//发帖
		$arrParams = array(
			'req' => array(
				'product_private_key' => 'special_pro',//豁免UEG策略 TODO 目前看来很多豁免不了
				'forum_id' => $forum_id,
				'forum_name' => $forum_name,
				'thread_id' => 0,
				'user_id' => $user_id,
				'user_name' => $user_name,
				'user_ip' => $user_ip,
				'title' => $title,
				'content' => $content,
				'vcode_free_gate' => 1,
				'thread_type' => $thread_type,
				'ie' => 'utf-8',
				//支持多吧发贴start
				"is_multi_forum" => isset($is_multi_forum) ? intval($is_multi_forum) : 0,
				"v_forum_ids"    => isset($v_forum_ids) ? $v_forum_ids : array(),
				//支持多吧发贴end

            ),
		);
		
		if(!is_null($ext_attr)) {
			$arrParams['req']['ext_attr'] = array(
				$ext_attr,
			);
		}
		
		$arrRet = Tieba_Service::call('post', 'addThread', $arrParams, null, null, 'post', 'php', 'utf-8');
		if($arrRet === false) {
			Bingo_Log::warning('call post service fail');
			return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}
		
		if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
			Bingo_Log::warning('call service post::addThread fail, input=' . serialize($arrParams) . ',ret=' . serialize($arrRet));
			return self::errRet($arrRet['errno']);
		}
		
		$arrThreadInfo = $arrRet['res'];
		
		//锁帖
		$arrParams = array(
			'req' => array(
				'thread_id' => $arrThreadInfo['thread_id'],
			),
		);
		$arrRet = Tieba_Service::call("anti", "antiLockThread", $arrParams, null, null, 'post', 'php', 'utf-8');
		if($arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
			Bingo_Log::warning('service.anti.antiLockThread error. [input='.serialize($arrParams).'][output='.serialize($arrRet).']');
		}
		
		return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrThreadInfo);
	}
    /**
     * [addVideoThread description]
     * @param [type] $arrInput [description]
     * @return
     */
    public static function addVideoThread($arrReq){
        // TODO : param check

        Bingo_Log::warning(sprintf("addVideoThread input[%s]", json_encode($arrReq)));

        $arrInput = array(
            'req' => $arrReq,
        );
        $strService = 'post';
        $strMethod  = 'addThread';
        $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null,null,'post','php','utf-8');
        Bingo_Log::warning(sprintf("call post::addThread. input[%s] output[%s]", json_encode($arrInput), json_encode($arrOutput)));
        $strLog = sprintf("call $strService::$strMethod failed! input[%s] output[%s]", serialize($arrInput), serialize($arrOutput));
        //Bingo_Log::warning($strLog);
        if ($arrOutput == false){
            Bingo_Log::fatal($strLog);
            return false;
        }elseif($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning($strLog);
            return false;
        }
        $arrThreadRes = $arrOutput['res'];

        return $arrThreadRes;
    }
    /**
     * [addVideoThread description]
     * @param [type] $arrInput [description]
     * @return
     */
    public static function delVideoThread($intTid,$intOpUid,$strOpUname,$strCallFrom = ''){
        // TODO : param check
        if('' === $strCallFrom){
            $strCallFrom = Util_Const::DEFAULT_CALL_FROM;
        }
        $arrInput = array(
            'req' => array(
                'op_uid'    => $intOpUid, //操作人ID
                'op_uname'  => $strOpUname, //操作人用户名
                'thread_id' => $intTid, //主题id
                'call_from' => $strCallFrom,
                'op_ip'     => 1 //用户ip
            ),
        );
        $strService = 'post';
        $strMethod  = 'deleteThread';
        $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null,null,'post','php','utf-8');
        $strLog = sprintf("call $strService::$strMethod failed! input[%s] output[%s]", serialize($arrInput), serialize($arrOutput));
        Bingo_Log::warning($strLog);
        if ($arrOutput == false){
            Bingo_Log::fatal($strLog);
            return false;
        }else if($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning($strLog);
            return false;
        }
        return true;
    }
    /**
     * [addVideoThread description]
     * @param [type] $arrInput [description]
     * @return
     */
    public static function recVideoThread($intTid,$intOpUid,$strOpUname,$strCallFrom = ''){
        // TODO : param check
        if('' === $strCallFrom){
            $strCallFrom = Util_Const::DEFAULT_CALL_FROM;
        }
        $arrInput = array(
            'req' => array(
                'op_uid'    => $intOpUid, //操作人ID
                'op_uname'  => $strOpUname, //操作人用户名
                'thread_id' => $intTid, //主题id
                'call_from' => $strCallFrom,
                'op_ip'     => 1 //用户ip
            ),
        );
        $strService = 'post';
        $strMethod  = 'recoverThread';
        $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null,null,'post','php','utf-8');
        $strLog = sprintf("call $strService::$strMethod failed! input[%s] output[%s]", serialize($arrInput), serialize($arrOutput));
        Bingo_Log::warning($strLog);
        if ($arrOutput == false){
            Bingo_Log::fatal($strLog);
            return false;
        }else if($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning($strLog);
            return false;
        }
        return true;
    }
    /**
     * [setThreadKey description]
     * @param [type] $intThreadId [description]
     * @param [type] $key         [description]
     * @param [type] $value       [description]
     * @return
     */
    public static function setThreadKey($intThreadId, $key, $value){
        $arrInput = array(
            "input" => array( // 输入参数
                0 => array(
                    "tid" => $intThreadId,
                    "fields" => array(
                        0 => array(
                            "fkey" => $key,
                            "value" => $value,
                        ),
                    ),
                ),
            ),
        );
        $strService = 'post';
        $strMethod  = 'setKeyInThreadInfo';
        $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null,null,'post','php','utf-8');
        $strLog = sprintf("call $strService::$strMethod  input[%s] output[%s]", serialize($arrInput), serialize($arrOutput));
        Bingo_Log::warning($strLog);
        if ($arrOutput == false){
            Bingo_Log::fatal($strLog);
            return false;
        }elseif($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("fail:".$strLog);
            return false;
        }
        return true;

    }
    /**
     * [getThreadDynamicInfo description]
     * @param  [type] $intTid [description]
     * @return [type]         [description]
     */
    public static function getThreadDynamicInfo($intTid){
        $arrTids = array(
            0 => $intTid,
        );
        $arrOutput = self::mgetThreadDynamicInfo($arrTids);
        if(false === $arrOutput){
            return false;
        }
        return $arrOutput[$intTid];
    }
    /**
     * [mgetThreadDynamicInfo description]
     * @param  [type] $arrTids [description]
     * @return [type]          [description]
     */
    public static function mgetThreadDynamicInfo($arrTids){
        $arrParam = array(
            "thread_ids" => $arrTids,
            "call_from"  => "client_frs",
        );
        $arrRes = Tieba_Service::call('post', 'getThreadDynamicInfo', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning(sprintf("call post mgetThread failed. [input = %s] [output = %s]",serialize($arrParam), serialize($arrRes)));
            return false;
        }
        return $arrRes['output']['thread_info'];
    }


    /**
     * @param $tb_sig
     * @param $thread_id
     * @param $op_uid
     * @param $op_uname
     * @param $op_ip
     * @return array
     */
    public static function deleteThread($thread_id, $op_uid, $op_uname, $op_ip)
    {
        //解锁
        $arrParams = array(
            'req' => array(
                'thread_infos' => array($thread_id),
            ),
        );
        $arrRet = Tieba_Service::call("anti", "antiUnlockThread", $arrParams, null, null, 'post', 'php', 'utf-8');
        if($arrRet === false || !isset($arrRet['errno'])){
            Bingo_Log::warning("service_anti_antiUnlockThread error. [input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        //删帖
        $arrParams = array(
            'req' => array(
                'thread_id' => $thread_id,
                'call_from' => 'mis_back',
                'op_uid' => $op_uid,
                'op_uname' => $op_uname,
                'op_ip' => $op_ip,
                'ie' => 'utf-8',
            ),
        );

        $arrRet = Tieba_Service::call('post', 'deleteThread', $arrParams, null, null, 'post', 'php', 'utf-8');
        if($arrRet === false) {
            Bingo_Log::warning('call post service fail');
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call service post::deleteThread fail, input=' . serialize($arrParams) . ',ret=' . serialize($arrRet));
            return self::errRet($arrRet['errno']);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }
    
    /**
     * 删帖
     */
    public static function deleteThreadInfo($thread_id, $op_uid, $op_uname, $call_from="mis_back")
    {
        //删帖
        $arrParams = array(
            'req' => array(
                'thread_id' => $thread_id,
                'call_from' => $call_from,
                'op_uid' => $op_uid,
                'op_uname' => $op_uname,
                'op_ip' => 1,
                'ie' => 'utf-8',
            ),
        );

        $arrRet = Tieba_Service::call('post', 'deleteThread', $arrParams, null, null, 'post', 'php', 'utf-8');
        if($arrRet === false) {
            Bingo_Log::warning('call post service fail');
            return false;
        }

        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call service post::deleteThread fail, input=' . serialize($arrParams) . ',ret=' . serialize($arrRet));
            return false;
        }

        return true;
    }
    /**
     * @param $forum_id
     * @param $forum_name
     * @param $thread_id
     * @param $user_id
     * @param $user_name
     * @param $user_ip
     * @param $content
     * @param null $ext_attr
     * @deprecated 不建议使用，因为201705支持多吧发帖，鉴于目前还没调用暂时没改 另外视频模块目前也不需要
     * @return array
     */
    public static function addPost($forum_id, $forum_name, $thread_id, $user_id, $user_name, $user_ip,
                                   $content, $ext_attr = null)
    {
        $arrParams = array(
            'req' => array(
                'product_private_key'=>"special_pro",//豁免ueg一切策略 TODO 目前看来很多豁免不了
                "user_id" => $user_id,
                "user_ip" => $user_ip,
                "user_name" => $user_name,
                "forum_id" => $forum_id,
                "forum_name" => $forum_name,
                'title' => '',
                'content' => $content,
                'thread_id' => $thread_id,
                'create_time' => time(),
                'vcode_free_gate' => 1,
                'ie' => 'utf-8',
            ),
        );

        if(!is_null($ext_attr)) {
            $arrParams['req']['ext_attr'] = array(
                $ext_attr,
            );
        }

        $arrRet = Tieba_Service::call('post', 'addPost', $arrParams, null, null, 'post', 'php', 'utf-8');
        if($arrRet === false) {
            Bingo_Log::warning('call post service fail');
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call service post::addThread fail, input=' . serialize($arrParams) . ',ret=' . serialize($arrRet));
            return self::errRet($arrRet['errno']);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['res']);
    }

    /**
     * @param $forum_id
     * @param $forum_name
     * @param $thread_id
     * @param $user_id
     * @param $user_name
     * @param $user_ip
     * @return array
     */
    public static function addTop($forum_id, $forum_name, $thread_id, $user_id, $user_name, $user_ip)
    {
        $arrInput = array(
            'req' => array(
                'forum_id' => $forum_id,
                'forum_name' => $forum_name,
                'thread_id' => $thread_id,
                'user_id' => $user_id,
                'user_name' => $user_name,
                'user_ip' => $user_ip,
            )
        );

        $arrRet = Tieba_Service::call('post','addTop',$arrInput, null, null, 'post', 'php', 'utf-8');
        if($arrRet === false) {
            Bingo_Log::warning('call post service fail');
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call service post::addTop fail, input=' . serialize($arrInput) . ',ret=' . serialize($arrRet));
            return self::errRet($arrRet['errno']);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $forum_id
     * @param $forum_name
     * @param $thread_id
     * @param $user_id
     * @param $user_name
     * @param $user_ip
     * @return array
     */
    public static function addGood($forum_id, $forum_name, $thread_id, $user_id, $user_name, $user_ip)
    {
        $arrInput = array(
            'req' => array(
                'forum_id' => $forum_id,
                'forum_name' => $forum_name,
                'thread_id' => $thread_id,
                'user_id' => $user_id,
                'user_name' => $user_name,
                'user_ip' => $user_ip,
            )
        );

        $arrRet = Tieba_Service::call('post','addGood',$arrInput, null, null, 'post', 'php', 'utf-8');
        if($arrRet === false) {
            Bingo_Log::warning('call post service fail');
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call service post::addGood fail, input=' . serialize($arrInput) . ',ret=' . serialize($arrRet));
            return self::errRet($arrRet['errno']);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $thread_id
     * @return array
     */
    public static function getFirstPostInfo($thread_id) {
        $arrParams = array(
            'thread_id' => $thread_id,
            'has_ext' => 1,
            'offset' => 0,
            'res_num' => 1,
            'see_author' => 1,
            'has_comment' => 0,
            'has_mask' => 0,
            'need_set_pv' => 0,
        );
        $arrRet = Tieba_Service::call('post', 'getPostsByThreadId', $arrParams, null, null, 'post', 'php', 'utf-8');
        if(false === $arrRet) {
            Bingo_Log::warning('call service post::getPostsByThreadId, input='.serialize($arrParams));
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        if(!isset($arrRet['output']['output'][0]['post_infos'][0])) {
            Bingo_Log::warning("no this thread, thread_id=$thread_id");
            return self::errRet(Tieba_Errcode::ERR_NO_RECORD);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['output']['output'][0]['post_infos'][0]);
    }

    /**
     * @param $intType
     * @param $intInterval
     * @param array $arrContent
     * @return array
     */
    public static function sendGlobalMsg($intType, $intInterval, array $arrContent)
    {
        $update_time = time();
        $end_time = $update_time + $intInterval;

        $arrContent['msg_type'] = $intType;

        $arrParams = array(
            'type' => 1,
            'field' => array(
                'msg_type' => 1,
                'update_time' => $update_time,
                'end_time' => $end_time,
                'msg_status' => 1,
                'msg_body' => $arrContent,
            ),
        );

        $arrRet = Tieba_Service::call('gmessage', 'sendGlobalMsg', $arrParams, null, null, 'post', 'php', 'utf-8');
        if (false == $arrRet) {
            Bingo_Log::warning("call service gmessage::sendGlobalMsg fail, input=[".serialize($arrParams)."],ret=".serialize($arrRet));
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning("call service gmessage::sendGlobalMsg fail, input=[".serialize($arrParams)."],ret=".serialize($arrRet));
            return self::errRet($arrRet['errno']);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }
    /**
     * [mgetThread description]
     * @param  [type] $arrTids [description]
     * @return [type]          [description]
     */
    public static function mgetThread($arrTids){
        $threadInput = array(
            'thread_ids'        => $arrTids,
            'need_abstract'     => 0,
            'forum_id'          => 1,
            'need_photo_pic'    => 0,
            'need_user_data'    => 0,
            'icon_size'         => 0,
            'need_forum_name'   => 0,
            'call_from'         => 'client_frs',
        );
        $threadOutput = Tieba_Service::call('post', 'mgetThread', $threadInput, null, null, 'post', 'php', 'utf-8' );
        if ($threadOutput === false ){
            Bingo_Log::fatal("call post::mgetThread error. [input]:" . serialize($threadInput) . " [output]:" . serialize($threadOutput));
            return array();
        }else if($threadOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call post::mgetThread error. [input]:" . serialize($threadInput) . " [output]:" . serialize($threadOutput));
            return array();
        }

        return $threadOutput['output']['thread_list'];
    }
    /**
     * [getThread description]
     * @param  [type] $intTid [description]
     * @return [type]         [description]
     */
    public static function getThread($intTid){
        $arrThreads = self::mgetThread(array($intTid));

        if(!isset($arrThreads[$intTid])){
            return false;
        }
        return $arrThreads[$intTid];
    }
    /**
     * [getPostInfo description]
     * @param  [type] $arrPids [description]
     * @return [type]          [description]
     */
    public static function getPostInfo($arrPids){
        $arrInput = array(
            "post_ids" => $arrPids,
            "has_ext" => array( //扩展属性
                "fields" => array( //各个扩展key
                    0 => "video_info", //各个扩展key
                ),
            ),
        );
        $arrRes   = Tieba_Service::call('post', 'getPostInfo', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']) {
            Bingo_Log::warning("video post getPostInfo error. [arrVideoInfo]".serialize($arrInput)."]. [output]".serialize($arrRes));
            return false;
        }
        return $arrRes['output'];

    }
    /**
     * [getPost description]
     * @param  [type] $intPid [description]
     * @return [type]         [description]
     */
    public static function getPost($intPid){
        $arrPosts = self::getPostInfo(array($intPid));
        if(!isset($arrPosts[0])){
            return false;
        }
        return $arrPosts[0];
    }
    /**
     * [recPost description]
     * @param  [type] $intPostId   [description]
     * @param  [type] $intOpUid    [description]
     * @param  [type] $strOpUname  [description]
     * @param  [type] $intOpUip    [description]
     * @param  [type] $strCallFrom [description]
     * @return [type]              [description]
     */
    public static function recPost($intPostId, $intOpUid, $strOpUname, $intOpUip, $strCallFrom){
        $arrInput = array(
            'req' => array(
                'op_uid'     => $intOpUid,
                'op_uname'   => $strOpUname,
                'post_id'    => $intPostId,
                'call_from'  => $strCallFrom, // 找 @gongwen 申请
                'op_ip'      => $intOpUip,
                'transform'  => 1,
            ),
        );
        $strService = 'post';
        $strMethod  = 'recoverPost';
        $arrOutput   = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
        $strLog = sprintf("call $strService::$strMethod failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput));
        if(false === $arrOutput){
            Bingo_Log::fatal($strLog);
            return false;
        }else if (Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            Bingo_Log::warning($strLog);
            return false;
        }
        return true;
    }
    /**
     * [delPost description]
     * @param  [type] $intPostId   [description]
     * @param  [type] $intOpUid    [description]
     * @param  [type] $strOpUname  [description]
     * @param  [type] $intOpUid    [description]
     * @param  [type] $strCallFrom [description]
     * @return [type]              [description]
     */
    public static function delPost($intPostId, $intOpUid, $strOpUname, $intOpUip, $strCallFrom){
        $arrInput = array(
            'req' => array(
                'op_uid'     => $intOpUid,
                'op_uname'   => $strOpUname,
                'post_id'    => $intPostId,
                'call_from'  => $strCallFrom, // 找 @gongwen 申请
                'op_ip'      => $intOpUip,
            ),
        );
        $strService = 'post';
        $strMethod  = 'deletePost';
        Bingo_Log::warning('del_post'. serialize($arrInput));
        $arrOutput   = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
        $strLog = sprintf("call $strService::$strMethod failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput));
        if(false === $arrOutput){
            Bingo_Log::fatal($strLog);
            return false;
        }else if (Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            Bingo_Log::warning($strLog);
            return false;
        }
        return true;
    }

    /**
     * @brief 删帖
     * @param $intThreadId
     * @param $intPostId
     * @param $intOpUid
     * @param $strOpUname
     * @param $intUserId
     * @param $strUserName
     * @param $intOpUip
     * @param $strCallFrom
     * @return bool
     */
    public static function newDelPost($intThreadId, $intPostId, $intOpUid, $strOpUname, $intUserId, $strUserName, $intOpUip, $strCallFrom) {
        $arrParam = array(
            'command_no'=> 12,
            'op_uid'    => $intOpUid,
            'op_uname'  => $strOpUname,
            'user_name' => $strUserName, // 不过白名单随意填写
            'user_id'   => $intUserId, // 如过白名单需要填写帖子的用户id和用户名
            'thread_id' => $intThreadId,
            'post_id'   => $intPostId,
            'call_from' => $strCallFrom,
            'op_ip'        => $intOpUip,
            'monitor_type' => '',
        );

        $strOpUip = Bingo_Http_Ip::newlong2ip($intOpUip);
        if(Bingo_Http_Ip::getIpVersion($strOpUip) == Bingo_Http_Ip::IP_V6){
            $arrParam['op_ip6'] = $strOpUip;
        }
        $arrReq = array(
            'req' => $arrParam,
        );
        $arrOutput = Tieba_Service::call('unihandle','handleCmd', $arrReq);
        $strLog = sprintf("call unihandle::handleCmd failed input[%s] output[%s]", serialize($arrReq), serialize($arrOutput));
        if(false === $arrOutput){
            Bingo_Log::fatal($strLog);
            return false;
        }else if (Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            Bingo_Log::warning($strLog);
            return false;
        }
        return true;
    }

    /**
     * [getSecondFloor description]
     * @param  [type] $intTid [description]
     * @return [type]         [description]
     */
    public static function getSecondFloorContent($intTid){
        $arrInput = array(
            "thread_id" => $intTid, //帖子id
            "offset" => 0,
            "res_num" => 10,
            "see_author" => 1,
            "has_comment" => 0,
            "has_mask" => 1,
            "has_ext" => 1,
            "need_set_pv" => 0,
            "structured_content" => 0,
        );

        $strService = "post";
        $strMethod = "getPostsByThreadId";
        $arrOutput   = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($arrOutput === false ){
            Bingo_Log::fatal("call $strService::$strMethod error. [input]:" . serialize($arrInput) . " [output]:" . serialize($arrOutput));
            return '';
        }else if($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call $strService::$strMethod error. [input]:" . serialize($arrInput) . " [output]:" . serialize($arrOutput));
            return '';
        }
        return $arrOutput['output']['output'][0]['post_infos'][1]['content'];
    }
    /**
     * [getMaskInfo description]
     * @param  [type]  $arrTids [description]
     * @param  integer $intFid  [description]
     * @return [type]           [description]
     */
    public static function getMaskInfo($arrTids, $intFid = 0){
         $arrInput = array(
            "thread_ids" => $arrTids, //帖子id
            "forum_id" => $intFid,
        );
        $strService = "post";
        $strMethod = "getThreadMaskInfo";
        $arrOutput   = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($arrOutput === false ){
            Bingo_Log::fatal("call $strService::$strMethod error. [input]:" . serialize($arrInput) . " [output]:" . serialize($arrOutput));
            return array();
        }else if($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call $strService::$strMethod error. [input]:" . serialize($arrInput) . " [output]:" . serialize($arrOutput));
            return array();
        }
        $arrMap = array();
        foreach($arrOutput['output']['thread_info'] as $k => $v){
            $intTid = $v['thread_id'];
            $arrMap[$intTid] = $v;
        }
        return $arrMap;
    }
    /** [setThreadPrivacy description]
     * 暂时没有调用
     * @param [type] $intTid  [description]
     * @param [type] $intPid  [description]
     * @param [type] $intUid  [description]
     * @param [type] $intHide [description]
     * @return 
     */
    public static function setThreadPrivacy($intTid,$intPid,$intUid,$intHide){
        $arrInput['input'] = array(
            'user_id'   => $intUid,
            'forum_id'  => 0,
            'thread_id' => $intTid,
            'post_id'   => $intPid,
            'is_hide'   => $intHide,
        );
        $ret = Tieba_Service::call('post', 'setUserPrivacy', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false === $ret || $ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal('call post setUserPrivacy failed, input is ' .json_encode($arrInput). ' output is'.json_encode($ret));
            return $ret;
        }
        return $ret;
    }
    /**
     * [arrRet description]
     * @param  [type] $errno [description]
     * @param  [type] $data  [description]
     * @return [type]        [description]
     */
    public static function errRet($errno, $data = null){
        return array(
            'errno'     => $errno,
            'errmsg'    => Tieba_Error::getErrmsg($errno),
            'data'      => $data,
        );
    }
}
