<?php
/**
 * <AUTHOR>
 * @date 2018-07-01
 * @desc 获取成员基本信息
 */

class getMemberInfoAction extends Util_Base {

    private $arrOutput = array();

    /**
     * @param
     * @return [type] [description]
     */
    public function _execute(){
        if(!$this->_checkPost()){
            Bingo_Log::warning(sprintf('must post!'));
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return true;
        }

        //todo post only
        $arrGetParams = Bingo_Http_Request::getGetAll();
        $arrPostParams = Bingo_Http_Request::getPostAll();
        $arrParams = array_merge($arrGetParams, $arrPostParams);

        //params check
        $o = Util_Validator::instance(array(
            'member_id' => 'int|optional:-1|gt:0',
            'nani_id' => 'int|optional:-1|gt:0',
        ));
        if (!$o->validate($arrParams, $arrParams)) {
            Bingo_Log::warning(sprintf('params error, input=[%s], error=[%s]', serialize($arrParams), $o->getLastError()));
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return true;
        }

        $intUserId = $this->_arrUserInfo['user_id'];

        //公会状态检查
        $arrInput = array(
            'user_id' => $intUserId,
        );
        $arrRet = Tieba_Service::call('video', 'getUnionInfoByUserId', $arrInput, null, null, 'post', 'php', 'utf-8');
        if(false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
            Bingo_Log::warning(sprintf('service call failed! service=[%s], input=[%s], output=[%s]', 'video::getUnionInfoByUid', serialize($arrInput), serialize($arrRet)));
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return true;
        }
        $arrData = $arrRet['data'];
        //入驻的
        if(empty($arrData) || !in_array($arrData['status'], array(Lib_Define_Union::UNION_AUDIT_STATUS_ENTER))){
            Bingo_Log::warning(sprintf('union is not pass, user_id=[%s]', $intUserId));
            $this->_jsonRet(Lib_Define_Union::ERR_UNION_IS_NOT_AUDIT_PASS, '公会还没有入驻，请耐心等待', $this->arrOutput);
            return true;
        }
        $intUnionId = $arrData['union_id'];

        //nani_id => user_id
        $arrInput = array(
            'nani_id' => $arrParams['nani_id'],
        );
        $arrRet = Tieba_Service::call('video', 'getUserDataByNaniId', $arrInput, null, null, 'post', 'php', 'utf-8');
        if(false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
            Bingo_Log::warning(sprintf('service call failed! service=[%s], input=[%s], output=[%s]', 'video::getUserDataByNaniId', serialize($arrInput), serialize($arrRet)));
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return true;
        }
        $arrData = $arrRet['data']['0'];
        if(empty($arrData)){
            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', array());
            return true;
        }
        $intMemberUserId = $arrData['user_id'];

        //成员信息
        $arrInput = array(
            'union_id' => $intUnionId,
            'member_id' => $arrParams['member_id'],
            'user_id' => $intMemberUserId,
        );
        $arrRet = Tieba_Service::call('video', 'getUnionMemberByUserId', $arrInput, null, null, 'post', 'php', 'utf-8');
        if(false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
            Bingo_Log::warning(sprintf('service call failed! service=[%s], input=[%s], output=[%s]', 'video::getUnionMemberByUserId', serialize($arrInput), serialize($arrRet)));
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return true;
        }
        $arrData = $arrRet['data']['0'];
        if(empty($arrData)){
            Bingo_Log::warning(sprintf('is not union member, sheet_id=[%s], union_id=[%s], nani_id=[%s]', $arrParams['sheet_id'], $intUnionId, $arrParams['nani_id']));
            $this->_jsonRet(Lib_Define_Union::ERR_UNION_MEMBER_IS_NOT_EXIST, 'TA还不是你的成员', $this->arrOutput);
            return true;
        }

        //昵称
        $arrInput = array(
            'user_id' => $intMemberUserId,
        );
        $arrRet = Tieba_Service::call('user', 'getUserData', $arrInput, null, null, 'post', 'php', 'utf-8');
        if(false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
            Bingo_Log::warning(sprintf('service call failed! service=[%s], input=[%s], output=[%s]', 'video::getUserData', serialize($arrInput), serialize($arrRet)));
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return true;
        }
        $arrUser = $arrRet['user_info']['0'];
        if(empty($arrUser)){
            Bingo_Log::warning(sprintf('is not union member, sheet_id=[%s], union_id=[%s], nani_id=[%s]', $arrParams['sheet_id'], $intUnionId, $arrParams['nani_id']));
            $this->_jsonRet(Lib_Define_Union::ERR_UNION_MEMBER_IS_NOT_EXIST, 'TA还不是你的成员', $this->arrOutput);
            return true;
        }

        $arrOutput = array(
            'member_id' => '' . $arrData['member_id'],
            'nani_id' => '' . $arrData['nani_id'],
            'nick_name' => !empty($arrUser['nani_nickname']) ? $arrUser['nani_nickname'] : (!empty($arrUser['user_nickname']) ? $arrUser['user_nickname'] : $arrUser['user_name']),
            'category_name' => Lib_Define_Union::$arrCategory[$arrData['category_id']],
            'category_id' => '' . $arrData['category_id'],
            'remark' => $arrData['remark'],
        );
        //通过的成员 获取作品信息
        if(in_array($arrData['status'], array(Lib_Define_Union::UNION_MEMBER_STATUS_AUDIT_PASS, Lib_Define_Union::UNION_MEMBER_STATUS_ENTER))){
            $arrOutput['video_num'] = '' . $arrData['video_num'];
            $arrOutput['good_num'] = '' . $arrData['good_num'];
            $arrOutput['pre_grade'] = '' . $arrData['pre_grade'];
        }

        $this->arrOutput = $arrOutput;
        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, '', $this->arrOutput);
        return true;
    }

}
