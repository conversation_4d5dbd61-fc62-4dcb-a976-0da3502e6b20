<?php
/**
 * 设置用户历史发视频数据
 */
ini_set ( "memory_limit", "-1" );
define('MODULE_NAME', 'video');
date_default_timezone_set ( "Asia/Chongqing" );

define ( 'APP_NAME', 'video' );
define ( 'SCRIPT_NAME', 'setUserAttr' );
define ( 'ROOT_PATH', dirname ( __FILE__ ) . '/../../..' );
define ( 'SCRIPT_ROOT_PATH', ROOT_PATH . '/app/' . APP_NAME . '/script' );
define ( 'SCRIPT_LOG_PATH', ROOT_PATH . '/log/app/' . APP_NAME );
define ( 'SCRIPT_CONF_PATH', ROOT_PATH . '/conf/app/' . APP_NAME );

/**
 * 自动加载
 * @param unknown $strClassName
 * @return null
 */
function __autoload($strClassName) {
	$strNewClassName = str_replace ( '_', '/', $strClassName . ".php" );
	$arrClass = explode ( '/', $strNewClassName );
	$intPathLen = count ( $arrClass );
	$strLastName = $arrClass [$intPathLen - 1];
	$strTmp = strtolower ( $strNewClassName );
	$intPreLen = strlen ( $strTmp ) - strlen ( $strLastName );
	$strNewClassName = substr ( $strTmp, 0, $intPreLen ) . $strLastName;
	$strClassPath = ROOT_PATH . '/app/' . APP_NAME . '/' . $strNewClassName;
	require_once $strClassPath;
}

spl_autoload_register ( '__autoload' );




$arrOutput = Lib_Edit::getInsertSearchTypeWithBiases(8, 20);
print_r($arrOutput);

