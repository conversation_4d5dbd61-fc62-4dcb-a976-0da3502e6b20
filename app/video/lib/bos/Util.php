<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2016/10/10
 * Time: 16:03
 */

include __DIR__.'/BaiduBce.phar';

use BaiduBce\Services\Bos\BosClient;
use BaiduBce\Services\Bos\BosOptions;
use BaiduBce\Services\Bos\CannedAcl;
use BaiduBce\Auth\SignOptions;

class Lib_Bos_Util {

    const DEFAULT_PRODUCT = 'tieba-video';

    const REGION_INNER = "bpc-internal";
    const REGION_OUTER = "bpc";

//    const SRC_URL = "bos.nj.bpc.baidu.com";
    const SRC_URL = "su.bcebos.com";
    const TGT_URL = "tb-video.bdstatic.com";

    const ACL_PRIVATE = CannedAcl::ACL_PRIVATE;
    const ACL_PUBLIC_READ = CannedAcl::ACL_PUBLIC_READ;

    protected static $_bos_client = null;
    protected static $_config = array(
        'credentials' => array(
//            'ak' => 'be738f305dca451e9c678e9c91a61f83',   // offline
//            'sk' => '9215b2438bdb497e8b536ac034042383',
//            'ak' => 'de94045c2e42438fad71ab8df47a6727',  // online bpc
//            'sk' => '33fc91c0a87d430f82d831e3eb103c4c',
            'ak' => 'a99d4b023f6942f291c44b3d0b53e0f0',  // online bce
            'sk' => 'cea2d43ed2eb4e6fa7e0955d61adc7f2',
        ),

//        'endpoint' => '10.105.97.15:80',  // offline
//        'endpoint' => 'http://bos.nj.bpc-internal.baidu.com',  // online bpc
        'endpoint' => 'http://su.bcebos.com',  // online bce
    );

    /**
     * @brief : Check _bos_client null or not.
     * @return bool
     */
    private static function _init()
    {
        if(!self::$_bos_client) {
            self::$_bos_client = new BosClient(self::$_config);
        }

        if (!self::$_bos_client){
            return false;
        }

        return true;
    }

    /**
     * @param $strAcl
     * @param $strProduct
     * @return bool
     * @deprecated 代码中创建bucket将被禁止
     */
    public static function createBucket($strAcl = self::ACL_PUBLIC_READ, $strProduct = '')
    {
        if(!self::_init()){
            Bingo_Log::warning("Init fail.");
            return false;
        }

        try{
            empty($strProduct) && $strProduct= self::DEFAULT_PRODUCT;

            if(!self::$_bos_client->doesBucketExist($strProduct)) {
                self::$_bos_client->createBucket($strProduct);
            }

            self::$_bos_client->setBucketCannedAcl($strProduct, $strAcl);
            return true;
        }catch(Exception $e){
            Bingo_Log::warning("Exception: ". $e->getMessage());
            return false;
        }

    }

    /**
     * @param $strFileName
     * @param $strData
     * @param $arrMetaData
     * @param $strProduct
     * @return bool
     */
    public static function saveObjectFromString($strFileName, $strData, &$arrMetaData = null, $strProduct = '')
    {
        if(!self::_init()){
            Bingo_Log::warning("Init fail.");
            return false;
        }

        try{
            empty($strProduct) && $strProduct= self::DEFAULT_PRODUCT;

            Bingo_Timer::start('BOS_putObjectFromString');
            $objResponse = self::$_bos_client->putObjectFromString($strProduct, $strFileName, $strData);
            Bingo_Timer::end('BOS_putObjectFromString');

            $arrMetaData = array(
                'content_type' => $objResponse->metadata['contentType'],
                'content_length' => $objResponse->metadata['contentLength'],
                'content_md5' => $objResponse->metadata['contentMd5'],
            );
            return true;
        }catch (Exception $e){
            Bingo_Log::warning("Exception: ". $e->getMessage());
            return false;
        }
    }


    /**
     * @param $objName
     * @param $fileName
     * @param $arrMetaData
     * @param $strProduct
     * @return bool
     */
    public static function saveObjectFromFile($objName, $fileName, &$arrMetaData = null, $strProduct = '')
    {
        if(!self::_init()){
            Bingo_Log::warning("Init fail.");
            return false;
        }

        try{
            empty($strProduct) && $strProduct= self::DEFAULT_PRODUCT;

            Bingo_Timer::start('BOS_putObjectFromFile');
            $objResponse = self::$_bos_client->putObjectFromFile($strProduct, $objName, $fileName);
            Bingo_Timer::end('BOS_putObjectFromString');

            $arrMetaData = array(
                'content_type' => $objResponse->metadata['contentType'],
                'content_length' => $objResponse->metadata['contentLength'],
                'content_md5' => $objResponse->metadata['contentMd5'],
            );
            return true;
        }catch (Exception $e){
            Bingo_Log::warning("Exception: ". $e->getMessage());
            return false;
        }
    }

    /**
     * @param $strFileName
     * @param $intExpireTime
     * @param $strUrl
     * @param $bolInnerUrl
     * @param $strProduct
     * @return bool
     */
    public static function generateUrl($strFileName, $intExpireTime, &$strUrl, $bolInnerUrl = false, $strProduct = '')
    {
        if (empty($strFileName)) {
            $strUrl = '';
            return true;
        }

        if(!self::_init()){
            Bingo_Log::warning("Init fail.");
            return false;
        }

        try{
            empty($strProduct) && $strProduct= self::DEFAULT_PRODUCT;

            $arrOptions = array();
//            if(!$bolInnerUrl) {
//                $arrOptions[BosOptions::CONFIG]['endpoint']
//                    = str_replace(self::REGION_INNER, self::REGION_OUTER, self::$_config['endpoint']);
//            }
            if($intExpireTime > 0) {
                $arrOptions[BosOptions::SIGN_OPTIONS][SignOptions::EXPIRATION_IN_SECONDS] = intval($intExpireTime);
            }
            if($intExpireTime == -1) {
                $arrOptions[BosOptions::SIGN_OPTIONS][SignOptions::EXPIRATION_IN_SECONDS] = -1;
            }
            Bingo_Timer::start('BOS_generatePreSignedUrl');
            $strUrl = self::$_bos_client->generatePreSignedUrl($strProduct, $strFileName, $arrOptions);
            //add by dongliang04
            if(!$bolInnerUrl){
                $strUrl = str_replace(self::SRC_URL, self::TGT_URL, $strUrl);
            }
            Bingo_Timer::end('BOS_generatePreSignedUrl');
            return true;
        }catch(Exception $e){
            Bingo_Log::warning("Exception: ". $e->getMessage());
            return false;
        }
    }

    /**
     * @param $strFileName
     * @param $strData
     * @param $strProduct
     * @return bool
     */
    public static function getObjectAsString($strFileName, &$strData, $strProduct = '')
    {
        if(!self::_init()){
            Bingo_Log::warning("Init fail.");
            return false;
        }

        try{
            empty($strProduct) && $strProduct= self::DEFAULT_PRODUCT;

            Bingo_Timer::start('BOS_getObjectAsString');
            $strData = self::$_bos_client->getObjectAsString($strProduct, $strFileName);
            Bingo_Timer::end('BOS_getObjectAsString');

            return true;
        }catch(Exception $e){
            Bingo_Log::warning("Exception: ". $e->getMessage());
            return false;
        }
    }

    /**
     * @param $strFileName
     * @param $hOStream
     * @param $arrMetaData
     * @param $strProduct
     * @return bool
     */
    public static function getObjectToStream($strFileName, $hOStream, &$arrMetaData = null, $strProduct = '')
    {
        if(!self::_init()){
            Bingo_Log::warning("Init fail.");
            return false;
        }

        try{
            empty($strProduct) && $strProduct= self::DEFAULT_PRODUCT;

            Bingo_Timer::start('BOS_getObject');
            $objResponse = self::$_bos_client->getObject($strProduct, $strFileName, $hOStream);
            Bingo_Timer::end('BOS_getObject');

            $arrMetaData = array(
                'content_type' => $objResponse->metadata['contentType'],
                'content_length' => $objResponse->metadata['contentLength'],
                'content_md5' => $objResponse->metadata['contentMd5'],
            );
            return true;
        }catch (Exception $e){
            Bingo_Log::warning("Exception: ". $e->getMessage());
            return false;
        }
    }

    /**
     * @param $strFileName
     * @param $strProduct
     * @return bool
     * @deprecated please use delMoVideo
     */
    public static function delObject($strFileName, $strProduct = '')
    {
        if(!self::_init()){
            Bingo_Log::warning("Init fail.");
            return false;
        }

        try{
            empty($strProduct) && $strProduct= self::DEFAULT_PRODUCT;

            Bingo_Timer::start('BOS_deleteObject');
            self::$_bos_client->deleteObject($strProduct, $strFileName);
            Bingo_Timer::end('BOS_deleteObject');

            return true;
        }catch(Exception $e){
            Bingo_Log::warning("Exception: ". $e->getMessage());
            return false;
        }
    }

    /**
     * @param $strFromFileName
     * @param $strToFileName
     * @param $strProduct
     * @return bool
     */
    public static function copyObject($strFromFileName, $strToFileName, $strProduct = '')
    {
        if(!self::_init()){
            Bingo_Log::warning("Init fail.");
            return false;
        }

        try{
            empty($strProduct) && $strProduct= self::DEFAULT_PRODUCT;

            Bingo_Timer::start('BOS_copyObject');
            self::$_bos_client->copyObject($strProduct, $strFromFileName, $strProduct, $strToFileName);
            Bingo_Timer::end('BOS_copyObject');

            return true;
        }catch(Exception $e){
            Bingo_Log::warning("Exception: ". $e->getMessage());
            return false;
        }
    }

}
