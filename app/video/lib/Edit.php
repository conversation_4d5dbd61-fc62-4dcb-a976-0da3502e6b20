<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Edit.php
 * <AUTHOR>
 * @date 2016/11/13 11:21:01
 * @brief 视频编辑库相关基础接口
 *  
 **/
class Lib_Edit {
    const VIDEO_EDIT_TABLE_PREFIX = 'video_edit_';

    private static $_arrThreadFields = array(
        'thread_id',
        'forum_id',
        'user_id',
        'post_id',
        'title',
        'create_time',
    );
    private static $_arrVideoFields = array(
        'thumbnail_width',
        'thumbnail_height',
        'video_md5',
        'video_url',
        'video_duration',
        'video_type',
        'video_length',
        'video_width',
        'video_height',
        'thumbnail_url',
        //'video_from',
    );

    const REDIS_KEY_VIDEO_WATERMARK_TYPE = 'video_watermark_type_of_thread_id';

    /**
     * @brief 生成openapi的access_token
     * @param
     * @return string
     */
    public static function getOpenapiToken()
    {
        $it = new Service_Video_InnerToken(11);

        $appid = '11637138';
        $uid = '0';
        $sk = 'qIbDM3ZUSmryHElMp2B8EB0ATFdYnnFn';

        $token = $it->generateToken($appid, $uid, $sk);

        return $token;
    }

    /**
     * 编辑完成后，将tag 写到帖子属性里面 
     * @param [type] $arrInput [description]
     * @return
     */
    public static function setVideoTags($arrInput){
        $intTid = (int)$arrInput['thread_id'];
        $strTags = $arrInput['tag'];
        $arrTags = explode(',', $strTags);
        $strFirCate = $arrInput['fir_category'];
        $strSecCate = $arrInput['sec_category'];
        // $arrTags[] = $strFirCate;
        // $arrTags[] = $strSecCate;

        $bolSetKey = true;
        if(!empty($arrTags)){
            $bolSetKey = Util_Post::setThreadKey($intTid, 'video_tag_info', $arrTags);
        }
        return $bolSetKey;
    }

    /**
     * [getTableName description]
     * @param  [type] $intTime [description]
     * @return [type]          [description]
     */
    public static function getTableName($intTime){
        $tempDate = date('Ym', $intTime);
        $strTableName = Lib_Define_Edit::VIDEO_EDIT_TABLE_PREFIX. $tempDate;
        return $strTableName;
    }

    /**
     * @param
     * @return array
     */
    public static function getNeedCheckTableName() {
        $arrCheckTable = array();
        $strNowYearMonth = date("Ym");
        $tempDate = $strNowYearMonth;
        $intNum = 0;
        while ($intNum <= 2) {  //最近三个月
            // $tempDate = date('Ym', strtotime("-$intNum Month"));
            // $arrCheckTable[] = Lib_Define_Edit::VIDEO_EDIT_TABLE_PREFIX. $tempDate;
            $intTime = strtotime("-$intNum Month");
            $arrCheckTable[] = Lib_Edit::getTableName($intTime);
            $intNum  ++;
        }
        return $arrCheckTable;
    }

    /**
     * [checkNeedEdit description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function checkNeedEdit($arrInput){
        if(Util_Const::$_arrNoNeedToEditTypes[$arrInput['video_type']]){
            return false;
        }
        if (Util_Const::$arrNaniSourceVTypes[$arrInput['video_type']]) {
            return false;
        }
        return true;
    }

    /**
     * [editCheckInput description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function checkEditInput($arrInput){
        if(!isset($arrInput['thread_id'])){
            Bingo_Log::warning('no tid');
            return false;
        }
        $intTid = (int)$arrInput['thread_id'];
        
        // fix video_info: begin
        if(!isset($arrInput['video_info'])){
            if(isset($arrInput['ext_attr'])){
                $arrInput['video_info'] = Lib_Audit::getVideoInfo($arrInput);
                $arrInput['video_play'] = Lib_Audit::getVideoInfo($arrInput, 'video_play');
                unset($arrInput['ext_attr']);
            }else{
                $arrThreads = Util_Post::mgetThread(array($intTid));
                $thread = $arrThreads[$intTid];
                $arrInput = array_merge($arrInput, $thread);
            }
        }
        if(!isset($arrInput['video_info'])){
            Bingo_Log::warning('not a video thread');
            return false;
        }
        // fix video_info: end
        
        // fix act_info: begin
        // $intActId = (int)$arrInput['video_play']['activity_info']['activity_id'];
        $intActId = Lib_Post::getActIdFromThreadInfo($arrInput);
        if ($intActId <= 0) {
            $intActId = (int)$arrInput['video_activity']['activity_id'];
        }
        if($intActId <= 0){
            $intActId = (int)$arrInput['ui_trans_params']['video_act_id'];
        }
        if($intActId > 0){
            $arrActInput = array('id' => $intActId);
            $arrActOutput = Tieba_Service::call('video', 'getActivity4Script', $arrActInput, null, null, 'post', 'php', 'utf-8');
            if(false === $arrActOutput || Tieba_Errcode::ERR_SUCCESS !== $arrActOutput['errno']){
                Bingo_Log::warning(sprintf("call video::getActivity4Script failed! tid[%s] input[%s] output[%s]", $intTid,serialize($arrActInput), serialize($arrActOutput)));
            }
            $act = $arrActOutput['data'][0];
            $act['ext'] = unserialize($act['ext']);
            $arrInput['act_info'] = $act;
        } 
        // fix act_info: end
        
        // fix forum_id: begin
        if(!isset($arrInput['forum_id']) || 0 == $arrInput['forum_id']){
            if(isset($arrInput['forum_id_shared'])){
                $arrInput['forum_id'] = (int)$arrInput['forum_id_shared'][0];
            }
        }
        // fix forum_id: end
        
        // fix video_md5: begin 
        if($arrInput['video_info']['video_type'] >= 100 && $arrInput['video_info']['video_type'] <= 500){
            if(empty($arrInput['video_info']['video_md5'])){
                $arrInput['video_info']['video_md5'] = md5($arrInput['video_info']['video_url']);
            }
        }
        if(empty($arrInput['video_info']['video_md5'])) {
            Bingo_Log::warning("empty video_md5");
            return false;
        }
        // fix video_md5: end
        
        // fix post_id:begin
        if(!isset($arrInput['post_id']) && isset($arrInput['first_post_id'])){
            $arrInput['post_id'] = (int)$arrInput['first_post_id'];
        }
        // fix post_id: end

        // fix video_length: begin
        if(!isset($arrInput['video_info']['video_length']) && isset($arrInput['video_info']['video_size'])){
            $arrInput['video_info']['video_length'] = $arrInput['video_info']['video_size'];
        }
        // fix video_length: end
        
        // fix idl thumbnail width & height: start
        $bolGenCover = Lib_Audit::getNeedGenCover($arrInput['video_info']);
        if($bolGenCover){
            $arrInput['video_info']['thumbnail_width']  = $arrInput['video_info']['video_width'];
            $arrInput['video_info']['thumbnail_height'] = $arrInput['video_info']['video_height'];
        }
        // fix idl thumbnail width & height: end 
         
        
        // build general input
        foreach (self::$_arrThreadFields as $field) {
            if (!isset($arrInput[$field])) {
                Bingo_Log::warning(sprintf("input params invalid. field[%s] input[%s]", $field, serialize($arrInput) ));
                return false;
            }
        }
        foreach (self::$_arrVideoFields as $field) {
            if (!isset($arrInput['video_info'][$field])) {
                Bingo_Log::warning(sprintf("input params invalid. field[%s] video_info[%s]", $field, serialize($arrInput['video_info']) ));
                return false;
            }
            $arrInput[$field] = $arrInput['video_info'][$field];
        }
        unset($arrInput['video_info']);

        $arrInput['thread_create_time'] = $arrInput['create_time'];
        return $arrInput;

    }
    /**
     * [getLastEditRecord description]
     * @param  video_md5 optional
     * @param  thread_id optional
     * @param  get_all_record 获取1条 or 所有
     * @return 
     */
    public static function getLastEditRecord($arrInput){
        $arrRet = array();
        $intThreadId  = (int)$arrInput['thread_id'];
        $strVideoMd5  = strval($arrInput['video_md5']);
        $intGetAll    = (int)$arrInput['get_all_record'];
        $arrTableName = Lib_Edit::getNeedCheckTableName();
        if($intThreadId > 0){
            foreach ($arrTableName as $strTableName) {
                $arrDlInput['function']   = 'selectEditByTid';
                $arrDlInput['table_name'] = $strTableName;
                $arrDlInput['thread_id']  = $intThreadId;
                $arrOutputRet = Dl_Video_Sql::execSql($arrDlInput);
                if(false === $arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                    Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');
                    return Lib_Edit::arrRet(isset($arrOutputRet['errno']) ? $arrOutputRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
                if(!empty($arrOutputRet['results'][0])) {
                    $arrRet += $arrOutputRet['results'][0];
                    if(!$intGetAll){
                        return Lib_Edit::arrRet(Tieba_Errcode::ERR_SUCCESS,$arrRet);
                    }
                }
            }
        }
        if(strlen($strVideoMd5) > 0){
            foreach ($arrTableName as $strTableName) {
                $arrDlInput['function']   = 'selectEditByMD5';
                $arrDlInput['table_name'] = $strTableName;
                $arrDlInput['video_md5']  = $strVideoMd5;
                $arrOutputRet = Dl_Video_Sql::execSql($arrDlInput);
                if(false === $arrOutputRet || $arrOutputRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                    Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrOutputRet).']');
                    return Lib_Edit::arrRet(isset($arrOutputRet['errno']) ? $arrOutputRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
                if(!empty($arrOutputRet['results'][0])) {
                    $arrRet += $arrOutputRet['results'][0];
                    if(!$intGetAll){
                        return Lib_Edit::arrRet(Tieba_Errcode::ERR_SUCCESS,$arrRet);
                    }
                }
            }
        }
        return Lib_Edit::arrRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }

    /**
     * [buildRepeatEditInput description]
     * @param  [type] $arrEditInput      [description]
     * @param  [type] $arrLastEditRecord [description]
     * @return [type]                    [description]
     */
    public static function buildRepeatEditInput($arrEditInput, $arrLastEditRecord){
        $intTime = time();
        $arrNewIntFields = array(
            'forum_id'           => 'forum_id',
            'user_id'            => 'user_id',
            'thread_id'          => 'thread_id',
            'video_type'         => 'video_type',
            'thread_create_time' => 'thread_create_time',
            'callback_type'      => 'callback_type',
            'video_from'         => 'video_from',    // 这个需要check ？？？
        );
        $arrNewStrFields = array(
            'level_1_name'       => 'first_dir',
            'level_2_name'       => 'second_dir',
        );
        $arrOldFields = array(
            'edit_result',
            'search_type',
            'fir_category',
            'sec_category',
            'tag',
            'video_score',
            'reject_reason',
            'reject_field',
            'video_quality',
            'cover_quality',
            'other_quality',
            'title_quality',
            'title',
            'bjh_user_name',
            'bjh_status',
            'dispatch_path',// not in use
            'video_site_id',
            'video_site_name',
            'audit_flag',
            'video_cover',
            'portrait_video_cover',
            'video_length',
            'ext1',
            'ext2',
            'ext3',
        );
        $arrStaticFields = array(
            'op_uid'           => Lib_Define_Edit::$strSysOpuid,//编辑是机器人
            'op_uname'         => Lib_Define_Edit::$strSysOpun,//编辑是机器人
            'edit_status'      => Lib_Define_Edit::EDIT_STATUS_NO_NEED_TO_EDIT,//不必编辑
            'dispatch_status'  => Lib_Define_Edit::DISPATCH_STATUS_DONT_SYNC,//不同步百家号
            'create_time'      => $intTime,
            'op_time'          => $intTime,
            'edit_finish_time' => $intTime,
            'pre_sync_time'    => 0,
            'publish_time'     => 0,
            'bjh_create_time'  => 0,
            'bjh_update_time'  => 0,
            'priority'         => 0,
        );
        $arrInput = $arrLastEditRecord;

        foreach($arrNewIntFields as $edit_field => $post_field){
            $arrInput[$edit_field] = (int)$arrEditInput[$post_field];
        }
        foreach($arrNewStrFields as $edit_field => $post_field){
            $arrInput[$edit_field] = strval($arrEditInput[$post_field]);
        }
        foreach($arrStaticFields as $field => $value){
            $arrInput[$field] = $value;
        }

        $arrInput['is_video_good'] = Lib_Define_Edit::IS_VIDEO_GOOD_DEFAULT;
        return $arrInput;
    }

    /**
     * [buildEditInput description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function buildEditInput($arrInput){
        $arrInput['videocp']     = Util_Videocp::mgetTidInfo($arrInput);
        $arrInput['user']        = Util_User::getUserData($arrInput);
        $arrInput['description'] = Util_Post::getSecondFloorContent($arrInput['thread_id']);
        $arrInput['audit_flag']  = Lib_Edit::getVideoFlagByTid($arrInput);


        $arrInput = Lib_Edit::autoReject($arrInput);
        $arrInput = Lib_Edit::buildCategory($arrInput);
        //$arrInput = Lib_Edit::buildVideoFrom($arrInput);
        $arrInput = Lib_Edit::buildThumbnail($arrInput);  
        $arrInput = Lib_Edit::buildPriority($arrInput); 
        $arrInput = Lib_Edit::buildDispatch($arrInput); 
        $arrInput = Lib_Edit::buildOther($arrInput);
        // $arrInput = Lib_Edit::buildXiaoying($arrInput);

        return $arrInput;
    }
    /**
     * [buildDirInfo description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function buildCategory($arrInput){
        $strLevel1Name = $arrInput['first_dir'];
        $strLevel2Name = $arrInput['second_dir'];
        if('' == $strLevel1Name){
            $arrForumDir = Util_Forum::getForumLevel($arrInput['forum_id']);
            $strLevel1Name = $arrForumDir['level_1_name'];
            $strLevel2Name = $arrForumDir['level_2_name'];
        }
        $intVideoSiteId = 0;
        $strVideoSiteName = '';
        $strTags = isset($arrInput['tag'])?$arrInput['tag']:'';

        if(isset(Util_Tag::$arrForumDir2ToMap[$strLevel2Name])) {
            $strSecDir = Util_Tag::$arrForumDir2ToMap[$strLevel2Name];
        } else {
            $strSecDir = Util_Tag::$strDefaultSecDir;
        }
        $strFirDir = Util_Tag::$arrSecDirMapFirstDir[$strSecDir];

        if(!empty($arrInput['videocp'])){
            if(Lib_Define_Edit::VIDEOCP_FROM_XIAOYING == $arrInput['videocp']['video_from']){
                if(!empty($arrInput['videocp']['tags'])){
                    $strTags = $arrInput['videocp']['tags'];
                }
                $uid = intval($arrInput['videocp']['user_id']);
                if(!empty(Util_Tag::$mapFromTB2BJHofXiaoYing1Category[$uid]) &&
                    !empty(Util_Tag::$mapFromTB2BJHofXiaoYing2Category[$uid])){
                    $strFirDir = Util_Tag::$mapFromTB2BJHofXiaoYing1Category[$uid];
                    $strSecDir = Util_Tag::$mapFromTB2BJHofXiaoYing2Category[$uid];
                }
            }
            $intVideoSiteId = intval($arrInput['videocp']['video_site_id']);
            $strVideoSiteName = trim($arrInput['videocp']['video_from']);
        }
        if(206 == $arrInput['video_type']){
            $intRandNum = rand(0,5);
            $strFirDir = Util_Tag::$_str206FirCate;
            $strSecDir = Util_Tag::$_arr206SecCate[$intRandNum];
        }
        // TODO : set first_cate sec_cate
        if(Lib_Define_Edit::SEARCH_TYPE_MINI_VIDEO == $arrInput['search_type']){
            if(isset($arrInput['act_info'])){
                $ext = $arrInput['act_info']['ext'];
                $strFirDir = $ext['fircate'];
                $strSecDir = $ext['seccate'];
            }
        }

        $arrInput['video_site_id']   = $intVideoSiteId;
        $arrInput['video_site_name'] = $strVideoSiteName;
        $arrInput['level_1_name'] = strval($strLevel1Name);
        $arrInput['level_2_name'] = strval($strLevel2Name);
        $arrInput['fir_category'] = strval($strFirDir);
        $arrInput['sec_category'] = strval($strSecDir);
        $arrInput['tag'] = $strTags;
        return $arrInput;
    }
    /**
     * [buildDispatch description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function buildDispatch($arrInput){
        $arrInput['bjh_user_name'] ='';
        $arrInput['dispatch_status'] = 0;
        if(!empty($arrInput['videocp'])){
            $arrInput['video_site_id'] = intval($arrInput['videocp']['video_site_id']);
            $arrInput['video_site_name'] = trim($arrInput['videocp']['video_from']);
            if(Lib_Define_Edit::VIDEOCP_FROM_XIAOYING == $arrInput['videocp']['video_from']){
                $uid = intval($arrInput['videocp']['user_id']);
                if(!empty(Util_Tag::$mapFromTB2BJHofXiaoYing[$uid])){
                    $arrInput['bjh_user_name'] = Util_Tag::$mapFromTB2BJHofXiaoYing[$uid];
                }
                
            }
        }
        return $arrInput;
    }
    /**
     * [buildOther description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function buildOther($arrInput){
        $arrInput['ext1'] = 0;
        $arrInput['ext2'] = 0;
        $arrInput['ext3'] = '';
        $arrInput['callback_type'] = isset($arrInput['callback_type']) ? $arrInput['callback_type'] : 0;

        if(!empty($arrInput['videocp'])){
            if(Lib_Define_Edit::VIDEOCP_FROM_XIAOYING == $arrInput['videocp']['video_from']){
                if(!empty($arrInput['videocp']['title'])){
                    $arrInput['ext3'] = $arrInput['videocp']['title'];
                }
            }
        }
        return $arrInput;
    }
    
    /**
     * [getVideoFlagByTid description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getVideoFlagByTid($arrInput){
        $intCreateTime = (int)$arrInput['create_time'];
        $intThreadId   = (int)$arrInput['thread_id'];
        $intWeekKey   = (int)Lib_Audit::getThatDayMonday($arrInput['create_time']);
        $arrParams = array(
            'weekkey' => $intWeekKey,
            'cond' => array(
                'thread_id' => $intThreadId,
            ),
        );
        $arrRet = Lib_Audit::selectVideoInfo($arrParams);
        if ($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call Lib_Audit::selectVideoInfo error. [input]:" . serialize($arrParams) . " [output]:" . serialize($arrRet));
            //return self::arrRet($arrRet ? $arrRet['errno'] : Tieba_Errcode::ERR_DB_QUERY_FAIL);
            return 0;
        }
        if (empty($arrRet['data'])) {
            Bingo_Log::warning("can not find the video in audit db. [input]:" . serialize($arrParams) . " [output]:" . serialize($arrRet));
        }
        $arrAuditInfo = empty($arrRet['data']) ? array() : $arrRet['data'][0];
        $intAuditFlag = (int)$arrAuditInfo['audit_flag'];
        return $intAuditFlag;
    }
    /**
     * [buildPriority description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function buildPriority($arrInput){
        $bolIsWhiteUser = (Lib_Define_Edit::SEARCH_TYPE_WHITE_LIST === $arrInput['search_type']);
        $intType        = (int)$arrInput['search_type'];
        $intAuditFlag   = (int)$arrInput['audit_flag'];
        $intFrom        = (int)$arrInput['video_from'];
        $intPass        = (int)$arrInput['edit_result'];
        if($bolIsWhiteUser){
            $intType = (Lib_Define_Edit::SEARCH_TYPE_USER_RECORD === $intType||Lib_Define_Edit::SEARCH_TYPE_WHITE_LIST == $intType) ? Lib_Define_Edit::SEARCH_TYPE_WHITE_LIST : $intType;
        }
        $strType = Lib_Define_Edit::$arrVideoTypeStr[$intType] . Lib_Define_Edit::$arrVideoFromStr[$intFrom] . Lib_Define_Edit::$arrPassStr[$intPass];
        
        $arrInput['priority'] = intval(Lib_Define_Edit::$arrPriority[$strType]);
        if(Lib_Define_Edit::AUDIT_FLAG_SICK === $intAuditFlag|| Lib_Define_Edit::AUDIT_FLAG_VULGAR === $intAuditFlag ){
            $arrInput['priority'] = 70;
        }

        if(!empty($arrInput['videocp'])){
            if(Lib_Define_Edit::VIDEOCP_FROM_XIAOYING == $arrInput['videocp']['video_from']){
                $arrInput['priority'] = 100; //100
            }
        }
        return $arrInput;
    }
    /**
     * [buildThumbnail description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function buildThumbnail($arrInput){
        $intThumbnailHeight = intval($arrInput['thumbnail_height']);
        $intThumbnailWidth = intval($arrInput['thumbnail_width']);
        $strCoverUrl = strval($arrInput['thumbnail_url']);

        $intPortraitCoverWidth = 0;
        $intPortraitCoverHeight = 0;
        $strPortraitCoverUrl = "";
        if($intThumbnailHeight > $intThumbnailWidth){
            Bingo_Log::warning("need portrait.");
            $intPortraitCoverHeight = $intThumbnailHeight;
            $intPortraitCoverWidth = $intThumbnailWidth;
            $strPortraitCoverUrl = $strCoverUrl;
            // $arrOutput = getimagesize($strCoverUrl);
            // Bingo_Log::warning(serialize($arrOutput));
            // if(!empty($arrOutput) && $arrOutput[0] < $arrOutput[1]){
            //     $strPortraitCoverUrl = $strCoverUrl;
            //     $intPortraitCoverWidth = $arrOutput[0];
            //     $intPortraitCoverHeight = $arrOutput[1];
            // }
        }
        if($intThumbnailWidth <= 2048) {   //切图有宽高尺寸限制 http://man.baidu.com/ksarch/common/pic/#图片在线处理_说明
            if($intThumbnailWidth <= 980) {
                $intThumbnailNeedWidth = $intThumbnailWidth;
            }
            else {
                $arrOkSize = array(980, 1000,1024,1044,1050,1080,1100,1140,1152,1200,1280,1300,1360,1366,1400,1440,1458,1500,1600,1680,1856,1920,2026,2048);
                foreach ($arrOkSize as $key => $intSize) {
                    if(intval($intSize) <= $intThumbnailWidth) {
                        $intThumbnailNeedWidth = $intSize;
                    }
                }
            }
            $intThumbnailNeedHeight = ceil(($intThumbnailNeedWidth / 16.0) * 9);
            $strSwitchCoverUrl = Util_Pic::extUrlToInnerUrl($strCoverUrl, "awhcrop=$intThumbnailNeedWidth,$intThumbnailNeedHeight");
        }
        if(!empty($strSwitchCoverUrl)) {
            Bingo_Log::warning("use switch cover url");
            $strCoverUrl = $strSwitchCoverUrl;
            $intThumbnailHeight = $intThumbnailNeedHeight;
            $intThumbnailWidth = $intThumbnailNeedWidth;
        }
        // TODO : make sure how 'portrait_XXX' works
        $arrInput['portrait_cover_height'] = $intPortraitCoverHeight;
        $arrInput['portrait_cover_width']  = $intPortraitCoverWidth;
        $arrInput['thumbnail_width']       = $intThumbnailWidth;
        $arrInput['thumbnail_height']      = $intThumbnailHeight;
        $arrInput['video_cover']           = $strCoverUrl;
        $arrInput['portrait_video_cover']  = $strPortraitCoverUrl;
        return $arrInput;
    }

    /**
     * @param string $title
     * @param $strContent
     * @return bool
     */
    public static function autoReject($arrInput){
        $intVideoFrom     = 0;
        $intVideoType     = (int)$arrInput['video_type'];
        $intVideoDuration = (int)$arrInput['video_duration'];
        $intVideoHeight   = (int)$arrInput['video_height'];
        $intVideoWidth    = (int)$arrInput['video_width'];
        $bolIsPGC         = Lib_Edit::isPGC($arrInput);
        $bolIsWhiteUser   = Lib_Edit::isWhiteUser($arrInput);
        $bolIsUserRecord  = (Molib_Util_Video::TEMP_VIDEO_FROM_CLIENT_RECORD == $intVideoType);
        $bolIsVertical    = ($intVideoHeight > $intVideoWidth) && ($intVideoWidth >= 240)  && ($intVideoDuration >= 6) && ($intVideoDuration <= 60);
        $bolIsMiniVideo   = Lib_Edit::isMiniVideo($arrInput);
        $bolIsHaoKanVideo = Lib_Edit::isHaoKanVideo($arrInput);
        
        // 这块儿还没实际上线，先不加开关了
        if ($bolIsHaoKanVideo) {
            Bingo_Log::pushNotice('search_type','haokanvideo');
            $arrRet = Lib_Edit::filterHaoKanVideo($arrInput);
        }
        else if($bolIsMiniVideo){
            Bingo_Log::pushNotice('search_type','minivideo');
            $arrRet = Lib_Edit::filterMiniVideo($arrInput);
        }else if ($bolIsVertical){
            Bingo_Log::pushNotice('search_type','verticle');
            $arrRet = Lib_Edit::filterVertical($arrInput);
        }else if($bolIsPGC){
            Bingo_Log::pushNotice('search_type','pgc');
            $arrRet = Lib_Edit::filterPGC($arrInput);
        }else if($bolIsWhiteUser){
            Bingo_Log::pushNotice('search_type','white');
            $arrRet = Lib_Edit::filterWhiteUser($arrInput);
        }else if($bolIsUserRecord){
            Bingo_Log::pushNotice('search_type','user_record');
            $arrRet = Lib_Edit::filterUserRecord($arrInput);
        }else{
            Bingo_Log::pushNotice('search_type','ugc');
            $arrRet = Lib_Edit::filterUGC($arrInput);
        }

        if($bolIsWhiteUser){//??????
            if(Lib_Define_Edit::SEARCH_TYPE_USER_RECORD == $arrRet['search_type']
            || Lib_Define_Edit::SEARCH_TYPE_UGC == $arrRet['search_type']){
                $arrRet['search_type'] = Lib_Define_Edit::SEARCH_TYPE_WHITE_LIST;
            }
        }
        if(1 == $arrInput['video_from']) {   //机筛数据
            if($bolIsPGC) {
                $intVideoFrom = Lib_Define_Edit::VIDEO_FROM_PGC_FILTER;  //pgc机筛
            }else{
                $intVideoFrom = Lib_Define_Edit::VIDEO_FROM_UGC_FILTER; //ugc机筛
            }
        }else if(2 == $arrInput['video_from']){
            if($bolIsPGC){
                $intVideoFrom = Lib_Define_Edit::VIDEO_FROM_PGC_STRATEGY;  //pgc策略
            }else{
                $intVideoFrom = Lib_Define_Edit::VIDEO_FROM_UGC_STRATEGY; //ugc策略
            }
        }
        if(1 === $arrInput['is_recommend']){
            $intVideoFrom = Lib_Define_Edit::VIDEO_FROM_RECOMMEND;
        }

        $arrInput['reject_field']  = strval($arrRet['reject_field']) ;
        $arrInput['search_type']   = intval($arrRet['search_type']); 
        $arrInput['is_video_good'] = intval($arrRet['is_video_good']);
        $arrInput['video_from']    = $intVideoFrom ;

        $intEditResult = Lib_Define_Edit::EDIT_RESULT_INIT;
        $intEditStatus = Lib_Define_Edit::EDIT_STATUS_READY; //未编辑状态
        if('' !== $arrInput['reject_field']) {
            $intEditResult = Lib_Define_Edit::EDIT_RESULT_REJECT;  //拒绝
            $intEditStatus = Lib_Define_Edit::EDIT_STATUS_ENGINE_REJECT;
        }

        if(!empty($arrInput['videocp'])){
            if(Lib_Define_Edit::VIDEOCP_FROM_XIAOYING == $arrInput['videocp']['video_from']){
                $intEditResult = Lib_Define_Edit::EDIT_RESULT_REJECT;  //站点细分 10.小影
                $intEditStatus = Lib_Define_Edit::EDIT_STATUS_NO_NEED_TO_EDIT;  //站点细分 10.小影
            }
        }
        if(206 == $arrInput['video_type']){
            $intEditResult = Lib_Define_Edit::EDIT_STATUS_NO_NEED_TO_EDIT;
            $intEditStatus = Lib_Define_Edit::EDIT_RESULT_PASS;
        }
        $arrInput['edit_result'] = $intEditResult;
        $arrInput['edit_status'] = $intEditStatus;
        return $arrInput;
    }
    /**
     * 判断某个视频贴是否为pgc内容
     * @param $arrInput is mgetThread return
     * @param $thread_id 用来确认是判断哪个thread
     * @return bool true是大神 false不是
     */
    public static function isPGC($arrInput){
        //先判断是不是微吧来的视频 是的话标记为pgc内容
        if(isset($arrInput['user']['video_channel_info'])){
            return true;
        }
        //判断是否为pgc后台上传的视频
        if(($arrInput['video_type'] >= 100 && $arrInput['video_type'] <= 500)
        || Molib_Util_Video::TEMP_VIDEO_FROM_PGC == $arrInput['video_type'] ){
            return true;
        }
        $isGod = Util_User::getAuthorGod($arrInput);
        if(1 == $isGod){ //全吧大神
            return true;
        }
        return false;
    }
    /**
     * @param string $title
     * @param $strContent
     * @return bool
     */
    public static function isWhiteUser($arrInput){
        $intUid = (int)$arrInput['user_id'];
        $arrInput = array(
            'field' => array($intUid),
        );
        $arrOutput = Lib_WordList::get($arrInput);
        if(intval($arrOutput[$intUid]) == 1){
            return true;
        }
        return false;
    }
    /**
     * [isMiniVideo description]
     * @param  [type]  $video_type [description]
     * @param  [type]  $arrInput   [description]
     * @return boolean             [description]
     */
    public static function isMiniVideo($arrInput){
        $intVideoType = (int)$arrInput['video_type'];
        if(Lib_Define_Edit::$arrMiniVideoVType[$intVideoType]){
            return true;
        }
        return false;
    }


    /**
     * @brief  判断视频是否是好看视频
     * @param  $arrInput
     * @return bool
     */
    public static function isHaoKanVideo($arrInput) {
        $intVideoType = (int)$arrInput['video_type'];
        return ($intVideoType == 110) ? true : false;
    }

    /**
     * @param $arrInput
     * @return bool || $arrInput
     */
    public static function filterMiniVideo($arrInput){   // 拆分 抓取视频 以及 kol视频，但保留所有小视频逻辑
        $arrRet = array(
            'search_type' => Lib_Define_Edit::SEARCH_TYPE_MINI_VIDEO,    // 默认为小视频
            'is_video_good' =>  Lib_Define_Edit::IS_VIDEO_GOOD_DEFAULT,
        );

        $intVideoHeight   = intval($arrInput['video_height']);
        $intVideoWidth    = intval($arrInput['video_width']);
        $intVideoDuration = intval($arrInput['video_duration']);
        $intCoverHeight   = intval($arrInput['thumbnail_height']);
        $intCoverWidth    = intval($arrInput['thumbnail_width']);
        $intVideoType     = intval($arrInput['video_type']);
        $intUid           = intval($arrInput['user_id']);
        //满足以下条件之一，不符合微视频精选
        if(($intVideoDuration < 6) 
        || (($intVideoWidth / $intVideoHeight) > 1)  // 横屏
        || (($intCoverWidth / $intCoverHeight) > 1) 
        || $intVideoWidth < 240 
        || $intVideoType == Molib_Util_Video::TEMP_VIDOE_CP_FOR_NANI){
            $arrRet['is_video_good'] = Lib_Define_Edit::IS_NOT_VIDEO_GOOD;
        }

        // new add 抓取 拆分
        if(Lib_Define_Edit::$arrSpiderVideoVType[$intVideoType]){ // 是抓取视频，即video_type in (207, 210)
                $arrRet['search_type'] = Lib_Define_Edit::SEARCH_TYPE_SPIDER_VIDEO;
                return $arrRet;
        }

        if(Lib_Edit::isJudgeKol($intUid)){
            $arrRet['search_type'] = Lib_Define_Edit::SEARCH_TYPE_KOL_VIDEO;
        }

        // CRM
        if (Lib_Union::checkMemberVideoType($intVideoType, $intUid)) {
            if (!Lib_Union::checkUnionMember($intUid, $bolExist)) {
                Bingo_Log::fatal("[filterMiniVideo] check union member fail, uid: $intUid");
            } else if ($bolExist) {
                $arrRet['search_type'] = Lib_Define_Edit::SEARCH_TYPE_UNION_MEMBER_VIDEO;
            }
        }

        return $arrRet;
    }
    /**
     * @param int
     * @return bool
     */
    public static function isJudgeKol($intUid){

        // 新增 is kol
        $arrParams = array(
            'uid' => $intUid,
            'ps'  => 1,
            'pn'  => 1,
            'show_del' => 2,
        );
        $arrOutputFromDB = Service_Pgc_Management::getPgcUserList($arrParams);

        if(false === $arrOutputFromDB || Tieba_Errcode::ERR_SUCCESS != $arrOutputFromDB["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "call Video::getPgcUserList fail. input:[" . serialize($arrParams) . "]; output:[" .
                serialize($arrOutputFromDB) . "]";
            Bingo_Log::warning($strLog);
            return false;
        }

        if($arrOutputFromDB['data']['count'] >= 1){  // is_kol, 将kol视频拆分出来
           return true;
        }
    }

    /**
     * @param string $title
     * @param $strContent
     * @return bool
     */
    public static function filterWhiteUser($arrInput){
        $arrRet = array(
            'search_type' => Lib_Define_Edit::SEARCH_TYPE_WHITE_LIST,
        );
        $intThumbnailWidth  = (int)$arrInput['thumbnail_width'];
        $intThumbnailHeight = (int)$arrInput['thumbnail_height'];
        $intVideoHeight     = (int)$arrInput['video_height'];
        $intVideoWidth      = (int)$arrInput['video_width'];
        $intVideoType       = (int)$arrInput['video_type'];
        $intVideoDuration   = (int)$arrInput['video_duration'];

        //加入了竖版封面判定 如果是竖版的需要宽高大于414*414
        //封面宽高
        if($intThumbnailWidth <= $intThumbnailHeight){
            if($intThumbnailWidth < 414 || $intThumbnailHeight < 414){
                $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_NO_SMALL_COVER;
                $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_TEXT_SMALL_COVER;
                return $arrRet;
            }
        }else{
            if($intThumbnailWidth < 414 || $intThumbnailHeight < 233){
                $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_NO_SMALL_COVER;
                $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_TEXT_SMALL_COVER;
                return $arrRet;
            }
        }
        //机筛分辨率低
        if($intVideoHeight < 240){
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_NO_LOW_RESOLUTION;
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_TEXT_LOW_RESOLUTION;
            return $arrRet;
        }
        //大搜内容
        if($intVideoType == 6){
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_NO_SEARCH;
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_TEXT_SEARCH;
            return $arrRet;
        }
        //时长
        if($intVideoDuration <= 8){
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_NO_SHORT_DURATION;
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_TEXT_SHORT_DURATION;
            return $arrRet;
        }
        return $arrRet;
    }

    /**
     * @param string $title
     * @param $strContent
     * @return bool
     */
    public static function filterPGC($arrInput){
        $arrRet = array(
            'search_type' => Lib_Define_Edit::SEARCH_TYPE_PGC,
        );
        $intThumbnailWidth  = (int)$arrInput['thumbnail_width'];
        $intThumbnailHeight = (int)$arrInput['thumbnail_height'];
        $intVideoHeight     = (int)$arrInput['video_height'];
        $intVideoWidth      = (int)$arrInput['video_width'];
        $intVideoType       = (int)$arrInput['video_type'];
        $intVideoDuration   = (int)$arrInput['video_duration'];
        $arrRet['search_type'] = ($intVideoType >= 100 && $intVideoType <= 500) ? Lib_Define_Edit::SEARCH_TYPE_SITE : Lib_Define_Edit::SEARCH_TYPE_PGC;

        //加入了竖版封面判定 如果是竖版的需要宽高大于414*414
        //封面宽高
        if($intThumbnailWidth <= $intThumbnailHeight){
            if($intThumbnailWidth < 414 || $intThumbnailHeight < 414){
                $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_NO_SMALL_COVER;
                $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_TEXT_SMALL_COVER;
                return $arrRet;
            }
        }else{
            if($intThumbnailWidth < 414 || $intThumbnailHeight < 233){
                $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_NO_SMALL_COVER;
                $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_TEXT_SMALL_COVER;
                return $arrRet;
            }
        }
        //机筛分辨率低
        if($intVideoHeight < 240){
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_NO_LOW_RESOLUTION;
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_TEXT_LOW_RESOLUTION;
            return $arrRet;
        }
        //大搜内容
        if($intVideoType == 6){
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_NO_SEARCH;
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_TEXT_SEARCH;
            return $arrRet;
        }
        //时长
        if($intVideoDuration <= 8){
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_NO_SHORT_DURATION;
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_TEXT_SHORT_DURATION;
            return $arrRet;
        }
        return $arrRet;
    }
    /**
     * @param string $title
     * @param $strContent
     * @return bool
     */
    public static function filterUGC($arrInput){
        $arrRet = array(
            'search_type' => Lib_Define_Edit::SEARCH_TYPE_UGC,
        );
        $intThumbnailWidth  = (int)$arrInput['thumbnail_width'];
        $intThumbnailHeight = (int)$arrInput['thumbnail_height'];
        $intVideoHeight     = (int)$arrInput['video_height'];
        $intVideoWidth      = (int)$arrInput['video_width'];
        $intVideoType       = (int)$arrInput['video_type'];
        $intVideoDuration   = (int)$arrInput['video_duration'];
        $strTitle           = $arrInput['title'];
        if ('' === $strTitle
        || Lib_Define_Edit::DEFAULT_TITLE === $strTitle
        || mb_strlen($strTitle, 'UTF-8') < 10
        || is_numeric($strTitle)){
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_NO_BAD_TITLE;
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_TEXT_BAD_TITLE;
            return $arrRet;
        }
        //////////////////VIDOECP TODO

        //加入了竖版封面判定 如果是竖版的需要宽高大于414*414
        //封面宽高
        if($intThumbnailWidth <= $intThumbnailHeight){
            if($intThumbnailWidth < 414 || $intThumbnailHeight < 414){
                $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_NO_SMALL_COVER;
                $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_TEXT_SMALL_COVER;
                return $arrRet;
            }
        }else{
            if($intThumbnailWidth < 414 || $intThumbnailHeight < 233){
                $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_NO_SMALL_COVER;
                $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_TEXT_SMALL_COVER;
                return $arrRet;
            }
        }

        //机筛分辨率低
        if($intVideoHeight < 240){
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_NO_LOW_RESOLUTION;
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_TEXT_LOW_RESOLUTION;
            return $arrRet;
        }

        //大搜内容
        if($intVideoType == 6){
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_NO_SEARCH;
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_TEXT_SEARCH;
            return $arrRet;
        }
        //时长
        if($intVideoDuration <= 15){
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_NO_SHORT_DURATION;
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_TEXT_SHORT_DURATION;
            return $arrRet;
        }
        return $arrRet;

    }
    /**
     * @param string $title
     * @param $strContent
     * @return bool
     */
    public static function filterUserRecord($arrInput){
        $arrRet = array(
            'search_type' => Lib_Define_Edit::SEARCH_TYPE_USER_RECORD,
        );
        $intVideoDuration = intval($arrInput['video_duration']);
        if($intVideoDuration <= 6){
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_NO_SHORT_DURATION;
            $arrRet['reject_field'] = Lib_Define_Edit::REJECT_FIELD_TEXT_SHORT_DURATION;
            return $arrRet;
        }
        return $arrRet;
    }
    /**
     * [getSpreadInfo description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getTopicIds($arrInput){
        $intTopicId = 0;
        // 在配置吧
        $arrForumTopicIds = Lib_Edit::getTopicIdByFid($arrInput);//forum_id
        // 在主题活动上
        $arrTitleTopicIds = Lib_Edit::getTopicIdByTitle($arrInput);// title
        $arrTopicIds = array();
        if(!empty($arrForumTopicIds)){
            $arrTopicIds = array_merge($arrTopicIds, $arrForumTopicIds);
        }
        if(!empty($arrTitleTopicIds)){
            $arrTopicIds = array_merge($arrTopicIds, $arrTitleTopicIds);
        }
        $arrTopicIds = array_unique($arrTopicIds);
        Bingo_Log::warning(sprintf("topicIdByFid[%s] topicIdByTitle[%s] topic_ids[%s]", join('_', $arrForumTopicIds), join('_', $arrTitleTopicIds), join('_', $arrTopicIds)));
        $intTopicId  = (int)$arrTopicIds[0];
        return $arrTopicIds;
    }
    /**
     * [getTopicIdByUid description]
     * @param  [type] $intUid     [description]
     * @param  [type] $intTopicId [description]
     * @return [type]             [description]
     */
    public static function getTopicBigV($intUid, $intTopicId){
        Bingo_Timer::start(__FUNCTION__);
        $intBigV = 0;
        $arrInput = array(
            'user_id'  => $intUid,
            'topic_id' => $intTopicId,
        );
        $strService = 'video';
        $strMethod  = 'getTopicIdByUid';
        $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput);
        $strLog = sprintf("call $strService::$strMethod failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput));
        if(false === $arrOutput){
            Bingo_Log::fatal($strLog);
            return $intBigV;
        }else if(Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning($strLog);
            return $intBigV;
        }
        $intScore = (int)$arrOutput['data'][$intUid];
        if($intScore > 0){
            $intBigV = 1;
        }
        Bingo_Timer::end(__FUNCTION__);

        return $intBigV;
    }
    /**
     * [getTopicIdByFid description]
     * @param  [type] $intFid   [description]
     * @param  array  $arrVFids [description]
     * @return [type]           [description]
     */
    public static function getTopicIdByFid($arrInput){
        if($arrInput['forum_id'] <=0){
            return array();
        }
        Bingo_Timer::start(__FUNCTION__);
        $intFid = (int)$arrInput['forum_id'];
        $arrTopicIds = array();

        $arrVFids []= $intFid;
        $arrInput = array('forum_id' => $intFid);
        $strService = 'forum';
        $strMethod  = 'getBtxInfo';
        $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput);
        $strLog = sprintf("call $strService::$strMethod failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput));
        if(false === $arrOutput){
            Bingo_Log::fatal($strLog);
            return $arrTopicIds;
        }else if(Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning($strLog);
            return $arrTopicIds;
        }
        $intTopicId = (int)$arrOutput['attrs']['video']['topic']['id'];
        if($intTopicId > 0){
            $arrTopicIds []= $intTopicId;
        }
        Bingo_Timer::end(__FUNCTION__);
        return $arrTopicIds;

    }
    /**
     * [getTopicIdByTitle description]
     * @param  [type] $strTitle [description]
     * @return [type]           [description]
     */
    public static function getTopicIdByTitle($arrInput){
        $strTitle = $arrInput['title'];
        Bingo_Timer::start(__FUNCTION__);
        $arrTopicIds = array();
        if(preg_match('/#([^#]+)#/', $strTitle, $result)>0){
            $strActName = $result[1];
            $arrInput = array();
            // $strService = 'ala';
            // $strMethod  = 'selectAllActivity';
            $strService = 'video';
            $strMethod  = 'getAllActs';
            $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null,null,'post','php','utf-8');
            $strLog = sprintf("call $strService::$strMethod failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput));
            if(false === $arrOutput){
                Bingo_Log::fatal($strLog);
                return $arrTopicIds;
            }else if(Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
                Bingo_Log::warning($strLog);
                return $arrTopicIds;
            }
            $arrActNames = array();
            foreach($arrOutput['data'] as $activity){
                $arrActNames[]=$activity['activity_name'].":".$activity['topic_id'];
                if($strActName == $activity['activity_name'] && $activity['topic_id']>0){
                    $arrTopicIds []= (int)$activity['topic_id'];
                }
            }
            Bingo_Log::warning(sprintf("act_name:[%s] all_actname:[%s]", $strActName, join('_', $arrActNames)));
        }
        Bingo_Timer::end(__FUNCTION__);
        return $arrTopicIds;
    }
    /**
     * [pushToRecom description]
     * @param  [type] $arrInput     [description]
     * @param  [type] $arrVideoInfo [description]
     * @return [type]               [description]
     */
    public static function pushToRecom($arrInput){
        Bingo_Timer::start(__FUNCTION__);
        
        $intTid   = (int)$arrInput['thread_id'];
        $intUid   = (int)$arrInput['user_id'];
        $intOpUid = (int)$arrInput['op_uid'];
        $strOpUn  = $arrInput['op_uname'];
        $intTopicId = (int)$arrInput['topic_id'];
        if($intTopicId <= 0){
            return true;
        }
        $intBigV  = (int)Lib_Edit::getTopicBigV($intUid, $intTopicId); 
        $bolRet  = Util_Recall::store($intTopicId, $intTid, $intBigV);

        $arrDbInput = array(
            'user_id'   => $intUid,
            'topic_id'  => $intTopicId,
            'thread_id' => $intTid,
            'op_uid'    => $intOpUid,
            'op_uname'  => $strOpUn,
        );
        $bolDBOut = Service_Topic_User::inflowTopicUser($arrDbInput);
        $bolDBOut = Service_Topic_Thread::inflowTopicThread($arrDbInput);

        /*
        // add by dongliang04 tryLuck activity
        $arrServiceInput = array(
            'user_id' => $intUid,
            'type' => Lib_Define_Reward::TYPE_SELECTED,
        );
        $arrServiceOutput = Service_Reward_Funny::addUserFunny($arrServiceInput);
        Util_Service::checkServiceOutput($arrServiceInput, $arrServiceOutput, 'addUserFunny');

        $arrServiceInput = array(
            'user_id' => $intUid,
            'type' => Lib_Define_Reward::TYPE_ADD_THREAD,
            'thread_id' => $intTid,
        );
        $arrServiceOutput = Service_Reward_Funny::addUserFunny($arrServiceInput);
        Util_Service::checkServiceOutput($arrServiceInput, $arrServiceOutput, 'addUserFunny');
        
         */
        Bingo_Timer::end(__FUNCTION__);
        return $bolRet;
    }

    /**
     * @brief  筛选好看视频
     * @param  $arrInput
     * @return array
     */
    public static function filterHaoKanVideo($arrInput) {
        $arrRet = array(
            'search_type' => Lib_Define_Edit::SEARCH_TYPE_HAOKAN,
        );
        return $arrRet;
    }

    /**
     * @brief  筛选竖屏视频
     * @param  $arrInput
     * @return mixed
     */
    public static function filterVertical($arrInput){
        $arrRet = array(
            'search_type' => Lib_Define_Edit::SEARCH_TYPE_VERTICAL,
        );
        return $arrRet;
    }

    /**
     * @param $arrThreadIds
     * @param $arrWaterMarkTypes
     * @return bool
     */
    public static function getVideoWaterMarkTypeByThreadIds($arrThreadIds, &$arrWaterMarkTypes)
    {
        $arrWaterMarkTypes = array();
        $arrChunks = array_chunk($arrThreadIds, 2000);
        $bolRet = true;
        foreach ($arrChunks as $arrThreadIds) {
            $arrFields = array_map(function($intThreadId){
                return 'thread_id_' . $intThreadId;
            }, $arrThreadIds);
            $arrInput = array(
                'key'    => self::REDIS_KEY_VIDEO_WATERMARK_TYPE,
                'fields' => $arrFields,
            );
            $arrOutput = Util_Redis::hmgetFromRedis($arrInput);
            if(false === $arrOutput){
                Bingo_Log::warning('hmgetFromRedis fail, input='.json_encode($arrInput).', ret='.json_encode($arrOutput));
                $bolRet = false;
            } else {
                foreach($arrThreadIds as $intThreadId){
                    $strField = 'thread_id_' . $intThreadId;
                    $arrWaterMarkTypes[$intThreadId] = isset($arrOutput[$strField]) ? (int)$arrOutput[$strField] : 0;
                }
            }
        }
        return $bolRet;
    }

    /**
     * 错误信息
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function arrRet($errno, $data = null){
        return array(
            'errno'     => $errno,
            'errmsg'    => Tieba_Error::getErrmsg($errno),
            'data'      => $data,
        );
    }

    /**
     * 发送nmq参数拼接(编辑后发送给推荐nmq)
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function buildRecomNmqInput($arrInput){
        $arrOriginInput = $arrInput;

        // get thread edit info from db
        $intTid = intval($arrInput['thread_id']);
        if(!isset($arrInput['thread_create_time']) || empty($arrInput['thread_create_time'])){
            $arrData = Util_Post::mgetThread(array($intTid));
            $arrThreadInfo = $arrData[$intTid];
            $intCreateTime = $arrThreadInfo['create_time'];
        } else {
            $intCreateTime = $arrInput['thread_create_time'];
        }
        $strTableName = self::getTableName($intCreateTime);
        $arrDlInput = array(
            'function' => 'selectEditByTid',
            'table_name' => $strTableName,
            'thread_id' => $intTid,
        );
        $arrDlOutput = Dl_Video_Sql::execSql($arrDlInput);
        if(false === $arrDlOutput || $arrDlOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS || empty($arrDlOutput['results'][0])){
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return array();
        }
        $arrDlData = current($arrDlOutput['results'][0]);
        $arrOriData = $arrDlData;
        foreach($arrDlData as $k => $v){
            if(isset($arrInput[$k])){
                $arrDlData[$k] = $arrInput[$k];
            }
        }

        $floatEsSharpness = floatval(round(Lib_Audit::getVideoClarityScore($intTid,$intCreateTime)/10,5));
        Bingo_Log::warning(sprintf('getEsSharpness buildRecomNmqInput-DEBUG, es_sharpness=%f',$floatEsSharpness));

        // build fields
        $arrFields = array(
            'thread_id',
            'create_time',
            'video_width',
            'video_height',
            //'video_watermark_type',
            'video_type',
            'user_id',
            'title',
            'fir_category',
            'sec_category',
            'video_quality',
            'title_quality',
            'video_score',
            'other_quality',
            'cover_quality',
            'is_video_square',
            'is_video_good',
            'forum_id',
            'edit_result',
            'edit_finish_time',
            'dispatch_status',
            'edit_status',
            'tag',
            'video_length',
            'level_1_name',
            'level_2_name',
            'sharpness',
            'es_sharpness',
            'class_id',
            'sub_class_id',
            'sound_quality',
            'realtime_type',
            'first_publish_time',
            'vulgar_type',
        );
        $arrEditInfo = array();
        foreach($arrFields as $field){
            if(isset($arrDlData[$field])){
                $arrEditInfo[$field] = $arrDlData[$field];
            }else{
                $arrEditInfo[$field] = '';
            }
        }
        $arrEditInfo['create_time'] = $arrDlData['thread_create_time'];
        $arrEditInfo['video_width'] = $arrDlData['thumbnail_width'];
        $arrEditInfo['video_height'] = $arrDlData['thumbnail_height'];
        // 首次编辑标记
        $arrEditInfo['is_first_edit'] = ($arrInput['edit_status'] == Lib_Define_Edit::EDIT_STATUS_2ND) ? 1 : 0;
        $arrEditInfo['es_sharpness'] = $floatEsSharpness;
        //增加三个新字段代表新分类新标签
        if(isset($arrInput['tag_type']) && $arrInput['tag_type'] =='old' ){
            $arrEditInfo['fir_category']     = $arrInput['fir_category'];
            $arrEditInfo['sec_category']     = $arrInput['sec_category'];
            $arrEditInfo['tag']              = $arrInput['tag'];
            $arrEditInfo['new_fir_category'] = $arrOriData['fir_category'];
            $arrEditInfo['new_sec_category'] = $arrOriData['sec_category'];
            $arrEditInfo['new_tag']          = $arrOriData['tag'];
            unset($arrEditInfo['tag_type']);
        }elseif(isset($arrInput['tag_type']) && $arrInput['tag_type'] =='new'){
            $arrEditInfo['new_fir_category'] = $arrDlData['fir_category'];
            $arrEditInfo['new_sec_category'] = $arrDlData['sec_category'];
            $arrEditInfo['new_tag']          = $arrDlData['tag'];
            $arrEditInfo['new_tag_category'] = Lib_Edit::_getNewTagCate($arrEditInfo['new_fir_category'],$arrEditInfo['new_sec_category'],$arrEditInfo['new_tag']);
            $arrEditInfo['fir_category']     = '';
            $arrEditInfo['sec_category']     = '';
            $arrEditInfo['tag']              = '';
            unset($arrEditInfo['tag_type']);
        }
        $arrOutput = array(
            'origin_input' => $arrOriginInput,
            'edit_info' => $arrEditInfo,
        );
        return $arrOutput;
    }

    //获取标签对应的标签分类
    public static function _getNewTagCate($firstCateName,$secondCateName,$tag){
        $arrList = array();
        $handleWordServer = Wordserver_Wordlist::factory();   
        $strTableName     = "tb_wordlist_redis_new_cate_tag_info"; 
        $key = 'tag_'.$firstCateName.'_'.$secondCateName;
        $arrTagInput = array($key);
        $arrTagOutput = $handleWordServer->getValueByKeys($arrTagInput, $strTableName); 
        if (!$arrTagOutput || $arrTagOutput['err_no'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf("_getNewTagCate,call wordlist::getValueByKeys failed! input[%s] output[%s]", serialize($arrTagInput), serialize($arrTagOutput)));
        }
        $arrTags = unserialize($arrTagOutput[$key]);
        if(empty($arrTags)){
            Bingo_Log::warning("_getNewTagCate,wordlist::getValueByKeys , tag is null .tagKay=".$key);
        }
        $arrInputTags = explode(",",$tag);
        foreach($arrTags as $k=>$v){
                $arrList[$k]['label'] = $v['label'];
                $arrList[$k]['option'] = explode(",",$v['option']);
        }
        $arrList = array_values($arrList);
        $retTag = array();
        $tagTmp = array();
        foreach($arrInputTags as $k=>$v){
            foreach($arrList as $vv){
                    if(in_array($v,$vv['option'])){
                        $retTag[$vv['label']][] = trim($v);
                        $tagTmp[$v] = $vv['label'];
                    }
            }
            if(empty($tagTmp[$v])){
                $retTag['chaned'][] = trim($v); 
            }
        }
        Bingo_Log::warning('_getNewTagCate, fir_cate='.$firstCateName.' sec_cate='.$secondCateName.' input_tag='.$tag.' ret='.serialize($retTag));
        return $retTag;
    }

    /**
     * get search_type with biases.
     * if the user name prefix is BF(bai fu) or HNX(he nuo xing) then the search_type should be 
     * add corresponding biases, otherwise return the value of both. 
     * @param : {
     *  'search_type' => array(
     *          1,
     *          2,
     *      ),
     *  'amis_user' => 'xxxx',
     * }
     * @return : {
     *  'search_type' => {
     *      xxxx,
     *      xxxx,
     *  }
     * }
     */ 
    public static function getSearchTypeWithBiases($input)
    {
        $strUserName = $input['amis_user'];
        $arrSearchType = $input['search_type'];

        $arrOutput = array(
            'search_type' => array(),
        );

        if (empty($arrSearchType)) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ': params errror! must need user and search_type field.');
            return $arrOutput;
        }
        
        $arrPrefix = Lib_Define_Edit::$VIDEO_EDIT_OUTSOURCING_COMPANY_PREFIX;
        $arrBiases = Lib_Define_Edit::$VIDEO_EDIT_OUTSOURCING_COMPANY_BIASES;

        foreach ($arrPrefix as $key => $value) {
            $intLen = strlen($value);
            if (substr($strUserName, 0, $intLen) === $value) {
                foreach ($arrSearchType as $intSearchType) {
                    $arrOutput['search_type'][] = $intSearchType + $arrBiases[$value] * Lib_Define_Edit::VIDEO_EDIT_OUTSOURCING_COMPANY_BASE;
                }
            }
        }

        if (empty($arrOutput['search_type'])) {
            foreach ($arrBiases as $key => $value) {
                foreach ($arrSearchType as $intSearchType) {
                    $arrOutput['search_type'][] = $intSearchType + $value * Lib_Define_Edit::VIDEO_EDIT_OUTSOURCING_COMPANY_BASE;    
                }
            }    
        }

        $arrOutput['search_type'] = array_unique($arrOutput['search_type']);

        return $arrOutput;
    }

    /**
     * @brief according to the thread id generate search type with biases
     * @param
     * @return 
     *  null => error
     *  int => search type
     */
    public static function getInsertSearchTypeWithBiases($intSearchType, $intThreadId)
    {
        $arrInputForSearchType = array(
            'amis_user' => '',
            'search_type' => array(
                $intSearchType,
            ),
        );
        $arrCompanyPrefix = Lib_Define_Edit::$VIDEO_EDIT_OUTSOURCING_COMPANY_PREFIX;

        $intIndex = $intThreadId % sizeof($arrCompanyPrefix);
        $arrInputForSearchType['amis_user'] = $arrCompanyPrefix[$intIndex];
        $arrSearchTypeWithBiases = self::getSearchTypeWithBiases($arrInputForSearchType);

        if (empty($arrSearchTypeWithBiases['search_type']) || sizeof($arrSearchTypeWithBiases['search_type']) !== 1) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ': call Lib_Edit::getSearchTypeWithBiases return a empty array or length not equals 1! input: ' . json_encode($arrInputForSearchType));
            return null;
        }
        return $arrSearchTypeWithBiases['search_type'][0];
    }

    /**
     * @brief remove biases in search type. because biases are plused by a fixed value.
     *      so just need to % fixed value.
     * @param
     * @return
     */
    public static function delBiasesInSearchType($intSearchType)
    {
        if (empty($intSearchType)) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ': param of $intSearchType is empty!');
            return null;
        }
        $intBase = Lib_Define_Edit::VIDEO_EDIT_OUTSOURCING_COMPANY_BASE;
        $intNoBiases = $intSearchType % $intBase;
        return $intNoBiases;
    }

    /**
     * @brief construct odyssey startegy_exinfo fields
     * @return array
     */
    public static function processOdysseyStrategy($intThreadId, $intCreateTime, $arrOriginParam = array())
    {
        if ($intThreadId <= 0 || empty($intCreateTime)) {
            return array();
        }

        $floatEsSharpness = floatval(round(Lib_Audit::getVideoClarityScore($intThreadId,$intCreateTime)/10,5));
        Bingo_Log::warning(sprintf('getEsSharpness processOdysseyStrategy-DEBUG, es_sharpness=%f',$floatEsSharpness));

        $strTableName = self::getTableName($intCreateTime);
        
        $arrDlInput = array(
            'function' => 'selectEditByTid',
            'table_name' => $strTableName,
            'thread_id' => $intThreadId,
        );

        $arrDlOutput = Dl_Video_Sql::execSql($arrDlInput);
        
        if (false === $arrDlOutput || $arrDlOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS || empty($arrDlOutput['results'][0])) {
            Bingo_Log::warning('call db fail, input:[' . serialize($arrDlInput) . '],output:[' . serialize($arrDlOutput) . ']');
            return array();
        }
        
        $arrFields = current($arrDlOutput['results'][0]);

        $arrRetData = array(
            'origin_input'  =>  array(
                'thread_id'     =>  $arrFields['thread_id'],
            ),
            'video_cover'   =>  $arrFields['video_cover'],
            'first_dir'     =>  strval($arrFields['level_1_name']),
            'second_dir'    =>  strval($arrFields['level_2_name']),
            'edit_info'     =>  array(
                'first_category'    =>  $arrFields['fir_category'],
                'second_category'   =>  $arrFields['sec_category'],
                'tag'               =>  empty($arrFields['tag']) ? $arrOriginParam['tag'] : $arrFields['tag'],
                'thread_create_time'=>  intval($arrFields['thread_create_time']),
                'video_quality'     =>  $arrFields['video_quality'],
                'other_quality'     =>  $arrFields['other_quality'],
                'cover_quality'     =>  $arrFields['cover_quality'],
                'title_quality'     =>  $arrFields['title_quality'],
                'video_score'       =>  $arrFields['video_score'],
                'video_type'        =>  $arrFields['video_type'],
                'video_width'       =>  $arrFields['video_width'],
                'video_height'      =>  $arrFields['video_height'],
                'edit_result'       =>  $arrFields['edit_result'],
                'sharpness'         =>  $arrFields['sharpness'],
                'es_sharpness'      =>  $floatEsSharpness,
                'sound_quality'     =>  intval($arrFields['sound_quality']),
                'realtime_type'     =>  intval($arrFields['realtime_type']),
                'first_publish_time' => empty($arrFields['first_publish_time']) ? time() : intval($arrFields['first_publish_time']),
                'vulgar_type'      => intval($arrFields['vulgar_type']),
            ),
        );

        if (isset($arrOriginParam['video_score'])) {
            $arrRetData['edit_info']['video_score'] = $arrOriginParam['video_score'];
        }

        if (isset($arrOriginParam['reaudit_type'])) {
            $arrRetData['edit_info']['reaudit_type'] = $arrOriginParam['reaudit_type'];
        }

        if ($arrOriginParam['tag_type'] == 'new') {
            $arrRetData['edit_info']['new_fir_category']     = $arrOriginParam['fir_category'];
            $arrRetData['edit_info']['new_sec_category']     = $arrOriginParam['sec_category'];
            $arrRetData['edit_info']['new_tag']              = $arrOriginParam['tag'];
            $arrRetData['edit_info']['new_tag_category']     = Lib_Edit::_getNewTagCate($arrRetData['edit_info']['new_fir_category'],$arrRetData['edit_info']['new_sec_category'] ,$arrRetData['edit_info']['new_tag'] );
            $arrRetData['edit_info']['fir_category']     = '';
            $arrRetData['edit_info']['sec_category']     = '';
            $arrRetData['edit_info']['first_category']     = '';
            $arrRetData['edit_info']['second_category']     = '';
            $arrRetData['edit_info']['tag']              = '';
        } else if ($arrOriginParam['tag_type'] == 'old') {
            $arrRetData['edit_info']['new_fir_category']     = $arrFields['fir_category'];
            $arrRetData['edit_info']['new_sec_category']     = $arrFields['sec_category'];
            $arrRetData['edit_info']['new_tag']              = $arrFields['tag'];
            $arrRetData['edit_info']['fir_category']     = $arrOriginParam['fir_category'];
            $arrRetData['edit_info']['sec_category']     = $arrOriginParam['sec_category'];
            $arrRetData['edit_info']['first_category']     = $arrOriginParam['fir_category'];
            $arrRetData['edit_info']['second_category']     = $arrOriginParam['sec_category'];
            $arrRetData['edit_info']['tag']              = $arrOriginParam['tag'];
        }
            
        return $arrRetData;
    }
}
