<?php
/**
 * date: 2018-01-05 16:51:41
 * author : dongliang04
 **/
class Lib_Define_Music{
    const PAGE_SIZE = 20;

    const STATUS_ONLINE = 1;
    const STATUS_OFFLINE = 2;
    const STATUS_TEST = 3;


    const STATUS_MUSIC_OFFLINE = 4; //这里专门给nani_music_tag_rel用, 主要应用是当音乐下线之后把标签状态置为4

    // 音乐库 nani_video_music里面的type定义
    const TYPE_ORI_MUSIC   = 1;   // 用户上传的原声视频
    const TYPE_CUT_MUSIC   = 2;   // 裁剪的音乐
    const TYPE_BAIDU_MUSIC = 3;   // 百度音乐(forum_movideo.nani_video_music.type字段不包含该值)
    const TYPE_BM_STORAGE  = 4;   // 入nani库的百度音乐，(baidu music storage)

    //搜索类型定义 目前客户端传的是1
    const TYPE_SEARCH_BY_CLIENT    = 1; // 客户端搜索，tieba.baidu.com/c/f/nani/music/searchMusic
    const TYPE_SEARCH_BY_CLIENT_V2 = 2; // 客户端搜索，新版，tieba.baidu.com/c/f/nani/music/newSearch，by kangqinmou

    //音乐库的recommend_type定义
    const STATUS_RECOMMEND = 1; //得上热门推荐

    //每个搜索类型 对应的应该去库里面查哪种type
    public static $arrSearchType = array(
        self::TYPE_SEARCH_BY_CLIENT => array(
            self::TYPE_CUT_MUSIC,
            //self::TYPE_ORI_MUSIC,
        ),
        self::TYPE_SEARCH_BY_CLIENT_V2 => array(
            self::TYPE_CUT_MUSIC,
            self::TYPE_BM_STORAGE
        ),
    );


    const MUSIC_COLLECT_STATUS_HAS_COLLECTED = 1;
    const MUSIC_COLLECT_STATUS_HAS_NO_COLLECTED = 2;
}