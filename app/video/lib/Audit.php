<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file Audit.php
 * <AUTHOR>
 * @date 2016/10/27 11:21:01
 * @brief 视频审核库相关基础接口
 *
 **/
class Lib_Audit {

    const WRITE_REDIS_PREFIX = 'can_commit_odyssey_video_prefix_';
    const VDIEO_TABLE_PRE           = 'video_audit_'; // 审核库表前缀
    const VIDEO_TABLE_WORK           = 'works'; // 作品表
    const VIDEO_TABLE_UP         = 'videoup_info'; // 作者信息表
    const NANI_THREAD_REDIS_KEY     = 'nani_thread_redis_key_queue';
    const WORKS_SHIELD_WORDS_WORDLIST_NAME = 'tb_wordlist_redis_video_works_shield_words_switch';
    const AUDIT_PLAT_APP_ID = '25980668';

    private static $_arrHighQualityAuthor = array(1, 3, 4, 6, 8);

    // 数据库字段列表
    private static $dbParam = array(
        'id',
        'forum_id',
        'user_id',
        'thread_id',
        'thread_type',
        'title',
        'create_time',
        'duration',
        'video_url',
        'video_md5',
        'video_type',
        'video_cover',
        'video_name',
        'video_status',
        'audit_status',
        'delogo_status',
        'op_uid',
        'op_time',
        'op_uname',
        'weekkey',
        'base_type',
        'is_god',
        'status_change',
        'delete_type',
        'ext_param',
        'frame_status',
        'audit_sequence',
        'xiaoying_index',
        'title_base64',
        'forum_name',
        'user_name',
        'post_id',
        'has_manager',
        'video_width',
        'video_height',
        'url_thread_source',
        'audit_section',
        'ext_spread',
        'topic_id',
        'delete_reason',
        'audit_flag',
        'video_log_id',
        'inspect_id',
        'nid',
        'odyssey_message',
        // 下面开始给works用的
        'audit_key',
        'work_status',
        'status_msg',
        'trancode_info',
        'priority',
    );
    private static $_arrUservideoExt = array(
        'video_url',
        'video_width',
        'video_height',
        'video_cover',
        'title_base64',
        'video_length',
        'duration',
        'video_md5',
    );
    /**
     * 插入视频到审核数据库
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function deleteVideoInfo($arrInput) {
    }

    /**
     * 从审核数据库获取数据
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function selectVideoInfo($arrInput) {

        $page_num       = (isset($arrInput['pn']) && $arrInput['pn'] > 0) ? $arrInput['pn'] : 1;
        $return_num     = (isset($arrInput['rn']) && $arrInput['rn'] > 0) ? $arrInput['rn'] : 100;
        $offset_num     = (isset($arrInput['offset']) && $arrInput['offset'] > 0) ? $arrInput['offset'] : 0;
        $arrCond        = $arrInput['cond'];
        $strAppend      = strval($arrInput['append']);
        $bolForUpdate   = isset($arrInput['for_update']) && $arrInput['for_update'];

        $leap = 0;
        foreach ($arrCond as $key => $value) {
            if ($key == 'start_time' || $key == 'end_time' || $key == 'start_optime' || $key == 'end_optime' || $key == 'pn' || $key == 'rn' || $key == 'offset') {
                continue;
            }
            if (in_array($key, self::$dbParam) && $leap == 0) {
                $leap = 1;
                if ($key != 'nid' && is_numeric($value)) {
                    $ext_fields = $key . " = " . $value;
                } else {
                    $ext_fields = $key . " = '" . $value . "'";
                }
                continue;
            }
            if (in_array($key, self::$dbParam)) {
                if ($key != 'nid' && is_numeric($value)) {
                    $ext_fields .=  " and " . $key . " = " . $value;
                } else {
                    $ext_fields .=  " and " . $key . " = '" . $value . "'";
                }
            }
        }
        if (isset($arrInput['cond']['start_time']) && isset($arrInput['cond']['end_time'])) {
            if ($leap == 0) {
                $ext_fields     = "create_time > " . $arrInput['cond']['start_time'] . " and create_time < " . $arrInput['cond']['end_time'];
            } else {
                $ext_fields     .= " and create_time > " . $arrInput['cond']['start_time'] . " and create_time < " . $arrInput['cond']['end_time'];
            }
        }
        if (isset($arrInput['cond']['start_optime']) && isset($arrInput['cond']['end_optime'])) {
            if ($leap == 0) {
                $ext_fields     = "op_time > " . $arrInput['cond']['start_optime'] . " and op_time < " . $arrInput['cond']['end_optime'];
            } else {
                $ext_fields     .= " and op_time > " . $arrInput['cond']['start_optime'] . " and op_time < " . $arrInput['cond']['end_optime'];
            }
        }

        // 数据列表
        $arrRet = array();

        // 获取表名
        if (isset($arrInput['weekkey'])) {
            $arrList = array(
                0 => $arrInput['weekkey'],
            );
        } else {
            $arrList = Lib_Audit::getTableList($arrInput['cond']);
        }

        // check the validation of the table list
        if (isset($arrList['errno']) && intval($arrList['errno']) === Tieba_Errcode::ERR_PARAM_ERROR) {
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_SUCCESS, array());
        }

        foreach ($arrList as $strPartition) {
            $tableName = self::VDIEO_TABLE_PRE . $strPartition;

            // 查询数据
            $arrGetDbInput = array(
                'function'          => $bolForUpdate ? 'selectVideoInfoForUpdate' : 'selectVideoInfo',
                'table_name'        => $tableName,
                'pn'                => ($page_num - 1) * $return_num + $offset_num,
                'rn'                => $return_num,
            );
            if (isset($ext_fields)) {
                $arrGetDbInput['ext_fields'] = $ext_fields;
            } else {
                // 如果没有设置查询条件，那么这里给where一个永真条件做兼容
                $arrGetDbInput['ext_fields'] = '1 = 1';
            }
            if( !empty($strAppend) ){
                $arrGetDbInput['ext_fields'] .= $strAppend;
            }
            $arrGetDbOutput = Dl_Audit_Audit::execSql($arrGetDbInput);
            if (!$arrGetDbOutput || $arrGetDbOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call db fail, input:['.serialize($arrGetDbInput).'],output:['.serialize($arrGetDbOutput).']');
                return Lib_Audit::arrRet($arrGetDbOutput['errno']);
            }

            foreach ($arrGetDbOutput['results'][0] as $value) {
                $arrRet[] = $value;
            }
        }
        return Lib_Audit::arrRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }

    /**
     * 更新视频数据到审核数据库
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function updateVideoInfo($arrInput) {
        // 设置字段信息
        $arrField   = $arrInput['field'];
        // 设置条件信息
        $arrCond    = $arrInput['cond'];
        // 必要入参检测
        if ((!isset($arrInput['create_time']) || $arrInput['create_time'] <= 0 ) && !isset($arrInput['table_name'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::arrRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if( isset($arrInput['create_time']) && $arrInput['create_time']< 1440345600){
            Bingo_Log::warning("create_time earlier than 20150824");
            return self::arrRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 整理各个更新字段和值
        $leap = 0;
        foreach ($arrField as $key => $value) {
            if (in_array($key, self::$dbParam) && $leap == 0) {
                $leap = 1;
                if (is_numeric($value)) {
                    $field = $key . " = " . $value;
                } else {
                    $field = $key . " = '" . $value . "'";
                }
                continue;
            }
            if (in_array($key, self::$dbParam)) {
                if (is_numeric($value)) {
                    $field .=  ", " . $key . " = " . $value;
                } else {
                    $field .=  ", " . $key . " = '" . $value . "'";
                }
            }
        }

        // 整理各个条件字段和值
        $leap = 0;
        foreach ($arrCond as $key => $value) {
            if (in_array($key, self::$dbParam) && $leap == 0) {
                $leap = 1;
                if ($key != 'nid' && is_numeric($value)) {
                    $cond = $key . " = " . $value;
                } else {
                    $cond = $key . " = '" . $value . "'";
                }
                continue;
            }
            if (in_array($key, self::$dbParam)) {
                if ($key != 'nid' && is_numeric($value)) {
                    $cond .=  " and " . $key . " = " . $value;
                } else {
                    $cond .=  " and " . $key . " = '" . $value . "'";
                }
            }
        }

        // 获取表名
        if (isset($arrInput['table_name'])) {
            $tableName = $arrInput['table_name'];
        } else {
            $strPartition = self::getThatDayMonday($arrInput['create_time']);
            $tableName = self::VDIEO_TABLE_PRE . $strPartition;
        }

        $arrSetDbInput = array(
            'function'          => 'updateVideoInfo',
            'table_name'        => $tableName,
            'field'             => $field,
            'cond'              => $cond,
        );
        $arrSetDbOutput = Dl_Audit_Audit::execSql($arrSetDbInput);
        if (!$arrSetDbOutput || $arrSetDbOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrSetDbInput).'],output:['.serialize($arrSetDbOutput).']');
            return self::arrRet($arrSetDbOutput['errno']);
        }

        return self::arrRet(Tieba_Errcode::ERR_SUCCESS);
    }
    /**
     * [checkAddThreadInput description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function checkAddThreadInput($arrInput){
        $arrParam = array(
            'title',
            //'forum_id',  //201705为支持多吧发帖和个人中心，forum_id 不是必须
            'thread_type',
            'user_id',
            'ext_attr',
            'user_ip',
            'user_name',
            'forum_name',
            'create_time',
        );
        foreach ($arrParam as $param) {
            if (!isset($arrInput[$param]) || $arrInput['user_id'] <= 0) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
        }
        if(isset($arrInput['forum_id']) && 0 == $arrInput['forum_id'] && (!isset($arrInput['is_multi_forum']) || empty($arrInput['is_multi_forum']))){
            Bingo_Log::warning("forum_id is 0 and is_multi_forum also null or 0. [".serialize($arrInput)."]");
            return false;
        }
        if(!isset($arrInput['forum_id']) && !isset($arrInput['is_multi_forum']) && empty($arrInput['v_forum_ids'])){
            Bingo_Log::warning("forum_id  v_forum_ids is_multi_forum all null. [".serialize($arrInput)."]");
            return false;
        }

        $arrVideoExtAttr = Lib_Audit::getVideoInfo($arrInput);

        if (!isset($arrVideoExtAttr['video_type']) || !isset($arrVideoExtAttr['video_duration']) || !isset($arrVideoExtAttr['video_url']) || !isset($arrVideoExtAttr['video_md5']) || !isset($arrVideoExtAttr['thumbnail_url'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrVideoExtAttr)."]");
            return false;
        }
        return true;
    }
    /**
     * [checkRepeatRecord description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function checkRepeatRecord($arrInput){
        $bolRepeat = false;
        $intVideoLogId = (int)$arrInput['video_log_id'];
        $intWeekKey = self::getThatDayMonday($arrInput['create_time']);
        if($intVideoLogId > 0){
            $arrDBInput = array(
                'cond' => array(
                    'video_log_id'  => $intVideoLogId,
                ),
                'weekkey' => $intWeekKey,
            );
            $arrDBOutput = Lib_Audit::selectVideoInfo($arrDBInput);
            $strLog = sprintf("call Lib_Audit::selectVideoInfo failed  video_log_id[%s] weekkey[%s] input[%s] output[%s]", $intVideoLogId, $intWeekKey, serialize($arrDBInput), serialize($arrDBOutput));
            if(false === $arrDBOutput ){
                Bingo_Log::fatal($strLog);
                //return $bolRepeat;
            }elseif (Tieba_Errcode::ERR_SUCCESS !== $arrDBOutput['errno']){
                Bingo_Log::warning($strLog);
                //return $bolRepeat;
            }
            if(!empty($arrDBOutput['data'])){
                $bolRepeat = true;
            }
        }
        return $bolRepeat;
    }

    /**
     * @param $arrInput
     * @return int
     */
    public static function getInitAuditStatus($arrInput)
    {
        $intVideoType = (int)$arrInput['video_type'];
        //$intUserId = (int)$arrInput['user_id'];
        return isset(Util_Const::$_arrVTypeStatusMap[$intVideoType]) ? Util_Const::$_arrVTypeStatusMap[$intVideoType] : Util_Const::$_intDefaultAuditStatus;
    }

    public static function getShieldWordsSwitch($uid){
        $handleWordServer = Wordserver_Wordlist::factory();
        $strTableName     = self::WORKS_SHIELD_WORDS_WORDLIST_NAME;
        $keySwitch = 'shield_words_switch';
        $keyUid = $uid;
        $arrInput = array($keySwitch,$keyUid);
        $arrOutput = $handleWordServer->getValueByKeys($arrInput, $strTableName);
        if (!$arrOutput || $arrOutput['err_no'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf("call wordlist::getValueByKeys failed! input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return false;
        }
        if($arrOutput[$keySwitch] == 1 || $arrOutput[$keyUid] == 1){
            Bingo_Log::notice(sprintf("call wordlist::getValueByKeys success! input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return true;
        }

        return false;
    }

    /**
     * 获取写库字段， 并填充一些基本信息进来
     * @param  [type] &$arrInput [description]
     * @return [type]            [description]
     */
    public static function transAddThreadInput(&$arrInput){
        $arrVideoExtAttr = $arrInput['ext_attr'];
        $arrVideoInfo = Lib_Audit::getVideoInfo($arrInput);
        $arrInput['video_type']   = (int)$arrVideoInfo['video_type'];
        $intVideoType = $arrInput['video_type'];
        $arrInput['audit_status'] = Lib_Audit::getInitAuditStatus($arrInput);
        $strDeleteReason = '';
        $isworks=0;
        foreach($arrVideoExtAttr as $ext_attr){
            if ($ext_attr['key'] == 'works_info') {
                $arrWorkInfo = $ext_attr['value'];
                $isworks = isset($arrWorkInfo['is_works']) ? $arrWorkInfo['is_works'] : 0;
                break;
            }
        }
        Bingo_Log::warning('transAddThreadInput isworks:'.$isworks);
        //测试人员or开关开启才走命中屏蔽词流程
        $bolShieldWordsSwitch = self::getShieldWordsSwitch($arrInput['user_id']);
        if($bolShieldWordsSwitch && ($intVideoType == Molib_Util_Video::TEMP_VIDEO_WORKS_FROM_PC || $isworks) && isset($arrInput['shield_words']) && !empty($arrInput['shield_words']) ){
            //记录命中的屏蔽词，在物料回调流程中更改audit_status为8（命中屏蔽词跳过审核平台直接进入编辑平台）
            $strDeleteReason = '命中屏蔽词:'.strval($arrInput['shield_words']);
            if(!empty($arrVideoInfo['delete_reason'])){
                $strDeleteReason = strval($arrVideoInfo['delete_reason']).' , '.$strDeleteReason;
            }
            Bingo_Log::notice('transAddThreadInput,deleteReason='.$strDeleteReason);
        }
        $arrInput['has_manager']  = (int)Util_Perm::getHasManager($arrInput);
        $arrInput['video_name']   = Lib_Audit::getVideoName($arrVideoInfo);
        $arrInput['is_god']       = (0 < (int)Util_User::getAuthorGod($arrInput));
        $arrInput['create_time']  = (isset($arrInput['create_time']) && $arrInput['create_time'] > 0) ? $arrInput['create_time'] : time();
        $arrInput['video_status'] = (isset($arrInput['video_status']) && $arrInput['video_status'] > 0) ? $arrInput['video_status'] : 0;

        $intAuditSection = Util_Const::AUDIT_SECTION_NORMAL;

        $strContent = !empty($arrInput['utf8_ori_content']) ? $arrInput['utf8_ori_content'] : $arrInput['ori_content'];
        $arrThreadReq = array (
            'product_private_key'       => 'special_pro', // 这个参数表示免UEG策略
            'vcode_free_gate'           => true,
            'cmd_no'                    => 20,
            'title'                     => $arrInput['title'],
            'title_base64'              => base64_encode($arrInput['title']),
            'content'                   => $strContent,
            'anonymous'                 => $arrInput['anonymous'],
            'forum_id'                  => isset($arrInput['forum_id']) ? $arrInput['forum_id'] : 0,
            'is_multi_forum'            => isset($arrInput['is_multi_forum']) ? $arrInput['is_multi_forum'] : 0, // 201705为多吧发帖和个人中心
            'v_forum_ids'               => isset($arrInput['v_forum_ids']) ? $arrInput['v_forum_ids'] : array(), // 201705为多吧发帖和个人中心
            'forum_name'                => strval($arrInput['forum_name']),
            'thread_type'               => intval($arrInput['thread_type']),
            'floor_num'                 => intval($arrInput['floor_num']),
            'quote_id'                  => intval($arrInput['quote_id']),
            'repostid'                  => intval($arrInput['repostid']),
            'product_client_type'       => $arrInput['product_client_type'],
            'is_ntitle'                 => intval($arrInput['is_ntitle']),
            'phone_type'                => $arrInput['phone_type'],
            'client_version'            => isset($arrInput['client_version']) ? $arrInput['client_version'] : '',
            'open_id'                   => intval($arrInput['open_id']),
            'user_ip'                   => $arrInput['user_ip'],
            'user_id'                   => intval($arrInput['user_id']),
            'user_name'                 => strval($arrInput['user_name']),
            'during_time'               => intval($arrInput['during_time']),
            'ptype'                     => intval($arrInput['ptype']),
            'is_bub'                    => intval($arrInput['is_bub']),
            'group_id'                  => intval($arrInput['group_id']),
            'start_time'                => intval($arrInput['start_time']),
            'is_god'                    => intval($arrInput['is_god']),
            'xiaoying_index'            => isset($arrInput['xiaoying_index']) ? $arrInput['xiaoying_index'] : '',
            'thread_id'                 => (!isset($arrInput['thread_id']) || $arrInput['thread_id'] <= 0) ? 0 : intval($arrInput['thread_id']),
            'post_id'                   => (!isset($arrInput['post_id']) || $arrInput['post_id'] <= 0) ? 0 : intval($arrInput['post_id']),
            'video_type'                => intval($arrInput['video_type']),
            'audit_status'              => intval($arrInput['audit_status']),
            'has_manager'               => intval($arrInput['has_manager']),
            'create_time'               => intval($arrInput['create_time']),
            'video_name'                => strval($arrInput['video_name']),
            'video_status'              => intval($arrInput['video_status']),
            'is_idl_thumbnail_url'      => 0,           //新加字段标记是否更换过封面
            'base_type'                 => 0,
            //'audit_sequence'            => Util_Const::AUDIT_BEFORE_POST,
            'video_log_id'              => intval($arrVideoInfo['video_log_id']),
            'is_hide'                   => intval($arrInput['is_hide']),
            'audit_section'             => $intAuditSection,
            'ui_trans_params'           => $arrInput['ui_trans_params'], // 透传nmq字段
            'pro_zone'                  => intval($arrInput['pro_zone']), // 透传nmq字段
            'abrakadabra'               => strval($arrInput['abrakadabra']),
            'thread_mask_flag'          => intval($arrInput['thread_mask_flag']),
        );
        if(!empty($strDeleteReason)){
            $arrThreadReq['delete_reason'] = $strDeleteReason;
        }


        if(Util_Const::AUDIT_STATUS_PASS == $arrInput['audit_status']
            || Util_Const::AUDIT_STATUS_DELETE == $arrInput['audit_status']){
            $arrVideoInfo['auditing'] = Util_Const::VIDEO_INFO_AUDITED;
            $arrVideoExtAttr = Lib_Audit::setVideoInfo($arrVideoExtAttr,$arrVideoInfo,'video_info');
        }
        // 设置帖子扩展属性并获取视频扩展属性
        $arrThreadReq['ext_attr'] = $arrInput['ext_attr'] = $arrVideoExtAttr;



        // 将发帖必须参数整理成json格式存储到数据库的扩展字段中，以备不时之需
        //$arrThreadReq['ext_param'] = Bingo_String::array2json($arrThreadReq);
        $arrThreadReq['ext_param'] = json_encode($arrThreadReq);
        $arrThreadReq['video_ext_attr']  = $arrInput['video_ext_attr']  = $arrVideoExtAttr;
        $arrThreadReq['ui_trans_params'] = $arrInput['ui_trans_params'];

        // 下面几个字段已经在 ext_param的ext_attr里面了
        $arrThreadReq['duration']          = intval($arrVideoInfo['video_duration']);
        $arrThreadReq['video_length']      = (isset($arrVideoInfo['video_length']) && $arrVideoInfo['video_length'] > 0 ? (int)$arrVideoInfo['video_length'] : (int)$arrVideoInfo['video_size']);
        $arrThreadReq['video_url']         = $arrVideoInfo['video_url'];
        $arrThreadReq['video_md5']         = $arrVideoInfo['video_md5'];
        $arrThreadReq['video_cover']       = $arrVideoInfo['thumbnail_url'];
        $arrThreadReq['url_thread_source'] = intval($arrVideoInfo['video_from']);
        $arrThreadReq['video_height']      = intval($arrVideoInfo['video_height']);
        $arrThreadReq['video_width']       = intval($arrVideoInfo['video_width']);
        $arrThreadReq['is_private']        = (int)$arrVideoInfo['is_private'];
        $arrThreadReq['video_log_id']      = (int)$arrVideoInfo['video_log_id'];
        $arrThreadReq['topic_id']          = 0;// 已经废弃
        $arrThreadReq['ext_spread']        = '';// 已经废弃
        return $arrThreadReq;
    }
    /**
     * [buildOldInput description]
     * @param  [type] $arrInput     [description]
     * @param  [type] $arrVideoInfo [description]
     * @return [type]               [description]
     */
    public static function buildOldInput($arrInput, $arrVideoInfo){
        $arrServiceInput = array(
            "user_id"    => $arrInput['user_id'],
            "user_name"  => $arrInput['user_name'],
            "forum_id"   => $arrInput['forum_id'],
            "forum_name" => $arrInput['forum_name'],
            "thread_id"  => $arrInput['thread_id'],
            "post_id"    => $arrInput['post_id'],
            "title"      => $arrInput['title'],
            "video_md5"  => $arrVideoInfo['video_md5'],
            "video_url"  => $arrVideoInfo['video_url'],
            "video_duration" => $arrVideoInfo['video_duration'],
            "thumbnail_url"  => $arrVideoInfo['thumbnail_url'],
            "is_thread"      => $arrInput['_cmd'] == "threadCommit" ? 1 : 0,
            //支持多吧发贴start
            "is_multi_forum" => intval($arrInput['is_multi_forum']),
            "v_forum_ids"    => isset($arrInput['v_forum_ids']) ? $arrInput['v_forum_ids'] : array(),
            //支持多吧发贴end
        );
        return $arrServiceInput;
    }

    /**
     * @brief  get the video info by log id
     * @param  $arrInput
     * @return array
     */
    public static function getVideoByLogId($arrInput) {
        $intVideoLogId = intval($arrInput['video_log_id']);
        $strPartition  = self::getThatDayMonday($arrInput['create_time']);
        $strTableName  = self::VDIEO_TABLE_PRE . $strPartition;

        $arrGetDbInput = array(
            'function'   => 'getVideoInfoByVLogId',
            'table_name' => $strTableName,
            'cond'       => " video_log_id = $intVideoLogId "
        );
        $arrGetDbOutput = Dl_Audit_Audit::execSql($arrGetDbInput);
        if (!$arrGetDbOutput || $arrGetDbOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrGetDbInput).'],output:['.serialize($arrGetDbOutput).']');
            return Lib_Audit::arrRet($arrGetDbOutput['errno']);
        }
        if (!empty($arrGetDbOutput['results'][0])) {
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_SUCCESS, $arrGetDbOutput['results'][0][0]);
        }
        return Lib_Audit::arrRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * [insertVideoInfoNew description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function insertVideoInfoNew($arrInput){
        // 获取表名
        $strWeekkey    = self::getThatDayMonday($arrInput['create_time']);

        // 参数准备
        $intVideoType = $arrInput['video_type'];
        //获取作者类型
        $userId = isset($arrInput['user_id']) ? $arrInput['user_id'] : 0;
        $arrInput['priority'] = self::getPriority($userId, $intVideoType);

        // 构造写audit db的各个字段数值
        $arrAuditDbInput = array(
            'function'         => 'insertVideoInfo',
            'table_name'       => self::VDIEO_TABLE_PRE . $strWeekkey,
            'weekkey'          => intval($strWeekkey),
            'isReturnInsertId' => 1,
        );

        foreach (self::$dbParam as $k => $field) {
            if (isset($arrInput[$field])) {
                $arrAuditDbInput[$field] = $arrInput[$field];
            }
        }
        if (!isset($arrAuditDbInput['ext_param'])) {
            $arrAuditDbInput['ext_param'] = '';
        }
        Bingo_Log::warning('ext_param:'.json_encode($arrAuditDbInput['ext_param']));
        if (!isset($arrAuditDbInput['ext_spread'])) {
            $arrAuditDbInput['ext_spread'] = '';
        }

        $arrAuditDbInput['op_uid']   = 0;
        $arrAuditDbInput['op_uname'] = '';
        $arrAuditDbInput['op_time']  = 0;

        // 这里有个奇怪的逻辑，为啥会有在发帖接口里面带过来的delete_reason，可以注意一下
        if (!empty($arrAuditDbInput['delete_reason'])) {
            $arrAuditDbInput['function'] = 'insertVideoAuditInfo';
        }
        // end audit db

        $arrRet = array();
        $intErrno = Tieba_Errcode::ERR_SUCCESS;
        $intVideoAuditId = 0;
        Bingo_Log::warning('arrAuditDbInput input'.json_encode($arrAuditDbInput));
        // 开启事务
        do {
            if (Dl_Audit_Audit::startTransaction()) {
                // 写数据到video_audit_xxxxxxxx表格中
                $arrAuditDbOutput = Dl_Audit_Audit::execSql($arrAuditDbInput);
                Bingo_Log::warning(sprintf("debug info|add video thread|write data to video_audit_xxxxxxx table, input[%s] output[%s]", json_encode($arrAuditDbInput), json_encode($arrAuditDbOutput)));

                if ($arrAuditDbOutput == false || $arrAuditDbOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::fatal('call db fail, input:[' . serialize($arrAuditDbInput) . '],output:[' . serialize($arrAuditDbOutput) . ']');
                    $intErrno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }

                // 构造审核表的返回信息
                $intVideoAuditId = intval($arrAuditDbOutput['results'][0]);
                $arrRet['id']        = intval($arrAuditDbOutput['results'][0]);
                $arrRet['video_url'] = $arrAuditDbInput['video_url'];

                // 判断是否是作品，是作品写作品表格
                $isworks = 0;
                $ext_param = $arrAuditDbInput['ext_param'];
                $ext_param = json_decode($ext_param,true);//字符串转数组
                Bingo_Log::warning("insertVideoInfoNew ext_param input".json_encode($ext_param));
                foreach ($ext_param['ext_attr'] as $value) {
                    if ($value['key'] == 'works_info') {
                        $arrWorkInfo = $value['value'];
                        $isworks = isset($arrWorkInfo['is_works']) ? $arrWorkInfo['is_works'] : 0;
                        break;
                    }
                }
                Bingo_Log::warning("insertVideoInfoNew isworks:".$isworks);
                if ($intVideoType == Molib_Util_Video::TEMP_VIDEO_WORKS_FROM_PC || $isworks) {
                    $arrWorkDbInput = array(
                        'function'         => 'insertWorkInfo',
                        'table_name'       => self::VIDEO_TABLE_WORK,
                        'audit_key'        => $strWeekkey . '_' . $arrRet['id'],
                        'work_status'      => Util_Const::WORK_STATUS_AUDITINT,
                        'status_msg'       => '我们会尽快完成审核，审核结果我们会通过消息告知您。',
                        'user_id'          => intval($arrAuditDbInput['user_id']),
                        'forum_id'         => intval($arrAuditDbInput['forum_id']),
                        'create_time'      => intval($arrAuditDbInput['create_time']),
                        'isReturnInsertId' => 1,
                    );

                    $arrWorkDbOutput = Dl_Audit_Audit::execSql($arrWorkDbInput);
                    Bingo_Log::warning(sprintf("debug info|add work thread|write data to works, input[%s] output[%s]", json_encode($arrWorkDbInput), json_encode($arrWorkDbOutput)));

                    if ($arrWorkDbOutput == false || $arrWorkDbOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                        Bingo_Log::warning('call db fail, input:[' . serialize($arrWorkDbInput) . '],output:[' . serialize($arrWorkDbOutput) . ']');
                        $intErrno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                        break;
                    }

                    $arrRet['work_id'] = intval($arrWorkDbOutput['results'][0]);
                }

                if (!Dl_Audit_Audit::commit()) {
                    Bingo_Log::fatal(sprintf("call dl_audit_audit commit failed!"));
                    $intErrno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }
            } else {
                Bingo_Log::fatal(sprintf("call dl_audit_audit start transaction failed! output: "));
                return self::arrRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
        } while (0);

        if ($intErrno != Tieba_Errcode::ERR_SUCCESS) {
            if (!Dl_Audit_Audit::rollback()) {
                Bingo_Log::fatal(sprintf("call dl_audit_audit commit failed!"));
            }

            return self::arrRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        /*
         * Nani逻辑，可下线
        if (Util_Const::$_arrNewAPPVType[$intVideoType]) {
            foreach (self::$_arrUservideoExt as $k => $field) {
                $arrUVExt[$field] = $arrInput[$field];
            }
            $arrUVExt['video_audit_id']   = $intVideoAuditId;
            $arrIntoDbInput['ext']        = serialize($arrUVExt);
            $arrIntoDbInput['is_private'] = isset($arrInput['is_private']) ? (int) $arrInput['is_private'] : Util_Const::$_intDefaultPrivate;
            $arrUserVideoOut              = Service_Uservideo_Uservideo::insertUservideo($arrIntoDbInput);
            if ($arrUserVideoOut == false || $arrUserVideoOut['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::fatal('call db fail, input:[' . serialize($arrIntoDbInput) . '],output:[' . serialize($arrUserVideoOut) . ']');
            }
        }
         */

        return self::arrRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }

    /**
     * [getPriority description]
     * @param  [int] $userId [作者uid]
     * @param  [int] $videoType [视频类型]
     * @return [int] $priority  [description]
     */
    public static function getPriority($userId, $videoType, $authorType = 0) {
        //引入作者:1、普通用户:2、原生作者:3、MCN:4 、搬运作者:5、内部作者:6、官方号:7、官方账号:8、同事账号:9、黑名单:10、热点作者:11、运营作者12
        $firstAuditAuthor = self::$_arrHighQualityAuthor;
        $videoTypePriorityMap = array(
            Molib_Util_Video::TEMP_VIDEO_UPLOAD_FROM_URL_SPIDER => 4,// URL视频抓取
            Molib_Util_Video::TEMP_VIDEO_UPLOAD_FROM_PC => 3,// pc上传
            Molib_Util_Video::TEMP_VIDEO_FROM_CLIENT_LOCAL => 2,// 用户本地上传视频
            Molib_Util_Video::TEMP_VIDEO_FROM_CLIENT_RECORD => 1,// 用户本地上传视频
        );
        $authorWeight = 100;//作者类型 权重
        $videoTypeWeight = 1;//视频类型 权重
        //获取视频类型优先级
        $videoTypePriority = isset($videoTypePriorityMap[$videoType]) ? $videoTypePriorityMap[$videoType] : 0;
        $authorPriority = 1;//作者类型优先级初始化
        if($userId <= 0){
            $priority = $authorPriority * $authorWeight + $videoTypePriority * $videoTypeWeight;
            return $priority;
        }
        //获取作者类型
        if ($authorType == 0) {
            $arrAuditDbInput = array(
                'uid'              => $userId,
                'function'         => 'selectvideoupInfo',
                'table_name'       => self::VIDEO_TABLE_UP,
            );
            $arrAuditDbOutput = Dl_Usertable_Usertable::execSql($arrAuditDbInput);
            if ($arrAuditDbOutput == false || $arrAuditDbOutput['errno'] != Tieba_Errcode::ERR_SUCCESS || empty($arrAuditDbOutput['results'])) {
                Bingo_Log::warning('call db fail, or success but results empty, input:[' . serialize($arrAuditDbInput) . '],output:[' . serialize($arrAuditDbOutput) . ']');
            }else{
                $authorType = isset($arrAuditDbOutput['results'][0][0]['author_type']) ? $arrAuditDbOutput['results'][0][0]['author_type'] : 0;
            }
        }

        if(in_array($authorType, $firstAuditAuthor)){
            $authorPriority = 2;
        }
        if($authorType == Util_Const::AUTHOR_TYPE_HOTSPOT){
            $authorPriority = 3;
        }
        $priority = $authorPriority * $authorWeight + $videoTypePriority * $videoTypeWeight;
        return $priority;
    }

    /**
     * [getVideoName description]
     * @param  [type] $arrVideoExtAttr [description]
     * @return [type]                  [description]
     */
    public static function getVideoName($arrVideoExtAttr){
        $arrFileName    = explode("/", $arrVideoExtAttr['video_url']);
        $strFileName    = end($arrFileName);
        $strTemp        = explode(".", $strFileName);
        $video_name     = $strTemp[0];
        return $video_name;
    }
    /**
     * [getVideoInfo description]
     * @param  [type] $arrInput [description]
     * @return array
     */
    public static function getVideoInfo($arrInput, $key = 'video_info')
    {
        // 整理扩展属性数据
        $arrVideoInfo = array();
        if (!empty($arrInput['ext_attr'])) {
            foreach ($arrInput['ext_attr'] as $value) {
                if ($value['key'] == $key) {
                    $arrVideoInfo = $value['value'];
                }
            }
        }

        // 针对不同的 ext_attr 格式做兼容
        if (!is_array($arrVideoInfo) && false !== unserialize($arrVideoInfo)) {
            $arrVideoInfo = unserialize($arrVideoInfo);
        }

        return $arrVideoInfo;
    }
    /**
     * [setVideoInfo description]
     * @param [type] $arrInput [description]
     * @param [type] $arrParam [description]
     * @param string $key      [description]
     * @return array
     */
    public static function setVideoInfo(&$arrExtAttr,$arrNew, $key = 'video_info'){
        // 整理扩展属性数据
        $arrVideoInfo = array();
        if(!empty($arrExtAttr)){
            foreach($arrExtAttr as $k => $value){
                if ($value['key'] == $key){
                    $arrExtAttr[$k]['value'] = $arrNew;
                }
            }
        }
        return $arrExtAttr;
    }
    /**
     * [transExtAttr description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function transExtAttr($arrInput){
        foreach($arrInput['ext_attr'] as $key => $value){
            if(is_string($value['value'])){
                $strTmp = unserialize($value['value']);
                if(false !== $strTmp){
                    $arrInput['ext_attr'][$key]['value'] = $strTmp;
                }
            }
        }
        return $arrInput;
    }
    /**
     * [geneAuditInput description]
     * @param  [type] $arrThreadInfoFromDB [description]
     * @return [type]                      [description]
     */
    public static function geneAuditInput($arrInput){

        $intAuditId    = (int)$arrInput['video_audit_id'];
        $intOpType     = (int)$arrInput['op_type'];
        $strTitle      = $arrInput['title'];
        $intCreateTime = (int)$arrInput['create_time'];
        $intWeekKey    = Lib_Audit::getThatDayMonday($intCreateTime);
        $intStatus     = Util_Const::$_arrOpStatusMap[$intOpType];
        // TODO : remove some other time start
        $intPush2Recom = (int)$arrInput['push_to_recom'];
        // TODO : remove some other time end
        $intUid        = (int)$arrInput['user_id'];

        $arrDBInput = array(
            'field'  => array(
                'title',
                'title_base64',
                'ext_param',
                'thread_id',
                'post_id',
                'video_type',
                'video_url',
                'video_cover',
                'ext_spread',
                'audit_section',
                'nid',
                'odyssey_message',
            ),
            'cond' => array(
                'id'  => $intAuditId,
            ),
            'weekkey' => $intWeekKey,
        );
        $arrDBOutput = Lib_Audit::selectVideoInfo($arrDBInput);
        Bingo_Log::warning("dh_repeat_".serialize($arrDBOutput));
        $strLog = sprintf("call Serivce_Audit_Audit::getVideoFromAudit failed input[%s] output[%s] id[%s] weekkey[%s]",serialize($arrDBInput), serialize($arrDBOutput), $intAuditId, $intWeekKey);
        if(false === $arrDBOutput ){
            Bingo_Log::fatal($strLog);
            return false;
        }elseif (Tieba_Errcode::ERR_SUCCESS !== $arrDBOutput['errno']|| empty($arrDBOutput['data'])){
            Bingo_Log::warning($strLog);
            return false;
        }
        $arrThreadInfoFromDB = $arrDBOutput['data'][0];
        //$arrThreadReq = Bingo_String::json2array($arrThreadInfoFromDB['ext_param'], true);
        $arrThreadReq = json_decode($arrThreadInfoFromDB['ext_param'], true);
        $arrOdysseyMessage = json_decode($arrThreadInfoFromDB['odyssey_message'], true);

        // 检查 ext_attr 中是否存在 come_from=shoubai_ugc，如果存在则添加 subapp 参数
        $bolHasShoubaiUgc = false;
        if (isset($arrThreadReq['ext_attr']) && is_array($arrThreadReq['ext_attr'])) {
            foreach ($arrThreadReq['ext_attr'] as $extAttr) {
                if (isset($extAttr['key'], $extAttr['value']) &&
                    $extAttr['key'] === 'come_from' && $extAttr['value'] === 'shoubai_ugc') {
                    $bolHasShoubaiUgc = true;
                    Bingo_Log::warning("geneAuditInput: Found shoubai_ugc signal, adding subapp parameter");
                    break;
                }
            }
        }

        if ($bolHasShoubaiUgc) {
            $arrThreadReq['subapp'] = 'shoubai_ugc';
        }

        if(empty($strTitle)){
            $strTitle = Util_Const::DEFAULT_TITLE;
        }
        $uname = Util_User::getUnameByUid($arrThreadInfoFromDB);
        $fname = Util_Forum::getFnameByFid($arrThreadInfoFromDB);
        $arrThreadReq['user_id']    = (int)$arrThreadInfoFromDB['user_id'];
        $arrThreadReq['user_name']  = $uname;
        $arrThreadReq['forum_name'] = $fname;
        $arrThreadReq['title']      = $strTitle;
        $arrThreadReq['thread_id']  = (int)$arrThreadInfoFromDB['thread_id'];
        $arrThreadReq['post_id']    = (int)$arrThreadInfoFromDB['post_id'];
        $arrThreadReq['predistributed_thread_id'] = $arrOdysseyMessage['predistributed_thread_id'];
        $arrThreadReq['odyssey_message'] = $arrOdysseyMessage;

        //$intHasPlay = 0;
        foreach($arrThreadReq['ext_attr'] as &$ext_attr){
            if('video_info' === $ext_attr['key']){
                $ext_attr['value']['auditing'] = Util_Const::VIDEO_INFO_AUDITED;
                $ext_attr['value']['ptid'] = intval($arrThreadReq['predistributed_thread_id']);
            }
        }

        $intVideoLogId = (int)$arrThreadInfoFromDB['video_log_id'];
        $intUid        = (int)$arrThreadInfoFromDB['user_id'];
        $arrUVDBInput  = array(
            'user_id'      => $intUid,
            'video_log_id' => $intVideoLogId,
        );
        $arrUVDBOutput = Service_Uservideo_Uservideo::getUserVideoInfo($arrUVDBInput);
        $strLog = sprintf("call Service_Uservideo_Uservideo::getUserVideoInfo failed vlogid[%s] weekkey[%s] input[%s] output[%s] ", $intAuditId, $intWeekKey,serialize($arrUVDBInput), serialize($arrUVDBOutput));
        if(false === $arrUVDBOutput ){
            Bingo_Log::fatal($strLog);
        }elseif (Tieba_Errcode::ERR_SUCCESS !== $arrUVDBOutput['errno']){
            Bingo_Log::warning($strLog);
        }
        $arrUserVideo = $arrUVDBOutput['data']['list'][0];
        if(!empty($arrUserVideo)){
            $arrUserVideo['audit_status'] = $intStatus;
            $arrThreadReq['is_hide'] = (int)$arrUserVideo['is_private'];
        }
        $arrInput['weekkey']       = $intWeekKey;
        $arrInput['op_time']       = time();
        $arrInput['status']        = $intStatus;
        $arrInput['ext_spread']    = $arrThreadInfoFromDB['ext_spread'];
        $arrInput['msg_type']      = 0;
        $arrInput['audit_section'] = $arrThreadInfoFromDB['audit_section'];
        $arrInput['video_type']    = (int)$arrThreadInfoFromDB['video_type'];
        $arrInput['is_private']    = (int)$arrUserVideo['is_private'];
        $arrVideoInfo              = Lib_Audit::getVideoInfo($arrThreadReq);
        $arrAllInput = array(
            'origin_input' => $arrInput,
            'thread_input' => $arrThreadReq,
            'video_info'   => $arrVideoInfo,
            'user_video'   => $arrUserVideo,
        );
        return $arrAllInput;
    }

    public static function genWorksThreadInput($arrInput) {
        $threadId = $arrInput['thread_id'];
        $editTitle = $arrInput['title'];
        $editCover = $arrInput['video_cover'];
        $intCreateTime = intval($arrInput['create_time']);
        $intWeekKey    = Lib_Audit::getThatDayMonday($intCreateTime);
        $arrDBInput = array(
            'field'  => array(
                'id',
                'title',
                'title_base64',
                'ext_param',
                'thread_id',
                'post_id',
                'video_type',
                'video_url',
                'video_cover',
                'ext_spread',
                'audit_section',
                'nid',
                'odyssey_message',
            ),
            'cond' => array(
                'thread_id'  => $threadId,
            ),
            'weekkey' => $intWeekKey,
        );
        $arrDBOutput = Lib_Audit::selectVideoInfo($arrDBInput);

        // 构造发帖到入参
        $strLog = sprintf("call Serivce_Audit_Audit::getVideoFromAudit failed input[%s] output[%s] thrad_id[%s] weekkey[%s]",serialize($arrDBInput), serialize($arrDBOutput), $threadId, $intWeekKey);
        if(false === $arrDBOutput ){
            Bingo_Log::fatal($strLog);
            return false;
        }elseif (Tieba_Errcode::ERR_SUCCESS !== $arrDBOutput['errno']|| empty($arrDBOutput['data'])){
            Bingo_Log::warning($strLog);
            return false;
        }

        $arrThreadInfoFromDB = $arrDBOutput['data'][0];
        $arrThreadReq = json_decode($arrThreadInfoFromDB['ext_param'], true);
        $arrOdysseyMessage = json_decode($arrThreadInfoFromDB['odyssey_message'], true);
        $strTitle = empty($editTitle) ? $arrThreadInfoFromDB['title'] : $editTitle;

        if(empty($strTitle)){
            $strTitle = Util_Const::DEFAULT_TITLE;
        }
        $uname = Util_User::getUnameByUid($arrThreadInfoFromDB);
        $fname = Util_Forum::getFnameByFid($arrThreadInfoFromDB);
        $arrThreadReq['user_name']  = $uname;
        $arrThreadReq['forum_name'] = $fname;
        $arrThreadReq['title']      = empty($editTitle) ? $arrThreadInfoFromDB['title'] : $strTitle;
        $arrThreadReq['thread_id']  = (int)$arrThreadInfoFromDB['thread_id'];
        $arrThreadReq['post_id']    = (int)$arrThreadInfoFromDB['post_id'];
        $arrThreadReq['predistributed_thread_id'] = $arrOdysseyMessage['predistributed_thread_id'];
        $arrThreadReq['odyssey_message'] = $arrOdysseyMessage;
        $arrThreadReq['weekkey'] = $intWeekKey;
        $arrThreadReq['video_audit_id'] = intval($arrThreadInfoFromDB['id']);
        $arrThreadReq['video_type'] = $arrThreadInfoFromDB['video_type'];

        foreach($arrThreadReq['ext_attr'] as &$ext_attr){
            if('video_info' === $ext_attr['key']){
                $ext_attr['value']['auditing'] = Util_Const::VIDEO_INFO_AUDITED;
                $ext_attr['value']['thumbnail_url'] = empty($editCover) ? $ext_attr['value']['video_cover'] : $editCover;
            }
            if ('works_info' == $ext_attr['key'] && isset($ext_attr['value']['tag_list'])) {
                unset($ext_attr['value']['tag_list']);
            }
        }

        $intVideoLogId = (int)$arrThreadInfoFromDB['video_log_id'];
        $intUid        = (int)$arrThreadInfoFromDB['user_id'];
        $arrUVDBInput  = array(
            'user_id'      => $intUid,
            'video_log_id' => $intVideoLogId,
        );
        $arrUVDBOutput = Service_Uservideo_Uservideo::getUserVideoInfo($arrUVDBInput);
        $strLog = sprintf("call Service_Uservideo_Uservideo::getUserVideoInfo failed thread_id[%s] weekkey[%s] input[%s] output[%s] ", $threadId, $intWeekKey,serialize($arrUVDBInput), serialize($arrUVDBOutput));
        if(false === $arrUVDBOutput ){
            Bingo_Log::fatal($strLog);
        }elseif (Tieba_Errcode::ERR_SUCCESS !== $arrUVDBOutput['errno']){
            Bingo_Log::warning($strLog);
        }
        $arrUserVideo = $arrUVDBOutput['data']['list'][0];
        if(!empty($arrUserVideo)){
            $arrThreadReq['is_hide'] = (int)$arrUserVideo['is_private'];
        }
        return $arrThreadReq;
    }
    /**
     * [writeBackVideo description]
     * @param  [type] $arrInput     [description]
     * @param  [type] $arrVideoInfo [description]
     * @return [type]               [description]
     */
    public static function writeBackVideo($arrInput, $arrThreadReq, $arrUserVideo){
        Bingo_Timer::start(__FUNCTION__);

        // 12.4 发视频作品贴恢复贴时 帖子ID必须有(预设的)
        if(Molib_Util_Video::TEMP_VIDEO_WORKS_FROM_PC != $arrInput['video_type'] && (Util_Const::AUDIT_OP_TYPE_REFUSE  == $arrInput['op_type'] || Util_Const::AUDIT_OP_TYPE_RECOVER == $arrInput['op_type'])) {
            // 删帖 恢复， 不更新 thread_id  post_id 恢复和删除原生作品不更新审核库（不沿用）
            $arrInput['thread_id'] = -1;
            $arrInput['post'] = -1;
        }
        $intWeekKey = (int)$arrInput['weekkey'];
        if (isset($arrThreadReq['odyssey_message'])) {
            unset($arrThreadReq['odyssey_message']);
        }
        $strThreadReq = json_encode($arrThreadReq);
        $arrUpdateDBInput = array(
            'field' => array(
                'audit_status' => $arrInput['status'],
                'op_time'      => $arrInput['op_time'],
                'op_uid'       => $arrInput['op_uid'],
                'op_uname'     => $arrInput['op_uname'],
            ),
            'cond'   => array(
                'id' => $arrInput['video_audit_id'],
            ),
            'table_name' => Lib_Audit::VDIEO_TABLE_PRE . $intWeekKey,
        );
        if(isset($arrInput['status_change'])){
            $arrUpdateDBInput['field']['status_change'] = $arrInput['status_change'];
        }
        if(isset($arrInput['base_type'])){
            $arrUpdateDBInput['field']['base_type'] = $arrInput['base_type'];
        }
        if(isset($arrInput['delete_reason'])){
            $arrUpdateDBInput['field']['delete_reason'] = $arrInput['delete_reason'];
        }
        if(isset($arrInput['audit_flag'])){
            $arrUpdateDBInput['field']['audit_flag'] = $arrInput['audit_flag'];
        }
        if($arrInput['thread_id'] > 0){
            $arrUpdateDBInput['field']['thread_id'] = $arrInput['thread_id'];
        }
        if($arrInput['post_id'] > 0 ){
            $arrUpdateDBInput['field']['post_id'] = $arrInput['post_id'];
        }
        if (Util_Const::AUDIT_OP_TYPE_REFUSE != $arrInput['op_type']){
            $arrUpdateDBInput['field']['ext_param'] =  mysql_escape_string($strThreadReq);
        }

        $arrRet = Lib_Audit::updateVideoInfo($arrUpdateDBInput);
        $strLog = sprintf("call Lib_Audit:updateVideoInfo select db failed, input:[%s] output:[%s]",serialize($arrUpdateDBInput),serialize($arrRet));
        if (false === $arrRet){
            Bingo_Log::fatal($strLog);
            return false;
        } else if($arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning($strLog);
            return false;
        }

        // 更新分享库
        if ($arrInput['op_type'] == Util_Const::AUDIT_OP_TYPE_REFUSE && !empty($arrInput['video_log_id'])) {
            $arrParam = array(
                'video_share_id' => intval($arrInput['video_log_id']),
            );
            $arrRet = Service_Share_Thread::deleteVideoShareThread($arrParam);
        }

        // 更新用户视频库
        if (!empty($arrUserVideo)) {
            $arrUserInput = array(
                'user_id'      => $arrUserVideo['user_id'],
                'id'           => $arrUserVideo['id'],
                'audit_status' => $arrInput['status'],
            );

            if ($arrInput['thread_id'] > 0) {
                $arrUserInput['thread_id'] = (int)$arrInput['thread_id'];
            }
            $arrRet = Service_Uservideo_Uservideo::writeBackUservideo($arrUserInput);
            $strLog = sprintf("call Service_Uservideo_Uservideo:writeBackUservideo select db failed, input:[%s] output:[%s]",serialize($arrUserInput),serialize($arrRet));
            if (false === $arrRet) {
                Bingo_Log::fatal($strLog);
            } else if ($arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning($strLog);
            }
        }

        Bingo_Timer::end(__FUNCTION__);
        return true;
    }
    /**
     * [postPGCVideo description]
     * @param  [type] $arrInput     [description]
     * @param  [type] $arrVideoInfo [description]
     * @return [type]               [description]
     */
    public static function postPGCVideo($arrInput, $arrVideoInfo){
        Bingo_Timer::start(__FUNCTION__);
        $intUid = (int)$arrInput['user_id'];
        $intTid = (int)$arrInput['thread_id'];
        $intVideoType = (int)$arrVideoInfo['video_type'];
        $intChannelId = (int)$arrVideoInfo['channel_id'];
        $arrPGCInput  = Lib_Audit::genePGCInput($arrInput, $arrVideoInfo);
        $bolIsPGCUser = Util_User::getIsPGCUser($intUid);

        if(Molib_Util_Video::TEMP_VIDEO_FROM_PGC === $intVideoType
            || (Molib_Util_Video::TEMP_VIDEO_UPLOAD_FROM_PC == $intVideoType && $bolIsPGCUser)){
            $arrInput = $arrPGCInput;
            $strService = 'video';
            $strMethod  = 'savePGCData';
            $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'UTF-8');
            $strLog = sprintf("call $strService::$strMethod failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput));
            if(false === $arrOutput){
                Bingo_Log::fatal($strLog);
                return false;
            }else if(Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
                Bingo_Log::warning($strLog);
                return false;
            }
        }
        if(Molib_Util_Video::TEMP_VIDEO_UPLOAD_FROM_PC == $intVideoType && !$bolIsPGCUser && $intChannelId > 0){
            $arrInput = array(
                'user_id'    => $intUid,
                'channel_id' => $intChannelId,
                'thread_ids' => $intTid,
            );
            $strService = 'video';
            $strMethod  = 'addVideos';
            $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'UTF-8');
            $strLog = sprintf("call $strService::$strMethod failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput));
            if(false === $arrOutput){
                Bingo_Log::fatal($strLog);
                return false;
            }else if(Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
                Bingo_Log::warning($strLog);
                return false;
            }
        }
        Bingo_Timer::end(__FUNCTION__);
        return true;
    }

    /**
     * [writeBackVideoCP description]
     * @param  [type] $arrInput     [description]
     * @param  [type] $arrVideoInfo [description]
     * @return [type]               [description]
     */
    public static function writeBackVideoCP($arrInput, $intCPId){
        Bingo_Timer::start(__FUNCTION__);
        $intThreadId = (int)$arrInput['thread_id'];
        //$intCPId     = (int)$arrVideoInfo['cp_id'];

        if($intCPId == 0){
            return true;
        }
        $arrVideocpInput = array(
            'thread_id' => $intThreadId,
            'cp_id'     => $intCPId,
        );
        $bolRet = Util_Videocp::updateVideocp($arrVideocpInput);
        Bingo_Timer::end(__FUNCTION__);
        return $bolRet;
    }
    /**
     * [pushToSearch description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function pushToSearch($arrInput){
        $intTid = (int)$arrInput['thread_id'];
        $intOpType = $arrInput['op_type'];
        //$intVideoType = (int)$arrVideoInfo['video_type'];
        $bolRet = true;

        if(Util_Const::AUDIT_OP_TYPE_PASS == $intOpType && $intTid > 0){
            Bingo_Log::warning("push to bigSearch tid:$intTid");
            $bolRet = Util_Push::pushToSearch($intTid);
        }
        return $bolRet;
    }
    /**
     * [pushToEditMis description]
     * @param  [type] $arrThreadReq [description]
     * @param  [type] $arrVideoInfo [description]
     * @return [type]               [description]
     */
    public static function pushToEditMis($arrInput, $arrThreadReq, $arrVideoInfo){
        $intOpType = (int)$arrInput['op_type'];
        $intUid = (int)$arrInput['user_id'];
        $intVideoType = (int)$arrVideoInfo['video_type'];
        $intPrivate = (int)$arrInput['is_private'];
        $intThreadId = (int)$arrInput['thread_id'];
        if(Util_Const::USERVIDEO_PRIVATE == $intPrivate){
            if($intThreadId > 0){
                Bingo_Log::warning("is_private:$intThreadId");
            }
            //return true;
        }

        if((Util_Const::$_arrNewAPPVType[$intVideoType]  || Util_Const::$_arrWhiteUser[$intUid])){
            //$arrThreadReq['title'] = Bingo_Encode::convert($arrThreadReq['title'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
            $arrOutput = Tieba_Service::call('video','addVideoToEditMis',$arrThreadReq, null,null,'post','php','utf-8');
            $strLog = sprintf("video::addVideoToEditMis input[%s] output[%s]", serialize($arrThreadReq), serialize($arrOutput));
            Bingo_Log::warning($strLog);

            if(false === $arrOutput||Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
                Bingo_Log::fatal($strLog);
            }

        }

        return true;

    }

    /**
     * [pushToEditMis description]
     * @param  [type] $arrThreadReq [description]
     * @param  [type] $arrVideoInfo [description]
     * @return [type]               [description]
     */
    public static function pushVideoFeed($arrInput, $arrVideoInfo){
        $intOpType    = (int)$arrInput['op_type'];
        $intUid       = (int)$arrInput['user_id'];
        $intVideoType = (int)$arrVideoInfo['video_type'];
        $intPrivate   = (int)$arrInput['is_private'];
        $intThreadId  = (int)$arrInput['thread_id'];
        $strTitle     = strval($arrInput['title']);

        if(Util_Const::USERVIDEO_PRIVATE == $intPrivate){
            if($intThreadId > 0){
                Bingo_Log::warning("is_private:$intThreadId");
            }
            //return true;
        }

        $arrUserInput = array(
            'nani_id'   => array($intUid), //nani_userid
            'user_type' => 0,   //KOL用户
        );


        $arrOutput = Service_Feed_BandPassAndNani::getDataPassNani($arrUserInput); // is or not kol
        if(false === $arrOutput||Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning("video::getPgcUserList failed input=".serialize($arrUserInput)."Output=".serialize($arrOutput));
        }

        if($arrOutput['data']['count'] >= 1){  // is kol
            $arrDBInput = array(
                'thread_id' => $intThreadId,
                'uid'       => $intUid,
                'title'     => $strTitle,
            );
            $arrDBOutput = Service_Edit_Edit::insertKolTid($arrDBInput); // insert
            if(false === $arrDBOutput||Tieba_Errcode::ERR_SUCCESS !== $arrDBOutput['errno']){
                Bingo_Log::warning("video:: insertKolTid failed input=".serialize($arrDBInput)."Output=".serialize($arrDBOutput));
            }
        }
        return true;

    }

    /**
     * @param [type] $intUserId [description]
     * @return
     */
    public static function addThreadFreshMan($intUserId){
        $arrServiceInput = array(
            'user_id' => $intUserId,
        );
        $strService = 'nani';
        $strMethod = 'addThreadFreshMan';
        $arrServiceOutput = Tieba_Service::call($strService , $strMethod, $arrServiceInput, null, null, 'post', 'php', 'utf-8');
        if($arrServiceOutput == false || $arrServiceOutput['errno'] != Tieba_Errcode::ERR_SUCCESS){
            $strLog = sprintf("call %s:%s fail. input:[%s] output:[%s]", $strService, $strMethod, serialize($arrServiceInput), serialize($arrServiceOutput));
            Bingo_Log::warning($strLog);
            return false;
        }
        return true;
    }

    /**
     * [transThread description]
     * @param  [type] $arrInput     [description]
     * @param  [type] $arrVideoInfo [description]
     * @return [type]               [description]
     */
    public static function transThread($arrThreadReq, $arrThreadRes){
        Bingo_Timer::start(__FUNCTION__);
        if(!$arrThreadReq['is_multi_forum']){
            Bingo_Log::pushNotice('multi_forum',0);
            return true;
        }
        Bingo_Log::pushNotice('multi_forum',1);
        $arrNeedTrans = array();
        $arrForbidTrans = array();
        foreach($arrThreadRes['v_forum_info'] as $k => $v) {
            if($v['errno'] == 0 && $v['v_forum_id'] >0){
                array_push($arrNeedTrans,$v['v_forum_id']);
            }else{
                array_push($arrForbidTrans,$v['v_forum_id']);
            }
        }
        Bingo_Log::pushNotice('need_trans', join(',', $arrNeedTrans));
        Bingo_Log::pushNotice('forbid_trans', join(',', $arrForbidTrans));
        if(empty($arrNeedTrans)){
            Bingo_Log::warning('no need to trans!');
            return true;
        }
        $strService = 'post';
        $strMethod = 'transThreadToForum';
        // TODO: params not initiated
        $arrReq = array(
            'req' => array(
                'v_forum_ids'     => $arrNeedTrans,
                'forum_id'        => $arrThreadReq['forum_id'],
                'forum_name'      => $arrThreadReq['forum_name'],
                'is_multi_forum'  => 1,
                'cuid'            => 'ala_live_auto_share',
                'user_id'         => intval($arrThreadReq['user_id']),
                'user_name'       => $arrThreadReq['user_name'],
                'user_ip'         => $arrThreadReq['user_ip'],
                'thread_type'     => $arrThreadReq['thread_type'],
                'thread_id'       => $arrThreadRes['thread_id'],
                'check_ueg'       => 0,
                'vcode_free_gate' => 1,
                'product_private_key' => 'ala_clt',
            ),
        );
        //Bingo_Log::warning('Input transThreadToForum :'.serialize($arrReq));
        $arrRes = Tieba_Service::call($strService, $strMethod, $arrReq, null, null, 'post', 'php', 'utf-8');

        $strLog = sprintf("call $strService::$strMethod failed! input[%s] output[%s]",serialize($arrReq), serialize($arrRes));
        if ($arrRes === false || $arrRes['errno'] != 0){
            Bingo_Log::fatal($strLog);
            return false;
        }
        //Bingo_Log::warning('Output transThreadToForum :'.serialize($arrRes));
        $vSuccForumInfo = $arrRes['res']['v_forum_info']['success'];
        $arrSuccFids = array();
        foreach ($vSuccForumInfo as $intFid => $item){
            $arrSuccFids[$intFid] = $arrThreadRes['thread_id'];
        }
        $vFailForumInfo = $arrRes['res']['v_forum_info']['forbidden'];
        foreach ($vFailForumInfo as $intFid => $item) {
            $strMsg = $item[$intFid];
            Bingo_Log::warning("share to forum[$intFid] fail, msg[$strMsg]");
        }
        Bingo_Timer::end(__FUNCTION__);
        return true;

    }

    /**
     * @breif   根据视频类型判断是否需要预先转码，目前需要预先转码的有：贴吧故事贴1\2，pc上传，客户端本地
     * @param   array
     * @return  bool
     */
    public static function getNeedPreTranscode($arrInput)
    {
        $intVideoType = $arrInput['video_type'];

        $bolNeedTranscode = (int)Util_Const::$_arrPreTranscodeVTypes[$intVideoType];

        return $bolNeedTranscode;
    }

    /**
     * @brief 获取后转码策略，目前这个函数只有PGC和url爬取，这两个类别会返回true
     * @param  array
     * @return bool
     */
    public static function getNeedTranscodeLast($arrInput)
    {
        $intVideoType = $arrInput['video_type'];

        $bolNeedTranscodePre = (int)Util_Const::$_arrPreTranscodeVTypes[$intVideoType];
        $bolNeedTranscodeAll = (int)Util_Const::$_arrTranscodeLastVTypes[$intVideoType];

        return (!$bolNeedTranscodePre && $bolNeedTranscodeAll);
    }

    /**
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getNeedWatermark($arrInput){
        $intVideoType = $arrInput['video_type'];
        return isset(Util_Const::$arrWatermarkVTypes[$intVideoType]);
    }
    /**
     * @brief  根据入参视频信息，得到该视频是否需要打水印、去水印、片尾等信息
     * @param  array
     * @return array
     */
    public static function getVideoFilterOptions($arrInput)
    {
        $arrFilterOptions = array();

        $arrVideoInfo     = Lib_Audit::getVideoInfo($arrInput);
        $intVideoType     = intval($arrInput['video_type']);
        $isTiebaVideo     = Util_Const::$_arrTiebaVType[$intVideoType]; // 客户端本地、客户端录制、pc上传都返回true
        $intWeekkey       = $arrInput['weekkey'];
        $intVideoAuditId  = $arrInput['video_audit_id'];
        $intAuthorUid     = $arrInput['user_id'];
        $handleWordServer = Wordserver_Wordlist::factory();
        $strTableName     = 'tb_wordlist_redis_video_tieba_watermark';

        $arrWordListKey = array(
            $intAuthorUid,
        );
        $arrRet = $handleWordServer->getValueByKeys($arrWordListKey, $strTableName);

        // 全量贴吧视频打水印，只要不是pipixia或者nani和newapp相关的类型都打贴吧水印
        if (!isset(Util_Const::$arrNaniSourceVTypes[$intVideoType]) && !isset(Util_Const::$arrAntiTiebaWatermarkVTypes[$intVideoType])) {
            $arrFilterOptions['overlay'] = Util_Const::$arrWatermarkSettingList['tieba'];
            $arrFilterOptions['levels'] = 1;
        }

        // 片尾处理
        if (1 == $isTiebaVideo && isset($arrRet[$intAuthorUid])) {
            $arrFilterOptions['concat'] = Util_Const::$arrConcatSettingList['tieba'];
            if (!empty($arrInput['forum_name'])) {
                $arrFilterOptions['concat']['text'] = $arrInput['forum_name'] . '吧：' . $arrInput['user_name'];
            }
            else {
                $arrFilterOptions['concat']['text'] = $arrInput['user_name'];
            }
        }

        // 视频入大搜视频搜索
        // 影视类资源打马赛克屏蔽水印
        if ($intVideoType === Molib_Util_Video::TEMP_VIDEO_FROM_SEO_LONGVIDEO) {
            $intDelogoSequence = 0;
            $intErrorNo = Tieba_Errcode::ERR_SUCCESS;
            do {
                // 事务
                if (!Lib_Audit::startTransaction()) {
                    Bingo_Log::warning('call Lib_Audit::startTransaction fail, input:[' . json_encode($arrInput) . ']');
                    $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }

                // 检查状态
                $arrParams = array(
                    'cond' => array(
                        'id' => $intVideoAuditId,
                    ),
                    'weekkey' => $intWeekkey,
                    'for_update' => true,
                );
                $arrRet = Lib_Audit::selectVideoInfo($arrParams);
                if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                    Bingo_Log::warning('call Lib_Audit::selectVideoInfo fail, params:[' . json_encode($arrParams) . '],ret:[' . json_encode($arrRet) . ']');
                    $intErrorNo = $arrRet['errno'];
                    break;
                }
                if (empty($arrRet['data'])) {
                    Bingo_Log::warning('no this video record, params:[' . json_encode($arrParams) . '],ret:[' . json_encode($arrRet) . ']');
                    $intErrorNo = Tieba_Errcode::ERR_NO_RECORD;
                    break;
                }
                $arrAuditInfo = $arrRet['data'][0];
                if ($arrAuditInfo['delogo_status'] == Util_Const::DELOGO_STATUS_PROC) {
                    Bingo_Log::warning('delogo status error, params:[' . json_encode($arrParams) . '],ret:[' . json_encode($arrRet) . ']');
                    $intErrorNo = Tieba_Errcode::ERR_STATUS_ERROR;
                    break;
                }
                $arrExtParam = json_decode($arrAuditInfo['ext_param'], true);

                // 只有在没去过水印的情况下才会走“管他三七二十一，发贴直接去除右上角好看水印”策略
                if (!isset($arrExtParam['delogo_sequence'])) {
                    // 记录Delogo Sequence
                    $intDelogoSequence = $arrExtParam['delogo_sequence'] = 1;
                    $strFinalExtParam = mysql_escape_string(json_encode($arrExtParam));

                    // 修改状态
                    $arrParams = array(
                        'field' => array(
                            'ext_param' => $strFinalExtParam,
                        ),
                        'cond' => array(
                            'id' => $intVideoAuditId,
                        ),
                        'table_name' => Lib_Audit::VDIEO_TABLE_PRE . $intWeekkey,
                    );
                    if ($arrAuditInfo['delogo_status'] != Util_Const::DELOGO_STATUS_DONE) {
                        $arrParams['field']['delogo_status'] = Util_Const::DELOGO_STATUS_PROC;
                    }
                    $arrRet = Lib_Audit::updateVideoInfo($arrParams);
                    if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
                        Bingo_Log::fatal('call Lib_Audit:updateVideoInfo select db failed, params:['
                            . json_encode($arrParams) . '],ret:[' . json_encode($arrRet) . ']');
                        $intErrorNo = $arrRet['errno'];
                        break;
                    }

                    $arrFilterOptions['delogo_sequence'] = $intDelogoSequence;
                    $arrFilterOptions['delogo'] = array();

                    // 第一种打水印方式，自己计算
                    /*
                    $arrFilterOptions['delogo'][] = array(
                        'x' =>  intval($intPositionX),
                        'y' =>  intval($intPositionY),
                        'w' =>  intval($intWatermarkWidth),
                        'h' =>  intval($intWatermarkHeight),
                    );
                    */
                    // 第二种打水印方式，转码集群自己计算，这里只给相对位置
                    $arrFilterOptions['delogorela'][] = array(
                        'x' => 0,
                        'y' => 0,
                        'w' => 0.19,
                        'h' => 0.17,
                    );
                    $arrFilterOptions['delogorela'][] = array(
                        'x' => 0.81,
                        'y' => 0,
                        'w' => 0.19,
                        'h' => 0.17,
                    );

                    $arrFilterOptions['editing'] = array(
                        'head' => 6,
                        'tail' => 2,
                    );
                }

                // 提交
                if (!Lib_Audit::commit()) {
                    Bingo_Log::warning('call Lib_Audit::commit fail, input:['.json_encode($arrInput).']');
                    $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }
            } while (0);

        }

        return $arrFilterOptions;
    }
    /**
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getDefaultWatermarkSettings(){
        // return Util_Const::$arrWatermarkSettings;
        return array();
    }
    /**
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getWatermarkSettings($strType){
        if (isset(Util_Const::$arrWatermarkSettingList[$strType])) {
            return Util_Const::$arrWatermarkSettingList[$strType];
        } else {
            // return Util_Const::$arrWatermarkSettings;
            return array();
        }
    }
    /**
     * [getNeedNakeTrans description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getNeedNakeTrans($arrInput)
    {
        $intUserId = (int)$arrInput['user_id'];
        if (isset(Util_Const::$arrQualityTestUsers[$intUserId])) {
            return false;
        }
        $intVideoType = (int)$arrInput['video_type'];
        $strClientVersion = $arrInput['ui_trans_params']['client_version'];
        $strModel = $arrInput['ui_trans_params']['model'];
        $strBrand = $arrInput['ui_trans_params']['brand'];
        $intLocalCodec = (int)$arrInput['ui_trans_params']['local_codec'];
        Bingo_Log::pushNotice("getNeedNakeTrans", $intVideoType."_". $intLocalCodec."_".$strModel);
        if(!Util_Const::$arrNakeTransVTypes[$intVideoType]){
            return false;
        }
        if (Util_Version::compare($strClientVersion, '1.0.0') < 0
            && Util_Version::compare($strClientVersion, '1.1.1') > 0) {
            return false;
        }
        /*foreach(Lib_Define_Device::$_arrTrsDeviceModels as $pattern){
            if(preg_match($pattern, $strModel, $match)){
                return true;
            }
        }*/
        if(Lib_Define_Device::$_arrTrsDeviceBrand[$strBrand]){
            return true;
        }
        return false;
    }
    /**
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getNeedSpecialTransForNani($arrInput)
    {
        // 仅NANI类型
        $intVideoType = (int)$arrInput['video_type'];
        if (!isset(Util_Const::$_arrNaniVType[$intVideoType])
            || !Util_Const::$_arrNaniVType[$intVideoType]) {
            return false;
        }
        /*
                // 暂时仅转android
                $strPhoneType = strtolower($arrInput['phone_type']);
                if ($strPhoneType != 'android') {
                    return false;
                }

                // 暂时小流量测试
                $intUserId = (int)$arrInput['user_id'];
                if (!isset(Util_Const::$_arrWhiteUser[$intUserId])
                    || !Util_Const::$_arrWhiteUser[$intUserId]) {
                    return false;
                }
        */
        return true;
    }
    /**
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getNeedOverlayWithLowResolution($arrInput)
    {
        $intVideoType = (int)$arrInput['video_type'];
        return !isset(Util_Const::$_arrNewAPPVType[$intVideoType]);
    }
    /**
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getNeedNoTrans($arrInput)
    {
        $intUserId = (int)$arrInput['user_id'];
        if (isset(Util_Const::$arrQualityTestUsers[$intUserId])) {
            return true;
        }

        $intVideoType = (int)$arrInput['video_type'];
        return !isset(Util_Const::$_arrAllTranscodeVTypes[$intVideoType]);
    }
    /**
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getNeedNotReTransOnSlowStart($arrInput)
    {
        $intUserId = (int)$arrInput['user_id'];
        if (isset(Util_Const::$arrQualityTestUsers[$intUserId])) {
            return true;
        }
        return false;
    }
    /**
     * @brief 发起转码，在视频流程中属于后转码
     * @param  [type] $arrInput     [description]
     * @param  [type] $arrVideoInfo [description]
     * @return [type]               [description]
     */
    public static function postTranscode($arrInput, $arrVideoInfo)
    {
        Bingo_Timer::start(__FUNCTION__);

        $bolNeedTranscodeLast = Lib_Audit::getNeedTranscodeLast($arrInput);
        $bolNeedWatermark     = Lib_Audit::getNeedWatermark($arrVideoInfo);
        $arrFilterOptions     = Lib_Audit::getVideoFilterOptions($arrInput);
        $bolNeedNakeTrans     = Lib_Audit::getNeedNakeTrans($arrInput);
        $bolNeedSpecialTransForNani = Lib_Audit::getNeedSpecialTransForNani($arrInput);
        $bolNeedNoTrans       = Lib_Audit::getNeedNoTrans($arrInput);

        if (!$bolNeedTranscodeLast) {
            return true;
        }

        $intWeekKey  = (int)$arrInput['weekkey'];
        $intAuditId  = (int)$arrInput['video_audit_id'];
        $strVideoUrl = $arrVideoInfo['video_url'];
        $intTid      = (int)$arrInput['thread_id'];

        $arrTransInput = array (
            'weekkey'        => $intWeekKey,
            'video_audit_id' => $intAuditId,
            'video_url'      => $strVideoUrl,
            'thread_id'      => $intTid,
        );

        if ($bolNeedWatermark) {
            $arrTransInput['overlay'] = Lib_Audit::getDefaultWatermarkSettings();
        }
        if (!empty($arrFilterOptions)) {
            $arrTransInput = array_merge($arrTransInput, $arrFilterOptions);
        }
        if ($bolNeedSpecialTransForNani) {
            $arrTransInput['preset_id'] = Util_Const::TRANS_PRESET_ID_NANI;
        }
        if ($bolNeedNakeTrans) {
            $arrTransInput['notrans'] = Util_Const::NO_TRANS_TYPE_NO_SCALES;
        } else if($bolNeedNoTrans) {
            $arrTransInput['notrans'] = Util_Const::NO_TRANS_TYPE_ALL;
        }

        $arrTransOutput = Service_Video_Transcode::reqTranscodeVideo($arrTransInput);
        if(false === $arrTransOutput||Tieba_Errcode::ERR_SUCCESS !== $arrTransOutput['errno']){
            Bingo_Log::fatal(sprintf("transcode_info at start input[%s] output[%s]", serialize($arrTransInput), serialize($arrTransOutput)));
        }else{
            Bingo_Log::warning(sprintf("transcode_info at start input[%s] output[%s]", serialize($arrTransInput), serialize($arrTransOutput)));
        }
        Bingo_Timer::end(__FUNCTION__);
        return true;
    }
    /**
     * 获取是否需要idl打封面策略
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getNeedGenCover($arrVideoInfo)
    {
        // no thumbnail
        if (empty($arrVideoInfo['thumbnail_url'])) {
            return true;
        }

        // 策略修改，删掉后面的需要生成封面的条件，只保留没有封面时才生成的逻辑
        // temp frame pic
//        if (strpos($arrVideoInfo['thumbnail_url'], 'tieba-video-frame') !== false) {
//            return true;
//        }

        // client user thumbnail
        if (isset($arrVideoInfo['cover_text'])) {
            return false;
        }

        // vertical video with horizontal thumbnail
//        if ($arrVideoInfo['video_width'] < $arrVideoInfo['video_height']
//            && $arrVideoInfo['thumbnail_width'] > $arrVideoInfo['thumbnail_height']) {
//            return true;
//        }

        // video type
//        $intVideoType = $arrVideoInfo['video_type'];
//        if ($intVideoType != Molib_Util_Video::TEMP_VIDEO_FROM_PGC
//            && $intVideoType != Molib_Util_Video::TEMP_VIDEO_UPLOAD_FROM_PC
//            && !($intVideoType >= 100 && $intVideoType <= 500)) {
//            return true;
//        }

        return false;
    }
    /**
     * [genCover description]
     * @param  [type] $arrInput     [description]
     * @param  [type] $arrVideoInfo [description]
     * @return [type]               [description]
     */
    public static function genCover($arrInput, $arrVideoInfo){
        Bingo_Timer::start(__FUNCTION__);
        $bolGenCover = Lib_Audit::getNeedGenCover($arrVideoInfo);

        $bolRet = true;
        //IDL请求生成封面
        if ($bolGenCover) {
            $strVideoUrl = $arrVideoInfo['video_url'];
            $intUid = (int)$arrInput['user_id'];
            $intTid = (int)$arrInput['thread_id'];
            $strWeekKey = $arrInput['weekkey'];
            $bolRet = Tieba_Video_IDL::commitThumbnailReq($strWeekKey, $intTid, $intUid, $strVideoUrl);
            Bingo_Log::warning('commit thumbnail request, thread_id='.$intTid.', video_info='.json_encode($arrVideoInfo));
        }
        Bingo_Timer::end(__FUNCTION__);
        return $bolRet;
    }
    /**
     *
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function pushMsgToUser($arrInput){

        $intType    = (int)$arrInput['op_type'];
        $intVideoType = (int)$arrInput['video_type'];
        $strTitle   = $arrInput['title'];
        $intTid     = (int)$arrInput['thread_id'];
        $intUid     = (int)$arrInput['user_id'];
        $strUrl     = sprintf("http://tieba.baidu.com/p/%d", $intTid);
        $strJumpUrl = sprintf("<a href=\"%s\">%s</a>",$strUrl, $strUrl);
        //$strPushMsg = '';

        //add by dongliang04
        switch($intType){
            case Util_Const::AUDIT_OP_TYPE_REFUSE:{
                $strPushMsg = Util_Const::DELETE_DEFAULT_USER_MSG;
                if($arrInput['delete_reason'] == '其他' || empty($arrInput['delete_reason'])){
                    //$strPushMsg = Util_Const::DELETE_DEFAUL_USER_MSG;
                }else{
                    //读一下redis
                    $arrRedisInput = array(
                        'field' => array(
                            strval($arrInput['delete_reason']),
                        ),
                        'table_name' => 'tb_wordlist_redis_video_delete_reason',
                    );
                    $arrRedisOutput = Lib_WordList::get($arrRedisInput);


                    $strPreMsg = strval($arrRedisOutput[$arrInput['delete_reason']]);
                    if(!empty($strPreMsg)){
                        $strPushMsg = sprintf(Util_Const::$_arrPushMsg[$intType], $strPreMsg);
                    }

                }
                break;
            }

            default:{
                $strPushMsg = sprintf(Util_Const::$_arrPushMsg[$intType], $strUrl);
                break;
            }

        }

        //$strPushMsg = sprintf(Util_Const::$_arrPushMsg[$intType], $strUrl);
        $strPcMsg   = sprintf(Util_Const::$_arrPcMsg[$intType],$strJumpUrl);

        if(Util_Const::$_arrTiebaVType[$intVideoType]){
            if( !empty($strPushMsg) ){

                $arrImInput = array(
                    'user_id' => $intUid,
                    'content' => $strPushMsg,
                );
                $bolImRet = Util_Im::sendMsg($arrImInput);
            }
            if( !empty($strPcMsg)){
                $arrPcInput = array(
                    'user_id' => $intUid,
                    'content' => $strPcMsg,
                );
                $bolPcRet = Util_Sysmsg::sendSysmsg($arrPcInput);
            }
        }
        if(Util_Const::$_arrNaniVType[$intVideoType]){
            if(Util_Const::AUDIT_OP_TYPE_REFUSE == $arrInput['op_type']){
                $bolNaniRet = Util_Im::sendNaniMsg($intUid,$strPushMsg);
            }
        }
        return true;
    }

    /**
     * 给用户发push消息
     * @param $arrInput
     * @return bool
     */
    public static function pushMsgToWorksUser($arrInput) {

        $arrInput = array(
            'title' => $arrInput['push']['title'],
            'content'   => $arrInput['push']['content'],
            'url'       => $arrInput['push']['link'],
            'image_url'     => $arrInput['push']['image_url'],
            'uid'       => array($arrInput['user_id']),
            'user_id'   => $arrInput['push']['user_id'],
            'user_name' => $arrInput['push']['user_name'],
            'task_id'   => $arrInput['push']['task_id'],
            'service_id'    =>  $arrInput['push']['service_id'],
            'user_type' => 4,
            'msg_type'  => 1,
            'push_type' => 2,
        );

        $arrOutput = Tieba_Service::call('common', 'newPushMsg', $arrInput, null, null, 'post', 'php', 'utf-8');
        if(false === $arrOutput||Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(__CLASS__ . "::" . __METHOD__ . 'call newPushMsg failed'.serialize($arrInput).'output = '.serialize($arrOutput));
            return false;
        }
        return true;
    }

    /**
     * [genePGCInput description]
     * @param  [type] $arrInput     [description]
     * @param  [type] $arrVideoInfo [description]
     * @return [type]               [description]
     */
    public static function genePGCInput($arrInput, $arrVideoInfo){

        $input = array(
            'uid'              => $arrInput['user_id'],
            'forum_id'         => $arrInput['forum_id'],
            //支持多吧发帖 start
            'v_forum_ids'      => $arrInput['v_forum_ids'],
            'is_multi_forum'   => $arrInput['is_multi_forum'],
            //支持多吧发帖 end
            'thread_id'        => $arrInput['thread_id'],
            'video_url'        => isset($arrVideoInfo['video_url'])? strval($arrVideoInfo['video_url']) : '',
            'video_width'      => isset($arrVideoInfo['video_width'])? intval($arrVideoInfo['video_width']) : 0,
            'video_height'     => isset($arrVideoInfo['video_height'])? intval($arrVideoInfo['video_height']) : 0,
            'thumbnail_picid'  => isset($arrVideoInfo['thumbnail_picid'])? intval($arrVideoInfo['thumbnail_picid']) : 0,
            'thumbnail_width'  => isset($arrVideoInfo['thumbnail_width'])? intval($arrVideoInfo['thumbnail_width']) : 0,
            'thumbnail_height' => isset($arrVideoInfo['thumbnail_height'])? intval($arrVideoInfo['thumbnail_height']) : 0,
            'video_duration'   => isset($arrVideoInfo['video_duration'])? intval($arrVideoInfo['video_duration']) : 0,
            'video_md5'        => isset($arrVideoInfo['video_md5'])? strval($arrVideoInfo['video_md5']) : '',
            'channel_id'       => isset($arrVideoInfo['channel_id'])? intval($arrVideoInfo['channel_id']) : 0,
            'title'            => $arrInput['title'],
            'video_format'     => isset($arrVideoInfo['video_format'])? strval($arrVideoInfo['video_format']) : '',
        );
        return $input;
    }

    /**
     * [addVideoThread description]
     * @param [type] $arrInput [description]
     * @return
     */
    public static function addVideoThread($arrInput){
        $arrVideoInfo = Lib_Audit::getVideoInfo($arrInput);
        $intVideoType = (int)$arrVideoInfo['video_type'];

        // 视频贴增加摘要，发帖ui接口那里并没有把摘要追到content字段内，而是先放到ui_trans_params，
        // 在真正审核通过调post::addThread前把ui_trans_params内video_abstract字段
        // 追加到content后(如果存在)，然后移掉ui_trans_params内的摘要字段
        // 20220805 下线正文 pm <EMAIL>
        if (!empty($arrInput['ui_trans_params']['video_abstract'])) {
            Bingo_Log::warning(sprintf("has content. tid[%s] content[%s]", $arrInput['predistributed_thread_id'], $arrInput['ui_trans_params']['video_abstract']));
            //$arrInput['content'] .= $arrInput['ui_trans_params']['video_abstract'];
            unset($arrInput['ui_trans_params']['video_abstract']);
        }

        $addThreadReq = array(
            'product_private_key'  => 'special_pro',
            'user_ip'              => $arrInput['user_ip'],
            'user_id'              => $arrInput['user_id'],
            'user_name'            => $arrInput['user_name'],
            'forum_id'             => $arrInput['forum_id'],
            //支持多吧发帖 start
            'v_forum_ids'          => $arrInput['v_forum_ids'],
            'is_multi_forum'       => $arrInput['is_multi_forum'],
            //支持多吧发帖 end
            'forum_name'           => $arrInput['forum_name'],
            'title'                => $arrInput['title'],
            'content'              => $arrInput['content'],
            'create_time'          => $arrInput['create_time'],
            'thread_type'          => $arrInput['thread_type'],
            'ext_attr'             => $arrInput['ext_attr'],
            'is_audit_before_post' => 2,
            'ueg_mis_audit'        => 1,    // 这个字段是跟@姜灿约定好的，不过UEG也不会对发帖进行屏蔽
            'vcode_free_gate'      => true,
            'cmd_no'               => 20,
            'phone_type'           => $arrInput['phone_type'],
            'client_version'       => $arrInput['client_version'],
            'is_hide'              => (int)$arrInput['is_hide'],
            'ui_trans_params'      => $arrInput['ui_trans_params'],
            'pro_zone'             => intval($arrInput['pro_zone']),
            'abrakadabra'          => strval($arrInput['abrakadabra']),
            'thread_mask_flag'     => intval($arrInput['thread_mask_flag']),
        );

        if (isset($arrInput['predistributed_thread_id']) && $arrInput['predistributed_thread_id'] > 0) {
            $addThreadReq['predistributed_thread_id'] = $arrInput['predistributed_thread_id'];
        }

        if (Util_Const::$_arrNewAPPVType[$intVideoType]) {
            $addThreadReq['ui_trans_params']['feed_type'] = 1;
        }

        // check no username case
        $addThreadReq = self::_checkUserInfo($addThreadReq);

        $arrThreadRes = Util_Post::addVideoThread($addThreadReq);
        $intThreadId = (int)$arrThreadRes['thread_id'];

        // 如果发帖失败
        if (0 === $intThreadId) {
            return false;
        }

        $arrVideoPlay = array(
            'count' => 0,
            'time'  => time(),
        );
        $bolSetKey = Util_Post::setThreadKey($intThreadId, 'video_play', $arrVideoPlay);

        return $arrThreadRes;
    }

    /**
     * @param $arrInput
     * @return mixed
     */
    private static function _checkUserInfo($arrInput) {
        if (!empty($arrInput['user_name'])) {
            return $arrInput;
        }

        $arrParam = array(
            "user_id" => $arrInput['user_id'],
        );
        $arrRet = Tieba_Service::call('user', 'getUserData', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call user getUserData failed. [input = %s][output = %s]", serialize($arrParam), serialize($arrRet)));
            return $arrInput;
        }
        $arrInput['nick_name'] = strval($arrRet['user_info'][0]['user_nickname']);
        return $arrInput;
    }

    /**
     * [buildVideoAuditInput description]
     * @param  [type] $arrInput     [description]
     * @param  [type] $arrThreadReq [description]
     * @param  [type] $arrVideoInfo [description]
     * @return [type]               [description]
     */
    public static function buildVideoAuditInput($arrAllInput){
        //return $arrAllInput;
        $arrNmqInput = $arrAllInput['thread_input'];
        $arrNmqInput['other_info'] = array(
            'video_audit_id' => (int)$arrAllInput['origin_input']['video_audit_id'],
            'weekkey'        => (int)$arrAllInput['origin_input']['weekkey'],
            'op_type'        => (int)$arrAllInput['origin_input']['op_type'],
            'add_thread'     => (int)$arrAllInput['origin_input']['add_thread'],
            'delete_thread'  => (int)$arrAllInput['origin_input']['delete_thread'],
            'recover_thread' => (int)$arrAllInput['origin_input']['recover_thread'],
        );
        return $arrNmqInput;
    }

    /**
     * @brief 获取先切帧策，当前是所有都先切帧
     * @param  array
     * @return int(bool)
     */
    public static function getNeedCutFrame($arrInput)
    {
        return 1;
    }

    /**
     * @brief 根据日期获取当前表名
     * @param {int} $thatDayStamp 存储数据
     * @return: timestamp.
     **/
    public static function getThatDayMonday($thatDayStamp)
    {
        // 得到最近的下一个周一的时间戳
        $MonDayDateStamp = strtotime("Monday");
        // 判断这个时间戳距离入参时间有几个周末，向上取整
        $skipWeek = ceil(($MonDayDateStamp - $thatDayStamp) / 86400 / 7);
        // 使用下一个周一减去上一步中求出的周末数量
        $thatMonday = $MonDayDateStamp - $skipWeek * 7 * 86400;

        return date("Ymd", $thatMonday);
    }
    /**
     * [allocVideoLogid description]
     * @param  integer $intTime [description]
     * @return [type]           [description]
     */
    public static function allocVideoLogid($intTime = 0){
        Bingo_Timer::start('atom_nt_video_id');
        $arrAllocId = Tieba_Idalloc::alloc('nt_video_id');
        Bingo_Timer::end('atom_nt_video_id');

        Bingo_Log::notice("[video] nt_video_id :" .json_encode($arrAllocId));

        if (!isset($arrAllocId[0]) || $arrAllocId[0] == 0) {
            Bingo_Log::warning('alloc video_log_id failed');
            return 0;
        }
        $intVideoId = $arrAllocId[0];
        if($intTime <= 0){
            $intTime = time();
        }
        $intWeekKey = Lib_Audit::getThatDayMonday($intTime);
        $intVideoId = intval($intVideoId) . $intWeekKey;
        return $intVideoId;
    }
    /**
     * @brief 根据起止日期查询数据所在的表列表
     * @param
     * @return: timestamp.
     **/
    public static function getTableList($arrInput) {
        // 对时间做校验并兼容
        $nowTime = time();
        $arrInput['end_time'] = (!isset($arrInput['end_time']) || $arrInput['end_time'] > $nowTime) ? $nowTime : $arrInput['end_time'];
        if (!isset($arrInput['start_time']) || $arrInput['start_time'] <= 0 || $arrInput['start_time'] > $arrInput['end_time']) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::arrRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 获取该时间段的所有表列表
        $arrTableList = array();
        $tempTime = $arrInput['start_time'];
        $arrTableList[] = self::getThatDayMonday($tempTime);
        do {
            $tempTime = $tempTime + 7 * 86400;
            $arrTableList[] = self::getThatDayMonday($tempTime);
        } while ($tempTime < $arrInput['end_time']);

        return $arrTableList;
    }

    /**
     * @param $arrVideoInfo
     * @param $arrRealVideoInfo
     * @return bool
     */
    public static function validateVideoInfo($arrVideoInfo, $arrRealVideoInfo)
    {
        $strVideoUrl = isset($arrVideoInfo['origin_video_url']) ? strval($arrVideoInfo['origin_video_url']) : strval($arrVideoInfo['video_url']);
        $intVideoType = intval($arrVideoInfo['video_type']);
        $strVideoMd5Report = strval($arrVideoInfo['video_md5']);
        $intVideoSizeReport = isset($arrVideoInfo['video_length']) ? intval($arrVideoInfo['video_length']) : intval($arrVideoInfo['video_size']);
        $intVideoDurationReport = isset($arrVideoInfo['video_duration']) ? intval($arrVideoInfo['video_duration']) : intval($arrVideoInfo['duration']);
        $intVideoWidthReport = intval($arrVideoInfo['video_width']);
        $intVideoHeightReport = intval($arrVideoInfo['video_height']);

        // video_md5 check
        if (!preg_match('#^[0-9a-f]{32}$#i', $strVideoMd5Report)) {
            Bingo_Log::warning('validate video_md5 fail, value = '.$strVideoMd5Report);
            return false;
        }
        if (isset($arrRealVideoInfo['video_md5']) && !empty($arrRealVideoInfo['video_md5'])) {
            $strVideoMd5Real = $arrRealVideoInfo['video_md5'];
            if (strcasecmp($strVideoMd5Report, $strVideoMd5Real) != 0) {
                // do nothing at present, since the client may report an unknown md5 indeed!
            }
        }

        // video_size check
        if (isset($arrRealVideoInfo['video_size']) && $arrRealVideoInfo['video_size'] > 0) {
            $intVideoSizeReal = intval($arrRealVideoInfo['video_size']);
            if (abs($intVideoSizeReport - $intVideoSizeReal) > 1) {
                Bingo_Log::warning('validate video_size fail, report value = '.$intVideoSizeReport, ', real value = '.$intVideoSizeReal);
                return false;
            }
        }

        // video_width & video_height check
        if (isset($arrRealVideoInfo['video_width']) && isset($arrRealVideoInfo['video_height'])
            && $arrRealVideoInfo['video_width'] > 0 && $arrRealVideoInfo['video_height'] > 0) {
            $intVideoWidthReal = intval($arrRealVideoInfo['video_width']);
            $intVideoHeightReal = intval($arrRealVideoInfo['video_height']);
            if ($intVideoType != Molib_Util_Video::TEMP_VIDEO_CP_FEEDS // feeds type videos' w&h are totally wrong
                && $intVideoType != Molib_Util_Video::TEMP_VIDEO_UPLOAD_FROM_PC
                && $intVideoType != Molib_Util_Video::TEMP_VIDEO_FROM_PGC) { // pc interface can't supply us with real w&h, so it report 0x0
                // we just compare video resolution, cause that the client may report exchanged w&h on a rotated video,
                // further more, nani with old version may report wrong video_height for record type (vertical)
                $intVideoResolutionReport = min($intVideoWidthReport, $intVideoHeightReport);
                $intVideoResolutionReal = min($intVideoWidthReal, $intVideoHeightReal);
                if (abs($intVideoResolutionReport - $intVideoResolutionReal) > 10) {
                    Bingo_Log::warning('validate video_width & video_height fail, report value = '.$intVideoWidthReport.'x'.$intVideoHeightReport
                        . ', real value = '.$intVideoWidthReal.'x'.$intVideoHeightReal);
                    return false;
                }
            }
        }

        // video_duration check
        if (isset($arrRealVideoInfo['video_duration']) && $arrRealVideoInfo['video_duration'] > 0) {
            $intVideoDurationReal = intval($arrRealVideoInfo['video_duration']);
            if (($intVideoType == Molib_Util_Video::TEMP_VIDEO_FROM_CLIENT_LOCAL && $intVideoDurationReal > 600)
                || (($intVideoType == Molib_Util_Video::TEMP_VIDEO_FROM_CLIENT_RECORD
                        || $intVideoType == Molib_Util_Video::TEMP_VIDEO_NEWAPP_CLIENT_LOCAL
                        || $intVideoType == Molib_Util_Video::TEMP_VIDEO_NEWAPP_CLIENT_RECORD) && $intVideoDurationReal > 30)) {
                Bingo_Log::warning('validate video_duration fail, report value = '.$intVideoDurationReport, ', real value = '.$intVideoDurationReal);
                return false;
            }
            if ($intVideoDurationReport > 1) { // pc interface can't supply us with video's duration, so it report 0
                // the client may bot be able to get the video's duration, and in such a case, it report 1
                if (abs($intVideoDurationReport - $intVideoDurationReal) > 1) {
                    Bingo_Log::warning('validate video_duration fail, report value = '.$intVideoDurationReport, ', real value = '.$intVideoDurationReal);
                    return false;
                }
            }
        }

        // video_url check
        if ($intVideoType == Molib_Util_Video::TEMP_VIDEO_UPLOAD_FROM_PC
            || $intVideoType == Molib_Util_Video::TEMP_VIDEO_FROM_PGC) {
            $strVideoNamePattern = 'tieba-movideo/'.$intVideoSizeReport.'_'.$strVideoMd5Report.'_[0-9a-f]{12}\.[0-9a-z]{2,4}';
        } else if ($intVideoType == Molib_Util_Video::TEMP_VIDEO_FROM_CLIENT_LOCAL
            || $intVideoType == Molib_Util_Video::TEMP_VIDEO_FROM_CLIENT_RECORD
            || $intVideoType == Molib_Util_Video::TEMP_VIDEO_NEWAPP_CLIENT_LOCAL
            || $intVideoType == Molib_Util_Video::TEMP_VIDEO_NEWAPP_CLIENT_RECORD) {
            // there may be a little difference between the duration in video_name and video_duration!
            $arrAcceptDurations = array($intVideoDurationReport - 1, $intVideoDurationReport, $intVideoDurationReport + 1);
            $strVideoNamePattern = 'tieba-smallvideo/('.implode('|', $arrAcceptDurations).')_'.$strVideoMd5Report.'\.mp4';
        } else {
            $strVideoNamePattern = '[a-z-]+/[0-9a-z_-]+\.[0-9a-z]{2,4}';
        }
        if (!preg_match('#^http://tb-video.bdstatic.com/'.$strVideoNamePattern.'$#i', $strVideoUrl)) {
            Bingo_Log::warning('validate video_url fail, value = '.$strVideoUrl);
            return false;
        }

        return true;
    }

    /**
     * @brief 安全地修改视频帖子属性..
     * @param $intThreadId
     * @param $arrKeyVals
     * @param string $strWeekKey
     * @return int
     */
    public static function updateThreadVideoInfo($intThreadId, $arrKeyVals, $strWeekKey = '')
    {
        // 过滤掉保留字段
        $arrReservedKeys = array(
            'video_url' => 1,
            'origin_video_url' => 1,
            'video_type' => 1,
            'video_log_id' => 1,
            'video_desc' => 1,
        );
        $arrKeyVals = array_diff_key($arrKeyVals, $arrReservedKeys);
        if (empty($arrKeyVals)) {
            Bingo_Log::warning('param error');
            return Tieba_Errcode::ERR_PARAM_ERROR;
        }

        // 获取帖子信息
        if (!Lib_Post::getThreadInfo($intThreadId, $arrThreadInfo) || !$arrThreadInfo) {
            Bingo_Log::warning('get thread info fail');
            return Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
        }
        $intPostId = $arrThreadInfo['first_post_id'];
        $arrExtAttr = empty($arrThreadInfo['video_info'])?array():$arrThreadInfo['video_info'];

        // 获取 weekkey
        if (empty($strWeekKey)) {
            if (isset($arrExtAttr['video_log_id'])) {
                $strWeekKey = substr($arrExtAttr['video_log_id'], -8);
            } else {
                $arrParams = array(
                    'cond' => array(
                        'thread_id' => $intThreadId,
                        'start_time' => $arrThreadInfo['create_time'] - 86400 * 15,
                        'end_time' => $arrThreadInfo['create_time'] + 1,
                    ),
                );
                $arrRet = self::selectVideoInfo($arrParams);
                if ($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('query video info fail');
                    return Tieba_Errcode::ERR_DB_QUERY_FAIL;
                }
                if (empty($arrRet['data'])) {
                    Bingo_Log::warning('no video info found');
                    return Tieba_Errcode::ERR_NO_RECORD;
                }
                $arrAuditInfo = $arrRet['data'][0];
                $strWeekKey = $arrAuditInfo['weekkey'];
            }
        }

        // 修改审核库
        $intErrorNo = Tieba_Errcode::ERR_SUCCESS;
        do {
            if (!self::startTransaction()) {
                Bingo_Log::warning('start transaction fail');
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            //获取 ext_param
            $arrParams = array(
                'cond' => array(
                    'thread_id' => $intThreadId,
                ),
                'weekkey' => $strWeekKey,
                'for_update' => true,
            );
            $arrRet = self::selectVideoInfo($arrParams);
            if ($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('query video info fail');
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
            if (empty($arrRet['data'])) {
                Bingo_Log::warning('no video info found');
                $intErrorNo = Tieba_Errcode::ERR_NO_RECORD;
                break;
            }
            $arrAuditInfo = $arrRet['data'][0];
            $arrExtParam = json_decode($arrAuditInfo['ext_param'], true);

            // 更新 ext_attr
            foreach ($arrExtParam['ext_attr'] as $value) {
                if($value['key'] == 'video_info' && $value['value']) {
                    $arrExtAttrDb = is_string($value['value']) ? unserialize($value['value']) : $value['value'];
                    !empty($arrExtAttrDb) && $arrExtAttr = array_merge($arrExtAttr, $arrExtAttrDb);
                    break;
                }
            }
            $arrExtAttr = array_merge($arrExtAttr, $arrKeyVals);
            foreach ($arrExtParam['ext_attr'] as &$value) {
                if($value['key'] == 'video_info') {
                    $value['value'] = $arrExtAttr;
                    break;
                }
            }

            // 回写ext_param
            $strFinalExtParam = mysql_escape_string(json_encode($arrExtParam));
            $arrParams = array(
                'field' => array(
                    'ext_param'     => $strFinalExtParam,
                ),
                'cond' => array(
                    'thread_id' => $strWeekKey,
                ),
                'table_name' => self::VDIEO_TABLE_PRE . $strWeekKey,
            );
            $arrRet = self::updateVideoInfo($arrParams);
            if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
                Bingo_Log::warning('update video info fail');
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            if (!self::commit()) {
                Bingo_Log::warning('commit fail');
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
        } while(0);

        if (Tieba_Errcode::ERR_SUCCESS != $intErrorNo) {
            if (!self::rollback()) {
                Bingo_Log::warning('rollback fail');
            }
            return $intErrorNo;
        }

        // 设置该帖子扩展属性
        if (!Lib_Post::setExtAttr($intThreadId, $intPostId, $arrExtAttr)) {
            Bingo_Log::warning('set thread attr fail');
            return Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
        }
        return Tieba_Errcode::ERR_SUCCESS;
    }

    /**
     * begin
     * @return bool
     */
    public static function startTransaction()
    {
        return Dl_Audit_Audit::startTransaction();
    }

    /**
     * commit
     * @return bool
     */
    public static function commit()
    {
        return Dl_Audit_Audit::commit();
    }

    /**
     * rollback
     * @return bool
     */
    public static function rollback()
    {
        return Dl_Audit_Audit::rollback();
    }

    /**
     * @param $intThreadId
     * @param $intVideoType
     * @return bool
     */

    public static function checkVideoRepeat($intThreadId, $intVideoType){

        $_SERVER['HTTP_X_BD_FORCE_IDC'] = 'gz'; // 指定机房
        $arrParam = array(
            'thread_id' => $intThreadId,
            'video_type' => $intVideoType,
        );
        $arrRet = Tieba_Service::call('inbound', 'videoRecomQuality', $arrParam, null, null, 'post', 'php', 'utf-8');
        if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning('call inbound::videoRecomQuality is failed, input='.serialize($arrParam)." outPut".serialize($arrRet));
            unset($_SERVER['HTTP_X_BD_FORCE_IDC']);
            return true;      // 重复度服务失败。依然入库
        }
        unset($_SERVER['HTTP_X_BD_FORCE_IDC']);
        return $arrRet['data']['is_rec'] < 2; // 2=IN_NANI_REC, 3 = IN_NANI_TIEBA_REC
    }
    /**
     * 错误信息
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function arrRet($errno, $data = null){
        return array(
            'errno'     => $errno,
            'errmsg'    => Tieba_Error::getErrmsg($errno),
            'data'      => $data,
        );
    }

    /**
     * @TODO
     * @brief 根据一系列策略，判断一个视频是否满足阀值条件，满足返回true，不满足返回false
     * @param array
     * @return bool
     */
    public static function isAbleToAudit($arrInput) {
        return true;
    }

    /**
     * @brief 有一些视频类型不需要转码，就可以直接进审核库或者发帖，这里获取当前视频来源是否需要转码
     *          注意这个和getPreTranscode等函数的差别，他们是判断是先转码还是后转码，但都得转码，而本函数判断出来的视频是需不需要转码
     * @param $intVideoType
     * @return bool true => need; false => un-need
     */
    public static function isNeedTranscodeType($intVideoType) {
        $arrTypes = array(
            Molib_Util_Video::TEMP_VIDEO_FROM_ODYSSEY
        );

        if (in_array($intVideoType, $arrTypes)) {
            return false;
        }
        else {
            return true;
        }
    }

    /**
     * @brief 直接发帖？如果是的话，则将审核库的审核状态直接置为审核通过，并且直接发帖
     * @param $intVideoType
     * @return bool true => direct; false => un-direct
     */
    public static function isDirectAddThread($intVideoType) {
        if (Util_Const::$_arrVTypeStatusMap[$intVideoType] === Util_Const::AUDIT_STATUS_PASS) {
            return true;
        }
        else {
            return false;
        }
    }

    /**
     * @brief 写reids，使用incr表明转码or切帧有一个完成了，并将值返回给上游
     * @param $intVideoAuditId
     * @param $intWeekkey
     * @return mixed
     */
    public static function writeRedisTranscodeDone($intVideoAuditId, $intWeekkey) {
        return self::_writeRedisIncrForCommitOdyssey($intVideoAuditId, $intWeekkey);
    }

    /**
     * @brief 写reids，使用incr表明转码or切帧有一个完成了，并将值返回给上游
     * @param $intVideoAuditId
     * @param $intWeekkey
     * @return mixed
     */
    public static function writeRedisCutFrameDone($intVideoAuditId, $intWeekkey) {
        return self::_writeRedisIncrForCommitOdyssey($intVideoAuditId, $intWeekkey);
    }

    /**
     * @brief 将
     * @param $intVideoAuditId
     * @param $intWeekkey
     * @return array|bool
     */
    private static function _writeRedisIncrForCommitOdyssey($intVideoAuditId, $intWeekkey) {
        $objRedis = new Bingo_Cache_Redis('sign');
        $strKey = self::WRITE_REDIS_PREFIX . $intVideoAuditId . '_' . $intWeekkey;

        $arrInput = array(
            'key' => $strKey,
            'expire' => 3600,
        );

        $arrOutput = $objRedis->incr($arrInput);

        if ($arrOutput['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            return false;
        }
        else {
            return array('value' => $arrOutput['ret'][$strKey]);
        }
    }

    /**
     * @warning 不要乱用这个发号器函数，除非找arch或者相关人员确认
     * @brief 调用arch的发号器直接，获取一个tid，但是此时这个tid并未真正发帖，所以不要乱传这个tid
     * @return int thread_id
     */
    private static function _getUnUsedTid() {
        $ids = Tieba_Idalloc::alloc('gthread_id');

        Bingo_Log::notice("[video] gthread_id :" .json_encode($ids));

        if (empty($ids[0])) {
            Bingo_Log::warning(sprintf('failed to get gthread_id .'));
            return 0;
        }else{
            return intval($ids[0]);
        }
    }

    /**
     * @brief 调用提交物料库接口
     * @param $arrInput
     * @return array
     */
    public static function commitOdysseyVideo($arrInput) {
        // 先生成一个tid，方便存储到物料库
        $intThreadId= self::_getUnUsedTid();

        if ($intThreadId == 0) {
            Bingo_Log::fatal('call arch to get unused thread id failed!');
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'can\'t get tid from arch');
        }

        // 提交到物料库
        $arrCommitOdysseyInput = array(
            'video_audit_id'    => $arrInput['video_audit_id'],
            'weekkey'           => $arrInput['weekkey'],
            'thread_id'         => $intThreadId,
        );

        $arrCommitOdysseyOutput = Tieba_Service::call('thirdparty', 'commitOdysseyVideo', $arrCommitOdysseyInput, null, null, 'post', 'php', 'utf-8');

        if ($arrCommitOdysseyOutput === false || $arrCommitOdysseyOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal('call thirtparty::commitOdysseyVideo failed! input: ' . serialize($arrCommitOdysseyInput) . '     output: ' . serialize($arrCommitOdysseyOutput));
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        // nid和message字段写回审核库
        $arrDBInput = array(
            'field' => array(
                'nid' => $arrCommitOdysseyOutput['data']['nid'],
                'odyssey_message' => json_encode(array('predistributed_thread_id' => $intThreadId,)),
            ),
            'cond' => array(
                'id' => $arrInput['video_audit_id'],
            ),
            'table_name' => Lib_Audit::VDIEO_TABLE_PRE . $arrInput['weekkey'],
        );

        $arrDBOutput = Lib_Audit::updateVideoInfo($arrDBInput);

        if (Tieba_Errcode::ERR_SUCCESS !== $arrDBOutput['errno']) {
            Bingo_Log::fatal('update odyssey message to audit db failed! [input: ' . serialize($arrDBInput) . '] [output: ' . serialize($arrDBOutput) . ']');
            return Lib_Audit::arrRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return Lib_Audit::arrRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief 判断入参数字是否满足提交物料库的要求
     * @param $intScore
     * @return bool
     */
    public static function canCommitCenterCtl($intScore, $intVideoAuditId, $intWeekkey) {
        // 2 表示转码和切帧都完成了
        if ($intScore == 2) {
            $objRedis = new Bingo_Cache_Redis('sign');
            $strKey = self::WRITE_REDIS_PREFIX . $intVideoAuditId . '_' . $intWeekkey;

            $arrInput = array(
                'key' => $strKey,
            );

            $arrOutput = $objRedis->del($arrInput);

            return true;
        }

        return false;
    }

    /**
     * @brief 给审核库用的基础库，当审核平台完成任意操作后，将这一操作接过同步至物料库
     * @param $arrInput array update 1 => 审核通过;  update 0 => 审核不通过; delete => 删帖; recover => 恢复删除的帖子
     * @return mixed
     */
    public static function informOdysseyAfterAudited($arrInput) {
        if ($arrInput['type'] === 'update') {
            $arrOdysseyInput = array(
                'audited' => array(
                    'value' => intval($arrInput['value']),
                ),
                'odyssey_message' => $arrInput['odyssey_message'],
                'op_uid' => $arrInput['op_uid'],
                'op_uname' => $arrInput['op_uname'],
                'op_time' => $arrInput['op_time'],
            );

            $arrOutput = Tieba_Service::call('thirdparty', 'updateOdysseyVideo', $arrOdysseyInput, null, null, 'post', 'php', 'utf-8');
        }
        else if ($arrInput['type'] === 'intervene_delogo') {
            $arrOdysseyInput = array(
                'intervene' => array(
                    'displaytype_exinfo' => array(
                        'video_desc' => $arrInput['value'],
                    ),
                ),
                'localation' => array(
                    'weekkey' => isset($arrInput['weekkey']) ? $arrInput['weekkey'] : self::getThatDayMonday($arrInput['thread_create_time']),
                    'thread_id' => $arrInput['thread_id'],
                ),
            );
            $arrOutput = Tieba_Service::call('thirdparty', 'updateOdysseyVideo', $arrOdysseyInput, null, null, 'post', 'php', 'utf-8');
        }
        else if ($arrInput['type'] === 'intervene_edit') {
            $arrOdysseyInput = array(
                'intervene' => array(
                    'strategy_exinfo' => $arrInput['value'],
                ),
                'localation' => array(
                    'weekkey' => isset($arrInput['weekkey']) ? $arrInput['weekkey'] : self::getThatDayMonday($arrInput['thread_create_time']),
                    'thread_id' => $arrInput['thread_id'],
                ),
            );

            if (isset($arrInput['is_delay']) && $arrInput['is_delay'] > 0) {
                $arrOutput = Lib_CenterCtl::commitCenterCtlIntervene($arrOdysseyInput, intval($arrInput['is_delay']));
            } else {
                $arrOutput = Tieba_Service::call('thirdparty', 'updateOdysseyVideo', $arrOdysseyInput, null, null, 'post', 'php', 'utf-8');
            }
        }
        else if ($arrInput['type'] === 'delete') {
            $arrOdysseyInput = array(
                'thread_id' => $arrInput['thread_id'],
            );

            $arrOutput = Tieba_Service::call('thirdparty', 'deleteOdysseyVideo', $arrOdysseyInput, null, null, 'post', 'php', 'utf-8');
        }
        else if ($arrInput['type'] === 'recover') {
            $arrOdysseyInput = array(
                'thread_id' => $arrInput['thread_id'],
                'thread_create_time' => $arrInput['thread_create_time'],
            );

            $arrOutput = Tieba_Service::call('thirdparty', 'recoverOdysseyVideo', $arrOdysseyInput, null, null, 'post', 'php', 'utf-8');
        }

        Bingo_Log::warning(sprintf("call odysseyVideo input[%s] output[%s]", json_encode($arrOdysseyInput), json_encode($arrOutput)));
        if ($arrOutput === false || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal(sprintf("call informOdysseyAfterAudited failed! input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
        }

        return $arrOutput;
    }

    /**
     * @brief 通过thread_id获取video信息
     * @param array $arrInput array('thread_id' => 123,'create_time'=> 123123)
     * @return array
     */
    public static function getVideoInfoByTid($arrInput){
        $strTableName = self::getThatDayMonday($arrInput['create_time']);
        $arrDlInput = array(
            'function' => 'selectAuditByTid',
            'table_name' =>  self::VDIEO_TABLE_PRE . $strTableName,
            'thread_id' => $arrInput['thread_id'],
        );
        $arrDlOutput = Dl_Audit_Audit::execSql($arrDlInput);
        if( $arrDlOutput=== false || $arrDlOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(sprintf('call Dl_Audit_Audit::execSql fail, input: %s, output: %s',serialize($arrDlInput),serialize($arrDlOutput)));
            return false;
        }
        if (empty($arrDlOutput['results']) || empty($arrDlOutput['results'][0]) || empty($arrDlOutput['results'][0][0])){
            Bingo_Log::warning(sprintf('call Dl_Audit_Audit::execSql empty, input: %s, output: %s',serialize($arrDlInput),serialize($arrDlOutput)));
            return array();
        }
        return $arrDlOutput['results'][0][0];
    }

    /**
     * @brief 获取 odyssey_message 字段内的 视频清晰度字段
     * @param int $intThreadId
     * @param int $intCreateTime 创建时间
     * @return float
     */
    public static function getVideoClarityScore($intThreadId,$intCreateTime){
        $arrInput = array(
            'thread_id' => $intThreadId,
            'create_time' => $intCreateTime,
        );
        $arrOutput = self::getVideoInfoByTid($arrInput);
        if ($arrOutput === false || empty($arrOutput) ){
            $arrInput['create_time'] = $arrInput['create_time'] - 60*60; // 发帖和视频信息入库时间可能存在差异，导致不同表
            $arrOutput = self::getVideoInfoByTid($arrInput);
            if ($arrOutput === false || empty($arrOutput) ){
                Bingo_Log::warning(sprintf('call Lib_Audit::getVideoInfoByTid fail or empty,return score=0, input: %s, output: %s',serialize($arrInput),serialize($arrOutput)));
                return 0;
            }
        }

        $arrOdysseyMsg = json_decode($arrOutput['odyssey_message'],true);
        if (empty($arrOdysseyMsg) || empty($arrOdysseyMsg['commit_callback']['data']['vu']['video_clarity_score'])){
            Bingo_Log::warning(sprintf('call Lib_Audit::getVideoInfoByTid sharpness empty, return score=0, input: %s, output: %s',serialize($arrInput), serialize($arrOdysseyMsg)));
            return 0;
        }else{
            return floatval($arrOdysseyMsg['commit_callback']['data']['vu']['video_clarity_score']);
        }
    }

    /**
     * 调用ueg接口查看这个视频贴的user title abstrct是否是广告文本
     */
    public static function isUegTextAds($strVideoId) {
        if (empty($strVideoId)) {
            return false;
        }

        $arrKeys = explode('_', $strVideoId);
        if (sizeof($arrKeys) != 2) {
            return false;
        }

        $strWeekkey = $arrKeys[0];
        $intId = $arrKeys[1];

        $arrDBInput = array(
            'cond' => array(
                'id'  => $intId,
            ),
            'weekkey' => $strWeekkey,
        );
        $arrDBOutput = self::selectVideoInfo($arrDBInput);

        if ($arrDBOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf("call self::selectVideoInfo failed! input[%s] output[%s]", json_encode($arrDBInput), json_encode($arrDBOutput)));
            return false;
        }
        if (empty($arrDBOutput['data'])) {
            Bingo_Log::warning(sprintf("call self::selectVideoInfo empty! input[%s] output[%s]", json_encode($arrDBInput), json_encode($arrDBOutput)));
            return false;
        }

        $arrAuditInfo = $arrDBOutput['data'][0];
        $arrExtParam = json_decode($arrAuditInfo['ext_param'], true);

        $arrInput = array(
            'data' => array(
                'title'          => strval($arrDBOutput['data'][0]['title']),
                "abstract"       => strval($arrExtParam['ui_trans_params']['video_abstract']),
                "user_id"        => intval($arrDBOutput['data'][0]['user_id']),
                "user_name"      => strval($arrDBOutput['data'][0]['user_name']),
                "forum_name"     => strval($arrDBOutput['data'][0]['forum_name']),
                "forum_id"       => intval($arrDBOutput['data'][0]['forum_id']),
                "is_multi_forum" => intval($arrExtParam['is_multi_forum']),
                "is_work"        => $arrDBOutput['data'][0]['video_type'] == 20 ? 1 : 0,
                "is_pc_publish"  => $arrDBOutput['data'][0]['video_type'] == 8 ? 1 : 0,
                "create_time"    => intval($arrDBOutput['data'][0]['create_time']),
                "video_name"     => strval($arrDBOutput['data'][0]['video_name']),
            ),
        );

        $arrHeader = array(
            'Host'     => 'service.tieba.baidu.com',
            'service'  => 'antiserver',
            'method'   => 'antiServer',
        );
        $arrIdcList = array('nj', 'nj03');
        $strIdcKey  = array_rand($arrIdcList, 1);
        $strIdc     = isset($arrIdcList[$strIdcKey]) ? trim($arrIdcList[$strIdcKey]) : 'nj';
        ral_set_idc($strIdc);
        ral_set_pathinfo("/service/antiserver");
        ral_set_querystring("ie=utf-8&method=tbvideoContent&format=json&strict_format=1");
        $strRes = ral('service_antiserver_tieba_app' , 'post' , $arrInput , rand(), $arrHeader);
        Bingo_Log::warning(sprintf("prepare call antiserver::tbvideoContent input[%s] output[%s]", json_encode($arrInput), json_encode($strRes)));
        $arrOutput = json_decode($strRes, true);
        if (!$arrOutput || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf("call antiserver::tbvideoContent failed! input[%s] output[%s]", json_encode($arrInput), json_encode($arrOutput)));
            return false;
        }

        // 作品一律先不拦截
        if ($arrDBOutput['data'][0]['video_type'] == 20) {
            return false;
        }

        if ($arrOutput['res']['is_pass'] == 0) {
            return true;
        }

        return false;
    }

    public static function getUegStatus($arrUegRes) {
        if(isset($arrUegRes['is_overtime']) && $arrUegRes['is_overtime'] == 1) {
            return Util_Const::UEG_STATUS_OVERTIME;
        }
        if(!empty($arrUegRes['video_heku'])) {
            return Util_Const::UEG_STATUS_HEKU;
        }
        if(!empty($arrUegRes['image_heku']['hit_list'])){
            return Util_Const::UEG_STATUS_IMAGE_HEKU;
        }
        if($arrUegRes['video_politics']['asr']['is_sensitive'] == 1 || $arrUegRes['video_politics']['title']['is_sensitive'] == 1
            || $arrUegRes['video_politics']['face']['is_sensitive'] == 1 || $arrUegRes['video_politics']['ocr']['is_sensitive'] == 1) {
            return Util_Const::UEG_STATUS_POLITICS;
        }

        if($arrUegRes['video_politics']['asr']['is_sensitive'] === null || $arrUegRes['video_politics']['title']['is_sensitive'] === null
            || $arrUegRes['video_politics']['face']['is_sensitive'] === null || $arrUegRes['video_politics']['ocr']['is_sensitive'] === null) {
            return Util_Const::UEG_STATUS_OVERTIME;
        }

        return Util_Const::UEG_STATUS_PASS;
    }

    /**
     * @param $uid
     * 通过uid获取用户信息
     */
    private static function mgetUserDataByUid($uid) {
        if (empty($uid) || !is_numeric($uid)) {
            return array();
        }

        $input = array(
            "user_id" => array($uid)
        );
        $user = Tieba_Service::call('user', 'mgetUserData', $input, null, null, 'post', 'php', 'utf-8');

        if (!isset( $user ['errno'] ) || $user['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call service failed:mgetUserData input: ".serialize($input).', res: '.serialize($user));
            return array();
        }

        return $user;
    }

    /**
     * @param $user
     * 判断用户是否大神
     */
    private static function getGodUserRet($user, $uid) {
        $isGodUser   = false; // 是否是大神号

        if(isset($user['user_info'][$uid]['god_info']['status']) && 3 == $user['user_info'][$uid]['god_info']['status'])
        {
            $isGodUser = true;
        }

        return $isGodUser;
    }

    /**
     * 推送超审的数据生成基础库。这里入参是审核表里面的完整记录，基于这个数据生成推送到超审那边，用于落盘的审核可视化数据
     * unittested
     */
    public static function sendAudit($arrAuditInfo, $intAuditStatus = 0) {
        $strAuditPlatAK = 'C6vOSQw2xIqMd2xQrd1ygnArEem6MBVi';
        $strAuditPlatSK = 'Ts8aw63KjTwRxBTwDP9MPyhOgRBnzRX4';
        $strAuditPlatFrom = 'tieba';
        $strAuditPlatModule = 'tieba';
        $intTs = time();
        $strTk = hash_hmac('sha256', $strAuditPlatAK . $intTs, $strAuditPlatSK);

        $strAuditKey = $arrAuditInfo['weekkey'] . '_' . $arrAuditInfo['id'];

        // 解析核心数据
        $arrOdysseyMessage = json_decode($arrAuditInfo['odyssey_message'], true);
        $arrExtParam = json_decode($arrAuditInfo['ext_param'], true);
        $arrVideoInfo = array();
        foreach ($arrExtParam['ext_attr'] as $v) {
            if ($v['key'] == 'video_info') {
                $arrVideoInfo = $v['value'];
                break;
            }
        }

        // 用转码后视频覆盖原始视频
        if (isset($arrVideoInfo['video_desc'][0])) {
            $arrVideoInfo = array_merge($arrVideoInfo, $arrVideoInfo['video_desc'][0]);
        }
        // 将视频链接转为内网bos链接
        if (strpos($arrVideoInfo['video_url'], 'tb-video.bdstatic.com') !== false) {
            $arrVideoInfo['video_url'] = str_replace('tb-video.bdstatic.com', 'su.bcebos.com', $arrVideoInfo['video_url']);
        }

        //将视频封面图压缩
        if(!empty($arrVideoInfo['thumbnail_url'])){
            //如果有pic_id直接用pic_id压缩，没有先获取pic_id
            $thumbnailPicId=$arrVideoInfo['thumbnail_picid']?:Util_Pic::getPicIdFromUrl($arrVideoInfo['thumbnail_url']);
            if($thumbnailPicId){
                $arrVideoInfo['thumbnail_url']=Util_Pic::pid2url($thumbnailPicId,'w=960;q=60');
            }else{
                Bingo_Log::warning(sprintf("video audit get pic_id error,url is: [%s],pic_id result is:[%s]", $arrVideoInfo['thumbnail_url'],$thumbnailPicId));
            }
        }

        // 将封面链接转为内网链接
        if (strpos($arrVideoInfo['thumbnail_url'], 'tiebapic.baidu.com') !== false) {
            $arrVideoInfo['thumbnail_url'] = str_replace('tiebapic.baidu.com', 'tiebapic0.baidu.com', $arrVideoInfo['thumbnail_url']);
        }

        Bingo_Log::warning(sprintf("video audit thubmnail_url is[%s]", $arrVideoInfo['thumbnail_url']));


        $intThreadId = $arrOdysseyMessage['predistributed_thread_id'];

        $arrMultiOut = self::_sendAuditMultiCall($arrAuditInfo);
        
        $arrFrameInfo = $arrMultiOut['frame_info'];
        $arrAuthorInfo = $arrMultiOut['author_info'];


        $forum_name = strval($arrAuditInfo['forum_name']);
        $user_id =  strval($arrAuditInfo['user_id']);
        // 红单用户、高危吧 标志
        if ($arrAuditInfo['hitWordList']) {
            $isRedListUser = $arrAuditInfo['hitWordList'][Service_Audit_Audit::ANTI_REDLIST_USER];
            if ($isRedListUser === 1) { // 红单用户，红底白字
                $user_id .= '<span style="display: inline-block; background: red; color: white; padding: 10px;">红单用户</span>';
            }
            $isHighRiskForum = $arrAuditInfo['hitWordList'][Service_Audit_Audit::ANTI_HIGHRISK_FORUM];
            if ($isHighRiskForum === 1) { // 高危吧，黄底红字
                $forum_name .= '<span style="display: inline-block; background: yellow; color: red; padding: 10px;">高危吧</span>';
            }
        }

        $arrAuditRecord = array(
            'app_id' => self::AUDIT_PLAT_APP_ID,
            'data' => array(
                'article_id' => strval($intThreadId), 
                'publish_time' => strval($arrAuditInfo['create_time']),
                'title' => strval($arrAuditInfo['title']),
                'status' => 'draft',
                'contents' => array(
                    array( // 可视化切帧图
                        'ele_id' => 1,
                        'ele_name' => '切帧图',
                        'type' => 1,
                        'sub_type' => 5,
                        'content' => '空',
                        'content_html' => self::genCutFrameViewHtml($arrFrameInfo['frame_list'], $arrVideoInfo['thumbnail_url']),
                    ),
                    array( // 可视化视频播放
                        'ele_id' => 2,
                        'ele_name' => '视频内容',
                        'type' => 3,
                        'sub_type' => 5,
                        'content' => json_encode(array(
                            'url' => $arrVideoInfo['video_url'],
                            'cover_url' => $arrVideoInfo['thumbnail_url'],
                            'width' => intval($arrVideoInfo['video_width']),
                            'height' => intval($arrVideoInfo['video_height']),
                            'protocol' => 'http',
                        )),
                    ),
                ),
                'url' => 'http://tieba.baidu.com/p/' . $intThreadId,
                'sub_product' => 'tieba_video',
            ),
            'extra_data' => array(
                'user_id' => $user_id,
                'user_name' => strval($arrAuditInfo['user_name']),
                'assist_info' => array(
                    array(
                        'type' => 'text',
                        'name' => '吧名',
                        'value' => $forum_name,
                    ),
                    array(
                        'type' => 'text',
                        'name' => '视频时长',
                        'value' => strval($arrVideoInfo['video_duration']),
                    ),
                    array(
                        'type' => 'image',
                        'name' => '用户头像',
                        'value' => 'http://tb.himg.baidu.com/sys/portrait/item/' . Tieba_Ucrypt::encode($arrAuditInfo['user_id'], $arrAuditInfo['user_name']),
                        'url' => 'http://tb.himg.baidu.com/sys/portrait/item/' . Tieba_Ucrypt::encode($arrAuditInfo['user_id'], $arrAuditInfo['user_name']),
                    ),
                    array(
                        'type' => 'text',
                        'name' => '标题',
                        'value' => strval($arrAuditInfo['title']),
                    ),
                ),
                'original_content' => array(  // 回调字段
                    'audit_key' => strval($strAuditKey),
                ),
                'parent_id' => strval($arrAuditInfo['forum_name']),
                // 如果下游中变量$isNeedEnter定义说明是大神视频贴 需要推进优质作者审核区审核 否则按历史逻辑执行
                'is_excellent_author' => intval(self::isExcellentAuthor($arrAuthorInfo, $arrAuditInfo)),
            ),
        );

        if ($intAuditStatus == Util_Const::AUDIT_STATUS_DELETE) {
            $arrAuditRecord['extra_data']['just_refused'] = 1;  
        }

        $arrHeader = array(
            'pathinfo' => "/auditreceiverapi",
            'querystring'=> sprintf("module=%s&from=%s&ak=%s&ts=%s&tk=%s", $strAuditPlatModule, $strAuditPlatFrom, $strAuditPlatAK, $intTs, $strTk),
            'content-type' => "application/json",
        );
        
        Bingo_Log::warning(sprintf("prepare call audit platform. header[%s] body[%s]", json_encode($arrHeader), json_encode($arrAuditRecord)));

        $arrOutput = ral("service_audit_platform", "post", $arrAuditRecord, null, $arrHeader);

        // 这里不是贴吧的服务，用ERRCODE容易迷惑，所以写0
        if (!$arrOutput || $arrOutput['errno'] != 0) {
            Bingo_Log::fatal(sprintf("call audit platform failed! input[%s] output[%s]", json_encode($arrAuditRecord), json_encode($arrOutput)));
            return false;
        }

        return true;
    }

    /**
     * 生成切帧图浏览html，直接渲染到前端用的
     * unittested
     */
    public static function genCutFrameViewHtml($arrInfo, $strCoverUrl = '') {
        $strHtml = '';


        if (!empty($strCoverUrl)) {
            $strHtml = $strHtml . sprintf("<img src=\"%s\" width=\"33.3%%\">", $strCoverUrl);
        }

        foreach ($arrInfo as $k => $v) {
            $strHtml = $strHtml . sprintf("<img src=\"%s\" width=\"33.3%%\">", $v);
        }

        return $strHtml;
    }



    /**
     * 用来推送超审时，并行rpc的集合。
     * unittested
     */
    public static function _sendAuditMultiCall($arrAuditInfo) {
        $objMutiCall = new Tieba_Multi('audit_multi');

        $strVideoUrl    = strval($arrAuditInfo['video_url']);
        $arrFileName    = explode("/", $strVideoUrl);
        $strFileName    = end($arrFileName);
        $strTemp        = explode(".", $strFileName);
        $strVideoName   = $strTemp[0];
        
        $arrAllInput = array();

        $arrFrameInput = array(
            "serviceName" => 'video',
            "method" => 'getVideoFrameForAuditMis',
            'ie'     => 'utf-8',
            "input"  => array(
                'video_name' => $strVideoName,
            ),
        );
        $arrAllInput['getVideoFrameForAuditMis'] = $arrFrameInput;
        $objMutiCall->register('getVideoFrameForAuditMis', new Tieba_Service('video'), $arrFrameInput);

        $intAuthorId = $arrAuditInfo['user_id'];
        $arrAuthorInput = array(
            "serviceName" => 'video',
            "method" => 'isAuthor',
            'ie'     => 'utf-8',
            "input"  => array(
                'uid' => $intAuthorId,
            ),
        );
        $arrAllInput['isAuthor'] = $arrAuthorInput;
        $objMutiCall->register('isAuthor', new Tieba_Service('video'), $arrAuthorInput);

        Bingo_Timer::start('send_audit_multi');
        $objMutiCall->call();
        Bingo_Timer::end('send_audit_multi');

        $arrRet = array();
        foreach ($arrAllInput as $key => $value) {
            $arrOutput = $objMutiCall->getResult($key);
            if(!$arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
                Bingo_Log::warning(sprintf("call %s failed! input[%s] output[%s]", $key, json_encode($value), json_encode($arrOutput)));
                continue;
            }

            if ($key === 'getVideoFrameForAuditMis') {
                $arrRet['frame_info'] = array(
                    'frame_list' => $arrOutput['data']['video_frame_list'],
                    'extract_state' => $arrOutput['data']['extract_state'],
                );
            }

            if ($key === 'isAuthor') {
                $arrRet['author_info'] = $arrOutput['data'];
            }
        }

        return $arrRet;
    }

    /**
     * 生成给用户看的通过审核push消息
     * unittested
     */
    public static function genPassPush($arrInfo, $arrPushInfo) {
        $arrPushInfo['content'] = sprintf($arrPushInfo['content'], "https://tieba.baidu.com/p/" . $arrInfo['thread_id'] . '?is_official_video=true ');
        $arrPushRet = self::_genPushInfo($arrInfo, $arrPushInfo);

        $arrPushInfo['content'] = sprintf($arrPushInfo['content'], "https://tieba.baidu.com/p/" . $arrInfo['thread_id']);
        $arrPcPushRet = self::_genPushInfo($arrInfo, $arrPushInfo);
                  
        return array(
            'push' => $arrPushRet['push'],
            'pc_push' => $arrPcPushRet['pc_push'],
        );
    }

    /**
     * 通过词表，获取展示给用户看的拒绝原因并生成对应的push消息
     * unittested
     */
    public static function genRefusedPush($arrInfo, $arrPushInfo, $strTitle) {
        $arrRedisInput = array(
            'field' => array(
                strval($arrInfo['delete_reason']),
            ),
            'table_name' => 'tb_wordlist_redis_video_delete_reason',
        );
        $arrRedisOutput = Lib_WordList::get($arrRedisInput);

        $strPreMsg = strval($arrRedisOutput[$arrInfo['delete_reason']]);
        Bingo_Log::warning(sprintf("delete reason to user push msg: %s", json_encode($strPreMsg)));

        if (empty($strPreMsg)) {
            Bingo_Log::warning(sprintf("get delete reason fail.input[%s] output[%s]", json_encode($arrRedisInput), json_encode($arrRedisOutput)));
            $strPreMsg = '其他';
        }
        
        $arrPushInfo['content'] = sprintf($arrPushInfo['content'], date('n月j日', $arrInfo['create_time']), $strTitle, $strPreMsg);
        return self::_genPushInfo($arrInfo, $arrPushInfo);
    }

    /**
     * 生成展示给用户看的视频贴恢复文案
     * unittested
     */
    public static function genRecoverPush($arrInfo, $arrPushInfo) {
        $arrPushInfo['content'] = sprintf($arrPushInfo['content'], "https://tieba.baidu.com/p/" . $arrInfo['thread_id']);
        return self::_genPushInfo($arrInfo, $arrPushInfo);
    }

    /**
     * 构建push数据结构
     * unittested
     */
    public static function _genPushInfo($arrInfo, $arrPushInfo) {
        $arrPushInput = array(
            'thread_id'     => $arrInfo['thread_id'],
            'create_time'   => $arrInfo['create_time'],
            'user_id'       => $arrInfo['user_id'],
            'push'          => array(
                'title'     => $arrPushInfo['title'],
                'content'   => $arrPushInfo['content'],
                'task_id'   => $arrPushInfo['task_id'],
                'service_id'=> $arrPushInfo['service_id'],
                'user_id'   => $arrPushInfo['user_id'],
                'user_name' => $arrPushInfo['user_name'],
                'link'      => $arrPushInfo['link'],
                'image_url' => $arrPushInfo['image_url'],
            ),
        );
        
        $arrPcPushInput = array(
            'user_id'   => $arrInfo['user_id'],
            'content'   => $arrPushInfo['content'],
        );

        return array(
            'push' => $arrPushInput,
            'pc_push' => $arrPcPushInput,
        );
    }

    /**
     * 获取用户是否是优质作者
     * unittested
     */
    public static function isExcellentAuthor($arrAuthorInfo, $arrAuditInfo) {

        // 得到用户数据信息
        $userGodInfo   = self::mgetUserDataByUid($arrAuditInfo['user_id']);
        // 判断用户是否大神
        $isGodUser     = self::getGodUserRet($userGodInfo, $arrAuditInfo['user_id']);
        if ($isGodUser) {
            // 是大神
            // 大神创建时间
            $godCreatetime = 0;
            if(!empty($userGodInfo['user_info'][$arrAuditInfo['user_id']]['god_info']['update_time'])) {
                $godCreatetime = $userGodInfo['user_info'][$arrAuditInfo['user_id']]['god_info']['update_time'];
            }

            //发帖时间
            $threadCreateTime = 0;
            if(!empty($arrAuditInfo['create_time'])) {
                $threadCreateTime = $arrAuditInfo['create_time'];
            }

            Bingo_Log::notice('sendAudit::video god audit user_id='. $arrAuditInfo['user_id']. ' godCreatetime='.$godCreatetime.
                ' threadCreateTime='.$threadCreateTime);
            // 发视频帖时间大于成为大神时间  说明需要进优质作者审核区
            if ($threadCreateTime > $godCreatetime) {
                return 1;
            }
        }

        if (in_array($arrAuthorInfo['author_type'], self::$_arrHighQualityAuthor)) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 将数据生成能够直接调用amisThreadAudit的格式
     * unittested
     */
    public static function genAuditThreadInput($arrInput) {
        $arrDBRecord = $arrInput['audit_info'];
        $arrTidRecord = $arrInput['thread_info'];
        $arrCallbackRecord = $arrInput['callback_info'];

        $intPostId = 0;
        $intThreadId = 0;
        if (isset($arrTidRecord['first_post_id'])) {
            $intPostId = $arrTidRecord['first_post_id'];
        }
        if (isset($arrTidRecord['thread_id'])) {
            $intThreadId = $arrTidRecord['thread_id'];
        }

        $arrAuditInput= array(
            'video_audit_id' => $arrDBRecord['id'],
            'post_id'        => $intPostId,
            'thread_id'      => $intThreadId,
            'weekkey'        => $arrDBRecord['weekkey'],
            'forum_id'       => $arrDBRecord['forum_id'],

            //TODO 注释了下述逻辑，记得测试一下无吧视频贴
            //支持多吧发帖 start 
            //'v_forum_ids'    => array(), // TODO
            // 'is_multi_forum' => 0, // TODO
            //支持多吧发帖 end

            'forum_name'     => $arrDBRecord['forum_name'],
            'user_id'        => $arrDBRecord['user_id'],
            'user_name'      => $arrDBRecord['user_name'],
            //'user_ip'        => $arrItem['user_ip'], // TODO 注释掉了，似乎没啥用
            'title'          => $arrDBRecord['title'],
            'title_base64'   => $arrDBRecord['title_base64'],
            'create_time'    => $arrDBRecord['create_time'],
            'pre_status'     => $arrDBRecord['audit_status'],
            'video_url'      => $arrDBRecord['video_url'],
            'delete_reason'  => strval($arrCallbackRecord['delete_reason']),
            'video_log_id'   => intval($arrDBRecord['video_log_id']),
            'op_uid'         => 1, // 超审专用op uid
            'op_uname'       => 'super_audit_platform', 
            //'op_ip'          => $intUserIp, // TODO
            'op_type'        => intval($arrCallbackRecord['op_type']), 
             //'audit_flag'     => $intAuditFlag, // TODO 审核通过是1
            'push_to_recom'  => 0,
            'date'           => time(),
            'call_from'      => 'xiaoying_mis',
        );

        return $arrAuditInput;
    }
}