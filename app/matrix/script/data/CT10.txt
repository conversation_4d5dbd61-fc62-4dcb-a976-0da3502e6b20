
=============================================================
 提交接口
=============================================================



-------------------------------------------------------------
1 测试方法
-------------------------------------------------------------


1.1 列出所有入参，根据业务需求，考虑每个入参的取值可能性，发起提交请求，检查存储和返回是否正确

1.2 如果该提交接口是暴漏给前端用户的，需要重点考虑修改入参为异常值的作弊行为


-------------------------------------------------------------
2 测试点
-------------------------------------------------------------


2.1 入参正常值提交

2.1.1 安全

2.1.2 检查提交后的返回值

2.1.3 检查提交后的存储

2.1.3.1 cache

2.2 入参异常判断

2.2.1 通过前端提交

2.2.1.1 异常参数拒绝，返回提示信息

2.2.2 暴漏给贴吧用户的接口，考虑作弊行为，修改相应参数，绕过前端提交请求

2.2.2.1 必填项检查

2.2.2.2 参数的异常值

2.2.2.3 重点考虑权限判断

2.2.2.3.1 修改入参值，当前角色是否有权限做该提交操作

2.2.2.3.1.1 eg：删除或者封禁接口，修改接口中的吧名参数，是否有权限提交

2.3 是否涉及外部接口

2.4 数据一致性

2.4.1 幂等性

2.4.1.1 连续两次入参相同的提交 结果与第一次成功提交无差别

2.5 ueg

2.5.1 粒度

2.5.2 敏感词

