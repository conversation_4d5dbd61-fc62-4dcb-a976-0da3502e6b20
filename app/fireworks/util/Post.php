<?php
class Util_Post{
	public static function getPORThreads($arrInput){
		if(!isset($arrInput['redis']) ||!isset($arrInput['key'])){
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$redis = $arrInput['redis'];
		$key = $arrInput['key'];
		
		$arrParams = array(
				"key" => $key,
		);
		$arrRet = Util_Redis::redisQuery($redis, "HGETALL", $arrParams);
		if($arrRet['err_no'] !== 0){
			Bingo_Log::warning("call redis error.[input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
		}
		$data = array();
		foreach($arrRet['ret'][$key] as $item){
			$data[] = array(
					'forum_id' => intval($item['field']),
					'thread_id' => intval($item['value']),
			);
		}
		
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$data);
	}
	public static function getForumUniqueThread($arrInput){
		if(!isset($arrInput['redis']) ||!isset($arrInput['key']) || !isset($arrInput['forum_id'])){
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$redis = $arrInput['redis'];
		$key = $arrInput['key'];
		$forum_id = $arrInput['forum_id'];
		
		$arrParams = array(
				"key" => $key,
				"field" => $forum_id,
		);
		$arrRet = Util_Redis::redisQuery($redis, "HGET", $arrParams);
		if($arrRet['err_no'] !== 0){
			Bingo_Log::warning("call redis error.[input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
		}
		$thread_id = intval($arrRet['ret'][$key]);
	
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$thread_id);
	}
	public static function addPostOrReplyThread($arrInput){
		if(!isset($arrInput['redis']) ||!isset($arrInput['key']) || !isset($arrInput['user_id']) || !isset($arrInput['user_name']) || !isset($arrInput['create_time'])
			|| !isset($arrInput['forum_id']) || !isset($arrInput['forum_name']) || !isset($arrInput['title']) || !isset($arrInput['thread_content']) || !isset($arrInput['post_content'])){
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		
		$redis = $arrInput['redis'];
		$key = $arrInput['key'];
		$user_id = intval($arrInput['user_id']);
		$user_ip = isset($arrInput['user_ip'])?$arrInput['user_ip']:167772160;//167772160=10.0.0.0
		$user_name = $arrInput['user_name'];
		$forum_id = intval($arrInput['forum_id']);
		$forum_name = $arrInput['forum_name'];
		$create_time = intval($arrInput['create_time']);
		$title = $arrInput['title'];
		$post_content = $arrInput['post_content'];
		$thread_content = $arrInput['thread_content'];
		$ignore_if_thread_not_exist = isset($arrInput['ignore_if_thread_not_exist']) ? intval($arrInput['ignore_if_thread_not_exist']) : 0;
		
		$data = array(
				"thread_id" => 0,
				"post_id" => 0,
		);
		
		$arrPostParams = array(
				'product_private_key'=>"special_pro",//豁免ueg一切策略
				"user_id" => $user_id,
				"user_ip" => $user_ip,
				"user_name" => $user_name,
				"forum_id" => $forum_id,
				"forum_name" => $forum_name,
				"create_time" => $create_time,
		);
		
		$arrParams = array(
				"key" => $key,
				"field" => $forum_id,
		);
		$arrRet = Util_Redis::redisQuery($redis, "HGET", $arrParams);
		if($arrRet['err_no'] !== 0){
			Bingo_Log::warning("call redis error.[input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
		}
		$thread_id = intval($arrRet['ret'][$key]);
		if($thread_id > 0){
			$arrPostParams['content'] = $post_content;
			$arrPostParams['thread_id'] = $thread_id;
			
			$data['thread_id'] = $thread_id;
			$arrRet = self::addPost($arrPostParams);
			if($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				return $arrRet;
			}
			$data['post_id'] = $arrRet['data'];
		}else{
			if($ignore_if_thread_not_exist === 1){
				Bingo_Log::pushNotice("ignore_if_thread_not_exist",1);
				return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$data);
			}
			$arrPostParams['title'] = $title;
			$arrPostParams['content'] = $thread_content;
			if(isset($arrInput['thread_type'])){
				$arrPostParams['thread_type'] = $arrInput['thread_type'];
			}
			$arrRet = self::addThread($arrPostParams);
			if($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				return $arrRet;
			}
			$thread_id = $arrRet['data'];
			$data['thread_id'] = $thread_id;
			
			if($thread_id <= 0){
				Bingo_Log::warning("add new thread error.[thread_id=$thread_id]");
			}else{
				$arrParams = array(
						"key" => $key,
						"field" => $forum_id,
						"value" => $thread_id,
				);
				$arrRet = Util_Redis::redisQuery($redis, "HSET", $arrParams);
				if($arrRet['err_no'] !== 0){
					Bingo_Log::warning("call redis error.[input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
					return Util_Function::errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
				}
				
				//锁帖
				$arrParams = array(
						'req' => array(
								'thread_id' => $thread_id,
						),
				);
				$arrRet = Tieba_Service::call("anti", "antiLockThread", $arrParams, NULL, NULL, 'post', 'php', 'utf-8');
			}
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$data);
	}
	
	public static function addPost($arrInput){	
		/*
		$arrParams = array(
				'req'=>array(
						'product_private_key'=>"tieba_pc",
						'user_ip' => $user_ip,
						'user_id' => $user_id,
						'user_name' => $user_name,
						'forum_id' => $bconf_info['forum_id'],
						'forum_name' => $bconf_info['forum_name'],
						'content' => $content,
						'create_time' => time(),
						'thread_id' => $bconf_info['thread_id'],
						'vcode_free_gate' => 1,
				)
		);
		*/
		$arrParams = array(
				'req'=> $arrInput,
		);
		$arrParams['req']['vcode_free_gate'] = 1;
		$arrRet = Tieba_Service::call('post', 'addPost', $arrParams, NULL, NULL, 'post', 'php', 'utf-8');
		if( !isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning("call addPost error.[input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
			return Util_Function::errRet($arrRet['errno']);
		}
		$post_id = isset($arrRet['res']['post_id'])?intval($arrRet['res']['post_id']):0;
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$post_id);
	}
	
	public static function addThread($arrInput){
		/*
		$input = array(
		    'req'=>array(
			    'product_private_key'=>"tieba_pc",
			    'user_ip'=>$user_ip,
			    'user_id'=>$user_id,
			    'user_name'=>$user_name,
			    'forum_id'=>$forum_id,
			    'forum_name'=>$forum_name,
			    'title'=>$title,
			    'content'=>$content,
			    'create_time'=>$current_time,
			    //'thread_id'=>0,
			    //'post_id'=>0,
			    'vcode_free_gate'=>1,
		    )
		);
   	 	$add_res = Tieba_Service::Call('post', 'addThread', $input);
		*/
		$arrParams = array(
				'req'=> $arrInput,
		);
		$arrParams['req']['vcode_free_gate'] = 1;
		$arrRet = Tieba_Service::call('post', 'addThread', $arrParams, NULL, NULL, 'post', 'php', 'utf-8');
		if( !isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning("call addThread error.[input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
			return Util_Function::errRet($arrRet['errno']);
		}
		
		$thread_id = isset($arrRet['res']['thread_id'])?intval($arrRet['res']['thread_id']):0;
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$thread_id);
	
	}
	
	public static function checkPostRule($arrInput){
		if(!isset($arrInput['user_id']) ||!isset($arrInput['user_name'])||!isset($arrInput['user_ip']) || !isset($arrInput['forum_id']) || !isset($arrInput['forum_name']) || !isset($arrInput['thread_id']) || !isset($arrInput['content'])){
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrInput['product_private_key'] = "tieba_pc";
		$arrInput['need_not_commit'] = 1;
		$arrInput['vcode_free_gate'] = 1;
		$arrParams = array(
				'req'=> $arrInput,
		);
		$arrRet = Tieba_Service::call('post', 'addPost', $arrParams, NULL, NULL, 'post', 'php', 'utf-8');
		if( !isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning("call addPost error.[input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
			return Util_Function::errRet($arrRet['errno']);
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
	}
}