<?php
class Util_Service{
	private static $_callType = 'local';
	public static function call($strServiceName, $strMethod, $arrInput, $callType = NULL){
		if ($callType === NULL){
			$callType = self::$_callType;
		}
		$timer_key = 'service_'.$strServiceName.'_'.$strMethod;
        Bingo_Timer::start($timer_key);
        $arrOut = Tieba_Service::call($strServiceName, $strMethod, $arrInput,
                                            NULL,NULL,'post','php','utf-8',$callType);
        Bingo_Timer::end($timer_key);
        
        if (false === $arrOut){
            Bingo_Log::warning('Tieba_Service::call '.$strServiceName.'_'.$strMethod.' return false');
        }       
        
        return $arrOut;
	}
}