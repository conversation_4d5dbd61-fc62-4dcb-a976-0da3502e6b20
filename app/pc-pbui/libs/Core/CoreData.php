<?php
/*
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-08-15 18:19:00
 * @version
 **/

class Core_CoreData
{
    private static $_arr_call_data  = array();
    private static $_arr_core_data = array();

    private static function _init(){
        $arr_param = array(
            'ext_call_type'      => 1, //扩展子模块调用方式，0为HTTP，1为本地local
            'core_call_type'     => 0, //核心service调用方式，0为并行，1为串行
            'ext_http_type'		 => 0, //扩展子模块采用HTTP调用的方式，0为并行，1为串行
	    'platform'		=> false,
        );
        Tieba_Hello_Controller::init('pb', $arr_param);
    }

    public static function getCoreData($arr_pb_data, $arr_user_lists, $arr_signids, $arr_pb_head )
    {
        $int_fid = $arr_pb_head['forum_id'];
        $str_fname = $arr_pb_head['keyword'];
        $int_tid = intval($arr_pb_head['thread_id']);
        $arr_post_uids = $arr_user_lists['post_uids'];
        $arr_total_uids = $arr_user_lists['total_uids'];

        //add the login user id
        $int_uid = Util_Session::get('intUid');
        if( Util_Session::get('bolLogin') && !in_array($int_uid, $arr_post_uids) ){
            $arr_post_uids[] = $int_uid;
        }
        $arr_diff_uids = array_diff($arr_total_uids, $arr_post_uids);
        $referer = Bingo_Http_Request::getServer("HTTP_REFERER");
        $eqid = self::_getPsEqid($referer);

        self::_init();

        self::_regForumInfo($int_fid, $str_fname);
        self::_regPostInfo($int_fid, $int_tid);
        self::_regPermInfo($int_fid);
        self::_regUserRankInfo($int_fid);
        self::_regUserRecoInfo();
        self::_regUserInfo($int_fid, $arr_post_uids, $arr_diff_uids, $arr_signids);
        self::_regUserStateInfo($int_fid);
        self::_regStatisticsNum($int_fid);
        self::_regGlobalAttr();
        self::_regEqid2QueryWord($eqid,$int_tid);
        self::$_arr_call_data = Tieba_Hello_Controller::callService();

        self::_buildRetData($arr_pb_data, $arr_pb_head);

        if( empty(self::$_arr_core_data) ){
            return false;
        }
        
        self::_mergeUserData($arr_diff_uids);
        return true;
    }

    /**
     * 分页取数据每页100个
     * @param $uids
     * @return array
     */
    private function _mergeUserData($uids){
        if (empty($uids)) {
            return true;
        }
        $arrInput = array(
            'user_id'   => $uids,
            'get_icon'  => 3,
        );

        $arrRet = Molib_Util_MultiService::multiGetUserData($arrInput, 'user', 'mgetUserData', 'pbui_coredata_mgetuserdata');
        if (!empty($arrRet['user_info'])) {
            self::$_arr_core_data['user']['mgetUserForumInfo'] = self::$_arr_core_data['user']['mgetUserForumInfo'] + $arrRet['user_info'];

        }
        return true;
    }

    public static function getCupid()
    {
        $arr_forum_info = self::get('forum', 'getBtxInfo');
        $str_fname = strval($arr_forum_info['forum_name']['forum_name']);
        $str_dir = strval($arr_forum_info['dir']['level_1_name']);
        $str_second_dir = strval($arr_forum_info['dir']['level_2_name']);
        $str_forum_id = intval($arr_forum_info['forum_name']['forum_id']);

        $arr_cupid_input = array(
            'type'             => 'frs',
            'forum_name'       => $str_fname,
            'forum_dir'        => $str_dir,
            'forum_second_dir' => $str_second_dir,
            'forum_id'         => $str_forum_id,
        );
        $res = Ui_UiCupid::getCupid( $arr_cupid_input );

        if( Util_Session::get('bolLogin') ){
            $str_uname = Util_Session::get('strUname');
            $arr_cupid_input = array(
                'type'  => 'bawu',
                'fname_uname' => $str_fname . ' ' . $str_uname, 
            );
            $res = Ui_UiCupid::getCupid( $arr_cupid_input );
        }
        return true;
    }

    /**
     * @param
     * @return
     **/
    public static function _regEqid2QueryWord($eqid, $thread_id)
    {
        if($eqid == ''){
            return;
        }
        $arr_param = array(
            'eqid' => $eqid,
            'push_queue' => 1,
            'thread_id' => $thread_id,
        );
        Tieba_Hello_Controller::registerService('push', 'eqid2QueryWord', $arr_param, 'post' ,'utf-8');
    }

    public static function _regUserRankInfo($int_fid)
    {
        if( Util_Session::get('bolLogin') ){
            $arr_param = array(
                'fid' => $int_fid,
                'uid' => Util_Session::get('intUid'),
            );
            Tieba_Hello_Controller::registerService('userrank', 'userRankInfo', $arr_param, 'post' ,'utf-8');
        }
    }

    public static function _regUserRecoInfo()
    {
    	if( Util_Session::get('bolLogin') ){
    		$arr_param = array(
    			'uid' => Util_Session::get('intUid'),
    		);
    		Tieba_Hello_Controller::registerService('common', 'contentQueryRecoInfo', $arr_param);
    	}
    }
    
    private static function _regUserInfo($int_fid, $arr_post_uids, $arr_diff_uids, $arr_signids)
    {
        $arr_param = array(
            'user_id' => $arr_post_uids,
            'forum_id' => $int_fid,
            'get_icon' => 3,
        );
        Tieba_Hello_Controller::registerService('user', 'mgetUserForumInfo', $arr_param, 'post' ,'utf-8');


       
        if( !empty($arr_signids) ){
            $arr_param = array(
                'sids' => $arr_signids,
            );
            Tieba_Hello_Controller::registerService('user', 'mgetSignaturesBySids', $arr_param, 'post' ,'utf-8');
        }
		$intUid =  Util_Session::get('intUid');
		if($intUid > 0){
			$arr_param = array(
            	'user_id' => $intUid,
				'get_icon' => 1,
        	);
        	Tieba_Hello_Controller::registerService('user', 'getUserData', $arr_param, 'post' ,'utf-8');
		}
        
    }

    private static function _regForumInfo($int_fid, $str_fname)
    {
	/*
        $arr_param = array(
            'forum_id' => $int_fid,
        );
        Tieba_Hello_Controller::registerService('forum', 'getBtxInfo', $arr_param);
	*/
        //for the function of merged forum
        if (!empty($str_fname)) {
            $arr_param = array(
                'query_words' => array($str_fname),
            );
            Tieba_Hello_Controller::registerService('forum', 'getFidByFname', $arr_param, 'post' ,'utf-8');
        }
    }

    private static function _regPostInfo($int_fid, $int_tid)
    {
        if (isset($_POST['pbui_thread_data']) && !empty($_POST['pbui_thread_data']) && $_POST['pbui_thread_data']['errno'] === Tieba_Errcode::ERR_SUCCESS) {
            return;
        }
        $arr_param = array(
            'thread_ids'        => array($int_tid),
            'need_abstract'     => 0,           //do not need abstract
            'forum_id'          => $int_fid,    //enhance the cache hit rate
            'need_photo_pic'    => 0,           //do not need the picture of several floors
            'need_user_data'    => 0,           //do not need user data
        );
        Tieba_Hello_Controller::registerService('post','mgetThread',$arr_param, 'post' ,'utf-8');
    }

    private static function _regGlobalAttr()
    {
	$arr_param = array();
        Tieba_Hello_Controller::registerService('forum','getAllGlobalAttr',$arr_param, 'get','utf-8');
    }

    private static function _regUserStateInfo($int_fid)
    {
//        if ( !$_POST['pbui_is_hide_forum_info'] ) {
            $arr_param = array(
                'forum_id'  => $int_fid,
                'user_id'   => Util_Session::get('intUid'),
                'ip'   => Util_Session::get('intUip'),
                'req_source' => 'pc',
                'extra' => array('phoneblock'), // 使得手机号黑库封禁生效
            );
            Tieba_Hello_Controller::registerService('userstate', 'get_user_block_info', $arr_param, 'post' ,'utf-8');
//        }
    }
	private static function _regStatisticsNum($int_fid){

		$arr_param = array(
				'forum_id' => array($int_fid,),
				);
		Tieba_Hello_Controller::registerService('forum','mgetBtxInfoEx',$arr_param, 'post' ,'utf-8');
	}

    private static function _regPermInfo($int_fid)
    {
        $arr_param = array(
            'forum_id' => $int_fid,
        );
        Tieba_Hello_Controller::registerService('perm', 'getBawuList', $arr_param, 'post' ,'utf-8');

        $arr_param = array(
            'forum_id'  => $int_fid,
            'user_id'   => Util_Session::get('intUid'),
            'user_ip'   => Util_Session::get('intUip'),
            'need_user_tip' => 1,   
        );
        Tieba_Hello_Controller::registerService('perm', 'getPerm', $arr_param, 'post' ,'utf-8');

        $arr_param = array(
            'forum_id' => $int_fid,
        );
        Tieba_Hello_Controller::registerService('perm', 'getForumMemberInfo', $arr_param, 'post' ,'utf-8');
    }

    private static function _buildRetData($arr_pb_data, $arr_pb_head)
    {
        $eqid_tmp = self::$_arr_call_data['push']['eqid2QueryWord'];
        if( isset($eqid_tmp['errno']) && 0 == $eqid_tmp['errno'] ){
            //Bingo_Log::pushNotice("eqid",$eqid_tmp['data']['eqid']);
            //Bingo_Log::pushNotice("query_word",$eqid_tmp['data']['query_word']);
            Tieba_Stlog::addNode("eqid",$eqid_tmp['data']['eqid']);
            Tieba_Stlog::addNode("query_word",$eqid_tmp['data']['query_word']);
        }
        //forum data
        $arr_forum_data = array();
        //$tmp_forum = self::$_arr_call_data['forum']['getBtxInfo'];
        $tmp_forum = $_POST['pbui_forum_data'];
        $tmp_fid_info = self::$_arr_call_data['forum']['getFidByFname'];
        if( isset($tmp_forum['errno']) && 0 == $tmp_forum['errno'] ){
            unset($tmp_forum['errno']);
            unset($tmp_forum['errmsg']);
            $arr_forum_data['getBtxInfo'] = (!empty($tmp_forum)) ? $tmp_forum : array();
        }else{
            $arr_forum_data['getBtxInfo'] = array();
        }
        if( isset($tmp_fid_info['errno']) && 0 == $tmp_fid_info['errno'] && !empty($tmp_fid_info['forum_id'][0]) ){
            $arr_forum_data['getFidByFname'] = $tmp_fid_info['forum_id'][0];
        }else{
            $arr_forum_data['getFidByFname'] = array();
        }
        $arr_forum_data['mgetBtxInfoEx'] = self::$_arr_call_data['forum']['mgetBtxInfoEx'];

        //post data
        if (isset($_POST['pbui_thread_data']) && !empty($_POST['pbui_thread_data']) && $_POST['pbui_thread_data']['errno'] === Tieba_Errcode::ERR_SUCCESS) {
            $tmp_tid_info = $_POST['pbui_thread_data'];
        } else {
            $tmp_tid_info = self::$_arr_call_data['post']['mgetThread'];
        }
        $int_tid = intval($arr_pb_head['thread_id']);
        if( isset($tmp_tid_info['errno']) && 0 == $tmp_tid_info['errno'] && !empty($tmp_tid_info['output']['thread_list']) ){
            $arr_post_data['mgetThread'] = (!empty($tmp_tid_info['output']['thread_list'][$int_tid])) ? 
                $tmp_tid_info['output']['thread_list'][$int_tid] : array();
        }else{
            $arr_post_data['mgetThread'] = array();
        }

        //perm data
        $arr_perm_data = array();
        $tmp_bawu = self::$_arr_call_data['perm']['getBawuList'];
        $tmp_perm = self::$_arr_call_data['perm']['getPerm'];
        $tmp_fminfo = self::$_arr_call_data['perm']['getForumMemberInfo'];
        if( isset($tmp_bawu['errno']) && 0 == $tmp_bawu['errno'] ){
            $arr_perm_data['getBawuList'] = (!empty($tmp_bawu['output'])) ? $tmp_bawu['output'] : array();
        }else{
            $arr_perm_data['getBawuList'] = array();
        }
        if( isset($tmp_perm['errno']) && 0 == $tmp_perm['errno'] ){
            $arr_perm_data['getPerm'] = (!empty($tmp_perm['output'])) ? $tmp_perm['output'] : array();
        }else{
            $arr_perm_data['getPerm'] = array();
        }
        if( isset($tmp_fminfo['errno']) && 0 == $tmp_fminfo['errno'] ){
            $arr_perm_data['getForumMemberInfo'] = (!empty($tmp_fminfo['output'])) ? $tmp_fminfo['output'] : array();
        }else{
            $arr_perm_data['getForumMemberInfo'] = array();
        }

        //userrank data
        $arr_userrank_data = array();
        $tmp_urank = self::$_arr_call_data['userrank']['userRankInfo'];
        if( isset($tmp_urank['errno']) && 0 == $tmp_urank['errno'] ){
            $arr_userrank_data['userRankInfo'] = (!empty($tmp_urank['rankInfoRes']['rankInfo'])) ? 
                $tmp_urank['rankInfoRes']['rankInfo']: array();
        }else{
            $arr_userrank_data['userRankInfo'] = array();
        }

        //user data
        $arr_user_data = array();
        $tmp_user_info = self::$_arr_call_data['user']['mgetUserForumInfo'];
        $tmp_signature = self::$_arr_call_data['user']['mgetSignaturesBySids'];
        $tmp_login_user= self::$_arr_call_data['user']['getUserData'];
        if( isset($tmp_user_info['errno']) && 0 == $tmp_user_info['errno'] ){
            $arr_user_data['mgetUserForumInfo'] = (!empty($tmp_user_info['user_info'])) ? 
                $tmp_user_info['user_info'] : array();
        }else{
            $arr_user_data['mgetUserForumInfo'] = array();
        }
        if( isset($tmp_signature['errno']) && 0 == $tmp_signature['errno'] ){
            $arr_user_data['mgetSignaturesBySids'] = (!empty($tmp_signature['signatures'])) ? 
                $tmp_signature['signatures'] : array();
        }else{
            $arr_user_data['mgetSignaturesBySids'] = array();
        }
        if( isset($tmp_login_user['errno']) && 0 == $tmp_login_user['errno'] ){
            $arr_user_data['getUserData'] = (!empty($tmp_login_user['user_info'][0])) ? 
                $tmp_login_user['user_info'][0] : array();
        }else{
            $arr_user_data['getUserData'] = array();
        }

        //userstate data
        $arr_userstate_data = array(
            'get_user_block_info' => array(),
            'get_user_all_blocking_info' => array(),
        );
//        if ( !$_POST['pbui_is_hide_forum_info'] ) {
            $tmp_user_state = self::$_arr_call_data['userstate']['get_user_block_info'];
            if( isset($tmp_user_state['errno']) && 0 == $tmp_user_state['errno'] ){
                $arr_userstate_data['get_user_block_info'] = (!empty($tmp_user_state['block_info'][0])) ? 
                    $tmp_user_state['block_info'][0] : array();
            }
//        } else {
            // 全吧态PB页，获取用户在分发吧的封禁信息
            $tmp_user_state = self::$_arr_call_data['userstate']['get_user_all_blocking_info'];
            if( isset($tmp_user_state['errno']) && 0 == $tmp_user_state['errno'] ) {
                $arr_userstate_data['get_user_all_blocking_info'] = !empty($tmp_user_state['block_info']) ? 
                    $tmp_user_state['block_info'] : array();
            }
//        }

	//global attr
	$tmp_globalAttr = self::$_arr_call_data['forum']['getAllGlobalAttr'];
        if( !isset($tmp_globalAttr['errno']) || 0 !== $tmp_globalAttr['errno'] ){
	    $tmp_globalAttr = array('data' => array());
        }
	$arr_forum_data['getAllGlobalAttr'] = $tmp_globalAttr['data'];

        //postlogic data
        $arr_post_data['pb'] = $arr_pb_data;
        $arr_post_data['head'] = $arr_pb_head;
        
        //beyond module
        if(isset($arr_post_data['pb'][0])){
            $arr_post_data['pb'][0]['has_pushed'] = 0;
            if (!empty($arr_post_data['mgetThread']) && isset($arr_post_data['mgetThread']['has_pushed'])) {
                $arr_post_data['pb'][0]['has_pushed'] = $arr_post_data['mgetThread']['has_pushed'];
            }
            if (!empty($arr_post_data['mgetThread']) && isset($arr_post_data['mgetThread']['swan_info'])) {
                $swan_info = $arr_post_data['mgetThread']['swan_info'];
                $arr_post_data['pb'][0]['third_app_info'] = array(
                    'app_id'     => $swan_info['id'],
                    'app_name'   => $swan_info['name'],
                    'app_avatar' => $swan_info['avatar'],
                    'app_pic'    => $swan_info['pic'],
                    'app_link'   => $swan_info['link'],
                );
            }
        }

        //userreco data
        $arr_userreco_data = array();
        $tmp_ureco = self::$_arr_call_data['common']['contentQueryRecoInfo'];
        if(isset($tmp_ureco['errno']) && 0 == $tmp_ureco['errno'] && !empty($tmp_ureco['data'])){
        	$str_dir = strval($arr_forum_data['getBtxInfo']['dir']['level_1_name']);
        	$str_second_dir = strval($arr_forum_data['getBtxInfo']['dir']['level_2_name']);
        	$str_forum_id = intval($arr_forum_data['getBtxInfo']['forum_name']['forum_id']);
        	if ( (!empty($tmp_ureco['data']['fdir']) && $str_dir != $tmp_ureco['data']['fdir'])
        		|| (!empty($tmp_ureco['data']['sdir']) && $str_second_dir != $tmp_ureco['data']['sdir'])
        		|| (!empty($tmp_ureco['data']['fid']) && $str_forum_id != $tmp_ureco['data']['fid'])){
        		$arr_userreco_data['userRecoInfo'] = array();
        	}
        	else {
        		$arr_userreco_data['userRecoInfo'] = $tmp_ureco['data'];
        	}
        }
        
        //T豆Y币切换开关数据（默认）
        $arrYYLiveConfig = array(
        	'getConfig' => array(
        		'yy_pay_open' => 0,
        		'yy_is_convert' => 0,
        	),
        );
        
        
        self::$_arr_core_data = array(
            'forum'     => $arr_forum_data,
            'user'      => $arr_user_data,
            'perm'      => $arr_perm_data,
            'userrank'  => $arr_userrank_data,
        	'userreco'	=> $arr_userreco_data,
            'userstate' => $arr_userstate_data,
            'post'      => $arr_post_data,
        );
        
        if(!empty($arr_userstate_data['get_user_block_info'])){
        	//anti-block_reason
        	self::$_arr_core_data['anti']['antiGetBlockReason'] = self::_getBlockReason($arr_pb_head['forum_id']);
        }

        return true;
    }

    public static function get($service, $method, $default=array())
    {
        $service = strval($service);
        $method  = strval($method);
        if( isset(self::$_arr_core_data[$service][$method]) ){
            return self::$_arr_core_data[$service][$method];
        }else{
            return $default;
        }
    }
    private static function _getBlockReason($int_fid){
        $arr_param = array(
            'forum_id'  => $int_fid,
            'user_id'   => Util_Session::get('intUid'),
        );      
        $out = Tieba_Service::call('anti', 'antiGetBlockReason', $arr_param, NULL, NULL, 'post', 'php', 'utf-8');
        return $out;
    }
    /**
     * @param
     * @return
     * */
    private static function _getPsEqid($refer)
	{	   	
	    if ('' === $refer)
		{
			Bingo_Log::warning("input refer is null");
			return false;
		}
		$refer = urldecode($refer);		
		$preg = '/^https?:\/\/www\.baidu\.com.+(eqid)\s*=\s*([^&]+)/i';
		$out = array();
		$pregRet = preg_match($preg, $refer, $out);
		
		if ($pregRet > 0)
		{
			$strPsEqid = isset($out[2])?$out[2]:'';			
		}
		else
		{
			$strPsEqid = '';
		}	
		
		return $strPsEqid;		
	}
}
