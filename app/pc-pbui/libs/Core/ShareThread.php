<?php
/**
 * Created by <PERSON><PERSON><PERSON>tor<PERSON>.
 * User: lihuan08
 * Date: 2017/11/1
 * Time: 19:53
 */

class Core_ShareThread {


    const CMD_NO   = 55050;
    const TEXT_OF_DELETE_THREAD = '原贴已被屏蔽或删除。';
    const SMALL_PIC_OF_DELETE_THREAD = 'http://imgsrc.baidu.com//forum//pic//item//01e93901213fb80e94cdd17c3dd12f2eb9389479.jpg';//'http://imgsrc.baidu.com/forum/abpic/item/033b5bb5c9ea15ce2c1ae1b3bd003af33b87b297.jpg';
    const BIG_PIC_OF_DELETE_THREAD = 'http://imgsrc.baidu.com//forum//pic//item//01e93901213fb80e94cdd17c3dd12f2eb9389479.jpg';//;'http://imgsrc.baidu.com/forum/w%3D580/sign=8b46946603fa513d51aa6cd60d6c554c/033b5bb5c9ea15ce2c1ae1b3bd003af33b87b297.jpg';
    private static $_objMultiCaller=null;
    private static $arrMaskUserId = array();
    /**
     * @param $arrThreadInfo
     * @param $arrCartoonInfo
     * @return bool
     */
    public static function getOriginThreadInfo($arrTids){
        if( !is_array($arrTids) || count($arrTids) === 0 ){
            Bingo_Log::warning("tid is empty.");
            return array();
        }
        //mgetThread
        $arrInput = array(
            'thread_ids'        => $arrTids,
            'need_abstract'     => 1,
            'forum_id'          => 0,
            'need_photo_pic'    => 0,
            'need_forum_name'   => 1,
        );      
        //ie=utf8          
        $arrRet = Tieba_Service::call('post', 'mgetThread', $arrInput, null, null, 'post', 'php', 'utf-8');
        if ( $arrRet === false) {
            Bingo_Log::warning('call post:mgetThread failed.  [sevice_name:post] [method:mgetThread] [input:'.serialize($arrInput).'] ');
            return false;
        }
        if ( $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call post:mgetThread failed.  [sevice_name:post] [method:mgetThread] [input:'.serialize($arrInput).'] [output:'.serialize( $arrRet).']');
            return false;
        }
        $arrRawThreadList = $arrRet['output']['thread_list'];

	$arrNewThreadList = self::_registerMultiInput($arrRawThreadList);
        $arrThreadList = array();
        foreach ($arrNewThreadList as $arrRawThread) {
            $arrThread = array();
            $arrThreadType = Tieba_Type_Thread::getTypeArray($arrRawThread['thread_types']);
            
            //这些帖子也要处理吗
            if ( $arrThreadType['is_pic_album'] === true ||
                 $arrThreadType['is_vote']      === true ||
                 $arrThreadType['is_bakan']     === true ||
                 $arrThreadType['is_protal']    === true ||
                 $arrThreadType['is_yydb'] === true){
                continue;
            }
            else{
                //$arrThread['id']            = $arrRawThread['thread_id'];
                $arrThread['tid']           = $arrRawThread['thread_id'];
                $arrThread['title']         = trim(html_entity_decode($arrRawThread['title'], ENT_COMPAT, 'UTF-8'));
                $arrThread['thread_type']   = 0;
                $arrThread['fid']           = $arrRawThread['forum_id'];
                $arrThread['fname']           = $arrRawThread['forum_name'];
		$arrThread['media']           = $arrRawThread['media'];

                
                //视频贴
                if (isset($arrRawThread['video_info'])) {
                    $arrThread['thread_type'] = Tieba_Type_Thread::MOVIDEO_THREAD;
                }
                if ($arrThreadType['is_twzhibo_thread'] === true) {
                    //$strLiveCoverSrc = Molib_Util_ImgCDN_Frs::procImgTextLivePic($arrRawThread['livecover_src'], $arrSpec);
                    //$arrThread['livecover_src'] = $strLiveCoverSrc;
                    $arrThread['thread_type'] = Tieba_Type_Thread::TWZHIBO_THREAD;
                }
                if (isset($arrRawThread['poll_info'])) {
                    $arrThread['thread_type'] = Tieba_Type_Thread::POLL_THREAD;
                }
                if (isset($arrRawThread['jid']['jid'])) {
                    $arrThread['thread_type'] = Tieba_Type_Thread::JNEWS_THREAD;
                }
                // ALa直播和录播贴,ala分享贴
                if ($arrThreadType['is_ala_live_video'] === true ) {
                    $arrThread['thread_type'] = Tieba_Type_Thread::ALA_LIVE_VIDEO_THREAD;
                    $arrThread['ala_info'] = self::_getALaInfo($arrRawThread);
                }
                elseif ($arrThreadType['is_ala_video'] === true || $arrThreadType['is_ala_share_thread'] === true ) {
                    $arrThread['thread_type'] = Tieba_Type_Thread::ALA_VIDEO_THREAD;
                    $arrThread['ala_info'] = self::_getALaInfo($arrRawThread);
                    //$arrThread['video_info'] = $arrRawThread['video_info'];
                }elseif ($arrThreadType['is_ala_share_thread'] === true ) {
                    $arrThread['thread_type'] = Tieba_Type_Thread::ALA_SHARE_THREAD;
                    $arrThread['ala_info'] = self::_getALaInfo($arrRawThread);
                }
          
                if($arrThreadType['is_link_thread'] === true) {
                    $arrThread['thread_type'] = Tieba_Type_Thread::LINK_THREAD;
                }          
                //author 
                //abstract 是否要再处理下
                $strAbstract = $arrRawThread['abstract'];
                $arrAbstract = array();
                if ( !empty($strAbstract) ){
                    $arrAbstract[] = array(
                        'type' => Molib_Util_RichText_Parser::SLOT_TYPE_TEXT,
			'text' => html_entity_decode(htmlspecialchars_decode($strAbstract), ENT_COMPAT, 'UTF-8'),
                    );
                }
                $arrThread['abstract'] = $arrAbstract;
                /*if( !empty($arrThread['media']) ){
                    foreach ($arrThread['media'] as $key =>  &$arrItem) {

                        if ($arrItem['type'] === 'pic') {
                               $arrItem['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_IMG;
                        }else if( $arrItem['type'] === 'abstract' ){
                            $arrItem['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_TEXT;
                        }else if( $arrItem['type'] === 'flash' ){
                            $arrItem['type'] =  Molib_Util_RichText_Parser::SLOT_TYPE_EMBED;
                        }else if( $arrItem['type'] === 'music' ){
                            $arrItem['type'] = Molib_Util_RichText_Parser::SLOT_TYPE_MUSIC;
                        }
                    }
		}*/
                // process the movideo thread
                if ($arrThread['thread_type'] == Tieba_Type_Thread::MOVIDEO_THREAD) {
                    $arrThread['media'][0] = self::_getVideoCover($arrRawThread);
                }
                    
                // 分享原帖是否被删除或屏蔽，被屏蔽的原帖所在的吧是否在分享贴所在吧（屏蔽只作用于原帖所在吧吧）
                $isTheSameForum = $_POST['pbui_forum_info']['forum_id']['0']['forum_id'] === $arrThread['fid'] ? true : false;
                $isOriginalThreadMask = 1 === intval($arrRawThread['is_frs_mask']) ? true : false;
                $isOriginalThreadDel = 1 === intval($arrRawThread['is_deleted']) ? true : false;

                $arrThread['abstract'] = $arrAbstract;
                //判断贴子是否被删除 is_deleted
                if( true == self::$arrMaskUserId[$arrRawThread['user_id']] || (1 == $isOriginalThreadDel || ($isOriginalThreadMask&&$isTheSameForum))){
                    $arrThread['title'] = self::TEXT_OF_DELETE_THREAD;
                    $arrThread['media'][0]['water_pic'] = self::SMALL_PIC_OF_DELETE_THREAD;
                    $arrThread['media'][0]['small_pic'] = self::SMALL_PIC_OF_DELETE_THREAD;
                    $arrThread['media'][0]['big_pic'] = self::BIG_PIC_OF_DELETE_THREAD;
                    $arrThread['media'][0]['width'] = 96;
                    $arrThread['media'][0]['height'] = 96;
                    $arrThread['media'][0]['type'] =  Molib_Util_RichText_Parser::SLOT_TYPE_IMG;
                    //$arrThread['title'] = trim(html_entity_decode(self::TEXT_OF_DELETE_THREAD, ENT_COMPAT, 'UTF-8'));
                }
                $arrThreadList[$arrRawThread['thread_id']] = $arrThread;
            }
        }
        return $arrThreadList;

    }

    /**
     * @brief  get the video cover info
     * @param  $arrRawThread
     * @return array
     */
    private function _getVideoCover($arrRawThread){
        $arrVideoInfo = $arrRawThread['video_info'];
        $arrVideoCover = array(
            'type'      => 'video',
            'small_pic' => $arrVideoInfo['thumbnail_url'],
            'big_pic'   => $arrVideoInfo['thumbnail_url'],
            'water_pic' => $arrVideoInfo['thumbnail_url'],
            'size'      => 0,
            'post_id'   => $arrRawThread['first_post_id'],
            'width'     => isset($arrVideoInfo['thumbnail_width']) ? $arrVideoInfo['thumbnail_width'] : 320,
            'height'    => isset($arrVideoInfo['thumbnail_height']) ? $arrVideoInfo['thumbnail_height'] : 320,
            'video_url' => $arrVideoInfo['video_url'],
            'video_md5' => $arrVideoInfo['video_md5'],
        );
        return $arrVideoCover;
    }

     /**
     * @param $arrThreadList
     * 处理ALa贴子的ala_info
     * @return
     */
    private function _getALaInfo($arrRawThread) {
        $arrThreadType = Tieba_Type_Thread::getTypeArray($arrRawThread['thread_types']);
        if ($arrThreadType['is_ala_live_video'] === true) {
            $alaLiveAttr = $arrRawThread['ala_live_attr'];
            if (!empty($alaLiveAttr)) {
                //$arrAlaUserInfo['ala_id'] = $alaLiveAttr['ala_user_id'];
                $arrAlaUserInfo['user_name'] = $alaLiveAttr['ala_user_name'];
                $arrAlaUserInfo['portrait'] = $alaLiveAttr['ala_portrait'];
                //$arrAlaUserInfo['intro'] = $alaLiveAttr['ala_intro'];
                //$arrAlaUserInfo['gender'] = $alaLiveAttr['ala_gender'];
                $alaInfo['user_info'] = $arrAlaUserInfo;
    
                $alaInfo['live_id'] = $alaLiveAttr['live_id'];;
                $alaInfo['cover'] = $alaLiveAttr['ala_live_cover'];
                //$alaInfo['thumbnail_width'] = ;
                //$alaInfo['thumbnail_height'] = ;
                $alaInfo['session_id'] = $alaLiveAttr['live_session_info']['sessionId'];
                $alaInfo['rtmp_url'] = $alaLiveAttr['live_session_info']['rtmpUrl'];
                $alaInfo['hls_url'] = $alaLiveAttr['live_session_info']['hlsUrl'];


                return $alaInfo;
            }
        } 
    }
    /**
     * @param $arrRawThrad
     * @return $arrNewThrad
     */
    private  function _registerMultiInput($arrRawThreadList){
        $arrThreadLinkCode = array();
        $userList = array();
        foreach ($arrRawThreadList as $arrRawThread) {
            if (!empty($arrRawThread['link_url_code'])) {
                $arrThreadLinkCode[$arrRawThread['link_url_code']] = $arrRawThread['link_url_code'];
            }
            if( intval($arrRawThread['user_id']) > 0){
                $userList[] = intval($arrRawThread['user_id']) ;
            }

        }
        self::$_objMultiCaller = new Tieba_Multi("share_thread_multi_call");
        if( !empty($arrThreadLinkCode) ){
            $arrMultiInput = array(
                'serviceName' => 'threadlink',
                'method' => 'batchQueryThreadlink',
                'ie' => 'utf-8',
                'input' => array(
                      'link_url_code' => $arrThreadLinkCode,
                ),
            );
            self::$_objMultiCaller->register("threadlink_batchQueryThreadlink", new Tieba_Service("threadlink"), $arrMultiInput);
        }
        $arrMultiInput = array(
                'serviceName' => 'anti',
                'method' => 'antiTbmaskQuery',
                'ie' => 'utf-8',
                'input' => array(
                    'req' => array(
                        'id_list'     => $userList,
                        'mask_list'   => array(
                            'anti_browse',
                        ),
                        'strMethod'   => 'tbmask_query',
                        'command_no'  => self::CMD_NO,
                    ),
                ),
        );
        self::$_objMultiCaller->register("anti_antiTbmaskQuery", new Tieba_Service("anti"), $arrMultiInput);
        // 分享贴兼容屏蔽获取是否屏蔽字段is_frs_mask
        $arrMultiInput = array(
            'serviceName' => 'post',
            'method' => 'getThreadMaskInfo',
            'ie' => 'utf-8',
            'input' => array(
                "thread_ids" => array_keys($arrRawThreadList),
            ),
        );
        self::$_objMultiCaller->register("post_getThreadMaskInfo", new Tieba_Service("post"), $arrMultiInput);
        
        self::$_objMultiCaller->call();
        
        if( !empty($arrThreadLinkCode) ){
            $arrLinkUrlOut = self::$_objMultiCaller->getResult("threadlink_batchQueryThreadlink");
            if ($arrLinkUrlOut == false || Tieba_Errcode::ERR_SUCCESS !== $arrLinkUrlOut['errno']) {
                Bingo_Log::warning("call threadlink::batchQueryThreadlink failed. ret:" . serialize($arrLinkUrlOut));
            }
            $arrLinkUrlCode = $arrLinkUrlOut['data'];
        }
        $userMaskInfo = self::$_objMultiCaller->getResult("anti_antiTbmaskQuery");
        if ($userMaskInfo == false || Tieba_Errcode::ERR_SUCCESS !== $userMaskInfo['errno']) {
            Bingo_Log::warning("call anti_antiTbmaskQuery failed. ret:" . serialize($userMaskInfo));
        }
        $arrGetThreadStateRes  = self::$_objMultiCaller->getResult("post_getThreadMaskInfo");
        if($arrGetThreadStateRes == false || $arrGetThreadStateRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("Failed to call service:[post], method:[getThreadMaskInfo]. ret:".serialize($arrGetThreadStateRes));
        }

        if(!empty($arrGetThreadStateRes)){
            foreach($arrRawThreadList as &$item) {
                $item['ori_thread_id'] = $arrGetThreadStateRes['output']['thread_info']['0']['thread_id'];
                $item['is_deleted'] = $arrGetThreadStateRes['output']['thread_info']['0']['is_deleted'];
                $item['is_frs_mask'] = $arrGetThreadStateRes['output']['thread_info']['0']['is_frs_mask'];
            }
        }

        foreach($userMaskInfo['res']['res_list'] as $arrMask) {
            if($arrMask ['anti_browse'] === 1) {
                self::$arrMaskUserId[$arrMask['id']] = true;
            }
        }
        // 拼接链接贴数据
        if( !empty($arrLinkUrlCode ) ){
            foreach($arrRawThreadList as &$item) {
                $arrThreadType = Tieba_Type_Thread::getTypeArray($item['thread_types']);
                $strLinkUrlCode = $item['link_url_code'];
                $item['link_info'] = $arrLinkUrlCode[$strLinkUrlCode];
                if( !empty($item['link_info']) && isset($arrThreadType['is_link_thread'])){
                    $arrLinkContent = $item['link_info']['link_content'][0];
                    $item['title'] = empty($arrLinkContent) ? $item['title']:$arrLinkContent['link_title'];
                    $item['abstract'] = empty($arrLinkContent) ? $item['abstract']:$arrLinkContent['link_abstract'];
                    $item['media'][0]['small_pic'] = $arrLinkContent['link_head_small_pic'];
                    $item['media'][0]['big_pic'] = $arrLinkContent['link_head_big_pic'];
                    $item['media'][0]['type'] = $arrLinkContent['link_type'];
                }
            }
        }
        return $arrRawThreadList;

    }


}
