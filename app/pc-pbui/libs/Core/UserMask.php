<?php
/*
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-08-09 17:40:00
 * @version
 */

class Core_UserMask{

    public static function isThreadHide($int_login_uid, $int_fid, $int_tid, $int_first_uid)
    {
        $input = array(
            'req'=>array(
                'type'   =>'T',
                'userId' =>$int_login_uid,
                'forumId'=>$int_fid,
                'subjectId' =>array($int_tid),
                'pUserId'=>array($int_first_uid),
            ),
            'conf'=>array(
                'instanceName'=>'Redis_spam_umask_forum',
                'redis_uname'=>'spam',
                'redis_tk'=>'spam',
            ),
        );

        $output = Ueg_Mask_Umask::antiUserMaskQuery($input);

        if(0 !== $output['errno'])
        {
            Bingo_Log::warning('fail to call umask->antiUserMaskQuery :input['.serialize($input). ']output:['.serialize($output).']' );
            return false;
        }
        if(in_array(strval($int_tid), $output['res']['maskSubjectId'], true)
            ||in_array($int_first_uid, $output['res']['maskUserId'], true))
        {
            return true;
        }
        return false;
    }

    public static function filterPostAndComment($int_login_uid, $int_tid, $arr_uids, &$arr_post_infos, &$arr_sub_post_infos)
    {
        if( empty($int_tid) || empty($arr_post_infos) || empty($arr_uids) ){
            return false;
        }

        $arr_mask_uids = self::getUserMask($int_login_uid, $int_tid, $arr_uids);
        if( empty($arr_mask_uids) ){
            return false;
        }

        //过滤回复
        $new_arr_post_infos = array();
        foreach( $arr_post_infos as $post)
        {
            $int_uid = $post['user_id'];
            $int_pid = $post['id'];
            if( isset($maskuids[$int_uid]) ){
                unset($arr_sub_post_infos[$int_pid]);
            }else{
                $new_arr_post_infos = $post;
            }
        }
        $arr_post_infos = $new_arr_post_infos;

        //过滤楼中楼
        $new_arr_sub_post_infos = array();
        foreach( $arr_sub_post_infos as $key => $subpost ){
            $tmp_arr_sub = array();
            foreach( $subpost as $post ){
                $int_uid = $post['user_id'];
                if( isset($maskuids[$int_uid]) ){
                }else{
                    $tmp_arr_sub = $post;
                }
            }
            if( !empty($tmp_arr_sub) ){
                $new_arr_sub_post_infos[$key] = $tmp_arr_sub;
            }
        }
        $arr_sub_post_infos = $new_arr_sub_post_infos;
    }

    public static function getUserMask($int_login_uid, $int_tid, $arr_uids)
    {
        //获取被隐藏的用户和贴子
        $input = array(
            'req'=>array(
                'type'     => 'UP',
                'userId'   => $int_login_uid,
                'subjectId'=> $int_tid,
                'pUserId'  => $arr_uids,
                'postId'   => array(),
            ),
            'conf'=>array(
                'instanceName' => 'Redis_spam_umask_forum',
                'redis_uname'  => 'spam',
                'redis_tk'     => 'spam',
            ),
        );

        $output = Ueg_Mask_Umask::antiUserMaskQuery($input);
        if(0 !== $output['errno'])
        {
            Bingo_Log::warning('fail to call antiUserMaskQuery input : ['.serialize($input). ']output:['.serialize($output).']' );
            return false;
        }
        //key化处理
        $tmp_mark_uids = $output['res']['maskUserId'];
        foreach( $tmp_mark_uids as $int_mark_uid){
            $arr_mark_uids[$int_mark_uid] = $int_mark_uid;
        }
        return $arr_mark_uids;
    }
}
