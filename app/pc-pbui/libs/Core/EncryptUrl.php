<?php
/*
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-02-25 15:26
 * @version
 */

class Core_EncryptUrl
{
    const JUMP_URL_DOMAIN_NAME = 'http://jump2.bdimg.com';
    private static $_arrLinkAttr = array(
        'rel' => 'noopener noreferrer nofollow',
        'class' => 'j-no-opener-url',
    );

    public static function dealPost($str_content)
    {
        $str_content = self::getUrlAndEncrypt($str_content);
        return $str_content;
    }

    public static function dealSubPost($arr_comment_info)
    {
        foreach( $arr_comment_info as $key => $comment ){
            $str_content = $comment['content'];
            $str_content = self::getUrlAndEncrypt($str_content);
            $arr_comment_info[$key]['content'] = $str_content;
        }
        return $arr_comment_info;
    }

    private function getUrlAndEncrypt($str_content)
    {
        $str_patten="/<a\s[^>]*href=\"(http[^\"]*)\"(.*)>(.*)<\/a>/siU";

        $str_content = preg_replace_callback($str_patten, array('self', 'encodeUrl'),
            $str_content);
        return $str_content;
    }

    private function encodeUrl($matches)
    {
        $orignalUrl = $matches[1];

        $bol_login  = Util_Session::get('bolLogin');
        $int_uid    = Util_Session::get('intUid');
        $str_bdui   = Util_Session::get('strBaiduid');

        if( ($bol_login === false) || (empty($int_uid)) )
        {
            $int_uid = 1;
        }

        $tmpUrl = htmlspecialchars_decode($orignalUrl);
        $arrInput = array(array(
            'url' => $tmpUrl,
            'uid' => $int_uid,
            'bid' => $str_bdui,
        ));
        $tmpStrstr = strstr($orignalUrl, "http://tieba.baidu.com/i/sys/jump");
        if ($tmpStrstr){
            return "<a href=\"" . $tmpurl."\" ".$matches[2]. ">" .$matches[3] . "</a>";
        }else{
            $encryptUrl = Tieba_Anti_UrlEncodeAndDecode::encode($arrInput);
            $url = $encryptUrl[0];
            $strAttr = '';
            foreach (self::$_arrLinkAttr as $key => $value) {
                $strAttr .= " $key=\"$value\" ";
            }

            return "<a href=\"" . self::JUMP_URL_DOMAIN_NAME."/safecheck/index?url=".$url."\" " . $matches[2]. $strAttr . ">" .
                $matches[3] . "</a>";
        }
    }
}
