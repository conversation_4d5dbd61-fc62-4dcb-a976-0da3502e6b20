<?php

/*
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-08-21 00:22:00
 * @version
 **/

class Ui_DataProc
{
    const VIDEO_MODULE_ID = 'video_p1';           // 视频
    const DISK_MODULE_ID = 'thirty-three_p1';    // 网盘
    const STAR_FRS_MODULE_ID = 'starfrs_p1';         // 明星frs页
    const COMFORUM_MODULE_ID = 'commerce_p1';        // 商业吧
    const CUSTOMIZE_MODULE_ID = 'customize_p1';       // 通用定制吧
    const GCON_MODULE_ID = 'official_p1';        //官方吧
    const MAX_ICON_SIZE = 12;
    const FORUM_PERM_TOPICTHREAD = 107;
    const MEMBER_MUTE_TYPE      = 'app_member_mute';    //会员禁言类型
    const MEMBER_MUTE_DEFAULT	= 1;    				//会员禁言被禁言
    const MEMBER_MUTE_UNMUTE	= 0;    				//会员禁言被禁言
    const MEMBER_LEVEL_PROPS	= 2;    				//会员会员等级
    const GAME_COMMON_KEYWORDS = 'game_common';
    const EDU_COMMON_KEYWORDS = 'edu_common';
    const SHUMA_COMMON_KEYWORDS = 'shuma_common';
    const DONGMAN_COMMON_KEYWORDS = 'dongman_common';
    const KEYWORD_MAX_LENTH = 5;
    const CATEGORY_OTHER = 0;
    const CATEGORY_GAME = 1; // 游戏垂类
    const CATEGORY_WENXUE = 2; // 文学垂类
    const CATEGORY_EDU = 3; // 教育垂类
    const CATEGORY_SHUMA = 4; // 数码垂类
    const CATEGORY_DONGMAN = 5;

    private static $_commonKeyMap = array(
        self::CATEGORY_EDU => self::EDU_COMMON_KEYWORDS,
        self::CATEGORY_SHUMA => self::SHUMA_COMMON_KEYWORDS,
        self::CATEGORY_DONGMAN => self::DONGMAN_COMMON_KEYWORDS,
    );
    private static $_logKeyMap = array(
        self::CATEGORY_EDU => 'edu',
        self::CATEGORY_SHUMA => 'shuma',
        self::CATEGORY_DONGMAN => 'dongman',
    );
    private static $_gameFdirMap = array(
        '手机游戏'=>'手游',
        '直播平台及主播'=>'游戏主播',
        '游戏角色'=>'游戏角色',
        '小游戏'=>'小游戏',
        '电子竞技及选手'=>'电子竞技',
        '游戏交易及功能'=>'游戏交易',
        '单机游戏'=>'游戏',
        '网页版网游'=>'游戏',
        '客户端网游'=>'游戏',
        '桌游'=>'游戏',
        '电视游戏'=>'游戏',
        '主机及单机游戏'=>'游戏',
        '掌机游戏'=>'游戏',
        '其他游戏及话题'=>'游戏',
        '游戏平台'=>'游戏'
    );
    private static $_wenxueLv2Name = array('严肃小说','传统武侠小说','儿童文学','其他小说作品','军事·历史小说','古典文学','古言小说','外国文学','奇幻·玄幻小说','当代其他文学作品','悬疑·推理小说','文学期刊','文学话题','游戏小说','灵异·超能力小说','科幻小说','近现代文学作品','都市·言情小说');

    private static $_forum_shared = array(
        '24981790'  =>  1,
    );
    private static $_arrForumNameShared = array();
    private static $_title_prex_dir = array(
                                        '网友俱乐部'=>array('个人贴吧','其他','网友俱乐部','百度知道用户团队'),
                                        '情感'=>array('其他情感话题','女性话题','婚姻家庭','恋爱','年代秀','情感生活',
                                                        '感情文化','美色'),
                                        '生活'=>array('其他生活话题','杂志期刊'),
                                        '电视节目'=>array('新闻资讯'),
                                        '高等院校'=>array('校园青春'),
                                        '当代人物'=>array('热点新闻人物'),
                                        '社会'=>array('社会事件及话题'),
                                        '人文自然'=>array('社会科学话题'),
                                        '百度服务中心'=>array('贴吧活动与交流'),
    );
    private static $_arr_template_data = array();       //模板数据
    private static $_arr_params_data = array();       //data which will be passed to sub-ui

    public static function buildTemplateData() {
        self::_buildForumInfo();
        self::_buildUserInfo();
        self::_buildContext();
        self::_buildPerm();
        self::_buildPostInfo();
        self::_buildPostUser();
	    self::_buildMetaInfo();
		self::_buildMute();
        return self::$_arr_template_data;
    }

    public static function buildSpiderTemplateData() {
        self::_buildForumInfo();
        self::_buildUserInfo();
        self::_buildContext();
	    self::_buildMetaInfo();
        self::_buildTdkInfo();
        self::_buildPostInfo();
        self::_buildPostUser();
        return self::$_arr_template_data;
    }

    public static function buildParamData($arr_user_lists) {
        self::_paramForumData();
        self::_paramUserData();
        self::_paramContextData();
        self::_paramCupidData();
        self::_paramPermData();
        self::_paramPostData($arr_user_lists);

        return self::$_arr_params_data;
    }

    private static function _buildTdkInfo() {
        $spider_switch = self::_getSpiderSwitch();
        if($spider_switch != 1){
            return;
        }

        //泛时效性，添加时间属性 by wangquanxiang
        $arr_post_info = Core_CoreData::get('post', 'pb');
        self::$_arr_template_data['meta']['datePublished'] = date('Y-m-d H:i:s',$arr_post_info[0]['now_time']);
        $arr_tid_info = Core_CoreData::get('post', 'mgetThread');
        self::$_arr_template_data['meta']['dateLatestReply'] = date('Y-m-d H:i:s',$arr_tid_info['last_modified_time']);
        $thread_id = Util_Request::get('intTid', 0);
        $input = array(
            'extid' => $thread_id,
            'type' => 1,
        );
        $arrRet = Tieba_Service::call('push', 'getTdkRecord', $input, null, null, 'post', 'php', 'gbk');
        if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call push getTdkRecord failed.input:'.serialize($input).'].output:['.serialize($arrRet).'].');
        }
        $tdktitle = '';
        $tdkdesc = '';
        $tdkkws = '';
        $querywords = '';
        if(!empty($arrRet['data']['tdktitle'])){
            if(!is_utf8($arrRet['data']['tdktitle'])){
                $tdktitle = Bingo_Encode::convert($arrRet['data']['tdktitle'], Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
            }else{
                $tdktitle = $arrRet['data']['tdktitle'];
            }
            self::$_arr_template_data['meta']['tag_title'] = $tdktitle;
        }
        if(!empty($arrRet['data']['tdkdesc'])){
            if(!is_utf8($arrRet['data']['tdkdesc'])){
                $tdkdesc = Bingo_Encode::convert($arrRet['data']['tdkdesc'], Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
            }else{
                $tdkdesc = $arrRet['data']['tdkdesc'];
            }
            self::$_arr_template_data['meta']['description'] = $tdkdesc;
        }
        if(!empty($arrRet['data']['tdkkws'])){
            if(!is_utf8($arrRet['data']['tdkkws'])){
                $tdkkws = Bingo_Encode::convert($arrRet['data']['tdkkws'], Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
            }else{
                $tdkkws = $arrRet['data']['tdkkws'];
            }
            self::$_arr_template_data['meta']['keywords'] = $tdkkws;
        }
        if(!empty($arrRet['data']['querywords'])){
            if(!is_utf8($arrRet['data']['querywords'])){
                $querywords = Bingo_Encode::convert($arrRet['data']['querywords'], Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
            }else{
                $querywords = $arrRet['data']['querywords'];
            }
            $querywords = unserialize($querywords);
            $topQuery = array();
            $secondQuery = array();
            $totalQuery = array();
            foreach($querywords as $perQuery){
                foreach($perQuery as $key => $perWord){
                    if(empty($perWord)){
                        continue;
                    }
                    if($key == 'query_word'){
                        $topQuery[] = $perWord;
                    }
                    if($key == 'related_words' && is_array($perWord)){
                        $secondQuery = array_merge($secondQuery, $perWord);
                    }

                }
            }
            $totalQuery = array_merge($topQuery, $secondQuery);
            $totalQuery = array_unique($totalQuery);
			$queryHit = array();
			if(count($totalQuery) > 2){
				$queryHit = array_rand($totalQuery,2);
			}else{
				$queryHit = $totalQuery;
			}

			$inner_link = array();
			$thread_ids_arr = array();
			if(is_array($queryHit) && !empty($queryHit)){
				$objRalMulti = new Tieba_Multi('push_getRecommendResources_multi');
				foreach($queryHit as $index => $perQuery){
					$arrMultiInput = array(
						'serviceName'   =>  'push',
						'method'        =>  'getRecommendResources',
						'ie'            =>  'gbk',
						'input'         =>  array(
							'query_words' => array($perQuery),
							'thread_id' => $thread_id,
						),
					);
					$objRalMulti->register('push_grr' . $index, new Tieba_Service('push'), $arrMultiInput);
				}
				$arrMultiOutput = $objRalMulti->call();
				foreach ($arrMultiOutput as $perPushInfo) {
					if (!empty($perPushInfo['data'])) {
						foreach($perPushInfo['data'] as $key => $queryInfo){
							foreach($queryInfo as $index => $urlInfo){
								$preg_ret = preg_match("/http\:\/\/tieba\.baidu\.com\/p\/(.*)/i", $urlInfo['redirect_url'], $value);
								if($preg_ret != 1){
									unset($queryInfo[$index]);
									continue;
								}
								$thread_ids_arr[] = $value[1];	
								
							}
							$inner_link = array_merge($inner_link, $queryInfo);
						}
					}
				}
			}
			if(!empty($thread_ids_arr)){
				$thread_ids_str = implode("_", $thread_ids_arr);
				Tieba_Stlog::addNode('inner_link', $thread_ids_str);
			}

            //$url_pre = 'http://tieba.baidu.com/f/search/res?qw=';
            //$url_aft = '&sm=2';
            //self::$_arr_template_data['inner_link'][0]['querywords'] = $totalQuery;
            //self::$_arr_template_data['inner_link'][0]['url_pre'] = $url_pre;
            //self::$_arr_template_data['inner_link'][0]['url_aft'] = $url_aft;
			if(!empty($inner_link)){
				if(!is_utf8($inner_link[0]['title'])){
					$inner_link = Bingo_Encode::convert($inner_link, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
				}
				self::$_arr_template_data['inner_link'] = $inner_link;
			}
        }
        $arrForumInfo = Core_CoreData::get('forum', 'getBtxInfo');
        $forumDirInfo = $arrForumInfo['dir'];
        switch(self::_judgeType($forumDirInfo['level_1_name'],$forumDirInfo['level_2_name'])){
            case self::CATEGORY_WENXUE:
                self::getWenxueTdkInfo();
                break;
            case self::CATEGORY_GAME:
                self::getGameTdkInfo();
                break;
            case self::CATEGORY_EDU:
                self::getCommonTdkInfo(self::CATEGORY_EDU);
                break;
            case self::CATEGORY_SHUMA:
                self::getCommonTdkInfo(self::CATEGORY_SHUMA);
                break;
            case self::CATEGORY_DONGMAN:
                self::getCommonTdkInfo(self::CATEGORY_DONGMAN,array('suffix' => '动漫'));
                break;
        } 
    }
    private static function _judgeType($lv1Name,$lv2Name){
        if(empty($lv1Name)){
            return self::CATEGORY_OTHER;
        }
        switch(true){
            case $lv1Name == "游戏":
                return self::CATEGORY_GAME;
            case $lv1Name == "文学" && in_array($lv2Name,self::$_wenxueLv2Name):
                return self::CATEGORY_WENXUE;
            case $lv1Name == "高等院校" && $lv2Name != '校园青春':
                return self::CATEGORY_EDU;
            case $lv1Name == "电脑数码":
                return self::CATEGORY_SHUMA;
            case $lv1Name == "动漫":
                return self::CATEGORY_DONGMAN;
        }
        return self::CATEGORY_OTHER;
    }

    protected static function getGameTdkInfo(){
        $arrForumInfo = Core_CoreData::get('forum', 'getBtxInfo');
        $arrTidInfo = Core_CoreData::get('post', 'mgetThread');
        $pbInfo = Core_CoreData::get('post', 'pb');

        $forumDirInfo = $arrForumInfo['dir'];
        $strFname = $arrForumInfo['forum_name']['forum_name'];
        $strTypeName = self::$_gameFdirMap[$forumDirInfo['level_2_name']]?  self::$_gameFdirMap[$forumDirInfo['level_2_name']] : $forumDirInfo['level_2_name'];
        $strTitle = mb_substr($arrTidInfo['title'],0,15);
        $strContent = !empty($pbInfo[0]['content']) ? trim(strip_tags($pbInfo[0]['content'])) : "";

        $arrCommonKeywords = array();
        // 获取游戏公共词库
        $arrInput = array('keys' => array(self::GAME_COMMON_KEYWORDS));
        $arrOutput = Tieba_Service::call('common', 'getTDKKeywords',$arrInput,null, null, 'post', 'php', 'utf-8');
        if($arrOutput === false || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(sprintf("call common::getTDKKeywords input: %s, output: %s",serialize($arrInput),serialize($arrOutput)));
        }else{
            $strCommonKeywords= empty($arrOutput['data'][self::GAME_COMMON_KEYWORDS])?array():$arrOutput['data'][self::GAME_COMMON_KEYWORDS];
            $arrCommonKeywords = explode(",",$strCommonKeywords);
        }

        $keywordsIndex = array();
        $keywordsCount = array();
        if($strContent != "" && !empty($arrCommonKeywords)){
            foreach($arrCommonKeywords as $kw){
                $count = mb_substr_count($strContent,$kw);
                if($count >0){
                    $keywordsCount[$kw] = $count;
                    $keywordsIndex[] = $kw;
                }
            }
        }
        $arrKeywords = self::_sortKeywords($keywordsCount,array_flip($keywordsIndex));

        if(!empty($arrKeywords)){
            Tieba_Stlog::addNode("pb_tdk_game_keyword",1);
        }else{
            Tieba_Stlog::addNode("pb_tdk_game_no_keyword",1);
        }
        $arrTitleKeywords = array_slice($arrKeywords,0,self::KEYWORD_MAX_LENTH);
        $strTitleKeywords = implode("_",$arrTitleKeywords);
        if($strTitleKeywords != ""){
            $strTitleKeywords = "_".$strTitleKeywords;
        }

        self::$_arr_template_data['meta']['tag_title'] = sprintf("%s_%s_%s%s-百度贴吧",$strTitle,$strFname,$strTypeName,$strTitleKeywords);
        self::$_arr_template_data['meta']['description'] = sprintf("%s%s",$strTitle,mb_substr($strContent,0,100));
        // {原贴Title}，{游戏名字}，{二级目录}，{词库关键词1}，{词库关键词2}，游戏

        $arrKeywordList = array($strTitle,$strFname,$strTypeName);
        foreach($arrTitleKeywords as $kw){
            $arrKeywordList[] = $kw;
        }
        $arrKeywordList[] = '游戏'; 
        self::$_arr_template_data['meta']['keywords'] = implode(",",$arrKeywordList);
    }

    protected static function getWenxueTdkInfo(){
        $arrTidInfo = Core_CoreData::get('post', 'mgetThread');
        $arrForumInfo = Core_CoreData::get('forum', 'getBtxInfo');
        $pbInfo = Core_CoreData::get('post', 'pb');
        $strTitle = $arrTidInfo['title'];
        $strFname = $arrForumInfo['forum_name']['forum_name'];
        $intFnameLen = mb_strlen($strFname);
        if(mb_substr($strTitle,0,$intFnameLen) != $strFname){
            $strTitle = sprintf("%s_%s",$strFname,$strTitle);
        }
        // 标题最长控制在7字以内，但是小说放量的标题为“小说名-章节名”,所以为了控制章节名，需要保留len(小说名-)+7
        $strShortTitle = mb_substr($strTitle,0,$intFnameLen+1+7);
        $strContent = !empty($pbInfo[0]['content']) ? trim(strip_tags($pbInfo[0]['content'])) : "";

        $strAuthor = "";
        $arrRoles = array();
        // 小说专属词库
        $authorKey = sprintf("forum_author_%d",$arrForumInfo['forum_name']['forum_id']);
        $rolesKey = sprintf("forum_roles_%d",$arrForumInfo['forum_name']['forum_id']);
        $arrInput = array('keys' => array(
            $authorKey,$rolesKey
        ));
        $arrOutput = Tieba_Service::call('common', 'getTDKKeywords',$arrInput,null, null, 'post', 'php', 'utf-8');
        if($arrOutput === false || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(sprintf("call common::getTDKKeywords input: %s, output: %s",serialize($arrInput),serialize($arrOutput)));
        }else{
            $strAuthor = !empty($arrOutput['data'][$authorKey])?$arrOutput['data'][$authorKey]:"";
            $strRoles = !empty($arrOutput['data'][$rolesKey]) ? $arrOutput['data'][$rolesKey] : "";
            $arrRoles = explode(",",$strRoles);
        }

        $keywords = array();
        foreach($arrRoles as $role){
            if(mb_strpos($strContent,$role) !== false){
                $keywords[] = $role;
            }
        }
        if (!empty($keywords)){
            Tieba_Stlog::addNode("pb_tdk_literature_keyword",1);
        }else{
            Tieba_Stlog::addNode("pb_tdk_literature_no_keyword",1);
        }

        // {文学名字}_{章节名}_{文学名字}最新章节全文免费阅读-百度贴吧
        self::$_arr_template_data['meta']['tag_title'] = sprintf("%s_%s最新章节全文免费阅读-百度贴吧",$strShortTitle,$strFname);

        //{标题}{正文前100字}
        self::$_arr_template_data['meta']['description'] = sprintf("%s%s",$strTitle,mb_substr($strContent,0,100));

        // {文学名字}，{文学作者}，{文学人物1}，{文学人物2}。。。{文学人物4}
        $arrKeywordList = array($strFname);
        if(!empty($strAuthor)){
            $arrKeywordList[] = $strAuthor;
        }
        if(!empty($keywords)){
            foreach($keywords as $kw){
                $arrKeywordList[] = $kw;
            }
        }
        self::$_arr_template_data['meta']['keywords'] = implode(",",$arrKeywordList);
    }

    protected static function getCommonTdkInfo($fieldType,$other = array()){
        $arrForumInfo = Core_CoreData::get('forum', 'getBtxInfo');
        $arrTidInfo = Core_CoreData::get('post', 'mgetThread');
        $pbInfo = Core_CoreData::get('post', 'pb');

        $forumDirInfo = $arrForumInfo['dir'];
        $strFname = $arrForumInfo['forum_name']['forum_name'];
        $strTitle = mb_substr($arrTidInfo['title'],0,15);
        $strContent = !empty($pbInfo[0]['content']) ? trim(strip_tags($pbInfo[0]['content'])) : "";

        $commonKey = self::$_commonKeyMap[$fieldType];
        $arrCommonKeywords = array();
        // 获取公共词库
        $arrInput = array('keys' => array($commonKey));
        $arrOutput = Tieba_Service::call('common', 'getTDKKeywords',$arrInput,null, null, 'post', 'php', 'utf-8');
        if($arrOutput === false || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(sprintf("call common::getTDKKeywords input: %s, output: %s",serialize($arrInput),serialize($arrOutput)));
        }else{
            $strCommonKeywords= empty($arrOutput['data'][$commonKey])?array():$arrOutput['data'][$commonKey];
            $arrCommonKeywords = explode(",",$strCommonKeywords);
        }

        $keywordsIndex = array();
        $keywordsCount = array();
        if($strContent != "" && !empty($arrCommonKeywords)){
            foreach($arrCommonKeywords as $kw){
                $count = mb_substr_count($strContent,$kw);
                if($count >0){
                    $keywordsCount[$kw] = $count;
                    $keywordsIndex[] = $kw;
                }
            }
        }
        $arrKeywords = self::_sortKeywords($keywordsCount,array_flip($keywordsIndex));

        if(!empty($arrKeywords)){
            Tieba_Stlog::addNode(sprintf("pb_tdk_%s_keyword",self::$_logKeyMap[$fieldType]),1);
        }else{
            Tieba_Stlog::addNode(sprintf("pb_tdk_%s_no_keyword",self::$_logKeyMap[$fieldType]),1);
        }
        $arrKeywords = array_slice($arrKeywords,0,self::KEYWORD_MAX_LENTH);

        $arrTitle = array($strTitle,$strFname);
        if(isset($other['suffix'])){
            $arrTitle[] = $other['suffix'];
        }
        if(!empty($arrKeywords)){
            foreach($arrKeywords as $kw){
                $arrTitle[] = $kw;
            }
        }
        self::$_arr_template_data['meta']['tag_title'] = sprintf("%s-百度贴吧",implode("_",$arrTitle));
        self::$_arr_template_data['meta']['description'] = sprintf("%s,%s",$strTitle,mb_substr($strContent,0,100));

        $arrKeywordList = array($strTitle,$strFname);
        foreach($arrKeywords as $kw){
            $arrKeywordList[] = $kw;
        }
        self::$_arr_template_data['meta']['keywords'] = implode(",",$arrKeywordList);
    }
    /**
     * 判断本吧是否开放
     * @param  [type]  $intForumId [description]
     * @return boolean             [description]
     */
    private static function _getSpiderSwitch() {
        $handleWordServer = Wordserver_Wordlist::factory();
        $strTableName = 'tb_wordlist_redis_pushconfig';
        $arrInput = array('pb_push_spider_switch');
        $arrConf = $handleWordServer->getValueByKeys($arrInput, $strTableName);
        return intval($arrConf['pb_push_spider_switch']);
    }
    private static function _buildPostInfo() {
        $arr_post_info = Core_CoreData::get('post', 'pb');
        $arr_sig_info = Core_CoreData::get('user', 'mgetSignaturesBySids');

        $bolHideFormInfo = $_POST['pbui_is_hide_forum_info'];
        $arrInput = array();
        foreach ($arr_post_info as $key => $value) {
            $int_sid = isset($value['sign_id']) ? intval($value['sign_id']) : 0;
            $int_pid = isset($value['post_id']) ? intval($value['post_id']) : 0;

            if (!empty($int_sid) && isset($arr_sig_info[$int_sid])) {
                $arr_post_info[$key]['sign_info'] = $arr_sig_info[$int_sid];
            }

            if ( $bolHideFormInfo ) {
                // 若forum_id为0，查询会出错，因此填充me0407的fid(24981790)
                $arrInput[] = array(
                    'user_id' => $value['user_id'],
                    'forum_id' => (0 == $value['v_forum_id'] ? 24981790 : $value['v_forum_id']),
                );
            }
        }
        // 如果为全态PB页，查询每个用户的回复来源吧的等级
        if ( $bolHideFormInfo ) {
            $arrRet = Tieba_Service::call('perm', 'mgetUserForumLevel', 
                array(
                    'req' => $arrInput,
                ), null, null, 'post', 'php', 'utf-8');
            $arrUserForumLevel = array();
            if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
                Bingo_Log::warning('call perm mgetUserForumLevel failed.input:'.serialize($arrInput).'].output:['.serialize($arrRet).'].');
                $bolHideFormInfo = false;
            } else {
                $arrUserForumLevel = $arrRet['score_info'];
            }
        }

        $build_post = $arr_post_info;
        //unset ip for security
        foreach ($build_post as $key => &$post_item) {
            if (isset($post_item['ip'])) {
                unset($post_item['ip']);
            }
            if (isset($post_item['comment_info'])) {
                foreach ($post_item['comment_info'] as &$comment_item) {
                    if (isset($comment_item['ip'])) {
                        unset($comment_item['ip']);
                    }
                }
            }
            // 若为全吧态，将用户回复来源对应的吧及等级存入post_list
            if( $bolHideFormInfo && array_key_exists($arrUserForumLevel[$key]['forum_id'], self::$_arrForumNameShared) ) {
                $arrLevelInfo = $arrUserForumLevel[$key];
                $post_item['from_level'] = array(
                    'forum_name'    =>  self::$_arrForumNameShared[$arrLevelInfo['forum_id']],
                    'level_id'      =>  $arrLevelInfo['level_id'],
                    'level_name'    =>  $arrLevelInfo['level_name'],
                );
            }
        }
        self::$_arr_template_data['post_list'] = $build_post;
    }

    private static function _buildMetaInfo() {
	    $arr_post_info = Core_CoreData::get('post', 'pb');
	    $arr_tid_info = Core_CoreData::get('post', 'mgetThread');
        $arr_forum_info = Core_CoreData::get('forum', 'getBtxInfo');
	    $intLzUid = $arr_tid_info['user_id'];

	    // 生成keywords
	    $strKeywords = '百度贴吧,'. strval($arr_forum_info['forum_name']['forum_name']);
	    $str_title = isset($arr_tid_info['title']) ? strval($arr_tid_info['title']) : $arr_post_info[0]['title'];
	    if (!empty($str_title)) {
		    $arrTitleKeywords = Util_String::splitString($str_title);
		    $strKeywords .= implode(',', $arrTitleKeywords);
	    }

	    // 生成description
	    $strDesc = '';
	    $strNoDesc = '';
	    $arrPostComment = array(); // 楼中楼
	    foreach ($arr_post_info as $post) {
		    // 获取每个楼层的楼中楼
		    if (!empty($post['comment_info'])) {
			    $arrPostComment = array_merge($arrPostComment, $post['comment_info']);
		    }

		    $strTxtContent = trim(strip_tags($post['content']));

		    if (empty($strTxtContent)) {
			    continue;
		    }

		    // 内容长度>200
		    if (strlen($strTxtContent) > 200) {
				// 此楼层为楼主
			    if ($post['user_id'] == $intLzUid) {
				    $strDesc = Util_String::mb_substr($strTxtContent, 0, 100);
				    break;
			    } else {
				    $strNoDesc = Util_String::mb_substr($strTxtContent, 0, 100);
			    }
		    } else {
			    // 拼接楼主的各个楼层内容
			    if (strlen($strDesc) > 200) {
				    continue;
			    }
			    if ($post['user_id'] == $intLzUid) {
				    $strDesc .= $strTxtContent;
			    }
		    }
	    }

	    if (strlen($strDesc) < 200 && $strNoDesc != '') {
		    $strDesc = $strNoDesc;
	    }

	    if ($strDesc == '') {
		    // 楼中楼内容不为空
		    if (!empty($arrPostComment)) {
			    foreach ($arrPostComment as $comment) {
				    $strTxtContent = trim(strip_tags($comment['content']));
				    if (empty($strTxtContent)) {
					    continue;
				    }
				    if (strlen($strTxtContent) > 100) {
					    $strDesc = Util_String::mb_substr($strTxtContent, 0, 100);
					    break;
				    } else {
					    if (strlen($strDesc) > 100) {
						    continue;
					    }
					    $strDesc .= $strTxtContent;
				    }
			    }
		    }
	    }

		$strSubTitle = Util_String::mb_substr($str_title, 0, 10);
	    $arrMetaInfo = array(
	    	'keywords' => $strKeywords,
            'description' => $strSubTitle. '..'. $strDesc,
            '_desc_suffix' => $strDesc, // 临时字段，暂存一下$strDesc，后续使用
	    );
	    self::$_arr_template_data['meta'] = $arrMetaInfo;
    }

    private static function _buildUserInfo() {
        $intUid = Util_Session::get('intUid');
        $arr_user_info = Core_CoreData::get('user', 'getUserData');
        $arr_user_more = Core_CoreData::get('user', 'mgetUserForumInfo');
        $arr_thread_info = Core_CoreData::get('post', 'mgetThread');
        $arr_perm_info = Core_CoreData::get('perm', 'getPerm');
        $arr_urank_info = Core_CoreData::get('userrank', 'userRankInfo');
        $arr_ureco_info = Core_CoreData::get('userreco','userRecoInfo');
        $arr_block_info = Core_CoreData::get('userstate', 'get_user_block_info');
        $arr_block_reason = Core_CoreData::get('anti', 'antiGetBlockReason');

        //merge the user data
        $int_uid = Util_Session::get('intUid');
        if (!empty($arr_user_more[$int_uid])) {
            $arr_user_info = array_merge($arr_user_info, $arr_user_more[$int_uid]);
        }
        $arr_user_info['show_nickname'] = Molib_Util_User::getShowNickname($arr_user_info);
        //urank数据处理
        $arr_user_rank = array();
        if (!empty($arr_urank_info)) {
            $arr_user_rank = $arr_urank_info;
            unset($arr_user_rank['errNo']);
        }

        //forbidden数据处理
        $arr_forbidden = array();
        if (!empty($arr_block_info)) {
            $arr_forbidden = array(
                'isForbid' => true,
                'days_to_free' => ceil(($arr_block_info['end_time'] - $arr_block_info['start_time']) / 86400),
                'type' => $arr_block_info['block_type'],
                'opgroup' => $arr_block_info['opgroup'],
                'block_reason' => $arr_block_reason['res']['remarks'],
                'block_errno' => $arr_block_info['block_errno'], // 添加封禁错误号
            );
        } else if (!empty($arr_thread_info['forum_id_shared'])) {
            $objRalMulti = new Tieba_Multi('pc_pbui_dataproc_multi');
            foreach ($arr_thread_info['forum_id_shared'] as $index => $intFid) {
                $arrMultiInput = array(
                    'serviceName'   =>  'userstate',
                    'method'        =>  'get_user_block_info',
                    'ie'            =>  'utf-8',
                    'input'         =>  array(
                        'forum_id'      => $intFid,
                        'user_id'       => Util_Session::get('intUid'),
                        'ip'            => Util_Session::get('intUip'),
                    ),
                );
                $objRalMulti->register('get_user_block_info' . $index, new Tieba_Service('userstate'), $arrMultiInput);
            }
            $arrMultiOutput = $objRalMulti->call();
            $arrBlockedForums = array();
            foreach ($arrMultiOutput as $arrUserBlockInfo) {
                if (!empty($arrUserBlockInfo['block_info'])) {
                    $intBlockedFid = $arrUserBlockInfo['block_info'][0]['forum_id'];
                    $arrBlockedForums[] = self::$_arrForumNameShared[$intBlockedFid] . '吧';
                }
            }
            $arr_forbidden = array(
                'isForbid' => !empty($arrBlockedForums),
                'opgroup' => 'multi',
                'forum_names' => $arrBlockedForums,
            );
        }
        //构建登录用户数据
        if (Util_Session::get('bolLogin')) {
            $arrCreatorInfo = Tieba_Video_Util::isVideoCreator(Util_Session::get('intUid'), 'pc');
            $bolIsCreator = ($arrCreatorInfo['is_creator'] == 0 ? false : true);
            $intPopUpInfo = $arrCreatorInfo['pop_up_info'];
            $build_user_info = array(
                'is_login' => Util_Session::get('bolLogin'),
                'user_id' => Util_Session::get('intUid'),
            	'show_content_info' => $arr_ureco_info,
                // 'session_id'    => Tieba_Session_Socket::getSessionId(),
                'user_name' => Util_Session::get('strUname'),
                'no_un' => Util_Session::get('bolNoname'),
                'mobilephone' => Tieba_Util::maskPhone(Util_Session::get('strMobile')),
                'email' => Tieba_Util::maskEmail(Util_Session::get('strEmail')),
                'urank' => $arr_user_rank,
                'userhide' => 0,       //add
                'need_black_pop' => 0,   //add
                'black_pop_level' => 0,   //add
                'forbidden' => $arr_forbidden,
                'tips' => (!empty($arr_perm_info['tip'])) ? $arr_perm_info['tip'] : array(),
                'baidu_id' => Util_Session::get('strBaiduid'),
                'is_videocreator' => $bolIsCreator,
                'pop_up_info' => $intPopUpInfo,
            );
            $build_user_info = array_merge($build_user_info, $arr_user_info);
            $build_user_info['portrait'] = Tieba_Ucrypt::encode(Util_Session::get('intUid'), Util_Session::get('strUname'));
            $reqUserData = array(
                'user_id' => Util_Session::get('intUid'),
                'need_pass_info' => 1,
            );
            $resUserData = Tieba_Service::call('user','getUserDataEx',$reqUserData);
            if ($resUserData['user_info']['portrait_time']>0)
            {
                $build_user_info['portrait'] .= "?t=".$resUserData['user_info']['portrait_time'];
            }
             //判断是否为官方号
             $build_user_info['is_business_account'] = isset($resUserData['user_info']['business_account']['status']) ? intval($resUserData['user_info']['business_account']['status']) : 0;
        } else {
            $build_user_info = array(
                'is_login' => false,
                'id' => 0,
                'sid' => '',
                'name' => '',
                'no_un' => '',
                'mobilephone' => '',
                'email' => '',
                'baidu_id' => Util_Session::get('strBaiduid'),
            );
        }

        //PC登录优化，PM luodunfeng01
        $build_user_info['pb_login_switch'] = self::_getPcLoginSwitch();

        //PC行为交互优化，PM luodunfeng01
        $build_user_info['interaction_switch'] = self::_getPcInteractionSwitch();
        $build_user_info['login_day'] = Tieba_Config_Flowconf::getPcLoginDay();

        /*
        Tieba_Stlog::addNode('no_un', $arr_login_info['bolNoname']);
        Tieba_Stlog::addNode('mobilephone', $arr_login_info['strMobile']);
        Tieba_Stlog::addNode('email', $arr_login_info['strEmail']);
        Tieba_Stlog::addNode('new_pb', intval($arrUser['is_new_version']));
        Tieba_Stlog::addNode('cookieuid', Tieba_Session_Socket::getCookieUid());
         */

        self::$_arr_template_data['user'] = $build_user_info;
    }

    /**
     * [_doHottopicHref 给post_list中pb的content#内容#加上href的超链接
     * <AUTHOR>
     * @date 2016-04-20 16:33:30
     * @param  [type] &$post_list [description]
     * @return [type]             [description]
     */
    private static function _doHottopicHref(&$title) {
        if (!empty($title)) {
            $patterns = '/#([^#]+)#/';
            $replacements = '<a href="http://tieba.baidu.com/hottopic/browse/hottopic?topic_name=\1">#\1#</a>';
            $title = preg_replace($patterns, $replacements, html_entity_decode($title));
        }
        return $title;
    }


    /**
     * 处理帖子内容，将图片和文本分离
     * @param string $content
     * @return array
     */
    public static function parseHtmlImg($content) {
        require_once ROOT_PATH . '/php/phplib/phptest/SystemTest/lib/domparser/simple_html_dom.php';
        $html = str_get_html ( $content );
        $text = $html->plaintext;
        $text = nl2br ( $text );
        $picUrls = array ();
        foreach ( $html->find ( 'img' ) as $img ) {
            return true;
        }
        foreach ( $html->find ( 'a' ) as $img ) {
            return true;
        }
        foreach ( $html->find ( 'span' ) as $img ) {
            return true;
        }
        foreach ( $html->find ( 'div' ) as $img ) {
            return true;
        }
        return false;
    }

    /**
     * @param
     * @return
     */
    public static function checkHtmlTag($match) {
        if (self::parseHtmlImg($match[0])) {
            return $match[0];
        } else {
            return self::buildHrefUrl($match[1]);
        }
    }

    /**
     * @param
     * @return
     */
    public static function buildHrefUrl($strTopicName) {
        return '<a href="http://tieba.baidu.com/hottopic/browse/hottopic?topic_name=' . $strTopicName .  '">#' . $strTopicName  . '#</a>';
    }

    private static function _buildContext() {
        $arr_post_info = Core_CoreData::get('post', 'pb');
        $arr_head_info = Core_CoreData::get('post', 'head');
        $arr_fid_info = Core_CoreData::get('forum', 'getFidByFname');
        $arr_perm_info = Core_CoreData::get('perm', 'getPerm');
        $arr_forum_info = Core_CoreData::get('forum', 'getBtxInfo');
        $arr_tid_info = Core_CoreData::get('post', 'mgetThread');
        $arr_user_info = Core_CoreData::get('user', 'mgetUserForumInfo');
        //whether from pad
        $str_user_agent = Util_Request::get('strUserAgent', '');
        $str_user_agent = strtolower($str_user_agent);
        if ((bool)strpos($str_user_agent, 'ipad')) {
            $bol_pad = true;
        } else {
            $bol_pad = false;
        }

        //page number info
        $int_start_no = isset($arr_head_info['start_no']) ? intval($arr_head_info['start_no']) : 0;
        $int_valid_no = isset($arr_head_info['valid_post_num']) ? intval($arr_head_info['valid_post_num']) : 0;
        $int_page_size = Util_Request::get('intPageSize', '30');
        $arr_page = array(
            'cur_page' => intval(($int_start_no + 1) / $int_page_size) + 1,
            'total_page' => ceil($int_valid_no / $int_page_size),
            'page_size' => ($int_valid_no-$int_start_no) < $int_page_size?($int_valid_no-$int_start_no):$int_page_size,
        );

        //special info
        $arr_special = array(
            'has_sub_post' => 1,           //the function of sub-post is definite��equal one
            'has_grade' => 1,           //the function of grade is definite, equal one
            'has_lucky_lottery' => Ui_UiCupid::getLuckLottery(),
            'has_basket_lottery' => Ui_UiCupid::getBasketLottery(),
            'has_ssq_lottery' => Ui_UiCupid::getSsqLottery(),
            'has_foot_lottery' => Ui_UiCupid::getFootLottery(),
            'is_match_news' => Ui_UiCupid::getFootBallNews(),
            'lz_only' => Util_Request::get('intLzOnly', 0),   //whether choose the function of lz only
            'has_lz_only' => 1,           //the function of browse only the author's post is definite, equal one
        );

        //version info
        $int_version = Util_UiTool::getPbVersion();

        //build tbs
        $str_first_uname = (!empty($arr_head_info['first_post_username'])) ? $arr_head_info['first_post_username'] : '';
        $bol_login = Util_Session::get('bolLogin');
        $int_uid = Util_Session::get('intUid');
        $int_tid = Util_Request::get('intTid', 0);
        $int_fid = intval($arr_head_info['forum_id']);
        $str_fname = strval($arr_forum_info['forum_name']['forum_name']);
        $arr_tbs = Util_UiTool::buildTbs($bol_login, $int_uid, $str_first_uname, $int_tid, $int_fid, $str_fname);

        //whether the forum has been merged
        //Attention: the difference between the forum which has been merged and the forum which name has been changed
        $int_post_fid = isset($arr_head_info['forum_id']) ? $arr_head_info['forum_id'] : 0;
        $int_fetch_fid = isset($arr_fid_info['forum_id']) ? $arr_fid_info['forum_id'] : 0;
        $int_merge = 0;
        if ($int_post_fid != $int_fetch_fid && $int_post_fid != 0 && $int_fetch_fid != 0) {
            $int_merge = 1;
        }
        //trick for qiku
        if(($int_post_fid == 20921315) || ($int_post_fid ==1010255))
        {
            $int_merge = 0;
        }
        //judge the source
        $str_userfrom = Util_UiTool::buildUserFrom();

        //build reply num, include posts number and sub-posts number
        if (1 == Util_Request::get('intLzOnly')) {
            $int_reply_num = isset($arr_head_info['valid_post_num']) ? $arr_head_info['valid_post_num'] : 0;
        } else {
            $int_reply_num = isset($arr_head_info['total_post_num']) ? $arr_head_info['total_post_num'] : 0;
        }


        //build the pv num of the thread
        $int_freq_num = isset($arr_tid_info['freq_num']) ? intval($arr_tid_info['freq_num']) : 0;

        //build the author's info
        $int_author_uid = intval($arr_head_info['first_post_userid']);
        $arr_author_info = isset($arr_user_info[$int_author_uid]) ? $arr_user_info[$int_author_uid] : array();
        // 判断作者是否是入驻吧的官方号
        $arrBusinessInfo = $arr_author_info['business_account'];
        if (!empty($arrBusinessInfo) && $arrBusinessInfo['status'] == 1 && !empty($arrBusinessInfo['fid']) && in_array($int_fid, $arrBusinessInfo['fid'])) {
            $arr_author_info['is_forum_business_account'] = 1;
        } else {
            $arr_author_info['is_forum_business_account'] = 0;
        }

        //build thread info
        $str_title = isset($arr_post_info[0]['title']) ? strval($arr_post_info[0]['title']) : $arr_tid_info['title'];
        $str_title = Util_UiTool::ltrimStr($str_title);
        $int_tid = Util_Request::get('intTid', 0);
        $int_thread_type = isset($arr_head_info['thread_type']) ? $arr_head_info['thread_type'] : 0;
        //$str_title = self::_doHottopicHref($str_title);
        
        /*有效特征加入title tag(帖子图片数大于等于4,title tag最前面加【图片】;帖子为视频贴时,title tag最前面加【视频】)
          当帖子标题中包含：图、视频两个字段时，不增加此特征。 */
        $title_prex = "";
        $arr_forum_info = Core_CoreData::get('forum', 'getBtxInfo');
        //print_r($arr_forum_info['dir']);
        //print_r(self::$_title_prex_dir);
        //var_dump($arr_forum_info['dir']['level_2_name']);
        //var_dump(self::$_title_prex_dir[$arr_forum_info['dir']['level_1_name']]);
        //var_dump(array_key_exists($arr_forum_info['dir']['level_1_name'],self::$_title_prex_dir));
        //var_dump(in_array($arr_forum_info['dir']['level_2_name'],self::$_title_prex_dir[$arr_forum_info['dir']['level_1_name']]));
        //if((array_key_exists($arr_forum_info['dir']['level_1_name'],self::$_title_prex_dir) && in_array($arr_forum_info['dir']['level_2_name'],self::$_title_prex_dir[$arr_forum_info['dir']['level_1_name']])) || strpos($str_title,$str_fname)!==false || strpos($str_title,"视频")!==false || strpos($str_title,"图")!==false || strpos($str_fname,"视频")!==false || strpos($str_fname,"图")!==false)
        if(strpos($str_title,$str_fname)!==false || strpos($str_title,"视频")!==false || strpos($str_title,"图")!==false || strpos($str_fname,"视频")!==false || strpos($str_fname,"图")!==false)
        {
              $_show = "yes";//在这些一/二级目录中的吧，title还是显示_下划线     
        }
        $input_picnum = array(
            "thread_ids" => array(
                0 => $int_tid,
            ),      
            "need_abstract" => 0,
            "forum_id" => 0,
            "need_photo_pic" => 1,
            "need_user_data" => 0,
            "need_forum_name" => 0, //是否获取吧名
            "call_from" => "pc_frs" //上游模块名
        );
        $res_picnum = Tieba_Service::call('post', 'mgetThread', $input_picnum, null, null, 'post', 'php', 'utf-8');
        //var_dump($res_picnum['output']['thread_list'][$int_tid]);
        if($int_thread_type==40 && strpos($str_title,"视频")===false && strpos($str_fname,"视频")===false)
        {
            $title_prex = "【视频】";
        }
        elseif($res_picnum['output']['thread_list'][$int_tid]['media_num']['pic']>=4 && strpos($str_title,"图")===false && strpos($str_fname,"图")===false)
        {
            $title_prex = "【图片】";
        }
        //如果PB本身没有标题则用首楼内容做标题
        if($arr_post_info[0]['is_ntitle']===1)
        {
            $arrInput = array (
                'thread_id' => $int_tid,
                'res_num' => 1,
                'offset' => 0,
                'see_author' => 0,
                'has_comment' => 0,
                'has_mask' => 0,
                'has_ext' => 0,
                'structured_content' => 1,
            );
            $arrOutput = Tieba_Service::call ( 'post','getPostsByThreadId', $arrInput, null, null, 'post', 'php', 'utf-8');
            //print_r($arrOutput['output']['output'][0]['post_infos'][0]['content']);
            $strContentNew = "";
            foreach ($arrOutput['output']['output'][0]['post_infos'][0]['content'] as $value)
            {
                if($value['tag']=='plainText')
                {
                    $strContentNew .= $value['value'];
                }
            }
            //var_dump($strContentNew);
            if($strContentNew=="")
            {
                $str_title_fill = $str_title;
            }
            else
            {
                //先过滤表情和图片，取50个字符
                $str_title_fill = Util_String::mb_substr($strContentNew,0,50);
                //找50个字以后的第一个标题
                $pun = array('，','。','？','“','”','：','；','……','！','（','）');//常用的中文标点
                $str = Util_String::mb_substr($strContentNew,50,100);
                $charset = 'UTF-8';
                $strlen=mb_strlen($str);     
                while($strlen)
                {
                    $tval = mb_substr($str,0,1,$charset);  
                    $array_temp[]=$tval;         
                    //var_dump($tval);
                    //var_dump(preg_match('/^[\x7f-\xff]+$/', $tval));
                    if((preg_match("/^\d*$/",$tval) || preg_match('/^[a-zA-Z]+$/',$tval) || preg_match('/^[\x7f-\xff]+$/', $tval)) && in_array($tval,$pun)===false)
                    {
                        $str_title_fill .= $tval;
                    }
                    else
                    {
                        break;
                    }
                    $str=mb_substr($str,1,$strlen,$charset);
                    $strlen=mb_strlen($str);    
                }
                //print_r($array_temp);
            }
        }
        else
        {
            $str_title_fill = $str_title;
        }
        $str_title_fill = Util_String::mb_substr($str_title_fill,0,100);//如果tag_title>100，强切割
        $int_not_top_stick = 0;
        if (isset($arr_tid_info['uegnottopstick'])) {
        	//ueg control
        	$int_not_top_stick = 1;
        }
        $arr_thread = array(
            'tag_title' => $title_prex.$str_title_fill,
            'title'     => $str_title,
            'thread_id' => $int_tid,
            'thread_type' => $int_thread_type,
            'author_name' => $str_first_uname,
            'author_info' => $arr_author_info,
            'reply_num' => $int_reply_num,
            'is_not_top_stick' => $int_not_top_stick,
            'type_array' => Tieba_Type_Thread::getTypeArray($int_thread_type),
            '_show'=>$_show,
        );
        if (isset($arr_tid_info['keywords'])) {
            $arr_thread['keywords'] = $arr_tid_info['keywords'];
        }
        if (isset($arr_tid_info['yc_novel'])) {
            $arr_thread['yc_novel'] = $arr_tid_info['yc_novel'];
        }
        if (isset($arr_tid_info['yc_comment'])) {
            $arr_thread['yc_comment'] = $arr_tid_info['yc_comment'];
        }
        //can post pic num / img num / vedio num and vedio whitelist
        $arr_post_perm = Util_OldUrl::getPostPerm();

        //aqiyi vedio
        $int_local_upload = Ui_UiCupid::getCanVideoLocalUpload();

        // 12.4 视频作品pc pb落地页
        $worksInfo = array();
        if (isset($arr_tid_info['works_info']) && !empty($arr_tid_info['works_info']) && isset($arr_tid_info['works_info']['is_works']) && $arr_tid_info['works_info']['is_works'] == 1) {
            $worksInfo = $arr_tid_info['works_info'];
        } else {
            $worksInfo = array("is_works" => 0);
        }
        $arr_thread['works_info'] = $worksInfo;

        //新增评价帖标示
        if (isset($arr_tid_info['item_id']) && intval($arr_tid_info['item_id']) > 0 && isset($arr_tid_info['item_score']) && !empty($arr_tid_info['item_score'])) {
            $itemInfo = array(
                'id' => $arr_tid_info['item_id'],
                'score' => json_decode($arr_tid_info['item_score'], true),
            );
            $arr_thread['item_info'] = $itemInfo;
        }
        //贴+评论保护
        $arr_thread['is_launched_tiebaplus'] = 0;
        if (!empty($arr_tid_info['is_launched_tiebaplus'])) {
            $arr_thread['is_launched_tiebaplus'] = 1;
        }
        //build  context
        $build_context = array(
            'is_from_pad' => $bol_pad,
            'page' => $arr_page,
            'is_from_spider' => Util_Conf::checkSpider(),
            'mask' => 0,       //add
            'special' => $arr_special,
            'search_pid' => Util_Request::get('intPid', 0),
            'search_cid' => Util_Request::get('intCid', 0),
            'pb_version' => $int_version,
            'tbs' => $arr_tbs,
            'join_forum' => $int_merge,
            'userfrom' => $str_userfrom,
            'thread_info' => $arr_thread,
            'post_perm' => $arr_post_perm,
            'is_interest_smiley' => 1,       //the function of interest smiley is constant, equal one
            'can_local_upload' => $int_local_upload,
            'simple_mode' => Util_Conf::get('lite_version', 0),
            'refer' => Util_Request::get('strHttpRefer'),
            'is_bazhu_apply' => isset($arr_tid_info['is_bazhu_apply']) ? $arr_tid_info['is_bazhu_apply'] : 0,
            'is_pro_thread' => isset($arr_tid_info['is_pro_thread']) ? $arr_tid_info['is_pro_thread'] : 0,
        );
        if ($arr_page['cur_page'] <= $arr_page['total_page'] && count($arr_post_info) == 0) {
            $build_context['exist_forbidden_tip'] = 1;
        }


        //fourth manager and PM can view PV of the thread
        if (isset($arr_perm_info['perm']['can_view_freq']) && true == $arr_perm_info['perm']['can_view_freq']) {
            $build_context['thread_info']['freq_num'] = $int_freq_num;
        }

        self::$_arr_template_data['context'] = $build_context;
    }

    private static function _buildForumInfo() {
        $arr_forum_info = Core_CoreData::get('forum', 'getBtxInfo');
        $arr_bawu_info = Core_CoreData::get('perm', 'getBawuList');
        $arr_perm_info = Core_CoreData::get('perm', 'getPerm');
        $arr_frmmbr_info = Core_CoreData::get('perm', 'getForumMemberInfo');
        $arr_btx_ex = Core_CoreData::get('forum', 'mgetBtxInfoEx');
        $arr_global_attr = Core_CoreData::get('forum', 'getAllGlobalAttr');
        $arr_thread_info = Core_CoreData::get('post', 'mgetThread');

        //通过cupid获取部分吧属性
        //是否在图片贴吧黑名单当中
        $has_in_photo_black_list = (boolean)Ui_UiCupid::getInPhotoBlackList();
        if (1 == $has_in_photo_black_list) {
            $has_album_photo = 0;
            $has_open_photo_frs = 0;
            $switch_to_photo = 0;
        } else {
            //查询是否默认开启图册精品区小流量
            $has_album_photo = (boolean)Ui_UiCupid::getAlbumPhoto();
            //查询是否默认开启图片贴吧小流量
            $has_open_photo_frs = 1;//图片贴吧全流量
            //是否可以从 看图pb页切换到 图片贴吧
            $switch_to_photo = (boolean)Ui_UiCupid::getIfSwitchToPhoto();
        }

        //查询是否禁止开启图片话题贴
        $has_ban_pic_topic = Ui_UiCupid::getBanPicTopic();
        $album_good_smallflow = (boolean)Ui_UiCupid::getDefaultPhotoFrs();
        //部分吧禁止发图片
        $no_post_pic = intval(Ui_UiCupid::getBanPostPic());
        //是否可以设置直播贴/话题贴
        $bol_forum_topic = intval(Ui_UiCupid::getTopicInfo());

        //处理fstyle数据
        //网盘帖相关
        $bol_disk = isset($arr_forum_info['attrs'][self::DISK_MODULE_ID]);
        //视频贴相关
        $bol_vedio = isset($arr_forum_info['attrs'][self::VIDEO_MODULE_ID]);
        //是否是明星贴吧
        $bol_star = isset($arr_forum_info['attrs'][self::STAR_FRS_MODULE_ID]);
        //是否是商业吧
        $bol_comforum = isset($arr_forum_info['attrs'][self::COMFORUM_MODULE_ID]);
        //是否是定制版
        $bol_custom = isset($arr_forum_info['attrs'][self::CUSTOMIZE_MODULE_ID]);
        //是否是官方吧
        $bol_official = isset($arr_forum_info['attrs'][self::GCON_MODULE_ID]);

        //处理第四吧主数据
        if (!empty($arr_bawu_info['fourthmanager'])) {
            foreach ($arr_bawu_info['fourthmanager'] as $key => $value) {
                if (!empty($value['user'])) {
                    $arr_fourthmanager[$key] = $value['user'];
                }
            }
        } else {
            $arr_fourthmanager = array();
        }

        //shield
        $bol_shield = false;
        if ('disable' !== Util_Conf::get('HideUserAndPost', 'disable') && Util_Session::get('bolLogin')) {
            $bol_shield = true;
        }

        //backgound
        if (!empty($arr_forum_info['background'])) {
            $arr_background = $arr_forum_info['background'];
        } else {
            //default value, result from the past arrangement
            $arr_background = array(
                'bg_id' => 1,
                'more_bg_open' => 0,
                'more_bg_types' => 0,
            );
        }

        // $bolHideFormInfo = false;
        // if(self::$_forum_shared[$arr_forum_info['forum_name']['forum_id']]) {
        //     $bolHideFormInfo = true;
        // }
        // 如果帖子是全吧态，则需要查询分发吧名
        $bolHideFormInfo = $_POST['pbui_is_hide_forum_info'];
//        if ( $bolHideFormInfo && is_array($arr_thread_info['forum_id_shared']) ) {

        //多吧态过滤  分别返回被删除的吧和未被删的吧给前端 add by gaoweizhen01   
        $arrDelFname = array();
        if ( is_array($arr_thread_info['forum_id_shared']) ) {
            // foreach($arrShareFid as $value) {
            //     if ($arr_thread_info['forum_share_state'][$value]['is_deleted'] == 0) {
            //         $arrFid[] = $value;
            //     } 
            // }
            // //查询分发吧名
            // $arrInput = array(
            //     "forum_id" => $arrFid,
            // );
            // $arrRet = Tieba_Service::call('forum', 'getFnameByFid', $arrInput, null, null, 'post', 'php', 'utf-8');
            // if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            //     Bingo_Log::warning('call forum getFnameByFid failed.input:'.serialize($arrInput).'].output:['.serialize($arrRet).'].');
            // } else {
            //     foreach ($arrRet['forum_name'] as $fname) {
            //         self::$_arrForumNameShared[$fname['forum_id']] = $fname['forum_name'];
            //     }
            // }
                 //查询分发吧名
            $arrInput = array(
                "forum_id" => $arr_thread_info['forum_id_shared'],
            );
            $arrRet = Tieba_Service::call('forum', 'getFnameByFid', $arrInput, null, null, 'post', 'php', 'utf-8');
            if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
                Bingo_Log::warning('call forum getFnameByFid failed.input:'.serialize($arrInput).'].output:['.serialize($arrRet).'].');
            } else {
                foreach ($arrRet['forum_name'] as $fid => $fname) {
                    if (!empty($arr_thread_info['forum_share_state'])) {
                        if(intval($arr_thread_info['forum_share_state'][$fid]['is_deleted']) == 0) {
                            self::$_arrForumNameShared[$fname['forum_id']] = $fname['forum_name'];
                        } else {
                            $arrDelFname[$fname['forum_id']] = $fname['forum_name'];
                        }
                    }else {
                        self::$_arrForumNameShared[$fname['forum_id']] = $fname['forum_name'];
                    }
                }
            }
        }
        //加入PC导流浮层
        $pcdrift = array("switch"=>0,"jumpurl"=>'',"picurl"=>'');
        $pcstartendtime = self::getConf("tb_wordlist_redis_split_envelope","pc_drift_2019_activetime","str");
        $pctimetmp = explode("-",$pcstartendtime);
        if(time()>=strtotime($pctimetmp[0]) && time()<=strtotime($pctimetmp[1]))
        {
            $pcdrift['switch']=1;
            $pcdrift_jumptid = self::getConf("tb_wordlist_redis_split_envelope","pc_drift_2019_jumpurl","str");
            $pcdrift['jumpurl'] = "https://tieba.baidu.com/p/".$pcdrift_jumptid;
            $pcdrift['picurl'] = self::getConf("tb_wordlist_redis_split_envelope","pc_drift_2019_picurl","str");
        }
        //跳转帖本身不展示导流图片
        if(Util_Request::get('intTid', 0)==$pcdrift_jumptid)
        {
            $pcdrift = array("switch"=>0,"jumpurl"=>'',"picurl"=>'');
        }
        else
        {
            //进行IP判断，内网和北上广深不展示导流条
            $redis = new Bingo_Cache_Redis('twlive');
            $uip = Bingo_Http_Ip::getConnectIp();
            $uiptmp = explode(".", $uip);
            if($uiptmp[0] == 10 || ($uiptmp[0] == 192 && $uiptmp[1] == 168) || ($uiptmp[0] == 172 && $uiptmp[1] >= 16 && $uiptmp[1] <=31))
            {
                //先判断是否内网IP
                Bingo_Log::warning("pcdriftuip=".$uip." in Intranet.");
                $pcdrift = array("switch"=>0,"jumpurl"=>'',"picurl"=>'');
            }
            else
            {
                //如果不是内网IP再查ipcity redis
                $rinput = array(
                        'key' => "ipcity".$uiptmp[0].".".$uiptmp[1],
                );
                $redis_ret = self::redis_recall($redis,'GET',$rinput);
                if($redis_ret['ret'][$rinput['key']] != null)
                {
                    $ipinterval = unserialize($redis_ret['ret'][$rinput['key']]);
                    foreach($ipinterval as $uk => $uv)
                    {
                        $ut = explode("|",$uv);
                        if(ip2long($uip)>=$ut[0] && ip2long($uip)<=$ut[1])
                        {
                            Bingo_Log::warning("pcdriftuip=".$uip." in bsgs.".$uv);
                            $pcdrift = array("switch"=>0,"jumpurl"=>'',"picurl"=>'');
                        }
                    }
                }
            }
        }
        //构建forum数据
        $build_forum_info = array(
            'forum_name' => strval($arr_forum_info['forum_name']['forum_name']),
            'forum_id' => intval($arr_forum_info['forum_name']['forum_id']),
            'level_1_name' => strval($arr_forum_info['dir']['level_1_name']),
            'level_2_name' => strval($arr_forum_info['dir']['level_2_name']),
            'managers' => (!empty($arr_bawu_info['manager'])) ? $arr_bawu_info['manager'] : array(),
            'fourthmanager' => $arr_fourthmanager,
            'forbid_flag' => isset($arr_perm_info['block_type']) ? intval($arr_perm_info['block_type']) : 0,
            'background' => $arr_background,
            'attrs' => (!empty($arr_forum_info['attrs'])) ? $arr_forum_info['attrs'] : array(),
            'need_safe' => 0,       //add
            'can_forum_topic' => $bol_forum_topic,
            'member_name' => $arr_frmmbr_info['member_name'],
            'member_count' => $arr_frmmbr_info['member_count'],
            'album_forum' => $has_album_photo,
            'ban_pic_topic' => $has_ban_pic_topic,
            'is_album_post' => $switch_to_photo,
            'album_good_smallflow' => $album_good_smallflow,
            'no_post_pic' => $no_post_pic,
            //'is_open_disk'      => $bol_disk,
            'is_video_v1' => $bol_vedio,
            //'is_star'           => $bol_star,
            //'is_customize'      => $bol_custom,
            //'is_comforum'       => $bol_comforum,
            //'is_official'       => $bol_official,
            'is_readonly' => intval(Util_Conf::get('global_read_only', 0)),
            'has_picture_frs' => $has_open_photo_frs,
            'shield_post' => $bol_shield,
            'global_attr' => $arr_global_attr,

            'hide_forum_info' => $bolHideFormInfo,
            'forum_name_shared' => self::$_arrForumNameShared,
            'true_forum_id' => $_POST['pbui_true_forum_id'],
            'forum_shared_deleted' => $arrDelFname,
            'pcdrift' => $pcdrift,
        );
        //为模板提供吧头像变量
        $build_forum_info['avatar'] = $arr_forum_info['card']['avatar'];

        $build_forum_info['post_num'] = $arr_btx_ex['output'][$build_forum_info['forum_id']]['statistics']['post_num'];

        //Tieba_Stlog::addNode('bgid', intval($build_forum_info['background']['bg_id']));

        self::$_arr_template_data['forum'] = $build_forum_info;
    }

    private static function _buildPerm() {
        $arr_perm_info = Core_CoreData::get('perm', 'getPerm');
        $arr_head_info = Core_CoreData::get('post', 'head');
        $arr_thread_info = Core_CoreData::get('post', 'mgetThread');
        $arr_user_info = Core_CoreData::get('user', 'mgetUserForumInfo');

        //get the author's private set
        $int_author_uid = intval($arr_head_info['first_post_userid']);
        $int_author_reply_private = isset($arr_user_info[$int_author_uid]['priv_sets']['reply']) ? $arr_user_info[$int_author_uid]['priv_sets']['reply'] : 1;

        //build user perm
        if (Util_Session::get('bolLogin')) {

            //lz delete thread/post
            $int_login_uid = Util_Session::get('intUid', 0);
            $int_first_uid = isset($arr_head_info['first_post_userid']) ? $arr_head_info['first_post_userid'] : 0;
            $bol_lz_del = false;
            if ($int_login_uid == $int_first_uid) {
                $bol_lz_del = true;
            }

            //whether have the power of picasso
            $int_power_level = intval(Util_Conf::get('default_tuya_grade', 7));
            $int_user_level = isset($arr_perm_info['grade']['level_id']) ? intval($arr_perm_info['grade']['level_id']) : 0;
            $bol_picasso = false;
            if ($int_user_level >= $int_power_level) {
                $bol_picasso = true;
            }

            $build_perm = (!empty($arr_perm_info['perm'])) ? $arr_perm_info['perm'] : array();
            $build_perm['forever_ban'] = Ui_UiCupid::getForeverBan();
            $build_perm['lz_del'] = $bol_lz_del;
            $build_perm['picasso'] = $bol_picasso;

            //多吧删除icon add by gaoweizhen01 
            $build_perm['share_forum_perm'] = array();
            if (!empty($arr_thread_info['forum_share_state'])) {
                $objRalMulti =  new Tieba_Multi('pc_pbui_dataproc_multi');
                foreach($arr_thread_info['forum_share_state'] as $key => $value ) {     
                    $arrMultiInput = array(
                        'serviceName' => 'perm',
                        'method'      => 'getPerm',
                        'ie'          => 'utf-8',
                        'input'       => array(
                            "forum_id" => $key,
                            'user_id'   => Util_Session::get('intUid'),
                            'user_ip'   => Util_Session::get('intUip'),
                            'need_user_tip' => 1,
                        ),
                    );
                    $objRalMulti->register($key, new Tieba_Service('perm'), $arrMultiInput);
                }
                $arrMultiOutput = $objRalMulti->call();
                foreach ($arrMultiOutput as $arrKey => $arrValue) {
                    if (!empty($arrValue['output']['perm'])) {
                        $build_perm['share_forum_perm'][$arrKey]['can_type3_audit_post'] = $arrValue['output']['perm']['can_type3_audit_post'];
                        $build_perm['share_forum_perm'][$arrKey]['can_type2_audit_post'] = $arrValue['output']['perm']['can_type2_audit_post'];
                        $build_perm['share_forum_perm'][$arrKey]['can_type1_audit_post'] = $arrValue['output']['perm']['can_type1_audit_post'];
                    }
                }
            }

            //话题贴权限判断
            $build_perm['can_set_topic'] = false;
            $arr_forum_perm = Core_CoreData::get('forum', 'getBtxInfo');
            $bol_forum_perm = isset($arr_forum_perm['perm'][self::FORUM_PERM_TOPICTHREAD]);
            if ($build_perm['can_op_topic'] && ($bol_forum_perm || (bool)Ui_UiCupid::getTopicInfo())) {
                $build_perm['can_set_topic'] = true;
            }

            $bolHideFormInfo = self::$_arr_template_data['forum']['hide_forum_info'];
            if ( $bolHideFormInfo ) {
                $build_perm['can_post'] = true;
            }

            $build_perm['reply_private_flag'] = Molib_Util_UserReplyPerm::getReplyPrivateFlag($int_author_reply_private, $int_author_uid, Util_Session::get('intUid'));
            if (isset($arr_thread_info['is_bazhu_apply']) && intval($arr_thread_info['is_bazhu_apply']) == 1){
                $build_perm['reply_private_flag'] = 0;
            }
        } else {
            $build_perm = array(
                'forever_ban' => false,
                'lz_del' => false,
                'picasso' => false,
            );
        }

        self::$_arr_template_data['perm'] = $build_perm;
    }

    private static function _buildPostUser() {
        $arr_user_info = Core_CoreData::get('user', 'mgetUserForumInfo');
        $arr_bawu_info = Core_CoreData::get('perm', 'getBawuList');
        $ptimeuids = array();
        foreach ($arr_user_info as $key => $value) {
            $build_post_user[$key] = $value;

            //build the users' head portrait data through calc of uid and uname
            $build_post_user[$key]['portrait'] = Tieba_Ucrypt::encode($value['user_id'], $value['user_name']);
            array_push($ptimeuids,$value['user_id']);
            //users' nickname
            $build_post_user[$key]['nickname'] = (!empty($value['user_nickname'])) ?
                $value['user_nickname'] : $value['user_name'];
            $build_post_user[$key]['profession_manager_nick_name'] = (!empty($value['profession_manager_nick_name'])) ?
                $value['profession_manager_nick_name'] : null;
            //get users' bawu title
            $build_post_user[$key]['bawu'] = 0;
            foreach ($arr_bawu_info['manager'] as $manager) {
                if ($manager['user']['user_id'] == $value['user_id']) {
                    $build_post_user[$key]['bawu'] = 1;
                    break;
                }
            }
            if (empty($build_post_user[$key]['bawu'])) {
                foreach ($arr_bawu_info['assist'] as $assist) {
                    if ($assist['user']['user_id'] == $value['user_id']) {
                        $build_post_user[$key]['bawu'] = 2;
                        break;
                    }
                }
            }

            foreach ($arr_bawu_info['profession_manager'] as $manager) {
                if ($manager['user']['user_id'] == $value['user_id']) {
                    $build_post_user[$key]['bawu'] = 3;
                    break;
                }
            }

            //user icon
            if (!empty($value['iconinfo']) && self::MAX_ICON_SIZE > count($value['iconinfo'])) {
                $build_post_user[$key]['iconinfo'] = array_slice($value['iconinfo'], 0, self::MAX_ICON_SIZE);
            }
        }

        $arrReq = array(
            'user_id' => $ptimeuids,
            'need_follow_info' => 0,
            'get_icon' => 0,
        );
        $res = Molib_Util_MultiService::multiGetUserData($arrReq, 'user', 'mgetUserDataEx', 'pbui_dataproc_mgetuserdataEX');

        foreach($build_post_user as $k => &$v)
        {
            if($res['user_info'][$v['user_id']]['portrait_time']>0)
            {
                $v['portrait'] .= "?t=".$res['user_info'][$v['user_id']]['portrait_time'];
            } 
            $v['show_nickname'] = Molib_Util_User::getShowNickname($res['user_info'][$v['user_id']]);  
        }
        self::$_arr_template_data['post_user_list'] = $build_post_user;
    }

    private static function _paramForumData() {
        //forum data
        $arr_forum = self::$_arr_template_data['forum'];
        $arr_forum_style = array(
            'is_open_disk' => $arr_forum['is_open_disk'],
            'is_video_v1' => $arr_forum['is_video_v1'],
            'is_star' => $arr_forum['is_star'],
            'is_customize' => $arr_forum['is_customize'],
            'is_comforum' => $arr_forum['is_comforum'],
            'is_official' => $arr_forum['is_official'],
        );
        $arr_team = array(
            'managers' => $arr_forum['managers'],
            'fourthmanager' => $arr_forum['fourthmanager'],
        );

        $param_forum = array(
            'forum_id' => $arr_forum['forum_id'],
            'forum_name' => $arr_forum['forum_name'],
            'level_1_name' => $arr_forum['level_1_name'],
            'level_2_name' => $arr_forum['level_2_name'],
            'attrs' => $arr_forum['attrs'],
            'forum_style' => $arr_forum_style,
            'team' => $arr_team,
            'true_forum_id' => $arr_forum['true_forum_id'],
            'forum_name_shared' => $arr_forum['forum_name_shared'],
        );

        self::$_arr_params_data['forum'] = $param_forum;
    }

    private static function _paramUserData() {
        $arr_user = self::$_arr_template_data['user'];
        $canPushPosts = (!empty($arr_user['can_push_posts'])) ? intval($arr_user['can_push_posts']) : 0; //beyond
        $beyondUserInfo = (!empty($arr_user['beyond_user_info'])) ? $arr_user['beyond_user_info'] : array(); //beyond
        $param_user = array(
            'user_id' => $arr_user['user_id'],
            'user_name' => $arr_user['user_name'],
            'user_ip' => Util_Session::get('intUip'),
            'email' => $arr_user['email'],
            'mobilephone' => $arr_user['mobilephone'],
            'fans_attr' => $arr_user['fans_attr'],
            'baidu_id' => $arr_user['baidu_id'],
            'is_like' => $arr_user['is_like'],
            'is_black' => $arr_user['is_black'],
            'can_push_posts' => $canPushPosts,
            'beyond_user_info' => $beyondUserInfo,
        	'Parr_scores' => $arr_user['Parr_scores'],
        );

        self::$_arr_params_data['user'] = $param_user;
    }

    private static function _paramContextData() {
        $arr_context = self::$_arr_template_data['context'];
        $arr_querystring = array_merge($_GET, $_POST);

        $param_context = array(
            'refer' => Util_Request::get('strHttpRefer'),
            'querystring' => $arr_querystring,
            'log_id' => REQUEST_ID,
            'cookie' => $_COOKIE,
            'server' => $_SERVER,
            'user_agent' => Util_Request::get('strUserAgent', ''),
            'special' => $arr_context['special'],
            'thread_info' => $arr_context['thread_info'],
            'conf' => Util_Conf::getAll(),
        );

        self::$_arr_params_data['context'] = $param_context;
    }

    private static function _paramCupidData() {
        $param_cupid = Ui_UiCupid::getData();

        self::$_arr_params_data['cupid'] = $param_cupid;
    }

    private static function _paramPermData() {
        $arr_perm = self::$_arr_template_data['perm'];

        $param_perm = array(
            'role' => array(//add by fengzhen,2014-12-02
                            'can_type1_audit_post' => $arr_perm['can_type1_audit_post'],//bawu,assist,voiceadmin
                            'can_type2_audit_post' => $arr_perm['can_type2_audit_post'],//manager
                            'can_type3_audit_post' => $arr_perm['can_type3_audit_post'],//pm
                            'can_op_as_4thmgr' => $arr_perm['can_op_as_4thmgr'],//fourth_manager
            ),
        );

        self::$_arr_params_data['perm'] = $param_perm;
    }

    private static function _paramPostData($arr_user_lists) {
        $arr_post = self::$_arr_template_data['post_list'];
        $arr_context = self::$_arr_template_data['context'];
        $arr_head_info = Core_CoreData::get('post', 'head');

        $arr_user_list = array(
            'post_user_map' => $arr_user_lists['post_user_map'],
            'sub_user_map' => $arr_user_lists['sub_post_user_map'],
        );

        $int_lz_uid = (!empty($arr_head_info['first_post_userid'])) ? intval($arr_head_info['first_post_userid']) : 0;
        $str_lz_uname = (!empty($arr_head_info['first_post_username'])) ? strval($arr_head_info['first_post_username']) : '';
        $int_first_uid = (!empty($arr_post[0]['user_id'])) ? intval($arr_post[0]['user_id']) : 0;
        $str_fist_uname = $arr_user_lists['post_user_map'][$int_first_uid];
        $intHasPushed = (!empty($arr_post[0]['has_pushed'])) ? intval($arr_post[0]['has_pushed']) : 0;

        $arr_post_need = self::_filterNecessaryField($arr_post);
        $param_post = array(
            'thread_id' => $arr_context['thread_info']['thread_id'],
            'thread_type' => $arr_context['thread_info']['thread_type'],
            'thread_type_array' => $arr_context['thread_info']['type_array'],
            'thread_title' => $arr_context['thread_info']['title'],
            'lz_uid' => $int_lz_uid,
            'lz_uname' => $str_lz_uname,
            'first_uid' => $int_first_uid,
            'first_uname' => $str_fist_uname,
            'first_pid' => $arr_post[0]['post_id'],
            'first_content' => $arr_post[0]['content'],
            'user_list' => $arr_user_list,
            'post_list' => $arr_post_need,
            'has_pushed' => $intHasPushed, //beyond
        );
        if (isset($arr_context['thread_info']['keywords'])) {
            $param_post['keywords'] = $arr_context['thread_info']['keywords'];
        }

        if (isset($arr_post[1]) && isset($arr_post[1]['post_id']) && isset($arr_post[1]['content'])) {
            $param_post['second_pid'] = $arr_post[1]['post_id'];
            $param_post['second_content'] = $arr_post[1]['content'];
        }

        if (isset($arr_post[2]) && isset($arr_post[2]['post_id']) && isset($arr_post[2]['content'])) {
            $param_post['third_pid'] = $arr_post[2]['post_id'];
            $param_post['third_content'] = $arr_post[2]['content'];
        }

        if (is_array($arr_post) && !empty($arr_post)) {
            foreach ($arr_post as $value) {
                if (isset($value['post_id'])) {
                    $param_post['pid_list'][] = $value['post_id'];
                    $param_post['pno_list'][$value['post_id']] = $value['post_no'];
                }
            }
        }

        if (isset($arr_post[0]['from_thread_id'])) {
            $param_post['from_thread_id'] = intval($arr_post[0]['from_thread_id']);
        }
        if (isset($arr_post[0]['from_post_id'])) {
            $param_post['from_post_id'] = intval($arr_post[0]['from_post_id']);
        }

        self::$_arr_params_data['post'] = $param_post;
    }

    private static function _buildSpringDefense($build_data) {
        return true;
    }

    public static function buildNetLog() {
        $arr_netlog = array(
            'pro' => 'tieba',
            'mid' => 'feye',
            'agent' => Util_Request::get('strUserAgent'),
            'cookieuid' => Tieba_Session_Socket::getLoginUid(),
            'logid' => REQUEST_ID,
            'is_new_user' => intval(Tieba_Cookie_UserType::isNew()),
            'pn' => Util_Request::get('intPageNo'),
            'urlkey' => '20-0',
            'serveice_type' => 'pc',
            'uid' => Util_Session::get('intUid'),
            'un' => Util_Session::get('strUname'),
            'mobilephone' => Tieba_Util::maskPhone(Util_Session::get('strMobile')),
            'email' => Tieba_Util::maskEmail(Util_Session::get('strEmail')),
            'url' => Bingo_Http_Request::getServer('REQUEST_URI'),
            'referer' => Util_Request::get('strHttpRefer'),
            'uip' => Bingo_Http_Ip::ip2long(Bingo_Http_Ip::getConnectIp()),
            'ispv' => 1,
            //'bduid'           => substr(strip_tags(Bingo_Http_Request::getCookie('BAIDUID')), 0, 32),
            'no_un' => Util_Session::get('bolNoname'),
            'user_start_time' => Tieba_Cookie_UserType::getStartTime(),
            'fid' => self::$_arr_template_data['forum']['forum_id'],
            'fname' => self::$_arr_template_data['forum']['forum_name'],
            'post_id' => self::$_arr_template_data['context']['search_pid'],
            'tid' => self::$_arr_template_data['context']['thread_info']['thread_id'],
            'openid' => 'tieba',
            'LzOnly' => Util_Request::get('intLzOnly'),
            'is_like' => self::$_arr_template_data['user']['is_like'],
            'gradelevel' => self::$_arr_template_data['user']['level_id'],
        );

        return $arr_netlog;
    }
    /**
     * some fields of post_list that should be given to subui
     * @param  [type] $arrPosts [post_list]
     * @return [type]           [post_list only with necessary fields]
     */
    private static function _filterNecessaryField($arrPosts){
        $arrNeedPosts = array();
        $necessaryFields = array(
            'pb_tpoint',
        );
        // get these fields in Conf file
        //$necessaryFields = Util_Conf::get('subui_field', '');
        //$arrNeedField = explode(' ', $necessayField);
        foreach($arrPosts as $post){
            $needPost = array(
                'post_id' => $post['post_id'],
                'create_time' => $post['create_time'],
            );
            foreach($necessaryFields as $field){
                $needPost[$field] = (isset($post[$field])? $post[$field]: false);
            }
            $arrNeedPosts[] = $needPost;
        }
        return $arrNeedPosts;
    }
	//add by pangzhanbo for mute 07/01/2015
    private static function _buildMute(){
        $intThreadUserId	= self::$_arr_template_data['context']['thread_info']['author_info']['user_id'];
		$arrThreadUserInfo	= self::$_arr_template_data['post_user_list'][$intThreadUserId];
		$intNowTime	= time();

		//判断楼主会员权限
		if(self::MEMBER_LEVEL_PROPS != $arrThreadUserInfo['mParr_props']['level']['props_id'] || $arrThreadUserInfo['mParr_props']['level']['end_time'] < $intNowTime){
			return false;
		}


		//查询最近封禁用户
		$intThreadId	= self::$_arr_template_data['post_list'][0]['thread_id'];
		$intFirstPostId	= self::$_arr_template_data['post_list'][0]['post_id'];	

		//查询楼主封禁的人
        $arrParam	= array(
			'req'	=> array(
				'user_id'   => $intThreadUserId,
				'type'  => self::MEMBER_MUTE_TYPE,
			),
        );
        $arrRet = Tieba_Service::call('anti','antiUserMuteQuery',$arrParam,null,null,'post','php','utf-8');
        if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning('call anti antiUserMuteQuery failed.input:'.serialize($arrParam).'].output:['.serialize($arrRet).'].');
        }
		$arrUserIds	= array();
		foreach($arrRet['res']['mute_user_info'] as $k  => $v){
			$arrUserIds[]	= $k;
		}
		$arrParam	= array(
			'user_id'	=> $arrUserIds,
			);
        $arrRet = Molib_Util_MultiService::multiGetUserData($arrParam, 'user', 'mgetUserData', 'pbui_dataproc_mgetuserdata');
		if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
			Bingo_Log::warning('call user mgetUserData failed.input:'.serialize($arrParam).'].output:['.serialize($arrRet).'].');
		}
		$arrMuteUserInfo	= $arrRet['user_info'];
		self::$_arr_template_data['mute_info']	= $arrMuteUserInfo;

		//当前用户不是楼主
		if(self::$_arr_template_data['user']['user_id'] != $intThreadUserId){
			return false;
		}

		
        $arrMuteUserIds = $arrUserIds;
		//添加被封禁标示
		foreach(self::$_arr_template_data['post_user_list'] as $intUserId => $arrUserInfo){
			if($intUserId == $intThreadUserId){
				continue;
			}
			if(in_array($intUserId,$arrMuteUserIds)){
				self::$_arr_template_data['post_user_list'][$intUserId]['is_mute']	= self::MEMBER_MUTE_DEFAULT;
			}else{
				self::$_arr_template_data['post_user_list'][$intUserId]['is_mute']	= self::MEMBER_MUTE_UNMUTE;
			}
		}
		
    }
    /**
      * [getConf description]
      * @param  [type] $strTableName [description]
      * @param  [type] $key          [description]
      * @return [type]               [description]
      */
     private static function getConf($strTableName, $key,$type=""){
        $handleWordServer = Wordserver_Wordlist::factory();
        $arrItemInfo = $handleWordServer->getValueByKeys(array($key), $strTableName);
        if ($arrItemInfo == false || !is_array($arrItemInfo) || !array_key_exists($key, $arrItemInfo)) {
            Bingo_Log::warning ( 'get wordlist tagName_data error'. var_export($arrItemInfo,true));
            return false;
        } else {
            if($type == "str")
            {
                return $arrItemInfo[$key];
            }
            else
            {
                return unserialize($arrItemInfo[$key]);
            }
        }
     }
     /**
      * [redis_recall description]
      * @param  [type] $redis  [escription]
      * @param  [type] $method [description]
      * @param  [type] $input  [description]
      * @return [type]         [description]
      */
     public static function redis_recall($redis,$method,$input){
        $ret = $redis->$method($input);
        $retry = 0;
        while($retry < 3 && ($ret['err_no'] != 0 || !$ret)){
            sleep(1);
            $retry++;
            $ret = $redis->$method($input);
        }
        return $ret;
     }

    /**
     * pc端小流量判断
     * @param empty
     * @return bool
     */
    private static function _getPcLoginSwitch() {
//        $strBaiduid = Bingo_Http_Request::getCookie("BAIDUID");
        $strBaiduid = substr(strip_tags(Bingo_Http_Request::getCookie('BAIDUID')), 0, 32);
        $arrPcSwitchInput = array(
            'page' => 'pb',
            'baiduid' => $strBaiduid,
        );
        $boolRes = Tieba_Config_Flowconf::getPcLoginSwitch($arrPcSwitchInput);

        //打点
        Tieba_Stlog::addNode("pb_login_abtest", $boolRes ? '1' : '2');  //命中小流量,1是命中了，2没有
        Tieba_Stlog::addNode("pb_login_status", Util_Session::get('bolLogin') ? '1' : '2');  //是否登录，1登录，2没有
        return $boolRes;
    }

    /**
     * pc端行为交互小流量判断
     * @param empty
     * @return bool
     */
    private static function _getPcInteractionSwitch() {
        $intUid = Util_Session::get('intUid');
        $arrPcSwitchInput = array(
            'page' => 'pb',
            'uid' => $intUid,
        );
        $boolRes = Tieba_Config_Flowconf::getPcInteractionSwitch($arrPcSwitchInput);

        //打点
        Tieba_Stlog::addNode("pb_interaction_abtest", $boolRes ? '1' : '2');  //命中小流量,1是命中了，2没有
        return $boolRes;
    }

    private static function _sortKeywords($kwCount,$kwFlipIndex){
        $data = array();
        $kwIndex = array();
        foreach($kwCount as $kw => $count){
            $data[$kw] = array(
                'count' => $count,
                'index' => $kwFlipIndex[$kw],
            );
            $kwIndex[$kw] = $kwFlipIndex[$kw];
        }
        array_multisort($kwCount,SORT_DESC,$kwIndex,SORT_ASC,$data);
        return array_keys($data);
    }
}
