<?php
/**
 * 全局字典，请求处理中，每次组织模板变量只可以赋值，最后才可以修改，然后赋值给模板
 *
 **/
class Ui_EndProc
{
    public static function assignAll( $build_data ){
        if( empty($build_data) ) {
            return ;
        }

        require_once 'Bingo/Page.php';
        foreach( $build_data as $key => $val ) {
            Bingo_Page::assign($key,$val);
        }
    }

    public static function buildNetLog($arr_log)
    {
        if( !empty($arr_log) ){
            foreach( $arr_log as $key => $value ){
                Tieba_Stlog::addNode($key, $value);
            }
        }

        return true;
    }
}
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
