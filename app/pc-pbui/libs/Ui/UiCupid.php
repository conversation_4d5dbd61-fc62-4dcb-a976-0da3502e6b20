<?php
/*
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-08-08 15:39:33
 * @version
 **/

class Ui_UiCupid
{
    const CUPID_PHOTO_FORUM_NAME_IN_BLACK_LIST  = 225;  //是否在图片贴吧黑名单当中
    const CUPID_ALBUM_PHOTO                     = 27;   //是否默认开启图册精品区小流量
    const CUPID_PHOTO_PB_SWITCH                 = 230;  //是否可以从 看图pb页切换到 图片贴吧
    const CUPID_BAN_PIC_TOPIC                   = 226;  //是否禁止开启图片话题贴
    const CUPID_DEFAULT_PHOTO_FRS               = 205;  //图册小流量
    const CUPID_BANPOSTPIC                      = 203;  //是否禁止发图片
    const CUPID_CONCERN                         = 144;
    const CUPID_BRANDZONE                       = 201;  //是否请求软贴
    const CUPID_ZHIBO_TOPIC                     = 213;  //是否可以设置直播贴/话题贴
    const CUPID_LUCK_LOTTERY                    = 311;  //pb
    const CUPID_BASKET_LOTTERY                  = 321;  //pb
    const CUPID_SSQ_LOTTERY                     = 310;  //pb
    const CUPID_FOOT_LOTTERY                    = 288;
    const CUPID_CAN_VIDEO_LOCAL_UPLOAD          = 255;  //爱奇艺视频本地上传
    const CUPID_FOREVER_BAN_POWER               = 216;  //永久封禁
    const CUPID_FOOTBALL_NEWS					= 331; //pb足球魔方

    private static $_cupid = array();

    public static function getCupid($arr_cupid_input)
    {
        $arr_cupid = array();
        if( !empty($arr_cupid_input) ){
        	$arr_cupid_input = Bingo_Encode::convert($arr_cupid_input,Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
            $arr_cupid = Tbapi_Midlib_Midl_Cupid::query($arr_cupid_input);
            $arr_cupid = Bingo_Encode::convert($arr_cupid,Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
        }
        if( !empty($arr_cupid) ){
            if( empty(self::$_cupid) ){
                self::$_cupid = array();
            }
            self::$_cupid = self::$_cupid + $arr_cupid;
        }
    }

    public static function getData()
    {
        if( !empty(self::$_cupid) ){
            return self::$_cupid;
        }
        return array();
    }

    public static function getInPhotoBlackList()
    {
        return isset( self::$_cupid[self::CUPID_PHOTO_FORUM_NAME_IN_BLACK_LIST] ) ? 1 : 0;
    }

    public static function getAlbumPhoto()
    {
        return isset (self::$_cupid[self::CUPID_ALBUM_PHOTO]) ? 1 : 0;
    }

    public static function getIfSwitchToPhoto()
    {
        return isset (self::$_cupid[self::CUPID_PHOTO_PB_SWITCH]) ? 1 : 0;
    }

    public static function getBanPicTopic()
    {
        return isset (self::$_cupid[self::CUPID_BAN_PIC_TOPIC]) ? 1 : 0;
    }

    public static function getDefaultPhotoFrs()
    {
        return isset (self::$_cupid[self::CUPID_DEFAULT_PHOTO_FRS]) ? 1 : 0;
    }

    public static function getBanPostPic()
    {
        return isset (self::$_cupid[self::CUPID_BANPOSTPIC]) ? 1 : 0;
    }

    public static function getForeverBan()
    {
        return isset (self::$_cupid[self::CUPID_FOREVER_BAN_POWER]) ? 1 : 0;
    }

    public static function getLikeType()
    {
        return isset (self::$_cupid[self::CUPID_CONCERN]) ? 1 : 0;
    }

    public static function getBrandZone()
    {
        return isset (self::$_cupid[self::CUPID_BRANDZONE]) ? 1 : 0;
    }

    public static function getTopicInfo()
    {
        return isset (self::$_cupid[self::CUPID_ZHIBO_TOPIC]) ? 1 : 0;
    }

    public static function getLuckLottery()
    {
        return isset (self::$_cupid[self::CUPID_LUCK_LOTTERY]) ? 1 : 0;
    }

    public static function getBasketLottery()
    {
        return isset (self::$_cupid[self::CUPID_BASKET_LOTTERY]) ? 1 : 0;
    }

    public static function getSsqLottery()
    {
        return isset (self::$_cupid[self::CUPID_SSQ_LOTTERY]) ? 1 : 0;
    }

    public static function getFootLottery()
    {
        return isset (self::$_cupid[self::CUPID_FOOT_LOTTERY]) ? 1 : 0;
    }
 	public static function getFootBallNews()
    {
        return isset (self::$_cupid[self::CUPID_FOOTBALL_NEWS]) ? 1 : 0;
    }

    public static function getCanVideoLocalUpload()
    {
        return isset (self::$_cupid[self::CUPID_CAN_VIDEO_LOCAL_UPLOAD]) ? 1 : 0;
    }
}

