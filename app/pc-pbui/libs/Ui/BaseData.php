<?php
/*
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-08-08 15:39:33
 * @version
 */

class Ui_BaseData{

    public static function getHttpData()
    {
        $res = Util_Request::getRequest();
        return $res;
    }

    public static function getConfData()
    {
        $res = Util_Conf::getUiConf();
        return $res;
    }

    public static function getSessionData()
    {
        $res = Util_Session::getSession();
        return $res;
    }

    public static function getWiseJudge()
    {
        //在配置中配置是否开启自动跳转功能
        $int_auto_jump = intval(Util_Conf::get('has_auto_jump_wap',0));
        if( 0 === $int_auto_jump ){
            return false;
        }

        //从无线跳转过来的，不再跳回到无线
        if( 'wap' === Util_Request::get('strFr') ){
            //手机强制访问PC的cookie有效时长改为当前会话有效 by ha<PERSON><PERSON><PERSON>
            setcookie('wap_skipped',1,0,"/");
            return false;
        }

        //判断是否自动跳转
        if( 1 === Util_Request::get('intAutoJumpSkip') ){
            return false;
        }

        //手机强制访问PC的cookie有效时长改为当前会话有效 by haoyunfeng
        //不需要判断cookie是否存在，如果原来用户已设置为原1年有效期，也更新为当前会话有效
        $bol_wise_device = Util_Device::isWiseDevice( Util_Session::get('intUip') );
        setcookie('wise_device', intval($bol_wise_device), 0, "/");
        return $bol_wise_device;
    }
}
