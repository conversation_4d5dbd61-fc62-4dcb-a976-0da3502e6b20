<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-07-11
 * @brief 划词游戏信息
 * @version
 */

 class Ui_MarkWord{
	 private static $arrQuery = array(
                 '巴西世界杯' => 789,
                 '世界杯' => 789,
                 '敢达' => 752,
                 'SD高达' => 752,
                 '高达' => 752,
                 '大闹天宫' => 495,
                 //'火影忍者' => 773,
                 '死神' => 760,
                 '舞动' => 530,
                 'TOUCH' => 530,
                 '星纪元' => 730,
                 '天才樱木来了' => 737,
                 '热血战纪' => 614,
                 '热血海贼王' => 412,
                 '海贼王' => 412,
                 '暗黑世界' => 653,
                 '暗黑' => 653,
                 '英雄三国' => 823,
//                 '英雄' => 823,
                 'MOBA' => 823,
        );

	 public static function markWord($arrPostData){
		$arrPats = array();
		$arrReps = array();
		foreach(self::$arrQuery as $key => $value){
			$arrPats[] = $key;
			$arrReps[] = sprintf('<a class="comforum-swap-words"  data-swapword="%s">%s</a>', $value, $key);
		}
		$arrPostList = self::_markWord($arrPostData, $arrPats, $arrReps);

		return $arrPostList;
	 }

	 private static function _markWord($arrPostData, $arrPats, $arrReps){
		$strText = '';
		$strSep = '!@#@$@%@^@&@*';
		$index = 0;
		$arrPosIndex  = array();
		foreach($arrPostData as $postdata){
			if(!strstr($postdata['content'], $strSep)){
				$strText .= $postdata['content'];
				$strText .= $strSep;
				$arrPosIndex[$postdata['post_id']] = $index;
				$index++;
			}
		}
		if($strText != ''){
			$strNewText = str_replace($arrPats, $arrReps, $strText);
			if($strNewText!=null && $strNewText!=''){
				$arrNewText = explode($strSep, $strNewText);
				foreach($arrPostData as $key => $postdata){
					if(isset($arrPosIndex[$postdata['post_id']])){
						$arrPostData[$key]['content'] = $arrNewText[$arrPosIndex[$postdata['post_id']]];
					}
				}
			}
		}

		return $arrPostData;
	}
	
 }
