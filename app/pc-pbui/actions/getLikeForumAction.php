<?php
/*
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-08-08 15:39:33
 * @version
 */

class getLikeForumAction extends Bingo_Action_Abstract
{
    public function execute()
    {
        $arrSession = $this->_getSession();
        $arrParams  = $this->_getParams();
        $intUid = $arrSession['uid'];
        if (0 === $intUid){
            $intUid = $arrParams['uid'];
        }
        if (0 === $intUid){
            $this->_jsonRet(210009, 'error to get uid');
            return false;           
        }
        /*
        $arrInput = array(
            'uid' => $intUid,
            );
        $result = Tbapi_Core_Server::apicall('userforum','my_like_pc',$arrInput);
		 */
		//del get like forum from favo-forum add by jiangcan 2016.12.27 16:44
		$result['info'] = array();
		$input = array(
			"user_id" => $intUid, 
			"page_no" => 1,
			"page_size" => 10,
			"check_forum" => 1,
			);
		$res   = Tieba_Service::call('perm', 'getLikeForumList', $input, NULL, NULL, 'post', 'php', 'utf-8');
		if($res['errno'] == 0 && $res['output']['member_count'] > 0){
			foreach($res['output']['member_list'] as $val){
				$tmp =  array();
				$tmp['forum_name'] = $val['forum_name'];
				$tmp['user_level'] = $val['levle_id'];
				$tmp['user_exp'] = $val['cur_score'];
				$tmp['id'] = $val['forum_id'];
				$tmp['is_like'] = 1;
				$tmp['favo_type'] = 2;
				$result['info'][]=$tmp;
			}
		}
		$this->_jsonRet(0, 'success', $result);
        return true;
    }

    private function _getParams(){
        $arrParam = array();
        $arrParam['uid'] = intval(Bingo_Http_Request::getGet('uid', 0));
        return $arrParam;
    }

    private function _jsonRet($intErrno, $strErrmsg, $arrData=''){
        $arrRet = array(
            'errno'     => $intErrno,
            'errmsg'    => $strErrmsg,
            );
        if (is_array($arrData) && count($arrData) > 0){
            $arrRet['data'] = $arrData;
        }
        echo Bingo_String::array2json($arrRet);
    }

    private function _getSession(){
        $arrSession = array();
        require_once 'Tieba/Session/Socket.php';
        $arrSession['uid'] = Tieba_Session_Socket::getLoginUid();
        return $arrSession;
    }
}
