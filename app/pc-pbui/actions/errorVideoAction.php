<?php
/*
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-08-08 15:39:33
 * @version
 */

class errorVideoAction extends Bingo_Action_Abstract
{
    const WORDSERVER_TABLE_NAME = 'tb_wordlist_redis_pb_error_video_conf';
    const WORDSERVER_KEY = 'fids';

    const BATOU_VIDEO_COUNT = 20;
    // 吧头视频黑名单和白名单配置词表
    const WORD_LIST_BATOU_VIDEO_CONF = 'tb_wordlist_redis_pc_batou_video_conf';
    // 吧头视频全局控制配置词表（一键开启吧头视频功能、全栈吧头视频配置视频贴）
    const WORD_LIST_BATOU_VIDEO_NAMELIST = 'tb_wordlist_redis_pc_batou_video_namelist';
    // 吧头视频单吧推荐视频配置词表（每个吧只能配置一个）
    const WORD_LIST_BATOU_VIDEO_THREAD = 'tb_wordlist_redis_pc_batou_video_thread';

    private $intAllForumBatouVideoTid = 0;

    /**
     * [execute description]
     * @return [type] [description]
     */
    public function execute()
    {
        $tbs = Tieba_Tbs::gene(Tieba_Session_Socket::isLogin());
        Bingo_Page::assign('tbs', $tbs);
        
        $handleWordServer = Wordserver_Wordlist::factory();
        $ret = $handleWordServer->getValueByKeys(array('fids'), 'tb_wordlist_redis_pb_error_video_conf');
        if (empty($ret) || empty($ret[self::WORDSERVER_KEY])) {
            Bingo_Log::warning('word server is empty');
            return false;
        }

        $arrFids = unserialize($ret[self::WORDSERVER_KEY]);

        $key = array_rand($arrFids);

        $intFid = $arrFids[$key];


        $isOpen = $this->_isOpenBatouVideo($intFid, $this->intAllForumBatouVideoTid);
        if (!$isOpen) {
            Bingo_Page::assign('videolist', array());
            return;
        }
        $this->intAllForumBatouVideoTid = $this->_getConfTid($intFid, $this->intAllForumBatouVideoTid);

        $intRn = self::BATOU_VIDEO_COUNT;
        if ($this->intAllForumBatouVideoTid > 0) {
            $intRn = self::BATOU_VIDEO_COUNT - 1;
        }

        $input = array(
            'forum_id' => $intFid,
            'rn' => $intRn,
        );
        $ret = Tieba_Service::call('livegroup', 'getTidsForBatouVideo', $input, null, null, 'get', 'php', 'utf-8');

        if (false == $ret || Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning('get livegroup::getTidsForBatouVideo false, input['.serialize($input),'],out['.serialize($ret).']');
        }
        $arrRes = $this->_buildThreadList(
            $ret['data']['thread_id'], 
            $this->intAllForumBatouVideoTid,
            self::BATOU_VIDEO_COUNT
        );

        Bingo_Page::assign('videoList', $arrRes);
        Bingo_Page::setTpl('404video.php');
        return true;
    }


    /**
     * 判断是否展示吧头视频.
     * @param: null
     * @return: true | false
     **/
    private function _isOpenBatouVideo($intFid, &$intAllForumBatouVideoTid) {

        $handleWordServer = Wordserver_Wordlist::factory();
        
        // 获取词表中的吧头视频一键全量开关
        $strTableName = self::WORD_LIST_BATOU_VIDEO_CONF;//英文表名
        $strKey1 = 'all_forum_batou_video_tid';
        $arrInput = array($strKey1);
        $batouConf = $handleWordServer->getValueByKeys($arrInput, $strTableName);
        
        // 这里如果配置了全栈吧头视频贴，则获取全栈吧头视频贴
        if (isset($batouConf['all_forum_batou_video_tid']) && $batouConf['all_forum_batou_video_tid'] > 0) {
            $intAllForumBatouVideoTid = $batouConf['all_forum_batou_video_tid'];
        }

        return true;
    }

    /**
     * @brief 获取推荐的帖子信息. 拷自client_frs佩言的代码
     * @param: null
     * @return: array
     **/
    private function _getConfTid($intFid, $intTid) {
        // 从mis中获取单吧配置的吧头视频贴数据（单吧配置的视频贴优先级大于全栈配置的视频贴）
        $strTableName = self::WORD_LIST_BATOU_VIDEO_THREAD;//英文表名
        $handleWordServer = Wordserver_Wordlist::factory();

        $arrInput = array($intFid);
        $arrItemInfo = $handleWordServer->getValueByKeys($arrInput, $strTableName);
        $intTid = (intval($arrItemInfo[$intFid]) > 0) ? intval($arrItemInfo[$intFid]) : $intTid;
        
        return $intTid;
    }

    /**
     * build贴子列表  拷自client_frs佩言的代码
     * @param  [type] $_arrThreadIdList [description]
     * @param  [type] $_intRn           [description]
     * @return [type]                   [description]
     */
    private function _buildThreadList($_arrThreadIdList, $_intAllForumBatouVideoTid, $_intRn) {
        if (is_array($_arrThreadIdList)) {
            // 对帖子id进行检测
            foreach ($_arrThreadIdList as $key => $value) {
                if ($value <= 0) {
                    unset($_arrThreadIdList[$key]);
                }
            }
        } else {
            $_arrThreadIdList = array();
        }

        if ($_intAllForumBatouVideoTid > 0) {
            array_unshift($_arrThreadIdList, $_intAllForumBatouVideoTid);
        }

        if (count($_arrThreadIdList) <= 0){
            Bingo_Log::warning("input params invalid. [".serialize($_arrThreadIdList)."]");
            return false;
        }

        $arrInput = array(
            'thread_ids'          => $_arrThreadIdList,
            'need_abstract'       => 1,
            'forum_id'            => 0,
            'need_photo_pic'      => 0,
            'need_user_data'      => 1,
            'icon_size'           => 0,
            'need_forum_name'     => 0,
            'need_post_content'   => 0,
            'call_from'           => 'pc_frs',
        );
        $arrOutput = Tieba_Service::call('post', 'mgetThread', $arrInput, null, null, 'post', 'php', 'utf-8');
        if(!$arrOutput || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::fatal("call post::mgetThread error." . serialize($arrOutput));
            return false;
        }

        $arrUidList = array();
        foreach ($arrOutput['output']['thread_list'] as $value) {
            array_push($arrUidList, intval($value['user_id']));
        }
        // 针对屏蔽的用户帖子做过滤
        $input = array(
            'req' => array( //查询输入
                'id_list' => $arrUidList,
                'mask_list' => array('anti_browse'),
                'strMethod' => 'tbmask_query',
            ),
        );
        $output = Tieba_Service::call('anti', 'antiTbmaskQuery', $input, null, null, 'post', 'php', 'utf-8');
        if (null === $output['res'] || Tieba_Errcode::ERR_SUCCESS !== $output['errno']) {
            Bingo_Log::warning('call anti::antiTbmaskQuery fail with input ['.serialize($input).'] ouput ['.serialize($output).']');
            return false;
        }
        $arrUidStatus = array();
        foreach ($output['res']['res_list'] as $value) {
            $arrUidStatus[$value['id']] = $value['anti_browse'];
        }
        // 对删除的帖子做过滤
        foreach ($arrOutput['output']['thread_list'] as $value){
            if ($value['is_deleted'] == 1) {
                continue;
            }
            if ($arrUidStatus[$value['user_id']] == 1) {
                continue;
            }
            $tempThreadList[intval($value['thread_id'])] = $value;
        }

        if (count($tempThreadList) > $_intRn) {
            $threadList = array_slice($tempThreadList, 0, $_intRn);
        } else {
            $threadList = $tempThreadList;
        }
        
        return $threadList;

    }
}
