<?php

class commentAction extends Bingo_Action_Abstract
{
    public function execute()
    {
        $arr_request = array();
        $arr_session = array();

        $arr_request = $this->getRequest();
        $arr_session = $this->getSession();
        
        $arr_input = array(
            'thread_id' => $arr_request['intTid'],
            'post_id'   => $arr_request['intPid'],
            'comment_id'=> 0,
            'offset'    => $arr_request['intOffset'],
            'res_num'   => $arr_request['intPageSize'],
            'status'    => 0,
            'has_mask'  => 1,
        );

        $arr_output = Tieba_Service::call('post', 'getComments', $arr_input, NULL, NULL, 'post', 'php', 'utf-8');

        if( !isset($arr_output['errno']) || 0 != $arr_output['errno'] ){
            Bingo_Log::warning('call service post::getComments err, input : '. serialize($arr_input));
        }
        $arr_comments = $arr_output['output'];

        if('enabled' == Util_Conf::get('HideUserAndPost', 'disabled') ){
            $arr_comments = $this->filterComment($arr_comments, $arr_session, $arr_request);
        }

        $this->buildData($arr_comments, $arr_request);
    }

    private function getRequest()
    {
        $arr_request = array();

        $arr_request = array(
            'intTid'        => intval(Bingo_Http_Request::getGet('tid', 0)),
            'intPid'        => intval(Bingo_Http_Request::getGet('pid', 0)),
            'intPageSize'   => intval(Bingo_Http_Request::getGet('rn', 10)),
            'intPageno'     => intval(Bingo_Http_Request::getGet('pn', 2)),
        );

        $intOffset = ($arr_request['intPageno'] - 1 ) * $arr_request['intPageSize'];
        if ($intOffset < 0) {
            $intOffset = 0;
        }
        $arr_request['intOffset'] = $intOffset;

        return $arr_request;
    }

    private function getSession()
    {
        $arr_session = array();
        require_once 'Tieba/Session/Socket.php';

        $arr_session = array(
            'bolLogin'      => Tieba_Session_Socket::isLogin(),
            'intUid'        => Tieba_Session_Socket::getLoginUid(),
            'strUname'      => Tieba_Session_Socket::getLoginUname(),
            'bolNoname'     => (boolean)Tieba_Session_Socket::getNo_un(),
            'strMobile'     => Tieba_Session_Socket::getMobilephone(),
            'strEmail'      => Tieba_Session_Socket::getEmail(),
        );

        return $arr_session;
    }

    private function filterComment($arr_comments, $arr_session, $arr_request)
    {
        $arr_uids       = array();
        $arr_cids       = array();
        $arr_mask_uids  = array();

        // 判断帖子是否需要过滤
        $bolIsNeedFilter = Molib_Util_ThreadFilter::getUegAuditedFilter($arr_request['intTid'], $arr_session['intTid'], 'pc');

        foreach($arr_comments['post_infos'] as $index => $comment){
            if($comment['now_time'] < Molib_Util_ThreadFilter::$oldThreadTimeline) {
                if ($bolIsNeedFilter) {
                    unset($arr_comments['post_infos'][$index]);
                }
            }

            $int_uid = intval($comment['user_id']);
            $int_cid = intval($comment['post_id']);
            $tmp_uids[$int_uid] = $int_uid;
            $arr_uids [] = $int_uid;
            $arr_comment_infos [] = array(
                'thread_id'  => (int)$arr_request['intTid'],
                'post_id'    => (int)$arr_request['intPid'],
                'comment_id' => (int) $comment['post_id'],
            );
        }
        $arr_uids = array_unique($arr_uids);
        $arr_input = array(
            'input' => array(
                'thread_ids' => array($arr_request['intTid'], ),
                'post_ids'   => array($arr_request['intPid'], ),
                'comment_infos' => $arr_comment_infos,
            ),
        );

        $arr_output = Tieba_Service::call('post', 'getMaskInfo', $arr_input, null, null, 'post', 'php', 'utf-8');

        if( !isset($arr_output['errno']) || 0 != $arr_output['errno'] ){
            Bingo_Log::warning('call service post::getMaskInfo err, input : '. serialize($arr_input));
        }
        $arrThreadStatus = $arr_output['output']['threads_mask_status'][$arr_request['intTid']];
        $arrPostStatus = $arr_output['output']['posts_mask_status'][$arr_request['intPid']];
        $arrCommentStatus = $arr_output['output']['subpost_mask_status'];

        if($arrThreadStatus['is_key_deleted'] || $arrThreadStatus['is_key_mask'] || $arrPostStatus['is_key_deleted'] || $arrPostStatus['is_key_mask']){
            Bingo_Log::warning('tid or pid deleted or masked tid=[' . $arr_request['intTid'].'] pid=['.$arr_request['intPid'].']');
            return array();
        }

        // zengguowang add 2019-01-03 添加验证 "主题被主客态后，其回复贴除对主题发帖人可见外，对其余用户不可见" start
        // 获取帖子是否设置主态
        Bingo_Log::warning('call '.__METHOD__.' Beginning of the host and the guest .'.time());
        $thread_id = (int)$arr_request['intTid'];
        $post_id   = (int)$arr_request['intPid'];
        $maskThreadInput = array(
            "forum_id"   => 0, //吧id
            "thread_ids" => array(
                $thread_id
            )
        );
        $maskThreadOutput = Tieba_Service::call('post', 'getThreadMaskInfo', $maskThreadInput, null, null, 'post', 'php', 'utf-8');
        Bingo_Log::warning('call '.__METHOD__.' post::getThreadMaskInfo, input='.serialize($maskThreadInput).', res='.serialize($maskThreadOutput));
        if (!isset($maskThreadOutput['errno']) || $maskThreadOutput['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($maskThreadOutput['output']['thread_info'][0]['is_partial_visible']))
        {
            // 接口正确返回，且有此帖子信息，且 is_partial_visible = 1，表示已经是主态，其他情况（包括报错）都认为没被设为主态
            Bingo_Log::warning('call '.__METHOD__.' post::getThreadMaskInfo, Interface request failed, input='.serialize($maskThreadInput).', res='.serialize($maskThreadOutput));
        }

        // 获取回复详细信息
        $postInput = array(
            "post_ids" => array(
                0 => $post_id
            )
        );
        $postOutput = Tieba_Service::call('post', 'getPostInfo', $postInput, NULL, NULL, 'post', 'php', 'utf-8');
        Bingo_Log::warning('call '.__METHOD__.' post::getPostInfo, input='.serialize($postInput).', res='.serialize($postOutput));
        if(!isset($postOutput['errno']) || Tieba_Errcode::ERR_SUCCESS != $postOutput['errno'] || !isset($postOutput['output']['0']['user_id']))
        {
            Bingo_Log::warning('call '.__METHOD__.' post::getPostInfo The calling interface failed, or the parameter is empty, output ['.serialize($postOutput).']');
        }

        // 验证主题回复是否一致
        if(isset($postOutput['output']['0']['thread_id']) && intval($postOutput['output']['0']['thread_id']) != $thread_id)
        {
            Bingo_Log::warning('call '.__METHOD__.' Inconsistent responses to topic posts');
            return array();
        }

        // 设置了主客态，验证当前用户是否是发帖用户(不管是主题还是回复设置了主客态都需要查询出发主题帖的人)
        $threadPartialVisible = isset($maskThreadOutput['output']['thread_info'][0]['is_partial_visible']) ?
            intval($maskThreadOutput['output']['thread_info'][0]['is_partial_visible']) : 0;
        $postPartialVisible = isset($arr_output['output']['posts_mask_status'][$post_id]['is_key_visible']) ?
            intval($arr_output['output']['posts_mask_status'][$post_id]['is_key_visible']) : 0;
        Bingo_Log::warning('call '.__METHOD__.' Thread setting host and guest state ['.$threadPartialVisible.'] post setting host and guest state ['.$postPartialVisible.']');

        // 都设置主客态直接退出
        if(1 == $threadPartialVisible && 1 == $postPartialVisible)
        {
            Bingo_Log::warning('call '.__METHOD__.' Thread and post host and guest state，exit directly');
            return array();
        }

        if(1 == $threadPartialVisible)
        {
            Bingo_Log::warning('call '.__METHOD__.' The reply currently does set the host and guest state, and passed the verification.');

            // 获取thread的user_id等相关信息
            $threadInput = array(
                "thread_ids"      => array(
                    0 => intval($thread_id)
                ),
                "need_abstract"   => 1,
                "forum_id"        => 0,
                "need_photo_pic"  => 1,
                "need_user_data"  => 0,
                "icon_size"       => 1,
                "need_forum_name" => 1, //是否获取吧名
                "call_from"       => "pc_frs" //上游模块名
            );
            $threadOutput = Tieba_Service::call('post', 'mgetThread', $threadInput, NULL, NULL, 'post', 'php', 'utf-8');
            Bingo_Log::warning('call '.__METHOD__.' post::mgetThread input ['.serialize($threadInput).'] output ['.serialize($threadOutput).']');
            if(false === $threadOutput || Tieba_Errcode::ERR_SUCCESS != $threadOutput['errno'])
            {
                Bingo_Log::warning('call '.__METHOD__.' post::mgetThread use falta, output ['.serialize($threadOutput).']');
                return array();
            }

            // 发帖人id
            $threadUserId = isset($threadOutput['output']['thread_list'][intval($thread_id)]['user_id']) ?
                intval($threadOutput['output']['thread_list'][intval($thread_id)]['user_id']) : 0;
            Bingo_Log::warning('call '.__METHOD__.' Thread id of the topic thread user_id ['.$threadUserId.'] post user_id ['.$postOutput['output']['0']['user_id'].']');
            if($threadUserId != intval($arr_session['intUid']))
            {
                Bingo_Log::warning('call '.__METHOD__.' The thread sets the host and guest state, and the current login account is not the poster itself, directly exit');
                return array();
            }
        }

        if(1 == $postPartialVisible && intval($postOutput['output']['0']['user_id']) != intval($arr_session['intUid']))
        {

            Bingo_Log::warning('call '.__METHOD__.' Reply to set the host and guest state, verify whether the current user is the user who sent the reply');
            return array();
        }

        Bingo_Log::warning('call '.__METHOD__.' The main guest state verification passed and ended .'.time());
        // zengguowang add 2019-01-03 添加验证 "主题被主客态后，其回复贴除对主题发帖人可见外，对其余用户不可见" end

        if( !empty($arr_uids) ){
            $arr_mask_uids = Core_UserMask::getUserMask($arr_session['intUid'], $arr_comments['thread_id'], $arr_uids);
        }

        $arr_new_comment_infos = array();
        if( !empty($arr_mask_uids) ){
            foreach( $arr_comments['post_infos'] as $comment ){
                $int_uid = intval($comment['user_id']);
                if( isset($arr_mask_uids[$int_uid]) ){
                    continue;
                }
                $arr_new_comment_infos[] = $comment;
            }
        }else{
            $arr_new_comment_infos = $arr_comments['post_infos'];
        }

        $arr_comments['post_infos'] = $arr_new_comment_infos;

        return $arr_comments;
    }

    private function buildData($arr_comments, $arr_request)
    {
        $build_cid_list = array();
        $arr_posts  = $arr_comments['post_infos'];

        $arr_ret = $this->getInfoFromList($arr_posts);
        $arr_uid_list = $arr_ret['uid_list'];
        $arr_cid_list = $arr_ret['cid_list'];

        $build_cid_list = $arr_comments;
        unset($build_cid_list['post_infos']);

        $arr_param = array(
            'user_id'   => $arr_uid_list,
            'get_icon'  => 3,
        );
        $arr_user_output = Tieba_Service::call('user', 'mgetUserData', $arr_param, NULL, NULL, 'post', 'php', 'utf-8');

        if( !isset($arr_user_output['errno']) || 0 !== intval($arr_user_output['errno']) ){
            Bingo_Log::warning('call service user::mgetUserData err. input : '. serialize($arr_param));
            $arr_user_output = array();
        }else{
            $arr_user_output = $arr_user_output['user_info'];
        }

        $arr_param = array('pids' => $arr_cid_list);
        $arr_voice_output = Tieba_Service::call('voice', 'getThreadVoiceInfosByPids', $arr_param, NULL, NULL, 'post', 'php', 'utf-8');
        if( !isset($arr_voice_output['errno']) || 0 !== intval($arr_voice_output['errno']) ){
            Bingo_Log::warning('call service voice::getThreadVoiceInfosByPids err. input : '. serialize($arr_param));
            $arr_voice_output = array();
        }else{
            $arr_voice_output = $arr_voice_output['ret']['postVoiceList'];
        }
        $arrVoiceInfo = array();
        if( !empty($arr_voice_output) ){
            foreach( $arr_voice_output as $item ){
                $arrVoiceInfo[$item['post_id']] = $item;
            }
        }

        //build cid list
        $new_arr_posts = array();
        foreach($arr_posts as $key => &$comment){
            if(strpos($comment['content'],'portrait="') !== false)
                    {
                        $tmp = explode('portrait="',$comment['content']);
                        $tmp1 = explode('" target="',$tmp[1]);
                        $portrait = $tmp1[0];
                        $user_id = Tieba_Ucrypt::decode($portrait);
                        $uuinput = array("user_id" => array(0 => $user_id ));
                        $uures   = Tieba_Service::call('user', 'getUnameByUids', $uuinput, NULL, NULL, 'post', 'php', 'utf-8');
                        $comment['cite_name'] = $uures['output']['unames'][0]['user_name'];
                    }
            $int_cid = intval($comment['post_id']);
            $comment['during_time'] = $arrVoiceInfo[$int_cid]['during_time'];
            $comment['comment_id'] = $comment['post_id'];
            $comment['post_id'] = $comment['quote']['post_id'];
            unset($comment['quote']);
            $new_arr_posts[] = $comment;
        }

        $build_cid_list['total_page']   = ceil($build_cid_list['valid_post_num']/$arr_request['intPageSize']);
        $build_cid_list['cur_page']     = $arr_request['intPageno'];
        $build_cid_list['comment_num']  = $build_cid_list['valid_post_num'];
        unset($build_cid_list['valid_post_num']);
        unset($build_cid_list['total_post_num']);
        $build_cid_list['comment_info']   = $new_arr_posts;

        //【运营活动需求】屏蔽滑稽表情2天,时间:2017年2月8日12点———2017年2月10日12点, RD:chenzude PM:liling10
        $intNowTime = time();
        if (($intNowTime > Util_Const::SHIELD_FUNNY_FACE_START_TIME) && ($intNowTime <= Util_Const::SHIELD_FUNNY_FACE_END_TIME)) {
            foreach ($build_cid_list['comment_info'] as $commentKey => $commentVal) {
                $commentContent = $commentVal['content'];
                $build_cid_list['comment_info'][$commentKey]['content'] = preg_replace('/<img[^>]*class="BDE_Smiley"[^>]*(i_f25.png|image_emoticon25.png)[^>]*>/U', '#滑稽go die#', $commentContent);
            }
        }

        //build user list
        foreach( $arr_user_output as $key => $value ){
            $int_uid    = intval($value['user_id']);
            $str_uname  = strval($value['user_name']);
            $arr_user_output[$key]['portrait'] = Tieba_Ucrypt::encode($int_uid, $str_uname);
			$arr_user_output[$key]['nickname'] = (empty($value['user_nickname'])) ? $str_uname : $value['user_nickname'];
			$arr_user_output[$key]['is_private'] = isset($value['user_status']) ? $value['user_status'] : false;
			$arr_user_output[$key]['is_verify'] = isset($value['user_type']) ? (bool)($value['user_type']>0) : false;
			$arr_user_output[$key]['is_official'] = (bool)(isset($arr_user_output[$key]['is_verify']) && $arr_user_output[$key]['is_verify'] === '1');
            $arr_user_output[$key]['show_nickname'] = Molib_Util_User::getShowNickname($value);
        }

        Bingo_Page::assign('sub_post_info', $build_cid_list);
        Bingo_Page::assign('user_list', $arr_user_output);
        Bingo_Page::setTpl('lzl_tpl.php');

        return ;
    }

    private function getInfoFromList($arr_posts)
    {
        $arr_pid_list = array();
        $arr_user_list = array();

        foreach( $arr_posts as $comment ){
            $int_cid = intval($comment['post_id']);
            $int_uid = intval($comment['user_id']);
            $arr_pid_list[] = $int_cid;
            $arr_user_list[] = $int_uid;
        }

        $arr_pid_list = array_unique($arr_pid_list);
        $arr_user_list = array_unique($arr_user_list);

        $ret = array(
            'cid_list'  => $arr_pid_list,
            'uid_list'  => $arr_user_list,
        );

        return $ret;
    }
}
