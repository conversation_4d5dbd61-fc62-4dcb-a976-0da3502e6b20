<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-02-24 17:39
 * @version
 */

class Util_Request {

    private static $_arr_request = array();

    public static function getRequest()
    {
        $int_default_pagesize = intval(Util_Conf::get('default_page_size',30));
        $int_max_pagesize     = intval(Util_Conf::get('max_page_size',50));

        $arr_request = array();
        $arr_request['strHttpRefer']    = strval(Bingo_Http_Request::getServer('HTTP_REFERER', ''));
        $arr_request['strUri']          = strval(Bingo_Http_Request::getServer('REQUEST_URI', ''));
        $arr_request['intTid']          = intval(Bingo_Http_Request::getRouterParam('thread_id'));
        $arr_request['intPid']          = intval(Bingo_Http_Request::getRouterParam('post_id'));
        $arr_request['intCid']          = intval(Bingo_Http_Request::getGet('cid', 0));
        $arr_request['intPageNo']       = intval(Bingo_Http_Request::getGet('pn', 1));
        $arr_request['intPageSize']     = intval(Bingo_Http_Request::getGet('rn', $int_default_pagesize));
        $arr_request['intLzOnly']       = intval(Bingo_Http_Request::getGet('see_lz',0));
        $arr_request['strFr']           = strval(Bingo_Http_Request::getGet('fr', ''));
        $arr_request['strPstaala']      = strval(Bingo_Http_Request::getGet('pstaala', ''));
        $arr_request['intAutoJumpSkip'] = intval(Bingo_Http_Request::getCookie('wap_skipped', 0));
        $arr_request['strUserAgent']    = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';

        //处理不合规的页码数据
        if ( 0 >= $arr_request['intPageNo'] ){
            $arr_request['intPageNo'] = 1;
        }

        //处理不合规的返回数目的数据
        if ( $int_max_pagesize < $arr_request['intPageSize'] ){
            $arr_request['intPageSize'] = $int_max_pagesize;
        }else if( 0 >= $arr_request['intPageSize'] ){
            $arr_request['intPageSize'] = $int_default_pagesize;
        }

        //PC登录优化，PM luodunfeng01
        $boolisLogin = Tieba_Session_Socket::isLogin();
        if (!$boolisLogin) {
            $arr_request['intPageSize'] = 15;
        }

        if( empty($arr_request) ){
            Bingo_Log::warning('get session fail');
            return false;
        }

        self::$_arr_request = $arr_request;
        return true;
    }

    public static function get($key, $default=false)
    {
        $value = isset(self::$_arr_request[$key]) ? self::$_arr_request[$key] : $default;
        return $value;
    }
}
