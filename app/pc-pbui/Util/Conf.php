<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-08-08 16:04:33
 * @version
 */

class Util_Conf {

    private static $_conf = null;

	private static $_arrSpider = array(
        	'Sosospider',
        	'bingbot',
        	'Googlebot',
        	'Bai<PERSON><PERSON><PERSON>',
        	'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
        	'Sogou web spider',
        );

    public static function getUiConf(){
        if(self::$_conf){
            return self::$_conf ;
        }
        self::$_conf = Bd_Conf::getConf('/app/pc-pbui/ui_pbui');

        if(false === self::$_conf){
            Bingo_Log::warning("read conf fail");
            return false;
        }
        return true;
    }

    public static function get($key, $default=''){
        $ret = self::getUiConf();
        if( !empty($ret) ){
            $value = isset(self::$_conf[$key]) ? self::$_conf[$key] : $default;
            return $value;
        }else{
            return $default;
        }
	}

	public static function getAll(){
		if( !empty(self::$_conf) ){
			return self::$_conf;
		}else{
			return array();
		}
	}

	public static function checkSpider() {
		$strAgent = $_SERVER['HTTP_USER_AGENT'];
		foreach(self::$_arrSpider as $strSpider) {
			if(false != strpos($strAgent, $strSpider)) {
				return true;
			}
		}
		return false;
	}
}
