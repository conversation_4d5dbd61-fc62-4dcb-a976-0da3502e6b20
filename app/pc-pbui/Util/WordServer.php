<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-08-08 11:39:33
 * @version
 */

class Util_WordServer {

    private static $_word_server = null;

    public static function init()
    {
        if(self::$_word_server){
            return self::$_word_server ;
        }

        self::$_word_server = Wordserver_Wordlist::factory();

        if( null == self::$_word_server ){
            Bingo_Log::warning("create word list fail");
            self::$_word_server = null;
            return false;
        }
        return self::$_word_server;
    }

    public static function getWordServer()
    {
        if( empty(self::$_word_server) ){
            self::init();
        }
        return self::$_word_server;
    }
}
