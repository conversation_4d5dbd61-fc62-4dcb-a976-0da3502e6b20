<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-08-08 11:39:33
 * @version
 */

class Util_Cache {

    private static $_cache;
    private static $_cache_name = 'forum_relay_in_pb';

    public static function getUiCache(){
        if(self::$_cache){
            return self::$_cache ;
        }
        Bingo_Timer::start('cacheinit');

        self::$_cache = new Bingo_Cache_Memcached(self::$_cache_name);

        Bingo_Timer::end('cacheinit');  

        if(!self::$_cache || !self::$_cache->isEnable()){
            Bingo_Log::warning("init cache fail.");
            self::$_cache = null; 
            return null;
        }
        return self::$_cache;
    }

    public static function getCache()
    {
        if(empty(self::$_cache) ){
	  		return self::getUiCache();
		}
         return self::$_cache;
    }
}
?>
