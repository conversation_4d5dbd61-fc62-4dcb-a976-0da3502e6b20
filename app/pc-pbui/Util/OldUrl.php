<?php
/*
 * 处理资源url功能
 */

class Util_OldUrl
{
    /**
     * 解析URL
     * @param unknown_type $strImgUrl
     * @return 
     *  {
     *      false : image
     *      0     : video
     *      1     : baidu video
     *  }
     */
    public static function parseImgUrl($strImgUrl)
    {
        $strBaiduVideoDomain = Util_Conf::get('baidu_video_domain', 'http://db-testing-shipin00.db01.baidu.com:8323/export/flashplayer.swf');
        if (self::isBaiduVideo($strImgUrl, $strBaiduVideoDomain)) {
            return 1;
        }
        //判定是否是video
        $arrVideoList = self::$_vedio_list['white_list'];
        if (! empty($arrVideoList)) {
            foreach ($arrVideoList as $_strVideo) {
                if (strncasecmp($strImgUrl, $_strVideo, strlen($_strVideo)) == 0) {
                    return 0;
                }
            }
        }
        return false;
    }

    public static function getPostPerm()
    {
        if( !empty(self::$_vedio_list) )
            return self::$_vedio_list;
        return array();
    }

    protected static function isBaiduVideo($strUrl, $strDomain)
    {
        if (strncasecmp($strUrl, 'http://', strlen('http://')) !=0 ) {
            return false;
        }
        if (strlen($strUrl) < strlen($strDomain)) {
            return false;
        }
        if (strncasecmp($strUrl, $strDomain, strlen($strDomain)) !=0 ) {
            return false;
        }
        return true;
    }

    private static $_vedio_list = array(
        'img_num'    =>  10,
        'video_num'  =>  10,
        'smiley_num' =>  100,
        'white_list' =>  array(
            'http://www.tudou.com/',
            'http://v.blog.sohu.com/',
            'http://tv.sohu.com/',
            'http://share.vrs.sohu.com/',
            'http://my.tv.sohu.com/',
            'http://player.56.com/',
            'http://www.56.com/',
            'http://kankanews.com/',
            'http://video6.smgbb.cn/',
            'http://www.youku.com/',
            'http://player.youku.com/',
            'http://static.youku.com/',
            'http://www.ku6.com/',
            'http://player.ku6.com/',
            'http://video.sina.com.cn/',
            'http://vhead.blog.sina.com.cn/',
            'http://you.video.sina.com.cn/',
            'http://video.qq.com/',
            'http://www.baidu.com/',
            'http://box.baidu.com/',
            'http://hi.baidu.com/',
            'http://mv.baidu.com/',
            'http://mvimg.baidu.com/',
            'http://mvideo.baidu.com/',
            'http://player.cntv.cn/',
            'http://player.xiyou.cntv.cn/',
            'http://www.yinyuetai.com/',
            'http://player.yinyuetai.com/',
            'http://www.aipai.com/',
            'http://www.cutv.com/',
            'http://player.cutv.com/',
            'http://www.pptv.com/',
            'http://v.pptv.com/',
            'http://www.letv.com/',
            'http://www.iqiyi.com/',
            'http://yule.iqiyi.com/',
            'http://player.video.qiyi.com/',
            'http://www.ifeng.com/',
            'http://s.v.ifeng.com/',
            'http://v.ifeng.com/',
            'http://www.m1905.com/',
            'http://www.joy.cn/',
            'http://client.joy.cn/',
            'http://www.molihe.com/',
            'http://mv.molihe.com/',
            'http://swf.molihe.com/',
            'http://www.baomihua.com/',
            'http://video.baomihua.com/',
            'http://www.ouou.com/',
            'http://flash.ouou.com/',
            'http://dv.ouou.com/',
            'http://misc.home.news.cn/',
            'http://www.news.cn/',
            'http://www.wasu.cn/',
            'http://play1.wasu.cn/',
            'http://play.wasu.cn/',
            'http://v.iask.com/',
            'http://i7.imgs.letv.com/',
            'http://static.video.qq.com/',
            'http://player.pptv.com/',
            'http://www.mgtv.com/',
            'http://www.meipai.com/',
            'http://baishi.baidu.com/',
        		//增加b站的以下四个域名
        		'http://www.bilibili.com/',
        		'http://share.acg.tv/',
        		'http://static.hdslb.com/',
        		'http://bangumi.bilibili.com',
        ),
    );

}
