<?php
/***************************************************************************
 * 
 * Copyright (c) 2011 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * @file Device.php
 * <AUTHOR>
 * @date 2012-03-26 20:23:59
 * @brief 从 wisejudge 中获取手机类型信息
 *  
 **/
class Util_Device {

    // $_SERVER 中的 key => 访问 wisejudge 中的params的key值
        protected static $_arrParams = array (
        'HTTP_USER_AGENT'       => 'user_agent',
        'HTTP_ACCEPT'           => 'accept',
        'HTTP_ACCEPT_CHARSET'   => 'accept_charset',
        'HTTP_ENCODING'         => 'accept_encoding',
        'HTTP_LANGUAGE'         => 'accept_language',
    );

    protected static $_bolHasRpcCall = false;

    protected static $_isWiseDevice = false;

    protected static function _call($int_uip) {
        $_arrTmp = array();
        foreach (self::$_arrParams as $serverKey => $paramsKey) {
            $_tmpVal = Bingo_Http_Request::getServer($serverKey);
            if (!is_null ($_tmpVal)) {
                $_arrTmp[] = array (
                    'key'   => $paramsKey,
                    'value' => $_tmpVal,
                );
            }
        }
        $arrReq = array (
            'wise_input' => array (
            'http_info'  => $_arrTmp,
            'uip'        => $int_uip,
            ),
        );

        Bingo_Timer::start ('wise_judge');
        require_once 'Rpc/Wisejudge.php';
        $arrRet = Rpc_Wisejudge::getDevice($arrReq);
        Bingo_Timer::end('wise_judge');

        if ($arrRet!==false 
            && isset ($arrRet['err_no']) && $arrRet['err_no']==0
            && isset ($arrRet['is_wise_device']) && $arrRet['is_wise_device']==1) {
                self::$_isWiseDevice = true;
            }
    }

    public static function isWiseDevice( $int_uip ) {
        if (self::$_bolHasRpcCall === false) {
            self::_call($int_uip);
            self::$_bolHasRpcCall = true;
        }
        return self::$_isWiseDevice;
    }
}
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
