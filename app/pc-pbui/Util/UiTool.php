<?php
// 只看楼主小流量功能的工具类

class Util_UiTool {

    // 首贴是否是匿名
    public static function isFirstPostAn($strFirstUname) {
        $strFirstUname = (string)$strFirstUname;
        if (false===strpos($strFirstUname,'.')) {
            return false;
        }
        return true;
    }

    //title有特殊处理，有可能首贴被删除了，直接那第一个帖子会有问题。
    public static function ltrimStr($str, $strTrim = '�ظ���')
    {
        $intLen = strlen($strTrim);
        while (substr($str, 0, $intLen) == $strTrim) {
            $str = substr($str, $intLen);
        }
        return $str;
    }

    public static function geneForwardTbs($strUname, $intFid, $intTid, $strFname, $intUid)
    {
        $strKey = (string) Util_Conf::get('forward_key', 'itieba_tbs');
        $strTbs = sprintf( 
            'cmd=repaste&product_id=1&fid=%d&tid=%d&pid=0&fname=%s&login_uid=%d&un=%s&tbs_key=%s',
            $intFid, $intTid, $strFname, $intUid, $strUname,
            $strKey);
        $strTbs = substr(md5($strTbs), 0, 16);
        return $strTbs;
    }

    public static function geneItiebaTbs()
    {
        $strKey = (string) Util_Conf::get('forward_key', 'itieba_tbs');
        $strTbs = sprintf('BDUSS=%s&tbs_key=%s', Tieba_Session_Socket::getBDUSS(), $strKey);
        return substr(md5($strTbs), 0, 16);
    }

    /**
     *获取给rp(推荐)系统使用的uid的Tbs
     **/
    public static function getRpTbs ($intUid) {
        $strKey = '4f89b6ebff8341a69f6bb9c6d4e7e7a9';
        $intID  = 1;
        return fcrypt_id_2hstr($strKey, $intID, intval ($intUid));
    }

    public static function getEncodeUname($strUname)
    {
        if (Tieba_Util::isUnameIp($strUname)) {
            $intIp = Bingo_Http_Ip::ip2long($strUname);
            $strKey = (string) Conf_Config::get('fcrypt.tieba_key', 'Baidu.Vote.2007.04.12', 'tieba');
            $strUname = fcrypt_id_2hstr($strKey, $intIp, 123456);
        }
        return $strUname;
    }

    /*
     * get the version of pbui, for FE
     */
    public static function getPbVersion()
    {
        $int_version = 0;
        $word_server = Util_WordServer::getWordServer();
        if( false == $word_server ){
            return $int_version;
        }
        $table_name = 'pb_version';
        $arr_input = array( 'table' => $table_name, 'start' => 0, 'stop' => -1,);
        $ret = $word_server->getTableContents($arr_input);
        if (false === $ret || Tieba_Errcode::ERR_SUCCESS != $ret['err_no']){
            Bingo_Log::warning("get word item fails");
            return $int_version;
        }

        $int_version = intval($ret['ret']['type']);
        return $int_version;
    }

    /*
     * gene the tbs
     */
    public static function buildTbs($bol_login, $int_uid, $str_first_uname, $int_tid, $int_fid, $str_fname)
    {
        $arr_tbs = array(
            'forward'   => '',
            'follow'    => '',
            'upload_img'=> '',
            'common'    => '',
            'rp'        => '',
        );
        $arr_tbs['common']      = (string)Tieba_Tbs::gene($bol_login);
        $arr_tbs['upload_img']  = (string)Ziyuan_Upload::getTbs(1);
        if (! empty($str_first_uname)) {
            $str_first_uname    = self::getEncodeUname($str_first_uname);
        }
        $arr_tbs['forward']     = self::geneForwardTbs($str_first_uname, $int_fid, $int_tid, $str_fname, $int_uid);
        $arr_tbs['follow']      = self::geneItiebaTbs();
        $arr_tbs['rp']          = self::getRpTbs($int_uid);

        return $arr_tbs;
    }

    /*
     * judge source of the user
     */
    public static function buildUserFrom()
    {
        $str_ret = array();
        $str_fr = Util_Request::get('strFr', '');
        $str_pstaala = Util_Request::get('pstaala', '');
        $str_refer = Util_Request::get('strHttpRefer', '');

        $arr_refer_info = parse_url($str_refer);
        $arr_pstaala_page = array(1,2,3);

        if($str_fr  == 'ala0' && in_array($str_pstaala,$arr_pstaala_page))
        {
            $str_ret = 'ala0';
        }   
        elseif($arr_refer_info['host'] == 'm.baidu.com' || $arr_refer_info['host'] == 'www.baidu.com') 
        {   
            $str_ret = 'ps';
        }   
        elseif($arr_refer_info['host'] == 'tieba.baidu.com')
        {   
            $str_ret = 'tieba';
        }   
        elseif(!$str_refer)
        {   
            $str_ret = 'none';
        }
        return $str_ret;
    }
    public static function maskIP($strIP)
    {
        $intPos  = strrpos($strIP, '.');
        $strHalf = substr($strIP, 0, $intPos);
        return $strHalf.'.*';
    }   
}
