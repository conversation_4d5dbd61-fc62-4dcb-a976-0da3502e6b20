<?php

class Service_Recommend_Recommend extends Service_Libs_Base {

    const REDIS_NAME        = Util_Redis::REDIS_NAME;

    const USER_RECOMMEND    = 'user_recommend_';

    const PRODUCT_RECOMMEND = 'product_recommend_';

    const RECOMMEND_ON      = 0;

    /**
     * 用户推荐,纯redis操作
     *
     * @param $arrInput
     * @return array
     */
    public static function recommendProduct( $arrInput ) {
        $product_id = intval( $arrInput['product_id'] );
        $user_id    = intval( $arrInput['user_id'] );
        $recommend  = intval( $arrInput['recommend'] );

        if ( $product_id <= 0 || $user_id <= 0 || $recommend < 0 ) {
            Bingo_Log::warning( "params error" );

            return self::fail( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        try {
            if ( self::isRecommend( $recommend ) ) {
                $add = 1;
                if ( !self::setIfUserNotRecommend( $product_id, $user_id, $add ) ) {
                    Bingo_Log::warning( "user has recommended" );

                    return self::fail( Tieba_Errcode::ERR_USER_HAS_RECOMMENDED );
                }

                $recommendations = self::incTotalProductRecommendationAndGetCurrent( $product_id, $add );

                return self::success( compact( 'recommendations', 'add' ) );
            } elseif ( self::isUnRecommend( $recommend ) ) {
                $reduce = self::getUserRecommend( $product_id, $user_id );

                if ( $reduce != 0 ) {
                    if ( !self::deleteUserRecommend( $product_id, $user_id ) ) {
                        Bingo_Log::warning( "delete error" );

                        return self::fail( Tieba_Errcode::ERR_REDIS_CALL_FAIL );
                    }
                    $recommendations = self::decTotalProductRecommendationAndGetCurrent( $product_id, $reduce );
                }
                else {
                    $recommendations = self::getProductRecommend($product_id);
                }

                return self::success( compact( 'recommendations', 'reduce' ) );
            } else {
                Bingo_Log::warning( "recommend param error" );

                return self::fail( Tieba_Errcode::ERR_PARAM_ERROR );
            }
        } catch ( Exception $e ) {
            Bingo_Log::warning( "errno=" . $e->getCode() . " msg=" . $e->getMessage() );

            return self::fail( $e->getCode() );
        }
    }

    /**
     * 获取用户推荐数
     *
     * @param $arrInput
     * @return array
     */
    public static function getUserRecommendations( $arrInput ) {
        $product_id = intval( $arrInput['product_id'] );
        $user_id    = intval( $arrInput['user_id'] );

        if ( $product_id <= 0 || $user_id <= 0 ) {
            Bingo_Log::warning( "params error" );

            return self::fail( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        try {

            $recommendations = self::getUserRecommend( $product_id, $user_id );

            return self::success( compact( 'recommendations' ) );
        } catch ( Exception $e ) {
            Bingo_Log::warning( "errno=" . $e->getCode() . " msg=" . $e->getMessage() );

            return self::fail( $e->getCode() );
        }
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function userHasRecommend( $arrInput ) {
        $product_id = intval( $arrInput['product_id'] );
        $user_id    = intval( $arrInput['user_id'] );

        if ( $product_id <= 0 || $user_id <= 0 ) {
            Bingo_Log::warning( "params error" );

            return self::fail( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        try {

            $recommend = self::getUserRecommend( $product_id, $user_id );

            $has_recommend = $recommend > 0;

            return self::success( compact( 'has_recommend' ) );
        } catch ( Exception $e ) {
            Bingo_Log::warning( "errno=" . $e->getCode() . " msg=" . $e->getMessage() );

            return self::fail( $e->getCode() );
        }
    }

    /**
     * 获取用户推荐数
     *
     * @param $arrInput
     * @return array
     */
    public static function getProductRecommendations( $arrInput ) {
        $product_id = intval( $arrInput['product_id'] );

        if ( $product_id <= 0 ) {
            Bingo_Log::warning( "params error" );

            return self::fail( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        try {
            if(self::RECOMMEND_ON) {
                $recommendations = self::getProductRecommend( $product_id );
            }
            else {
                $recommendations = -110;
            }

            return self::success( compact( 'recommendations' ) );
        } catch ( Exception $e ) {
            Bingo_Log::warning( "errno=" . $e->getCode() . " msg=" . $e->getMessage() );

            return self::fail( $e->getCode() );
        }
    }

    /**
     * @param     $product_id
     * @param     $user_id
     * @param int $recommend
     * @return bool
     * @throws \Exception
     */
    private static function setIfUserNotRecommend( $product_id, $user_id, $recommend = 1 ) {
        $key       = self::USER_RECOMMEND . $product_id;
        $arrParams = array(
            'key'   => $key,
            'field' => $user_id,
            'value' => $recommend,
        );
        $redis_out = Util_Redis::redisQuery( self::REDIS_NAME, "HSETNX", $arrParams );

        if ( $redis_out == false || $redis_out['err_no'] != 0 ) {
            $msg = 'HSETNX user recommend fail. [key:' . $key . ']';
            //            Bingo_Log::warning( $msg  );
            throw new Exception( $msg, Tieba_Errcode::ERR_REDIS_CALL_FAIL );
        } else {
            $value = intval( $redis_out['ret'][ $key ] );

            return $value == 1; // key已经存在,表示1,则返回0
        }
    }

    /**
     * 删除用户推荐
     *
     * @param $product_id
     * @param $user_id
     * @return int
     * @throws \Exception
     */
    private static function deleteUserRecommend( $product_id, $user_id ) {
        $key       = self::USER_RECOMMEND . $product_id;
        $arrParams = array(
            'key'   => $key,
            'field' => $user_id,
        );
        $redis_out = Util_Redis::redisQuery( self::REDIS_NAME, "HDEL", $arrParams );

        if ( $redis_out == false || $redis_out['err_no'] != 0 ) {
            $msg = 'HDEL user recommend fail. [key:' . $key . ']';
            //            Bingo_Log::warning( $msg  );
            throw new Exception( $msg, Tieba_Errcode::ERR_REDIS_CALL_FAIL );
        } else {
            return intval( $redis_out['ret'][ $key ] ) == 1;// 1表示成功,0失败
        }
    }

    /**
     * @param $recommend
     * @return bool
     */
    private static function isRecommend( $recommend ) {
        return $recommend == 1;
    }

    /**
     * @param $recommend
     * @return bool
     */
    private static function isUnRecommend( $recommend ) {
        return $recommend == 0;
    }

    /**
     * @param $product_id
     * @param $recommend
     * @return int
     * @throws \Exception
     */
    private static function incTotalProductRecommendationAndGetCurrent( $product_id, $recommend ) {
        $key       = self::PRODUCT_RECOMMEND . $product_id;
        $arrParams = array(
            'key'  => $key,
            'step' => $recommend,
        );
        $redis_out = Util_Redis::redisQuery( self::REDIS_NAME, "INCRBY", $arrParams );

        if ( $redis_out == false || $redis_out['err_no'] != 0 ) {
            $msg = 'INCRBY product recommend fail. [key:' . $key . ']';
            //            Bingo_Log::warning( $msg  );
            throw new Exception( $msg, Tieba_Errcode::ERR_REDIS_CALL_FAIL );
        } else {
            $value = intval( $redis_out['ret'][ $key ] );

            return $value;
        }
    }

    /**
     * @param $product_id
     * @param $recommend
     * @return int
     * @throws \Exception
     */
    private static function decTotalProductRecommendationAndGetCurrent( $product_id, $recommend ) {
        $key       = self::PRODUCT_RECOMMEND . $product_id;
        $arrParams = array(
            'key'  => $key,
            'step' => $recommend,
        );
        $redis_out = Util_Redis::redisQuery( self::REDIS_NAME, "DECRBY", $arrParams );

        if ( $redis_out == false || $redis_out['err_no'] != 0 ) {
            $msg = 'DECRBY product recommend fail. [key:' . $key . ']';
            //            Bingo_Log::warning( $msg  );
            throw new Exception( $msg, Tieba_Errcode::ERR_REDIS_CALL_FAIL );
        } else {
            $value = intval( $redis_out['ret'][ $key ] );

            return $value;
        }
    }

    /**
     * @param $product_id
     * @param $user_id
     * @return int
     * @throws \Exception
     */
    private static function getUserRecommend( $product_id, $user_id ) {
        $key       = self::USER_RECOMMEND . $product_id;
        $arrParams = array(
            'key'   => $key,
            'field' => $user_id,
        );
        $redis_out = Util_Redis::redisQuery( self::REDIS_NAME, "HGET", $arrParams );
       
        if ( $redis_out == false || $redis_out['err_no'] != 0 ) {
            $msg = 'HGET user recommend fail. [key:' . $key . ']';
            //            Bingo_Log::warning( $msg  );
            throw new Exception( $msg, Tieba_Errcode::ERR_REDIS_CALL_FAIL );
        } else {
            $value = intval( $redis_out['ret'][ $key ] );

            return $value; // key已经存在,则返回0
        }
    }

    /**
     * @param $product_id
     * @return int
     * @throws \Exception
     */
    private static function getProductRecommend( $product_id ) {
        $key       = self::PRODUCT_RECOMMEND . $product_id;
        $arrParams = array(
            'key'   => $key,
            'field' => $product_id,
        );
        $redis_out = Util_Redis::redisQuery( self::REDIS_NAME, "GET", $arrParams );
        if ( $redis_out == false || $redis_out['err_no'] != 0 ) {
            $msg = 'GET product recommend fail. [key:' . $key . ']';
            //            Bingo_Log::warning( $msg  );
            throw new Exception( $msg, Tieba_Errcode::ERR_REDIS_CALL_FAIL );
        } else {
            $value = intval( $redis_out['ret'][ $key ] );

            return $value; // key已经存在,则返回0
        }
    }
}