<?php

interface Libs_Repository_Contracts_RepositoryInterface {

    /**
     * @param array $columns
     * @return mixed
     */
    //public function all($columns = array('*'));

    /**
     * @param int   $perPage
     * @param array $columns
     * @return mixed
     */
    //public function paginate($perPage = 15, $columns = array('*'));

    /**
     * @param array $data
     * @return mixed
     */
    public function create(array $data);

    /**
     * @param array $data
     * @param       $id
     * @return mixed
     */
    public function update(array $data, $id);

    /**
     * @param $id
     * @return mixed
     */
    //public function delete($id);

    /**
     * @param       $id
     * @param array $columns
     * @return mixed
     */
    public function find($id, $columns = null);

    /**
     * @param       $condition
     * @param null  $columns
     * @return mixed
     */
    public function findBy($condition, $columns = null);
}