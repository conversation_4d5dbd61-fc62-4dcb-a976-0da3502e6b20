<?php

class Model_Ansmsg_AnsMsgRepository extends Model_Base_BaseRepository {


    static $msgTypes = [
        Model_Ansmsg_AnsMsg::MSG_TYPE => Model_Ansmsg_AnsMsg::class,
        Model_Ansmsg_EventMsg::MSG_TYPE => Model_Ansmsg_EventMsg::class,
        Model_Ansmsg_CrontabMsg::MSG_TYPE => Model_Ansmsg_CrontabMsg::class,
    ];

    /**
     * @type Dl_Db_AnsMsg
     */
    public        $db;

    /**
     * Model_Ansmsg_AnsMsgRepository constructor.
     *
     * @param null $db
     */
    public function __construct($db = null) {
        if(!$db) {
            $this->db = $this->makeDb();
        }
        else {
            $this->db = $db;
        }
    }

    /**
     * @return static
     */
    public static function newInstance() {
        return new static();
    }

    /**
     * @param array $data
     * @return null|Model_Ansmsg_AnsMsg
     */
    public function create( array $data ) {
        $fields = [
            'field' => $data,
        ];
        $ret = $this->db->insert($fields);
        if(Service_Libs_Base::isFail($ret)) {
            Bingo_Log::warning(__CLASS__ . ':' . __FUNCTION__ . 'error.');
            // 对于主键冲突的问题，返回正确提示,errno=1062
            return null;
        }
        $model =  self::getAnsModel($data);
        $model->setAttribute('msg_id',intval($ret['data']));
        return $model;
    }

   

    /**
     * @param       $id
     * @param array $columns
     * @return Model_Ansmsg_AnsMsg
     */
    public function find( $id, $columns = null ) {
        if(is_null($columns)) {
            $columns = Dl_Db_AnsMsg::$fields;
        }
        $query = [
            'field' => $columns,
            'cond'  => [
                'msg_id' => $id,
            ]
        ];
        $ret = $this->db->select($query);
        if(Service_Libs_Base::isFail($ret)) {
            return null;
        }
        $data = $ret['data'];
        if(empty($data)) {
            return null;
        }
        return self::getAnsModel($data[0]);
    }
    
    /**
     * 根据属性值创建ans model,是一个工厂方法
     * @param $attributes
     * @return null
     */
    public static function getAnsModel( $attributes ) {
        if(isset($attributes['msg_type'])) {
            $msg_type = $attributes['msg_type'];
            $className = isset(self::$msgTypes[$msg_type]) ? self::$msgTypes[$msg_type] : '';
            if($className && class_exists($className)) {
                return new $className($attributes);
            }
            else {
                return null;
            }
        }
        else {
            return null;
        }
    }

    /**
     * @return array
     */
    public function getFields() {
        return Dl_Db_AnsMsg::$fields;
    }

    /**
     * overwrite
     */
    public function makeDb() {
        return new Dl_Db_AnsMsg();
    }
}