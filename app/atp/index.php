<?php
//定义全局变量
define('BINGO_ENCODE_LANG', 'UTF-8');
define ('MODULE_NAME', 'atp');
define('FOR_TEST', 'Off');
define ('MODULE_ORP_BASE_PATH', dirname(__FILE__)."/../..");
define ('MODULE_UI_BASE_PATH',  MODULE_ORP_BASE_PATH.'/app/'.MODULE_NAME);
define ('MODULE_LOG_BASE_PATH', MODULE_ORP_BASE_PATH.'/log/'.MODULE_NAME);
define ('MODULE_CONF_BASE_PATH',    MODULE_ORP_BASE_PATH.'/conf/app/'.MODULE_NAME);
define ('MODULE_ACTION_PATH',   MODULE_UI_BASE_PATH .'/actions');
define ('MODULE_SERVICE_PATH',  MODULE_UI_BASE_PATH .'/service');
define ('MODULE_DATA_PATH', MODULE_ORP_BASE_PATH . '/data/app/'.MODULE_NAME);
define ('MODULE_LIBS_PATH', MODULE_UI_BASE_PATH . '/libs');
define ('MODULE_TOOLS_PATH',    MODULE_UI_BASE_PATH . '/tools');
define ('MODULE_UTIL_PATH',  MODULE_UI_BASE_PATH . '/util');
define ('MODULE_VIEW_PATH', MODULE_ORP_BASE_PATH . '/template/automation');
define('QA_PLATFORM_HOST',  'http://tc-testing-all-forum26-vm.epc.baidu.com:8206');

//设置全局加载的目录
set_include_path(get_include_path() . PATH_SEPARATOR. realpath(MODULE_LIBS_PATH) . PATH_SEPARATOR . realpath(MODULE_UTIL_PATH));

//设置自动加载
function __autoload($strClassName)
{
	require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');

//设置默认时间
date_default_timezone_set('Asia/Chongqing');

//设置路由
$objHttpRouter= new Bingo_Http_Router_Pathinfo();
$objHttpRouter->getHttpRouter();
$arrPath = explode('/', Bingo_Http_Request::getDispatchRouter());
$strSubMoudle = isset($arrPath[1])?$arrPath[1]:'atp';
$arrRouter = array();
if (isset($arrRouter[$strSubMoudle])){
	$strSubMoudle = $arrRouter[$strSubMoudle];
}

//初始化模块
Tieba_Init::init("atp", "automation");

//Bingo的分发设置
$objFrontController = Bingo_Controller_Front::getInstance(array(
    "actionDir" => MODULE_ACTION_PATH,
    "defaultRouter" => "index",
    "notFoundRouter" => "error",
    "beginRouterIndex" => 1
));

Bingo_Timer::start('total');
Bingo_Page::init(array(
    "baseDir" => MODULE_VIEW_PATH,
    "debug" => false,
    "outputType" => ".",
    "isXssSafe" => true,
    "module" => "atp",
    "useTbView" => true,
    //"viewRootpath" => MODULE_VIEW_PATH . "/../",
));

try{
    $objFrontController->dispatch();
}catch(Exception $e){
    //出错处理，直接转向到错误页面
    Bingo_Log::warning(sprintf('main process failure!HttpRouter=%s,error[%s]file[%s]line[%s]',
        Bingo_Http_Request::getStrHttpRouter(), $e->getMessage(), $e->getFile(), $e->getLine()));
    Bingo_Page::setErrno($e->getCode());
    header('location:http://tc-testing-all-forum26-vm.epc.baidu.com:8090/tb/error.html');
}

//渲染模板
Bingo_Timer::start('build_page');
Bingo_Page::buildPage();
Bingo_Timer::end('build_page');
Bingo_Timer::end('total');
$strTimeLog = Bingo_Timer::toString();
Bingo_Log::pushNotice('Timer','['.$strTimeLog.']');
Bingo_Log::buildNotice();