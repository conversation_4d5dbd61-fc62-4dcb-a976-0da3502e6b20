# coding=utf-8
__author__ = 'hanlingzhi'

# ATP任务回调接口

import sys
import urllib2
import json

if __name__ == '__main__':
    assert len(sys.argv) == 4
    # 测试平台任务ID
    task_id = sys.argv[1]
    # 线上环境还是联调环境
    env = sys.argv[2]
    # 目录或者文件的连接字符串
    split_char = sys.argv[3]
    atp_online_host = "http://dev.tieba.baidu.com/"
    atp_offline_host = "http://tc-testing-all-forum26-vm.epc.baidu.com:8080/"
    if env == 'offline':
        url = '%satp/task/info?t_id=%s' % (atp_offline_host, task_id)
    else:
        url = '%satp/task/info?t_id=%s' % (atp_online_host, task_id)
    response = urllib2.urlopen(url)
    if response.getcode() != 200:
        sys.exit(1)
    response_json = json.loads(response.read())
    if response_json is None or response_json['errno'] != 0:
        sys.exit(1)
    # print response_json['data']['task_info']['t_mark'].encode('utf-8')
    path = response_json['data']['task_info']['t_path']
    if len(path) == 0:
        sys.exit(1)
    elif len(path) == 1:
        print path[0]
    else:
        print split_char.join(response_json['data']['task_info']['t_path'])