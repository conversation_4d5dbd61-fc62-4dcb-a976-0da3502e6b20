<?php
/***************************************************************************
 *
* Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
*
**************************************************************************/

/**
 * @file infoAction.php
 * <AUTHOR>
 * @date 2015/03/01 10:52:34
 * @brief TASK INFO API
 *
 **/
class infoAction extends BaseAction {

    //对外提供的公共API，需要赋值该属性为TRUE
    public $_isAPI = true;

    //获取参数
    public function _getPrivateInfo() {
        $this->_inputParamArray['t_id'] = intval(Bingo_Http_Request::get('t_id',0));   //任务ID
    }

    //参数校验和重组
    public function _checkPrivate(){
        if(!$this->_inputParamArray || !is_array($this->_inputParamArray)) return false;
        if($this->_inputParamArray['t_uname'] == '' && isset($this->_user_info['user_name'])) {
            $this->_inputParamArray['t_uname'] = $this->_user_info['user_name'];
        }
        foreach($this->_inputParamArray as $key => $value) {
            if( $key == 't_id') {
                if( $value <= 0 ) {
                    BaseLog::warning( get_class($this) . " $key is invalid! $value");
                    return false;
                }
            }
        }
        return true;
    }

    //主要业务逻辑
    public function _execute() {
        $arrInput = $this->_inputParamArray;
        //调用服务获取任务信息
        $arrOut = BaseService::innerCall('atp', 'getTaskInfoByTaskId', $arrInput);
        if($arrOut == false || $arrOut['errno'] !=0 ){
            BaseLog::warning( get_class($this) . ' call atp:getTaskInfoByTaskId service fail! ' . serialize($arrOut));
            $this->_intErrorNo = isset($arrOut['errno']) ? $arrOut['errno'] : BaseError::ERR_SERVICE_CALL_FAIL;
            return false;
        }
        //更新任务的状态为开始
        if($arrOut['res'][0]['t_status'] >= ConstParam::TASK_STATUS_START) {
            return $arrOut;
        }
        $arrInput['t_status'] = ConstParam::TASK_STATUS_START;
        $arrInput['t_mark'] = '框架调取信息状态变更为开始';
        $arrOutput = BaseService::innerCall('atp', 'setStatusByTaskId', $arrInput);
        if($arrOutput == false || $arrOutput['errno'] !=0 ){
            BaseLog::warning( get_class($this) . ' call atp:setStatusByTaskId service fail! ' . serialize($arrOutput));
            $this->_intErrorNo = isset($arrOutput['errno']) ? $arrOutput['errno'] : BaseError::ERR_SERVICE_CALL_FAIL;
            return false;
        }
        return $arrOut;
    }

    //构造输出
    public function _buildData($result){
        $this->_outputParamArray['task_info'] = $result['res'][0];
    }

}
