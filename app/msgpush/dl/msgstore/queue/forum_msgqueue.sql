CREATE DATABASE forum_im;
use forum_im;

CREATE TABLE `msgqueue` (
  `queue_id`    bigint(20) unsigned NOT NULL,
  `msg_id`      bigint(20) unsigned NOT NULL auto_increment,
  `insert_id`   int(11) NOT NULL,
  PRIMARY KEY  (`queue_id`,`msg_id`),
  UNIQUE INDEX `insert_id` (`queue_id`,`insert_id`),
  KEY  `msg_id`(`msg_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `qname` (
  `queue_id`    bigint(20) unsigned NOT NULL auto_increment,
  `part_id`     bigint(20) unsigned NOT NULL,
  `qname`       varchar(128) NOT NULL,
  PRIMARY KEY  (`queue_id`),
  UNIQUE INDEX `qname` (`part_id`,`qname`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--pushQueueMsgId
insert into msgqueue(queue_id,insert_id) values(0,0);
select count(*) from msgqueue where queue_id=0 ;
select msg_id from msgqueue where queue_id=0 order by msg_id desc limit 0,200;
select msg_id from msgqueue where queue_id=0 and insert_id=0;
delete from msgqueue where queue_id=0 and msg_id<0;

--getQueueLastid
select msg_id from msgqueue where queue_id=0 order by msg_id asc limit 1;

--getQueueRangeMsg
select msg_id from msgqueue where queue_id=0 order by msg_id desc limit 0,200;
