//@description: msglogic,消息队列存储
//@author: <EMAIL>
#ifndef __MSGQUEUE_IDL__
#define __MSGQUEUE_IDL__

struct getQueueRangeMsgParam 
{
    string      qname;          //消息队列唯一标识名称
    uint64_t    last_msg_id;    //消息id
    uint64_t    count;          //拉消息最大长度
};

struct getQueueRangeMsgOutput
{    
    uint64_t msg_id[];         //消息id
};

service msgqueue
{    
    /**
     * @brief :  对一个消息队列新增消息
     * @param [in]    qname           : string      :   消息队列唯一标识名称
     * @param [in]    insert_id       : uint64_t      :   插入标识id
     * @param [out]   msg_id          : uint64_t        :   新插入的消息id
     * @example 
     **/
    void pushQueueMsg(string qname,uint64_t insert_id, out uint64_t msg_id);

    /**
     * @brief :  批量查询消息列表
     * @param [in]    reqs[]          : getQueueRangeMsgParam  :   参数
     * @param [out]   result[]        : getQueueRangeMsgOutput :   返回值，数组key是qname
     * @example 
     **/
    void getQueueRangeMsg(getQueueRangeMsgParam reqs[], out getQueueRangeMsgOutput result[]);

    /**
     * @brief :  批量查询最新消息id
     * @param [in]    qnames         : string               :   消息队列唯一标识名称
     * @param [out]   last_msg_id[]  : uint64_t  			:   返回值，数组key是qname
     * @example 
     **/
    void getQueueLastid(string qnames[], out uint64_t last_msg_id[]);
    
    /**
     * @brief :  对消息list进行截断
     * @param [in]    qname         : string               :   消息队列唯一标识名称
     * @example 
     **/
    void truncateQueueByLength(string qname);
};

#endif

