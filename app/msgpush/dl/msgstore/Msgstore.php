<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013/11/18 16:21:00
 * @version 1.0
 * @structs & methods(copied from idl.)
 */


define("MODULE","msgstore_dl");
class Dl_Msgstore_Msgstore { 
    const SERVICE_NAME          = "Dl_Msgstore_Msgstore";
    const CONFIG_PATH           = "/app/msgpush/msgstore/dl_msgstore_msgstore";
    const MSG_ID_CACHE_PREFIX   = 'dl_msgpush_ms_';
    const JOBMSG_CACHE_PREFIX   = 'dl_msgpush_jobms_';
    const MSG_ID_CACHE_TIME     = 86400;
    const MSG_ID_CACHE_NUM      = 10000;
    protected static $_conf     = null;
    const QUEUE_DELIMITER       = ':';
    const DEFAULT_APPID         = 1;
    const DEFAULT_MIN_MSGID     = 1;
    
    const MSG_INFO_CACHE_PREFIX   = 'dl_msgpush_msinfo_';
    const MSG_INFO_CACHE_NUM      = 100;
    const MSG_INFO_CACHE_TIME   = 600;
    // max msg num in cache
    const MAX_MSG_CACHE_NUM = 20;


    private static function _init() {
        if (self::$_conf == null) {    
            self::$_conf = Bd_Conf::getConf(self::CONFIG_PATH);
            if (self::$_conf == false) {
                Lib_Util_Log::warning("init get conf fail.");
                return false;
            }
        }
        return true; 
    }

    private static function _errRet($errno) {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }


    private static function _getPartId($qname,$partId) {
        $partId = intval($partId);

        if ($partId <= 0) {
            $partId = crc32($qname);
        }

        return $partId;
    }

    private static function _getMsgFromCache($arrMsgIds) {
        if (empty($arrMsgIds)) {
            return array();
        }

        $cacheObj  = Lib_Util_Cache::getInstance();
        if ($cacheObj === false) {
            Lib_Util_Log::warning('cache getInstance fail!');
            return array();
        }

        $arrOutPut = array();
        $leftMsgIds = $arrMsgIds;
        while (count($leftMsgIds) > 0) {
            $key = array_splice($leftMsgIds, 0, self::MSG_ID_CACHE_NUM);
            $retData  = $cacheObj->multiget($key,self::MSG_ID_CACHE_PREFIX);
            if($retData === false) {
                Lib_Util_Log::warning('get cache fail!'.serialize($key));
                continue;
            }

            foreach($retData as $data) {
                if (!isset($data['msg_id'])) {
                    continue;
                }
                $arrOutPut[$data['msg_id']] = $data;
            }
        }
        return $arrOutPut;
    }

    private static function _clearMsgFromCache($arrMsgIds) {
        if (empty($arrMsgIds)) {
            return false;
        }
        $cacheObj  = Lib_Util_Cache::getInstance();
        if ($cacheObj === false) {
            Lib_Util_Log::warning('cache getInstance fail when _clearMsgFromCache!'.serialize($arrMsgIds));
            return false;
        }

        // TODO 后续添加批量删除消息支持
        foreach($arrMsgIds as $msg_id) {
            $retData   = $cacheObj->remove($msg_id,self::MSG_ID_CACHE_PREFIX);
            if ($retData === false) {
                Lib_Util_Log::warning('del cache fail!'.serialize($msg_id));
                continue;
            }
        }
        return true;
    }


    /**
     * @brief  消息队列存储接口
     * @arrInput:
     *     string   qname
     *     uint64_t msg_id
     *     uint64_t user_id
     *     string   content
     *     uint32_t duration
     *     uint32_t msg_type
     *     uint32_t create_time
     *     string   msg_tag
     * @return: $arrOutput
     *     
     **/
    public static function pushQueueMsg($arrInput) {
        if (!isset($arrInput['qname']) || !isset($arrInput['msg_id']) || !isset($arrInput['user_id']) ||  
            !isset($arrInput['content']) || !isset($arrInput['duration'])|| !isset($arrInput['msg_type']) || 
            !isset($arrInput['create_time']))
        {
            Lib_Util_Log::warning("pushQueueMsg input params invalid. [". serialize($arrInput) ."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (!self::_init()) {
            Lib_Util_Log::warning("pushQueueMsg getConf failed. [". self::CONFIG_PATH. "]");
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $qname       = strval($arrInput['qname']);
        $msgId       = intval($arrInput['msg_id']);
        $userId      = intval($arrInput['user_id']);
        $content     = strval($arrInput['content']);
        $duration    = intval($arrInput['duration']);
        $msgType     = intval($arrInput['msg_type']);
        $createTime  = intval($arrInput['create_time']);
		$msgTag		 = strval($arrInput['msg_tag']);
		$opFlag		 = strval($arrInput['op_flag']);
        $partId      = 0; 
        $seqId       = 0; 
        if (isset($arrInput['part_id']) && $arrInput['part_id'] > 0) {
            $partId = intval($arrInput['part_id']); //兼容IM消息是根据group_id来分片的情况
        } else {
            $partId = crc32($qname);
        }
        if (isset($arrInput['seq_id'])) {
            $seqId = intval($arrInput['seq_id']);
        }

        //写数据到di
        Dl_Msgstore_Di_Msgstore::startTransaction();
        $arrReq = array(
            'method'     => 'update',
            'function'   => 'insertMsg',
            'msg_id'     => $msgId,
            'part_id'    => $partId,
            'user_id'    => $userId,
            'content'    => $content,
            'duration'   => $duration,
            'msg_type'   => $msgType,
            'create_time'=> $createTime,
			'msg_tag'	 => $msgTag,
            'qname'      => $qname,
            'seq_id'     => $seqId,
		    'op_flag'    => $opFlag,
        );
        $arrRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
        if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::warning('Dl_Msgstore_Di_Msgstore::query(insertMsg) failed'.serialize($arrReq) );
            return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
        }

        //di写入cache
        $cacheObj  = Lib_Util_Cache::getInstance();
        if ($cacheObj === false) {
            Lib_Util_Log::warning('cache getInstance fail!'.serialize($key));
        } else {
            $key       = $msgId.'_'.$qnme;
            $cacheValue = array(
                'msg_id'     => $msgId,
                'part_id'    => $partId,
                'user_id'    => $userId,
                'content'    => $content,
                'duration'   => $duration,
                'msg_type'   => $msgType,
                'create_time'=> $createTime,
				'msg_tag'	 => $msgTag,
                'status'     => 0,
                'qname'      => $qname,
                'seq_id'     => $seqId,
		        'op_flag'    => $opFlag,
            );
            $cacheTime  = self::MSG_ID_CACHE_TIME;
            $cacheRet = $cacheObj->add($key,$cacheValue,$cacheTime,self::MSG_ID_CACHE_PREFIX);
            if ($cacheRet === false) {
                Lib_Util_Log::warning('cache add fail!'.serialize($cacheValue));
            }
            Lib_Util_Log::debug('msgstore_dl cache add req:'.serialize($cacheValue).'   response_ret:'. $cacheRet);
        }

        //写数据到logic
        $arrReq = array(
            'qname'        => $qname,
            'msg_id'       => $msgId,
            'create_time'  => $createTime,
        );
        $arrRes = Dl_Msgstore_Logic_Msgstore::pushQueueMsgId($arrReq);
        if (!isset($arrRes['errno']) || $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::warning('Dl_Msgstore_Logic_Msgstore::pushQueueMsgId failed'.serialize($arrReq));
            Dl_Msgstore_Di_Msgstore::rollback();
            return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
        }

        Dl_Msgstore_Di_Msgstore::commit();
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'    => $error,
            'errmsg'   => Tieba_Error::getErrmsg($error),
            'result'   => array(
                'qname'         => $qname,
                'msg_id'        => $msgId,
                'create_time'   => $createTime,
            ),
        );
        return $arrOutput;
    }

    /**
     * @brief  消息队列存储接口
     * @arrInput:
     *     string   qname
     *     uint64_t msg_id
     *     uint64_t user_id
     *     string   content
     *     uint32_t duration
     *     uint32_t msg_type
     *     uint32_t create_time
     *     string   msg_tag
     * @return: $arrOutput
     *     
     **/
    public static function storeMsgBody($arrInput) {
        if (!isset($arrInput['qname']) || !isset($arrInput['msg_id']) || !isset($arrInput['user_id']) ||  
            !isset($arrInput['content']) || !isset($arrInput['duration'])|| !isset($arrInput['msg_type']) || 
            !isset($arrInput['create_time']))
        {
            Lib_Util_Log::warning("storeMsg input params invalid. [". serialize($arrInput) ."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (!self::_init()) {
            Lib_Util_Log::warning("storeMsg getConf failed. [". self::CONFIG_PATH. "]");
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $qname       = strval($arrInput['qname']);
        $qid         = strval($arrInput['qid']);
        $msgId       = intval($arrInput['msg_id']);
        $userId      = intval($arrInput['user_id']);
        $content     = strval($arrInput['content']);
        $duration    = intval($arrInput['duration']);
        $msgType     = intval($arrInput['msg_type']);
        $createTime  = intval($arrInput['create_time']);
        $msgTag      = strval($arrInput['msg_tag']);
        $opFlag		 = strval($arrInput['op_flag']);
        $targetCuid	 = strval($arrInput['target_cuid']);//online debug target cuid
        $ignoreDb    = intval($arrInput['ignore_db']); 
        $partId      = 0; 
        $seqId       = 0; 
        if (isset($arrInput['part_id']) && $arrInput['part_id'] > 0) {
            $partId = intval($arrInput['part_id']); //兼容IM消息是根据group_id来分片的情况
        } else {
            $partId = crc32($qname);
        }
        if (isset($arrInput['seq_id'])) {
            $seqId = intval($arrInput['seq_id']);
        }

        //cache中是否已经存在
        $arrOldMsg = array();
        if (1 == Lib_Util_Const::UPGRADE_MSG_CHAIN) {
            $cacheObj  = Lib_Util_Cache::getInstance();
            if ($cacheObj === false) {
                Lib_Util_Log::warning('cache getInstance fail!'.serialize($key));
            } 
            else {
                $newKey = $qname;
                $cacheRet = $cacheObj->get($newKey, self::MSG_INFO_CACHE_PREFIX);
                if (!$cacheRet) {
                //    Lib_Util_Log::warning("cache get failed,".serialize($newKey));
                } else if (!empty($cacheRet)) {
                    $arrOldMsg = $cacheRet;
                    $cacheRet = $cacheObj->remove($newKey, self::MSG_INFO_CACHE_PREFIX);
                    if ($cacheRet === false) {
                        Lib_Util_Log::warning("remove msgbody to cache failed, key: ".serialize($newKey)." value: ".serialize($arrTmp));
                    }
                }
            }
        }

        //写数据到di
        Dl_Msgstore_Di_Msgstore::startTransaction();
        if ($ignoreDb != 1) {
            $arrReq = array(
                'method'     => 'update',
                'function'   => 'insertMsg',
                'msg_id'     => $msgId,
                'part_id'    => $partId,
                'user_id'    => $userId,
                'content'    => $content,
                'duration'   => $duration,
                'msg_type'   => $msgType,
                'create_time'=> $createTime,
                'msg_tag'	 => $msgTag,
                'qname'      => $qname,
                'seq_id'     => $seqId,
                'op_flag'    => $opFlag,
            );
            if( $msgType == Lib_Util_Const::MESSAGE_TYPE_NOTIFY_LOG ){
                $arrReq['attr']['cuid'] = $targetCuid;
            }
            $arrRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
            if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Lib_Util_Log::warning('Dl_Msgstore_Di_Msgstore::query(insertMsg) failed'.serialize($arrReq) );
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
            }
        } else { 
            Lib_Util_Log::debug('Dl_Msgstore_Di_Msgstore  livesysmsg ignoredb  req:'. serialize($arrInput));
        }

        //di写入cache
        $cacheObj  = Lib_Util_Cache::getInstance();
        if ($cacheObj === false) {
            Lib_Util_Log::warning('cache getInstance fail!'.serialize($key));
        } 
        else {
            $cacheValue = array(
                'msg_id'     => $msgId,
                'part_id'    => $partId,
                'user_id'    => $userId,
                'content'    => $content,
                'duration'   => $duration,
                'msg_type'   => $msgType,
                'create_time'=> $createTime,
                'msg_tag'	 => $msgTag,
                'status'     => 0,
                'qname'      => $qname,
                'seq_id'     => $seqId,
                'op_flag'    => $opFlag,
            );
            if( $msgType == Lib_Util_Const::MESSAGE_TYPE_NOTIFY_LOG ){
                $cacheValue['attr']['cuid'] = $targetCuid;
            }
            $cacheTime  = self::MSG_ID_CACHE_TIME;
            if (0 == Lib_Util_Const::UPGRADE_MSG_CHAIN) {
                $key       = $msgId.'_'.$qname;
                $cacheRet = $cacheObj->add($key,$cacheValue,$cacheTime,self::MSG_ID_CACHE_PREFIX) ;
                if ($cacheRet === false) {
                    Lib_Util_Log::warning('cache add fail!'.serialize($cacheValue));
                }
                Lib_Util_Log::debug('cache add req:'.serialize($cacheValue).'   res:'.$cacheRet );
            }
            else {
                $newKey = $qname;
                $arrTmp = array();
                $arrTmp[] = $cacheValue;
                if (!empty($arrOldMsg)) {
                    $arrTmp = array_merge($arrOldMsg, $arrTmp);
                    if (count($arrTmp) > self::MAX_MSG_CACHE_NUM) {
                        $arrTmp = array_slice($arrTmp, -(self::MAX_MSG_CACHE_NUM));
                    }
                }
                $cacheRet = $cacheObj->add($newKey, $arrTmp, $cacheTime, self::MSG_INFO_CACHE_PREFIX);
                if ($cacheRet === false) {
                    Lib_Util_Log::warning("add msgbody to cache failed, key: ".serialize($newKey)." value: ".serialize($arrTmp));
                    return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
                }
            }
        }
        Dl_Msgstore_Di_Msgstore::commit();
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'    => $error,
            'errmsg'   => Tieba_Error::getErrmsg($error),
            'result'   => array(
                'qname'         => $qname,
                'msg_id'        => $msgId,
                'create_time'   => $createTime,
            ),
        );
        return $arrOutput;
    }

    /**
     * @brief 消息拉链从db加载到redis 
     * @arrInput:
     *     string       qnames[]
     * @param array - input param
     * @return: 
     *     queueMsgid  msg_ids
     **/
    public static function loadMsgChain($arrInput) {
        if (!isset($arrInput['qnames'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        $qnames = Tieba_Service::getArrayParams($arrInput, 'qnames');
        $arrGids = array();
        foreach ($qnames as $item) {
            $arrGids[] = self::_getGid($item);
        }
        if(!self::_init()){
            Lib_Util_Log::warning("load config fail by getQueueLastid.arrInput:[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        if(empty($arrGids)) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrReq = array(
            'method'   => 'load',
            'function' => 'loadMsgChain',
            'chain_key' => implode(',', $arrGids), 
        );

        $retRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
        if (!is_array($retRes) || $retRes['errno']!==0 || !isset($retRes['results'][0])) {
            Lib_Util_Log::warning('Dl_Msgstore_Di_Msgstore::getMsg failed ' . serialize($arrReq));
            //return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrTmp = $retRes['results'][0];
        if (empty($arrTmp)) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $arrMsgChain = array();
        $arrLastIds = array();
        foreach ($arrTmp as $item) {
			$key = $item['chain_key'];
			$value = $item['chain_info'];
            if (empty($value)) {
                continue;
            }
            $arrMsgIds = explode(",", $value);
            $strKey = self::_getQname(self::DEFAULT_APPID, '', $key);
            $arrLastIds[$strKey] = $arrMsgIds[0];
            $arrMsgChain[] = array(
                'chain_key' => $strKey,
                'msg_ids' => $arrMsgIds,
            ); 
        }
        $arrReq = array(
            'msg_chain' => $arrMsgChain,
        );

        $arrRes = Dl_Msgstore_Logic_Msgstore::addMsgChain($arrReq);
        if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::warning("addMsgChain failed. [".serialize($arrReq)."]");
            if (isset($arrRes['errno'])) {
                return self::_errRet($arrRes['errno']); 
            } else {
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
            }
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'    => $error,
            'errmsg'   => Tieba_Error::getErrmsg($error),
            'result'   => $arrLastIds,
        );
        return $arrOutput;
    }
    

    /**
     * @brief 消息拉链从redis移除到db 
     * @arrInput:
     *     string       qnames[]
     * @param array - input param
     * @return: 
     *     queueMsgid  msg_ids
     **/
    public static function dumpMsgChain($arrInput) {
        if (!isset($arrInput['qnames'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        $qnames = Tieba_Service::getArrayParams($arrInput, 'qnames');

        if(!self::_init()){
            Lib_Util_Log::warning("load config fail by getQueueLastid.arrInput:[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.
        $arrReq = array(
            'qnames' => $qnames,
        );
        $arrRes = Dl_Msgstore_Logic_Msgstore::delMsgChain($arrReq);
        if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::warning("OOOOOOO delMsgChain failed. [".serialize($arrReq)."]");
            if (isset($arrRes['errno'])) {
                return self::_errRet($arrRes['errno']); 
            } else {
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
            }
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'    => $error,
            'errmsg'   => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }

    /**
     * @param input params
     * @return arrOutput
     */
    public static function delMsgChainByMsgId($arrInput) {
        if (!isset($arrInput['reqs']) || empty($arrInput['reqs'])) {
            Lib_Util_Log::warning("storeMsg input params invalid. [". serialize($arrInput) ."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!self::_init()) {
            Lib_Util_Log::warning("delMsgChainByMsgId getConf failed. [". self::CONFIG_PATH. "]");
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $reqs = Tieba_Service::getArrayParams($arrInput, 'reqs');
        $arrReq = array(
            'reqs' => $reqs,
        );
        $arrRes = Dl_Msgstore_Logic_Msgstore::delMsgChainByMsgId($arrReq);
        if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::warning("OOOOOOO delMsgChainByMsgId failed. [".serialize($arrReq)."]");
            if (isset($arrRes['errno'])) {
                return self::_errRet($arrRes['errno']);
            } else {
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
            }
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'    => $error,
            'errmsg'   => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }

    /**
    * 存储运营消息体
    * @param array - input param
    * @return array - arroutput
    */
    public static function storeJobMsgBody($arrInput) {
        if (!isset($arrInput['qname']) || !isset($arrInput['msg_id']) || !isset($arrInput['user_id']) ||  
            !isset($arrInput['content']) || !isset($arrInput['duration'])|| !isset($arrInput['msg_type']) || 
            !isset($arrInput['create_time']))
        {
            Lib_Util_Log::warning("storeMsg input params invalid. [". serialize($arrInput) ."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (!self::_init()) {
            Lib_Util_Log::warning("storeMsg getConf failed. [". self::CONFIG_PATH. "]");
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $qname       = strval($arrInput['qname']);
        $msgId       = intval($arrInput['msg_id']);
        $jobid       = intval($arrInput['jobid']);
        $userId      = intval($arrInput['user_id']);
        $content     = strval($arrInput['content']);
        $duration    = intval($arrInput['duration']);
        $msgType     = intval($arrInput['msg_type']);
        $createTime  = intval($arrInput['create_time']);
        $msgTag     = strval($arrInput['msg_tag']);
        $opFlag     = strval($arrInput['op_flag']);
        $targetCuid = serialize($arrInput['target_cuid']);//online debug target cuid
        $ignoreDb    = intval($arrInput['ignore_db']); 
        $partId      = 0; 
        $seqId       = 0; 
        if (isset($arrInput['part_id']) && $arrInput['part_id'] > 0) {
            $partId = intval($arrInput['part_id']); //兼容IM消息是根据group_id来分片的情况
        } else {
            $partId = crc32($qname);
        }
        if (isset($arrInput['seq_id'])) {
            $seqId = intval($arrInput['seq_id']);
        }

        // 查询cache
        $cacheObj  = Lib_Util_Cache::getInstance();
        if ($cacheObj === false) {
            Lib_Util_Log::warning('cache getInstance fail!'.serialize($key));
        }else{
            $key = $jobid.'_JobMsg';
            $cacheRet = $cacheObj->get($key,self::JOBMSG_CACHE_PREFIX) ;
            if ($cacheRet || !empty($cacheRet)) {
                Lib_Util_Log::debug('JobMsg exist, ignore store in db '.serialize($cacheRet));
                $error = Tieba_Errcode::ERR_SUCCESS;
                $arrOutput = array(
                    'errno'    => $error,
                    'errmsg'   => Tieba_Error::getErrmsg($error),
                    'result'   => array(
                        'qname'         => $qname,
                        'msg_id'        => $msgId,
                        'create_time'   => $createTime,
                    ),
                );
                return $arrOutput;
            }
        }

        //写数据到di
        Dl_Msgstore_Di_Msgstore::startTransaction();
        if ($ignoreDb != 1) {
            $arrReq = array(
                'method'     => 'update',
                'function'   => 'insertJobMsg',
                'jobid'      => $jobid,
                'msg_id'     => $msgId,
                'part_id'    => $partId,
                'user_id'    => $userId,
                'content'    => $content,
                'duration'   => $duration,
                'msg_type'   => $msgType,
                'create_time'=> $createTime,
                'msg_tag'    => $msgTag,
                'qname'      => $qname,
                'seq_id'     => $seqId,
                'op_flag'    => $opFlag,
            );
            if( $msgType == Lib_Util_Const::MESSAGE_TYPE_NOTIFY_LOG ){
                $arrReq['attr']['cuid'] = $targetCuid;
            }
            $arrRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
            if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Lib_Util_Log::warning('Dl_Msgstore_Di_Msgstore::query(insertMsg) failed'.serialize($arrReq) );
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
            }
        } else {
            Lib_Util_Log::debug('Dl_Msgstore_Di_Msgstore  livesysmsg ignoredb  req:'. serialize($arrInput));
        }


        //di写入cache
        //$cacheObj  = Lib_Util_Cache::getInstance();
        if ($cacheObj === false) {
            Lib_Util_Log::warning('cache getInstance fail!'.serialize($key));
        } 
        else {
            $key       = $jobid.'_JobMsg';
            $cacheValue = array(
                'jobid'     => $jobid,
                'part_id'    => $partId,
                'user_id'    => $userId,
                'content'    => $content,
                'duration'   => $duration,
                'msg_type'   => $msgType,
                'create_time'=> $createTime,
                'msg_tag'    => $msgTag,
                'status'     => 0,
                'qname'      => $qname,
                'seq_id'     => $seqId,
                'op_flag'     => $opFlag,
            );
            if( $msgType == Lib_Util_Const::MESSAGE_TYPE_NOTIFY_LOG ){
                $cacheValue['attr']['cuid'] = $targetCuid;
            }
            $cacheTime  = 259200; // cache 3 天
            $cacheRet = $cacheObj->add($key,$cacheValue,$cacheTime,self::JOBMSG_CACHE_PREFIX) ;
            if ($cacheRet === false) {
                Lib_Util_Log::warning('cache add fail!'.serialize($cacheValue));
            }
            Lib_Util_Log::debug('cache add req:'.serialize($cacheValue).'   res:'.$cacheRet );

        }
        Dl_Msgstore_Di_Msgstore::commit();
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'    => $error,
            'errmsg'   => Tieba_Error::getErrmsg($error),
            'result'   => array(
                'qname'         => $qname,
                'msg_id'        => $msgId,
                'create_time'   => $createTime,
            ),
        );
        return $arrOutput;
    }

    /**
     * @brief  获取消息队列最新消息id
     * @param:
     *     string       qnames[]
     * @return: 
     *     queueLastid  msg_ids
     **/
    public static function truncateMsgLink($arrInput) {
        if (!isset($arrInput['qnames'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        $qnames = Tieba_Service::getArrayParams($arrInput, 'qnames');
        $limit = isset($arrInput['limit']) ? $arrInput['limit'] : 50;

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        $arrRes = array();
        $arrReq = array(
            'qnames' => $qnames,
            'limit'=> $limit,
        );
        $arrRes = Dl_Msgstore_Logic_Msgstore::truncateMsgLink($arrReq);
        if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::warning("Dl_Msgstore_Logic_Msgstore::truncateMsgLink fail. [".serialize($arrRes)."]");
            if (isset($arrRes['errno'])) {
                return self::_errRet($arrRes['errno']); 
            } else {
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
            }
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'    => $error,
            'errmsg'   => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }

    /**
     * @brief  获取消息队列最新消息id
     * @arrInput:
     *     string       qnames[]
     * @return: 
     *     queueLastid  msg_ids
     **/
    public static function getQueueLastid($arrInput) {
        if (!isset($arrInput['qnames'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        $qnames = Tieba_Service::getArrayParams($arrInput, 'qnames');

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        $arrRes = array();
        $queueLastIds = array();

        $arrReq = array(
            'qnames' => $qnames,
            'user_id'=> $arrInput['user_id'],
        );
        $arrRes = Dl_Msgstore_Logic_Msgstore::getQueueLastid($arrReq);
        if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['result'])) {
            Lib_Util_Log::warning("getQueueLastid fail. [".serialize($arrRes)."]");
            if (isset($arrRes['errno'])) {
                return self::_errRet($arrRes['errno']); 
            } else {
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
            }
        }
        if (isset($arrRes['result']['lastIds'])) {
            $queueLastIds = $arrRes['result']['lastIds'];
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'    => $error,
            'errmsg'   => Tieba_Error::getErrmsg($error),
            'msg_ids' => $queueLastIds,
        );
        return $arrOutput;
    }

    /**
     * @brief  获取消息队列最新消息id
     * @param:
     *     string       qnames[]
     * @return: 
     *     queueLastid  msg_ids
     **/
    public static function getQueueLastidEx($arrInput) {
        if (!isset($arrInput['qnames'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        $qnames = Tieba_Service::getArrayParams($arrInput, 'qnames');

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        $arrRes = array();
        $queueLastIds = array();

        $arrReq = array(
            'qnames' => $qnames,
        );
        $arrRes = Dl_Msgstore_Logic_Msgstore::getQueueLastidEx($arrReq);
        if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['result'])) {
            Lib_Util_Log::warning("getQueueLastidEx fail. [".serialize($arrRes)."]");
            if (isset($arrRes['errno'])) {
                return self::_errRet($arrRes['errno']); 
            } else {
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
            }
        }
        if (isset($arrRes['result']['lastIds'])) {
            $queueLastIds = $arrRes['result']['lastIds'];
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'    => $error,
            'errmsg'   => Tieba_Error::getErrmsg($error),
            'msg_ids' => $queueLastIds,
        );
        return $arrOutput;
    }

    /**
     * @brief  获取消息队列最新消息id
     * @arrInput:
     *     string       qnames[]
     * @return: 
     *     queueLastid  msg_ids
     **/
    public static function getQueueLastidWithNeedtype($arrInput) {
        if (!isset($arrInput['qnames'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        $qnames = Tieba_Service::getArrayParams($arrInput, 'qnames');

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        $arrRes = array();
        $queueLastIds = array();

        if (0 == Lib_Util_Const::UPGRADE_MSG_CHAIN) {
            $arrReq = array(
                'qnames' => $qnames,
            );
            $arrRes = Dl_Msgstore_Logic_Msgstore::getQueueLastid($arrReq);
            if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['result'])) {
                Lib_Util_Log::warning("getQueueLastid fail. [".serialize($arrRes)."]");
                if (isset($arrRes['errno'])) {
                    return self::_errRet($arrRes['errno']); 
                } else {
                    return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
                }
            }
            if (isset($arrRes['result']['lastIds'])) {
                $queueLastIds = $arrRes['result']['lastIds'];
            }
        } else {
            $arrQnames1 = $arrQnames2 = array();
            $arrRes1 = $arrRes2 = array();
            foreach ($qnames as $qname) {
                $arrTmp = explode(':', $qname);
                if (is_array($arrTmp) && Bingo_Array::in_array($arrTmp[2], Lib_Util_Const::$GROUP_ID_KEEP_REDIS_LIST)) {
                    $arrQnames1[] = $qname;
                } else {
                    $arrQnames2[] = $qname;
                }
            }
            if (!empty($arrQnames1)) {
                $arrReq1 = array(
                    'qnames' => $arrQnames1,
                );
                $arrRes1 = Dl_Msgstore_Logic_Msgstore::getQueueLastid($arrReq1);
                if (!isset($arrRes1['errno']) || $arrRes1['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes1['result'])) {
                    Lib_Util_Log::warning("getQueueLastid fail. [".serialize($arrRes1)."]");
                    if (isset($arrRes1['errno'])) {
                        return self::_errRet($arrRes1['errno']); 
                    } else {
                        return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
                    }
                }
                if (isset($arrRes1['result']['lastIds'])) {
                    $queueLastIds = $arrRes1['result']['lastIds'];
                }
            }
            if (!empty($arrQnames2)) {
                $arrReq2 = array(
                    'qnames' => $arrQnames2,
                );
                $arrRes2 = Dl_Msgstore_Logic_Msgstore::getQueueLastidEx($arrReq2); 
                if (!isset($arrRes2['errno']) || $arrRes2['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes2['result'])) {
                    Lib_Util_Log::warning("getQueueLastid fail. [".serialize($arrRes2)."]");
                    if (isset($arrRes2['errno'])) {
                        return self::_errRet($arrRes2['errno']); 
                    } else {
                        return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
                    }
                }
                if (isset($arrRes2['result']['lastIds'])) {
                    $queueLastIds = array_merge($queueLastIds, $arrRes2['result']['lastIds']);
                }
            }
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'    => $error,
            'errmsg'   => Tieba_Error::getErrmsg($error),
            'msg_ids' => $queueLastIds,
        );
        return $arrOutput;
    }

	private static function _getCachekeyStr(&$item1, $key, $prefix){
		     $item1 = $item1.'_'.$prefix;
	}
    /**
     * @brief  消息队列批量查询接口
     * @arrInput:
     *     GetQueueRangeMsgParam reqs[]
     * @return: 
     *     Msg msg_list
     *     
     **/
    public static function getQueueRangeMsg($arrInput) {
        if (!isset($arrInput['reqs'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        $reqs = Tieba_Service::getArrayParams($arrInput, 'reqs');

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
		//直播群使用新拉链, 其它使用旧拉链
		$oldLogicReq = array();
		$newLogicReq = array();
        $arrReqInfo = array();
        foreach($reqs as $queueReqParam) {
            if (!isset($queueReqParam['qname']) || !isset($queueReqParam['last_msg_id'])) {
                Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
            }
            $qname   = $queueReqParam['qname'];
            $partId = 0;
            if (isset($queueReqParam['part_id']) && $queueReqParam['part_id'] > 0) {
                $partId = intval($queueReqParam['part_id']);
            } else {
                $partId = crc32($qname);
            }
            $arrReqInfo[$qname] = array(
                'part_id' => $partId,
            );
			if(isset($queueReqParam['new_logic']) && intval($queueReqParam['new_logic'])===1){
				$newLogicReq[] = $queueReqParam;
			}else{
				$oldLogicReq[] = $queueReqParam;
			}
        }


       //拉取双拉链信息
		$queueIdLists = array();
		if(!empty($oldLogicReq)){
			$arrReq = array(
				'reqs' => $oldLogicReq,
			);
			$arrRes = Dl_Msgstore_Logic_Msgstore::getQueueRangeMsg($arrReq);
			if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['result'])) {
				Lib_Util_Log::warning("getQueueRangeMsg fail. [".serialize($arrRes)."]");
				if (isset($arrRes['errno'])) {
					return self::_errRet($arrRes['errno']); 
				} else {
					return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
				}
			}
			if(!empty($arrRes['result'])){
				$queueIdLists = $arrRes['result'];
			}
		}
        if(!empty($newLogicReq)){
			$arrReq = array(
				'reqs' => $newLogicReq,
			);
			$arrRes = Dl_Msgstore_Queue_Msgqueue::getQueueRangeMsg($arrReq);
			if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['result'])) {
				Lib_Util_Log::warning("getQueueRangeMsg fail. [".serialize($arrRes)."]");
				if (isset($arrRes['errno'])) {
					return self::_errRet($arrRes['errno']); 
				} else {
					return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
				}
			}
			if(!empty($arrRes['result'])){
				$queueIdLists = array_merge($queueIdLists, $arrRes['result']);
			}
		}

     //从cache拉取全部msg_info
        $arrAllMsgIds = array();
        $arrNewAllMsgIds = array();
        foreach($queueIdLists as $qname => $arrMids) {
   
        $arrTmpMsgInfo = array();
         foreach($arrMids as $key => $value){
            $arrTmpMsgInfo[$key] =   $value.'_'.$qname;
         }

         $arrNewAllMsgIds = array_merge($arrNewAllMsgIds, $arrTmpMsgInfo);
         $arrAllMsgIds = array_merge($arrAllMsgIds, $arrMids);
        }
     
        Bingo_Log::pushNotice('group_all', count($arrAllMsgIds));
        $arrAllMsgInfos = self::_getMsgFromCache($arrNewAllMsgIds);
        Bingo_Log::pushNotice('group_cache', count($arrAllMsgInfos));

        // 从redis中获取推送job的msgid
        foreach ($queueIdLists as $qname => &$temparrMids) {
            foreach ($temparrMids as $key => $value) {
		    $arrMsgidAndJobid = explode("_", strval($value));
		    if (is_array($arrMsgidAndJobid) && count($arrMsgidAndJobid) > 0 ) {
                    $tempLastmsgid = $arrMsgidAndJobid[0];
                    $tempjobid = $arrMsgidAndJobid[1];
		            $temparrMids[$key] = $tempLastmsgid;
                    if (intval($tempjobid) > 0) { // 从redis 中获取消息体
		                $tempret = Dl_Msgstore_Logic_Msgstore::getJobMsgByJobid($tempjobid);
                        if (empty($tempret)) {
                            $partId = $arrReqInfo[$qname]['part_id'];
                            // get cache
                            $cacheObj  = Lib_Util_Cache::getInstance();
                            $cacheRet = array();
                            $retMsgInfos = array();
                            if ($cacheObj === false) {
                                Lib_Util_Log::warning('cache getInstance fail!');
                            }else{
                                $key       = $tempjobid.'_JobMsg';
                                $cacheRet = $cacheObj->get($key,self::JOBMSG_CACHE_PREFIX) ;
                            }
                            if (!empty($cacheRet)) {
                                Lib_Util_Log::debug('hit-cache jobmsg '.serialize($cacheRet));
                                $cacheRet['part_id'] = $partId;
                                $retMsgInfos = $cacheRet;
                            }else{
                                $arrReq = array(
                                    'method'   => 'query',
                                    'function' => 'getJobMsg',
                                    'jobids'  => $tempjobid,
                                    'part_id'  => $partId,
                                );
                                $retRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
                                if (!is_array($retRes) || $retRes['errno']!==0 || !isset($retRes['results'][0])) {
                                    Lib_Util_Log::warning('Dl_Msgstore_Di_Msgstore::getJobMsg failed ' . serialize($retRes));
                                    continue;
                                }
                                $retMsgInfos = $retRes['results'][0][0];
                                $key       = $tempjobid.'_JobMsg';
                                $cacheTime  = 259200;
                                $cacheRet = $cacheObj->add($key,$retMsgInfos,$cacheTime,self::JOBMSG_CACHE_PREFIX);
                                if ($cacheRet === false) {
                                    Lib_Util_Log::warning('jobmsg cache add fail!'.serialize($retMsgInfos));
                                }
                            }
                                
                                $retMsgInfos['msg_id'] = intval($tempLastmsgid);
                                $retMsgInfos['part_id'] = intval($$partId);
                                $arrAllMsgInfos[$tempLastmsgid] = $retMsgInfos;
                        }else{
                            $tempret['msg_id'] = intval($tempLastmsgid);
                            $arrAllMsgInfos[$tempLastmsgid] = $tempret;
                            Bingo_Log::pushNotice('getJobMsgByJobid', $tempLastmsgid);
                        }
                    }
                }
            }
        }	

	unset($arrMids);
        //待优化：sql合并为union更为合理（需要升级db2）
        foreach ($queueIdLists as $qname => $arrMids) {
            if (empty($arrMids)) {
                continue;
            }
            
            $arrUnknownMids = array();
            foreach($arrMids as $msgId){
                if (isset($arrAllMsgInfos[$msgId])) {
                    continue;
                }
                $arrUnknownMids[] = $msgId;
            }
            if (empty($arrUnknownMids)) {
                continue;
            }

            if (!isset($arrReqInfo[$qname])) {
                Lib_Util_Log::warning('Dl_Msgstore_Di_Msgstore::getMsg failed ' . $qname);
                continue;
            }

            $partId = $arrReqInfo[$qname]['part_id'];
            $arrReq = array(
                'method'   => 'query',
                'function' => 'getMsg',
                'msg_ids'  => implode(',',$arrUnknownMids),
                'part_id'  => $partId,
                'qname'    => $qname, 
            );

            $retRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
            if (!is_array($retRes) || $retRes['errno']!==0 || !isset($retRes['results'][0])) {
                Lib_Util_Log::warning('Dl_Msgstore_Di_Msgstore::getMsg failed ' . serialize($retRes));
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
            }
            $retMsgIdInfos = $retRes['results'][0];
			Bingo_Log::pushNotice('group_db', count($retMsgIdInfos));

            foreach ($retMsgIdInfos as $msgInfo) {
                if (!isset($msgInfo['msg_id'])) {
                    continue;
                }
                $arrAllMsgInfos[$msgInfo['msg_id']] = $msgInfo;
            }
        }

        //拼装数据
        $msgList = array();
        foreach ($queueIdLists as $qname => $arrMids) {
            $msgList[$qname] = array();
            foreach ($arrMids as $msgId) {
                if (!isset($arrAllMsgInfos[$msgId])) {
                    Lib_Util_Log::debug('msg id not exist!'.$msgId."|".$qname);
                    continue;
                }
                $msgList[$qname][] = $arrAllMsgInfos[$msgId];
            }
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'    => $error,
            'errmsg'   => Tieba_Error::getErrmsg($error),
            'msg_list' => $msgList,
        );
        return $arrOutput;
    }

    /**
     *
     * 获取存放在拉链结构种的消息内容
     * 消息的latest id列表和消息的实体都存放在redis中
     *
     *  根据信息类型通过不同的方式获取消息实体：
     *  1. 10，30的新存入的以及31、32、33直接从redis中获取消息体
     *  2. 以前存入的10，30走旧的获取redis的逻辑
     *
     * <AUTHOR>   <<EMAIL>>
     * @since  im2.0.1
     *         1：2015.10.21 build
     * @param  array $arrInput
     * @param  array $logicReq
     * @return array
     */
    private static function _getMsgInfoWithList($arrInput, $arrReqInfo, $logicReq)
    {
        $queueIdLists = array(); //最新消息id列表
        $allMsgInfoRes = array(
            'errno' => Tieba_Errcode::ERR_SUCCESS,
            'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
            'result' => array(),
        );

        if (!empty($logicReq)) {

            //获取qname和群类型的对应关系数组
            $qnameAndTypeMapArr = array();
            foreach ($logicReq as $groupInfo) {
                $_qnameTmp = strval($groupInfo['qname']);
                $_groupTypeTmp = intval($groupInfo['group_type']);
                $qnameAndTypeMapArr[$_qnameTmp] = $_groupTypeTmp;
                unset($_qnameTmp);
                unset($_groupTypeTmp);
            }


            //获取最新id列表
            $arrReq = array(
                'reqs' => $logicReq,
            );
            $arrRes = Dl_Msgstore_Logic_Msgstore::getQueueRangeMsg($arrReq);
            if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['result'])) {
                Lib_Util_Log::warning("getQueueRangeMsgFromRedis fail. [" . serialize($arrRes) . "]");
                if (isset($arrRes['errno'])) {
                    return self::_errRet($arrRes['errno']);
                } else {
                    return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
                }
            }
            if (!empty($arrRes['result'])) {
                $queueIdLists = $arrRes['result'];
            }

            //获取新/旧消息的消息id数组
            $oldMsgIdsArr = array(); // array( qname=> arrar(0=>id ,1 =>id ,..,) ,..., ....)
            $msgIdsArr = array(); // array( qname=> arrar() ,..., ....)
            foreach ($queueIdLists as $qname => $idArr) {
                foreach ($idArr as $val) {
                    $idStoreStr = $val;
                    $_groupTypeTmp = $qnameAndTypeMapArr[$qname];
                    if (!$_groupTypeTmp || !$idStoreStr) {
                        continue;
                    }
                    if ($_groupTypeTmp === Lib_Util_Const::GROUP_TYPE_PLATFORM || $_groupTypeTmp === Lib_Util_Const::GROUP_TYPE_USER_ACT_MSG) {
                        $isOldId = strpos($idStoreStr, '_');
                        if ($isOldId === false) {
                            $msgIdsArr[$qname][] = $idStoreStr;
                        } else {
                            $oldMsgIdsArr[$qname][] = $idStoreStr;
                        }
                    } else {
                        if ($_groupTypeTmp === Lib_Util_Const::GROUP_TYPE_ALL_USER || $_groupTypeTmp === Lib_Util_Const::GROUP_ID_IOS_USER || $_groupTypeTmp === Lib_Util_Const::GROUP_ID_ANDR_USER) {
                            $_idArrTmp = explode('_', $idStoreStr);
                            $position = intval($arrInput['user_id']) % 10  ;
                            $msgIdsArr[$qname][] = $_idArrTmp[$position];
                        } else {
                            $msgIdsArr[$qname][] = $idStoreStr;
                        }
                    }
                    unset($_groupTypeTmp);
                }
            }

            $oldMsgInfoArr = array();
            $msgInfoArr = array();
            //获取新消息的消息体
            if (!empty($msgIdsArr)) {
                foreach ($msgIdsArr as $qname => $idList) {
                    $arrMsgRes = Dl_Msgstore_Logic_Msgstore::getMultiMsg($idList);
                    if (!isset($arrMsgRes['errno']) || $arrMsgRes['errno'] != Tieba_Errcode::ERR_SUCCESS ) {
                        Lib_Util_Log::warning("get multimsg fail. [" . serialize($arrMsgRes) . "]");
                        continue;
                    }
                    foreach($arrMsgRes['result'] as $msgId => $arrMsgContent){
                        $arrTmp = $arrMsgContent;
                        $arrTmp['msg_id'] = intval($msgId);
                        if (!isset($arrMsgContent['msg_tag']) || empty($arrMsgContent['msg_tag'])) {
                            if ($arrMsgContent['user_type'] == Lib_Im_Const::MSG_USER_TYPE_FORUM) {
                                $arrTmp['msg_tag'] = "f_" . $arrMsgContent['user_id'] . ":" . $arrInput['user_id'];
                            } else {
                                $arrTmp['msg_tag'] = $arrMsgContent['user_id'] . ":" . $arrInput['user_id'];
                            }
                        }
                        $msgInfoArr[$qname][] = $arrTmp;
                        unset($arrTmp);
                    }
                }
            }

            //获取旧消息的消息体
            if (!empty($oldMsgIdsArr)) {
                $oldMsgInfoRes = self::_getMsgBody($arrInput, $arrReqInfo, $oldMsgIdsArr);
                if (!isset($oldMsgInfoRes['errno']) || $oldMsgInfoRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($oldMsgInfoRes['result'])) {
                    Lib_Util_Log::warning("_get old Msg content fail. [" . serialize($oldMsgInfoRes) . "]");
                } else {
                    $oldMsgInfoArr = empty($oldMsgInfoRes['result']) ? array() : $oldMsgInfoRes['result'];
                }
            }

            //合并消息
            if (!empty($oldMsgInfoArr)) {
                foreach ($oldMsgInfoArr as $qname => $msgList) {
                    if (isset($msgInfoArr[$qname])) {
                        $msgInfoArr[$qname] = array_merge((array)$msgInfoArr[$qname], (array)$msgList);
                    } else {
                        $msgInfoArr[$qname] = $msgList;
                    }
                }
            }
            $allMsgInfoRes['result'] = $msgInfoArr;
        }

        return $allMsgInfoRes;
    }

    /**
     *
     * 获取没有存放在拉链结构中的消息内容
     *
     * <AUTHOR>   <<EMAIL>>
     * @since  im2.0.1
     *         1：2015.10.21 build
     * @param  array $arrInput
     * @param  array $logicReq
     * @return array
     */
    private static function _getMsgInfoWithoutList($arrInput, $logicReq){
        $msgInfoRes = array(
            'errno'    => Tieba_Errcode::ERR_SUCCESS,
            'errmsg'   => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
            'result'   => array(),
        );

        if(!empty($logicReq)){//从db取消息
            $arrReq = array(
                'reqs' => $logicReq,
                'user_id' => $arrInput['user_id'],
            );
            $arrRes = self::_getRangeMsg($arrReq);//查cache，分类查不同的表，
            if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['result'])) {
                Lib_Util_Log::warning("_getRangeMsg fail. [".serialize($arrRes)."]");
                if (isset($arrRes['errno'])) {
                    return self::_errRet($arrRes['errno']);
                }
                else {
                    return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
                }
            }
            if(!empty($arrRes['result'])){
                $msgInfoRes['result'] = $arrRes['result'];
            }
        }

        return $msgInfoRes;
    }

    /**
     *
     * 获取直播的消息内容
     *
     * <AUTHOR>   <<EMAIL>>
     * @since  im2.0.1
     *         1：2015.10.21 build
     * @param  array $arrInput
     * @param  array $arrReqInfo
     * @param  array $logicReq
     * @return array
     */
    private static function _getLiveMsgInfo($arrInput, $arrReqInfo, $logicReq){
        $queueIdLists = array();  //最新消息id列表
        $msgInfoRes = array(
            'errno'    => Tieba_Errcode::ERR_SUCCESS,
            'errmsg'   => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
            'result'   => array(),
        );

        //获取直播群的最新消息id
        if(!empty($logicReq)){
            $arrReq = array(
                'reqs' => $logicReq,
            );
            $arrRes = Dl_Msgstore_Queue_Msgqueue::getQueueRangeMsg($arrReq);
            if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['result'])) {
                Lib_Util_Log::warning("getQueueRangeMsg fail. [".serialize($arrRes)."]");
                if (isset($arrRes['errno'])) {
                    return self::_errRet($arrRes['errno']);
                } else {
                    return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
                }
            }
            if(!empty($arrRes['result'])){
                $queueIdLists = $arrRes['result'];
            }
        }
        //根据拉链数据获取直播群的最新消息的消息体
        if(!empty($queueIdLists)){
            $arrRes = self::_getMsgBody($arrInput, $arrReqInfo, $queueIdLists);
            if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['result'])) {
                Lib_Util_Log::warning("_getRangeMsg fail. [".serialize($arrRes)."]");
                if (isset($arrRes['errno'])) {
                    return self::_errRet($arrRes['errno']);
                }
                else {
                    return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
                }
            }
            if(!empty($arrRes['result'])){
                $msgInfoRes['result'] = $arrRes['result'];
            }
        }

        return $msgInfoRes;
    }


  	/**
     * @brief  批量拉消息接口，redis优化后使用
     * @param:
     *     GetQueueRangeMsgParam reqs[]
     * @return: 
     *     Msg msg_list
     *     
     **/

    public static function getQueueRangeMsgEx($arrInput) {
        if (!isset($arrInput['reqs'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        $reqs = Tieba_Service::getArrayParams($arrInput, 'reqs');
        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        /**
         * 将请求数据根据类型分为三种： 直播、存放在拉链中（10、30）、不存放在拉链中 ，分别获取三种类型数据的消息信息后进行聚合
         */
        $arrAllMsgInfos      = array(); //所有最新消息的聚合数组，也是最后被返回的
        $withListLogicReq    = array(); //使用了拉链的群
        $withoutListLogicReq = array(); //没有使用拉链的群
		$newLogicReq         = array(); //直播群
        $arrReqInfo          = array(); //请求群的part_id信息

        foreach($reqs as $queueReqParam) {
            if (!isset($queueReqParam['qname']) || !isset($queueReqParam['last_msg_id'])) {
                Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
            }
            $qname = $queueReqParam['qname'];
            $partId = 0;
            if (isset($queueReqParam['part_id']) && $queueReqParam['part_id'] > 0) {
                $partId = intval($queueReqParam['part_id']);
            } 
            else {
                $partId = crc32($qname);
            }
            $arrReqInfo[$qname] = array(
                'part_id' => $partId,
            );
			if(isset($queueReqParam['new_logic']) && intval($queueReqParam['new_logic'])===1){
				$newLogicReq[] = $queueReqParam;
			}
			elseif(Bingo_Array::in_array($queueReqParam['group_type'],Lib_Util_Const::$GROUP_TYPE_KEEP_REDIS_LIST)){
				$withListLogicReq[] = $queueReqParam;
			}
			else{
			    $withoutListLogicReq[] = array_merge($queueReqParam, array('part_id' => $partId,));
			}
        }

        $msgInfoWithoutList      = array(); //没有使用拉链的群获取到的最新消息内容
        $msgInfoWithList         = array(); //使用拉链的群获取到的最新消息内容
        $liveMsgInfo             = array(); //直播群获取到的最新消息内容

        $msgInfoWithoutListRes = self::_getMsgInfoWithoutList($arrInput, $withoutListLogicReq);
        if($msgInfoWithoutListRes['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            $errorRes = $msgInfoWithoutListRes;
            return $errorRes;
        }
        $msgInfoWithoutList  = $msgInfoWithoutListRes['result'];

        $msgInfoWithListRes = self::_getMsgInfoWithList($arrInput,$arrReqInfo, $withListLogicReq);
        if($msgInfoWithListRes['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            $errorRes = $msgInfoWithListRes;
            return $errorRes;
        }
        $msgInfoWithList  = $msgInfoWithListRes['result'];

        $liveMsgInfoRes = self::_getLiveMsgInfo($arrInput, $arrReqInfo, $newLogicReq);
        if($liveMsgInfoRes['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            $errorRes = $liveMsgInfoRes;
            return $errorRes;
        }
        $liveMsgInfo   = $liveMsgInfoRes['result'];

        //合并消息
        $arrAllMsgInfos = array_merge($msgInfoWithoutList, $msgInfoWithList, $liveMsgInfo);

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'    => $error,
            'errmsg'   => Tieba_Error::getErrmsg($error),
            'msg_list' => $arrAllMsgInfos,
        );
        return $arrOutput;
    }



    /**
     * 
     * 从db中拉取消息
     * @param unknown_type $arrInput
     * @return 
     */
    private static function _getRangeMsg($arrInput){
        if (!isset($arrInput['reqs'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        $arrOut = array(
            'errno'   => Tieba_Errcode::ERR_SUCCESS,
            'errmsg'  => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
            'result'  => array(),
        );
        if (empty($arrInput['reqs'])) {
            return $arrOut;
        }
        //先查cache，没中的话，根据group_type构造不同的请求参数，查db
        $arrData = array();
        foreach($arrInput['reqs'] as $queueReqParam) {           
            $strQname  = strval($queueReqParam['qname']);
            $arrCacheKeys[] = $strQname;
        }
        $arrCacheData = self::_getMsgInfoFromCache($arrCacheKeys);
        $arrDbMsgReq1 = array();
        $arrDbMsgReq2 = array();
        $arrDbJobReq1 = array();
        $arrDbJobReq2 = array();
        foreach($arrInput['reqs'] as $queueReqParam) {
            $strQname  = $queueReqParam['qname'];
            $strCacheKey = $strQname;
            if(isset($arrCacheData[$strCacheKey])){
                $arrBegMsg = $arrCacheData[$strCacheKey][0];
                $nCnt = count($arrCacheData[$strCacheKey]);
                $arrEndMsg = $arrCacheData[$strCacheKey][$nCnt-1];
                if($queueReqParam['last_msg_id'] >= $arrBegMsg['msg_id'] &&
                $queueReqParam['last_msg_id'] < $arrEndMsg['msg_id']){
                    $intPos = 0;
                    foreach ($arrCacheData[$strCacheKey] as $arrTmp){
                        if ($arrTmp['msg_id'] > $queueReqParam['last_msg_id']){
                            break;
                        }
                        $intPos++;
                    }
                    if ($intPos < $nCnt) {
                        $arrData[$strQname] = array_slice($arrCacheData[$strCacheKey], $intPos);//todo 
                    }
                }
                else {
                    $arrDbMsgReq1[] = $queueReqParam;
                }
            }
            else{
                $arrDbMsgReq2[] = $queueReqParam;
            }
        }
        $arrDbMsgReq = array_merge($arrDbMsgReq1,$arrDbMsgReq2);
        $arrDbMsgRes = array();
        if (!empty($arrDbMsgReq)) {
            $arrRes = Dl_Msgstore_Logic_Msgstore::getMsgFromMsgInfo(array('reqs' => $arrDbMsgReq));
            if (false === $arrRes || $arrRes['errno'] != 0 || empty($arrRes['result'])){
                Lib_Util_Log::warning('Dl_Msgstore_Logic_Msgstore::getMsgFromMsgInfo fail.input:'.serialize($arrDbMsgReq).' output:'.serialize($arrRes));
            }
            $arrDbMsgRes = empty($arrRes['result']) ? array() : $arrRes['result'];
        }
        $arrDbRst = $arrDbMsgRes;
        $arrDbReq = $arrDbMsgReq;
        if (!empty($arrDbReq) && !empty($arrDbRst)){
            foreach($arrDbReq as $queueReqParam) {
                $strQname = $queueReqParam['qname'];
                $intPartId = $queueReqParam['part_id'];
                if (isset($arrDbRst[$intPartId])){
                    $arrData[$strQname] = $arrDbRst[$intPartId];
                }
            }
        }
        //add cache
        $arrNeedCache = $arrDbMsgReq2;
        if (!empty($arrNeedCache) && !empty($arrDbRst)){
            $arrCacheKeys = array();
            foreach($arrNeedCache as $queueReqParam) {
                $strQname = $queueReqParam['qname'];
                $intPartId = $queueReqParam['part_id'];
                if (isset($arrDbRst[$intPartId])){
                    $arrCacheKeys[$strQname] = array_slice($arrDbRst[$intPartId],-(self::MAX_MSG_CACHE_NUM));
                }
            }
            $cacheObj  = Lib_Util_Cache::getInstance();
            if ($cacheObj === false) {
                Lib_Util_Log::warning('cache getInstance fail when _setMsgInfoToCache!file:dl_msgstore_Msgstore.php');
            }
            elseif (!empty($arrCacheKeys)){
                $ret = $cacheObj->multiset($arrCacheKeys,self::MSG_INFO_CACHE_TIME,self::MSG_INFO_CACHE_PREFIX);
            }
        }
        $arrOut['result'] = $arrData;
        return $arrOut;
    }
    /**
     * 
     * 根据拉链数据获取消息体
     * @param unknown_type $arrInput
     * @return array
     */
    private static function  _getMsgBody($arrInput,$arrReqInfo,$queueIdLists){
        $arrAllMsgIds = array();
        $arrNewAllMsgIds = array();
        foreach($queueIdLists as $qname => $arrMids) {       
            $arrTmpMsgInfo = array();
            foreach($arrMids as $key => $value){
                $arrTmpMsgInfo[$key] =   $value.'_'.$qname;
            }
            $arrNewAllMsgIds = array_merge($arrNewAllMsgIds, $arrTmpMsgInfo);
            $arrAllMsgIds = array_merge($arrAllMsgIds, $arrMids);
        }
        
        Bingo_Log::pushNotice('group_all', count($arrAllMsgIds));
        $arrAllMsgInfos = self::_getMsgFromCache($arrNewAllMsgIds);
        Bingo_Log::pushNotice('group_cache', count($arrAllMsgInfos));
        
        // get cache
        $cacheObj  = Lib_Util_Cache::getInstance();
        
         // 从redis中获取推送job的msgid
        foreach ($queueIdLists as $qname => &$temparrMids) {
            foreach ($temparrMids as $key => $value) {
                $arrMsgidAndJobid = explode("_", strval($value));
                if (is_array($arrMsgidAndJobid) && count($arrMsgidAndJobid) > 0 ) {
                    $tempLastmsgid = $arrMsgidAndJobid[0];
                    $tempjobid = $arrMsgidAndJobid[1];
                    $temparrMids[$key] = $tempLastmsgid;
                    if (intval($tempjobid) > 0) { // 从redis 中获取消息体	                
                        $tempret = Dl_Msgstore_Logic_Msgstore::getJobMsgByJobid($tempjobid);
                        if (empty($tempret)) {
                            $partId = $arrReqInfo[$qname]['part_id'];
                            $cacheRet = array();
                            $retMsgInfos = array();
                            if ($cacheObj === false) {
                                Lib_Util_Log::warning('cache getInstance fail!');
                            }
                            else{
                                $key       = $tempjobid.'_JobMsg';
                                $cacheRet = $cacheObj->get($key,self::JOBMSG_CACHE_PREFIX) ;
                            }
                            if (!empty($cacheRet)) {
				//Lib_Util_Log::warning('_JobMsg hit-cache:1 jobmsg '.serialize($cacheRet));
                                $cacheRet['part_id'] = $partId;
                                $retMsgInfos = $cacheRet;
                            }else{
				//Lib_Util_Log::warning('_JobMsg hit-cache:0');
                                //增加校验，校验该运营消息是否为过期已经失效的消息
                            	//已经失效缓存30天，防止3天未登录用户上来拉不到消息 
                            	//如果不是则查DB，DB中查不到则为空消息，存入已经失效cache中
                            	//运行半年可以下掉此逻辑 上线时间20150926
                            	$strDirtyKey = $tempjobid.'_DirtyJobMsg';
                                $cacheRet    = $cacheObj->get($strDirtyKey,self::JOBMSG_CACHE_PREFIX) ;
                                if (!empty($cacheRet)) {
                                    	//Lib_Util_Log::warning('_DirtyJobMsg hit-cache:1 jobmsg '.serialize($cacheRet).' DirtyKey:['.$strDirtyKey.']');
                                    	continue;
                                }
                                $arrReq = array(
                                    'method'   => 'query',
                                    'function' => 'getJobMsg',
                                    'jobids'  => $tempjobid,
                                    'part_id'  => $partId,
                                );
                                $retRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
                                if (!is_array($retRes) || $retRes['errno']!==0 || !isset($retRes['results'][0]) || empty($retRes['results'][0][0])) {
                                    Lib_Util_Log::warning('Dl_Msgstore_Di_Msgstore::getJobMsg failed or filter empty data.ret:['.serialize($retRes).'] arrReq:['.serialize($arrReq).']');
                                    //查询数据成功了，但是未查到数据，说明这个是历史脏数据，数据缓存30天
                                    //运行半年可以下掉此逻辑 上线时间20150926
                                    $cacheTime  = 2592000;
                                    $cacheRet = $cacheObj->add($strDirtyKey,time(),$cacheTime,self::JOBMSG_CACHE_PREFIX);
                                    if ($cacheRet === false) {
                                        Lib_Util_Log::warning('jobmsg dirty cache add fail!key:['.$strDirtyKey.'] ret:['.serialize($cacheRet).']');
                                    }
                                    continue;
                                }
                                $retMsgInfos = $retRes['results'][0][0];
                                $key       = $tempjobid.'_JobMsg';
                                $cacheTime  = 259200;
                                $cacheRet = $cacheObj->add($key,$retMsgInfos,$cacheTime,self::JOBMSG_CACHE_PREFIX);
                                if ($cacheRet === false) {
                                    Lib_Util_Log::warning('jobmsg cache add fail!'.serialize($retMsgInfos));
                                }
                            }                               
                            $retMsgInfos['msg_id'] = intval($tempLastmsgid);
                            $retMsgInfos['part_id'] = intval($$partId);
                            $arrAllMsgInfos[$tempLastmsgid] = $retMsgInfos;
                        } else {
                            $tempret['msg_id'] = intval($tempLastmsgid);
                            $arrAllMsgInfos[$tempLastmsgid] = $tempret;
                        }
                    }
                }
            }
        }	
        unset($arrMids);
        $msgList = array(); 
        //待优化：sql合并为union更为合理（需要升级db2）
        foreach ($queueIdLists as $qname => $arrMids) {
            if (empty($arrMids)) {
                continue;
            }            
            $msgList[$qname] = array();
            $arrUnknownMids = array();
            foreach($arrMids as $msgId){
                if (isset($arrAllMsgInfos[$msgId])) {
                    $msgList[$qname][] = $arrAllMsgInfos[$msgId];
                    continue;
                }
                $arrUnknownMids[] = $msgId;
            }
            if (empty($arrUnknownMids)) {
                continue;
            }   
            if (!isset($arrReqInfo[$qname])) {
                Lib_Util_Log::warning('Dl_Msgstore_Di_Msgstore::getMsg failed ' . $qname);
                continue;
            }    
            $partId = $arrReqInfo[$qname]['part_id'];
            $arrReq = array(
                'method'   => 'query',
                'function' => 'getMsg',
                'msg_ids'  => implode(',',$arrUnknownMids),
                'part_id'  => $partId,
                'qname'    => $qname, 
            );

            $retRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
            if (!is_array($retRes) || $retRes['errno']!==0 || !isset($retRes['results'][0])) {
                Lib_Util_Log::warning('Dl_Msgstore_Di_Msgstore::getMsg failed ' . serialize($retRes));
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
            }
            $retMsgIdInfos = $retRes['results'][0];
            Bingo_Log::pushNotice('group_db', count($retMsgIdInfos));

            foreach ($retMsgIdInfos as $msgInfo) {
                if (!isset($msgInfo['msg_id'])) {
                    continue;
                }
                //$arrAllMsgInfos[$msgInfo['msg_id']] = $msgInfo;
                $msgList[$qname][] = $msgInfo;
            }
            //拼装数据   
            /*
            $msgList[$qname] = array();
            foreach ($arrMids as $msgId) {
                if (!isset($arrAllMsgInfos[$msgId])) {
                    Lib_Util_Log::debug('msg id not exist!'.$msgId."|".$qname);
                    continue;
                }
                $msgList[$qname][] = $arrAllMsgInfos[$msgId];
            }
             */
        }
        return array(
            'errno'   => Tieba_Errcode::ERR_SUCCESS,
            'errmsg'  => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
            'result'  => $msgList,
        );
    }


    /**
     * 存储运营消息体
     * @param array - input param
     * @return array - arroutput
     */
    private static function _getMsgInfoFromCache($arrQnames){
        if (empty($arrQnames)) {
            return array();
        }

        $cacheObj  = Lib_Util_Cache::getInstance();
        if ($cacheObj === false) {
            Lib_Util_Log::warning('cache getInstance fail!');
            return array();
        }

        $arrOutPut = array();
        $leftQnames = $arrQnames;
        while (count($leftQnames) > 0) {
            $key = array_splice($leftQnames, 0, self::MSG_INFO_CACHE_NUM);
            $retData  = $cacheObj->multiget($key,self::MSG_INFO_CACHE_PREFIX);
            if($retData === false) {
                Lib_Util_Log::warning('get cache fail!'.serialize($key));
                continue;
            }

            foreach($retData as $key => $data) {
                $arrKey = explode(':', $key);
                if (count($arrKey) != 5 || !isset($arrKey[2]) || !isset($arrKey[3]) || !isset($arrKey[4])) {
                    continue;
                }
                $strQname = $arrKey[2] . self::QUEUE_DELIMITER . $arrKey[3] . self::QUEUE_DELIMITER .$arrKey[4];
                $arrOutPut[$strQname] = $data;
            }
        }
        return $arrOutPut;
    }
    public static function deleteQueueMsg($arrInput) {
        if (!isset($arrInput['qname']) || !isset($arrInput['msg_ids'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        $qname      = strval($arrInput['qname']);
        $partId     = intval($arrInput['part_id']);
        $arrMsgid   = Tieba_Service::getArrayParams($arrInput, 'msg_ids');
        if (empty($arrMsgid)) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        $strMsgIds = implode(',', $arrMsgid);

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        $partId = self::_getPartId($qname,$partId);

        $arrReq = array(
            'method'     => 'update',
            'function'   => 'deleteMsg',
            'msg_ids'    => $strMsgIds,
            'part_id'    => $partId,
            'qname'      => $qname,
        );
        $arrRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
        if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::warning('Dl_Msgstore_Di_Msgstore::query(deleteMsg) failed'.serialize($arrReq) );
            return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
        }

	$arrTmpMsgid = $arrMsgid;
	array_walk($arrTmpMsgid, '_getCachekeyStr', $qname);
        if (self::_clearMsgFromCache($arrTmpMsgid) == false) {
            Lib_Util_Log::warning("deleteQueueMsg cache fail. input[".serialize($arrMsgid)."]");
        }

        //output params.
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'    => $error,
            'errmsg'   => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }

    public static function updateQueueMsgStatus($arrInput) {
        if (!isset($arrInput['qname']) || !isset($arrInput['msg_ids']) || !isset($arrInput['status'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        $qname      = strval($arrInput['qname']);
        $partId     = intval($arrInput['part_id']);
        $status     = intval($arrInput['status']);
        $arrMsgid   = Tieba_Service::getArrayParams($arrInput, 'msg_ids');
        if (empty($arrMsgid) || $status >= 10) { //有效范围0-9
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        $strMsgIds = implode(',', $arrMsgid);

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        $partId = self::_getPartId($qname,$partId);

        $arrReq = array(
            'method'     => 'update',
            'function'   => 'setMsgStatus',
            'msg_ids'    => $strMsgIds,
            'part_id'    => $partId,
            'qname'      => $qname,
            'status'     => $status,
        );
        $arrRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
        if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::warning('Dl_Msgstore_Di_Msgstore::query(deleteMsg) failed'.serialize($arrReq) );
            return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
        }

        if (self::_clearMsgFromCache($arrMsgid) == false) {
            Lib_Util_Log::warning("updateQueueMsgStatus cache fail. input[".serialize($arrMsgid)."]");
        }
        
        //output params.
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'    => $error,
            'errmsg'   => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }
    
    public static function getBeginAndEndtime($flag){
       switch($flag)
	   {
	     case 0:
			$date = date("Y-m-d");
            break;
	   
		 case 1:
			 $date = date('Y-m-d' , strtotime('-1 day'));
		    break;
		 
		 case 2:
			 $date =  date('Y-n-d', strtotime('-2 day'));
			 break;
		 
		 default:
			 break;
	   }

	   $begintime = strtotime($date." 00:00:00");
	   $endtime = strtotime($date." 23:59:59");

	   $arrTime = array(
		  'begintime' => $begintime,
		  'endtime' => $endtime,
		  );

	   return $arrTime;
	}
	
   public static function getMsgByTime($arrInput){
	   if(!isset($arrInput['reqs'])){
                 Lib_Util_Log::warning("input param invalid. [".serialize($arrInput)."]");
		 return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	 }
     
	 if(!self::_init()){
                Lib_Util_Log::warning("init fail");
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	 }
	 
	 $reqs = Tieba_Service::getArrayParams($arrInput, 'reqs');
	 
	 $arrReqInfo = array();
	 foreach($reqs as $queueReqParam){
         if(!isset($queueReqParam['qname']) | !isset($queueReqParam['offset']) || !isset($queueReqParam['count'])){
                       Lib_Util_Log::warning("input param invalid.".serialize($queueReqParam)."]");
		       return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		 }
		 
		 $partId = 0;
		 if(isset($queueReqParam['partId']) && $queueReqParam['partId'] > 0){
            $partId = intval($queueReqParam['partId']);
		 }else {
                     $partId = crc32($queueReqParam['qname']);
		 }
         
         $qname = $queueReqParam['qname'];

          $arrReqInfo[$qname] = array(
			     'part_id' => $partId,
			     'flag'   => $queueReqParam['flag'],
			     'offset' => $queueReqParam['offset'],
			     'count'  => $queueReqParam['count'],
			 );   
	   }
 	
	$arrMsgList = array();
        foreach ($arrReqInfo as $qname => $value) {
                if (empty($value)) {
                continue;
            }

     $flag = $value['flag'];
     if(($flag != 0) && ($flag != 1) && ($flag !=2)){  
	     Lib_Util_Log::warning("this method isn't support");
	     return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
   }
     $arrTime = self::getBeginAndEndtime($flag);
     if($flag == 0 || $flag == 1){
           $arrReq = array(
                    'method'   => 'query',
                    'function' => 'getMsgByTime',
                    'part_id'  => $value['part_id'],
                    'qname'    => $qname, 
	            'begintime' => $arrTime['begintime'],
		    'endtime'   => $arrTime['endtime'],
		    'offset'   => $value['offset'],
		    'count'    => $value['count'],
            );
			
	}else if($flag == 2){
           $arrReq = array(
			     'method'   => 'query',
				 'function' => 'getMsgByOtherTime',
				 'part_id'  => $value['part_id'],
				 'qname'    => $qname,
	                         'begintime' => $arrTime['begintime'],
				 'endtime'   => $arrTime['endtime'],
				 'offset'   => $value['offset'],
				 'count'    => $value['count'],
			   );
		}else{
                   Lib_Util_Log::warning("this method isn't support");
		   return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}
		
		$retRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
           if (!is_array($retRes) || $retRes['errno']!==0 || !isset($retRes['results'][0])) {
                  Lib_Util_Log::warning('Dl_Msgstore_Di_Msgstore::getMsg failed ' . serialize($retRes));

		 return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL); 
         }
	
	
          $retMsgIdInfos = $retRes['results'][0];
	  $count = 0;
	  foreach ($retMsgIdInfos as $key => $msgInfo) {
				$msgId = $msgInfo['msg_id']; 
				$msgInfo['reply_status']=  1 & $msgInfo['msg_status'];
				 unset($msgInfo['msg_status']);
                                $arrAllMsgInfos[$msgId] = $msgInfo;
			}
		   
		   $arrMsgList[$qname] = $arrAllMsgInfos;
			$arrCountInput = array(
				 'part_id' => $value['part_id'],
				 'qname'   => $qname,
				 'day'    => $value['flag'],
				);

		   $total = self::getMsgCountByTime($arrCountInput);	
		   $arrMsgList[$qname]['total'] = $total;
		   $arrAllMsgInfos = array();
     }
		
      $error = Tieba_Errcode::ERR_SUCCESS;
      $arrOutput = array(
            'errno'    => $error,
            'errmsg'   => Tieba_Error::getErrmsg($error),
            'msg_list' => $arrMsgList,
        );

      return $arrOutput;
   }
public static function getMsgCountByTime($arrInput){
  if(!isset($arrInput['part_id']) || !isset($arrInput['qname']) || !isset($arrInput['day'])){
     Lib_Util_Log::warning("input param invalid. [".serialize($arrInput)."]");
	 return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
  }

  if(($arrInput['day'] != 0) && ($arrInput['day'] != 1) && ($arrInput['day'] !=2)){  
	     Lib_Util_Log::warning("this method isn't support");
	     return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
   }
   $arrTime = self::getBeginAndEndtime($arrInput['day']);
  if($arrInput['day'] == 0 || $arrInput['day'] == 1){
      $arrReq = array(
	    'method' => 'query',
		'function'=> 'getMsgCountByTime',
		'part_id' => $arrInput['part_id'],
		'qname'  => $arrInput['qname'],
	        'begintime' => $arrTime['begintime'],
		'endtime'   => $arrTime['endtime'],
	    );
   }else if($arrInput['day'] == 2){
       $arrReq = array(
	    'method' => 'query',
		'function'=> 'getMsgCntByOtherTime',
		'part_id' => $arrInput['part_id'],
		'qname'  => $arrInput['qname'],
	        'begintime' => $arrTime['begintime'],
		'endtime'   => $arrTime['endtime'],
	   );
	}else{
           Lib_Util_Log::warning("this method isn't support");
	   return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
   }

  $arrRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
  if(!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
      Lib_Util_Log::warning("call getMsgCountByTime fail");
	  return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
  }
  
    $count = $arrRes['results'][0][0]['total'];
    return $count;
}
    public static function getQueAllMsg($arrInput) {
		if (!isset($arrInput['reqs'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
     
		$reqs = Tieba_Service::getArrayParams($arrInput, 'reqs');

        $arrReqInfo = array();
        foreach($reqs as $queueReqParam) {
            if (!isset($queueReqParam['qname']) || !isset($queueReqParam['offset'])) {
                Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
            }
          
	    $qname   = $queueReqParam['qname'];

            $partId = 0;
            if (isset($queueReqParam['partId']) && $queueReqParam['partId'] > 0) {
                $partId = intval($queueReqParam['partId']);
            } else {
                $partId = crc32($qname);
            }
		
            $arrReqInfo[$qname] = array(
                'part_id' => $partId,
				'offset' => $queueReqParam['offset'],
				'count'  => $queueReqParam['count'],
            );
        }
		
        
		$arrMsgList = array();
        foreach ($arrReqInfo as $qname => $value) {
            if (empty($value)) {
                continue;
            }

        $arrReq = array(
                'method'   => 'query',
                'function' => 'getAllMsg',
                'part_id'  => $value['part_id'],
				'offset'   => $value['offset'],
				'count'    => $value['count'],
                'qname'    => $qname, 
            );
        $retRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
        if (!is_array($retRes) || $retRes['errno']!==0 || !isset($retRes['results'][0])) {
                Lib_Util_Log::warning('Dl_Msgstore_Di_Msgstore::getMsg failed ' . serialize($retRes));
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
        }
			
      $retMsgIdInfos = $retRes['results'][0];
	  $total = 0;	  
	  foreach ($retMsgIdInfos as $key => $msgInfo) {
				$msgId = $msgInfo['msg_id']; 
				$msgInfo['reply_status']=  1 & $msgInfo['msg_status'];
				unset($msgInfo['msg_status']);
                                $arrAllMsgInfos[$msgId] = $msgInfo;
            }

	    $arrMsgList[$qname] = $arrAllMsgInfos;
	    $arrCountInput = array(
		     'part_id' => $value['part_id'],
		     'qname'  => $qname,
		   );

	    $total = self::getAllMsgCount($arrCountInput);
	    $arrMsgList[$qname]['total'] = $total;
	    $arrAllMsgInfos = array();
        }
		
      $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'    => $error,
            'errmsg'   => Tieba_Error::getErrmsg($error),
            'msg_list' => $arrMsgList,
        );

        return $arrOutput;
    }
    public static function getAllMsgCount($arrInput){
      if(!isset($arrInput['part_id']) || !isset($arrInput['qname'])){
		  Lib_Util_Log::warning("input param invalid.[".serialize($arrInput)."]");
		  return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	  }
      
      $arrReq = array(
		    'method'    =>  'query',
		    'function'  =>  'getAllMsgCount',
		    'part_id'   =>  $arrInput['part_id'],
		    'qname'     =>  $arrInput['qname'],
		  );

      $arrRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
      if(!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
      Lib_Util_Log::warning("call getAllMsgCount fail");
	 return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
      }
		

      $count = $arrRes['results'][0][0]['total'];
      return $count;
}
public static function setReplyStatus($arrInput){
	if(!isset($arrInput['msg_id']) || !isset($arrInput['msg_status'])){
          Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
          return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
     }
  
     $arrReq = array(
	     'method' => 'update',
		 'function' => 'setReplyStatus',
		 'msg_id' => intval($arrInput['msg_id']),
		 'msg_status' => intval($arrInput['msg_status']),
	  );
  
	  $arrRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
	  if(!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS){

		  return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
	  }

	  $error = Tieba_Errcode::ERR_SUCCESS;
	  $arrOutput=  array(
		    'errno'  => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
		  );

	return $arrOutput;
  
}


public static function setPmsgReplyStatus($arrInput){
	if(!isset($arrInput['msg_id']) || !isset($arrInput['msg_status'])){
          Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
          return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
     }
 
	 $partId = 0;
	 if(isset($arrInput['partId']) && $arrInput['partId'] > 0){
          $partId = intval($arrInput['partId']);
	 }else{
          $partId = crc32($arrInput['qname']);
	 }
	 
     $arrReq = array(
	     'method' => 'update',
		 'function' => 'setPmsgReplyStatus',
		 'msg_id' => intval($arrInput['msg_id']),
		 'msg_status' => intval($arrInput['msg_status']),
		 'part_id' => $partId,
		 'qname'  => $arrInput['qname'],
	  );
	
	  $arrRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
	  if(!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS){

		  return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
	  }

	  $error = Tieba_Errcode::ERR_SUCCESS;
	  $arrOutput=  array(
		    'errno'  => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
		  );

	return $arrOutput;
  
}
public static function getPmsgReplyStatus($arrInput){
   if (!isset($arrInput['msg_ids']) || !isset($arrInput['bit']) || !isset($arrInput['status']) || !isset($arrInput['partId']) || !isset($arrInput['qname'])){
       Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
       return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
   }

   $msg_ids = Tieba_Service::getArrayParams($arrInput, 'msg_ids');
   $strMsgIds = implode(',', $msg_ids);
   
   $parId = 0;
   if(isset($arrInput['partId']) && $arrInput['partId'] > 0){
       $partId = intval($arrInput['partId']);
   }else{
       $partId = crc32($arrInput['qname']);
   }
   
   $arrReq = array(
		  'method'     => 'query',
		  'function'   => 'getPmsgReplyStatus',
		  'msg_ids'    => strval($strMsgIds),
		  'qname'      => $arrInput['qname'],
		  'part_id'     => $partId,
	);

   $arrRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
   if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
        Lib_Util_Log::warning('Dl_Msgstore_Di_Msgstore::query(deleteMsg) failed'.serialize($arrReq));
	     return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL); 									}  


 $msg_status = $arrRes['results'][0];

 $arrMsgStatus = array();
 foreach($msg_status  as $key => $value){

     $msgId = $value['msg_id'];
     $msgStatus = $value['msg_status'];
     $arrMsgStatus[$msgId] = $msgStatus;
 }


 $error = Tieba_Errcode::ERR_SUCCESS;
 $arrOutput = array(
		    'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
			'status' => $arrMsgStatus,
		  );

	  return $arrOutput;	  
}
	
	
public static function getReplyStatus($arrInput){
   if (!isset($arrInput['msg_ids']) || !isset($arrInput['bit']) || !isset($arrInput['status'])) {
       Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
       return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
   }

   $msg_ids = Tieba_Service::getArrayParams($arrInput, 'msg_ids');
   $strMsgIds = implode(',', $msg_ids);
   $arrReq = array(
		  'method'     => 'query',
		  'function'   => 'getReplyStatus',
		  'msg_ids'    => strval($strMsgIds),
	);

$arrRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
      Lib_Util_Log::warning('Dl_Msgstore_Di_Msgstore::query(deleteMsg) failed'.serialize($arrReq));
	  return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL); 									}  


 $msg_status = $arrRes['results'][0];

 $arrMsgStatus = array();
 foreach($msg_status  as $key => $value){

     $msgId = $value['msg_id'];
     $msgStatus = $value['msg_status'];
     $arrMsgStatus[$msgId] = $msgStatus;
 }


 $error = Tieba_Errcode::ERR_SUCCESS;
 $arrOutput = array(
		    'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
			'status' => $arrMsgStatus,
		  );

	  return $arrOutput;	  
	}

/**
     * @brief  获取消息队列最新消息id 宇哲新logic
     * @arrInput:
     *     string       qnames[]
     * @return: 
     *     queueLastid  msg_ids
     **/
    public static function getNewQueueLastid($arrInput) {
        if (!isset($arrInput['qnames'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        $qnames = Tieba_Service::getArrayParams($arrInput, 'qnames');

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.
        $arrReq = array(
            'qnames' => $qnames,
        );
        $arrRes = Dl_Msgstore_Queue_Msgqueue::getQueueLastid($arrReq);
        if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['last_msg_id'])) {
            Lib_Util_Log::warning("getQueueLastid fail. [".serialize($arrRes)."]");
            if (isset($arrRes['errno'])) {
                return self::_errRet($arrRes['errno']); 
            } else {
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL); 
            }
        }
		$queueLastIds = $arrRes['last_msg_id'];

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'    => $error,
            'errmsg'   => Tieba_Error::getErrmsg($error),
            'msg_ids' => $queueLastIds,
        );
        return $arrOutput;
    }
	
	public static function deleteNewQueueMsg($arrInput) {
        if (!isset($arrInput['qname']) || !isset($arrInput['msg_ids'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
		
		$arrRes = Dl_Msgstore_Queue_Msgqueue::deleteQueueMsg($arrInput);
		if($arrRes===false || $arrRes['errno']!=Tieba_Errcode::ERR_SUCCESS) {
			Lib_Util_Log::warning("call deleteNewQueueMsg fail. [".serialize($arrRes)."]");
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL); 
		}

        //output params.
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'    => $error,
            'errmsg'   => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }
    /**
    * @param array - input param
    * @return array - arroutput
    */
    private static function _getQname($appId, $space, $qid) {
        return intval($appId). self::QUEUE_DELIMITER . strval($space). self::QUEUE_DELIMITER . intval($qid);
    }
    /**
    * @param array - input param
    * @return array - arroutput
    */
    private static function _getGid($qname) {
        $arrRes = explode(":", $qname);
        return $arrRes[2];  
    }

}
