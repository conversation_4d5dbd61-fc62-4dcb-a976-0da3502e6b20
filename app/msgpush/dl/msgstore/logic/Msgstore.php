<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * <AUTHOR>
 * @date 2013/11/20 19:03:37
 * @version 2.0
 */

class Dl_Msgstore_Logic_Msgstore {
    protected static $_redis = null;
    protected static $_device_redis = null;
    protected static $_conf = null;

    protected static $_keys = array();
    protected static $_redis_msglist_suffix = 'msglogic';
    protected static $_redis_new_msglist_suffix = 'msglogic';
    protected static $_expire_suffix        = 'expire';
    protected static $_msg_logic_max        = 200;  //消息队列保持长度
    protected static $_msg_trunk_min_length = 50;   //消息队列截断最小长度
    protected static $_msg_logic_expire     = 0;    //默认消息保留时长

    //const MSGSYS_MSGSTORE_LOGIC  = 'msgstore_logic';
    const MSGSYS_MSGSTORE_LOGIC  = 'msglogic';  //复用IM的Redis消息拉链
    const MSGSYS_DEVICE_STORE    = 'msglogic'; 
    const DEVICE_STORE_EXPIRE = 15552000;   //半年，180*86400
    const KEY_SPLIT_CHAR         = '_';
    const MAX_RECORED_MID_EXPIRE =365; 
    const MIN_MSG_ID             = 1 ;//msgid=0保留，为异常状态
    const LOGIC_CONF_PATH = "/app/msgpush/msgstore/logic_msgstore_msgstore";
    const QUEUE_DELIMITER = ":";
    const IM_APP_ID       =  1;
    const JOBMSG_STORE_REDIS_FIX = '_jobMsgStore';
    const MAX_CHAIN_SIZE = 100;

    protected static $arrMaxLength = array (   //消息队列保持长度,如果cmd不在数组中则用默认配置
    	'207001' => 1,
    	'205006' => 200,
    	'205001' => 100,
    );
    
    protected static $arrMinTruncLength = array (  //消息队列保持长度,如果cmd不在数组中则用默认配置
    	'207001' => 10,
    );
    
    protected static $arrMaxLengthByGroup = array(
        2 => 100, //私有群
        4 => 100, //官方群
        30 => 50,//运营群
    );

    protected static $arrMinLengthByGroup = array(
        30 => 10,
    );

    // 31, 32,33群的qname和redis key的对应关系
    protected static $qnameAndRedisKeyMap = array(
        '1::31' => 'MSGLINK_31_msglogic',
        '1::32' => 'MSGLINK_32_msglogic',
        '1::33' => 'MSGLINK_33_msglogic',


        '1::41' => 'MSGLINK_41_msglogic',    //pluginpush 系统群全量android推送
    );


    /**
     * @param
     * @return
     **/
    private static function _errRet($errno){
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }
 	/**
     * @param
     * @return
     * 这里对app_id:space:qid的格式做一次兼容, 和之前的IM redis的key保持一致, 复用IM的Redis消息拉链
     **/
    private static function genKey($qname, $suffix){

        if (!is_array($qname)) {
            // 31,32,33做单独处理
            if (isset(self::$qnameAndRedisKeyMap[$qname])) {
                return self::$qnameAndRedisKeyMap[$qname];
            }
            $arrQname = explode(self::QUEUE_DELIMITER, $qname);
            if (count($arrQname) == 3 && $arrQname[0] == self::IM_APP_ID && empty($arrQname[1]) && $arrQname[2] > 0) {
                return implode(self::KEY_SPLIT_CHAR, array($arrQname[2], $suffix));
            }
            return implode(self::KEY_SPLIT_CHAR, array($qname, $suffix));
        } else {
            return implode(self::KEY_SPLIT_CHAR, array_merge($qname, $suffix));
        }
    }

    /**
     * @brief redis init
     * @param
     * @return: true if success. false if fail.
     **/
    private static function init() {
        if(self::$_redis == null) {
            self::$_redis = new Bingo_Cache_Redis(self::MSGSYS_MSGSTORE_LOGIC);
            if(self::$_redis == false) {
                Lib_Util_Log::warning("init redis init fail:" . serialize(Bd_RalRpc::get_error()));
                return false;
            }
        }

        if(self::$_device_redis == null) {
            self::$_device_redis = new Bingo_Cache_Redis(self::MSGSYS_DEVICE_STORE);
            if(self::$_device_redis == false) {
                Lib_Util_Log::warning("init device redis init fail:" . serialize(Bd_RalRpc::get_error()));
                return false;
            }
        }

        if(self::$_conf == null){    
            self::$_conf = Bd_Conf::getConf(self::LOGIC_CONF_PATH);
            if(self::$_conf == false){
                Lib_Util_Log::warning("init get conf fail.");
                return false;
            }
        }

        if( isset(self::$_conf['truncate_strategy']) ){
            $confStrategy = self::$_conf['truncate_strategy'];
            if(isset($confStrategy['msg_logic_max']) && $confStrategy['msg_logic_max'] > 0 ){
                self::$_msg_logic_max = $confStrategy['msg_logic_max'];
            }

            if(isset($confStrategy['msg_trunk_min_length']) && $confStrategy['msg_trunk_min_length'] >= 0 ){
                self::$_msg_trunk_min_length = $confStrategy['msg_trunk_min_length'];
            }

            if(isset($confStrategy['msg_logic_expire']) && $confStrategy['msg_logic_expire'] >= 0 ){
                self::$_msg_logic_expire = $confStrategy['msg_logic_expire'];
            }
        }

        return true;
    }

    /**
     * @brief  批量自动truncatelist数据
     * 原截断函数不能进行批量处理，直截断了最后一条key的消息拉链，这里添加批量逻辑
     * @param:
     *      uint64_t key
     *      uint64_t msg_id
     *      uint32_t create_time
     * @return: 
     *      boolean true/false
     **/
    private static function _multiTruncateQueueByLength($arrKeys, $intMsgId, $createTime, $cmd = 0, $groupType = 0) {
        //length stragy
        $msg_logic_max = isset(self::$arrMaxLength[$cmd]) ? self::$arrMaxLength[$cmd] : self::$_msg_logic_max;
        $msg_trunc_min = isset(self::$arrMinTruncLength[$cmd]) ? self::$arrMinTruncLength[$cmd] : self::$_msg_trunk_min_length;
        if($groupType > 0){
            $msg_logic_max = isset(self::$arrMaxLengthByGroup[$groupType]) ? self::$arrMaxLengthByGroup[$groupType] : $msg_logic_max;
            $msg_trunc_min = isset(self::$arrMinLengthByGroup[$groupType]) ? self::$arrMinLengthByGroup[$groupType] : $_msg_trunk_min_length;
        }
        
        if ($msg_logic_max > 0 && !empty($arrKeys)) {

            $arrRedisInput = array();
            foreach($arrKeys as $strKey){
                $arrRedisInput['reqs'][]    = array(
                    'key'   => $strKey,
                );
            }
            $ret = self::$_redis->ZCARD($arrRedisInput);
            if(!$ret || $ret['err_no']!==0) {
                Lib_Util_Log::warning("llen redis error. [".serialize($ret)."]");
                return true;
            }

            $arrRedisInput  = array();
            foreach($arrKeys as $strKey){
                $intQueueLength = $ret['ret'][$strKey];
                if ($intQueueLength > ($msg_logic_max + $msg_trunc_min)) {
                    //del old msg
                    $arrRedisInput['reqs'][] = array(
                        'key' => $strKey,
                        'start' => 0,
                        'stop' => $intQueueLength - $msg_logic_max- 1 ,
                    );
                }
            }

            if(!empty($arrRedisInput['reqs'])){
                $ret = self::$_redis->ZREMRANGEBYRANK($arrRedisInput);
                if(!$ret || $ret['err_no']!=0) {
                    Lib_Util_Log::warning("query redis（ZREMRANGEBYRANK） error. [".serialize($ret)."]");
                }
            }
        }

        //exprire strage 
        if (self::$_msg_logic_expire > 0 && !empty($arrKeys)) {
            //add by  pzb，20171225  add expire logic,expire time = _msg_logic_expire * 86400
            $intExpireTime  = self::$_msg_logic_expire * 86400;
            $arrParam   = array();
            foreach($arrKeys as $strKey){
                $arrParam['reqs'][] = array(
                    'key'   => $strKey,
                    'seconds'   => $intExpireTime,
                );
            }
            $ret = self::$_redis->EXPIRE($arrParam);
            if(!$ret || $ret['err_no']!=0) {
                Lib_Util_Log::warning("query redis（EXPIRE） error. [".serialize($ret)."]");
                return true;
            }
        }
        return true;
    }


    /**
     * @brief  自动truncatelist数据
     * @param:
     *      uint64_t key
     *      uint64_t msg_id
     *      uint32_t create_time
     * @return: 
     *      boolean true/false
     **/
    private static function _truncateQueueByLength($strKey, $intMsgId, $createTime, $cmd = 0, $groupType = 0) {
        //length stragy
        $msg_logic_max = isset(self::$arrMaxLength[$cmd]) ? self::$arrMaxLength[$cmd] : self::$_msg_logic_max;
        $msg_trunc_min = isset(self::$arrMinTruncLength[$cmd]) ? self::$arrMinTruncLength[$cmd] : self::$_msg_trunk_min_length;
        if($groupType > 0){
            $msg_logic_max = isset(self::$arrMaxLengthByGroup[$groupType]) ? self::$arrMaxLengthByGroup[$groupType] : $msg_logic_max;
            $msg_trunc_min = isset(self::$arrMinLengthByGroup[$groupType]) ? self::$arrMinLengthByGroup[$groupType] : $_msg_trunk_min_length;
        }
        
        if ($msg_logic_max > 0) {
            $arrRedisInput = array(
                'key' => $strKey,
            );
            $ret = self::$_redis->ZCARD($arrRedisInput);
            if(!$ret || $ret['err_no']!==0) {
                Lib_Util_Log::warning("llen redis error. [".serialize($ret)."]");
                return true;
            }
            $intQueueLength = $ret['ret'][$strKey];

            if ($intQueueLength > ($msg_logic_max + $msg_trunc_min)) {
                //del old msg
                $arrRedisInput = array(
                    'key' => $strKey,
                    'start' => 0,
                    'stop' => $intQueueLength - $msg_logic_max- 1 ,
                );
                $ret = self::$_redis->ZREMRANGEBYRANK($arrRedisInput);
                if(!$ret || $ret['err_no']!=0) {
                    Lib_Util_Log::warning("query redis（ZREMRANGEBYRANK） error. [".serialize($ret)."]");
                    return true;
                }
            }
        }

        //exprire strage 
        if (self::$_msg_logic_expire > 0) {
            //add by  pzb，20171225  add expire logic,expire time = _msg_logic_expire * 86400
            $intExpireTime  = self::$_msg_logic_expire * 86400;
            $arrParam   = array(
                'key'   => $strKey,
                'seconds'   => $intExpireTime,
            );
            $ret = self::$_redis->EXPIRE($arrParam);
            if(!$ret || $ret['err_no']!=0) {
                Lib_Util_Log::warning("query redis（EXPIRE） error. [".serialize($ret)."]");
                return true;
            }
        }
        return true;
    }

    /**
     * @brief TruncateResult数据
     * @param:
     *      array arrMsgIds 
     * @return: 
     *      array arrMsgIds
     **/
    private static function _browseTruncateResult($arrMsgIds) {
        //$msg_logic_max = isset(self::$arrMaxLength[$cmd]) ? self::$arrMaxLength[$cmd] : self::$_msg_logic_max;
        if (self::$_msg_logic_max > 0) {
            $length = count($arrMsgIds);
            if ($length > self::$_msg_logic_max) { 
                return array_slice($arrMsgIds, $length - self::$_msg_logic_max); //最多返回self::$_msg_logic_max
            } else {
                return $arrMsgIds;
            }
        }

        if( self::$_msg_logic_expire > 0 ){
            // to do 
        }

        return $arrMsgIds;
    }

    /**
     * @brief  添加消息
     * @param:
     *      uint64_t qname
     *      uint64_t msg_id
     * @return: 
     *      uint64_t:errno
     *      string:  errmsg
     **/
    public static function updateLastMsgIds($arrInput) {
        if (!isset($arrInput['qnames']) || !isset($arrInput['msg_id'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        if (!self::init()) {
            Lib_Util_Log::warning('load config fail by updateLastMsgIds.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $intMsgId   = intval($arrInput['msg_id']);
        $qnames = Tieba_Service::getArrayParams($arrInput, 'qnames');
        $value = $intMsgId;
        
        $reqs = array();
        foreach ($qnames as $qname ) {
            $redisQueueKey = self::genKey(strval($qname), self::$_redis_new_msglist_suffix);
            $reqs[] =   array(
                'key'    => $redisQueueKey,
                'value'  => $value, //$intMsgId
                'seconds' => self::DEVICE_STORE_EXPIRE,
            );
        }
        $arrRedisInput = array('reqs' => $reqs);

        //add msg_id in list 
        $ret = self::$_device_redis->SETEX($arrRedisInput);
        if (!$ret || $ret['err_no'] !== 0) {
            Lib_Util_Log::warning("updateLastMsgIds failed. [".serialize($ret)."]");
            return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }

    /**
     * @brief  添加消息
     * @param:
     *      uint64_t qname
     *      uint64_t msg_id
     *      uint32_t create_time
     * @return: 
     *      uint64_t:errno
     *      string:  errmsg
     **/
    public static function pushQueueMsgIds($arrInput) {
        if (!isset($arrInput['qnames']) || !isset($arrInput['msg_id']) || !isset($arrInput['create_time'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        if (!self::init()) {
            Lib_Util_Log::warning('load config fail by pushQueueMsgIds.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $intMsgId   = intval($arrInput['msg_id']);
        $createTime = intval($arrInput['create_time']);
        $qnames = Tieba_Service::getArrayParams($arrInput, 'qnames');
        $jobId = 0;
        if(isset($arrInput['job_id']) && !empty($arrInput['job_id'])){
            $jobId = intval($arrInput['job_id']);
        }
        $value = $intMsgId;
        if(0 < $jobId){
            $value = strval($intMsgId).'_'.strval($jobId);
        }
        $reqs = array();
        foreach ($qnames as $qname ) {
            $redisQueueKey = self::genKey(strval($qname), self::$_redis_msglist_suffix);
            $arrQueueKey[] = $redisQueueKey;
            $reqs[] =   array(
                'key'    => $redisQueueKey,
                'score'  => $intMsgId,
                'member' => $value, //$intMsgId
            );
        }
        $arrRedisInput = array('reqs' => $reqs);

        //add msg_id in list 
        $ret = self::$_redis->ZADD($arrRedisInput);
        if (!$ret || $ret['err_no'] !== 0) {
            Lib_Util_Log::warning("msg_id add fail. [".serialize($ret)."]");
            return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
        }

        //truncate list
        $arrRes = self::_multiTruncateQueueByLength($arrQueueKey, $intMsgId , $createTime);
        if ($arrRes === false) {
            Lib_Util_Log::warning("_truncateList fail. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }

    /**
     * @brief  添加消息
     * @param:
     *      uint64_t qname
     *      uint64_t msg_id
     *      uint32_t create_time
     * @return: 
     *      uint64_t:errno
     *      string:  errmsg
     **/
    public static function pushQueueMsgId($arrInput) {
        if (!isset($arrInput['qname']) || !isset($arrInput['msg_id']) || !isset($arrInput['create_time'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        if (!self::init()) {
            Lib_Util_Log::warning('load config fail by pushQueueMsgId.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $strQname   = strval($arrInput['qname']);
        $intMsgId   = intval($arrInput['msg_id']);
        $createTime = intval($arrInput['create_time']);
        $cmd        = intval($arrInput['cmd']);

        $redisQueueKey = self::genKey($strQname, self::$_redis_msglist_suffix);
        $strJobid = strval($arrInput['jobid']);
        $strUsertype = strval($arrInput['usertype']);
        $intGrouptype = intval($arrInput['grouptype']);
        if ($intGrouptype == 6 || $intGrouptype == 30) {
            $arrRedisInput = array(
                'key'    => $redisQueueKey,
                'score'  => $intMsgId,
                'member' => strval($intMsgId)."_".$strJobid,
            );
        }else{
            //add msg_id in list 
            $arrRedisInput = array(
                'key'    => $redisQueueKey,
                'score'  => $intMsgId,
                'member' => $intMsgId,
            );
        }
        
        $ret = self::$_redis->ZADD($arrRedisInput);
        if (!$ret || $ret['err_no'] !== 0) {
            Lib_Util_Log::warning("msg_id add fail. [".serialize($ret)."]");
            return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
        }

        //truncate list
        $arrRes = self::_truncateQueueByLength($redisQueueKey, $intMsgId , $createTime, $cmd, $intGrouptype);
        if ($arrRes === false) {
            Lib_Util_Log::warning("_truncateList fail. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }
    /**
     *     
     *     * @param array - input param
     *     * @return array - arroutput
     *                 
     **/
    public static function existsMsgChain($arrInput) {
        if (!isset($arrInput['qnames'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        if (!self::init()) {
            Lib_Util_Log::warning('load config fail by getQueueLastid.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        $arrKeys = Tieba_Service::getArrayParams($arrInput, 'qnames');

        if (empty($arrMsgChain)) {
            return array(
                'errno'   => Tieba_Errcode::ERR_SUCCESS,
                'errmsg'  => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
                'result'  => array(),
            );
        }

        $arrRedisInput = array();
        foreach ($arrKeys as $key) {
            $arrRedisInput[] = array(
                'key'   => self::genKey($key, self::$_redis_msglist_suffix),
            );
        }
        $arrRedisInput = array(
            'reqs' => $arrRedisInput,
        );
        $ret = self::$_redis->EXISTS($arrRedisInput);
        if (!$ret || $ret['err_no'] != 0) { 
            Lib_Util_Log::warning("existsMsgChain. [".serialize($arrRedisInput)."]");
            if (empty($ret['ret'])) {  //redis 某个分片出现问题，则尽力返回其它成功的分片
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
            }
        }

        $existRes = $ret['ret'];
        $arrOut['errno']  = Tieba_Errcode::ERR_SUCCESS;
        $arrOut['errmsg'] = Tieba_Error::getErrmsg($arrOut['errno']);
        $arrOut['result'] = $existRes;
        return $arrOut;
    }
    
    /**
     *     
     *     * @param array - input param
     *     * @return array - arroutput
     *                 
     **/
    public static function addMsgChain($arrInput) {
        if (!isset($arrInput['msg_chain'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        if (!self::init()) {
            Lib_Util_Log::warning('load config fail by getQueueLastid.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        $arrMsgChain = Tieba_Service::getArrayParams($arrInput, 'msg_chain');

        if (empty($arrMsgChain)) {
            return array(
                'errno'   => Tieba_Errcode::ERR_SUCCESS,
                'errmsg'  => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
                'result'  => array(),
            );
        }

        $arrRedisInput = array();
        foreach ($arrMsgChain as $item) {
			$key = $item['chain_key'];
            $arrMembers = array();
            foreach ($item['msg_ids'] as $msgId) {
				$arrTmp = explode('_', $msgId);
				$score = $arrTmp[0];
                $arrMembers[] = array(
                    'member' => $msgId,
                    'score' => $score,
                );
            }
            $arrRedisInput[] = array(
                'key'   => self::genKey($key, self::$_redis_msglist_suffix),
                'members' => $arrMembers,
            );
            $arrExpireParam[]   = array(
                'key'   => self::genKey($key, self::$_redis_msglist_suffix), 
                'seconds'   => 90 * 86400,//90 days expire time
            );
        }
        $arrRedisInput = array(
            'reqs' => $arrRedisInput,
        );
        $ret = self::$_redis->ZADD($arrRedisInput);
        if (!$ret || $ret['err_no'] != 0) { 
            Lib_Util_Log::warning("addMsgChain. [".serialize($arrRedisInput)."]");
            if (empty($ret['ret'])) {  //redis 某个分片出现问题，则尽力返回其它成功的分片
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
            }
        }

        //add expire logic by pzb ,20171225
        //this function will recover message queue from db to redis.the key with no expire logic will be in redis forever.
        $arrParam   = array(
            'reqs'  => $arrExpireParam,
        );
        $ret = self::$_redis->EXPIRE($arrParam);
        if (!$ret || $ret['err_no'] != 0) { 
            Lib_Util_Log::warning("addMsgChain expire intpu:. [".serialize($arrParam)."]");
            if (empty($ret['ret'])) {  //redis 某个分片出现问题，则尽力返回其它成功的分片
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
            }
        }


        $arrOut['errno']  = Tieba_Errcode::ERR_SUCCESS;
        $arrOut['errmsg'] = Tieba_Error::getErrmsg($arrOut['errno']);
        return $arrOut;
    }


    /**
     *     
     *     * @param array - input param
     *     * @return array - arroutput
     *                 
     **/
    public static function delMsgChain($arrInput) {
        if (!isset($arrInput['qnames'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        if (!self::init()) {
            Lib_Util_Log::warning('load config fail by getQueueLastid.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $arrQnames = Tieba_Service::getArrayParams($arrInput, 'qnames');
        if (empty($arrQnames)) {
            return array(
                'errno'   => Tieba_Errcode::ERR_SUCCESS,
                'errmsg'  => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
                'result'  => array(),
            );
        }

        $arrRedisInput = array();
        foreach($arrQnames as $strKey){
            $arrRedisInput[] = array(
                'key'   => self::genKey($strKey, self::$_redis_msglist_suffix),
            );
        }
        $arrRedisInput = array(
            'reqs' => $arrRedisInput,
        );
        $ret = self::$_redis->DEL($arrRedisInput);
        if (!$ret || $ret['err_no'] != 0) { 
            Lib_Util_Log::warning("del redis error. [".serialize($ret)."]");
        }

        $arrOut['errno']  = Tieba_Errcode::ERR_SUCCESS;
        $arrOut['errmsg'] = Tieba_Error::getErrmsg($arrOut['errno']);
        return $arrOut;
    }

    /**
     *
     *     * @param array - input param
     *     * @return array - arroutput
     *
     **/
    public static function delMsgChainByMsgId($arrInput) {
        if (!isset($arrInput['reqs']) || empty($arrInput['reqs'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (!self::init()) {
            Lib_Util_Log::warning('load config fail by delMsgChainByMsgId.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $reqs = Tieba_Service::getArrayParams($arrInput, 'reqs');

        $arrRedisInput = array();
        foreach($reqs as $req){
            if (!isset($req['qname']) || !isset($req['min_id']) || !isset($req['max_id'])) {
                Lib_Util_Log::warning('req param error:['.serialize($req).']');
                continue;
            }
            $arrRedisInput[] = array(
                'key'   => self::genKey($req['qname'], self::$_redis_msglist_suffix),
                'min'   => $req['min_id'],
                'max'   => $req['max_id'],
            );
        }
        $arrRedisInput = array(
            'reqs' => $arrRedisInput,
        );
        $ret = self::$_redis->ZREMRANGEBYSCORE($arrRedisInput);
        if (!$ret || $ret['err_no'] != 0) {
            Lib_Util_Log::warning("del redis error. [".serialize($ret)."]");
        }

        $arrOut['errno']  = Tieba_Errcode::ERR_SUCCESS;
        $arrOut['errmsg'] = Tieba_Error::getErrmsg($arrOut['errno']);
        return $arrOut;
    }

    /**
     *     
     *     * @param array - input param
     *     * @return array - arroutput
     *                 
     **/
    public static function getMsgChain($arrInput) {
        if (!isset($arrInput['qnames'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        if (!self::init()) {
            Lib_Util_Log::warning('load config fail by getQueueLastid.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $arrQnames = $arrInput['qnames'];
        if (!is_array($arrQnames)) {
            $arrQnames = array($arrQnames);
        }
        if (empty($arrQnames)) {
            return array(
                'errno'   => Tieba_Errcode::ERR_SUCCESS,
                'errmsg'  => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
                'result'  => array(),
            );
        }

        $arrRedisInput = array();
        foreach($arrQnames as $strQname){
            $strQname = strval($strQname);
            $arrRedisInput[] = array(
                'key'   => self::genKey($strQname, self::$_redis_msglist_suffix),
                'start' => 0,
                'stop'  => -1,
            );
        }
        $arrRedisInput = array(
            'reqs' => $arrRedisInput,
        );
        $ret = self::$_redis->ZRANGE($arrRedisInput);
        if (!$ret || $ret['err_no'] != 0) { 
            Lib_Util_Log::warning("query redis error. [".serialize($ret)."]");
            if (empty($ret['ret'])) {  //redis 某个分片出现问题，则尽力返回其它成功的分片
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
            }
        }
        $retQueueLastIds = $ret['ret'];

        $queueIdLists = array();
        foreach($arrInput['qnames'] as $qname) {
            $key = self::genKey($qname, self::$_redis_msglist_suffix);
            if (isset($retQueueLastIds[$key])) {
                $tmp = $retQueueLastIds[$key];
                $length = count($tmp);
                if ($length > self::MAX_CHAIN_SIZE) {
                    $queueIdLists[$qname] = array_slice($tmp, $length - self::MAX_CHAIN_SIZE);
                } else {
                    $queueIdLists[$qname] = $tmp;
                }
            }
        }

        $arrOut['errno']  = Tieba_Errcode::ERR_SUCCESS;
        $arrOut['errmsg'] = Tieba_Error::getErrmsg($arrOut['errno']);
        $arrOut['result'] = $queueIdLists;
        return $arrOut;
    }

    /**
     * @brief 获取最新消息id
     * @param:
     *      qnames
     * @return: 
     *      uint64_t:   errno
     *      string:     errmsg
     *      array:      result => array(
     *                              qname => last_msg_id,...
     *                            );
     **/
    public static function getQueueLastidEx($arrInput) {
        if (!isset($arrInput['qnames'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        if (!self::init()) {
            Lib_Util_Log::warning('load config fail by getQueueLastidEx.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $arrQnames = $arrInput['qnames'];
        if (!is_array($arrQnames)) {
            $arrQnames = array($arrQnames);
        }
        if (empty($arrQnames)) {
            return array(
                'errno'   => Tieba_Errcode::ERR_SUCCESS,
                'errmsg'  => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
                'result'  => array(),
            );
        }

        $arrRedisInput = array();
        foreach($arrQnames as $strQname){
            $strQname = strval($strQname);
            $arrRedisInput[] = array(
                'key'   => self::genKey($strQname, self::$_redis_new_msglist_suffix),
            );
        }
        $arrRedisInput = array(
            'reqs' => $arrRedisInput,
        );
        $ret = self::$_device_redis->GET($arrRedisInput);
        if (!$ret || $ret['err_no'] != 0) { 
            Lib_Util_Log::warning("query redis error. [".serialize($ret)."]");
            if (empty($ret['ret'])) {  //redis 某个分片出现问题，则尽力返回其它成功的分片
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
            }
        }
        $retQueueLastIds = $ret['ret'];

        $queueLastIds = array();
        foreach($arrQnames as $qname) {
            $qname = strval($qname);
            $key   = self::genKey($qname, self::$_redis_new_msglist_suffix);
            if (isset($retQueueLastIds[$key]) && intval($retQueueLastIds[$key]) > 0) {
                $queueLastIds[$qname] = intval($retQueueLastIds[$key]);
            } else {
                $queueLastIds[$qname] = self::MIN_MSG_ID;
//                Lib_Util_Log::warning("Get queueLastIds failed for key . [".serialize($key)."] and the result of redis is [".serialize($retQueueLastIds)."]");
            }
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array();
        $arrOutput['errno'] = $error;
        $arrOutput['errmsg'] = Tieba_Error::getErrmsg($error);
        $arrOutput['result'] = array(
            'lastIds' => $queueLastIds,
        );
        return $arrOutput;
    }

    /**
     * @brief query msgLinkCount
     * @param:
     *      qnames
     * @return:
     *      uint64_t:   errno
     *      string:     errmsg
     **/
    public static function queryMsgLinkCount($arrInput) {
        if (!isset($arrInput['qnames'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        if (!self::init()) {
            Lib_Util_Log::warning('load config fail by queryMsgLinkCount. arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $arrQnames = $arrInput['qnames'];
        if (!is_array($arrQnames)) {
            $arrQnames = array($arrQnames);
        }
        if (empty($arrQnames)) {
            return array(
                'errno'   => Tieba_Errcode::ERR_SUCCESS,
                'errmsg'  => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
            );
        }

        foreach ($arrQnames as $strQname){
            $strQname = strval($strQname);
            $arrRedisInput[] = array(
                'key'   => self::genKey($strQname, self::$_redis_msglist_suffix),
            );
        }
        $arrRedisInput = array(
            'reqs' => $arrRedisInput,
        );
        $ret = self::$_redis->ZCARD($arrRedisInput);
        if (!$ret || $ret['err_no'] != 0) { 
            Lib_Util_Log::warning("query redis error. [".serialize($ret)."]");
            if (empty($ret['ret'])) {  //redis 某个分片出现问题，则尽力返回其它成功的分片
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
            }
        }

        $arrData = array();
        foreach ($arrQnames as $strQname) {
            $strKey = self::genKey($strQname, self::$_redis_msglist_suffix);
            if (isset($ret['ret'][$strKey])) {
                $arrData[$strQname] = $ret['ret'][$strKey];
            }
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $arrData,
        );
        return $arrOutput;
    }

    /**
     * @brief truncate msgLink
     * @param:
     *      qnames
     * @return:
     *      uint64_t:   errno
     *      string:     errmsg
     **/
    public static function truncateMsgLink($arrInput) {
        if (!isset($arrInput['qnames']) || !isset($arrInput['limit'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        if (!self::init()) {
            Lib_Util_Log::warning('load config fail by truncateMsgLink. arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $arrQnames = $arrInput['qnames'];
        $limit = $arrInput['limit'];
        if (!is_array($arrQnames)) {
            $arrQnames = array($arrQnames);
        }
        if (empty($arrQnames)) {
            return array(
                'errno'   => Tieba_Errcode::ERR_SUCCESS,
                'errmsg'  => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
            );
        }

        foreach ($arrQnames as $strQname){
            $strQname = strval($strQname);
            $arrRedisInput[] = array(
                'key'   => self::genKey($strQname, self::$_redis_msglist_suffix),
            );
        }
        $arrRedisInput = array(
            'reqs' => $arrRedisInput,
        );
        $ret = self::$_redis->ZCARD($arrRedisInput);
        if (!$ret || $ret['err_no'] != 0) { 
            Lib_Util_Log::warning("query redis error. [".serialize($ret)."]");
            if (empty($ret['ret'])) {  //redis 某个分片出现问题，则尽力返回其它成功的分片
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
            }
        }

        $reqs = array();
        foreach ($arrQnames as $strQname) {
            $strKey = self::genKey($strQname, self::$_redis_msglist_suffix);
            if (isset($ret['ret'][$strKey]) && $ret['ret'][$strKey] > $limit) {
                $reqs[] = array(
                    'key' => $strKey,
                    'start' => 0,
                    'stop' => $ret['ret'][$strKey] - $limit - 1,
                ); 
            }
        }
        if (empty($reqs)) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $arrRedisInput = array(
            'reqs' => $reqs,
        );
        $ret = self::$_redis->ZREMRANGEBYRANK($arrRedisInput);
        if (!$ret || $ret['err_no'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::warning("redis ZREMRANGEBYRANK failed, input: ".serialize($arrRedisInput). " output: ".serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    } 

    /**
     * @brief 获取最新消息id
     * @param:
     *      qnames
     * @return:
     *      uint64_t:   errno
     *      string:     errmsg
     *      array:      result => array(
     *                              qname => last_msg_id,...
     *                            );
     **/
    public static function getQueueLastid($arrInput) {
        if (!isset($arrInput['qnames'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        if (!self::init()) {
            Lib_Util_Log::warning('load config fail by getQueueLastid.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $arrQnames = $arrInput['qnames'];
        $user_id   = $arrInput['user_id'];
        if (!is_array($arrQnames)) {
            $arrQnames = array($arrQnames);
        }
        if (empty($arrQnames)) {
            return array(
                'errno'   => Tieba_Errcode::ERR_SUCCESS,
                'errmsg'  => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
                'result'  => array(),
            );
        }

        $arrRedisInput = array();

        //去重重复的qname;  重复qname会造成后面请求redis失败
        $arrQnames = array_unique($arrQnames);

        foreach($arrQnames as $strQname){
            $strQname = strval($strQname);
            $arrRedisInput[] = array(
                'key'   => self::genKey($strQname, self::$_redis_msglist_suffix),
                'start' => 0,
                'stop'  => 0,
            );
        }
        $arrRedisInput = array(
            'reqs' => $arrRedisInput,
        );
        $ret = self::$_redis->ZREVRANGE($arrRedisInput);
        if (!$ret || $ret['err_no'] != 0) { 
            Lib_Util_Log::warning("query redis error. [".serialize($ret)."]");
            if (empty($ret['ret'])) {  //redis 某个分片出现问题，则尽力返回其它成功的分片
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
            }
        }
        $retQueueLastIds = $ret['ret'];

        /*
	$queueLastIds = array();
        foreach($arrQnames as $qname) {
            $qname = strval($qname);
            $key   = self::genKey($qname, self::$_redis_msglist_suffix);
            if (isset($retQueueLastIds[$key][0])) {
            	$arrMsgidAndJobid = explode("_", strval($retQueueLastIds[$key][0]));
                if (is_array($arrMsgidAndJobid)  && count($arrMsgidAndJobid) ==2 ) {
                    $tempLastmsgid = $arrMsgidAndJobid[0];
                    $tempjobid = $arrMsgidAndJobid[1];
                    if ($templastMsgid > 0) {
                        $queueLastIds[$qname] = $templastMsgid;
                    } else {
                        $queueLastIds[$qname] = self::MIN_MSG_ID;
                    }
                }
	     }
            
	}
	*/

        $queueLastIds = array();
        $arrNeedDb = array();
        foreach($arrQnames as $qname) {
            $qname = strval($qname);
            $key   = self::genKey($qname, self::$_redis_msglist_suffix);
            if (isset($retQueueLastIds[$key][0]) && strlen($retQueueLastIds[$key][0]) > 0) {
	            $arrMsgidAndJobid = explode("_", strval($retQueueLastIds[$key][0]));

                if (is_array($arrMsgidAndJobid)  && count($arrMsgidAndJobid) >0 ) {
                    //10个id拼接的是新的存储
                    if( count($arrMsgidAndJobid) == 10){
                        $position = intval($user_id) % 10  ;
                        $tempLastmsgid = $arrMsgidAndJobid[$position];
                    }else{
                        $tempLastmsgid = $arrMsgidAndJobid[0];
                    }

                    $tempjobid = $arrMsgidAndJobid[1];
                    //$tempusertype = $arrMsgidAndJobid[2];
                    if ($tempLastmsgid > 0) {
                        $queueLastIds[$qname] = $tempLastmsgid;
                    }   
                }else{
                     $queueLastIds[$qname] = intval($retQueueLastIds[$key][0]);
		      }  	

            } else {
                $queueLastIds[$qname] = self::MIN_MSG_ID;
                $arrNeedDb[] = $qname;
//                Lib_Util_Log::warning("Get queueLastIds failed for key . [".serialize($key)."] and the result of redis is [".serialize($retQueueLastIds)."]");
            }
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array();
        $arrOutput['errno'] = $error;
        $arrOutput['errmsg'] = Tieba_Error::getErrmsg($error);
        $arrOutput['result'] = array(
            'needDb' => $arrNeedDb,
            'lastIds' => $queueLastIds,
        );
        return $arrOutput;
    }
    /**
    * 从Redis 中获取消息体
    * @param
    * @return
    */
    public static function getJobMsgByJobid($jobid){
		$arrOutput = array();
        $strJobMsgKey = strval($jobid).self::JOBMSG_STORE_REDIS_FIX;
		$input = array(
            'key' => $strJobMsgKey,
        );
        $ret = self::$_redis->GET($input);
        if (!$ret || $ret['err_no'] != 0) {
            Lib_Util_Log::warning("query redis（GET） error. [".serialize($ret)."]");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        if ($ret['ret'][$input['key']] != null) {
			$strVal = gzinflate($ret['ret'][$input['key']]);
            $arrOutput = unserialize($strVal);
        }
        return $arrOutput;
    }




    /**
     * 根据消息id批量获取消息体
     *
     * <AUTHOR>
     * @param  array $msgIds
     *
     *         eg: $msgIds = array(id1,id2,......)
     *
     * @return array $arrOutput
     *
     *         eg: $arrOutput = array(
     *              'errno' => ,
     *              'errmsg' => ,
     *              'result' => array($msgId=>content,..),
     *             )
     *
     */
    public static function getMultiMsg($msgIds){
        $multiReq['reqs'] = array();
        $arrContent = array();
        $arrOut['errno']  = Tieba_Errcode::ERR_SUCCESS;
        $arrOut['errmsg'] = Tieba_Error::getErrmsg($arrOut['errno']);

        foreach($msgIds as $msgId){
            $strJobMsgKey = strval($msgId).self::JOBMSG_STORE_REDIS_FIX;
            $redisKeyInfo = array(
                'key' => $strJobMsgKey,
            );
            $multiReq['reqs'][] = $redisKeyInfo;
        }
        unset($redisKeyInfo);
        unset($msgId);

        if(empty($multiReq['reqs'])){
            $arrOut['result'] = $arrContent;
            return $arrOut;
        }

        $ret = self::$_redis->GET($multiReq);
        if (!$ret || $ret['err_no'] != 0) {
            Lib_Util_Log::warning("query redis（GET） error. [".serialize($ret)."]");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        foreach($multiReq['reqs'] as $redisKeyInfo){
            if ($ret['ret'][$redisKeyInfo['key']] != null) {
                $strMsgContent = gzinflate($ret['ret'][$redisKeyInfo['key']]);
                $arrMsgContent = unserialize($strMsgContent);
                $arrVal  = explode('_',$redisKeyInfo['key']);
                $msgId   = $arrVal[0];
                $arrContent[$msgId] = $arrMsgContent;
            }

        }

        $arrOut['result'] = $arrContent;
        return $arrOut;
    }

    /**
     * @brief 批量获取队列最新消息id拉链
     * @param:
     *      reqs => array(
     *                       array(
     *                           string     qname,
     *                           uint64_t   last_msg_id,
     *                           uint32_t   count, //拉消息数目, 0表示无长度限制
     *                       ),...
     *                   )
     * @return: 
     *      返回消息id列表从小到大排序
     *      uint64_t:   errno
     *      string:     errmsg
     *      array:      result => array(
     *                              qname => array(msg_id,...),..
     *                            );
     **/
    public static function getQueueRangeMsg($arrInput) {
        if (!isset($arrInput['reqs'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        if (empty($arrInput['reqs'])) {
            return array(
                'errno'   => Tieba_Errcode::ERR_SUCCESS,
                'errmsg'  => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
                'result'  => array(),
            );
        }
        if (!self::init()) {
            Lib_Util_Log::warning('load config fail by getQueueRangeMsg.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $arrRedisInput = array();
        foreach($arrInput['reqs'] as $queueReqParam) {
            if (!isset($queueReqParam['qname']) || !isset($queueReqParam['last_msg_id'])) {
                Lib_Util_Log::warning("input params invalid. [".var_export($arrInput,true)."]");
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
            }

            $count = -1;
            if (isset($queueReqParam['count']) && intval($queueReqParam['count']) > 0) {
                $count = intval($queueReqParam['count']);
                if ($count > self::$_msg_logic_max) {
                    $count = self::$_msg_logic_max;
                }
            }
            $arrRedisInput[] = array(
                'key'    => self::genKey(strval($queueReqParam['qname']), self::$_redis_msglist_suffix) ,
                'min'    => '(' . intval($queueReqParam['last_msg_id']) ,
                'max'    => '+inf',
                'offset' => 0,
                'count'  => $count,
            );
        }
        $arrRedisInput = array(
            'reqs' => $arrRedisInput,
        );

        //查询数据
        $ret = self::$_redis->ZRANGEBYSCORE($arrRedisInput);
        if (!$ret || $ret['err_no'] != 0) {
            Lib_Util_Log::warning("query redis ZRANGEBYSCORE error. [".serialize($ret)."]");
            if (empty($ret['ret'])) {  //redis 某个分片出现问题，则尽力返回其它成功的分片
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
            }
        }
        $retQueueIdLists = $ret['ret'];

        $queueIdLists = array();
        foreach($arrInput['reqs'] as $queueReqParam) {
            $strQname = strval($queueReqParam['qname']);
            $key      = self::genKey($strQname, self::$_redis_msglist_suffix);
            if (isset($retQueueIdLists[$key])) {
                $queueIdLists[$strQname] = self::_browseTruncateResult($retQueueIdLists[$key]);
                // $templastMsgid = strstr(strval($retQueueLastIds[$key][0]),'_',true);
                // if ($templastMsgid > 0) {
                //     $queueIdLists[$strQname] = self::_browseTruncateResult($templastMsgid);
                // }
                
            } else {
                $queueIdLists[$strQname] = array();
            }
        }

        $arrOut['errno']  = Tieba_Errcode::ERR_SUCCESS;
        $arrOut['errmsg'] = Tieba_Error::getErrmsg($arrOut['errno']);
        $arrOut['result'] = $queueIdLists;
        return $arrOut;
    }
	/**
     * 
     * 临时删除运营消息拉链
     * @param group_id
     * @return
     */
    public static function truncateQueue($arrInput){
        if(!isset($arrInput['qids']) || !isset($arrInput['max_len'])){
            Lib_Util_Log::warning('input params invalid.'.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        if (!self::init()) {
            Lib_Util_Log::warning('load config fail by truncateQueue:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        $intMax = intval($arrInput['max_len']);
        $arrReqs = array();
        foreach ($arrInput['qids'] as $intGroupId){
            $arrTmp = array(
                'key' => $intGroupId.'_'.self::$_redis_msglist_suffix,
            );
            $arrReqs[] = $arrTmp;
        }
        $input = array(
            'reqs' => $arrReqs,
        );
        
        $ret = self::$_redis->ZCARD($input);
        if(!$ret || empty($ret['ret'])) {
            Lib_Util_Log::warning("llen redis error. [".serialize($ret)."]");
            return self::_errRet(Tieba_Errcode::ERR_DL_DATA);
        }
        $arrReqs = array();
        foreach ($arrInput['qids'] as $intGroupId){
            $strKey = $intGroupId.'_'.self::$_redis_msglist_suffix;
            if (isset($ret['ret'][$strKey]) && $ret['ret'][$strKey] > $intMax + 1){
                $arrReqs[] = array(
                    'key' => $strKey,
                    'start' => 0,
                    'stop' => $ret['ret'][$strKey] - $intMax - 1,
                );
            }          
        }
        if(empty($arrReqs)){
            return  self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $input = array(
            'reqs' => $arrReqs,
        );       
        $ret = self::$_redis->ZREMRANGEBYRANK($input);
        if(!$ret || empty($ret['ret'])) {
            Lib_Util_Log::warning("query redis（ZREMRANGEBYRANK） error. [".serialize($ret)."]");
            return self::_errRet(Tieba_Errcode::ERR_DL_DATA);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);   
    }
    /**
     * @abstract 从mysql im_jobmsg_info表中拉消息
     * @param ArrayObject $arrInput
     * @return ArrayObject $arrOut
     */
    public static function getMsgFromJob($arrInput){
        if (!isset($arrInput['reqs'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        if (empty($arrInput['reqs'])) {
            return array(
                'errno'   => Tieba_Errcode::ERR_SUCCESS,
                'errmsg'  => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
                'result'  => array(),
            );
        }
        $retMsgInfos = array();
        foreach ($arrInput['reqs'] as $arrItem){
            $arrReq = array(
                'method'   => 'query',
                'function' => 'getJobMsgInfo',
                'group_id' => $arrItem['part_id'],
                'msg_id'   => $arrItem['last_msg_id'],
            );
            $retRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
            if (!is_array($retRes) || $retRes['errno']!==0 || !isset($retRes['results'][0])) {
                Lib_Util_Log::warning('Dl_Msgstore_Di_Msgstore::getJobMsg failed ' . serialize($retRes));
                continue;
            }
            $retMsgInfos[$arrItem['part_id']] = $retRes['results'];
        }
        $arrOut['errno']  = Tieba_Errcode::ERR_SUCCESS;
        $arrOut['errmsg'] = Tieba_Error::getErrmsg($arrOut['errno']);
        $arrOut['result'] = $retMsgInfos;
        return $arrOut;
    }
	/**
     * @abstract 从mysql im_message_info表中拉消息
     * @param ArrayObject $arrInput
     * @return ArrayObject $arrOut
     */
    public static function getMsgFromMsgInfo($arrInput){
        if (!isset($arrInput['reqs'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        if (empty($arrInput['reqs'])) {
            return array(
                'errno'   => Tieba_Errcode::ERR_SUCCESS,
                'errmsg'  => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
                'result'  => array(),
            );
        }
        $retMsgInfos = array();
        foreach ($arrInput['reqs'] as $arrItem){
            if(1130352203== $arrItem['part_id']){
                $arrReq = array(
                    'method'   => 'query',
                    'function' => 'getMsgInfoFromPrimary',
                    'part_id' => $arrItem['part_id'],
                    'msg_id'   => $arrItem['last_msg_id'],
                );
                Bingo_Log::warning('pzbtest msgpush '.var_export($arrReq,true));
                Bingo_Log::warning('pzbtest msgpush '.var_export($arrInput,true));
            }
            else{
                $arrReq = array(
                    'method'   => 'query',
                    'function' => 'getMsgInfo',
                    'part_id' => $arrItem['part_id'],
                    'msg_id'   => $arrItem['last_msg_id'],
                );
            }
            $retRes = Dl_Msgstore_Di_Msgstore::query($arrReq);
            if (!is_array($retRes) || $retRes['errno']!==0 || !isset($retRes['results'][0])) {
                Lib_Util_Log::warning('Dl_Msgstore_Di_Msgstore::getJobMsg failed ' . serialize($retRes));
                continue;
            }
            $retMsgInfos[$arrItem['part_id']] = $retRes['results'][0];
        }
        $arrOut['errno']  = Tieba_Errcode::ERR_SUCCESS;
        $arrOut['errmsg'] = Tieba_Error::getErrmsg($arrOut['errno']);
        $arrOut['result'] = $retMsgInfos;
        return $arrOut;
    }
}
