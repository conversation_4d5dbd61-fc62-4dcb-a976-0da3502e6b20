//@description: 消息队列存储服务(message store)
//@author: <EMAIL>

struct Msg{
    string   qname;          //消息队列唯一标识名称
    uint64_t msg_id;         //消息id
    uint32_t msg_type;       //消息类型：文本1 图片2 语音3
    uint64_t user_id;        //发送该消息的用户id(运营消息视为系统发送，user_id为1)
    string   content;        //消息内容
    uint32_t duration;       //语音消息长度
    uint32_t create_time;    //消息发表时间
    uint32_t status;         //消息状态 (1~9)表示状态 0新消息 1通过 2拒绝
};

struct Output
{
    int32_t errno;              //错误号
    string  errmsg;             //错误信息    
};

struct getQueueRangeMsgParam {
    string      qname;          //消息队列唯一标识名称
    uint64_t    last_msg_id;    //消息id
    uint64_t    count;          //拉消息最大长度
};

struct getQueueRangeMsgOutput
{
    int32_t errno;              //错误号
    string errmsg;              //错误信息    
    Msg    msg_list[];          //消息列表
};

struct queueLastid {
    uint64_t    qname ;         //消息队列唯一标识名称
    uint64_t    last_msg_id;    //消息id
};

struct getQueueLastidOutput
{
    int32_t errno;              // 错误号（0:成功，其它：失败）
    string errmsg;              // 错误信息    
    queueLastid  msg_ids[];     // 消息id
};

service msgstore
{    
    /**
     * @brief :  消息队列存储接口
     * @param [in]    qname           : uint64_t      :   消息队列唯一标识名称
     * @param [in]    msg_id          : uint64_t      :   消息id
     * @param [in]    user_id         : uint64_t      :   发送该消息的用户id
     * @param [in]    content         : string        :   消息内容
     * @param [in]    duration        : uint32_t      :   消息长度
     * @param [in]    msg_type        : uint32_t      :   消息类型
     * @param [in]    create_time     : uint32_t      :   发表时间
     * @param [out]   output          : Output        :   返回值
     * @example service.tieba.baidu.com/service/msgpush?method=pushQueueMsg&qname=1:g:202003&msg_id=123&user_id=111&content=abc&duration=0&msg_type=1&create_time=111ie=utf-8&format=json
     **/
    void pushQueueMsg(string qname, uint64_t msg_id, uint64_t user_id, string content, uint32_t duration, uint32_t msg_type, uint32_t create_time, out Output output);

    /**
     * @brief :  消息队列批量查询接口
     * @param [in]    reqs[]          : getQueueRangeMsgParam  :   参数
     * @param [out]   output          : getQueueRangeMsgOutput :   返回值
     * @example service.tieba.baidu.com/service/msgpush?method=getQueueRangeMsg&reqs=[]&ie=utf-8&format=json
     **/
    void getQueueRangeMsg(getQueueRangeMsgParam reqs[], out getQueueRangeMsgOutput output);

    /**
     * @brief :  消息删除接口
     * @param [in]    qname           : uint64_t      :   消息队列唯一标识名称
     * @param [in]    msg_ids         : uint64_t      :   消息id
     * @param [out]   output          : Output        :   返回值
     **/
    void deleteQueueMsg(string qname, uint64_t msg_ids[], out Output output);

    /**
     * @brief :  消息状态更新接口
     * @param [in]    qname           : uint64_t      :   消息队列唯一标识名称
     * @param [in]    msg_ids         : uint64_t      :   消息id
     * @param [in]    status          : uint32_t      :   消息状态
     * @param [out]   output          : Output        :   返回值
     **/
    void updateQueueMsgStatus(string qname, uint64_t msg_ids[], uint32_t status, out Output output);


    /**
     * @brief :  消息队列最新消息id批量查询接口
     * @param [in]    qnames         : string                 :   消息队列唯一标识名称
     * @param [out]   output         : getQueueLastidOutput   :   返回值
     * @example service.tieba.baidu.com/service/msgpush?method=getQueueLastid&qnames=[]&&ie=utf-8&format=json
     **/
    void getQueueLastid(string qnames[], out getQueueLastidOutput output);
};
