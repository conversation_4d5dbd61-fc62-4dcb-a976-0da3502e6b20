<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR> @date 2013:11:17 23:11:45
 * @version 
 * @structs & methods(copied from idl.)
*/



define("MODULE","dl_schedule_schedule");
class Dl_Schedule_Schedule{

const TIEBA_IM_SCHEDULE_APP = 'msglogic'; //********
const KEY_SPLIT_CHAR = "_";
const JOBID_SUFFIX = 'jobid';
const JOBINFO_SUFFIX = 'jobinfo';
const TOTAL_JOBINFO_SUFFIX = 'totaljobinfo';

const JOBID_BASE = 4000000;
protected static $_conf = null;
protected static $_redis = null;
protected static $_idalloc = null;
	
/**
 * @brief init
 * @return: true if success. false if fail.

**/		
private static function _init(){
    if(self::$_redis == null) {
        self::$_redis = new Bingo_Cache_Redis(self::TIEBA_IM_SCHEDULE_APP);
        if(self::$_redis == false) {
            Lib_Util_Log::warning("init redis init fail:" . var_export(Bd_RalRpc::get_error(), true));
            return false;
        }
    }
//     if(self::$_idalloc == null) {
//         self::$_idalloc = Bd_RalRpc::create('IdAlloc', array(
//             'pid' => 'forum_im_schedule',
//             'tk' => 'forum_im_schedule',
//         ));
//         if(self::$_idalloc == null) {
//             return false;
//         }
//     }

	return true; 
}


private static function _errRet($errno){
    return array(
        'errno' => $errno,
        'errmsg' => Tieba_Error::getErrmsg($errno),
    );
}

public static function preCall($arrInput){
    // pre-call hook
}

public static function postCall($arrInput){
    // post-call hook
}

private static function genKey($arrInput,$suffix){
    return join(self::KEY_SPLIT_CHAR,array($arrInput,$suffix));
}

public static function _firstCheck($file, $pos) {
     if ($pos > 0) {
        fseek($file, $pos-1);
        $temp = fgetc($file); 
        echo $temp;
        if($temp != "\r"|| $temp != "\n") {
           $uid = fgets($file); 
           $pos += strlen($uid);
        }
     }
}

/**
 * NMQ拆分统一收敛添加group_id字段接口
 * add by 易剑东
 * 2014-09-01 17:05:00
 */
public static function nmqSplit($arrNMQInput){
	//目前为止调研统计的是qid，groupId,room_id等
	$arrNMQOutput = $arrNMQInput;
	if( isset($arrNMQInput['group_id']) && intval($arrNMQInput['group_id']) > 0){
		//nothing
	}else if( isset($arrNMQInput['room_id']) ){ 
		$arrNMQOutput['group_id'] = 1; //PC直播所有的走一个nmq机器
	}else if( isset($arrNMQInput['groupId']) && intval($arrNMQInput['groupId']) > 0 ){
		$arrNMQOutput['group_id'] = intval($arrNMQInput['groupId']); 
	}else if( isset($arrNMQInput['qid']) && intval($arrNMQInput['qid']) > 0 ){
		$arrNMQOutput['group_id'] = intval($arrNMQInput['qid']);
	}else{
		$arrNMQOutput['group_id'] = mt_rand(1,3);//如果不符合上述条件，返回一个1~3之间的随机数[包含1，3]
	}
	
	if( $arrNMQOutput['group_id'] == Lib_Util_Const::GROUP_ID_COMMON_MSG ){
		$arrNMQOutput['_partition_key'] = mt_rand(1,3); // 回复 + @ + 粉丝 => 再次拆分 随机
	} else {
		$arrNMQOutput['_partition_key'] = $arrNMQOutput['group_id'];
	}
	return $arrNMQOutput;
}



public static function getUidsFromFile($file_path, $pos, $count, $limit_pos) {
    if( !self::_init() ) {
        Lib_Util_Log::warning('load config fail by getUidsFromFile.filepath:'.$file_path);
        return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
    }
    $file_path = dirname(__FILE__) . '/../../../../'.$file_path;
    $arrUids = array();
    if ($file = fopen($file_path, "r")) {
    //    fseek($file, $pos);
     //   self::_firstCheck($file, $pos);
        if ($pos > 0) {
           fseek($file, $pos-1);
           $temp = fgetc($file); 
           if($temp != "\r"|| $temp != "\n") {
              $uid = fgets($file); 
              $pos += strlen($uid);
           }
        }
        $cur_pos = $pos;
        $feof_flag = 0;
        $temp_count = $count;
        $arrUids = array();
        while($count > 0) { 
            if (feof($file))
                $feof_flag = 1;
            
            if ($feof_flag ==0) {
                $uid = fgets($file); 
                $cur_pos += strlen($uid);
                if ($cur_pos >= $limit_pos) 
                    $feof_flag = 1;

		        $uid = preg_replace('/[\\r\\n ]+/', '', $uid);
                if ($uid != '')
                    $arrUids[] = $uid; 
                $count--;
            }
            if ($feof_flag == 1)
                break;
        } 
        fclose($file);
    } else {
        Lib_Util_Log::warning("open file error. filepath:".$file_path);
        $cur_pos = $pos;
        $feof_flag = 0;
    }
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
        'uids' => $arrUids,
        'pos' => $cur_pos,
        'count' => count($arrUids),
        'feof' => $feof_flag,
	);
    return $arrOutput;
}

public static function getJobInfo($jobid, $jobinfo, $suffix = self::JOBINFO_SUFFIX) {
    if( !self::_init() ) {
        Lib_Util_Log::warning('load config fail by getJobInfo.jobid:['.serialize($jobid).'] jobinfo:['.serialize($jobinfo).']');
        return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
    }
    $key = self::genKey($jobid, $suffix);
    $arrJobInfoInput = array(
        'key'    => strval($key),
        'field' => $jobinfo,
    ); 
    $ret = self::$_redis->HMGET($arrJobInfoInput);
    if(!$ret || $ret['err_no']!=0) {
        Lib_Util_Log::warning("query redis（HMGET） error. ".serialize($ret)."   arrJobInfoInput:".serialize($arrJobInfoInput));
	    return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL); 
    }
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
        'result' => $ret['ret'][$key],
	);
    return $arrOutput;
}

public static function setJobInfo($jobid, $jobinfo, $suffix = self::JOBINFO_SUFFIX) {
    if( !self::_init() ) {
        Lib_Util_Log::warning('load config fail by setJobInfo.jobid:['.serialize($jobid).'] jobinfo:['.serialize($jobinfo).']');
        return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
    }
    $key = self::genKey($jobid, $suffix);
    $arrJobInfoInput = array(
        'key'    => $key,
        'fields' => $jobinfo,
    ); 
    $ret = self::$_redis->HMSET($arrJobInfoInput);
    if(!$ret || $ret['err_no']!=0) {
        Lib_Util_Log::warning("query redis（HMSET） error. ".serialize($ret)."     arrJobInfoInput:".serialize($arrJobInfoInput));
	    return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL); 
    }
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
	);
    return $arrOutput;
}

/**
*为了解决官方吧调用msgpush接口超时问题，批量进行setJobInfo工作
*input:
	$jobInfos 数组，每个元素包含key和fields
	$suffix key的构成元素
*/
public static function setJobInfoBat($jobInfos, $suffix = self::JOBINFO_SUFFIX) {
	if ( !self::_init() ) {
	    Lib_Util_Log::warning('load config fail by setJobInfoBat. jobInfos:['.serialize($jobInfos).']');
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	foreach($jobInfos as $key => $jobInfo) {
		$jobInfos[$key]['key'] = self::genKey($jobInfo['key'] , $suffix);
	}
	$arrJobInfoInput = array(
		'reqs' => $jobInfos ,
	);
	$ret = self::$_redis->HMSET($arrJobInfoInput);
	Lib_Util_Log::pushNotice('HMSET_BAT:', serialize($ret));
	if ( !$ret || $ret['err_no'] != 0 ) {
		Lib_Util_Log::warning("query redis (HMSET) error. ".serialize($ret)."	arrJobInfoInput:".serialize($arrJobInfoInput));
		return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
	}
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
	);
	return $arrOutput ;
}

public static function delJobInfo($jobid) {
    if( !self::_init() ) {
        Lib_Util_Log::warning('load config fail by delJobInfo.jobid:['.serialize($jobid).']');
        return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
    }
    $key = self::genKey($jobid, self::JOBINFO_SUFFIX);
    $arrJobInfoInput = array(
        'key'    => $key,
    ); 
    $ret = self::$_redis->DEL($arrJobInfoInput);
    if(!$ret || $ret['err_no']!=0) {
        Lib_Util_Log::warning("query redis（DEL） error. ".serialize($ret)."   arrJobInfoInput:".serialize($arrJobInfoInput));
	    return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL); 
    }
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
	);
    return $arrOutput;
}

public static function setJobId($arrJobid, $privilege) {
    if( !self::_init() ) {
        Lib_Util_Log::warning('load config fail by setJobId.arrJobid:['.serialize($arrJobid).'] privilege:['.serialize($privilege).']');
        return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
    }
    $key = self::genKey($privilege, self::JOBID_SUFFIX);
    $arrJobIdInput = array(
        'key'    => $key,
        'members' => $arrJobid,
    );
	Lib_Util_Log::pushNotice('setJobId_value:', serialize($arrJobIdInput));
    $ret = self::$_redis->ZADD($arrJobIdInput);
    if(!$ret || $ret['err_no']!=0) {
        Lib_Util_Log::warning("query redis（ZADD） error. ".serialize($ret)."  arrJobIdInput:".serialize($arrJobIdInput));
	    return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL); 
        return true;
    }
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
	);
	return $arrOutput;
}

public static function getJobId($member, $privilege) {
    if( !self::_init() ) {
        Lib_Util_Log::warning('load config fail by getJobId.member:['.serialize($member).'] privilege:['.serialize($privilege).']');
        return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
    }
    $key = self::genKey($privilege, self::JOBID_SUFFIX);
    $arrJobIdInput = array(
        'key'    => $key,
        'start'  => $start,
        'stop'  => $stop,
    );
    $ret = self::$_redis->ZRANGEWITHSCORE($arrJobIdInput);
    if(!$ret || $ret['err_no']!=0) {
            Lib_Util_Log::warning("query redis（ZRANGEWITHSCORE） error ".serialize($ret).
                    "   arrJobIdInput:".serialize($arrJobIdInput));
	    return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL); 
    }
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
	);
	return $arrOutput;
}

public static function delJobId($member, $privilege) {
    if( !self::_init() ) {
        Lib_Util_Log::warning('load config fail by delJobId.member:['.serialize($member).'] privilege:['.serialize($privilege).']');
        return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
    }
    $key = self::genKey($privilege, self::JOBID_SUFFIX);
    $arrJobIdInput = array(
        'key'    => $key,
        'member'  => $member,
    );
    $ret = self::$_redis->ZREM($arrJobIdInput);
    if(!$ret || $ret['err_no']!=0) {
        Lib_Util_Log::warning("query redis（ZREM） error. ".serialize($ret)."   arrJobIdInput:".serialize($arrJobIdInput));
	    return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL); 
    }
    $deleted = $ret['ret'];
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
        'ret' => $deleted,
	);
	return $arrOutput;
}


public static function delJobIdsByPr($privilege) {
    if( !self::_init() ) {
        Lib_Util_Log::warning('load config fail by delJobIdsByPr. privilege:['.serialize($privilege).']');
        return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
    }
    $key = self::genKey($privilege, self::JOBID_SUFFIX);
    $arrJobIdInput = array(
        'key'    => $key,
        'start'    => '0',
        'stop'    => '-1',
    );
    $ret = self::$_redis->ZREMRANGEBYRANK($arrJobIdInput);
    if(!$ret || $ret['err_no']!=0) {
        Lib_Util_Log::warning("query redis（ZREMRANGEBYRANK） error. ".serialize($ret)."   arrJobIdInput:".serialize($arrJobIdInput));
	    return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL); 
    }
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
	);
	return $arrOutput;
}

public static function getJobIdsByPr($privilege) {
    if( !self::_init() ) {
        Lib_Util_Log::warning('load config fail by getJobIdsByPr.privilege:['.serialize($privilege).']');
        return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
    }
    $key = self::genKey($privilege, self::JOBID_SUFFIX);
    $arrJobIdInput = array(
        'key'    => $key,
        'min'    => 1,
        'max'    => time(),
    );
    $ret = self::$_redis->ZRANGEBYSCORE($arrJobIdInput);
    if(!$ret || $ret['err_no']!=0) {
        Lib_Util_Log::warning("query redis（ZRANGEBYSCORE） error. ".serialize($ret).
            "   arrJobIdInput:".serialize($arrJobIdInput));
	    return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL); 
    }
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
        'gids' => $ret['ret'][$key] ,
	);
	return $arrOutput;
}

/**
 * @brief 分配name对应的新id
 * @arrInput:
 *     string name
 * @return: $arrOutput
 *     uint64_t id
**/
public static function getNewId($arrInput) {
    if(!self::_init()) {
        Lib_Util_Log::warning('load config fail by getNewId.arrInput:['.serialize($arrInput).']');
        return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
    }

    $intId = 0;
//     $ret = self::$_idalloc->incr(array(
//         'name' => 'job_id',
//         'method' => 'default',
//     ));
    $ret = Molib_Util_ImIdAlloc::getNewId(1, 'job_id');
    if (!$ret ||  $ret['error_no'] !== 0){
        Lib_Util_Log::warning("query idalloc error. ".serialize($ret));
        return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
    }
    $intId = intval($ret['id']) + self::JOBID_BASE;

    $error = Tieba_Errcode::ERR_SUCCESS;
    $arrOutput = array(
        'errno' => $error,
        'errmsg' => Tieba_Error::getErrmsg($error),
        'id' => $intId,
    );
    return $arrOutput;
}

public static function getLiveJobId($arrInput) {
    if(!self::_init()) {
        Lib_Util_Log::warning('load config fail by getLiveJobId.arrInput:['.serialize($arrInput).']');
        return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
    }

    $intId = intval(microtime(true) * 10000) - 14000000000000;

    $error = Tieba_Errcode::ERR_SUCCESS;
    $arrOutput = array(
        'errno' => $error,
        'errmsg' => Tieba_Error::getErrmsg($error),
        'id' => $intId,
    );
    return $arrOutput;
}

/**
 * @brief job发送数目incr
 * @arrInput:
 *     string name
 * @return: $arrOutput
 *     uint64_t id
**/
public static function incrTotalJobSendNum($arrInput) {
    if(!self::_init()) {
        Lib_Util_Log::warning('load config fail by incrTotalJobSendNum.arrInput:['.serialize($arrInput).']');
        return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
    }
    $jobid = intval($arrInput['jobid']);
    $num = intval($arrInput['num']);
    $key = self::genKey($jobid, self::TOTAL_JOBINFO_SUFFIX);
    $ipnut = array(
        'key' => $key,
        'field' => 'sendnum',
        'step' => $num,
    );
    $ret = self::$_redis->HINCRBY($ipnut);
    if(!$ret || $ret['err_no']!=0) {
        Lib_Util_Log::warning("query redis（HINCRBY） error. ".serialize($ret)."   arrJobInfoInput:".serialize($ipnut));
	    return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
    }
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
	);
    return $arrOutput;
}

/**
 * @brief job发送数目query
 * @arrInput:
 *     string name
 * @return: $arrOutput
 *     uint64_t id
**/
public static function queryTotalJobSendNum($arrInput) {
    if(!self::_init()) {
        Lib_Util_Log::warning('load config fail by queryTotalJobSendNum.arrInput:['.serialize($arrInput).']');
        return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
    }
    $jobid = intval($arrInput['jobid']);
    $key = self::genKey($jobid, self::TOTAL_JOBINFO_SUFFIX);
    $ipnut = array(
        'key' => $key,
        'field' => 'sendnum',
    );
    $ret = self::$_redis->HGET($ipnut);
    if(!$ret || $ret['err_no']!=0) {
        Lib_Util_Log::warning("query redis（HINCRBY） error. ".serialize($ret)."   arrJobInfoInput:".serialize($ipnut));
	    return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
    }
    $num = $ret['ret'][$key][0];
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
        'sendnum' => $num,
	);
    return $arrOutput;
}


}
