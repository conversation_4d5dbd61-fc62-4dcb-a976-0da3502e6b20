<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013/11/21 14:58:22
 * @version 
*/

define('BINGO_ENCODE_LANG', 'UTF-8');
define("MODULE","msgstore_service");
class Service_Msgstore_Msgstore {
    
    const SERVICE_NAME = "Service_Msgstore";
    const MAX_BATCH_SIZE = 50;
    const QUEUE_DELIMITER = ":";
    protected static $_conf = null;
    static $service_ie='utf-8';
        
    /**
     * @brief init
     * @return: true if success. false if fail.
    
    **/        
    private static function _init(){
        
        //add init code here. init will be called at every public function beginning.
        //not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
        //init should be recalled for many times.
        
        if(self::$_conf == null){    
            self::$_conf = Bd_Conf::getConf("/app/msgpush/service_msgstore_msgstore");
            if(self::$_conf == false){
                Lib_Util_Log::warning("init get conf fail.");
                return false;
            }
            
        }
        return true; 
    }
    
    /**
     * @param array - input param
     * @return array - arroutput
     **/
    private static function _errRet($errno,$data=array()){
        return array(
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data'   => $data,
        );
    }
    
    /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function pushQueueMsg($arrInput) {
        if(!self::_init()){
            Lib_Util_Log::warning('Service_Msgstore_Msgstore init fail in pushQueueMsg.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        $arrRes = Dl_Msgstore_Msgstore::pushQueueMsg($arrInput);
        if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['result'])) {
            Lib_Util_Log::fatal("Dl_Msgstore_Msgstore::pushQueueMsg failed. [".serialize($arrInput)."]".serialize($arrRes));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'result' => $arrRes['result'],
        );
        return $arrOutput;
    }
    /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function pushQueueMsgId($arrInput) {
        if(!self::_init()){
            Lib_Util_Log::warning('Service_Msgstore_Msgstore init fail in pushQueueMsgId.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        //写数据到拉链
        $arrRes = Dl_Msgstore_Logic_Msgstore::pushQueueMsgId($arrInput);
        if (!isset($arrRes['errno']) || $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::fatal('Dl_Msgstore_Logic_Msgstore::pushQueueMsgId failed.arrInput:['.serialize($arrInput).'] arrRes:['.serialize($arrRes).']');
            return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }
    /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function pushQueueMsgIds($arrInput) {
        if(!self::_init()){
            Lib_Util_Log::warning('Service_Msgstore_Msgstore init fail in pushQueueMsgIds.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        //写数据到拉链
        $arrRes = Dl_Msgstore_Logic_Msgstore::pushQueueMsgIds($arrInput);
        if (!isset($arrRes['errno']) || $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::fatal('Dl_Msgstore_Logic_Msgstore::pushQueueMsgId failed.arrInput:['.serialize($arrInput).'] arrRes:['.serialize($arrRes).']');
            return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }

    /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function queryMsgLinkCount($arrInput) {
        if(!self::_init()){
            Lib_Util_Log::warning('Service_Msgstore_Msgstore init fail in queryMsgLinkCount.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        //写数据到拉链
        $arrRes = Dl_Msgstore_Logic_Msgstore::queryMsgLinkCount($arrInput);
        if (!isset($arrRes['errno']) || 
            $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS || 
            !isset($arrRes['data'])) {
            Lib_Util_Log::fatal('Dl_Msgstore_Logic_Msgstore::queryMsgLinkCount failed.arrInput:['.serialize($arrInput).'] arrRes:['.serialize($arrRes).']');
            return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $arrRes['data'],
        );
        return $arrOutput;
    }

    /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function updateLastMsgIds($arrInput) {
        if(!self::_init()){
            Lib_Util_Log::warning('Service_Msgstore_Msgstore init fail in updateLastMsgIds .arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        //写数据到拉链
        $arrRes = Dl_Msgstore_Logic_Msgstore::updateLastMsgIds($arrInput);
        if (!isset($arrRes['errno']) || $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::fatal('Dl_Msgstore_Logic_Msgstore::updateLastMsgIds failed.arrInput:['.serialize($arrInput).'] arrRes:['.serialize($arrRes).']');
            return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL); 
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }

    /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function storeMsgBody($arrInput) {
        if(!self::_init()){
            Lib_Util_Log::warning('Service_Msgstore_Msgstore init fail in storeMsgBody.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        $arrRes = Dl_Msgstore_Msgstore::storeMsgBody($arrInput);
        if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['result'])) {
            Lib_Util_Log::fatal("Dl_Msgstore_Msgstore::pushQueueMsg failed. [".serialize($arrInput)."]".serialize($arrRes));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'result' => $arrRes['result'],
        );
        return $arrOutput;
    }
    
    /**
    * @param array - arrinput
    * @return array - arroutput
    *
    */
    public static function storeJobMsgBody($arrInput) {
        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        $arrRes = Dl_Msgstore_Msgstore::storeJobMsgBody($arrInput);
        if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['result'])) {
            Lib_Util_Log::warning("Dl_Msgstore_Msgstore::storeJobMsgBody failed. [".serialize($arrInput)."]".serialize($arrRes));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'result' => $arrRes['result'],
        );
        return $arrOutput;
    }
    /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function getQueueLastid($arrInput) {
        if(!self::_init()){
            Lib_Util_Log::warning('Service_Msgstore_Msgstore init fail in getQueueLastid.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        $arrRes = Dl_Msgstore_Msgstore::getQueueLastid($arrInput);
        if( !isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['msg_ids'])) {
            Lib_Util_Log::fatal("Dl_Msgstore_Msgstore::getQueueLastid failed. [".serialize($arrInput)."]".serialize($arrRes));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'msg_ids' => $arrRes['msg_ids'],
        );
        return $arrOutput;
    }

    /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function getQueueLastidEx($arrInput) {
        if(!self::_init()){
            Lib_Util_Log::warning('Service_Msgstore_Msgstore init fail in getQueueLastidEx.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        $arrRes = Dl_Msgstore_Msgstore::getQueueLastidEx($arrInput);
        if( !isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['msg_ids'])) {
            Lib_Util_Log::fatal("Dl_Msgstore_Msgstore::getQueueLastidEx failed. [".serialize($arrInput)."]".serialize($arrRes));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'msg_ids' => $arrRes['msg_ids'],
        );
        return $arrOutput;
    }

    /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function truncateMsgLink($arrInput) {
        if(!self::_init()){
            Lib_Util_Log::warning('Service_Msgstore_Msgstore init fail in truncateMsgLink. arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        $arrRes = Dl_Msgstore_Msgstore::truncateMsgLink($arrInput);
        if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::fatal("Dl_Msgstore_Msgstore::truncateMsgLink failed. [".serialize($arrInput)."]".serialize($arrRes));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }

    /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function loadMsgChain($arrInput) {
        if (!isset($arrInput['app_id']) || !isset($arrInput['reqs'])) {
            Lib_Util_Log::warning("loadMsgChain params invalid. arrInput:[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }    

        if(!self::_init()){
            Lib_Util_Log::warning('init fail in loadMsgChain.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }    

        $appId = intval($arrInput['app_id']);
        $reqs = Tieba_Service::getArrayParams($arrInput, 'reqs');
        if (count($reqs) > self::MAX_BATCH_SIZE) {
            $reqs = array_slice($reqs, 0,  self::MAX_BATCH_SIZE);
        }    

        $arrQnames = array();
        foreach($reqs as $value) {
            $arrQnames[] = self::_getQname($appId, $value['space'], $value['qid']);
        } 
        $arrReq = array(
            'qnames' => $arrQnames,
        );
        $arrRes = Dl_Msgstore_Msgstore::loadMsgChain($arrReq);
        if( !isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::fatal("Dl_Msgstore_Msgstore::loadMsgChain failed. [".serialize($arrReq)."]".serialize($arrRes));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }
    
    /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function dumpMsgChain($arrInput) {
        if (!isset($arrInput['app_id']) || !isset($arrInput['reqs'])) {
            Lib_Util_Log::warning("dumpMsgChain params invalid. arrInput:[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }    

        if(!self::_init()){
            Lib_Util_Log::warning('init fail in dumpMsgChain.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }    

        $appId = intval($arrInput['app_id']);
        $reqs = Tieba_Service::getArrayParams($arrInput, 'reqs');
        if (count($reqs) > self::MAX_BATCH_SIZE) {
            $reqs = array_slice($reqs, 0,  self::MAX_BATCH_SIZE);
        }    

        $arrQnames = array();
        foreach($reqs as $value) {
            $arrQnames[] = self::_getQname($appId, $value['space'], $value['qid']);
        } 
        $arrReq = array(
            'qnames' => $arrQnames,
        );
        $arrRes = Dl_Msgstore_Msgstore::dumpMsgChain($arrReq);
        if( !isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::fatal("Dl_Msgstore_Msgstore::dumpMsgChain failed. [".serialize($arrReq)."]".serialize($arrRes));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }

    /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function delMsgChainByMsgId($arrInput) {
        if (!isset($arrInput['app_id']) || !isset($arrInput['reqs'])) {
            Lib_Util_Log::warning("dumpMsgChain params invalid. arrInput:[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if(!self::_init()){
            Lib_Util_Log::warning('init fail in dumpMsgChain.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $appId = intval($arrInput['app_id']);
        $reqs = Tieba_Service::getArrayParams($arrInput, 'reqs');
        if (count($reqs) > self::MAX_BATCH_SIZE) {
            $reqs = array_slice($reqs, 0,  self::MAX_BATCH_SIZE);
        }

        $arrDelParams = array();
        foreach($reqs as $value) {
            $arrDelParam = array();
            $arrDelParam['qname'] = self::_getQname($appId, $value['space'], $value['qid']);
            $arrDelParam['min_id'] = $value['min_id'];
            $arrDelParam['max_id'] = $value['max_id'];
            $arrDelParams[] = $arrDelParam;
        }
        $arrReq = array(
            'reqs' => $arrDelParams,
        );
        $arrRes = Dl_Msgstore_Msgstore::delMsgChainByMsgId($arrReq);
        if( !isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::fatal("Dl_Msgstore_Msgstore::dumpMsgChain failed. [".serialize($arrReq)."]".serialize($arrRes));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }
    
    /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function getQueueMsgByTime($arrInput){
        if(!self::_init()){
            Lib_Util_Log::warning('Service_Msgstore_Msgstore init fail in getQueueMsgByTime.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $arrRes = Dl_Msgstore_Msgstore::getMsgByTime($arrInput);
        if(!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['msg_list'])){
            Lib_Util_Log::fatal("getQueueMsgByTime call dl fail.arrInput:[".serialize($arrInput)."] arrRes:[".serialize($arrRes)."]");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'msg_list' => $arrRes['msg_list'],
        );

        return $arrOutput;	  
    }
   
    /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function getQueAllMsg($arrInput) {
        if(!self::_init()){
            Lib_Util_Log::warning('Service_Msgstore_Msgstore init fail in getQueAllMsg.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        $arrRes = Dl_Msgstore_Msgstore::getQueAllMsg($arrInput);
        if( !isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['msg_list'])) {
            Lib_Util_Log::fatal("Dl_Msgstore_Msgstore::getQueAllMsg failed. [".serialize($arrInput)."]".serialize($arrRes));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'msg_list' => $arrRes['msg_list'],
        );
        return $arrOutput;
    }
    /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function getQueueRangeMsg($arrInput) {
        if(!self::_init()){
            Lib_Util_Log::warning('Service_Msgstore_Msgstore init fail in getQueueRangeMsg.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        if(Lib_Util_Const::UPGRADE_MSG_CHAIN == 0){
            $arrRes = Dl_Msgstore_Msgstore::getQueueRangeMsg($arrInput);
        }
        else{
            $arrRes = Dl_Msgstore_Msgstore::getQueueRangeMsgEx($arrInput);
        }
        if( !isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['msg_list'])) {
            Lib_Util_Log::fatal("Dl_Msgstore_Msgstore::getQueueRangeMsg failed. [".serialize($arrInput)."]".serialize($arrRes));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'msg_list' => $arrRes['msg_list'],
        );
        return $arrOutput;
    }
    /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function deleteQueueMsg($arrInput) {
        if (!self::_init()) {
            Lib_Util_Log::warning('Service_Msgstore_Msgstore init fail in deleteQueueMsg.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        $arrRes = Dl_Msgstore_Msgstore::deleteQueueMsg($arrInput);
        if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::fatal("Dl_Msgstore_Msgstore::deleteQueueMsg failed. [".serialize($arrInput)."]".serialize($arrRes));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }
    /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function updateQueueMsgStatus($arrInput) {
        if (!self::_init()) {
            Lib_Util_Log::warning('Service_Msgstore_Msgstore init fail in updateQueueMsgStatus.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        $arrRes = Dl_Msgstore_Msgstore::updateQueueMsgStatus($arrInput);
        if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::fatal("Dl_Msgstore_Msgstore::updateQueueMsgStatus failed. [".serialize($arrInput)."]".serialize($arrRes));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }
    /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function setPmsgReplyStatus($arrInput){
        if(!self::_init()){
            Lib_Util_Log::warning('Service_Msgstore_Msgstore init fail in setPmsgReplyStatus.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        $arrRes = Dl_Msgstore_Msgstore::getPmsgReplyStatus($arrInput);
        if(!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || empty($arrRes['status'])){
            Lib_Util_Log::fatal("setPmsgReplyStatus call getReplyStatus failed.arrInput:[".serialize($arrInput)."] arrRes:[".serialize($arrRes)."]");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        
        $oldStatus = $arrRes['status'];
        $bit = intval($arrInput['bit']);
        $newBitvalue = intval($arrInput['status']);
        $partId = $arrInput['partId'];
        $qname = $arrInput['qname'];
        foreach($oldStatus as $msgId => $msgStatus){
            $tmpstatus = intval($msgStatus);
            $newStatus = self::setStatusByBit($tmpstatus, $bit, $newBitvalue);
            $arrInnerParam = array(
                'msg_id' => $msgId,
                'msg_status' => $newStatus,
        	    'qname'   => $qname,
        	    'part_id' => $partId,
            );
        
        
            $arrRes = Dl_Msgstore_Msgstore::setPmsgReplyStatus($arrInnerParam);
            if(!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
                Lib_Util_Log::fatal("setPmsgReplyStatus call setReplyStatus fail.arrInnerParam:[".serialize($arrInnerParam)."] arrRes:[".serialize($arrRes)."]");
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'   => $error,
            'errmsg'  => Tieba_Error::getErrmsg($error),
        );
        
        return $arrOutput;
     }
     /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function setReplyStatus($arrInput){
        if(!self::_init()){
            Lib_Util_Log::warning('Service_Msgstore_Msgstore init fail in setReplyStatus.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        $arrRes = Dl_Msgstore_Msgstore::getReplyStatus($arrInput);
        if(!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || empty($arrRes['status'])){
            Lib_Util_Log::fatal("setReplyStatus call getReplyStatus failed.arrInput:[".serialize($arrInput)."] arrRes:[".serialize($arrRes)."]");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        
        
        $oldStatus = $arrRes['status'];
        $bit = intval($arrInput['bit']);
        $newBitvalue = intval($arrInput['status']);
        foreach($oldStatus as $msgId => $msgStatus){
            $tmpstatus = intval($msgStatus);
            $newStatus = self::setStatusByBit($tmpstatus, $bit, $newBitvalue);
            $arrInnerParam = array(
                'msg_id' => $msgId,
                'msg_status' => $newStatus,
            );
        
        
            $arrRes = Dl_Msgstore_Msgstore::setReplyStatus($arrInnerParam);
            if(!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
                Lib_Util_Log::fatal("setReplyStatus call setReplyStatus fail.arrInnerParam:[".serialize($arrInnerParam)."] arrRes:[".serialize($arrRes)."]");
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'   => $error,
        	'errmsg'  => Tieba_Error::getErrmsg($error),
        );
        
        return $arrOutput;
    }

	/**
     * @param array - input param
     * @return array - arroutput
     **/
    public static  function setStatusByBit($msgStatus , $bit, $newBitvalue){    
    	$bitvalue = 1 << $bit;
    	$ret = intval($msgStatus) | $bitvalue;
        return $ret;
    }

    //写msgid到新存储拉链
	/**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function pushNewQueueMsgId($arrInput) {
    	if(!self::_init()){
    	    Lib_Util_Log::warning('Service_Msgstore_Msgstore init fail in pushNewQueueMsgId.arrInput:['.serialize($arrInput).']');
    		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
    	}
            
        //写数据到拉链
        $arrRes = Dl_Msgstore_Queue_Msgqueue::pushQueueMsg($arrInput);
        if(!isset($arrRes['errno']) || $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS){
    		Lib_Util_Log::fatal('Dl_Msgstore_Queue_Msgqueue::pushQueueMsgId failed.arrInput:['.serialize($arrInput).'] arrRes:['.serialize($arrRes).']');
    	}
    
        return $arrRes;
    }

    // 直播群新logic
    /**
     * @param array - input param
     * @return array - arroutput
     **/
    public static function getNewQueueLastid($arrInput) {
        if(!self::_init()){
            Lib_Util_Log::warning('Service_Msgstore_Msgstore init fail in getNewQueueLastid.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        $arrRes = Dl_Msgstore_Msgstore::getNewQueueLastid($arrInput);
        if( !isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['msg_ids'])) {
            Lib_Util_Log::fatal("Dl_Msgstore_Msgstore::getNewQueueLastid failed. [".serialize($arrInput)."]".serialize($arrRes));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'msg_ids' => $arrRes['msg_ids'],
        );
        return $arrOutput;
    }
	
	//新拉链删除消息
	/**
     * @param array - input param
     * @return array - arroutput
     **/
	public static function deleteNewQueueMsg($arrInput) {
        if (!isset($arrInput['qname']) || !isset($arrInput['msg_ids'])) {
            Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
		
		$arrRes = Dl_Msgstore_Msgstore::deleteNewQueueMsg($arrInput);
		if($arrRes===false || $arrRes['errno']!=Tieba_Errcode::ERR_SUCCESS) {
			Lib_Util_Log::fatal("call deleteNewQueueMsg fail. arrInput:[".serialize($arrInput)." arrRes:[".serialize($arrRes)."]");
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL); 
		}

        //output params.
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'    => $error,
            'errmsg'   => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }
    /**
     * 
     * 临时删除运营消息拉链
     * @param group_id
     * @return
     */
    public static function truncateQueue($arrInput){
        if(!isset($arrInput['qids']) || !isset($arrInput['max_len'])){
            Lib_Util_Log::warning('input params invalid.'.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        $arrQids = explode(',', $arrInput['qids']);
        if(empty($arrQids) || $arrInput['max_len'] < 100){
            Lib_Util_Log::warning('input params invalid.'.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        if(count($arrQids) > 200){
            $arrQids = array_slice($arrQids, 0, 200);
        }
        $arrReq = array(
            'qids' => $arrQids,
            'max_len' => intval($arrInput['max_len']),
        );
        $arrRet = Dl_Msgstore_Logic_Msgstore::truncateQueue($arrReq);
        if (false === $arrRet || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Lib_Util_Log::warning('delete fail.'.serialize($arrRet));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL); 
        }
        return $arrRet;
    }
        
    /**
     * @param array - input param
     * @return array - arroutput
     **/
    private static function _getQname($appId, $space, $qid) {
        return intval($appId). self::QUEUE_DELIMITER . strval($space). self::QUEUE_DELIMITER . intval($qid);
    }


    /**
     * 获取plugin更新全量投放的信息
     *
     *
     * @param $arrInput
     * @return array
     *
     *
     */
    public static function getSendAllUserMsg($arrInput){

        //@todo input参数校验
        $arrRes = array();

        $arrReq['reqs']  = array($arrInput);
//        Lib_Util_Log::warning("getSendAllUserMsg req. [".serialize($arrInput)."]");

        $arrRes = Dl_Msgstore_Logic_Msgstore::getQueueRangeMsg($arrReq);
        if (!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($arrRes['result'])) {
            Lib_Util_Log::warning("getQueueRangeMsg fail. [".serialize($arrRes)."]");
            if (isset($arrRes['errno'])) {
                return self::_errRet($arrRes['errno']);
            } else {
                return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
            }
        }
        if(!empty($arrRes['result'])){
            $queueIdLists = $arrRes['result'];
        }


        $arrMsgId = array();
        foreach ($queueIdLists as $qname => $arrJoinId) {
            foreach ($arrJoinId as $strJoinId) {
                $arrIdTmp = explode('_', $strJoinId);
//                $position = intval($arrInput['user_id']) % 10  ;
                $position = 0;
                $arrMsgId[$qname][] = $arrIdTmp[$position];
            }
        }
        $msgInfoArr = array();


        if (!empty($arrMsgId)) {
            foreach ($arrMsgId as $qname => $idList) {
                $arrMsgRes = Dl_Msgstore_Logic_Msgstore::getMultiMsg($idList);
                if (!isset($arrMsgRes['errno']) || $arrMsgRes['errno'] != Tieba_Errcode::ERR_SUCCESS ) {
                    Lib_Util_Log::warning("get multimsg fail. [" . serialize($arrMsgRes) . "]");
                    continue;
                }

                foreach($arrMsgRes['result'] as $msgId => $arrMsgContent){
                    $intMsgId = intval($msgId);
                    if (!isset($arrMsgContent['msg_tag']) || empty($arrMsgContent['msg_tag'])) {
                        $arrMsgContent['msg_tag'] = $arrMsgContent['user_id'] . ":" . $arrInput['user_id'];
                    }

                    $currGroupId = ($arrInput['qname'] == $qname) ? $arrInput['group_id'] : '';

                    $arrContent = json_decode($arrMsgContent['content'], true);

                    if($intMsgId >= 172968277852){
                        $msgInfoArr[$qname][] =  array (
                            'msg_id'   => $intMsgId,
                            'msg_type' => $arrMsgContent['msg_type'],
                            'user_id'  => $arrMsgContent['user_id'],
                            'content'  => $arrMsgContent['content'],
                            'duration' => 0,
                            'create_time' => $arrMsgContent['create_time'],
                            'status' => $arrMsgContent['status'],
                            'seq_id' => 0,
                            'msg_tag' => $arrMsgContent['msg_tag'],
                            'op_flag' => '',
                            'client_type'    => $arrMsgContent['client_type'],
                            'client_version' => $arrContent['userMsg']['client_version'],
                            'task_id' => '',
                            'groupId' => $currGroupId,
                            'recordId' => 0,
                        );
                    }

                    unset($intMsgId);
                    unset($currGroupId);

                }
            }
        }
//        Lib_Util_Log::warning("get multimsg res. [" . serialize($msgInfoArr) . "]");

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'msg_list' => $msgInfoArr,
        );
        return $arrOutput;

    }
}
