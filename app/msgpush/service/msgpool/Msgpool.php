<?php

define('BINGO_ENCODE_LANG', 'UTF-8');
class Service_Msgpool_Msgpool{
    protected static $_redis = null;
    protected static $_conf = null;
	const SERVICE_NAME = 'Service_Msgpool';
	const MSGPOOL_STORE  = 'msgpool';
	//const CACHE_SERVICE_NAME = 'forum_transform';
	
	/**
	 * @brief init
	 * @return: true if success. false if fail.
	**/
	private static function _init() {
        if(self::$_redis == null) {
            self::$_redis = new Bingo_Cache_Redis(self::MSGPOOL_STORE);
            if(self::$_redis == false) {
                Lib_Util_Log::warning("init redis init fail:" . serialize(Bd_RalRpc::get_error()));
                return false;
            }
        }
        
	    if(self::$_conf == null) {
	        self::$_conf = Bd_Conf::getConf('/app/msgpush/service_msgpool_msgpool');
	        if(self::$_conf == false) {
	            Lib_Util_Log::warning("init get conf fail.");
	            return false;
	        }
	    }
	    return true;
	}
	/**
	 * @brief errRet
	 * @return: errno and errmsg
	**/
	private static function _errRet($errno) {
	    $errmsg = Tieba_Error::getErrmsg($errno);
	    Bingo_Log::pushNotice('errno', $errno);
	    Bingo_Log::pushNotice('errmsg', $errmsg);
	    return array(
	        'errno' => $errno,
	        'errmsg' => $errmsg,
	    );
	}
     
	public static function getAllMessage($arrInput){
	    if (!isset($arrInput['uid'])){
	        Lib_Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    	return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	    }
	    $uid = intval($arrInput['uid']);
	    $key = strval($uid);
        if (!self::_init()) {
            Lib_Util_Log::warning('Service_Msgpool_Msgpool init fail in getAllMessage.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        Lib_Util_Log::pushNotice('get_data_uid', $uid);
        
        $arrRedisInput = array(
            'key' => $key,
        );
        $ret = self::$_redis->HGETALL($arrRedisInput);
        if (!$ret || $ret['err_no'] != 0) { 
            Lib_Util_Log::fatal("query redis error. ret:[".serialize($ret)."] arrRedisInput:[".serialize($arrRedisInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
        }
        
        $arrTmp = array();
        for ($i = 1; $i <= 23; $i++) {
            $arrTmp[$i - 1] = 0;
        }
        $arrMessage = array();
        if (isset($ret['ret'][$key])) {
            $arrValues = $ret['ret'][$key];
            foreach ($arrValues as $value) {
                $type = intval($value['field']);
                $msgNum = intval($value['value']);
                $arrTmp[$type - 1] = $msgNum;
            }
            //Lib_Util_Log::pushNotice('get_data_ret', serialize($arrValues));
            $arrMessage['fans'] = $arrTmp[0];
            $arrMessage['evaluate'] = $arrTmp[1];
            $arrMessage['money'] = $arrTmp[2];
            $arrMessage['replyme'] = $arrTmp[3];
            $arrMessage['feature'] = $arrTmp[4];
            $arrMessage['guess'] = $arrTmp[5]; //6呢？
            $arrMessage['anti'] = $arrTmp[7];
            $arrMessage['atme'] = $arrTmp[8];
            $arrMessage['recycle'] = $arrTmp[9];
            $arrMessage['zan'] = $arrTmp[19];
            $arrMessage['friend'] = $arrTmp[21];
            $arrMessage['gift'] = $arrTmp[22];
        }
        Lib_Util_Log::pushNotice('arrMsg_replyme', $arrMessage['replyme']);
        Lib_Util_Log::pushNotice('arrMsg_zan', $arrMessage['zan']);
	   	$error = Tieba_Errcode::ERR_SUCCESS;
    	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
    	    'message' => $arrMessage,
    	);
	    return $arrOutput;
	}
}
