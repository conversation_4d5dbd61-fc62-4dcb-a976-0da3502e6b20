<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR> @date 2013:11:13 14:19:09
 * @version 
 * @structs & methods(copied from idl.)
*/
class Service_Schedule_Logic_JobHandler {
    protected static $_conf = null;
    protected static $_broadcast_chunknum = 30;
    protected static $_multicast_chunknum = 10;
    protected static $_fromid_chunknum = 10;
    protected static $_groupcast_chunknum = 100;
    
    protected static $_transform_chunksize = 200;

    protected static $privilegeQueueLength = 500;
    
    private static function _errRet($errno) {
        Lib_Util_Log::pushNotice('errno', $errno);
        Lib_Util_Log::pushNotice('errmsg', Tieba_Error::getErrmsg($errno));
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }
    
    public static function preCall($arrInput) {
        
    }
    
    public static function postCall($arrInput) {
        
    }
    
    /**
     * @brief init
     * @return: true if success. false if fail.
     **/
    private static function _init() {
        if (self::$_conf == null) {
            self::$_conf = Bd_Conf::getConf('/app/msgpush/service_schedule_schedule');
            if (self::$_conf == false) {
                Lib_Util_Log::warning('init get conf fail.');
                return false;
            }
        }
        self::_getParams();
        return true;
    }
    
    private static function _getParams() {
        if (isset(self::$_conf['schedule_job']['broadcast_chunknum']) && intval(self::$_conf['schedule_job']['broadcast_chunknum']) > 0) {
            self::$_broadcast_chunknum = intval(self::$_conf['schedule_job']['broadcast_chunknum']);
        }
        if (isset(self::$_conf['schedule_job']['multicast_chunknum']) && intval(self::$_conf['schedule_job']['multicast_chunknum']) > 0) {
            self::$_multicast_chunknum = intval(self::$_conf['schedule_job']['multicast_chunknum']);
        }
        if (isset(self::$_conf['schedule_job']['fromid_chunknum']) && intval(self::$_conf['schedule_job']['fromid_chunknum']) > 0) {
            self::$_fromid_chunknum = intval(self::$_conf['schedule_job']['fromid_chunknum']);
        }
        
        Lib_Util_Log::debug('_getParam   broadcast_chunknum:' . self::$_broadcast_chunknum . '    multicast_chunknum:' . self::$_multicast_chunknum);
    }
    
    /**
     * @brief
     * @arrInput:
     * 	BroadcastJob job
     * @return: $arrOutput
     * 	uint32_t result
    **/
    public static function getJobStatusByJobid($jobid) {
        if (!self::_init()) {
            Lib_Util_Log::warning('init fail in getJobStatusByJobid.jobid:['.serialize($jobid).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        $jobType = $job['type'];
        $arrJobInfo = array('pos');
        $arrResJobInfo = Dl_Schedule_Schedule::getJobInfo($jobid, $arrJobInfo);
        if ($arrResJobInfo == false || Tieba_Errcode::ERR_SUCCESS != $arrResJobInfo['errno']) {
            Lib_Util_Log::fatal("Dl_Schedule_Schedule::getJobInfo    fail!    arrResJobInfo:".serialize($arrResJobInfo)." jobid:[".serialize($jobid)."] arrJobInfo:[".serialize($arrJobInfo)."]");
            return self::_errRet($arrResJobInfo['errno']);
        }
        if (!isset($arrResJobInfo['ret'][$key])) {
            $jobStatus = 2;
        } else {
            $jobPos = $arrResJobInfo['ret'][$key][0];
            if ($jobPos == 0) {
                $jobStatus = 0;
            } else {
                $jobStatus = 1;
            }
        }
        
        $job_status = $arrResJobInfo['ret'][$key][0];
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'result' => $jobStatus,
        );
        return $arrOutput;
    }
    
    /**
     * @brief
     * @arrInput:
     * 	BroadcastJob job
     * @return: $arrOutput
     * 	uint32_t result
    **/
    public static function cancelJobsByJobid($jobid) {
        if (!self::_init()) {
            Lib_Util_Log::warning('init fail in cancelJobsByJobid.jobid:['.serialize($jobid).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        $jobType = $job['type'];
        $arrJobInfo = array('privilege');
        $arrResJobInfo = Dl_Schedule_Schedule::getJobInfo($jobid, $arrJobInfo);
        if ($arrResJobInfo == false || Tieba_Errcode::ERR_SUCCESS != $arrResJobInfo['errno']) {
            Lib_Util_Log::fatal("Dl_Schedule_Schedule::getJobInfo    fail!    arrResJobInfo:".serialize($arrResJobInfo)." jobid:[".serialize($jobid)."] arrJobInfo:[".serialize($arrJobInfo)."]");
            return self::_errRet($arrResJobInfo['errno']);
        }
        $jobPrivilege = $arrResJobInfo['ret'][$key][0];
        
        $count = 1;
        if ($jobType === Service_Schedule_Logic_Const::$send_type_multi) {
            $count = self::_multicast_chunknum;
        } else {
            if ($jobType === Service_Schedule_Logic_Const::$send_type_broad) {
                $count = self::_broadcast_chunknum;
            }
        }
        
        for ($i = 0; $i < $count; $i++) {
            $arrSubjobid[] = $jobid * Service_Schedule_Logic_Const::$subjob_size + $i;
        }
        $arrResJobId = Dl_Schedule_Schedule::delJobId($arrSubjobid, $jobPrivilege);
        if ($arrResJobId == false || Tieba_Errcode::ERR_SUCCESS != $arrResJobId['errno']) {
            Lib_Util_Log::fatal("Dl_Schedule_Schedule::delJobId fail! arrResJobId:".serialize($arrResJobId)." arrSubjobid:[".serialize($arrSubjobid)."] arrJobInfo:[".serialize(jobPrivilege)."]");
            return self::_errRet($arrResJobInfo['errno']);
        }
        
        foreach ($arrSubjobid as $subJobid) {
            $arrResJobInfo = Dl_Schedule_Schedule::delJobInfo($subJobid);
            if ($arrRet == false || Tieba_Errcode::ERR_SUCCESS != $arrResJobInfo['errno']) {
                Lib_Util_Log::fatal("Dl_Schedule_Schedule::delJobInfo fail!     arrResJobId:".serialize($arrResJobId)." subJobid:[".serialize($subJobid)."]");
                continue;
            }
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'result' => $job_status,
        );
        return $arrOutput;
    }
    
    public static function delJobInfoByJobid($arrInput) {
        if (!self::_init()) {
            Lib_Util_Log::warning('init fail in delJobInfoByJobid.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        if (!isset($arrInput['jobids'])) {
            Lib_Util_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrFailedJobid = array();
        $arrSubjobid = json_decode($arrInput['jobids']);
        foreach ($arrSubjobid as $subJobid) {
            $arrResJobInfo = Dl_Schedule_Schedule::delJobInfo($subJobid);
            if ($arrRet == false || Tieba_Errcode::ERR_SUCCESS != $arrResJobInfo['errno']) {
                Lib_Util_Log::fatal('Dl_Schedule_Schedule::delJobInfo fail!     arrResJobId:' . serialize($arrResJobId));
                $arrFailedJobid[] = $subJobid;
                continue;
            }
        }
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'result' => $arrFailedJobid,
        );
        return $arrOutput;
    }
    
    private static function _getChunkInfo($type, $arrInput) {
        $start_pos = intval($arrInput['start_pos']);
        $arrChunksInfo = array();
        if ($type === Service_Schedule_Logic_Const::$send_type_multi) {
            //$arrInput['filepath'] = dirname(__FILE__) . '/../../../../../' . $arrInput['filepath'];
            $filepath = dirname(__FILE__) . '/../../../../../' . $arrInput['filepath'];
            Lib_Util_Log::debug('_getChunkInfo  filepath:' . $filepath . '   srcfilepath:' . $arrInput['filepath']);
            $fileSize = intval($arrInput['filesize']);
            if (!file_exists($filepath)) {
                Lib_Util_Log::warning("file not exist!!!!! don't care...." . $arrInput['filepath']);
            } else {
                if ($fileSize == 0) {
                    $fileSize = filesize($filepath);
                }
            }
            
            if ($fileSize > 0) {
                if ($fileSize < 1000) {
                    $chunkCount = 1;
                } else {
                    if ($fileSize < 20000) {
                        $chunkCount = 10;
                    } else {
                        $chunkCount = self::$_multicast_chunknum;
                    }
                }
                
                $chunkSize = intval($fileSize / $chunkCount);
                for ($i = 0; $i < $chunkCount; $i++) {
                    $pos = $i * $chunkSize;
                    if ($i < $chunkCount - 1) {
                        $range = $pos + $chunkSize;
                    } else {
                        if ($i == $chunkCount - 1) {
                            $range = $fileSize;
                        }
                    }
                    $arrChunkInfo = array(
                        'count' => $i,
                        'pos' => $pos,
                        'range' => $range,
                        'index' => $arrInput['filepath'],
                    );
                    $arrChunksInfo[] = $arrChunkInfo;
                }
            }
        } else {
            if ($type === Service_Schedule_Logic_Const::$send_type_broad) {
                for ($i = 0; $i < self::$_broadcast_chunknum; $i++) {
                    $arrChunkInfo = array(
                        'count' => $i,
                        'pos' => $start_pos,
                        'range' => self::$_broadcast_chunknum,
                        'index' => $i,
                    );
                    $arrChunksInfo[] = $arrChunkInfo;
                }
            } else {
                if ($type === Service_Schedule_Logic_Const::$send_type_group) {
                    for ($i = 0; $i < self::$_groupcast_chunknum; $i++) {
                        $arrChunkInfo = array(
                            'count' => $i,
                            'pos' => $start_pos,
                            'range' => self::$_groupcast_chunknum,
                            'index' => $i,
                        );
                        $arrChunksInfo[] = $arrChunkInfo;
                    }
                } else {
                    if ($type === Service_Schedule_Logic_Const::$send_type_fromid) {
                        for ($i = 0; $i < self::$_fromid_chunknum; $i++) {
                            $arrChunkInfo = array(
                                'count' => $i,
                                'pos' => $start_pos,
                                'range' => self::$_fromid_chunknum,
                                'index' => $i,
                            );
                            $arrChunksInfo[] = $arrChunkInfo;
                        }
                    } else {
                        if ($type === Service_Schedule_Logic_Const::$send_type_custom) {
                            $arrChunkInfo = array(
                                'count' => 0,
                                'pos' => 0,
                                'range' => 0,
                                'index' => 0,
                            );
                            $arrChunksInfo[] = $arrChunkInfo;
                        }
                    }
                }
            }
        }
        return $arrChunksInfo;
    }
    
    /**
     * @brief
     * @arrInput:
     * 	BroadcastJob job
     * @return: $arrOutput
     * 	uint32_t result
    **/
    public static function addTimingMsgJob($type, $arrInput) {
        if (!self::_init()) {
	        Lib_Util_Log::warning('init fail in addTimingMsgJob.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        if (!isset($arrInput['content']) || !isset($arrInput['start_time']) || !isset($arrInput['msg_type'])) {
            Lib_Util_Log::warning("input params invalid. arrInput:[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $score = strtotime($arrInput['start_time']);
        if ($score == false) {
            $score = time();
        }
        
        $privilege = 0;
        if (isset($arrInput['privilege'])) {
            $privilege = intval($arrInput['privilege']);
        }
        
        if(isset($arrInput['from_platform_type'])){
            switch (intval($arrInput['from_platform_type'])){
                case Service_Schedule_Logic_Const::PUSH_MSG_FROM_PLATFORM_TYPE_MIS:
                    $valueRandon = mt_rand(1000, 1999);  //扩大随机数范围，增加均匀分布概率（10~19）
                    $privilege = intval($valueRandon/100);
                    break;
                default:
                    break;
            }
        }
        
        $currJobs = self::getAllJobsByPr($privilege);
        if ($currJobs == false || Tieba_Errcode::ERR_SUCCESS != $currJobs['errno']) {
            Lib_Util_Log::warning('Dl_Schedule_Schedule::getJobIdsByPr  fail!    arrResJobInfo:' . serialize($currJobs).' and input is '.$privilege);
            return self::_errRet($arrResJobInfo['errno']);
        }else{
            $currJobCount = count($currJobs['gids']);
            if ($currJobCount >= self::$privilegeQueueLength) { // 当前任务队列过长，驳回提交
                Lib_Util_Log::warning('Job TOO Much for privilege: -- ' .$privilege.' and current count is -- '.$currJobCount.' and jobid is --'.$arrInput['jobid']);
                return self::_errRet(Tieba_Errcode::ERR_NVOTE_TOO_MANY_ITEMS);
            }
        }

        $arrTotalJobInfo = array(
            array(
                'field' => 'type',
                'value' => $type,
            ),
            array(
                'field' => 'pr',
                'value' => $privilege,
            ),
            array(
                'field' => 'sendnum',
                'value' => 0,
            ),
            array(
                'field' => 'data',
                'value' => serialize($arrInput),
            ),
        );
        $totalJobId = $arrInput['jobid'];
        Bingo_Timer::start('service_logic_addTimingMsg_SetJobInfo');
        //yijiandong 20140716 add
        $ret = Dl_Schedule_Schedule::setJobInfo($totalJobId, $arrTotalJobInfo, 'totaljobinfo');
        Bingo_Timer::end('service_logic_addTimingMsg_SetJobInfo');
        //yijiandong 20140716 add
        if (!$ret || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::warning(" Dl_Schedule_Schedule::setJobInfo   set redis error. totaljobid:".$totalJobId.
                    "     [".serialize($arrTotalJobInfo)."] ret:[".serialize($ret)."]");
        }
        
        Lib_Util_Log::debug('addTimingMsgJob  type:' . $type . '    input:' . serialize($arrInput));
        $arrSubJobid = array();
        Bingo_Timer::start('service_logic_addTimingMsg_getChunkInfo');
        // yijiandong 20140716 add
        $arrChunksInfo = self::_getChunkInfo($type, $arrInput);
        Bingo_Timer::end('service_logic_addTimingMsg_getChunkInfo');
        // yijiandong 20140716 add
        Bingo_Timer::start('service_logic_addTimingMsg_foreach');
        
        //yijiandong 20140716 add
        foreach ($arrChunksInfo as $arrChunkInfo) {
            $subJobid = $arrInput['jobid'] * Service_Schedule_Logic_Const::$subjob_size + $arrChunkInfo['count'];
            Lib_Util_Log::debug(' jobid:' . $arrInput['jobid'] . '  count:' . $arrChunkInfo['count'] . '   ' . serialize($arrChunkInfo) . '  subjobid:' . $subJobid . '  index:' . $arrChunkInfo['index']);
            $jobinfo = array(
                array(
                    'field' => 'type',
                    'value' => $type,
                ),
                array(
                    'field' => 'index',
                    'value' => $arrChunkInfo['index'],
                ),
                array(
                    'field' => 'time',
                    'value' => time(),
                ),
                array(
                    'field' => 'pr',
                    'value' => $privilege,
                ),
                array(
                    'field' => 'pos',
                    'value' => $arrChunkInfo['pos'],
                ),
                array(
                    'field' => 'range',
                    'value' => $arrChunkInfo['range'],
                ),
                array(
                    'field' => 'data',
                    'value' => serialize($arrInput),
                ),
            );
            $ret = Dl_Schedule_Schedule::setJobInfo($subJobid, $jobinfo);
            if (!$ret || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Lib_Util_Log::warning(" Dl_Schedule_Schedule::setJobInfo   set redis error. subjobid:".$subJobid.
                        "     [".serialize($jobinfo)."] ret:[".serialize($ret)."]");
                continue;
            }
            $arrSubJobid[] = array(
                'score' => $score,
                'member' => $subJobid,
            );
        }
        Bingo_Timer::end('service_logic_addTimingMsg_foreach');
        //yijiandong 20140716 add
        if (!empty($arrSubJobid)) {
            $ret = Dl_Schedule_Schedule::setJobId($arrSubJobid, $privilege);
            if (!$ret || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Lib_Util_Log::fatal(' Dl_Schedule_Schedule::setJobId   set redis error. jobid:' . $totalJobId . '     [' . serialize($arrSubJobid) . ']');
                return self::_errRet($ret['errno']);
            }
        }
        Lib_Util_Log::pushNotice('subjobcount', count($arrSubJobid));
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }
    
    /**
     *为了解决官方吧调用msgpush接口超时而临时设立的
     *2014-07-21
    **/
    public static function addTimingMsgJobForPlatForum($type, $arrInput) {
        if (!self::_init()) {
	        Lib_Util_Log::warning('init fail in addTimingMsgJobForPlatForum.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        if (!isset($arrInput['content']) || !isset($arrInput['start_time']) || !isset($arrInput['msg_type'])) {
            Lib_Util_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        $score = strtotime($arrInput['start_time']);
        if ($score == false) {
            $score = time();
        }
        
        $privilege = 0;
        if (isset($arrInput['privilege'])) {
            $privilege = intval($arrInput['privilege']);
        }

        $currJobs = self::getAllJobsByPr($privilege);
        if ($currJobs == false || Tieba_Errcode::ERR_SUCCESS != $currJobs['errno']) {
            Lib_Util_Log::warning('Dl_Schedule_Schedule::getJobIdsByPr  fail!    arrResJobInfo:' . serialize($currJobs).' and input is '.$privilege);
            return self::_errRet($arrResJobInfo['errno']);
        }else{
            $currJobCount = count($currJobs['gids']);
            if ($currJobCount >= self::$privilegeQueueLength) { // 当前任务队列过长，驳回提交
                Lib_Util_Log::warning('Job TOO Much for privilege: -- ' .$privilege.' and current count is -- '.$currJobCount.' and jobid is --'.$arrInput['jobid']);
                return self::_errRet(Tieba_Errcode::ERR_NVOTE_TOO_MANY_ITEMS);
            }
        }
        
        $arrTotalJobInfo = array(
            array(
                'field' => 'type',
                'value' => $type,
            ),
            array(
                'field' => 'pr',
                'value' => $privilege,
            ),
            array(
                'field' => 'sendnum',
                'value' => 0,
            ),
            array(
                'field' => 'data',
                'value' => serialize($arrInput),
            ),
        );
        $totalJobId = $arrInput['jobid'];
        Bingo_Timer::start('service_logic_addTimingMsg_SetJobInfo');
        $ret = Dl_Schedule_Schedule::setJobInfo($totalJobId, $arrTotalJobInfo, 'totaljobinfo');
        Bingo_Timer::end('service_logic_addTimingMsg_SetJobInfo');
        
        if (!$ret || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
		    Lib_Util_Log::warning(" Dl_Schedule_Schedule::setJobInfo	setredis error. total jobid:".$totalJobId.
				"	[".serialize($arrTotalJobInfo)."] ret:[".serialize($ret)."]");
        }
        Lib_Util_Log::debug('addTimingMsgJobForPlatForum type:' . $type . "\tinput:" . serialize($arrInput));
        
        $arrSubJobid = array();
        $arrJobInfo = array();
        $arrChunksInfo = self::_getChunkInfo($type, $arrInput);
        foreach ($arrChunksInfo as $arrChunkInfo) {
            $subJobid = $arrInput['jobid'] * Service_Schedule_Logic_Const::$subjob_size + $arrChunkInfo['count'];
            Lib_Util_Log::debug(' jobid:' . $arrInput['jobid'] . "\tcount:" . $arrChunkInfo['count'] . "\t" . serialize($arrChunkInfo) . "\tsubjobid:" . $subJobid . "\tindex:" . $arrChunkInfo['index']);
            $jobinfo = array(
                array(
                    'field' => 'type',
                    'value' => $type,
                ),
                array(
                    'field' => 'index',
                    'value' => $arrChunkInfo['index'],
                ),
                array(
                    'field' => 'time',
                    'value' => time(),
                ),
                array(
                    'field' => 'pr',
                    'value' => $privilege,
                ),
                array(
                    'field' => 'pos',
                    'value' => $arrChunkInfo['pos'],
                ),
                array(
                    'field' => 'range',
                    'value' => $arrChunkInfo['range'],
                ),
                array(
                    'field' => 'data',
                    'value' => serialize($arrInput),
                ),
            );
            $arrJobInfo[] = array(
                'key' => $subJobid,
                'fields' => $jobinfo,
            );
            $arrSubJobid[] = array(
                'score' => $score,
                'member' => $subJobid,
            );
        }
        Bingo_Timer::start('logic_Schedule_setJobInfoBat');
        $ret = Dl_Schedule_Schedule::setJobInfoBat($arrJobInfo);
        Bingo_Timer::end('logic_Schedule_setJobInfoBat');
        if (!$ret || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
		    Lib_Util_Log::fatal(" Dl_Schedule_Schedule::setJobInfoBat	set redis error. arrJobInfo:[".serialize($arrJobInfo)."] ret:".serialize($ret)."]");
            
            //Lib_Util_Log::warning(" Dl_Schedule_Schedule::setJobInfoBat return value is :[".serialize($ret)."]");
            return self::_errRet($ret['errno']);
        }
        if (!empty($arrSubJobid)) {
            $ret = Dl_Schedule_Schedule::setJobId($arrSubJobid, $privilege);
            if (!$ret || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
	            Lib_Util_Log::fatal("	Dl_Schedule_Schedule::setJobId set redis error. jobid:".$totalJobId." arrSubJobid:[".serialize($arrSubJobid)."] privilege:[".serialize($privilege)."] ret:[".serialize($ret)."]");
                return self::_errRet($ret['errno']);
            }
        }
        
        Lib_Util_Log::pushNotice('sunjobcount:', count($arrSubJobid));
        
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }
    
    public static function rmAllJobsByPr($pr) {
        if (!self::_init()) {
            Lib_Util_Log::warning('init fail in rmAllJobsByPr.pr:['.serialize($pr).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        $arrResJobInfo = Dl_Schedule_Schedule::delJobIdsByPr($pr);
        if ($arrResJobInfo == false || Tieba_Errcode::ERR_SUCCESS != $arrResJobInfo['errno']) {
            Lib_Util_Log::fatal("Dl_Schedule_Schedule::delJobIdsByPr  fail! arrResJobInfo:[".serialize($arrResJobInfo).'] pr:['.serialize($pr).']');
            return self::_errRet($arrResJobInfo['errno']);
        }
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }
    
    public static function getAllJobsByPr($pr) {
        if (!self::_init()) {
	        Lib_Util_Log::warning('init fail in getAllJobsByPr.pr:['.serialize($pr).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        $arrResJobInfo = Dl_Schedule_Schedule::getJobIdsByPr($pr);
        if ($arrResJobInfo == false || Tieba_Errcode::ERR_SUCCESS != $arrResJobInfo['errno']) {
            Lib_Util_Log::fatal("Dl_Schedule_Schedule::getJobIdsByPr  fail! arrResJobInfo:[".serialize($arrResJobInfo)."] pr:[".serialize($pr)."]");
            return self::_errRet($arrResJobInfo['errno']);
        }
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'gids' => $arrResJobInfo['gids'],
        );
        return $arrOutput;
    }
    
    public static function getJobInfoById($arrInput) {
        if (!self::_init()) {
	        Lib_Util_Log::warning('init fail in getJobInfoById.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        if (!isset($arrInput['jobid'])) {
            Lib_Util_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $subJobid = intval($arrInput['jobid']);
        
        $arrJobInfo = array('type', 'index', 'time', 'pr', 'pos', 'range', 'data');
        $arrResJobInfo = Dl_Schedule_Schedule::getJobInfo($subJobid, $arrJobInfo);
        if ($arrResJobInfo == false || Tieba_Errcode::ERR_SUCCESS != $arrResJobInfo['errno']) {
            Lib_Util_Log::fatal("Dl_Schedule_Schedule::getJobInfo ret fail!arrResJobInfo:[".serialize($arrResJobInfo)."] subJobid:".serialize($subJobid)."] arrJobInfo:[".serialize($arrJobInfo)."]");
            return self::_errRet($arrResJobInfo['errno']);
        }
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'jobinfo' => $arrResJobInfo['result'],
            'jobid' => $subJobid,
        );
        return $arrOutput;
    }
    
    public static function getTotalJobInfoById($arrInput) {
        if (!self::_init()) {
	        Lib_Util_Log::warning('init fail in getTotalJobInfoById.arrInput:['.serialize($arrInput).']');
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        
        if (!isset($arrInput['jobid'])) {
            Lib_Util_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $jobid = intval($arrInput['jobid']);
        
        $arrJobInfo = array('type', 'data', 'sendnum', 'pr');
        $arrResJobInfo = Dl_Schedule_Schedule::getJobInfo($jobid, $arrJobInfo, 'totaljobinfo');
        if ($arrResJobInfo == false || Tieba_Errcode::ERR_SUCCESS != $arrResJobInfo['errno']) {
            Lib_Util_Log::fatal("Dl_Schedule_Schedule::getJobInfo ret fail!arrResJobInfo:[".serialize($arrResJobInfo)."] jobid:[".serialize($jobid)."] arrJobInfo:[".serialize($arrJobInfo)."]");
            return self::_errRet($arrResJobInfo['errno']);
        }
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'jobinfo' => $arrResJobInfo['result'],
            'jobid' => $jobid,
        );
        return $arrOutput;
    }
    
    // add by yanbin 2014.2.20
    public static function cancelCustomJobsByJobid($jobid) {
        $subJobId = intval($jobid) * Service_Schedule_Logic_Const::$subjob_size;
        $arrFields = array('type', 'pr');
        $arrResJobInfo = Dl_Schedule_Schedule::getJobInfo($subJobId, $arrFields);
        if ($arrResJobInfo == false || Tieba_Errcode::ERR_SUCCESS != $arrResJobInfo['errno']) {
            Lib_Util_Log::fatal("Dl_Schedule_Schedule::getJobInfo fail!subJobId:[".serialize($subJobId)."] arrFields:[".serialize($arrFields)."] arrResJobInfo:[".serialize($arrResJobInfo)."]");
            return false;
        }
        $jobPrivilege = $arrResJobInfo['result'][1];
        $ret = Dl_Schedule_Schedule::delJobId($subJobId, $jobPrivilege);
        if (!isset($ret['errno']) || $ret['errno'] !== 0) {
            Lib_Util_Log::fatal("Dl_Schedule_Schedule::delJobId fail!subJobId:[".serialize($subJobId)."] jobPrivilege:[".serialize($jobPrivilege)."] ret:[".serialize($ret)."]");
            return false;
        }
        $ret = Dl_Schedule_Schedule::delJobInfo($subJobId);
        if (!isset($ret['errno']) || $ret['errno'] !== 0) {
	        Lib_Util_Log::fatal("Dl_Schedule_Schedule::delJobInfo fail!subJobId:".serialize($subJobId)." ret:[".serialize($ret)."]");
            return false;
        }
        return true;
    }
}

/* vim: set ft=php expandtab ts=4 sw=4 sts=4 tw=0: */
