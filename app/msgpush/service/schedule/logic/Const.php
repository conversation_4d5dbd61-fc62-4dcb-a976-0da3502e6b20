<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Const.php
 * <AUTHOR>
 * @date 2013/11/25 15:12:23
 * @brief 
 *  
 **/

class Service_Schedule_Logic_Const {
    public static $subjob_size = 1000;    //每个任务最多分为1000子任务

    public static $id_type_uid = 1;
    public static $id_type_imei = 2;
    
    public static $send_type_multi = 1;
    public static $send_type_broad = 2;
    public static $send_type_group = 3;
    public static $send_type_custom = 4; //个性化推送
    public static $send_type_fromid = 5; //渠道推送

    public static $term_all = 0;
    public static $term_ios = 1;
    public static $term_android = 2;

    public static $msg_channel_common = 1;
    public static $msg_channel_personal = 2;

    public static $uid_qid = 10;
    public static $cuid_qid = 11;
    
    //推送消息的业务平台来源，如有需要，请顺序增加
    const PUSH_MSG_FROM_PLATFORM_TYPE_MIS = 1;    //来自mis系统的消息推送
}





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
