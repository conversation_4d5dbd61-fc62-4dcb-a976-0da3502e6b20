//@description: register service
//@author: baidu.com

struct SendMsgResult {
	int32_t errno;       // 错误号（0:成功，其它：失败）
	string errmsg;       // 错误信息	
};
struct UserInfo{
	string cuid;  //用户的cuid
	int mask;   //屏蔽状态
	int mask_apns; //apns的屏蔽状态
};

struct ClientInfo {
    string cuid;
    string lcs_ip;
    uint32_t lcs_port;
    uint32_t lcs_fd;
    uint32_t in_time;
    string device;
    string position;
    string lng;
    string lat;
    string client_type;
    string bduss;
    string ios_token;
    uint32_t channel_id;
    uint32_t channel_uid;
    uint32_t user_id;
};


service ConnProxy {
    /**
    * @brief : 更新client信息
    * @param [in]    cuid : string : 端id
    * @param [in]    lcs_ip : string : lcs ip地址
    * @param [in]    lcs_port : uint32_t : lcs 端口
    * @param [in]    lcs_fd : uint32_t : lcs 文件描述符
    * @param [in]    device : string : 端设备类型
    **/
    void updateClientInfo
    (
        string cuid,
        string user_id,
        string lcs_ip,
        uint32_t lcs_port,
        uint32_t lcs_fd,
        string device = optional(),
        string position = optional(),
        string lng = optional(),
        string lat = optional()
        string client_type = optional() ,
        string bduss = optional(), 
        string ios_token = optional() ,
        uint32_t channel_id = optional() ,
        uint32_t channel_uid = optional() ,
    );
    
  /**
   * @brief :  消息推送接口
   * @param [in]	app_id	  : uint32_t	:	产品应用编号
   * @param [in]	cmd   	  : uint32_t	:	命令编号
   * @param [in]	seqid   	 : uint32_t	:	命令编号
   * @param [in]	cuids[]		: uint32_t  :	发送设备编号
   * @param [in]	content 	: string	 :	发送内容（mix array）
   * @param [in]    notify_content : string  :  离线apns推送消息
   * @param [out]	output	  : sendMsg_output   :  返回值(成功：返回空内容，失败返回错误信息)
   * @example service.tieba.baidu.com/service/connproxy?method=pushMsg&app_id=1&cmd=0&cuids=123&content=test&ie=utf-8&format=look
   **/
	void pushMsg(uint32_t app_id, uint32_t cmd, UserInfo cuids[], string content,string abstract, out SendMsgResult  output);

    /**
    * @brief : 更新client信息
    * @param [in]    cuid : string : 端id
    * @param [in]    lcs_ip : string : lcs ip地址
    * @param [in]    lcs_port : uint32_t : lcs 端口
    * @param [in]    lcs_fd : uint32_t : lcs 文件描述符
    * @param [in]    device : string : 端设备类型
    **/
    void updateUserInfo(string cuid, uint_32 user_id, int type);

    void getClientInfo(string cuid, out  ClientInfo client);

    void getClientInfoByCuids(string cuids, out  ClientInfo []client);
    /**
     * @brief : 删除过期的连接
     * @param [in] cuids : string : 要删除的cuid，以逗号分隔
    **/
    void delClientInfo(string cuids);
};
