<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @desc
 * @date 2015:09:10 14:40:02
 * @version 1.0
 */


define ('EASYSCRIPT_DEBUG',true);
define ('EASYSCRIPT_THROW_EXEPTION',true);  

define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../../' );
define ('SCRIPTNAME',basename(__FILE__,".php"));   //
define ('BASEPATH',dirname(__FILE__));
define ('CONFPATH',BASEPATH."/conf");
define ('DATAPATH',BASEPATH."/data");
define ('LOGPATH',BASEPATH."/log");
set_include_path(get_include_path() . PATH_SEPARATOR. BASEPATH.'/../../');

/**
 * auto load php file
 * @param unknown $strClassName
 */
function __autoload($strClassName)
{
    require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');

Lib_Util_Log::notice(SCRIPTNAME.' script begin runing.');
define ('LOG', 'log');
define ('OLD_REDIS_NAME', 'msglogic');
define ('NEW_REDIS_NAME', 'twlive');
try{
    $scriptConf = array(
        'memory_limit' => '10240M',
        'data_path'      => DATAPATH,
        'conf_path'      => CONFPATH,
        'lock_file_name' => SCRIPTNAME.'lock',
        'done_file_name' => SCRIPTNAME.'.done',
        'db_alias' => array(
            'im' => 'forum_livegroup',
        ),
        'conf_alias' => array(
            'main' => 'main.conf',
        ),
    );
    $easyScriptObj = new Lib_Util_EasyScript($scriptConf);
    if( $easyScriptObj->checkScriptIsRuning() === true ){
        Lib_Util_Log::notice(SCRIPTNAME.' script is runing.');
        echo SCRIPTNAME." script is runing\r\n";
        exit;
    }

    Bingo_Log::init(array(
        LOG => array(
            'file'  => LOGPATH ."/". SCRIPTNAME. ".log",
            'level' => 0xFF,
        ),
    ), LOG);
    Lib_Util_Log::notice(SCRIPTNAME.' script begin runing.');
    echo SCRIPTNAME." script begin runing.\r\n";
    
    $old_redis = new Bingo_Cache_Redis(OLD_REDIS_NAME);
    if(null == $old_redis){
        Lib_Util_Log::warning(SCRIPTNAME.'call redis fail.');
        exit;
    }
    $new_redis = new Bingo_Cache_Redis(NEW_REDIS_NAME);
    if(null == $new_redis){
        Lib_Util_Log::warning(SCRIPTNAME.'call redis fail.');
        exit;
    }

    $myfile=fopen('label.txt','r');
    while(!feof($myfile)){
        $intThreadId = strval(trim(fgets($myfile)));
        if ($intThreadId <= 0){
            continue;
        }
        $arrReq = array(
            'key' => $intThreadId . '_livepost_label_hot_suffix',
            'min' => '+INF',
            'max' => '-INF',
            'offset' => 0,
            'count' => 20,
        );
        $arrRes = $old_redis->ZREVRANGEBYSCOREWITHSCORES($arrReq);
        if ($arrRes && $arrRes['err_no'] == 0) {
            $arrAdd = array();
            $reqs = array();
            foreach ($arrRes['ret'][$arrReq['key']] as $item ){
                $arrAdd[] = array(
                    'member' => $item['member'],
                    'score' =>  $item['score'],
                );
                $arrGroup = explode('_',$item['member']);
                if (count($arrGroup) != 2){
                    continue;
                }
                $intLableId = intval($arrGroup[1]);
                if ($intLableId <= 0){
                    continue;
                }
                $reqs[] = array(
                    'key' => $intLableId . '_livepost_label_name_suffix',
                );
            }
            $res = $old_redis->GET(array('reqs' => $reqs,));
            if ($res && $res['err_no'] == 0){
                foreach ($res['ret'] as $key => $value) {
                    $arrSet = array(
                        'key' => $key,
                        'value' => $value,
                    );
                    $ret = $new_redis->SET($arrSet);
                    if (!$ret || $ret['err_no'] != 0){
                        echo $intThreadId ."\n";
                    }
                }
            }
            $ret = $new_redis->ZADD(array('key' => $arrReq['key'],'members' => $arrAdd,));
            if (!$ret || $ret['err_no'] != 0){
                echo $intThreadId ."\n";
            }
        }
    }
    fclose($myfile);
    echo SCRIPTNAME." script end runing.\r\n";
}catch(Exception $e){
    if($easyScriptObj !== false){
        Bingo_Log::warning(SCRIPTNAME.' script run fail!'.$easyScriptObj->getErr2String());
    }else{
        Bingo_Log::warning(SCRIPTNAME.' script run fail![null]');
    }
    Lib_Util_Log::notice(SCRIPTNAME.' run fail. desc:'.$easyScriptObj->getErr2String());
}
Lib_Util_Log::notice(SCRIPTNAME." script stop");
