<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @desc
 * @date 2015:09:10 14:40:02
 * @version 1.0
 */


define ('EASYSCRIPT_DEBUG',false);
define ('EASYSCRIPT_THROW_EXEPTION',false);  

define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../../' );
define ('SCRIPTNAME',basename(__FILE__,".php"));   //
define ('BASEPATH',dirname(__FILE__));
define ('CONFPATH',BASEPATH."/conf");
define ('DATAPATH',BASEPATH."/data");
define ('LOGPATH',BASEPATH."/log");
set_include_path(get_include_path() . PATH_SEPARATOR. BASEPATH.'/../../');

/**
 * auto load php file
 * @param unknown $strClassName
 */
function __autoload($strClassName)
{
    require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');

/**
 * @desc 根据用户等级获取所有用户id及粉丝数
 * @param
 *      int:intLevel 用户等级
 * @return
 */
function getAuthorId($intLevel)
{
    $arrResult = array();
    $index = 0;
    $count = 50;
    $queryCount = 50;
    $retry = 0;
    while($queryCount == $count && $retry <= 3){
        $input = array(
            "user_level" => $intLevel,
            "pn" => $index,
            "rn" => $count,
        );
        $output = Tieba_Service::call("livegroup", "queryAnchorIdsByLevel", $input);
        if ($output['errno'] == 0){
            $arrMget = array();
            $queryCount = count($output['result']);
            $index += $count;
            foreach ($output['result'] as $key => $value) {
                $arrMget[] = $value['anchor_id'];
            }
            $input = array(
                "user_id" => $arrMget,
                "need_follow_info" => 1,
                "need_pass_info" => 0,
                "get_icon" => 0,
            );
            $output = Tieba_Service::call("user", "mgetUserDataEx", $input);
            if ($output['errno'] == 0){
                foreach($output['user_info'] as $userId=>$userInfo){
                    if ($userInfo['followed_count'] >= 1){
                            $arrResult[$userId] = $userInfo['followed_count'];
                    }
                }
            }
        }
        else{
            $retry++;
            sleep(60);
        }
    }
    return $arrResult;
}

Lib_Util_Log::notice(SCRIPTNAME.' script begin runing.');
define ('LOG', 'log');
define ('REDIS_NAME', 'twlive');
try{
    $scriptConf = array(
        'memory_limit' => '10240M',
        'data_path'      => DATAPATH,
        'conf_path'      => CONFPATH,
        'lock_file_name' => SCRIPTNAME.'lock',
        'done_file_name' => SCRIPTNAME.'.done',
        'db_alias' => array(
            'im' => 'forum_livegroup',
        ),
        'conf_alias' => array(
            'main' => 'main.conf',
        ),
    );
    $easyScriptObj = new Lib_Util_EasyScript($scriptConf);
    if( $easyScriptObj->checkScriptIsRuning() === true ){
        Lib_Util_Log::notice(SCRIPTNAME.' script is runing.');
        echo SCRIPTNAME." script is runing\r\n";
        exit;
    }

    Bingo_Log::init(array(
        LOG => array(
            'file'  => LOGPATH ."/". SCRIPTNAME. ".log",
            'level' => 0xFF,
        ),
    ), LOG);
    Lib_Util_Log::notice(SCRIPTNAME.' script begin runing.');
    echo SCRIPTNAME." script begin runing.\r\n";
    
    $redis = new Bingo_Cache_Redis(REDIS_NAME);
    if(null == $redis){
        Lib_Util_Log::warning(SCRIPTNAME.'call redis fail.');
        exit;
    }

    $arrResult = getAuthorId(12);
    arsort($arrResult);
    
    $order = 1;
    $arrMembers = array();
    foreach ($arrResult as $key => $value) {
        if ($order <= 200) {
                $arrMembers[] = array(
                    'score' => $order++,
                    'member' => $key,
                );
        }
        else {
                break;
        }
    }
    //把排名数据存储到redis,如果失败则一直尝试
    $retry = 0;
    if (count($arrMembers) > 0){
        while (($retry++) <= 1000) {
            $arrReq = array(
                'key' => 'lv4_fans_num_pk_result_key',
            );
            $retRedis = $redis->DEL($arrReq);
            //只有原来的排序正常删除后，才添加新的排序规则，否则会相互干扰
            if(null != $retRedis && isset($retRedis['err_no']) && 0 == $retRedis['err_no']){
                $arrReq = array(
                    'key' => 'lv4_fans_num_pk_result_key',
                    'members' => $arrMembers,
                );
                $retRedis = $redis->ZADD($arrReq);
                if(null == $retRedis || !isset($retRedis['err_no']) || 0 != $retRedis['err_no']){
                    //添加失败
                    sleep(1);
                    continue;
                }
                else {
                    $retry = 1001;
                    break;
                }
            }
            else{
                sleep(1);
            }
        }
    }
    echo SCRIPTNAME." script end runing.\r\n";
}catch(Exception $e){
    if($easyScriptObj !== false){
        Bingo_Log::warning(SCRIPTNAME.' script run fail!'.$easyScriptObj->getErr2String());
    }else{
        Bingo_Log::warning(SCRIPTNAME.' script run fail![null]');
    }
    Lib_Util_Log::notice(SCRIPTNAME.' run fail. desc:'.$easyScriptObj->getErr2String());
}
Lib_Util_Log::notice(SCRIPTNAME." script stop");
