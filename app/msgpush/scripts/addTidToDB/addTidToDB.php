<?php
/**
 * 把帖子的tid写到DB和redis 中
* <EMAIL>
* 2015/11/10 11:27
**/

//执行方式 cd $ORP_PATH ;  php app/msgpush/scripts/detectTimingTask.php  调整目录,add by magong<PERSON><PERSON>@baidu.com

define ('EASYSCRIPT_DEBUG',true);          //debug 模式
define ('EASYSCRIPT_THROW_EXEPTION',true);  //抛异常模式

define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../../' );
define ('SCRIPTNAME',basename(__FILE__,".php"));   //定义脚本名
define ('BASEPATH',dirname(__FILE__));
define ('CONFPATH',BASEPATH."/conf");
define ('DATAPATH',BASEPATH."/data");
define ('LOGPATH',"./log/msgpush/scripts/addtidtodb");
set_include_path(get_include_path() . PATH_SEPARATOR. BASEPATH.'/../../');


/**
 * 获取当前毫秒级时间戳
 * @param：null
 * @return：string
*/
function __autoload($strClassName)
{
	require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');

Lib_Util_Log::notice('addtidtodb script begin runing.');

define ('LOG', 'log');
Bingo_Log::init(array(
LOG => array(
'file'  => LOGPATH ."/". SCRIPTNAME. ".log",
'level' => 0xFF,
),
), LOG);

/**
 * 获取当前毫秒级时间戳
 * @param：null
 * @return：string
*/
function microtime_float()
{
	list($usec, $sec) = explode(" ", microtime());
	return ((float)$usec + (float)$sec);
}

$easyScriptObj  = false;
try{
	$ScriptConf = array(
			'memory_limit'   => '1024M',
			'data_path'      => DATAPATH,
			'conf_path'      => CONFPATH,
			'lock_file_name' => SCRIPTNAME.'.lock',
			'done_file_name' => SCRIPTNAME.'.done',
			'db_alias'       => array(
					'im'     => 'forum_livegroup',
			),
			'conf_alias'     => array(
					'main'     => 'main.conf',
			),
	);

	$easyScriptObj = new Lib_Util_EasyScript($ScriptConf);
	//防止脚本重复执行
	if( $easyScriptObj->checkScriptIsRuning() === true ){
		Lib_Util_Log::notice('addtidtodb  script is runing.');
		exit;
	}
	Lib_Util_Log::notice('addtidtodb  script begin runing.');
	$addTidToDB = new addTidToDBAction();
	$addTidToDB->_execute();

}catch(Exception $e){
	if($easyScriptObj !== false){
		Bingo_Log::warning('addtidtodb script run fail!'.$easyScriptObj->getErr2String());
	}else{
		Bingo_Log::warning('addtidtodb script run fail![null]');
	}
	Bingo_Log::notice('addtidtodb run fail.'.$easyScriptObj->getErr2String());
	exit;
}
Bingo_Log::notice('addtidtodb run success.');

class addTidToDBAction {
	private $arrTidCopy =array();
	private $arrTidErr =array();
	private $arrTidMoveErr =array();
	private $arrSuccTid =array();
	private $arrTids=array(1065471540,1615247481,2078577725,2672332267,2757898625,2783845429,2806724835,2816299021,2851014217,2852907194,2914454968,2965335826,3178998652,3373857265,3390298893,3455683483,3557306150,3628088278,3671283497,3679724948,3680298992,3690745849,3710829528,3728970846,3744461435,3753708325,3757157773,3791737069,3795301156,3801092992,3824072805,3841178514,3846530475,3857360499,3872175563,3873346108,3895107375,3907278768,3920394917,3922989311,3925072708,3925073650,3933061101,3935870247,3937903096,3940645702,3943600181,3945376416,3953670785,3953956762,3976082584,3990759258,4016306329,4022474190,4022786743,4029561322,4039019797,4041473334,4052617341,4053772186,4055863268,4062203965,4072201277,4083345659,4084894359,4085715258,4101665906,4102237214,4106197275,4119767359,4121931856,4132329492,4132430700,4132932222,4133922032,4134471429,4137896972,4138821463,4140870870,4140923907,4142160965,4143929886,4145329300,4146203073,4146986431,4147191919,4147414026,4147460806,4147680026,4148426787,4156578040,4156937781,4157128973,4158938383,4159462362,4160036196,4160056121,4161325607,4161369963,4161467574,4161544739,4161660143);
	private $arrTidsTwo=array(4012316499
,3195591831
,3580879724
,3934402718
,4009039699
,4154334573
,3981937255
,4148626244
,4141065572
,4160081882
,4043912646
,3663465843
,3016932633
,4158252374
,4077318426
,4148218290
,3145357847
,4145238968
,3402373141
,2939316073
,3955542368
,4121291967
,4097828350
,3742782125
,2131822373
,3939812401
,4135871497
,4148902914
,4082027116
,4136853997
,3782237915
,1430441926
,3950805188
,4159436413
,3905397447
,3662079842
,4138429262
,4137347903
,2657748563
,4017958202
,4117107060
,4137942428
,4154379033
,3771359667
,4093975810
,4115614134
,4118675551
,4144675212
,4124357289
,4064092288
,4157537269
,3837274402
,3934087839
,1202204131
,2459544065
,3989949886
,3675692329
,3711112783
,3580648794
,3845061548
,4159826419
,3419382236
,4097662063
,4127565256
,4134788946
,4137507284
,4153696549
,3823997512
,1430007744
,1700848494
,4153239504
,4063321800
,3891396203
,3487458708
,3817335490
,4134247427
,3578599268
,4161068608
,3865694859
,4121023336
,4073831356
,4090288631
,4107016669
,4162004707
,4128453618
,4147088661
,3497256311
,4002400751
,4079871033
,3989589228
,4072257753
,4159851450
,3233587866
,3180580682
,4012994251
,3890517162
,4154584604
,2811717665
,3710698673
,3988370110
,4037456681
,3613350956
,3974202553
,4084320652
,3303939613
,3843506243
,3894047167
,4113305043
,3578206728
,4127383229
,3977268399
,4100370423
,3893445273
,2801977140
,2754447831
,4078978924
,3934494498
,3921929397
,4018909356
,4093516490
,2153883001
,4154248071
,2940696247
,2455830100
,3141753550
,4135020601
,3740824428
,4139715196
,4115963820
,4128381385
,4133845706
,3611050794
,4146549321
,3311631681
,4025337787
,4041420165
,3727449524
,4151050147
,4133976675
,3875659534
,3741798714
,4141639766
,4069886403
,4090081245
,3526228197
,4154370006
,2268593507
,736645507
,3309971371
,3883103863
,3289118115
,3951651012
,3912785905
,4084578943
,3945141383
,3800700615
,4096550042
,4007181417
,3940590467
,4102517908
,3949488147
,4130934533
,3427946275
,4027560217
,4158106443
,3877071043
,3773457692
,4128042968
,4147600996
,4027202053
,4127795247
,4065786448
,3713369516
,4119844424
,4157181523
,3911973712
,4029185455
,3458951617
,4138931402
,4068518259
,3835048213
,3993498414
,4143898970
,4035925407
,3939338234
,3525042367
,3938471057
,3495205679
,4063741332
,3208854231
,4152809580
,4060972306
,4157798006
,4079049703
,4044410926
,4122632885
,3757480419
,4161713721
,3665018486
,3795068060
,3911721827
,3977529139
,4123332347
,2709453086
,4098227983
,3927332045
,3253455150
,4161864209
,4156616012
,4046518958
,3962055877
,4042066135
,2851964493
,3707304848
,3032837479
,3417431355
,4153943255
,4111490732
,1749594728
,4057676433
,4161200779
,4089734927
,4124335041
,3990486814
,1518683145
,486462687
,4146428773
,2899301910
,2435382804
,4140272087
,3487613209
,4128897370
,3625987718
,4006080686
,4151503340
,2153057143
,4107139832
,3829514456
,4126185096
,3258772920
,3889811599
,1661002836
,3993209223
,4156217869
,3959746106
,2470932260
,3910583555
,4150508532
,3607706958
,4137141469
,4148349557
,2439514515
,4147201089
,4144789394
,3938469369
,4123647590
,3561321019
,3978984102
,4142340505
,4129936763
,4119824074
,3229655693
,3603606435
,2741529341
,4143589286
,4058258897
,3950628676
,3909086334
,4144596069
,3090453826
,2476689505
,2983449729
,4132064437
,4160150796
,4141031324
,4078198052
,4120126477
,3965845889
,4160327233
,3998823959
,3747249564
,3830137197
,4106293413
,3920269425
,4038462851
,3960085485
,3577805186
,4158808193
,3153244812
,4102164453
,1809644539
,4112897439
,3845730443
,4094889758
,2351346443
,2532340451
,4147076027
,4080485384
,4106230797
,4075992297
,4099062192
,1859829219
,4103553146
,3919538041
,4119361891
,4095665861
,4105205791
,3579491302
,4085934871
,3982370805
,3510820132
,4016152874
,4142922081
,3754593397
,3898017830
,4057931674
,3347848894
,4161466228
,4011553439
,2493472422
,4159362758
,4059215589
,4118509217
,3155851538
,3139581616
,4157975399
,3810767547
,3249047831
,4141498796
,1792126335
,3994771474
,4080499149
,4035093700
,4075929230
,4146649273
,4161026822
,3730152633
,4133574494
,4095226894
,4159673280
,4105805584
,3245837115
,3760240270
,3374461897
,1841886224
,4072919010
,4023365655
,3702671621
,4142579696
,4083880833
,3573883127
,3867630854
,4159543185
,4138900503
,4057076534
,4143082995
,4093758986
,3677245754
,4151516249
,4144070445
,3937866757
,4129386904
,4155163082
,3584434435
,3832214487
,3697611895
,3634442199
,2725623730
,3212156176
,2698554827
,3445235579
,3266345368
,4159788677
,4137326383
,3311363724
,1840421470
,4096784440
,4110254183
,4156435340);
	private $arrTidsThree=array(
		4125306871
,3833588848
,3623379825
,4145816536
,4137856937
,4157650791
,4136297330
,4109677780
,3924882414
,4155392705
,3883608077
,4159036529
,4161964166
,3943818132
,3993904090
,4124828440
,4158062125
,3830532748
,4160895129
,3032016308
,4092900930
,4105934152
,4151420330
,3607248133
,3911523306
,4093102925
,3167074517
,4161821835
,3841217501
,4084645912
,3360215096
,4116140712
,4158417339
,3996158471
,4010111829
,3973027331
,3707964543
,4155551038
,3786105131
,4083516814
,4155827879
,4138258524
,3914946731
,4146242173
,4043351307
,3874358826
,4150288965
,3948452952
,4156935497
,4157694986
,4126486374
,4156111326
,4161704971
,4134372794
,4149121334
,4047038349
,1511422804
,3445754862
,4147632116
,3995234719
,3904976712
,3998646287
,4158488428
,4105506442
,4154505049
,4154961658
,4155288491
,4121951083
,3507559383
,4146458412
,4109438028
,4131294448
,3228199095
,4099684604
,4157301317
,4160538549
,4002636386
,3939369704
,3006456907
,4116868749
,4159727599
,4084507265
,3886453962
,3942947231
,4106494586
,4160198252
,3884330576
,3955553406
,4094141627
,4159486622
,4042198505
,4151143437
,4146857532
,4118532268
,3591488759
,4158812242
,4072643006
,4105252320
,4084021102
,4040893555
,4120897202
,4152092923
,3427191188
,3540943178
,4157988734
,4152508582
,4136280331
,4113048483
,3965979909
,4161724177
,4071513072
,3671059158
,3896770749
,4153886871
,4155229457
,4133162214
,4129212627
,3869381110
,4161795127
,4021756749
,4115021099
,3566945561
,4087805122
,3658528363
,3625490026
,3942674254
,4078414290
,4158417908
,4109673373
,4151206184
,4139197653
,4143979144
,3963893256
,4150330829
,4071429242
,4161983809
,4154736307
,4137195027
,4060985909
,4153863739
,4111530059
,4140918410
,4159758218
,4092111631
,4154329350
,4120354026
,4162080935
,4150633031
,4114106136
,4161897778
,978224745
,4071752861
,3934806709
,4038064240
,4159225820
,4018177414
,3889956233
,4105352649
,4144731279
,4132319919
,4138176673
,4111934828
,3901406325
,4158282397
,4073859436
,3815219818
,4140130972
,4155336860
,2889448955
,3872457466
,4091782283
,4097306424
,4132164447
,4139857185
,3943944928
,4161572241
,4155844186
,4159230697
,4116475027
,3718451515
,3988493961
,3871668987
,4157816116
,4142848659
,4133223951
,4153079646
,4149034885
,3707119768
,4154972119
,4111747383
,4120789114
,4064758701
,3002102897
,4145621212
,3625433392
,3648918973
,4154578469
,4134629724
,4156799027
,4126148566
,4111028079
,4151325440
,3891638283
,4161545654
,4161243964
,4091225928
,4151746730
,4042667737
,3994438774
,4158473676
,4073755235
,4137230768
,4144460523
,3880248893
,4117926734
,4147499167
,4161388271
,3941064004
,4108951193
,3766459813
,3828862691
,3637405184
,4105494394
,4099881305
,3455149224
,4050833737
,3810899799
,4140740735
,4130649421
,4159569070
,3959448497
,4116722428
,4137239971
,4150192456
,4154401863
,3414670847
,4129587440
,4161885507
,3959583154
,4160434737
,4157611553
,2958181237
,4075251281
,3798705969
,4159261030
,3903364322
,4004138443
,4018489657
,4138966115
,4157853308
,3850319763
,4012993847
,3702237174
,4108372247
,3767497145
,4122688814
,4098960633
,4156026249
,3399548502
,4119111171
,4139784283
,4150053179
,4089801564
,4145074602
,4159169194
,4118177756
,3546952710
,4160584227
,4114153081
,3945181148
,4067277238
,3936735869
,4160180470
,4064293015
,3945165840
,1174832397
,4093214965
,3769574691
,4145624711
,3970155294
,4085423473
,4091515378
,4098639877
,4155003458
,4102226079);
	
	private $arrTidsFour =array(3561321019,4148041509,3942947231,4161713979,4088426527,4172319967,4154307979);
	/**
	 * 根据词表获取需要搬贴的信息
	 * @param：$strTwZhiRedis
	 * @return：null
	 */
	public function _execute(){
		Bingo_Log::warning('wangxbd===7号数据开始导入========'.var_export($this->arrTids,1));
		foreach ($this->arrTids as $tid){
			$threadInfo =$this->getThreadInfoByTid($tid);
			if(isset($threadInfo)){
				if(isset($threadInfo['is_copytwzhibo']) && $threadInfo['is_copytwzhibo'] == 4){
					//同步到mis
					$arrParam =array(
						'forum_id'   => $threadInfo['forum_id'],
						'thread_id' => $tid,
						'user_id' =>  $threadInfo['user_id'],
						'source_id' => 4,
						'create_time' => 1449424800,
					);
					$this->addTwMovedThread($arrParam);
				}else {
					$this->arrTidCopy[]=$tid;
				}
			}
			else{
				$this->arrTidErr[] = $tid;
			}	
		}
		
		Bingo_Log::warning('wangxbd===arrSuccTid========'.var_export($this->arrSuccTid,1));
		Bingo_Log::warning('wangxbd===arrTidCopy========'.var_export($this->arrTidCopy,1));
		Bingo_Log::warning('wangxbd===arrTidErr========'.var_export($this->arrTidErr,1));
		Bingo_Log::warning('wangxbd===arrTidMoveErr========'.var_export($this->arrTidMoveErr,1));
		Bingo_Log::warning('wangxbd===7号数据导入结束========');
		$this->arrSuccTid =null;
		$this->arrTidCopy =null;
		$this->arrTidErr =null;
		$this->arrTidMoveErr =null;
		Bingo_Log::warning('wangxbd===8号数据开始导入========'.var_export($this->arrTidsTwo,1));
		foreach ($this->arrTidsTwo as $tid){
			$threadInfo =$this->getThreadInfoByTid($tid);
			if(isset($threadInfo)){
				if(isset($threadInfo['is_copytwzhibo']) && $threadInfo['is_copytwzhibo'] == 4){
					//同步到mis
					$arrParam =array(
							'forum_id'   => $threadInfo['forum_id'],
							'thread_id' => $tid,
							'user_id' =>  $threadInfo['user_id'],
							'source_id' => 4,
							'create_time' => 1449511200,
					);
					$this->addTwMovedThread($arrParam);
				}else {
					$this->arrTidCopy[]=$tid;
				}
			}
			else{
				$this->arrTidErr[] = $tid;
			}
		}
		Bingo_Log::warning('wangxbd===arrSuccTid========'.var_export($this->arrSuccTid,1));
		Bingo_Log::warning('wangxbd===arrTidCopy========'.var_export($this->arrTidCopy,1));
		Bingo_Log::warning('wangxbd===arrTidErr========'.var_export($this->arrTidErr,1));
		Bingo_Log::warning('wangxbd===arrTidMoveErr========'.var_export($this->arrTidMoveErr,1));
		Bingo_Log::warning('wangxbd===8号数据导入结束========');
		$this->arrSuccTid =null;
		$this->arrTidCopy =null;
		$this->arrTidErr =null;
		$this->arrTidMoveErr =null;
		Bingo_Log::warning('wangxbd===9号数据开始导入========'.var_export($this->arrTidsThree,1));
		foreach ($this->arrTidsThree as $tid){
			$threadInfo =$this->getThreadInfoByTid($tid);
			if(isset($threadInfo)){
				if(isset($threadInfo['is_copytwzhibo']) && $threadInfo['is_copytwzhibo'] == 4){
					//同步到mis
					$arrParam =array(
							'forum_id'   => $threadInfo['forum_id'],
							'thread_id' => $tid,
							'user_id' =>  $threadInfo['user_id'],
							'source_id' => 4,
							'create_time' => 1449597600,
					);
					$this->addTwMovedThread($arrParam);
				}else {
					$this->arrTidCopy[]=$tid;
				}
			}
			else{
				$this->arrTidErr[] = $tid;
			}
		}
		Bingo_Log::warning('wangxbd===arrSuccTid========'.var_export($this->arrSuccTid,1));
		Bingo_Log::warning('wangxbd===arrTidCopy========'.var_export($this->arrTidCopy,1));
		Bingo_Log::warning('wangxbd===arrTidErr========'.var_export($this->arrTidErr,1));
		Bingo_Log::warning('wangxbd===arrTidMoveErr========'.var_export($this->arrTidMoveErr,1));
		Bingo_Log::warning('wangxbd===9号数据导入结束========');
		
		
		$this->arrSuccTid =null;
		$this->arrTidCopy =null;
		$this->arrTidErr =null;
		$this->arrTidMoveErr =null;
		Bingo_Log::warning('wangxbd===10号数据开始导入========'.var_export($this->arrTidsFour,1));
		foreach ($this->arrTidsFour as $tid){
			$threadInfo =$this->getThreadInfoByTid($tid);
			if(isset($threadInfo)){
				if(isset($threadInfo['is_copytwzhibo']) && $threadInfo['is_copytwzhibo'] == 4){
					//同步到mis
					$arrParam =array(
							'forum_id'   => $threadInfo['forum_id'],
							'thread_id' => $tid,
							'user_id' =>  $threadInfo['user_id'],
							'source_id' => 4,
							'create_time' => 1449597600,
					);
					$this->addTwMovedThread($arrParam);
				}else {
					$this->arrTidCopy[]=$tid;
				}
			}
			else{
				$this->arrTidErr[] = $tid;
			}
		}
		Bingo_Log::warning('wangxbd===arrSuccTid========'.var_export($this->arrSuccTid,1));
		Bingo_Log::warning('wangxbd===arrTidCopy========'.var_export($this->arrTidCopy,1));
		Bingo_Log::warning('wangxbd===arrTidErr========'.var_export($this->arrTidErr,1));
		Bingo_Log::warning('wangxbd===arrTidMoveErr========'.var_export($this->arrTidMoveErr,1));
		Bingo_Log::warning('wangxbd===10号数据导入结束========');
		
			
		
	}
	/**
	 *获取帖子信息
	 * @param：null
	 * @return：null
	 */
	private function getThreadInfoByTid($tid){
		$arrFrsReq = array(
				'thread_ids' => $tid,
				'need_abstract' => 0,
				'forum_id' => null,
				'need_photo_pic' => 0,
				'need_user_data' => 0,
				'icon_size' => null,
				'need_forum_name' => 0,
				'need_post_content' => 0,
				'call_from' => 'client_frs',
		);
		$arrRes = Tieba_Service::call('post', 'mgetThread', $arrFrsReq, null, null, 'post', 'php', 'utf-8');
		if(!$arrRes || $arrRes['errno']!==Tieba_Errcode::ERR_SUCCESS){
			Lib_Util_Log::warning("loadTidIntoDB call mgetThread  error.  req:" .serialize($arrFrsReq)."   res:".serialize($arrRes));
			return null;
		} else {
			$threadList = $arrRes['output']['thread_list'];
			$threadInfo =$threadList[$tid];
			return $threadInfo;
		}
	}
	
	/**
	 *搬贴同步到mis
	 * @param：null
	 * @return：null
	 */
	private function addTwMovedThread($arrParam){
		$arrInput =array(
				'forum_id'   => $arrParam['forum_id'] ,
				'thread_id' => $arrParam['thread_id'],
				'user_id' => $arrParam['user_id'],
				'source_id' => $arrParam['source_id'],
		);
		Bingo_Log::warning('wangxbd=====$$addTwMovedThread==='.var_export($arrInput,true));
		$arrRes = Tieba_Service::call('livegroup', 'addTwMovedThread', $arrInput);
		if (!$arrRes || $arrRes['errno'] != 0){
			$this->arrTidMoveErr[] = $arrParam['thread_id'];
		}else {
			$this->arrSuccTid[] = $arrParam['thread_id'];
		}
		
		Bingo_Log::warning('wangxbd=====$addTwMovedThread==='.var_export($arrRes,true));
	}

	
	

}


