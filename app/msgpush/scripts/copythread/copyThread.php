<?php
/**
 * 检测定时任务队列
 * <EMAIL>
 * 2013/11/13 11:27
**/

//执行方式 cd $ORP_PATH ;  php app/msgpush/scripts/detectTimingTask.php  调整目录,add by magong<PERSON><PERSON>@baidu.com

define ('EASYSCRIPT_DEBUG',true);          //debug 模式
define ('EASYSCRIPT_THROW_EXEPTION',true);  //抛异常模式

define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../../' );
define ('SCRIPTNAME',basename(__FILE__,".php"));   //定义脚本名
define ('BASEPATH',dirname(__FILE__));
define ('CONFPATH',BASEPATH."/conf");
define ('DATAPATH',BASEPATH."/data");
define ('LOGPATH',"./log/msgpush/scripts/copythread");
define('IS_ORP_RUNTIME', true);
set_include_path(get_include_path() . PATH_SEPARATOR. BASEPATH.'/../../');


/**
 * 获取当前毫秒级时间戳
 * @param：null
 * @return：string
 */
function __autoload($strClassName)
{
    require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');

/**
 * @param
 * @return
 */
function statLog($strLog){
	$log_path = ROOT_PATH . "/log/stlog/";
	if(!file_exists($log_path)){
		mkdir($log_path, 0777, true);
	}
	$strLogFileName = $log_path . 'wap' . "." . strftime('%Y%m%d%H');
	$logStr = 'NOTICE: ' . strftime('%m-%d %H:%M:%S') . ':  stat-log' . ' * ' .  posix_getpid() . " " . $strLog . "\n";
	file_put_contents($strLogFileName, $logStr, FILE_APPEND);
}


Lib_Util_Log::notice('copythread script begin runing.');

define ('LOG', 'log');
Bingo_Log::init(array(
    LOG => array(
        'file'  => LOGPATH ."/". SCRIPTNAME. ".log",
        'level' => 0xFF,
    ),
), LOG);

/**
 * 获取当前毫秒级时间戳
 * @param：null
 * @return：string
 */
function microtime_float()
{
    list($usec, $sec) = explode(" ", microtime());
    return ((float)$usec + (float)$sec);
}

$easyScriptObj  = false;
try{
	$strCopyThreadRedis = $argv[1];
	$from = $argv[2];
	if(empty($strCopyThreadRedis)){
		Lib_Util_Log::warning('script name set is invalid,name:['.SCRIPTNAME.'] arrParam:['.serialize(argv).']');
		exit;
	}
	
	$ScriptConf = array(
        'memory_limit'   => '1024M',
        'data_path'      => DATAPATH,
        'conf_path'      => CONFPATH,
        'lock_file_name' => SCRIPTNAME.'.lock',
        'done_file_name' => SCRIPTNAME.'.done',
        'db_alias'       => array(
            'im'     => 'forum_livegroup',
        ),
        'conf_alias'     => array(
            'main'     => 'main.conf',
        ),
    );

	$easyScriptObj = new Lib_Util_EasyScript($ScriptConf);
    //防止脚本重复执行
    if( $easyScriptObj->checkScriptIsRuning() === true ){
        Lib_Util_Log::notice('copythread  script is runing.');
        exit;
    }
    Lib_Util_Log::notice('copythread  script begin runing.');
    $copyThread = new copyThreadAction();
    $copyThread->_buildFineThreadTagList($strCopyThreadRedis,$from);
	
}catch(Exception $e){
    if($easyScriptObj !== false){
       Bingo_Log::warning('copythread script run fail!'.$easyScriptObj->getErr2String());
    }else{
       Bingo_Log::warning('copythread script run fail![null]');
    }
    Bingo_Log::notice('copythread run fail.'.$easyScriptObj->getErr2String());
    exit;
}
Bingo_Log::notice('copythread run success.');

class copyThreadAction {
	
	private $forumId = null;
	private $from =null; //代表是升级帖子，还是恢复帖子

	private static $clientType = 2;
	private static $clientVersion = '6.8.5';
	private $_arrForumInfo = array ();
	private static $strPhoneType = 'android';
	private $_arrThreadId = array ();
	
	private $_twZhiBoRedis = null;
	private $intCurtTime;
	private $intUid ;
	private $arrCoverPicUrl = null;
	private $arrSuccTid = array ();
	private $arrTypeErrTid =array();
	private $arrFinaFloorErrTid =array();
	private $arrBlackListErrTid =array();
	private $arrArchSerErrTid =array();
	private $arrGuanFangErrTid =array();
	
	private $lastPostInfo =null;
	private  $intWarmFloorNum =3;
	private $arrUserInfo =array();
	private $arrWarnDoc =array();
	private $arrWarmSuccTids =array();
	private $arrWarmSuccPids =array();


	const FLOOR_PAGE_SIZE = 30;
	const PB_PAGE_SIZE = 30;

	const TWZHIBO_COPYPOST='tb_wordlist_redis_TWZhiBO_CopyPost';
	private $warmFloorDocRedis ='tb_wordlist_redis_TWZhiBO_WarmFloorDoc';

	/**
	 * 根据词表获取需要搬贴的信息
	 * @param：$strTwZhiRedis
	 * @return：null
	 */
	public function _buildFineThreadTagList($strTwZhiRedis,$from){
		Bingo_Log::warning('wangxbd===$$arrInput$from===='.var_export($from,true));
		$this->_twZhiBoRedis =$strTwZhiRedis;;
		$this->from =$from;
		if($from ==1 || $from == 2){
			//从redis 中取数据
			$this->intCurtTime =time();
			$arrInput = array (
				'curtime' => $this->intCurtTime,
				'from' => $from,
			);
			Bingo_Log::warning('wangxbd===$$arrInput===='.var_export($arrInput,true));
			$arrTids = Tieba_Service::call ('livegroup', 'getApplyCopyThreadTid', $arrInput );
			Bingo_Log::warning('wangxbd===$arrTids===='.var_export($arrTids,true));
			if ($arrTids === false || $arrTids ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
				return Tieba_Errcode::ERR_CLIENT_CALL_FORUM;
			} else {
				$this->_arrThreadId = $arrTids['data'];
			}
			
		}else if($from == 4){
			$arrKeys = array('twzhibocopypost'=>'twzhibocopypost');
			$handleWordServer = Wordserver_Wordlist::factory();
			$arrTagList= $handleWordServer->getValueByKeys($arrKeys,$this->_twZhiBoRedis);
			if(isset($arrTagList['twzhibocopypost'])){
				Bingo_Log::warning('wangxbd=====$arrTagList========'.var_export($arrTagList,true));
				$strTids =$arrTagList['twzhibocopypost'];
				$arrTids = explode(",",$strTids);
				if(empty($arrTids)){
					return ;
				}
				$this->_arrThreadId =$arrTids;
				$this->addReMindCopyThreadTid();
			}
		}
		$this->_execute();
		
		Bingo_Log::warning('wangxbd=====$arrSuccTid========'.var_export($this->arrSuccTid,true));
		Bingo_Log::warning('wangxbd=====$arrTypeErrTid========'.var_export($this->arrTypeErrTid,true));
		Bingo_Log::warning('wangxbd=====$arrFinaFloorErrTid========'.var_export($this->arrFinaFloorErrTid,true));
		Bingo_Log::warning('wangxbd=====$arrGuanFangErrTid========'.var_export($this->arrGuanFangErrTid,true));
		Bingo_Log::warning('wangxbd=====$arrBlackListErrTid========'.var_export($this->arrBlackListErrTid,true));
		Bingo_Log::warning('wangxbd=====$arrArchSerErrTid========'.var_export($this->arrArchSerErrTid,true));
		Bingo_Log::warning('wangxbd======$arrWarmSuccTids======'.var_export($this->arrWarmSuccTids,true));
		Bingo_Log::warning('wangxbd======$arrWarmSuccPids======'.var_export($this->arrWarmSuccPids,true));
	}
	/**
	 * 把PM主动搬贴的tid记录下来
	 * @param：null
	 * @return：null
	 */
	private function addReMindCopyThreadTid(){
		if(empty($this->arrTids)){
			return ;
		}
		$arrParams = array(
			'tids' => $this->_arrThreadId,
			'from' => 2,
		);
// 		Bingo_Log::warning('wangxbd======addReMindCopy==='.var_export($arrParams,true));
		$arrRet = Tieba_Service::call('livegroup', 'addReMindCopyThreadTid', $arrParams, null, null, 'post', 'php', 'utf-8', null);
		Bingo_Log::warning('wangxbd======addReMindCopy==='.var_export($arrRet,true));
		if (!isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning("call livegroup addReMindCopyThreadTid filed");
		}
	}

	/**
	 * 执行搬贴的逻辑
	 * @param：null
	 * @return：null
	 */
	private function _execute() {
		Bingo_Log::warning('wangxbd===_arrThreadId===='.var_export($this->_arrThreadId,true));
		if(empty($this->_arrThreadId)){
			return;
		}
		$this->arrUserInfo = UserInfo::getUserInfo();
		$this->arrWarnDoc =$this->getWarmFloorDoc();
		// 因为不能一次取完所有贴子，所以不能批量执行
		foreach ( $this->_arrThreadId as $key => $val ) {
			$intTid = intval ( $val );
			$boolThreadType =$this->checkThreadType($intTid);
			Bingo_Log::warning('wangxbd===$boolThreadType===='.$intTid.'====='.var_export($boolThreadType,true));
			if($boolThreadType === false){
				$this->arrTypeErrTid[] = $intTid;
				continue;
			}
			if($this->from !=2){
				//判断帖子最后一楼是不是安卓端发的
				$boolRes =$this->checkLastFloor($intTid);
				Bingo_Log::warning('wangxbd===$checkLastFloor===='.$intTid.'====='.var_export($boolRes,true));
				// 			Bingo_Log::warning('wangxbd===$$boolResforumId===='.var_export($this->forumId,true));
				if($boolRes ===false || empty($this->forumId)){
					$this->arrFinaFloorErrTid[] = $intTid;
					continue;
				}
				//官方吧不能发直播贴
				$boolRes =$this->isOpenTWZhiBo();
				Bingo_Log::warning('wangxbd===$arrGuanFangErrTid===='.$intTid.'====='.var_export($boolRes,true));
				if($boolRes === false){
					$this->arrGuanFangErrTid[] = $intTid;
					continue;
				}
			
			
				//校验主播是否在黑名单内
				$boolRes =$this->checkTWZhiBoUserLevel();
				Bingo_Log::warning('wangxbd===$$checkTWZhiBoUserLevel===='.var_export($boolRes,true));
				if($boolRes ===false ){
					$this->arrBlackListErrTid[] = $intTid;
					continue;
				}
			}
			
			$intValue =0;
			//is_copytwzhibo 值的意思 1:主播手动搬贴的，2：PM提醒的，3：命中关键字的，4：PM强制搬的
			if($this->from == 4){
				$intValue =4;
			}else if($this->from == 1){
				//判断tid的来源
				$arrInput =array(
					'tid' =>$intTid,
					'from' => 1,
				);
				$boolRes =$this->isReMindCopyThreadTid($arrInput);
				if($boolRes ==true){
					$intValue = 2;
				}else{
					$intValue =1;
				}
			}
			$input = array(
				"forum_id" => $this->forumId,
				"thread_id" => $intTid,
				"ext_infos" =>  array(array(
					"key" => "is_copytwzhibo",
					"value" => $intValue,
				),),
			);
			//搬贴重试三次
			$i=0;
			$arrRet = array();
			for (;$i<3;$i++){
				Bingo_Log::warning('wangxbd===setExtAttrinput===='.var_export($input,true));
				$arrRet   = Tieba_Service::call('post', 'setExtAttr', $input, null, null, 'post', 'php', 'utf-8');
				Bingo_Log::warning('wangxbd===setExtAttr$$$arrRet===='.var_export($arrRet,true));
				if (!isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
					Bingo_Log::warning("call post service filed");
					continue;
				}
				if (Tieba_Errcode::ERR_SUCCESS == $arrRet['errno']) {
					break;
				}
			}
			
			if($arrRet['errno'] == Tieba_Errcode::ERR_SUCCESS){
// 				statLog('[pro=client urlkey=msgpush/scripts/copythread _client_type=2 _client_version=7.0.4 uid='.$this->intUid.' tid='.$intTid.' errno=0 uip=3778490378 agent=twlive is_copytwzhibo='.$intValue.']');
				$this->copyThreadStatlog('msgpush/scripts/copythread',$this->intUid, $intTid,null,'is_copytwzhibo',$intValue);
				$this->arrSuccTid[] =$intTid;
				if($this->from == 1 ||$this->from == 4){
					$this->setTWAnchorLevel();
					if($this->from == 4){
						$this->setLiveCoverPic($intTid);
					}
					$arrParams = array(
						'thread_id' => $intTid,
						'state' => 1,
					);
					$arrRet = Tieba_Service::call('livegroup', 'setThreadCopyState', $arrParams, null, null, 'post', 'php', 'utf-8', null);
					if (!isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
						Bingo_Log::warning("call post service filed");
					}else{
						$content ='恭喜你，你申请的贴子已升级为新版直播贴，赶快来体验吧！';
						$this->remindPushMsg($intTid, 4, $content);
					}
					//同步到mis
					$arrParam =array(
						'forum_id'   => $this->forumId,
						'thread_id' => $intTid,
						'user_id' => $this->intUid,
						'source_id' => $intValue,
					);
					$this->addTwMovedThread($arrParam);
					//搬贴后暖楼
					$this->warmFloor();
					
					
				}else {
					if($this->from == 2){
						//从mis中删除
						$arrParam =array(
							'forum_id'   => $this->forumId,
							'thread_id' => $intTid,
							'user_id' => $this->intUid,
						);
						$this->rollbackTwMovedThread($arrParam);
					}
					$arrInput = array (
						'thread_ids' => array (
							$intTid,
						),
					);
					$arrOutput = Tieba_Service::call ( 'livegroup', 'delThreadCopyState', $arrInput, null, null,'post','php','utf-8'  );
					if ($arrOutput === false) {
						Bingo_Log::fatal('call livegroup:delThreadCopyState failed.[sevice_name:post][method:mgetThread] [input:' . serialize ( $arrInput ) . '] [output:' . serialize ( $arrOutput ) . ']' );
					}
					if ($arrOutput ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
						Bingo_Log::warning ( 'call livegroup_delThreadCopyState threadids[' . serialize ( self::$_arrInput[0]) . '] output[' . serialize ( $arrTidStatus ) . '] errno:'.$arrOutput ['errno'].' errmsg:'.Molib_Client_Error::getErrMsg($arrOutput ['errno']) );
					}
				
					$content ='你的直播贴已恢复成主题贴了';
					$this->remindPushMsg($intTid, 5, $content);
				}
				
				
				$arrInput = array (
					'curtime' => $this->intCurtTime,
					'from' => $this->from ,
				);
				$arrTids = Tieba_Service::call ('livegroup', 'deleteApplyCopyThreadTid', $arrInput );
				if ($arrTids === false || $arrTids ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
					return Tieba_Errcode::ERR_CLIENT_CALL_FORUM;
				}
			}
			else {
				$this->arrArchSerErrTid[] = $intTid;
			}
		}
	}
	/**
	 *搬贴同步到mis
	 * @param：null
	 * @return：null
	 */
	private function addTwMovedThread($arrParam){
		$arrInput =array(
			'forum_id'   => $arrParam['forum_id'] ,
			'thread_id' => $arrParam['thread_id'],
			'user_id' => $arrParam['user_id'],
			'source_id' => $arrParam['source_id'],
		);
// 		Bingo_Log::warning('wangxbd=====$$addTWMovedThread==='.var_export($arrInput,true));
		$arrRes = Tieba_Service::call('livegroup', 'addTWMovedThread', $arrInput);
		if ($arrRes === false || $arrRes ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('wangxbd=====$addTWMovedThread==='.var_export($arrRes,true));
		}
		
	}
	/**
	 *恢复贴贴从mis 中删除
	 * @param：null
	 * @return：null
	 */
	private function rollbackTwMovedThread($arrParam){
		$arrInput =array(
			'forum_id'   => $arrParam['forum_id'] ,
			'thread_id' => $arrParam['thread_id'],
			'user_id' => $arrParam['user_id'],
		);
		Bingo_Log::warning('wangxbd=====$$rollbackTWMovedThread$arrInput==='.var_export($arrInput,true));
		$arrRes = Tieba_Service::call('livegroup', 'rollbackTWMovedThread', $arrInput);
		if ($arrRes === false || $arrRes ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('wangxbd=====$$rollbackTWMovedThread==='.var_export($arrRes,true));
		}
		
	}
	/**
	 *判断tid是否在提醒的redis
	 * @param：null
	 * @return：null
	 */
	private function isReMindCopyThreadTid($arrInput){
		
// 		Bingo_Log::warning('wangxbd======isReMindCopyThreadTid==='.var_export($arrParams,true));
		$arrRet = Tieba_Service::call('livegroup', 'isReMindCopyThreadTid', $arrInput, null, null, 'post', 'php', 'utf-8', null);
		Bingo_Log::warning('wangxbd======isReMindCopyThreadTid==='.var_export($arrRet,true));
		if (!isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning("call livegroup isReMindCopyThreadTid filed");
			return false;
		}
		$isExit =$arrRet['is_exit'];
		if($isExit ==1){
			return true;
		}else {
			return false;
		}
	}
	/**
	 *搬贴成功或者失败给用户发消息
	 * @param：null
	 * @return：null
	 */
	private function remindPushMsg($intThreadId, $msgType, $content){
		if(empty($intThreadId) || empty($msgType) ||empty($content)){
			return ;
		}

		$content =is_utf8($content) ? Bingo_Encode::convert($content, Bingo_Encode::ENCODE_GBK,
				Bingo_Encode::ENCODE_UTF8) : $content;
// 		Bingo_Log::warning('wangxbd======$content======'.var_export($content,true));
		$item =$this->getThreadInfo($intThreadId, $msgType, $content);
		if (count($item) > 0){
			$strLog = "anchor " . $item['user_id'] . " thread_id " . $intThreadId . " has been reminded ".$intRemindCount ." times,msg push begin.\r\n";
			Lib_Util_Log::notice($strLog);
			echo $strLog;
		
			$strContent = Bingo_String::array2json($item);
			$strLog = "push msg to anchor:" . $item['user_id'] . ".content:" . $strContent."\r\n";
			Lib_Util_Log::notice($strLog);

            $apnsExt = array(
                'tp' => 13, 
                'subtp' => 1, 
                'to' => $item['user_id'], 
                'from' => 1639597190, 
                'msg' => array('to' => $item['user_id']), 
            );
            $abstract = serialize(array('ext'=>$apnsExt, 'content'=>'你收到一条新的消息'));
			//echo $strLog;
			$arrParams = array(
            	'user_id' => 1639597190,//官方账号
             	'user_type' => 4,//用户类型
                'user_name' => '贴吧直播',//账号名称
                'from_uid' => $item['user_id'],//主播uid
                'to_uids' => array($item['user_id'],),//主播uid
// 				'to_uids' => array(1192263067,1092391541,1279926718,982626404,591524557,754496842,147982869,980020407,),//主播uid
                'msg_type' => 25,//消息类型
                'content' => '[' . $strContent . ']',//消息内容
                'abstract' => $abstract,
                '_partition_key' => mt_rand(1,3),
            );
			Bingo_Log::warning('wangxbd======pushNMQ======'.var_export($arrParams,true));
			$arrNMQRes = Tieba_Commit::commit('msgpush', 'sendGfMsgByUids', $arrParams);
			Bingo_Log::warning('wangxbd======pushNMQ======'.var_export($arrNMQRes,true));
			if(!$arrNMQRes || !isset($arrNMQRes['err_no']) || $arrNMQRes['err_no'] != 0 ){
				sleep(1);
			}
		
			
	 }
	}
	/**
	 *组装返回的消息内容
	 * @param：null
	 * @return：null
	 */
	private  function getThreadInfo($intThreadId,$msgType,$content){
		$item = array();
		$input = array(
			"thread_ids" => array(
				0 => $intThreadId,
			),
			"need_abstract" => 0,
			"forum_id" => 0,
			"need_photo_pic" => 1,
			"need_user_data" => 1,
			"icon_size" => 0,
			"need_forum_name" => 1,
		);
		$output = Tieba_Service::call("post", "mgetThread", $input);
		if ($output['errno'] == 0){
			$intUserId = $output['output']['thread_list'][$intThreadId]['user_id'];
			$item['user_id'] = $intUserId;
			$item['msg_type'] = $msgType;
			$item['nick_name'] = $output['output']['thread_user_list'][$intUserId]['user_name'];
			$strPortraitTime = $output['output']['thread_user_list'][$intUserId]['portrait_time'];
			$item['head_url'] = Tieba_Ucrypt::encode($intUserId, $item['nick_name'], $strPortraitTime);
			$item['caller_time'] = time() * 1000;
			$item['content'] = $item['caller_content'] = $content;
			$item['thread_id'] = $intThreadId;
			$item['thread_title'] = $output['output']['thread_list'][$intThreadId]['title'];
			$item['forum_name'] = $output['output']['thread_list'][$intThreadId]['forum_name'];
            $iosLink = 'http://tieba.baidu.com/p/'.$intThreadId;
            if ($msgType == 4) {
                $iosLink .= '?thread_type=33';
            }
			$item['link'] = $iosLink;
		}
		return $item;
	}
		
		
	
	/**
	 *校验搬贴最后一楼是不是安卓端发的
	 * @param：null
	 * @return：null
	 */
	private function checkLastFloor($threadId){
		if(empty($threadId)){
			return false;
		}
		
		$input = array(
			'thread_id' => $threadId,
			'offset' => 0,
			'res_num' => 1,
			'see_author' => 1,
			'has_comment' => 0,
			'has_mask' => 0,
			'structured_content' => 1,
		);
		$output = Tieba_Service::call("post", "getInvertPostsByThreadId", $input);
	    if ($output['errno'] == 0){
// 	    	Bingo_Log::warning('wangxbd=====getInvertPostsByThreadId========'.var_export($output['output']['output'],true));
	       	$phoneType = $output['output']['output'][0]['post_infos'][0]['phone_type'];
	       	if($phoneType =='apple' || $phoneType =='ios'){
	       		$this->forumId =null;
	       		$this->lastPostInfo =null;
	       		return false;
	       	}else{
	       		$this->forumId = $output['output']['output'][0]['post_infos'][0]['forum_id'];
	       		$this->intUid = $output['output']['output'][0]['post_infos'][0]['user_id'];
	       		if(empty($this->forumId)){
	       			$this->lastPostInfo =null;
	       			return false;
	       		}else {
	       			$this->lastPostInfo = $output['output']['output'][0]['post_infos'][0];
	       			return true;
	       		}
	       		
	       	}
	        
	    }else{
	    	$this->forumId =null;
	    	$this->lastPostInfo =null;
	    	return false;
	    	
	    }
	    	
	}
	/**
	 *  取暖楼的文案库
	 * @param：$postInfo
	 * @return：
	 */
	private function getWarmFloorDoc(){
		$arrKeys = array('warmfloordockey'=>'warmfloordockey');
		$handleWordServer = Wordserver_Wordlist::factory();
		$arrDocList= $handleWordServer->getValueByKeys($arrKeys,$this->warmFloorDocRedis);
		$arrWarmDoc =null;
		if(isset($arrDocList['warmfloordockey'])){
			$arrWarmDoc=unserialize($arrDocList['warmfloordockey']);
		}
		return $arrWarmDoc;
	}
	/**
	 *  搬贴暖楼
	 * @param：$postInfo
	 * @return：
	 */
	private function warmFloor(){
		if(empty($this->lastPostInfo)){
			return;
		}
		$arrIndex =array_rand($this->arrUserInfo,$this->intWarmFloorNum);
// 		Bingo_Log::warning('wangxbd======$$arrIndex======'.var_export($arrIndex,true));
		$boolWarmPost =false;
		foreach ($arrIndex as $index){
			//瞬间取一个文案
			$intDocIndex =array_rand($this->arrWarnDoc,1);
			$content=$this->arrWarnDoc[$intDocIndex];
			$boolRet =$this->addPost($this->lastPostInfo, $content, $index, $this->arrUserInfo[$index]);
			if($boolRet ==true){
				$boolWarmPost =true;
			}
		}
		if($boolWarmPost ==true){
			//$intform 1：代表新开暖楼 2：代表最新更新贴暖楼 3：代表暖楼弹幕
			$this->arrWarmSuccTids[] =$this->lastPostInfo['thread_id'];
// 			statLog('[pro=client urlkey=msgpush/scripts/warmfloor _client_type=2 _client_version=7.0.4 uid='.$this->lastPostInfo['user_id'].' tid='.$this->lastPostInfo['thread_id'].' errno=0 uip=3778490378 agent=twlive is_warmThread=3]');
			$this->copyThreadStatlog('msgpush/scripts/warmfloor',$this->lastPostInfo['user_id'], $this->lastPostInfo['thread_id'],null,'is_warmThread',3);
		}
	}
	
	/**
	 *  根据参数生成主题帖信息
	 * @param：$postInfo
	 * @return：
	 */
	private function addPost($postInfo,$content,$userId,$userName) {
		if(empty($postInfo)){
			return ;
		}
		$boolWarmPost =false;
		$userName =trim($userName);
		$content=Bingo_Encode::convert($content, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8 );
		$content =strip_tags($content,"<img>");
		
		$userName =is_utf8($userName)? Bingo_Encode::convert($userName,Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8):$userName;
		$arrRequest = array (
				'content' => preg_replace("#<a[^>]*class=\"at\"*>(.*?)</a>#is", "$1", $content),
				'anonymous' => 0,
				'forum_id' => $postInfo['forum_id'],
				'forum_name' => $postInfo['word'],
				'thread_id' => $postInfo['thread_id'],
				'vcode' => '',
				'vcode_md5' => '',
				'tag' => 11,
				'new_vcode' => '',
				'floor_num' => $postInfo ['post_no'],
				'quote_id' => $postInfo['post_id'],
				'repostid' => $postInfo['post_id'],
				'is_addition' => 0,
				'product_client_type' => 1,
				'phone_type' => self::$strPhoneType,
				'product_private_key' => self::$strPhoneType,
				'open_id' => isset ( $postInfo ['openid'] ) ? $postInfo ['openid'] : 'tbclient',
				'user_ip' => $postInfo ['ip'],
				'user_id' => $userId,
				'user_name' => $userName,
				'voice_md5' => null,
				'during_time' => null,
				'ptype' => $postInfo ['ptype'],
				'is_bub' => 0,
				'cmd_no' => 20,
				'is_twzhibo_thread' => 1,
		);
	
		$arrRequest ['client_port'] = Bingo_Http_Request::getServer ( 'HTTP_X_BD_CLIENTPORT', 0 );
		$i=0;
		for (;$i<3;$i++){
// 			Bingo_Log::warning('wangxbd====$$arrRequest========'.var_export($arrRequest,true));
			$arrResponse = Tieba_Service::call ( 'post', 'addPost', array ('req' => $arrRequest), 'service_commit' );
// 			Bingo_Log::warning('wangxbd====$arrResponse========'.var_export($arrResponse,true));
			if (false === $arrResponse) {
				// 认为稳定性错误
				$arrResponse ['errno'] = Tieba_Errcode::ERR_CLIENT_CALL_COMMIT;
			}
	
			if ('gbk' === $arrResponse ['ie']) {
				$arrResponse ['res'] = Bingo_Encode::convert ( $arrResponse ['res'], Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK );
			}
	
			$strErrno = $arrResponse ['errno'];
			$strErrmsg = Molib_Client_Error::getErrMsg ( $strErrno );
			if (Tieba_Errcode::ERR_SUCCESS != $strErrno) {
				sleep(1);
				continue;
			}
			if (Tieba_Errcode::ERR_SUCCESS == $strErrno) {
				$boolWarmPost =true;
				//$intform 1：代表新开暖楼 2：代表最新更新贴暖楼3：代表搬贴暖楼
				$this->arrWarmSuccPids[]=$postInfo['thread_id'].'_'.$arrResponse ['res']['post_id'];
// 				statLog('[pro=client urlkey=msgpush/scripts/warmfloor _client_type=2 _client_version=7.0.4 uid='.$postInfo['user_id'].' tid='.$postInfo['thread_id'].' pid ='.$arrResponse ['res']['post_id'].' errno=0 uip=3778490378 agent=twlive is_warmpost=3]');
				$this->copyThreadStatlog('msgpush/scripts/warmfloor',$arrResponse ['res']['user_id'], $postInfo['thread_id'],$arrResponse ['res']['post_id'],'is_warmThread',3);
				break;
			}
	
		}
		return $boolWarmPost;
	
	
	}
	/**
	 * 判断吧是否能发图文直播贴
	 * @param
	 * @return boolean
	 */
	private function isOpenTWZhiBo(){
		$arrInput  = array(
				'forum_id' => $this->forumId,
		);
		$arrRes = Molib_Tieba_Service::call('livegroup','isOpenTWZhiBo',$arrInput);
		if ($arrRes === false　|| Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']){
			return false;
		}else {
			$isOpen =$arrRes['isOpen'];
			if($isOpen == true){
				return true;
			}else{
				return false;
			}
			 
		}
	}
	
	/**
	 *检查帖子是否符合搬贴要求
	 * @param：null
	 * @return：null
	 */
	private function  checkThreadType($intThreadId){
		$arrInput = array (
			'thread_ids' => array (
				$intThreadId,
			),
			'need_abstract' => 1,
			'forum_id' => null,
			'need_photo_pic' => 1,
			'need_user_data' => 0,
			'call_from' => 'client_frs',
		);
		$arrOutput = Tieba_Service::call ( 'post', 'mgetThread', $arrInput, null, null,'post','php','utf-8'  );
		//图文直播搬贴改变帖子类型为图文直播贴
		Molib_Util_ThreadFilter::setThreadTypesByCopyTwZhiBo($arrOutput['output']['thread_list']);
		if ($arrOutput === false || empty ($arrOutput )) {
			Bingo_Log::fatal('call post:mgetThread failed.[sevice_name:post][method:mgetThread] [input:' . serialize ( $arrInput ) . '] [output:' . serialize ( $arrOutput ) . ']' );
			return false;
		}
		$threadInfo= $arrOutput ['output'] ['thread_list'] [$intThreadId];
		$arrThreadType = Tieba_Type_Thread::getTypeArray ($threadInfo ['thread_types'] );
		if($this->from ==2){
			if($arrThreadType ['is_twzhibo_thread'] == true) {
				return true;
			}else {
				return false;
			}
		}else {
			Bingo_Log::warning('wangxbd======$$arrThreadType=='.$intThreadId.'======='.var_export($arrThreadType,true));
			if((empty($arrThreadType) ||$threadInfo ['thread_types'] == 1024) || (count($arrThreadType) ==1 && $arrThreadType ['is_pic'] == true)) {
				$arrMedia =$threadInfo['media'];
// 				Bingo_Log::warning('wangxbd======$arrMedia=='.var_export($threadInfo,true));
				if(!empty($arrMedia)){
					foreach ($arrMedia as $media){
						if($media['type'] == 'pic'){
							$this->arrCoverPicUrl[] = $media['big_pic'];
						}
					}
				}
				return true;
			}else{
				return false;
			}
		}
	}
	/**
	 *给帖子加上封面
	 * @param：null
	 * @return：null
	 */
	private function setLiveCoverPic($intThreadId){
		//找出满足条件的封面
		$intLiveCoverPid =null;
// 		Bingo_Log::warning('wangxbd=====arrCoverPicUrl==='.var_export($this->arrCoverPicUrl,true));
		if(empty($this->arrCoverPicUrl)){
			//从图片库 中瞬间取一张 
			$intLiveCoverPid= $this->getSrandLiveCoverPicLib();
			$this->setLiveCoverService($intLiveCoverPid, $intThreadId,true);
		}else{
			foreach ($this->arrCoverPicUrl as $strPic){
// 				Bingo_Log::warning('wangxbd=====$strPic==='.var_export($strPic,true));
				$picSign = Molib_Util_ClientImgLogic::getPicSignFromUrl($strPic);
    			$arrOutput = Space_Urlcrypt::decodePicUrlCrypt($picSign);
		    	if($arrOutput === false || !isset($arrOutput[0]) ||
		    			($intFid && intval($arrOutput[0]) != $intFid)){
		    		Bingo_Log::warning("decode pic id error! input[".$strId."]");
		    		continue;
		    	}
		    	
		    	$picId = $arrOutput[1];
		    	$ret=Space_Imgquery::queryPictureInfo(array('pic_id' => $picId));
		    	$arrPicWH =array();
		    	Bingo_Log::warning('wangxbd=====$$ret==='.var_export($ret,true));
		    	if($ret === false || $ret['err_no'] != 0 ){
		    		Bingo_Log::warning('Space_Imgquery::queryPictureInfo:fail_space_query pid:'.$picId.'|'.serialize($ret));
		    		continue;
		    	}else{
		    		$intPicW = $ret['width'];
		    		$intPicH = $ret['height'];
		    		if($intPicW < 480 ||$intPicH <320){
		    			continue;
		    		}else if($intPicW > $intPicH){
		    			$intMinH =(intPicW*1.5)/3;
		    			$intMaxH =(intPicW*2.5)/3;
		    			if($intPicH >=$intMinH && $intPicH <=$intMaxH){
		    				$intLiveCoverPid = $picId;
		    				break;
		    			}
		    		}else if($intPicW < $intPicH){
		    			$intMinW =($intPicH*2.5)/2;
		    			$intMaxW =($intPicH*3.5)/2;
		    			if($intPicW >=$intMinW && $intPicW <=$intMaxW){
		    				$intLiveCoverPid = $picId;
		    				break;
		    			}
		    		}
		    	}
			}
			Bingo_Log::warning('wangxbd=====$$$intLiveCoverPid==='.var_export($intLiveCoverPid,true));
			if($intLiveCoverPid != null){
				$this->setLiveCoverService($intLiveCoverPid, $intThreadId,false);
			}else {
				$intLiveCoverPid= $this->getSrandLiveCoverPicLib();
				$this->setLiveCoverService($intLiveCoverPid, $intThreadId,true);
			}
		}
	}
	/**
	 * 从图片库随机取得图片
	 * @param 
	 * @return 
	 */
	private function getSrandLiveCoverPicLib(){
		
		$arrPicLib = LiveCoverPicLib::$CONST_ARRCOVERPIC_LIB;
		$picIdIndex =array_rand($arrPicLib,1);
// 		Bingo_Log::warning('wangxbd===SrandLiveCover======='.var_export($picIdIndex,true));
		$intLiveCoverPid= $arrPicLib[$picIdIndex];
// 		Bingo_Log::warning('wangxbd====SrandLiveCover======'.var_export($intLiveCoverPid,true));
		return $intLiveCoverPid;
	}
	/**
	 * 设置封面，先走审核的
	 * @param unknown $picId
	 * @return Ambigous <string, mixed>
	 */
	private function setLiveCoverService($intLiveCoverPid,$intThreadId,$boolPass){
		Bingo_Log::warning('wangxbd=====$$intLiveCoverPid==='.var_export($intLiveCoverPid,true));
		if(empty($intLiveCoverPid)){
			return ;
		}
		//走审核
		$strLiveCoverPic =$this->generateNewUrl($intLiveCoverPid);
		$arrInput =array(
			'user_id'   =>  $this->intUid,
			'thread_id' => $intThreadId,
			'livecover_src' => $strLiveCoverPic,
			'status' => 0,
		);
// 		Bingo_Log::warning('wangxbd=====$$ret==='.var_export($arrInput,true));
		$arrRes = Tieba_Service::call('livegroup', 'addLiveCoverPicRedis', $arrInput);
// 		Bingo_Log::warning('wangxbd=====$$addLiveCoverPicRedis==='.var_export($arrRes,true));
		$arrParamInput =array(
			'forumId'   =>  $this->forumId,
			'threadId'  =>  $intThreadId,
			'coverName' => $strLiveCoverPic,
			'coverId' =>$intLiveCoverPid,
			'anchorId' => $this->intUid,
		);
// 		Bingo_Log::warning('wangxbd=====$$ret==='.var_export($arrParamInput,true));
		$arrRes = Tieba_Service::call('livegroup', 'addMisCoverForLivePost', $arrParamInput);
// 		Bingo_Log::warning('wangxbd=====$$addMisCoverForLivePost==='.var_export($arrRes,true));
		
		if($boolPass){
			$arrInput =array(
				'operatorId' => 1,
				'operatorName' => 'machine',
				'info' =>array(array(
					'forumId'   =>  $this->forumId,
					'threadId'  =>  $intThreadId,
					'anchorId' => $this->intUid,
					'coverId' =>$intLiveCoverPid,
					'coverName' => $strLiveCoverPic,
					'addedTime' => date("Y-m-d H:i:s",time()),
					),
				),
			);
// 			Bingo_Log::warning('wangxbd=====$arrInput==='.var_export($arrInput,true));
			$arrRes = Tieba_Service::call('livegroup', 'passMisCoverForLivePost', $arrInput);
// 			Bingo_Log::warning('wangxbd=====$$passMisCoverForLivePost==='.var_export($arrRes,true));
			//livecoverpic=2 代表使用图库
// 			statLog('[pro=client urlkey=msgpush/scripts/copythread _client_type=2 _client_version=7.0.4 uid='.$this->intUid.' tid='.$intThreadId.' errno=0 uip=3778490378 agent=twlive livecoverpic=2]');
			$this->copyThreadStatlog('msgpush/scripts/copythread',$this->intUid, null,'livecoverpic',2);
		}
	}
	

	
	/**
	 * 根据pid重现生成图片url
	 * @param unknown $picId
	 * @return Ambigous <string, mixed>
	 */
	private static function generateNewUrl($picId){
		$strUrl = '';
		if(isset($picId)){
			$obj = new Bd_Pic_UrlCrypt();
			$arrIput = array(
				"pic_id" => $picId,
				'foreign_key' => 0,
				"product_name" => "forum",
				"pic_spec" => 'pic',//图片处理特征，abpic表示小图，其他参加文档或wiki
			);
			$arrReqs[] = $arrIput;
			$ret = $obj->BatPid2Url($arrReqs);
			 
			if (isset($ret['resps'][0])) {
				$strUrl = $ret['resps'][0];
				//域名统一 *.cp01-ocean-400.epc.baidu.com:8082 统一转换成c.cp01-ocean-400.epc.baidu.com:8082
				$strUrl = preg_replace('/http:\/\/[a-z].hiphotos/','http://c.hiphotos',$strUrl);
			}
		}
	
		return $strUrl;
	}
	/**
	 *把楼主升级成普通主播
	 * @return：null
	 */
	private function setTWAnchorLevel(){
		$arrReq = array(
				'user_id'=> $this->intUid,
				'source' => 10,
		) ;
		$arrRes = Molib_Tieba_Service::call('livegroup','upgradeAnchorlevel',$arrReq);
		Bingo_Log::warning('wangxbd======$upgradeAnchorlevel======'.var_export($arrRes,true));
		if (!$arrRes){
			Bingo_Log::warning('call livegroup:upgradeAnchorlevel failed. [input:'.serialize($arrReq).'] [output:'.serialize($arrRes).']');
			return false;
		}
		if ($arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning('call livegroup:upgradeAnchorlevel failed. [input:'.serialize($arrReq).'] [output:'.serialize($arrRes).']');
			return false;
		}
	}
	
	/**
	 * 校验图文直播主播等级
	 * @param unknown $arrVoiceInput
	 * @return boolean
	 */
	private function checkTWZhiBoUserLevel(){
		$arrReq = array(
			'user_ids'=> array(
				$this->intUid,
			),
		) ;
		$arrRes = Molib_Tieba_Service::call('livegroup','getAnchorAllAttr',$arrReq);
		if (!$arrRes){
			Bingo_Log::warning('call livegroup:getAnchorAllAttr failed. [input:'.serialize($arrReq).'] [output:'.serialize($arrRes).']');
			return false;
		}
		if ($arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning('call livegroup:getAnchorAllAttr failed. [input:'.serialize($arrReq).'] [output:'.serialize($arrRes).']');
			return false;
		}
		$intBlackList= intval($arrRes['tw_anchor_info'][$this->intUid]['in_black_list']);
		if($intBlackList ==1){
			return false;
		}else{
			return true;
		}
	
	}
	/**
	 * 生成统计日志
	 * @param unknown $arrVoiceInput
	 * @return boolean
	 */
	private function copyThreadStatlog($urlkey,$uid,$intTid,$intPid,$strKey,$intValue){
		
		Tieba_Stlog::setFileName('wap');
		Tieba_Stlog::addNode('pro', 'client');
		Tieba_Stlog::addNode('urlkey', $urlkey);
		Tieba_Stlog::addNode('_client_type', 2);
		Tieba_Stlog::addNode('_client_version', '7.0.4');
		Tieba_Stlog::addNode('uid', $uid);
		Tieba_Stlog::addNode('tid', $intTid);
		if(isset($intPid) && $intPid >0){
			Tieba_Stlog::addNode('pid', $intPid);
		}
		Tieba_Stlog::addNode('errno', 0);
		Tieba_Stlog::addNode('uip', 3778490378);
		Tieba_Stlog::addNode('agent', 'twlive');
		Tieba_Stlog::addNode($strKey, $intValue);
		$arrLogData = Tieba_Stlog::getLogData();
		Bingo_Log::warning('wangxbd=====copyThreadStatlog=========$'.var_export(11232,true));
		$intErrno = isset($arrLogData['errno']) ? intval($arrLogData['errno']) : 0;
		Tieba_Stlog::notice();
// 		Bingo_Log::notice('stat'.Tieba_Stlog::notice(), '', '', 0, 0, array(), $intErrno);
		Bingo_Log::warning('wangxbd=====copyThreadStatlog=========$'.var_export($arrLogData,true));
	
}
	

}


