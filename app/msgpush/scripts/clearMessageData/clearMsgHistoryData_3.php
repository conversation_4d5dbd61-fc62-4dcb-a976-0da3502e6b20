<?php

define ('EASYSCRIPT_DEBUG',true);          //debug 模式
define ('EASYSCRIPT_THROW_EXEPTION',true);  //抛异常模式

define ('SCRIPTNAME',basename(__FILE__,".php"));   //定义脚本名
define ('BASEPATH',dirname(__FILE__));
define ('CONFPATH',BASEPATH."/conf");
define ('DATAPATH',BASEPATH."/data");
define ('LOGPATH',BASEPATH."/log");
set_include_path(get_include_path() . PATH_SEPARATOR. BASEPATH.'/../../');

define ('REDIS_NAME', 'msglogic');

define ('SCRIPT_RUN_STATE_KEY', SCRIPTNAME.'_runstate');
define ('MAXGROUP_KEY', SCRIPTNAME.'_max_group_id');
define ('DEL_FAIL_MSGID_LIST_KEY', SCRIPTNAME.'_delete_fail_msgid_list');


define ('FRAG_ALL', "all");

/**
 * auto load php file
 * @param unknown $strClassName
 */
function __autoload($strClassName)
{
    require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');

Lib_Util_Log::notice(SCRIPTNAME.' script begin runing.');

define ('LOG', 'log');
Bingo_Log::init(array(
    LOG => array(
        'file'  => LOGPATH ."/". SCRIPTNAME. ".log",
        'level' => 0xFF,
    ),
), LOG);

$redis = null;

try{
    $arrParam = explode('_', SCRIPTNAME);
    if(2 != count($arrParam) || 0 > intval($arrParam[1]) || 3 < intval($arrParam[1])){
        Lib_Util_Log::warning('script name set is invalid,name:['.SCRIPTNAME.'] arrParam:['.serialize($arrParam).']');
        exit;
    }
	$scriptConf = array(
	    'memory_limit' => '1024M',
	    'data_path'      => DATAPATH,
	    'conf_path'      => CONFPATH,
	    'lock_file_name' => SCRIPTNAME.'.lock',
	    'done_file_name' => SCRIPTNAME.'.done',
	    'db_alias' => array(
	        'im'     => 'forum_im',
	    ),
	    'conf_alias' => array(
	        'main'     => 'main.conf',
	    ),
	);
	$easyScriptObj = new Lib_Util_EasyScript($scriptConf);
    //防止脚本重复执行
    if( $easyScriptObj->checkScriptIsRuning() === true ){
        Lib_Util_Log::notice(SCRIPTNAME.' script is runing.');
        echo SCRIPTNAME." script is runing\r\n";
        exit;
    }
    Lib_Util_Log::notice(SCRIPTNAME.' script begin runing.');
	echo SCRIPTNAME." script begin\r\n";
	$redis = new Bingo_Cache_Redis(REDIS_NAME);
	if(null == $redis){
	    Lib_Util_Log::warning(SCRIPTNAME.'call redis fail.');
	    exit;
	}
	//从redis中获取当前脚本运行的状态
	$currentRunState = null;
	$arrRedisReq = array(
	    'key' => SCRIPT_RUN_STATE_KEY,
	);
	$retRedis = $redis->GET($arrRedisReq);
	if(null != $retRedis && isset($retRedis['ret']) && 0 == $retRedis['err_no']){
	    Lib_Util_Log::notice('redis return ret:['.serialize($retRedis).'] req:['.serialize($arrRedisReq).']');
	    foreach ($retRedis['ret'] as $key => $value){
	        if(SCRIPT_RUN_STATE_KEY == $key){
	            $currentRunState = $value;
	            break;
	        }
	    }
	    if('isRun' == $currentRunState){
	        //当前脚本正在运行，直接退出
	        echo SCRIPTNAME." script is runing\r\n";
	        Lib_Util_Log::notice(SCRIPTNAME.' script is runing.Run flag:['.$currentRunState.'] req:['.serialize($arrRedisReq).']');
	        exit;
	    }
	}
	//把当前运行标志置为运行状态
	$currentRunState = 'isRun';
	$arrRedisReq = array(
	    'key' => SCRIPT_RUN_STATE_KEY,
	    'value' => $currentRunState,
	);
	$retRedis = $redis->SET($arrRedisReq);
	if(null == $retRedis || !isset($retRedis['ret']) || 0 != $retRedis['err_no']){
	    Lib_Util_Log::warning(SCRIPTNAME.' set run flag fail.ret:['.serialize($retRedis).'] req:['.serialize($arrRedisReq).']');
	    exit;
	}
	//获取脚本处理的分片标示
	$frag = null;
	$frag = $arrParam[1]; //$easyScriptObj->conf_main_get('current_frag_no');
	if(null === $frag){
	    $frag = FRAG_ALL;  //如果没有设置，则全部分片串行处理
	}
	Lib_Util_Log::notice('current_frag_no:'.serialize($frag));
	//获取一次删除处理的最大群组数
	$dealGroupNum = intval($easyScriptObj->conf_main_get('max_deal_num', 5000));
	if(0 >= $dealGroupNum){
	    $dealGroupNum = 5000;
	}
	Lib_Util_Log::notice('dealGroupNum:'.serialize($dealGroupNum));
	//获取删除历史数据的时间间隔
	$clearTimeInterval = intval($easyScriptObj->conf_main_get('clear_time_interval', 180)) * 86400;
	if(0 >= $clearTimeInterval){
	    $clearTimeInterval = 180*86400; //默认为半年的数据
	}
	Lib_Util_Log::notice('clearTimeInterval:'.serialize($clearTimeInterval));
	//获取单次删除msg的最大行数
	$clearMsgNumStep = intval($easyScriptObj->conf_main_get('clear_msg_num_step', 500));
	if(0 >= $clearMsgNumStep){
	    $clearMsgNumStep = 500;
	}
	Lib_Util_Log::notice('clearMsgNumStep:'.serialize($clearMsgNumStep));
	//获取分片删除间隔
	$clearFragTimeInterval = intval($easyScriptObj->conf_main_get('clear_frag_time_interval', 2));
	if( 0 > $clearFragTimeInterval){
	    $clearFragTimeInterval = 1;
	}
	Lib_Util_Log::notice('clearFragTimeInterval:'.serialize($clearFragTimeInterval));
	//获取每次删除删除msginfo的间隔
	$clearMsgStepTimeInterval = intval($easyScriptObj->conf_main_get('clear_msg_step_time_interval', 2));
	if(0 >$clearMsgStepTimeInterval){
	    $clearMsgStepTimeInterval = 1;
	}
	Lib_Util_Log::notice('clearMsgStepTimeInterval:'.serialize($clearMsgStepTimeInterval));
	
	
	//获取保存的上次的最大的groupId
	$maxGroupId = 0;
	$arrRedisReq = array(
	    'key' => MAXGROUP_KEY,
	);
	$retRedis = $redis->GET($arrRedisReq);
	if(null != $retRedis && isset($retRedis['ret']) && 0 == $retRedis['err_no']){
	    Lib_Util_Log::notice(SCRIPTNAME.' from redis get ret:['.serialize($retRedis).'] req:['.serialize($arrRedisReq).']');
	    foreach ($retRedis['ret'] as $key => $value){
	        if(MAXGROUP_KEY == $key){
	            $maxGroupId = intval($value);
	            break;
	        }
	    }
	}
	/*$ret = $easyScriptObj->getLastSaveData('max_group_id');
	if(null != $ret){
	    $maxGroupId = intval($ret);
	}*/
	Lib_Util_Log::notice('maxGroupId:'.serialize($maxGroupId));
	//获取上次删除失败的MsgId列表
	$arrDelFailMsgIdlist = array();
	$arrRedisReq = array(
	    'key' => DEL_FAIL_MSGID_LIST_KEY,
	);
	$retRedis = $redis->GET($arrRedisReq);
	if(null != $retRedis && isset($retRedis['ret']) && 0 == $retRedis['err_no']){
	    Lib_Util_Log::notice(SCRIPTNAME.' from redis get ret:['.serialize($retRedis).'] req:['.serialize($arrRedisReq).']');
	    foreach ($retRedis['ret'] as $key => $value){
	        if(DEL_FAIL_MSGID_LIST_KEY == $key){
	            $arrDelFailMsgIdlist = unserialize($value);
	            break;
	        }
	    }
	}
	//$arrDelFailMsgIdlist = $easyScriptObj->getLastSaveData('delete_fail_msgid_list');
	Lib_Util_Log::notice('arrDelFailMsgIdlist:'.serialize($arrDelFailMsgIdlist));
	
	$strSql = "select group_id from im_group_info where group_id > %d order by group_id limit %d";
	$strSql = sprintf($strSql, $maxGroupId, $dealGroupNum);
	
	Lib_Util_Log::notice('strSql:'.$strSql);
	
	Bingo_Timer::start('db_im_query_groupid');
	$allGroupIds = $easyScriptObj->db_im_query($strSql);
	Bingo_Timer::end('db_im_query_groupid');
	Lib_Util_Log::notice('allGroupIds count:'.serialize(count($allGroupIds)));
	if(false == $allGroupIds || null == $allGroupIds || empty($allGroupIds)){
	    Lib_Util_Log::warning('Select groupid fail. sql:['.$strSql.'] ret:['.serialize($allGroupIds).']');
	    return ;
	}
	Lib_Util_Log::notice('>>>>>>>>>>>handle frag<<<<<<<<<<<<');
	//把查询的groupid分成四个分片
	$arrGroupFlag0 = array();
	$arrGroupFlag1 = array();
	$arrGroupFlag2 = array();
	$arrGroupFlag3 = array();
	foreach ($allGroupIds as $oneGroupId){
	    $groupid = intval($oneGroupId['group_id']);
	    if($groupid > $maxGroupId){
	        $maxGroupId = $groupid;
	    }
	    switch ($groupid%4){
	        case 0:
	            $arrGroupFlag0[] = $groupid;
	            break;
	        case 1:
	            $arrGroupFlag1[] = $groupid;
	            break;
	        case 2:
	            $arrGroupFlag2[] = $groupid;
	            break;
	        case 3:
	            $arrGroupFlag3[] = $groupid;
	            break;
	    }
	}
	$arrGroupFlagAll = array(
	    '0' => $arrGroupFlag0,
	    '1' => $arrGroupFlag1,
	    '2' => $arrGroupFlag2,
	    '3' => $arrGroupFlag3,
	);
	//Lib_Util_Log::notice('arrGroupFlagAll:'.serialize($arrGroupFlagAll));
	
	$strSql ="";
	$delMaxTime = time() - $clearTimeInterval;
	if(0 >= $delMaxTime){
	    $strSql = "select msg_id from im_message_info where group_id in (%s) and create_time < UNIX_TIMESTAMP(NOW())-%d";
	    $delMaxTime = $clearTimeInterval;
	}else{
	    $strSql = "select msg_id from im_message_info where group_id in (%s) and create_time < %d";
	}
	Lib_Util_Log::notice('strSql:['.serialize($strSql).'] delMaxTime:['.serialize($delMaxTime).']');
	//查询msgid
	
	//$strSql = "Delete from im_message_info where group_id in (%s) and UNIX_TIMESTAMP(NOW())-create_time > %d";
	$strDelSql = "Delete from im_message_info where group_id in (%s) and msg_id in (%s)";
	
	$arrTmpMsgId = array();
	if(null != $arrDelFailMsgIdlist && !empty($arrDelFailMsgIdlist)){
	    //首先尝试删除上一次没有删除成功的groupid列表
	    foreach ($arrDelFailMsgIdlist as $strGroupidList_i => $strMsgIdList){
	        $arrTmp = explode('_', $strGroupidList_i);
	        $sql = sprintf($strDelSql, $arrTmp[0], $strMsgIdList);
	        Lib_Util_Log::notice('last strDelSql:'.serialize($sql));
	        Bingo_Timer::start('db_im_delete_msgid');
	        $dbRes = $easyScriptObj->db_im_query($sql);
	        Bingo_Timer::end('db_im_delete_msgid');
	        if(false == $dbRes){
	            //没有删除成功仍然保存下来
	            $arrTmpMsgId[$strGroupidList_i] = $strMsgIdList;
	            Lib_Util_Log::warning('delete history message fail. sql:['.$sql.'] ret:['.serialize($dbRes).']');
	        }
	        $dbRes = $easyScriptObj->db_im_close();
	        if(false == $dbRes){
	            Lib_Util_Log::warning('close db connection fail. seq:['.strval($i).']');
	        }
	        if(0 < $clearMsgStepTimeInterval){
	            sleep($clearMsgStepTimeInterval);   //删除间隔
	        }
	    }
	}
	Lib_Util_Log::notice('>>>>run frag:['.serialize($frag).']<<<<');
	$arrDelFailMsgIdlist = array();
	foreach ($arrGroupFlagAll as $fragKey => $arrGroupFlagX){
	    if(FRAG_ALL != $frag && intval($frag) != intval($fragKey)){
	        Lib_Util_Log::notice('fragKey:['.$fragKey.'] current set handle frag:['.strval($frag).'], no handle!!');
	        continue;  //如果不是全分片并且当前分片与设定的分片不同时
	    }
	    $strGroupIdList = join(',', $arrGroupFlagX);
	    $sql = sprintf($strSql, $strGroupIdList, $delMaxTime);
	    Lib_Util_Log::notice(strval($fragKey).'_sql:'.serialize($sql));
	    //首先查询出具体的msgid
	    Bingo_Timer::start('db_im_query_msgid');
	    $arrRetMsgId = $easyScriptObj->db_im_query($sql);
	    Bingo_Timer::end('db_im_query_msgid');
	    $arrMsgId = array();
	    foreach ($arrRetMsgId as $oneMsgId){
	        $arrMsgId[] = $oneMsgId['msg_id'];
	    }
	    $iMsgIdCount = count($arrMsgId);
	    Lib_Util_Log::notice('MsgIdCount:['.serialize($iMsgIdCount).']');
	    if(0 == $iMsgIdCount || empty($arrMsgId)){
	        Lib_Util_Log::warning('msgis is empty. MsgIdCount:['.serialize($iMsgIdCount).'] arrMsgId:['.serialize($arrMsgId).']');
	        continue;
	    }
	    //根据每次删除的最大函数进行分次删除
	    $iLoopCount = intval($iMsgIdCount/$clearMsgNumStep) + 1;
	    Lib_Util_Log::notice('iLoopCount:['.serialize($iLoopCount).']');
	    for($i=0; $i<$iLoopCount; $i++){
	        $arrStepMsgId = array();
	        //获取每次限定长度删除的msgid数组
	        if($i == $iLoopCount-1){
	            $arrStepMsgId = array_slice($arrMsgId, $i*$clearMsgNumStep, $iMsgIdCount-$i*$clearMsgNumStep);
	        }else{
	            $arrStepMsgId = array_slice($arrMsgId, $i*$clearMsgNumStep, $clearMsgNumStep);
	        }
	        if(empty($arrStepMsgId)){
	            continue;
	        }
	        $strMsgIdList = join(',', $arrStepMsgId);
	        $sql = sprintf($strDelSql, $strGroupIdList, $strMsgIdList);
	        Lib_Util_Log::notice('current_strDelSql:'.strval($i).'_'.$sql);
	        Bingo_Timer::start('db_im_delete_msgid');
	        $dbRes = $easyScriptObj->db_im_query($sql);
	        Bingo_Timer::end('db_im_delete_msgid');
	        if(false == $dbRes){
	            //未删除成功的id以：$strGroupidList_$i => $strMsgIdList的数组保存
	            $arrDelFailMsgIdlist[$strGroupIdList.'_'.strval($i)] = $strMsgIdList;
	            Lib_Util_Log::warning('delete history message fail. sql:['.$sql.'] ret:['.serialize($dbRes).']');
	        }
	        $dbRes = $easyScriptObj->db_im_close();
	        if(false == $dbRes){
	            Lib_Util_Log::warning('close db connection fail. seq:['.strval($i).']');
	        }
	        if(0 < $clearMsgStepTimeInterval){
	            sleep($clearMsgStepTimeInterval);   //删除间隔
	        }
	    }  
	    if(0 < $clearFragTimeInterval){
	        sleep($clearFragTimeInterval);  //分片间的处理间隔
	    }
	}
	
	//合并两次没有成功的数据
	if(!empty($arrTmpMsgId)){
	    $arrDelFailMsgIdlist = array_merge($arrDelFailMsgIdlist, $arrTmpMsgId);
	}
	
	//保存此次最大的groupid以便用于下次查询
	//$easyScriptObj->addSaveData('max_group_id', $maxGroupId);
	$arrRedisReq = array(
	    'key' => MAXGROUP_KEY,
	    'value' => $maxGroupId,
	);
	$retRedis = $redis->SET($arrRedisReq);
	if(null == $retRedis || !isset($retRedis['err_no']) || 0 != $retRedis['err_no']){
	    Lib_Util_Log::warning(SCRIPTNAME.' add to redis fail.ret:['.serialize($retRedis).'] req:['.serialize($arrRedisReq).']');
	}
	//保存删除失败的groupid列表用于下次删除
	//$easyScriptObj->addSaveData('delete_fail_msgid_list', $arrDelFailMsgIdlist);
	$retRedis = null;
	if(empty($arrDelFailMsgIdlist)){
	    $arrRedisReq = array(
	        'key' => DEL_FAIL_MSGID_LIST_KEY,
	    );
	    $retRedis = $redis->DEL($arrRedisReq);
	}else{
	    $arrRedisReq = array(
	        'key' => DEL_FAIL_MSGID_LIST_KEY,
	        'value' => serialize($arrDelFailMsgIdlist),
	    );
	    $retRedis = $redis->SET($arrRedisReq);
	}
	if(null == $retRedis || !isset($retRedis['err_no']) || 0 != $retRedis['err_no']){
	    Lib_Util_Log::warning(SCRIPTNAME.' add OR rem to redis fail.ret:['.serialize($retRedis).'] req:['.serialize($arrRedisReq).']');
	}
	//删除运行标志
	$arrRedisReq = array(
	    'key' => SCRIPT_RUN_STATE_KEY,
	);
	$retRedis = $redis->DEL($arrRedisReq);
	if(null == $retRedis || !isset($retRedis['err_no']) || 0 != $retRedis['err_no']){
	    Lib_Util_Log::warning(SCRIPTNAME.' remove runflag from redis fail.ret:['.serialize($retRedis).'] req:['.serialize($arrRedisReq).']');
	}
	//把保存的数据写入文件
	//$easyScriptObj->runSuccess();
	Lib_Util_Log::notice(SCRIPTNAME.' run success!!!!');
}catch(Exception $e){
	if($easyScriptObj !== false){
       Bingo_Log::warning(SCRIPTNAME.' script run fail!'.$easyScriptObj->getErr2String());
    }else{
       Bingo_Log::warning(SCRIPTNAME.' script run fail![null]');
    }
    Lib_Util_Log::notice(SCRIPTNAME.' run fail. desc:'.$easyScriptObj->getErr2String());
}

//删除运行标志
$arrRedisReq = array(
    'key' => SCRIPT_RUN_STATE_KEY,
);
if(null != $redis){
    $retRedis = $redis->DEL($arrRedisReq);
    if(null == $retRedis || !isset($retRedis['err_no']) || 0 != $retRedis['err_no']){
        Lib_Util_Log::warning(SCRIPTNAME.' remove runflag from redis fail.ret:['.serialize($retRedis).'] req:['.serialize($arrRedisReq).']');
    }
}

Lib_Util_Log::notice(SCRIPTNAME." script stop");
Lib_Util_Log::notice(SCRIPTNAME.' run success.');