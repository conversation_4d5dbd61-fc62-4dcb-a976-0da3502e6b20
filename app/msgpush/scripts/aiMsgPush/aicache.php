<?php
/**
 * cache
 * <AUTHOR>
 *
 */
class AiCache {

    private static $_cache = null;
        
    //cache的pid
    const CACHE_PID = 'forum_twlive';
    //forum_present
    
    //cache配置总开关，方便测试
	const SWITCH_OF_CACHE = true;
	
	//所有cache的key的前缀，修改前缀即可失效现有所有cache
	const PREFIX_ALL_KEY = 'ai_push_';

    /**
     * 初始化cache
     * @return [type] [description]
     */
    private static function initCache(){
        if(false === self::SWITCH_OF_CACHE) {
            return null;
        }
        if(self::$_cache && self::$_cache->isEnable()){
            return self::$_cache ;
        }
        
        Bingo_Timer::start('memcached_init');
        self::$_cache = new Bingo_Cache_Memcached(self::CACHE_PID);
        Bingo_Timer::end('memcached_init');

        if(!self::$_cache || !self::$_cache->isEnable()){
            Bingo_Log::warning("init cache fail.");
            self::$_cache = null;
            return null;
        }
        return self::$_cache;
    }
    
    /**
     * @param null
     * @return string
     */
    public static function getKeyPrefix(){
    	return self::PREFIX_ALL_KEY;
    }
    
    /**
     * 获取单个cache
     * @param  [type] $strKey [description]
     * @return [type]         [description]
     */
	public static function getCache($strKey){
    	if(false === self::SWITCH_OF_CACHE) {
    		return null;
    	}
        if(!self::$_cache){
        	self::initCache();
        	if(!self::$_cache->isEnable()) {
        		return null;
        	}
        }
        $strKey = self::PREFIX_ALL_KEY.$strKey;
        Bingo_Timer::start('memcached_get');
        $mixRes = self::$_cache->get($strKey);
        Bingo_Timer::end('memcached_get');
        return $mixRes;
	}

    /**
     * 批量获取cache
     * @param  [type] $arrKey [description]
     * @return [type]         [description]
     */
	public static function mgetCache($arrKey, $externalPrefix = ''){
    	if(false === self::SWITCH_OF_CACHE) {
    		return null;
    	}
        if(!self::$_cache){
        	self::initCache();
        	if(!self::$_cache->isEnable()) {
        		return null;
        	}
        }
        if(empty($externalPrefix) || '' == $externalPrefix){
        	foreach ($arrKey as &$strKey) {
        		$strKey = self::PREFIX_ALL_KEY.$strKey;
        	}
        }else{
        	foreach ($arrKey as &$strKey) {
        		$strKey = self::PREFIX_ALL_KEY.$externalPrefix.$strKey;
        	}
        }
        Bingo_Timer::start('memcached_mget');
        $mixRes = self::$_cache->multipleGet($arrKey);
        Bingo_Timer::end('memcached_mget');
        return $mixRes;
	}
	
	/**
	 * 批量删除cache
	 * @param  [type] $arrKey [description]
	 * @return [type]         [description]
	 */
	public static function mremoveCache($arrKey){
	    if(null == $arrKey || empty($arrKey)){
	        return null;
	    }
	    if(false === self::SWITCH_OF_CACHE) {
	        return null;
	    }
	    if(!self::$_cache){
	        self::initCache();
	        if(!self::$_cache->isEnable()) {
	            return null;
	        }
	    }
	    
	    foreach ($arrKey as &$strKey) {
	        $strKey = self::PREFIX_ALL_KEY.$strKey;
	    }

	    Bingo_Timer::start('memcached_mget');
	    $mixRes = self::$_cache->multipleRemove($arrKey);
	    Bingo_Timer::end('memcached_mget');
	    $bolRet = false;
	    foreach ($mixRes as $key => $val){
	        if (CACHE_OK !== $val){
	            Bingo_Log::warning('multi remove cache err no : ['.$val.'] key:['.$key.']');
	            continue;
	        }
	        $bolRet = true;
	    }
	    return $bolRet;
	}

	/**
     * 删除cache
     * @param  [type] $strKey [description]
     * @return [type]         [description]
     */
	public static function removeCache($strKey){
	    if(empty($strKey)){
	        return false;
	    }
    	if(false === self::SWITCH_OF_CACHE) {
    		return true;
    	}
        if(!self::$_cache){
        	self::initCache();
        	if(!self::$_cache->isEnable()) {
        		return false;
        	}
        }

        $strKey = self::PREFIX_ALL_KEY.$strKey;
        Bingo_Timer::start('memcached_del');
        $mixRes = self::$_cache->remove(strval($strKey));
        Bingo_Timer::end('memcached_del');
        if($mixRes === CACHE_OK){
        	return true;
        }
        else{  
        	Bingo_Log::warning('remove cache err no : '.$mixRes." key[$strKey]");
        	return false;
        }
	}

	/**
     * 设置cache
     * @param [type] $strKey      [description]
     * @param [type] $mixValue    [description]
     * @param [type] $intLifeTime [description]
     * @return [type] [description]
     */
	public static function addCache($strKey, $mixValue, $intLifeTime){
    	if(false === self::SWITCH_OF_CACHE) {
    		return true;
    	}

        if(!self::$_cache){
        	self::initCache();
        	if(!self::$_cache->isEnable()) {
        		return false;
        	}
        }
        $strKey = self::PREFIX_ALL_KEY.$strKey;
        $intLifeTime = intval($intLifeTime);
        Bingo_Timer::start('memcached_add');
        $mixRes = self::$_cache->add(strval($strKey), $mixValue, $intLifeTime);
        Bingo_Timer::end('memcached_add');
        //Bingo_Log::warning('[debug]'.CACHE_OK);
        if($mixRes === CACHE_OK){
        	return true;
        }else{  
        	Bingo_Log::warning('add cache err no : '.$mixRes." key[$strKey] val[".
        		serialize($mixValue)."] time[$intLifeTime]");
        	return false;
        }
	}

    /**
     * 批量设置cache
     * @param  [type] $arrKeyAndVal [description]
     * @return [type]               [description]
     */
    public static function maddCache($arrKeyAndVal) {
        if(false === self::SWITCH_OF_CACHE) {
            return true;
        }
    
        if(null === self::$_cache){
            self::initCache();
            if(null === self::$_cache || !self::$_cache->isEnable()) {
                return false;
            }
        }
    
        Bingo_Timer::start('memcached_add');
        $totalCount = count($arrKeyAndVal);
        for ($i = 0 ; $i < $totalCount; $i++) {
            $arrKeyAndVal[$i]['key'] = self::PREFIX_ALL_KEY . $arrKeyAndVal[$i]['key']; 
        }
        $mixRes = self::$_cache->multipleAdd($arrKeyAndVal);
        Bingo_Timer::end('memcached_add');
    
        foreach ($mixRes as $key=>$val)
        {
            $errno = intval($val);
            if (0 !== $errno)
            {
                Bingo_Log::warning("madd cache key=$key, val=$val");
            }
        }
            /*
            if($mixRes === CACHE_OK){
                return true;
            }else{
                Bingo_Log::warning('madd cache err no : '.serialize($mixRes)." vals[".
                        serialize($arrKeyAndVal)."]");
                        return false;
            }
             */
        return true;
    }

}
?>
