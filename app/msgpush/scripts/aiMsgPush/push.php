<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @desc 智能推送脚本消息推送模块
 * @date 2016:08:10 14:40:02
 * @version 1.0
 */


define ('EASYSCRIPT_DEBUG',false);          //debug 模式
define ('EASYSCRIPT_THROW_EXEPTION',false);  //抛异常模式

define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../../' );
define ('SCRIPTNAME',basename(__FILE__,".php"));   //定义脚本名
define ('BASEPATH',dirname(__FILE__));
define ('CONFPATH',BASEPATH."/conf");
define ('DATAPATH',BASEPATH."/data");
//define ('LOGPATH',BASEPATH."/log");
//define ('REDIS_NAME', 'twlive');
set_include_path(get_include_path() . PATH_SEPARATOR. BASEPATH.'/../../');
require_once ROOT_PATH . "app/msgpush/scripts/aiMsgPush/base.php";
require_once ROOT_PATH . "app/msgpush/scripts/aiMsgPush/aicache.php";

/**
 * auto load php file
 * @param unknown $strClassName
 */
function __autoload($strClassName)
{
    require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');
$g_arrTidsInfo = array();
$g_arrTids = array();
$g_arrTasksInfo = array();
$g_flag = 0;
$g_cacheObj = null;


/**
 * 
 * @param unknown $uid
 * @return string|unknown
 */
function getUnameByUid($uid){
	global $g_cacheObj;
	
	if(!is_numeric($uid) || $uid <= 0){
		return '';
	}
	$prefix = 'push_uid_un_';
	$key = $prefix.$uid;
	$strUname = '';
	$bolHitCache = false;
	if($g_cacheObj){
		$retCache = $g_cacheObj->getCache($key);
		if(null != $retCache && !empty($retCache)){
			$strUname = $retCache;
			$bolHitCache = true;
		}
	}
	if($bolHitCache){
		return $strUname;
	}
	$arrInUserId = array(
		'user_id' => array($uid),
	);
	$arrUserName = Tieba_Service::call('user', 'getUnameByUids', $arrInUserId);
	if ($arrUserName != false && $arrUserName['errno'] == Tieba_Errcode::ERR_SUCCESS) {
		$strUname = $arrUserName['output']['unames'][0]['user_name'];
		if($g_cacheObj && !empty($strUname)){
			$g_cacheObj->addCache($key, $strUname, 604800);  //7天的缓存
		}
	}
	return $strUname;
}

/**
 *@param
 *@return
 */
function build_ai_content($arrMsgInfo,$currentDate){
	global $g_arrTidsInfo;

	global $g_arrTasksInfo;

	$defaultUserId = '1501754229';//  贴吧精选
	$task_id = $arrMsgInfo['task_id'];
	$arrContent = array();
	foreach ($arrMsgInfo['tids'] as $tid) {
		if (!isset($g_arrTidsInfo[$tid])){
			debug_log($g_arrTidsInfo[$tid]);
			continue;
		}
		$arrTmp = array(
			"title" => $g_arrTidsInfo[$tid]['title'],
			"url" => strlen($g_arrTidsInfo[$tid]['url']) > 10 ? $g_arrTidsInfo[$tid]['url'] : "http://tieba.baidu.com/p/".$tid,
			"text" => $g_arrTidsInfo[$tid]['abstract'],
			"src" => $g_arrTidsInfo[$tid]['cover'],
			"task_id" => ($currentDate * MSGPUSH_BASE_NUM + $task_id)."",
			"switch" => $g_arrTasksInfo[$task_id]['switch'],
		);
		$arrContent[] = $arrTmp;
	}
	if (empty($arrContent) || !isset($arrContent[0])){
		debug_log($arrContent);
		return array();
	}
	$arrParams = array(
		'client_type' => $g_arrTasksInfo[$task_id]['client_type'],
		'user_id' => $g_arrTasksInfo[$task_id]['official_id'] > 0 ? $g_arrTasksInfo[$task_id]['official_id'] : $defaultUserId,//官方账号
		'user_type' => 4,//用户类型
		'target_uids' =>array($arrMsgInfo['user_id'],),
		'content_type' => 7,//消息类型
		'content' => Bingo_String::array2json($arrContent,'UTF-8'),//消息内容
		'abstract'=> mb_substr($arrContent[0]["title"],0,1000,'utf-8')."...",
		'msg_expire_time' => 3,
		'task_id' => ($currentDate * MSGPUSH_BASE_NUM + $task_id)."",
		'tids' => implode(",", $arrMsgInfo['tids']),
	);
	
	$arrMultiInput = array(
		'serviceName' => 'opmsg',
		'method' => 'sendGfMsgByUids',
		'input' => $arrParams,
		'ie' => 'utf-8',
	);

	return $arrMultiInput;
}

/**
 *@param
 *@return
 */
function build_msg_content($arrMsgInfo,$currentDate,$curTask,$curSubtask){
	global $g_arrTidsInfo;

	$defaultUserId = '1501754229';//  贴吧精选
        $task_id = $curTask['task_id'];
	$subtask_id = $curSubtask['subtask_id'];
	$token = $curTask['token'];

	if($token == 'ipush'){
        $tidArr = explode(',', trim($curSubtask['push_subtask_ids']));
        $arrContent = array();
        foreach ($tidArr as $tid) {
            $intTid = intval($tid);
            if (!isset($g_arrTidsInfo[$intTid])){
                Bingo_Log::warning("[MIS_TRACE_LOG]['GET THREAD INFO FAIL'][".json_encode($g_arrTidsInfo));
                continue;
            }
            $arrTmp = array(
                "title" => $g_arrTidsInfo[$intTid]['title'],
                "url" => strlen($g_arrTidsInfo[$intTid]['url']) > 10 ? $g_arrTidsInfo[$intTid]['url'] : "http://tieba.baidu.com/p/".$intTid,
                "text" => $g_arrTidsInfo[$intTid]['abstract'],
                "src" => $g_arrTidsInfo[$intTid]['cover'],
                "task_id" => ($currentDate * MSGPUSH_BASE_NUM + intval($task_id))."",
                "subtask_id" => $subtask_id."",
                "switch" => $curTask['switch'],
            );
            $arrContent[] = $arrTmp;
        }
        if (empty($arrContent) || !isset($arrContent[0])){
            return array();
        }
        $content = Bingo_String::array2json($arrContent,'UTF-8');
        $arrParams = array(
            'client_type' => $curSubtask['client_type'],
            'user_id' => $curSubtask['official_id'] > 0 ? $curSubtask['official_id'] : $defaultUserId,//官方账号
            'user_type' => 4,//用户类型
            'target_cuids' => $arrMsgInfo['cuids'],
            'content_type' => 7,//消息类型
            'content' => $content,//消息内容
            'abstract'=> mb_substr($arrContent[0]["title"],0,1000,'utf-8')."...",
            'msg_expire_time' => 3,
            'task_id' => ($currentDate * MSGPUSH_BASE_NUM + intval($task_id))."",
            'subtask_id' => $subtask_id."",
            'tids' => $curSubtask['push_subtask_ids'],
        );
        $arrAndroidOfflineParam = array(
            'task_id' => ($currentDate * MSGPUSH_BASE_NUM + intval($task_id))."",
            'subtask_id' => $subtask_id."",
            'content' => array(
                'title' => iconv('UTF-8','GBK','贴吧精选'),
                'description' => iconv('UTF-8','GBK',$arrParams['abstract']),
                'scheme' => $arrContent[0]['url'],
            ),
        );

        $arrParams['android_offline_push'] = $arrAndroidOfflineParam;
    }else{
	    $content = $curSubtask['push_content'];

        $arrParams = array(
            'client_type' => $curSubtask['client_type'],
            'user_id' => $curSubtask['official_id'] > 0 ? $curSubtask['official_id'] : $defaultUserId,//官方账号
            'user_type' => 4,//用户类型
            'target_cuids' => $arrMsgInfo['cuids'],
            'content_type' => 7,//消息类型
            'content' => $content,//消息内容
            'abstract'=> mb_substr($curSubtask['push_title'],0,1000,'utf-8')."...",
            'msg_expire_time' => 3,
            'task_id' => ($currentDate * MSGPUSH_BASE_NUM + intval($task_id))."",
            'subtask_id' => $subtask_id."",
            'tids' => $curSubtask['push_subtask_ids'],
        );
        $arrAndroidOfflineParam = array(
            'task_id' => ($currentDate * MSGPUSH_BASE_NUM + intval($task_id))."",
            'subtask_id' => $subtask_id."",
            'content' => array(
                'title' => iconv('UTF-8','GBK','贴吧精选'),
                'description' => iconv('UTF-8','GBK',$arrParams['abstract']),
                'scheme' => $curSubtask['push_url'],
            ),
        );

        $arrParams['android_offline_push'] = $arrAndroidOfflineParam;
    }

	$arrMultiInput = array(
		'serviceName' => 'opmsg',
		'method' => 'sendGfMsgByCuids',
		'input' => $arrParams,
		'ie' => 'utf-8',
	);
	
	return $arrMultiInput;
}
/**
 *@param
 *@return
 */
function ai_push($arrMsgList,$currentDate){
	global $g_arrTasksInfo;

	$objRalMulti = new Tieba_Multi('msg_push');
	$writeRecord = array();
	foreach ($arrMsgList as $index => $arrMsgInfo){
		if (empty($arrMsgInfo['tids'])){
			debug_log($arrMsgInfo['tids']);
			continue;
		}
		$arrMultiInput = build_ai_content($arrMsgInfo,$currentDate);
		if (empty($arrMultiInput)){
			debug_log($arrMultiInput);
			continue;
		}
		$objRalMulti->register($index . "_opmsg", new Tieba_Service('post'),$arrMultiInput);

		//智能推送，这种情况下要记录推送记录
		$writeRecord[] = array(
			'user_id' => $arrMsgInfo['user_id'],
			'tids' => $arrMsgInfo['tids'],
		);
		
	}

	if (!empty($writeRecord)){
		$arrMultiInput = array(
			'serviceName' => 'common',
			'method' => 'msgpushSavePushList',
			'input' => array('input'=>$writeRecord,),
			'ie' => 'utf-8',
		);
		$objRalMulti->register('common',new Tieba_Service('post'),$arrMultiInput);
	}

	if(DEBUG == 1){
	    $arrRet = array();
        foreach ($arrMsgList as $index => $arrMsgInfo){
            $arrRet[$index . "_opmsg"] = array('errno' => Tieba_Errcode::ERR_SUCCESS);
        }
    }else{
        $objRalMulti->call();
        $arrRet = $objRalMulti->results;
    }

	return $arrRet;
}


/**
*@param
*@return
*/
function dr_push($arrMsgList,$currentDate,$curTask,$curSubtask){
	$objRalMulti = new Tieba_Multi('msg_push');

	foreach ($arrMsgList as $index => $arrMsgInfo){
		
		//add by qifeng02 for debug
		Bingo_Log::warning('task_id:'. $curTask .' directional_push pop cuids count:'.count($arrMsgInfo['cuids']));

		$arrMultiInput = build_msg_content($arrMsgInfo,$currentDate,$curTask,$curSubtask);
		Bingo_Log::warning(Bingo_String::array2json($arrMultiInput));
	   	if (empty($arrMultiInput)){
	   		continue;
	   	}
		$objRalMulti->register($index . "_opmsg", new Tieba_Service('post'),$arrMultiInput);
	}  

	if(DEBUG == 1){
        $arrRet = array();
        foreach ($arrMsgList as $index => $arrMsgInfo){
            $arrRet[$index . "_opmsg"] = array(
                "errno" => Tieba_Errcode::ERR_SUCCESS
            );
        }
    }else{
        $objRalMulti->call();
        $arrRet = $objRalMulti->results;
    }
	return $arrRet;
}

/**
 *@param
 *@return
 */
function save_info($redis,&$statInfo,$curTask,$curSubtask){
    //todo:更新主任务detail
    $input = array(
        'task_id' => $curTask['task_id'],
        'run_date' => $statInfo['run_date'],
        'push_users' => $statInfo['push_users']>0 ? $statInfo['push_users']: 0,
        'success_push_users' => $statInfo['success_push_users']>0 ? $statInfo['success_push_users']: 0,
        'end_time' => time(),
    );
    $ret = server_recall('common','msgpushUpdateTaskDetail',$input, 'gbk', getCallType());
    if(!$ret || $ret['errno'] != 0){
        Bingo_Log::warning('[service call error] call common/msgpushUpdateTaskDetail fail, task_id: ' . $curTask['task_id'] . ', subtask_id: ' . $curSubtask['subtask_id'] . ', error: ' . Bingo_String::array2json($ret));
    }else{
        //todo：更新子任务detail
        $input = array(
            'subtask_id' => $curSubtask['subtask_id'],
            'run_date' => $statInfo['run_date'],
            'push_users' => $statInfo['push_users']>0 ? $statInfo['push_users']: 0,
            'success_push_users' => $statInfo['success_push_users']>0 ? $statInfo['success_push_users']: 0,
            'subtask_end_time' => $statInfo['subtask_end_time'],
        );
        $ret = server_recall('common','updateSubTaskDetail',$input, 'gbk', getCallType());
        if(!$ret || $ret['errno'] != 0){
            Bingo_Log::warning('[service call error] call common/updateSubTaskDetail fail, task_id: ' . $curTask['task_id'] . ', subtask_id: ' . $curSubtask['subtask_id'] . ', error: ' . Bingo_String::array2json($ret));
        }else{
            $statInfo = array();
        }

    }

}

/**
 *@param
 *@return
 */
function sms_push($arrMsgList,$arrMsgInfo,$currentDate,&$statInfo){
	global $g_arrTasksInfo;
	
	//先查5天内已发送的用户
	$getsentmsglog['task_id'] = $input_savelog['task_id'] = $arrMsgInfo['task_id'];
	$getsentmsglog['sent_date'] = date('Ymd',strtotime('-5 day'));
	$getsentmsglog_ret = server_recall('common','sendMsgGetLog',$getsentmsglog);
	$fivedays_sentuids = array();
	foreach($getsentmsglog_ret['data'] as $tmpuid)
	{
		array_push($fivedays_sentuids,$tmpuid['uid']);
	}
	$fivedays_sentuids = array_unique($fivedays_sentuids);
	//发送短信
	$input_msg['content'] = $g_arrTasksInfo[$arrMsgInfo['task_id']]['task_content'];
	$statInfo[$arrMsgInfo['task_id']]['run_date'] = $currentDate;
	foreach($arrMsgList as $temp)
	{
		foreach($temp['user_ids'] as $temp2)
		{
			//只有uid不在5天内发送过的uids中，才发送短信
			if(!in_array($temp2,$fivedays_sentuids))
			{
				$input_msg['userid'] = $temp2;
				$ret = Tieba_Service::call('common','sendMsgByUid',$input_msg,null,null,'post','php','gbk');
				$statInfo[$arrMsgInfo['task_id']]['push_users'] += 1;
				debug_log($statInfo[$arrMsgInfo['task_id']]['push_users']);
				if($ret['errno']===1000)
				{
					$statInfo[$arrMsgInfo['task_id']]['success_push_users'] += 1;
					//将成功发送的短信记录（任务ID、UID、发送日期）
					$input_savelog['uid'] = $input_msg['userid'];
					$input_savelog['sent_date'] = $currentDate;
					server_recall('common','sendMsgSaveLog',$input_savelog);
				}
				usleep(2000000);
			}
		}
	}
	$statInfo[$arrMsgInfo['task_id']]['end_time'] = time();
	return 0;
}

/**
 *@param
 *@return
 */
function init(){
	global $g_arrTidsInfo;
	
	global $g_arrTids;
	
	$g_arrTidsInfo = getThreadInfo($g_arrTidsInfo,MSGPUSH_THREAD_STATUS_PASSED,true);
	$g_arrTidsInfo = getThreadInfo($g_arrTidsInfo,MSGPUSH_THREAD_STATUS_GOOD,true);
	$g_arrTids = array_keys($g_arrTidsInfo);

}
/**
 *@desc 推送统计
 *@param
 *@return
 */
function push_stat($arrMsgList,$arrRet,$currentDate,$currentTime,&$statInfo){
	//统计计划推送人数
	foreach ($arrMsgList as $key => $arrMsgInfo){
		$arrStatUids = array();
		foreach($arrMsgInfo['cuids'] as $cuidInfo){
			$arrStatUids[$cuidInfo['user_id']] = 1;
		}

		$pushTaskId = date('Ymd',time())* MSGPUSH_BASE_NUM + intval($arrMsgInfo['task_id']) ;
		Bingo_Log::warning("system=push partition=mis stage=push task_id=".$pushTaskId.' subtask_id='.$arrMsgInfo['subtask_id']." total_push_users=".count($arrStatUids)." key=".$key);
		$statInfo['push_users'] += count($arrStatUids);
		$statInfo['run_date'] = $currentDate;
		$statInfo['subtask_end_time'] = $currentTime;
	}

	//计算成功推送的人数
	foreach ($arrMsgList as $key => $arrMsgInfo){
		$arrStatUids = array();
		if ($arrRet[$key. "_opmsg"]['errno'] === Tieba_Errcode::ERR_SUCCESS){
			foreach($arrMsgInfo['cuids'] as $cuidInfo){
				$arrStatUids[$cuidInfo['user_id']] = 1;
			}
			$pushTaskId = date('Ymd',time())* MSGPUSH_BASE_NUM + intval($arrMsgInfo['task_id']) ;
                        Bingo_Log::warning("system=push partition=mis stage=push task_id=".$pushTaskId.' subtask_id='.$arrMsgInfo['subtask_id']." success_push_users=".count($arrStatUids)." key=".$key);
			$statInfo['success_push_users'] += count($arrStatUids);
		}
	}
}

/*
 
if (empty($arrMsgAllList)){
	$arrMsgAllList[0] = array(
		'task_id' => $arrMsgInfo['task_id'],
		'tids' =>  $arrMsgInfo['tids'],
		'cuids' => array(),
	);
	foreach($arrMsgList as $index => $msgInfo){
		$arrMsgAllList[0]['cuids'] = array_merge($arrMsgAllList[0]['cuids'],$msgInfo['cuids']);
	}
}
else if (count($arrMsgAllList[0]['cuids']) <= 200 && $arrMsgAllList[0]['task_id'] == $arrMsgInfo['task_id']){
	foreach($arrMsgList as $index => $msgInfo){
		$arrMsgAllList[0]['cuids'] = array_merge($arrMsgAllList[0]['cuids'],$msgInfo['cuids']);
	}
}
else if($arrMsgAllList[0]['task_id'] != $arrMsgInfo['task_id']){
	$arrRet = dr_push($arrMsgAllList,$currentDate);
	push_stat($arrMsgAllList,$arrRet,$currentDate,$currentTime,$statInfo);
	$arrMsgAllList = $arrMsgList;
}
else {
	$arrRet = dr_push($arrMsgAllList,$currentDate);
	push_stat($arrMsgAllList,$arrRet,$currentDate,$currentTime,$statInfo);
	$arrMsgAllList = array();
}
*/


/**
*@desc 推送逻辑
*@param
*@return
*/
function msg_push($arrMsgList,$currentDate,$currentTime,&$statInfo,$curTask,$curSubtask){
//	if ($curSubtask['task_type'] == MSGPUSH_TASK_TYPE_INTELLIGENCE){
//		$arrRet = ai_push($arrMsgList,$currentDate);
//	}
	if ($curSubtask['subtask_type'] == MSGPUSH_TASK_TYPE_DIRECTIONAL){//todo:
		$arrRet = dr_push($arrMsgList,$currentDate,$curTask,$curSubtask);
	}
	push_stat($arrMsgList,$arrRet,$currentDate,$currentTime,$statInfo);
	return 0;
}

/**
*@param
*@return
*/
function run($redis, $frag){
    global $g_arrTasksInfo;
    
    $currentTime = time();
    $currentDate = date('Ymd',$currentTime);
	$current_hour = strftime('%H',$currentTime);
    // 0~9点不推送（pm @<EMAIL>）
    if ($current_hour >= FORBID_PUSH_BEGIN_TIME && $current_hour <= FORBID_PUSH_END_TIME){
		return 0;
	}
	$g_arrTasksInfo = getTaskInfo();

    $curTask = array();
    $curSubtask = array();

	foreach ($g_arrTasksInfo as $task_id => $taskInfo){
		$intBeginTime = strlen($taskInfo['begin_date']) >10 ? intval($taskInfo['begin_date'])/1000 : intval($taskInfo['begin_date']);
		if ($currentTime >= $intBeginTime){
		    $subtasks = getSubTaskInfo($task_id, MSGPUSH_SUBTASK_STATUS_SUCCESS);
		    foreach($subtasks as $subtaskId => $subtaskInfo){
                $planBeginDate = strlen($subtaskInfo['plan_begin_date']) >10 ? intval($subtaskInfo['plan_begin_date'])/1000 : intval($subtaskInfo['plan_begin_date']);
                if($currentTime > $planBeginDate){
                    $input = array(
                        'key' => $task_id . '-' . $subtaskId . MSGPUSH_LIST_PUSH . $frag%5,
                    );
                    $ret = redis_recall($redis,'LLEN',$input);
                    //任务已经开始执行并且任务队列中有数据，则开始执行此队列任务
                    if ($ret && $ret['err_no'] === 0 && intval($ret['ret'][$input['key']]) > 0){
                        $curTask = $taskInfo;
                        $curSubtask = $subtaskInfo;
                        break;
                    }
                    if(!$ret || $ret['err_no'] != 0){
                        Bingo_Log::warning('[redis call error] call llen fail, input: ' . Bingo_String::array2json($input) . ', error: ' . Bingo_String::array2json($ret));
                    }
                }

            }

		}
	}

	if ($curTask['task_id'] <= 0 || $curSubtask['subtask_id'] <= 0){
		return 0;
	}
	init();
	Bingo_Log::warning(Bingo_String::array2json($curSubtask));
	$key = $curTask['task_id'] . '-' . $curSubtask['subtask_id'] . MSGPUSH_LIST_PUSH . $frag%5;
	$save_count = 0;
	$statInfo = array();
	//默认设置1天失效日期
	$req = array(
		'key' => $key,
		'seconds' => 86400,//3600*24*7
	);
	$ret = redis_recall($redis,'EXPIRE',$req);
    if(!$ret || $ret['err_no'] != 0){
        Bingo_Log::warning('[redis call error] call expire fail, input: ' . Bingo_String::array2json($req) . ', error: ' . Bingo_String::array2json($ret));
    }

    $input = array(
        'key' => $key,
    );
    $ret = redis_recall($redis,'RPOP',$input);
    while ($ret && $ret['err_no'] === 0 && !empty($ret['ret'][$input['key']])){
    	$arrMsgList = Bingo_String::json2array($ret['ret'][$input['key']]);

        //如果任务规定的结束时间已经到了，则丢掉删除队列
        $subtaskEndTime = strlen($curSubtask['plan_end_date']) >10 ? intval($curSubtask['plan_end_date'])/1000 : intval($curSubtask['plan_end_date']);
        if ($currentTime >= $subtaskEndTime) {
        	Bingo_Log::warning("push list ". $key. " expired");
        	//把对列删除
        	$ret = redis_recall($redis,'DEL',$input);
            if(!$ret || $ret['err_no'] != 0){
                Bingo_Log::warning('[redis call error] call del fail, input: ' . Bingo_String::array2json($input) . ', error: ' . Bingo_String::array2json($ret));
            }
            return;
        }
        
        //如果任务类型是短信，就调发短信的方法。
//        if($curTask['task_type'] == MSGPUSH_TASK_TYPE_MESSAGE){
//        	sms_push($arrMsgList,$arrMsgInfo,$currentDate,$statInfo);
//        	$ret = redis_recall($redis,'RPOP',$input);
//        	continue;
//        }

        //todo:
        msg_push($arrMsgList,$currentDate,$currentTime,$statInfo,$curTask,$curSubtask);
    	
        if ($save_count++ >= 100){
        	$save_count = 0;
        	save_info($redis,$statInfo,$curTask,$curSubtask);
        	$currentTime = time();
        	$currentDate = date('Ymd',$currentTime);
        	$current_hour = strftime('%H',$currentTime);
            if ($current_hour <= 09){
        		break;
        	}
        }
        $ret = redis_recall($redis,'RPOP',$input);
    }
    if(!$ret || $ret['err_no'] != 0){
        Bingo_Log::warning('[redis call error] call rpop fail, input: ' . Bingo_String::array2json($input) . ', error: ' . Bingo_String::array2json($ret));
    }
    save_info($redis,$statInfo,$curTask,$curSubtask);
}

try{
    $frag = intval($argv[1]);
    if (2 != $argc){
        exit;
    }
    $scriptConf = array(
        'memory_limit' => '1024M',
        'data_path'      => DATAPATH,
        'conf_path'      => CONFPATH,
        'done_file_name' => SCRIPTNAME.'_'.$frag.'.done',
    );
    $easyScriptObj = new Lib_Util_EasyScript($scriptConf);
    //防止脚本重复执行
    if ( $easyScriptObj->checkScriptIsRuning() === true ){
        echo SCRIPTNAME.'_'.$frag." script is runing\r\n";
        exit;
    }
    $redis = new Bingo_Cache_Redis(REDIS_NAME);
    if (null == $redis){
        echo SCRIPTNAME.'call redis fail.';
        exit;
    }
    $g_cacheObj = new AiCache();
    echo SCRIPTNAME.'_'.$frag." script begin\r\n";
    run($redis, $frag);
    echo SCRIPTNAME.'_'.$frag." script end\r\n";
}catch(Exception $e){
    echo SCRIPTNAME.' run fail. desc:'.$easyScriptObj->getErr2String();
}
