<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @desc 智能推送脚本任务分发模块
 * @date 2016:09:10 14:40:02
 * @version 1.0
 */


define ('EASYSCRIPT_DEBUG',false);
define ('EASYSCRIPT_THROW_EXEPTION',false);

define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../../' );
define ('SCRIPTNAME',basename(__FILE__,".php"));   //
define ('BASEPATH',dirname(__FILE__));
define ('CONFPATH',BASEPATH."/conf/");
define ('DATAPATH',BASEPATH."/data/");
//define ('LOGPATH',BASEPATH."/log/");
define ('LOG', 'log');

set_include_path(get_include_path() . PATH_SEPARATOR. BASEPATH.'/../../');
require_once ROOT_PATH . "app/msgpush/scripts/aiMsgPush/base.php";

/**
 * auto load php file
 * @param unknown $strClassName
 */
function __autoload($strClassName)
{
    require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');

/**
 *@desc 如果是对全量在线用户进行智能推送，拆分成10个任务进行分配
 *@param
 *@return
 */
function dispatch_task($redis,$taskInfo,$subtaskInfo,$currentDate){

    $taskId = $taskInfo['task_id'];
    $subtaskId = $subtaskInfo['subtask_id'];
    $subtaskTag = (date('Ymd',time())* MSGPUSH_BASE_NUM + intval($taskId)) . '-' . $subtaskId;

    //todo: 更新子任务的detail
    $input = array(
        'subtask_id' => $subtaskInfo['subtask_id'],
        'run_date' => $currentDate,
    	'subtask_begin_time' => time(),
    );
    $ret = server_recall('common','updateSubTaskDetail',$input, 'gbk', getCallType());
 	if (!$ret || $ret['errno'] != 0){
        Bingo_Log::warning('[service call error] call common/updateSubTaskDetail fail, task_id: ' . $taskId . ', subtask_id: ' . $subtaskId . ', error: ' . Bingo_String::array2json($ret));
        return;
    }else{
        $input = array(
            'subtask_id' => $subtaskId,
            'operator' => 'mis-machine',
            'subtask_status' => MSGPUSH_SUBTASK_STATUS_SUCCESS
        );
        $pushTaskId = date('Ymd',time())* MSGPUSH_BASE_NUM + intval($taskId);
        Bingo_Log::warning($pushTaskId.'-'.$subtaskId.' task set ' . MSGPUSH_SUBTASK_STATUS_SUCCESS);
        $ret = server_recall('common','updateSubTaskStatus',$input, 'gbk', getCallType());
        if(!$ret || $ret['errno'] != 0){
            Bingo_Log::warning('[service call error] call common/updateSubTaskStatus fail, task_id: ' . $taskId . ', subtask_id: ' . $subtaskId . ', error: ' . Bingo_String::array2json($ret));
            return;
        }
    }

    $task_status = MSGPUSH_TASK_SUCCESS;
    $block_num = 0;

    if($subtaskInfo['user_type'] > MSGPUSH_USER_TYPE_ALL_ONLINE){
        if (($subtaskInfo['file_id'] <> "" || $subtaskInfo['file_address'] <> "") && $subtaskInfo['user_type'] == MSGPUSH_USER_TYPE_UPLOAD_UIDS){
            //todo:
            $subtaskInfo['file_id'] = ($currentDate * MSGPUSH_BASE_NUM + $subtaskInfo['task_id']) . '-' . $subtaskInfo['subtask_id'];
            $task_status = parse_upload_uids($redis,$taskInfo,$subtaskInfo,$block_num,$currentDate);
        } else if ($subtaskInfo['user_type'] > MSGPUSH_USER_TYPE_UPLOAD_UIDS && $subtaskInfo['user_type'] < MSGPUSH_USER_TYPE_CUSTOM){
            //todo：file_id和子任务关联
            $subtaskInfo['file_id'] = ($currentDate * MSGPUSH_BASE_NUM + $subtaskInfo['task_id']) . '-' . $subtaskInfo['subtask_id'];
            $task_status = parse_special_uids($redis,$taskInfo,$subtaskInfo,$block_num);
        } else if($subtaskInfo['user_type'] == MSGPUSH_USER_TYPE_CUSTOM && !empty($subtaskInfo['file_address'])){
            $subtaskInfo['file_id'] = ($currentDate * MSGPUSH_BASE_NUM + $subtaskInfo['task_id']) . '-' . $subtaskInfo['subtask_id'];
            $task_status = parse_ftp_uids($redis,$taskInfo,$subtaskInfo,$block_num);
        }
                Bingo_Log::warning("push user_ids into queue fail task_id=".$taskId);

        if ($task_status == MSGPUSH_TASK_FAIL || $block_num == 0){ //如果用户包获取失败，将子任务重置，等待下一次调度
            Bingo_Log::warning($subtaskTag . ' get uids fail, reset status and wait to be scheduled for next time');
            setSubtaskStatus($taskId, $subtaskId, MSGPUSH_SUBTASK_STATUS_TODO);
            return;
        }
    }

    //入库成功，可以把拆分的任务放到队列了
    //todo: 主任务队列变成子任务队列
    $arrSubtaskList  = array();
    for($index = 0; $index <= 4; $index++){
        $subtaskInfo['index'] = $index;
        $subtaskInfo['block_num'] = $block_num;
        $subtaskInfo['task_info'] = $taskInfo;
        $arrSubtaskList[] = Bingo_String::array2json($subtaskInfo);
    }
    $input = array(
        'key' => MSGPUSH_LIST_TASK,
        'value' => $arrSubtaskList,
    );
    Bingo_log::warning(Bingo_String::array2json($input));
    $ret = redis_recall($redis,'LPUSH',$input);
    if (!$ret || $ret['err_no'] != 0){//任务队列没有放成功
        //todo：重置子任务的detail
        Bingo_Log::warning("insert into redis queue fail:taskid=".$taskInfo['task_id'].",run_date=".$currentDate);
        setSubtaskStatus($taskId, $subtaskId, MSGPUSH_SUBTASK_STATUS_TODO);
    }
}

/**
 *@desc 把mis平台上传的二进制uid文件，重新解析为文本文件，并输出到redis
 *@param
 *@return
 */
function parse_upload_uids($redis,$taskInfo,$subtaskInfo,&$block_num,$currentDate){
	$fileId = $subtaskInfo['file_id'];
	$tmpFileName = DATAPATH . "/tmp_uids_" . $fileId;
    if($subtaskInfo['file_address']<>"")
    {
		$tmpFileaddress = $subtaskInfo['file_address'];
		$cmd="cd ". DATAPATH . ";wget " . $tmpFileaddress . " -O " . basename($tmpFileName) ;
		if(strtolower(substr(trim($tmpFileaddress),0,5)) == "https"){
			$cmd="cd ". DATAPATH . ";wget  --no-check-certificate  " . $tmpFileaddress . " -O " . basename($tmpFileName) ;
		}
		exec($cmd);
    }
    else
    {
        //todo：为了兼容mis，需要根据token判断是大神推送还是mis推送，mis的redis key不区分子任务
        if($taskInfo['token'] == 'ipush'){
            $key = ($currentDate * MSGPUSH_BASE_NUM + $subtaskInfo['task_id']) . '_intelligent_msgpush_row_uids_file';
        }else{
            $key = $fileId . '_intelligent_msgpush_row_uids_file';
        }

		$input = array(
			'key' => $key,
		);
		$output = redis_recall($redis,'HLEN',$input);
		if ($output && $output['err_no'] === 0){
			$intTotalBlockNum = intval($output['ret'][$key]);
			if ($intTotalBlockNum == 0) {
				return MSGPUSH_TASK_FAIL;
			}
			for($intBlock=1;$intBlock <= $intTotalBlockNum;$intBlock++){
				$input = array(
					'key' => $key,
					'field' => $intBlock,
				);
				$res = redis_recall($redis,'HGET',$input);
				if ($res && $res['err_no'] === 0 && !empty($res['ret'][$key])){
					if (false == file_put_contents($tmpFileName, $res['ret'][$key], FILE_APPEND)){
						return MSGPUSH_TASK_FAIL;
					}
				} else {
					if(!$res || $res['err_no'] != 0){
                        Bingo_Log::warning('[redis call error] call hget fail, task_id: ' . $taskInfo['task_id'] . ', subtask_id: ' . $subtaskInfo['subtask_id'] . ', input: ' . Bingo_String::array2json($input) . ', error: ' . Bingo_String::array2json($res));
                    }
					return MSGPUSH_TASK_FAIL;
				}
			}
		}else{
            Bingo_Log::warning('[redis call error] call hlen fail, task_id: ' . $taskInfo['task_id'] . ', subtask_id: ' . $subtaskInfo['subtask_id'] . ', input: ' . Bingo_String::array2json($input) . ', error: ' . Bingo_String::array2json($output));
            return MSGPUSH_TASK_FAIL;
        }
    }

	$tmpRst = writeUidsFromFile($tmpFileName, $redis, $taskInfo, $subtaskInfo, $block_num);
	if($tmpRst == MSGPUSH_TASK_SUCCESS && $subtaskInfo['file_address'] == "" && $subtaskInfo['file_id'] <> ""){
		//走到这里说明每一个文件块都读取成功了，删掉原始存储
		$ret = redis_recall($redis,'DEL',array('key' => $key,));
        Bingo_Log::warning('[redis call error] call del fail, task_id: ' . $taskInfo['task_id'] . ', subtask_id: ' . $subtaskInfo['subtask_id'] . ', input: ' . Bingo_String::array2json($key) . ', error: ' . Bingo_String::array2json($ret));
	}
	return $tmpRst;
}


/**
 *@desc 解析ftp文件
 *@param
 *@return
 */
function parse_ftp_uids($redis,$taskInfo,$subtaskInfo,&$block_num){
	$fileId = $subtaskInfo['file_id'];
	$strFileAddress = $subtaskInfo['file_address'];
	$cmd="cd ". DATAPATH . ";wget " . $strFileAddress ;
	if(strtolower(substr(trim($strFileAddress),0,5)) == "https"){
		$cmd="cd ". DATAPATH . ";wget --no-check-certificate  " . $strFileAddress ;
	}
	exec($cmd);

	$tmpFileName = DATAPATH . basename($strFileAddress);

	return writeUidsFromFile($tmpFileName, $redis, $taskInfo, $subtaskInfo, $block_num);
}

/**
 *@desc
 *@param
 *@return
 */
function get_special_sql($client_type,$user_type){
	$current_date = date('Ymd',time()-86400*2);
	$sql = "select uid from tieba_adm_user_freq_day where day=".$current_date." and ";
	$login_freq = "";
	if ($user_type == MSGPUSH_USER_TYPE_3_DAY_UNLOGIN){
		switch ($client_type) {
			case MSGPUSH_CLIENT_TYPE_IOS:
				$login_freq = " last_3days_nonlogin_ios=2";
				break;
			case MSGPUSH_CLIENT_TYPE_ANDROID:
				$login_freq = " last_3days_nonlogin_and=2";
				break;
			default:
				$login_freq = " (last_3days_nonlogin_ios=2 or last_3days_nonlogin_and=2) ";
				break;
		}
	}
	else if ($user_type >= MSGPUSH_USER_TYPE_MONTH_1_DAY && $user_type <= MSGPUSH_USER_TYPE_MONTH_30_DAY){
		$freq = 1;
		switch ($user_type) {
			case MSGPUSH_USER_TYPE_MONTH_1_DAY:
				$freq = 1;
				break;
			case MSGPUSH_USER_TYPE_MONTH_2_5_DAY:
				$freq = 4;
				break;
			case MSGPUSH_USER_TYPE_MONTH_6_15_DAY:
				$freq = 8;
				break;
			case MSGPUSH_USER_TYPE_MONTH_16_29_DAY:
				$freq = 12;
				break;
			case MSGPUSH_USER_TYPE_MONTH_30_DAY:
				$freq = 16;
				break;
			default:
				return "";
		}
		switch ($client_type) {
			case MSGPUSH_CLIENT_TYPE_IOS:
				$login_freq = " login_freq_ios=".$freq;
				break;
			case MSGPUSH_CLIENT_TYPE_ANDROID:
				$login_freq = " login_freq_and=".$freq;
				break;
			default:
				$login_freq = " (login_freq_ios=" . $freq . " or " . "login_freq_and=" . $freq . ")";
				break;
		}
	}
	else if ($user_type == MSGPUSH_USER_TYPE_ALL_MONTH){
		switch ($client_type) {
			case MSGPUSH_CLIENT_TYPE_IOS:
				$login_freq = " login_freq_ios in (1,4,8,12,16) ";
				break;
			case MSGPUSH_CLIENT_TYPE_ANDROID:
				$login_freq = " login_freq_and in (1,4,8,12,16) ";
				break;
			default:
				$login_freq = " ( login_freq_ios in (1,4,8,12,16) or login_freq_and in (1,4,8,12,16) ) ";
				break;
		}
	}
	else if ($user_type == MSGPUSH_USER_TYPE_ALL_QUARTER){
		switch ($client_type) {
			case MSGPUSH_CLIENT_TYPE_IOS:
				$login_freq = " login_freq_ios in (1,4,8,12,16,32) ";
				break;
			case MSGPUSH_CLIENT_TYPE_ANDROID:
				$login_freq = " login_freq_and in (1,4,8,12,16,32) ";
				break;
			default:
				$login_freq = " ( login_freq_ios in (1,4,8,12,16,32) or login_freq_and in (1,4,8,12,16,32) ) ";
				break;
		}
	}
	//debug_log($sql . $login_freq);
	return $sql . $login_freq;
}

/**
 *@desc
 *@param
 *@return
 */
function get_special_sql_msg($user_type){
    $current_date = date('Ymd',time()-86400*2);
    $sql = "select uid from tieba_user_client_login_freq_day where day=".$current_date." and ";
    $login_freq = "";
    switch ($user_type) {
        case MSGPUSH_USER_TYPE_MONTH_PC_0_DAY:
            $login_freq .= " login_freq_pc = 0 limit 10000;";
            break;
        case MSGPUSH_USER_TYPE_MONTH_PC_3_DAY:
            $login_freq .= " login_freq_pc = 3 limit 10000;";
            break;
        case MSGPUSH_USER_TYPE_MONTH_PC_7_DAY:
            $login_freq .= " login_freq_pc = 7 limit 10000;";
            break;
        case MSGPUSH_USER_TYPE_MONTH_PC_15_DAY:
            $login_freq .= " login_freq_pc = 15 limit 10000;";
            break;
        case MSGPUSH_USER_TYPE_MONTH_WEB_0_DAY:
            $login_freq .= " login_freq_web = 0 limit 10000;";
            break;
        case MSGPUSH_USER_TYPE_MONTH_WEB_3_DAY:
            $login_freq .= " login_freq_web = 3 limit 10000;";
            break;
        case MSGPUSH_USER_TYPE_MONTH_WEB_7_DAY:
            $login_freq .= " login_freq_web = 7 limit 10000;";
            break;
        case MSGPUSH_USER_TYPE_MONTH_WEB_15_DAY:
            $login_freq .= " login_freq_web = 15 limit 10000;";
            break;
        case MSGPUSH_USER_TYPE_MONTH_CE_0_DAY:
            $login_freq .= " login_freq_kuang = 0 limit 10000;";
            break;
        case MSGPUSH_USER_TYPE_MONTH_CE_3_DAY:
            $login_freq .= " login_freq_kuang = 3 limit 10000;";
            break;
        case MSGPUSH_USER_TYPE_MONTH_CE_7_DAY:
            $login_freq .= " login_freq_kuang = 7 limit 10000;";
            break;
        case MSGPUSH_USER_TYPE_MONTH_CE_15_DAY:
            $login_freq .= " login_freq_kuang = 15 limit 10000;";
            break;
        default:
            break;
    }
    debug_log($sql . $login_freq);
    return $sql . $login_freq;
}

/**
 *@desc 从palo库里获取uids
 *@param
 *@return
 */
function parse_special_uids($redis,$taskInfo,$subtaskInfo,&$block_num){
	$fileId = $subtaskInfo['file_id'];
	$tmpFileName = DATAPATH . "/tmp_uids_" . $fileId;

    $sql = get_special_sql($subtaskInfo["client_type"], $subtaskInfo["user_type"]);

	if (empty($sql)){
		return MSGPUSH_TASK_FAIL;
	}
	$cmd="mysql -hpalo-yqa.baidu.com -uns_tieba_off -pns_tieba_off_123 -P9030 tieba -Bs -e '".$sql."' > ". $tmpFileName;
	exec($cmd);

	return writeUidsFromFile($tmpFileName, $redis, $taskInfo, $subtaskInfo, $block_num);
}

function writeUidsFromFile($tmpFileName,$redis,$taskInfo,$subtaskInfo,&$block_num){
    $taskId = $taskInfo['task_id'];
    $subtaskId = $subtaskInfo['subtask_id'];
    $subtaskTag = (date('Ymd',time())* MSGPUSH_BASE_NUM + intval($taskInfo['task_id'])) . '-' . $subtaskInfo['subtask_id'];

    $destKey = $subtaskInfo['file_id'] . '_intelligent_msgpush_uids_file';
    $tmpFile = fopen($tmpFileName,'r');
    if (!$tmpFile){
        return MSGPUSH_TASK_FAIL;
    }

    //在写入uids_file队列之前，就将uid按照mod128聚集，同时遵守MSGPUSH_REDIS_LIMIT的上限限制
    $uidModArr = array();
    $intIndex = 1;
    $uidCount = 0;
    while (!feof($tmpFile)){
        $uid = intval(fgets($tmpFile));
        if ($uid > 0){
            $uidCount++;
            $uidModArr[$uid % 128][] = $uid;
            if(count($uidModArr[$uid % 128]) == MSGPUSH_REDIS_LIMIT){
                $input = array(
                    'key' => $destKey,
                    'field' => $intIndex,
                    'value' => implode(",", $uidModArr[$uid % 128])
                );
                $res = redis_recall($redis,'HSET',$input);
                if (!$res || $res['err_no'] != 0){
                    Bingo_Log::warning('[redis call error] call hset fail, task_id: ' . $taskId . ', subtask_id: ' . $subtaskId . ', input: ' . Bingo_String::array2json($input) . ', error: ' . Bingo_String::array2json($res));
                }else{
                    $intIndex++;
                }

                unset($uidModArr[$uid % 128]);

            }
        }

    }
    //数据还没准备好，返回执行失败
    if ($uidCount == 0){
        unlink($tmpFileName);
        return MSGPUSH_TASK_FAIL;
    }
    $task_id = $taskInfo['task_id']+strftime("%Y%m%d")*MSGPUSH_BASE_NUM;
    Bingo_Log::warning("system=push partition=mis stage=dispatch task_id=".$task_id." subtask_id=".$subtaskId." uid_count=".$uidCount);
    Bingo_Log::pushNotice("task_id",$task_id);
    Bingo_Log::pushNotice("uid_count",$uidCount);
    foreach($uidModArr as $uids){
        if(count($uids) > 0){
            $input = array(
                'key' => $destKey,
                'field' => $intIndex,
                'value' => implode(",", $uids)
            );
            $res = redis_recall($redis,'HSET',$input);
            if (!$res || $res['err_no'] != 0){
                Bingo_Log::warning('[redis call error] call hset fail, task_id: ' . $taskId . ', subtask_id: ' . $subtaskId . ', input: ' . Bingo_String::array2json($input) . ', error: ' . Bingo_String::array2json($res));
                Bingo_Log::warning("task_id=".$taskInfo['task_id']." task set user_ids into redis fail");
            }else{
                $intIndex++;
            }
        }
    }

    $input = array(
        'key' => $destKey,
    );
    $output = redis_recall($redis,'HLEN',$input);
    if ($output && $output['err_no'] === 0){
        $block_num = intval($output['ret'][$destKey]);
    }else{
        Bingo_Log::warning('[redis call error] call hlen fail, task_id: ' . $taskId . ', subtask_id: ' . $subtaskId . ', input: ' . Bingo_String::array2json($input) . ', error: ' . Bingo_String::array2json($output));
    }

    //默认设置3天失效日期，也就是说如果3天内还未执行任务的话，上传的用户包就没用了
    $req = array(
        'key' => $destKey,
        'seconds' => 259200,//3600*24*3
    );
    $res = redis_recall($redis,'EXPIRE',$req);
    if(!$res || $res['err_no'] != 0){
        Bingo_Log::warning('[redis call error] call expire fail, task_id: ' . $taskId . ', subtask_id: ' . $subtaskId . ', input: ' . Bingo_String::array2json($input) . ', error: ' . Bingo_String::array2json($res));
    }
    fclose($tmpFile);
    unlink($tmpFileName);

    return MSGPUSH_TASK_SUCCESS;
}

function setTaskOffline($taskId){
    $input = array(
        'task_id' => $taskId,
        'task_status' => MSGPUSH_TASK_STATUS_OFFLINE,
        'operator' => 'machine',
    );
    Bingo_Log::warning($taskId.' task offline');
    $ret = server_recall('common','msgpushUpdateTaskStatus',$input, 'gbk', getCallType());
    if(!$ret || $ret['errno'] != 0){
        Bingo_Log::warning('[service call error] call common/msgpushUpdateTaskInfo fail, task_id: ' . $taskId . ' error: ' . Bingo_String::array2json($ret));

    }
}

function setSubtaskStatus($taskId, $subtaskId, $subtaskStatus){
    $input = array(
        'subtask_id' => $subtaskId,
        'operator' => 'mis-machine',
        'subtask_status' => $subtaskStatus
    );
    $pushTaskId = date('Ymd',time())* MSGPUSH_BASE_NUM + intval($taskId);
    Bingo_Log::warning($pushTaskId.'-'.$subtaskId.' task set ' . $subtaskStatus);
    $ret = server_recall('common','updateSubTaskStatus',$input, 'gbk', getCallType());
    if(!$ret || $ret['errno'] != 0){
        Bingo_Log::warning('[service call error] call common/updateSubTaskStatus fail, task_id: ' . $taskId . ', subtask_id: ' . $subtaskId . ', error: ' . Bingo_String::array2json($ret));

    }
}


/**
*@param
*@return
*/
function run($redis){
    global $targetPhoneArr;
    $arrTaskInfo = getTaskInfo(); //todo：获取当天在线主任务

    //todo: 根据主任务获取子任务，对子任务做检查并dispatch
    foreach ($arrTaskInfo as $taskId => $taskInfo) {
    	Bingo_Log::warning(Bingo_String::array2json($taskInfo));
        $pushTaskId = date('Ymd',time())* MSGPUSH_BASE_NUM + intval($taskId);
    	if($taskInfo['task_status'] != MSGPUSH_TASK_STATUS_ONLINE){
    	    continue;
        }

        strlen($taskInfo['begin_date'])>10 ? $taskBeginTime = intval($taskInfo['begin_date'])/1000 : $taskBeginTime = $taskInfo['begin_date'];
        strlen($taskInfo['end_date'])>10 ? $taskEndTime = intval($taskInfo['end_date'])/1000 : $taskEndTime = $taskInfo['end_date'];
    	$currentTime = time();
        $currentDate = date('Ymd',$currentTime);

        if ($taskInfo['task_id'] <= 0 ||
        	$taskInfo['client_type'] < MSGPUSH_CLIENT_TYPE_IOS ||
        	$taskInfo['client_type'] > MSGPUSH_CLIENT_TYPE_ALL ||
        	$taskInfo['task_type'] != MSGPUSH_TASK_TYPE_DIRECTIONAL ||
            $taskInfo['begin_date'] >= $taskInfo['end_date'] ){
            Bingo_Log::warning($taskId.' task param error continue');

            $msg = '【MIS平台 task_id: ' . $pushTaskId . '】任务参数错误';
            sendMessage(array('msg' => $msg, 'target' => $targetPhoneArr));
            setTaskOffline($taskId);
            continue;
        }

        if($currentTime > $taskEndTime){ //将过期的在线任务置为离线
            if($currentTime < (strtotime(date("Y-m-d",time()))+22*3600) && $currentTime > (strtotime(date("Y-m-d",time()))+9*3600)) {
                $msg = '【MIS平台 task_id: ' . $pushTaskId . '】任务过期即将下线';
                sendMessage(array('msg' => $msg, 'target' => $targetPhoneArr));
            }
            setTaskOffline($taskId);
            continue;
        }

        if($currentDate != date('Ymd',$taskBeginTime)){ //任务还没开始
            Bingo_Log::warning($taskId.' task not started');
            continue;
        }

        //如果是当天的任务需要提前准备数据

        //获取该任务上次执行情况
        $input = array(
            'task_id' => $taskId,
        );
        $ret = server_recall('common','msgpushQueryLastTaskDetail',$input, 'gbk', getCallType());


        if ($ret && $ret['errno'] === 0){
            if (empty($ret['data'][0]['run_date'])){ //没有detail记录,需要添加
                Bingo_Log::warning($taskId." task does not have a detail");
                $input = array(
                    'task_id' => $taskId,
                    'run_date' => $currentDate,
                    'begin_time' => time(),
                );
                $ret = server_recall('common','msgpushUpdateTaskDetail',$input, 'gbk', getCallType());
                if (!$ret || $ret['errno'] != 0){
                    Bingo_Log::warning('[service call error] call common/msgpushUpdateTaskDetail fail, task_id: ' . $taskId . ' error: ' . Bingo_String::array2json($ret));
                    continue;
                }
            }
        } else {
            Bingo_Log::warning('[service call error] call common/msgpushQueryLastTaskDetail fail, task_id: ' . $taskId . ' error: ' . Bingo_String::array2json($ret));
            continue;
        }

        $arrSubtaskInfo = getSubTaskInfo($taskId, MSGPUSH_SUBTASK_STATUS_TODO);

        foreach($arrSubtaskInfo as $subtaskId => $subtaskInfo){
            if( $subtaskInfo['subtask_id'] <= 0
                || $subtaskInfo['subtask_type'] != MSGPUSH_SUBTASK_TYPE_DIRECTIONAL
                || $subtaskInfo['client_type'] <= MSGPUSH_CLIENT_TYPE_UNKNOWN || $subtaskInfo['client_type'] > MSGPUSH_CLIENT_TYPE_ALL
                || $subtaskInfo['user_type'] == MSGPUSH_USER_TYPE_UNKNOWN
                || $subtaskInfo['plan_begin_date'] >= $subtaskInfo['plan_end_date']
            ){
                Bingo_Log::warning($pushTaskId.'-'.$subtaskId.' task param error continue');
                $msg = '【MIS平台 task_id: ' . $pushTaskId . '-' . $subtaskId . '】参数错误';
                sendMessage(array('msg' => $msg, 'target' => $targetPhoneArr));
                setSubtaskStatus($taskId, $subtaskId, MSGPUSH_SUBTASK_STATUS_FAIL);
                continue;
            }
            $g_arrTidsInfo = getThreadInfo($g_arrTidsInfo,MSGPUSH_THREAD_STATUS_PASSED,true);
            $g_arrTidsInfo = getThreadInfo($g_arrTidsInfo,MSGPUSH_THREAD_STATUS_GOOD,true);

            if(!isset($g_arrTidsInfo[$subtaskInfo['push_subtask_ids']]) && MSGPUSH_TASK_TYPE_DIRECTIONAL == $subtaskInfo['user_type'])
            {
                Bingo_Log::warning($pushTaskId.'-'.$subtaskId.' task push empty content when directional push continue');
                $msg = '【MIS平台 task_id: ' . $pushTaskId . '-' . $subtaskId . '】获取定向推送帖子内容失败';
                sendMessage(array('msg' => $msg, 'target' => $targetPhoneArr));
                setSubtaskStatus($taskId, $subtaskId, MSGPUSH_SUBTASK_STATUS_FAIL);
                continue;
            }
            if((MSGPUSH_USER_TYPE_UPLOAD_UIDS == $subtaskInfo['user_type'] && ($subtaskInfo['file_id'] == "" && $subtaskInfo['file_address'] == ""))
                || (MSGPUSH_USER_TYPE_CUSTOM == $subtaskInfo['user_type'] && $subtaskInfo['file_address'] == "")
            ){
                Bingo_Log::warning($pushTaskId.'-'.$subtaskId.' task user file error continue');
                $msg = '【MIS平台 task_id: ' . $pushTaskId . '-' . $subtaskId . '】没有指定用户包';
                sendMessage(array('msg' => $msg, 'target' => $targetPhoneArr));
                setSubtaskStatus($taskId, $subtaskId, MSGPUSH_SUBTASK_STATUS_FAIL);
                continue;//没有用户包的指定用户包任务
            }

            if (MSGPUSH_TASK_TYPE_DIRECTIONAL == $subtaskInfo['subtask_type'] && $taskInfo['token'] != 'ipush' && empty($subtaskInfo['push_content'])){
                Bingo_Log::warning($pushTaskId.'-'.$subtaskId.' task push empty content when directional push continue');
                $msg = '【MIS平台 task_id: ' . $pushTaskId . '-' . $subtaskId . '】定向推送没有指定内容';
                sendMessage(array('msg' => $msg, 'target' => $targetPhoneArr));
                setSubtaskStatus($taskId, $subtaskId, MSGPUSH_SUBTASK_STATUS_FAIL);
                continue;
            }

            if(MSGPUSH_TASK_TYPE_DIRECTIONAL == $subtaskInfo['subtask_type'] && $taskInfo['token'] == 'ipush' && empty($subtaskInfo['push_subtask_ids'])){
                Bingo_Log::warning($pushTaskId.'-'.$subtaskId.' task push empty tids when directional push continue');
                $msg = '【MIS平台 task_id: ' . $pushTaskId . '-' . $subtaskId . '】定帖推送没有指定帖子';
                sendMessage(array('msg' => $msg, 'target' => $targetPhoneArr));
                setSubtaskStatus($taskId, $subtaskId, MSGPUSH_SUBTASK_STATUS_FAIL);
                continue;//定贴推送，但是没指定贴子
            }

            strlen($subtaskInfo['plan_begin_date'])>10 ? $subtaskBeginTime = intval($subtaskInfo['plan_begin_date'])/1000 : $subtaskBeginTime = $subtaskInfo['plan_begin_date'];
            strlen($subtaskInfo['plan_end_date'])>10 ? $subtaskEndTime = intval($subtaskInfo['plan_end_date'])/1000 : $subtaskEndTime = $subtaskInfo['plan_end_date'];

            if ($currentTime > $subtaskEndTime){
                //子任务已过期,更改任务状态
                setSubtaskStatus($taskId, $subtaskId, MSGPUSH_SUBTASK_STATUS_EXPIRED);
                continue;
            }

            if(date('Ymd',$currentTime) != date('Ymd',$subtaskBeginTime)){
                Bingo_Log::warning($pushTaskId.'-'.$subtaskId.' task not started');
                continue;
            }

            //定向推送的话，需要重置来源，以避免被智能推送使用
            if ($subtaskInfo['subtask_type'] == MSGPUSH_TASK_TYPE_DIRECTIONAL && $taskInfo['token'] != 'ipush'){
                $arrInput = array(
                    'tids' => $subtaskInfo['push_content'],
                    'call_from' => 0,
                    'function' => 'updateCallFrom',
                );
                $arrRet = server_recall('common', "msgpushThreadAudit", $arrInput);
                if(!$arrRet || $arrRet['errno'] != 0){
                    Bingo_Log::warning('[service call error] call common/msgpushThreadAudit fail, task_id: ' . $taskId . ' subtask_id: '.$subtaskId. ' error: ' . Bingo_String::array2json($arrRet));
                }
            }

            //分配任务
            Bingo_Log::warning(Bingo_String::array2json($subtaskInfo));
            dispatch_task($redis,$taskInfo,$subtaskInfo,$currentDate);
        }

    }
}

try{
    $scriptConf = array(
        'memory_limit' => '10240M',
        'data_path'      => DATAPATH,
        'conf_path'      => CONFPATH,
        'lock_file_name' => SCRIPTNAME.'lock',
        'done_file_name' => SCRIPTNAME.'.done',
    );
    $easyScriptObj = new Lib_Util_EasyScript($scriptConf);
    if ($easyScriptObj->checkScriptIsRuning() === true ){
        echo SCRIPTNAME." script is runing\r\n";
        exit;
    }
    Bingo_Log::warning('dispatch begin');
    $redis = new Bingo_Cache_Redis(REDIS_NAME);
    if (null == $redis){
        echo SCRIPTNAME.'call redis fail.';
        exit;
    }
    echo SCRIPTNAME." script begin runing.\r\n";
    run($redis);
    Bingo_Log::warning('dispatch end');
    echo SCRIPTNAME." script end runing.\r\n";
}catch(Exception $e){
    Bingo_Log::warning("dispatch run fail");
    echo SCRIPTNAME.' run fail. desc:'.$easyScriptObj->getErr2String();
}
