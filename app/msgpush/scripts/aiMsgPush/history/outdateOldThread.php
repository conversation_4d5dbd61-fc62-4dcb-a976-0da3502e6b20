<?php

/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 16/11/8
 * Time: 上午10:27
 */

ini_set ( "memory_limit", "-1" );
date_default_timezone_set ( "Asia/Chongqing" );

define ( 'APP_NAME', 'msgpush' );
define ( 'SCRIPT_NAME', 'OutdateOldThread' );
define ( 'ROOT_PATH', dirname ( __FILE__ ) . '/../../../../..' );
define ( 'SCRIPT_LOG_PATH', ROOT_PATH . '/log/app/' . APP_NAME );

/**
 * @param $strClassName
 */
function __autoload($strClassName) {
	$strNewClassName = str_replace ( '_', '/', $strClassName . ".php" );
	$arrClass = explode ( '/', $strNewClassName );
	$intPathLen = count ( $arrClass );
	$strLastName = $arrClass [$intPathLen - 1];
	$strTmp = strtolower ( $strNewClassName );
	$intPreLen = strlen ( $strTmp ) - strlen ( $strLastName );
	$strNewClassName = substr ( $strTmp, 0, $intPreLen ) . $strLastName;
	$strClassPath = ROOT_PATH . '/app/' . APP_NAME . '/' . $strNewClassName;
	require_once $strClassPath;
}
spl_autoload_register ( '__autoload' );

if (! defined ( 'REQUEST_ID' )) {
	$requestTime = gettimeofday ();
	define ( 'REQUEST_ID', (intval ( $requestTime ['sec'] * 100000 + $requestTime ['usec'] / 10 ) & 0x7FFFFFFF) );
}

if (function_exists ( 'camel_set_logid' )) {
	camel_set_logid ( REQUEST_ID );
}

Bingo_Log::init ( array (
	LOG_SCRIPT => array (
		'file' => SCRIPT_LOG_PATH . '/' . SCRIPT_NAME . '/' . SCRIPT_NAME . '.log',
		'level' => 0x01 | 0x02 | 0x04 | 0x08,
	),
), LOG_SCRIPT );

$ret = OutdateOldThread::process();
if ($ret) {
	var_dump('execute success!');
	exit(0);
}
var_dump('execute fail!');
exit(1);

/**
 * 7天前入库的贴子设置过期
 * <AUTHOR>
 * Class Thread
 */
class OutdateOldThread
{
	// 贴子过期状态
	const THREAD_OUT_DATE = 4;
	const EXPIRE_DAY = 7;

	/**
	 * @return bool
	 */
	public static function process()
	{
		$strDate = date('Ymd', strtotime('-'. self::EXPIRE_DAY . ' day'));
		$arrInput = array(
			'date' => $strDate,
			'status' => self::THREAD_OUT_DATE,
			'operator' => '<',
			'function' => 'updateStatusByDate',
		);
		// 设置7天前贴子过期
		$arrRet = Tieba_Service::call('common', "msgpushThreadAudit", $arrInput);
		if($arrRet === false || $arrRet['errno'] != 0){
			$arrRet = Tieba_Service::call('common', "msgpushThreadAudit", $arrInput);
			if($arrRet === false || $arrRet['errno'] != 0) {
				Bingo_Log::fatal("call common:msgpushThreadAudit error! input[" . serialize($arrInput) . "]");
				return false;
			}
		}
		return true;
	}
}
