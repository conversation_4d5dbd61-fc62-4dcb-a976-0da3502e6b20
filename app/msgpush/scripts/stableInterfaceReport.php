<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * 接口级别的稳定报表发送邮件脚本
 */

ini_set ( "memory_limit", "-1" );
define('MODULE_NAME', 'msgpush');
date_default_timezone_set ( "Asia/Chongqing" );
define ('EASYSCRIPT_DEBUG',true);          //debug 模式
define ('EASYSCRIPT_THROW_EXEPTION',true);  //抛异常模式
// 定义相关路径信息
define ( 'APP_NAME', 'msgpush');
define ( 'SCRIPT_NAME', 'stableInterfaceReport');
define ( 'ROOT_PATH', dirname ( __FILE__ ) . '/../../..' );
define ( 'SCRIPT_ROOT_PATH', ROOT_PATH . '/app/' . APP_NAME . '/scripts' );
define ( 'SCRIPT_LOG_PATH', ROOT_PATH . '/log/' . APP_NAME . '/scripts' );
define ( 'SCRIPT_CONF_PATH', ROOT_PATH . '/conf/app/' . APP_NAME );

/**
 * 自动加载
 * @param $strClassName
 * @return
 */
function __autoload($strClassName) {
    $strNewClassName = str_replace ( '_', '/', $strClassName . ".php" );
    $arrClass = explode ( '/', $strNewClassName );
    $intPathLen = count ( $arrClass );
    $strLastName = $arrClass [$intPathLen - 1];
    $strTmp = strtolower ( $strNewClassName );
    $intPreLen = strlen ( $strTmp ) - strlen ( $strLastName );
    $strNewClassName = substr ( $strTmp, 0, $intPreLen ) . $strLastName;
    $strClassPath = ROOT_PATH . '/app/' . APP_NAME . '/' . $strNewClassName;
    require_once $strClassPath;
}
spl_autoload_register ( '__autoload' );
// 设置logid
if (! defined ( 'REQUEST_ID' )) {
    $requestTime = gettimeofday ();
    define ( 'REQUEST_ID', (intval ( $requestTime ['sec'] * 100000 + $requestTime ['usec'] / 10 ) & 0x7FFFFFFF) );
}

if (function_exists ( 'camel_set_logid' )) {
    camel_set_logid ( REQUEST_ID );
}

Bingo_Log::init ( array (
    LOG_SCRIPT => array (
        'file' => SCRIPT_LOG_PATH . '/' . SCRIPT_NAME . '.log',
        'level' => 0x01 | 0x02 | 0x04 | 0x08,
    ),
), LOG_SCRIPT );

require_once ROOT_PATH . "/app/ems/script/Mail_MSGLib/SendMail.php";

$date = date("Y-m-d");
$time = strtotime($date) - 86400;
$yesDate = date("Y-m-d",$time);
$type = 'all';
//$arrCoreUrl = array();
$arrBusiness = array(
    'livegroup', 
    'live',
);
$arrInput = array(
    'date' => $yesDate,
    'business' => $arrBusiness,
    'type' => $type,
);

$arrInterfaceToLevel = array();
$arrUILevel = Bd_Conf::getConf('/app/ems/ui_level/ui_level_list');
foreach ($arrUILevel['core'] as $ui){
    $arrInterfaceToLevel[$ui] = CORE_LEVEL;
}
foreach ($arrUILevel['importance'] as $ui){
    $arrInterfaceToLevel[$ui] = IMPORTANCE_LEVEL;
}
$arrCoreUrl = $arrUILevel['core'];
$arrImportantUi = $arrUILevel['importance'];


/*$objWordServer = Wordserver_Wordlist::factory();
if(null != $objWordServer){
    $arrCoreUrl = $objWordServer->getTableContents($arrInput);
}*/

$arrData = getInterfaceStableData($arrInput);
if(null == $arrData || empty($arrData)){
    Bingo_Log::warning('stableInterfaceReport call getInterfaceStableData failed.');
    return ;
}
$arrMailContent = array();
$arrOutItemByLevel = array();
$strHtml = '';
foreach($arrData as $arrItem) {
    $strHtmlBody = buildHTMLBody($arrItem['module'], $arrItem['interface']);
    $strHtml .= $strHtmlBody;
}
$strExplain = getExplain();
$strHtml .= $strExplain;
$strOutput = buildHtmlPage($strHtml);
$arrMailContent[] = $strOutput;

$strHtml = '';
$strOutput = '';
if(!empty($arrOutItemByLevel)){
    ksort($arrOutItemByLevel);
    foreach ($arrOutItemByLevel as $strLevel => $arrInterface){
        $strBody = buildMailInterfaceBodyByLevel($strLevel, $arrInterface);
        $strHtml .= $strBody;
    }
    $strExplain = getExplain();
    $strHtml .= $strExplain;
    $strOutput = buildHtmlPage($strHtml);
    $arrMailContent[] = $strOutput;
}

$arrReceiver = array(
    0 => '<EMAIL>',
);


foreach ($arrMailContent as $key => $strMailContent){
    $arrEmail = array (
        'sender' => '<EMAIL>',
        'receiver' => $arrReceiver[$key],
        'title' => '图文直播service稳定性报表_'.$yesDate );
    $htmlText = "text"; //发送html格式不能mailText为空
    $mail = new SendMail ();
    $mail->setTo ( $arrEmail ['receiver'] );
    $mail->setFrom ( $arrEmail ['sender'] );
    $mail->setCC ( $arrEmail ['cc'] );
    $mail->setCharset ( "utf-8" );
    $mail->setAttachments($strHtmlFile);
    $mail->setSubject ( $arrEmail ['title'] );
    $mail->setText ( $htmlText );
    $mail->setHTML ( $strMailContent );
    $result = $mail->send ();
    if ($result == false) {
        Bingo_Log::warning ( "send mail fail.mailcontent:[".$strMailContent.']');
        var_dump ( "send mail fail." );
        continue;
    }
    sleep(1);
}



function buildHtmlPage($strBody) {
    $strOutput = <<<EOF
<html>
	<head>
		<link href="http://cdn.bootcss.com/twitter-bootstrap/3.0.1/css/bootstrap.min.css" rel="stylesheet">
		<meta name="generator" content="HTML Tidy for HTML5 (experimental) for Windows https://github.com/w3c/tidy-html5/tree/c63cc39" />
		<meta charset="utf-8" />
		<title>Ral Check Report</title>

		<style>
		    #container {
		        width: 1060px;
		        margin: 0 auto;
		        padding: 10px;
		    }
			.my-table-class {
			    border-collapse: collapse;
			    width: 100%;
			    font-size: 80%;
			}
		
			.my-table-header-class tr {
			    background-color: #4E90B2;
			    color: #fff;
			}
			.my-table-header-class th {
			    border: 1px solid #B5B5B5;
			    font-size: 15px;
			    font-weight: bold;
			    text-align: center;
			    vertical-align: middle;
			    height: 30px;
			}
			.tablehead th {
			    border: 1px solid #B5B5B5;
			    font-size: 15px;
			    font-weight: bold;
			    text-align: center;
			    vertical-align: middle;
			    height: 30px;
				background: #FFFFFF;
				color: #000000;
			}
			.my-table-body-class tr {
			    background-color: #fff;
			}
			.my-table-body-class td {
			    border: 1px solid #B5B5B5;
			    text-align: center;
			    vertical-align: middle;
			    height: 30px;
			    font-size: 15px;
				    word-break: break-all;
			}
            .tr-redfy {
                font-weight:800;
                color:#EE0000;
            }
            .tr-yellowfy {
                font-weight:800;
                color:#FFC125;
            }
            .table-title {
                font-size: 25px;
                text-align: center;
            }
		</style>
	</head>

	<body>
	$strBody
	</body>
</html>
EOF;
    return $strOutput;
}


function buildMailInterfaceBodyByLevel($strLevelDesc, $arrInterface)
{
    global $arrInterfaceToLevel;
    $strBody = <<<EOF
	<div id="container">
    <div class='table-title'>
EOF;
    $strBody .= '【' . "$strLevelDesc" . '】';
    $strBody .= <<<EOF
    </div>
      <table id="my-table-id" class="my-table-class">
        <thead id="my-table-header-id" class="my-table-header-class">
          <tr>
            <th>idc</th>
           	<th>urlkey</th>
            <th>module</th>
            <th>level</th>
            <th>duty</th>
            <!--<th>business</th>-->
            <th>COST(ms)</th>
            <th>QPS</th>
            <th>PV</th>
            <th>HTTP200Rate(%)</th>
            <th>HTTP302Rate(%)</th>
            <th>HTTP499Rate(%)</th>
            <th>STABLE(%)</th>
          </tr>
			</thead>
			<tbody id="my-table-body-id" class="my-table-body-class">
EOF;
    foreach ($arrInterface as $interface) {
        
        foreach ($interface['stat_data'] as $arrItem) {
            $strAttr = '';
            $strUrlKey = $interface['stat_item'];
            $strIdcKey = $arrItem['idc'];
            if (isCoreUi($strUrlKey)) {
                $strAttr .= "<span style='color:brown;background-color:yellow;'>[core]</span>";
            } else 
                if (isImportantUi($strUrlKey)) {
                    $strAttr .= "<span style='color:brown;background-color:aquamarine;'>[importance]</span>";
                }
            $strBgColor = 'white';
            if ('all' == $strIdcKey) {
                $strBgColor = 'whiteSmoke';
            }
            $arrItem['http_200_rate'] *= 100;
            $arrItem['http_302_rate'] *= 100;
            $arrItem['http_499_rate'] *= 100;
            $arrItem['stable'] *= 100;
            if (false && intval($arrItem['stable']) === 100) {
                $strBody .= "<tr class=tr-yellowfy style='background-color:{$strBgColor};'><td>{$strIdcKey}</td>\n
                    <td>{$strUrlKey}{$strAttr}</td>\n
                    <td>{$interface['module']}</td>\n
                    <td>{$interface['level']}</td>\n
                    <td>{$interface['duty']}</td>\n
                    <td>{$arrItem['cost']}</td>\n
                    <td>{$arrItem['qps']}</td>\n
                    <td>{$arrItem['pv']}</td>\n
                    <td>{$arrItem['http_200_rate']}</td>\n
                    <td>{$arrItem['http_302_rate']}</td>\n
                    <td>{$arrItem['http_499_rate']}</td>\n
                    <td>{$arrItem['stable']}</td>\n
                    </tr>";
            } else 
                if (checkDanger($strUrlKey, $arrItem['stable'])) {
                    $strBody .= "<tr class=tr-redfy style='background-color:{$strBgColor};'><td>{$strIdcKey}</td>\n
                    <td>{$strUrlKey}{$strAttr}</td>\n
                    <td>{$interface['module']}</td>\n
                    <td>{$interface['level']}</td>\n
                    <td>{$interface['duty']}</td>\n
                    <td>{$arrItem['cost']}</td>\n
                    <td>{$arrItem['qps']}</td>\n
                    <td>{$arrItem['pv']}</td>\n
                    <td>{$arrItem['http_200_rate']}</td>\n
                    <td>{$arrItem['http_302_rate']}</td>\n
                    <td>{$arrItem['http_499_rate']}</td>\n
                    <td>{$arrItem['stable']}</td>\n
                    </tr>";
                } else {
                    $strBody .= "<tr style='background-color:{$strBgColor};'><td>{$strIdcKey}</td>\n
                    <td>{$strUrlKey}{$strAttr}</td>\n
                    <td>{$interface['module']}</td>\n
                    <td>{$interface['level']}</td>\n
                    <td>{$interface['duty']}</td>\n
                    <td>{$arrItem['cost']}</td>\n
                    <td>{$arrItem['qps']}</td>\n
                    <td>{$arrItem['pv']}</td>\n
                    <td>{$arrItem['http_200_rate']}</td>\n
                    <td>{$arrItem['http_302_rate']}</td>\n
                    <td>{$arrItem['http_499_rate']}</td>\n
                    <td>{$arrItem['stable']}</td>\n
                    </tr>";
                }
        }
    }
    $strBody .= <<<EOF
			</tbody>
		    </table>
	        </div><br><br>
EOF;
    return $strBody;
}

/**
 * 构建表格html
 * @param  [type] $arrModuleData      [description]
 * @param  [type] $arrInterfaceData   [description]
 * @param  [type] &$arrOutItemByLevel [description]
 * @return [type]                     [description]
 */
function buildHTMLBody($arrModuleData, $arrInterfaceData, &$arrOutItemByLevel) {
    global $arrInterfaceToLevel;

    $strModule = $arrModuleData['module'];
    $strBody = <<<EOF
	<div id="container">
    <div class='table-title'>
EOF;
    $strBody .= "$strModule";
    $strBody .= <<<EOF
    </div>
      <table id="my-table-id" class="my-table-class">
        <thead id="my-table-header-id" class="my-table-header-class">
          <tr>
            <th>idc</th>
           	<th>urlkey</th>
            <th>level</th>
            <th>duty</th>
            <!--<th>business</th>-->
            <th>COST(ms)</th>
            <th width=100>QPS</th>
            <th>PV</th>
            <th>HTTP200Rate(%)</th>
            <th>HTTP302Rate(%)</th>
            <th>HTTP499Rate(%)</th>
            <th>STABLE(%)</th>
          </tr>
			</thead>
			<tbody id="my-table-body-id" class="my-table-body-class">
EOF;
    foreach($arrInterfaceData as $interface) {
        $strAttr = '';
        $strUrlKey = $interface['interface_name'];
        $interface['module'] = $arrModuleData['module'];
        if(isCoreUi($strUrlKey)){
            $strAttr .= "<span style='color:brown;background-color:yellow;'>[core]</span>";
            $arrOutItemByLevel['core'][] = $interface;
        }else if (isImportantUi($strUrlKey)){
            $strAttr .= "<span style='color:brown;background-color:aquamarine;'>[importance]</span>";
            $arrOutItemByLevel['importance'][] = $interface;
        }else{
            $arrOutItemByLevel['other'][] = $interface;
        }
        foreach($interface['idc_data'] as $arrItem) {
            $strIdcKey = $arrItem['idc_name'];
            $strBgColor = 'white';
            if('all' != $strIdcKey){
            	continue;
            }
//             $strSuffix = '';
//             if(!empty($arrInterfaceToLevel) && !empty($arrInterfaceToLevel[$strUrlKey])){
//                 $strSuffix = '['.$arrInterfaceToLevel[$strUrlKey].']';
//             }
            $arrItem['http_200_rate']['value'] *= 100;
            $arrItem['http_302_rate']['value'] *= 100;
            $arrItem['http_499_rate']['value'] *= 100;
            $arrItem['stable']['value'] *= 100;
            if(false && intval($arrItem['stable']) === 100) {
                $strBody .= "<tr class=tr-yellowfy style='background-color:{$strBgColor};'><td>{$strIdcKey}</td>\n
                <td>{$strUrlKey}{$strAttr}</td>\n
                <td>{$interface['level']}</td>\n
                <td>{$interface['duty']}</td>\n
                <td>{$arrItem['cost']}</td>\n
                <td>".floor($arrItem['qps']['value'] / 86400)."</td>\n
                <td>{$arrItem['qps']['value']}</td>\n
                <td>{$arrItem['http_200_rate']}</td>\n
                <td>{$arrItem['http_302_rate']}</td>\n
                <td>{$arrItem['http_499_rate']}</td>\n
                <td>{$arrItem['stable']}</td>\n
                </tr>";
            } else if(checkDanger($strUrlKey, $arrItem['stable']['value'])) {
                $strBody .= "<tr class=tr-redfy style='background-color:{$strBgColor};'><td>{$strIdcKey}</td>\n
                <td>{$strUrlKey}{$strAttr}</td>\n
                <td>{$arrModuleData['level']}</td>\n
                <td>".join(",", $arrModuleData['duty'])."</td>\n
                <td>{$arrItem['cost']['value']}</td>\n
                <td>".floor($arrItem['qps']['value'] / 86400)."</td>\n
                <td>{$arrItem['qps']['value']}</td>\n
                <td>{$arrItem['http_200_rate']['value']}</td>\n
                <td>{$arrItem['http_302_rate']['value']}</td>\n
                <td>{$arrItem['http_499_rate']['value']}</td>\n
                <td>{$arrItem['stable']['value']}</td>\n
                </tr>";
            } else {
                $strBody .= "<tr style='background-color:{$strBgColor};'><td>{$strIdcKey}</td>\n
                <td>{$strUrlKey}{$strAttr}</td>\n
                <td>{$arrModuleData['level']}</td>\n
                <td>".join(",", $arrModuleData['duty'])."</td>\n
                <td>{$arrItem['cost']['value']}</td>\n
                <td>".floor($arrItem['qps']['value'] / 86400)."</td>\n
                <td>{$arrItem['qps']['value']}</td>\n
                <td>{$arrItem['http_200_rate']['value']}</td>\n
                <td>{$arrItem['http_302_rate']['value']}</td>\n
                <td>{$arrItem['http_499_rate']['value']}</td>\n
                <td>{$arrItem['stable']['value']}</td>\n
                </tr>";
            }
        }
    }
    $strBody .= <<<EOF
			</tbody>
		    </table>
	        </div><br><br>
EOF;
    return $strBody;
}



function getInterfaceStableData($arrInput){
    if(!isset($arrInput['business']) || empty($arrInput['business'])){
        return false;
    }

    $date = $arrInput['date'];
    $arrBusiness = $arrInput['business'];
    $arrRet = array();
    foreach ($arrBusiness as $business) {
        $arrInput = array(
            'business' => $business,
        );
        $arrOutput = Tieba_Service::call('ems', 'getModuleByBusiness',$arrInput);
        $arrOutputData = $arrOutput['data'];
        $arrModulesInfo = array();
        foreach ($arrOutputData as $moduleData) {

            $arrGetInterfaceInput = array(
                'module' => $moduleData['module'],
                'date' => $date,
            );
            $arrGetInterfaceOutput = Tieba_Service::call('ems', 'getInterfaceStableChartByMod', $arrGetInterfaceInput);
            if (is_array($arrGetInterfaceOutput['data']) && !empty($arrGetInterfaceOutput['data'])) {
            	
            	$arrInterfaceData=sortArrByFieldByQPS($arrGetInterfaceOutput['data']);
                $arrRet[] = array(
                    'module' => $moduleData,
                    'interface' => $arrInterfaceData,
                );
            }
        }
    }
    
    return $arrRet;
}

/**
 *@param
 *@return
 */
function sortArrByFieldByQPS($arrData){
	$arrInterfaceData=array();
	$arrKeyQps =array();

	foreach($arrData as $interface) {
		$key =$interface['interface_name'];
		foreach($interface['idc_data'] as $arrItem) {
			$strIdcKey = $arrItem['idc_name'];
			if('all' != $strIdcKey){
				continue;
			}
			$arrKeyQps[$arrItem['qps']['value']]=$key;
			
		}
	}	
	krsort($arrKeyQps);
	foreach($arrKeyQps as $key){
		foreach($arrData as $interface) {
			$interfaceKey =$interface['interface_name'];
			if($key !=$interfaceKey){
				continue;
			}
			$arrInterfaceData[]=$interface;
		}
	}
	return $arrInterfaceData;

}

function _getDB() {
    $objTbMysql = Tieba_Mysql::getDB ( "forum_ems" );
    if ($objTbMysql && $objTbMysql->isConnected ()) {
        return $objTbMysql;
    } else {
        var_dump ( "db connect fail." );
        var_dump ( debug_backtrace () );
        return null;
    }
}

function getExplain() {
    $strBody = <<<EOF
     <p style="FONT-SIZE: 16px;">  <strong>指标说明:</strong><br>
     <strong>1. 耗时：cost</strong><br>
       对一个服务所有接口做汇总后的当日平均每个请求的耗时，单位(ms)。<br>
   <strong>2.流量：qps</strong><br>
      一个服务一天里的平均每秒请求数量，单位(个/秒)。<br>
   <strong>3.流量：pv</strong><br>
       一个服务一天里的整体请求数量。<br>
   <strong>4.HTTP200成功率</strong><br>
     服务所有接口返回http200的统计成功率，单位(100%)。<br>
   <strong>5.稳定性：stable</strong><br>
    服务所有接口返回stablecode=0的统计成功率，单位(100%)。<br>
   <strong>6.ui等级：core、importance</strong><br>
    接口级别，core级要求稳定性达到4个9，importance级要求达到3个9，其余接口要求达到2个9。<br>
</p>
<p style="FONT-SIZE: 20px;"> 其他模块稳定性请查看附件或者登陆ems平台查看：<a href="http://op.tieba.baidu.com/ems/summary">点击进入平台</a><br>
</p>
EOF;
    return $strBody;
}


function checkDanger($strUrl, $floRate) {
    global $arrCoreUrl;
    global $arrInterfaceToLevel;
    if(empty($arrInterfaceToLevel)){
        return false;
    }
    $strLevel = $arrInterfaceToLevel[$strUrl];
    /*if(null == $arrCoreUrl || empty($arrCoreUrl) || empty($strUrl)){
        return false;
    }*/
    if(CORE_LEVEL == $strLevel){
        if($floRate < 99.99) {
            return true;
        }
    }else if(IMPORTANCE_LEVEL == $strLevel){
        if($floRate < 99.9) {
            return true;
        }
    }else {
        if($floRate < 99){
            return true;
        } 
    }
    /*if(array_key_exists($strUrl, $arrCoreUrl)) {
        if($floRate < 99.99) {
            return true;
        }
    } else {
        if($floRate < 99.9) {
            return true;
        }
    }*/
    return false;
}

function isCoreUi($strUrl) {
    global $arrCoreUrl;
    if(null == $arrCoreUrl || empty($arrCoreUrl) || empty($strUrl)){
        return false;
    }
    if(in_array($strUrl, $arrCoreUrl)) {
        return true;
    }
    return false;
}

function isImportantUi($strUrl) {
    global $arrImportantUi;
    if(null == $arrImportantUi || empty($arrImportantUi) || empty($strUrl)){
        return false;
    }
    if(in_array($strUrl,$arrImportantUi)) {
        return true;
    }
    return false;
}


 
