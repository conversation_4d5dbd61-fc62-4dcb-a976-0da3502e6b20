<?php
/**
 * 检测定时任务队列
 * <EMAIL>
 * 2013/11/13 11:27
**/

//执行方式 cd $ORP_PATH ;  php app/msgpush/scripts/detectTimingTask.php  调整目录,add by magong<PERSON><PERSON>@baidu.com

define ('EASYSCRIPT_DEBUG',true);          //debug 模式
define ('EASYSCRIPT_THROW_EXEPTION',true);  //抛异常模式
//require_once("EasyScript.php");

define ('SCRIPTNAME',basename(__FILE__,".php"));   //定义脚本名
define ('BASEPATH',dirname(__FILE__));
define ('CONFPATH',BASEPATH."/conf");
define ('DATAPATH',BASEPATH."/data");
define ('LOGPATH',"./log/msgpush/scripts");
set_include_path(get_include_path() . PATH_SEPARATOR. BASEPATH.'/../');


function __autoload($strClassName)
{
    require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');

Lib_Util_Log::notice('script begin runing.');

define ('LOG', 'log');
Bingo_Log::init(array(
     LOG => array(
         'file'  => LOGPATH ."/". SCRIPTNAME. ".log",
         'level' => 0xFF,
     ),
), LOG);

/**
 * 获取当前毫秒级时间戳
 * @param：null
 * @return：string
 */
function microtime_float()
{
    list($usec, $sec) = explode(" ", microtime());
    return ((float)$usec + (float)$sec);
}

$easyScriptObj  = false;
try{
	$ScriptConf = array(
                    'memory_limit'   => '1024M',
                    'data_path'      => DATAPATH,
                    'conf_path'      => CONFPATH,
                    'lock_file_name' => SCRIPTNAME.'.lock',
                    'done_file_name' => SCRIPTNAME.'.done',
                    'db_alias'       => array(
                                           'im'     => 'forum_im',
                                        ),
                    'conf_alias'     => array(
                                           'main'     => 'main.conf',
                                        ),
                );

	$easyScriptObj = new Lib_Util_EasyScript($ScriptConf);
    //防止脚本重复执行
    if( $easyScriptObj->checkScriptIsRuning() === true ){
        Lib_Util_Log::notice(' script is runing.');
        exit;
    }
	
    Lib_Util_Log::notice(' script begin runing.');
	$JOBID_SUFFIX = 'jobid';
	$JOBINFO_SUFFIX = 'jobinfo';
	$KEY_SPLIT_CHAR = "_";

	// 初始化redis
	$redis = new Bingo_Cache_Redis('msglogic');
	if($redis == NULL){
		Bingo_Log::warning('script call reids failure!'.$easyScriptObj->getErr2String());
		exit;
	}
	
    $maxPriority = 20;
	for($i=0; $i <= $maxPriority; $i++){
		$key = join($KEY_SPLIT_CHAR, array($i, $JOBID_SUFFIX));
		$input = array(
			'key' => $key,
			'min' => 1,
			'max' => time(), 
			'offset' => 0, 
			'count' => 15,  //每次取10个
			);
		$task = $redis->ZRANGEBYSCORE($input);
		if($task === false || empty($task)){
			Bingo_Log::notice(".serialize($qkey) no need runing task");
			break;
		}else{
            $count = 0;
			$subTasks = $task['ret'][$key];
			Lib_Util_Log::notice('job_key :'.serialize($key).' subTasks :'.serialize($subTasks));
			foreach ($subTasks as $subTask) {
				$arrNMQParams = array(
					'jobid' => $subTask,
				);
				$strTopic = 'msgpush';
				$strCmd = 'sendTimingMsg';
				$arrNMQParams['group_id'] = mt_rand(1,3); //nmq 拆分，添加一个group_id字段
				$arrNMQParams['_partition_key'] = $arrNMQParams['group_id'];
				
				Bingo_Log::warning('sendTimingMsg_script_commit_begin_'.strval($subTask).':['.strval(microtime_float()).']');
				
				$arrNMQRes = Tieba_Commit::commit($strTopic, $strCmd, $arrNMQParams);
				
				Bingo_Log::warning('sendTimingMsg_script_commit_end_'.strval($subTask).':['.strval(microtime_float()).']');
				
				if($arrNMQRes===false || !isset($arrNMQRes['err_no']) || $arrNMQRes['err_no']!=0) {
                    Lib_Util_Log::warning('Failed to commit nmq.  cmd:'.$strCmd.'   topic:'.strTopic
                        .'   params:'.serialize($arrNMQParams));
				}
                $count++;
                if ( $count%5 == 0)
                    usleep(60 * 1000); //降低10陪
                    //if ($i ==0 && $count%5 == 0)
                    //usleep(600 * 1000); //降低10陪
			}
		}
	}
}catch(Exception $e){
    if($easyScriptObj !== false){
       Bingo_Log::warning('script run fail!'.$easyScriptObj->getErr2String());
    }else{
       Bingo_Log::warning('script run fail![null]');
    }
    Bingo_Log::notice('run fail.'.$easyScriptObj->getErr2String());
    exit;
}
Bingo_Log::notice('run success.');
