<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-09-09
 * @version 
 */

// 处理与lcs交互数据的打包解包
class Lib_Util_Pack {
    private static $strCuid = "";
    private static $arrInput = array();

    // 请求数据解析
    // lcs请求格式
    //    cuid: 12345
    //    cmd:  202001             // 请求类型 例如发消息，拉消息
    //    msg_tag:{sequence:123}   // server返回需带回
    //    lcs 添加appid lcs_ip lcs_port lcs_fd
    //    data:{..}
    public static function pack2array($format) {
        $rawInput = "";
        $arrInput = array();

        if ($format == "") { // for test 直接get post
            self::getParamFromRequest();
            Lib_Util_Log::notice("-----CLIENT-INPUT-----(post) cuid[". self::$strCuid. "] input[". serialize(self::$arrInput) . "]");
        } else {  // 解包请求数据
            $rawInput = file_get_contents("php://input");
            if ($format == "msgpack") {
                $arrInput = msgpack_unpack($rawInput);
                self::getParamFromRawInput($arrInput);
            } else if ($format == "json") {
                $arrInput = Bingo_String::json2array($rawInput, Bingo_Encode::ENCODE_UTF8);
                self::getParamFromRawInput($arrInput);
            } else if ($format == "mcpackraw") {
                $arrInput = @mc_pack_pack2array($rawInput);
                self::getParamFromRawInput($arrInput);
            } else if ($format == "mcpack") {
                $rawInput = @mc_pack_text2pack($rawInput); 
                $arrInput = @mc_pack_pack2array($rawInput);
                self::getParamFromRawInput($arrInput);
            } else {
                $arrInput = unserialize($rawInput);
                self::getParamFromRawInput($arrInput);
            }
            $len = strlen($rawInput);
            //Lib_Util_Log::notice("-----CLIENT-INPUT-----(len:$len) array[". serialize($arrInput) . "] pack[" . $rawInput . "]");
            Lib_Util_Log::notice("-----CLIENT-INPUT-----(len:$len) cuid[". self::$strCuid. "] input[". serialize($arrInput) . "]");
        }

        return self::$arrInput;
    }

    // 生成返回数据
    public static function array2pack($arrOutput, $format = 'json') {
        $strOutput = '';
        if ($format == "msgpack") {
            $strOutput = msgpack_pack($arrOutput);
        } else if ($format == "mcpackraw") {
            $strOutput = @mc_pack_array2pack($arrOutput);
        } else if ($format == "mcpack") {
            $strOutput = @mc_pack_array2pack($arrOutput);
            $strOutput = @mc_pack_pack2text($strOutput);
        } else {
            $strOutput = Bingo_String::array2json($arrOutput, Bingo_Encode::ENCODE_UTF8);
        } 

        $len = strlen($strOutput);
        //Lib_Util_Log::notice("-----SERVER-OUTPUT-----(len:$len) array[". serialize($arrOutput) . "] pack[" . $strOutput . "]");
        Lib_Util_Log::notice("-----SERVER-OUTPUT-----(len:$len) cuid[". self::$strCuid. "] output[". serialize($arrOutput) . "]");
        return $strOutput;
    }


    // 判断key是否存在
    private static function getInput($arrInput, $key, $default) {
        if (isset($arrInput[$key])) { 
            return $arrInput[$key];
        } else {
            return $default;
        }
    }

    private static function getParamFromRequest() {
        $strEmptyJson = "[]";
        self::$arrInput['head'] = array(
            'cuid' => strval(Bingo_Http_Request::getNoXssSafe('cuid','')),
            'cmd' => intval(Bingo_Http_Request::get('cmd','')),
            'msg_tag' => @json_decode(strval(Bingo_Http_Request::getNoXssSafe('msgTag',$strEmptyJson)), true),
        );
        self::$strCuid = self::$arrInput['head']['cuid'];
        self::$arrInput['body'] = Bingo_String::json2array(strval(Bingo_Http_Request::getNoXssSafe('data',$strEmptyJson)), Bingo_Encode::ENCODE_UTF8);
        return true;
    }

    private static function getParamFromRawInput($arrInput) {
        $arrEmpty = array();
        self::$arrInput['head'] = array(
            'cuid' => strval(self::getInput($arrInput, 'cuid', '')),
            'cmd' => intval(self::getInput($arrInput, 'cmd', 0)),
            'msg_tag' => self::getInput($arrInput, 'msgTag', $arrEmpty),
        );
        self::$strCuid = self::$arrInput['head']['cuid'];
        self::$arrInput['body'] = self::getInput($arrInput, 'data', $arrEmpty);
        return true;
    }
}
