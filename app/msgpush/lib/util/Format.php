<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-09-09
 * @version 
 */

class Lib_Util_Format {
    /** 
     * @brief 将key修改为驼峰，并且将value全部转换为字符串类型
     * @param 
     *       array $arrVals
     * @return 
     *       array $arrVals 
     **/
    public static function changeKeyStyle(array &$arrVals) {
        foreach( $arrVals as $key => $item ) { 
            $key2 = preg_replace("/([\w\d]{1,1})_([\w\d]{1,1})/ie","'$1'.strtoupper('$2')",$key);
            if ( is_array($item) ) { 
                $item = Lib_Util_Format::changeKeyStyle($arrVals[$key]);
            }else if( !is_string($item) ){
                $item = (string)$item;
            }

            if ( $key2 != $key) {
                $arrVals[$key2] = $item;
                unset( $arrVals[$key] );
            }
        }

        return $arrVals;
    } 

}
