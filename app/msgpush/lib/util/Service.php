<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013/9/18 15:55:25
 * @version 
 */
class Lib_Util_Service {
    const LOG_PREFIX_NAME = '__log_prefix__';
    
    /**
     * 
     * 对pc-service的调用进行封装，主要设置默认参数、打印日志
     * @param unknown_type $req
     * @param unknown_type $res
     */
    public static function call($strServiceName, $strMethod, $arrInput,$callType = 'local') 
    {
        //$timer_key = 'service_'.$strServiceName.'_'.$strMethod;
        //Bingo_Timer::start($timer_key);
        if($callType != 'local' ){
            $arrInput[self::LOG_PREFIX_NAME] = Lib_Util_Log::getLogPrefix();
        }
        $arrOut = Tieba_Service::call($strServiceName, $strMethod, $arrInput,
                                            NULL,NULL,'post','php','utf-8',$callType);
        //Bingo_Timer::end($timer_key);
        
        if (false === $arrOut){
            Lib_Util_Log::warning('Tieba_Service::call '.$strServiceName.'_'.$strMethod.' return false');
        }
        
        return $arrOut;
    }
    
}
