<?php
/**
 * 获取发送的消息统计数据.
 * <AUTHOR>
 * @name Stats
 * @desc 获取发送的消息统计数据。
 *
 */
//namespace xmpush;

class Lib_Mipush_Stats extends Lib_Mipush_HttpBase{
	private $package;	//android用
	private $bundle;	//ios用
	
	public function __construct(){
		parent::__construct();
		$this->package = Lib_Mipush_Constants::$packageName;
		$this->bundle = Lib_Mipush_Constants::$bundle_id;
	}
	
	public function getStats($startDate,$endDate,$type='android',$retries=1){
		if($type == 'ios'){
			$fields = array(
					'start_date' => $startDate,
					'end_date' => $endDate,
					'restricted_package_name' => $this->bundle
			);
		}else{
			$fields = array(
					'start_date' => $startDate,
					'end_date' => $endDate,
					'restricted_package_name' => $this->package
			);
		}
		$url = Lib_Mipush_Constants::$domain.Lib_Mipush_Constants::stats_url;
		$result = $this->getResult($url, $fields, $retries);
		return $result;
	}
	
}

?>
