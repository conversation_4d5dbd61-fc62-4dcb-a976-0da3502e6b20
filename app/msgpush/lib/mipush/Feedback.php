<?php
/**
 * 获取失效的regId列表.
 * <AUTHOR>
 * @name Feedback
 * @desc 获取失效的regId列表。
 *
 */
//namespace xmpush;

class Lib_Mipush_Feedback extends Lib_Mipush_HttpBase {
	
	public function __construct(){
		parent::__construct();
	}
	
        public function getInvalidRegIds($retries=1){
		$url = Lib_Mipush_Constants::fetch_invalid_regids_url;
		$result = $this->getResult($url, array(), $retries);
		return $result;
	}
	
}

?>
