<?php
/**
 * 常量定义.
 * <AUTHOR>
 * @name Constants
 * @desc 常量定义
 *
 */
//namespace xmpush;

class Lib_Mipush_Constants {
	public static $domain = 'https://api.xmpush.xiaomi.com';
	public static $comma = ',';
	public static $multi_topic_split = ';$;';
	public static $packageName = '';
	public static $bundle_id = '';
	public static $secret = '';
	
	const reg_url = '/v2/message/regid';
	const alias_url = '/v2/message/alias';
	const topic_url = '/v2/message/topic';
	const multi_topic_url = '/v2/message/multi_topic';
        const all_url = '/v2/message/all';
	const multi_messages_regids_url = '/v2/multi_messages/regids';
	const multi_messages_aliases_url = '/v2/multi_messages/aliases';
	const stats_url = '/v1/stats/message/counters';
	const message_trace_url = '/v1/trace/message/status';
	const messages_trace_url = '/v1/trace/messages/status';
	const validation_regids_url = '/v1/validation/regids';
	const subscribe_url = '/v2/topic/subscribe';
	const unsubscribe_url = '/v2/topic/unsubscribe';
	const fetch_invalid_regids_url = 'https://feedback.xmpush.xiaomi.com/v1/feedback/fetch_invalid_regids';

	const UNION = 'UNION';
	const INTERSECTION = 'INTERSECTION';
	const EXCEPT = 'EXCEPT';

	public static function setPackage($package){
		self::$packageName = $package;
	}
	
	public static function setSecret($secret){
		self::$secret = $secret;
	}

	public static function setBundleId($bundleId){
		self::$bundle_id = $bundleId;
	}
	
	public static function useOfficial(){
		self::$domain = 'https://api.xmpush.xiaomi.com';
	}
	
	public static function useSandbox(){
		self::$domain = 'https://sandbox.xmpush.xiaomi.com';
	}
}

?>
