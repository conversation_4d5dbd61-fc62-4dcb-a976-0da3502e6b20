<?php
/**
 * MiPush消息发送类.
 * <AUTHOR>
 * @name Sender
 * @desc MiPush消息发送
 *
 */
//namespace xmpush;

class Lib_Mipush_Sender extends Lib_Mipush_HttpBase {
	
	public function __construct(){
		parent::__construct();
	}
	
	//指定regId单发消息
	public function send(Lib_Mipush_Message $message,$regId,$retries=1){
		$fields = $message->getFields();
		$fields['registration_id'] = $regId;
		$url = Lib_Mipush_Constants::$domain.Lib_Mipush_Constants::reg_url;
		return $this->postResult($url, $fields,$retries);
	}
	
	//指定regId列表群发
	public function sendToIds(Lib_Mipush_Message $message,$regIdList,$retries=1){
		$fields = $message->getFields();
		$jointRegIds = '';
		foreach($regIdList as $regId){
			if(isset($regId)){
				$jointRegIds .= $regId.Lib_Mipush_Constants::$comma;
			}
		}
		$fields['registration_id'] = $jointRegIds;
		$url = Lib_Mipush_Constants::$domain.Lib_Mipush_Constants::reg_url;
		return $this->postResult($url, $fields,$retries);
	}
	
	//多条发送
	public function multiSend($targetMessages,$type,$retries=1){
		if($type == Lib_Mipush_TargetedMessage::TARGET_TYPE_ALIAS){
			$url = Lib_Mipush_Constants::$domain.Lib_Mipush_Constants::multi_messages_aliases_url;
		}else{
			$url = Lib_Mipush_Constants::$domain.Lib_Mipush_Constants::multi_messages_regids_url;
		}
		$data = array();
		foreach($targetMessages as $targetMsg){
			array_push($data,$targetMsg->getFields());
		}
		$fields = array('messages' => json_encode($data));
		return $this->postResult($url, $fields,$retries);
	}
	
	//多条发送
	public function multiSendAtTime($targetMessages,$type,$timeToSend,$retries=1){
		if($type == Lib_Mipush_TargetedMessage::TARGET_TYPE_ALIAS){
			$url = Lib_Mipush_Constants::$domain.Lib_Mipush_Constants::multi_messages_aliases_url;
		}else{
			$url = Lib_Mipush_Constants::$domain.Lib_Mipush_Constants::multi_messages_regids_url;
		}
		$data = array();
		foreach($targetMessages as $targetMsg){
			array_push($data,$targetMsg->getFields());
		}
		$fields = array('messages' => json_encode($data),'time_to_send' => $timeToSend);
		return $this->postResult($url, $fields,$retries);
	}

	//指定别名单发
	public function sendToAlias(Lib_Mipush_Message $message,$alias,$retries=1){
		$fields = $message->getFields();
		$fields['alias'] = $alias;
		$url = Lib_Mipush_Constants::$domain.Lib_Mipush_Constants::alias_url;
		return $this->postResult($url, $fields, $retries);
	}
	
	//指定别名列表群发
	public function sendToAliases(Lib_Mipush_Message $message,$aliasList,$retries=1){
		$fields = $message->getFields();
		$jointAliases = '';
		foreach($aliasList as $alias){
			if(strlen($jointAliases)>0){
				$jointAliases = $jointAliases.Lib_Mipush_Constants::$comma;
			}
			$jointAliases = $jointAliases.$alias;
		}
		$fields['alias'] = $jointAliases;
		$url = Lib_Mipush_Constants::$domain.Lib_Mipush_Constants::alias_url;
		return $this->postResult($url, $fields, $retries);
	}
	
	//指定topic群发
	public function broadcast(Lib_Mipush_Message $message,$topic,$retries=1){
		$fields = $message->getFields();
		$fields['topic'] = $topic;
		$url = Lib_Mipush_Constants::$domain.Lib_Mipush_Constants::topic_url;
		return $this->postResult($url, $fields, $retries);
	}
	
	//向所有设备发送消息
	public function broadcastAll(Lib_Mipush_Message $message,$retries=1){
		$fields = $message->getFields();
		$url = Lib_Mipush_Constants::$domain.Lib_Mipush_Constants::all_url;
		return $this->postResult($url, $fields, $retries);
	}

	//广播消息，多个topic，支持topic间的交集、并集或差集
	public function multiTopicBroadcast(Lib_Mipush_Message $message,$topicList,$topicOp,$retries=1){
		if(count($topicList)==1){
			return $this->broadcast($message,$topicList[0],$retries);
		}
		$fields = $message->getFields();
		$jointTopics = '';
		foreach($topicList as $topic){
			if(strlen($jointTopics)>0){
				$jointTopics = $jointTopics.Lib_Mipush_Constants::$multi_topic_split;
			}
			$jointTopics = $jointTopics.$topic;
		}
		$fields['topics'] = $jointTopics;
		$fields['topic_op'] = $topicOp;
		$url = Lib_Mipush_Constants::$domain.Lib_Mipush_Constants::multi_topic_url;
		return $this->postResult($url, $fields, $retries);
	}
}

?>
