<?php
/**
 * 订阅/取消订阅标签.
 * <AUTHOR>
 * @name Subscription
 *
 */
//namespace xmpush;

class Lib_Mipush_Subscription extends Lib_Mipush_HttpBase {
	
	public function __construct(){
		parent::__construct();
	}
	
        public function subscribe($regId, $topic,$retries=1){
		$fields = array('registration_id' => $regId,'topic' => $topic);
		$url = Lib_Mipush_Constants::$domain.Lib_Mipush_Constants::subscribe_url;
		return $this->postResult($url, $fields,$retries);
	}

	public function subscribeForRegids($regIdList, $topic,$retries=1){
		$jointRegIds = '';
		foreach($regIdList as $regId){
			if(isset($regId)){
				$jointRegIds .= $regId.Lib_Mipush_Constants::$comma;
			}
		}
		$fields = array('registration_id' => $jointRegIds,'topic' => $topic);
		$url = Lib_Mipush_Constants::$domain.Lib_Mipush_Constants::subscribe_url;
		return $this->postResult($url, $fields,$retries);
	}	

	public function unsubscribe($regId, $topic,$retries=1){
		$fields = array('registration_id' => $regId,'topic' => $topic);
		$url = Lib_Mipush_Constants::$domain.Lib_Mipush_Constants::unsubscribe_url;
		return $this->postResult($url, $fields,$retries);
	}

	public function unsubscribeForRegids($regIdList, $topic,$retries=1){
		$jointRegIds = '';
		foreach($regIdList as $regId){
			if(isset($regId)){
				$jointRegIds .= $regId.Lib_Mipush_Constants::$comma;
			}
		}
		$fields = array('registration_id' => $jointRegIds,'topic' => $topic);
		$url = Lib_Mipush_Constants::$domain.Lib_Mipush_Constants::unsubscribe_url;
		return $this->postResult($url, $fields,$retries);
	}	
}

?>
