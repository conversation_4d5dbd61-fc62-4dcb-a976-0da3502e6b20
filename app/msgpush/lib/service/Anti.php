<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013/9/24 17:39:47
 * @version
 */
class Lib_Service_Anti {
    const SERVICE_NAME = "anti";
	
	/**
	 *  The class constructor
	 *  @param array
	 *  @return bool
	 **/
    public static function actsctrlCommit($arrInput){
        if( !isset($arrInput['cmd']) || !isset($arrInput['app']) ){
            Bingo_Log::warning('param error!' . serialize($arrInput));
            return false;
        }

        $req = $arrInput;
        $req['rulegroup'] = array('app');
        $arrInput = array('req' => $req );
        $ret = Lib_Util_Service::call(self::SERVICE_NAME, 'antiActsctrlSubmit', $arrInput);
        Bingo_Log::pushNotice('Service_Anti::antiActsctrlSubmit', $ret['errno']);
        if($ret === false) {
            Bingo_Log::warning('call anti service fail, method:antiActsctrlSubmit, output:' . serialize($ret));
            return true;
        }
        if($ret['errno'] !== 0) {
            Bingo_Log::warning('anti strategy hit, output:' . serialize($ret));
            return false;
        }
        return true;
    }
}
