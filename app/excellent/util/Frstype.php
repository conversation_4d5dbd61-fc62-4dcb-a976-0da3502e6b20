<?php
/**
 * @brife 列表页样式枚举值
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-8-19
 * @version 
*/
class Util_Frstype {
	const FRSTYPE_IMAGE_NONE    = 0;
	const FRSTYPE_IMAGE_ONE     = 1;
	const FRSTYPE_IMAGE_THREE   = 2;
	const FRSTYPE_IMAGE_BANNER  = 3;
	
	
	public static $frsTypeImageCount = array(
		self::FRSTYPE_IMAGE_NONE   => 0,
		self::FRSTYPE_IMAGE_ONE    => 1,
		self::FRSTYPE_IMAGE_THREE  => 3,
		self::FRSTYPE_IMAGE_BANNER => 1,
	);
	protected static $frsType = array(
		self::FRSTYPE_IMAGE_NONE   => '无图',
		self::FRSTYPE_IMAGE_ONE    => '单图',
		self::FRSTYPE_IMAGE_THREE  => '三图',
		self::FRSTYPE_IMAGE_BANNER => '通栏',
	);
	/**
	 * 判断frs模式是否有效
	 * @param unknown $frsType
	 * @return boolean
	 */
	public static function isvalid($frsType){
		$frsType = intval($frsType);
		return isset(self::$frsType[$frsType]);
	}
}