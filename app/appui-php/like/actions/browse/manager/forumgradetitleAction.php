<?php
/***************************************************************************
 * 
 * Copyright (c) 2011 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file forumgradetitleAction.php
 * <AUTHOR>
 * @date 2011/05/06 19:06:36
 * @brief 
 *  
 **/
define ('MODULE_NAME','appui');
class forumgradetitleAction extends Manage_Browser_Base_Action
{
    public function init(){
        if(false === parent::init()){
            return false;
        }
        $this->_strTemplate = 'gradetitle.php';
        $this->_objView->setOutputType('');
        $this->_objView->setBaseDir(VIEW_PATH);
        return true;
    }
    public function process(){
        /*if($this->_bolUserLogin === false){
            $this->_intErrorNo = Appui_ErrorNo::errNeedLogin;
            Bingo_Log::warning('User need log in');
            return false;
        }*/
        
    	$outPutParams = array();
        if($this->checkRole() === false){
            $this->_intErrorNo = UserLikeErrorNo::errNeedRole;
            $url = "Location:"."http://tieba.baidu.com/f?kw=".$this->_strFname;
            header($url);
            exit;
            //return false;
        }
        
        //get title info 
        $retInfo = RpcIdlFulike::getGradeTitle($this->_intForumId,$this->_strFname);
        if (false === $retInfo){
            Bingo_Log::fatal('call_RpcIdlFulike_getGradeTitle_in_appui_like');
        }
        
        $this->_arrTpl['tbs']['common']=$this->_strTbs;
        $this->_arrTpl['forum']	= array(
        				'name' =>$this->_strFname,
        				'id'   =>$this->_intForumId,
        				'member_count' =>$this->_arrMemberInfo['member_count'],
        				'member_title' =>$this->_arrMemberInfo['member_name'],
        				'admin_list'   => $this->_arrManagerNames,
        		);
        
         $this->_arrTpl['user'] = array(
    			'is_login' => $this->_bolUserLogin, //���Ϊfalse����Ҫ���µı���
    			'id' => $this->_intUid, //�û����˺�id��Ŀǰ���������ȡ˽����Ŀ
    			'sid' => $this->_strSid,  //�û�session id,�����˳���¼
    			'portrait'=>Ucrypt::ucrypt_encode($this->_intUid,$this->_strUname), //�û�ͷ����ܴ������ڴ�PDC��ȡ���ݼ�չʾͷ��
    			'name' => $this->_strUname,
    			'is_admin'=> $this->_bolAdmin,
         		'is_member' =>$this->_isMember,
			);
        
    	if (!empty($retInfo)) 
    	{
    		$retUserDefined = fulike::isUserDefined($retInfo);
    		
    		if ( 1 !== $retUserDefined ) 
    		{
    			$retSaveTitle = RpcIdlTitle::getGradeTitle($this->_intForumId);
    			
    			if (!empty($retSaveTitle[0]['title'])) 
    			{
    				foreach ($retInfo['result_params']['res'] as $varkey => $varItem) 
    				{
    					if ( 1 == $varItem['is_user_define']) 
    					{
    						foreach ($retInfo['result_params']['res'][$varkey]['level_info'] as $levelKey => $levelItem) 
    						{
    							$retInfo['result_params']['res'][$varkey]['level_info'][$levelKey]['name'] = $retSaveTitle[0]['title'][$levelItem['id']]['name'];
    						}
    					}
    				}
    			}
    		}
    	}
    	
    	$this->_arrTpl['page'] = '';
    	$this->_arrTpl['info'] = $retInfo;
        
     	$this->_objView->setOutputType('');
    }
    protected function getSignTpl($arrSign){
        $sign = array();
        $sign = array(
            'user_name'     => $arrSign['user_name'],
            'user_id'		=> $arrSign['user_id'],
            'exp'           => $arrSign['cur_score'],
            'lv'            => $arrSign['level_id'],
            'like_date'     => Date('Y-m-d',$arrSign['in_time']),
            //'is_black'      => $true,
        );
        $sign['portrait']   = Ucrypt::ucrypt_encode($arrSign['user_id'], $arrSign['user_name']);
        return $sign;
    }
    protected function parseSignTpl($arrData){
        $signTpl = array();
        foreach($arrData as $arrSign){
            $signTpl[] = $this->getSignTpl($arrSign);
        }
        return $signTpl;
    }

}





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
