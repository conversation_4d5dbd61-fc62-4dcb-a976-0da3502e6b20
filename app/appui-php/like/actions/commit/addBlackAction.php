<?php
/***************************************************************************
 * 
 * Copyright (c) 2011 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file addBlackAction.php
 * <AUTHOR>
 * @date 2011/04/25 11:13:51
 * @brief 
 *  
 **/
/*
 * ���һ��������
 */
define ('MODULE_NAME','appui');
class addBlackAction extends Like_Commit_Base_Action
{
	protected $_bolIsDone		= false;
	protected $_intBlackUid		= 0;
	protected $_strBlackUname   = '';
	protected $_strForumname	= '';
	
	protected $_intBlackInfo    = array();  /*�����ӿ�*/
	
    public function init(){
         	
        $this->_intCmdNo = LikeCommitCmd::SetBlack;
        $ret = parent::init();
        
        if($ret === false){
            Bingo_Log::warning('in add black action, init failed');
            return false;
        }
        return true;
    }
    public function process(){    	
        /*if(!($this->checkActsCtrl($this->_intCmdNo))){
            return false;
        }*/
 //Bingo::warning('this is'.var_export($this,true));
        $uids	= strip_tags(trim(Bingo_Http_Request::get('uids', '')));
 		$this->_strBlackUname	= strip_tags(trim(Bingo_Http_Request::get('usn', '')));
        $this->_strForumName	= (trim(Bingo_Http_Request::get('fname', '')));
        $this->_strForumName = Bingo_String::xssDecode($this->_strForumName); //xss©��������fehtmlspecialchars ���룬����ת��, by jiangshuai 20140916 
        /*�ж��û�Ȩ��*/
 		if(!($this->checkRole()))
 		{
            return false;
 		}
 		/*�жϲ����û��Ƿ񱻺�*/
        $ret = fulike::getUserBlack($this->_intFid,$this->_intUid);
        if($ret === false){
            $this->_intErrorNo = Appui_ErrorNo::errTalkWithFulike;
            Bingo_Log::warning('get data from fulike failed');
            return false;
        }
        $isBlack = intval($ret);
        if($isBlack > 0){
            $this->_intErrorNo = UserLikeErrorNo::errIsBlack;
            Bingo_Log::warning('opuser is black');
            return false;
        }
        /*�жϲ�����ȷ��*/
        if((empty($this->_strBlackUname) && $uids ==="" ) || empty($this->_strForumName)){
        	$this->_intErrorNo = UserLikeErrorNo::errInvalidParam;
            Bingo_Log::warning('in add black action, uname or fname is empty');
            return false;
        }
        
        /*�����ӿ�*/
        if( $uids !=="" ){
        	$user_ids = explode(',',$uids);
        	$intUids = array();
        	foreach($user_ids as $value){
        		$intUids[] = intval($value);
        	}
        	
        	/*ͨ��id��ȡ�û���Ϣ*/
        	$this->_intBlackInfo = RpcIdlFucenter::getUserinfoByUids($intUids);
        	if($this->_intBlackInfo === false || empty($this->_intBlackInfo)){
        		$this->_intErrorNo = UserLikeErrorNo::errUserNotExit;
	            Bingo_Log::warning('get userInfo by uid erro!'.print_r($arrUinfos,TRUE));
	            return false;
        	}
        }
        else{
        	/*����û���id*/
        	//$ret=RpcIdlFucenter::getUserInfoByUname($this->_strBlackUname);
			$ret = $this->_getUidByUname($this->_strBlackUname);
        	if($ret === false || !isset($ret['uid'])){
	        	$this->_intErrorNo = UserLikeErrorNo::errUserNotExit;
	            Bingo_Log::warning('user does not exist');
	            return false;
        	}
        	$this->_intBlackInfo[0]['uid']      = $ret['uid'];
        	//$this->_intBlackInfo[0]['username'] = $ret['username'];
			$this->_intBlackInfo[0]['username'] = $this->_strBlackUname;
        }
        
        /*�ж��û��Ƿ�like�˱���*/
        /*$ret = fulike::getUserLike($this->_intFid,$this->_intBlackUid);
        if($ret === false){
            $this->_intErrorNo = Appui_ErrorNo::errTalkWithFulike;
            Bingo_Log::warning('get data from fulike failed');
            return false;
        }

        if($ret == 0){
            $this->_intErrorNo = UserLikeErrorNo::errYetLike;
            Bingo_Log::warning('yet like');
            return false;
        }*/
        /*���������û��Ƿ��Ѿ��Ǻ�����                   ----���������ж��Ƿ��Ѿ�������
    	$ret = fulike::getUserBlack($this->_intFid,$this->_intBlackUid);
        if($ret === false){
            $this->_intErrorNo = Appui_ErrorNo::errTalkWithFulike;
            Bingo_Log::warning('get data from fulike failed');
            return false;
        }
        $isBlack = intval($ret);
        if($isBlack > 0){
            $this->_intErrorNo = UserLikeErrorNo::errIsBlack;
            Bingo_Log::warning('the user is already blacked');
            return false;
        }
        */
        foreach($this->_intBlackInfo as $oneBlack){
        	//�������ȿ���
	        if(!($this->checkActsCtrl($this->_intCmdNo))){
	        	$this->_intErrorNo = UserLikeErrorNo::errActsCtrl;
	        	Bingo_Log::warning('actCtrl faile!');
	            return false;
	        }
        
	        $arrInput = array(
	            'command_no'    	=> $this->_intCmdNo,
	            'forum_id'      	=> $this->_intFid,
	        	'forumname'         => $this->_strForumName,
	            'op_uid'        	=> $this->_intUid,        	
	        	'cookie_username'	=> $this->_strUname,
	        	'username'      	=> $oneBlack['username'],
	        	'user_id'			=> $oneBlack['uid'],
	            //'now_time'      	=> time(),
	        );
	        $ret = RpcIdlPostcm::call($arrInput);
	        if($ret === false){
	            $this->_intErrorNo = Appui_ErrorNo::errTalkWithPostCm;
                Bingo_Log::fatal('call_RpcIdlPostcm_in_appui_like fail');
	            //Bingo_Log::warning('in add like, talk with postcm failed');
	            return false;
	        }
        }
        $this->_bolIsDone = true;
    }
    
	protected function render_page(){
		$outPutParams = array();	
		$outPutParams['no'] = $this->_intErrorNo;
        $outPutParams['data']['ret']['is_done'] = $this->_bolIsDone;
        $outPutParams['error'] = '';

		echo Bingo_String::array2json($outPutParams);
	}
}



/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
