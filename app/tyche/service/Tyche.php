<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/



/**
 * @file Tyche.php
 * <AUTHOR>
 * @date 2014/06/18 13:28:51
 * @brief 
 *  
 **/


define("MODULE","Tyche_service");
class Service_Tyche {

    const SERVICE_NAME = "Service_Tyche";
    protected static $_conf = null;
    //public static $service_ie = 'utf-8';

    const STATUS_INIT = 0;
    const STATUS_ACCEPT = 1;

    const AWARD_TYPE_REAL = 1;
    const AWARD_TYPE_EXKEY = 2;
    const AWARD_TYPE_FORTUNE = 3;

    const SOURCE_PLATFORM = 1;
    const EXKEY_ACT_TYPE = 2;

    const CHANCE_TYPE_DEFAULT = '_D';
    const CHANCE_TYPE_TRY = '_T';

    const DEFAULT_DUMP_COUNT = 6000;
    const MAX_DUMP_COUNT = 10000;

    const ACT_TYPE_TRYLUCK = 3 ;
    const ACT_TYPE_TRYAWARD = 4;

    const DB_DUPLICATE = 1062;

    const MEIZU_CASE_AWARD_ACT_ID = 117478;

    /**
     * @brief init
     * @return: true if success. false if fail.

     **/     
    private static function _init(){

        //add init code here. init will be called at every public function beginning.
        //not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
        //init should be recalled for many times.

        if(self::$_conf == null){
            self::$_conf = Bd_Conf::getConf("/app/tyche/service_tyche");
            if(self::$_conf == false){
                Bingo_Log::warning("init get conf fail.");
                return false;
            }

        }
        return true;
    }

    private static function _errRet($errno){
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    private static function _addAward($award_act_id, $award, $rawInput) {
        $callback = array();

        $dlInput = array(
            'award_act_id'=>$award_act_id,
            'award_type'=>intval($award['award_type']),
            'award_name'=>$award['award_name'],
            'desc_pic'=>$award['desc_pic'],
            'calc_type'=>intval($award['calc_type']),
            'calc_rule'=>$award['calc_rule'],
            'award_count'=>intval($award['award_count']),
        );
        if (isset($award['desc_text'])) $dlInput['desc_text'] = $award['desc_text'];
        if (isset($award['ext_info'])) $dlInput['ext_info'] = $award['ext_info'];

        $dlOut = Dl_Base_Base::addAward($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('add award failed. award_info['.substr(serialize($award),0,1024).']');
            return false;
        }
        $award_id = intval($dlOut['award_id']);

        if ($award['award_type']!=self::AWARD_TYPE_EXKEY) {
            $dlInput = array(
                'award_id'=>$award_id,
                'award_count'=>intval($award['award_count']),
            );
        /*
        $dlOut = Dl_Tycnt_Tycnt::setAwardCurCnt($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('set award current count failed. input['.serialize($dlInput).'] output['.serialize($output).']');
            return false;
        }
         */
            $callback []= array(
                'callback'=>'msetAwardCurCnt',
                'param'=>$dlInput,
                'award_id'=>$award_id,
            );
        }

        //if it is an exkey award
        if ($award['award_type']==self::AWARD_TYPE_EXKEY) {
            $award_id = $award_id;
            $exkey_count = 0;
            $exkey_act_id = 0;
            if (isset($award['exkey_act_id']))
            {
                $exkey_act_id = intval($award['exkey_act_id']);
                $exkey_count  = $award['award_count'];
            } 
            else 
            {
                $arrInput = array(
                    'status'=>1,
                    'related_source'=>intval($rawInput['related_source']),
                    'related_act_type'=>self::EXKEY_ACT_TYPE,
                    'related_act_id'=>$award_id,
                    'forum_id'=>intval($rawInput['forum_id']),
                    'can_dup'=>1,
                    'start_time'=>intval($rawInput['start_time']),
                    'end_time'=>intval($rawInput['end_time']),
                    'op_user_id'=>intval($rawInput['op_user_id']),
                );
                $arrOut = Tieba_Service::call('exkey', 'createExKeyAct', $arrInput);
                if ($arrOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                    $dlDeleteAwardInput = array(
                        'award_id'      => $award_id,
                        'award_act_id'  => $award_act_id,
                    );
                    $dlDeleteAwardOutput = Dl_Base_Base::deleteAward($dlDeleteAwardInput);
                    if (false === $dlDeleteAwardOutput) {
                        Bingo_Log::warning("delete award failed when createExKeyAct failed!");
                    }
                    Bingo_Log::warning('create an exkey act failed when add award. award_info['.substr(serialize($award),0,1024).']');
                    return false;
                }
                $exkey_act_id = $arrOut['data']['exkey_act_id'];

                $exkey_count = 0;
                if (!empty($award['keys'])) {
                    //add exkeys
                    $arrInput = array(
                        'exkey_act_id'=>$exkey_act_id,
                        'keys'=>$award['keys'],
                    );
                    $arrOut = Tieba_Service::call('exkey', 'addExKey', $arrInput);
                    if ($arrOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                        Bingo_Log::warning('add exkeys failed when add award. award_info['.substr(serialize($award),0,1024).']');
                        //return false;
                    }
                    $exkey_total_count = count($award['keys']);
                    $exkey_count = $arrOut['data']['succ_count'];
                    Bingo_Log::pushNotice("succ-total", "$exkey_count-$exkey_total_count");
                }
            }
            $exkey_act_id = (int)$exkey_act_id;
            if (0 >= $exkey_act_id) {
                $dlDeleteAwardInput = array(
                    'award_id'      => $award_id,
                    'award_act_id'  => $award_act_id,
                );
                $dlDeleteAwardOutput = Dl_Base_Base::deleteAward($dlDeleteAwardInput);
                if (false === $dlDeleteAwardOutput) {
                    Bingo_Log::warning("delete award failed when createExKeyAct failed!");
                    return false;
                }
            }
            //update success count and ext_info
            $award['ext_info']['exkey_act_id'] = $exkey_act_id;
            $dlInput = array(
                'award_id'=>$award_id,
                'award_act_id'=>$award_act_id,
                'award_count'=>$exkey_count,
                'ext_info'=>$award['ext_info'],
            );
            $dlOut = Dl_Base_Base::modifyAward($dlInput);
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('update exkey award info failed when add award. award_info['.substr(serialize($award),0,1024).']');
                return false;
            }
            $callback []= array(
                'callback'=>'maddAwardCurCnt',
                'param'=>$dlInput,
            );
        }
        return $callback;
    }

    private static function _modifyAward($award_act_id, $award_id, $award, $rawInput) {
        $callback = array();

        //get original info first
        $dlInput = array(
            'award_id'=>$award_id,
            'award_act_id'=>$award_act_id,
        );
        $dlOut = Dl_Base_Base::getAward($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('get original info failed when modify award. award_info['.substr(serialize($award),0,1024).']');
            return false;
        }
        $org_award = $dlOut['award'];
        if ($dlOut['award']['award_type']==self::AWARD_TYPE_EXKEY) {
            $award['ext_info']['exkey_act_id'] = $org_award['ext_info']['exkey_act_id'];
        }

        if (isset($award['award_type'])) $dlInput['award_type'] = intval($award['award_type']);
        if (isset($award['award_name'])) $dlInput['award_name'] = $award['award_name'];
        if (isset($award['desc_text'])) $dlInput['desc_text'] = $award['desc_text'];
        if (isset($award['desc_pic'])) $dlInput['desc_pic'] = $award['desc_pic'];
        if (isset($award['calc_type'])) $dlInput['calc_type'] = intval($award['calc_type']);
        if (isset($award['calc_rule'])) $dlInput['calc_rule'] = $award['calc_rule'];
        //if (isset($award['award_count'])) $dlInput['award_count'] = intval($award['award_count']);
        if (isset($award['ext_info'])) $dlInput['ext_info'] = $award['ext_info'];
        $dlOut = Dl_Base_Base::modifyAward($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('modify award failed when modify award. award_info['.serialize($award).']');
            return false;
        }

        //set award count
        if (isset($award['award_count']) && $org_award['award_type']!=self::AWARD_TYPE_EXKEY) {
            $dlInput = array(
                'award_ids'=>array($award_id),
            );
            $dlOut = Dl_Tycnt_Tycnt::mgetAwardCurCnt($dlInput);
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('add award count failed. input['.serialize($dlInput).'] output['.serialize($output).']');
                return false;
            }
            $org_cur_cnt = intval($dlOut['award_cur_counts'][$award_id]);

            $intAwardDeltaCount = $award['award_count'] - $org_award['award_count'];

            //update count
            $dlInput = array(
                'award_id'      =>$award_id,
                'award_act_id'  =>$award_act_id,
                'award_count'   =>(int)$award['award_count'],
            );

            $dlOut = Dl_Base_Base::modifyAward($dlInput);
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('update award count failed. award_info['.substr(serialize($award),0,1024).']');
                return false;
            }
    
            // 兼容活动头图
            $strCallFrom = $rawInput['call_from'];
            $dlInput = array(
                'award_id'=>$award_id,
                'award_count'=>$org_cur_cnt+$intAwardDeltaCount,
            );
            if('activityhead' === $strCallFrom) {
                Bingo_Log::warning(__FUNCTION__."_".$strCallFrom);
                $dlInput['award_count'] = (int)$award['award_count'];
            }
        /*
        $dlOut = Dl_Tycnt_Tycnt::setAwardCurCnt($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('add award count failed. input['.serialize($dlInput).'] output['.serialize($output).']');
            return false;
        }
         */
            $callback []= array(
                'callback'=>'msetAwardCurCnt',
                'param'=>$dlInput,
            );
        }

        //if it is an exkey award
        if ($award['award_type']==self::AWARD_TYPE_EXKEY && !empty($award['keys'])) {
            $exkey_act_id = $award['ext_info']['exkey_act_id'];
            //add exkeys
            $arrInput = array(
                'exkey_act_id'=>$exkey_act_id,
                'keys'=>$award['keys'],
            );
            $arrOut = Tieba_Service::call('exkey', 'addExKey', $arrInput);
            if ($arrOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('add exkeys failed when add award. award_info['.substr(serialize($award),0,1024).']');
                return false;
            }

            //update success count
            $dlInput = array(
                'award_id'=>$award_id,
                'award_act_id'=>$award_act_id,
                'award_count'=>$org_award['award_count']+$arrOut['data']['succ_count'],
            );
            $dlOut = Dl_Base_Base::modifyAward($dlInput);
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('update award count failed. award_info['.substr(serialize($award),0,1024).']');
                return false;
            }
            $dlInput['award_count'] = $arrOut['data']['succ_count'];
        /*
        $dlOut = Dl_Tycnt_Tycnt::addAwardCurCnt($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('add award current count failed. input['.serialize($dlInput).'] output['.serialize($output).']');
            return false;
        }
         */
            $callback []= array(
                'callback'=>'maddAwardCurCnt',
                'param'=>$dlInput,
            );
        }

        return $callback;
    }

    //杩藉姞濂栧搧鏁帮紝淇敼濂栧搧淇℃伅
    private static function _appendAward($award_act_id, $award_id, $award, $rawInput) {
        $intCurAwardCount = 0;
        $bolNeedModifyAwardCurCount = true;
        $callback = array();

        //get original info first
        $dlInput = array(
            'award_id'=>$award_id,
            'award_act_id'=>$award_act_id,
        );
        $dlOut = Dl_Base_Base::getAward($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('get original info failed when modify award. award_info['.substr(serialize($award),0,1024).']');
            return false;
        }
        $org_award = $dlOut['award'];
        if ($dlOut['award']['award_type']==self::AWARD_TYPE_EXKEY) {
            $award['ext_info']['exkey_act_id'] = $org_award['ext_info']['exkey_act_id'];
        }

        $dlCurCountInput = array(
            'award_ids'=>array($award_id),
        );
        $dlCurCountOutput = Dl_Tycnt_Tycnt::mgetAwardCurCnt($dlCurCountInput);
        if ($dlCurCountOutput['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('get award current count failed!');
            return false;
        }
        $org_cur_cnt = intval($dlCurCountOutput['award_cur_counts'][$award_id]);

        if (isset($award['award_type'])) $dlInput['award_type'] = intval($award['award_type']);
        if (isset($award['award_name'])) $dlInput['award_name'] = $award['award_name'];
        if (isset($award['desc_text'])) $dlInput['desc_text'] = $award['desc_text'];
        if (isset($award['desc_pic'])) $dlInput['desc_pic'] = $award['desc_pic'];
        if (isset($award['calc_type'])) $dlInput['calc_type'] = intval($award['calc_type']);
        if (isset($award['calc_rule'])) $dlInput['calc_rule'] = $award['calc_rule'];

        //award count setting
        if (isset($award['award_append_count'])) {
            $intDeltaCount = (int)$award['award_append_count'];
            $dlInput['award_count'] = $org_award['award_count']+$intDeltaCount;
            $intCurAwardCount = $org_cur_cnt + $intDeltaCount;
        } else if (isset($award['award_cur_count'])) {
            $intCurAwardCount = (int)$award['award_cur_count'];
            $intDeltaCount = $intCurAwardCount - $org_cur_cnt;
            $intAwardCount = $org_award['award_count']+$intDeltaCount;
            if ($intAwardCount < 0) {
                //process bad case
                $intAwardCount = $intCurAwardCount;
            }
            $dlInput['award_count'] = $intAwardCount;
        } else {
            $bolNeedModifyAwardCurCount = false;
        }

        if (isset($award['ext_info'])) $dlInput['ext_info'] = $award['ext_info'];
        $dlOut = Dl_Base_Base::modifyAward($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('modify award failed when modify award. award_info['.serialize($award).']');
            return false;
        }

        /*
    //set award count
    if ($bolNeedModifyAwardCurCount && $org_award['award_type']!=self::AWARD_TYPE_EXKEY) {
        $append_award_count = (int)$award['award_append_count'];

        $dlInput = array(
            'award_id'      => $award_id,
            'award_count'   => $intCurAwardCount,
        );

        $dlOut = Dl_Tycnt_Tycnt::setAwardCurCnt($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('add award count failed. input['.serialize($dlInput).'] output['.serialize($output).']');
            return false;
        }

        $callback []= array(
            'callback'=>'msetAwardCurCnt',
            'param'=>$dlInput,
        );
    }
         */

        //if it is an exkey award
        if ($bolNeedModifyAwardCurCount) {
            if ($award['award_type']==self::AWARD_TYPE_REAL) {
                /*
                $dlInput = array(
                    'award_id'=>$award_id,
                    'award_act_id'=>$award_act_id,
                    'award_count'=>$org_award['award_count']+$award['award_append_count'],
                );
                 */

                $callback []= array(
                    'callback'=>'msetAwardCurCnt',
                    'param'=>array(
                        'award_id'=>$award_id,
                        'award_act_id'=>$award_act_id,
                        'award_count'=>$intCurAwardCount,
                    ),
                );
            } elseif ($award['award_type']==self::AWARD_TYPE_EXKEY && !empty($award['keys'])) {
                $exkey_act_id = $award['ext_info']['exkey_act_id'];
                //add exkeys
                $arrInput = array(
                    'exkey_act_id'=>$exkey_act_id,
                    'keys'=>$award['keys'],
                );

                if ('helloworld' === $award['keys'][0]) {
                    Bingo_Log::warning('fake change only award info!');
                    return true;
                }

                $arrOut = Tieba_Service::call('exkey', 'addExKey', $arrInput);
                if ($arrOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('add exkeys failed when add award. award_info['.substr(serialize($award),0,1024).']');
                    return false;
                }
                Bingo_Log::pushNotice("succ_count", $arrOut['data']['succ_count']);
                Bingo_Log::pushNotice("real_count", $award['keys']);
                //update success count
                $dlInput = array(
                    'award_id'=>$award_id,
                    'award_act_id'=>$award_act_id,
                    'award_count'=>$org_award['award_count']+$arrOut['data']['succ_count'],
                );
                $dlOut = Dl_Base_Base::modifyAward($dlInput);
                if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('update award count failed. award_info['.substr(serialize($award),0,1024).']');
                    return false;
                }

                $callback []= array(
                    'callback'=>'maddAwardCurCnt',
                    'param'=>array(
                        'award_id'=>$award_id,
                        'award_act_id'=>$award_act_id,
                        'award_count'=>$arrOut['data']['succ_count'],
                    ),
                );
            } else if(self::AWARD_TYPE_FORTUNE==$award['award_type']) {
                $callback[]= array(
                    'callback'=>'msetAwardCurCnt',
                    'param'=>array(
                        'award_id'=>$award_id,
                        'award_act_id'=>$award_act_id,
                        'award_count'=>$intCurAwardCount,
                    ),
                );
            }
        }

        return $callback;
    }

    public static function createAwardAct($arrInput) {
        if (!isset($arrInput['related_act_type']) || !isset($arrInput['related_act_id']) || !isset($arrInput['related_source']) ||
            !isset($arrInput['forum_id']) || !isset($arrInput['start_time']) || !isset($arrInput['end_time']) ||
            !isset($arrInput['op_user_id'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
            }

        if ($arrInput['status']==self::STATUS_ACCEPT && !isset($arrInput['audit_user_id'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        $time = time();
        Bingo_Timer::start('tyche_createAwardAct');
        $dlInput = array(
            'related_source'=>intval($arrInput['related_source']),
            'related_act_type'=>intval($arrInput['related_act_type']),
            'related_act_id'=>intval($arrInput['related_act_id']),
            'can_dup'=>intval($arrInput['can_dup']),
            'forum_id'=>intval($arrInput['forum_id']),
            'start_time'=>intval($arrInput['start_time']),
            'end_time'=>intval($arrInput['end_time']),
            'op_user_id'=>intval($arrInput['op_user_id']),
            'op_time'=>$time,
        );
        $dlInput['ext_info']['init_partake'] = isset($arrInput['init_partake']) ? intval($arrInput['init_partake']) : 0;
        $dlInput['ext_info']['partake_ratio'] = isset($arrInput['partake_ratio']) ? intval($arrInput['partake_ratio']) : 1;
        if (isset($arrInput['audit_user_id'])) {
            $dlInput['audit_user_id'] = intval($arrInput['audit_user_id']);
            $dlInput['audit_time'] = $time;
        }
        if (isset($arrInput['status'])) $dlInput['status'] = intval($arrInput['status']);
        if (isset($arrInput['ext_info'])) $dlInput['ext_info'] = $arrInput['ext_info'];
        $dlOut = Dl_Base_Base::createAwardAct($dlInput);
        if ($dlOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal('create award act failed. input['.serialize($arrInput).']');
            return self::_errRet($dlOut['errno']);
        }
        $award_act_id = $dlOut['award_act_id'];
        Bingo_Timer::end('tyche_createAwardAct');
        $data = array(
            'award_act_id'=>$award_act_id,
        );

        Bingo_Timer::start('tyche_addAward');
        //if there are awards
        if (is_array($arrInput['award_info'])) {
            $callback = array();
            foreach ($arrInput['award_info'] as $i=>$award) {
                Bingo_Timer::start("tyche_addAward_idx_{$i}");
                $res = self::_addAward($award_act_id, $award, $arrInput);
                if ($res !== false) {
                    $callback = array_merge($callback, $res);
                }
                Bingo_Timer::end("tyche_addAward_idx_{$i}");
            }
            $func = array();
            foreach ($callback as $cb) {
                if (isset($func[$cb['callback']])) {
                    $func[$cb['callback']] []= $cb['param'];
                } else {
                    $func[$cb['callback']] = array($cb['param']);
                }
            }
            foreach ($func as $callback=>$params) {
                switch ($callback) {
                case 'maddAwardCurCnt':
                    $dlInput = $params;
                    $dlOut = Dl_Tycnt_Tycnt::maddAwardCurCnt($dlInput);
                    if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                        Bingo_Log::warning('add award current count failed. input['.serialize($dlInput).'] output['.serialize($output).']');
                        return false;
                    }
                    break;
                case 'msetAwardCurCnt':
                    $dlInput = $params;
                    $dlOut = Dl_Tycnt_Tycnt::msetAwardCurCnt($dlInput);
                    if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                        Bingo_Log::warning('set award current count failed. input['.serialize($dlInput).'] output['.serialize($output).']');
                        return false;
                    }
                    break;
                default:
                }
            }
        }
        Bingo_Timer::end('tyche_addAward');

        /*
        Bingo_Timer::start('tyche_initActPartake');
        //if there are init_partake  add by yunting
        if (0 < intval($arrInput['init_partake'])) {
            $initInput = array('award_act_id' => $award_act_id, 'init_partake' => intval($arrInput['init_partake']));
            $initRes = Dl_Tycnt_Tycnt::initActPartake($initInput);
            if ($initRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('init act partake fail param:'. serialize($initInput));
            }
        }
        Bingo_Timer::end('tyche_initActPartake');
         */

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );  
        return $arrOutput;
    }


    /*
     *
     * @brief only update award_count
     */
    public static function modifyAwardCount($arrInput) {
        if (!isset($arrInput['award_act_id']) || !isset($arrInput['award_id']) || !isset($arrInput['award_count'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        $dlInput = array(
            'award_act_id'  => (int)$arrInput['award_act_id'],
            'award_id'      => (int)$arrInput['award_id'],
            'award_count'   => (int)$arrInput['award_count'],
        );

        $dlOut = Dl_Base_Base::modifyAward($dlInput);
        if (0 !== (int)$dlOut['errno']) {
            Bingo_Log::warning("modifyAwardCount failed, input is [" . serialize($arrInput) . "]");
            return false;
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );  
        return $arrOutput;
    }


    public static function modifyAwardAct($arrInput) {
        if (!isset($arrInput['award_act_id']) || !isset($arrInput['op_user_id'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        if ($arrInput['status']==self::STATUS_ACCEPT && !isset($arrInput['audit_user_id'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        $time = time();
        Bingo_Timer::start('tyche_modifyAwardAct');
        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
            'op_user_id'=>intval($arrInput['op_user_id']),
            'op_time'=>$time,
        );
        if (isset($arrInput['audit_user_id'])) {
            $dlInput['audit_user_id'] = intval($arrInput['audit_user_id']);
            $dlInput['audit_time'] = $time;
        }
        if (isset($arrInput['can_dup'])) $dlInput['can_dup'] = intval($arrInput['can_dup']);
        if (isset($arrInput['status'])) $dlInput['status'] = intval($arrInput['status']);
        if (isset($arrInput['start_time'])) $dlInput['start_time'] = intval($arrInput['start_time']);
        if (isset($arrInput['end_time'])) $dlInput['end_time'] = intval($arrInput['end_time']);
        if (isset($arrInput['ext_info'])) $dlInput['ext_info'] = $arrInput['ext_info'];
        $dlInput['ext_info']['init_partake'] = isset($arrInput['init_partake']) ? intval($arrInput['init_partake']) : 0;
        $dlInput['ext_info']['partake_ratio'] = isset($arrInput['partake_ratio']) ? intval($arrInput['partake_ratio']) : 1;

        // first delete cache
        $arrClearCacheInput = array(
            'award_act_id' => array(
                $arrInput['award_act_id'],
            ),
        );

        $arrClearCacheOutput = Service_AwardAct::mdelAwardActCache($arrClearCacheInput);
        if (false === $arrClearCacheOutput || 0 !== (int)$arrClearCacheOutput['errno']) {
            Bingo_Log::warning("fail to remove cache when modifyAwardAct, award_act_id : ".$arrInput['award_act_id']);
            return self::_errRet($arrClearCacheOutput['errno']);
        }

        $dlOut = Dl_Base_Base::modifyAwardAct($dlInput);
        if ($dlOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal('modify award act failed. input['.serialize($arrInput).']');
            return self::_errRet($dlOut['errno']);
        }
        Bingo_Timer::end('tyche_modifyAwardAct');

        $award_act_id = intval($arrInput['award_act_id']);

        //if there are awards
        Bingo_Timer::start('tyche_modifyAward');
        if (is_array($arrInput['award_info'])) {
            //get current awards info
            $dlInput = array(
                'award_act_id'=>$award_act_id,
            );
            $dlOut = Dl_Base_Base::getAwardByAct($dlInput);
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::fatal("get award act failed. award_act_id[{$award_act_id}]");
                return self::_errRet($dlOut['errno']);
            }
            $orgAwardInfo = $dlOut['award_info'];

            //diff the award id
            $orgAwardId = array();
            foreach ($orgAwardInfo as $award) {
                $orgAwardId[$award['award_id']] = 1;
            }

            Bingo_Timer::start('tyche_modifyAward');
            $callback = array();
            //update or add award
            foreach ($arrInput['award_info'] as $i=>$award) {
                Bingo_Timer::start("tyche_modify_idx_{$i}");
                if (!empty($award['award_id']) && isset($orgAwardId[intval($award['award_id'])])) {
                    $award_id = intval($award['award_id']);
                    $res = self::_modifyAward($award_act_id, $award_id, $award, $arrInput);
                    $orgAwardId[$award_id] = 2;
                } else {
                    $res = self::_addAward($award_act_id, $award, $arrInput);
                }
                if ($res !== false) {
                    $callback = array_merge($callback, $res);
                }
                Bingo_Timer::end("tyche_modify_idx_{$i}");
            }
            Bingo_Timer::end('tyche_modifyAward');

            $func = array();
            foreach ($callback as $cb) {
                if (isset($func[$cb['callback']])) {
                    $func[$cb['callback']] []= $cb['param'];
                } else {
                    $func[$cb['callback']] = array($cb['param']);
                }
            }
            foreach ($func as $callback=>$params) {
                switch ($callback) {
                case 'maddAwardCurCnt':
                    $dlInput = $params;
                    $dlOut = Dl_Tycnt_Tycnt::maddAwardCurCnt($dlInput);
                    if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                        Bingo_Log::warning('add award current count failed. input['.serialize($dlInput).'] output['.serialize($output).']');
                        return false;
                    }
                    break;
                case 'msetAwardCurCnt':
                    $dlInput = $params;
                    $dlOut = Dl_Tycnt_Tycnt::msetAwardCurCnt($dlInput);
                    if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                        Bingo_Log::warning('set award current count failed. input['.serialize($dlInput).'] output['.serialize($output).']');
                        return false;
                    }
                    break;
                default:
                }
            }

            //delete no use award
            foreach ($orgAwardId as $award_id=>$flag) {
                if ($flag !== 2) {
                    $dlInput = array(
                        'award_id'      => $award_id,
                        'award_act_id'  => $award_act_id,
                    );
                    $dlOut = Dl_Base_Base::deleteAward($dlInput);
                    if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                        Bingo_Log::warning("delete award failed when modify award act. award_id[{$award}]");
                    }
                }
            }
        }
        Bingo_Timer::end('tyche_modifyAward');

        /* no use
        Bingo_Timer::start('tyche_initPartake');
        //add by yunting
        $partInput = array(
            'award_act_id' => $award_act_id, 
            'init_partake' => intval($arrInput['init_partake']),
        );
        $partRes = Dl_Tycnt_Tycnt::initActPartake($partInput);
        if ($partRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("update partake times fail");
        }
        Bingo_Timer::end('tyche_initPartake');
         */

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );  
        return $arrOutput;
    }


    public static function deleteAward($arrInput) {
        if (!isset($arrInput['award_act_id']) || !isset($arrInput['award_id'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        //check the if the act is match with award_act_id
        $award_act_id   = (int)$arrInput['award_act_id'];
        $award_id       = (int)$arrInput['award_id'];

        $dlInput = array(
            'award_act_id'=>$award_act_id,
        );

        $dlOut = Dl_Base_Base::getAwardByAct($dlInput);

        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal("get award act failed. award_act_id[{$award_act_id}]");
            return self::_errRet($dlOut['errno']);
        }
        $arrAwardInfo = $dlOut['award_info'];

        $bolHasThisAward = false;
        foreach ($arrAwardInfo as $award) {
            $award_id_tmp = (int)$award['award_id'];
            if ($award_id === $award_id_tmp) {
                $bolHasThisAward = true;
                break;
            }
        }

        if (!$bolHasThisAward) {
            Bingo_Log::fatal("the award_id is not match with award_act_id, [". serialize($arrInput) ."].");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $dlInput = array(
            'award_id'      => $award_id,
            'award_act_id'  => $award_act_id,
        );

        $dlOut = Dl_Base_Base::deleteAward($dlInput);
        Bingo_Log::warning(serialize($dlOut));
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("fail to delete award, award_id is $award_id.");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrOutput = array(
            'errno'     => 0,
            'errmsg'    => 'success',
            'data'      => $data,
        );  
        return $arrOutput;

    }

    public static function modifyAward($arrInput) {
        if (!isset($arrInput['award_act_id']) || !isset($arrInput['award_info'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        $award_act_id = intval($arrInput['award_act_id']);

        $callback = array();
        foreach ($arrInput['award_info'] as $i=>$award) {
            $award_id = intval($award['award_id']);
            if ($award_id == 0) {
                $res = self::_addAward($award_act_id, $award, $arrInput);
            } else {
                $res = self::_appendAward($award_act_id, $award_id, $award, $arrInput);
            }
            if ($res !== false) {
                $callback = array_merge($callback, $res);
            }
        }

        $func = array();
        $arrAwardIds = array();
        $arrExkeyMaps = array();
        foreach ($callback as $cb) {
            $intAwardId = (int)$cb['param']['award_id'];
            if (0 !== $intAwardId) {
                $arrAwardIds [] = $intAwardId;
            }
            if (isset($cb['param']['ext_info']['exkey_act_id']))
            {
                $arrExkeyMaps[$intAwardId] = intval($cb['param']['ext_info']['exkey_act_id']);
            }
            if (isset($func[$cb['callback']])) {
                $func[$cb['callback']] []= $cb['param'];
            } else {
                $func[$cb['callback']] = array($cb['param']);
            }
        }
        foreach ($func as $callback=>$params) {
            switch ($callback) {
            case 'maddAwardCurCnt':
                $dlInput = $params;
                $dlOut = Dl_Tycnt_Tycnt::maddAwardCurCnt($dlInput);
                if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('add award current count failed. input['.serialize($dlInput).'] output['.serialize($output).']');
                    return false;
                }
                break;
            case 'msetAwardCurCnt':
                $dlInput = $params;
                $dlOut = Dl_Tycnt_Tycnt::msetAwardCurCnt($dlInput);
                if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('set award current count failed. input['.serialize($dlInput).'] output['.serialize($output).']');
                    return false;
                }
                break;
            default:
            }
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'award_ids' => $arrAwardIds,
            'exkey_ids' => $arrExkeyMaps, 
        );  
        return $arrOutput;
    }

    public static function getAwardAct($arrInput) {
        if (!isset($arrInput['award_act_id'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        //base info
        Bingo_Timer::start('tyche_getAwardAct');
        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
        );
        $dlOut = Dl_Base_Base::getAwardAct($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("get award act failed. award_act_id[{$arrInput['award_act_id']}]");
            return self::_errRet($dlOut['errno']);
        }
        $data = array(
            'award_act_info'=>$dlOut['award_act_info'],
        );
        Bingo_Timer::end('tyche_getAwardAct');

        //award_info
        Bingo_Timer::start('tyche_getAwardByAct');
        $dlOut = Dl_Base_Base::getAwardByAct($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("get award info failed. award_act_id[{$arrInput['award_act_id']}]");
            return self::_errRet($dlOut['errno']);
        }
        $data['award_info'] = $dlOut['award_info'];
        Bingo_Timer::end('tyche_getAwardByAct');

        //award_count
        Bingo_Timer::start('tyche_getAwardCurCnt');
        $dlInput = array('award_ids'=>array());
        foreach ($data['award_info'] as $award) {
            $dlInput['award_ids'] []= $award['award_id'];
        }
        $dlOut = Dl_Tycnt_Tycnt::mgetAwardCurCnt($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("get award info failed. award_act_id[{$arrInput['award_act_id']}]");
            return self::_errRet($dlOut['errno']);
        }
        foreach ($data['award_info'] as &$award) {
            $award['award_cur_count'] = $dlOut['award_cur_counts'][$award['award_id']];
        }
        unset($award);
        Bingo_Timer::end('tyche_getAwardCurCnt');

        //recent award
        Bingo_Timer::start('tyche_getRecentAward');
        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
        );
        $dlOut = Dl_Tycnt_Tycnt::getRecentAwardRec($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("get recent award record failed. award_act_id[{$arrInput['award_act_id']}]");
            return self::_errRet($dlOut['errno']);
        }
        $data['recent_award_records'] = $dlOut['recent_award_records'];
        Bingo_Timer::end('tyche_getRecentAward');

        /*
        //partake info add by yunting
        Bingo_Timer::start('tyche_getPartake');
        $partakeOut = Dl_Tycnt_Tycnt::getActPartake($dlInput);
        if (isset($partakeOut['partake_info']['show_partake']) && $partakeOut['partake_info']['show_partake'] > 0) {
            $data['partake_info'] = $partakeOut['partake_info'];
        }
        Bingo_Timer::end('tyche_getPartake');
         */

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );  
        return $arrOutput;
    }

    public static function addAwardCount($arrInput) {
    }

    private static function _calcRecChanceCnt($chance_record) {
        $res = array(
            'chance_count'=>0,
            'record_add_count'=>0,
            'record_decr_count'=>0,
        );
        foreach ($chance_record as $rec) {
            if ($rec['chance_count']>0) {
                $res['record_add_count'] += $rec['chance_count'];
            } elseif ($rec['chance_count']<0) {
                $res['record_decr_count'] += $rec['chance_count'];
            }
            $res['chance_count'] += $rec['chance_count'];
        }
        return $res;
    }

    public static function addUserChance($arrInput) {
        if (!isset($arrInput['award_act_id']) || !isset($arrInput['chance_count']) ||
            !isset($arrInput['chance_type']) || !isset($arrInput['user_id'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
            }

        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
            'user_id'=>intval($arrInput['user_id']),
            'chance_count'=>intval($arrInput['chance_count']),
            'chance_type'=>$arrInput['chance_type'],
            'op_time'=>isset($arrInput['op_time'])?intval($arrInput['op_time']):time(),
        );

        $dlOut = Dl_Tycnt_Tycnt::addUserChanceRec($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal('add user chance record failed. input['.serialize($arrInput).']');
            return self::_errRet($dlOut['errno']);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );  
        return $arrOutput;
    }

    public static function getUserChance($arrInput) {
        if (!isset($arrInput['award_act_id']) || !isset($arrInput['user_id'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
            'user_id'=>intval($arrInput['user_id']),
            'rec_start_time'=>isset($arrInput['rec_start_time'])?intval($arrInput['rec_start_time']):0,
        );

        self::_printBasicNoticeLog($arrInput);

        $dlOut = Dl_Tycnt_Tycnt::getUserChanceRec($dlInput);

        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal("get user chance record failed. input[".serialize($arrInput).']');
            return self::_errRet($dlOut['errno']);
        }
        $chance_record = $dlOut['chance_record'];

        if (isset($arrInput['chance_default_count']) && 0 < $arrInput['chance_default_count']) {
            $chance_refresh = false;
            if (empty($chance_record)) {
                $chance_refresh = true;
            } else {
                $chance_refresh = true;
                foreach ($chance_record as $rec) {
                    // check have add chance?
                    if ($rec['chance_type']==self::CHANCE_TYPE_DEFAULT) {
                        $chance_refresh = false;
                        break;
                    }
                }
            }
            if ($chance_refresh) {
                $time = time();
                $dlInput['op_time'] = $time;
                $dlInput['chance_type'] = self::CHANCE_TYPE_DEFAULT;
                $dlInput['chance_count'] = intval($arrInput['chance_default_count']);
                $dlOut = Dl_Tycnt_Tycnt::addUserChanceRec($dlInput);
                if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::fatal("add user default chance failed. award_act_id[{$arrInput['award_act_id']}], user_id[{$arrInput['user_id']}]");
                    return self::_errRet($dlOut['errno']);
                }
                $chance_record []= array(
                    'op_time'=>$time,
                    'chance_type'=>self::CHANCE_TYPE_DEFAULT,
                    'chance_count'=>intval($arrInput['chance_default_count']),
                );
            }
        }

        $data = self::_calcRecChanceCnt($chance_record);
        $data['chance_record'] = $chance_record;

        //add by cuishichao start
        if (isset($arrInput['day_limit_count']))
        {
            $day_limit = intval($arrInput['day_limit_count']);
            $intLeftCount = self::_calcLeftCount($chance_record, $day_limit);
            $data['day_left_count'] = $intLeftCount;
        }
        //add by cuishichao end

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );  
        return $arrOutput;
    }

    public static function getUserAward($arrInput) {
        if (!isset($arrInput['award_act_id']) || !isset($arrInput['user_id'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
            'user_id'=>intval($arrInput['user_id']),
        );
        $dlOut = Dl_Base_Base::getUserAward($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal("get user award failed. award_act_id[{$arrInput['award_act_id']}] user_id[{$arrInput['user_id']}]");
            return self::_errRet($dlOut['errno']);
        }

        $user_award = $dlOut['user_award'];
        $arrAwardHash = array();
        foreach($user_award as &$ua) {
            $award_id = $ua['award_id'];
            if(isset($arrAwardHash[$award_id])) {
                $ua['award_info'] = $arrAwardHash[$award_id];
            } else {
                $dlInput = array(
                    'award_id' => $award_id,
                );
                $dlOut = Dl_Base_Base::getAward($dlInput);
                if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('get award info failed');
                } else {
                    $ua['award_info'] = $dlOut['award'];
                    $arrAwardHash[$award_id] = $dlOut['award'];
                }
            }
        }
        unset($ua);

        $data = array(
            'user_award'=>$user_award,
        );

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );  
        return $arrOutput;
    }

    public static function modifyUserInfo($arrInput) {
        if (!isset($arrInput['award_act_id']) || !isset($arrInput['lucky_id']) || 
            !isset($arrInput['user_id']) || !isset($arrInput['user_info'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
            }

        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
            'lucky_id'=>intval($arrInput['lucky_id']),
            'user_id'=>intval($arrInput['user_id']),
            'user_info'=>$arrInput['user_info'],
        );
        $dlOut = Dl_Base_Base::modifyUserInfo($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal("modify user award user info failed. award_act_id[{$arrInput['award_act_id']}] lucky_id[{$arrInput['lucky_id']}] user_id[{$arrInput['user_id']}]");
            return self::_errRet($dlOut['errno']);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }


    /**
     * @brief try to get a award directly.
     *
     * @param $arrInput
     *
     * @return 
     */
    public static function tryAward($arrInput) {
        $intRecordId = (int)$arrInput['record_id'];
        $intRecordType = (int)$arrInput['record_type'];
        $intUid = (int)$arrInput['user_id'];
        $intAwardActId = (int)$arrInput['award_act_id'];
        $intAwardId = (int)$arrInput['award_id'];
        $strFrom = isset($arrInput['from']) ? $arrInput['from'] : 'wise';
        $intCustomId = (int)$arrInput['custom_id'];

        $arrNoticeLogField = array(
            'user_id' => $intUid,
            'award_id' => $intAwardId,
            'award_act_id' => $intAwardActId,
            'record_id' => $intRecordId,
            'record_type' => $intRecordType,
            'from' => $strFrom,
            'custom_id' => $intCustomId,
        );

        self::_buildNoticeLog($arrNoticeLogField);

        if (0 >= $intUid || 0 >= $intAwardActId || 0 >= $intAwardId || 0 >= $intRecordId || 0 >= $intRecordType) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // get user award
        $dlInput = array(
            'award_act_id' => $intAwardActId,
            'user_id' => $intUid,
            'record_id' => $intRecordId,
            'record_type' => $intRecordType,
            'award_id' => $intAwardId,
        );
        $dlOut = Dl_Base_Base::getUserAwardByRecord($dlInput);
        if (0 !== (int)$dlOut['errno']) {
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        // judge if the record has exist.
        // 保持幂等性的策略
        if (!empty($dlOut['user_award'])) {
            $arrRet = array(
                'errno' => Tieba_Errcode::ERR_SUCCESS,
                'errmsg' => 'success',
                'data' => array(
                    'hit' => true,
                    'retry' => true,
                ),
            );
            return $arrRet;
        }

        $dlInput = array(
            'award_act_id' => $intAwardActId,
        );
        $strTimerKey = "Dl_tryAward::getAwardAct";
        Bingo_Timer::start($strTimerKey);
        $dlOut = Dl_Base_Base::getAwardAct($dlInput);
        Bingo_Timer::end($strTimerKey);
        if ($dlOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal("get award act info failed. award_act_id[{$arrInput['award_act_id']}]");
            return self::_errRet($dlOut['errno']);
        }
        $arrAwardActInfo = $dlOut['award_act_info'];

        $time = time();
        if ($arrAwardActInfo['end_time'] <= $time || $arrAwardActInfo['start_time'] >= $time) {
            Bingo_Log::warning("act time is error!");
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }

        //get award info
        $dlInput = array(
            'award_act_id' => $intAwardActId,
        );
        $strTimerKey = "Dl_tryAward::getAwardByAct";
        Bingo_Timer::start($strTimerKey);
        $dlOut = Dl_Base_Base::getAwardByAct($dlInput);
        Bingo_Timer::end($strTimerKey);
        if ($dlOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal("get award info failed. award_act_id[{$arrInput['award_act_id']}]");
            return self::_errRet($dlOut['errno']);
        }
        $arrAwardInfo = $dlOut['award_info'];

        // check if the award id belong to this.
        $arrCurAward = array();
        foreach($arrAwardInfo as $award) {
            $intTmpAwardId = (int)$award['award_id'];
            if ($intTmpAwardId === $intAwardId) {
                $arrCurAward = $award;
                break;
            }
        }

        if (empty($arrCurAward)) {
            Bingo_Log::warning(sprintf('fail to find proper award, award_act_id[%d], award_id[%d]', $intAwardActId, $intAwardId));
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }

        $intAwardType = (int)$arrCurAward['award_type'];

        // get award cur count.
        $dlInput = array(
            'award_ids' => array(
                $intAwardId,
            ),
        );
        $strTimerKey = "Dl_tryAward::mgetAwardCurCnt";
        Bingo_Timer::start($strTimerKey);
        $dlOut = Dl_Tycnt_Tycnt::mgetAwardCurCnt($dlInput);
        Bingo_Timer::end($strTimerKey);
        if (false === $dlOut || Tieba_Errcode::ERR_SUCCESS !== $dlOut['errno']) {
            Bingo_Log::warning("fail to get award_cur_count, award_id[$intAwardId]");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $intAwardCurCount = (int)$dlOut['award_cur_counts'][$intAwardId];
        if (0 >= $intAwardCurCount) {
            Bingo_Log::warning("award current count is not enough, award_id[$intAwardId]");
            Bingo_Log::pushNotice('award_not_enough', $intAwardId);
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }

        // check the transaction
        $arrAddRecordInput = array(
            'record_id' => $intRecordId,
            'user_id' => $intUid,
        );
        $arrAddRecordOutput = Dl_Base_Base::addRecord($arrAddRecordInput);
        if (0 === (int)$arrAddRecordOutput['errno']) {
            //继续执行剩余的逻辑
        } elseif (self::DB_DUPLICATE === (int)$arrAddRecordOutput['errno']) {
            Bingo_Log::pushNotice('retry', 1);
            $arrRet = array(
                'errno' => Tieba_Errcode::ERR_SUCCESS,
                'errmsg' => 'success',
                'data' => array(
                    'hit' => true,
                    'retry' => true,
                ),
            );
            return $arrRet;
        } else {
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrRet = array();
        if (self::AWARD_TYPE_EXKEY === $intAwardType) {
            $exkey_act_id = (int)$arrCurAward['ext_info']['exkey_act_id'];
            $exkeyInput = array(
                'exkey_act_id' => $exkey_act_id,
                'user_id' => $user_id,
            );
            $strTimerKey = "tryAward::obtainExKey";
            Bingo_Timer::start($strTimerKey);
            $arrExkeyOut = Tieba_Service::call('exkey', 'obtainExKey', $exkeyInput);
            Bingo_Timer::end($strTimerKey);
            $intRalNo = ral_get_errno();
            if (false === $arrExkeyOut) {
                Bingo_Log::warning("fail to obtainExKey, ral_no[$intRalNo]");
                return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
            }
            $arrRet['key'] = $arrExkeyOut['data']['key'];
        }

        // add user award.
        $dlInput = array(
            'award_id' => $intAwardId,
            'award_count' => -1,
        );
        $strTimerKey = "tryAward::addAwardCurCnt";
        Bingo_Timer::start($strTimerKey);
        $dlOut = Dl_Tycnt_Tycnt::addAwardCurCnt($dlInput);
        Bingo_Timer::end($strTimerKey);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal('decr award current count failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
            return false;
        }

        $strUname = self::_getUnameByUid($intUid);
        if (false === $strUname) {
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }

        // add user award
        $dlInput = array(
            'user_name' => $strUname,
            'user_id' => $intUid,
            'award_act_id' => $intAwardActId,
            'award_id' => $intAwardId,
            'record_id' => $intRecordId,
            'record_type' => $intRecordType,
            'award_time' => $time,
        );
        if (isset($arrRet['key'])) {
            $dlInput['award_desc']['key'] = $arrRet['key'];
        }
        $strTimerKey = "tryAward::addUserAwardByRecord";
        Bingo_Timer::start($strTimerKey);
        $dlOut = Dl_Base_Base::addUserAwardByRecord($dlInput);
        if (0 !== (int)$dlOut['errno']) {
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        Bingo_Timer::end($strTimerKey);
        $lucky_id = intval($dlOut['lucky_id']);
        $arrRet['lucky_id'] = $lucky_id;
        if ($dlOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal('add user award failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
            return false;
        }

        // add recent award.
        $dlInput = array(
            'user_id' => $intUid,
            'award_act_id' => $intAwardActId,
            'award' => array(
                'user_name'     => $strUname,
                'lucky_id'      => $lucky_id,
                'award_time'    => $time,
                'award_name'    => $arrCurAward['award_name'],
                'award_id'      => $arrCurAward['award_id'],
                'record_id'     => $intRecordId,
                'award_type'    => $arrCurAward['award_type'],
            ),
        );
        if (0 < $intCustomId) {
            $dlInput['award']['custom_id'] = $intCustomId;
        }
        $strTimerKey = "tryAward::addRecentAwardRec";
        Bingo_Timer::start($strTimerKey);
        $dlOut = Dl_Tycnt_Tycnt::addRecentAwardRec($dlInput);
        Bingo_Timer::end($strTimerKey);
        if ($dlOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('add recent award record failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
        }
        $arrRet['hit'] = true;
        $arrRet['user_award'] = $arrCurAward;
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data'=> $arrRet,
        );
        return $arrOutput;
    }

    /**
     * @brief core tryLuck.
     *
     * @param $arrInput
     *
     * @return 
     */
    public static function tryLuck($arrInput) {
        if (!isset($arrInput['award_act_id']) || !isset($arrInput['user_id'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        // from pc or wise
        $strFrom = isset($arrInput['from']) ? $arrInput['from'] : 'wise';

        /*
         * add some notice log.
         */
        Bingo_Log::pushNotice('from', $strFrom);
        Bingo_Log::pushNotice('award_user_id', $arrInput['user_id']);
        Bingo_Log::pushNotice('award_act_id', $arrInput['award_act_id']);

        $arrAppendRule = array();
        if (isset($arrInput['append_rule'])) {
            $arrAppendRule = $arrInput['append_rule'];
        }

        // check if the act has ended
        $dlInput = array(
            'award_act_id' => (int)$arrInput['award_act_id'],
        );

        $strTimerKey = "Dl_tryLuck::getAwardAct";
        Bingo_Timer::start($strTimerKey);
        $dlOut = Dl_Base_Base::getAwardAct($arrInput);
        Bingo_Timer::end($strTimerKey);
        if ($dlOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal("get award act info failed. award_act_id[{$arrInput['award_act_id']}]");
            return self::_errRet($dlOut['errno']);
        }

        $time = time();
        $award_act_info = $dlOut['award_act_info'];
        if ($award_act_info['end_time'] <= $time || $award_act_info['start_time'] >= $time) {
            Bingo_Log::warning("award act time check failed! start time is {$award_act_info['start_time']}, and end time is {$award_act_info['end_time']}!");
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }

        // 判断related act type, 拒绝tryAward活动的非法请求
        $intRelatedActType = (int)$award_act_info['related_act_type'];
        if (self::ACT_TYPE_TRYAWARD === $intRelatedActType) {
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }

        // get current user chance
        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
            'user_id'=>intval($arrInput['user_id']),
            'rec_start_time'=>intval($arrInput['chance_expire_time']),
        );
        $strTimerKey = "Dl_tryLuck::getUserChanceRec";
        Bingo_Timer::start($strTimerKey);
        $dlOut = Dl_Tycnt_Tycnt::getUserChanceRec($dlInput);
        Bingo_Timer::end($strTimerKey);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal("get user chance record failed. award_act_id[{$arrInput['award_act_id']}], user_id[{$arrInput['user_id']}]");
            return self::_errRet($dlOut['errno']);
        }
        $chance_record = $dlOut['chance_record'];

        if (isset($arrInput['chance_expire_time']) && isset($arrInput['chance_default_count'])) {
            $chance_refresh = false;
            if (empty($chance_record)) {
                $chance_refresh = true;
            } else {
                $chance_refresh = true;
                foreach ($chance_record as $rec) {
                    if ($rec['chance_type']==self::CHANCE_TYPE_DEFAULT) {
                        $chance_refresh = false;
                        break;
                    }
                }
            }
            if ($chance_refresh) {
                $dlInput['op_time'] = $time;
                $dlInput['chance_type'] = self::CHANCE_TYPE_DEFAULT;
                $dlInput['chance_count'] = intval($arrInput['chance_default_count']);
                $strTimerKey = "Dl_tryLuck::addUserChanceRec_init";
                Bingo_Timer::start($strTimerKey);
                $dlOut = Dl_Tycnt_Tycnt::addUserChanceRec($dlInput);
                Bingo_Timer::end($strTimerKey);
                if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::fatal("add user default chance failed. award_act_id[{$award_act_id}] user_id[{$user_id}]");
                    return self::_errRet($dlOut['errno']);
                }
                // init success, add to array.
                $chance_record = array(
                    array(
                        'chance_count' => (int)$arrInput['chance_default_count'],
                    ),
                );
            }
        }

        $chance = self::_calcRecChanceCnt($chance_record);
        if ($chance['chance_count'] <= 0) {
            Bingo_Log::pushNotice('no_chance', 1);
            return self::_errRet(Tieba_Errcode::ERR_TYCHE_CHANCE_NOT_ENOUGH);
        }
        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
            'user_id'=>intval($arrInput['user_id']),
            'chance_count' => -1,
            'chance_type' => self::CHANCE_TYPE_TRY,
            'op_time'=>$time,
        );
        $strTimerKey = "Dl_tryLuck::addUserChanceRec_use";
        Bingo_Timer::start($strTimerKey);
        $dlOut = Dl_Tycnt_Tycnt::addUserChanceRec($dlInput);
        Bingo_Timer::end($strTimerKey);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('add user chance record failed. input['.serialize($arrInput).']');
        }

        $data = array('hit'=>false);

        // check if forced to no award
        if ($arrInput['no_award'] == 1) {
            Bingo_Log::pushNotice('no_award', 1);
            $error = Tieba_Errcode::ERR_SUCCESS;
            $arrOutput = array(
                'errno' => $error,
                'errmsg' => Tieba_Error::getErrmsg($error),
                'data'=>$data,
            );  
            return $arrOutput;
        }
        Bingo_Log::pushNotice('no_award', 0);

        // 豁免魅族单case, silly
        if(self::MEIZU_CASE_AWARD_ACT_ID === (int)$arrInput['award_act_id']) {
            $award_act_info['can_dup'] = 1;
        }

        // check if user has got award
        if ($award_act_info['can_dup']==0) {
            $has_award = false;
            $dlInput = array(
                'award_act_id'=>intval($arrInput['award_act_id']),
                'user_id'=>intval($arrInput['user_id']),
            );
            $strTimerKey = "Dl_tryLuck::getUserAward";
            Bingo_Timer::start($strTimerKey);
            $dlOut = Dl_Base_Base::getUserAward($dlInput);
            Bingo_Timer::end($strTimerKey);
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('get user award failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
                $has_award = true;
            } elseif (!empty($dlOut['user_award'])) {
                $has_award = true;
            }
            if ($has_award) {
                $error = Tieba_Errcode::ERR_SUCCESS;
                $arrOutput = array(
                    'errno' => $error,
                    'errmsg' => Tieba_Error::getErrmsg($error),
                    'data'=>$data,
                );  
                return $arrOutput;
            }
        }

        // get award info
        $strTimerKey = "Dl_tryLuck::getAwardByAct";
        Bingo_Timer::start($strTimerKey);
        $dlOut = Dl_Base_Base::getAwardByAct($arrInput);
        Bingo_Timer::end($strTimerKey);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal("get award info failed. award_act_id[{$arrInput['award_act_id']}]");
            return self::_errRet($dlOut['errno']);
        }
        $award_info = $dlOut['award_info'];

        $hit = self::_getAwardHit($award_info, intval($arrInput['user_id']), $time, $award_act_info, $arrAppendRule);
        if ($hit) {
            Bingo_Log::pushNotice('is_hit', 1);
            $data['hit'] = true;
            $data['user_award'] = array($hit['awardHit']);
            if ($hit['awardHit']['award_type']==self::AWARD_TYPE_EXKEY) {
                $data['key'] = $hit['key'];
            }
        } else {
            Bingo_Log::pushNotice('is_hit', 0);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data'=>$data,
        );  
        return $arrOutput;
    }

    /**
     * desc
     * @param unknown_type $arrInput
     * @return errno
     */   
    private static function _getAwardHit($award_info, $user_id, $time, $award_act_info, $arrAppendRule) {
        $res = array();

        $awardHit = Util_Calc::calcAward($award_info);

        if (isset($awardHit['ext_info']['level_limit'])) {
            $intLimitLevel = intval($awardHit['ext_info']['level_limit']);
            $intUserLevel  = intval($arrAppendRule['user_level_id']);
            if ($intUserLevel < $intLimitLevel)
            {
                Bingo_Log::warning(sprintf("user leval is not enough, user[%d]:limit[%d]", $intUserLevel, $intLimitAwardId));
                return false;
            }
        }
        if (!empty($awardHit)) {
            //check award cur count
            $dlInput = array(
                'award_ids'=>array($awardHit['award_id']),
            );
            $strTimerKey = "Dl_tryLuck::mgetAwardCurCnt";
            Bingo_Timer::start($strTimerKey);
            $dlOut = Dl_Tycnt_Tycnt::mgetAwardCurCnt($dlInput);
            Bingo_Timer::end($strTimerKey);
            Bingo_Log::pushNotice("hit_award_id", $awardHit['award_id']);
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('get current award count failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
                return false;
            } elseif ($dlOut['award_cur_counts'][$awardHit['award_id']] <= 0) {
                Bingo_Log::warning('award not enough. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
                return false;
            }

            //filter the award_rule
            $bolIsReallyHit = self::_checkAwardRule($award_act_info, $award_info, $user_id, $awardHit['award_id'], $time);
            if (!$bolIsReallyHit) {
                return false;
            }

            $dlInput = array(
                'award_id'=>$awardHit['award_id'],
                'award_count'=>-1,
                'award_act_id'=>$awardHit['award_act_id'],
                'user_id'=>$user_id,
                'award_time'=>$time,
            );
            if ($awardHit['award_type']==self::AWARD_TYPE_EXKEY) {
                //obtain one exkey
                $exkeyInput = array(
                    'exkey_act_id'=>$awardHit['ext_info']['exkey_act_id'],
                    'user_id'=>$user_id,
                );
                $strTimerKey = "tryLuck::obtainExKey";
                Bingo_Timer::start($strTimerKey);
                $exkeyOut = Tieba_Service::call('exkey', 'obtainExKey', $exkeyInput);
                Bingo_Timer::end($strTimerKey);
                if ($exkeyOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('get exkey failed. input['.serialize($exkeyInput).'] output['.serialize($exkeyOut).']');
                    return false;
                }
                $res['key'] = $dlInput['award_desc']['key'] = $exkeyOut['data']['key'];
            }
            $strTimerKey = "tryLuck::addAwardCurCnt";
            Bingo_Timer::start($strTimerKey);
            $dlOut = Dl_Tycnt_Tycnt::addAwardCurCnt($dlInput);
            Bingo_Timer::end($strTimerKey);
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::fatal('decr award current count failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
                return false;
            }

            // get user name
            $arrGetUnameInput = array(
                'user_id' => array(
                    $user_id,
                ),
            );

            Bingo_Timer::start("tryLuck::getUnameByUids");
            $arrGetUnameOutput = Tieba_Service::call('user', 'getUnameByUids', $arrGetUnameInput, null, null, 'post', 'php', 'gbk');
            Bingo_Timer::end("tryLuck::getUnameByUids");
            if (false === $arrGetUnameOutput) {
                Bingo_Log::fatal("get user name failed, user_id[$user_id]");
            }
            $user_name = $arrGetUnameOutput['output']['unames'][0]['user_name'];
            $dlInput['user_name'] = $user_name;

            $strTimerKey = "tryLuck::addUserAward";
            Bingo_Timer::start($strTimerKey);
            $dlOut = Dl_Base_Base::addUserAward($dlInput);
            Bingo_Timer::end($strTimerKey);
            $lucky_id = intval($dlOut['lucky_id']);
            $awardHit['lucky_id'] = $lucky_id;
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::fatal('add user award failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
                return false;
            }

            /* 以下的逻辑，都可以时候处理 */
            // TODO, 准备注释掉以下的逻辑
            /*
            // only set useful data, or the redis save too much
            $dlInput = array(
                'user_id' => $user_id,
                'award_act_id' => $awardHit['award_act_id'],
                'award' => array(
                    'user_name'     => $user_name,
                    'lucky_id'      => $lucky_id,
                    'award_time'    => $time,
                    'award_name'    => $awardHit['award_name'],
                    'award_id'      => $awardHit['award_id'],
                ),
            );
            $strTimerKey = "tryLuck::addRecentAwardRec";
            Bingo_Timer::start($strTimerKey);
            $dlOut = Dl_Tycnt_Tycnt::addRecentAwardRec($dlInput);
            Bingo_Timer::end($strTimerKey);
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('add recent award record failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
            }
            */
            // TODO, 注释上面的东西

            // commit nmq
            $arrNmqCommitInput = array(
                'user_id' => $user_id,
                'user_name' => $user_name,
                'award_hit' => $awardHit,
            );
            $intNmqRet = self::_commitNmq($arrNmqCommitInput);
            $res['awardHit'] = $awardHit;
            return $res;
        } else {
            return false;
        }
    }

    /**
     * Dump all Awards, given the award_act_id
     * <AUTHOR>
     * @param  int $arrInput['award_act_id']
     * @param  int $arrInput['offset']
     * @return array  errno,errmsg,data
     */
    public static function dumpAllUserExkeyAward($arrInput){
        $arrInput['award_type'] = self::AWARD_TYPE_EXKEY;
        return self::dumpAllUserAward($arrInput);
    }
    /**
     * Dump all Awards, given the award_act_id
     * <AUTHOR>
     * @param  int $arrInput['award_act_id']
     * @param  int $arrInput['offset']
     * @return array  errno,errmsg,data
     */
    public static function dumpAllUserRealAward($arrInput) {
        $arrInput['award_type'] = self::AWARD_TYPE_REAL;
        return self::dumpAllUserAward($arrInput);
    }
    /**
     * Dump all Awards, given the award_act_id
     * <AUTHOR>
     * @param  int $arrInput['award_act_id']
     * @param  int $arrInput['offset']
     * @return array  errno,errmsg,data
     */
    public static function dumpAllUserAward($arrInput){
        if (!isset($arrInput['award_act_id']) || !isset($arrInput['offset'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        if (!isset($arrInput['count'])) {
            $arrInput['count'] = self::DEFAULT_DUMP_COUNT;
        }
        $arrInput['count'] = min(intval($arrInput['count']), self::MAX_DUMP_COUNT);

        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
            'offset'=>intval($arrInput['offset']),
            'count'=>intval($arrInput['count']),
        );
        if(isset($arrInput['award_type'])){
            $dlInput['award_type'] = intval($arrInput['award_type']);
        }
        $strTimerKey = "Dl_getAllUserAwards";
        Bingo_Timer::start($strTimerKey);
        $dlOut = Dl_Base_Base::getAllUserAwards($dlInput);
        Bingo_Timer::end($strTimerKey);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal('dump user award failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
            return self::_errRet($dlOut['errno']);
        }

        $data = array(
            'all_user_awards'=>$dlOut['all_user_awards'],
        );

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data'=>$data,
        );  
        return $arrOutput;
    }

    //////////////////////////////add by cuishichao//////////////////////////////
    public static function tryLuckAccordingLimit($arrInput) {
        if (!isset($arrInput['award_act_id']) || !isset($arrInput['user_id'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        //get limit
        $arrLimit = isset($arrInput['limit'])?$arrInput['limit']:array();
        //check if the act has ended
        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
        );
        $dlOut = Dl_Base_Base::getAwardAct($arrInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal("get award act info failed. award_act_id[{$arrInput['award_act_id']}]");
            return self::_errRet($dlOut['errno']);
        }

        $time = time();
        $award_act_info = $dlOut['award_act_info'];
        if ($award_act_info['end_time'] <= $time) {
            Bingo_Log::warning("award act has ended. award_act_id[{$arrInput['award_act_id']}]");
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }
        //add by yunting
        $partInput = array('award_act_id' => intval($arrInput['award_act_id']), 'partake_ratio' => intval($award_act_info['ext_info']['partake_ratio']));
        $partakeRes = Dl_Tycnt_Tycnt::addActPartake($partInput);
        if ($partakeRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("get award info failed. award_act_id[{$arrInput['award_act_id']}]");
        }
        //get current user chance
        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
            'user_id'=>intval($arrInput['user_id']),
            'rec_start_time'=>intval($arrInput['chance_expire_time']),
        );
        $dlOut = Dl_Tycnt_Tycnt::getUserChanceRec($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal("get user chance record failed. award_act_id[{$award_act_id}] user_id[{$user_id}]");
            return self::_errRet($dlOut['errno']);
        }
        $chance_record = $dlOut['chance_record'];

        if (isset($arrInput['chance_expire_time']) && isset($arrInput['chance_default_count'])) {
            $chance_refresh = false;
            if (empty($chance_record)) {
                $chance_refresh = true;
            } else {
                $chance_refresh = true;
                foreach ($chance_record as $rec) {
                    if ($rec['chance_type']==self::CHANCE_TYPE_DEFAULT) {
                        $chance_refresh = false;
                        break;
                    }
                }
            }
            if ($chance_refresh) {
                $dlInput['op_time'] = $time;
                $dlInput['chance_type'] = self::CHANCE_TYPE_DEFAULT;
                $dlInput['chance_count'] = intval($arrInput['chance_default_count']);
                $dlOut = Dl_Tycnt_Tycnt::addUserChanceRec($dlInput);
                if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::fatal("add user default chance failed. award_act_id[{$award_act_id}] user_id[{$user_id}]");
                    return self::_errRet($dlOut['errno']);
                }
            }
        }

        $chance = self::_calcRecChanceCnt($chance_record);
        if ($chance['chance_count'] <= 0) {
            Bingo_Log::warning('user has no chance. input['.serialize($arrInput).'] chance_record['.serialize($chance_record).']');
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }
        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
            'user_id'=>intval($arrInput['user_id']),
            'chance_count' => -1,
            'chance_type' => self::CHANCE_TYPE_TRY,
            'op_time'=>$time,
        );
        $dlOut = Dl_Tycnt_Tycnt::addUserChanceRec($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('add user chance record failed. input['.serialize($arrInput).']');
        }

        $data = array('hit'=>false);

        //check if user has got award
        /*
        if ($award_act_info['can_dup']==0) {
            $has_award = false;
            $dlInput = array(
                'award_act_id'=>intval($arrInput['award_act_id']),
                'user_id'=>intval($arrInput['user_id']),
            );
            $dlOut = Dl_Base_Base::getUserAward($dlInput);
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('get user award failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
                $has_award = true;
            } elseif (!empty($dlOut['user_award'])) {
                $has_award = true;
            }
            if ($has_award) {
                $error = Tieba_Errcode::ERR_SUCCESS;
                $arrOutput = array(
                    'errno' => $error,
                    'errmsg' => Tieba_Error::getErrmsg($error),
                    'data'=>$data,
                );  
                return $arrOutput;
            }
        }
         */
        //check if forced to no award
        if ($arrInput['no_award'] == 1) {
            $error = Tieba_Errcode::ERR_SUCCESS;
            $arrOutput = array(
                'errno' => $error,
                'errmsg' => Tieba_Error::getErrmsg($error),
                'data'=>$data,
            );  
            return $arrOutput;
        }

        //get award info
        $dlOut = Dl_Base_Base::getAwardByAct($arrInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal("get award info failed. award_act_id[{$arrInput['award_act_id']}]");
            return self::_errRet($dlOut['errno']);
        }
        $award_info = $dlOut['award_info'];

        $hit = self::_getAwardHitAccordingLimit($award_info, intval($arrInput['user_id']), $time, $arrLimit);
        if ($hit) {
            $data['hit'] = true;
            $data['user_award'] = array($hit['awardHit']);
            if ($hit['awardHit']['award_type']==self::AWARD_TYPE_EXKEY) {
                $data['key'] = $hit['key'];
            }
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data'=>$data,
        );  
        return $arrOutput;
    }

    private static function _getAwardHitAccordingLimit($award_info, $user_id, $time, $arrLimit) {
        $res = array();

        $awardHit = Util_Calc::calcAward($award_info);

        if (!empty($awardHit)) {
            //check award cur count
            $dlInput = array(
                'award_ids'=>array($awardHit['award_id']),
            );
            //check limit 
            $intUserId  = $user_id;
            $intActId   = intval($awardHit['award_act_id']);
            $intHitType = intval($awardHit['award_type']);
            //add  by cuishichao trick 2014-11-11涓嬫帀锛�            
            $objRedis = new Bingo_Cache_Redis('platform');
            if (!$objRedis || !$objRedis->isEnable()) {
                Bingo_Log::warning('init redis failed');
                return false;
            }
            $intLimitAwardId = intval($awardHit['award_id']);
            $arrRedisInput = array(
                'key' => 'jingdong_dalibao_toy_award',
                'member' => $intLimitAwardId,
            );
            $arrRes = $objRedis->SISMEMBER($arrRedisInput);
            if ($arrRes['err_no'] !== 0) {
                Bingo_Log::warning("call redis error! ".serialize($arrRes).'_'.$arrRedisInput);
                return false;
            }
            if (intval($arrRes['ret']['jingdong_dalibao_toy_award']) !== 1) {
                $intLimitAwardId = 0; //
            }              

            //add by cuishichao end
            if (self::_checkIsLimit($intUserId, $intActId, $intHitType, $arrLimit, $intLimitAwardId)) {
                Bingo_Log::warning("user award reach limit! $intUserId, $intActId, $intHitType, $intLimitAwardId");
                return false;
            }
            $dlOut = Dl_Tycnt_Tycnt::mgetAwardCurCnt($dlInput);
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('get current award count failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
                return false;
            } elseif ($dlOut['award_cur_counts'][$awardHit['award_id']] <= 0) {
                Bingo_Log::warning('award not enough. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
                return false;
            }

            $dlInput = array(
                'award_id'=>$awardHit['award_id'],
                'award_count'=>-1,
                'award_act_id'=>$awardHit['award_act_id'],
                'user_id'=>$user_id,
                'award_time'=>$time,
            );
            if ($awardHit['award_type']==self::AWARD_TYPE_EXKEY) {
                //obtain one exkey
                $exkeyInput = array(
                    'exkey_act_id'=>$awardHit['ext_info']['exkey_act_id'],
                    'user_id'=>$user_id,
                );
                $exkeyOut = Tieba_Service::call('exkey', 'obtainExKey', $exkeyInput);
                if ($exkeyOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('get exkey failed. input['.serialize($exkeyInput).'] output['.serialize($exkeyOut).']');
                    return false;
                }
                $res['key'] = $dlInput['award_desc']['key'] = $exkeyOut['data']['key'];
            }
            $dlOut = Dl_Tycnt_Tycnt::addAwardCurCnt($dlInput);
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::fatal('decr award current count failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
                return false;
            }

            //get user name
            $arrGetUnameInput = array(
                'user_id' => array(
                    $user_id,
                ),
            );

            Bingo_Timer::start("tryLuck::getUnameByUids");
            $arrGetUnameOutput = Tieba_Service::call('user', 'getUnameByUids', $arrGetUnameInput, null, null, 'post', 'php', 'gbk');
            Bingo_Timer::end("tryLuck::getUnameByUids");
            if (false === $arrGetUnameOutput) {
                Bingo_Log::fatal("get user name failed, user_id[$user_id]");
            }
            $user_name = $arrGetUnameOutput['output']['unames'][0]['user_name'];
            $dlInput['user_name'] = $user_name;

            $dlOut = Dl_Base_Base::addUserAward($dlInput);
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::fatal('add user award failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
                return false;
            }
            //add limit
            if (!self::_addLimit($intUserId, $intActId, $intHitType, $arrLimit, $intLimitAwardId)){
                Bingo_Log::warning("add limit error!  $intUserId, $intActId, $intHitType, $intLimitAwardId");
            }
            $dlInput['award'] = $awardHit;
            $dlOut = Dl_Tycnt_Tycnt::addRecentAwardRec($dlInput);
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('add recent award record failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
            }

            $res['awardHit'] = $awardHit;
            return $res;
        } else {
            return false;
        }
    }

    private static function _checkIsLimit($intUserId, $intActId, $intHitType, $arrLimit, $intLimitAwardId) {
        $intRealAct = isset($arrLimit['is_only_once_real'])?intval($arrLimit['is_only_once_real']):0;
        $intVirtual = isset($arrLimit['is_only_once_virtual'])?intval($arrLimit['is_only_once_virtual']):0;
        $arrInput = array(
            'act_id' => $intActId,
            'user_id'  => $intUserId,
            'award_type' => $intHitType,
        );
        $bolIsLimit = true;
        if ($intLimitAwardId !== 0) {//鏍规嵁濂栧搧鏉ラ檺鍒讹紝姣忎釜浜哄彧鑳戒腑涓�釜杩欎釜濂栧搧
            $arrInput['award_id'] = $intLimitAwardId;
            $arrRes = Dl_Tycnt_Tycnt::checkUserAwardTypeExist($arrInput);
            if ($arrRes['errno'] === 0) {
                return false;
            } 
            return true;
        }        
        if ($intHitType== self::AWARD_TYPE_EXKEY && $intVirtual === 1) {
            $arrInput['day'] = date("Y-m-d");
            $arrRes = Dl_Tycnt_Tycnt::checkUserAwardTypeExist($arrInput);
            if ($arrRes['errno'] === 0) {
                $bolIsLimit = false;
            }
        } elseif ($intHitType == self::AWARD_TYPE_REAL && $intRealAct === 1) {
            $arrRes = Dl_Tycnt_Tycnt::checkUserAwardTypeExist($arrInput);
            if ($arrRes['errno'] === 0) {
                $bolIsLimit = false;
            }
        } else {
            $bolIsLimit = false;
        }
        return $bolIsLimit;
    }

    private static function _addLimit($intUserId, $intActId, $intHitType, $arrLimit, $intLimitAwardId) {
        $intRealAct = isset($arrLimit['is_only_once_real'])?intval($arrLimit['is_only_once_real']):0;
        $intVirtual = isset($arrLimit['is_only_once_virtual'])?intval($arrLimit['is_only_once_virtual']):0;
        $arrInput = array(
            'act_id' => $intActId,
            'user_id'  => $intUserId,
            'award_type' => $intHitType,
        );
        if ($intLimitAwardId !== 0) {//鏍规嵁濂栧搧鏉ラ檺鍒讹紝姣忎釜浜哄彧鑳戒腑涓�釜杩欎釜濂栧搧
            $arrInput['award_id'] = $intLimitAwardId;
            $arrRes = Dl_Tycnt_Tycnt::setUserAwardType($arrInput);
            return true;
        }
        if ($intHitType== self::AWARD_TYPE_EXKEY && $intVirtual === 1) {
            $arrInput['day'] = date("Y-m-d");
            $arrRes = Dl_Tycnt_Tycnt::setUserAwardType($arrInput);
        } elseif ($intHitType == self::AWARD_TYPE_REAL && $intRealAct === 1) {
            $arrRes = Dl_Tycnt_Tycnt::setUserAwardType($arrInput);
        }
        return true;
    }

    //娓呮椿鍔╟ache
    public static function delAwardActCache($arrInput) {
        if (!isset($arrInput['award_act_id'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        $dlInput['award_act_id'] = intval($arrInput['award_act_id']);
        $dlOut = Dl_Base_Base::delAwardActCache($dlInput);
        if ($dlOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('remove award act cache fail. input['.serialize($arrInput).']');
            return self::_errRet($dlOut['errno']);
        }
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;  
    }

    public static function tryLuckByLimit($arrInput) {
        if (!isset($arrInput['award_act_id']) || !isset($arrInput['user_id'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //check if the act has ended
        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
        );
        $dlOut = Dl_Base_Base::getAwardAct($arrInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal("get award act info failed. award_act_id[{$arrInput['award_act_id']}]");
            return self::_errRet($dlOut['errno']);
        }

        $time = time();
        $award_act_info = $dlOut['award_act_info'];
        if ($award_act_info['end_time'] <= $time) {
            Bingo_Log::warning("award act has ended. award_act_id[{$arrInput['award_act_id']}]");
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }
        //add by yunting
        $partInput = array('award_act_id' => intval($arrInput['award_act_id']), 'partake_ratio' => intval($award_act_info['ext_info']['partake_ratio']));
        $partakeRes = Dl_Tycnt_Tycnt::addActPartake($partInput);
        if ($partakeRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("get award info failed. award_act_id[{$arrInput['award_act_id']}]");
        }
        //get current user chance
        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
            'user_id'=>intval($arrInput['user_id']),
            'rec_start_time'=>intval($arrInput['chance_expire_time']),
        );
        $dlOut = Dl_Tycnt_Tycnt::getUserChanceRec($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal("get user chance record failed. award_act_id[{$award_act_id}] user_id[{$user_id}]");
            return self::_errRet($dlOut['errno']);
        }
        $chance_record = $dlOut['chance_record'];

        if (isset($arrInput['chance_expire_time']) && isset($arrInput['chance_default_count'])) {
            $chance_refresh = false;
            if (empty($chance_record)) {
                $chance_refresh = true;
            } else {
                $chance_refresh = true;
                foreach ($chance_record as $rec) {
                    if ($rec['chance_type']==self::CHANCE_TYPE_DEFAULT) {
                        $chance_refresh = false;
                        break;
                    }
                }
            }
            if ($chance_refresh) {
                $dlInput['op_time'] = $time;
                $dlInput['chance_type'] = self::CHANCE_TYPE_DEFAULT;
                $dlInput['chance_count'] = intval($arrInput['chance_default_count']);
                $dlOut = Dl_Tycnt_Tycnt::addUserChanceRec($dlInput);
                if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::fatal("add user default chance failed. award_act_id[{$award_act_id}] user_id[{$user_id}]");
                    return self::_errRet($dlOut['errno']);
                }
            }
        }

        $chance = self::_calcRecChanceCnt($chance_record);
        if ($chance['chance_count'] <= 0) {
            Bingo_Log::warning('user has no chance. input['.serialize($arrInput).'] chance_record['.serialize($chance_record).']');
            return self::_errRet(Tieba_Errcode::ERR_TYCHE_CHANCE_NOT_ENOUGH);
        }
        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
            'user_id'=>intval($arrInput['user_id']),
            'chance_count' => -1,
            'chance_type' => self::CHANCE_TYPE_TRY,
            'op_time'=>$time,
        );
        $dlOut = Dl_Tycnt_Tycnt::addUserChanceRec($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('add user chance record failed. input['.serialize($arrInput).']');
        }

        $data = array('hit'=>false);
        /*
        //check if user has got award
        if ($award_act_info['can_dup']==0) {
            $has_award = false;
            $dlInput = array(
                    'award_act_id'=>intval($arrInput['award_act_id']),
                    'user_id'=>intval($arrInput['user_id']),
            );
            $dlOut = Dl_Base_Base::getUserAward($dlInput);
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('get user award failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
                $has_award = true;
            } elseif (!empty($dlOut['user_award'])) {
                $has_award = true;
            }
            if ($has_award) {
                $error = Tieba_Errcode::ERR_SUCCESS;
                $arrOutput = array(
                        'errno' => $error,
                        'errmsg' => Tieba_Error::getErrmsg($error),
                        'data'=>$data,
                );
                return $arrOutput;
            }
        }
         */
        //check if forced to no award
        if ($arrInput['no_award'] == 1) {
            $error = Tieba_Errcode::ERR_SUCCESS;
            $arrOutput = array(
                'errno' => $error,
                'errmsg' => Tieba_Error::getErrmsg($error),
                'data'=>$data,
            );
            return $arrOutput;
        }

        //get award info
        $dlOut = Dl_Base_Base::getAwardByAct($arrInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal("get award info failed. award_act_id[{$arrInput['award_act_id']}]");
            return self::_errRet($dlOut['errno']);
        }
        $award_info = $dlOut['award_info'];

        $hit = self::_getAwardHitByLimit($award_info, $arrInput, $time);
        if ($hit) {
            $data['hit'] = true;
            $data['user_award'] = array($hit['awardHit']);
            if ($hit['awardHit']['award_type']==self::AWARD_TYPE_EXKEY) {
                $data['key'] = $hit['key'];
            }
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data'=>$data,
        );
        return $arrOutput;

    }
    private static function _getAwardHitByLimit($award_info, $arrInput, $time) {
        $res = array();
        $user_id = intval($arrInput['user_id']);
        $awardHit = Util_Calc::calcAward($award_info);

        if (!empty($awardHit)) {

            //check limit add by zhengchengguo

            $arrAwardLimit = array(
                //award_id => numLimit
                '106226'=>1,
                '106227'=>1,
                '106228'=>1,
                '106229'=>1,
                '106230'=>2,
            );
            $awardList = array(
                //award_id => num
                '106226'=>0,
                '106227'=>0,
                '106228'=>0,
                '106229'=>0,
                '106230'=>0,
            );
            $input = array(
                'award_act_id'=>intval($arrInput['award_act_id']),
                'user_id'=>intval($arrInput['user_id']),
            );
            $arrRet = self::getUserAward($input);
            if ($arrRet['errno']!==Tieba_Errcode::ERR_SUCCESS)
            {
                return false;
            }
            if (!empty($arrRet['data']['user_award']))
            {
                foreach ($arrRet['data']['user_award'] as $award)
                {
                    if (isset($awardList[$award['award_id']]) && $award['lucky_id']>0)
                    {
                        $awardList[$award['award_id']]++;
                    }
                }
                if (isset($awardList[$awardHit['award_id']]) && $awardList[$awardHit['award_id']]>=$arrAwardLimit[$awardHit['award_id']])
                {
                    return false;
                }
            }

            //check limit end

            //check award cur count
            $dlInput = array(
                'award_ids'=>array($awardHit['award_id']),
            );
            $dlOut = Dl_Tycnt_Tycnt::mgetAwardCurCnt($dlInput);
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('get current award count failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
                return false;
            } elseif ($dlOut['award_cur_counts'][$awardHit['award_id']] <= 0) {
                Bingo_Log::warning('award not enough. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
                return false;
            }

            $dlInput = array(
                'award_id'=>$awardHit['award_id'],
                'award_count'=>-1,
                'award_act_id'=>$awardHit['award_act_id'],
                'user_id'=>$user_id,
                'award_time'=>$time,
            );
            if ($awardHit['award_type']==self::AWARD_TYPE_EXKEY) {
                //obtain one exkey
                $exkeyInput = array(
                    'exkey_act_id'=>$awardHit['ext_info']['exkey_act_id'],
                    'user_id'=>$user_id,
                );
                $exkeyOut = Tieba_Service::call('exkey', 'obtainExKey', $exkeyInput);
                if ($exkeyOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('get exkey failed. input['.serialize($exkeyInput).'] output['.serialize($exkeyOut).']');
                    return false;
                    /*
                     if ($exkeyOut['errno']===Tieba_Errcode::ERR_EXKEY_NOT_ENOUGH) {
                    return false;
                    } else {
                    return false;
                    }
                     */
                }
                $res['key'] = $dlInput['award_desc']['key'] = $exkeyOut['data']['key'];
            }
            $dlOut = Dl_Tycnt_Tycnt::addAwardCurCnt($dlInput);
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::fatal('decr award current count failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
                return false;
            }
            $dlOut = Dl_Base_Base::addUserAward($dlInput);
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::fatal('add user award failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
                return false;
            }
            $dlInput['award'] = $awardHit;
            //add award_time by yaokun
            $dlInput['award']['award_time'] = $time;
            $dlOut = Dl_Tycnt_Tycnt::addRecentAwardRec($dlInput);
            if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('add recent award record failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
            }
            $input = array(
                'award_act_id'=>intval($arrInput['award_act_id']),
                'user_id'=>intval($arrInput['user_id']),
            );
            $arrRet = self::getUserAward($input);
            if ($arrRet['errno']!==Tieba_Errcode::ERR_SUCCESS)
            {
                return false;
            }
            $lastAward = end($arrRet['data']['user_award']);
            $awardHit['lucky_id'] = $lastAward['lucky_id'];
            $res['awardHit'] = $awardHit;
            return $res;
        } else {
            return false;
        }
    }


    /**
     * @return     boolean
     * <AUTHOR>
     * @brief      检查各种抽奖规则
     *
     *  award_act_info表的抽奖规则储存格式
     *  结构如下:
     *
     *  rule
     *      award_rule = array(
     *          //key is award_id
     *          //'award_id' => 10,
     *          12 => 2,
     *      )
     *
     *      award_act_rule
     *          max_0_count     //0表示奖品类型
     *          max_count       //所有奖品的数量
     *          
     *          gap
     *              award_type => array(
     *                  //key 表示奖品的类型
     *                  0 => array(
     *                      'gap'       => 1230012, //秒
     *                      'count'     => 1,       //可中的次数
     *                  ),
     *              )
     *
     *              total_award => array(
     *                  //与上面一样
     *              )
     **/
    private static function _checkAwardRule (&$arrAwardActInfo, &$arrAwardInfo, $intUserId, $intHitAwardId, $intTime) {

        //get award rule
        $arrAwardRule       = $arrAwardActInfo['ext_info']['rule']['award_rule'];
        $arrAwardActRule    = $arrAwardActInfo['ext_info']['rule']['award_act_rule'];

        //create award_info hash
        $arrAwardInfoHash = array();
        foreach ($arrAwardInfo as $award) {
            $award_id = (int)$award['award_id'];
            $arrAwardInfoHash[$award_id] = $award;
        }

        //get user award
        $intAwardActId      = (int)$arrAwardActInfo['award_act_id'];
        $dlInput = array(
            'award_act_id'  => $intAwardActId,
            'user_id'       => $intUserId,
        );
        $dlOut = Dl_Base_Base::getUserAward($dlInput);
        if ($dlOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('get user award failed. input[' . serialize($dlInput) . '] output[' . serialize($dlOut) . ']');
            return false;
        }

        $arrUserAward = $dlOut['user_award'];
        $arrUserAwardHash = array();

        //count current the award count
        foreach ($arrUserAward as $val) {
            $award_id = (int)$val['award_id'];
            $user_award_type = (int)$arrAwardInfoHash[$award_id]['award_type'];
            $award_type_key = "award_type_$user_award_type";
            $arrUserAwardHash['type_count'][$award_type_key] ++;
            $arrUserAwardHash['award_count'][$award_id] ++;
            $arrUserAwardHash['total_count'] ++;

            //record the last award_time
            $award_time = (int)$val['award_time'];
            //save the award_time
            $arrUserAwardHash['last_award_list'][$award_type_key] []    = $award_time;
            $arrUserAwardHash['last_award_list']['total_award'] []      = $award_time;
        }

        //filter the award rule
        foreach ($arrAwardRule as $key => $val) {
            $award_count = (int)$val;
            if (0 < $award_count) {
                $award_id = (int)$key;
                if ($arrUserAwardHash['award_count'][$award_id] >= $award_count) {
                    Bingo_Log::warning('reach the award rule count!');
                    return false;
                }
            }
        }

        $intHitAwardType = (int)$arrAwardInfoHash[$intHitAwardId]['award_type'];
        if (isset($arrAwardActRule["max_" . $intHitAwardType . "_count"])) {
            $intAwardTypeMax = (int)$arrAwardActRule['max_' . $intHitAwardType . "_count"];

            $strAwardTypeKey = "award_type_$intHitAwardType";
            if ($arrUserAwardHash['type_count'][$strAwardTypeKey] >= $intAwardTypeMax) {
                Bingo_Log::warning('reach the award_type count!');
                return false;
            }
        }

        if (isset($arrAwardActRule['max_count'])) {
            $intAwardMax = (int)$arrAwardActRule['max_count'];
            if ($arrUserAwardHash['total_count'] >= $intAwardMax) {
                Bingo_Log::warning('reach the award count!');
                return false;
            }
        }

        //分类型的间隔设置
        $arrAwardTypeGap = $arrAwardActRule['gap']['award_type'];
        if (isset($arrAwardTypeGap[$intHitAwardType])) {
            $val = $arrAwardTypeGap[$intHitAwardType];
            $gap    = (int)$val['gap'];
            $count  = (int)$val['count'];
            if (0 < $gap && 0 < $count) {
                $award_type_key = "award_type_$intHitAwardType";
                $last_award_list = $arrUserAwardHash['last_award_list'][$award_type_key];
                $search_award_time = (int)$last_award_list[count($last_award_list)-$count];
                if ($intTime - $search_award_time < $gap) {
                    Bingo_Log::warning("can not get award less than the award_type gap, and the type is [$intHitAwardType]!");
                    return false;
                }
            }
        }

        $arrAwardGap    = $arrAwardActRule['gap']['total_award'];
        $intTotalGap    = (int)$arrAwardGap['gap'];
        $intTotalCount  = (int)$arrAwardGap['count'];
        if (0 < $intTotalGap && 0 < $intTotalCount) {
            $last_award_list = $arrUserAwardHash['last_award_list']['total_award'];
            $search_award_time = (int)$last_award_list[count($last_award_list)-$intTotalCount];
            if ($intTime - $search_award_time < $intTotalGap) {
                Bingo_Log::warning("can not get award less than the gap!");
                return false;
            }
        }

        return true;
    }

    public static function trimRecentAwardRec($arrInput) {
        if (!isset($arrInput['award_act_id']) || !isset($arrInput['start']) || !isset($arrInput['stop'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $dlOut = Dl_Tycnt_Tycnt::trimRecentAwardRec($arrInput);
        if (0 !== $dlOut['errno']) {
            Bingo_Log::warning("trim user award failed!");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL); 
        }

        $arrRet = array(
            'errno' => 0,
            'errmsg' => 'success',
        );
        return $arrRet;
    }

    public static function delUserAward($arrInput) {
        if (!isset($arrInput['award_act_id']) || !isset($arrInput['user_id']) || !isset($arrInput['award_id'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $dlOut = Dl_Base_Base::delUserAward($arrInput);
        if (0 !== $dlOut['errno']) {
            Bingo_Log::warning("delete user award failed!");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL); 
        }

        $arrRet = array(
            'errno' => 0,
            'errmsg' => 'success',
        );
        return $arrRet;
    }

    public static function dumpAllUserAwardForDev($arrInput){
        if (!isset($arrInput['primary_id']) || !isset($arrInput['slice_offset']) || !isset($arrInput['slice_count'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }
        $intAwardActId = (int)$arrInput['primary_id'];

        $dlInput = array(
            'award_act_id' => $intAwardActId,
            'offset'=>intval($arrInput['slice_offset']),
            'count'=>intval($arrInput['slice_count']),
        );
        $dlOut = Dl_Base_Base::getAllUserAwards($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal('dump user award failed. input['.serialize($dlInput).'] output['.serialize($dlOut).']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrUids = array();
        foreach($dlOut['all_user_awards'] as $user_award) {
            $arrUids [] = (int)$user_award['user_id'];
        }

        $arrUidsInput = array('user_id' => $arrUids, );
        $arrUidsOutput = Tieba_Service::call('user', 'getUnameByUids', $arrUidsInput);
        if (false === $arrUidsOutput || 0 !== (int)$arrUidsOutput['errno']) {
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrUidsHash = array();
        //create uid hash
        foreach($arrUidsOutput['output']['unames'] as $uname) {
            $arrUidsHash[$uname['user_id']] = $uname['user_name'];
        }

        $arrData = array();
        foreach($dlOut['all_user_awards'] as $user_award) {
            $uid = (int)$user_award['user_id'];
            $item = array(
                'user_name' => $arrUidsHash[$uid],
            );
        }

        $arrAwardAct = self::getAwardAct(
            array(
                'award_act_id' => $intAwardActId)
            );
        if (false === $arrAwardAct) {
            Bingo_Log::warning('fail to get award_info from award_act');
        }

        $arrAwardHash = array();
        foreach($arrAwardAct['data']['award_info'] as $award_info) {
            $award_id = $award_info['award_id'];
            $arrAwardHash[$award_id] = $award_info['award_name'];
        }

        $arrData = array();
        foreach($dlOut['all_user_awards'] as $user_award) {
            $user_award['user_name']    = $arrUidsHash[$user_award['user_id']];
            $user_award['award_name']   = (string)$arrAwardHash[$user_award['award_id']];
            unset($user_award['lucky_id']);
            unset($user_award['award_id']);
            unset($user_award['award_act_id']);
            $user_award['award_time'] = date("Y-m-d h:i:sa", $user_award['award_time']);

            $arrUserInfo = $user_award['user_info'];
            $strUserInfo = '';
            foreach ($arrUserInfo as $key => $val) {
                if ($key == 'user_define') {
                    continue;
                }
                $strUserInfo .= "  $key : $val  ";
            }
            $user_award['user_info'] = $strUserInfo;

            $arrAwardDesc = $user_award['award_desc'];
            if(isset($arrAwardDesc['key'])) {
                $user_award['award_desc'] = "key : " . $arrAwardDesc['key'];
            } else {
                $user_award['award_desc'] = '';
            }
            $item = array();
            $item [] = $user_award['award_name'];
            $item [] = $user_award['user_name'];
            $item [] = $user_award['award_time'];
            $item [] = $user_award['user_info'];
            $item [] = $user_award['award_desc'];
            $arrData [] = $item;
        }
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $arrData,
        );  
        return $arrOutput;
    }

    /**
     * @brief 
     *
     * @param $user_id
     * @param $function
     *
     * @return 
     */
    private static function _getUnameByUid($user_id, $function = '') {
        if (0 >= strlen($function)) {
            $function = 'default';
        }

        // get user name
        $arrGetUnameInput = array(
            'user_id' => array(
                $user_id,
            ),
        );
        Bingo_Timer::start("$function::getUnameByUids");
        $arrGetUnameOutput = Tieba_Service::call('user', 'getUnameByUids', $arrGetUnameInput, null, null, 'post', 'php', 'gbk');
        Bingo_Timer::end("$function::getUnameByUids");
        if (false === $arrGetUnameOutput) {
            $intRalNo = ral_get_errno();
            Bingo_Log::fatal("get user name failed, user_id[$user_id], ral_no[$intRalNo]");
            return '';
        }
        $user_name = $arrGetUnameOutput['output']['unames'][0]['user_name'];
        return $user_name;
    }

    /**
     * @brief print notice log.
     *
     * @param $arrField
     *
     * @return 
     */
    private static function _buildNoticeLog($arrField) {
        foreach($arrField as $k => $v) {
            Bingo_Log::pushNotice($k, $v);
        }
    }

    /**
     *@param : award_id, award_act_id
     *@return : errno, errmsg
     **/
    public static function modifyAwardBaseInfo($arrInput) 
    {
        if (!isset($arrInput['award_act_id']) || !isset($arrInput['award_info'])) 
        {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        $award_act_id = intval($arrInput['award_act_id']);
        $award_id     = intval($arrInput['award_info']['award_id']);
        $award        = $arrInput['award_info'];

        //get original info first
        $dlInput = array(
            'award_id'     => $award_id,
            'award_act_id' => $award_act_id,
        );
        $dlOut = Dl_Base_Base::getAward($dlInput);
        if ($dlOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) 
        {
            Bingo_Log::warning('get original info failed when modify award. award_info['.substr(serialize($award),0,1024).']');
            return false;
        }
        $org_award = $dlOut['award'];
        if ($dlOut['award']['award_type'] == self::AWARD_TYPE_EXKEY) 
        {
            $award['ext_info']['exkey_act_id'] = $org_award['ext_info']['exkey_act_id'];
        }
        if (isset($award['award_type'])) 
        {
            $dlInput['award_type'] = intval($award['award_type']);
        }
        if (isset($award['award_name'])) 
        {
            $dlInput['award_name'] = $award['award_name'];
        }
        if (isset($award['desc_text'])) 
        {
            $dlInput['desc_text'] = $award['desc_text'];
        }
        if (isset($award['desc_pic'])) 
        {
            $dlInput['desc_pic'] = $award['desc_pic'];
        }
        if (isset($award['calc_type'])) 
        {
            $dlInput['calc_type'] = intval($award['calc_type']);
        }
        if (isset($award['calc_rule']))
        {
            $dlInput['calc_rule'] = $award['calc_rule'];
        }
        if (isset($award['ext_info'])) 
        {
            $dlInput['ext_info'] = $award['ext_info'];
        }
        $dlOut = Dl_Base_Base::modifyAwardBaseInfo($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) 
        {
            Bingo_Log::warning('modify award base info failed when modify award. award_info['.serialize($award).']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );  
        return $arrOutput;
    }

    /**
     * @brief 
     *
     * @param $arrInput
     *
     * @return 
     */
    private static function _printBasicNoticeLog(&$arrInput) {
        $arrField = array(
            'user_id',
            'award_act_id',
            'award_id',
        );
        foreach($arrField as $k) {
            if( isset($arrInput[$k]) ) {
                $v = (int)$arrInput[$k];
                Bingo_Log::pushNotice($k, $v);
            }
        }
    }

    /**
     * @brief 接收nmq请求
     *
     * @param $arrInput
     *
     * @return 
     */
    public static function nmqHandle($arrInput) {
        $strInputData = file_get_contents("php://input");
        $strTransMcpack  = mc_pack_text2pack(substr($strInputData,5));
        if($strTransMcpack === false || empty($strTransMcpack)) {
            Bingo_Log::fatal("mc_pack_text2pack error.");
            Bingo_Log::warning($strInputData);
            header('X-PHP-Response-Code: 505', true, 505);
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_MCPACK_ERR);
        }
        $arrTransPack = mc_pack_pack2array($strTransMcpack);
        if($arrTransPack === false || empty($arrTransPack)) {
            Bingo_Log::fatal("mc_pack_pack2array error.");
            Bingo_Log::warning($strInputData);
            header('X-PHP-Response-Code: 505', true, 505);
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_MCPACK_ERR);
        }
        return Service_Nmqhandle::main($arrTransPack);
    }

    /**
     * @brief 
     *
     * @param $arrInput
     *
     * @return 
     */
    private static function _commitNmq($arrInput) {
        $intUid = (int)$arrInput['user_id'];
        $strUname = (string)$arrInput['user_name'];
        $arrHitInfo = array(
            'award_act_id' => (int)$arrInput['award_hit']['award_act_id'],
            'award_id' => (int)$arrInput['award_hit']['award_id'],
            'award_name' => (string)$arrInput['award_hit']['award_name'],
            'lucky_id' => (int)$arrInput['award_hit']['lucky_id'],
        );

        /* 务必要检查传入Nmq的参数, 如果是其中有false或者null，将会导致解包失败 */
        $arrNmqInput = array(
            'user_id' => $intUid,
            'user_name' => $strUname,
            'hit_info' => $arrHitInfo,
        );
        $arrNmqOutput = Tieba_Commit::commit('tyche', 'tryluckCommit', $arrNmqInput);
        if (false === $arrNmqOutput || 0 !== $arrNmqOutput['err_no']) {
            $errmsg = sprintf("fail to commit nmq, input[%s], output[%s]", serialize($arrNmqInput), serialize($arrNmqOutput));
            Bingo_Log::warning($errmsg);
            return -1;
        }
        return 0;
    }

    /**
     * @brief 
     *
     * @param $arrInput
     *
     * @return 
     */
    public static function getTailInfo($arrInput) {
        return Service_Tail::getTailInfo($arrInput);
    }

    /**
     * @brief 
     *
     * @param $arrInput
     *
     * @return 
     */
    public static function mgetAwardAct($arrInput) {
        return Service_AwardAct::mgetAwardAct($arrInput);
    }

    /**
     * @brief 
     *
     * @param $arrInput
     *
     * @return 
     */
    public static function mdelAwardActCache($arrInput) {
        return Service_AwardAct::mdelAwardActCache($arrInput);
    }

    /**
     * @brief
     *
     * @param
     *
     * @return
     */
    public static function allocLucky($arrInput) {
        return Service_LuckyPool::allocLucky($arrInput);
    }


    /**
     * @brief
     *
     * @param
     *
     * @return
     */
    public static function createLucky($arrInput) {
        return Service_LuckyPool::createLucky($arrInput);
    }
    
    /**
     * @brief 
     *
     * @param
     *
     * @return
     */
    public static function getUserAllocStatus($arrInput) {
        return Service_LuckyPool::getUserAllocStatus($arrInput);
    }

    /**
     * @brief
     *
     * @param
     *
     * @return
     */
    public static function getLuckyNum($arrInput) {
        return Service_LuckyPool::getLuckyNum($arrInput);
    }


    /**
     * @brief 
     *
     * @param
     *
     * @return
     */
    public static function tryLuckEx($arrInput) {
        return Service_LuckEx::tryLuckEx($arrInput);
    }

    //*********************add by cuishichao start****************//
    /**
     * @brief 
     *
     * @param $arrInput
     *
     * @return 
     */
    public static function addUChance($arrInput) 
    {
        if (!isset($arrInput['award_act_id'])|| !isset($arrInput['chance_count']) 
            || !isset($arrInput['user_id'])) 
        {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
        );
        $dlOut = Dl_Base_Base::getAwardAct($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) 
        {
            Bingo_Log::warning("get award act failed. award_act_id[{$arrInput['award_act_id']}]");
            return self::_errRet($dlOut['errno']);
        }
        $arrAwardRule = $dlOut['award_act_info']['ext_info']['chance_rule'];
        if (empty($arrAwardRule))
        {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intRecStartTime = isset($arrAwardRule['day_period'])?strtotime(date('Y-m-d')):0;
        $intInitCount    = isset($arrAwardRule['start'])?intval($arrAwardRule['start']):0;
        $intLimitCount   = isset($arrAwardRule['limit'])?intval($arrAwardRule['limit']):10000;
        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
            'user_id'=>intval($arrInput['user_id']),
            'rec_start_time'=>$intRecStartTime,
        );
        $dlOut = Dl_Tycnt_Tycnt::getUserChanceRec($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) 
        {
            Bingo_Log::fatal("get user chance record failed. input[".serialize($arrInput).']');
            return self::_errRet($dlOut['errno']);
        }
        $dlOut = self::_calcRecChanceCnt($dlOut['chance_record']);
        $intAddChance = intval($dlOut['record_add_count']);
        $intDiff = $intLimitCount - $intAddChance;
        if ($intDiff <= 0)
        {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $intCount = min($intDiff, $arrInput['chance_count']);
        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
            'user_id'=>intval($arrInput['user_id']),
            'chance_count'=>$intCount,
            'chance_type' => 'add',
            'op_time'=>isset($arrInput['op_time'])?intval($arrInput['op_time']):time(),
        );

        $dlOut = Dl_Tycnt_Tycnt::addUserChanceRec($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal('add user chance record failed. input['.serialize($arrInput).']');
            return self::_errRet($dlOut['errno']);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );  
        return $arrOutput;
    }
    
    /**
     * @brief 
     *
     * @param $arrInput
     *
     * @return 
     */
    public static function getUChance($arrInput) {
        if (!isset($arrInput['award_act_id']) || !isset($arrInput['user_id'])) 
        {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
        );
        $dlOut = Dl_Base_Base::getAwardAct($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) 
        {
            Bingo_Log::warning("get award act failed. award_act_id[{$arrInput['award_act_id']}]");
            return self::_errRet($dlOut['errno']);
        }
        $arrAwardRule = $dlOut['award_act_info']['ext_info']['chance_rule'];
        if (empty($arrAwardRule))
        {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intRecStartTime = isset($arrAwardRule['day_period'])?strtotime(date('Y-m-d')):0;
        $intInitCount    = isset($arrAwardRule['start'])?intval($arrAwardRule['start']):0;
        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
            'user_id'=>intval($arrInput['user_id']),
            'rec_start_time'=>$intRecStartTime,
        );

        self::_printBasicNoticeLog($arrInput);

        $dlOut = Dl_Tycnt_Tycnt::getUserChanceRec($dlInput);

        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) 
        {
            Bingo_Log::fatal("get user chance record failed. input[".serialize($arrInput).']');
            return self::_errRet($dlOut['errno']);
        }
        $chance_record = $dlOut['chance_record'];
        if ($intInitCount > 0) {
            $chance_refresh = false;
            if (empty($chance_record)) {
                $chance_refresh = true;
            } else {
                $chance_refresh = true;
                foreach ($chance_record as $rec) {
                    // check have add chance?
                    if ($rec['chance_type']==self::CHANCE_TYPE_DEFAULT) {
                        $chance_refresh = false;
                        break;
                    }
                }
            }
            if ($chance_refresh) {
                $time = time();
                $dlInput['op_time'] = $time;
                $dlInput['chance_type'] = self::CHANCE_TYPE_DEFAULT;
                $dlInput['chance_count'] = $intInitCount;
                $dlOut = Dl_Tycnt_Tycnt::addUserChanceRec($dlInput);
                if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::fatal("add user default chance failed. award_act_id[{$arrInput['award_act_id']}], user_id[{$arrInput['user_id']}]");
                    return self::_errRet($dlOut['errno']);
                }
                $chance_record []= array(
                    'op_time'=>$time,
                    'chance_type'=>self::CHANCE_TYPE_DEFAULT,
                    'chance_count'=>$intInitCount,
                );
            }
        }

        $data = self::_calcRecChanceCnt($chance_record);
        $data['chance_record'] = $chance_record;

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );  
        return $arrOutput;
    }

    /**
    *  @param  : 
    *  @return : 
    */
    public static function getRecentAward($arrInput)
    {
        if (!isset($arrInput['award_act_id']))
        {
            Bingo_Log::warning('param error!');
            $error = Tieba_Errcode::ERR_PARAM_ERROR;
            $arrOutput = array(
                'errno' => $error,
                'errmsg' => Tieba_Error::getErrmsg($error),
                'data' => $data,
            ); 
            return $arrOutput;
        }
        Bingo_Timer::start('tyche_getRecentAward');
        $dlInput = array(
            'award_act_id'=>intval($arrInput['award_act_id']),
        );
        $dlOut = Dl_Tycnt_Tycnt::getRecentAwardRec($dlInput);
        if ($dlOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("get recent award record failed. award_act_id[{$arrInput['award_act_id']}]");
            return self::_errRet($dlOut['errno']);
        }
        $data = $dlOut['recent_award_records'];
        Bingo_Timer::end('tyche_getRecentAward');
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );      
        return $arrOutput;
    }


    /**
     * @param : 
     * @return :
     */
    private static function _calcLeftCount($chance_record, $day_limit)
    {
        $intTodayTime = strtotime(date("Y-m-d"));
        $intAddCount  = 0;
        $intLeftCount = 0;
        foreach ($chance_record as $rec) 
        {
            if (($rec['chance_count']>0) && ($rec['op_time'] >= $intTodayTime))
            {
                $intAddCount += $rec['chance_count'];
            }
        }
        if ($intAddCount < $day_limit)
        {
            $intLeftCount = $day_limit - $intAddCount;
        }
        return $intLeftCount;
    }

    // add by yaokun for component
    /**
        * @brief h5topic版的tryluck
        *
        * @param $arrInput
        *
        * @return 
     */
    public static function tryLuckComp($arrInput) {
        return Service_Component::tryLuckComp($arrInput);
    }

    // add by yaokun for component
}


/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
