<?php
/**
* @copyright Copyright (c) www.baidu.com
* <AUTHOR> <PERSON><PERSON><PERSON><PERSON>@baidu.com
* @date 2009-8-18
* @version
*/
class Request
{
	private $_request ;
	public function __construct()
	{
		$this->_init();
	}
	private function _init()
	{
		$post = get_magic_quotes_gpc() ?stripslashes_deep($_POST):$_POST;
		$get = get_magic_quotes_gpc() ?stripslashes_deep($_GET):$_GET;
		$this->_request = array_merge($post,$get);		
	}

	public function getParam($key,$default =false)
	{
		if(isset($this->_request[$key]))
		{
			return $this->_request[$key];
		}
		else
		{
			if($default === false)
			{
				return false;
			}
			else 
			{
				return $default;
			}
		}	
	}
	public function getParamLike($key)
	{
		$arrValue = array();
		$arrRequestKey = array_keys($this->_request);
	    foreach ( $arrRequestKey as $eachkey)
        {
           if ( preg_match('/^'.$key.'/',$eachkey))
            {
                    $arrValue [$eachkey] = $this->_request[$eachkey];
            }
        }	
        return $arrValue;	
		
	}
	public function isExist($key)
	{
		return isset($this->_request[$key]);
	}
	public function setParam($key,$value = false)
	{
		$this->_request[$key] = $value;
	}
	public function getAll()
	{
		return $this->_request;
	}	
}
function stripslashes_deep($value)
{
    $value = is_array($value) ?
                array_map('stripslashes_deep', $value) :
                stripslashes($value);

    return $value;
}