<?php
/***************************************************************************
 * 
 * Copyright (c) 2009 Baidu.com, Inc. All Rights Reserved
 * $Id: trans.php,v 1.2 2009/09/10 02:32:29 jipc Exp $ 
 * 
 **************************************************************************/
 
 
 
/**
 * @file trans.php
 * <AUTHOR>
 * @date 2009/08/18 18:18:46
 * @version $Revision: 1.2 $ 
 * @brief 
 *  
 **/

$mysqli = new mysqli("127.0.0.1", "root", "2j9387e23ju2h39sd", "vote_old", 3307);
if (mysqli_connect_errno()) {
	printf("Connect failed: %s\n", mysqli_connect_error());
	exit();
}

$conn = new mysqli("127.0.0.1", "root", "2j9387e23ju2h39sd", "vote", 3307);
if (mysqli_connect_errno()) {
	printf("Connect failed: %s\n", mysqli_connect_error());
	exit();
}

printf("Host information: %s\n", $mysqli->host_info);
printf("Host information: %s\n", $conn->host_info);

$min = 1000000;
$max = 1200000;

for($idx=0; $idx<=1; $idx++){
	$sql = "SELECT * FROM vote_basic".$idx." where vote_id > ".$min." and vote_id < ".$max;
	$result = $mysqli->query($sql);
	if ($result === false) {
		return false;
	}
	while ($row = $result->fetch_assoc()) {	
		$str = $row['options'];
		$num = strlen($str) / 64;
		$sql = sprintf("insert into vote_basic%d(vote_id, commit_time, commit_uname, title, content, commit_uid, commit_ip, total_count, item_num, max_select_num, expire_time) values(%d, %d, '%s', '%s', '%s', 0, '', 0, %d, 0, 0);",
			$idx,
			$row['vote_id'], 
			$row['begin'], 
			$conn->real_escape_string(trim($row['username'])), 
			$conn->real_escape_string(trim($row['title'])), 
			$conn->real_escape_string(trim($row['description'])), $num);

		$conn->query($sql);

		$sql = sprintf("insert into vote_relation(product_id, id_1, id_2, vote_id) values(1, %d, %d, %d);", 
			$row['id1'], $row['id2'], $row['vote_id']);
		$conn->query($sql);

		for($i = 0; $i < $num; $i++){
			$text = substr($str, $i*64, 64);
			$text = trim($text);
			$sql = sprintf("insert into vote_item%d(vote_id, item_id, item_count, item_title, item_content) values(%d, %d, 0, '%s', '');", $idx, $row['vote_id'], $i+1, $conn->real_escape_string($text));
			$ret = $conn->query($sql);
			if(false === $ret){
				echo $sql."\n";
			}
		}
	}
	$result->close();

	$sql = "SELECT * FROM vote_option".$idx." where vote_id > ".$min." and vote_id < ".$max;
	$result = $mysqli->query($sql);
	if ($result === false) {
		return false;
	}
	while ($row = $result->fetch_assoc()) {
		if(intval($row['option_id']) === 100){
			$sql = sprintf("update vote_basic%d set total_count = %d where vote_id = %d;", $idx, $row['count'], $row['vote_id']);
			$ret = $conn->query($sql);
			if(false === $ret){
				echo $sql."\n";
			}
		}
		else{
			$sql = sprintf("update vote_item%d set item_count = %d where vote_id = %d and item_id = %d;",
				$idx, $row['count'], $row['vote_id'], $row['option_id'] + 1);

			$ret = $conn->query($sql);
			if(false === $ret){
				echo $sql."\n";
			}
		}
	}
	$result->close();

	$sql = "SELECT * FROM vote_flag".$idx." where vote_id > ".$min." and vote_id < ".$max;
	$result = $mysqli->query($sql);
	if ($result === false) {
		return false;
	}
	while ($row = $result->fetch_assoc()) {
		if(intval($row['perm']) === 1){
			$row['perm'] = 0;
		}
		else{
			$row['perm'] = 2;
		}	

		if(intval($row['status']) === 9){
			$row['status'] = 1;
		}
		else{
			$row['status'] = 0;
		}

		$sql = sprintf("update vote_basic%d set status = %d, perm = %d, max_select_num = %d, expire_time = %d where vote_id = %d;", $idx, $row['status'], $row['perm'], $row['max_num'], $row['end'], $row['vote_id']);
		$ret = $conn->query($sql);
		if(false === $ret){
			echo $sql."\n";
		}
	}
	$result->close();
}

$mysqli->close();
$conn->close();
/* vim: set ts=4 sw=4 sts=4 tw=100 noet: */
?>
