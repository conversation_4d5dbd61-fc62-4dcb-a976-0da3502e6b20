//cmd:309351
package tbclient.GetCartoonCategoryList;
import "client.proto";

message GetCartoonCategoryListResIdl {
    optional Error error = 1;
    optional DataRes data = 2;
}

message DataRes {
    //条漫作品列表
    repeated CartoonInfo cartoon_list = 1;
    //是否还有更多数据
    optional int32 has_more = 2;
}

message CartoonInfo {
    //吧id
    optional int64 forum_id = 1;
    //条漫id
    optional int64 cartoon_id = 2;
    //条漫名
    optional string cartoon_name = 3;
    //简介
    optional string introduction = 4;
    //是否完结
    optional int32 is_finish = 5;
    //封面图
    optional string cover_img = 6;
    //总章节数
    optional int32 total_chapter = 7;
}

