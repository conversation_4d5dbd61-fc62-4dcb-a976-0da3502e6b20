//cmd:309662
package tbclient.GetForumData;
import "client.proto";

message GetForumDataResIdl {
    optional Error error = 1;
    optional DataRes data = 2;
}

message DataRes {
    //吧数据各项数据
    repeated ForumDataItem data = 1;
}

message ForumDataGroup {
    //所有项总和
    optional double total = 1;
    //每天数据
    repeated ForumDataValue values = 2;
}

message ForumDataItem {
    //数据项类型,0：新增关注，1：新增PV，2：新增贴子，3：新增回复，4：签到率，5：人均时长，6：人均访问次数，7，上首页贴子数
    optional int32 type = 1;
    //数据项对应的多组数据
    repeated ForumDataGroup group = 2;
}

message ForumDataValue {
    //日期,例：2010-05-01
    optional string date = 1;
    //日期对应数值
    optional double value = 2;
}

