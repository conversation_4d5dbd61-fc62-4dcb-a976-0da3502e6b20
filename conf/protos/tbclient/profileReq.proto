//cmd:303012
//url:/c/u/user/profile
package tbclient.Profile;

import "client.proto";

message ProfileReqIdl {
    optional DataReq data = 1;
}

message DataReq {
    optional int64 uid = 1;
    //是否需要我的贴子总数,1需要，默认不需要
    optional uint32 need_post_count = 2;
    //好友uid
    optional int64 friend_uid = 3;
    //是否是好友标志
    optional uint32 is_guest = 4;
    //来源，用于统计
    optional string st_type = 5;
    //页码，默认为1，第一页
    optional uint32 pn = 6;
    //每页展示数量， 默认为20条 (现在客户端没有传)
    optional uint32 rn = 7;
    //(目前客户端没有传此参数)
    optional uint32 has_plist = 8;
    optional CommonReq common = 9;
    //屏幕宽
    optional uint32 scr_w = 10;
    //屏幕高度
    optional uint32 scr_h = 11;
    //图片质量
    optional uint32 q_type = 12;
    //屏幕dip
    optional double scr_dip = 13;
    //是否从个人中心请求
    optional int32 is_from_usercenter = 14;
    //客户端当前页面，1：个人中心一级页面，2：个人中心二级页面
    optional int32 page = 15;
    //uid的加密
    optional string friend_uid_portrait = 16;
}

