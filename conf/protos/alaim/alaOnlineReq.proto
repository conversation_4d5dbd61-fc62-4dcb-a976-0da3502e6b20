//cmd:601001
//url: /alaim/reg/online
package alaim.AlaOnline;
import "alaCommon.proto";
message DataReq{
    // bduss 
	optional string bduss = 1;
    // 设备信息json格式                                       
    optional string device = 2;
    // 加密串                                             
    optional bytes secretKey = 3;
    // 经度                                         
    optional double lat = 4;
    // 纬度                                            
    optional double lng = 5;
    // 宽度                                            
    optional int32 width = 6;
    // 高度                                           
    optional int32 height = 7;
    // 未读消息数
    optional int32 unread_msg = 8;
    // 
    optional int32 pub_env = 9;
    // 产品信息                                           
    optional string project = 10;
    
	//DEL optional int32 groupId = 11;
}

message AlaOnlineReqIdl{
    // cuid                                           
    optional string cuid = 1;
    // 请求数据                                           
    optional DataReq data = 2;
}
