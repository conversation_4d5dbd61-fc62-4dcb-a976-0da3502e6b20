//cmd:103009
package protobuf.QueryGroupsByLocation;
import "im.proto";
message Business{
    //                                              __time=2015-10-09 00:04:14
    optional string business = 1;
    //                                              __time=2015-10-09 00:04:14
    repeated GroupInfo groups = 2;
}
message DataRes{
    //                                              __time=2015-10-09 00:04:14
    repeated Business groups = 1;
    //                                              __time=2015-10-09 00:04:15
    optional int32 geo = 2;
    //                                              __time=2015-10-09 00:04:15
    optional int32 offset = 3;
    //                                              __time=2015-10-09 00:04:15
    optional int32 hasMore = 4;
}
message QueryGroupsByLocationResIdl{
    //                                              __time=2015-10-09 00:04:15
    optional Error error = 1;
    //                                              __time=2015-10-09 00:04:15
    optional DataRes data = 2;
}
