//cmd:1007
//url:/im/im/submit
package protobuf.UpdatePCClientInfo;

import "im.proto";

message UpdatePCClientInfoReqIdl {
    optional string cuid = 1;
    optional DataReq data = 2;
}

message DataReq {
    optional string bduss = 1;
    optional string device = 2;
    optional bytes secretKey = 3;
    optional double lat = 4;
    optional double lng = 5;
    optional int32 width = 6;
    optional int32 height = 7;
    optional int32 unread_msg = 8;
    optional int32 pub_env = 9;
    optional string project = 10;
    optional int64 groupId = 11;
}

