[addCategoryConf]
type: update
[.@command]
sql : insert ignore into predefine_category_list (parent_id, category_name, create_time, config_sql, config_desc, config_priority, category_type, config_sql_md5,category_from) values ({parent_id:n}, {category_name:s}, {create_time:n}, {config_sql:s}, {config_desc:s}, {config_priority:n}, {category_type:s}, {config_sql_md5:s}, {category_from:s})

[getCategoryConf]
type: query
[.@command]
sql : select category_id, parent_id, category_name, create_time, config_sql, config_desc, config_priority, category_type, config_sql_md5, category_from from predefine_category_list order by category_id desc limit {offset:n}, {size:n}

[getCategoryCount]
type: query
[.@command]
sql : select count(category_id)from predefine_category_list

[getCategoryConfByCategoryid]
type: query
[.@command]
sql : select category_id, parent_id, category_name, create_time, config_sql, config_desc, config_priority, category_type, config_sql_md5, category_from from predefine_category_list where category_id = {category_id:n}

[getCategoryConfByCategoryType]
type: query
[.@command]
sql : select category_id, parent_id, category_name, create_time, config_sql, config_desc, config_priority, category_type, config_sql_md5, category_from from predefine_category_list where category_type = {category_type:s}

[delCategoryConf]
type: update
[.@command]
sql : delete from predefine_category_list where category_id = {category_id:n}

[setCategoryConfByCategoryid]
type: update
[.@command]
sql : update predefine_category_list set category_name = {category_name:s}, config_priority = {config_priority:n}, update_time = {update_time:n}, config_sql = {config_sql:s}, config_sql_md5 =  {config_sql_md5:s}, category_type = {category_type:s}  where category_id = {category_id:n}

[addBuildTask]
type: update
[.@command]
sql : insert ignore into push_predefine_file_list (category_id, unique_id, task_name, create_time, status) values ({category_id:n}, {unique_id:s}, {task_name:s}, {create_time:n}, {status:n})

[getTaskByStatus]
type: query
[.@command]
sql : select id, unique_id, category_id, task_name, file_name, create_time, update_time, status from push_predefine_file_list where status in ({cond:r})

[getTaskByStatusAndTime]
type: query
[.@command]
sql : select id, unique_id, category_id, task_name, file_name, create_time, update_time, status from push_predefine_file_list where status = {status:n} and create_time > {limit_time:n}

[getTaskByUniqueid]
type: query
[.@command]
sql : select id, unique_id, category_id, task_name, file_name, create_time, update_time, status from push_predefine_file_list where unique_id in ('{cond:r}')

[setTaskByStatus]
type: update
[.@command]
sql : update push_predefine_file_list set status = {status:n}, update_time = {update_time:n} where id = {id:n}

[setTaskByStatusFilename]
type: update
[.@command]
sql : update push_predefine_file_list set status = {status:n}, update_time = {update_time:n}, file_name = {file_name:s} where id = {id:n}

[deletePredefineFile]
type: update
[.@command]
sql : delete from push_predefine_file_list where category_id = {category_id:n} and create_time >= {create_time:n}

[deletePushFileBytaskid]
type: update
[.@command]
sql : delete from push_predefine_task_list where task_id = {task_id:n} and create_time >= {create_time:n}

[deletePushTaskListBytaskid]
type: update
[.@command]
sql : delete from push_subtask_info where task_id = {task_id:n} and create_time >= {create_time:n}

[setCategoryFromByCategoryId]
type: update
[.@command]
sql : update predefine_category_list set category_from = {category_from:s} where category_id = {category_id:n}

[getCategoryConfByCategoryFrom]
type: query
[.@command]
sql : select category_id, parent_id, category_name, create_time, config_sql, config_desc, config_priority, category_type, config_sql_md5, category_from from predefine_category_list where category_from in ('{cond:r}') order by category_id desc limit {offset:n}, {size:n}

[getCategoryCountByCategoryFrom]
type: query
[.@command]
sql : select count(category_id)from predefine_category_list where category_from in ('{cond:r}')
