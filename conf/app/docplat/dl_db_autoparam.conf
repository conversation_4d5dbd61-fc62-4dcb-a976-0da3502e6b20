 [addAutoparam]
 type:batchInsert
 [.@command]
 sql : insert into visualtest_parameter(user_name, label_name, request_uri, request_param, create_time) values ({user_name:s}, {label_name:s}, {request_uri:s}, {request_param:s},  {create_time:n});

 [delById]
 type: update
 [.@command]
 sql : delete from visualtest_parameter where id = {id:n};

 [getAutoparamByLabel]
 type: query
 [.@command]
 sql : select id,request_param, create_time from visualtest_parameter where label_name = {label_name:s} and user_name = {user_name:s} and request_uri = {request_uri:s};

 [getLabelByUser]
 type: query
 [.@command]
 sql : select id,label_name, create_time from visualtest_parameter where user_name = {user_name:s} and request_uri = {request_uri:s};

 [getRecommendReq]
 type: query
 [.@command]
 sql : select id,label_name, user_name from visualtest_parameter where request_uri = {request_uri:s} and user_name not in ({user_name:s}) ;
 
 [getAutoparamById]
 type: query
 [.@command]
 sql : select request_param,request_uri, create_time from visualtest_parameter where id = {id:d} ;
