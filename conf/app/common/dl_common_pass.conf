[dbConf]
dbRalName:DB_forum_live

[addAbnormalInfoOp]
type:update
[.@command]
sql : insert into passabnormalinfo (messageid, uid, time, model, action, ipaddress, devicelist,create_time ) values ({messageid:s}, {uid:n}, {time:n}, {model:s}, {action:s}, {ipaddress:s}, {devicelist:s}, {create_time:n})

[getAbnormalInfoOp]
type:query
[.@command]
sql : select id,messageid,uid,time,model,action,ipaddress,devicelist,create_time from passabnormalinfo limit {num:n} offset {offset:n}

[delAbnormalInfoOp]
type:update
[.@command]
sql : delete from passabnormalinfo where uid in ({uids:r})
