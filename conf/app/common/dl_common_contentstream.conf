[dbConf]
dbRalName:DB_forum_content

[addContentStream]
type: update
use_transaction : 0
[.@command]
sql : insert into content_stream (stream_id, stream_name, create_time, team_id, sources, select_condition, deliver_pools, job_desc, stream_status, op_uid, op_uname) values({stream_id:n}, {stream_name:s}, {create_time:n},{team_id:n},{sources:s},{select_condition:s},{deliver_pools:s},{job_desc:s},{stream_status:n},{op_uid:n},{op_name:s})

[getMaxStreamid]
type: query
use_transaction : 0
[.@command]
sql : select max(id) from content_stream

[getStreamConfig]
type: query
use_transaction : 0
[.@command]
sql : select stream_name, create_time, team_id, sources, select_condition, deliver_pools, job_desc, stream_status from content_stream where team_id={team_id:n} and stream_id={stream_id:n}

[getContentStream]
type: query
use_transaction : 0
[.@command]
sql : select id,stream_id, stream_name, create_time, team_id, sources, select_condition, deliver_pools, job_desc, stream_status from content_stream where team_id={team_id:n} and id<={id:n} and stream_status<99 order by id desc limit {limit:n}

[getContentStreamCnt]
type: query
use_transaction : 0
[.@command]
sql : select count(id) as cnt from content_stream where team_id={team_id:n} and id<={id:n} and stream_status<99

[updateContentStream]
type: update
use_transaction : 0
[.@command]
sql : update content_stream set stream_name={stream_name:s}, modify_time={modify_time:n}, op_uid={op_uid:n},op_uname={op_name:s}, team_id={team_id:n}, sources={sources:s}, select_condition={select_condition:s}, deliver_pools={deliver_pools:s}, job_desc={job_desc:s}, stream_status={stream_status:n} where stream_id={stream_id:n}

[updateContentStreamStatus]
type: update
use_transaction : 0
[.@command]
sql : update content_stream set stream_status={stream_status:n}, modify_time={modify_time:n}, op_uid={op_uid:n},op_uname={op_name:s} where stream_id={stream_id:n}

[checkStreamNameAvailable]
type: query
use_transaction : 0
[.@command]
sql : select stream_id from content_stream where team_id={team_id:n} and stream_name={stream_name:s}

[getStreamByTeamid]
type: query
use_transaction : 0
[.@command]
sql : select stream_id,stream_name from content_stream where team_id={team_id:n} and stream_status={stream_status:n}

[getSourceByStreamid]
type: query
use_transaction : 0
[.@command]
sql : select sources,team_id from content_stream where stream_id={stream_id:n}

[getSourceByMStreamid]
type: query
use_transaction : 0
[.@command]
sql : select stream_id, sources,team_id from content_stream where stream_id in ({stream_id_list:r})

[getDeliverPoolByStreamid]
type: query
use_transaction : 0
[.@command]
sql : select deliver_pools from content_stream where stream_id={stream_id:n}

[getResourceByCons]
type: query
use_transaction : 0
[.@command]
#sql : select id,import_date,resource_id,resource_type,source_id,audit_status,audit_strategy,hot_score,quality_score,op_uid,op_uname from content_audit_pool where quality_score<={quality_score:n} and stream_id={stream_id:n} {addCondition:r} order by quality_score desc limit {limit:n}
sql : select id,import_date,resource_id,resource_type,source_id,audit_status,audit_strategy,hot_score,quality_score,resource_tag_list,category_id,resource_fdir,resource_sdir,op_uid,op_uname,audit_task_id,old_resource_id,old_resource_type,import_source_db_date from content_audit_pool where stream_id={stream_id:n} {addCondition:r} order by {order_key:r} desc, quality_score desc limit {position:n}, {batch:n}

[getResourceByConsTime]
type: query
use_transaction : 0
[.@command]
sql : select id,import_date,resource_id,resource_type,source_id,audit_status,audit_strategy,hot_score,quality_score,resource_tag_list,category_id,resource_fdir,resource_sdir,op_uid,op_uname,audit_task_id,old_resource_id,old_resource_type,import_source_db_date from content_audit_pool where stream_id={stream_id:n} {addCondition:r} order by modify_time desc, quality_score desc limit {position:n}, {batch:n}

[getResourceByStream]
type: query
use_transaction : 0
[.@command]
sql : select import_date,resource_id,resource_type,source_id from content_audit_pool where import_date={import_date:n} and stream_id={stream_id:n} and audit_status={audit_status:n} and resource_id % {divisor:n}={remainder:n} order by id limit {offset:n}, {limit:n}

[getDataByAutoeditStatus]
type: query
use_transaction : 0
[.@command]
sql : select id,resource_id,resource_type from content_audit_pool where import_date={import_date:n} and stream_id={stream_id:n} and audit_status={audit_status:n} and autoedit_status={autoedit_status:n} and id % {divisor:n}={remainder:n} order by id limit {offset:n}, {limit:n}

[getResourceForPhotoAblum]
type: query
use_transaction : 0
[.@command]
sql : select id,import_date,resource_id,resource_type,source_id,audit_status,audit_strategy,hot_score,quality_score,op_uid,op_uname from content_audit_pool where stream_id={stream_id:n} {cond:r} order by modify_time desc limit {offset:n},{limit_num:n}

[getResourceByResourceId]
type: query
use_transaction : 0
[.@command]
sql : select id, import_date, resource_id, resource_type, source_id, audit_status, audit_strategy, hot_score, quality_score, stream_id, op_uid, op_uname, modify_time from content_audit_pool where resource_id = {resource_id:n} and resource_type = {resource_type:n} and stream_id = {stream_id:n}

[getResourceByResIdNoStreamid]
type: query
use_transaction : 0
[.@command]
sql : select id, import_date, resource_id, resource_type, source_id, audit_status, audit_strategy, hot_score, quality_score, stream_id, op_uid, op_uname, modify_time, resource_tag_list, category_id from content_audit_pool where resource_id = {resource_id:n} and resource_type = {resource_type:n} order by modify_time desc limit 1

[getResourceCnt]
type: query
use_transaction : 0
[.@command]
sql : select count(id) from content_audit_pool where quality_score>={quality_score:n} and stream_id={stream_id:n} {addCondition:r}

[changeResourceAuditStatus]
type: update
use_transaction : 0
[.@command]
sql : update content_audit_pool set audit_status={audit_status:n}, modify_time={modify_time:n}, op_uid={op_uid:n},op_uname={op_name:s} where id={id:n}

[changeResourceQualityScore]
type: update
use_transaction : 0
[.@command]
sql : update content_audit_pool set quality_score={quality_score:n}, modify_time={modify_time:n}, op_uid={op_uid:n},op_uname={op_name:s} where id={id:n}

[batchChangeResourceAuditStatus]
type: update
use_transaction : 0
[.@command]
sql : update content_audit_pool set audit_status = {audit_status:n}, modify_time = {modify_time:n}, op_uid = {op_uid:n}, op_uname = {op_name:s} where stream_id = {stream_id:n} {addCondition:r}

[changeResourceIdType]
type: update
use_transaction : 0
[.@command]
sql : update content_audit_pool set resource_id={resource_id:n}, resource_type={resource_type:n}, modify_time={modify_time:n}, op_uid={op_uid:n},op_uname={op_name:s} where id={id:n}

[changeAutoEditStatus]
type: update
use_transaction : 0
[.@command]
sql : update content_audit_pool set autoedit_status={autoedit_status:n}, modify_time={modify_time:n}, op_uid={op_uid:n},op_uname={op_name:s} where id={id:n}

[changeContentType]
type: update
use_transaction : 0
[.@command]
sql : update content_audit_pool set {feild:r} where id={id:n}

[cleanAuditPool]
type: update
use_transaction : 0
[.@command]
sql : delete from content_audit_pool where import_date <= {import_date:n} and (audit_status=0 or audit_status=3)

[delAuditPool]
type: update
use_transaction : 0
[.@command]
sql : delete from content_audit_pool where stream_id={stream_id:n} and resource_id={resource_id:n} and resource_type={resource_type:n}


[getJobConfig]
type: query
use_transaction : 0
[.@command]
sql : select stream_id, stream_name, create_time, team_id, sources, select_condition, deliver_pools, job_desc, modify_time,op_uid,op_uname from content_stream where stream_id>{stream_id:n} and stream_status={stream_status:n} and stream_id%{total_index:n}={index:n} order by stream_id limit {limit:n}

[getJobLastRunTime]
type: query
use_transaction : 0
[.@command]
sql : select max(end_time) from content_stream_history where stream_id={stream_id:n}

[getAuditContent]
type: query
use_transaction : 0
[.@command]
sql : select stream_id from content_audit_pool where stream_id={stream_id:n} and resource_id={resource_id:n} and resource_type={resource_type:n}

[addAuditContent]
type: update
use_transaction : 0
[.@command]
sql : insert ignore into content_audit_pool (stream_id, import_date, resource_id, resource_type, source_id, audit_status, audit_strategy, hot_score, quality_score, op_uid, op_uname, resource_tag_list, category_id,resource_fdir,resource_sdir,audit_task_id,old_resource_id,old_resource_type,import_source_db_date) values({stream_id:n}, {import_date:n}, {resource_id:n},{resource_type:n},{source_id:n},{audit_status:n},{audit_strategy:n},{hot_score:n},{quality_score:n},{op_uid:n},{op_name:s}, {resource_tag_list:s}, {category_id:n},{resource_fdir:s},{resource_sdir:s},{audit_task_id:n},{old_resource_id:n},{old_resource_type:n},{import_source_db_date:n})

[addJobHistory]
type: update
use_transaction : 0
[.@command]
sql : insert into content_stream_history (job_id, stream_id, create_time, end_time, team_id, result, status) values({job_id:n}, {stream_id:n}, {create_time:n}, {end_time:n}, {team_id:n},{result:s}, {status:n})

[getThreadEditDetailByTid]
type:query
[.@command]
#sql : select stream_id from resource_thread_detail where stream_id={stream_id:n} and thread_id={resource_id:n} and thread_type={resource_type:n} limit 1
sql : select stream_id from resource_thread_detail where stream_id={stream_id:n} and thread_id={resource_id:n}  limit 1

[getResourceById]
type: query
use_transaction : 0
[.@command]
sql : select id,import_date,resource_id,resource_type,source_id,audit_status,audit_strategy,hot_score,quality_score,op_uid,op_uname,audit_task_id from content_audit_pool where id={id:n}

[getAuditResourceDetailByConf]
type: query
use_transaction : 0
[.@command]
sql : select t1.id as id,t1.import_date as import_date,t1.resource_id as resource_id, t1.resource_type as resource_type, t1.source_id as source_id, t1.audit_status as audit_status, t1.audit_strategy as audit_strategy,t1.quality_score as quality_score,t1.resource_fdir as resource_fdir,t1.resource_sdir as resource_sdir, t2.thread_id as thread_id,t2.resource_title as resource_title,t2.resource_cover as resource_cover,t2.resource_content as resource_content ,t2.resource_tag_list as resource_tag_list,t2.category_id as category_id from (select id,import_date,resource_id,resource_type,source_id,audit_status,audit_strategy,hot_score,quality_score, resource_tag_list, category_id,resource_fdir,resource_sdir,op_uid,op_uname from content_audit_pool where stream_id={stream_id:n} {addCondition:r} limit {position:n}, {batch:n}) as t1 left join resource_thread_detail as t2  on t1.resource_id=t2.resource_id and t1.resource_type=t2.resource_type

[changeResourceAuditTaskId]
type: update
use_transaction : 0
[.@command]
sql : update content_audit_pool set audit_task_id={audit_task_id:n}, modify_time={modify_time:n}, op_uid={op_uid:n},op_uname={op_name:s} where id={id:n}


