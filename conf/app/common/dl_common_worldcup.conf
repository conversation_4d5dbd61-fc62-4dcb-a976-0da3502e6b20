[dbConf]
dbRalName:DB_forum_live

[addTopicOp]
type:update
[.@command]
sql : insert into worldcup_topic (topicposition,topicname,onlinetime,offlinetime,opname,isdel,ext) values ({topicposition:n}, {topicname:s}, {onlinetime:n}, {offlinetime:n}, {opname:s}, {isdel:n}, {ext:s})

[getTopicIdOp]
type:query
[.@command]
sql : select topicid,topicname,topicposition from worldcup_topic where isdel = 0 and topicposition={topicposition:n} and onlinetime < {time:n} and offlinetime>{time:n} order by topicposition

[getTopicOpByCond]
type:query
[.@command]
sql : select topicid,topicposition,topicname,onlinetime,offlinetime,opname,isdel,ext from worldcup_topic where {cond:r} limit {num:n} offset {offset:n}

[delTopicOp]
type:update
[.@command]
sql : delete from worldcup_topic where topicid={topicid:n}

[updateTopicOp]
type:update
[.@command]
sql : update worldcup_topic set topicposition={topicposition:n},topicname={topicname:s},onlinetime={onlinetime:n},offlinetime={offlinetime:n},opname={opname:s},ext={ext:s}  where topicid={topicid:n}

[getTopicCount]
type:query
[.@command]
sql : select count(topicid) as total from worldcup_topic where isdel=0

[getTopicCountByCond]
type:query
[.@command]
sql : select count(topicid) as total from worldcup_topic where isdel=0 and {cond:r}

[getTopic]
type:query
[.@command]
sql : select topicid as value,topicname as label from worldcup_topic where isdel=0 

[addVideoOp]
type:update
[.@command]
sql : insert into worldcup_video (threadid,topicid,opname,createtime,fixed,ext) values ({threadid:n}, {topicid:n}, {opname:s}, {createtime:n},{fixed:n}, {ext:s})

[getVideoOpByCond]
type:query
[.@command]
sql : select id,fixed,threadid,topicid,opname,createtime,ext from worldcup_video where {cond:r} order by fixed desc, createtime desc limit {num:n} offset {offset:n} 

[getVideoOp]
type:query
[.@command]
sql : select id,threadid,fixed,topicid,opname,createtime,ext from worldcup_video order by createtime desc limit {num:n} offset {offset:n} 

[delVideoOp]
type:update
[.@command]
sql : delete from worldcup_video where id={id:n}

[updateVideoOp]
type:update
[.@command]
sql : update worldcup_video set threadid={threadid:n},topicid={topicid:n},opname={opname:s},fixed={fixed:n}  where id={id:n}

[getVideoCount]
type:query
[.@command]
sql : select count(id) as total from worldcup_video

[getVideoCountByCond]
type:query
[.@command]
sql : select count(id) as total from worldcup_video where {cond:r}

