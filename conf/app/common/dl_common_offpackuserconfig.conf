[dbConf]
dbRalName:DB_forum_content

# wangbingbing
[insertOffPackUserConfig]
type:update
[.@command]
sql : insert into online_user_config (cuid, remarks, status,create_time, modify_time) values ({cuid:s}, {remarks:s}, {status:n}, {create_time:n}, {modify_time:n})

[getOneUserConfigInfoByCuidAndStatus]
type:query
[.@command]
sql : select id, cuid, remarks, create_time, modify_time from online_user_config where cuid={cuid:s} and status = 1

[getOffPackUserConfigList]
type:query
[.@command]
sql : select id, cuid, remarks, create_time, modify_time from online_user_config where status = 1 order by create_time desc limit {limit:r}

[getUserConfigTotalNum]
type:query
[.@command]
sql : select count(*) as count from online_user_config where status=1

[delOneUserConfigByCuid]
type:update
[.@command]
sql : update online_user_config set status = 0 , modify_time = {modify_time:n} where cuid = {cuid:s}

[getOneUserConfigInfoByCuid]
type:query
[.@command]
sql : select id, cuid, remarks, status,create_time, modify_time from online_user_config where cuid={cuid:s} 

[updateUserConfigInfoByCuid]
type:update
[.@command]
sql : update online_user_config set remarks = {remarks:s}, status = {status:n} , create_time={create_time:n}, modify_time = {modify_time:n} where cuid = {cuid:s}

# yangtianzheng
[batchReplaceUserConfig]
type:batchInsert
[.@command]
sql : replace into online_user_config (cuid, remarks, status,create_time, modify_time) values ({cuid:s}, {remarks:s}, {status:n}, {create_time:n}, {modify_time:n})

[updateAllUserConfigStatus]
type:update
[.@command]
sql : update online_user_config set status = {status:n}, create_time={create_time:n}, modify_time={modify_time:n}