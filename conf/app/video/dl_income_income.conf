[insertC<PERSON>Inco<PERSON>]
type: update
[.@command]
sql: insert into creator_income (uid, income_month, income, valid_play, interact_param, ext_param, create_time, modify_time, commit_time, status) values ({uid:n}, {income_month:n}, {income:n}, {valid_play:n}, {interact_param:s}, {ext_param:s}, {create_time:n}, {modify_time:n}, {commit_time:n}, {status:n});

[updateCreatorIncomeIncomeExtParamWithUidMonth]
type: update
[.@command]
sql: update creator_income set income = {income:n}, ext_param = {ext_param:s}, modify_time = {modify_time:n}, status = {status:n} where uid = {uid:n} and income_month = {income_month:n} and status in {old_status:r};

[selectCreatorIncomeJoinVideoupInfoWithCond]
type: query
[.@command]
sql: select a.uid as uid, income_month, income, valid_play, interact_param, ext_param, a.status as status, author_type, vertical_field, paint_style, advise_field, auth_desc from creator_income as a join videoup_info as b on a.uid = b.uid where {cond:r} limit {limit:r};

[countCreatorIncomeJoinVideoupInfoWithCond]
type: query
[.@command]
sql: select count(*) as total from creator_income as a join videoup_info as b on a.uid = b.uid where {cond:r};

[updateCreatorIncomeStatusWithUidMonthOldStatus]
type: update
[.@command]
sql: update creator_income set status = {status:n}, modify_time = {modify_time:n} where uid = {uid:n} and income_month = {income_month:n} and {old_status:r} limit 1;
