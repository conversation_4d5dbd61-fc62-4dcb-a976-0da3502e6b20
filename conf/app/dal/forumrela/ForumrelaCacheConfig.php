<?php 
return array(
    'type'=>'zcache',

    'zcache'=>array(
        //产品线名称
        'product_name'=>'qa_tinyurl',

        //token
		'token'=>'qa_tinyurl_token',

        //连接超时，秒级
        'conn_timeout'=>1,

        //重试时间，秒级
        'retry_time'=>3,

        //MCPACK版本
        'mcpack_version'=>'PHP_MC_PACK_V1',

        //持续连接
        'persistent'=>0,

        //使用哪套agent server
		'current_conf'=>'jx',

        //agent server配置
        'zcache_agent_server'=>array(
            "jx" => array(
    					array(  
    						"socket_address" => "***********",
    						"socket_port" => 10240,
    						"socket_timeout" => 20,
    					),      
			),
        ),
    ),
    /*
    'memcached'=>array(
        //产品线名称
        'product_name'=>'tieba',

        //zookeeper节点路径
		'zk_path'=>'/baidu/ns/ksarch/cache',

        //更新zk数据的时间间隔
        'zk_expire'=>20,

        //当前所在的机房标志
        'curr_idc'=>1,

        //默认失效时间
        'default_expire'=>0,

        //delete时是否先直接去memcached中删除
        'delete_directly'=>true,

        //delete时是否发给acm延迟删除
		'delete_delay'=>true,
    
        //zookeeper机器地址
        'zk_host'=>array(
        	'************:2181',
        ),

    ),
    */
    'memcached'=>array(
        //产品线名称
        'product_name'=>'forum_forumrela',

        //zookeeper节点路径
		'zk_path'=>'/baidu/ns/ksarch/cache',

        //更新zk数据的时间间隔
        'zk_expire'=>20,

        //当前所在的机房标志
        'curr_idc'=>1,

        //默认失效时间
        'default_expire'=>0,

        //delete时是否先直接去memcached中删除
        'delete_directly'=>true,

        //delete时是否发给acm延迟删除
		'delete_delay'=>true,
    
        //zookeeper机器地址
        'zk_host'=>array(
        	'************:8181',
        	'*************:8181',
        	'***********:8181',
        	'***********:8181',
        	'************:8181',
        ),

    ),
    
    
);
