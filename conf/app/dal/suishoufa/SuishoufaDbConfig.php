<?php
//this file is auto builded by rpc-mis 
class SuishoufaDbConfig {      
   public static $arrAllMachine;
   public static $arrServerTalkConf;
   public static $arrDBAuth;   
}

SuishoufaDbConfig::$arrAllMachine = array (
  'jx' => 
  array (
 
    'Dal_Suishoufa' => 
    array (
             
       0 => 
      array (
        'host' => '**********',
        'port' => 4600,
      ),
      1 => 
      array (
        'host' => '**********',
        'port' => 4600,
      ),
2 => 
      array (
        'host' => '**********',
        'port' => 4600,
      ),
       3 => 
      array (
        'host' => '**********',
        'port' => 4600,
      ),
    ),

  ),
  'tc' => 
  array (
    'Dal_Suishoufa' => 
    array (
            
       0 => 
      array (
        'host' => '**********',
        'port' => 4600,
      ),
       1 => 
      array (
        'host' => '**********',
        'port' => 4600,
      ),
2 => 
      array (
        'host' => '**********',
        'port' => 4600,
      ),
       3 => 
      array (
        'host' => '**********',
        'port' => 4600,
      ),
     ),
  ),
);

SuishoufaDbConfig::$arrServerTalkConf = array (
    'Dal_Suishoufa' => 
  array (
    'connect_timeout_ms' => 100,
    'read_timeout_ms' => 500,
    'write_timeout_ms' => 500,
    'retry' => 2,
  ),

);
include_once (dirname(__FILE__)."/SuishoufaDbAuth.php");
