[BaseConfig]
#推荐物料类型 thread forum topic micro_video(手百微视频)
MaterType: thread
#唯一标识id字段名称，与浏览过滤id 以及 rank返回结构体id字段名称必须保持一致，如tid、fid、topic_id
IdFieldName : tid
#abtest平台注册task_id
AbtestTaskId : 20
#是否进行人工干预（需要人工干预平台开通支持）
NeedManual : 0
#初始数据加载读超时时间 默认300ms
#LoadBaseRtimeout : 300
#是否需要获取用户实时历史(需要传入rank)
NeedUserRealHistory : 1
#lite版本小流量
LiteAbtestTaskId : 23

#分层小流量实验配置，TagPrefix-小流量实验tag前缀，NormalPrefix-normal流量level场景前缀. add by fengzhen
[.@MultiAbtest]
#首页样式实验
AbTaskId : 22
TagPrefix: ui
NormalPrefix:
[.@MultiAbtest]
#首页图文recall
AbTaskId : 27
TagPrefix: tuwen
NormalPrefix:
[.@MultiAbtest]
#融合
AbTaskId: 17
TagPrefix: msd
NormalPrefix:msd
[.@MultiAbtest]
#首页图文rank
AbTaskId : 3
TagPrefix: tw
NormalPrefix:
[.@MultiAbtest]
#首页图文rank空跑
AbTaskId : 26
VPrefix: v
TagPrefix: tw
NormalPrefix:
[.@MultiAbtest]
#首页视频rank
AbTaskId: 18
TagPrefix: video
NormalPrefix:video
[.@MultiAbtest]
#视频中间页
AbTaskId: 20
TagPrefix: midpage
NormalPrefix:
[.@MultiAbtest]
#首页直播
AbTaskId: 21
TagPrefix: live
NormalPrefix:

[UserFeature]
#是否使用用户特征 1 使用 0 不使用
NeedUserFeature : 1
[.@Feature]
#特征字段名 tieba_user_base(贴吧用户特征)
FieldName: tieba_user_base
[.@Feature]
#特征字段名 tieba_user_base(贴吧用户特征)
FieldName: tieba_user_base_real

#callback配置
#[@Callback]
#Name: common
#召回比例
#DefaultRate : 200
#SwitchParamName : is_vertical
#SwitchParamValue : 0


#132404  横版item协同召回
#[.@CallbackNode]
#Service:recall
#Method : middlepageItemCF


#131911  横版优质召回
#[.@CallbackNode]
#Service:recall
#Method : middlepageNonVerticalFine
#MergeNotRand : 1

#source : 131018
#[.@CallbackNode]
#Service:recall
#Method : videoMicrVideoMcnRecall
#Number : 200
#MergeNotRand : 1

#[@Callback]
#Name: vertical
#DefaultRate :200
#SwitchParamName: is_vertical
#SwitchParamValue : 1

#132405  竖版item协同召回
#[.@CallbackNode]
#Service:recall
#Method : middlepageItemCF

#131911  竖版优质内容召回
#[.@CallbackNode]
#Service:recall
#Method : middlepageVerticalFine
#MergeNotRand : 1

#rank 配置
[RankConfig]
# 多个rank返回结果合并方式 1 保持比例 2 数据不足其他数据源补充
MergeType : 1
#召回数据源百分比，如存在过滤按照100%召回，可能会导致过滤后数据不足
ResNumRate : 200
[.@Rank]
Name : common
#对应callback召回数据源
CallbackNode : common
#merge数据占比例范围 1-100
MergeRate : 100
#是否需要调用rank
NeedRank : 1
Service : recstg
Method : dealMidVideoRank
#是否为merge数据核心数据源,MergeType = 1返回数据不足的情况下，其他数据源按照这个数据条数计算相应比例返回
Primary : 1
#根据字段选择使用召回队列list
CallbackSwitchParam : is_vertical
[..@Switch]
ParamValue : 0
SwitchCallbackNode : common
[..@Switch]
ParamValue : 1
SwitchCallbackNode : vertical

#浏览过滤配置
[FilterConfig]
#是否需要进行浏览过滤
NeedFilter : 1
MaskCallFrom : client_index
#过滤字段名称，rank返回结构体字段名称一致
[.@Filter]
Field : tid
[.@Filter]
Field: sign


[.@Filter]
Field: video_sign
MaskField: sign
#NoMaskAbTag : video_tag_134
