[addStrategyClassify]
type: update
[.@command]
sql: insert into `spammis_monitor_index` (name, remark, business_id, group_id, parent, op_uid, op_username, op_time, creator_id, creator, create_time) values({name:s}, {remark:s}, {business_id:n}, {group_id:n}, {parent:n}, {op_uid:n}, {op_username:s}, {op_time:n}, {creator_id:n}, {creator:s}, {create_time:n})

[getStrategyClassifyInfo]
type: query
[.@command]
sql: select * from `spammis_monitor_index` where {cond:r} limit 1

[updateStrategyClassify]
type: update
[.@command]
sql: update `spammis_monitor_index` set name={classify_name:s}, remark={remark:s}, op_uid={op_uid:n}, op_username={op_username:s}, op_time={op_time:n} where index_id={index_id:n} and status=1

[removeStrategyClassify]
type: update
[.@command]
sql: update `spammis_monitor_index` set status={status:n}, op_uid={op_uid:n}, op_username={op_username:s}, op_time={op_time:n} where index_id={index_id:n} and status=1

[removeStrategyClassifyAndChild]
type: update
[.@command]
sql: update `spammis_monitor_index` set status={status:n}, op_uid={op_uid:n}, op_username={op_username:s}, op_time={op_time:n} where index_id={index_id:n} or parent={index_id:n} and status=1

[getStrategyClassifyList]
type: query
[.@command]
sql: select index_id, business_id, group_id, parent, name, remark, creator, op_username, op_time from `spammis_monitor_index` where {cond:r} order by index_id desc limit {offset:n},{limit:n}
[.@command]
sql: select count(1) from `spammis_monitor_index` where {cond:r}
