[default]
type : 10m
cur_type : 10s
value : qps
data_type : each_second
interval : 3600

[module]
[.mo_client_frs]
	[..subapp_type]
		[...subapp_type]
			module : tieba|mo_client_frs|client_frs
			key : subapp_type
			status : 1
			min : -0.3
			max : 0.3
			[....black]
				0 : subapp_type@tieba
		[...kuang_baiduapp_ios]
        	module : tieba|mo_client_frs|client_frs
        	key : subapp_type@kuang|from@baiduapp|client_type@ios
        	status : 1
        	min : -0.3
        	max : 0.3
        [...kuang_baiduapp_android]
            module : tieba|mo_client_frs|client_frs
            key : subapp_type@kuang|from@baiduapp|client_type@android
            status : 1
            min : -0.3
            max : 0.3
		[...kuang]
			module : tieba|mo_client_frs|client_frs
			key : subapp_type@kuang
			status : 1
			min : -0.3
			max : 0.3
		[...SDK_sdk_as]
			module : tieba|mo_client_frs|client_frs
			key : subapp_type@SDK|from@sdk_as|client_type@android
			status : 1
			min : -0.3
			max : 0.3
		[...SDK_91app_sdk]
        	module : tieba|mo_client_frs|client_frs
        	key : subapp_type@SDK|from@91app_sdk|client_type@android
        	status : 1
        	min : -0.3
        	max : 0.3
        [...SDK_BDGameSDK]
            module : tieba|mo_client_frs|client_frs
            key : subapp_type@SDK|from@BDGameSDK|client_type@android
            status : 1
            min : -0.3
            max : 0.3
        [...SDK_sdk_bv]
            module : tieba|mo_client_frs|client_frs
            key : subapp_type@SDK|from@sdk_bv|client_type@android
            status : 1
            min : -0.3
            max : 0.3
[.mo_client_pb]
	[..subapp_type]
		[...subapp_type]
			module : tieba|mo_client_pb|client_pb
			key : subapp_type
			status : 1
			min : -0.3
			max : 0.3
			[....black]
				0 : subapp_type@tieba
		[...kuang_baiduapp_ios]
        	module : tieba|mo_client_pb|client_pb
        	key : subapp_type@kuang|from@baiduapp|client_type@ios
        	status : 1
        	min : -0.3
        	max : 0.3
        [...kuang_baiduapp_android]
            module : tieba|mo_client_pb|client_pb
            key : subapp_type@kuang|from@baiduapp|client_type@android
            status : 1
            min : -0.3
            max : 0.3
		[...kuang]
			module : tieba|mo_client_pb|client_pb
			key : subapp_type@kuang
			status : 1
			min : -0.3
			max : 0.3
		[...SDK_sdk_as]
			module : tieba|mo_client_pb|client_pb
			key : subapp_type@SDK|from@sdk_as|client_type@android
			status : 1
			min : -0.3
			max : 0.3
		[...SDK_91app_sdk]
        	module : tieba|mo_client_pb|client_pb
        	key : subapp_type@SDK|from@91app_sdk|client_type@android
        	status : 1
        	min : -0.3
        	max : 0.3
        [...SDK_BDGameSDK]
            module : tieba|mo_client_pb|client_pb
            key : subapp_type@SDK|from@BDGameSDK|client_type@android
            status : 1
            min : -0.3
            max : 0.3
        [...SDK_sdk_bv]
            module : tieba|mo_client_pb|client_pb
            key : subapp_type@SDK|from@sdk_bv|client_type@android
            status : 1
            min : -0.3
            max : 0.3

[user]
#haoyunfeng : 18612012419
xiashanshan : 18510923533
#fanjunmei : 18613802682
#leizhihai : 15001299067
#zhaoboliang : 15652307634
#zhangmingyue01 : 18513400090
#weijiangmin : 18717910850
[message]
up : flow_rised_than_last_week.
down : flow_dropped_than_last_week.
[alarm]
num : 20
type : 1
