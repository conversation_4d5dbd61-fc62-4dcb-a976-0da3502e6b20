<?php
class RuleRouterControllerConfig 
{
    public static $config = array();
}
/**
 * ��̬·��
 */
RuleRouterControllerConfig::$config['staticMapping'] = array(
	'commit'=>array(
	    'cmActionChain',
	    FRAMEWORK_ACTIONS_PATH.'/ActionChain.class.php',
	    array(
	     	array(
				'ActionProduct',
				ITIEBA_UI_PATH . '/submit/ActionProduct.class.php',
				null,
			),   
			array(
	            'ActionCmInit',
	            ITIEBA_UI_PATH . '/submit/ActionCmInit.class.php',
	            NULL,
	        ),
	        array(
	            'ActionCmUser',
	            ITIEBA_UI_PATH . '/submit/ActionCmUser.class.php',
	            NULL,
			),
			// array(
			// 	'ActionCmActs',
			// 	ITIEBA_UI_PATH . '/submit/ActionCmActs.class.php',
			// 	NULL,	
			// ),
			array(
				'ActionCmProcess',
				ITIEBA_UI_PATH . '/submit/ActionCmProcess.class.php',
				NULL,	
			),
			array(
				'ActionBuildJson',
				ITIEBA_UI_PATH . '/submit/ActionBuildJson.class.php',
				NULL,	
			),
	    ),
	),
	'submit/add_user_favoforum' => array(
                'cmActionChain',
            FRAMEWORK_ACTIONS_PATH.'/ActionChain.class.php',
            array(
                array(
                                'begin',
                                ITIEBA_UI2_PATH . '/action/ActionBegin.class.php',
                                null,
                        ),
                array(
                                'submit',
                                ITIEBA_UI2_PATH . '/action/ActionAddUserForum.class.php',
                                null,
                        ),
                ),
        ),
       
        'submit/del_concernforum' => array(
                'cmActionChain',
            FRAMEWORK_ACTIONS_PATH.'/ActionChain.class.php',
            array(
                array(
                                'begin',
                                ITIEBA_UI2_PATH . '/action/ActionBegin.class.php',
                                null,
                        ),
                array(
                                'submit',
                                ITIEBA_UI2_PATH . '/action/ActionSubmitDelConcernForum.class.php',
                                null,
                        ),
                ),
        ),
        'submit/cancel_storethread' => array(
                'cmActionChain',
            FRAMEWORK_ACTIONS_PATH.'/ActionChain.class.php',
            array(
                array(
                                'begin',
                                ITIEBA_UI2_PATH . '/action/ActionBegin.class.php',
                                null,
                        ),
                array(
                                'submit',
                                ITIEBA_UI2_PATH . '/action/ActionSubmitCancelStoreThread.class.php',
                                null,
                        ),
                ),
        ),
        
        'submit/open_storethread' => array(
                'cmActionChain',
            FRAMEWORK_ACTIONS_PATH.'/ActionChain.class.php',
            array(
                array(
                                'begin',
                                ITIEBA_UI2_PATH . '/action/ActionBegin.class.php',
                                null,
                        ),
                array(
                                'submit',
                                ITIEBA_UI2_PATH . '/action/ActionSubmitOpenStoreThread.class.php',
                                null,
                        ),
                ),
        ),
        
        'submit/read_storethread' => array(
                'cmActionChain',
            FRAMEWORK_ACTIONS_PATH.'/ActionChain.class.php',
            array(
                array(
                                'begin',
                                ITIEBA_UI2_PATH . '/action/ActionBegin.class.php',
                                null,
                        ),
                array(
                                'submit',
                                ITIEBA_UI2_PATH . '/action/ActionSubmitReadStoreThread.class.php',
                                null,
                        ),
                ),
        ),
);
/**
 * ·�ɹ���
 */
RuleRouterControllerConfig::$config['rules'] = array(
	'ui2_sys' => array( // ������������ 
		'rule' => 'sys/:router',
		':router' => array('jump','jump2', 'creiid'),
	),
	'sys' => array( // ������������ user_json 
		'rule' => 'sys/:router',
	),	
	'ui2_1' => array( // ���������δ��
		'rule' => ':out_id',
	),	
	'ui2_2' => array( //�����ӿ�
		'rule' => ':out_id/:router',
		':router' => array(
			'main','profile','set_sign','replyme','atme','storethread','friendapply',
			'my_tie','my_reply','feature','fans','concern','forum',
    		'one','thread','reply',//'bind_phone','invite','vote', 'recycle','album','light',
			'action_one_xml','wap_my_like', 'wap_goodthreadfeed_xml','bdbrs','others',
		),
    ),    
	'ui2_data' => array( //�����ӿ�
		'rule' => 'data/:router',
		':router' => array(
			'user_token','panel','thread_by_userpost','allfeed_by_userpost','reply_by_userpost',
			'public_itb_fans','public_itb_follow','public_itb_info','public_itb_outline','public_itb_timeline','public_itb_user_relation','public_user_honour',
			'get_fid_by_fname',
		),
	),
    'browse2' => array(
		'rule' => ':out_id/:router',
	),

);
/**
 * ·��������
 */
RuleRouterControllerConfig::$config['routerConfig'] = array(
    'beginRouterIndex'=>1,//��ʼ�����index
	'sepOfRouter'=>'/',
    'sepOfParams'=>'-',
    'sepOfRouterAndParams'=>'-',
    'endOfParams'=>'.',//���鲻Ҫ�޸�
);
/**
 * map
 */
RuleRouterControllerConfig::$config['allMapping'] = array(
	'ui2_sys' => array(
        'ActionChain',
	    FRAMEWORK_ACTIONS_PATH.'/ActionChain.class.php',
	    array(
	        array(
	            'begin',
	            ITIEBA_UI2_PATH . '/action/ActionBegin.class.php',
	            NULL,
			),
	        array(
	            'data',
	            ITIEBA_UI2_PATH . '/action/ActionData.class.php',
	            NULL,
	        ),
	        array(
	            'page',
	            ITIEBA_UI2_PATH . '/action/ActionPage.class.php',
	            NULL,
	        ),
	    ),
	),
    'ui2browse' => array(
        'ActionChain',
	    FRAMEWORK_ACTIONS_PATH.'/ActionChain.class.php',
	    array(
	        array(
	            'begin',
	            ITIEBA_UI2_PATH . '/action/ActionBegin.class.php',
	            NULL,
			),
	        array(
	            'itb',
	            ITIEBA_UI2_PATH . '/action/ActionItb.class.php',
	            NULL,
			),
	        array(
	            'data',
	            ITIEBA_UI2_PATH . '/action/ActionData.class.php',
	            NULL,
	        ),
	        array(
	            'page',
	            ITIEBA_UI2_PATH . '/action/ActionPage.class.php',
	            NULL,
	        ),
	    ),
	),
    'browse' => array(
        'ActionChain',
	    FRAMEWORK_ACTIONS_PATH.'/ActionChain.class.php',
	    array(
	        array(
	            'common',
	            ITIEBA_UI_PATH . '/common/ActionCommon.class.php',
	            NULL,
			),
	        array(
	            'itieba',
	            ITIEBA_UI_PATH . '/common/ActionItieba.class.php',
	            NULL,
	        ),
	        array(
	            'framework',
	            ITIEBA_UI_PATH . '/common/ActionFramework.class.php',
	            NULL,
	        ),
	        array(
	            'pagebuilder',
	            ITIEBA_UI_PATH . '/common/ActionPageBuilder.class.php',
	            NULL,
	        ),
	    ),
	),
	'sys' => array(
        'ActionChain',
	    FRAMEWORK_ACTIONS_PATH.'/ActionChain.class.php',
	    array(
	        array(
	            'common',
	            ITIEBA_UI_PATH . '/common/ActionCommon.class.php',
	            NULL,
			),
	        array(
	            'framework',
	            ITIEBA_UI_PATH . '/common/ActionFramework.class.php',
	            NULL,
	        ),
	        array(
	            'pagebuilder',
	            ITIEBA_UI_PATH . '/common/ActionPageBuilder.class.php',
	            NULL,
	        ),
	    ),
	),
);
/**
 * router=>Action��map
 */
RuleRouterControllerConfig::$config['ruleMapping'] = array(
	'browse1' => RuleRouterControllerConfig::$config['allMapping']['browse'],
	'browse2' => RuleRouterControllerConfig::$config['allMapping']['browse'],
	'sys' => RuleRouterControllerConfig::$config['allMapping']['sys'],
	'ui2_1' => RuleRouterControllerConfig::$config['allMapping']['ui2browse'],
	'ui2_2' => RuleRouterControllerConfig::$config['allMapping']['ui2browse'],
	'ui2_sys' => RuleRouterControllerConfig::$config['allMapping']['ui2_sys'],
	'ui2_data' => RuleRouterControllerConfig::$config['allMapping']['ui2_sys'],
);
/**
 * Ĭ�ϴ���������Ӧ����routerΪ�յ����
 */
RuleRouterControllerConfig::$config['defaultAction'] = array(
	'default',
	ITIEBA_UI_PATH . '/ActionDefault.class.php',
	NULL
);
/**
 * �������������Ҳ�����router���ڵ�Action��ʱ�򣬵��á�
 */
RuleRouterControllerConfig::$config['notFoundAction'] = array(
	'default',
	ITIEBA_UI_PATH . '/ActionDefault.class.php',
	NULL
);
