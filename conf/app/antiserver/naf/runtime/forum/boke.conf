#框架的配置，或所有组件共享的配置
input_encoding:gbk
output_encoding:utf-8
debug_keep_day:8

#组件自己的配置
[component]
    [.Naf_Component_System]
        ral_sync : 0
        # modify for hhvm , post to inrouter instand of self
        async_callback_service : service_antiserver
        #async_callback_service : SELF
        async_delay : 2
        [..Redis]
            uname : forum
            tk : forum
            service : ueganti
            expire : 7200
            bdrp: 1

    [.Naf_Component_Dict] 
        [..Redis]
            uname : forum 
            tk : forum
            key_sep : "#"
            service : Redis_wordserver_forum_ueg_wordlist

        [..Confilter]
            pid : forum
            tk : forum
            en : utf-8
            service : Confilter
            max_batch : 60

        [..tb_confilter]
             pid : forum
             tk : forum
             en : utf-8
             service : tb_confilter
             max_batch : 60

        [..vocabulary]
             service : vocabulary_search
             max_batch : 50

        [..pandora_wordfilter]
             service : new_wordfilter
             max_batch : 50

    [.Naf_Component_Counter]
        uname : forum
        tk : forum
        service : Redis_forum_anti
        key_sep : "#"
        maxLinkSize : 300
        get_history : 0

    [.Naf_Component_Actsctrl]
        uname : forum
        tk : forum
        service : Redis_forum_anti
        key_sep : "#"
        maxLinkSize : 1000

    [.Naf_Component_Range]
        [..Redis]
            uname : forum
            tk : forum
            service : Redis_forum_anti

	[.Naf_Component_Forum_Dealer]

        [..tableInfo]

            [...debug_monitor]
                partition: time
                partition_param : antiCommitTime
                maxTableNum : 14
                createSql : "CREATE TABLE IF NOT EXISTS `$table` (`auto_id` bigint(20) NOT NULL AUTO_INCREMENT,`uniq_key` varchar(64) NOT NULL,`logid` bigint(20) NOT NULL,`cmd_no` int(11) NOT NULL,`thread_id` bigint(20) DEFAULT NULL,`post_id` bigint(20) DEFAULT NULL,`uid` bigint(11) NOT NULL,`fid` int(11) DEFAULT NULL, op_uid bigint(11) DEFAULT NULL, op_uname varchar(64) default '', op_status int(11) DEFAULT NULL, `monitor_type` int(11) NOT NULL,`monitor_node` varchar(255) NOT NULL,`monitor_list` varchar(255) DEFAULT NULL,`extra` varchar(4096),`create_time` bigint NOT NULL, PRIMARY KEY (`auto_id`),KEY `uniq_idx` (`uniq_key`), KEY `logid_idx` (`logid`),KEY `threadid_idx` (`thread_id`), KEY `postid_idx` (`post_id`),KEY `monitor_idx` (`monitor_type`,`create_time`), KEY `node_idx` (`monitor_node`), KEY `multi_uid_time_idx` (`uid`,`create_time`)) ENGINE=InnoDB DEFAULT CHARSET=utf8;"

        [..SqlTemplate]
            [...debugMonitorInsert]
                sql_pattern : insert ignore into $table(uniq_key, logid, cmd_no, thread_id, uid, fid, op_uid, op_uname, op_status, monitor_type, monitor_node, monitor_list, extra, create_time) values(?,?,?,?,?,?,?,?,?,?,?,?,?,UNIX_TIMESTAMP(now()))
                @sql_param : primarykey
                @sql_param : log_id
                @sql_param : commandNo
                @sql_param : threadId
                @sql_param : userId
                @sql_param : forumId
                @sql_param : op_uid
                @sql_param : op_uname
                @sql_param : op_status
                @sql_param : antiserver_monitor_type
                @sql_param : antiserver_monitor_node
                @sql_param : antiserver_monitor_list
                @sql_param : extra

            [...monitorInsert]
                sql_pattern : insert ignore into $table(topic, ip, post_type, forum, forum_id, thread_id, post_id, title, user, user_id, monitor_type, create_time, content,grade,monitor_list,op_time, op_uid, op_username,image_md5,extra,monitor_node,con_reason) values(?,?,?,?,?,?,?,?,?,?,?,FROM_UNIXTIME(?),?,?,?,now(),?,?,?,?,?,?)
                @sql_param : commandNo
                @sql_param : ip
                @sql_param : postType
                @sql_param : forumName
                @sql_param : forumId
                @sql_param : threadId
                @sql_param : postId
                @sql_param : title
                @sql_param : userName
                @sql_param : userId
                @sql_param : antiserver_monitor_type
                @sql_param : antiCommitTime
                @sql_param : content
                @sql_param : grade
                @sql_param : antiserver_monitor_list
                @sql_param : op_uid
                @sql_param : op_uname
                @sql_param : spaceImgSign
                @sql_param : extra
                @sql_param : antiserver_monitor_node
                @sql_param : reason
        [..Dealer]
            [...debugMonitorDealer]
            # 默认空dealer，不做实际操作
            [...defaultDealer]
            [...bokeAudioSuccCallbackDealer]
                [....Poster]
                    @post :bokeAudioSuccCallbackDealer
            [...bokeAudioErrorCallbackDealer]
                [....Poster]
                    @post :bokeAudioErrorCallbackDealer