[dbConf]
dbRalName:DB_forum_content

[addIncomeReport]
type: update
use_transaction : 0
[.@command]
sql : insert into income_report(import_date, report_date, ad_type, classify, sep_before_income, sep_after_income, create_time, op_uid, op_name) values({import_date:n}, {report_date:n}, {ad_type:s}, {classify:s}, {sep_before_income:r}, {sep_after_income:r}, {create_time:n}, {op_uid:n}, {op_name:s})

[getIncomeReportByCond]
type: query
[.@command]
sql : select id, import_date, report_date, ad_type, classify, sep_before_income, sep_after_income, create_time, op_uid, op_name from income_report where {condition:r} limit {offset:n}, {size:n}

[dropIncomByCond]
type: query
[.@command]
sql : delete from income_report where {condition:r}