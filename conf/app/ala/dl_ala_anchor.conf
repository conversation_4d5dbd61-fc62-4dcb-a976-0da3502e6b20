[getAllTails]
type: query
[.@command]
sql: select tail_id, tail_name, parent_tail_name from mis_live_tail_info

[getAuthAnchorList]
type: query
[.@command]
sql: select detail.id, detail.create_time, auth.user_id, auth.user_name, auth.audit_reason, auth.final_op_time, anchor.union_id, detail.tail_name, detail.parent_tail_name, detail.operator, detail.update_time from authentication_info auth left join amis_live_union_anchor anchor on anchor.anchor_user_id = auth.user_id and anchor.anchor_user_id > 0 and anchor.status = 1 left join mis_live_tail_detail detail on detail.user_id = auth.user_id and detail.user_id > 0 where {conditions:r} order by auth.final_commit_time desc {appends:r}

[getAuthAnchorCount]
type: query
[.@command]
sql: select count(1) as num from authentication_info auth left join amis_live_union_anchor anchor on anchor.anchor_user_id = auth.user_id and anchor.anchor_user_id > 0 left join mis_live_tail_detail detail on detail.user_id = auth.user_id and detail.user_id > 0 where {conditions:r}

[getAuthAnchorCountV2]
type: query
[.@command]
sql: select count(1) as num from authentication_info

[getAllUnions]
type: query
[.@command]
sql: select union_id, union_name from amis_live_union

[getTailForUserIds]
type: query
[.@command]
sql: select id, tail_name, parent_tail_name, user_id, create_time, forum_id, dir2, ext_attr from mis_live_tail_detail where user_id in ({user_ids:r})

[updateTailForUserId]
type: update
[.@command]
sql: update mis_live_tail_detail set tail_name = {tail_name:s}, parent_tail_name = {parent_tail_name:s}, operator = {operator:s} where id = {id:n} and user_id = {user_id:n}

[updateTailForId]
type: update
[.@command]
sql: update mis_live_tail_detail set tail_name = {tail_name:s}, parent_tail_name = {parent_tail_name:s}, operator = {operator:s} where id = {id:n}

[getUserInfoByIds]
type: query
[.@command]
sql: select user_id from authentication_info where user_id in ({user_ids:r})

[deleteTailForUserId]
type: update
[.@command]
sql: update mis_live_tail_detail set operator = {operator:s}, user_id = 0 where id = {id:n} and user_id = {user_id:n}

[deleteRecordForUserId]
type: update
[.@command]
sql: delete from mis_live_tail_detail where id = {id:n} and user_id = {user_id:n}

[amisAddUnionAnchorChain]
type: update
[.@command]
sql: insert into {table_name:r} (union_id, anchor_user_id, start_time, end_time) values ({union_id:n}, {anchor_user_id:n}, {start_time:n}, {end_time:n})

[amisDelAnchorChain]
type: update
[.@command]
sql: delete from {table_name:r} where id = {id:n}

[amisDelUnionAnchorChain]
type: update
[.@command]
sql: update {table_name:r} set end_time = {end_time:n} where id = {id:n}

[amisGetNearestUnionAnchorChain]
type: query
[.@command]
sql: select id, union_id, anchor_user_id, start_time, end_time from {table_name:r} where anchor_user_id = {anchor_user_id:n} and union_id = {union_id:n}  order by start_time desc limit 1

[amisGetUnionAnchorChainByTime]
type: query
[.@command]
sql: select union_id, anchor_user_id, start_time, end_time from {table_name:r} where union_id = {union_id:n} and end_time >= {time:n} and start_time <= {time:n} union all select union_id, anchor_user_id, start_time, end_time from {table_name:r} where union_id = {union_id:n} and end_time = 0 and start_time <= {time:n}

[amisGetAnchorChainByTime]
type: query
[.@command]
sql: select union_id, anchor_user_id, start_time, end_time from {table_name:r} where anchor_user_id = {anchor_user_id:n} and end_time >= {time:n} and start_time <= {time:n} union  select union_id, anchor_user_id, start_time, end_time from {table_name:r} where anchor_user_id = {anchor_user_id:n} and end_time = 0 and start_time <= {time:n}

[amisGetUnionAnchorChainByPeriod]
type: query
[.@command]
sql: select union_id, anchor_user_id, start_time, end_time from {table_name:r} where union_id = {union_id:n} and end_time > {start_time:n} and start_time < {end_time:n}

[amisGetStillInUnionAnchorChainByPeriod]
type: query
[.@command]
sql: select union_id, anchor_user_id, start_time, end_time from {table_name:r} where union_id = {union_id:n} and end_time = 0 and start_time < {end_time:n}

[getUserIdsWhoIsInUnion]
type: query
[.@command]
sql: select anchor_user_id as user_id from amis_live_union_anchor where anchor_user_id in ({str_user_ids:r}) and status = 1

[getAnchorUnionIdFromTable]
type: query
[.@command]
sql: select * from {table_name:r} where anchor_user_id = {anchor_user_id:n} and status = 1

[getAnchorUnionCreateUserIdFromTable]
type: query
[.@command]
sql: select * from {table_name:r} where union_id = {union_id:n}

[getAnchorList]
type: query
[.@command]
sql: select anchor_user_id from {table_name:r} where status = 1

[getStartNoticeStatus]
type: query
[.@command]
sql: select user_id, anchor_id from {table_name:r} where user_id = {user_id:n} {condition:r}

[getStartNoticeUserByAnchorId]
type: query
[.@command]
sql: select user_id from {table_name:r} where anchor_id = {anchor_id:n}

[insertStartNoticeStatus]
type: update
[.@command]
sql: insert into {table_name:r} (user_id, anchor_id, update_time) values ({user_id:n}, {anchor_id:n}, {update_time:n}) on duplicate key update update_time={update_time:n}

[deleteStartNoticeStatus]
type: update
[.@command]
sql: delete from {table_name:r} where user_id = {user_id:n} and anchor_id = {anchor_id:n}

[updateSubscriptionInfo]
type: update
[.@command]
sql: insert into {table_name:r} (user_id, update_time, check_status, open_status) values ({user_id:n}, {update_time:n}, {check_status:n}, {open_status:n}) on duplicate key update update_time={update_time:n}, open_status={open_status:n}, check_status={check_status:n}

[getSubscriptionInfo]
type: query
[.@command]
sql: select user_id, update_time, open_status, check_status from {table_name:r} where user_id = {user_id:n}

[getStartNoticeList]
type: query
[.@command]
sql: select id, user_id, anchor_id from sdk_shoubai_start_notice limit {offset:n},{limit:n}

[selectUserIdByStatus]
type:query
[.@command]
sql : select {condition:r} from live_challenge where create_time >= {start_time:n} and create_time < {end_time:n} and challenge_type = {challenge_type:n} and challenge_status = 5
