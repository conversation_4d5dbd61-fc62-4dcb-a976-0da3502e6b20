<?php
	require_once (CONF_PATH."/SpamMenuConfig.class.php");
	require_once (CONF_PATH."/MisConfig.class.php");
	require_once (CONF_PATH."/MisMenuConfig.class.php");
	require_once (CONF_PATH."/MisDbConfig.class.php");
	require_once (CONF_PATH."/WordListConfig.class.php");
	require_once (CONF_PATH."/TransferConfig.class.php");
	require_once (LIB_PATH."/log/ub_log.php");
	class LogConfig
	{
	    const RPC = 0;
	    const MIS = 1;
	    public static $arrConfig = array();
	}

	LogConfig::$arrConfig =  array( 
	    LogConfig::RPC => array(    
	        'file' => MIS_LOG_PATH . "/rpc.log",
	        'level'    =>0x11111111,
	    ), 
	    LogConfig::MIS => array(
	        'file'    =>  MIS_LOG_PATH . "/ui.log",
	        'level'    =>0x11111111,
	    ), 
	);
	registerLog (LogConfig :: $arrConfig);

	Rpc::registerLogger(getLogObject(LogConfig::RPC));	
	TbMisLog::setDefaultLogModule(LogConfig::MIS);

	Bingo_Log::init(LogConfig :: $arrConfig[LogConfig::MIS],LogConfig::MIS);
	Bingo_Log::warning(serialize($ret));//$ret in file MisDbConfig.class.php
	Bingo_Log::getModule()->flush();


	//data filter default
	function stripslashes_deep($value){
	    $value = is_array($value) ? array_map('stripslashes_deep', $value) :stripslashes($value);
	    return $value;
	}
	if (get_magic_quotes_gpc()) {
	    $_POST      = array_map( 'stripslashes_deep', $_POST );
	    $_GET       = array_map( 'stripslashes_deep', $_GET );
	    $_COOKIE    = array_map( 'stripslashes_deep', $_COOKIE );
	    $_REQUEST   = array_map( 'stripslashes_deep', $_REQUEST );    
	}
	//Smarty
	$smarty = new Smarty();	    		
	$smarty->template_dir = MIS_TEMPLATES_PATH; 
	$smarty->compile_dir  = MIS_TEMPLATES_PATH .'/templates_c/'; 
	$smarty->config_dir   = MIS_TEMPLATES_PATH . '/config/'; 
	$smarty->cache_dir    = MIS_TEMPLATES_PATH . '/cache/'; 
	$smarty->caching = false; 
	$smarty->left_delimiter = '<?'; 
	$smarty->right_delimiter = '?>';
	$smarty->plugins_dir = array(MIS_TEMPLATES_PLUGIN_PATH,MIS_TEMPLATES_PATH.'/plugins', 'plugins');
	//$smarty->debugging = true;	
	TbMisView::setupSmarty($smarty);
	TbMisView::assign('mis_feedback','');
