/* 2012.5.17 add by hlx */
/* DROP DATABASE IF EXISTS forum_pop; */

CREATE DATABASE IF NOT EXISTS forum_pop;

USE forum_pop;

/* ��¼�û��ĵ���״̬�� */
create table if not exists login_userinfo_1
(
	id	int unsigned not null AUTO_INCREMENT,
    uid         int unsigned not null comment '�û�id',
    flag      int unsigned not null default 0 comment '��ʾλ',
    count         int unsigned not null default 0 comment '��������',   
    primary key (id),
    unique key (uid)
)engine=InnoDB DEFAULT CHARSET=gbk;

/* �����ֱ� */
CREATE TABLE login_userinfo_1_0 LIKE login_userinfo_1;
CREATE TABLE login_userinfo_1_1 LIKE login_userinfo_1;
CREATE TABLE login_userinfo_1_2 LIKE login_userinfo_1;
CREATE TABLE login_userinfo_1_3 LIKE login_userinfo_1;

/* �ֶ������������Ϣ�� */
create table if not exists task_info_1
(
	id	int unsigned not null AUTO_INCREMENT,
    uid         int unsigned not null comment '�û�id',
    ipt_un      varchar(32) comment '�û���',
    ipt_role         varchar(32) comment '�û���ݣ�������С����������',
    ipt_grade         int unsigned comment '�ȼ�',
    ipt_ranking         int unsigned comment '�û�����',
    ipt_fn         varchar(32) comment '����',
    ipt_zone         varchar(32) comment '�û����ڵ���',
    ipt_fdir         varchar(32) comment '�û����ڷ���',
    ipt_thread         varchar(128) comment '�û������֪����',
    ipt_1         varchar(32) comment '����1',
    ipt_2         varchar(32) comment '����2',
    ipt_3         varchar(32) comment '����3',   
    primary key (id),
    unique key (uid)
)engine=InnoDB DEFAULT CHARSET=gbk;
