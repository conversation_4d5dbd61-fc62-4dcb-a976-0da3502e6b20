/**
 * 保存客户端游戏中心里游戏操作的各种状态
 * @authors tanxinyun
 * @date    2015-10-27 12:01:07
 * @version $Id$
 */

use forum_gamecenter;
CREATE TABLE `user_game_status` (    
  `id` bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `user_id` bigint(10) unsigned NOT NULL COMMENT '用户id',
  `game_id` bigint(12) unsigned NOT NULL COMMENT '游戏id',
  `status` tinyint(10) unsigned NOT NULL COMMENT '操作状态：1-开始下载；2-下载完成；3-下载后直接删除；4-安装；5-打开游戏；6-删除游戏',
  `update_time` int(10) unsigned NOT NULL default 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ugs` (`user_id`,`game_id`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT '用户对游戏进行操作的各种状态';