a:1:{s:5:"trunk";a:7:{s:3:"url";s:19:"c/e/mema/getpayinfo";s:11:"description";s:53:"查询会员支付接口（andriod专用）cmd:306002";s:6:"notice";N;s:3:"req";a:1:{i:0;a:4:{i:0;s:6:"pay_id";i:1;s:8:"uint32_t";i:2;s:12:"订单编号";i:3;s:4:"true";}}s:3:"res";s:1003:"{#*<font color=006699 size=4><b>errno&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>int32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;//错误号</b></font>#*<font color=006699 size=4><b>errmsg&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//错误信息</b></font>#*<font color=006699 size=4><b>usermsg&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//给用户提示信息</b></font>#*<font color=006699 size=4><b>pay_status&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//订单结果  0 订单成功, 1 订单失败, 2 订单通知未返回,  3 没有这个订单</b></font>#}#";s:6:"author";a:2:{i:0;s:9:"redfox241";i:1;N;}s:4:"fake";a:4:{s:5:"errno";i:4;s:6:"errmsg";s:11:"a string 38";s:7:"usermsg";s:11:"a string 42";s:10:"pay_status";i:48;}}}