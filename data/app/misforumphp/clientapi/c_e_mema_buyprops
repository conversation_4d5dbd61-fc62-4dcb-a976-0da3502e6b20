a:1:{s:5:"trunk";a:7:{s:3:"url";s:17:"c/e/mema/buyprops";s:11:"description";s:37:"会员支付接口（andriod专用）";s:6:"notice";N;s:3:"req";a:5:{i:0;a:4:{i:0;s:8:"pay_type";i:1;s:8:"uint32_t";i:2;s:15:"1 会员 2 T豆";i:3;s:4:"true";}i:1;a:4:{i:0;s:8:"props_id";i:1;s:8:"uint32_t";i:2;s:30:"会员道具id 1050001 1050002";i:3;s:4:"true";}i:2;a:4:{i:0;s:8:"quan_num";i:1;s:8:"uint32_t";i:2;s:27:"点券数据量(单位:元)";i:3;s:4:"true";}i:3;a:4:{i:0;s:7:"is_left";i:1;s:8:"uint32_t";i:2;s:39:"是否使用余额,0 不使用 1 使用";i:3;s:5:"false";}i:4;a:4:{i:0;s:9:"props_mon";i:1;s:8:"uint32_t";i:2;s:18:"道具购买月数";i:3;s:4:"true";}}s:3:"res";s:4522:"{#*<font color=006699 size=4><b>errno&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>int32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;//错误号</b></font>#*<font color=006699 size=4><b>errmsg&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//错误信息</b></font>#*<font color=006699 size=4><b>usermsg&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//给用户提示信息</b></font>#*<font color=006699 size=4><b>pay_info</b></font>:{&nbsp;&nbsp;&nbsp;&nbsp;<font color=008200 size=3><b>//购买信息</b></font>#**<font color=006699 size=4><b>productNo&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//百度点券为产品线分配的唯一标识</b></font>#**<font color=006699 size=4><b>orderId&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//产品线订单编号</b></font>#**<font color=006699 size=4><b>securityTimeStamp</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//时间戳</b></font>#**<font color=006699 size=4><b>returnUrl&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//订单处理成功后，服务器后台通知地址</b></font>#**<font color=006699 size=4><b>goodsName&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//商品名称</b></font>#**<font color=006699 size=4><b>amount&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//订单金额，单位为分</b></font>#**<font color=006699 size=4><b>productAmount&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//产品线虚拟币表示的金额</b></font>#**<font color=006699 size=4><b>signMethod&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//签名方式。默认为1，表示MD5签名方式。（1:MD5；2：RSA）</b></font>#**<font color=006699 size=4><b>clientIp&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//客户端ip</b></font>#**<font color=006699 size=4><b>charset&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//请求参数字符编码，默认为1，表示utf-8编码。（1:utf-8；2:GBK）</b></font>#**<font color=006699 size=4><b>version&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//接口版本号，目前为1.0</b></font>#**<font color=006699 size=4><b>device&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//百度设备号标识</b></font>#**<font color=006699 size=4><b>bduss&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//百度passport登录凭证</b></font>#**<font color=006699 size=4><b>currency&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint_32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;//币种 1 人名币 2 美元</b></font>#**<font color=006699 size=4><b>sign&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//md5加密串，加密规则：</b></font>#*}#}#";s:6:"author";a:2:{i:0;s:9:"redfox241";i:1;N;}s:4:"fake";a:4:{s:5:"errno";i:6;s:6:"errmsg";s:11:"a string 77";s:7:"usermsg";s:11:"a string 48";s:8:"pay_info";a:15:{s:9:"productNo";s:11:"a string 40";s:7:"orderId";s:10:"a string 1";s:17:"securityTimeStamp";s:11:"a string 69";s:9:"returnUrl";s:11:"a string 52";s:9:"goodsName";s:11:"a string 32";s:6:"amount";i:93;s:13:"productAmount";i:16;s:10:"signMethod";i:77;s:8:"clientIp";s:11:"a string 67";s:7:"charset";s:11:"a string 65";s:7:"version";s:11:"a string 86";s:6:"device";s:11:"a string 90";s:5:"bduss";s:11:"a string 17";s:8:"currency";s:9:"uint_32_t";s:4:"sign";s:11:"a string 94";}}}}