a:1:{s:5:"trunk";a:7:{s:3:"url";s:24:"c/i/group/applyJoinGroup";s:11:"description";s:23:"申请加群 cmd:103110";s:6:"notice";N;s:3:"req";a:4:{i:0;a:4:{i:0;s:4:"cuid";i:1;s:8:"uint64_t";i:2;s:18:"用户设备编号";i:3;s:4:"true";}i:1;a:4:{i:0;s:3:"cmd";i:1;s:8:"uint32_t";i:2;s:21:"=103110(加群申请)";i:3;s:4:"true";}i:2;a:4:{i:0;s:6:"msgTag";i:1;s:6:"MsgTag";i:2;s:38:"标识（见reponse 的MsgTag定义）";i:3;s:4:"true";}i:3;a:4:{i:0;s:4:"data";i:1;s:10:"reqestData";i:2;s:105:"{  <br>   groupId uint64_t   //申请加入群组的id <br>   string userMsg;        // 申请留言<br>}";i:3;s:4:"true";}}s:3:"res";s:2104:"{#*<font color=006699 size=4><b>isAck&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//=1(response 消息)</b></font>#*<font color=006699 size=4><b>cmd&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//=103110(加群申请)</b></font>#*<font color=006699 size=4><b>msgTag</b></font>:{&nbsp;&nbsp;&nbsp;&nbsp;<font color=008200 size=3><b>//标识（客户端传过来，原样返回）</b></font>#**<font color=006699 size=4><b>sequence&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>int32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;//序列号</b></font>#*}#*<font color=006699 size=4><b>error</b></font>:{&nbsp;&nbsp;&nbsp;&nbsp;<font color=008200 size=3><b>//错误信息，详见贴吧统一错误号文件</b></font>#**<font color=006699 size=4><b>errno&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>int32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;//错误号</b></font>#**<font color=006699 size=4><b>errmsg&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//错误信息</b></font>#**<font color=006699 size=4><b>usermsg&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//给用户提示信息</b></font>#*}#*<font color=006699 size=4><b>data</b></font>:{&nbsp;&nbsp;&nbsp;&nbsp;<font color=008200 size=3><b>//返回数据</b></font>#**<font color=006699 size=4><b>groupId&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//群id</b></font>#*}#}#";s:6:"author";a:2:{i:0;s:10:"songjingbo";i:1;N;}s:4:"fake";a:5:{s:5:"isAck";i:60;s:3:"cmd";i:51;s:6:"msgTag";a:1:{s:8:"sequence";i:16;}s:5:"error";a:3:{s:5:"errno";i:91;s:6:"errmsg";s:11:"a string 20";s:7:"usermsg";s:11:"a string 46";}s:4:"data";a:1:{s:7:"groupId";i:79;}}}}