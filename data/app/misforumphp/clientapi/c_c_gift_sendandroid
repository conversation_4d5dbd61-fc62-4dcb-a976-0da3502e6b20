a:1:{s:5:"trunk";a:7:{s:3:"url";s:20:"c/c/gift/sendandroid";s:11:"description";s:32:"赠送礼物的接口 cmd:308007";s:6:"notice";s:82:"android6.6以及以上版本使用，支持长端连接，密码采用密文传输";s:3:"req";a:6:{i:0;a:4:{i:0;s:11:"receiver_id";i:1;s:8:"uint32_t";i:2;s:22:"接受人id(ios不用)";i:3;s:4:"true";}i:1;a:4:{i:0;s:7:"gift_id";i:1;s:8:"uint32_t";i:2;s:19:"礼物id(ios不用)";i:3;s:4:"true";}i:2;a:4:{i:0;s:5:"price";i:1;s:8:"uint32_t";i:2;s:146:"用户显示的礼物价格（server端验证下，防止用户看到的price和server端不一致,如礼物价格已修改的情况）(ios不用)";i:3;s:4:"true";}i:3;a:4:{i:0;s:3:"num";i:1;s:8:"uint32_t";i:2;s:26:"赠送的数量(ios不用)";i:3;s:4:"true";}i:4;a:4:{i:0;s:4:"from";i:1;s:8:"uint32_t";i:2;s:23:"=1个人空间,=2私聊";i:3;s:4:"true";}i:5;a:4:{i:0;s:8:"password";i:1;s:6:"string";i:2;s:21:"rsa加密后的密码";i:3;s:4:"true";}}s:3:"res";s:1466:"{#*<font color=006699 size=4><b>error</b></font>:{&nbsp;&nbsp;&nbsp;&nbsp;<font color=008200 size=3><b>//错误信息 , 1990018=余额不足; 1990019=礼物价格有变化(请客户端重新请求commlist接口) ; 1990027=需要支付密码; 1990028=支付密码错误</b></font>#**<font color=006699 size=4><b>errorno&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//错误号</b></font>#**<font color=006699 size=4><b>errmsg&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//错误信息</b></font>#**<font color=006699 size=4><b>usermsg&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//用户提示</b></font>#*}#*<font color=006699 size=4><b>money&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//用户T豆余额</b></font>#*<font color=006699 size=4><b>public_key&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//公钥，在需要支付密码+支付密码错误</b></font>#}#";s:6:"author";a:2:{i:0;s:5:"cxzhp";i:1;N;}s:4:"fake";a:3:{s:5:"error";a:3:{s:7:"errorno";i:30;s:6:"errmsg";s:11:"a string 37";s:7:"usermsg";s:11:"a string 25";}s:5:"money";i:58;s:10:"public_key";s:10:"a string 6";}}}