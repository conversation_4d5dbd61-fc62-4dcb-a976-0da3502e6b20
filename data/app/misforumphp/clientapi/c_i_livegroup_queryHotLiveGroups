a:1:{s:5:"trunk";a:7:{s:3:"url";s:32:"c/i/livegroup/queryHotLiveGroups";s:11:"description";s:47:"获取全部热门直播信息列表 cmd:107003";s:6:"notice";N;s:3:"req";a:4:{i:0;a:4:{i:0;s:4:"cuid";i:1;s:8:"uint64_t";i:2;s:18:"用户设备编号";i:3;s:4:"true";}i:1;a:4:{i:0;s:3:"cmd";i:1;s:8:"uint32_t";i:2;s:45:"=107003(获取全部热门直播信息列表)";i:3;s:4:"true";}i:2;a:4:{i:0;s:6:"msgTag";i:1;s:6:"MsgTag";i:2;s:38:"标识（见reponse 的MsgTag定义）";i:3;s:4:"true";}i:3;a:4:{i:0;s:4:"data";i:1;s:10:"reqestData";i:2;s:190:"{ <br> uint32_t type;   	//直播群列表类型:1；正在直播  2，精彩回顾   3，预告<br> uint32_t offset; //列表开始位置 <br> uint32_t rn rn; //返回列表的条数 </br>}";i:3;s:4:"true";}}s:3:"res";s:6850:"{#*<font color=006699 size=4><b>isAck&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//=1(response 消息)</b></font>#*<font color=006699 size=4><b>cmd&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//=107003(获取全部热门直播信息列表)</b></font>#*<font color=006699 size=4><b>msgTag</b></font>:{&nbsp;&nbsp;&nbsp;&nbsp;<font color=008200 size=3><b>//标识（客户端传过来，原样返回）</b></font>#**<font color=006699 size=4><b>sequence&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>int32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;//序列号</b></font>#*}#*<font color=006699 size=4><b>error</b></font>:{&nbsp;&nbsp;&nbsp;&nbsp;<font color=008200 size=3><b>//错误信息，详见贴吧统一错误号文件</b></font>#**<font color=006699 size=4><b>errno&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>int32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;//错误号</b></font>#**<font color=006699 size=4><b>errmsg&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//错误信息</b></font>#**<font color=006699 size=4><b>usermsg&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//给用户提示信息</b></font>#*}#*<font color=006699 size=4><b>data</b></font>:{&nbsp;&nbsp;&nbsp;&nbsp;<font color=008200 size=3><b>//返回数据</b></font>#**<font color=006699 size=4><b>groups[]</b></font>:{&nbsp;&nbsp;&nbsp;&nbsp;<font color=008200 size=3><b>//群信息</b></font>#***<font color=006699 size=4><b>groupId&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//群id</b></font>#***<font color=006699 size=4><b>forumId&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//吧id</b></font>#***<font color=006699 size=4><b>name&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//群名</b></font>#***<font color=006699 size=4><b>intro&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//群简介</b></font>#***<font color=006699 size=4><b>portrait&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//群头像</b></font>#***<font color=006699 size=4><b>authorId&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//创建者id</b></font>#***<font color=006699 size=4><b>authorName&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//创建者name</b></font>#***<font color=006699 size=4><b>createTime&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//创建时间</b></font>#***<font color=006699 size=4><b>startTime&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//直播启动时间</b></font>#***<font color=006699 size=4><b>groupType&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//直播群类型（21语音 22视频）</b></font>#***<font color=006699 size=4><b>publisherName&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//发布人name</b></font>#***<font color=006699 size=4><b>publisherId&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//发布人id</b></font>#***<font color=006699 size=4><b>publisherPortrait</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//主播头像</b></font>#***<font color=006699 size=4><b>streamId&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//发布人streamId</b></font>#***<font color=006699 size=4><b>deviceId&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//发布人deviceId</b></font>#***<font color=006699 size=4><b>status&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//直播群状态（0删除 1未直播 2直播预告 3正常直播中 4模拟直播中 5直播暂停 6直播结束）</b></font>#***<font color=006699 size=4><b>listeners&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//收听人数</b></font>#***<font color=006699 size=4><b>likers&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//点赞数</b></font>#***<font color=006699 size=4><b>background&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>string</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//群背景</b></font>#***<font color=006699 size=4><b>isVip&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;//是否是加V直播间（0不是 1是）</b></font>#**}#**<font color=006699 size=4><b>hasMore&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font><font color=00000 size=4><b>uint32_t</b></font><font color=008200 size=3><b>&nbsp;&nbsp;&nbsp;///是否有更多(1有, 0无)</b></font>#*}#}#";s:6:"author";a:2:{i:0;s:9:"duyinghao";i:1;N;}s:4:"fake";a:5:{s:5:"isAck";i:4;s:3:"cmd";i:100;s:6:"msgTag";a:1:{s:8:"sequence";i:54;}s:5:"error";a:3:{s:5:"errno";i:8;s:6:"errmsg";s:10:"a string 5";s:7:"usermsg";s:11:"a string 62";}s:4:"data";a:2:{s:6:"groups";a:2:{i:0;a:20:{s:7:"groupId";i:1;s:7:"forumId";i:3;s:4:"name";s:11:"a string 24";s:5:"intro";s:11:"a string 19";s:8:"portrait";s:11:"a string 25";s:8:"authorId";i:15;s:10:"authorName";s:11:"a string 94";s:10:"createTime";i:14;s:9:"startTime";i:46;s:9:"groupType";i:76;s:13:"publisherName";s:11:"a string 59";s:11:"publisherId";i:89;s:17:"publisherPortrait";s:11:"a string 39";s:8:"streamId";s:11:"a string 40";s:8:"deviceId";s:11:"a string 49";s:6:"status";i:25;s:9:"listeners";i:68;s:6:"likers";i:92;s:10:"background";s:11:"a string 22";s:5:"isVip";i:69;}i:1;a:20:{s:7:"groupId";i:1;s:7:"forumId";i:3;s:4:"name";s:11:"a string 24";s:5:"intro";s:11:"a string 19";s:8:"portrait";s:11:"a string 25";s:8:"authorId";i:15;s:10:"authorName";s:11:"a string 94";s:10:"createTime";i:14;s:9:"startTime";i:46;s:9:"groupType";i:76;s:13:"publisherName";s:11:"a string 59";s:11:"publisherId";i:89;s:17:"publisherPortrait";s:11:"a string 39";s:8:"streamId";s:11:"a string 40";s:8:"deviceId";s:11:"a string 49";s:6:"status";i:25;s:9:"listeners";i:68;s:6:"likers";i:92;s:10:"background";s:11:"a string 22";s:5:"isVip";i:69;}}s:7:"hasMore";i:80;}}}}