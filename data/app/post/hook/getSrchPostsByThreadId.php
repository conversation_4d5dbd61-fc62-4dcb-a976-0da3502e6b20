<?php

class Post_GetSrchPostsByThreadId_Hook {
	public static function GetSrchPostsByThreadIdReq($arrInput) {
		if (isset($arrInput['ueg_ip'])) {
			$arrInput['ueg_ip'] = intval($arrInput['ueg_ip']);
		}

		if (isset($arrInput['uids'])) {
			$arrUserId = array();
			foreach ($arrInput['uids'] as $userId) {
				$arrUserId[] = intval($userId);
			}
			$arrInput['uids'] = $arrUserId;
		}

		return $arrInput;
	}
}