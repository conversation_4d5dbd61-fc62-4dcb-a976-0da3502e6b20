<?php
 class Post_DeleteProThread_Desc {
 public static $arrDesc = array ( 'Thread_info' => array ( 1 => array ( 0 => 'forum_id', 1 => false, 2 => false, 3 => 13, ), 2 => array ( 0 => 'thread_id', 1 => false, 2 => false, 3 => 4, ), 3 => array ( 0 => 'forum_name', 1 => false, 2 => false, 3 => 9, ), 4 => array ( 0 => 'title', 1 => false, 2 => false, 3 => 9, ), 5 => array ( 0 => 'user_id', 1 => false, 2 => false, 3 => 4, ), 6 => array ( 0 => 'user_ip', 1 => false, 2 => false, 3 => 13, ), 7 => array ( 0 => 'user_ip6', 1 => false, 2 => false, 3 => 9, ), 8 => array ( 0 => 'title_prefix', 1 => false, 2 => false, 3 => 9, ), 9 => array ( 0 => 'vote_id', 1 => false, 2 => false, 3 => 9, ), 10 => array ( 0 => 'post_num', 1 => false, 2 => false, 3 => 4, ), 11 => array ( 0 => 'freq_num', 1 => false, 2 => false, 3 => 4, ), 12 => array ( 0 => 'last_modified_time', 1 => false, 2 => false, 3 => 4, ), 13 => array ( 0 => 'last_user_id', 1 => false, 2 => false, 3 => 4, ), 14 => array ( 0 => 'last_user_ip', 1 => false, 2 => false, 3 => 4, ), 15 => array ( 0 => 'last_user_ip6', 1 => false, 2 => false, 3 => 9, ), 16 => array ( 0 => 'last_post_deleted', 1 => false, 2 => false, 3 => 4, ), 17 => array ( 0 => 'good_types', 1 => false, 2 => false, 3 => 9, ), 18 => array ( 0 => 'top_types', 1 => false, 2 => false, 3 => 9, ), 19 => array ( 0 => 'thread_types', 1 => false, 2 => false, 3 => 9, ), 20 => array ( 0 => 'last_post_id', 1 => false, 2 => false, 3 => 9, ), 21 => array ( 0 => 'user_name', 1 => false, 2 => false, 3 => 9, ), 22 => array ( 0 => 'last_user_name', 1 => false, 2 => false, 3 => 9, ), 23 => array ( 0 => 'from_thread_id', 1 => false, 2 => false, 3 => 12, ), 24 => array ( 0 => 'thread_classes', 1 => false, 2 => false, 3 => 9, ), 25 => array ( 0 => 'create_time', 1 => false, 2 => false, 3 => 4, ), 26 => array ( 0 => 'abstract', 1 => false, 2 => false, 3 => 9, ), 27 => array ( 0 => 'media', 1 => false, 2 => true, 3 => 'Media_info', ), 28 => array ( 0 => 'zan', 1 => false, 2 => false, 3 => 'Zan', ), 29 => array ( 0 => 'is_deleted', 1 => false, 2 => false, 3 => 13, ), 30 => array ( 0 => 'first_post_id', 1 => false, 2 => false, 3 => 4, ), ), 'Media_info' => array ( 1 => array ( 0 => 'type', 1 => false, 2 => false, 3 => 9, ), 2 => array ( 0 => 'small_pic', 1 => false, 2 => false, 3 => 9, ), 3 => array ( 0 => 'big_pic', 1 => false, 2 => false, 3 => 9, ), 4 => array ( 0 => 'water_pic', 1 => false, 2 => false, 3 => 9, ), ), 'Zan' => array ( 1 => array ( 0 => 'num', 1 => false, 2 => false, 3 => 4, ), 2 => array ( 0 => 'last_time', 1 => false, 2 => false, 3 => 4, ), 3 => array ( 0 => 'user_id_list', 1 => false, 2 => true, 3 => 9, ), ), 'Redis_output' => array ( 1 => array ( 0 => 'ret', 1 => false, 2 => false, 3 => 12, ), 2 => array ( 0 => 'err_no', 1 => false, 2 => false, 3 => 4, ), 3 => array ( 0 => 'err_msg', 1 => false, 2 => false, 3 => 9, ), ), 'DeleteProThreadReq' => array ( 1 => array ( 0 => 'forum_id', 1 => false, 2 => false, 3 => 13, ), 2 => array ( 0 => 'thread_id', 1 => false, 2 => false, 3 => 4, ), 3 => array ( 0 => 'op_uid', 1 => false, 2 => false, 3 => 4, ), 4 => array ( 0 => 'call_from', 1 => false, 2 => false, 3 => 9, ), ), 'DeleteProThreadRes' => array ( 1 => array ( 0 => 'errno', 1 => false, 2 => false, 3 => 4, ), 2 => array ( 0 => 'errmsg', 1 => false, 2 => false, 3 => 9, ), ), );
 }
 ?>
