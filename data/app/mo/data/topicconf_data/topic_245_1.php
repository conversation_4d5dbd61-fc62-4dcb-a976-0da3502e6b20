{"page_id": "2933", "topic_name": "世界杯专题页push", "product_type": "wise", "page_num": "1", "content": "<div class=\"bodywrap\">\n     <div class=\"header\">\n         <div class=\"top_logo\">\n              <span editor-font-data=\"1\">吧嘻世界杯</span>\n              <span class=\"right_corner\"></span>\n         </div>\n         <div class=\"bottom-font\">\n               <div class=\"head-title\" editor-font-data=\"1\">【你猜我猜】下一个被淘汰的强队会是谁？</div>\n               <div class=\"header-select\" editor-font-data=\"1\"></div>\n         </div>\n         <img editor-image-data-src=\"1\" src=\"http://tb1.bdstatic.com/tb/623P13.JPG\">\n     </div>\n     <div class=\"header_nav\">\n                <a class=\"j_button_join button-join button\" href=\"http://tieba.baidu.com/p/3120535663\"><span class=\"join_friend\"></span> <span editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://tieba.baidu.com/p/3120535663\">参与话题</span></a>\n         <a class=\"j_button_share button-share button\" editor-wise-string=\"1\" href=\"#\"><span class=\"share_front\"></span><span editor-font-data=\"1\">分享好友</span></a>\n     </div>\n\n<div class=\"interview_user_info\" editor-del-data=\"1\">\n     <span class=\"hot_talk\" editor-font-data=\"1\">热议(1021)：</span>\n     <div class=\"interview_user_content\">\n     <ul class=\"interview_content\" editor-attr-keys-name=\"data-lis\" editor-attr-keys-text=\"编辑滚动文字，每行必须用竖线开头，需提交修改后刷新才生效\" data-lis=\"|西班牙之后，下一个小组赛被淘汰的强队会是谁？\n||西班牙之后，下一个小组赛被淘汰的强队会是谁？\n|我觉得是英格兰。\n|人家问的是强队……\" style=\"margin-top: -40px; -webkit-transition: 0.8s linear; transition: 0.8s linear;\"><li>我觉得是英格兰。\n</li><li>人家问的是强队……</li><li>西班牙之后，下一个小组赛被淘汰的强队会是谁？\n</li><li>西班牙之后，下一个小组赛被淘汰的强队会是谁？\n</li></ul>   \n     </div>  \n</div>\n\n     <div class=\"content_wrap\">\n         <div class=\"tagList \" editor-add-data=\"1\" editor-del-data=\"1\" editor-up-data=\"1\" editor-down-data=\"1\">\n             <a class=\"barName\">\n                 <span class=\"barNameSpan\">\n                    <span class=\"user_name_span\" editor-font-data=\"1\">olifor100200</span>\n                     <span class=\"time_count\"><span editor-font-data=\"1\" style=\"font-family:arial\"></span>\n                     </span>\n                     <span class=\"memberCount subBar\" editor-font-data=\"1\">795</span>\n                 </span>\n                 </a><a class=\"author_pic pic\"><span editor-image-data-bg=\"1\" class=\"picOuter\" style=\"background-image:url(http://tb1.bdstatic.com/tb/623P25.jpg);\"></span>\n                     <span class=\"pic_border_right\" style=\"display: block;\"></span>\n                 </a>\n             \n             <a class=\"j_titleWrap titleWrap  \" editor-go-href=\"1\" href=\"http://wapp.baidu.com/f?kz=3121636785&amp;jump_tieba_native=1\">\n                 <span class=\"title\" editor-image-data-bg=\"1\" editor-font-data=\"1\">【晒幸福】看别人女球迷家的男盆友！</span>\n  <span class=\"sub_pic\" editor-del-data=\"1\">\n       <span class=\"sub_span\"> <span class=\"sub_pic_left\" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/6P5.jpg);\"></span></span>\n       <span class=\"sub_span\"> <span class=\"sub_pic_center\" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/6P6.jpg);\"></span></span>\n       <span class=\"sub_span\"> <span class=\"sub_pic_right \" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/6P7.jpg);\"></span></span>\n  </span>\n<span class=\"subtitle\" editor-font-data=\"1\">认识四年多，我老公只给我画过两次画，第一次是恋爱时画我的铅笔画。第二次就是我迷上范佩西，送我mini范锦鲤。</span>\n             </a>\n             <span class=\"authorList j_authorList\">\n                 <a class=\"picList j_picList\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=范佩西&amp;jump_tieba_native=1\">\n                     <span class=\"barOwner\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=范佩西&amp;jump_tieba_native=1\">范佩西</span><span class=\"picFont\"></span>\n                 </a>\n\n             </span>\n         </div><div class=\"tagList \" editor-add-data=\"1\" editor-del-data=\"1\" editor-up-data=\"1\" editor-down-data=\"1\">\n             <a class=\"barName\">\n                 <span class=\"barNameSpan\">\n                    <span class=\"user_name_span\" editor-font-data=\"1\">ligongye007</span>\n                     <span class=\"time_count\"><span editor-font-data=\"1\" style=\"font-family:arial\"></span>\n                     </span>\n                     <span class=\"memberCount subBar\" editor-font-data=\"1\">694</span>\n                 </span>\n                 </a><a class=\"author_pic pic\"><span editor-image-data-bg=\"1\" class=\"picOuter\" style=\"background-image:url(http://tb1.bdstatic.com/tb/623P24.jpg);\"></span>\n                     <span class=\"pic_border_right\" style=\"display: block;\"></span>\n                 </a>\n             \n             <a class=\"j_titleWrap titleWrap  \" editor-go-href=\"1\" href=\"http://wapp.baidu.com/f?kz=3121935442&amp;jump_tieba_native=1\">\n                 <span class=\"title\" editor-image-data-bg=\"1\" editor-font-data=\"1\">【惊呆】世界杯多对比赛遭操控，你还跳楼嘛？</span>\n  <span class=\"sub_pic\" editor-del-data=\"1\">\n       <span class=\"sub_span\"> <span class=\"sub_pic_left\" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/623P21.jpg);\"></span></span>\n       <span class=\"sub_span\"> <span class=\"sub_pic_center\" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/623P23.jpg);\"></span></span>\n       <span class=\"sub_span\"> <span class=\"sub_pic_right \" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/623P22.jpg);\"></span></span>\n  </span>\n<span class=\"subtitle\" editor-font-data=\"1\">根据《每日邮报》透露，FIFA确实已经证实了参加世界杯的部分球队受到了赌博集团的操控。</span>\n             </a>\n             <span class=\"authorList j_authorList\">\n                 <a class=\"picList j_picList\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=世界杯&amp;jump_tieba_native=1\">\n                     <span class=\"barOwner\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=世界杯&amp;jump_tieba_native=1\">世界杯</span><span class=\"picFont\"></span>\n                 </a>\n\n             </span>\n         </div><div class=\"tagList \" editor-add-data=\"1\" editor-del-data=\"1\" editor-up-data=\"1\" editor-down-data=\"1\">\n             <a class=\"barName\">\n                 <span class=\"barNameSpan\">\n                    <span class=\"user_name_span\" editor-font-data=\"1\">贴吧小贴贴</span>\n                     <span class=\"time_count\"><span editor-font-data=\"1\" style=\"font-family:arial\"></span>\n                     </span>\n                     <span class=\"memberCount subBar\" editor-font-data=\"1\">2435</span>\n                 </span>\n                 </a><a class=\"author_pic pic\"><span editor-image-data-bg=\"1\" class=\"picOuter\" style=\"background-image:url(http://tb1.bdstatic.com/tb/tbxtt.JPG);\"></span>\n                     <span class=\"pic_border_right\" style=\"display: block;\"></span>\n                 </a>\n             \n             <a class=\"j_titleWrap titleWrap  \" editor-go-href=\"1\" href=\"http://wapp.baidu.com/f?kz=3121807365&amp;jump_tieba_native=1\">\n                 <span class=\"title\" editor-image-data-bg=\"1\" editor-font-data=\"1\">【哈哈】c罗助攻！葡萄还剩最后一颗牙！</span>\n  \n<span class=\"subtitle\" editor-font-data=\"1\">葡萄还剩最后一颗牙！<br>感谢葡萄牙！为我们把悬念保留到最后一轮！</span>\n             </a>\n             <span class=\"authorList j_authorList\">\n                 <a class=\"picList j_picList\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=世界杯&amp;jump_tieba_native=1\">\n                     <span class=\"barOwner\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=世界杯&amp;jump_tieba_native=1\">世界杯</span><span class=\"picFont\"></span>\n                 </a>\n\n             </span>\n         </div>\n     </div>\n </div>\n<script>\n\n(function initScrollList(){\n\n   function createList(){\n     var list = $('.interview_user_content').find('ul:first-child');\n     list.attr('style', null);\n     var lis = list.attr('data-lis').split('|');\n     var str = '';\n     for(var i=0; i<lis.length; i++){\n       if(lis[i]){\n         str += '<li>' + lis[i].replace(/>/gi,'&gt;').replace(/</gi,'&lt;')+ '</li>';\n       }\n     }\n     list.html(str);\n   }\n   function autoScroll() {\n            var first_list = $('.interview_user_content').find('ul:first-child');\n            first_list.animate({\n                marginTop: \"-40px\"\n            }, 800, 'linear', function() {\n                first_list.css({\n                    marginTop: \"0px\"\n                }).find(\"li:first-child\").appendTo(first_list);\n            }); \n        };\n\n    createList();\n\n    window.setInterval(function() {\n        autoScroll();\n    }, 2000);\n\n})();\n\n\n$.track = function(opt){\n            opt = opt || {}; \n            var img = new Image();\n            var trackUrl = \"http://static.tieba.baidu.com/tb/img/track.gif?\";\n            var paramArr = [ \n                \"client_type=wap_smart\",\n                \"url=\" + encodeURIComponent(document.location.href),\n                \"refer=\" + encodeURIComponent(document.referrer),\n                \"uname=\" + encodeURIComponent(opt.uname || \"\"),\n                \"task=\" + (opt.task && encodeURIComponent(opt.task) || \"\"),\n                \"page=\" + (opt.page && encodeURIComponent(opt.page) || \"\"),\n                \"locate=\" + (opt.locate && encodeURIComponent(opt.locate)|| \"\"),\n                \"type=\" + (opt.type || \"click\"),\n                \"fname=\" + encodeURIComponent(opt.fname || \"\"),\n                \"fid=\" + (opt.fid || \"\"),\n                \"tid=\" + (opt.tid || \"\"),\n                \"pid=\" + (opt.pid || \"\"),\n                \"is_new_user=\" + (opt.isNewUser || \"\"),\n                \"_t=\" + (new Date()) * 1000\n            ];  \n            trackUrl += paramArr.join(\"&\");\n            img.src = trackUrl;\n            img = null;\n    };\n\n$.track({\n    task: '运营专题通用平台',\n    page: document.title,\n    locate: '主题墙',\n    type: 'view'\n});\n$('body').delegate('a', 'click', function(){\n  $.track({\n    task: '运营专题通用平台',\n    page: document.title,\n    locate: '主题墙',\n    type: 'click'\n  });\n});\n</script>", "layout_name": "主题墙样式", "create_user": "zhaoke02", "create_time": "2014-06-20 11:00:58", "last_user": "zhaoke02", "last_time": "2014-06-23 18:07:36", "before_content": "【你猜我猜】下一个被淘汰的强队会是谁？一起来玩坏世界杯吧~！", "after_content": "http://tb1.bdstatic.com/tb/%E7%8E%A9%E5%9D%8F1.jpg", "publish_user": null, "publish_time": "0000-00-00 00:00:00", "page_pc_url": "", "page_title": "【你猜我猜】下一个被淘汰的强队会是谁？一起来玩坏世界杯吧~！", "page_css": "html,body{         background-image:-webkit-gradient(linear, left top, right top, color-stop(0.40, #cccccc), color-stop(0.5, #fdfdfd), color-stop(0.60, #fefefe));         -webkit-background-size: 2px 2px;         background-size: 2px 2px;     \n}  \nbody{\n  padding-bottom:10px;   \n  word-wrap: break-word;\n  word-break: break-all;\n}    .header{         display: block;         position: relative;     }     .header img{         width: 100%;         display: block;     }     .top_logo{         padding: 5px 12px 5px 12px;         font-size: 15px;         font-weight: bold;         color: #fff;         position: absolute;         background: #dd6240;         opacity: .8;         top: 50%; margin-top:-9px; left: 0;     }     .right_corner{         position: absolute;         right: -17px;         top: 0;         background: url(http://tb2.bdstatic.com/tb/mobile/syunying/img/title-right-conner_45b7dce5.png) right top no-repeat;         height: 28px;         display: block;         width: 21px;     }     .button{         display: inline-block;         background: -webkit-gradient(linear,left top,left bottom,from(#3f8be8),to(#2475d9));         height: 25px;         padding-right: 5px;         border-radius: 3px;         line-height: 25px;         vertical-align: middle;         margin-top: 7px;         margin-bottom: 8px;         color: #ffffff;         font-weight: bold;         text-align: center;         padding-left: 35px;     }     .button-share{         float: right;         margin-right: 5px;         position: relative;     }     .share_front{         position: absolute;         left:5px;         top:50%;         display: block;         width: 20px;         height: 20px;         margin-top: -10px;         background-size: 20px 20px;         background-position: center center;         background-repeat: no-repeat;         background-color: #000;         background-image:url(http://tieba.baidu.com/tb/r/image/2013-08-07/f88d435d0fc71a63a29dcabba81e27ae.png);     }     .button-join{         float: right;         margin-right: 5px;         position: relative;     }     .join_friend{         position: absolute;         left:5px;         top:50%;         display: block;         width: 20px;         height: 20px;         margin-top: -10px;         background-size: 20px 20px;         background-color: #000;         background-position: center center;         background-repeat: no-repeat;         background-image: url(http://tieba.baidu.com/tb/r/image/2013-08-07/3faccaed3dc5d4cf8bec9102c8fa67f7.png);     }     .header_nav{         height: 40px;         background: -webkit-gradient(linear,left top,left bottom,from(#f7f8f9),to(#e7e9ea));         box-shadow: 0 1px 3px 0 rgba(0,0,0,.3);     }     .head-title{         color:#fff;         font-size: 18px;         line-height: 35px;         padding-left: 10px;         font-weight: bold;     }     .header-select{         padding-left: 10px;         color: #dcdcdc;         padding-bottom: 10px;         font-size: 14px;     }     .bottom-font{         position: absolute;         bottom: 0px;         left: 0px;   background:#333;opacity:0.8; display:block;width:100%;  }    .tag_feed_list_item{        margin-left: 6px;        margin-right: 6px;        margin-top: 18px;        background: #fcfcfd;        border-radius: 5px;        border: 1px solid #bdbfc3;        border-left: 0px;        border-bottom: 0px;        position: relative;    }    .tagList{        margin-left:6px;        margin-right:6px;        margin-top:18px;        background:#fcfcfd;        border-radius:5px;        border:1px solid #bdbfc3;        border-left:0px;        border-bottom:0px;        position:relative;    }    .firstDivClass{        margin-top: 0px;    }    .tagView{        padding-left:16px;        height:32px;        -webkit-background-size: 14px 15px;        background-size: 14px 15px;        background-repeat:no-repeat;        background-position:left center;        position:absolute;        top:0px;        right:8px;        display:block;        font-size:12px;        font-weight:bold;        line-height:32px;        text-align:center;        color:#fefefe;        background-image:url(http://tieba.baidu.com/tb/cms/client/tagBack.png);    }    .tagViewInner{        display:inline-block;        padding-left:16px;        height:32px;        left:0px;        top:1px;        position:absolute;        color:#93a6b8;    }    .tagred{        background-image:url(http://tieba.baidu.com/tb/cms/client/red-top.png);    }    .tagyellow{        background-image:url(http://tieba.baidu.com/tb/cms/client/yellow-top.png);    }    .tagpurple{        background-image:url(http://tieba.baidu.com/tb/cms/client/purple-top.png);    }    .tagblue{        background-image:url(http://tieba.baidu.com/tb/cms/client/blue-top.png);    }    .taggree{        background-image:url(http://tieba.baidu.com/tb/cms/client/gree-top.png);    }    .barName{        display:block;        padding:8px;        padding-top:8px;        text-align:left;        padding-bottom:3px;        background: #fcfcfc;        border-top-right-radius:5px;        border-top-left-radius:5px;        position:relative;    }    .barNameSpan{        display:block;        color:#777777;        line-height:22px;        font-size:14px;        font-weight:normal;        height:26px;        padding-left:54px;    }    .time_count{        color:#b2b6ba;        font-size:10px;        line-height:18px;        display:block;        line-height:15px;    }    .subBar{        color:#93a6b8;        font-size:12px;        padding-left:16px;        line-height:20px;        -webkit-background-size: 14px 14px;        background-size: 14px 14px;        background-repeat:no-repeat;        background-position:left center;        margin-left:5px;        display:inline-block;        position:absolute;        right:8px;        top:5px;    }    .memberCount{        background-image:url(http://tieba.baidu.com/tb/cms/client/reply_icon_new.png);    }    .user_name_span{        display:block;        color:#666666;        font-size:12px;        line-height:15px;    }    .articleCount{        background-image:url(http://tieba.baidu.com/tb/cms/client/acticle.png);    }    .titleWrap{        padding:10px;        padding-top:0px;        display:block;        padding-bottom:12px;        background:#fcfcfd;    }    .title{        display:block;        line-height:30px;        color:#262626;        text-align:left;        font-size:16px;        font-weight:normal;        padding-top:10px;        line-height:20px;    }    .classTitle{        padding-top:10px;    }    .subtitle{        text-align:left;        color:#777;        font-size:14px;        display:block;        padding-bottom:0px;        padding-top:7px;    }    .titlePic{        display:block;    }    .titlePicMore{        display:-webkit-box;        margin-top:8px;    }    .titlePic img{        width:100%;    }    .titlePicBox{        display:block;        -webkit-box-flex:1;        width:100%;    }    .titlePicBoxInner{        display:block;        margin-right:5px;        max-height:240px;        overflow : hidden;        text-align:left;        line-height:100%;        font-size:0px;    }    .last_titlePicBoxInner{        display:block;        margin-right:0px;        max-height:240px;        overflow : hidden;        text-align:left;    }    .titlePicBoxInner img{        width:100%;    }    .authorList{        display:block;        height:32px;        margin-top:-2px;        border-top:1px solid #e7e9ec;        border-bottom-right-radius:5px;        border-bottom-left-radius:5px;        text-align:left;        padding-left:10px;        padding-right:10px;        position:relative;        background:#F0F2F4;        -webkit-box-shadow: 0px 2px 2px #bdbfc3;        box-shadow: 0px 2px 2px #bdbfc3;    }    .authorListConect{        display:block;        background:#fcfcfd;        height:37px;        border-top:1px solid #f2f2f7;        margin-top:-34px;        text-align:left;        padding-left:10px;        padding-right:10px;        position:relative;        border-top:0px;    }    .picFont{        color:#93a6b8;        font-size:12px;        display:inline-block;        line-height:32px;        font-weight:bold;        margin-left:10px;        text-shadow:0px 1px 1px #fff;    }    .picFontPoint{        line-height:30px;    }    .pic{        display:inline-block;        width:50px;        height:50px;;        vertical-align:middle;        -webkit-background-size: 50px 50px;        background-size: 50px 50px;        position:absolute;        left:-1px;        top:-12px;    }    .picOuter{        display:block;        margin:4px;        -webkit-background-size: 46px 46px;        background-size: 46px 46px;        height:42px;        -webkit-box-shadow: 1px -1px 2px #fff;        box-shadow: 1px -1px 2px #fff;    }    .pic_border_right{        width:50px;        height:50px;        position:absolute;        left:0px;        top:0px;        display:block;        -webkit-background-size: 50px 50px;        background-size: 50px 50px;        background-image:url(http://tieba.baidu.com/tb/cms/client/first_page_outer.png);    }    .picFirst{        margin-left:0px;    }    .barOwner{        color:#93a6b8;        font-size:12px;        display:inline-block;        font-weight:bold;        text-shadow:0px 1px 1px #fff;        padding-left:18px;        line-height:32px;        -webkit-background-size: 14px 14px;        background-size: 14px 14px;        background-position:left center;        background-repeat:no-repeat;        background-image:url(http://tieba.baidu.com/tb/cms/client/bar_icon.png);        text-shadow:0px 1px 1px #fff;    }\n.sub_pic{\n  display: -webkit-box;\n  padding-top:5px;\n}\n.sub_span{\n  display:block;\n  -webkit-box-flex:1;\n  height:90px;\n  overflow:hidden;\n  text-align:left;\n  \n  \n}\n.sub_pic_left{\n background-size:cover;\n display:block;\n height:90px;\n margin-right:5px;\n background-image:url(http://imgstatic.baidu.com/img/image/shouye/xiaohuashouyelunbo0808.jpg);\n}\n.sub_pic_center{\n  background-size:cover;\n   display:block;\n height:90px;\n margin-right:5px;\n   background-image:url(http://imgstatic.baidu.com/img/image/shouye/tongliya0808.jpg);\n}\n.sub_pic_right{\n background-size:cover;\n display:block;\n height:90px;\n background-image:url(http://imgstatic.baidu.com/img/image/shouye/huoyingrenzhe0808.jpg);\n}\n\n\n.interview_user_info{\nborder: 1px solid #d2d2d2;\nheight: 40px;\nline-height: 40px;\npadding: 0 10px;\ncolor: #666;\nbackground-color: #f5f6f7;\noverflow: hidden;\n}\n.hot_talk {\nfloat: left;\ncolor: #1665be;\n}\n\n.interview_content{\n  margin-left: 104px;\n}\n.interview_user_info li{\n  height: 40px;\n}", "page_js": ""}