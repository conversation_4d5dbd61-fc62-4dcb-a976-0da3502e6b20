<?php
 class Lego_Carousel_Desc {
 public static $arrDesc = array ( 'CommonButton' => array ( 1 => array ( 0 => 'bIcon', 1 => false, 2 => false, 3 => 9, ), 2 => array ( 0 => 'bIconN', 1 => false, 2 => false, 3 => 9, ), 3 => array ( 0 => 'bIconType', 1 => false, 2 => false, 3 => 5, ), 4 => array ( 0 => 'bSelIcon', 1 => false, 2 => false, 3 => 9, ), 5 => array ( 0 => 'bSelIconN', 1 => false, 2 => false, 3 => 9, ), 6 => array ( 0 => 'bSelIconType', 1 => false, 2 => false, 3 => 5, ), 7 => array ( 0 => 'clickAction', 1 => false, 2 => false, 3 => 9, ), 8 => array ( 0 => 'preAction', 1 => false, 2 => false, 3 => 9, ), 9 => array ( 0 => 'statKey', 1 => false, 2 => false, 3 => 9, ), ), 'Carousel' => array ( 1 => array ( 0 => 'item_id', 1 => false, 2 => false, 3 => 9, ), 2 => array ( 0 => 'flip_id', 1 => false, 2 => false, 3 => 9, ), 3 => array ( 0 => 'scheme', 1 => false, 2 => false, 3 => 9, ), 4 => array ( 0 => 'statistics', 1 => false, 2 => false, 3 => 9, ), 5 => array ( 0 => 'statTab', 1 => false, 2 => false, 3 => 5, ), 6 => array ( 0 => 'showLine', 1 => false, 2 => false, 3 => 5, ), 7 => array ( 0 => 'duration', 1 => false, 2 => false, 3 => 3, ), 8 => array ( 0 => 'ratio', 1 => false, 2 => false, 3 => 1, ), 9 => array ( 0 => 'descOnPic', 1 => false, 2 => false, 3 => 5, ), 10 => array ( 0 => 'pics', 1 => false, 2 => true, 3 => 'PicItem', ), 11 => array ( 0 => 'showCover', 1 => false, 2 => false, 3 => 5, ), 12 => array ( 0 => 'bInfo', 1 => false, 2 => false, 3 => 'BottomInfo', ), 13 => array ( 0 => 'wMark', 1 => false, 2 => false, 3 => 'WaterMark', ), 14 => array ( 0 => 'sExtras', 1 => false, 2 => false, 3 => 9, ), 15 => array ( 0 => 'showKey', 1 => false, 2 => false, 3 => 9, ), 16 => array ( 0 => 'showExtra', 1 => false, 2 => false, 3 => 9, ), 17 => array ( 0 => 'bCeiling', 1 => false, 2 => false, 3 => 5, ), ), 'PicItem' => array ( 1 => array ( 0 => 'picId', 1 => false, 2 => false, 3 => 9, ), 2 => array ( 0 => 'pic', 1 => true, 2 => false, 3 => 9, ), 3 => array ( 0 => 'scheme', 1 => false, 2 => false, 3 => 9, ), 4 => array ( 0 => 'desc', 1 => false, 2 => false, 3 => 9, ), 5 => array ( 0 => 'descColor', 1 => false, 2 => false, 3 => 9, ), 6 => array ( 0 => 'descColorNight', 1 => false, 2 => false, 3 => 9, ), 7 => array ( 0 => 'mLines', 1 => false, 2 => false, 3 => 5, ), ), 'BottomInfo' => array ( 1 => array ( 0 => 'lbText', 1 => false, 2 => false, 3 => 9, ), 2 => array ( 0 => 'lbScheme', 1 => false, 2 => false, 3 => 9, ), 3 => array ( 0 => 'rIcon', 1 => false, 2 => false, 3 => 9, ), 4 => array ( 0 => 'rIconN', 1 => false, 2 => false, 3 => 9, ), 5 => array ( 0 => 'rIconType', 1 => false, 2 => false, 3 => 5, ), 6 => array ( 0 => 'rText', 1 => false, 2 => false, 3 => 9, ), 7 => array ( 0 => 'cb', 1 => false, 2 => false, 3 => 'CommonButton', ), ), 'WaterMark' => array ( 1 => array ( 0 => 'pic', 1 => false, 2 => false, 3 => 9, ), 2 => array ( 0 => 'picN', 1 => false, 2 => false, 3 => 9, ), 3 => array ( 0 => 'xPos', 1 => false, 2 => false, 3 => 5, ), 4 => array ( 0 => 'yPos', 1 => false, 2 => false, 3 => 5, ), ), );
 }
 ?>
