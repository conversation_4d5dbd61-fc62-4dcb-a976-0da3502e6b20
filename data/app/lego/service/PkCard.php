<?php
 class Lego_PkCard_Desc {
 public static $arrDesc = array ( 'PkCard' => array ( 1 => array ( 0 => 'item_id', 1 => false, 2 => false, 3 => 9, ), 2 => array ( 0 => 'flip_id', 1 => false, 2 => false, 3 => 9, ), 3 => array ( 0 => 'scheme', 1 => false, 2 => false, 3 => 9, ), 4 => array ( 0 => 'statistics', 1 => false, 2 => false, 3 => 9, ), 5 => array ( 0 => 'statTab', 1 => false, 2 => false, 3 => 5, ), 6 => array ( 0 => 'showCover', 1 => false, 2 => false, 3 => 5, ), 7 => array ( 0 => 'showLine', 1 => false, 2 => false, 3 => 5, ), 8 => array ( 0 => 'pk_id', 1 => false, 2 => false, 3 => 9, ), 9 => array ( 0 => 'p_url', 1 => true, 2 => false, 3 => 9, ), 10 => array ( 0 => 'title', 1 => true, 2 => false, 3 => 9, ), 11 => array ( 0 => 'user_pk_index', 1 => false, 2 => false, 3 => 5, ), 12 => array ( 0 => 'pks', 1 => false, 2 => true, 3 => 'PkInfo', ), 13 => array ( 0 => 'sExtras', 1 => false, 2 => false, 3 => 9, ), 14 => array ( 0 => 'showKey', 1 => false, 2 => false, 3 => 9, ), 15 => array ( 0 => 'showExtra', 1 => false, 2 => false, 3 => 9, ), 16 => array ( 0 => 'bCeiling', 1 => false, 2 => false, 3 => 5, ), ), 'PkInfo' => array ( 1 => array ( 0 => 'desc', 1 => true, 2 => false, 3 => 9, ), 2 => array ( 0 => 'num', 1 => true, 2 => false, 3 => 3, ), 3 => array ( 0 => 'index', 1 => true, 2 => false, 3 => 5, ), ), );
 }
 ?>
