<?php
 class Perm_SetForumVideoAdmin_Desc {
 public static $arrDesc = array ( 'User_output' => array ( 1 => array ( 0 => 'func_name', 1 => false, 2 => false, 3 => 9, ), 2 => array ( 0 => 'user_id', 1 => false, 2 => false, 3 => 4, ), 3 => array ( 0 => 'user_name', 1 => false, 2 => false, 3 => 9, ), 4 => array ( 0 => 'forum_id', 1 => false, 2 => false, 3 => 13, ), 5 => array ( 0 => 'forum_name', 1 => false, 2 => false, 3 => 9, ), 6 => array ( 0 => 'is_like', 1 => false, 2 => false, 3 => 13, ), 7 => array ( 0 => 'is_black', 1 => false, 2 => false, 3 => 13, ), 8 => array ( 0 => 'like_num', 1 => false, 2 => false, 3 => 13, ), 9 => array ( 0 => 'is_top', 1 => false, 2 => false, 3 => 13, ), 10 => array ( 0 => 'in_time', 1 => false, 2 => false, 3 => 5, ), 11 => array ( 0 => 'level_id', 1 => false, 2 => false, 3 => 13, ), 12 => array ( 0 => 'cur_score', 1 => false, 2 => false, 3 => 13, ), 13 => array ( 0 => 'score_left', 1 => false, 2 => false, 3 => 13, ), 14 => array ( 0 => 'level_name', 1 => false, 2 => false, 3 => 9, ), 15 => array ( 0 => 'display_flag', 1 => false, 2 => false, 3 => 13, ), 16 => array ( 0 => 'op_user_id', 1 => false, 2 => false, 3 => 4, ), 17 => array ( 0 => 'op_user_name', 1 => false, 2 => false, 3 => 9, ), 18 => array ( 0 => 'op_time', 1 => false, 2 => false, 3 => 13, ), ), 'Forum_output' => array ( 1 => array ( 0 => 'func_name', 1 => false, 2 => false, 3 => 9, ), 2 => array ( 0 => 'forum_id', 1 => false, 2 => false, 3 => 13, ), 3 => array ( 0 => 'forum_name', 1 => false, 2 => false, 3 => 9, ), 4 => array ( 0 => 'member_count', 1 => false, 2 => false, 3 => 13, ), 5 => array ( 0 => 'forum_threshold', 1 => false, 2 => false, 3 => 13, ), 6 => array ( 0 => 'member_name', 1 => false, 2 => false, 3 => 9, ), 7 => array ( 0 => 'page_type', 1 => false, 2 => false, 3 => 13, ), 8 => array ( 0 => 'page_no', 1 => false, 2 => false, 3 => 13, ), 9 => array ( 0 => 'page_size', 1 => false, 2 => false, 3 => 13, ), 10 => array ( 0 => 'level_list', 1 => false, 2 => false, 3 => 12, ), 11 => array ( 0 => 'member_list', 1 => false, 2 => false, 3 => 12, ), ), 'Forum_output_array' => array ( 1 => array ( 0 => 'user', 1 => true, 2 => false, 3 => 12, ), ), 'Batch_input' => array ( 1 => array ( 0 => 'id', 1 => false, 2 => false, 3 => 13, ), 2 => array ( 0 => 'name', 1 => false, 2 => false, 3 => 9, ), 3 => array ( 0 => 'score', 1 => false, 2 => false, 3 => 13, ), ), 'Level_info' => array ( 1 => array ( 0 => 'title_id', 1 => false, 2 => false, 3 => 13, ), 2 => array ( 0 => 'title_name', 1 => false, 2 => false, 3 => 9, ), 3 => array ( 0 => 'level_info', 1 => false, 2 => false, 3 => 12, ), 4 => array ( 0 => 'is_user_define', 1 => false, 2 => false, 3 => 13, ), 5 => array ( 0 => 'in_use', 1 => false, 2 => false, 3 => 13, ), ), 'Perm_array' => array ( 1 => array ( 0 => 'can_xyz', 1 => false, 2 => false, 3 => 12, ), ), 'role_array' => array ( 1 => array ( 0 => 'is_forum_xyz', 1 => false, 2 => false, 3 => 8, ), ), 'Perm_out' => array ( 1 => array ( 0 => 'perm', 1 => true, 2 => false, 3 => 12, ), 2 => array ( 0 => 'block_type', 1 => true, 2 => false, 3 => 13, ), 3 => array ( 0 => 'ueg_type', 1 => true, 2 => false, 3 => 13, ), 4 => array ( 0 => 'grade', 1 => true, 2 => false, 3 => 'User_output', ), 5 => array ( 0 => 'tip', 1 => false, 2 => false, 3 => 12, ), ), 'PermRole_out' => array ( 1 => array ( 0 => 'perm', 1 => true, 2 => false, 3 => 12, ), 2 => array ( 0 => 'block_type', 1 => true, 2 => false, 3 => 13, ), 3 => array ( 0 => 'ueg_type', 1 => true, 2 => false, 3 => 13, ), 4 => array ( 0 => 'grade', 1 => true, 2 => false, 3 => 'User_output', ), 5 => array ( 0 => 'role', 1 => true, 2 => false, 3 => 12, ), ), 'Role_out' => array ( 1 => array ( 0 => 'role', 1 => false, 2 => false, 3 => 12, ), ), 'Forum_user_role_t' => array ( 1 => array ( 0 => 'forum_id', 1 => false, 2 => false, 3 => 13, ), 2 => array ( 0 => 'user_id', 1 => false, 2 => false, 3 => 4, ), 3 => array ( 0 => 'role_id', 1 => false, 2 => false, 3 => 13, ), 4 => array ( 0 => 'role_name', 1 => false, 2 => false, 3 => 9, ), 5 => array ( 0 => 'op_user_id', 1 => false, 2 => false, 3 => 4, ), 6 => array ( 0 => 'update_time', 1 => false, 2 => false, 3 => 13, ), 7 => array ( 0 => 'expire_time', 1 => false, 2 => false, 3 => 13, ), ), 'Forum_block_out' => array ( 1 => array ( 0 => 'block_type', 1 => true, 2 => false, 3 => 13, ), ), 'SetForumVideoAdminReq' => array ( 1 => array ( 0 => 'forum_id', 1 => false, 2 => false, 3 => 13, ), 2 => array ( 0 => 'user_id', 1 => false, 2 => false, 3 => 4, ), 3 => array ( 0 => 'user_name', 1 => false, 2 => false, 3 => 9, ), 4 => array ( 0 => 'op_user_id', 1 => false, 2 => false, 3 => 4, ), 5 => array ( 0 => 'op_user_name', 1 => false, 2 => false, 3 => 9, ), 6 => array ( 0 => 'forum_name', 1 => false, 2 => false, 3 => 9, ), 7 => array ( 0 => 'need_memo', 1 => false, 2 => false, 3 => 13, ), 8 => array ( 0 => 'resource_from', 1 => false, 2 => false, 3 => 13, ), 9 => array ( 0 => 'role_name', 1 => false, 2 => false, 3 => 9, ), ), 'SetForumVideoAdminRes' => array ( 1 => array ( 0 => 'errno', 1 => true, 2 => false, 3 => 13, ), 2 => array ( 0 => 'errmsg', 1 => false, 2 => false, 3 => 9, ), 3 => array ( 0 => 'errtype', 1 => false, 2 => false, 3 => 13, ), 4 => array ( 0 => 'cur_num', 1 => false, 2 => false, 3 => 13, ), 5 => array ( 0 => 'max_num', 1 => false, 2 => false, 3 => 13, ), ), );
 }
 ?>
