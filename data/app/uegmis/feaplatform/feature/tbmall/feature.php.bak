<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/



/**
 * @file feature/anti/feature.php
 * <AUTHOR>
 * @date 2013/08/18 17:02:50
 * @brief 
 *  
 **/

//	vars from anti

$req = $input['req'] ;

$isPurchase = $req['is_purchase'] ;
$userId = $req['user_id'] ;
$userName = $req['user_name'];
$ip = $req['ip'] ;
$strIp = $req['str_ip'] ;
$userAgent = $req['user_agent'] ;
$time = $req['time'] ;
$propsId = intval($req['props_id']) ;
$scores = $req['scores'] ;
$referer = $req['referer'] ;
$baiduUid = $req['BAIDUID'] ;
$bduss = $req['BDUSS'] ;
$frUri = $req['fr_uri'] ;
$bindPhone = $req['bind_phone'] ;
$authPhone = $req['auth_phone'] ;
$vcodeSumScores = $req['vcode_sum_scores'] ;
$sugFlag = $req['sug_flag'] ;
$mustBindSumScores = $req['must_bind_sum_scores'] ;
$isNeedMobileCheck = $req['is_need_mobile_check'] ;
$commonLocationInfo = $req['common_location_info'];
$isCommonLocation = intval($commonLocationInfo['isCommonLocation']*1);
$commonLocation = array_unique(arrayIconv($commonLocationInfo['commonLocation'],array("gbk","utf8//IGNORE")));
$thisIpLocation = iconv("gbk","utf8//IGNORE",$commonLocationInfo['thisIpLocation']);
$isRegLocation=intval($commonLocationInfo['isRegLocation']*1);
$regCity = iconv("gbk","utf8//IGNORE",$commonLocationInfo['regCity']);
$regProvince = iconv("gbk","utf8//IGNORE",$commonLocationInfo['regProvince']);
$deviceType= $req['device_type'] ;
$daySumScores= $req['daysum_scores'] ;
$payPass= $req['pay_pass'] ;
$osVersion= $req['osversion'] ;
$osType= $req['ostype'] ;
$dayCheck= intval($req['day_check']);
$isUidBlocked=intval($req['is_uid_blocked']);
// vars default

$antiCommitTime = time() ;
$logId = $req['logid'] ;

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
