<?php
/*
    ORP ������ Tieba ���л����� Passport ����Ȩ��̫һ���������Ҫ����˺������ݣ�
    ��������� 3 ������ļ� webroot/index.php �� webroot/service_index.php �� webroot/data_index.php �У�����
    ��������������ֻ��Ҫ���� 3 ���޸ĵ�����
*/
define('IS_ORP_RUNTIME',true);

$passport_file = '../../conf/framework/Passport.conf.php';

if(is_file($passport_file)){
    require_once($passport_file);
}

/**
 * ��ȡģ�����ƣ�
 * ��һ���������Ƿ�ʹ��pathinfo�������֧�֣����޸ĳ�false��
 * �ڶ���������Ĭ��ģ��ID��Ĭ����test
 * @var unknown_type
 */
function _getUrlModuleName($bolUsePathinfo = true, $strDefaultModule='test')
{
    return 'sign-new';
}

define('MODULE', 'sign');
$subsys = Tieba_Env::getOrpAppname();
if(!empty($subsys))
{
	ral_set_log(RAL_LOG_SUBSYS, $subsys);
}

define ('ROOT_PATH', dirname(__FILE__) . "/../..");


if (!defined('REQUEST_ID')){
    define('REQUEST_ID', Bingo_Log::getLogId());
}
// new add for ral to camel by z<PERSON><PERSON><PERSON> 20111209
if (function_exists('camel_set_logid')) {
    camel_set_logid(REQUEST_ID);
}   
$strModuleName = _getUrlModuleName(true, 'test');
$strUiIndexPath = ROOT_PATH . '/app/' . $strModuleName . "/index.php";
//����ܹ�ģ����
define('MODULE_NAME_OF_PEFECT_ARCH', $strModuleName);
if (file_exists($strUiIndexPath)) {
    require_once $strUiIndexPath;
} else {
    //ֱ����������ҳ��
    header('location:http://static.tieba.baidu.com/tb/error.html');
}

/*
 * For memory usage statistics Maybe removed later
 * Both in index.php and service_index.php
 */
$resource_stat = array(
	'module'				=> $strModuleName,
	'mem_peak_usage'		=> memory_get_peak_usage(),
	'mem_peak_usage_real'	=> memory_get_peak_usage(true),
	'mem_cur_usage'			=> memory_get_usage(),
	'mem_cur_usage_real'	=> memory_get_usage(true),
);

ral_write_log(RAL_LOG_NOTICE, "REQ_USAGE_STAT", $resource_stat);
