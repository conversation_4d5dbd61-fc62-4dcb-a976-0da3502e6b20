<?php
class EnterForum
{
    private static $_isFTag = false;
    private static $_isOtherTag = false;

    /* cache forum name */
    private static $_strForumName;

    // onetieba smallflow list
    private static $_smallflow = array(
        2488131 => 1, // test123
        14621377 => 1, // 普通明星吧, 江海小舟
        16601812 => 1, // 果冻shining01
        16601717 => 1, // 果冻shining02
        15815453 => 1, // 普通明星吧,小七土土
        15811320 => 1, // 贴吧地区test

        245521 => 1,//原平,地方商业吧
        4764 => 1,//济南，地方普通吧
        3859648 => 1,//自体丰胸,垂直化商业吧
        8081461 => 1,//私人健身教练，垂直化普通吧
        6868 =>1,//林志炫,定制明星吧
        136643 => 1,//博洋家纺，企业吧

        /*
        // big forum
        7298 => 1, // 股票
        9739 => 1, // 证券
        1184636 => 1, // 美股
        663150 => 1, // sh600307股票
        
        14245734 => 1, // 范冰冰官方
       
        552164 => 1, // 刘诗诗
        4773274 => 1, // tfboys
        3791095 => 1, // snh48
        
         */
    );
    
    // exempt forum list
    private static $_exemptlist = array(
        /*
        358325 => 1,
        1528084 => 1,
        4875333 => 1,
        4570633 => 1,
        13537062 => 1,
        5290890 => 1,
        15702603 => 1,
        4073167 => 1,
        9195460 => 1,
        4771483 => 1,
        3252705 => 1,
        12041936 => 1,
        14959495 => 1,
        6167750 => 1,
        5576409 => 1,
        3878531 => 1,
        3486517 => 1,
        12018966 => 1,
        11815606 => 1,
        13541229 => 1,
        632086 => 1,
        3719989 => 1,
        4381617 => 1,
        1147089 => 1,
        1254565 => 1,
        277356 => 1,
        20337980 => 1,
        3022010 => 1,
        1328391 => 1,
        8244570 => 1,
        483347 => 1,
        13149197 => 1,
        5403 => 1,
        1214407 => 1,
        416282 => 1,
        938287 => 1,
        71 => 1,
        1245724 => 1,
        216 => 1,
        271786 => 1,
         */
    );

    private static $arrBigpipeTabs = array(
        'group',
        'game',
        'game_activity',
        'album',
        'video',
        'hotthread',
        'good',
        'wangju',
        'deal',
        'gathering',
        'qa',
        'category',
        'tuan',
        'schedule',
        "newDeal",
        'ad',
        'corearea',
        'main',
    );

    private static $arrOldFrsForums = array(
    );

    //get forum name
    public static function getForumName($strIe = 'utf-8')
    {
        if (isset(self::$_strForumName))
        {
            return self::$_strForumName;
        }

        /* Three way to get forum name from request */
        $strRawForumName = '';
        $strForumName    = '';
        $bolQueryString  = false;

        /* No.1:router */
        $strUrl = Bingo_Http_Request::getServer('REQUEST_URI');
        $intPos = strpos($strUrl, '?');
        if (false !== $intPos)
        {
            $strUrl = substr($strUrl, 0, $intPos);
        }
        $arrUrl = explode('/', $strUrl);
        if (count($arrUrl) >= 2)
        {
            $strRouter = $arrUrl[1];
            if ($strRouter !== 'f')
            {
                $strRawForumName = $strRouter;
                $bolQueryString  = false;
            }
            else
            {
                if (!empty($arrUrl[2]))
                {
                    self::$_isOtherTag = true;
                }
                //self::$_isFTag = true;
            }
        }
        /* No.2:keyword */
        $strKw   = Bingo_Http_Request::getRaw('kw');
        $strWord = Bingo_Http_Request::getRaw('word');
        if (!is_null($strKw) && empty($strRawForumName))
        {
            $strRawForumName = $strKw;
            $bolQueryString  = true;
        }

        /* No.3:word */
        if (!is_null($strWord) && empty($strRawForumName))
        {
            $strRawForumName = $strWord;
            $bolQueryString  = true;
        }

        /* Trans charset code */
        if (isset($strRawForumName))
        {
            $strForumName = self::_transRawFname($strRawForumName, $bolQueryString, $strIe);
        }

        /* cache forum name */
        self::$_strForumName = $strForumName;

        return $strForumName;
    }

    public static function getForumInfo($strForumName, $strIe = 'utf-8')
    {
        $arrInput = array(
            'forum_name' => $strForumName,
        );
        $arrRes = Tieba_Service::call('forum', 'getBtxInfoByName', $arrInput, null, null, 'post', 'php', $strIe);
        /*
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno'])
        {
            return false;
        }
        */
        return $arrRes;
    }

    /**
        * @brief 
        *
        * @param $bolPtForum
        * @param $arrForumInfo
        *
        * @return 
     */
    public static function getModuleName($bolPtForum = false, $arrForumInfo = array())
    {
        // check drs mode, 容灾header
        $drs_check = "HTTP_X_BD_DRS_MODE";
        if (0 < strlen($_SERVER[$drs_check]) || 0 < strlen($_SERVER[strtolower($drs_check)])) {
            return 'pt-pc-frsui';
            /*
             * example
             * {"i":"nj","m":"core0"}
            $json_drs = (array)json_decode($_SERVER[$drs_check]);
            if (false === $json_drs || !isset($json_drs['m'])) {
                $json_drs = (array)json_decode($_SERVER[strtolower($drs_check)]);
            }
            if (isset($json_drs['m']) && 'core0' === $json_drs['m']) {
            }
             */
        }
        if (self::isOldFrsui() === true)
        {
            return 'frsui';
        }
        $intForceFlag = (int)Bingo_Http_Request::get('tb_frs_flag');
        if ($intForceFlag === 1)
        {
            return 'frsui';
        }
        else if ($intForceFlag === 2)
        {
            return 'pc-frsui';
        }
        else if ($intForceFlag === 3)
        {
            return 'pt-pc-frsui';
        }
        else if ($intForceFlag === 4)
        {
            return 'pcfrsui';
        }
        else if ($intForceFlag == 5) {
            return 'personalforum';
        }

        $intFid = (int)$arrForumInfo['forum_id']['forum_id'];
        if ($bolPtForum)
        {
            $strInnerfr = Bingo_Http_Request::get('innerfr', '');
            //if(isset(self::$_smallflow[$intFid]) && !self::_checkExempt($arrForumInfo['attrs']) && !isset(self::$_exemptlist[$intFid])) {
            if(true && !self::_checkExempt($arrForumInfo['attrs']) && !isset(self::$_exemptlist[$intFid]) && 0 >= strlen($strInnerfr)) {
                return "pcfrsui";
            } else {
                return 'pt-pc-frsui';
            }
        } elseif (self::isPersonalForum($arrForumInfo)) {
            return 'personalforum'; //是否是个人吧，Add by zhaoshiya @date: 2016年05月16日
        } else {
            /*
             *temp bigpipe logic
             */

            //special forum to run old frs logic
            if (in_array(self::getForumName(), self::$arrOldFrsForums)) {
                return 'pc-frsui';
            }

            // third party page url run old frs logic
            $strHao123 = Bingo_Http_Request::get('hao123', '');
            // iframe page from other product in baidu
            $strInnerfr = Bingo_Http_Request::get('innerfr', '');
            // special iframe page like ff14
            $strFrsType = Bingo_Http_Request::get('frstype', '');
            // old frs ajax page request
            $strApage = intval(Bingo_Http_Request::get('apage', 0));
            // iframe page
            $strIframe = Bingo_Http_Request::get('iframe', '');
            if (!empty($strHao123) || !empty($strInnerfr) || !empty($strFrsType) || $strApage === 1 || !empty($strIframe)) {
                return 'pc-frsui';
            }

            // tp
            $intTp  = intval(Bingo_Http_Request::get('tp', 0));
            $strTab = Bingo_Http_Request::get('tab', '');
            if (($intTp !== 0) || (!empty($strTab) && (!in_array($strTab, self::$arrBigpipeTabs))) || self::$_isOtherTag)
            {
                return 'pc-frsui';
            }
            /*
            // hit small flow
            if (self::hitSmallFlow())
            {
                return "pcfrsui";
            }

            // bigpipe online test switch
            $intBigpipeCookie = intval(Bingo_Http_Request::getCookie('tb_bigpipe_mode', 0));
            if ($intBigpipeCookie !== 1)
            {
                return 'pc-frsui';
            }
            */
            return 'pcfrsui';
        }

        return 'pcfrsui';
    }

    public static function checkPtForum($arrForumInfo)
    {
        $bolPtForum = false;

        if (empty($arrForumInfo))
        {
            return $bolPtForum;
        }

        $arrRes = Tieba_Service::call('official', 'checkPtForum', $arrForumInfo, null, null, 'post', 'php', 'gbk');
        if (isset($arrRes['data']['is_pt']) && true === $arrRes['data']['is_pt'])
        {
            $bolPtForum = true;
        }

        return $bolPtForum;
    }

    public static function isOldFrsui()
    {
        //$strWiseFlag = Bingo_Http_Request::get('tn');
        $strWiseFlag = $_GET['tn'];
        if ($strWiseFlag == 'wiseFRSXML')
        {
            return true;
        }
        return false;
    }

    private static function _transRawFname($strForumName, $bolQueryString, $strIe)
    {
        /* Step1:url decode */
        $strForumName = rawurldecode($strForumName);

        /* Step2:charset code trans */
        if (is_utf8($strForumName))
        {
            $strTemp = utf8_to_gbk($strForumName, 0x3);
            if (false !== $strTemp)
            {
                $strForumName = $strTemp;
            }
        }

        /* Step3:maybe delete */
        if (function_exists('trans_to_gb'))
        {
            $strTemp = trans_to_gb($strForumName);
        }
        else if (function_exists ('gbk_to_gbi'))
        {
            $strTemp = gbk_to_gbi($strForumName);
        }
        else if (function_exists('trans_to_bj'))
        {
            $strTemp = trans_to_bj($strForumName);
        }
        if (false !== $strTemp)
        {
            if($strIe === 'utf-8')
            {
                $strForumName = mb_convert_encoding($strTemp,'UTF-8','GBK');
            }
            else
            {
                $strForumName = $strTemp;
            }
        }
        return $strForumName;
    }

    public static function hitSmallFlow()
    {
        $bolIsLogin = (bool)Tieba_Session_Socket::isLogin();
        $intUserId  = intval(Tieba_Session_Socket::getLoginUid());
        if (!$bolIsLogin)
        {
            return false;
        }
        if (($intUserId % 100) < 60)
        {
            return true;
        }
        return false;
    }

    /**
        * @brief 
        *
        * @param $arrForumAttr
        *
        * @return 
     */
    private static function _checkExempt($arrForumAttr) {
        if(!isset($arrForumAttr['official']['official_type'])) {
            return false;
        }
        $intOfficialType = (int)$arrForumAttr['official']['official_type'] ;
        if(1 === $intOfficialType) {
            return true;
        }
        return false;
    }

    /**
     * 判断某个吧是不是个人吧
     * check if one forum is personal forum
     * @param $arrForumAttr
     * @return bool
     */
    public static function isPersonalForum($arrForumInfo) {
        $isPerForum = false;

        if (empty($arrForumInfo)) {
            return $isPerForum;
        }

        if (isset($arrForumInfo['attrs']['is_personal_forum']) && $arrForumInfo['attrs']['is_personal_forum']) {
            $isPerForum = true;
        }
        return $isPerForum;
    }

}
