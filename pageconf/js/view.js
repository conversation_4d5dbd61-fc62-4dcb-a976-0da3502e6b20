var View = {};
View.SideMenu = Class({
	container : null,
	activeItem : null,
	_widgetTree : null,
	_promoteList : null,
	initial : function(container, pageData){
		var currPage = (pageData && pageData.curr_page) ? pageData.curr_page : 'promote',
			active = {'promote' : 0, 'widget' : 1}[currPage] || 0,
			self = this;
		
		this.container = container;
		this._widgetTree = new Com.side.WidgetTree($('.menu_item[name="widget"]:first'), 'widget_tree');
		this._promoteList = new Com.side.PromoteList($('.menu_item[name="promote"]:first'));
		this.container.accordion({'heightStyle' : 'content', 'active' : false, 'collapsible' : true});
		this.container.on('accordionactivate', function(event, ui){
			var name = ui.newPanel.attr('name');
			self._showMenu(name);
			self.container.accordion('refresh');
		});
		this._showMenu(currPage);
	},
	_showMenu : function(name){
		var func = this['_show' + name];
		if(!func || !$.isFunction(func)) return;
		func.call(this);
	},
	//��ȡ�ƹ�λ�б�
	_showpromote : function(){
		this._promoteList.buildUI();
	},
	_showwidget : function(){
		this._widgetTree.buildUI();
	}
});

View.UpdateTask = Class({
	info : null,
	condition : null,
	contents : null,
	submitBtn : null,
	promoted_id : 0,
	initial : function(promoted_id){
		
		this.promoted_id = promoted_id;
		
		if(!($.browser.chrome || $.browser.mozilla || $.browser.safari)) { //��֧��
			alert('�Ͷ��������ʹƽ̨���ܲ���������ʹ�ø߶���������Ƽ�chrome...');
			$('input').attr('disabled', true);
			$('button').attr('disabled', true);
			$('textarea').attr('disabled', true);
			$('select').attr('disabled', true);
			return;
		}
		
		var self = this;
		
		this.info = new Com.fieldGroup($('#info'));
		this.condition = new Com.fieldGroup($('#condition'));
		this.contents = new Com.fieldGroup($('#contents'));
		this.submitBtn = $('#submit_btn');
		
		this.submitBtn.bind('click', function(e){
			self.update();
		});
	},
	getVal : function(){
		var result = {},
			infoVal = this.info.getKeyVal(),
			conditionVal = this.condition.getKeyVal(),
			contentsVal = this.contents.getValArr();
			
		result = infoVal;
		result.condition = conditionVal;
		result.content_list = contentsVal;
		return result;
	},
	update : function(){
		var self = this;
		this.submitBtn.attr('disabled', true);
		var errorFunc = function(resultdata){
			var resultDlg = new Com.resultDlg();
			if (resultdata!=null && resultdata['error']!=null && resultdata['error']!='') {
				resultDlg.errorDialog(resultdata['error']);
			} else {
				resultDlg.errorDialog('����ʧ��');
			}
			self.submitBtn.attr('disabled', false);
		};
		var successFunc = function() {
			Com.PromotedPublisher.publishTest(function() {
				window.location.href = "/pageconf/showTasks?promoted=" + self.promoted_id;
			});
		};
		var data = {
			"promoted_curr" : this.promoted_id,
			"task_curr" : this.getVal()
		};
		DataManager.request('UPDATE_TASK', {data:$.json.encode(data)}, successFunc, errorFunc);
	}
});

View.ShowTasks = Class({
	container : null,
	options : {
		promoted : ''
	},
	initial : function(container,options){
		this.container = container;
		this.options = $.extend(this.options,options);
		var self = this;
		var jq_delete_btns = this.container.find('.j_delete_task');
		jq_delete_btns.bind('click',function() {
			self.deleteTask(this);
		});
		this.container.find('.j_task_up').click(function() {
			self.moveupTask(this);
		});
		this.container.find('.j_task_down').click(function() {
			self.movedownTask(this);
		});
		this.container.find('.j_set_bottom').click(function() {
			self.setTaskBottom(this);
		});
		this.container.find('.j_unset_bottom').click(function() {
			self.unsetTaskBottom(this);
		});
	},
	deleteTask : function(elem) {
		var regexec = /task=(\d*)&promoted=(\d*)/.exec(elem.href);
		var data = {
			'task' : regexec[1],
			'promoted' : regexec[2]
		};
		var resultDlg = new Com.resultDlg();
		var errorFunc = function(resultdata){
			if (resultdata!=null && resultdata['error']!=null && resultdata['error']!='') {
				resultDlg.errorDialog(resultdata['error']);
			} else {
				resultDlg.errorDialog('ɾ��ʧ��');
			}
		};
		var successFunc = function(resultdata){
			resultDlg.successDialog('ɾ���ɹ�',function() {
				Com.PromotedPublisher.publishTest(function() {
					window.location.reload();
				});	
			});			
		};
		DataManager.request('DELETE_TASK', data, successFunc, errorFunc);
	},
	moveupTask : function(elem) {
		var jq_listItem = $(elem).closest('tr');
		var jq_listItem_prev = jq_listItem.prev();
		if (!jq_listItem_prev.hasClass('j_table_head')
			&& ((jq_listItem.hasClass('j_task_high_priority') && !jq_listItem_prev.hasClass('j_task_normal'))
			|| jq_listItem.hasClass('j_task_normal'))) {    //��ͨ���õ׵������Ҳ��ǵ�һ��
			//�ѱ�����������/�µ�
			jq_listItem.insertBefore(jq_listItem_prev);
			if (!jq_listItem.hasClass('task_changed')) {
				jq_listItem.addClass('task_changed');
			}
			this.showSaveTip();
		}
	},
	movedownTask : function(elem) {
		var jq_listItem = $(elem).closest('tr');
		var jq_listItem_next= jq_listItem.next();
		if (jq_listItem_next.size()!=0
			&& (jq_listItem.hasClass('j_task_high_priority')
			|| (jq_listItem.hasClass('j_task_normal') && !jq_listItem_next.hasClass('j_task_high_priority') ))) {    //��ͨ���õ׵������Ҳ��ǵ�һ��
			//�ѱ����������µ�
			jq_listItem.insertAfter(jq_listItem_next);
			if (!jq_listItem.hasClass('task_changed')) {
				jq_listItem.addClass('task_changed');
			}
			this.showSaveTip();
		}
	},
	setTaskBottom : function(elem) {
		var jq_listItem = $(elem).closest('tr');
		jq_listItem.removeClass('task_normal j_task_normal').addClass('task_high_priority j_task_high_priority');
		if (!jq_listItem.hasClass('task_changed')) {
			jq_listItem.addClass('task_changed');
		}
		jq_listItem.detach();
		jq_listItem.insertAfter(this.container.find('tr:last'));
		this.showSaveTip();
	},
	unsetTaskBottom : function(elem) {
		var jq_listItem = $(elem).closest('tr');
		jq_listItem.removeClass('task_high_priority j_task_high_priority').addClass('task_normal j_task_normal');
		if (!jq_listItem.hasClass('task_changed')) {
			jq_listItem.addClass('task_changed');
		}
		jq_listItem.detach();
		var jq_lastNormalTask = this.container.find('tr.j_task_normal:last');
		if (jq_lastNormalTask.size()==0) {
			jq_lastNormalTask = this.container.find('tr.j_table_head');
		}
		jq_listItem.insertAfter(jq_lastNormalTask);
		this.showSaveTip();
	},
	showSaveTip : function() {
		if ($('#topside_tip').size()==0) {
			var self = this;
			var jq_tip = $('<div id="topside_tip" class="topside_tip" style="display: none;">����ȫ���������һ�𱣴�<button class="j_save">����</button></div>');
			jq_tip.appendTo('body').slideDown()
				.find('.j_save').click(function() {
					var normalTaskIdList = [];
					var highPriorityTaskIdList = [];
					self.container.find('tr').each(function() {
						var jq_item = $(this);
						var taskId = jq_item.attr('task-id');
						if (jq_item.hasClass('j_task_normal')) {
							normalTaskIdList.push(taskId);
						} else if (jq_item.hasClass('j_task_high_priority')) {
							highPriorityTaskIdList.push(taskId);
						}
					});
					var resultDlg = new Com.resultDlg();
					var errorFunc = function(resultdata){
						if (resultdata!=null && resultdata['error']!=null && resultdata['error']!='') {
							resultDlg.errorDialog(resultdata['error']);
						} else {
							resultDlg.errorDialog('�޸�ʧ��');
						}
					};
					var successFunc = function(resultdata){
						Com.PromotedPublisher.publishTest(function() {
							if (jq_tip!=null) {
								jq_tip.remove();
								jq_tip = null;
							}
							self.container.find('.task_changed').removeClass('task_changed');
						});
					};
					DataManager.request('REORDER_TASK',{
						normal_tasks:normalTaskIdList,
						high_tasks:highPriorityTaskIdList,
						promoted:self.options.promoted
					},successFunc,errorFunc);
				});
		}
	}
});

View.UnitEdit = Class({
	_container : null,
	_baseBox : null,
	_defaultContentBox : null,
	_conditionBox : null,
	_conditionContentBox : null,
	_defaultContent : null,
	_conditionList : null,
	_defaultConditionContent : null,
	_pageData : 0,
	initial : function(container, pageData){
		this._container = container || $('body');
		this._pageData = pageData;
		this._baseBox = $('#unit_base');
		this._conditionBox = $('#unit_condition');
		this._defaultContentBox = $('#unit_default_content');
		//this._baseBox.find('select').selectmenu();
		
		var temp = {
			'text' : document.getElementById('content_text_field').innerHTML,
			'img' : document.getElementById('content_image_field').innerHTML,
			'link' : document.getElementById('content_link_field').innerHTML
		};
		
		this._defaultContent = new Com.field.customContent(this._defaultContentBox.find('.content_container'), this._defaultContentBox.find('select[name="content_type"]'), temp);
		
		var conditionTemp = document.getElementById('condition_box_field').innerHTML,
			contentTmeps = {
				'text' : document.getElementById('content_text_field').innerHTML,
				'img' : document.getElementById('content_image_field').innerHTML,
				'link' : document.getElementById('content_link_field').innerHTML
			};
			
		this._conditionList = new Com.field.conditionList(this._conditionBox.find('.module_content'), this._pageData.id, contentTmeps, conditionTemp);		
		this._bindEvents();
	},
	_bindEvents : function(){
		var self = this;
		$('#edit_default_content_btn').click(function(){
			self._buildDefaultContent();
		});
		$('#edit_condition_content_btn').click(function(){
			self._buildCondition();
		});
		$('#default_content_submit').click(function(){
			self._submitDefaultContent();
		});
		$('#rechoice').click(function(){
			self._rechoice();
		});
		$('#add_condition').click(function(){
			self._conditionList.addItem({'content' : self._defaultConditionContent});
		});
		$('#update_condition').click(function(){
			self._submitCondition();
		});
	},
	_getSceneType : function(){
		var page = this._baseBox.find('select[name="page"]:first').val(),
			login = this._baseBox.find('select[name="login"]:first').val(),
			obj = function(l, p){ 
				return ([
					l=="login" && p == 'frs',
					l=="login" && p == 'pb',
					l=="login" && p == 'itieba',
					l=="logout" && p == 'frs',
					l=="logout" && p == 'pb',
					l=="logout" && p == 'itieba'
				].indexOf(true) + 1)
			},
			result = [];
		if(login != 'all' && page != 'all')
			result = [obj(login, page)];
		else if(login == 'all' && page == 'all'){
			result = [1, 2, 3, 4, 5, 6];
		}else if(login == 'all'){
			result = [
				obj('login', page),
				obj('logout', page)
			];
		}else if(page == 'all'){
			result = [
				obj(login, 'frs'),
				obj(login, 'pb'),
				obj(login, 'itieba')
			];
		}
		return result;
	},
	//������г����µ�Ĭ�������Ƿ�һ��
	_checkUnitDefaultContentDiff : function(scenesObj, scenesTypeList){
		var dcnt, diffSceneTypes = [];
		for(var i = 0, len = scenesTypeList.length; i < len; i ++){
			var type = scenesTypeList[i];
			if(!dcnt){
				dcnt = scenesObj[type]['content'];
				delete dcnt.id;
			}else {
				var cnt = scenesObj[type]['content'];
				delete cnt.id;
				if(!Com.util.equals(dcnt, cnt)){
					diffSceneTypes.push(type);
				}
			}
		}
		if(diffSceneTypes.length)
			return diffSceneTypes;
		return false;
	},
	//������г����µ������Ƿ�һ��
	_checkUnitConditionsDiff : function(scenesObj, scenesTypeList){
		var dcdt, diffSceneTypes = [];
		for(var i = 0, len = scenesTypeList.length; i < len; i ++){
			var type = scenesTypeList[i],
				scene = scenesObj[type];
			if(!dcdt){
				dcdt = scene['condition_list'];
				delete dcdt.id;
			}else{
				var cdt = scene['condition_list'];
				delete cdt.id;
				if(!Com.util.equals(dcdt,cdt)){
					diffSceneTypes.push(type);
				}
			}
		}
		if(diffSceneTypes.length)
			return diffSceneTypes;
		return false;
	},
	//����ѡ��ҳ����û�
	_rechoice : function(){
		$('#rechoice').hide();
		$('#edit_default_content_btn').attr('disabled', false);
		$('#edit_condition_content_btn').attr('disabled', false);
		this._conditionBox.hide();
		this._defaultContentBox.hide();
		this._baseBox.find('select').attr('disabled', false);
	},
	_buildDefaultContent : function(){
		var self = this,
			sceneType = this._getSceneType();
		this._baseBox.find('select').attr('disabled', true)/*.selectmenu()*/;
		this._conditionBox.hide();
		
		$('#rechoice').show();
		$('#edit_default_content_btn').attr('disabled', true);
		$('#edit_condition_content_btn').attr('disabled', false);
		
		DataManager.request('GET_UNIT', {unit_id : this._pageData.id}, function(result){
			//�жϸ��������������Ƿ�һ��
			var diffScenes = self._checkUnitDefaultContentDiff(result.scenes, sceneType),
				scene = result.scenes[sceneType[0]],
				content = scene.content,
                condition_list = scene.condition_list,
                default_content = result.default_content;
            self._defaultContentBox.show();
            self._defaultContent.build(content.content_type);
            self._defaultContent.setDefaultVal(default_content);
            var hasCondition = (condition_list && condition_list.length>0);
            var diffConditions = self._checkUnitConditionsDiff(result.scenes, sceneType);
            var sceneSame = (!diffScenes && !diffConditions && content && content.content_type);

			if (!sceneSame) {//���ϳ������ݲ�һ��
                alert('ע�⣺�ó������ǵķ�Χ�ϴ󣬴˲���Ҳ��ͬʱ�ı��������ǵķ�Χ��С�ĳ���');
                self._defaultContent.clearVal();
            } else if (hasCondition) {//�����ѡ���������ݶ�һ�£�������������
                alert('ע�⣺�ó������а�����չʾ�����ݣ��˲���������ó����µİ�����չʾ������');
                self._defaultContent.setVal(content);
            } else{	//�����ѡ���������ݶ�һ�£���û����������
                self._defaultContent.setVal(content);
			}
		});
	},
	_buildCondition : function(){
		var self = this,
			sceneType = this._getSceneType();
		this._baseBox.find('select').attr('disabled', true)/*.selectmenu()*/;
		this._defaultContentBox.hide();
		
		$('#rechoice').show();
		$('#edit_default_content_btn').attr('disabled', false);
		$('#edit_condition_content_btn').attr('disabled', true);
		
		DataManager.request('GET_UNIT', {unit_id : this._pageData.id}, function(result){
			self._conditionBox.show();
			
			var diffConditions = self._checkUnitConditionsDiff(result.scenes, sceneType),
                diffScenes = self._checkUnitDefaultContentDiff(result.scenes, sceneType),
                sceneSame = (!diffScenes && !diffConditions),
				scene = result.scenes[sceneType[0]];
			
			self._defaultConditionContent = scene.content;
            if (!sceneSame) {//���ϳ������ݲ�һ��
                alert('ע�⣺�ó������ǵķ�Χ�ϴ󣬴˲���Ҳ��ͬʱ�ı��������ǵķ�Χ��С�ĳ���');
                self._conditionList.build();
            } else{
                self._conditionList.build(scene.condition_list);
            }
		});
	},
	_submitDefaultContent : function(){
		var content = this._defaultContent.checkVal(),
			sceneTypes = this._getSceneType(),
			unitId = this._pageData.id;
		var data = {
			unit_id : unitId,
			widget_id : this._pageData.widget_id,
			scene_types : $.json.encode(sceneTypes),
			content : $.json.encode(content)
		};
		
		if(typeof content == 'string'){
			new Com.dialog.Alert('�޸�unit����', content);
		}else{
			DataManager.request('EDIT_UNIT_CONTENT', data, function(){
				Com.ContentPublisher.publishTest();
			}, function(result){
				var tip = (result!=null && result['error'].length>0)? result['error'] : '�ύʧ�ܣ�';
				new Com.dialog.Alert('�޸�unit����',tip);
			});
		}
	},
	_submitCondition : function(){
		var unitId = this._pageData.id,
			sceneTypes = this._getSceneType(),
			conditionListContent = this._conditionList.getVal();
		
		var data = {
			unit_id : unitId,
			widget_id : this._pageData.widget_id,
			scene_types : $.json.encode(sceneTypes),
			condition_list : $.json.encode(conditionListContent)
		};

		
		if(typeof conditionListContent == 'string')
			new Com.dialog.Alert('�޸�unit����', conditionListContent);
		else{
			DataManager.request('UPDATE_UNIT_CONDITION', data, function(result){
				Com.ContentPublisher.publishTest();
			}, function(result){
				var tip = (result!=null && result['error'].length>0)? result['error'] : '�ύʧ�ܣ�';
				new Com.dialog.Alert('�޸�unit����',tip);
			});
		}
	}
});

View.promotedEdit = Class({
	_treeContainer : 'unit_tree_region',
	_promotedInfo : null,
	_isNew : 0,
	_config : {
		promotedId : '',
		units : []
	},
	initial : function(isNew,config) {
		var self = this;
		self._config = $.extend(self._config,config);
		self._isNew = isNew;
		self._promotedInfo = new Com.fieldGroup($('#promoted_info'));
		self.submitBtn = $('#commit_promoted_data');
		var widgetTree = new Com.promotedEdit.WidgetTreeForPromoted('unit_tree_region',{checkUnits:self._config.units});
		//�������
		$('#commit_promoted_data').click(function() {
			var units = widgetTree.getCheckedUnit();
			var promoted_info = self._promotedInfo.getKeyVal();
			for (var i in promoted_info) {
				if ((i=='name' || i=='alias') && promoted_info[i]=='') {
					new Com.dialog.Alert('����ʧ��','�ƹ�λ��Ϣ�����пո���Ҫ��д��');
					return;
				}
			}
			var resultDlg = new Com.resultDlg();
			var errorFunc = function(resultdata){
				if (resultdata!=null && resultdata['error']!=null && resultdata['error']!='') {
					resultDlg.errorDialog(resultdata['error']);
				} else {
					resultDlg.errorDialog('����ʧ��');
				}
				self.submitBtn.attr('disabled', false);
			};
			var successFunc;
			if (isNew) {	//�½��ƹ�λ���跢����Ԥ����
				successFunc = function() {
					Com.PromotedPublisher.publishTest(function() {
						window.parent.location.reload();
					});
				};
				self.submitBtn.attr('disabled', true);
				DataManager.request('UPDATE_PROMOTE', { units : units, promoted : promoted_info }, successFunc, errorFunc);
			} else {
				successFunc = function() {
					Com.PromotedPublisher.publishTest(function() {
						window.location = "/pageconf/showTasks?promoted="+self._config.promotedId;
					});
				};
				new Com.dialog.Confirm('�޸��ƹ�λ', '�Ƿ�ȷ�ϸ��£�', function(){
					self.submitBtn.attr('disabled', true);
					DataManager.request('UPDATE_PROMOTE', { units : units, promoted : promoted_info }, successFunc, errorFunc);
				});
			}
		});	
	}
});

View.publishCheck = Class({
	_mod : null,
	_jq_approvalContainer : null,
	_approvalStatuses : {
		'init' : 'init',
		'start' : 'start',
		'success' : 'success',
		'fail' : 'fail'
	},
	initial : function(mod) {
		var self = this;
		this._mod = mod;
		$('#publish_online').click(function() {
			$('#publish_online').attr('disabled',true);
			if (mod=='content') {
				Com.ContentPublisher.publishOnline();
			} else if (mod=='promoted') {
				Com.PromotedPublisher.publishOnline();
			}
			self._jq_approvalContainer.find('.j_approval_btn_result').attr('disabled',true);
		});
		this._initApproval();
	},
	_initApproval : function() {
		var self = this;
		var jq_approvalContainer = this._jq_approvalContainer = $('#approval_apply');
		//��������
		jq_approvalContainer.find('.j_approval_btn_start').click(function() {
			self._startApproval();
		});
		//�鿴���
		jq_approvalContainer.find('.j_approval_btn_result').click(function() {
			self._getApprovalStatus();
		});
		//ȡ������
		jq_approvalContainer.find('.j_approval_btn_cancel').click(function() {
			self._cancelApproval();
		});
		//���·�������
		jq_approvalContainer.find('.j_approval_btn_restart').click(function() {
			self._startApproval();
		});
	},
	_startApproval : function() {
		this._setApprovalStatus(this._approvalStatuses['start']);
		DataManager.request('APPROVAL_START', {mod:this._mod});
	},
	_getApprovalStatus : function() {
		var self = this;
		DataManager.request('APPROVAL_RESULT', {mod:self._mod}, function(data) {
			var text = '����������...������鿴�����ˢ�½��';
			if (data['status']!=null && data['status']!='') {
				switch (data['status']) {
					case 'agree' :
						text = '����ͨ�������������������ϡ�';
						self._setApprovalStatus(self._approvalStatuses['success']);
						break;
					case 'reject' :
						text = '����ûͨ���������<a href="http://work.baidu.com/process" target="_blank">http://work.baidu.com/process</a>�鿴����';
						self._setApprovalStatus(self._approvalStatuses['fail']);
						break;
					case 'skip' :
						text = '�����������������������ϡ�';
						self._setApprovalStatus(self._approvalStatuses['success']);
						break;
				}
			}
			self._jq_approvalContainer.find('.j_approval_status_show').html(text);
		},function(data) {
			new Com.dialog.Alert('����ʧ��',data.error);
		});
	},
	_cancelApproval : function() {
		this._setApprovalStatus(this._approvalStatuses['fail']);
	},
	_setApprovalStatus : function(status) {
		var prefix = 'approval_process_';
		var classesStr = this._jq_approvalContainer.attr('class');
		var classes = classesStr.split(' ');
		var newClasses = [];
		for (var i=0; i<classes.length; i++) {
			if (classes[i]!='' && classes[i].indexOf(prefix)<0) {
				newClasses.push(classes[i]);
			}
		}
		newClasses.push(prefix+status);
		this._jq_approvalContainer.attr('class',newClasses.join(' '));
	}
});






