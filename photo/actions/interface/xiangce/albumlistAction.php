<?php
class albumlistAction extends photo_Public_SimpleBaseAction{
    public function init(){
        $this->_actionConf = conf_ActionConf::$actionConf['interface_xiangce_albumlistAction'];
        if (false === parent::init()){
            if($this->_intErrorNo === 0 ){
                $this->_intErrorNo  = conf_ErrorNo::errInitFaile;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }
    
    public function process(){
        //У���û��Ƿ��¼
		if(photo_Public_Request::$bolUserLogin === false ){
		    $this->_intErrorNo = conf_ErrorNo::errUsrNotLogin;
			$this->_strErrorMsg = 'user is not login!';
            Bingo_Log::debug($this->_strErrorMsg);
		    return false;
		}
		
		//��ȡ�û���������ͼ���б�
        $inputParam = array(
                         'user'       => photo_Public_Request::$intUid,
                         'page_no'    => photo_Public_Request::$arrInputParam['pn'],
                         'page_count' => photo_Public_Request::$arrInputParam['rn'],
                         'inc_system' => 1,
                      );
        $albumListInfos = Rpc_YxcOpenApi::qeuryAlbumsList($inputParam,$_COOKIE);
        if($albumListInfos === false || !is_array($albumListInfos)){
            $this->_intErrorNo = conf_ErrorNo::errDaoFaile;
			$this->_strErrorMsg = "qeuryAlbumsList error! ".serialize($inputParam)."|".serialize($albumListInfos);
            Bingo_Log::warning($this->_strErrorMsg);
			return false;
        }
        
        //����б�Ϊ�ղ�Ӧ�ñ���
        //if(empty($albumListInfos)){
        //    $this->_intErrorNo = conf_ErrorNo::errYxzHasNoAlbum;
		//	$this->_strErrorMsg = "albumlist  is not open! ".serialize($inputParam)."|".serialize($albumListInfos);
        //    Bingo_Log::debug($this->_strErrorMsg);
		//	return false;
        //}
        $albumList = array();
        foreach($albumListInfos as $value){
            if(!isset($value['album_id']) || !isset($value['album_name']) || !isset($value['picture_num']) )
            {
                $this->_intErrorNo = conf_ErrorNo::errDaoFaile;
    			$this->_strErrorMsg = "albumList return param lessing! ".serialize($value)."|".serialize($albumListInfos);
                Bingo_Log::debug($this->_strErrorMsg);
    			return false;
            }
            $albumList[] = array(
                                  'id'      => $value['album_id'],
                                  'name'    => $value['album_name'],
                                  'pic_num' => $value['picture_num'],
                                );
        }
        $this->_arrTplVar['album_list'] = $albumList ;
        
        $pictureList = array();
        $firstAlbumId = 0;
        if( isset($albumList[0]['id']) ){
            $firstAlbumId = $albumList[0]['id'];
        }
        if( $firstAlbumId !== 0 ){
            //��ȡ�û���һ��ͼ���ͼƬ��Ϣ
            $inputParam = array(
                             'user'       => photo_Public_Request::$intUid,
                             'album_id'   => $firstAlbumId,
                             'page_no'    => 1,
                             'page_count' => photo_Public_Request::$arrInputParam['prn'],
                          );
            $pictureInfos = Rpc_YxcOpenApi::queryPictureList($inputParam,$_COOKIE);
            if($pictureInfos === false || !is_array($pictureInfos)){
                $this->_intErrorNo = conf_ErrorNo::errDaoFaile;
    			$this->_strErrorMsg = "queryPictureList error! ".serialize($inputParam)."|".serialize($pictureInfos);
                Bingo_Log::warning($this->_strErrorMsg);
    			return false;
            }
            
            $pictureList= photo_Logic_Photo::buildYxzPictureInfo($pictureInfos);
        }
        
        $this->_arrTplVar['defalut_album'] = array(
                                                  'id'       => $firstAlbumId,
                                                  'pic_list' => $pictureList, 
                                             );
        
        
    }
}
