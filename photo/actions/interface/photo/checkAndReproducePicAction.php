<?php
/**
 * @summary ����ͼƬת��ӿ�
 * @desc 
 *   ���ԣ�
 *       A������ͼƬ ȫ��ת��
 *       B������ͼƬ
 *           I�����������Ʒ��ͼƬת��
 *          II��������ɵ�ͼƬ�������ͼƬϵͳ����ת�棬����ת�� 
 *            
 * @auth <EMAIL>
 * @date 2012-12-17 18:34:17
 * 
 **/
class checkAndReproducePicAction extends photo_Public_BrowseAction{
 
	public function init(){
        $this->_actionConf = conf_ActionConf::$actionConf['interface_photo_checkAndReproducePicAction'];
		if (false === parent::init()){
			if($this->_intErrorNo === 0 ){
				$this->_intErrorNo  = conf_ErrorNo::errInitFaile;
				$this->_strErrorMsg = "init return error!";
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
	}
	  
	public function process(){
	    static $MAX_PICURE_REPRODUCE_NUM = 100;
	    
	    //Ȩ��У��
	    $imgData = photo_Public_Request::$arrInputParam['imgdata'];
	    $this->_arrTplVar['imgdata'] = array();
	    if(empty($imgData)){
	       return true;
	    }
	    //������У��
	    if(count($imgData) > $MAX_PICURE_REPRODUCE_NUM ){
	        Bingo_Log::debug('input param is too many!'.serialize($imgData));
	        $imgData = array_slice($imgData,0,$MAX_PICURE_REPRODUCE_NUM);
	    }
	    
	    $arrInput = array(
	                   'imgdata' => $imgData,
	                );
	    $output = photo_Logic_Photo::checnAndReproducePicture($arrInput);
	    if($output === false ){
	        $this->_intErrorNo  = conf_ErrorNo::errDaoFaile;
		    $this->_strErrorMsg = "checnAndReproducePicture faile!";
			Bingo_Log::warning($this->_strErrorMsg);
	    }
	    $this->_arrTplVar = $output;
	}
}
