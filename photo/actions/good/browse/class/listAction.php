<?php
class listAction extends photo_Public_BrowseAction{
	public function init(){
 		$this->_actionConf = conf_ActionConf::$actionConf['good_browse_class_listAction'];
		if (false === parent::init()){
			if($this->_intErrorNo === 0 ){
				$this->_intErrorNo  = conf_ErrorNo::errInitFaile;
				$this->_strErrorMsg = "init return error!";
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
	}
	
	public function process(){
		//У���Ƿ���з������Ȩ��
		if(photo_Logic_Perm::hasClassifyPerm(photo_Public_Request::$intFid,
						                     photo_Public_Request::$intUid,
						                     photo_Public_Request::$intUip) === false){
		   $this->_intErrorNo = conf_ErrorNo::errNoRight;
		   $this->_strErrorMsg = "no right to modify classify! fid=".photo_Public_Request::$intFid;
		   Bingo_Log::warning($this->_strErrorMsg);
		   return false;
		}
		//������Ϣ
    	$this->_arrTplVar['params'] = photo_Public_Request::$arrInputParam;	
		$this->_arrTplVar['params']['path'] = Bingo_Http_Request::getServer('PATH_INFO');
		
		//��ȡ�������б�                                                                                                            
		$arrInput = array('forum_id'=>photo_Public_Request::$intFid, 'product_id'=>photo_Public_Product::getProductId() );
        $arrClassify = photo_Logic_Classify::getAllClassifyByForum($arrInput); 

	 	if ($arrClassify === false) {
        	$this->_intErrorNo = conf_ErrorNo::errDaoFaile;
	        $this->_strErrorMsg = 'getAllClassifyByForum forum faile!['.serialize($arrInput).']';
	        Bingo_Log::warning($this->_strErrorMsg);
	        return false;
        } else if (empty($arrClassify)) {
        	Bingo_Log::debug('getAllClassifyByForum empty!');
        }
        	
		$arrClassifyIds = array();
        foreach ($arrClassify as $arrValue) {
        	$arrClassifyIds[] = $arrValue['classify_id'];
        }
        
		//build classify
		$arrClassifyRes = photo_Logic_Photo::buildClassifyInfo($arrClassify);
		$arrClassifyInfos = array();
        if (!empty($arrClassifyRes) && is_array($arrClassifyRes)) {
        	foreach ($arrClassifyRes as $arrClassifyInfo) {
        		$arrClassifyInfos[$arrClassifyInfo['id']] = $arrClassifyInfo;
        	}
        }		
		
		$this->_arrTplVar['catalog_list'] = $arrClassifyInfos;
		
		$arrInput = array(	'forum_id' => photo_Public_Request::$intFid,
							'not_use_cache' => true);
		$arrRecommendClass = photo_Logic_Classify::getRecommendClassByForum($arrInput);
		
		$arrViewRecommendClass = photo_Logic_Photo::buildRecommendClassByForum($arrRecommendClass);
		
		$this->_arrTplVar['recommend_catalog_list'] = $arrViewRecommendClass;
		//var_dump($this->_arrTplVar);exit;
	}
}