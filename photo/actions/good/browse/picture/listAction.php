<?php
/**
 * 
 * ͼƬ�ϴ�
 * <AUTHOR>
 * @date 2013-01-22 7:46:42
 * @comment ͼ������ͼƬ�б���Ϣ��
 *
 */
class listAction extends photo_Public_BrowseAction{
  static $rollBackInfo = array();
  
  public function init(){
    $this->_actionConf = conf_ActionConf::$actionConf['good_browse_picture_listAction'];
    if (false === parent::init()){
      if($this->_intErrorNo === 0 ){
        $this->_intErrorNo  = conf_ErrorNo::errInitFaile;
        $this->_strErrorMsg = "init return error!";
        Bingo_Log::warning($this->_strErrorMsg);
      }
    }
    return true; 
  }
  
  
  public function process(){
        //������Ϣ
    	$this->_arrTplVar['params'] = photo_Public_Util::getQueryString();
    	
    	//��ȡ����ͼƬ�б���Ϣ
		$album_id = photo_Public_Request::$arrInputParam['alb_id'];
		$pn       = photo_Public_Request::$arrInputParam['pn'];
		$rn       = photo_Public_Request::$arrInputParam['rn'];
		$ps       = photo_Public_Request::$arrInputParam['ps'];
		$pe       = photo_Public_Request::$arrInputParam['pe'];
		$see_lz   = intval(photo_Public_Request::$arrInputParam['see_lz']);
		$moreinfo = intval(photo_Public_Request::$arrInputParam['info']);
		
		$start_num = ($pn-1)*$rn+($ps-1);
		$num       =  $pe-$ps+1;
		
	    //��ȡ�����Ϣ
		$albumInfos = photo_Data_Album::getAlbumByAbumId(photo_Public_Product::getProductId(), 
        		                                         photo_Public_Request::$intFid,
        		                                         array($album_id)) ;
		if($albumInfos === false ){
			$this->_intErrorNo = conf_ErrorNo::errDaoFaile;
			$this->_strErrorMsg = "get album info error! album_id=[".photo_Public_Request::$arrInputParam['alb_id']."]".serialize($albumInfos);
            Bingo_Log::warning($this->_strErrorMsg);
			return false;
		}
		if(empty($albumInfos)){
			$this->_intErrorNo = conf_ErrorNo::errAlbumIdInvaild;
			Bingo_log::debug("album_id is invalid! album_id=".photo_Public_Request::$arrInputParam['alb_id']);
			return false;
		}
		$albumInfo = $albumInfos[0];
		
		$pictureInfos = photo_Data_Picture::getPicByALbumId(photo_Public_Product::getProductId(), 
        		                                            photo_Public_Request::$intFid, 
        		                                            $album_id, 
        		                                            $start_num, 
        		                                            $num,
        		                                            0,//conf_Include::GET_PICTURE_ORDER_BY_TIME_ORDER,
        		                                            0
        		                                            );
		if($pictureInfos === false ){
			$this->_intErrorNo = conf_ErrorNo::errDaoFaile;
			$this->_strErrorMsg = "get picture info list  error! album_id=".photo_Public_Request::$arrInputParam['alb_id'];
            Bingo_Log::warning($this->_strErrorMsg);
			return false;
		}
		
		//build picture
		$viewPictureInfo = photo_Logic_Photo::buildPictureInfo($pictureInfos,
		                                                       photo_Logic_Photo::BUILD_PICTURE_POWER,
		                                                       $start_num);
	    $this->_arrTplVar['pic_list'] = $viewPictureInfo;               
		$pageInfo = $this->getPageTpl($pn, $albumInfo['all_picture_count'], $rn);
		$this->_arrTplVar = array_merge($this->_arrTplVar,$pageInfo);
		
		$this->_arrTplVar['power']      = array(
                                         'upload' => photo_Logic_Perm::hasUploadPerm(
			                                        photo_Public_Request::$intFid,
			                                        photo_Public_Request::$intUid,
			                                        photo_Public_Request::$intUip
			                                      ),  // �ϴ�ͼƬ;
			                             'catalog_manager' => photo_Logic_Perm::hasClassifyPerm(
			                                        photo_Public_Request::$intFid,
			                                        photo_Public_Request::$intUid,
			                                        photo_Public_Request::$intUip
			                                      ),//Ŀ¼�༭Ȩ��
			                            );
		
        if($moreinfo !== 0 ){
    		//build albumInfo 
    		$viewAlbums = photo_Logic_Photo::buildAlbumInfo($albumInfos,
    		                                                photo_Logic_Photo::BUILD_ABLUM_COUNT
    		                                               );
    		$viewAlbum = $viewAlbums[0];
    		$this->_arrTplVar['album_info'] = $viewAlbum;
    		
            //��ȡ��ᴴ���ߵ���Ϣ
    		$inputUids = array($albumInfo['create_user_name'] => $albumInfo['create_user_id']);
    		$albumCreateUserInfos = photo_Logic_User::getUserInfoList($inputUids);
    		if(empty($albumCreateUserInfos)){
    			Bingo_Log::debug('get userinfo empty!'.serialize($inputUids));
    		}
    		$this->_arrTplVar['user_list'] = $albumCreateUserInfos;
    	}
  		if ($ps == 1) {
    		Tieba_Stlog::addNode('goodalbum_pb', 1);
    	}
  }
  
}