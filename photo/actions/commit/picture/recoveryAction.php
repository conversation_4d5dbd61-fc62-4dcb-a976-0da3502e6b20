<?php
class recoveryAction extends photo_Public_CommitAction{
	public function init(){
 		$this->_actionConf = conf_ActionConf::$actionConf['commit_picture_recoveryAction'];
		if (false === parent::init()){
			if($this->_intErrorNo === 0 ){
				$this->_intErrorNo  = conf_ErrorNo::errInitFaile;
				$this->_strErrorMsg = "init return error!";
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
	}
	
	public function process(){
		//Ȩ��У��
	    if(photo_Logic_Perm::hasRcorverPhotoPerm(photo_Public_Request::$intFid,
		                                         photo_Public_Request::$intUid,
		                                         photo_Public_Request::$intUip) === false ){
	      $this->_intErrorNo  = conf_ErrorNo::errNoRight;
	      $this->_strErrorMsg = "no right to revorver  picture!";
	      Bingo_Log::debug($this->_strErrorMsg);
	      return false;
	    }
	    
	    //��ȡͼƬ��Ϣ
		$pictureId    = photo_Public_Request::$arrInputParam['pic_id'];
		$pictureInfos = photo_Data_Picture::getMutiPicById(photo_Public_Product::getProductId(), 
		                                                   photo_Public_Request::$intFid, 
		                                                   array($pictureId),true);
		if($pictureInfos === false){
			$this->_intErrorNo = conf_ErrorNo::errDaoFaile;
		    $this->_strErrorMsg = "get picture info error!! pic_id=".photo_Public_Request::$arrInputParam['pic_id'];
		    Bingo_Log::warning($this->_strErrorMsg);
		    return false;
		}
		if(empty($pictureInfos)){
		    $this->_intErrorNo = conf_ErrorNo::errPictureIdInvaild;
		    $this->_strErrorMsg = "picture not exist!! pic_id=".photo_Public_Request::$arrInputParam['pic_id'];
		    Bingo_Log::debug($this->_strErrorMsg);
		    return false;
		}
		$pictureInfo = $pictureInfos[0];
		
		//Bingo_Log::debug(print_r($pictureInfo,true));
		//˵������Ѿ�ɾ����
		if($pictureInfo['is_delete'] >= conf_Include::PICTURE_ALBUM_NORMAL_DELETE){
			$this->_intErrorNo = conf_ErrorNo::errAlbumDelete;
		    $this->_strErrorMsg = "picture's album have been delete!! pic_id=".photo_Public_Request::$arrInputParam['pic_id'];
		    Bingo_Log::debug($this->_strErrorMsg);
		    return false;
		}
		
		if($pictureInfo['is_delete'] != conf_Include::PICTURE_NORMAL_DELETE &&
		   $pictureInfo['is_delete'] != conf_Include::PICTURE_VERIFY_DELETE &&
		   $pictureInfo['is_delete'] != conf_Include::PICTURE_PM_DELETE
		  ){
		    Bingo_Log::debug("no need to recovery  the picture!! pic_id=".photo_Public_Request::$arrInputParam['pic_id'].' status:'.$pictureInfo['is_delete']);
		    return false;
	    }
		
		if(($pictureInfo['is_delete'] == conf_Include::PICTURE_PM_DELETE ||
		    $pictureInfo['is_delete'] == conf_Include::PICTURE_VERIFY_FAILE) &&
		    photo_Logic_Perm::isPM(photo_Public_Request::$intFid, 
		                           photo_Public_Request::$intUid, 
		                           photo_Public_Request::$intUip) === false 
		   ){
			    $this->_intErrorNo = conf_ErrorNo::errNoRightRecoveryPM;
			    $this->_strErrorMsg = "no right to recovery  the picture!! pic_id=".photo_Public_Request::$arrInputParam['pic_id'];
			    Bingo_Log::debug($this->_strErrorMsg);
			    return false;
		}
	    
		//��ʼ�ָ�ͼƬ
		$ret = photo_Logic_Photo::recoveryPicture(photo_Public_Product::getProductId(), 
		                                          photo_Public_Request::$intFid,
		                                          $pictureInfo,
		                                          photo_Public_Request::$intRequestTime, 
		                                          photo_Public_Request::$intUid, 
		                                          photo_Public_Request::$strUname);
		if($ret === false ){
			$this->_intErrorNo = conf_ErrorNo::errDaoFaile;
			$this->_strErrorMsg = "recovery picture error! pictureId=".$pictureId;
            Bingo_Log::warning($this->_strErrorMsg);
			return false;
		}
		if($ret ===-100){
			Bingo_Log::debug('picture already recovery!picture_id='.$pictureId);
			dal_photo::rollback();
			return true;
		}
		
		if($ret === -1 ){//��ͼƬ��ͼ��δ�ָ����޷��ָ�ͼƬ
			$this->_intErrorNo = conf_ErrorNo::errAlbumIdInvaild;
			$this->_strErrorMsg = "picture's album is delete ! pictureId=".$pictureId;
            Bingo_Log::debug($this->_strErrorMsg);
			return false;
		}
		
	   //�ָ�����
	   $arrInput = array(
    		          'product_id'      => photo_Public_Product::getProductId(),
    	              'foreign_id'      => $pictureId, 
    	              'op_user_id'      => photo_Public_Request::$intUid,  
    	              'op_user_name'    => photo_Public_Request::$strUname, 
    	              'op_user_ip'      => photo_Public_Request::$strUip,
    	            );
	   $arrOutput = Rpc_CommonComment::recoveryCommentByForeign($arrInput);
       if($arrOutput === false){
			$this->_intErrorNo = conf_ErrorNo::errDaoFaile;
			$this->_strErrorMsg = 'recovery picture\'s comment error!'.serialize($arrInput);
			Bingo_Log::warning($this->_strErrorMsg);
			return false;
	   }
	}
}
