<?php
class listAction extends photo_Public_SimpleBaseAction{

	private $_mArrUser = array();

	public function init(){
 		$this->_actionConf = conf_ActionConf::$actionConf['browse_catch_listAction'];
		if (false === parent::init()){
			if($this->_intErrorNo === 0 ){
				$this->_intErrorNo  = conf_ErrorNo::errInitFaile;
				$this->_strErrorMsg = "init return error!";
				Bingo_Log::debug($this->_strErrorMsg);
			}
		}
		////У���û��Ƿ��¼
		if(photo_Public_Request::$bolUserLogin === false ){
			$this->_mArrUser['is_login']      = false;
			$this->_intErrorNo = conf_ErrorNo::errUsrNotLogin;
			$this->_strErrorMsg = 'user is not login!';
			Bingo_Log::debug($this->_strErrorMsg);
			return true;
		}


		////���û����ж�
		//if($this->checkNoUserName === true && photo_Public_Request::$intNoUn == true ){
		//	$this->_mArrUser['no_un']      = 1;
		//	$this->_intErrorNo = conf_ErrorNo::errNoUserName;
		//	$this->_strErrorMsg = 'user name is null!';
		//	Bingo_Log::warning($this->_strErrorMsg);
		//	return true;
		//}
		
		if (!$this->genTbs()){
			$this->_intErrorNo = conf_ErrorNo::errTBSFaile;
			$this->_strErrorMsg = 'create tbs fail!';
			Bingo_Log::debug($this->_strErrorMsg);
			return true;
		}

		return true;
	}
	
	public function process(){
		
		$this->_mArrUser['id']            = photo_Public_Request::$intUid;
		$this->_mArrUser['name'] 	      = photo_Public_Request::$strUname;
		
		if(photo_Public_Request::$intUid > 0){
			$arrItiebaUserInfo = Rpc_UserRelation::getItiebaByUids(array(photo_Public_Request::$intUid));
			$this->_mArrUser['itieba_id']     = $intItiebaId = $arrItiebaUserInfo[0]['itieba_id'];
			$this->_mArrUser['outer_id']      = $intOuterId  = $arrItiebaUserInfo[0]['outer_id'];
			$this->_mArrUser['is_login']      = true;
		}
		
		$arrInput = array();
		if($intItiebaId > 0) {
			$arrInput = array(
				'itieba_id' => $intItiebaId,
				'offset'    => 0,
				'limit'		=> 10,
				'includes'  => array(array(
							'product'	=> 1,
							'subtype'	=> 2,
						)),
				'excludes'  => array(),
				'filter_chain' => 'chain_mask',
           	    'grouper' => 'grouper_single',
			);
		}
		$ret = Rpc_Event::getFollowEventsSelf($arrInput);
		$arrEventsInfo = $ret['follow_events'];
		
		$i = 0;
		if (count($arrEventsInfo) > 0) {
			foreach ($arrEventsInfo as $value) {
				$this->_arrTplVar[$i]['forum_id'] = $value[0]['event']['forum_id'];
				$this->_arrTplVar[$i]['thread_id'] = $value[0]['event']['thread_id'];
				$this->_arrTplVar[$i]['title'] = $value[0]['event']['title'];
				$str = $value[0]['event']['other'];
				$intPos = strpos($str, 'forum_name');
				$str = substr($str, $intPos + 10);
				$intPos = strpos($str, 'ITM');
				
				$str = substr($str, 0, $intPos);
				
				$this->_arrTplVar[$i]['forum_name'] = $str;
				$i++;
			}
		}
	}


	/*
	 * ��ȡtbs
	 */
	protected function genTbs(){
		photo_Public_Request::$strTbs = Tieba_Tbs::gene(photo_Public_Request::$bolUserLogin);
		if (photo_Public_Request::$strTbs === false){
			$this->_intErrorNo = conf_ErrorNo::errTBSFaile;
			$this->_strErrorMsg = 'gen tbs error';
            Bingo_Log::warning($this->_strErrorMsg);
			return false;
		}
		return true;
	}


	/**
	 *  �ж�ĳ�����Ƿ��ܷ�ͼ��
	 *  @return 1,�ܷ�ͼ��  0,���ܷ�ͼ��
	 */
	public function  getCanSendPics($fid,$fname){

		$intRes = 0;

		Bingo_Timer::start ( 'cupid' );
		$intRes = Rpc_Cupid::getBrandZone ( $fname, $fid );
		Bingo_Timer::start ( 'cupid' );

		if($intRes === false){
			Bingo_Log::warning ( 'get failure!talk with cupid error!' );
		}

		if ($intRes == 0) {
			$intRes = 1;
		} else {
			$intRes = 0;
		}

		return $intRes;

	}

}