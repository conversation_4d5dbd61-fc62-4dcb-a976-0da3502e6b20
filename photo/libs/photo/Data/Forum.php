<?php
class photo_Data_Forum{ 
	/**
	 * ��ȡ������Ļ�����Ϣ
	 * 
	 * @param
	 *     $product_id => integer , //��Ʒid
	 *     $forum_id   => integer , //��id
	 * @return
	 *     �ɹ���
	 *          �������Ϣ
	 *     ʧ�ܣ�
	 *          false
	 */
	public static function getForumInfo($product_id,
	                                    $forum_id,
	                                    $notUseCache = false
	                                   )
	{
		static $buff_product_id = null;
		static $buff_forum_id   = null;
		static $buff_forum_infos = null;
		if($buff_product_id == $product_id && $buff_forum_id ==$forum_id){
			Bingo_Log::debug('you hit!'.__FUNCTION__);
			return $buff_forum_infos;
		}
		
		$arrInput = array(
		                    'product_id' => $product_id,
	                        'forum_id'   => $forum_id,
		                 );
		$outPut =  photo_Public_DalCache::getData(__FUNCTION__, $arrInput,$notUseCache);
	    if(!empty($outPut)){
	    	$buff_product_id  = $product_id;
	    	$buff_forum_id    = $forum_id;
			$buff_forum_infos = $outPut;
		}
		return $outPut;
	}
	
	/**
	 * 
	 * �޸����������Ϣ
	 * @param
	 *    $arrInput =>
	 *     {
     *             'forum_id'               => integer,       //��id
     *             'product_id'             => integer,       //��Ʒid
     *             'mod_name'               => string(null),  //���������
     *             'display_album'          => integer(null), //�������ʾͼƬid
     *             'add_album_count'        => integer(null), //����������������
     *             'add_picture_count'      => integer(null), //�����������Ƭ����
     *             'add_post_album_count'   => integer(null), //����ͼ������ͼƬ��
     *             'add_post_picture_count' => integer(null), //����ͼ������ͼ����
     *             'add_good_album_count'   => integer(null), //�������Ʒ�����
     *             'add_top_album_count'    => integer(null), //������ö������
     *             'add_classify_count'     => integer(null), //���ӷ������
     *             'add_picture_size'       => integer(null), //�����������Ƭ��ռ�ռ��С
	 *     }
	 *    
	 */
	public static function modifyForumInfo( $arrInput){
		$arrInput['command_no']  = conf_Command::modifyForumPhotoInfo;
	    $outPut =  photo_Public_DalCache::getData(__FUNCTION__, $arrInput);
		return $outPut;                       	
	}
	
	
	/**
	 * 
	 * ��ͨ�����
	 * @param
	 *    $arrInput =>
	 *     {
     *             'forum_id'               => integer,       //��id
     *             'product_id'             => integer,       //��Ʒid
     *             'mod_name'               => string,        //���������
     *             'display_album'          => integer(null), //�������ʾͼƬid
     *             'op_user_id'             => integer,       //��Ч��־���������û�id
     *             'op_user_name'           => string,        //��Ч��־���������û���
     *             'op_time'                => integer,       //��Ч��־������ʱ��
	 *     }
	 *    
	 */
	public static function addForumPhoto($arrInput){
	    if(!isset($arrInput['display_album'])){
	       $arrInput['display_album'] = 0;
	    }
	    $arrInput['command_no']  = conf_Command::addForumPhoto;
	    $outPut =  photo_Public_DalCache::getData(__FUNCTION__, $arrInput);
		return $outPut;  
	}
	public static  function queryClub($arrInput){
		$outPut =  photo_Public_DalCache::getData(__FUNCTION__, $arrInput);
		return $outPut;  
	}
	
	public static function updataClub($arrInput){
		$outPut = photo_Public_DalCache::getData(__FUNCTION__, $arrInput);
	}
}
