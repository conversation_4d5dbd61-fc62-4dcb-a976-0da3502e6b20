<?php
/**
 * ���ݿ������
 * @author:cuishichao
 */
require_once('crawlerLog.php');

define(CONFIG_DBHOST, "db_host");
define(CONFIG_DBPORT, "db_port");
define(CONFIG_DBNAME, "db_name");
define(CONFIG_USERNAME, "username");
define(CONFIG_PASSWORD, "password");
class dbManager{

	public $conn = null;
	/*
	 * ����connection
	 * @param arrInfo ������Ϣ������һ��
	 * ����host��port��username, password��dbname
	 * @reuturn connection
	 */
	public function __construct($arrInfo)
	{

		crawlerLog::getInstance()->writeLog("�������ݿ�, ����: ".$arrInfo[CONFIG_DBHOST]." Port: ".$arrInfo[CONFIG_DBPORT]." ���ݿ�����: ".$arrInfo[CONFIG_DBNAME], 0);
		$this->conn = mysql_connect($arrInfo[CONFIG_DBHOST].":".$arrInfo[CONFIG_DBPORT],$arrInfo[CONFIG_USERNAME],$arrInfo[CONFIG_PASSWORD]);
		if($this->conn == FALSE){
		    crawlerLog::getInstance()->writeLog("�������ݿ����: ".$arrInfo[CONFIG_DBHOST]." Port: ".$arrInfo[CONFIG_DBPORT]." ���ݿ�����: ".$arrInfo[CONFIG_DBNAME], 2);
			carwlerLog::getInstance()->writeLog("ErrorMessage: ".mysql_error());
			die();
		}
		mysql_select_db($arrInfo[CONFIG_DBNAME]);
		return $this->conn;
	}

	public function excuteQuery($strSQL)
	{
		$result = mysql_query($strSQL);
		return $result;
	}

	public function __destruct()
	{
		mysql_close($this->conn);
		$this->conn = null;
	}
}
?>
